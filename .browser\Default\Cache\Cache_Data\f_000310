{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Periodic Advertiser<span id=\"periodic-advertiser\" class=\"self-anchor\"><a class=\"perm\" href=\"#periodic-advertiser\">#</a></span></h1><p style=\"color:inherit\">Periodic Advertiser. </p><p style=\"color:inherit\">Provides support for advertising with periodic advertising trains that do not have subevents or response slots. </p><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-periodic-advertiser-flags\" target=\"_blank\" rel=\"\">Periodic Advertising Configuration Flags</a></p><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-periodic-advertiser-set-data\">sl_bt_periodic_advertiser_set_data</a>(uint8_t advertising_set, size_t data_len, const uint8_t *data)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-periodic-advertiser-set-long-data\">sl_bt_periodic_advertiser_set_long_data</a>(uint8_t advertising_set)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-periodic-advertiser-start\">sl_bt_periodic_advertiser_start</a>(uint8_t advertising_set, uint16_t interval_min, uint16_t interval_max, uint32_t flags)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-periodic-advertiser-stop\">sl_bt_periodic_advertiser_stop</a>(uint8_t advertising_set)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-periodic-advertiser-set-data-id\">sl_bt_cmd_periodic_advertiser_set_data_id</a> 0x00580020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-periodic-advertiser-set-long-data-id\">sl_bt_cmd_periodic_advertiser_set_long_data_id</a> 0x01580020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-periodic-advertiser-start-id\">sl_bt_cmd_periodic_advertiser_start_id</a> 0x02580020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-periodic-advertiser-stop-id\">sl_bt_cmd_periodic_advertiser_stop_id</a> 0x03580020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-periodic-advertiser-set-data-id\">sl_bt_rsp_periodic_advertiser_set_data_id</a> 0x00580020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-periodic-advertiser-set-long-data-id\">sl_bt_rsp_periodic_advertiser_set_long_data_id</a> 0x01580020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-periodic-advertiser-start-id\">sl_bt_rsp_periodic_advertiser_start_id</a> 0x02580020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-periodic-advertiser-stop-id\">sl_bt_rsp_periodic_advertiser_stop_id</a> 0x03580020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_periodic_advertiser_set_data<span id=\"sl-bt-periodic-advertiser-set-data\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-periodic-advertiser-set-data\">#</a></span></h3><blockquote>sl_status_t sl_bt_periodic_advertiser_set_data (uint8_t advertising_set, size_t data_len, const uint8_t * data)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">data_len</td><td><p style=\"color:inherit\">Length of data in <code>data</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">data</td><td><p style=\"color:inherit\">Data to be set</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set the data for periodic advertising on an advertising set. Maximum 254 bytes of data can be set with this command. For setting longer advertising data, use command <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-periodic-advertiser#sl-bt-periodic-advertiser-set-long-data\" target=\"_blank\" rel=\"\">sl_bt_periodic_advertiser_set_long_data</a>.</p><p style=\"color:inherit\">If the periodic advertising is currently enabled, the new advertising data will be used immediately. Periodic advertising can be enabled using the command <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-periodic-advertiser#sl-bt-periodic-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_periodic_advertiser_start</a>.</p><p style=\"color:inherit\">The invalid parameter error will be returned if the data is too long to fit into the advertisement.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>3013</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_periodic_advertiser_set_long_data<span id=\"sl-bt-periodic-advertiser-set-long-data\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-periodic-advertiser-set-long-data\">#</a></span></h3><blockquote>sl_status_t sl_bt_periodic_advertiser_set_long_data (uint8_t advertising_set)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set data for periodic advertising on an advertising set. Data currently in the system data buffer will be extracted as the advertising data. The buffer will be emptied after this command regardless of the completion status.</p><p style=\"color:inherit\">Prior to calling this command, add data to the buffer with one or multiple calls to <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-system#sl-bt-system-data-buffer-write\" target=\"_blank\" rel=\"\">sl_bt_system_data_buffer_write</a>.</p><p style=\"color:inherit\">Maximum 1650 bytes of data can be set for periodic advertising. Advertising parameters may limit the amount of data that can be sent.</p><p style=\"color:inherit\">See <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-periodic-advertiser#sl-bt-periodic-advertiser-set-data\" target=\"_blank\" rel=\"\">sl_bt_periodic_advertiser_set_data</a> for more details.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>3036</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_periodic_advertiser_start<span id=\"sl-bt-periodic-advertiser-start\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-periodic-advertiser-start\">#</a></span></h3><blockquote>sl_status_t sl_bt_periodic_advertiser_start (uint8_t advertising_set, uint16_t interval_min, uint16_t interval_max, uint32_t flags)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">interval_min</td><td><p style=\"color:inherit\">Minimum periodic advertising interval. Value in units of 1.25 ms</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>Range:</strong> 0x06 to 0xFFFF</p></li><li><p style=\"color:inherit\">Time range: 7.5 ms to 81.92 s</p></li><li><p style=\"color:inherit\"><strong>Default</strong> : 100 ms </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">interval_max</td><td><p style=\"color:inherit\">Maximum periodic advertising interval. Value in units of 1.25 ms</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>Range:</strong> 0x06 to 0xFFFF</p></li><li><p style=\"color:inherit\">Time range: 7.5 ms to 81.92 s</p></li><li><p style=\"color:inherit\">Note: interval_max should be bigger than interval_min</p></li><li><p style=\"color:inherit\"><strong>Default</strong> : 200 ms </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">Additional periodic advertising options. Value: 0 or bitmask of <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-periodic-advertiser-flags\" target=\"_blank\" rel=\"\">Periodic Advertising Configuration Flags</a></p></td></tr></tbody></table></div><p style=\"color:inherit\">Start periodic advertising on an advertising set.</p><p style=\"color:inherit\">According to the Bluetooth Core specification, periodic advertising PDUs cannot be transmitted until at least one extended advertising event has been completed. If the application needs exact control over the extended advertising data and parameters, use the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser\" target=\"_blank\" rel=\"\">Advertiser</a> class to configure the parameters of the advertising set and the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser\" target=\"_blank\" rel=\"\">Extended Advertiser</a> class to set or generate the desired extended advertising data payload. If the application does not configure the parameters or set the data, the default parameters and empty advertising data are used for the extended advertising.</p><p style=\"color:inherit\">If the application has not already started extended advertising and the flag <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-periodic-advertiser-flags#sl-bt-periodic-advertiser-auto-start-extended-advertising\" target=\"_blank\" rel=\"\">SL_BT_PERIODIC_ADVERTISER_AUTO_START_EXTENDED_ADVERTISING</a> is set in <code>flags</code>, the stack will automatically start extended advertising with the parameters and extended advertising data currently configured to the advertising set. The application may stop the automatically started extended advertising using the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-stop\" target=\"_blank\" rel=\"\">sl_bt_advertiser_stop</a> command.</p><p style=\"color:inherit\">If the application has not already started extended advertising and the flag <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-periodic-advertiser-flags#sl-bt-periodic-advertiser-auto-start-extended-advertising\" target=\"_blank\" rel=\"\">SL_BT_PERIODIC_ADVERTISER_AUTO_START_EXTENDED_ADVERTISING</a> is not set in <code>flags</code>, the stack will momentarily start extended advertising with the parameters and extended advertising data currently configured to the advertising set. Unless the application starts extended advertising before the first extended advertising event has completed, the stack will automatically stop the momentary extended advertising after the first extended advertising event.</p><p style=\"color:inherit\">Periodic advertising PDUs are transmitted on the secondary PHY configured for the advertising set with the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-set-phy\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_set_phy</a> command.</p><p style=\"color:inherit\">Use <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-periodic-advertiser#sl-bt-periodic-advertiser-stop\" target=\"_blank\" rel=\"\">sl_bt_periodic_advertiser_stop</a> command to stop the periodic advertising.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>3098</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_periodic_advertiser_stop<span id=\"sl-bt-periodic-advertiser-stop\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-periodic-advertiser-stop\">#</a></span></h3><blockquote>sl_status_t sl_bt_periodic_advertiser_stop (uint8_t advertising_set)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Stop the periodic advertising on an advertising set. Counterpart to <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-periodic-advertiser#sl-bt-periodic-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_periodic_advertiser_start</a>.</p><p style=\"color:inherit\">This command does not affect the enable state of the legacy or extended advertising on the advertising set, i.e., the legacy or extended advertising is not stopped.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>3117</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_periodic_advertiser_set_data_id<span id=\"sl-bt-cmd-periodic-advertiser-set-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-periodic-advertiser-set-data-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_periodic_advertiser_set_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00580020</pre><br><div>Definition at line <code>2939</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_periodic_advertiser_set_long_data_id<span id=\"sl-bt-cmd-periodic-advertiser-set-long-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-periodic-advertiser-set-long-data-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_periodic_advertiser_set_long_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01580020</pre><br><div>Definition at line <code>2940</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_periodic_advertiser_start_id<span id=\"sl-bt-cmd-periodic-advertiser-start-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-periodic-advertiser-start-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_periodic_advertiser_start_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02580020</pre><br><div>Definition at line <code>2941</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_periodic_advertiser_stop_id<span id=\"sl-bt-cmd-periodic-advertiser-stop-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-periodic-advertiser-stop-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_periodic_advertiser_stop_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03580020</pre><br><div>Definition at line <code>2942</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_periodic_advertiser_set_data_id<span id=\"sl-bt-rsp-periodic-advertiser-set-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-periodic-advertiser-set-data-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_periodic_advertiser_set_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00580020</pre><br><div>Definition at line <code>2943</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_periodic_advertiser_set_long_data_id<span id=\"sl-bt-rsp-periodic-advertiser-set-long-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-periodic-advertiser-set-long-data-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_periodic_advertiser_set_long_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01580020</pre><br><div>Definition at line <code>2944</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_periodic_advertiser_start_id<span id=\"sl-bt-rsp-periodic-advertiser-start-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-periodic-advertiser-start-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_periodic_advertiser_start_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02580020</pre><br><div>Definition at line <code>2945</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_periodic_advertiser_stop_id<span id=\"sl-bt-rsp-periodic-advertiser-stop-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-periodic-advertiser-stop-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_periodic_advertiser_stop_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03580020</pre><br><div>Definition at line <code>2946</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-periodic-advertiser", "status": "success"}