{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>PAST Receiver<span id=\"past-receiver\" class=\"self-anchor\"><a class=\"perm\" href=\"#past-receiver\">#</a></span></h1><p style=\"color:inherit\">PAST Receiver. </p><p style=\"color:inherit\">Synchronize to periodic advertising trains by receiving Periodic Advertising Synchronization Transfer over a connection. </p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-past-receiver-mode-t\">sl_bt_past_receiver_mode_t</a> {</div><div class=\"enum\">sl_bt_past_receiver_mode_ignore = 0x0</div><div class=\"enum\">sl_bt_past_receiver_mode_synchronize = 0x1</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Specifies the mode for receiving synchronization transfers. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-past-receiver-set-default-sync-receive-parameters\">sl_bt_past_receiver_set_default_sync_receive_parameters</a>(uint8_t mode, uint16_t skip, uint16_t timeout, uint8_t reporting_mode)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-past-receiver-set-sync-receive-parameters\">sl_bt_past_receiver_set_sync_receive_parameters</a>(uint8_t connection, uint8_t mode, uint16_t skip, uint16_t timeout, uint8_t reporting_mode)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-past-receiver-set-default-sync-receive-parameters-id\">sl_bt_cmd_past_receiver_set_default_sync_receive_parameters_id</a> 0x00510020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-past-receiver-set-sync-receive-parameters-id\">sl_bt_cmd_past_receiver_set_sync_receive_parameters_id</a> 0x01510020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-past-receiver-set-default-sync-receive-parameters-id\">sl_bt_rsp_past_receiver_set_default_sync_receive_parameters_id</a> 0x00510020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-past-receiver-set-sync-receive-parameters-id\">sl_bt_rsp_past_receiver_set_sync_receive_parameters_id</a> 0x01510020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>sl_bt_past_receiver_mode_t<span id=\"sl-bt-past-receiver-mode-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-past-receiver-mode-t\">#</a></span></h3><blockquote>sl_bt_past_receiver_mode_t</blockquote><p style=\"color:inherit\">Specifies the mode for receiving synchronization transfers. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_past_receiver_mode_ignore</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) No attempt is made to synchronize to a periodic advertising train for which the synchronization information was received. No event will be triggered towards the application. </p></td></tr><tr><td class=\"fieldname\">sl_bt_past_receiver_mode_synchronize</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Attempt to synchronize to a periodic advertising train for which the synchronization information was received. When the information is received, an event will be triggered to indicate success or failure and to provide the application with the periodic advertising synchronization handle. </p></td></tr></tbody></table><br><div>Definition at line <code>5208</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_past_receiver_set_default_sync_receive_parameters<span id=\"sl-bt-past-receiver-set-default-sync-receive-parameters\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-past-receiver-set-default-sync-receive-parameters\">#</a></span></h3><blockquote>sl_status_t sl_bt_past_receiver_set_default_sync_receive_parameters (uint8_t mode, uint16_t skip, uint16_t timeout, uint8_t reporting_mode)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-past-receiver#sl-bt-past-receiver-mode-t\" target=\"_blank\" rel=\"\">sl_bt_past_receiver_mode_t</a>. The mode to specify how the Bluetooth stack reacts when synchronization information is received. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_past_receiver_mode_ignore (0x0):</strong> No attempt is made to synchronize to a periodic advertising train for which the synchronization information was received. No event will be triggered towards the application.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_past_receiver_mode_synchronize (0x1):</strong> Attempt to synchronize to a periodic advertising train for which the synchronization information was received. When the information is received, an event will be triggered to indicate success or failure and to provide the application with the periodic advertising synchronization handle.</p></li></ul><p style=\"color:inherit\">Default: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-past-receiver#sl-bt-past-receiver-mode-ignore\" target=\"_blank\" rel=\"\">sl_bt_past_receiver_mode_ignore</a> (No attempt is made to synchronize) </p></td></tr><tr><td>[in]</td><td class=\"paramname\">skip</td><td><p style=\"color:inherit\">The maximum number of periodic advertising packets that can be skipped after a successful receive.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x0000 to 0x01F3</p></li><li><p style=\"color:inherit\">Default value: 0 </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">timeout</td><td><p style=\"color:inherit\">The maximum permitted time between successful receives. If this time is exceeded, synchronization is lost. Unit: 10 ms.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x0A to 0x4000</p></li><li><p style=\"color:inherit\">Unit: 10 ms</p></li><li><p style=\"color:inherit\">Time range: 100 ms to 163.84 s</p></li><li><p style=\"color:inherit\">Default value: 1000 ms </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">reporting_mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync#sl-bt-sync-reporting-mode-t\" target=\"_blank\" rel=\"\">sl_bt_sync_reporting_mode_t</a>. Specifies the initial mode for reporting data received in the periodic advertising train after it has achieved synchronization. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_sync_report_none (0x0):</strong> Data received in periodic advertising trains is not reported to the application.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_sync_report_all (0x1):</strong> Data received in periodic advertising trains is reported to the application.</p></li></ul><p style=\"color:inherit\">Default: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync#sl-bt-sync-report-all\" target=\"_blank\" rel=\"\">sl_bt_sync_report_all</a> (Data received in periodic advertising trains is reported to the application) </p></td></tr></tbody></table></div><p style=\"color:inherit\">Set the default parameters for receiving Periodic Advertising Synchronization Transfers (PAST) over connections. The default parameters will be in effect for all subsequent connections, unless overridden by command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-past-receiver#sl-bt-past-receiver-set-sync-receive-parameters\" target=\"_blank\" rel=\"\">sl_bt_past_receiver_set_sync_receive_parameters</a> after the connection is opened.</p><p style=\"color:inherit\">This command sets parameters that do not limit the synchronization based on the CTE type. If the application includes bluetooth_feature_aoa_receiver or bluetooth_feature_aod_receiver component and wants to specify a particular CTE limitation, the application should use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-cte-receiver#sl-bt-cte-receiver-set-default-sync-receive-parameters\" target=\"_blank\" rel=\"\">sl_bt_cte_receiver_set_default_sync_receive_parameters</a> to set the default parameters.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sync-transfer-received\" target=\"_blank\" rel=\"\">sl_bt_evt_sync_transfer_received</a> - Triggered after synchronization transfer is received for a periodic advertising train that does not have subevents or response slots. This event is used only when the application does not include bluetooth_feature_periodic_sync or bluetooth_feature_pawr_sync components.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-periodic-sync-transfer-received\" target=\"_blank\" rel=\"\">sl_bt_evt_periodic_sync_transfer_received</a> - If the application includes the bluetooth_feature_periodic_sync or bluetooth_feature_pawr_sync component, triggered after synchronization transfer is received for a periodic advertising train that does not have subevents or response slots.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-pawr-sync-transfer-received\" target=\"_blank\" rel=\"\">sl_bt_evt_pawr_sync_transfer_received</a> - If the application includes the bluetooth_feature_pawr_sync component, triggered after synchronization transfer is received for a Periodic Advertising with Responses (PAwR) train. </p></li></ul><br><div>Definition at line <code>5305</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_past_receiver_set_sync_receive_parameters<span id=\"sl-bt-past-receiver-set-sync-receive-parameters\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-past-receiver-set-sync-receive-parameters\">#</a></span></h3><blockquote>sl_status_t sl_bt_past_receiver_set_sync_receive_parameters (uint8_t connection, uint8_t mode, uint16_t skip, uint16_t timeout, uint8_t reporting_mode)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle of the connection used to receive the sync transfer </p></td></tr><tr><td>[in]</td><td class=\"paramname\">mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-past-receiver#sl-bt-past-receiver-mode-t\" target=\"_blank\" rel=\"\">sl_bt_past_receiver_mode_t</a>. The mode to specify how the Bluetooth stack reacts when synchronization information is received. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_past_receiver_mode_ignore (0x0):</strong> No attempt is made to synchronize to a periodic advertising train for which the synchronization information was received. No event will be triggered towards the application.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_past_receiver_mode_synchronize (0x1):</strong> Attempt to synchronize to a periodic advertising train for which the synchronization information was received. When the information is received, an event will be triggered to indicate success or failure and to provide the application with the periodic advertising synchronization handle.</p></li></ul><p style=\"color:inherit\">Default: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-past-receiver#sl-bt-past-receiver-mode-ignore\" target=\"_blank\" rel=\"\">sl_bt_past_receiver_mode_ignore</a> (Do not attempt to synchronize) </p></td></tr><tr><td>[in]</td><td class=\"paramname\">skip</td><td><p style=\"color:inherit\">The maximum number of periodic advertising packets that can be skipped after a successful receive.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x0000 to 0x01F3</p></li><li><p style=\"color:inherit\">Default value: 0 </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">timeout</td><td><p style=\"color:inherit\">The maximum permitted time between successful receives. If this time is exceeded, synchronization is lost. Unit: 10 ms.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x0A to 0x4000</p></li><li><p style=\"color:inherit\">Unit: 10 ms</p></li><li><p style=\"color:inherit\">Time range: 100 ms to 163.84 s</p></li><li><p style=\"color:inherit\">Default value: 1000 ms </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">reporting_mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync#sl-bt-sync-reporting-mode-t\" target=\"_blank\" rel=\"\">sl_bt_sync_reporting_mode_t</a>. Specifies the initial mode for reporting data received in the periodic advertising train after it has achieved synchronization. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_sync_report_none (0x0):</strong> Data received in periodic advertising trains is not reported to the application.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_sync_report_all (0x1):</strong> Data received in periodic advertising trains is reported to the application.</p></li></ul><p style=\"color:inherit\">Default: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync#sl-bt-sync-report-all\" target=\"_blank\" rel=\"\">sl_bt_sync_report_all</a> (Data received in periodic advertising trains is reported to the application) </p></td></tr></tbody></table></div><p style=\"color:inherit\">Set the parameters for receiving Periodic Advertising Synchronization Transfers (PAST) over the specified connection. The parameters do not affect periodic advertising trains that the device has already synchronized to.</p><p style=\"color:inherit\">This command sets parameters that do not limit the synchronization based on the CTE type. If the application includes bluetooth_feature_aoa_receiver or bluetooth_feature_aod_receiver component and wants to specify a particular CTE limitation, the application should use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-cte-receiver#sl-bt-cte-receiver-set-sync-receive-parameters\" target=\"_blank\" rel=\"\">sl_bt_cte_receiver_set_sync_receive_parameters</a> to set the parameters.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sync-transfer-received\" target=\"_blank\" rel=\"\">sl_bt_evt_sync_transfer_received</a> - Triggered after synchronization transfer is received for a periodic advertising train that does not have subevents or response slots. This event is used only when the application does not include bluetooth_feature_periodic_sync or bluetooth_feature_pawr_sync components.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-periodic-sync-transfer-received\" target=\"_blank\" rel=\"\">sl_bt_evt_periodic_sync_transfer_received</a> - If the application includes the bluetooth_feature_periodic_sync or bluetooth_feature_pawr_sync component, triggered after synchronization transfer is received for a periodic advertising train that does not have subevents or response slots.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-pawr-sync-transfer-received\" target=\"_blank\" rel=\"\">sl_bt_evt_pawr_sync_transfer_received</a> - If the application includes the bluetooth_feature_pawr_sync component, triggered after synchronization transfer is received for a Periodic Advertising with Responses (PAwR) train. </p></li></ul><br><div>Definition at line <code>5383</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_past_receiver_set_default_sync_receive_parameters_id<span id=\"sl-bt-cmd-past-receiver-set-default-sync-receive-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-past-receiver-set-default-sync-receive-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_past_receiver_set_default_sync_receive_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00510020</pre><br><div>Definition at line <code>5200</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_past_receiver_set_sync_receive_parameters_id<span id=\"sl-bt-cmd-past-receiver-set-sync-receive-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-past-receiver-set-sync-receive-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_past_receiver_set_sync_receive_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01510020</pre><br><div>Definition at line <code>5201</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_past_receiver_set_default_sync_receive_parameters_id<span id=\"sl-bt-rsp-past-receiver-set-default-sync-receive-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-past-receiver-set-default-sync-receive-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_past_receiver_set_default_sync_receive_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00510020</pre><br><div>Definition at line <code>5202</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_past_receiver_set_sync_receive_parameters_id<span id=\"sl-bt-rsp-past-receiver-set-sync-receive-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-past-receiver-set-sync-receive-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_past_receiver_set_sync_receive_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01510020</pre><br><div>Definition at line <code>5203</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-past-receiver", "status": "success"}