{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Synchronization<span id=\"synchronization\" class=\"self-anchor\"><a class=\"perm\" href=\"#synchronization\">#</a></span></h1><p style=\"color:inherit\">Synchronization. </p><p style=\"color:inherit\">Provides the base functionality of periodic advertising synchronization. Use bluetooth_feature_sync_scanner and/or bluetooth_feature_past_receiver components to include the synchronization mechanisms that the application requires. Use bluetooth_feature_periodic_sync to include support for trains that do not have subevents or response slots, and/or bluetooth_feature_pawr_sync to include support for Periodic Advertising with Responses (PAwR) trains.</p><p style=\"color:inherit\">Some functionality in this class is considered <strong>deprecated</strong> and has been superseded by new classes. When one or more of bluetooth_feature_sync_scanner, bluetooth_feature_periodic_sync, or bluetooth_feature_pawr_sync components is included by the application, commands that have been superseded by the new classes are no longer available for use in the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync\" target=\"_blank\" rel=\"\">Synchronization</a> class. Calling them will receive SL_STATUS_NOT_SUPPORTED error code. These commands are as follows:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync#sl-bt-sync-set-parameters\" target=\"_blank\" rel=\"\">sl_bt_sync_set_parameters</a></p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync#sl-bt-sync-open\" target=\"_blank\" rel=\"\">sl_bt_sync_open</a></p></li></ul><p style=\"color:inherit\">See the command descriptions for the replacements.</p><p style=\"color:inherit\">Events that are deprecated and superseded by the new classes are no longer triggered by the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync\" target=\"_blank\" rel=\"\">Synchronization</a> class if any of the new classes are included in the application. See event descriptions for the replacements. </p><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sync-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_sync_opened</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sync-transfer-received\" target=\"_blank\" rel=\"\">sl_bt_evt_sync_transfer_received</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sync-data\" target=\"_blank\" rel=\"\">sl_bt_evt_sync_data</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sync-closed\" target=\"_blank\" rel=\"\">sl_bt_evt_sync_closed</a></p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sync-reporting-mode-t\">sl_bt_sync_reporting_mode_t</a> {</div><div class=\"enum\">sl_bt_sync_report_none = 0x0</div><div class=\"enum\">sl_bt_sync_report_all = 0x1</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Specifies the mode for periodic advertising reports. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sync-advertiser-clock-accuracy-t\">sl_bt_sync_advertiser_clock_accuracy_t</a> {</div><div class=\"enum\">sl_bt_sync_clock_accuracy_500 = 0x1f4</div><div class=\"enum\">sl_bt_sync_clock_accuracy_250 = 0xfa</div><div class=\"enum\">sl_bt_sync_clock_accuracy_150 = 0x96</div><div class=\"enum\">sl_bt_sync_clock_accuracy_100 = 0x64</div><div class=\"enum\">sl_bt_sync_clock_accuracy_75 = 0x4b</div><div class=\"enum\">sl_bt_sync_clock_accuracy_50 = 0x32</div><div class=\"enum\">sl_bt_sync_clock_accuracy_30 = 0x1e</div><div class=\"enum\">sl_bt_sync_clock_accuracy_20 = 0x14</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values indicate the advertiser clock accuracy in a periodic advertising synchronization. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sync-set-parameters\">sl_bt_sync_set_parameters</a>(uint16_t skip, uint16_t timeout, uint32_t flags)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sync-open\">sl_bt_sync_open</a>(bd_addr address, uint8_t address_type, uint8_t adv_sid, uint16_t *sync)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sync-set-reporting-mode\">sl_bt_sync_set_reporting_mode</a>(uint16_t sync, uint8_t reporting_mode)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sync-update-sync-parameters\">sl_bt_sync_update_sync_parameters</a>(uint16_t sync, uint16_t skip, uint16_t timeout)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sync-close\">sl_bt_sync_close</a>(uint16_t sync)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sync-set-parameters-id\">sl_bt_cmd_sync_set_parameters_id</a> 0x02420020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sync-open-id\">sl_bt_cmd_sync_open_id</a> 0x00420020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sync-set-reporting-mode-id\">sl_bt_cmd_sync_set_reporting_mode_id</a> 0x03420020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sync-update-sync-parameters-id\">sl_bt_cmd_sync_update_sync_parameters_id</a> 0x04420020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sync-close-id\">sl_bt_cmd_sync_close_id</a> 0x01420020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sync-set-parameters-id\">sl_bt_rsp_sync_set_parameters_id</a> 0x02420020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sync-open-id\">sl_bt_rsp_sync_open_id</a> 0x00420020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sync-set-reporting-mode-id\">sl_bt_rsp_sync_set_reporting_mode_id</a> 0x03420020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sync-update-sync-parameters-id\">sl_bt_rsp_sync_update_sync_parameters_id</a> 0x04420020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sync-close-id\">sl_bt_rsp_sync_close_id</a> 0x01420020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>sl_bt_sync_reporting_mode_t<span id=\"sl-bt-sync-reporting-mode-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sync-reporting-mode-t\">#</a></span></h3><blockquote>sl_bt_sync_reporting_mode_t</blockquote><p style=\"color:inherit\">Specifies the mode for periodic advertising reports. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_sync_report_none</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Data received in periodic advertising trains is not reported to the application. </p></td></tr><tr><td class=\"fieldname\">sl_bt_sync_report_all</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Data received in periodic advertising trains is reported to the application. </p></td></tr></tbody></table><br><div>Definition at line <code>4627</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sync_advertiser_clock_accuracy_t<span id=\"sl-bt-sync-advertiser-clock-accuracy-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sync-advertiser-clock-accuracy-t\">#</a></span></h3><blockquote>sl_bt_sync_advertiser_clock_accuracy_t</blockquote><p style=\"color:inherit\">These values indicate the advertiser clock accuracy in a periodic advertising synchronization. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_sync_clock_accuracy_500</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1f4) Clock accuracy 500 ppm </p></td></tr><tr><td class=\"fieldname\">sl_bt_sync_clock_accuracy_250</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0xfa) Clock accuracy 250 ppm </p></td></tr><tr><td class=\"fieldname\">sl_bt_sync_clock_accuracy_150</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x96) Clock accuracy 150 ppm </p></td></tr><tr><td class=\"fieldname\">sl_bt_sync_clock_accuracy_100</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x64) Clock accuracy 100 ppm </p></td></tr><tr><td class=\"fieldname\">sl_bt_sync_clock_accuracy_75</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x4b) Clock accuracy 75 ppm </p></td></tr><tr><td class=\"fieldname\">sl_bt_sync_clock_accuracy_50</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x32) Clock accuracy 50 ppm </p></td></tr><tr><td class=\"fieldname\">sl_bt_sync_clock_accuracy_30</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1e) Clock accuracy 30 ppm </p></td></tr><tr><td class=\"fieldname\">sl_bt_sync_clock_accuracy_20</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x14) Clock accuracy 20 ppm </p></td></tr></tbody></table><br><div>Definition at line <code>4639</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_sync_set_parameters<span id=\"sl-bt-sync-set-parameters\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sync-set-parameters\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_sync_set_parameters (uint16_t skip, uint16_t timeout, uint32_t flags)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">skip</td><td><p style=\"color:inherit\">The maximum number of periodic advertising packets that can be skipped after a successful receive.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x0000 to 0x01F3</p></li><li><p style=\"color:inherit\">Default value: 0 </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">timeout</td><td><p style=\"color:inherit\">The maximum permitted time between successful receives. If this time is exceeded, synchronization is lost. Unit: 10 ms.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x0A to 0x4000</p></li><li><p style=\"color:inherit\">Unit: 10 ms</p></li><li><p style=\"color:inherit\">Time range: 100 ms to 163.84 s</p></li><li><p style=\"color:inherit\">Default value: 1000 ms </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">No flags defined currently</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync-scanner#sl-bt-sync-scanner-set-sync-parameters\" target=\"_blank\" rel=\"\">sl_bt_sync_scanner_set_sync_parameters</a>.</p><p style=\"color:inherit\">Configure periodic advertiser synchronization parameters. The specified parameters take effect immediately for all advertisers that have not already established synchronization.</p><p style=\"color:inherit\">The application should determine skip and timeout values based on the periodic advertising interval provided by the advertiser. Ensure that you use a long enough timeout to allow multiple receives. If <code>skip</code> and <code>timeout</code> are used, select appropriate values so that they allow a few receiving attempts. Periodic advertising intervals are reported in event <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-scan-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_scan_report</a>.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>4907</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sync_open<span id=\"sl-bt-sync-open\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sync-open\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_sync_open (<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> address, uint8_t address_type, uint8_t adv_sid, uint16_t * sync)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">address</td><td><p style=\"color:inherit\">Address of the advertiser </p></td></tr><tr><td>[in]</td><td class=\"paramname\">address_type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>.</p><p style=\"color:inherit\">Advertiser address type.</p><p style=\"color:inherit\">If the application does not include the bluetooth_feature_use_accurate_api_address_types component, <code>address_type</code> uses the following values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Public address</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Random address</p></li></ul><p style=\"color:inherit\">If the application includes the bluetooth_feature_use_accurate_api_address_types component, <code>address_type</code> uses enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a> values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address (0x0):</strong> Public device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_resolvable_address (0x2):</strong> Resolvable private random address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_nonresolvable_address (0x3):</strong> Non-resolvable private random address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address_resolved_from_rpa (0x4):</strong> Public identity address resolved from a resolvable private address (RPA)</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address_resolved_from_rpa (0x5):</strong> Static identity address resolved from a resolvable private address (RPA) </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">adv_sid</td><td><p style=\"color:inherit\">Advertising set identifier </p></td></tr><tr><td>[out]</td><td class=\"paramname\">sync</td><td><p style=\"color:inherit\">A handle that will be assigned to the periodic advertising synchronization after the synchronization is established. This handle is valid only if the result code of this response is SL_STATUS_OK.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync-scanner#sl-bt-sync-scanner-open\" target=\"_blank\" rel=\"\">sl_bt_sync_scanner_open</a>.</p><p style=\"color:inherit\">Start establishing synchronization with the specified periodic advertiser in parallel with other advertisers given in previous invocations of this command. The stack will internally enable scanning when needed so that synchronizations can occur. The scanning responses from the internal scanning are not passed to the application unless the application has also enabled scanning.</p><p style=\"color:inherit\">Advertisers that have not already synced before the invocation of this command will be synced using the <code>skip</code> and <code>timeout</code> values configured in the most recent invocation of command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-scan-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_scan_report</a>.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sync-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_sync_opened</a> - Triggered after the synchronization is established.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sync-data\" target=\"_blank\" rel=\"\">sl_bt_evt_sync_data</a> - Indicates that a periodic advertisement packet is received.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sync-closed\" target=\"_blank\" rel=\"\">sl_bt_evt_sync_closed</a> - Triggered after periodic advertising synchronization was lost or explicitly closed, or a synchronization establishment procedure was canceled. </p></li></ul><br><div>Definition at line <code>4969</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sync_set_reporting_mode<span id=\"sl-bt-sync-set-reporting-mode\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sync-set-reporting-mode\">#</a></span></h3><blockquote>sl_status_t sl_bt_sync_set_reporting_mode (uint16_t sync, uint8_t reporting_mode)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">sync</td><td><p style=\"color:inherit\">Periodic advertising synchronization handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">reporting_mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync#sl-bt-sync-reporting-mode-t\" target=\"_blank\" rel=\"\">sl_bt_sync_reporting_mode_t</a>. Specifies the mode for reporting data received in the periodic advertising train. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_sync_report_none (0x0):</strong> Data received in periodic advertising trains is not reported to the application.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_sync_report_all (0x1):</strong> Data received in periodic advertising trains is reported to the application.</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Set data reporting mode of the periodic advertising synchronization.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>4990</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sync_update_sync_parameters<span id=\"sl-bt-sync-update-sync-parameters\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sync-update-sync-parameters\">#</a></span></h3><blockquote>sl_status_t sl_bt_sync_update_sync_parameters (uint16_t sync, uint16_t skip, uint16_t timeout)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">sync</td><td><p style=\"color:inherit\">Periodic advertising synchronization handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">skip</td><td><p style=\"color:inherit\">The maximum number of periodic advertising packets that can be skipped after a successful receive.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x0000 to 0x01F3</p></li><li><p style=\"color:inherit\">Default value: 0 </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">timeout</td><td><p style=\"color:inherit\">The maximum permitted time between successful receives. If this time is exceeded, synchronization is lost. Unit: 10 ms.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x0A to 0x4000</p></li><li><p style=\"color:inherit\">Unit: 10 ms</p></li><li><p style=\"color:inherit\">Time range: 100 ms to 163.84 s</p></li><li><p style=\"color:inherit\">Default value: 1000 ms</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Update synchronization parameters for a periodic sync that was already established.</p><p style=\"color:inherit\">When a sync is established by scanning (see <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync-scanner\" target=\"_blank\" rel=\"\">Periodic Advertising Sync Scanner</a>) or by receiving Periodic Advertising Synchronization Transfer (see <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-past-receiver\" target=\"_blank\" rel=\"\">PAST Receiver</a>), the sync gets the <code>skip</code> and <code>timeout</code> parameters that were configured in the corresponding class. The application can use this command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync#sl-bt-sync-update-sync-parameters\" target=\"_blank\" rel=\"\">sl_bt_sync_update_sync_parameters</a> to update the values of a sync that has been established. The application can for example update the values to better match the actual interval of the periodic advertising train, or to increase the <code>skip</code> value to minimize wakeups when power saving is prioritized over receiving every periodic advertisement.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>5023</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sync_close<span id=\"sl-bt-sync-close\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sync-close\">#</a></span></h3><blockquote>sl_status_t sl_bt_sync_close (uint16_t sync)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">sync</td><td><p style=\"color:inherit\">Periodic advertising synchronization handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Close a periodic advertising synchronization or cancel an ongoing attempt of establishing a synchronization.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sync-closed\" target=\"_blank\" rel=\"\">sl_bt_evt_sync_closed</a> - Triggered after a periodic advertising synchronization has been closed or canceled. </p></li></ul><br><div>Definition at line <code>5041</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_sync_set_parameters_id<span id=\"sl-bt-cmd-sync-set-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sync-set-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sync_set_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02420020</pre><br><div>Definition at line <code>4613</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sync_open_id<span id=\"sl-bt-cmd-sync-open-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sync-open-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sync_open_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00420020</pre><br><div>Definition at line <code>4614</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sync_set_reporting_mode_id<span id=\"sl-bt-cmd-sync-set-reporting-mode-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sync-set-reporting-mode-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sync_set_reporting_mode_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03420020</pre><br><div>Definition at line <code>4615</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sync_update_sync_parameters_id<span id=\"sl-bt-cmd-sync-update-sync-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sync-update-sync-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sync_update_sync_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x04420020</pre><br><div>Definition at line <code>4616</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sync_close_id<span id=\"sl-bt-cmd-sync-close-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sync-close-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sync_close_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01420020</pre><br><div>Definition at line <code>4617</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sync_set_parameters_id<span id=\"sl-bt-rsp-sync-set-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sync-set-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sync_set_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02420020</pre><br><div>Definition at line <code>4618</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sync_open_id<span id=\"sl-bt-rsp-sync-open-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sync-open-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sync_open_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00420020</pre><br><div>Definition at line <code>4619</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sync_set_reporting_mode_id<span id=\"sl-bt-rsp-sync-set-reporting-mode-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sync-set-reporting-mode-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sync_set_reporting_mode_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03420020</pre><br><div>Definition at line <code>4620</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sync_update_sync_parameters_id<span id=\"sl-bt-rsp-sync-update-sync-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sync-update-sync-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sync_update_sync_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x04420020</pre><br><div>Definition at line <code>4621</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sync_close_id<span id=\"sl-bt-rsp-sync-close-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sync-close-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sync_close_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01420020</pre><br><div>Definition at line <code>4622</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync", "status": "success"}