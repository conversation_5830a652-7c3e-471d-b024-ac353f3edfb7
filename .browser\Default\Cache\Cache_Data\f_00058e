(self["webpackChunkfile_viewer"]=self["webpackChunkfile_viewer"]||[]).push([[736],{7518:e=>{e.exports=function(e,n,t){const r=void 0!==e.__vccOpts?e.__vccOpts:e,o=r[n];if(void 0===o)r[n]=t;else for(const a in t)void 0===o[a]&&(o[a]=t[a])}},1959:(e,n,t)=>{"use strict";t.d(n,{Bj:()=>i,qq:()=>v,Fl:()=>We,X3:()=>Le,PG:()=>Pe,dq:()=>$e,Xl:()=>Te,Jd:()=>C,WL:()=>De,qj:()=>xe,iH:()=>Re,lk:()=>P,Um:()=>Se,XI:()=>Ie,IU:()=>Oe,j:()=>j,X$:()=>T,SU:()=>He});var r=t(2323);let o;const a=[];class i{constructor(e=!1){this.active=!0,this.effects=[],this.cleanups=[],!e&&o&&(this.parent=o,this.index=(o.scopes||(o.scopes=[])).push(this)-1)}run(e){if(this.active)try{return this.on(),e()}finally{this.off()}else 0}on(){this.active&&(a.push(this),o=this)}off(){this.active&&(a.pop(),o=a[a.length-1])}stop(e){if(this.active){if(this.effects.forEach((e=>e.stop())),this.cleanups.forEach((e=>e())),this.scopes&&this.scopes.forEach((e=>e.stop(!0))),this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.active=!1}}}function s(e,n){n=n||o,n&&n.active&&n.effects.push(e)}const l=e=>{const n=new Set(e);return n.w=0,n.n=0,n},c=e=>(e.w&h)>0,d=e=>(e.n&h)>0,u=({deps:e})=>{if(e.length)for(let n=0;n<e.length;n++)e[n].w|=h},p=e=>{const{deps:n}=e;if(n.length){let t=0;for(let r=0;r<n.length;r++){const o=n[r];c(o)&&!d(o)?o.delete(e):n[t++]=o,o.w&=~h,o.n&=~h}n.length=t}},m=new WeakMap;let f=0,h=1;const w=30,b=[];let g;const k=Symbol(""),y=Symbol("");class v{constructor(e,n=null,t){this.fn=e,this.scheduler=n,this.active=!0,this.deps=[],s(this,t)}run(){if(!this.active)return this.fn();if(!b.includes(this))try{return b.push(g=this),E(),h=1<<++f,f<=w?u(this):_(this),this.fn()}finally{f<=w&&p(this),h=1<<--f,P(),b.pop();const e=b.length;g=e>0?b[e-1]:void 0}}stop(){this.active&&(_(this),this.onStop&&this.onStop(),this.active=!1)}}function _(e){const{deps:n}=e;if(n.length){for(let t=0;t<n.length;t++)n[t].delete(e);n.length=0}}let x=!0;const S=[];function C(){S.push(x),x=!1}function E(){S.push(x),x=!0}function P(){const e=S.pop();x=void 0===e||e}function j(e,n,t){if(!L())return;let r=m.get(e);r||m.set(e,r=new Map);let o=r.get(t);o||r.set(t,o=l());const a=void 0;O(o,a)}function L(){return x&&void 0!==g}function O(e,n){let t=!1;f<=w?d(e)||(e.n|=h,t=!c(e)):t=!e.has(g),t&&(e.add(g),g.deps.push(e))}function T(e,n,t,o,a,i){const s=m.get(e);if(!s)return;let c=[];if("clear"===n)c=[...s.values()];else if("length"===t&&(0,r.kJ)(e))s.forEach(((e,n)=>{("length"===n||n>=o)&&c.push(e)}));else switch(void 0!==t&&c.push(s.get(t)),n){case"add":(0,r.kJ)(e)?(0,r.S0)(t)&&c.push(s.get("length")):(c.push(s.get(k)),(0,r._N)(e)&&c.push(s.get(y)));break;case"delete":(0,r.kJ)(e)||(c.push(s.get(k)),(0,r._N)(e)&&c.push(s.get(y)));break;case"set":(0,r._N)(e)&&c.push(s.get(k));break}if(1===c.length)c[0]&&M(c[0]);else{const e=[];for(const n of c)n&&e.push(...n);M(l(e))}}function M(e,n){for(const t of(0,r.kJ)(e)?e:[...e])(t!==g||t.allowRecurse)&&(t.scheduler?t.scheduler():t.run())}const A=(0,r.fY)("__proto__,__v_isRef,__isVue"),F=new Set(Object.getOwnPropertyNames(Symbol).map((e=>Symbol[e])).filter(r.yk)),z=q(),$=q(!1,!0),R=q(!0),I=N();function N(){const e={};return["includes","indexOf","lastIndexOf"].forEach((n=>{e[n]=function(...e){const t=Oe(this);for(let n=0,o=this.length;n<o;n++)j(t,"get",n+"");const r=t[n](...e);return-1===r||!1===r?t[n](...e.map(Oe)):r}})),["push","pop","shift","unshift","splice"].forEach((n=>{e[n]=function(...e){C();const t=Oe(this)[n].apply(this,e);return P(),t}})),e}function q(e=!1,n=!1){return function(t,o,a){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_raw"===o&&a===(e?n?ye:ke:n?ge:be).get(t))return t;const i=(0,r.kJ)(t);if(!e&&i&&(0,r.RI)(I,o))return Reflect.get(I,o,a);const s=Reflect.get(t,o,a);if((0,r.yk)(o)?F.has(o):A(o))return s;if(e||j(t,"get",o),n)return s;if($e(s)){const e=!i||!(0,r.S0)(o);return e?s.value:s}return(0,r.Kn)(s)?e?Ce(s):xe(s):s}}const H=D(),B=D(!0);function D(e=!1){return function(n,t,o,a){let i=n[t];if(!e&&(o=Oe(o),i=Oe(i),!(0,r.kJ)(n)&&$e(i)&&!$e(o)))return i.value=o,!0;const s=(0,r.kJ)(n)&&(0,r.S0)(t)?Number(t)<n.length:(0,r.RI)(n,t),l=Reflect.set(n,t,o,a);return n===Oe(a)&&(s?(0,r.aU)(o,i)&&T(n,"set",t,o,i):T(n,"add",t,o)),l}}function U(e,n){const t=(0,r.RI)(e,n),o=e[n],a=Reflect.deleteProperty(e,n);return a&&t&&T(e,"delete",n,void 0,o),a}function W(e,n){const t=Reflect.has(e,n);return(0,r.yk)(n)&&F.has(n)||j(e,"has",n),t}function V(e){return j(e,"iterate",(0,r.kJ)(e)?"length":k),Reflect.ownKeys(e)}const G={get:z,set:H,deleteProperty:U,has:W,ownKeys:V},J={get:R,set(e,n){return!0},deleteProperty(e,n){return!0}},Z=(0,r.l7)({},G,{get:$,set:B}),K=e=>e,Y=e=>Reflect.getPrototypeOf(e);function X(e,n,t=!1,r=!1){e=e["__v_raw"];const o=Oe(e),a=Oe(n);n!==a&&!t&&j(o,"get",n),!t&&j(o,"get",a);const{has:i}=Y(o),s=r?K:t?Ae:Me;return i.call(o,n)?s(e.get(n)):i.call(o,a)?s(e.get(a)):void(e!==o&&e.get(n))}function Q(e,n=!1){const t=this["__v_raw"],r=Oe(t),o=Oe(e);return e!==o&&!n&&j(r,"has",e),!n&&j(r,"has",o),e===o?t.has(e):t.has(e)||t.has(o)}function ee(e,n=!1){return e=e["__v_raw"],!n&&j(Oe(e),"iterate",k),Reflect.get(e,"size",e)}function ne(e){e=Oe(e);const n=Oe(this),t=Y(n),r=t.has.call(n,e);return r||(n.add(e),T(n,"add",e,e)),this}function te(e,n){n=Oe(n);const t=Oe(this),{has:o,get:a}=Y(t);let i=o.call(t,e);i||(e=Oe(e),i=o.call(t,e));const s=a.call(t,e);return t.set(e,n),i?(0,r.aU)(n,s)&&T(t,"set",e,n,s):T(t,"add",e,n),this}function re(e){const n=Oe(this),{has:t,get:r}=Y(n);let o=t.call(n,e);o||(e=Oe(e),o=t.call(n,e));const a=r?r.call(n,e):void 0,i=n.delete(e);return o&&T(n,"delete",e,void 0,a),i}function oe(){const e=Oe(this),n=0!==e.size,t=void 0,r=e.clear();return n&&T(e,"clear",void 0,void 0,t),r}function ae(e,n){return function(t,r){const o=this,a=o["__v_raw"],i=Oe(a),s=n?K:e?Ae:Me;return!e&&j(i,"iterate",k),a.forEach(((e,n)=>t.call(r,s(e),s(n),o)))}}function ie(e,n,t){return function(...o){const a=this["__v_raw"],i=Oe(a),s=(0,r._N)(i),l="entries"===e||e===Symbol.iterator&&s,c="keys"===e&&s,d=a[e](...o),u=t?K:n?Ae:Me;return!n&&j(i,"iterate",c?y:k),{next(){const{value:e,done:n}=d.next();return n?{value:e,done:n}:{value:l?[u(e[0]),u(e[1])]:u(e),done:n}},[Symbol.iterator](){return this}}}}function se(e){return function(...n){return"delete"!==e&&this}}function le(){const e={get(e){return X(this,e)},get size(){return ee(this)},has:Q,add:ne,set:te,delete:re,clear:oe,forEach:ae(!1,!1)},n={get(e){return X(this,e,!1,!0)},get size(){return ee(this)},has:Q,add:ne,set:te,delete:re,clear:oe,forEach:ae(!1,!0)},t={get(e){return X(this,e,!0)},get size(){return ee(this,!0)},has(e){return Q.call(this,e,!0)},add:se("add"),set:se("set"),delete:se("delete"),clear:se("clear"),forEach:ae(!0,!1)},r={get(e){return X(this,e,!0,!0)},get size(){return ee(this,!0)},has(e){return Q.call(this,e,!0)},add:se("add"),set:se("set"),delete:se("delete"),clear:se("clear"),forEach:ae(!0,!0)},o=["keys","values","entries",Symbol.iterator];return o.forEach((o=>{e[o]=ie(o,!1,!1),t[o]=ie(o,!0,!1),n[o]=ie(o,!1,!0),r[o]=ie(o,!0,!0)})),[e,t,n,r]}const[ce,de,ue,pe]=le();function me(e,n){const t=n?e?pe:ue:e?de:ce;return(n,o,a)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?n:Reflect.get((0,r.RI)(t,o)&&o in n?t:n,o,a)}const fe={get:me(!1,!1)},he={get:me(!1,!0)},we={get:me(!0,!1)};const be=new WeakMap,ge=new WeakMap,ke=new WeakMap,ye=new WeakMap;function ve(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function _e(e){return e["__v_skip"]||!Object.isExtensible(e)?0:ve((0,r.W7)(e))}function xe(e){return e&&e["__v_isReadonly"]?e:Ee(e,!1,G,fe,be)}function Se(e){return Ee(e,!1,Z,he,ge)}function Ce(e){return Ee(e,!0,J,we,ke)}function Ee(e,n,t,o,a){if(!(0,r.Kn)(e))return e;if(e["__v_raw"]&&(!n||!e["__v_isReactive"]))return e;const i=a.get(e);if(i)return i;const s=_e(e);if(0===s)return e;const l=new Proxy(e,2===s?o:t);return a.set(e,l),l}function Pe(e){return je(e)?Pe(e["__v_raw"]):!(!e||!e["__v_isReactive"])}function je(e){return!(!e||!e["__v_isReadonly"])}function Le(e){return Pe(e)||je(e)}function Oe(e){const n=e&&e["__v_raw"];return n?Oe(n):e}function Te(e){return(0,r.Nj)(e,"__v_skip",!0),e}const Me=e=>(0,r.Kn)(e)?xe(e):e,Ae=e=>(0,r.Kn)(e)?Ce(e):e;function Fe(e){L()&&(e=Oe(e),e.dep||(e.dep=l()),O(e.dep))}function ze(e,n){e=Oe(e),e.dep&&M(e.dep)}function $e(e){return Boolean(e&&!0===e.__v_isRef)}function Re(e){return Ne(e,!1)}function Ie(e){return Ne(e,!0)}function Ne(e,n){return $e(e)?e:new qe(e,n)}class qe{constructor(e,n){this._shallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?e:Oe(e),this._value=n?e:Me(e)}get value(){return Fe(this),this._value}set value(e){e=this._shallow?e:Oe(e),(0,r.aU)(e,this._rawValue)&&(this._rawValue=e,this._value=this._shallow?e:Me(e),ze(this,e))}}function He(e){return $e(e)?e.value:e}const Be={get:(e,n,t)=>He(Reflect.get(e,n,t)),set:(e,n,t,r)=>{const o=e[n];return $e(o)&&!$e(t)?(o.value=t,!0):Reflect.set(e,n,t,r)}};function De(e){return Pe(e)?e:new Proxy(e,Be)}class Ue{constructor(e,n,t){this._setter=n,this.dep=void 0,this._dirty=!0,this.__v_isRef=!0,this.effect=new v(e,(()=>{this._dirty||(this._dirty=!0,ze(this))})),this["__v_isReadonly"]=t}get value(){const e=Oe(this);return Fe(e),e._dirty&&(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function We(e,n){let t,o;const a=(0,r.mf)(e);a?(t=e,o=r.dG):(t=e.get,o=e.set);const i=new Ue(t,o,a||!o);return i}Promise.resolve()},3673:(e,n,t)=>{"use strict";t.d(n,{P$:()=>C,HY:()=>Ze,xv:()=>Ke,$d:()=>Jn,j4:()=>ln,iD:()=>sn,_:()=>fn,Us:()=>Re,Wm:()=>hn,aZ:()=>M,FN:()=>Tn,Q6:()=>T,h:()=>jt,f3:()=>v,Y3:()=>dt,Jd:()=>J,bv:()=>W,Ah:()=>Z,ic:()=>G,wg:()=>nn,JJ:()=>y,up:()=>We,U2:()=>P,nK:()=>O,Y8:()=>_,YP:()=>xt,w5:()=>u,wy:()=>Oe});var r=t(1959),o=t(2323);new Set;new Map;function a(e,n,...t){const r=e.vnode.props||o.kT;let a=t;const i=n.startsWith("update:"),s=i&&n.slice(7);if(s&&s in r){const e=`${"modelValue"===s?"model":s}Modifiers`,{number:n,trim:i}=r[e]||o.kT;i?a=t.map((e=>e.trim())):n&&(a=t.map(o.He))}let l;let c=r[l=(0,o.hR)(n)]||r[l=(0,o.hR)((0,o._A)(n))];!c&&i&&(c=r[l=(0,o.hR)((0,o.rs)(n))]),c&&Jn(c,e,6,a);const d=r[l+"Once"];if(d){if(e.emitted){if(e.emitted[l])return}else e.emitted={};e.emitted[l]=!0,Jn(d,e,6,a)}}function i(e,n,t=!1){const r=n.emitsCache,a=r.get(e);if(void 0!==a)return a;const s=e.emits;let l={},c=!1;if(!(0,o.mf)(e)){const r=e=>{const t=i(e,n,!0);t&&(c=!0,(0,o.l7)(l,t))};!t&&n.mixins.length&&n.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}return s||c?((0,o.kJ)(s)?s.forEach((e=>l[e]=null)):(0,o.l7)(l,s),r.set(e,l),l):(r.set(e,null),null)}function s(e,n){return!(!e||!(0,o.F7)(n))&&(n=n.slice(2).replace(/Once$/,""),(0,o.RI)(e,n[0].toLowerCase()+n.slice(1))||(0,o.RI)(e,(0,o.rs)(n))||(0,o.RI)(e,n))}let l=null,c=null;function d(e){const n=l;return l=e,c=e&&e.type.__scopeId||null,n}function u(e,n=l,t){if(!n)return e;if(e._n)return e;const r=(...t)=>{r._d&&on(-1);const o=d(n),a=e(...t);return d(o),r._d&&on(1),a};return r._n=!0,r._c=!0,r._d=!0,r}function p(e){const{type:n,vnode:t,proxy:r,withProxy:a,props:i,propsOptions:[s],slots:l,attrs:c,emit:u,render:p,renderCache:h,data:w,setupState:b,ctx:g,inheritAttrs:k}=e;let y,v;const _=d(e);try{if(4&t.shapeFlag){const e=a||r;y=yn(p.call(e,e,h,i,b,w,g)),v=c}else{const e=n;0,y=yn(e.length>1?e(i,{attrs:c,slots:l,emit:u}):e(i,null)),v=n.props?c:m(c)}}catch(S){Qe.length=0,Zn(S,e,1),y=hn(Ye)}let x=y;if(v&&!1!==k){const e=Object.keys(v),{shapeFlag:n}=x;e.length&&7&n&&(s&&e.some(o.tR)&&(v=f(v,s)),x=gn(x,v))}return t.dirs&&(x.dirs=x.dirs?x.dirs.concat(t.dirs):t.dirs),t.transition&&(x.transition=t.transition),y=x,d(_),y}const m=e=>{let n;for(const t in e)("class"===t||"style"===t||(0,o.F7)(t))&&((n||(n={}))[t]=e[t]);return n},f=(e,n)=>{const t={};for(const r in e)(0,o.tR)(r)&&r.slice(9)in n||(t[r]=e[r]);return t};function h(e,n,t){const{props:r,children:o,component:a}=e,{props:i,children:l,patchFlag:c}=n,d=a.emitsOptions;if(n.dirs||n.transition)return!0;if(!(t&&c>=0))return!(!o&&!l||l&&l.$stable)||r!==i&&(r?!i||w(r,i,d):!!i);if(1024&c)return!0;if(16&c)return r?w(r,i,d):!!i;if(8&c){const e=n.dynamicProps;for(let n=0;n<e.length;n++){const t=e[n];if(i[t]!==r[t]&&!s(d,t))return!0}}return!1}function w(e,n,t){const r=Object.keys(n);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const a=r[o];if(n[a]!==e[a]&&!s(t,a))return!0}return!1}function b({vnode:e,parent:n},t){while(n&&n.subTree===e)(e=n.vnode).el=t,n=n.parent}const g=e=>e.__isSuspense;function k(e,n){n&&n.pendingBranch?(0,o.kJ)(e)?n.effects.push(...e):n.effects.push(e):bt(e)}function y(e,n){if(On){let t=On.provides;const r=On.parent&&On.parent.provides;r===t&&(t=On.provides=Object.create(r)),t[e]=n}else 0}function v(e,n,t=!1){const r=On||l;if(r){const a=null==r.parent?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides;if(a&&e in a)return a[e];if(arguments.length>1)return t&&(0,o.mf)(n)?n.call(r.proxy):n}else 0}function _(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return W((()=>{e.isMounted=!0})),J((()=>{e.isUnmounting=!0})),e}const x=[Function,Array],S={name:"BaseTransition",props:{mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:x,onEnter:x,onAfterEnter:x,onEnterCancelled:x,onBeforeLeave:x,onLeave:x,onAfterLeave:x,onLeaveCancelled:x,onBeforeAppear:x,onAppear:x,onAfterAppear:x,onAppearCancelled:x},setup(e,{slots:n}){const t=Tn(),o=_();let a;return()=>{const i=n.default&&T(n.default(),!0);if(!i||!i.length)return;const s=(0,r.IU)(e),{mode:l}=s;const c=i[0];if(o.isLeaving)return j(c);const d=L(c);if(!d)return j(c);const u=P(d,s,o,t);O(d,u);const p=t.subTree,m=p&&L(p);let f=!1;const{getTransitionKey:h}=d.type;if(h){const e=h();void 0===a?a=e:e!==a&&(a=e,f=!0)}if(m&&m.type!==Ye&&(!dn(d,m)||f)){const e=P(m,s,o,t);if(O(m,e),"out-in"===l)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,t.update()},j(c);"in-out"===l&&d.type!==Ye&&(e.delayLeave=(e,n,t)=>{const r=E(o,m);r[String(m.key)]=m,e._leaveCb=()=>{n(),e._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=t})}return c}}},C=S;function E(e,n){const{leavingVNodes:t}=e;let r=t.get(n.type);return r||(r=Object.create(null),t.set(n.type,r)),r}function P(e,n,t,r){const{appear:o,mode:a,persisted:i=!1,onBeforeEnter:s,onEnter:l,onAfterEnter:c,onEnterCancelled:d,onBeforeLeave:u,onLeave:p,onAfterLeave:m,onLeaveCancelled:f,onBeforeAppear:h,onAppear:w,onAfterAppear:b,onAppearCancelled:g}=n,k=String(e.key),y=E(t,e),v=(e,n)=>{e&&Jn(e,r,9,n)},_={mode:a,persisted:i,beforeEnter(n){let r=s;if(!t.isMounted){if(!o)return;r=h||s}n._leaveCb&&n._leaveCb(!0);const a=y[k];a&&dn(e,a)&&a.el._leaveCb&&a.el._leaveCb(),v(r,[n])},enter(e){let n=l,r=c,a=d;if(!t.isMounted){if(!o)return;n=w||l,r=b||c,a=g||d}let i=!1;const s=e._enterCb=n=>{i||(i=!0,v(n?a:r,[e]),_.delayedLeave&&_.delayedLeave(),e._enterCb=void 0)};n?(n(e,s),n.length<=1&&s()):s()},leave(n,r){const o=String(e.key);if(n._enterCb&&n._enterCb(!0),t.isUnmounting)return r();v(u,[n]);let a=!1;const i=n._leaveCb=t=>{a||(a=!0,r(),v(t?f:m,[n]),n._leaveCb=void 0,y[o]===e&&delete y[o])};y[o]=e,p?(p(n,i),p.length<=1&&i()):i()},clone(e){return P(e,n,t,r)}};return _}function j(e){if(F(e))return e=gn(e),e.children=null,e}function L(e){return F(e)?e.children?e.children[0]:void 0:e}function O(e,n){6&e.shapeFlag&&e.component?O(e.component.subTree,n):128&e.shapeFlag?(e.ssContent.transition=n.clone(e.ssContent),e.ssFallback.transition=n.clone(e.ssFallback)):e.transition=n}function T(e,n=!1){let t=[],r=0;for(let o=0;o<e.length;o++){const a=e[o];a.type===Ze?(128&a.patchFlag&&r++,t=t.concat(T(a.children,n))):(n||a.type!==Ye)&&t.push(a)}if(r>1)for(let o=0;o<t.length;o++)t[o].patchFlag=-2;return t}function M(e){return(0,o.mf)(e)?{setup:e,name:e.name}:e}const A=e=>!!e.type.__asyncLoader;const F=e=>e.type.__isKeepAlive;RegExp,RegExp;function z(e,n){return(0,o.kJ)(e)?e.some((e=>z(e,n))):(0,o.HD)(e)?e.split(",").indexOf(n)>-1:!!e.test&&e.test(n)}function $(e,n){I(e,"a",n)}function R(e,n){I(e,"da",n)}function I(e,n,t=On){const r=e.__wdc||(e.__wdc=()=>{let n=t;while(n){if(n.isDeactivated)return;n=n.parent}e()});if(B(n,r,t),t){let e=t.parent;while(e&&e.parent)F(e.parent.vnode)&&N(r,n,t,e),e=e.parent}}function N(e,n,t,r){const a=B(n,e,r,!0);Z((()=>{(0,o.Od)(r[n],a)}),t)}function q(e){let n=e.shapeFlag;256&n&&(n-=256),512&n&&(n-=512),e.shapeFlag=n}function H(e){return 128&e.shapeFlag?e.ssContent:e}function B(e,n,t=On,o=!1){if(t){const a=t[e]||(t[e]=[]),i=n.__weh||(n.__weh=(...o)=>{if(t.isUnmounted)return;(0,r.Jd)(),Mn(t);const a=Jn(n,t,e,o);return An(),(0,r.lk)(),a});return o?a.unshift(i):a.push(i),i}}const D=e=>(n,t=On)=>(!Rn||"sp"===e)&&B(e,n,t),U=D("bm"),W=D("m"),V=D("bu"),G=D("u"),J=D("bum"),Z=D("um"),K=D("sp"),Y=D("rtg"),X=D("rtc");function Q(e,n=On){B("ec",e,n)}let ee=!0;function ne(e){const n=ae(e),t=e.proxy,a=e.ctx;ee=!1,n.beforeCreate&&re(n.beforeCreate,e,"bc");const{data:i,computed:s,methods:l,watch:c,provide:d,inject:u,created:p,beforeMount:m,mounted:f,beforeUpdate:h,updated:w,activated:b,deactivated:g,beforeDestroy:k,beforeUnmount:v,destroyed:_,unmounted:x,render:S,renderTracked:C,renderTriggered:E,errorCaptured:P,serverPrefetch:j,expose:L,inheritAttrs:O,components:T,directives:M,filters:A}=n,F=null;if(u&&te(u,a,F,e.appContext.config.unwrapInjectedRef),l)for(const r in l){const e=l[r];(0,o.mf)(e)&&(a[r]=e.bind(t))}if(i){0;const n=i.call(t,t);0,(0,o.Kn)(n)&&(e.data=(0,r.qj)(n))}if(ee=!0,s)for(const y in s){const e=s[y],n=(0,o.mf)(e)?e.bind(t,t):(0,o.mf)(e.get)?e.get.bind(t,t):o.dG;0;const i=!(0,o.mf)(e)&&(0,o.mf)(e.set)?e.set.bind(t):o.dG,l=(0,r.Fl)({get:n,set:i});Object.defineProperty(a,y,{enumerable:!0,configurable:!0,get:()=>l.value,set:e=>l.value=e})}if(c)for(const r in c)oe(c[r],a,t,r);if(d){const e=(0,o.mf)(d)?d.call(t):d;Reflect.ownKeys(e).forEach((n=>{y(n,e[n])}))}function z(e,n){(0,o.kJ)(n)?n.forEach((n=>e(n.bind(t)))):n&&e(n.bind(t))}if(p&&re(p,e,"c"),z(U,m),z(W,f),z(V,h),z(G,w),z($,b),z(R,g),z(Q,P),z(X,C),z(Y,E),z(J,v),z(Z,x),z(K,j),(0,o.kJ)(L))if(L.length){const n=e.exposed||(e.exposed={});L.forEach((e=>{Object.defineProperty(n,e,{get:()=>t[e],set:n=>t[e]=n})}))}else e.exposed||(e.exposed={});S&&e.render===o.dG&&(e.render=S),null!=O&&(e.inheritAttrs=O),T&&(e.components=T),M&&(e.directives=M)}function te(e,n,t=o.dG,a=!1){(0,o.kJ)(e)&&(e=de(e));for(const i in e){const t=e[i];let s;s=(0,o.Kn)(t)?"default"in t?v(t.from||i,t.default,!0):v(t.from||i):v(t),(0,r.dq)(s)&&a?Object.defineProperty(n,i,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e}):n[i]=s}}function re(e,n,t){Jn((0,o.kJ)(e)?e.map((e=>e.bind(n.proxy))):e.bind(n.proxy),n,t)}function oe(e,n,t,r){const a=r.includes(".")?Et(t,r):()=>t[r];if((0,o.HD)(e)){const t=n[e];(0,o.mf)(t)&&xt(a,t)}else if((0,o.mf)(e))xt(a,e.bind(t));else if((0,o.Kn)(e))if((0,o.kJ)(e))e.forEach((e=>oe(e,n,t,r)));else{const r=(0,o.mf)(e.handler)?e.handler.bind(t):n[e.handler];(0,o.mf)(r)&&xt(a,r,e)}else 0}function ae(e){const n=e.type,{mixins:t,extends:r}=n,{mixins:o,optionsCache:a,config:{optionMergeStrategies:i}}=e.appContext,s=a.get(n);let l;return s?l=s:o.length||t||r?(l={},o.length&&o.forEach((e=>ie(l,e,i,!0))),ie(l,n,i)):l=n,a.set(n,l),l}function ie(e,n,t,r=!1){const{mixins:o,extends:a}=n;a&&ie(e,a,t,!0),o&&o.forEach((n=>ie(e,n,t,!0)));for(const i in n)if(r&&"expose"===i);else{const r=se[i]||t&&t[i];e[i]=r?r(e[i],n[i]):n[i]}return e}const se={data:le,props:pe,emits:pe,methods:pe,computed:pe,beforeCreate:ue,created:ue,beforeMount:ue,mounted:ue,beforeUpdate:ue,updated:ue,beforeDestroy:ue,beforeUnmount:ue,destroyed:ue,unmounted:ue,activated:ue,deactivated:ue,errorCaptured:ue,serverPrefetch:ue,components:pe,directives:pe,watch:me,provide:le,inject:ce};function le(e,n){return n?e?function(){return(0,o.l7)((0,o.mf)(e)?e.call(this,this):e,(0,o.mf)(n)?n.call(this,this):n)}:n:e}function ce(e,n){return pe(de(e),de(n))}function de(e){if((0,o.kJ)(e)){const n={};for(let t=0;t<e.length;t++)n[e[t]]=e[t];return n}return e}function ue(e,n){return e?[...new Set([].concat(e,n))]:n}function pe(e,n){return e?(0,o.l7)((0,o.l7)(Object.create(null),e),n):n}function me(e,n){if(!e)return n;if(!n)return e;const t=(0,o.l7)(Object.create(null),e);for(const r in n)t[r]=ue(e[r],n[r]);return t}function fe(e,n,t,a=!1){const i={},s={};(0,o.Nj)(s,un,1),e.propsDefaults=Object.create(null),we(e,n,i,s);for(const r in e.propsOptions[0])r in i||(i[r]=void 0);t?e.props=a?i:(0,r.Um)(i):e.type.props?e.props=i:e.props=s,e.attrs=s}function he(e,n,t,a){const{props:i,attrs:s,vnode:{patchFlag:l}}=e,c=(0,r.IU)(i),[d]=e.propsOptions;let u=!1;if(!(a||l>0)||16&l){let r;we(e,n,i,s)&&(u=!0);for(const a in c)n&&((0,o.RI)(n,a)||(r=(0,o.rs)(a))!==a&&(0,o.RI)(n,r))||(d?!t||void 0===t[a]&&void 0===t[r]||(i[a]=be(d,c,a,void 0,e,!0)):delete i[a]);if(s!==c)for(const e in s)n&&(0,o.RI)(n,e)||(delete s[e],u=!0)}else if(8&l){const t=e.vnode.dynamicProps;for(let r=0;r<t.length;r++){let a=t[r];const l=n[a];if(d)if((0,o.RI)(s,a))l!==s[a]&&(s[a]=l,u=!0);else{const n=(0,o._A)(a);i[n]=be(d,c,n,l,e,!1)}else l!==s[a]&&(s[a]=l,u=!0)}}u&&(0,r.X$)(e,"set","$attrs")}function we(e,n,t,a){const[i,l]=e.propsOptions;let c,d=!1;if(n)for(let r in n){if((0,o.Gg)(r))continue;const u=n[r];let p;i&&(0,o.RI)(i,p=(0,o._A)(r))?l&&l.includes(p)?(c||(c={}))[p]=u:t[p]=u:s(e.emitsOptions,r)||u!==a[r]&&(a[r]=u,d=!0)}if(l){const n=(0,r.IU)(t),a=c||o.kT;for(let r=0;r<l.length;r++){const s=l[r];t[s]=be(i,n,s,a[s],e,!(0,o.RI)(a,s))}}return d}function be(e,n,t,r,a,i){const s=e[t];if(null!=s){const e=(0,o.RI)(s,"default");if(e&&void 0===r){const e=s.default;if(s.type!==Function&&(0,o.mf)(e)){const{propsDefaults:o}=a;t in o?r=o[t]:(Mn(a),r=o[t]=e.call(null,n),An())}else r=e}s[0]&&(i&&!e?r=!1:!s[1]||""!==r&&r!==(0,o.rs)(t)||(r=!0))}return r}function ge(e,n,t=!1){const r=n.propsCache,a=r.get(e);if(a)return a;const i=e.props,s={},l=[];let c=!1;if(!(0,o.mf)(e)){const r=e=>{c=!0;const[t,r]=ge(e,n,!0);(0,o.l7)(s,t),r&&l.push(...r)};!t&&n.mixins.length&&n.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}if(!i&&!c)return r.set(e,o.Z6),o.Z6;if((0,o.kJ)(i))for(let u=0;u<i.length;u++){0;const e=(0,o._A)(i[u]);ke(e)&&(s[e]=o.kT)}else if(i){0;for(const e in i){const n=(0,o._A)(e);if(ke(n)){const t=i[e],r=s[n]=(0,o.kJ)(t)||(0,o.mf)(t)?{type:t}:t;if(r){const e=_e(Boolean,r.type),t=_e(String,r.type);r[0]=e>-1,r[1]=t<0||e<t,(e>-1||(0,o.RI)(r,"default"))&&l.push(n)}}}}const d=[s,l];return r.set(e,d),d}function ke(e){return"$"!==e[0]}function ye(e){const n=e&&e.toString().match(/^\s*function (\w+)/);return n?n[1]:null===e?"null":""}function ve(e,n){return ye(e)===ye(n)}function _e(e,n){return(0,o.kJ)(n)?n.findIndex((n=>ve(n,e))):(0,o.mf)(n)&&ve(n,e)?0:-1}const xe=e=>"_"===e[0]||"$stable"===e,Se=e=>(0,o.kJ)(e)?e.map(yn):[yn(e)],Ce=(e,n,t)=>{const r=u(((...e)=>Se(n(...e))),t);return r._c=!1,r},Ee=(e,n,t)=>{const r=e._ctx;for(const a in e){if(xe(a))continue;const t=e[a];if((0,o.mf)(t))n[a]=Ce(a,t,r);else if(null!=t){0;const e=Se(t);n[a]=()=>e}}},Pe=(e,n)=>{const t=Se(n);e.slots.default=()=>t},je=(e,n)=>{if(32&e.vnode.shapeFlag){const t=n._;t?(e.slots=(0,r.IU)(n),(0,o.Nj)(n,"_",t)):Ee(n,e.slots={})}else e.slots={},n&&Pe(e,n);(0,o.Nj)(e.slots,un,1)},Le=(e,n,t)=>{const{vnode:r,slots:a}=e;let i=!0,s=o.kT;if(32&r.shapeFlag){const e=n._;e?t&&1===e?i=!1:((0,o.l7)(a,n),t||1!==e||delete a._):(i=!n.$stable,Ee(n,a)),s=n}else n&&(Pe(e,n),s={default:1});if(i)for(const o in a)xe(o)||o in s||delete a[o]};function Oe(e,n){const t=l;if(null===t)return e;const r=t.proxy,a=e.dirs||(e.dirs=[]);for(let i=0;i<n.length;i++){let[e,t,s,l=o.kT]=n[i];(0,o.mf)(e)&&(e={mounted:e,updated:e}),e.deep&&Pt(t),a.push({dir:e,instance:r,value:t,oldValue:void 0,arg:s,modifiers:l})}return e}function Te(e,n,t,o){const a=e.dirs,i=n&&n.dirs;for(let s=0;s<a.length;s++){const l=a[s];i&&(l.oldValue=i[s].value);let c=l.dir[o];c&&((0,r.Jd)(),Jn(c,t,8,[e.el,l,e,n]),(0,r.lk)())}}function Me(){return{app:null,config:{isNativeTag:o.NO,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Ae=0;function Fe(e,n){return function(t,r=null){null==r||(0,o.Kn)(r)||(r=null);const a=Me(),i=new Set;let s=!1;const l=a.app={_uid:Ae++,_component:t,_props:r,_container:null,_context:a,_instance:null,version:Lt,get config(){return a.config},set config(e){0},use(e,...n){return i.has(e)||(e&&(0,o.mf)(e.install)?(i.add(e),e.install(l,...n)):(0,o.mf)(e)&&(i.add(e),e(l,...n))),l},mixin(e){return a.mixins.includes(e)||a.mixins.push(e),l},component(e,n){return n?(a.components[e]=n,l):a.components[e]},directive(e,n){return n?(a.directives[e]=n,l):a.directives[e]},mount(o,i,c){if(!s){const d=hn(t,r);return d.appContext=a,i&&n?n(d,o):e(d,o,c),s=!0,l._container=o,o.__vue_app__=l,Un(d.component)||d.component.proxy}},unmount(){s&&(e(null,l._container),delete l._container.__vue_app__)},provide(e,n){return a.provides[e]=n,l}};return l}}function ze(){}const $e=k;function Re(e){return Ie(e)}function Ie(e,n){ze();const t=(0,o.E9)();t.__VUE__=!0;const{insert:a,remove:i,patchProp:s,createElement:l,createText:c,createComment:d,setText:u,setElementText:m,parentNode:f,nextSibling:w,setScopeId:g=o.dG,cloneNode:k,insertStaticContent:y}=e,v=(e,n,t,r=null,o=null,a=null,i=!1,s=null,l=!!n.dynamicChildren)=>{if(e===n)return;e&&!dn(e,n)&&(r=Y(e),V(e,o,a,!0),e=null),-2===n.patchFlag&&(l=!1,n.dynamicChildren=null);const{type:c,ref:d,shapeFlag:u}=n;switch(c){case Ke:_(e,n,t,r);break;case Ye:x(e,n,t,r);break;case Xe:null==e&&S(n,t,r,i);break;case Ze:$(e,n,t,r,o,a,i,s,l);break;default:1&u?P(e,n,t,r,o,a,i,s,l):6&u?R(e,n,t,r,o,a,i,s,l):(64&u||128&u)&&c.process(e,n,t,r,o,a,i,s,l,Q)}null!=d&&o&&Ne(d,e&&e.ref,a,n||e,!n)},_=(e,n,t,r)=>{if(null==e)a(n.el=c(n.children),t,r);else{const t=n.el=e.el;n.children!==e.children&&u(t,n.children)}},x=(e,n,t,r)=>{null==e?a(n.el=d(n.children||""),t,r):n.el=e.el},S=(e,n,t,r)=>{[e.el,e.anchor]=y(e.children,n,t,r)},C=({el:e,anchor:n},t,r)=>{let o;while(e&&e!==n)o=w(e),a(e,t,r),e=o;a(n,t,r)},E=({el:e,anchor:n})=>{let t;while(e&&e!==n)t=w(e),i(e),e=t;i(n)},P=(e,n,t,r,o,a,i,s,l)=>{i=i||"svg"===n.type,null==e?j(n,t,r,o,a,i,s,l):T(e,n,o,a,i,s,l)},j=(e,n,t,r,i,c,d,u)=>{let p,f;const{type:h,props:w,shapeFlag:b,transition:g,patchFlag:y,dirs:v}=e;if(e.el&&void 0!==k&&-1===y)p=e.el=k(e.el);else{if(p=e.el=l(e.type,c,w&&w.is,w),8&b?m(p,e.children):16&b&&O(e.children,p,null,r,i,c&&"foreignObject"!==h,d,u),v&&Te(e,null,r,"created"),w){for(const n in w)"value"===n||(0,o.Gg)(n)||s(p,n,null,w[n],c,e.children,r,i,K);"value"in w&&s(p,"value",null,w.value),(f=w.onVnodeBeforeMount)&&qe(f,r,e)}L(p,e,e.scopeId,d,r)}v&&Te(e,null,r,"beforeMount");const _=(!i||i&&!i.pendingBranch)&&g&&!g.persisted;_&&g.beforeEnter(p),a(p,n,t),((f=w&&w.onVnodeMounted)||_||v)&&$e((()=>{f&&qe(f,r,e),_&&g.enter(p),v&&Te(e,null,r,"mounted")}),i)},L=(e,n,t,r,o)=>{if(t&&g(e,t),r)for(let a=0;a<r.length;a++)g(e,r[a]);if(o){let t=o.subTree;if(n===t){const n=o.vnode;L(e,n,n.scopeId,n.slotScopeIds,o.parent)}}},O=(e,n,t,r,o,a,i,s,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=s?vn(e[c]):yn(e[c]);v(null,l,n,t,r,o,a,i,s)}},T=(e,n,t,r,a,i,l)=>{const c=n.el=e.el;let{patchFlag:d,dynamicChildren:u,dirs:p}=n;d|=16&e.patchFlag;const f=e.props||o.kT,h=n.props||o.kT;let w;(w=h.onVnodeBeforeUpdate)&&qe(w,t,n,e),p&&Te(n,e,t,"beforeUpdate");const b=a&&"foreignObject"!==n.type;if(u?M(e.dynamicChildren,u,c,t,r,b,i):l||B(e,n,c,null,t,r,b,i,!1),d>0){if(16&d)z(c,n,f,h,t,r,a);else if(2&d&&f.class!==h.class&&s(c,"class",null,h.class,a),4&d&&s(c,"style",f.style,h.style,a),8&d){const o=n.dynamicProps;for(let n=0;n<o.length;n++){const i=o[n],l=f[i],d=h[i];d===l&&"value"!==i||s(c,i,l,d,a,e.children,t,r,K)}}1&d&&e.children!==n.children&&m(c,n.children)}else l||null!=u||z(c,n,f,h,t,r,a);((w=h.onVnodeUpdated)||p)&&$e((()=>{w&&qe(w,t,n,e),p&&Te(n,e,t,"updated")}),r)},M=(e,n,t,r,o,a,i)=>{for(let s=0;s<n.length;s++){const l=e[s],c=n[s],d=l.el&&(l.type===Ze||!dn(l,c)||70&l.shapeFlag)?f(l.el):t;v(l,c,d,null,r,o,a,i,!0)}},z=(e,n,t,r,a,i,l)=>{if(t!==r){for(const c in r){if((0,o.Gg)(c))continue;const d=r[c],u=t[c];d!==u&&"value"!==c&&s(e,c,u,d,l,n.children,a,i,K)}if(t!==o.kT)for(const c in t)(0,o.Gg)(c)||c in r||s(e,c,t[c],null,l,n.children,a,i,K);"value"in r&&s(e,"value",t.value,r.value)}},$=(e,n,t,r,o,i,s,l,d)=>{const u=n.el=e?e.el:c(""),p=n.anchor=e?e.anchor:c("");let{patchFlag:m,dynamicChildren:f,slotScopeIds:h}=n;h&&(l=l?l.concat(h):h),null==e?(a(u,t,r),a(p,t,r),O(n.children,t,p,o,i,s,l,d)):m>0&&64&m&&f&&e.dynamicChildren?(M(e.dynamicChildren,f,t,o,i,s,l),(null!=n.key||o&&n===o.subTree)&&He(e,n,!0)):B(e,n,t,p,o,i,s,l,d)},R=(e,n,t,r,o,a,i,s,l)=>{n.slotScopeIds=s,null==e?512&n.shapeFlag?o.ctx.activate(n,t,r,i,l):I(n,t,r,o,a,i,l):N(e,n,l)},I=(e,n,t,r,o,a,i)=>{const s=e.component=Ln(e,r,o);if(F(e)&&(s.ctx.renderer=Q),In(s),s.asyncDep){if(o&&o.registerDep(s,q),!e.el){const e=s.subTree=hn(Ye);x(null,e,n,t)}}else q(s,e,n,t,o,a,i)},N=(e,n,t)=>{const r=n.component=e.component;if(h(e,n,t)){if(r.asyncDep&&!r.asyncResolved)return void H(r,n,t);r.next=n,ft(r.update),r.update()}else n.component=e.component,n.el=e.el,r.vnode=n},q=(e,n,t,a,i,s,l)=>{const c=()=>{if(e.isMounted){let n,{next:t,bu:r,u:a,parent:c,vnode:u}=e,m=t;0,d.allowRecurse=!1,t?(t.el=u.el,H(e,t,l)):t=u,r&&(0,o.ir)(r),(n=t.props&&t.props.onVnodeBeforeUpdate)&&qe(n,c,t,u),d.allowRecurse=!0;const h=p(e);0;const w=e.subTree;e.subTree=h,v(w,h,f(w.el),Y(w),e,i,s),t.el=h.el,null===m&&b(e,h.el),a&&$e(a,i),(n=t.props&&t.props.onVnodeUpdated)&&$e((()=>qe(n,c,t,u)),i)}else{let r;const{el:l,props:c}=n,{bm:u,m,parent:f}=e,h=A(n);if(d.allowRecurse=!1,u&&(0,o.ir)(u),!h&&(r=c&&c.onVnodeBeforeMount)&&qe(r,f,n),d.allowRecurse=!0,l&&ne){const t=()=>{e.subTree=p(e),ne(l,e.subTree,e,i,null)};h?n.type.__asyncLoader().then((()=>!e.isUnmounted&&t())):t()}else{0;const r=e.subTree=p(e);0,v(null,r,t,a,e,i,s),n.el=r.el}if(m&&$e(m,i),!h&&(r=c&&c.onVnodeMounted)){const e=n;$e((()=>qe(r,f,e)),i)}256&n.shapeFlag&&e.a&&$e(e.a,i),e.isMounted=!0,n=t=a=null}},d=new r.qq(c,(()=>pt(e.update)),e.scope),u=e.update=d.run.bind(d);u.id=e.uid,d.allowRecurse=u.allowRecurse=!0,u()},H=(e,n,t)=>{n.component=e;const o=e.vnode.props;e.vnode=n,e.next=null,he(e,n.props,o,t),Le(e,n.children,t),(0,r.Jd)(),gt(void 0,e.update),(0,r.lk)()},B=(e,n,t,r,o,a,i,s,l=!1)=>{const c=e&&e.children,d=e?e.shapeFlag:0,u=n.children,{patchFlag:p,shapeFlag:f}=n;if(p>0){if(128&p)return void U(c,u,t,r,o,a,i,s,l);if(256&p)return void D(c,u,t,r,o,a,i,s,l)}8&f?(16&d&&K(c,o,a),u!==c&&m(t,u)):16&d?16&f?U(c,u,t,r,o,a,i,s,l):K(c,o,a,!0):(8&d&&m(t,""),16&f&&O(u,t,r,o,a,i,s,l))},D=(e,n,t,r,a,i,s,l,c)=>{e=e||o.Z6,n=n||o.Z6;const d=e.length,u=n.length,p=Math.min(d,u);let m;for(m=0;m<p;m++){const r=n[m]=c?vn(n[m]):yn(n[m]);v(e[m],r,t,null,a,i,s,l,c)}d>u?K(e,a,i,!0,!1,p):O(n,t,r,a,i,s,l,c,p)},U=(e,n,t,r,a,i,s,l,c)=>{let d=0;const u=n.length;let p=e.length-1,m=u-1;while(d<=p&&d<=m){const r=e[d],o=n[d]=c?vn(n[d]):yn(n[d]);if(!dn(r,o))break;v(r,o,t,null,a,i,s,l,c),d++}while(d<=p&&d<=m){const r=e[p],o=n[m]=c?vn(n[m]):yn(n[m]);if(!dn(r,o))break;v(r,o,t,null,a,i,s,l,c),p--,m--}if(d>p){if(d<=m){const e=m+1,o=e<u?n[e].el:r;while(d<=m)v(null,n[d]=c?vn(n[d]):yn(n[d]),t,o,a,i,s,l,c),d++}}else if(d>m)while(d<=p)V(e[d],a,i,!0),d++;else{const f=d,h=d,w=new Map;for(d=h;d<=m;d++){const e=n[d]=c?vn(n[d]):yn(n[d]);null!=e.key&&w.set(e.key,d)}let b,g=0;const k=m-h+1;let y=!1,_=0;const x=new Array(k);for(d=0;d<k;d++)x[d]=0;for(d=f;d<=p;d++){const r=e[d];if(g>=k){V(r,a,i,!0);continue}let o;if(null!=r.key)o=w.get(r.key);else for(b=h;b<=m;b++)if(0===x[b-h]&&dn(r,n[b])){o=b;break}void 0===o?V(r,a,i,!0):(x[o-h]=d+1,o>=_?_=o:y=!0,v(r,n[o],t,null,a,i,s,l,c),g++)}const S=y?Be(x):o.Z6;for(b=S.length-1,d=k-1;d>=0;d--){const e=h+d,o=n[e],p=e+1<u?n[e+1].el:r;0===x[d]?v(null,o,t,p,a,i,s,l,c):y&&(b<0||d!==S[b]?W(o,t,p,2):b--)}}},W=(e,n,t,r,o=null)=>{const{el:i,type:s,transition:l,children:c,shapeFlag:d}=e;if(6&d)return void W(e.component.subTree,n,t,r);if(128&d)return void e.suspense.move(n,t,r);if(64&d)return void s.move(e,n,t,Q);if(s===Ze){a(i,n,t);for(let e=0;e<c.length;e++)W(c[e],n,t,r);return void a(e.anchor,n,t)}if(s===Xe)return void C(e,n,t);const u=2!==r&&1&d&&l;if(u)if(0===r)l.beforeEnter(i),a(i,n,t),$e((()=>l.enter(i)),o);else{const{leave:e,delayLeave:r,afterLeave:o}=l,s=()=>a(i,n,t),c=()=>{e(i,(()=>{s(),o&&o()}))};r?r(i,s,c):c()}else a(i,n,t)},V=(e,n,t,r=!1,o=!1)=>{const{type:a,props:i,ref:s,children:l,dynamicChildren:c,shapeFlag:d,patchFlag:u,dirs:p}=e;if(null!=s&&Ne(s,null,t,e,!0),256&d)return void n.ctx.deactivate(e);const m=1&d&&p,f=!A(e);let h;if(f&&(h=i&&i.onVnodeBeforeUnmount)&&qe(h,n,e),6&d)Z(e.component,t,r);else{if(128&d)return void e.suspense.unmount(t,r);m&&Te(e,null,n,"beforeUnmount"),64&d?e.type.remove(e,n,t,o,Q,r):c&&(a!==Ze||u>0&&64&u)?K(c,n,t,!1,!0):(a===Ze&&384&u||!o&&16&d)&&K(l,n,t),r&&G(e)}(f&&(h=i&&i.onVnodeUnmounted)||m)&&$e((()=>{h&&qe(h,n,e),m&&Te(e,null,n,"unmounted")}),t)},G=e=>{const{type:n,el:t,anchor:r,transition:o}=e;if(n===Ze)return void J(t,r);if(n===Xe)return void E(e);const a=()=>{i(t),o&&!o.persisted&&o.afterLeave&&o.afterLeave()};if(1&e.shapeFlag&&o&&!o.persisted){const{leave:n,delayLeave:r}=o,i=()=>n(t,a);r?r(e.el,a,i):i()}else a()},J=(e,n)=>{let t;while(e!==n)t=w(e),i(e),e=t;i(n)},Z=(e,n,t)=>{const{bum:r,scope:a,update:i,subTree:s,um:l}=e;r&&(0,o.ir)(r),a.stop(),i&&(i.active=!1,V(s,e,n,t)),l&&$e(l,n),$e((()=>{e.isUnmounted=!0}),n),n&&n.pendingBranch&&!n.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===n.pendingId&&(n.deps--,0===n.deps&&n.resolve())},K=(e,n,t,r=!1,o=!1,a=0)=>{for(let i=a;i<e.length;i++)V(e[i],n,t,r,o)},Y=e=>6&e.shapeFlag?Y(e.component.subTree):128&e.shapeFlag?e.suspense.next():w(e.anchor||e.el),X=(e,n,t)=>{null==e?n._vnode&&V(n._vnode,null,null,!0):v(n._vnode||null,e,n,null,null,null,t),kt(),n._vnode=e},Q={p:v,um:V,m:W,r:G,mt:I,mc:O,pc:B,pbc:M,n:Y,o:e};let ee,ne;return n&&([ee,ne]=n(Q)),{render:X,hydrate:ee,createApp:Fe(X,ee)}}function Ne(e,n,t,a,i=!1){if((0,o.kJ)(e))return void e.forEach(((e,r)=>Ne(e,n&&((0,o.kJ)(n)?n[r]:n),t,a,i)));if(A(a)&&!i)return;const s=4&a.shapeFlag?Un(a.component)||a.component.proxy:a.el,l=i?null:s,{i:c,r:d}=e;const u=n&&n.r,p=c.refs===o.kT?c.refs={}:c.refs,m=c.setupState;if(null!=u&&u!==d&&((0,o.HD)(u)?(p[u]=null,(0,o.RI)(m,u)&&(m[u]=null)):(0,r.dq)(u)&&(u.value=null)),(0,o.HD)(d)){const e=()=>{p[d]=l,(0,o.RI)(m,d)&&(m[d]=l)};l?(e.id=-1,$e(e,t)):e()}else if((0,r.dq)(d)){const e=()=>{d.value=l};l?(e.id=-1,$e(e,t)):e()}else(0,o.mf)(d)&&Gn(d,c,12,[l,p])}function qe(e,n,t,r=null){Jn(e,n,7,[t,r])}function He(e,n,t=!1){const r=e.children,a=n.children;if((0,o.kJ)(r)&&(0,o.kJ)(a))for(let o=0;o<r.length;o++){const e=r[o];let n=a[o];1&n.shapeFlag&&!n.dynamicChildren&&((n.patchFlag<=0||32===n.patchFlag)&&(n=a[o]=vn(a[o]),n.el=e.el),t||He(e,n))}}function Be(e){const n=e.slice(),t=[0];let r,o,a,i,s;const l=e.length;for(r=0;r<l;r++){const l=e[r];if(0!==l){if(o=t[t.length-1],e[o]<l){n[r]=o,t.push(r);continue}a=0,i=t.length-1;while(a<i)s=a+i>>1,e[t[s]]<l?a=s+1:i=s;l<e[t[a]]&&(a>0&&(n[r]=t[a-1]),t[a]=r)}}a=t.length,i=t[a-1];while(a-- >0)t[a]=i,i=n[i];return t}const De=e=>e.__isTeleport;const Ue="components";function We(e,n){return Ge(Ue,e,!0,n)||e}const Ve=Symbol();function Ge(e,n,t=!0,r=!1){const a=l||On;if(a){const t=a.type;if(e===Ue){const e=Wn(t);if(e&&(e===n||e===(0,o._A)(n)||e===(0,o.kC)((0,o._A)(n))))return t}const i=Je(a[e]||t[e],n)||Je(a.appContext[e],n);return!i&&r?t:i}}function Je(e,n){return e&&(e[n]||e[(0,o._A)(n)]||e[(0,o.kC)((0,o._A)(n))])}const Ze=Symbol(void 0),Ke=Symbol(void 0),Ye=Symbol(void 0),Xe=Symbol(void 0),Qe=[];let en=null;function nn(e=!1){Qe.push(en=e?null:[])}function tn(){Qe.pop(),en=Qe[Qe.length-1]||null}let rn=1;function on(e){rn+=e}function an(e){return e.dynamicChildren=rn>0?en||o.Z6:null,tn(),rn>0&&en&&en.push(e),e}function sn(e,n,t,r,o,a){return an(fn(e,n,t,r,o,a,!0))}function ln(e,n,t,r,o){return an(hn(e,n,t,r,o,!0))}function cn(e){return!!e&&!0===e.__v_isVNode}function dn(e,n){return e.type===n.type&&e.key===n.key}const un="__vInternal",pn=({key:e})=>null!=e?e:null,mn=({ref:e})=>null!=e?(0,o.HD)(e)||(0,r.dq)(e)||(0,o.mf)(e)?{i:l,r:e}:e:null;function fn(e,n=null,t=null,r=0,a=null,i=(e===Ze?0:1),s=!1,l=!1){const d={__v_isVNode:!0,__v_skip:!0,type:e,props:n,key:n&&pn(n),ref:n&&mn(n),scopeId:c,slotScopeIds:null,children:t,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:a,dynamicChildren:null,appContext:null};return l?(_n(d,t),128&i&&e.normalize(d)):t&&(d.shapeFlag|=(0,o.HD)(t)?8:16),rn>0&&!s&&en&&(d.patchFlag>0||6&i)&&32!==d.patchFlag&&en.push(d),d}const hn=wn;function wn(e,n=null,t=null,a=0,i=null,s=!1){if(e&&e!==Ve||(e=Ye),cn(e)){const r=gn(e,n,!0);return t&&_n(r,t),r}if(Vn(e)&&(e=e.__vccOpts),n){n=bn(n);let{class:e,style:t}=n;e&&!(0,o.HD)(e)&&(n.class=(0,o.C_)(e)),(0,o.Kn)(t)&&((0,r.X3)(t)&&!(0,o.kJ)(t)&&(t=(0,o.l7)({},t)),n.style=(0,o.j5)(t))}const l=(0,o.HD)(e)?1:g(e)?128:De(e)?64:(0,o.Kn)(e)?4:(0,o.mf)(e)?2:0;return fn(e,n,t,a,i,l,s,!0)}function bn(e){return e?(0,r.X3)(e)||un in e?(0,o.l7)({},e):e:null}function gn(e,n,t=!1){const{props:r,ref:a,patchFlag:i,children:s}=e,l=n?xn(r||{},n):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:l,key:l&&pn(l),ref:n&&n.ref?t&&a?(0,o.kJ)(a)?a.concat(mn(n)):[a,mn(n)]:mn(n):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:n&&e.type!==Ze?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&gn(e.ssContent),ssFallback:e.ssFallback&&gn(e.ssFallback),el:e.el,anchor:e.anchor};return c}function kn(e=" ",n=0){return hn(Ke,null,e,n)}function yn(e){return null==e||"boolean"===typeof e?hn(Ye):(0,o.kJ)(e)?hn(Ze,null,e.slice()):"object"===typeof e?vn(e):hn(Ke,null,String(e))}function vn(e){return null===e.el||e.memo?e:gn(e)}function _n(e,n){let t=0;const{shapeFlag:r}=e;if(null==n)n=null;else if((0,o.kJ)(n))t=16;else if("object"===typeof n){if(65&r){const t=n.default;return void(t&&(t._c&&(t._d=!1),_n(e,t()),t._c&&(t._d=!0)))}{t=32;const r=n._;r||un in n?3===r&&l&&(1===l.slots._?n._=1:(n._=2,e.patchFlag|=1024)):n._ctx=l}}else(0,o.mf)(n)?(n={default:n,_ctx:l},t=32):(n=String(n),64&r?(t=16,n=[kn(n)]):t=8);e.children=n,e.shapeFlag|=t}function xn(...e){const n={};for(let t=0;t<e.length;t++){const r=e[t];for(const e in r)if("class"===e)n.class!==r.class&&(n.class=(0,o.C_)([n.class,r.class]));else if("style"===e)n.style=(0,o.j5)([n.style,r.style]);else if((0,o.F7)(e)){const t=n[e],o=r[e];t!==o&&(n[e]=t?[].concat(t,o):o)}else""!==e&&(n[e]=r[e])}return n}const Sn=e=>e?Fn(e)?Un(e)||e.proxy:Sn(e.parent):null,Cn=(0,o.l7)(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Sn(e.parent),$root:e=>Sn(e.root),$emit:e=>e.emit,$options:e=>ae(e),$forceUpdate:e=>()=>pt(e.update),$nextTick:e=>dt.bind(e.proxy),$watch:e=>Ct.bind(e)}),En={get({_:e},n){const{ctx:t,setupState:a,data:i,props:s,accessCache:l,type:c,appContext:d}=e;let u;if("$"!==n[0]){const r=l[n];if(void 0!==r)switch(r){case 0:return a[n];case 1:return i[n];case 3:return t[n];case 2:return s[n]}else{if(a!==o.kT&&(0,o.RI)(a,n))return l[n]=0,a[n];if(i!==o.kT&&(0,o.RI)(i,n))return l[n]=1,i[n];if((u=e.propsOptions[0])&&(0,o.RI)(u,n))return l[n]=2,s[n];if(t!==o.kT&&(0,o.RI)(t,n))return l[n]=3,t[n];ee&&(l[n]=4)}}const p=Cn[n];let m,f;return p?("$attrs"===n&&(0,r.j)(e,"get",n),p(e)):(m=c.__cssModules)&&(m=m[n])?m:t!==o.kT&&(0,o.RI)(t,n)?(l[n]=3,t[n]):(f=d.config.globalProperties,(0,o.RI)(f,n)?f[n]:void 0)},set({_:e},n,t){const{data:r,setupState:a,ctx:i}=e;if(a!==o.kT&&(0,o.RI)(a,n))a[n]=t;else if(r!==o.kT&&(0,o.RI)(r,n))r[n]=t;else if((0,o.RI)(e.props,n))return!1;return("$"!==n[0]||!(n.slice(1)in e))&&(i[n]=t,!0)},has({_:{data:e,setupState:n,accessCache:t,ctx:r,appContext:a,propsOptions:i}},s){let l;return void 0!==t[s]||e!==o.kT&&(0,o.RI)(e,s)||n!==o.kT&&(0,o.RI)(n,s)||(l=i[0])&&(0,o.RI)(l,s)||(0,o.RI)(r,s)||(0,o.RI)(Cn,s)||(0,o.RI)(a.config.globalProperties,s)}};const Pn=Me();let jn=0;function Ln(e,n,t){const s=e.type,l=(n?n.appContext:e.appContext)||Pn,c={uid:jn++,vnode:e,type:s,parent:n,appContext:l,root:null,next:null,subTree:null,update:null,scope:new r.Bj(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:n?n.provides:Object.create(l.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ge(s,l),emitsOptions:i(s,l),emit:null,emitted:null,propsDefaults:o.kT,inheritAttrs:s.inheritAttrs,ctx:o.kT,data:o.kT,props:o.kT,attrs:o.kT,slots:o.kT,refs:o.kT,setupState:o.kT,setupContext:null,suspense:t,suspenseId:t?t.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return c.ctx={_:c},c.root=n?n.root:c,c.emit=a.bind(null,c),e.ce&&e.ce(c),c}let On=null;const Tn=()=>On||l,Mn=e=>{On=e,e.scope.on()},An=()=>{On&&On.scope.off(),On=null};function Fn(e){return 4&e.vnode.shapeFlag}let zn,$n,Rn=!1;function In(e,n=!1){Rn=n;const{props:t,children:r}=e.vnode,o=Fn(e);fe(e,t,o,n),je(e,r);const a=o?Nn(e,n):void 0;return Rn=!1,a}function Nn(e,n){const t=e.type;e.accessCache=Object.create(null),e.proxy=(0,r.Xl)(new Proxy(e.ctx,En));const{setup:a}=t;if(a){const t=e.setupContext=a.length>1?Dn(e):null;Mn(e),(0,r.Jd)();const i=Gn(a,e,0,[e.props,t]);if((0,r.lk)(),An(),(0,o.tI)(i)){if(i.then(An,An),n)return i.then((t=>{qn(e,t,n)})).catch((n=>{Zn(n,e,0)}));e.asyncDep=i}else qn(e,i,n)}else Hn(e,n)}function qn(e,n,t){(0,o.mf)(n)?e.type.__ssrInlineRender?e.ssrRender=n:e.render=n:(0,o.Kn)(n)&&(e.setupState=(0,r.WL)(n)),Hn(e,t)}function Hn(e,n,t){const a=e.type;if(!e.render){if(!n&&zn&&!a.render){const n=a.template;if(n){0;const{isCustomElement:t,compilerOptions:r}=e.appContext.config,{delimiters:i,compilerOptions:s}=a,l=(0,o.l7)((0,o.l7)({isCustomElement:t,delimiters:i},r),s);a.render=zn(n,l)}}e.render=a.render||o.dG,$n&&$n(e)}Mn(e),(0,r.Jd)(),ne(e),(0,r.lk)(),An()}function Bn(e){return new Proxy(e.attrs,{get(n,t){return(0,r.j)(e,"get","$attrs"),n[t]}})}function Dn(e){const n=n=>{e.exposed=n||{}};let t;return{get attrs(){return t||(t=Bn(e))},slots:e.slots,emit:e.emit,expose:n}}function Un(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy((0,r.WL)((0,r.Xl)(e.exposed)),{get(n,t){return t in n?n[t]:t in Cn?Cn[t](e):void 0}}))}function Wn(e){return(0,o.mf)(e)&&e.displayName||e.name}function Vn(e){return(0,o.mf)(e)&&"__vccOpts"in e}function Gn(e,n,t,r){let o;try{o=r?e(...r):e()}catch(a){Zn(a,n,t)}return o}function Jn(e,n,t,r){if((0,o.mf)(e)){const a=Gn(e,n,t,r);return a&&(0,o.tI)(a)&&a.catch((e=>{Zn(e,n,t)})),a}const a=[];for(let o=0;o<e.length;o++)a.push(Jn(e[o],n,t,r));return a}function Zn(e,n,t,r=!0){const o=n?n.vnode:null;if(n){let r=n.parent;const o=n.proxy,a=t;while(r){const n=r.ec;if(n)for(let t=0;t<n.length;t++)if(!1===n[t](e,o,a))return;r=r.parent}const i=n.appContext.config.errorHandler;if(i)return void Gn(i,null,10,[e,o,a])}Kn(e,t,o,r)}function Kn(e,n,t,r=!0){console.error(e)}let Yn=!1,Xn=!1;const Qn=[];let et=0;const nt=[];let tt=null,rt=0;const ot=[];let at=null,it=0;const st=Promise.resolve();let lt=null,ct=null;function dt(e){const n=lt||st;return e?n.then(this?e.bind(this):e):n}function ut(e){let n=et+1,t=Qn.length;while(n<t){const r=n+t>>>1,o=yt(Qn[r]);o<e?n=r+1:t=r}return n}function pt(e){Qn.length&&Qn.includes(e,Yn&&e.allowRecurse?et+1:et)||e===ct||(null==e.id?Qn.push(e):Qn.splice(ut(e.id),0,e),mt())}function mt(){Yn||Xn||(Xn=!0,lt=st.then(vt))}function ft(e){const n=Qn.indexOf(e);n>et&&Qn.splice(n,1)}function ht(e,n,t,r){(0,o.kJ)(e)?t.push(...e):n&&n.includes(e,e.allowRecurse?r+1:r)||t.push(e),mt()}function wt(e){ht(e,tt,nt,rt)}function bt(e){ht(e,at,ot,it)}function gt(e,n=null){if(nt.length){for(ct=n,tt=[...new Set(nt)],nt.length=0,rt=0;rt<tt.length;rt++)tt[rt]();tt=null,rt=0,ct=null,gt(e,n)}}function kt(e){if(ot.length){const e=[...new Set(ot)];if(ot.length=0,at)return void at.push(...e);for(at=e,at.sort(((e,n)=>yt(e)-yt(n))),it=0;it<at.length;it++)at[it]();at=null,it=0}}const yt=e=>null==e.id?1/0:e.id;function vt(e){Xn=!1,Yn=!0,gt(e),Qn.sort(((e,n)=>yt(e)-yt(n)));o.dG;try{for(et=0;et<Qn.length;et++){const e=Qn[et];e&&!1!==e.active&&Gn(e,null,14)}}finally{et=0,Qn.length=0,kt(e),Yn=!1,lt=null,(Qn.length||nt.length||ot.length)&&vt(e)}}const _t={};function xt(e,n,t){return St(e,n,t)}function St(e,n,{immediate:t,deep:a,flush:i,onTrack:s,onTrigger:l}=o.kT){const c=On;let d,u,p=!1,m=!1;if((0,r.dq)(e)?(d=()=>e.value,p=!!e._shallow):(0,r.PG)(e)?(d=()=>e,a=!0):(0,o.kJ)(e)?(m=!0,p=e.some(r.PG),d=()=>e.map((e=>(0,r.dq)(e)?e.value:(0,r.PG)(e)?Pt(e):(0,o.mf)(e)?Gn(e,c,2):void 0))):d=(0,o.mf)(e)?n?()=>Gn(e,c,2):()=>{if(!c||!c.isUnmounted)return u&&u(),Jn(e,c,3,[f])}:o.dG,n&&a){const e=d;d=()=>Pt(e())}let f=e=>{u=g.onStop=()=>{Gn(e,c,4)}};if(Rn)return f=o.dG,n?t&&Jn(n,c,3,[d(),m?[]:void 0,f]):d(),o.dG;let h=m?[]:_t;const w=()=>{if(g.active)if(n){const e=g.run();(a||p||(m?e.some(((e,n)=>(0,o.aU)(e,h[n]))):(0,o.aU)(e,h)))&&(u&&u(),Jn(n,c,3,[e,h===_t?void 0:h,f]),h=e)}else g.run()};let b;w.allowRecurse=!!n,b="sync"===i?w:"post"===i?()=>$e(w,c&&c.suspense):()=>{!c||c.isMounted?wt(w):w()};const g=new r.qq(d,b);return n?t?w():h=g.run():"post"===i?$e(g.run.bind(g),c&&c.suspense):g.run(),()=>{g.stop(),c&&c.scope&&(0,o.Od)(c.scope.effects,g)}}function Ct(e,n,t){const r=this.proxy,a=(0,o.HD)(e)?e.includes(".")?Et(r,e):()=>r[e]:e.bind(r,r);let i;(0,o.mf)(n)?i=n:(i=n.handler,t=n);const s=On;Mn(this);const l=St(a,i.bind(r),t);return s?Mn(s):An(),l}function Et(e,n){const t=n.split(".");return()=>{let n=e;for(let e=0;e<t.length&&n;e++)n=n[t[e]];return n}}function Pt(e,n){if(!(0,o.Kn)(e)||e["__v_skip"])return e;if(n=n||new Set,n.has(e))return e;if(n.add(e),(0,r.dq)(e))Pt(e.value,n);else if((0,o.kJ)(e))for(let t=0;t<e.length;t++)Pt(e[t],n);else if((0,o.DM)(e)||(0,o._N)(e))e.forEach((e=>{Pt(e,n)}));else if((0,o.PO)(e))for(const t in e)Pt(e[t],n);return e}function jt(e,n,t){const r=arguments.length;return 2===r?(0,o.Kn)(n)&&!(0,o.kJ)(n)?cn(n)?hn(e,null,[n]):hn(e,n):hn(e,null,n):(r>3?t=Array.prototype.slice.call(arguments,2):3===r&&cn(t)&&(t=[t]),hn(e,n,t))}Symbol("");const Lt="3.2.20"},8880:(e,n,t)=>{"use strict";t.d(n,{uT:()=>I,W3:()=>oe,ri:()=>pe});var r=t(2323),o=t(3673),a=t(1959);const i="http://www.w3.org/2000/svg",s="undefined"!==typeof document?document:null,l=new Map,c={insert:(e,n,t)=>{n.insertBefore(e,t||null)},remove:e=>{const n=e.parentNode;n&&n.removeChild(e)},createElement:(e,n,t,r)=>{const o=n?s.createElementNS(i,e):s.createElement(e,t?{is:t}:void 0);return"select"===e&&r&&null!=r.multiple&&o.setAttribute("multiple",r.multiple),o},createText:e=>s.createTextNode(e),createComment:e=>s.createComment(e),setText:(e,n)=>{e.nodeValue=n},setElementText:(e,n)=>{e.textContent=n},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>s.querySelector(e),setScopeId(e,n){e.setAttribute(n,"")},cloneNode(e){const n=e.cloneNode(!0);return"_value"in e&&(n._value=e._value),n},insertStaticContent(e,n,t,r){const o=t?t.previousSibling:n.lastChild;let a=l.get(e);if(!a){const n=s.createElement("template");if(n.innerHTML=r?`<svg>${e}</svg>`:e,a=n.content,r){const e=a.firstChild;while(e.firstChild)a.appendChild(e.firstChild);a.removeChild(e)}l.set(e,a)}return n.insertBefore(a.cloneNode(!0),t),[o?o.nextSibling:n.firstChild,t?t.previousSibling:n.lastChild]}};function d(e,n,t){const r=e._vtc;r&&(n=(n?[n,...r]:[...r]).join(" ")),null==n?e.removeAttribute("class"):t?e.setAttribute("class",n):e.className=n}function u(e,n,t){const o=e.style,a=o.display;if(t)if((0,r.HD)(t))n!==t&&(o.cssText=t);else{for(const e in t)m(o,e,t[e]);if(n&&!(0,r.HD)(n))for(const e in n)null==t[e]&&m(o,e,"")}else e.removeAttribute("style");"_vod"in e&&(o.display=a)}const p=/\s*!important$/;function m(e,n,t){if((0,r.kJ)(t))t.forEach((t=>m(e,n,t)));else if(n.startsWith("--"))e.setProperty(n,t);else{const o=w(e,n);p.test(t)?e.setProperty((0,r.rs)(o),t.replace(p,""),"important"):e[o]=t}}const f=["Webkit","Moz","ms"],h={};function w(e,n){const t=h[n];if(t)return t;let o=(0,r._A)(n);if("filter"!==o&&o in e)return h[n]=o;o=(0,r.kC)(o);for(let r=0;r<f.length;r++){const t=f[r]+o;if(t in e)return h[n]=t}return n}const b="http://www.w3.org/1999/xlink";function g(e,n,t,o,a){if(o&&n.startsWith("xlink:"))null==t?e.removeAttributeNS(b,n.slice(6,n.length)):e.setAttributeNS(b,n,t);else{const o=(0,r.Pq)(n);null==t||o&&!(0,r.yA)(t)?e.removeAttribute(n):e.setAttribute(n,o?"":t)}}function k(e,n,t,o,a,i,s){if("innerHTML"===n||"textContent"===n)return o&&s(o,a,i),void(e[n]=null==t?"":t);if("value"===n&&"PROGRESS"!==e.tagName){e._value=t;const r=null==t?"":t;return e.value!==r&&(e.value=r),void(null==t&&e.removeAttribute(n))}if(""===t||null==t){const o=typeof e[n];if("boolean"===o)return void(e[n]=(0,r.yA)(t));if(null==t&&"string"===o)return e[n]="",void e.removeAttribute(n);if("number"===o){try{e[n]=0}catch(l){}return void e.removeAttribute(n)}}try{e[n]=t}catch(c){0}}let y=Date.now,v=!1;if("undefined"!==typeof window){y()>document.createEvent("Event").timeStamp&&(y=()=>performance.now());const e=navigator.userAgent.match(/firefox\/(\d+)/i);v=!!(e&&Number(e[1])<=53)}let _=0;const x=Promise.resolve(),S=()=>{_=0},C=()=>_||(x.then(S),_=y());function E(e,n,t,r){e.addEventListener(n,t,r)}function P(e,n,t,r){e.removeEventListener(n,t,r)}function j(e,n,t,r,o=null){const a=e._vei||(e._vei={}),i=a[n];if(r&&i)i.value=r;else{const[t,s]=O(n);if(r){const i=a[n]=T(r,o);E(e,t,i,s)}else i&&(P(e,t,i,s),a[n]=void 0)}}const L=/(?:Once|Passive|Capture)$/;function O(e){let n;if(L.test(e)){let t;n={};while(t=e.match(L))e=e.slice(0,e.length-t[0].length),n[t[0].toLowerCase()]=!0}return[(0,r.rs)(e.slice(2)),n]}function T(e,n){const t=e=>{const r=e.timeStamp||y();(v||r>=t.attached-1)&&(0,o.$d)(M(e,t.value),n,5,[e])};return t.value=e,t.attached=C(),t}function M(e,n){if((0,r.kJ)(n)){const t=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{t.call(e),e._stopped=!0},n.map((e=>n=>!n._stopped&&e(n)))}return n}const A=/^on[a-z]/,F=(e,n,t,o,a=!1,i,s,l,c)=>{"class"===n?d(e,o,a):"style"===n?u(e,t,o):(0,r.F7)(n)?(0,r.tR)(n)||j(e,n,t,o,s):("."===n[0]?(n=n.slice(1),1):"^"===n[0]?(n=n.slice(1),0):z(e,n,o,a))?k(e,n,o,i,s,l,c):("true-value"===n?e._trueValue=o:"false-value"===n&&(e._falseValue=o),g(e,n,o,a))};function z(e,n,t,o){return o?"innerHTML"===n||"textContent"===n||!!(n in e&&A.test(n)&&(0,r.mf)(t)):"spellcheck"!==n&&"draggable"!==n&&("form"!==n&&(("list"!==n||"INPUT"!==e.tagName)&&(("type"!==n||"TEXTAREA"!==e.tagName)&&((!A.test(n)||!(0,r.HD)(t))&&n in e))))}"undefined"!==typeof HTMLElement&&HTMLElement;const $="transition",R="animation",I=(e,{slots:n})=>(0,o.h)(o.P$,D(e),n);I.displayName="Transition";const N={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},q=I.props=(0,r.l7)({},o.P$.props,N),H=(e,n=[])=>{(0,r.kJ)(e)?e.forEach((e=>e(...n))):e&&e(...n)},B=e=>!!e&&((0,r.kJ)(e)?e.some((e=>e.length>1)):e.length>1);function D(e){const n={};for(const r in e)r in N||(n[r]=e[r]);if(!1===e.css)return n;const{name:t="v",type:o,duration:a,enterFromClass:i=`${t}-enter-from`,enterActiveClass:s=`${t}-enter-active`,enterToClass:l=`${t}-enter-to`,appearFromClass:c=i,appearActiveClass:d=s,appearToClass:u=l,leaveFromClass:p=`${t}-leave-from`,leaveActiveClass:m=`${t}-leave-active`,leaveToClass:f=`${t}-leave-to`}=e,h=U(a),w=h&&h[0],b=h&&h[1],{onBeforeEnter:g,onEnter:k,onEnterCancelled:y,onLeave:v,onLeaveCancelled:_,onBeforeAppear:x=g,onAppear:S=k,onAppearCancelled:C=y}=n,E=(e,n,t)=>{G(e,n?u:l),G(e,n?d:s),t&&t()},P=(e,n)=>{G(e,f),G(e,m),n&&n()},j=e=>(n,t)=>{const r=e?S:k,a=()=>E(n,e,t);H(r,[n,a]),J((()=>{G(n,e?c:i),V(n,e?u:l),B(r)||K(n,o,w,a)}))};return(0,r.l7)(n,{onBeforeEnter(e){H(g,[e]),V(e,i),V(e,s)},onBeforeAppear(e){H(x,[e]),V(e,c),V(e,d)},onEnter:j(!1),onAppear:j(!0),onLeave(e,n){const t=()=>P(e,n);V(e,p),ee(),V(e,m),J((()=>{G(e,p),V(e,f),B(v)||K(e,o,b,t)})),H(v,[e,t])},onEnterCancelled(e){E(e,!1),H(y,[e])},onAppearCancelled(e){E(e,!0),H(C,[e])},onLeaveCancelled(e){P(e),H(_,[e])}})}function U(e){if(null==e)return null;if((0,r.Kn)(e))return[W(e.enter),W(e.leave)];{const n=W(e);return[n,n]}}function W(e){const n=(0,r.He)(e);return n}function V(e,n){n.split(/\s+/).forEach((n=>n&&e.classList.add(n))),(e._vtc||(e._vtc=new Set)).add(n)}function G(e,n){n.split(/\s+/).forEach((n=>n&&e.classList.remove(n)));const{_vtc:t}=e;t&&(t.delete(n),t.size||(e._vtc=void 0))}function J(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let Z=0;function K(e,n,t,r){const o=e._endId=++Z,a=()=>{o===e._endId&&r()};if(t)return setTimeout(a,t);const{type:i,timeout:s,propCount:l}=Y(e,n);if(!i)return r();const c=i+"end";let d=0;const u=()=>{e.removeEventListener(c,p),a()},p=n=>{n.target===e&&++d>=l&&u()};setTimeout((()=>{d<l&&u()}),s+1),e.addEventListener(c,p)}function Y(e,n){const t=window.getComputedStyle(e),r=e=>(t[e]||"").split(", "),o=r($+"Delay"),a=r($+"Duration"),i=X(o,a),s=r(R+"Delay"),l=r(R+"Duration"),c=X(s,l);let d=null,u=0,p=0;n===$?i>0&&(d=$,u=i,p=a.length):n===R?c>0&&(d=R,u=c,p=l.length):(u=Math.max(i,c),d=u>0?i>c?$:R:null,p=d?d===$?a.length:l.length:0);const m=d===$&&/\b(transform|all)(,|$)/.test(t[$+"Property"]);return{type:d,timeout:u,propCount:p,hasTransform:m}}function X(e,n){while(e.length<n.length)e=e.concat(e);return Math.max(...n.map(((n,t)=>Q(n)+Q(e[t]))))}function Q(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}function ee(){return document.body.offsetHeight}const ne=new WeakMap,te=new WeakMap,re={name:"TransitionGroup",props:(0,r.l7)({},q,{tag:String,moveClass:String}),setup(e,{slots:n}){const t=(0,o.FN)(),r=(0,o.Y8)();let i,s;return(0,o.ic)((()=>{if(!i.length)return;const n=e.moveClass||`${e.name||"v"}-move`;if(!le(i[0].el,t.vnode.el,n))return;i.forEach(ae),i.forEach(ie);const r=i.filter(se);ee(),r.forEach((e=>{const t=e.el,r=t.style;V(t,n),r.transform=r.webkitTransform=r.transitionDuration="";const o=t._moveCb=e=>{e&&e.target!==t||e&&!/transform$/.test(e.propertyName)||(t.removeEventListener("transitionend",o),t._moveCb=null,G(t,n))};t.addEventListener("transitionend",o)}))})),()=>{const l=(0,a.IU)(e),c=D(l);let d=l.tag||o.HY;i=s,s=n.default?(0,o.Q6)(n.default()):[];for(let e=0;e<s.length;e++){const n=s[e];null!=n.key&&(0,o.nK)(n,(0,o.U2)(n,c,r,t))}if(i)for(let e=0;e<i.length;e++){const n=i[e];(0,o.nK)(n,(0,o.U2)(n,c,r,t)),ne.set(n,n.el.getBoundingClientRect())}return(0,o.Wm)(d,null,s)}}},oe=re;function ae(e){const n=e.el;n._moveCb&&n._moveCb(),n._enterCb&&n._enterCb()}function ie(e){te.set(e,e.el.getBoundingClientRect())}function se(e){const n=ne.get(e),t=te.get(e),r=n.left-t.left,o=n.top-t.top;if(r||o){const n=e.el.style;return n.transform=n.webkitTransform=`translate(${r}px,${o}px)`,n.transitionDuration="0s",e}}function le(e,n,t){const r=e.cloneNode();e._vtc&&e._vtc.forEach((e=>{e.split(/\s+/).forEach((e=>e&&r.classList.remove(e)))})),t.split(/\s+/).forEach((e=>e&&r.classList.add(e))),r.style.display="none";const o=1===n.nodeType?n:n.parentNode;o.appendChild(r);const{hasTransform:a}=Y(r);return o.removeChild(r),a}const ce=(0,r.l7)({patchProp:F},c);let de;function ue(){return de||(de=(0,o.Us)(ce))}const pe=(...e)=>{const n=ue().createApp(...e);const{mount:t}=n;return n.mount=e=>{const o=me(e);if(!o)return;const a=n._component;(0,r.mf)(a)||a.render||a.template||(a.template=o.innerHTML),o.innerHTML="";const i=t(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},n};function me(e){if((0,r.HD)(e)){const n=document.querySelector(e);return n}return e}},2323:(e,n,t)=>{"use strict";function r(e,n){const t=Object.create(null),r=e.split(",");for(let o=0;o<r.length;o++)t[r[o]]=!0;return n?e=>!!t[e.toLowerCase()]:e=>!!t[e]}t.d(n,{Z6:()=>g,kT:()=>b,NO:()=>y,dG:()=>k,_A:()=>W,kC:()=>J,Nj:()=>X,l7:()=>S,E9:()=>ne,aU:()=>K,RI:()=>P,rs:()=>G,yA:()=>l,ir:()=>Y,kJ:()=>j,mf:()=>M,e1:()=>a,S0:()=>H,_N:()=>L,tR:()=>x,Kn:()=>z,F7:()=>_,PO:()=>q,tI:()=>$,Gg:()=>B,DM:()=>O,Pq:()=>s,HD:()=>A,yk:()=>F,WV:()=>h,hq:()=>w,fY:()=>r,C_:()=>m,j5:()=>c,Od:()=>C,hR:()=>Z,He:()=>Q,W7:()=>N});const o="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt",a=r(o);const i="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",s=r(i);function l(e){return!!e||""===e}function c(e){if(j(e)){const n={};for(let t=0;t<e.length;t++){const r=e[t],o=A(r)?p(r):c(r);if(o)for(const e in o)n[e]=o[e]}return n}return A(e)||z(e)?e:void 0}const d=/;(?![^(]*\))/g,u=/:(.+)/;function p(e){const n={};return e.split(d).forEach((e=>{if(e){const t=e.split(u);t.length>1&&(n[t[0].trim()]=t[1].trim())}})),n}function m(e){let n="";if(A(e))n=e;else if(j(e))for(let t=0;t<e.length;t++){const r=m(e[t]);r&&(n+=r+" ")}else if(z(e))for(const t in e)e[t]&&(n+=t+" ");return n.trim()}function f(e,n){if(e.length!==n.length)return!1;let t=!0;for(let r=0;t&&r<e.length;r++)t=h(e[r],n[r]);return t}function h(e,n){if(e===n)return!0;let t=T(e),r=T(n);if(t||r)return!(!t||!r)&&e.getTime()===n.getTime();if(t=j(e),r=j(n),t||r)return!(!t||!r)&&f(e,n);if(t=z(e),r=z(n),t||r){if(!t||!r)return!1;const o=Object.keys(e).length,a=Object.keys(n).length;if(o!==a)return!1;for(const t in e){const r=e.hasOwnProperty(t),o=n.hasOwnProperty(t);if(r&&!o||!r&&o||!h(e[t],n[t]))return!1}}return String(e)===String(n)}function w(e,n){return e.findIndex((e=>h(e,n)))}const b={},g=[],k=()=>{},y=()=>!1,v=/^on[^a-z]/,_=e=>v.test(e),x=e=>e.startsWith("onUpdate:"),S=Object.assign,C=(e,n)=>{const t=e.indexOf(n);t>-1&&e.splice(t,1)},E=Object.prototype.hasOwnProperty,P=(e,n)=>E.call(e,n),j=Array.isArray,L=e=>"[object Map]"===I(e),O=e=>"[object Set]"===I(e),T=e=>e instanceof Date,M=e=>"function"===typeof e,A=e=>"string"===typeof e,F=e=>"symbol"===typeof e,z=e=>null!==e&&"object"===typeof e,$=e=>z(e)&&M(e.then)&&M(e.catch),R=Object.prototype.toString,I=e=>R.call(e),N=e=>I(e).slice(8,-1),q=e=>"[object Object]"===I(e),H=e=>A(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,B=r(",key,ref,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),D=e=>{const n=Object.create(null);return t=>{const r=n[t];return r||(n[t]=e(t))}},U=/-(\w)/g,W=D((e=>e.replace(U,((e,n)=>n?n.toUpperCase():"")))),V=/\B([A-Z])/g,G=D((e=>e.replace(V,"-$1").toLowerCase())),J=D((e=>e.charAt(0).toUpperCase()+e.slice(1))),Z=D((e=>e?`on${J(e)}`:"")),K=(e,n)=>!Object.is(e,n),Y=(e,n)=>{for(let t=0;t<e.length;t++)e[t](n)},X=(e,n,t)=>{Object.defineProperty(e,n,{configurable:!0,enumerable:!1,value:t})},Q=e=>{const n=parseFloat(e);return isNaN(n)?e:n};let ee;const ne=()=>ee||(ee="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:"undefined"!==typeof t.g?t.g:{})},52:(e,n,t)=>{e.exports=t(7974)},8699:(e,n,t)=>{"use strict";var r=t(7210),o=t(4923),a=t(3634),i=t(7696),s=t(9835),l=t(3423),c=t(8365),d=t(701);e.exports=function(e){return new Promise((function(n,t){var u=e.data,p=e.headers,m=e.responseType;r.isFormData(u)&&delete p["Content-Type"];var f=new XMLHttpRequest;if(e.auth){var h=e.auth.username||"",w=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";p.Authorization="Basic "+btoa(h+":"+w)}var b=s(e.baseURL,e.url);function g(){if(f){var r="getAllResponseHeaders"in f?l(f.getAllResponseHeaders()):null,a=m&&"text"!==m&&"json"!==m?f.response:f.responseText,i={data:a,status:f.status,statusText:f.statusText,headers:r,config:e,request:f};o(n,t,i),f=null}}if(f.open(e.method.toUpperCase(),i(b,e.params,e.paramsSerializer),!0),f.timeout=e.timeout,"onloadend"in f?f.onloadend=g:f.onreadystatechange=function(){f&&4===f.readyState&&(0!==f.status||f.responseURL&&0===f.responseURL.indexOf("file:"))&&setTimeout(g)},f.onabort=function(){f&&(t(d("Request aborted",e,"ECONNABORTED",f)),f=null)},f.onerror=function(){t(d("Network Error",e,null,f)),f=null},f.ontimeout=function(){var n="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(n=e.timeoutErrorMessage),t(d(n,e,e.transitional&&e.transitional.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",f)),f=null},r.isStandardBrowserEnv()){var k=(e.withCredentials||c(b))&&e.xsrfCookieName?a.read(e.xsrfCookieName):void 0;k&&(p[e.xsrfHeaderName]=k)}"setRequestHeader"in f&&r.forEach(p,(function(e,n){"undefined"===typeof u&&"content-type"===n.toLowerCase()?delete p[n]:f.setRequestHeader(n,e)})),r.isUndefined(e.withCredentials)||(f.withCredentials=!!e.withCredentials),m&&"json"!==m&&(f.responseType=e.responseType),"function"===typeof e.onDownloadProgress&&f.addEventListener("progress",e.onDownloadProgress),"function"===typeof e.onUploadProgress&&f.upload&&f.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){f&&(f.abort(),t(e),f=null)})),u||(u=null),f.send(u)}))}},7974:(e,n,t)=>{"use strict";var r=t(7210),o=t(2938),a=t(8799),i=t(4495),s=t(7079);function l(e){var n=new a(e),t=o(a.prototype.request,n);return r.extend(t,a.prototype,n),r.extend(t,n),t}var c=l(s);c.Axios=a,c.create=function(e){return l(i(c.defaults,e))},c.Cancel=t(6678),c.CancelToken=t(8858),c.isCancel=t(6029),c.all=function(e){return Promise.all(e)},c.spread=t(5178),c.isAxiosError=t(5615),e.exports=c,e.exports["default"]=c},6678:e=>{"use strict";function n(e){this.message=e}n.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},n.prototype.__CANCEL__=!0,e.exports=n},8858:(e,n,t)=>{"use strict";var r=t(6678);function o(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");var n;this.promise=new Promise((function(e){n=e}));var t=this;e((function(e){t.reason||(t.reason=new r(e),n(t.reason))}))}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.source=function(){var e,n=new o((function(n){e=n}));return{token:n,cancel:e}},e.exports=o},6029:e=>{"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},8799:(e,n,t)=>{"use strict";var r=t(7210),o=t(7696),a=t(2591),i=t(516),s=t(4495),l=t(3170),c=l.validators;function d(e){this.defaults=e,this.interceptors={request:new a,response:new a}}d.prototype.request=function(e){"string"===typeof e?(e=arguments[1]||{},e.url=arguments[0]):e=e||{},e=s(this.defaults,e),e.method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var n=e.transitional;void 0!==n&&l.assertOptions(n,{silentJSONParsing:c.transitional(c.boolean,"1.0.0"),forcedJSONParsing:c.transitional(c.boolean,"1.0.0"),clarifyTimeoutError:c.transitional(c.boolean,"1.0.0")},!1);var t=[],r=!0;this.interceptors.request.forEach((function(n){"function"===typeof n.runWhen&&!1===n.runWhen(e)||(r=r&&n.synchronous,t.unshift(n.fulfilled,n.rejected))}));var o,a=[];if(this.interceptors.response.forEach((function(e){a.push(e.fulfilled,e.rejected)})),!r){var d=[i,void 0];Array.prototype.unshift.apply(d,t),d=d.concat(a),o=Promise.resolve(e);while(d.length)o=o.then(d.shift(),d.shift());return o}var u=e;while(t.length){var p=t.shift(),m=t.shift();try{u=p(u)}catch(f){m(f);break}}try{o=i(u)}catch(f){return Promise.reject(f)}while(a.length)o=o.then(a.shift(),a.shift());return o},d.prototype.getUri=function(e){return e=s(this.defaults,e),o(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},r.forEach(["delete","get","head","options"],(function(e){d.prototype[e]=function(n,t){return this.request(s(t||{},{method:e,url:n,data:(t||{}).data}))}})),r.forEach(["post","put","patch"],(function(e){d.prototype[e]=function(n,t,r){return this.request(s(r||{},{method:e,url:n,data:t}))}})),e.exports=d},2591:(e,n,t)=>{"use strict";var r=t(7210);function o(){this.handlers=[]}o.prototype.use=function(e,n,t){return this.handlers.push({fulfilled:e,rejected:n,synchronous:!!t&&t.synchronous,runWhen:t?t.runWhen:null}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){r.forEach(this.handlers,(function(n){null!==n&&e(n)}))},e.exports=o},9835:(e,n,t)=>{"use strict";var r=t(8380),o=t(6092);e.exports=function(e,n){return e&&!r(n)?o(e,n):n}},701:(e,n,t)=>{"use strict";var r=t(654);e.exports=function(e,n,t,o,a){var i=new Error(e);return r(i,n,t,o,a)}},516:(e,n,t)=>{"use strict";var r=t(7210),o=t(4330),a=t(6029),i=t(7079);function s(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){s(e),e.headers=e.headers||{},e.data=o.call(e,e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),r.forEach(["delete","get","head","post","put","patch","common"],(function(n){delete e.headers[n]}));var n=e.adapter||i.adapter;return n(e).then((function(n){return s(e),n.data=o.call(e,n.data,n.headers,e.transformResponse),n}),(function(n){return a(n)||(s(e),n&&n.response&&(n.response.data=o.call(e,n.response.data,n.response.headers,e.transformResponse))),Promise.reject(n)}))}},654:e=>{"use strict";e.exports=function(e,n,t,r,o){return e.config=n,t&&(e.code=t),e.request=r,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}},4495:(e,n,t)=>{"use strict";var r=t(7210);e.exports=function(e,n){n=n||{};var t={},o=["url","method","data"],a=["headers","auth","proxy","params"],i=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],s=["validateStatus"];function l(e,n){return r.isPlainObject(e)&&r.isPlainObject(n)?r.merge(e,n):r.isPlainObject(n)?r.merge({},n):r.isArray(n)?n.slice():n}function c(o){r.isUndefined(n[o])?r.isUndefined(e[o])||(t[o]=l(void 0,e[o])):t[o]=l(e[o],n[o])}r.forEach(o,(function(e){r.isUndefined(n[e])||(t[e]=l(void 0,n[e]))})),r.forEach(a,c),r.forEach(i,(function(o){r.isUndefined(n[o])?r.isUndefined(e[o])||(t[o]=l(void 0,e[o])):t[o]=l(void 0,n[o])})),r.forEach(s,(function(r){r in n?t[r]=l(e[r],n[r]):r in e&&(t[r]=l(void 0,e[r]))}));var d=o.concat(a).concat(i).concat(s),u=Object.keys(e).concat(Object.keys(n)).filter((function(e){return-1===d.indexOf(e)}));return r.forEach(u,c),t}},4923:(e,n,t)=>{"use strict";var r=t(701);e.exports=function(e,n,t){var o=t.config.validateStatus;t.status&&o&&!o(t.status)?n(r("Request failed with status code "+t.status,t.config,null,t.request,t)):e(t)}},4330:(e,n,t)=>{"use strict";var r=t(7210),o=t(7079);e.exports=function(e,n,t){var a=this||o;return r.forEach(t,(function(t){e=t.call(a,e,n)})),e}},7079:(e,n,t)=>{"use strict";var r=t(7210),o=t(4733),a=t(654),i={"Content-Type":"application/x-www-form-urlencoded"};function s(e,n){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=n)}function l(){var e;return("undefined"!==typeof XMLHttpRequest||"undefined"!==typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(e=t(8699)),e}function c(e,n,t){if(r.isString(e))try{return(n||JSON.parse)(e),r.trim(e)}catch(o){if("SyntaxError"!==o.name)throw o}return(t||JSON.stringify)(e)}var d={transitional:{silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},adapter:l(),transformRequest:[function(e,n){return o(n,"Accept"),o(n,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(s(n,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)||n&&"application/json"===n["Content-Type"]?(s(n,"application/json"),c(e)):e}],transformResponse:[function(e){var n=this.transitional,t=n&&n.silentJSONParsing,o=n&&n.forcedJSONParsing,i=!t&&"json"===this.responseType;if(i||o&&r.isString(e)&&e.length)try{return JSON.parse(e)}catch(s){if(i){if("SyntaxError"===s.name)throw a(s,this,"E_JSON_PARSE");throw s}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};r.forEach(["delete","get","head"],(function(e){d.headers[e]={}})),r.forEach(["post","put","patch"],(function(e){d.headers[e]=r.merge(i)})),e.exports=d},2938:e=>{"use strict";e.exports=function(e,n){return function(){for(var t=new Array(arguments.length),r=0;r<t.length;r++)t[r]=arguments[r];return e.apply(n,t)}}},7696:(e,n,t)=>{"use strict";var r=t(7210);function o(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,n,t){if(!n)return e;var a;if(t)a=t(n);else if(r.isURLSearchParams(n))a=n.toString();else{var i=[];r.forEach(n,(function(e,n){null!==e&&"undefined"!==typeof e&&(r.isArray(e)?n+="[]":e=[e],r.forEach(e,(function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),i.push(o(n)+"="+o(e))})))})),a=i.join("&")}if(a){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+a}return e}},6092:e=>{"use strict";e.exports=function(e,n){return n?e.replace(/\/+$/,"")+"/"+n.replace(/^\/+/,""):e}},3634:(e,n,t)=>{"use strict";var r=t(7210);e.exports=r.isStandardBrowserEnv()?function(){return{write:function(e,n,t,o,a,i){var s=[];s.push(e+"="+encodeURIComponent(n)),r.isNumber(t)&&s.push("expires="+new Date(t).toGMTString()),r.isString(o)&&s.push("path="+o),r.isString(a)&&s.push("domain="+a),!0===i&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var n=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return n?decodeURIComponent(n[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}()},8380:e=>{"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},5615:e=>{"use strict";e.exports=function(e){return"object"===typeof e&&!0===e.isAxiosError}},8365:(e,n,t)=>{"use strict";var r=t(7210);e.exports=r.isStandardBrowserEnv()?function(){var e,n=/(msie|trident)/i.test(navigator.userAgent),t=document.createElement("a");function o(e){var r=e;return n&&(t.setAttribute("href",r),r=t.href),t.setAttribute("href",r),{href:t.href,protocol:t.protocol?t.protocol.replace(/:$/,""):"",host:t.host,search:t.search?t.search.replace(/^\?/,""):"",hash:t.hash?t.hash.replace(/^#/,""):"",hostname:t.hostname,port:t.port,pathname:"/"===t.pathname.charAt(0)?t.pathname:"/"+t.pathname}}return e=o(window.location.href),function(n){var t=r.isString(n)?o(n):n;return t.protocol===e.protocol&&t.host===e.host}}():function(){return function(){return!0}}()},4733:(e,n,t)=>{"use strict";var r=t(7210);e.exports=function(e,n){r.forEach(e,(function(t,r){r!==n&&r.toUpperCase()===n.toUpperCase()&&(e[n]=t,delete e[r])}))}},3423:(e,n,t)=>{"use strict";var r=t(7210),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var n,t,a,i={};return e?(r.forEach(e.split("\n"),(function(e){if(a=e.indexOf(":"),n=r.trim(e.substr(0,a)).toLowerCase(),t=r.trim(e.substr(a+1)),n){if(i[n]&&o.indexOf(n)>=0)return;i[n]="set-cookie"===n?(i[n]?i[n]:[]).concat([t]):i[n]?i[n]+", "+t:t}})),i):i}},5178:e=>{"use strict";e.exports=function(e){return function(n){return e.apply(null,n)}}},3170:(e,n,t)=>{"use strict";var r=t(8593),o={};["object","boolean","number","function","string","symbol"].forEach((function(e,n){o[e]=function(t){return typeof t===e||"a"+(n<1?"n ":" ")+e}}));var a={},i=r.version.split(".");function s(e,n){for(var t=n?n.split("."):i,r=e.split("."),o=0;o<3;o++){if(t[o]>r[o])return!0;if(t[o]<r[o])return!1}return!1}function l(e,n,t){if("object"!==typeof e)throw new TypeError("options must be an object");var r=Object.keys(e),o=r.length;while(o-- >0){var a=r[o],i=n[a];if(i){var s=e[a],l=void 0===s||i(s,a,e);if(!0!==l)throw new TypeError("option "+a+" must be "+l)}else if(!0!==t)throw Error("Unknown option "+a)}}o.transitional=function(e,n,t){var o=n&&s(n);function i(e,n){return"[Axios v"+r.version+"] Transitional option '"+e+"'"+n+(t?". "+t:"")}return function(t,r,s){if(!1===e)throw new Error(i(r," has been removed in "+n));return o&&!a[r]&&(a[r]=!0,console.warn(i(r," has been deprecated since v"+n+" and will be removed in the near future"))),!e||e(t,r,s)}},e.exports={isOlderVersion:s,assertOptions:l,validators:o}},7210:(e,n,t)=>{"use strict";var r=t(2938),o=Object.prototype.toString;function a(e){return"[object Array]"===o.call(e)}function i(e){return"undefined"===typeof e}function s(e){return null!==e&&!i(e)&&null!==e.constructor&&!i(e.constructor)&&"function"===typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}function l(e){return"[object ArrayBuffer]"===o.call(e)}function c(e){return"undefined"!==typeof FormData&&e instanceof FormData}function d(e){var n;return n="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer,n}function u(e){return"string"===typeof e}function p(e){return"number"===typeof e}function m(e){return null!==e&&"object"===typeof e}function f(e){if("[object Object]"!==o.call(e))return!1;var n=Object.getPrototypeOf(e);return null===n||n===Object.prototype}function h(e){return"[object Date]"===o.call(e)}function w(e){return"[object File]"===o.call(e)}function b(e){return"[object Blob]"===o.call(e)}function g(e){return"[object Function]"===o.call(e)}function k(e){return m(e)&&g(e.pipe)}function y(e){return"undefined"!==typeof URLSearchParams&&e instanceof URLSearchParams}function v(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function _(){return("undefined"===typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!==typeof window&&"undefined"!==typeof document)}function x(e,n){if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),a(e))for(var t=0,r=e.length;t<r;t++)n.call(null,e[t],t,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&n.call(null,e[o],o,e)}function S(){var e={};function n(n,t){f(e[t])&&f(n)?e[t]=S(e[t],n):f(n)?e[t]=S({},n):a(n)?e[t]=n.slice():e[t]=n}for(var t=0,r=arguments.length;t<r;t++)x(arguments[t],n);return e}function C(e,n,t){return x(n,(function(n,o){e[o]=t&&"function"===typeof n?r(n,t):n})),e}function E(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}e.exports={isArray:a,isArrayBuffer:l,isBuffer:s,isFormData:c,isArrayBufferView:d,isString:u,isNumber:p,isObject:m,isPlainObject:f,isUndefined:i,isDate:h,isFile:w,isBlob:b,isFunction:g,isStream:k,isURLSearchParams:y,isStandardBrowserEnv:_,forEach:x,merge:S,extend:C,trim:v,stripBOM:E}},1411:(e,n,t)=>{"use strict";t.r(n),t.d(n,{default:()=>a});var r=t(7083),o=t(3906);const a=(0,r.xr)((({app:e})=>{e.use(o.ZP)}))},7435:(e,n,t)=>{"use strict";t.d(n,{Z:()=>I});var r=t(3673),o=t(1959),a=t(8880),i=t(4554),s=t(9754),l=t(2012),c=t(4716),d=t(1436);function u(e,n=250){let t,r=!1;return function(){return!1===r&&(r=!0,setTimeout((()=>{r=!1}),n),t=e.apply(this,arguments)),t}}function p(e,n,t,r){!0===t.modifiers.stop&&(0,c.sT)(e);const o=t.modifiers.color;let a=t.modifiers.center;a=!0===a||!0===r;const i=document.createElement("span"),s=document.createElement("span"),d=(0,c.FK)(e),{left:u,top:p,width:m,height:f}=n.getBoundingClientRect(),h=Math.sqrt(m*m+f*f),w=h/2,b=(m-h)/2+"px",g=a?b:d.left-u-w+"px",k=(f-h)/2+"px",y=a?k:d.top-p-w+"px";s.className="q-ripple__inner",(0,l.iv)(s,{height:`${h}px`,width:`${h}px`,transform:`translate3d(${g},${y},0) scale3d(.2,.2,1)`,opacity:0}),i.className="q-ripple"+(o?" text-"+o:""),i.setAttribute("dir","ltr"),i.appendChild(s),n.appendChild(i);const v=()=>{i.remove(),clearTimeout(_)};t.abort.push(v);let _=setTimeout((()=>{s.classList.add("q-ripple__inner--enter"),s.style.transform=`translate3d(${b},${k},0) scale3d(1,1,1)`,s.style.opacity=.2,_=setTimeout((()=>{s.classList.remove("q-ripple__inner--enter"),s.classList.add("q-ripple__inner--leave"),s.style.opacity=0,_=setTimeout((()=>{i.remove(),t.abort.splice(t.abort.indexOf(v),1)}),275)}),250)}),50)}function m(e,{modifiers:n,value:t,arg:r,instance:o}){const a=Object.assign({},o.$q.config.ripple,n,t);e.modifiers={early:!0===a.early,stop:!0===a.stop,center:!0===a.center,color:a.color||r,keyCodes:[].concat(a.keyCodes||13)}}const f={name:"ripple",beforeMount(e,n){const t={enabled:!1!==n.value,modifiers:{},abort:[],start(n){!0===t.enabled&&!0!==n.qSkipRipple&&(!0===t.modifiers.early?!0===["mousedown","touchstart"].includes(n.type):"click"===n.type)&&p(n,e,t,!0===n.qKeyEvent)},keystart:u((n=>{!0===t.enabled&&!0!==n.qSkipRipple&&!0===(0,d.So)(n,t.modifiers.keyCodes)&&n.type==="key"+(!0===t.modifiers.early?"down":"up")&&p(n,e,t,!0)}),300)};m(t,n),e.__qripple=t,(0,c.M0)(t,"main",[[e,"mousedown","start","passive"],[e,"touchstart","start","passive"],[e,"click","start","passive"],[e,"keydown","keystart","passive"],[e,"keyup","keystart","passive"]])},updated(e,n){if(n.oldValue!==n.value){const t=e.__qripple;t.enabled=!1!==n.value,!0===t.enabled&&Object(n.value)===n.value&&m(t,n)}},beforeUnmount(e){const n=e.__qripple;n.abort.forEach((e=>{e()})),(0,c.ul)(n,"main"),delete e._qripple}};t(9377);const h={left:"start",center:"center",right:"end",between:"between",around:"around",evenly:"evenly",stretch:"stretch"},w=Object.keys(h),b={align:{type:String,validator:e=>w.includes(e)}};function g(e){return(0,o.Fl)((()=>{const n=void 0===e.align?!0===e.vertical?"stretch":"left":e.align;return`${!0===e.vertical?"items":"justify"}-${h[n]}`}))}var k=t(2417);t(5363),t(71);function y(e){return void 0!==e.appContext.config.globalProperties.$router}function v(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}function _(e,n){return(e.aliasOf||e)===(n.aliasOf||n)}function x(e,n){for(const t in n){const r=n[t],o=e[t];if("string"===typeof r){if(r!==o)return!1}else if(!1===Array.isArray(o)||o.length!==r.length||r.some(((e,n)=>e!==o[n])))return!1}return!0}function S(e,n){return!0===Array.isArray(n)?e.length===n.length&&e.every(((e,t)=>e===n[t])):1===e.length&&e[0]===n}function C(e,n){return!0===Array.isArray(e)?S(e,n):!0===Array.isArray(n)?S(n,e):e===n}function E(e,n){if(Object.keys(e).length!==Object.keys(n).length)return!1;for(const t in e)if(!1===C(e[t],n[t]))return!1;return!0}const P={to:[String,Object],replace:Boolean,exact:Boolean,activeClass:{type:String,default:"q-router-link--active"},exactActiveClass:{type:String,default:"q-router-link--exact-active"},disable:Boolean};function j(){const e=(0,r.FN)(),{props:n,attrs:t,proxy:a}=e,i=y(e),s=(0,o.Fl)((()=>!0===i&&!0!==n.disable&&void 0!==n.to&&null!==n.to&&""!==n.to)),l=(0,o.Fl)((()=>{if(!0===s.value)try{return a.$router.resolve(n.to)}catch(e){}return null})),d=(0,o.Fl)((()=>null!==l.value)),u=(0,o.Fl)((()=>!0===d.value?"a":n.tag||"div")),p=(0,o.Fl)((()=>{if(!1===d.value)return null;const{matched:e}=l.value,{length:n}=e,t=e[n-1];if(void 0===t)return-1;const r=a.$route.matched;if(0===r.length)return-1;const o=r.findIndex(_.bind(null,t));if(o>-1)return o;const i=v(e[n-2]);return n>1&&v(t)===i&&r[r.length-1].path!==i?r.findIndex(_.bind(null,e[n-2])):o})),m=(0,o.Fl)((()=>!0===d.value&&p.value>-1&&x(a.$route.params,l.value.params))),f=(0,o.Fl)((()=>!0===m.value&&p.value===a.$route.matched.length-1&&E(a.$route.params,l.value.params))),h=(0,o.Fl)((()=>!0===d.value?!0===f.value?` ${n.exactActiveClass} ${n.activeClass}`:!0===n.exact?"":!0===m.value?` ${n.activeClass}`:"":"")),w=(0,o.Fl)((()=>!0===d.value?{href:l.value.href,target:t.target,role:"link"}:{}));function b(e){return!(!0===n.disable||e.metaKey||e.altKey||e.ctrlKey||e.shiftKey||!0!==e.__qNavigate&&!0===e.defaultPrevented||void 0!==e.button&&0!==e.button||"_blank"===t.target)&&((0,c.X$)(e),a.$router[!0===n.replace?"replace":"push"](n.to).catch((()=>{})))}return{hasLink:d,linkTag:u,linkRoute:l,linkIsActive:m,linkIsExactActive:f,linkClass:h,linkProps:w,navigateToLink:b}}const L={none:0,xs:4,sm:8,md:16,lg:24,xl:32},O={xs:8,sm:10,md:14,lg:20,xl:24},T={...k.LU,...P,type:{type:String,default:"button"},label:[Number,String],icon:String,iconRight:String,round:Boolean,outline:Boolean,flat:Boolean,unelevated:Boolean,rounded:Boolean,push:Boolean,glossy:Boolean,size:String,fab:Boolean,fabMini:Boolean,padding:String,color:String,textColor:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,tabindex:[Number,String],ripple:{type:[Boolean,Object],default:!0},align:{...b.align,default:"center"},stack:Boolean,stretch:Boolean,loading:{type:Boolean,default:null},disable:Boolean};function M(e){const n=(0,k.ZP)(e,O),t=g(e),{hasLink:r,linkProps:a,navigateToLink:i}=j(),s=(0,o.Fl)((()=>{const t=!1===e.fab&&!1===e.fabMini?n.value:{};return void 0!==e.padding?Object.assign({},t,{padding:e.padding.split(/\s+/).map((e=>e in L?L[e]+"px":e)).join(" "),minWidth:"0",minHeight:"0"}):t})),l=(0,o.Fl)((()=>!0===e.rounded||!0===e.fab||!0===e.fabMini)),c=(0,o.Fl)((()=>!0!==e.disable&&!0!==e.loading)),d=(0,o.Fl)((()=>!0===c.value?e.tabindex||0:-1)),u=(0,o.Fl)((()=>"a"===e.type||!0===r.value)),p=(0,o.Fl)((()=>!0===e.flat?"flat":!0===e.outline?"outline":!0===e.push?"push":!0===e.unelevated?"unelevated":"standard")),m=(0,o.Fl)((()=>{const n={tabindex:d.value};return"a"===e.type||"button"===e.type&&!0===r.value||(n.type=e.type),!0===r.value?(Object.assign(n,a.value),n.role="link"):n.role="a"===e.type?"link":"button",!0===e.loading&&void 0!==e.percentage&&Object.assign(n,{role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":e.percentage}),!0===e.disable&&(n.disabled="",n["aria-disabled"]="true"),n})),f=(0,o.Fl)((()=>{let n;return void 0!==e.color?n=!0===e.flat||!0===e.outline?`text-${e.textColor||e.color}`:`bg-${e.color} text-${e.textColor||"white"}`:e.textColor&&(n=`text-${e.textColor}`),`q-btn--${p.value} q-btn--`+(!0===e.round?"round":"rectangle"+(!0===l.value?" q-btn--rounded":""))+(void 0!==n?" "+n:"")+(!0===c.value?" q-btn--actionable q-focusable q-hoverable":!0===e.disable?" disabled":"")+(!0===e.fab?" q-btn--fab":!0===e.fabMini?" q-btn--fab-mini":"")+(!0===e.noCaps?" q-btn--no-uppercase":"")+(!0===e.dense?" q-btn--dense":"")+(!0===e.stretch?" no-border-radius self-stretch":"")+(!0===e.glossy?" glossy":"")})),h=(0,o.Fl)((()=>t.value+(!0===e.stack?" column":" row")+(!0===e.noWrap?" no-wrap text-no-wrap":"")+(!0===e.loading?" q-btn__content--hidden":"")));return{classes:f,style:s,innerClasses:h,attributes:m,hasLink:r,isLink:u,navigateToLink:i,isActionable:c}}var A=t(7657);const{passiveCapture:F}=c.rU;let z=null,$=null,R=null;const I=(0,r.aZ)({name:"QBtn",props:{...T,percentage:Number,darkPercentage:Boolean},emits:["click","keydown","touchstart","mousedown","keyup"],setup(e,{slots:n,emit:t}){const{proxy:l}=(0,r.FN)(),{classes:u,style:p,innerClasses:m,attributes:h,hasLink:w,isLink:b,navigateToLink:g,isActionable:k}=M(e),y=(0,o.iH)(null),v=(0,o.iH)(null);let _,x,S=null;const C=(0,o.Fl)((()=>void 0!==e.label&&null!==e.label&&""!==e.label)),E=(0,o.Fl)((()=>!0!==e.disable&&!1!==e.ripple&&{keyCodes:!0===b.value?[13,32]:[13],...!0===e.ripple?{}:e.ripple})),P=(0,o.Fl)((()=>({center:e.round}))),j=(0,o.Fl)((()=>{const n=Math.max(0,Math.min(100,e.percentage));return n>0?{transition:"transform 0.6s",transform:`translateX(${n-100}%)`}:{}})),L=(0,o.Fl)((()=>!0===e.loading?{onMousedown:D,onTouchstartPassive:D,onClick:D,onKeydown:D,onKeyup:D}:!0===k.value?{onClick:T,onKeydown:I,onMousedown:q,onTouchstartPassive:N}:{onClick:c.NS})),O=(0,o.Fl)((()=>({ref:y,class:"q-btn q-btn-item non-selectable no-outline "+u.value,style:p.value,...h.value,...L.value})));function T(n){if(null!==y.value){if(void 0!==n){if(!0===n.defaultPrevented)return;const t=document.activeElement;if("submit"===e.type&&t!==document.body&&!1===y.value.contains(t)&&!1===t.contains(y.value)){y.value.focus();const e=()=>{document.removeEventListener("keydown",c.NS,!0),document.removeEventListener("keyup",e,F),null!==y.value&&y.value.removeEventListener("blur",e,F)};document.addEventListener("keydown",c.NS,!0),document.addEventListener("keyup",e,F),y.value.addEventListener("blur",e,F)}}if(!0===w.value){const e=()=>{n.__qNavigate=!0,g(n)};t("click",n,e),!0!==n.defaultPrevented&&e()}else t("click",n)}}function I(e){null!==y.value&&(!0===(0,d.So)(e,[13,32])&&((0,c.NS)(e),$!==y.value&&(null!==$&&B(),y.value.focus(),$=y.value,y.value.classList.add("q-btn--active"),document.addEventListener("keyup",H,!0),y.value.addEventListener("blur",H,F))),t("keydown",e))}function N(e){null!==y.value&&(z!==y.value&&(null!==z&&B(),z=y.value,S=e.target,S.addEventListener("touchcancel",H,F),S.addEventListener("touchend",H,F)),_=!0,clearTimeout(x),x=setTimeout((()=>{_=!1}),200),t("touchstart",e))}function q(e){null!==y.value&&(R!==y.value&&(null!==R&&B(),R=y.value,y.value.classList.add("q-btn--active"),document.addEventListener("mouseup",H,F)),e.qSkipRipple=!0===_,t("mousedown",e))}function H(e){if(null!==y.value&&(void 0===e||"blur"!==e.type||document.activeElement!==y.value)){if(void 0!==e&&"keyup"===e.type){if($===y.value&&!0===(0,d.So)(e,[13,32])){const n=new MouseEvent("click",e);n.qKeyEvent=!0,!0===e.defaultPrevented&&(0,c.X$)(n),!0===e.cancelBubble&&(0,c.sT)(n),y.value.dispatchEvent(n),(0,c.NS)(e),e.qKeyEvent=!0}t("keyup",e)}B()}}function B(e){const n=v.value;!0===e||z!==y.value&&R!==y.value||null===n||n===document.activeElement||(n.setAttribute("tabindex",-1),n.focus()),z===y.value&&(null!==S&&(S.removeEventListener("touchcancel",H,F),S.removeEventListener("touchend",H,F)),z=S=null),R===y.value&&(document.removeEventListener("mouseup",H,F),R=null),$===y.value&&(document.removeEventListener("keyup",H,!0),null!==y.value&&y.value.removeEventListener("blur",H,F),$=null),null!==y.value&&y.value.classList.remove("q-btn--active")}function D(e){e.qSkipRipple=!0}return(0,r.Jd)((()=>{B(!0)})),Object.assign(l,{click:T}),()=>{let t=[];void 0!==e.icon&&t.push((0,r.h)(i.Z,{name:e.icon,left:!1===e.stack&&!0===C.value,role:"img","aria-hidden":"true"})),!0===C.value&&t.push((0,r.h)("span",{class:"block"},[e.label])),t=(0,A.vs)(n.default,t),void 0!==e.iconRight&&!1===e.round&&t.push((0,r.h)(i.Z,{name:e.iconRight,right:!1===e.stack&&!0===C.value,role:"img","aria-hidden":"true"}));const o=[(0,r.h)("span",{class:"q-focus-helper",ref:v})];return!0===e.loading&&void 0!==e.percentage&&o.push((0,r.h)("span",{class:"q-btn__progress absolute-full overflow-hidden"},[(0,r.h)("span",{class:"q-btn__progress-indicator fit block"+(!0===e.darkPercentage?" q-btn__progress--dark":""),style:j.value})])),o.push((0,r.h)("span",{class:"q-btn__content text-center col items-center q-anchor--skip "+m.value},t)),null!==e.loading&&o.push((0,r.h)(a.uT,{name:"q-transition--fade"},(()=>!0===e.loading?[(0,r.h)("span",{key:"loading",class:"absolute-full flex flex-center"},void 0!==n.loading?n.loading():[(0,r.h)(s.Z)])]:null))),(0,r.wy)((0,r.h)(!0===b.value?"a":"button",O.value,o),[[f,E.value,void 0,P.value]])}}})},4554:(e,n,t)=>{"use strict";t.d(n,{Z:()=>g});t(71);var r=t(3673),o=t(1959),a=t(2417),i=t(7657);const s=e=>e,l=e=>`ionicons ${e}`,c={"icon-":s,"bt-":e=>`bt ${e}`,"eva-":e=>`eva ${e}`,"ion-md":l,"ion-ios":l,"ion-logo":l,"mdi-":e=>`mdi ${e}`,"iconfont ":s,"ti-":e=>`themify-icon ${e}`,"bi-":e=>`bootstrap-icons ${e}`},d={o_:"-outlined",r_:"-round",s_:"-sharp"},u=new RegExp("^("+Object.keys(c).join("|")+")"),p=new RegExp("^("+Object.keys(d).join("|")+")"),m=/^M/,f=/^img:/,h=/^svguse:/,w=/^ion-/,b=/^[l|f]a[s|r|l|b|d]? /,g=(0,r.aZ)({name:"QIcon",props:{...a.LU,tag:{type:String,default:"i"},name:String,color:String,left:Boolean,right:Boolean},setup(e,{slots:n}){const{proxy:{$q:t}}=(0,r.FN)(),s=(0,a.ZP)(e),l=(0,o.Fl)((()=>"q-icon"+(!0===e.left?" on-left":"")+(!0===e.right?" on-right":"")+(void 0!==e.color?` text-${e.color}`:""))),g=(0,o.Fl)((()=>{let n,o=e.name;if(!o)return{none:!0,cls:l.value};if(null!==t.iconMapFn){const e=t.iconMapFn(o);if(void 0!==e){if(void 0===e.icon)return{cls:e.cls+" "+l.value,content:void 0!==e.content?e.content:" "};o=e.icon}}if(!0===m.test(o)){const[e,n]=o.split("|");return{svg:!0,cls:l.value,nodes:e.split("&&").map((e=>{const[n,t,o]=e.split("@@");return(0,r.h)("path",{style:t,d:n,transform:o})})),viewBox:void 0!==n?n:"0 0 24 24"}}if(!0===f.test(o))return{img:!0,cls:l.value,src:o.substring(4)};if(!0===h.test(o)){const[e,n]=o.split("|");return{svguse:!0,cls:l.value,src:e.substring(7),viewBox:void 0!==n?n:"0 0 24 24"}}let a=" ";const i=o.match(u);if(null!==i)n=c[i[1]](o);else if(!0===b.test(o))n=o;else if(!0===w.test(o))n=`ionicons ion-${!0===t.platform.is.ios?"ios":"md"}${o.substr(3)}`;else{n="notranslate material-icons";const e=o.match(p);null!==e&&(o=o.substring(2),n+=d[e[1]]),a=o}return{cls:n+" "+l.value,content:a}}));return()=>{const t={class:g.value.cls,style:s.value,"aria-hidden":"true",role:"presentation"};return!0===g.value.none?(0,r.h)(e.tag,t,(0,i.KR)(n.default)):!0===g.value.img?(t.src=g.value.src,(0,r.h)("img",t)):!0===g.value.svg?(t.viewBox=g.value.viewBox,(0,r.h)("svg",t,(0,i.vs)(n.default,g.value.nodes))):!0===g.value.svguse?(t.viewBox=g.value.viewBox,(0,r.h)("svg",t,(0,i.vs)(n.default,[(0,r.h)("use",{"xlink:href":g.value.src})]))):(0,r.h)(e.tag,t,(0,i.vs)(n.default,[g.value.content]))}}})},4090:(e,n,t)=>{"use strict";t.d(n,{Z:()=>x});var r=t(3673),o=t(1959),a=t(4688),i=t(2012);const s=[null,document,document.body,document.scrollingElement,document.documentElement];function l(e,n){let t=(0,i.sb)(n);if(void 0===t){if(void 0===e||null===e)return window;t=e.closest(".scroll,.scroll-y,.overflow-auto")}return s.includes(t)?window:t}function c(e){return e===window?window.pageYOffset||window.scrollY||document.body.scrollTop||0:e.scrollTop}function d(e){return e===window?window.pageXOffset||window.scrollX||document.body.scrollLeft||0:e.scrollLeft}let u;function p(){if(void 0!==u)return u;const e=document.createElement("p"),n=document.createElement("div");(0,i.iv)(e,{width:"100%",height:"200px"}),(0,i.iv)(n,{position:"absolute",top:"0px",left:"0px",visibility:"hidden",width:"200px",height:"150px",overflow:"hidden"}),n.appendChild(e),document.body.appendChild(n);const t=e.offsetWidth;n.style.overflow="scroll";let r=e.offsetWidth;return t===r&&(r=n.clientWidth),n.remove(),u=t-r,u}var m=t(4716);const{passive:f}=m.rU,h=["both","horizontal","vertical"],w=(0,r.aZ)({name:"QScrollObserver",props:{axis:{type:String,validator:e=>h.includes(e),default:"vertical"},debounce:[String,Number],scrollTarget:{default:void 0}},emits:["scroll"],setup(e,{emit:n}){const t={position:{top:0,left:0},direction:"down",directionChanged:!1,delta:{top:0,left:0},inflectionPoint:{top:0,left:0}};let o,a,i=null;function s(){i=null;const r=Math.max(0,c(o)),a=d(o),s={top:r-t.position.top,left:a-t.position.left};if("vertical"===e.axis&&0===s.top||"horizontal"===e.axis&&0===s.left)return;const l=Math.abs(s.top)>=Math.abs(s.left)?s.top<0?"up":"down":s.left<0?"left":"right";t.position={top:r,left:a},t.directionChanged=t.direction!==l,t.delta=s,!0===t.directionChanged&&(t.direction=l,t.inflectionPoint=t.position),n("scroll",{...t})}function u(){o=l(a,e.scrollTarget),o.addEventListener("scroll",h,f),h(!0)}function p(){void 0!==o&&(o.removeEventListener("scroll",h,f),o=void 0)}function h(n){!0===n||0===e.debounce||"0"===e.debounce?s():null===i&&(i=e.debounce?setTimeout(s,e.debounce):requestAnimationFrame(s))}(0,r.YP)((()=>e.scrollTarget),(()=>{p(),u()}));const w=(0,r.FN)();return(0,r.bv)((()=>{a=w.proxy.$el.parentNode,u()})),(0,r.Jd)((()=>{clearTimeout(i),cancelAnimationFrame(i),p()})),Object.assign(w.proxy,{trigger:h,getPosition:()=>t}),m.ZT}});function b(){const e=(0,o.iH)(!a.uX.value);return!1===e.value&&(0,r.bv)((()=>{e.value=!0})),e}const g="undefined"!==typeof ResizeObserver,k=!0===g?{}:{style:"display:block;position:absolute;top:0;left:0;right:0;bottom:0;height:100%;width:100%;overflow:hidden;pointer-events:none;z-index:-1;",url:"about:blank"},y=(0,r.aZ)({name:"QResizeObserver",props:{debounce:{type:[String,Number],default:100}},emits:["resize"],setup(e,{emit:n}){let t,o,a={width:-1,height:-1};function i(n){!0===n||0===e.debounce||"0"===e.debounce?s():t||(t=setTimeout(s,e.debounce))}function s(){if(t=void 0,o){const{offsetWidth:e,offsetHeight:t}=o;e===a.width&&t===a.height||(a={width:e,height:t},n("resize",a))}}const l=(0,r.FN)();if(Object.assign(l.proxy,{trigger:i}),!0===g){let e;return(0,r.bv)((()=>{(0,r.Y3)((()=>{o=l.proxy.$el.parentNode,o&&(e=new ResizeObserver(i),e.observe(o),s())}))})),(0,r.Jd)((()=>{clearTimeout(t),void 0!==e&&(void 0!==e.disconnect?e.disconnect():o&&e.unobserve(o))})),m.ZT}{const e=b();let n;function a(){clearTimeout(t),void 0!==n&&(void 0!==n.removeEventListener&&n.removeEventListener("resize",i,m.rU.passive),n=void 0)}function c(){a(),o&&o.contentDocument&&(n=o.contentDocument.defaultView,n.addEventListener("resize",i,m.rU.passive),s())}return(0,r.bv)((()=>{(0,r.Y3)((()=>{o=l.proxy.$el,o&&c()}))})),(0,r.Jd)(a),()=>{if(!0===e.value)return(0,r.h)("object",{style:k.style,tabindex:-1,type:"text/html",data:k.url,"aria-hidden":"true",onLoad:c})}}}});var v=t(7657),_=t(2547);const x=(0,r.aZ)({name:"QLayout",props:{container:Boolean,view:{type:String,default:"hhh lpr fff",validator:e=>/^(h|l)h(h|r) lpr (f|l)f(f|r)$/.test(e.toLowerCase())},onScroll:Function,onScrollHeight:Function,onResize:Function},setup(e,{slots:n,emit:t}){const{proxy:{$q:i}}=(0,r.FN)(),s=(0,o.iH)(null),l=(0,o.iH)(i.screen.height),c=(0,o.iH)(!0===e.container?0:i.screen.width),d=(0,o.iH)({position:0,direction:"down",inflectionPoint:0}),u=(0,o.iH)(0),m=(0,o.iH)(!0===a.uX.value?0:p()),f=(0,o.Fl)((()=>"q-layout q-layout--"+(!0===e.container?"containerized":"standard"))),h=(0,o.Fl)((()=>!1===e.container?{minHeight:i.screen.height+"px"}:null)),b=(0,o.Fl)((()=>0!==m.value?{[!0===i.lang.rtl?"left":"right"]:`${m.value}px`}:null)),g=(0,o.Fl)((()=>0!==m.value?{[!0===i.lang.rtl?"right":"left"]:0,[!0===i.lang.rtl?"left":"right"]:`-${m.value}px`,width:`calc(100% + ${m.value}px)`}:null));function k(n){if(!0===e.container||!0!==document.qScrollPrevented){const r={position:n.position.top,direction:n.direction,directionChanged:n.directionChanged,inflectionPoint:n.inflectionPoint.top,delta:n.delta.top};d.value=r,void 0!==e.onScroll&&t("scroll",r)}}function x(n){const{height:r,width:o}=n;let a=!1;l.value!==r&&(a=!0,l.value=r,void 0!==e.onScrollHeight&&t("scroll-height",r),C()),c.value!==o&&(a=!0,c.value=o),!0===a&&void 0!==e.onResize&&t("resize",n)}function S({height:e}){u.value!==e&&(u.value=e,C())}function C(){if(!0===e.container){const e=l.value>u.value?p():0;m.value!==e&&(m.value=e)}}let E;const P={instances:{},view:(0,o.Fl)((()=>e.view)),isContainer:(0,o.Fl)((()=>e.container)),rootRef:s,height:l,containerHeight:u,scrollbarWidth:m,totalWidth:(0,o.Fl)((()=>c.value+m.value)),rows:(0,o.Fl)((()=>{const n=e.view.toLowerCase().split(" ");return{top:n[0].split(""),middle:n[1].split(""),bottom:n[2].split("")}})),header:(0,o.qj)({size:0,offset:0,space:!1}),right:(0,o.qj)({size:300,offset:0,space:!1}),footer:(0,o.qj)({size:0,offset:0,space:!1}),left:(0,o.qj)({size:300,offset:0,space:!1}),scroll:d,animate(){void 0!==E?clearTimeout(E):document.body.classList.add("q-body--layout-animate"),E=setTimeout((()=>{document.body.classList.remove("q-body--layout-animate"),E=void 0}),155)},update(e,n,t){P[e][n]=t}};return(0,r.JJ)(_.YE,P),()=>{const t=(0,v.vs)(n.default,[(0,r.h)(w,{onScroll:k}),(0,r.h)(y,{onResize:x})]),o=(0,r.h)("div",{class:f.value,style:h.value,ref:!0===e.container?void 0:s},t);return!0===e.container?(0,r.h)("div",{class:"q-layout-container overflow-hidden",ref:s},[(0,r.h)(y,{onResize:S}),(0,r.h)("div",{class:"absolute-full",style:b.value},[(0,r.h)("div",{class:"scroll",style:g.value},[o])])]):o}}})},4379:(e,n,t)=>{"use strict";t.d(n,{Z:()=>s});var r=t(3673),o=t(1959),a=t(7657),i=t(2547);const s=(0,r.aZ)({name:"QPage",props:{padding:Boolean,styleFn:Function},setup(e,{slots:n}){const{proxy:{$q:t}}=(0,r.FN)(),s=(0,r.f3)(i.YE);(0,r.f3)(i.Mw,(()=>{console.error("QPage needs to be child of QPageContainer")}));const l=(0,o.Fl)((()=>{const n=(!0===s.header.space?s.header.size:0)+(!0===s.footer.space?s.footer.size:0);if("function"===typeof e.styleFn){const r=!0===s.isContainer.value?s.containerHeight.value:t.screen.height;return e.styleFn(n,r)}return{minHeight:!0===s.isContainer.value?s.containerHeight.value-n+"px":0===t.screen.height?0!==n?`calc(100vh - ${n}px)`:"100vh":t.screen.height-n+"px"}})),c=(0,o.Fl)((()=>"q-page "+(!0===e.padding?" q-layout-padding":"")));return()=>(0,r.h)("main",{class:c.value,style:l.value},(0,a.KR)(n.default))}})},2652:(e,n,t)=>{"use strict";t.d(n,{Z:()=>s});var r=t(3673),o=t(1959),a=t(7657),i=t(2547);const s=(0,r.aZ)({name:"QPageContainer",setup(e,{slots:n}){const{proxy:{$q:t}}=(0,r.FN)(),s=(0,r.f3)(i.YE,(()=>{console.error("QPageContainer needs to be child of QLayout")}));(0,r.JJ)(i.Mw,!0);const l=(0,o.Fl)((()=>{const e={};return!0===s.header.space&&(e.paddingTop=`${s.header.size}px`),!0===s.right.space&&(e["padding"+(!0===t.lang.rtl?"Left":"Right")]=`${s.right.size}px`),!0===s.footer.space&&(e.paddingBottom=`${s.footer.size}px`),!0===s.left.space&&(e["padding"+(!0===t.lang.rtl?"Right":"Left")]=`${s.left.size}px`),e}));return()=>(0,r.h)("div",{class:"q-page-container",style:l.value},(0,a.KR)(n.default))}})},9754:(e,n,t)=>{"use strict";t.d(n,{Z:()=>l});var r=t(3673),o=t(1959),a=t(2417);const i={size:{type:[Number,String],default:"1em"},color:String};function s(e){return{cSize:(0,o.Fl)((()=>e.size in a.Ok?`${a.Ok[e.size]}px`:e.size)),classes:(0,o.Fl)((()=>"q-spinner"+(e.color?` text-${e.color}`:"")))}}const l=(0,r.aZ)({name:"QSpinner",props:{...i,thickness:{type:Number,default:5}},setup(e){const{cSize:n,classes:t}=s(e);return()=>(0,r.h)("svg",{class:t.value+" q-spinner-mat",width:n.value,height:n.value,viewBox:"25 25 50 50"},[(0,r.h)("circle",{class:"path",cx:"50",cy:"50",r:"20",fill:"none",stroke:"currentColor","stroke-width":e.thickness,"stroke-miterlimit":"10"})])}})},2417:(e,n,t)=>{"use strict";t.d(n,{Ok:()=>o,LU:()=>a,ZP:()=>i});var r=t(1959);const o={xs:18,sm:24,md:32,lg:38,xl:46},a={size:String};function i(e,n=o){return(0,r.Fl)((()=>void 0!==e.size?{fontSize:e.size in n?`${n[e.size]}px`:e.size}:null))}},4705:(e,n,t)=>{"use strict";t.d(n,{Z:()=>i});var r=t(2002);const o={name:"material-icons",type:{positive:"check_circle",negative:"warning",info:"info",warning:"priority_high"},arrow:{up:"arrow_upward",right:"arrow_forward",down:"arrow_downward",left:"arrow_back",dropdown:"arrow_drop_down"},chevron:{left:"chevron_left",right:"chevron_right"},colorPicker:{spectrum:"gradient",tune:"tune",palette:"style"},pullToRefresh:{icon:"refresh"},carousel:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down",navigationIcon:"lens"},chip:{remove:"cancel",selected:"check"},datetime:{arrowLeft:"chevron_left",arrowRight:"chevron_right",now:"access_time",today:"today"},editor:{bold:"format_bold",italic:"format_italic",strikethrough:"strikethrough_s",underline:"format_underlined",unorderedList:"format_list_bulleted",orderedList:"format_list_numbered",subscript:"vertical_align_bottom",superscript:"vertical_align_top",hyperlink:"link",toggleFullscreen:"fullscreen",quote:"format_quote",left:"format_align_left",center:"format_align_center",right:"format_align_right",justify:"format_align_justify",print:"print",outdent:"format_indent_decrease",indent:"format_indent_increase",removeFormat:"format_clear",formatting:"text_format",fontSize:"format_size",align:"format_align_left",hr:"remove",undo:"undo",redo:"redo",heading:"format_size",code:"code",size:"format_size",font:"font_download",viewSource:"code"},expansionItem:{icon:"keyboard_arrow_down",denseIcon:"arrow_drop_down"},fab:{icon:"add",activeIcon:"close"},field:{clear:"cancel",error:"error"},pagination:{first:"first_page",prev:"keyboard_arrow_left",next:"keyboard_arrow_right",last:"last_page"},rating:{icon:"grade"},stepper:{done:"check",active:"edit",error:"warning"},tabs:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down"},table:{arrowUp:"arrow_upward",warning:"warning",firstPage:"first_page",prevPage:"chevron_left",nextPage:"chevron_right",lastPage:"last_page"},tree:{icon:"play_arrow"},uploader:{done:"done",clear:"clear",add:"add_box",upload:"cloud_upload",removeQueue:"clear_all",removeUploaded:"done_all"}},a=(0,r.Z)({iconMapFn:null,__icons:{}},{set(e,n){const t={...e,rtl:!0===e.rtl};t.set=a.set,Object.assign(a.__icons,t)},install({$q:e,iconSet:n,ssrContext:t}){void 0!==e.config.iconMapFn&&(this.iconMapFn=e.config.iconMapFn),e.iconSet=this.__icons,Object.defineProperty(e,"iconMapFn",{get:()=>this.iconMapFn,set:e=>{this.iconMapFn=e}}),!0===this.__installed?void 0!==n&&this.set(n):this.set(n||o)}}),i=a},8954:(e,n,t)=>{"use strict";t.d(n,{$:()=>L,Z:()=>M});var r=t(8880),o=t(4688),a=t(2002),i=t(4716);function s(e,n=250,t){let r;function o(){const o=arguments,a=()=>{r=void 0,!0!==t&&e.apply(this,o)};clearTimeout(r),!0===t&&void 0===r&&e.apply(this,o),r=setTimeout(a,n)}return o.cancel=()=>{clearTimeout(r)},o}const l=["sm","md","lg","xl"],{passive:c}=i.rU,d=(0,a.Z)({width:0,height:0,name:"xs",sizes:{sm:600,md:1024,lg:1440,xl:1920},lt:{sm:!0,md:!0,lg:!0,xl:!0},gt:{xs:!1,sm:!1,md:!1,lg:!1},xs:!0,sm:!1,md:!1,lg:!1,xl:!1},{setSizes:i.ZT,setDebounce:i.ZT,install({$q:e,onSSRHydrated:n}){if(e.screen=this,!0===this.__installed)return void(void 0!==e.config.screen&&(!1===e.config.screen.bodyClasses?document.body.classList.remove(`screen--${this.name}`):this.__update(!0)));const t=void 0!==e.config.screen&&!0===e.config.screen.bodyClasses;this.__update=e=>{const n=window.innerWidth,r=window.innerHeight;if(r!==this.height&&(this.height=r),n!==this.width)this.width=n;else if(!0!==e)return;let o=this.sizes;this.gt.xs=n>=o.sm,this.gt.sm=n>=o.md,this.gt.md=n>=o.lg,this.gt.lg=n>=o.xl,this.lt.sm=n<o.sm,this.lt.md=n<o.md,this.lt.lg=n<o.lg,this.lt.xl=n<o.xl,this.xs=this.lt.sm,this.sm=!0===this.gt.xs&&!0===this.lt.md,this.md=!0===this.gt.sm&&!0===this.lt.lg,this.lg=!0===this.gt.md&&!0===this.lt.xl,this.xl=this.gt.lg,o=(!0===this.xs?"xs":!0===this.sm&&"sm")||!0===this.md&&"md"||!0===this.lg&&"lg"||"xl",o!==this.name&&(!0===t&&(document.body.classList.remove(`screen--${this.name}`),document.body.classList.add(`screen--${o}`)),this.name=o)};let r,a={},i=16;this.setSizes=e=>{l.forEach((n=>{void 0!==e[n]&&(a[n]=e[n])}))},this.setDebounce=e=>{i=e};const d=()=>{const e=getComputedStyle(document.body),n=void 0!==window.visualViewport?window.visualViewport:window;e.getPropertyValue("--q-size-sm")&&l.forEach((n=>{this.sizes[n]=parseInt(e.getPropertyValue(`--q-size-${n}`),10)})),this.setSizes=e=>{l.forEach((n=>{e[n]&&(this.sizes[n]=e[n])})),this.__update(!0)},this.setDebounce=e=>{void 0!==r&&n.removeEventListener("resize",r,c),r=e>0?s(this.__update,e):this.__update,n.addEventListener("resize",r,c)},this.setDebounce(i),Object.keys(a).length>0?(this.setSizes(a),a=void 0):this.__update(),!0===t&&"xs"===this.name&&document.body.classList.add("screen--xs")};!0===o.uX.value?n.push(d):d()}});var u=t(9058);t(71);const p=()=>!0;function m(e){return"string"===typeof e&&""!==e&&"/"!==e&&"#/"!==e}function f(e){return!0===e.startsWith("#")&&(e=e.substr(1)),!1===e.startsWith("/")&&(e="/"+e),!0===e.endsWith("/")&&(e=e.substr(0,e.length-1)),"#"+e}function h(e){if(!1===e.backButtonExit)return()=>!1;if("*"===e.backButtonExit)return p;const n=["#/"];return!0===Array.isArray(e.backButtonExit)&&n.push(...e.backButtonExit.filter(m).map(f)),()=>n.includes(window.location.hash)}const w={__history:[],add:i.ZT,remove:i.ZT,install({$q:e}){if(!0===this.__installed)return;const{cordova:n,capacitor:t}=o.Lp.is;if(!0!==n&&!0!==t)return;const r=e.config[!0===n?"cordova":"capacitor"];if(void 0!==r&&!1===r.backButton)return;if(!0===t&&(void 0===window.Capacitor||void 0===window.Capacitor.Plugins.App))return;this.add=e=>{void 0===e.condition&&(e.condition=p),this.__history.push(e)},this.remove=e=>{const n=this.__history.indexOf(e);n>=0&&this.__history.splice(n,1)};const a=h(Object.assign({backButtonExit:!0},r)),i=()=>{if(this.__history.length){const e=this.__history[this.__history.length-1];!0===e.condition()&&(this.__history.pop(),e.handler())}else!0===a()?navigator.app.exitApp():window.history.back()};!0===n?document.addEventListener("deviceready",(()=>{document.addEventListener("backbutton",i,!1)})):window.Capacitor.Plugins.App.addListener("backButton",i)}};var b=t(1845);t(5363);function g(e,n,t=document.body){if("string"!==typeof e)throw new TypeError("Expected a string as propName");if("string"!==typeof n)throw new TypeError("Expected a string as value");if(!(t instanceof Element))throw new TypeError("Expected a DOM element");t.style.setProperty(`--q-${e}`,n)}var k=t(1436);function y(e){return!0===e.ios?"ios":!0===e.android?"android":void 0}function v({is:e,has:n,within:t},r){const o=[!0===e.desktop?"desktop":"mobile",(!1===n.touch?"no-":"")+"touch"];if(!0===e.mobile){const n=y(e);void 0!==n&&o.push("platform-"+n)}if(!0===e.nativeMobile){const n=e.nativeMobileWrapper;o.push(n),o.push("native-mobile"),!0!==e.ios||void 0!==r[n]&&!1===r[n].iosStatusBarPadding||o.push("q-ios-padding")}else!0===e.electron?o.push("electron"):!0===e.bex&&o.push("bex");return!0===t.iframe&&o.push("within-iframe"),o}function _(){const e=document.body.className;let n=e;void 0!==o.aG&&(n=n.replace("desktop","platform-ios mobile")),!0===o.Lp.has.touch&&(n=n.replace("no-touch","touch")),!0===o.Lp.within.iframe&&(n+=" within-iframe"),e!==n&&(document.body.className=n)}function x(e){for(const n in e)g(n,e[n])}const S={install(e){const{$q:n}=e;if(void 0!==n.config.brand&&x(n.config.brand),!0!==this.__installed){if(!0===o.uX.value)_();else{const e=v(o.Lp,n.config);document.body.classList.add.apply(document.body.classList,e)}!0===o.Lp.is.ios&&document.body.addEventListener("touchstart",i.ZT),window.addEventListener("keydown",k.ZK,!0)}}};var C=t(4705),E=t(2547),P=t(5578);const j=[o.ZP,S,u.Z,d,w,b.Z,C.Z];function L(e,n){const t=(0,r.ri)(e);t.config.globalProperties=n.config.globalProperties;const{reload:o,...a}=n._context;return Object.assign(t._context,a),t}function O(e,n){n.forEach((n=>{n.install(e),n.__installed=!0}))}function T(e,n,t){e.config.globalProperties.$q=t.$q,e.provide(E.Ng,t.$q),O(t,j),void 0!==n.components&&Object.values(n.components).forEach((n=>{Object(n)===n&&void 0!==n.name&&e.component(n.name,n)})),void 0!==n.directives&&Object.values(n.directives).forEach((n=>{Object(n)===n&&void 0!==n.name&&e.directive(n.name,n)})),void 0!==n.plugins&&O(t,Object.values(n.plugins).filter((e=>"function"===typeof e.install&&!1===j.includes(e)))),!0===o.uX.value&&(t.$q.onSSRHydrated=()=>{t.onSSRHydrated.forEach((e=>{e()})),t.$q.onSSRHydrated=()=>{}})}const M=function(e,n={}){const t={version:"2.2.2"};!1===P.Uf?(void 0!==n.config&&Object.assign(P.w6,n.config),t.config={...P.w6},(0,P.tP)()):t.config=n.config||{},T(e,n,{parentApp:e,$q:t,lang:n.lang,iconSet:n.iconSet,onSSRHydrated:[]})}},1845:(e,n,t)=>{"use strict";t.d(n,{Z:()=>s});t(5363);var r=t(2002);const o={isoName:"en-US",nativeName:"English (US)",label:{clear:"Clear",ok:"OK",cancel:"Cancel",close:"Close",set:"Set",select:"Select",reset:"Reset",remove:"Remove",update:"Update",create:"Create",search:"Search",filter:"Filter",refresh:"Refresh"},date:{days:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),daysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),firstDayOfWeek:0,format24h:!1,pluralDay:"days"},table:{noData:"No data available",noResults:"No matching records found",loading:"Loading...",selectedRecords:e=>1===e?"1 record selected.":(0===e?"No":e)+" records selected.",recordsPerPage:"Records per page:",allRows:"All",pagination:(e,n,t)=>e+"-"+n+" of "+t,columns:"Columns"},editor:{url:"URL",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",underline:"Underline",unorderedList:"Unordered List",orderedList:"Ordered List",subscript:"Subscript",superscript:"Superscript",hyperlink:"Hyperlink",toggleFullscreen:"Toggle Fullscreen",quote:"Quote",left:"Left align",center:"Center align",right:"Right align",justify:"Justify align",print:"Print",outdent:"Decrease indentation",indent:"Increase indentation",removeFormat:"Remove formatting",formatting:"Formatting",fontSize:"Font Size",align:"Align",hr:"Insert Horizontal Rule",undo:"Undo",redo:"Redo",heading1:"Heading 1",heading2:"Heading 2",heading3:"Heading 3",heading4:"Heading 4",heading5:"Heading 5",heading6:"Heading 6",paragraph:"Paragraph",code:"Code",size1:"Very small",size2:"A bit small",size3:"Normal",size4:"Medium-large",size5:"Big",size6:"Very big",size7:"Maximum",defaultFont:"Default Font",viewSource:"View Source"},tree:{noNodes:"No nodes available",noResults:"No matching nodes found"}};function a(){const e=!0===Array.isArray(navigator.languages)&&navigator.languages.length>0?navigator.languages[0]:navigator.language;if("string"===typeof e)return e.split(/[-_]/).map(((e,n)=>0===n?e.toLowerCase():n>1||e.length<4?e.toUpperCase():e[0].toUpperCase()+e.slice(1).toLowerCase())).join("-")}const i=(0,r.Z)({__langPack:{}},{getLocale:a,set(e=o,n){const t={...e,rtl:!0===e.rtl,getLocale:a};{const e=document.documentElement;e.setAttribute("dir",!0===t.rtl?"rtl":"ltr"),e.setAttribute("lang",t.isoName),t.set=i.set,Object.assign(i.__langPack,t),i.props=t,i.isoName=t.isoName,i.nativeName=t.nativeName}},install({$q:e,lang:n,ssrContext:t}){e.lang=i.__langPack,!0===this.__installed?void 0!==n&&this.set(n):this.set(n||o)}}),s=i},9058:(e,n,t)=>{"use strict";t.d(n,{Z:()=>i});t(5363);var r=t(2002),o=t(4688);const a=(0,r.Z)({isActive:!1,mode:!1},{__media:void 0,set(e){a.mode=e,"auto"===e?(void 0===a.__media&&(a.__media=window.matchMedia("(prefers-color-scheme: dark)"),a.__updateMedia=()=>{a.set("auto")},a.__media.addListener(a.__updateMedia)),e=a.__media.matches):void 0!==a.__media&&(a.__media.removeListener(a.__updateMedia),a.__media=void 0),a.isActive=!0===e,document.body.classList.remove("body--"+(!0===e?"light":"dark")),document.body.classList.add("body--"+(!0===e?"dark":"light"))},toggle(){a.set(!1===a.isActive)},install({$q:e,onSSRHydrated:n,ssrContext:t}){const{dark:r}=e.config;if(e.dark=this,!0===this.__installed&&void 0===r)return;this.isActive=!0===r;const a=void 0!==r&&r;if(!0===o.uX.value){const e=e=>{this.__fromSSR=e},t=this.set;this.set=e,e(a),n.push((()=>{this.set=t,this.set(this.__fromSSR)}))}else this.set(a)}}),i=a},4688:(e,n,t)=>{"use strict";t.d(n,{uX:()=>o,aG:()=>a,Lp:()=>f,ZP:()=>w});var r=t(1959);const o=(0,r.iH)(!1);let a,i=!1;function s(e,n){const t=/(edge|edga|edgios)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(vivaldi)[\/]([\w.]+)/.exec(e)||/(chrome|crios)[\/]([\w.]+)/.exec(e)||/(iemobile)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+).*(version)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(firefox|fxios)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[\/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("trident")>=0&&/(rv)(?::| )([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[];return{browser:t[5]||t[3]||t[1]||"",version:t[2]||t[4]||"0",versionNumber:t[4]||t[2]||"0",platform:n[0]||""}}function l(e){return/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(silk)/.exec(e)||/(android)/.exec(e)||/(win)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||/(playbook)/.exec(e)||/(bb)/.exec(e)||/(blackberry)/.exec(e)||[]}const c="ontouchstart"in window||window.navigator.maxTouchPoints>0;function d(e){a={is:{...e}},delete e.mac,delete e.desktop;const n=Math.min(window.innerHeight,window.innerWidth)>414?"ipad":"iphone";Object.assign(e,{mobile:!0,ios:!0,platform:n,[n]:!0})}function u(e){const n=e.toLowerCase(),t=l(n),r=s(n,t),o={};r.browser&&(o[r.browser]=!0,o.version=r.version,o.versionNumber=parseInt(r.versionNumber,10)),r.platform&&(o[r.platform]=!0);const a=o.android||o.ios||o.bb||o.blackberry||o.ipad||o.iphone||o.ipod||o.kindle||o.playbook||o.silk||o["windows phone"];return!0===a||n.indexOf("mobile")>-1?(o.mobile=!0,o.edga||o.edgios?(o.edge=!0,r.browser="edge"):o.crios?(o.chrome=!0,r.browser="chrome"):o.fxios&&(o.firefox=!0,r.browser="firefox")):o.desktop=!0,(o.ipod||o.ipad||o.iphone)&&(o.ios=!0),o["windows phone"]&&(o.winphone=!0,delete o["windows phone"]),(o.chrome||o.opr||o.safari||o.vivaldi||!0===o.mobile&&!0!==o.ios&&!0!==a)&&(o.webkit=!0),(o.safari&&o.blackberry||o.bb)&&(r.browser="blackberry",o.blackberry=!0),o.safari&&o.playbook&&(r.browser="playbook",o.playbook=!0),o.opr&&(r.browser="opera",o.opera=!0),o.safari&&o.android&&(r.browser="android",o.android=!0),o.safari&&o.kindle&&(r.browser="kindle",o.kindle=!0),o.safari&&o.silk&&(r.browser="silk",o.silk=!0),o.vivaldi&&(r.browser="vivaldi",o.vivaldi=!0),o.name=r.browser,o.platform=r.platform,n.indexOf("electron")>-1?o.electron=!0:document.location.href.indexOf("-extension://")>-1?o.bex=!0:(void 0!==window.Capacitor?(o.capacitor=!0,o.nativeMobile=!0,o.nativeMobileWrapper="capacitor"):void 0===window._cordovaNative&&void 0===window.cordova||(o.cordova=!0,o.nativeMobile=!0,o.nativeMobileWrapper="cordova"),!0===c&&!0===o.mac&&(!0===o.desktop&&!0===o.safari||!0===o.nativeMobile&&!0!==o.android&&!0!==o.ios&&!0!==o.ipad)&&d(o)),o}const p=navigator.userAgent||navigator.vendor||window.opera,m={has:{touch:!1,webStorage:!1},within:{iframe:!1}},f={userAgent:p,is:u(p),has:{touch:c},within:{iframe:window.self!==window.top}},h={install(e){const{$q:n}=e;!0===o.value?(e.onSSRHydrated.push((()=>{o.value=!1,Object.assign(n.platform,f),a=void 0})),n.platform=(0,r.qj)(this)):n.platform=this}};{let e;Object.defineProperty(f.has,"webStorage",{get:()=>{if(void 0!==e)return e;try{if(window.localStorage)return e=!0,!0}catch(n){}return e=!1,!1}}),i=!0===f.is.ios&&-1===window.navigator.vendor.toLowerCase().indexOf("apple"),!0===o.value?Object.assign(h,f,a,m):Object.assign(h,f)}const w=h},2012:(e,n,t)=>{"use strict";t.d(n,{iv:()=>o,sb:()=>a});var r=t(1959);function o(e,n){const t=e.style;Object.keys(n).forEach((e=>{t[e]=n[e]}))}function a(e){if(void 0===e||null===e)return;if("string"===typeof e)try{return document.querySelector(e)||void 0}catch(t){return}const n=!0===(0,r.dq)(e)?e.value:e;return n?n.$el||n:void 0}},4716:(e,n,t)=>{"use strict";t.d(n,{rU:()=>r,ZT:()=>o,FK:()=>a,sT:()=>i,X$:()=>s,NS:()=>l,M0:()=>c,ul:()=>d});t(71);const r={hasPassive:!1,passiveCapture:!0,notPassiveCapture:!0};try{const e=Object.defineProperty({},"passive",{get(){Object.assign(r,{hasPassive:!0,passive:{passive:!0},notPassive:{passive:!1},passiveCapture:{passive:!0,capture:!0},notPassiveCapture:{passive:!1,capture:!0}})}});window.addEventListener("qtest",null,e),window.removeEventListener("qtest",null,e)}catch(u){}function o(){}function a(e){return e.touches&&e.touches[0]?e=e.touches[0]:e.changedTouches&&e.changedTouches[0]?e=e.changedTouches[0]:e.targetTouches&&e.targetTouches[0]&&(e=e.targetTouches[0]),{top:e.clientY,left:e.clientX}}function i(e){e.stopPropagation()}function s(e){!1!==e.cancelable&&e.preventDefault()}function l(e){!1!==e.cancelable&&e.preventDefault(),e.stopPropagation()}function c(e,n,t){const o=`__q_${n}_evt`;e[o]=void 0!==e[o]?e[o].concat(t):t,t.forEach((n=>{n[0].addEventListener(n[1],e[n[2]],r[n[3]])}))}function d(e,n){const t=`__q_${n}_evt`;void 0!==e[t]&&(e[t].forEach((n=>{n[0].removeEventListener(n[1],e[n[2]],r[n[3]])})),e[t]=void 0)}},2002:(e,n,t)=>{"use strict";t.d(n,{Z:()=>o});var r=t(1959);const o=(e,n)=>{const t={},o=(0,r.qj)(e);return Object.keys(e).forEach((e=>{t[e]={get:()=>o[e],set:n=>{o[e]=n}}})),Object.defineProperties(n,t),n}},5578:(e,n,t)=>{"use strict";t.d(n,{w6:()=>r,Uf:()=>o,tP:()=>a});const r={};let o=!1;function a(){o=!0}},1436:(e,n,t)=>{"use strict";t.d(n,{ZK:()=>o,So:()=>i});let r=!1;function o(e){r=!0===e.isComposing}function a(e){return!0===r||e!==Object(e)||!0===e.isComposing||!0===e.qKeyEvent}function i(e,n){return!0!==a(e)&&[].concat(n).includes(e.keyCode)}},7657:(e,n,t)=>{"use strict";function r(e,n){return void 0!==e&&e()||n}function o(e,n){return void 0!==e?n.concat(e()):n}function a(e,n){return void 0===e?n:void 0!==n?n.concat(e()):e()}t.d(n,{KR:()=>r,vs:()=>o,pf:()=>a})},2547:(e,n,t)=>{"use strict";t.d(n,{Ng:()=>r,YE:()=>o,Mw:()=>a});const r="_q_",o="_q_l_",a="_q_pc_"},9592:(e,n,t)=>{"use strict";t.d(n,{Z:()=>i});var r=t(8954),o=t(1845),a=t(4705);const i={version:"2.2.2",install:r.Z,lang:o.Z,iconSet:a.Z}},7083:e=>{e.exports.xr=function(e){return e},e.exports.BC=function(e){return e},e.exports.h=function(e){return e}},392:(e,n,t)=>{var r=t(7358),o=t(419),a=t(3353),i=r.TypeError;e.exports=function(e){if(o(e))return e;throw i(a(e)+" is not a function")}},8248:(e,n,t)=>{var r=t(7358),o=t(419),a=r.String,i=r.TypeError;e.exports=function(e){if("object"==typeof e||o(e))return e;throw i("Can't set "+a(e)+" as a prototype")}},2852:(e,n,t)=>{var r=t(854),o=t(1074),a=t(928),i=r("unscopables"),s=Array.prototype;void 0==s[i]&&a.f(s,i,{configurable:!0,value:o(null)}),e.exports=function(e){s[i][e]=!0}},6412:(e,n,t)=>{"use strict";var r=t(1021).charAt;e.exports=function(e,n,t){return n+(t?r(e,n).length:1)}},7950:(e,n,t)=>{var r=t(7358),o=t(776),a=r.String,i=r.TypeError;e.exports=function(e){if(o(e))return e;throw i(a(e)+" is not an object")}},6963:(e,n,t)=>{var r=t(7120),o=t(1801),a=t(6042),i=function(e){return function(n,t,i){var s,l=r(n),c=a(l),d=o(i,c);if(e&&t!=t){while(c>d)if(s=l[d++],s!=s)return!0}else for(;c>d;d++)if((e||d in l)&&l[d]===t)return e||d||0;return!e&&-1}};e.exports={includes:i(!0),indexOf:i(!1)}},330:(e,n,t)=>{var r=t(7358),o=t(6894),a=t(7593),i=t(776),s=t(854),l=s("species"),c=r.Array;e.exports=function(e){var n;return o(e)&&(n=e.constructor,a(n)&&(n===c||o(n.prototype))?n=void 0:i(n)&&(n=n[l],null===n&&(n=void 0))),void 0===n?c:n}},6340:(e,n,t)=>{var r=t(330);e.exports=function(e,n){return new(r(e))(0===n?0:n)}},5173:(e,n,t)=>{var r=t(1890),o=r({}.toString),a=r("".slice);e.exports=function(e){return a(o(e),8,-1)}},5976:(e,n,t)=>{var r=t(7358),o=t(5705),a=t(419),i=t(5173),s=t(854),l=s("toStringTag"),c=r.Object,d="Arguments"==i(function(){return arguments}()),u=function(e,n){try{return e[n]}catch(t){}};e.exports=o?i:function(e){var n,t,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(t=u(n=c(e),l))?t:d?i(n):"Object"==(r=i(n))&&a(n.callee)?"Arguments":r}},8438:(e,n,t)=>{var r=t(7322),o=t(7764),a=t(2404),i=t(928);e.exports=function(e,n){for(var t=o(n),s=i.f,l=a.f,c=0;c<t.length;c++){var d=t[c];r(e,d)||s(e,d,l(n,d))}}},123:(e,n,t)=>{var r=t(6400);e.exports=!r((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},5912:(e,n,t)=>{"use strict";var r=t(4848).IteratorPrototype,o=t(1074),a=t(5442),i=t(1061),s=t(2184),l=function(){return this};e.exports=function(e,n,t){var c=n+" Iterator";return e.prototype=o(r,{next:a(1,t)}),i(e,c,!1,!0),s[c]=l,e}},1904:(e,n,t)=>{var r=t(9631),o=t(928),a=t(5442);e.exports=r?function(e,n,t){return o.f(e,n,a(1,t))}:function(e,n,t){return e[n]=t,e}},5442:e=>{e.exports=function(e,n){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:n}}},8810:(e,n,t)=>{"use strict";var r=t(8934),o=t(3577),a=t(6692),i=t(7961),s=t(419),l=t(5912),c=t(4945),d=t(6184),u=t(1061),p=t(1904),m=t(298),f=t(854),h=t(2184),w=t(4848),b=i.PROPER,g=i.CONFIGURABLE,k=w.IteratorPrototype,y=w.BUGGY_SAFARI_ITERATORS,v=f("iterator"),_="keys",x="values",S="entries",C=function(){return this};e.exports=function(e,n,t,i,f,w,E){l(t,n,i);var P,j,L,O=function(e){if(e===f&&z)return z;if(!y&&e in A)return A[e];switch(e){case _:return function(){return new t(this,e)};case x:return function(){return new t(this,e)};case S:return function(){return new t(this,e)}}return function(){return new t(this)}},T=n+" Iterator",M=!1,A=e.prototype,F=A[v]||A["@@iterator"]||f&&A[f],z=!y&&F||O(f),$="Array"==n&&A.entries||F;if($&&(P=c($.call(new e)),P!==Object.prototype&&P.next&&(a||c(P)===k||(d?d(P,k):s(P[v])||m(P,v,C)),u(P,T,!0,!0),a&&(h[T]=C))),b&&f==x&&F&&F.name!==x&&(!a&&g?p(A,"name",x):(M=!0,z=function(){return o(F,this)})),f)if(j={values:O(x),keys:w?z:O(_),entries:O(S)},E)for(L in j)(y||M||!(L in A))&&m(A,L,j[L]);else r({target:n,proto:!0,forced:y||M},j);return a&&!E||A[v]===z||m(A,v,z,{name:f}),h[n]=z,j}},9631:(e,n,t)=>{var r=t(6400);e.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},5354:(e,n,t)=>{var r=t(7358),o=t(776),a=r.document,i=o(a)&&o(a.createElement);e.exports=function(e){return i?a.createElement(e):{}}},4296:e=>{e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},8753:(e,n,t)=>{var r=t(5354),o=r("span").classList,a=o&&o.constructor&&o.constructor.prototype;e.exports=a===Object.prototype?void 0:a},9173:(e,n,t)=>{var r=t(9694);e.exports=r("navigator","userAgent")||""},5068:(e,n,t)=>{var r,o,a=t(7358),i=t(9173),s=a.process,l=a.Deno,c=s&&s.versions||l&&l.version,d=c&&c.v8;d&&(r=d.split("."),o=r[0]>0&&r[0]<4?1:+(r[0]+r[1])),!o&&i&&(r=i.match(/Edge\/(\d+)/),(!r||r[1]>=74)&&(r=i.match(/Chrome\/(\d+)/),r&&(o=+r[1]))),e.exports=o},2875:e=>{e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8934:(e,n,t)=>{var r=t(7358),o=t(2404).f,a=t(1904),i=t(298),s=t(3534),l=t(8438),c=t(4389);e.exports=function(e,n){var t,d,u,p,m,f,h=e.target,w=e.global,b=e.stat;if(d=w?r:b?r[h]||s(h,{}):(r[h]||{}).prototype,d)for(u in n){if(m=n[u],e.noTargetGet?(f=o(d,u),p=f&&f.value):p=d[u],t=c(w?u:h+(b?".":"#")+u,e.forced),!t&&void 0!==p){if(typeof m==typeof p)continue;l(m,p)}(e.sham||p&&p.sham)&&a(m,"sham",!0),i(d,u,m,e)}}},6400:e=>{e.exports=function(e){try{return!!e()}catch(n){return!0}}},9529:(e,n,t)=>{"use strict";t(7280);var r=t(1890),o=t(298),a=t(4348),i=t(6400),s=t(854),l=t(1904),c=s("species"),d=RegExp.prototype;e.exports=function(e,n,t,u){var p=s(e),m=!i((function(){var n={};return n[p]=function(){return 7},7!=""[e](n)})),f=m&&!i((function(){var n=!1,t=/a/;return"split"===e&&(t={},t.constructor={},t.constructor[c]=function(){return t},t.flags="",t[p]=/./[p]),t.exec=function(){return n=!0,null},t[p](""),!n}));if(!m||!f||t){var h=r(/./[p]),w=n(p,""[e],(function(e,n,t,o,i){var s=r(e),l=n.exec;return l===a||l===d.exec?m&&!i?{done:!0,value:h(n,t,o)}:{done:!0,value:s(t,n,o)}:{done:!1}}));o(String.prototype,e,w[0]),o(d,p,w[1])}u&&l(d[p],"sham",!0)}},4817:(e,n,t)=>{"use strict";var r=t(7358),o=t(6894),a=t(6042),i=t(422),s=r.TypeError,l=function(e,n,t,r,c,d,u,p){var m,f,h=c,w=0,b=!!u&&i(u,p);while(w<r){if(w in t){if(m=b?b(t[w],w,n):t[w],d>0&&o(m))f=a(m),h=l(e,n,m,f,h,d-1)-1;else{if(h>=9007199254740991)throw s("Exceed the acceptable array length");e[h]=m}h++}w++}return h};e.exports=l},4157:e=>{var n=Function.prototype,t=n.apply,r=n.bind,o=n.call;e.exports="object"==typeof Reflect&&Reflect.apply||(r?o.bind(t):function(){return o.apply(t,arguments)})},422:(e,n,t)=>{var r=t(1890),o=t(392),a=r(r.bind);e.exports=function(e,n){return o(e),void 0===n?e:a?a(e,n):function(){return e.apply(n,arguments)}}},3577:e=>{var n=Function.prototype.call;e.exports=n.bind?n.bind(n):function(){return n.apply(n,arguments)}},7961:(e,n,t)=>{var r=t(9631),o=t(7322),a=Function.prototype,i=r&&Object.getOwnPropertyDescriptor,s=o(a,"name"),l=s&&"something"===function(){}.name,c=s&&(!r||r&&i(a,"name").configurable);e.exports={EXISTS:s,PROPER:l,CONFIGURABLE:c}},1890:e=>{var n=Function.prototype,t=n.bind,r=n.call,o=t&&t.bind(r);e.exports=t?function(e){return e&&o(r,e)}:function(e){return e&&function(){return r.apply(e,arguments)}}},9694:(e,n,t)=>{var r=t(7358),o=t(419),a=function(e){return o(e)?e:void 0};e.exports=function(e,n){return arguments.length<2?a(r[e]):r[e]&&r[e][n]}},2344:(e,n,t)=>{var r=t(392);e.exports=function(e,n){var t=e[n];return null==t?void 0:r(t)}},8716:(e,n,t)=>{var r=t(1890),o=t(7475),a=Math.floor,i=r("".charAt),s=r("".replace),l=r("".slice),c=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,d=/\$([$&'`]|\d{1,2})/g;e.exports=function(e,n,t,r,u,p){var m=t+e.length,f=r.length,h=d;return void 0!==u&&(u=o(u),h=c),s(p,h,(function(o,s){var c;switch(i(s,0)){case"$":return"$";case"&":return e;case"`":return l(n,0,t);case"'":return l(n,m);case"<":c=u[l(s,1,-1)];break;default:var d=+s;if(0===d)return o;if(d>f){var p=a(d/10);return 0===p?o:p<=f?void 0===r[p-1]?i(s,1):r[p-1]+i(s,1):o}c=r[d-1]}return void 0===c?"":c}))}},7358:(e,n,t)=>{var r=function(e){return e&&e.Math==Math&&e};e.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof t.g&&t.g)||function(){return this}()||Function("return this")()},7322:(e,n,t)=>{var r=t(1890),o=t(7475),a=r({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,n){return a(o(e),n)}},600:e=>{e.exports={}},9970:(e,n,t)=>{var r=t(9694);e.exports=r("document","documentElement")},7021:(e,n,t)=>{var r=t(9631),o=t(6400),a=t(5354);e.exports=!r&&!o((function(){return 7!=Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a}))},2985:(e,n,t)=>{var r=t(7358),o=t(1890),a=t(6400),i=t(5173),s=r.Object,l=o("".split);e.exports=a((function(){return!s("z").propertyIsEnumerable(0)}))?function(e){return"String"==i(e)?l(e,""):s(e)}:s},3725:(e,n,t)=>{var r=t(1890),o=t(419),a=t(1089),i=r(Function.toString);o(a.inspectSource)||(a.inspectSource=function(e){return i(e)}),e.exports=a.inspectSource},7624:(e,n,t)=>{var r,o,a,i=t(9262),s=t(7358),l=t(1890),c=t(776),d=t(1904),u=t(7322),p=t(1089),m=t(203),f=t(600),h="Object already initialized",w=s.TypeError,b=s.WeakMap,g=function(e){return a(e)?o(e):r(e,{})},k=function(e){return function(n){var t;if(!c(n)||(t=o(n)).type!==e)throw w("Incompatible receiver, "+e+" required");return t}};if(i||p.state){var y=p.state||(p.state=new b),v=l(y.get),_=l(y.has),x=l(y.set);r=function(e,n){if(_(y,e))throw new w(h);return n.facade=e,x(y,e,n),n},o=function(e){return v(y,e)||{}},a=function(e){return _(y,e)}}else{var S=m("state");f[S]=!0,r=function(e,n){if(u(e,S))throw new w(h);return n.facade=e,d(e,S,n),n},o=function(e){return u(e,S)?e[S]:{}},a=function(e){return u(e,S)}}e.exports={set:r,get:o,has:a,enforce:g,getterFor:k}},6894:(e,n,t)=>{var r=t(5173);e.exports=Array.isArray||function(e){return"Array"==r(e)}},419:e=>{e.exports=function(e){return"function"==typeof e}},7593:(e,n,t)=>{var r=t(1890),o=t(6400),a=t(419),i=t(5976),s=t(9694),l=t(3725),c=function(){},d=[],u=s("Reflect","construct"),p=/^\s*(?:class|function)\b/,m=r(p.exec),f=!p.exec(c),h=function(e){if(!a(e))return!1;try{return u(c,d,e),!0}catch(n){return!1}},w=function(e){if(!a(e))return!1;switch(i(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}return f||!!m(p,l(e))};e.exports=!u||o((function(){var e;return h(h.call)||!h(Object)||!h((function(){e=!0}))||e}))?w:h},4389:(e,n,t)=>{var r=t(6400),o=t(419),a=/#|\.prototype\./,i=function(e,n){var t=l[s(e)];return t==d||t!=c&&(o(n)?r(n):!!n)},s=i.normalize=function(e){return String(e).replace(a,".").toLowerCase()},l=i.data={},c=i.NATIVE="N",d=i.POLYFILL="P";e.exports=i},776:(e,n,t)=>{var r=t(419);e.exports=function(e){return"object"==typeof e?null!==e:r(e)}},6692:e=>{e.exports=!1},410:(e,n,t)=>{var r=t(7358),o=t(9694),a=t(419),i=t(7673),s=t(8476),l=r.Object;e.exports=s?function(e){return"symbol"==typeof e}:function(e){var n=o("Symbol");return a(n)&&i(n.prototype,l(e))}},4848:(e,n,t)=>{"use strict";var r,o,a,i=t(6400),s=t(419),l=t(1074),c=t(4945),d=t(298),u=t(854),p=t(6692),m=u("iterator"),f=!1;[].keys&&(a=[].keys(),"next"in a?(o=c(c(a)),o!==Object.prototype&&(r=o)):f=!0);var h=void 0==r||i((function(){var e={};return r[m].call(e)!==e}));h?r={}:p&&(r=l(r)),s(r[m])||d(r,m,(function(){return this})),e.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:f}},2184:e=>{e.exports={}},6042:(e,n,t)=>{var r=t(4068);e.exports=function(e){return r(e.length)}},7529:(e,n,t)=>{var r=t(5068),o=t(6400);e.exports=!!Object.getOwnPropertySymbols&&!o((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},9262:(e,n,t)=>{var r=t(7358),o=t(419),a=t(3725),i=r.WeakMap;e.exports=o(i)&&/native code/.test(a(i))},1074:(e,n,t)=>{var r,o=t(7950),a=t(3605),i=t(2875),s=t(600),l=t(9970),c=t(5354),d=t(203),u=">",p="<",m="prototype",f="script",h=d("IE_PROTO"),w=function(){},b=function(e){return p+f+u+e+p+"/"+f+u},g=function(e){e.write(b("")),e.close();var n=e.parentWindow.Object;return e=null,n},k=function(){var e,n=c("iframe"),t="java"+f+":";return n.style.display="none",l.appendChild(n),n.src=String(t),e=n.contentWindow.document,e.open(),e.write(b("document.F=Object")),e.close(),e.F},y=function(){try{r=new ActiveXObject("htmlfile")}catch(n){}y="undefined"!=typeof document?document.domain&&r?g(r):k():g(r);var e=i.length;while(e--)delete y[m][i[e]];return y()};s[h]=!0,e.exports=Object.create||function(e,n){var t;return null!==e?(w[m]=o(e),t=new w,w[m]=null,t[h]=e):t=y(),void 0===n?t:a(t,n)}},3605:(e,n,t)=>{var r=t(9631),o=t(928),a=t(7950),i=t(7120),s=t(9158);e.exports=r?Object.defineProperties:function(e,n){a(e);var t,r=i(n),l=s(n),c=l.length,d=0;while(c>d)o.f(e,t=l[d++],r[t]);return e}},928:(e,n,t)=>{var r=t(7358),o=t(9631),a=t(7021),i=t(7950),s=t(8618),l=r.TypeError,c=Object.defineProperty;n.f=o?c:function(e,n,t){if(i(e),n=s(n),i(t),a)try{return c(e,n,t)}catch(r){}if("get"in t||"set"in t)throw l("Accessors not supported");return"value"in t&&(e[n]=t.value),e}},2404:(e,n,t)=>{var r=t(9631),o=t(3577),a=t(5604),i=t(5442),s=t(7120),l=t(8618),c=t(7322),d=t(7021),u=Object.getOwnPropertyDescriptor;n.f=r?u:function(e,n){if(e=s(e),n=l(n),d)try{return u(e,n)}catch(t){}if(c(e,n))return i(!o(a.f,e,n),e[n])}},1454:(e,n,t)=>{var r=t(1587),o=t(2875),a=o.concat("length","prototype");n.f=Object.getOwnPropertyNames||function(e){return r(e,a)}},4199:(e,n)=>{n.f=Object.getOwnPropertySymbols},4945:(e,n,t)=>{var r=t(7358),o=t(7322),a=t(419),i=t(7475),s=t(203),l=t(123),c=s("IE_PROTO"),d=r.Object,u=d.prototype;e.exports=l?d.getPrototypeOf:function(e){var n=i(e);if(o(n,c))return n[c];var t=n.constructor;return a(t)&&n instanceof t?t.prototype:n instanceof d?u:null}},7673:(e,n,t)=>{var r=t(1890);e.exports=r({}.isPrototypeOf)},1587:(e,n,t)=>{var r=t(1890),o=t(7322),a=t(7120),i=t(6963).indexOf,s=t(600),l=r([].push);e.exports=function(e,n){var t,r=a(e),c=0,d=[];for(t in r)!o(s,t)&&o(r,t)&&l(d,t);while(n.length>c)o(r,t=n[c++])&&(~i(d,t)||l(d,t));return d}},9158:(e,n,t)=>{var r=t(1587),o=t(2875);e.exports=Object.keys||function(e){return r(e,o)}},5604:(e,n)=>{"use strict";var t={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,o=r&&!t.call({1:2},1);n.f=o?function(e){var n=r(this,e);return!!n&&n.enumerable}:t},6184:(e,n,t)=>{var r=t(1890),o=t(7950),a=t(8248);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,n=!1,t={};try{e=r(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set),e(t,[]),n=t instanceof Array}catch(i){}return function(t,r){return o(t),a(r),n?e(t,r):t.__proto__=r,t}}():void 0)},9308:(e,n,t)=>{var r=t(7358),o=t(3577),a=t(419),i=t(776),s=r.TypeError;e.exports=function(e,n){var t,r;if("string"===n&&a(t=e.toString)&&!i(r=o(t,e)))return r;if(a(t=e.valueOf)&&!i(r=o(t,e)))return r;if("string"!==n&&a(t=e.toString)&&!i(r=o(t,e)))return r;throw s("Can't convert object to primitive value")}},7764:(e,n,t)=>{var r=t(9694),o=t(1890),a=t(1454),i=t(4199),s=t(7950),l=o([].concat);e.exports=r("Reflect","ownKeys")||function(e){var n=a.f(s(e)),t=i.f;return t?l(n,t(e)):n}},298:(e,n,t)=>{var r=t(7358),o=t(419),a=t(7322),i=t(1904),s=t(3534),l=t(3725),c=t(7624),d=t(7961).CONFIGURABLE,u=c.get,p=c.enforce,m=String(String).split("String");(e.exports=function(e,n,t,l){var c,u=!!l&&!!l.unsafe,f=!!l&&!!l.enumerable,h=!!l&&!!l.noTargetGet,w=l&&void 0!==l.name?l.name:n;o(t)&&("Symbol("===String(w).slice(0,7)&&(w="["+String(w).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!a(t,"name")||d&&t.name!==w)&&i(t,"name",w),c=p(t),c.source||(c.source=m.join("string"==typeof w?w:""))),e!==r?(u?!h&&e[n]&&(f=!0):delete e[n],f?e[n]=t:i(e,n,t)):f?e[n]=t:s(n,t)})(Function.prototype,"toString",(function(){return o(this)&&u(this).source||l(this)}))},9395:(e,n,t)=>{var r=t(7358),o=t(3577),a=t(7950),i=t(419),s=t(5173),l=t(4348),c=r.TypeError;e.exports=function(e,n){var t=e.exec;if(i(t)){var r=o(t,e,n);return null!==r&&a(r),r}if("RegExp"===s(e))return o(l,e,n);throw c("RegExp#exec called on incompatible receiver")}},4348:(e,n,t)=>{"use strict";var r=t(3577),o=t(1890),a=t(4481),i=t(136),s=t(2351),l=t(1586),c=t(1074),d=t(7624).get,u=t(5337),p=t(1442),m=l("native-string-replace",String.prototype.replace),f=RegExp.prototype.exec,h=f,w=o("".charAt),b=o("".indexOf),g=o("".replace),k=o("".slice),y=function(){var e=/a/,n=/b*/g;return r(f,e,"a"),r(f,n,"a"),0!==e.lastIndex||0!==n.lastIndex}(),v=s.UNSUPPORTED_Y||s.BROKEN_CARET,_=void 0!==/()??/.exec("")[1],x=y||_||v||u||p;x&&(h=function(e){var n,t,o,s,l,u,p,x=this,S=d(x),C=a(e),E=S.raw;if(E)return E.lastIndex=x.lastIndex,n=r(h,E,C),x.lastIndex=E.lastIndex,n;var P=S.groups,j=v&&x.sticky,L=r(i,x),O=x.source,T=0,M=C;if(j&&(L=g(L,"y",""),-1===b(L,"g")&&(L+="g"),M=k(C,x.lastIndex),x.lastIndex>0&&(!x.multiline||x.multiline&&"\n"!==w(C,x.lastIndex-1))&&(O="(?: "+O+")",M=" "+M,T++),t=new RegExp("^(?:"+O+")",L)),_&&(t=new RegExp("^"+O+"$(?!\\s)",L)),y&&(o=x.lastIndex),s=r(f,j?t:x,M),j?s?(s.input=k(s.input,T),s[0]=k(s[0],T),s.index=x.lastIndex,x.lastIndex+=s[0].length):x.lastIndex=0:y&&s&&(x.lastIndex=x.global?s.index+s[0].length:o),_&&s&&s.length>1&&r(m,s[0],t,(function(){for(l=1;l<arguments.length-2;l++)void 0===arguments[l]&&(s[l]=void 0)})),s&&P)for(s.groups=u=c(null),l=0;l<P.length;l++)p=P[l],u[p[0]]=s[p[1]];return s}),e.exports=h},136:(e,n,t)=>{"use strict";var r=t(7950);e.exports=function(){var e=r(this),n="";return e.global&&(n+="g"),e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.dotAll&&(n+="s"),e.unicode&&(n+="u"),e.sticky&&(n+="y"),n}},2351:(e,n,t)=>{var r=t(6400),o=t(7358),a=o.RegExp;n.UNSUPPORTED_Y=r((function(){var e=a("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),n.BROKEN_CARET=r((function(){var e=a("^r","gy");return e.lastIndex=2,null!=e.exec("str")}))},5337:(e,n,t)=>{var r=t(6400),o=t(7358),a=o.RegExp;e.exports=r((function(){var e=a(".","s");return!(e.dotAll&&e.exec("\n")&&"s"===e.flags)}))},1442:(e,n,t)=>{var r=t(6400),o=t(7358),a=o.RegExp;e.exports=r((function(){var e=a("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")}))},7933:(e,n,t)=>{var r=t(7358),o=r.TypeError;e.exports=function(e){if(void 0==e)throw o("Can't call method on "+e);return e}},3534:(e,n,t)=>{var r=t(7358),o=Object.defineProperty;e.exports=function(e,n){try{o(r,e,{value:n,configurable:!0,writable:!0})}catch(t){r[e]=n}return n}},1061:(e,n,t)=>{var r=t(928).f,o=t(7322),a=t(854),i=a("toStringTag");e.exports=function(e,n,t){e&&!o(e=t?e:e.prototype,i)&&r(e,i,{configurable:!0,value:n})}},203:(e,n,t)=>{var r=t(1586),o=t(6862),a=r("keys");e.exports=function(e){return a[e]||(a[e]=o(e))}},1089:(e,n,t)=>{var r=t(7358),o=t(3534),a="__core-js_shared__",i=r[a]||o(a,{});e.exports=i},1586:(e,n,t)=>{var r=t(6692),o=t(1089);(e.exports=function(e,n){return o[e]||(o[e]=void 0!==n?n:{})})("versions",[]).push({version:"3.19.0",mode:r?"pure":"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})},1021:(e,n,t)=>{var r=t(1890),o=t(1860),a=t(4481),i=t(7933),s=r("".charAt),l=r("".charCodeAt),c=r("".slice),d=function(e){return function(n,t){var r,d,u=a(i(n)),p=o(t),m=u.length;return p<0||p>=m?e?"":void 0:(r=l(u,p),r<55296||r>56319||p+1===m||(d=l(u,p+1))<56320||d>57343?e?s(u,p):r:e?c(u,p,p+2):d-56320+(r-55296<<10)+65536)}};e.exports={codeAt:d(!1),charAt:d(!0)}},1801:(e,n,t)=>{var r=t(1860),o=Math.max,a=Math.min;e.exports=function(e,n){var t=r(e);return t<0?o(t+n,0):a(t,n)}},7120:(e,n,t)=>{var r=t(2985),o=t(7933);e.exports=function(e){return r(o(e))}},1860:e=>{var n=Math.ceil,t=Math.floor;e.exports=function(e){var r=+e;return r!==r||0===r?0:(r>0?t:n)(r)}},4068:(e,n,t)=>{var r=t(1860),o=Math.min;e.exports=function(e){return e>0?o(r(e),9007199254740991):0}},7475:(e,n,t)=>{var r=t(7358),o=t(7933),a=r.Object;e.exports=function(e){return a(o(e))}},2181:(e,n,t)=>{var r=t(7358),o=t(3577),a=t(776),i=t(410),s=t(2344),l=t(9308),c=t(854),d=r.TypeError,u=c("toPrimitive");e.exports=function(e,n){if(!a(e)||i(e))return e;var t,r=s(e,u);if(r){if(void 0===n&&(n="default"),t=o(r,e,n),!a(t)||i(t))return t;throw d("Can't convert object to primitive value")}return void 0===n&&(n="number"),l(e,n)}},8618:(e,n,t)=>{var r=t(2181),o=t(410);e.exports=function(e){var n=r(e,"string");return o(n)?n:n+""}},5705:(e,n,t)=>{var r=t(854),o=r("toStringTag"),a={};a[o]="z",e.exports="[object z]"===String(a)},4481:(e,n,t)=>{var r=t(7358),o=t(5976),a=r.String;e.exports=function(e){if("Symbol"===o(e))throw TypeError("Cannot convert a Symbol value to a string");return a(e)}},3353:(e,n,t)=>{var r=t(7358),o=r.String;e.exports=function(e){try{return o(e)}catch(n){return"Object"}}},6862:(e,n,t)=>{var r=t(1890),o=0,a=Math.random(),i=r(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+i(++o+a,36)}},8476:(e,n,t)=>{var r=t(7529);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},854:(e,n,t)=>{var r=t(7358),o=t(1586),a=t(7322),i=t(6862),s=t(7529),l=t(8476),c=o("wks"),d=r.Symbol,u=d&&d["for"],p=l?d:d&&d.withoutSetter||i;e.exports=function(e){if(!a(c,e)||!s&&"string"!=typeof c[e]){var n="Symbol."+e;s&&a(d,e)?c[e]=d[e]:c[e]=l&&u?u(n):p(n)}return c[e]}},9377:(e,n,t)=>{"use strict";var r=t(8934),o=t(4817),a=t(7475),i=t(6042),s=t(1860),l=t(6340);r({target:"Array",proto:!0},{flat:function(){var e=arguments.length?arguments[0]:void 0,n=a(this),t=i(n),r=l(n,0);return r.length=o(r,n,n,t,0,void 0===e?1:s(e)),r}})},6843:(e,n,t)=>{"use strict";var r=t(7120),o=t(2852),a=t(2184),i=t(7624),s=t(8810),l="Array Iterator",c=i.set,d=i.getterFor(l);e.exports=s(Array,"Array",(function(e,n){c(this,{type:l,target:r(e),index:0,kind:n})}),(function(){var e=d(this),n=e.target,t=e.kind,r=e.index++;return!n||r>=n.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==t?{value:r,done:!1}:"values"==t?{value:n[r],done:!1}:{value:[r,n[r]],done:!1}}),"values"),a.Arguments=a.Array,o("keys"),o("values"),o("entries")},7280:(e,n,t)=>{"use strict";var r=t(8934),o=t(4348);r({target:"RegExp",proto:!0,forced:/./.exec!==o},{exec:o})},5363:(e,n,t)=>{"use strict";var r=t(4157),o=t(3577),a=t(1890),i=t(9529),s=t(6400),l=t(7950),c=t(419),d=t(1860),u=t(4068),p=t(4481),m=t(7933),f=t(6412),h=t(2344),w=t(8716),b=t(9395),g=t(854),k=g("replace"),y=Math.max,v=Math.min,_=a([].concat),x=a([].push),S=a("".indexOf),C=a("".slice),E=function(e){return void 0===e?e:String(e)},P=function(){return"$0"==="a".replace(/./,"$0")}(),j=function(){return!!/./[k]&&""===/./[k]("a","$0")}(),L=!s((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}));i("replace",(function(e,n,t){var a=j?"$":"$0";return[function(e,t){var r=m(this),a=void 0==e?void 0:h(e,k);return a?o(a,e,r,t):o(n,p(r),e,t)},function(e,o){var i=l(this),s=p(e);if("string"==typeof o&&-1===S(o,a)&&-1===S(o,"$<")){var m=t(n,i,s,o);if(m.done)return m.value}var h=c(o);h||(o=p(o));var g=i.global;if(g){var k=i.unicode;i.lastIndex=0}var P=[];while(1){var j=b(i,s);if(null===j)break;if(x(P,j),!g)break;var L=p(j[0]);""===L&&(i.lastIndex=f(s,u(i.lastIndex),k))}for(var O="",T=0,M=0;M<P.length;M++){j=P[M];for(var A=p(j[0]),F=y(v(d(j.index),s.length),0),z=[],$=1;$<j.length;$++)x(z,E(j[$]));var R=j.groups;if(h){var I=_([A],z,F,s);void 0!==R&&x(I,R);var N=p(r(o,void 0,I))}else N=w(A,s,F,z,R,o);F>=T&&(O+=C(s,T,F)+N,T=F+A.length)}return O+C(s,T)}]}),!L||!P||j)},71:(e,n,t)=>{var r=t(7358),o=t(4296),a=t(8753),i=t(6843),s=t(1904),l=t(854),c=l("iterator"),d=l("toStringTag"),u=i.values,p=function(e,n){if(e){if(e[c]!==u)try{s(e,c,u)}catch(r){e[c]=u}if(e[d]||s(e,d,n),o[n])for(var t in i)if(e[t]!==i[t])try{s(e,t,i[t])}catch(r){e[t]=i[t]}}};for(var m in o)p(r[m]&&r[m].prototype,m);p(a,"DOMTokenList")},3906:(e,n,t)=>{"use strict";t.d(n,{KM:()=>K,ZP:()=>Be});var r=t(3673),o=t(1959),a=t(8880),i=t(9754);const s={dark:{type:Boolean,default:null}};function l(e,n){return(0,o.Fl)((()=>null===e.dark?n.dark.isActive:e.dark))}const c={transitionShow:{type:String,default:"fade"},transitionHide:{type:String,default:"fade"},transitionDuration:{type:[String,Number],default:300}};function d(e,n){const t=(0,o.iH)(n.value);return(0,r.YP)(n,(e=>{(0,r.Y3)((()=>{t.value=e}))})),{transition:(0,o.Fl)((()=>"q-transition--"+(!0===t.value?e.transitionHide:e.transitionShow))),transitionStyle:(0,o.Fl)((()=>`--q-transition-duration: ${e.transitionDuration}ms`))}}const u=(0,r.aZ)({name:"QInnerLoading",props:{...s,...c,showing:Boolean,color:String,size:{type:[String,Number],default:42},label:String,labelClass:String,labelStyle:[String,Array,Object]},setup(e,{slots:n}){const t=(0,r.FN)(),s=l(e,t.proxy.$q),{transition:c,transitionStyle:u}=d(e,(0,o.Fl)((()=>e.showing))),p=(0,o.Fl)((()=>"q-inner-loading absolute-full column flex-center"+(!0===s.value?" q-inner-loading--dark":""))),m=(0,o.Fl)((()=>"q-inner-loading__label"+(void 0!==e.labelClass?` ${e.labelClass}`:"")));function f(){const n=[(0,r.h)(i.Z,{size:e.size,color:e.color})];return void 0!==e.label&&n.push((0,r.h)("div",{class:m.value,style:e.labelStyle},[e.label])),n}function h(){return!0===e.showing?(0,r.h)("div",{class:p.value,style:u.value},void 0!==n.default?n.default():f()):null}return()=>(0,r.h)(a.uT,{name:c.value,appear:!0},h)}});var p=t(9058),m=t(4554),f=t(2417),h=t(7657);const w=(0,r.aZ)({name:"QAvatar",props:{...f.LU,fontSize:String,color:String,textColor:String,icon:String,square:Boolean,rounded:Boolean},setup(e,{slots:n}){const t=(0,f.ZP)(e),a=(0,o.Fl)((()=>"q-avatar"+(e.color?` bg-${e.color}`:"")+(e.textColor?` text-${e.textColor} q-chip--colored`:"")+(!0===e.square?" q-avatar--square":!0===e.rounded?" rounded-borders":""))),i=(0,o.Fl)((()=>e.fontSize?{fontSize:e.fontSize}:null));return()=>{const o=void 0!==e.icon?[(0,r.h)(m.Z,{name:e.icon})]:void 0;return(0,r.h)("div",{class:a.value,style:t.value},[(0,r.h)("div",{class:"q-avatar__content row flex-center overflow-hidden",style:i.value},(0,h.pf)(n.default,o))])}}});var b=t(7435),g=(t(4716),t(5578));const k=[];let y=document.body;function v(e){const n=document.createElement("div");if(void 0!==e&&(n.id=e),void 0!==g.w6.globalNodes){const e=g.w6.globalNodes["class"];void 0!==e&&(n.className=e)}return y.appendChild(n),k.push(n),n}var _=t(8954);let x,S=0;const C={},E=["top-left","top-right","bottom-left","bottom-right","top","bottom","left","right","center"],P=["top-left","top-right","bottom-left","bottom-right"],j={positive:{icon:e=>e.iconSet.type.positive,color:"positive"},negative:{icon:e=>e.iconSet.type.negative,color:"negative"},warning:{icon:e=>e.iconSet.type.warning,color:"warning",textColor:"dark"},info:{icon:e=>e.iconSet.type.info,color:"info"},ongoing:{group:!1,timeout:0,spinner:!0,color:"grey-8"}},L={},O={};function T(e,n){return console.error(`Notify: ${e}`,n),!1}function M(e){return(0,r.aZ)({name:"QNotifications",devtools:{hide:!0},setup(){const n={},t=[];function s(e){clearTimeout(e.meta.timer);const r=n[e.position].value.indexOf(e);if(-1!==r){void 0!==e.group&&delete L[e.meta.group];const o=t[""+e.meta.uid];if(o){const{width:e,height:n}=getComputedStyle(o);o.style.left=`${o.offsetLeft}px`,o.style.width=e,o.style.height=n}n[e.position].value.splice(r,1),"function"===typeof e.onDismiss&&e.onDismiss()}}return E.forEach((e=>{n[e]=(0,o.iH)([]);const t=!0===["left","center","right"].includes(e)?"center":e.indexOf("top")>-1?"top":"bottom",r=e.indexOf("left")>-1?"start":e.indexOf("right")>-1?"end":"center",a=["left","right"].includes(e)?`items-${"left"===e?"start":"end"} justify-center`:"center"===e?"flex-center":`items-${r}`;O[e]=`q-notifications__list q-notifications__list--${t} fixed column no-wrap ${a}`})),x=(t,r)=>{if(!t)return T("parameter required");let a;const l={textColor:"white"};if(!0!==t.ignoreDefaults&&Object.assign(l,C),Object(t)!==t&&(l.type&&Object.assign(l,j[l.type]),t={message:t}),Object.assign(l,j[t.type||l.type],t),"function"===typeof l.icon&&(l.icon=l.icon(e)),l.spinner?(!0===l.spinner&&(l.spinner=i.Z),l.spinner=(0,o.Xl)(l.spinner)):l.spinner=!1,l.meta={hasMedia:Boolean(!1!==l.spinner||l.icon||l.avatar),hasText:Boolean(void 0!==l.message&&null!==l.message||void 0!==l.caption&&null!==l.caption)},l.position){if(!1===E.includes(l.position))return T("wrong position",t)}else l.position="bottom";if(void 0===l.timeout)l.timeout=5e3;else{const e=parseInt(l.timeout,10);if(isNaN(e)||e<0)return T("wrong timeout",t);l.timeout=e}0===l.timeout?l.progress=!1:!0===l.progress&&(l.meta.progressClass="q-notification__progress"+(l.progressClass?` ${l.progressClass}`:""),l.meta.progressStyle={animationDuration:`${l.timeout+1e3}ms`});const c=(!0===Array.isArray(t.actions)?t.actions:[]).concat(!0!==t.ignoreDefaults&&!0===Array.isArray(C.actions)?C.actions:[]).concat(void 0!==j[t.type]&&!0===Array.isArray(j[t.type].actions)?j[t.type].actions:[]);if(l.closeBtn&&c.push({label:"string"===typeof l.closeBtn?l.closeBtn:e.lang.label.close}),l.actions=c.map((({handler:e,noDismiss:n,...t})=>({flat:!0,...t,onClick:"function"===typeof e?()=>{e(),!0!==n&&d()}:()=>{d()}}))),void 0===l.multiLine&&(l.multiLine=l.actions.length>1),Object.assign(l.meta,{class:"q-notification row items-stretch q-notification--"+(!0===l.multiLine?"multi-line":"standard")+(void 0!==l.color?` bg-${l.color}`:"")+(void 0!==l.textColor?` text-${l.textColor}`:"")+(void 0!==l.classes?` ${l.classes}`:""),wrapperClass:"q-notification__wrapper col relative-position border-radius-inherit "+(!0===l.multiLine?"column no-wrap justify-center":"row items-center"),contentClass:"q-notification__content row items-center"+(!0===l.multiLine?"":" col"),leftClass:!0===l.meta.hasText?"additional":"single",attrs:{role:"alert",...l.attrs}}),!1===l.group?(l.group=void 0,l.meta.group=void 0):(void 0!==l.group&&!0!==l.group||(l.group=[l.message,l.caption,l.multiline].concat(l.actions.map((e=>`${e.label}*${e.icon}`))).join("|")),l.meta.group=l.group+"|"+l.position),0===l.actions.length?l.actions=void 0:l.meta.actionsClass="q-notification__actions row items-center "+(!0===l.multiLine?"justify-end":"col-auto")+(!0===l.meta.hasMedia?" q-notification__actions--with-media":""),void 0!==r){clearTimeout(r.notif.meta.timer),l.meta.uid=r.notif.meta.uid;const e=n[l.position].value.indexOf(r.notif);n[l.position].value[e]=l}else{const e=L[l.meta.group];if(void 0===e){if(l.meta.uid=S++,l.meta.badge=1,-1!==["left","right","center"].indexOf(l.position))n[l.position].value.splice(Math.floor(n[l.position].value.length/2),0,l);else{const e=l.position.indexOf("top")>-1?"unshift":"push";n[l.position].value[e](l)}void 0!==l.group&&(L[l.meta.group]=l)}else{if(clearTimeout(e.meta.timer),void 0!==l.badgePosition){if(!1===P.includes(l.badgePosition))return T("wrong badgePosition",t)}else l.badgePosition="top-"+(l.position.indexOf("left")>-1?"right":"left");l.meta.uid=e.meta.uid,l.meta.badge=e.meta.badge+1,l.meta.badgeClass=`q-notification__badge q-notification__badge--${l.badgePosition}`+(void 0!==l.badgeColor?` bg-${l.badgeColor}`:"")+(void 0!==l.badgeTextColor?` text-${l.badgeTextColor}`:"")+(l.badgeClass?` ${l.badgeClass}`:"");const r=n[l.position].value.indexOf(e);n[l.position].value[r]=L[l.meta.group]=l}}const d=()=>{s(l),a=void 0};return l.timeout>0&&(l.meta.timer=setTimeout((()=>{d()}),l.timeout+1e3)),void 0!==l.group?e=>{void 0!==e?T("trying to update a grouped one which is forbidden",t):d()}:(a={dismiss:d,config:t,notif:l},void 0===r?e=>{if(void 0!==a)if(void 0===e)a.dismiss();else{const n=Object.assign({},a.config,e,{group:!1,position:l.position});x(n,a)}}:void Object.assign(r,a))},()=>(0,r.h)("div",{class:"q-notifications"},E.map((e=>(0,r.h)(a.W3,{key:e,class:O[e],tag:"div",name:`q-notification--${e}`},(()=>n[e].value.map((e=>{const n=e.meta,o=[];if(!0===n.hasMedia&&(!1!==e.spinner?o.push((0,r.h)(e.spinner,{class:"q-notification__spinner q-notification__spinner--"+n.leftClass})):e.icon?o.push((0,r.h)(m.Z,{class:"q-notification__icon q-notification__icon--"+n.leftClass,name:e.icon,role:"img"})):e.avatar&&o.push((0,r.h)(w,{class:"q-notification__avatar q-notification__avatar--"+n.leftClass},(()=>(0,r.h)("img",{src:e.avatar,"aria-hidden":"true"}))))),!0===n.hasText){let n;const t={class:"q-notification__message col"};if(!0===e.html)t.innerHTML=e.caption?`<div>${e.message}</div><div class="q-notification__caption">${e.caption}</div>`:e.message;else{const t=[e.message];n=e.caption?[(0,r.h)("div",t),(0,r.h)("div",{class:"q-notification__caption"},[e.caption])]:t}o.push((0,r.h)("div",t,n))}const a=[(0,r.h)("div",{class:n.contentClass},o)];return!0===e.progress&&a.push((0,r.h)("div",{key:`${n.uid}|p|${n.badge}`,class:n.progressClass,style:n.progressStyle})),void 0!==e.actions&&a.push((0,r.h)("div",{class:n.actionsClass},e.actions.map((e=>(0,r.h)(b.Z,e))))),n.badge>1&&a.push((0,r.h)("div",{key:`${n.uid}|${n.badge}`,class:e.meta.badgeClass,style:e.badgeStyle},[n.badge])),(0,r.h)("div",{ref:e=>{t[""+n.uid]=e},key:n.uid,class:n.class,...n.attrs},[(0,r.h)("div",{class:n.wrapperClass},a)])})))))))}})}const A={create(e){return x(e)},setDefaults(e){e===Object(e)&&Object.assign(C,e)},registerType(e,n){n===Object(n)&&(j[e]=n)},install({$q:e,parentApp:n}){if(e.notify=this.create,e.notify.setDefaults=this.setDefaults,e.notify.registerType=this.registerType,void 0!==e.config.notify&&this.setDefaults(e.config.notify),!0!==this.__installed){const t=v("q-notify");(0,_.$)(M(e),n).mount(t)}}};var F="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof t.g?t.g:"undefined"!=typeof self?self:{};function z(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var $={exports:{}};!function(e){(function(){function n(e){var n={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic 'section-' prefix",type:"string"},rawPrefixHeaderId:{defaultValue:!1,describe:'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',type:"boolean"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},rawHeaderId:{defaultValue:!1,describe:"Remove only spaces, ' and \" from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids",type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},excludeTrailingPunctuationFromURLs:{defaultValue:!1,describe:"Excludes trailing punctuation from links generated with autoLinking",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,describe:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,describe:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,describe:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,describe:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,describe:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",describe:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,describe:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,describe:"Open all links in new windows",type:"boolean"},backslashEscapesHTMLTags:{defaultValue:!1,describe:"Support for HTML Tag escaping. ex: <div>foo</div>",type:"boolean"},emoji:{defaultValue:!1,describe:"Enable emoji support. Ex: `this is a :smile: emoji`",type:"boolean"},underline:{defaultValue:!1,describe:"Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`",type:"boolean"},ellipsis:{defaultValue:!0,describe:"Replaces three dots with the ellipsis unicode character",type:"boolean"},completeHTMLDocument:{defaultValue:!1,describe:"Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags",type:"boolean"},metadata:{defaultValue:!1,describe:"Enable support for document metadata (defined at the top of the document between `«««` and `»»»` or between `---` and `---`).",type:"boolean"},splitAdjacentBlockquotes:{defaultValue:!1,describe:"Split adjacent blockquote blocks",type:"boolean"}};if(!1===e)return JSON.parse(JSON.stringify(n));var t,r={};for(t in n)n.hasOwnProperty(t)&&(r[t]=n[t].defaultValue);return r}var t={},r={},o={},a=n(!0),i="vanilla",s={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0,backslashEscapesHTMLTags:!0,emoji:!0,splitAdjacentBlockquotes:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:n(!0),allOn:function(){var e,t=n(!0),r={};for(e in t)t.hasOwnProperty(e)&&(r[e]=!0);return r}()};function l(e,n){var r=n?"Error in "+n+" extension->":"Error in unnamed extension",o={valid:!0,error:""};t.helper.isArray(e)||(e=[e]);for(var a=0;a<e.length;++a){var i=r+" sub-extension "+a+": ",s=e[a];if("object"!=typeof s)return o.valid=!1,o.error=i+"must be an object, but "+typeof s+" given",o;if(!t.helper.isString(s.type))return o.valid=!1,o.error=i+'property "type" must be a string, but '+typeof s.type+" given",o;var l=s.type=s.type.toLowerCase();if("lang"!==(l="html"===(l="language"===l?s.type="lang":l)?s.type="output":l)&&"output"!==l&&"listener"!==l)return o.valid=!1,o.error=i+"type "+l+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',o;if("listener"===l){if(t.helper.isUndefined(s.listeners))return o.valid=!1,o.error=i+'. Extensions of type "listener" must have a property called "listeners"',o}else if(t.helper.isUndefined(s.filter)&&t.helper.isUndefined(s.regex))return o.valid=!1,o.error=i+l+' extensions must define either a "regex" property or a "filter" method',o;if(s.listeners){if("object"!=typeof s.listeners)return o.valid=!1,o.error=i+'"listeners" property must be an object but '+typeof s.listeners+" given",o;for(var c in s.listeners)if(s.listeners.hasOwnProperty(c)&&"function"!=typeof s.listeners[c])return o.valid=!1,o.error=i+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+c+" must be a function but "+typeof s.listeners[c]+" given",o}if(s.filter){if("function"!=typeof s.filter)return o.valid=!1,o.error=i+'"filter" must be a function, but '+typeof s.filter+" given",o}else if(s.regex){if(t.helper.isString(s.regex)&&(s.regex=new RegExp(s.regex,"g")),!(s.regex instanceof RegExp))return o.valid=!1,o.error=i+'"regex" property must either be a string or a RegExp object, but '+typeof s.regex+" given",o;if(t.helper.isUndefined(s.replace))return o.valid=!1,o.error=i+'"regex" extensions must implement a replace string or function',o}}return o}function c(e,n){return"¨E"+n.charCodeAt(0)+"E"}function d(e,n,t,r){var o,a,i,s,l=-1<(r=r||"").indexOf("g"),c=new RegExp(n+"|"+t,"g"+r.replace(/g/g,"")),d=new RegExp(n,r.replace(/g/g,"")),u=[];do{for(o=0;i=c.exec(e);)if(d.test(i[0]))o++||(s=(a=c.lastIndex)-i[0].length);else if(o&&!--o){var p=i.index+i[0].length;p={left:{start:s,end:a},match:{start:a,end:i.index},right:{start:i.index,end:p},wholeMatch:{start:s,end:p}};if(u.push(p),!l)return u}}while(o&&(c.lastIndex=a));return u}function u(e){return function(n,r,o,a,i,s,l){var c=o=o.replace(t.helper.regexes.asteriskDashAndColon,t.helper.escapeCharactersCallback),d="",u="";r=r||"",l=l||"";return/^www\./i.test(o)&&(o=o.replace(/^www\./i,"http://www.")),e.excludeTrailingPunctuationFromURLs&&s&&(d=s),r+'<a href="'+o+'"'+(u=e.openLinksInNewWindow?' rel="noopener noreferrer" target="¨E95Eblank"':u)+">"+c+"</a>"+d+l}}function p(e,n){return function(r,o,a){var i="mailto:";return o=o||"",a=t.subParser("unescapeSpecialChars")(a,e,n),e.encodeEmails?(i=t.helper.encodeEmailAddress(i+a),a=t.helper.encodeEmailAddress(a)):i+=a,o+'<a href="'+i+'">'+a+"</a>"}}t.helper={},t.extensions={},t.setOption=function(e,n){return a[e]=n,this},t.getOption=function(e){return a[e]},t.getOptions=function(){return a},t.resetOptions=function(){a=n(!0)},t.setFlavor=function(e){if(!s.hasOwnProperty(e))throw Error(e+" flavor was not found");t.resetOptions();var n,r=s[e];for(n in i=e,r)r.hasOwnProperty(n)&&(a[n]=r[n])},t.getFlavor=function(){return i},t.getFlavorOptions=function(e){if(s.hasOwnProperty(e))return s[e]},t.getDefaultOptions=n,t.subParser=function(e,n){if(t.helper.isString(e)){if(void 0===n){if(r.hasOwnProperty(e))return r[e];throw Error("SubParser named "+e+" not registered!")}r[e]=n}},t.extension=function(e,n){if(!t.helper.isString(e))throw Error("Extension 'name' must be a string");if(e=t.helper.stdExtName(e),t.helper.isUndefined(n)){if(!o.hasOwnProperty(e))throw Error("Extension named "+e+" is not registered!");return o[e]}"function"==typeof n&&(n=n());var r=l(n=t.helper.isArray(n)?n:[n],e);if(!r.valid)throw Error(r.error);o[e]=n},t.getAllExtensions=function(){return o},t.removeExtension=function(e){delete o[e]},t.resetExtensions=function(){o={}},t.validateExtension=function(e){return e=l(e,null),!!e.valid||(console.warn(e.error),!1)},t.hasOwnProperty("helper")||(t.helper={}),t.helper.isString=function(e){return"string"==typeof e||e instanceof String},t.helper.isFunction=function(e){return e&&"[object Function]"==={}.toString.call(e)},t.helper.isArray=function(e){return Array.isArray(e)},t.helper.isUndefined=function(e){return void 0===e},t.helper.forEach=function(e,n){if(t.helper.isUndefined(e))throw new Error("obj param is required");if(t.helper.isUndefined(n))throw new Error("callback param is required");if(!t.helper.isFunction(n))throw new Error("callback param must be a function/closure");if("function"==typeof e.forEach)e.forEach(n);else if(t.helper.isArray(e))for(var r=0;r<e.length;r++)n(e[r],r,e);else{if("object"!=typeof e)throw new Error("obj does not seem to be an array or an iterable object");for(var o in e)e.hasOwnProperty(o)&&n(e[o],o,e)}},t.helper.stdExtName=function(e){return e.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()},t.helper.escapeCharactersCallback=c,t.helper.escapeCharacters=function(e,n,t){return n="(["+n.replace(/([\[\]\\])/g,"\\$1")+"])",t&&(n="\\\\"+n),n=new RegExp(n,"g"),e.replace(n,c)},t.helper.unescapeHTMLEntities=function(e){return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")},t.helper.matchRecursiveRegExp=function(e,n,t,r){for(var o=d(e,n,t,r),a=[],i=0;i<o.length;++i)a.push([e.slice(o[i].wholeMatch.start,o[i].wholeMatch.end),e.slice(o[i].match.start,o[i].match.end),e.slice(o[i].left.start,o[i].left.end),e.slice(o[i].right.start,o[i].right.end)]);return a},t.helper.replaceRecursiveRegExp=function(e,n,r,o,a){var i;t.helper.isFunction(n)||(i=n,n=function(){return i});var s=d(e,r,o,a),l=(a=e,s.length);if(0<l){var c=[];0!==s[0].wholeMatch.start&&c.push(e.slice(0,s[0].wholeMatch.start));for(var u=0;u<l;++u)c.push(n(e.slice(s[u].wholeMatch.start,s[u].wholeMatch.end),e.slice(s[u].match.start,s[u].match.end),e.slice(s[u].left.start,s[u].left.end),e.slice(s[u].right.start,s[u].right.end))),u<l-1&&c.push(e.slice(s[u].wholeMatch.end,s[u+1].wholeMatch.start));s[l-1].wholeMatch.end<e.length&&c.push(e.slice(s[l-1].wholeMatch.end)),a=c.join("")}return a},t.helper.regexIndexOf=function(e,n,r){if(!t.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(n instanceof RegExp==0)throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";return n=e.substring(r||0).search(n),0<=n?n+(r||0):n},t.helper.splitAtIndex=function(e,n){if(!t.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";return[e.substring(0,n),e.substring(n)]},t.helper.encodeEmailAddress=function(e){var n=[function(e){return"&#"+e.charCodeAt(0)+";"},function(e){return"&#x"+e.charCodeAt(0).toString(16)+";"},function(e){return e}];return e.replace(/./g,(function(e){var t;return"@"===e?n[Math.floor(2*Math.random())](e):.9<(t=Math.random())?n[2](e):.45<t?n[1](e):n[0](e)}))},t.helper.padEnd=function(e,n,t){return n>>=0,t=String(t||" "),e.length>n?String(e):((n-=e.length)>t.length&&(t+=t.repeat(n/t.length)),String(e)+t.slice(0,n))},"undefined"==typeof console&&(console={warn:function(e){alert(e)},log:function(e){alert(e)},error:function(e){throw e}}),t.helper.regexes={asteriskDashAndColon:/([*_:~])/g},t.helper.emojis={"+1":"👍","-1":"👎",100:"💯",1234:"🔢","1st_place_medal":"🥇","2nd_place_medal":"🥈","3rd_place_medal":"🥉","8ball":"🎱",a:"🅰️",ab:"🆎",abc:"🔤",abcd:"🔡",accept:"🉑",aerial_tramway:"🚡",airplane:"✈️",alarm_clock:"⏰",alembic:"⚗️",alien:"👽",ambulance:"🚑",amphora:"🏺",anchor:"⚓️",angel:"👼",anger:"💢",angry:"😠",anguished:"😧",ant:"🐜",apple:"🍎",aquarius:"♒️",aries:"♈️",arrow_backward:"◀️",arrow_double_down:"⏬",arrow_double_up:"⏫",arrow_down:"⬇️",arrow_down_small:"🔽",arrow_forward:"▶️",arrow_heading_down:"⤵️",arrow_heading_up:"⤴️",arrow_left:"⬅️",arrow_lower_left:"↙️",arrow_lower_right:"↘️",arrow_right:"➡️",arrow_right_hook:"↪️",arrow_up:"⬆️",arrow_up_down:"↕️",arrow_up_small:"🔼",arrow_upper_left:"↖️",arrow_upper_right:"↗️",arrows_clockwise:"🔃",arrows_counterclockwise:"🔄",art:"🎨",articulated_lorry:"🚛",artificial_satellite:"🛰",astonished:"😲",athletic_shoe:"👟",atm:"🏧",atom_symbol:"⚛️",avocado:"🥑",b:"🅱️",baby:"👶",baby_bottle:"🍼",baby_chick:"🐤",baby_symbol:"🚼",back:"🔙",bacon:"🥓",badminton:"🏸",baggage_claim:"🛄",baguette_bread:"🥖",balance_scale:"⚖️",balloon:"🎈",ballot_box:"🗳",ballot_box_with_check:"☑️",bamboo:"🎍",banana:"🍌",bangbang:"‼️",bank:"🏦",bar_chart:"📊",barber:"💈",baseball:"⚾️",basketball:"🏀",basketball_man:"⛹️",basketball_woman:"⛹️&zwj;♀️",bat:"🦇",bath:"🛀",bathtub:"🛁",battery:"🔋",beach_umbrella:"🏖",bear:"🐻",bed:"🛏",bee:"🐝",beer:"🍺",beers:"🍻",beetle:"🐞",beginner:"🔰",bell:"🔔",bellhop_bell:"🛎",bento:"🍱",biking_man:"🚴",bike:"🚲",biking_woman:"🚴&zwj;♀️",bikini:"👙",biohazard:"☣️",bird:"🐦",birthday:"🎂",black_circle:"⚫️",black_flag:"🏴",black_heart:"🖤",black_joker:"🃏",black_large_square:"⬛️",black_medium_small_square:"◾️",black_medium_square:"◼️",black_nib:"✒️",black_small_square:"▪️",black_square_button:"🔲",blonde_man:"👱",blonde_woman:"👱&zwj;♀️",blossom:"🌼",blowfish:"🐡",blue_book:"📘",blue_car:"🚙",blue_heart:"💙",blush:"😊",boar:"🐗",boat:"⛵️",bomb:"💣",book:"📖",bookmark:"🔖",bookmark_tabs:"📑",books:"📚",boom:"💥",boot:"👢",bouquet:"💐",bowing_man:"🙇",bow_and_arrow:"🏹",bowing_woman:"🙇&zwj;♀️",bowling:"🎳",boxing_glove:"🥊",boy:"👦",bread:"🍞",bride_with_veil:"👰",bridge_at_night:"🌉",briefcase:"💼",broken_heart:"💔",bug:"🐛",building_construction:"🏗",bulb:"💡",bullettrain_front:"🚅",bullettrain_side:"🚄",burrito:"🌯",bus:"🚌",business_suit_levitating:"🕴",busstop:"🚏",bust_in_silhouette:"👤",busts_in_silhouette:"👥",butterfly:"🦋",cactus:"🌵",cake:"🍰",calendar:"📆",call_me_hand:"🤙",calling:"📲",camel:"🐫",camera:"📷",camera_flash:"📸",camping:"🏕",cancer:"♋️",candle:"🕯",candy:"🍬",canoe:"🛶",capital_abcd:"🔠",capricorn:"♑️",car:"🚗",card_file_box:"🗃",card_index:"📇",card_index_dividers:"🗂",carousel_horse:"🎠",carrot:"🥕",cat:"🐱",cat2:"🐈",cd:"💿",chains:"⛓",champagne:"🍾",chart:"💹",chart_with_downwards_trend:"📉",chart_with_upwards_trend:"📈",checkered_flag:"🏁",cheese:"🧀",cherries:"🍒",cherry_blossom:"🌸",chestnut:"🌰",chicken:"🐔",children_crossing:"🚸",chipmunk:"🐿",chocolate_bar:"🍫",christmas_tree:"🎄",church:"⛪️",cinema:"🎦",circus_tent:"🎪",city_sunrise:"🌇",city_sunset:"🌆",cityscape:"🏙",cl:"🆑",clamp:"🗜",clap:"👏",clapper:"🎬",classical_building:"🏛",clinking_glasses:"🥂",clipboard:"📋",clock1:"🕐",clock10:"🕙",clock1030:"🕥",clock11:"🕚",clock1130:"🕦",clock12:"🕛",clock1230:"🕧",clock130:"🕜",clock2:"🕑",clock230:"🕝",clock3:"🕒",clock330:"🕞",clock4:"🕓",clock430:"🕟",clock5:"🕔",clock530:"🕠",clock6:"🕕",clock630:"🕡",clock7:"🕖",clock730:"🕢",clock8:"🕗",clock830:"🕣",clock9:"🕘",clock930:"🕤",closed_book:"📕",closed_lock_with_key:"🔐",closed_umbrella:"🌂",cloud:"☁️",cloud_with_lightning:"🌩",cloud_with_lightning_and_rain:"⛈",cloud_with_rain:"🌧",cloud_with_snow:"🌨",clown_face:"🤡",clubs:"♣️",cocktail:"🍸",coffee:"☕️",coffin:"⚰️",cold_sweat:"😰",comet:"☄️",computer:"💻",computer_mouse:"🖱",confetti_ball:"🎊",confounded:"😖",confused:"😕",congratulations:"㊗️",construction:"🚧",construction_worker_man:"👷",construction_worker_woman:"👷&zwj;♀️",control_knobs:"🎛",convenience_store:"🏪",cookie:"🍪",cool:"🆒",policeman:"👮",copyright:"©️",corn:"🌽",couch_and_lamp:"🛋",couple:"👫",couple_with_heart_woman_man:"💑",couple_with_heart_man_man:"👨&zwj;❤️&zwj;👨",couple_with_heart_woman_woman:"👩&zwj;❤️&zwj;👩",couplekiss_man_man:"👨&zwj;❤️&zwj;💋&zwj;👨",couplekiss_man_woman:"💏",couplekiss_woman_woman:"👩&zwj;❤️&zwj;💋&zwj;👩",cow:"🐮",cow2:"🐄",cowboy_hat_face:"🤠",crab:"🦀",crayon:"🖍",credit_card:"💳",crescent_moon:"🌙",cricket:"🏏",crocodile:"🐊",croissant:"🥐",crossed_fingers:"🤞",crossed_flags:"🎌",crossed_swords:"⚔️",crown:"👑",cry:"😢",crying_cat_face:"😿",crystal_ball:"🔮",cucumber:"🥒",cupid:"💘",curly_loop:"➰",currency_exchange:"💱",curry:"🍛",custard:"🍮",customs:"🛃",cyclone:"🌀",dagger:"🗡",dancer:"💃",dancing_women:"👯",dancing_men:"👯&zwj;♂️",dango:"🍡",dark_sunglasses:"🕶",dart:"🎯",dash:"💨",date:"📅",deciduous_tree:"🌳",deer:"🦌",department_store:"🏬",derelict_house:"🏚",desert:"🏜",desert_island:"🏝",desktop_computer:"🖥",male_detective:"🕵️",diamond_shape_with_a_dot_inside:"💠",diamonds:"♦️",disappointed:"😞",disappointed_relieved:"😥",dizzy:"💫",dizzy_face:"😵",do_not_litter:"🚯",dog:"🐶",dog2:"🐕",dollar:"💵",dolls:"🎎",dolphin:"🐬",door:"🚪",doughnut:"🍩",dove:"🕊",dragon:"🐉",dragon_face:"🐲",dress:"👗",dromedary_camel:"🐪",drooling_face:"🤤",droplet:"💧",drum:"🥁",duck:"🦆",dvd:"📀","e-mail":"📧",eagle:"🦅",ear:"👂",ear_of_rice:"🌾",earth_africa:"🌍",earth_americas:"🌎",earth_asia:"🌏",egg:"🥚",eggplant:"🍆",eight_pointed_black_star:"✴️",eight_spoked_asterisk:"✳️",electric_plug:"🔌",elephant:"🐘",email:"✉️",end:"🔚",envelope_with_arrow:"📩",euro:"💶",european_castle:"🏰",european_post_office:"🏤",evergreen_tree:"🌲",exclamation:"❗️",expressionless:"😑",eye:"👁",eye_speech_bubble:"👁&zwj;🗨",eyeglasses:"👓",eyes:"👀",face_with_head_bandage:"🤕",face_with_thermometer:"🤒",fist_oncoming:"👊",factory:"🏭",fallen_leaf:"🍂",family_man_woman_boy:"👪",family_man_boy:"👨&zwj;👦",family_man_boy_boy:"👨&zwj;👦&zwj;👦",family_man_girl:"👨&zwj;👧",family_man_girl_boy:"👨&zwj;👧&zwj;👦",family_man_girl_girl:"👨&zwj;👧&zwj;👧",family_man_man_boy:"👨&zwj;👨&zwj;👦",family_man_man_boy_boy:"👨&zwj;👨&zwj;👦&zwj;👦",family_man_man_girl:"👨&zwj;👨&zwj;👧",family_man_man_girl_boy:"👨&zwj;👨&zwj;👧&zwj;👦",family_man_man_girl_girl:"👨&zwj;👨&zwj;👧&zwj;👧",family_man_woman_boy_boy:"👨&zwj;👩&zwj;👦&zwj;👦",family_man_woman_girl:"👨&zwj;👩&zwj;👧",family_man_woman_girl_boy:"👨&zwj;👩&zwj;👧&zwj;👦",family_man_woman_girl_girl:"👨&zwj;👩&zwj;👧&zwj;👧",family_woman_boy:"👩&zwj;👦",family_woman_boy_boy:"👩&zwj;👦&zwj;👦",family_woman_girl:"👩&zwj;👧",family_woman_girl_boy:"👩&zwj;👧&zwj;👦",family_woman_girl_girl:"👩&zwj;👧&zwj;👧",family_woman_woman_boy:"👩&zwj;👩&zwj;👦",family_woman_woman_boy_boy:"👩&zwj;👩&zwj;👦&zwj;👦",family_woman_woman_girl:"👩&zwj;👩&zwj;👧",family_woman_woman_girl_boy:"👩&zwj;👩&zwj;👧&zwj;👦",family_woman_woman_girl_girl:"👩&zwj;👩&zwj;👧&zwj;👧",fast_forward:"⏩",fax:"📠",fearful:"😨",feet:"🐾",female_detective:"🕵️&zwj;♀️",ferris_wheel:"🎡",ferry:"⛴",field_hockey:"🏑",file_cabinet:"🗄",file_folder:"📁",film_projector:"📽",film_strip:"🎞",fire:"🔥",fire_engine:"🚒",fireworks:"🎆",first_quarter_moon:"🌓",first_quarter_moon_with_face:"🌛",fish:"🐟",fish_cake:"🍥",fishing_pole_and_fish:"🎣",fist_raised:"✊",fist_left:"🤛",fist_right:"🤜",flags:"🎏",flashlight:"🔦",fleur_de_lis:"⚜️",flight_arrival:"🛬",flight_departure:"🛫",floppy_disk:"💾",flower_playing_cards:"🎴",flushed:"😳",fog:"🌫",foggy:"🌁",football:"🏈",footprints:"👣",fork_and_knife:"🍴",fountain:"⛲️",fountain_pen:"🖋",four_leaf_clover:"🍀",fox_face:"🦊",framed_picture:"🖼",free:"🆓",fried_egg:"🍳",fried_shrimp:"🍤",fries:"🍟",frog:"🐸",frowning:"😦",frowning_face:"☹️",frowning_man:"🙍&zwj;♂️",frowning_woman:"🙍",middle_finger:"🖕",fuelpump:"⛽️",full_moon:"🌕",full_moon_with_face:"🌝",funeral_urn:"⚱️",game_die:"🎲",gear:"⚙️",gem:"💎",gemini:"♊️",ghost:"👻",gift:"🎁",gift_heart:"💝",girl:"👧",globe_with_meridians:"🌐",goal_net:"🥅",goat:"🐐",golf:"⛳️",golfing_man:"🏌️",golfing_woman:"🏌️&zwj;♀️",gorilla:"🦍",grapes:"🍇",green_apple:"🍏",green_book:"📗",green_heart:"💚",green_salad:"🥗",grey_exclamation:"❕",grey_question:"❔",grimacing:"😬",grin:"😁",grinning:"😀",guardsman:"💂",guardswoman:"💂&zwj;♀️",guitar:"🎸",gun:"🔫",haircut_woman:"💇",haircut_man:"💇&zwj;♂️",hamburger:"🍔",hammer:"🔨",hammer_and_pick:"⚒",hammer_and_wrench:"🛠",hamster:"🐹",hand:"✋",handbag:"👜",handshake:"🤝",hankey:"💩",hatched_chick:"🐥",hatching_chick:"🐣",headphones:"🎧",hear_no_evil:"🙉",heart:"❤️",heart_decoration:"💟",heart_eyes:"😍",heart_eyes_cat:"😻",heartbeat:"💓",heartpulse:"💗",hearts:"♥️",heavy_check_mark:"✔️",heavy_division_sign:"➗",heavy_dollar_sign:"💲",heavy_heart_exclamation:"❣️",heavy_minus_sign:"➖",heavy_multiplication_x:"✖️",heavy_plus_sign:"➕",helicopter:"🚁",herb:"🌿",hibiscus:"🌺",high_brightness:"🔆",high_heel:"👠",hocho:"🔪",hole:"🕳",honey_pot:"🍯",horse:"🐴",horse_racing:"🏇",hospital:"🏥",hot_pepper:"🌶",hotdog:"🌭",hotel:"🏨",hotsprings:"♨️",hourglass:"⌛️",hourglass_flowing_sand:"⏳",house:"🏠",house_with_garden:"🏡",houses:"🏘",hugs:"🤗",hushed:"😯",ice_cream:"🍨",ice_hockey:"🏒",ice_skate:"⛸",icecream:"🍦",id:"🆔",ideograph_advantage:"🉐",imp:"👿",inbox_tray:"📥",incoming_envelope:"📨",tipping_hand_woman:"💁",information_source:"ℹ️",innocent:"😇",interrobang:"⁉️",iphone:"📱",izakaya_lantern:"🏮",jack_o_lantern:"🎃",japan:"🗾",japanese_castle:"🏯",japanese_goblin:"👺",japanese_ogre:"👹",jeans:"👖",joy:"😂",joy_cat:"😹",joystick:"🕹",kaaba:"🕋",key:"🔑",keyboard:"⌨️",keycap_ten:"🔟",kick_scooter:"🛴",kimono:"👘",kiss:"💋",kissing:"😗",kissing_cat:"😽",kissing_closed_eyes:"😚",kissing_heart:"😘",kissing_smiling_eyes:"😙",kiwi_fruit:"🥝",koala:"🐨",koko:"🈁",label:"🏷",large_blue_circle:"🔵",large_blue_diamond:"🔷",large_orange_diamond:"🔶",last_quarter_moon:"🌗",last_quarter_moon_with_face:"🌜",latin_cross:"✝️",laughing:"😆",leaves:"🍃",ledger:"📒",left_luggage:"🛅",left_right_arrow:"↔️",leftwards_arrow_with_hook:"↩️",lemon:"🍋",leo:"♌️",leopard:"🐆",level_slider:"🎚",libra:"♎️",light_rail:"🚈",link:"🔗",lion:"🦁",lips:"👄",lipstick:"💄",lizard:"🦎",lock:"🔒",lock_with_ink_pen:"🔏",lollipop:"🍭",loop:"➿",loud_sound:"🔊",loudspeaker:"📢",love_hotel:"🏩",love_letter:"💌",low_brightness:"🔅",lying_face:"🤥",m:"Ⓜ️",mag:"🔍",mag_right:"🔎",mahjong:"🀄️",mailbox:"📫",mailbox_closed:"📪",mailbox_with_mail:"📬",mailbox_with_no_mail:"📭",man:"👨",man_artist:"👨&zwj;🎨",man_astronaut:"👨&zwj;🚀",man_cartwheeling:"🤸&zwj;♂️",man_cook:"👨&zwj;🍳",man_dancing:"🕺",man_facepalming:"🤦&zwj;♂️",man_factory_worker:"👨&zwj;🏭",man_farmer:"👨&zwj;🌾",man_firefighter:"👨&zwj;🚒",man_health_worker:"👨&zwj;⚕️",man_in_tuxedo:"🤵",man_judge:"👨&zwj;⚖️",man_juggling:"🤹&zwj;♂️",man_mechanic:"👨&zwj;🔧",man_office_worker:"👨&zwj;💼",man_pilot:"👨&zwj;✈️",man_playing_handball:"🤾&zwj;♂️",man_playing_water_polo:"🤽&zwj;♂️",man_scientist:"👨&zwj;🔬",man_shrugging:"🤷&zwj;♂️",man_singer:"👨&zwj;🎤",man_student:"👨&zwj;🎓",man_teacher:"👨&zwj;🏫",man_technologist:"👨&zwj;💻",man_with_gua_pi_mao:"👲",man_with_turban:"👳",tangerine:"🍊",mans_shoe:"👞",mantelpiece_clock:"🕰",maple_leaf:"🍁",martial_arts_uniform:"🥋",mask:"😷",massage_woman:"💆",massage_man:"💆&zwj;♂️",meat_on_bone:"🍖",medal_military:"🎖",medal_sports:"🏅",mega:"📣",melon:"🍈",memo:"📝",men_wrestling:"🤼&zwj;♂️",menorah:"🕎",mens:"🚹",metal:"🤘",metro:"🚇",microphone:"🎤",microscope:"🔬",milk_glass:"🥛",milky_way:"🌌",minibus:"🚐",minidisc:"💽",mobile_phone_off:"📴",money_mouth_face:"🤑",money_with_wings:"💸",moneybag:"💰",monkey:"🐒",monkey_face:"🐵",monorail:"🚝",moon:"🌔",mortar_board:"🎓",mosque:"🕌",motor_boat:"🛥",motor_scooter:"🛵",motorcycle:"🏍",motorway:"🛣",mount_fuji:"🗻",mountain:"⛰",mountain_biking_man:"🚵",mountain_biking_woman:"🚵&zwj;♀️",mountain_cableway:"🚠",mountain_railway:"🚞",mountain_snow:"🏔",mouse:"🐭",mouse2:"🐁",movie_camera:"🎥",moyai:"🗿",mrs_claus:"🤶",muscle:"💪",mushroom:"🍄",musical_keyboard:"🎹",musical_note:"🎵",musical_score:"🎼",mute:"🔇",nail_care:"💅",name_badge:"📛",national_park:"🏞",nauseated_face:"🤢",necktie:"👔",negative_squared_cross_mark:"❎",nerd_face:"🤓",neutral_face:"😐",new:"🆕",new_moon:"🌑",new_moon_with_face:"🌚",newspaper:"📰",newspaper_roll:"🗞",next_track_button:"⏭",ng:"🆖",no_good_man:"🙅&zwj;♂️",no_good_woman:"🙅",night_with_stars:"🌃",no_bell:"🔕",no_bicycles:"🚳",no_entry:"⛔️",no_entry_sign:"🚫",no_mobile_phones:"📵",no_mouth:"😶",no_pedestrians:"🚷",no_smoking:"🚭","non-potable_water":"🚱",nose:"👃",notebook:"📓",notebook_with_decorative_cover:"📔",notes:"🎶",nut_and_bolt:"🔩",o:"⭕️",o2:"🅾️",ocean:"🌊",octopus:"🐙",oden:"🍢",office:"🏢",oil_drum:"🛢",ok:"🆗",ok_hand:"👌",ok_man:"🙆&zwj;♂️",ok_woman:"🙆",old_key:"🗝",older_man:"👴",older_woman:"👵",om:"🕉",on:"🔛",oncoming_automobile:"🚘",oncoming_bus:"🚍",oncoming_police_car:"🚔",oncoming_taxi:"🚖",open_file_folder:"📂",open_hands:"👐",open_mouth:"😮",open_umbrella:"☂️",ophiuchus:"⛎",orange_book:"📙",orthodox_cross:"☦️",outbox_tray:"📤",owl:"🦉",ox:"🐂",package:"📦",page_facing_up:"📄",page_with_curl:"📃",pager:"📟",paintbrush:"🖌",palm_tree:"🌴",pancakes:"🥞",panda_face:"🐼",paperclip:"📎",paperclips:"🖇",parasol_on_ground:"⛱",parking:"🅿️",part_alternation_mark:"〽️",partly_sunny:"⛅️",passenger_ship:"🛳",passport_control:"🛂",pause_button:"⏸",peace_symbol:"☮️",peach:"🍑",peanuts:"🥜",pear:"🍐",pen:"🖊",pencil2:"✏️",penguin:"🐧",pensive:"😔",performing_arts:"🎭",persevere:"😣",person_fencing:"🤺",pouting_woman:"🙎",phone:"☎️",pick:"⛏",pig:"🐷",pig2:"🐖",pig_nose:"🐽",pill:"💊",pineapple:"🍍",ping_pong:"🏓",pisces:"♓️",pizza:"🍕",place_of_worship:"🛐",plate_with_cutlery:"🍽",play_or_pause_button:"⏯",point_down:"👇",point_left:"👈",point_right:"👉",point_up:"☝️",point_up_2:"👆",police_car:"🚓",policewoman:"👮&zwj;♀️",poodle:"🐩",popcorn:"🍿",post_office:"🏣",postal_horn:"📯",postbox:"📮",potable_water:"🚰",potato:"🥔",pouch:"👝",poultry_leg:"🍗",pound:"💷",rage:"😡",pouting_cat:"😾",pouting_man:"🙎&zwj;♂️",pray:"🙏",prayer_beads:"📿",pregnant_woman:"🤰",previous_track_button:"⏮",prince:"🤴",princess:"👸",printer:"🖨",purple_heart:"💜",purse:"👛",pushpin:"📌",put_litter_in_its_place:"🚮",question:"❓",rabbit:"🐰",rabbit2:"🐇",racehorse:"🐎",racing_car:"🏎",radio:"📻",radio_button:"🔘",radioactive:"☢️",railway_car:"🚃",railway_track:"🛤",rainbow:"🌈",rainbow_flag:"🏳️&zwj;🌈",raised_back_of_hand:"🤚",raised_hand_with_fingers_splayed:"🖐",raised_hands:"🙌",raising_hand_woman:"🙋",raising_hand_man:"🙋&zwj;♂️",ram:"🐏",ramen:"🍜",rat:"🐀",record_button:"⏺",recycle:"♻️",red_circle:"🔴",registered:"®️",relaxed:"☺️",relieved:"😌",reminder_ribbon:"🎗",repeat:"🔁",repeat_one:"🔂",rescue_worker_helmet:"⛑",restroom:"🚻",revolving_hearts:"💞",rewind:"⏪",rhinoceros:"🦏",ribbon:"🎀",rice:"🍚",rice_ball:"🍙",rice_cracker:"🍘",rice_scene:"🎑",right_anger_bubble:"🗯",ring:"💍",robot:"🤖",rocket:"🚀",rofl:"🤣",roll_eyes:"🙄",roller_coaster:"🎢",rooster:"🐓",rose:"🌹",rosette:"🏵",rotating_light:"🚨",round_pushpin:"📍",rowing_man:"🚣",rowing_woman:"🚣&zwj;♀️",rugby_football:"🏉",running_man:"🏃",running_shirt_with_sash:"🎽",running_woman:"🏃&zwj;♀️",sa:"🈂️",sagittarius:"♐️",sake:"🍶",sandal:"👡",santa:"🎅",satellite:"📡",saxophone:"🎷",school:"🏫",school_satchel:"🎒",scissors:"✂️",scorpion:"🦂",scorpius:"♏️",scream:"😱",scream_cat:"🙀",scroll:"📜",seat:"💺",secret:"㊙️",see_no_evil:"🙈",seedling:"🌱",selfie:"🤳",shallow_pan_of_food:"🥘",shamrock:"☘️",shark:"🦈",shaved_ice:"🍧",sheep:"🐑",shell:"🐚",shield:"🛡",shinto_shrine:"⛩",ship:"🚢",shirt:"👕",shopping:"🛍",shopping_cart:"🛒",shower:"🚿",shrimp:"🦐",signal_strength:"📶",six_pointed_star:"🔯",ski:"🎿",skier:"⛷",skull:"💀",skull_and_crossbones:"☠️",sleeping:"😴",sleeping_bed:"🛌",sleepy:"😪",slightly_frowning_face:"🙁",slightly_smiling_face:"🙂",slot_machine:"🎰",small_airplane:"🛩",small_blue_diamond:"🔹",small_orange_diamond:"🔸",small_red_triangle:"🔺",small_red_triangle_down:"🔻",smile:"😄",smile_cat:"😸",smiley:"😃",smiley_cat:"😺",smiling_imp:"😈",smirk:"😏",smirk_cat:"😼",smoking:"🚬",snail:"🐌",snake:"🐍",sneezing_face:"🤧",snowboarder:"🏂",snowflake:"❄️",snowman:"⛄️",snowman_with_snow:"☃️",sob:"😭",soccer:"⚽️",soon:"🔜",sos:"🆘",sound:"🔉",space_invader:"👾",spades:"♠️",spaghetti:"🍝",sparkle:"❇️",sparkler:"🎇",sparkles:"✨",sparkling_heart:"💖",speak_no_evil:"🙊",speaker:"🔈",speaking_head:"🗣",speech_balloon:"💬",speedboat:"🚤",spider:"🕷",spider_web:"🕸",spiral_calendar:"🗓",spiral_notepad:"🗒",spoon:"🥄",squid:"🦑",stadium:"🏟",star:"⭐️",star2:"🌟",star_and_crescent:"☪️",star_of_david:"✡️",stars:"🌠",station:"🚉",statue_of_liberty:"🗽",steam_locomotive:"🚂",stew:"🍲",stop_button:"⏹",stop_sign:"🛑",stopwatch:"⏱",straight_ruler:"📏",strawberry:"🍓",stuck_out_tongue:"😛",stuck_out_tongue_closed_eyes:"😝",stuck_out_tongue_winking_eye:"😜",studio_microphone:"🎙",stuffed_flatbread:"🥙",sun_behind_large_cloud:"🌥",sun_behind_rain_cloud:"🌦",sun_behind_small_cloud:"🌤",sun_with_face:"🌞",sunflower:"🌻",sunglasses:"😎",sunny:"☀️",sunrise:"🌅",sunrise_over_mountains:"🌄",surfing_man:"🏄",surfing_woman:"🏄&zwj;♀️",sushi:"🍣",suspension_railway:"🚟",sweat:"😓",sweat_drops:"💦",sweat_smile:"😅",sweet_potato:"🍠",swimming_man:"🏊",swimming_woman:"🏊&zwj;♀️",symbols:"🔣",synagogue:"🕍",syringe:"💉",taco:"🌮",tada:"🎉",tanabata_tree:"🎋",taurus:"♉️",taxi:"🚕",tea:"🍵",telephone_receiver:"📞",telescope:"🔭",tennis:"🎾",tent:"⛺️",thermometer:"🌡",thinking:"🤔",thought_balloon:"💭",ticket:"🎫",tickets:"🎟",tiger:"🐯",tiger2:"🐅",timer_clock:"⏲",tipping_hand_man:"💁&zwj;♂️",tired_face:"😫",tm:"™️",toilet:"🚽",tokyo_tower:"🗼",tomato:"🍅",tongue:"👅",top:"🔝",tophat:"🎩",tornado:"🌪",trackball:"🖲",tractor:"🚜",traffic_light:"🚥",train:"🚋",train2:"🚆",tram:"🚊",triangular_flag_on_post:"🚩",triangular_ruler:"📐",trident:"🔱",triumph:"😤",trolleybus:"🚎",trophy:"🏆",tropical_drink:"🍹",tropical_fish:"🐠",truck:"🚚",trumpet:"🎺",tulip:"🌷",tumbler_glass:"🥃",turkey:"🦃",turtle:"🐢",tv:"📺",twisted_rightwards_arrows:"🔀",two_hearts:"💕",two_men_holding_hands:"👬",two_women_holding_hands:"👭",u5272:"🈹",u5408:"🈴",u55b6:"🈺",u6307:"🈯️",u6708:"🈷️",u6709:"🈶",u6e80:"🈵",u7121:"🈚️",u7533:"🈸",u7981:"🈲",u7a7a:"🈳",umbrella:"☔️",unamused:"😒",underage:"🔞",unicorn:"🦄",unlock:"🔓",up:"🆙",upside_down_face:"🙃",v:"✌️",vertical_traffic_light:"🚦",vhs:"📼",vibration_mode:"📳",video_camera:"📹",video_game:"🎮",violin:"🎻",virgo:"♍️",volcano:"🌋",volleyball:"🏐",vs:"🆚",vulcan_salute:"🖖",walking_man:"🚶",walking_woman:"🚶&zwj;♀️",waning_crescent_moon:"🌘",waning_gibbous_moon:"🌖",warning:"⚠️",wastebasket:"🗑",watch:"⌚️",water_buffalo:"🐃",watermelon:"🍉",wave:"👋",wavy_dash:"〰️",waxing_crescent_moon:"🌒",wc:"🚾",weary:"😩",wedding:"💒",weight_lifting_man:"🏋️",weight_lifting_woman:"🏋️&zwj;♀️",whale:"🐳",whale2:"🐋",wheel_of_dharma:"☸️",wheelchair:"♿️",white_check_mark:"✅",white_circle:"⚪️",white_flag:"🏳️",white_flower:"💮",white_large_square:"⬜️",white_medium_small_square:"◽️",white_medium_square:"◻️",white_small_square:"▫️",white_square_button:"🔳",wilted_flower:"🥀",wind_chime:"🎐",wind_face:"🌬",wine_glass:"🍷",wink:"😉",wolf:"🐺",woman:"👩",woman_artist:"👩&zwj;🎨",woman_astronaut:"👩&zwj;🚀",woman_cartwheeling:"🤸&zwj;♀️",woman_cook:"👩&zwj;🍳",woman_facepalming:"🤦&zwj;♀️",woman_factory_worker:"👩&zwj;🏭",woman_farmer:"👩&zwj;🌾",woman_firefighter:"👩&zwj;🚒",woman_health_worker:"👩&zwj;⚕️",woman_judge:"👩&zwj;⚖️",woman_juggling:"🤹&zwj;♀️",woman_mechanic:"👩&zwj;🔧",woman_office_worker:"👩&zwj;💼",woman_pilot:"👩&zwj;✈️",woman_playing_handball:"🤾&zwj;♀️",woman_playing_water_polo:"🤽&zwj;♀️",woman_scientist:"👩&zwj;🔬",woman_shrugging:"🤷&zwj;♀️",woman_singer:"👩&zwj;🎤",woman_student:"👩&zwj;🎓",woman_teacher:"👩&zwj;🏫",woman_technologist:"👩&zwj;💻",woman_with_turban:"👳&zwj;♀️",womans_clothes:"👚",womans_hat:"👒",women_wrestling:"🤼&zwj;♀️",womens:"🚺",world_map:"🗺",worried:"😟",wrench:"🔧",writing_hand:"✍️",x:"❌",yellow_heart:"💛",yen:"💴",yin_yang:"☯️",yum:"😋",zap:"⚡️",zipper_mouth_face:"🤐",zzz:"💤",octocat:'<img alt=":octocat:" height="20" width="20" align="absmiddle" src="https://assets-cdn.github.com/images/icons/emoji/octocat.png">',showdown:"<span style=\"font-family: 'Anonymous Pro', monospace; text-decoration: underline; text-decoration-style: dashed; text-decoration-color: #3e8b8a;text-underline-position: under;\">S</span>"},t.Converter=function(e){var n={},r=[],c=[],d={},u=i,p={parsed:{},raw:"",format:""};function m(e,n){if(n=n||null,t.helper.isString(e)){if(n=e=t.helper.stdExtName(e),t.extensions[e])return console.warn("DEPRECATION WARNING: "+e+" is an old extension that uses a deprecated loading method.Please inform the developer that the extension should be updated!"),void function(e,n){if("function"==typeof e&&(e=e(new t.Converter)),t.helper.isArray(e)||(e=[e]),n=l(e,n),!n.valid)throw Error(n.error);for(var o=0;o<e.length;++o)switch(e[o].type){case"lang":r.push(e[o]);break;case"output":c.push(e[o]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}}(t.extensions[e],e);if(t.helper.isUndefined(o[e]))throw Error('Extension "'+e+'" could not be loaded. It was either not found or is not a valid extension.');e=o[e]}if("function"==typeof e&&(e=e()),n=l(e=t.helper.isArray(e)?e:[e],n),!n.valid)throw Error(n.error);for(var a=0;a<e.length;++a){switch(e[a].type){case"lang":r.push(e[a]);break;case"output":c.push(e[a])}if(e[a].hasOwnProperty("listeners"))for(var i in e[a].listeners)e[a].listeners.hasOwnProperty(i)&&f(i,e[a].listeners[i])}}function f(e,n){if(!t.helper.isString(e))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+typeof e+" given");if("function"!=typeof n)throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+typeof n+" given");d.hasOwnProperty(e)||(d[e]=[]),d[e].push(n)}!function(){for(var r in e=e||{},a)a.hasOwnProperty(r)&&(n[r]=a[r]);if("object"!=typeof e)throw Error("Converter expects the passed parameter to be an object, but "+typeof e+" was passed instead.");for(var o in e)e.hasOwnProperty(o)&&(n[o]=e[o]);n.extensions&&t.helper.forEach(n.extensions,m)}(),this._dispatch=function(e,n,t,r){if(d.hasOwnProperty(e))for(var o=0;o<d[e].length;++o){var a=d[e][o](e,n,this,t,r);a&&void 0!==a&&(n=a)}return n},this.listen=function(e,n){return f(e,n),this},this.makeHtml=function(e){if(!e)return e;var o,a,i={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:r,outputModifiers:c,converter:this,ghCodeBlocks:[],metadata:{parsed:{},raw:"",format:""}};return e=(e=(e=(e=(e=e.replace(/¨/g,"¨T")).replace(/\$/g,"¨D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/\u00A0/g,"&nbsp;"),n.smartIndentationFix&&(a=(o=e).match(/^\s*/)[0].length,a=new RegExp("^\\s{0,"+a+"}","gm"),e=o.replace(a,"")),e="\n\n"+e+"\n\n",e=(e=t.subParser("detab")(e,n,i)).replace(/^[ \t]+$/gm,""),t.helper.forEach(r,(function(r){e=t.subParser("runExtension")(r,e,n,i)})),e=t.subParser("metadata")(e,n,i),e=t.subParser("hashPreCodeTags")(e,n,i),e=t.subParser("githubCodeBlocks")(e,n,i),e=t.subParser("hashHTMLBlocks")(e,n,i),e=t.subParser("hashCodeTags")(e,n,i),e=t.subParser("stripLinkDefinitions")(e,n,i),e=t.subParser("blockGamut")(e,n,i),e=t.subParser("unhashHTMLSpans")(e,n,i),e=(e=(e=t.subParser("unescapeSpecialChars")(e,n,i)).replace(/¨D/g,"$$")).replace(/¨T/g,"¨"),e=t.subParser("completeHTMLDocument")(e,n,i),t.helper.forEach(c,(function(r){e=t.subParser("runExtension")(r,e,n,i)})),p=i.metadata,e},this.makeMarkdown=this.makeMd=function(e,n){if(e=(e=(e=e.replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/>[ \t]+</,">¨NBSP;<"),!n){if(!window||!window.document)throw new Error("HTMLParser is undefined. If in a webworker or nodejs environment, you need to provide a WHATWG DOM and HTML such as JSDOM");n=window.document}n=n.createElement("div"),n.innerHTML=e;var r={preList:function(e){for(var n=e.querySelectorAll("pre"),r=[],o=0;o<n.length;++o)if(1===n[o].childElementCount&&"code"===n[o].firstChild.tagName.toLowerCase()){var a=n[o].firstChild.innerHTML.trim(),i=n[o].firstChild.getAttribute("data-language")||"";if(""===i)for(var s=n[o].firstChild.className.split(" "),l=0;l<s.length;++l){var c=s[l].match(/^language-(.+)$/);if(null!==c){i=c[1];break}}a=t.helper.unescapeHTMLEntities(a),r.push(a),n[o].outerHTML='<precode language="'+i+'" precodenum="'+o.toString()+'"></precode>'}else r.push(n[o].innerHTML),n[o].innerHTML="",n[o].setAttribute("prenum",o.toString());return r}(n)};!function e(n){for(var t=0;t<n.childNodes.length;++t){var r=n.childNodes[t];3===r.nodeType?/\S/.test(r.nodeValue)||/^[ ]+$/.test(r.nodeValue)?(r.nodeValue=r.nodeValue.split("\n").join(" "),r.nodeValue=r.nodeValue.replace(/(\s)+/g,"$1")):(n.removeChild(r),--t):1===r.nodeType&&e(r)}}(n);for(var o=n.childNodes,a="",i=0;i<o.length;i++)a+=t.subParser("makeMarkdown.node")(o[i],r);return a},this.setOption=function(e,t){n[e]=t},this.getOption=function(e){return n[e]},this.getOptions=function(){return n},this.addExtension=function(e,n){m(e,n=n||null)},this.useExtension=function(e){m(e)},this.setFlavor=function(e){if(!s.hasOwnProperty(e))throw Error(e+" flavor was not found");var t,r=s[e];for(t in u=e,r)r.hasOwnProperty(t)&&(n[t]=r[t])},this.getFlavor=function(){return u},this.removeExtension=function(e){t.helper.isArray(e)||(e=[e]);for(var n=0;n<e.length;++n){for(var o=e[n],a=0;a<r.length;++a)r[a]===o&&r.splice(a,1);for(var i=0;i<c.length;++i)c[i]===o&&c.splice(i,1)}},this.getAllExtensions=function(){return{language:r,output:c}},this.getMetadata=function(e){return e?p.raw:p.parsed},this.getMetadataFormat=function(){return p.format},this._setMetadataPair=function(e,n){p.parsed[e]=n},this._setMetadataFormat=function(e){p.format=e},this._setMetadataRaw=function(e){p.raw=e}},t.subParser("anchors",(function(e,n,r){function o(e,o,a,i,s,l,c){if(t.helper.isUndefined(c)&&(c=""),a=a.toLowerCase(),-1<e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m))i="";else if(!i){if(i="#"+(a=a||o.toLowerCase().replace(/ ?\n/g," ")),t.helper.isUndefined(r.gUrls[a]))return e;i=r.gUrls[a],t.helper.isUndefined(r.gTitles[a])||(c=r.gTitles[a])}return a='<a href="'+(i=i.replace(t.helper.regexes.asteriskDashAndColon,t.helper.escapeCharactersCallback))+'"',""!==c&&null!==c&&(a+=' title="'+(c=(c=c.replace(/"/g,"&quot;")).replace(t.helper.regexes.asteriskDashAndColon,t.helper.escapeCharactersCallback))+'"'),n.openLinksInNewWindow&&!/^#/.test(i)&&(a+=' rel="noopener noreferrer" target="¨E95Eblank"'),a+">"+o+"</a>"}return e=(e=(e=(e=(e=r.converter._dispatch("anchors.before",e,n,r)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g,o)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,o)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,o)).replace(/\[([^\[\]]+)]()()()()()/g,o),n.ghMentions&&(e=e.replace(/(^|\s)(\\)?(@([a-z\d]+(?:[a-z\d.-]+?[a-z\d]+)*))/gim,(function(e,r,o,a,i){if("\\"===o)return r+a;if(!t.helper.isString(n.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");return o="",r+'<a href="'+n.ghMentionsLink.replace(/\{u}/g,i)+'"'+(o=n.openLinksInNewWindow?' rel="noopener noreferrer" target="¨E95Eblank"':o)+">"+a+"</a>"}))),r.converter._dispatch("anchors.after",e,n,r)}));var m=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+?\.[^'">\s]+?)()(\1)?(?=\s|$)(?!["<>])/gi,f=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+\.[^'">\s]+?)([.!?,()\[\]])?(\1)?(?=\s|$)(?!["<>])/gi,h=/()<(((https?|ftp|dict):\/\/|www\.)[^'">\s]+)()>()/gi,w=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gim,b=/<()(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi;t.subParser("autoLinks",(function(e,n,t){return e=(e=(e=t.converter._dispatch("autoLinks.before",e,n,t)).replace(h,u(n))).replace(b,p(n,t)),t.converter._dispatch("autoLinks.after",e,n,t)})),t.subParser("simplifiedAutoLinks",(function(e,n,t){return n.simplifiedAutoLink?(e=t.converter._dispatch("simplifiedAutoLinks.before",e,n,t),e=(e=n.excludeTrailingPunctuationFromURLs?e.replace(f,u(n)):e.replace(m,u(n))).replace(w,p(n,t)),e=t.converter._dispatch("simplifiedAutoLinks.after",e,n,t)):e})),t.subParser("blockGamut",(function(e,n,r){return e=r.converter._dispatch("blockGamut.before",e,n,r),e=t.subParser("blockQuotes")(e,n,r),e=t.subParser("headers")(e,n,r),e=t.subParser("horizontalRule")(e,n,r),e=t.subParser("lists")(e,n,r),e=t.subParser("codeBlocks")(e,n,r),e=t.subParser("tables")(e,n,r),e=t.subParser("hashHTMLBlocks")(e,n,r),e=t.subParser("paragraphs")(e,n,r),r.converter._dispatch("blockGamut.after",e,n,r)})),t.subParser("blockQuotes",(function(e,n,r){e=r.converter._dispatch("blockQuotes.before",e,n,r);var o=/(^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+/gm;return n.splitAdjacentBlockquotes&&(o=/^ {0,3}>[\s\S]*?(?:\n\n)/gm),e=(e+="\n\n").replace(o,(function(e){return e=(e=(e=e.replace(/^[ \t]*>[ \t]?/gm,"")).replace(/¨0/g,"")).replace(/^[ \t]+$/gm,""),e=t.subParser("githubCodeBlocks")(e,n,r),e=(e=(e=t.subParser("blockGamut")(e,n,r)).replace(/(^|\n)/g,"$1  ")).replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,(function(e,n){return n.replace(/^  /gm,"¨0").replace(/¨0/g,"")})),t.subParser("hashBlock")("<blockquote>\n"+e+"\n</blockquote>",n,r)})),r.converter._dispatch("blockQuotes.after",e,n,r)})),t.subParser("codeBlocks",(function(e,n,r){return e=r.converter._dispatch("codeBlocks.before",e,n,r),e=(e=(e+="¨0").replace(/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=¨0))/g,(function(e,o,a){var i=o;o=a,a="\n",i=t.subParser("outdent")(i,n,r);return i=t.subParser("encodeCode")(i,n,r),i="<pre><code>"+(i=(i=(i=t.subParser("detab")(i,n,r)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+(a=n.omitExtraWLInCodeBlocks?"":a)+"</code></pre>",t.subParser("hashBlock")(i,n,r)+o}))).replace(/¨0/,""),r.converter._dispatch("codeBlocks.after",e,n,r)})),t.subParser("codeSpans",(function(e,n,r){return e=(e=void 0===(e=r.converter._dispatch("codeSpans.before",e,n,r))?"":e).replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,(function(e,o,a,i){return i=(i=i.replace(/^([ \t]*)/g,"")).replace(/[ \t]*$/g,""),i=o+"<code>"+(i=t.subParser("encodeCode")(i,n,r))+"</code>",t.subParser("hashHTMLSpans")(i,n,r)})),r.converter._dispatch("codeSpans.after",e,n,r)})),t.subParser("completeHTMLDocument",(function(e,n,t){if(!n.completeHTMLDocument)return e;e=t.converter._dispatch("completeHTMLDocument.before",e,n,t);var r,o="html",a="<!DOCTYPE HTML>\n",i="",s='<meta charset="utf-8">\n',l="",c="";for(r in void 0!==t.metadata.parsed.doctype&&(a="<!DOCTYPE "+t.metadata.parsed.doctype+">\n","html"!==(o=t.metadata.parsed.doctype.toString().toLowerCase())&&"html5"!==o||(s='<meta charset="utf-8">')),t.metadata.parsed)if(t.metadata.parsed.hasOwnProperty(r))switch(r.toLowerCase()){case"doctype":break;case"title":i="<title>"+t.metadata.parsed.title+"</title>\n";break;case"charset":s="html"===o||"html5"===o?'<meta charset="'+t.metadata.parsed.charset+'">\n':'<meta name="charset" content="'+t.metadata.parsed.charset+'">\n';break;case"language":case"lang":l=' lang="'+t.metadata.parsed[r]+'"',c+='<meta name="'+r+'" content="'+t.metadata.parsed[r]+'">\n';break;default:c+='<meta name="'+r+'" content="'+t.metadata.parsed[r]+'">\n'}return e=a+"<html"+l+">\n<head>\n"+i+s+c+"</head>\n<body>\n"+e.trim()+"\n</body>\n</html>",t.converter._dispatch("completeHTMLDocument.after",e,n,t)})),t.subParser("detab",(function(e,n,t){return e=(e=(e=(e=(e=(e=t.converter._dispatch("detab.before",e,n,t)).replace(/\t(?=\t)/g,"    ")).replace(/\t/g,"¨A¨B")).replace(/¨B(.+?)¨A/g,(function(e,n){for(var t=n,r=4-t.length%4,o=0;o<r;o++)t+=" ";return t}))).replace(/¨A/g,"    ")).replace(/¨B/g,""),t.converter._dispatch("detab.after",e,n,t)})),t.subParser("ellipsis",(function(e,n,t){return n.ellipsis?(e=(e=t.converter._dispatch("ellipsis.before",e,n,t)).replace(/\.\.\./g,"…"),e=t.converter._dispatch("ellipsis.after",e,n,t)):e})),t.subParser("emoji",(function(e,n,r){return n.emoji?(e=(e=r.converter._dispatch("emoji.before",e,n,r)).replace(/:([\S]+?):/g,(function(e,n){return t.helper.emojis.hasOwnProperty(n)?t.helper.emojis[n]:e})),r.converter._dispatch("emoji.after",e,n,r)):e})),t.subParser("encodeAmpsAndAngles",(function(e,n,t){return e=(e=(e=(e=(e=t.converter._dispatch("encodeAmpsAndAngles.before",e,n,t)).replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;")).replace(/<(?![a-z\/?$!])/gi,"&lt;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;"),t.converter._dispatch("encodeAmpsAndAngles.after",e,n,t)})),t.subParser("encodeBackslashEscapes",(function(e,n,r){return e=(e=(e=r.converter._dispatch("encodeBackslashEscapes.before",e,n,r)).replace(/\\(\\)/g,t.helper.escapeCharactersCallback)).replace(/\\([`*_{}\[\]()>#+.!~=|:-])/g,t.helper.escapeCharactersCallback),r.converter._dispatch("encodeBackslashEscapes.after",e,n,r)})),t.subParser("encodeCode",(function(e,n,r){return e=(e=r.converter._dispatch("encodeCode.before",e,n,r)).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,t.helper.escapeCharactersCallback),r.converter._dispatch("encodeCode.after",e,n,r)})),t.subParser("escapeSpecialCharsWithinTagAttributes",(function(e,n,r){return e=(e=(e=r.converter._dispatch("escapeSpecialCharsWithinTagAttributes.before",e,n,r)).replace(/<\/?[a-z\d_:-]+(?:[\s]+[\s\S]+?)?>/gi,(function(e){return e.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,t.helper.escapeCharactersCallback)}))).replace(/<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi,(function(e){return e.replace(/([\\`*_~=|])/g,t.helper.escapeCharactersCallback)})),r.converter._dispatch("escapeSpecialCharsWithinTagAttributes.after",e,n,r)})),t.subParser("githubCodeBlocks",(function(e,n,r){return n.ghCodeBlocks?(e=r.converter._dispatch("githubCodeBlocks.before",e,n,r),e=(e=(e+="¨0").replace(/(?:^|\n)(?: {0,3})(```+|~~~+)(?: *)([^\s`~]*)\n([\s\S]*?)\n(?: {0,3})\1/g,(function(e,o,a,i){var s=n.omitExtraWLInCodeBlocks?"":"\n";return i=t.subParser("encodeCode")(i,n,r),i="<pre><code"+(a?' class="'+a+" language-"+a+'"':"")+">"+(i=(i=(i=t.subParser("detab")(i,n,r)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+s+"</code></pre>",i=t.subParser("hashBlock")(i,n,r),"\n\n¨G"+(r.ghCodeBlocks.push({text:e,codeblock:i})-1)+"G\n\n"}))).replace(/¨0/,""),r.converter._dispatch("githubCodeBlocks.after",e,n,r)):e})),t.subParser("hashBlock",(function(e,n,t){return e=(e=t.converter._dispatch("hashBlock.before",e,n,t)).replace(/(^\n+|\n+$)/g,""),e="\n\n¨K"+(t.gHtmlBlocks.push(e)-1)+"K\n\n",t.converter._dispatch("hashBlock.after",e,n,t)})),t.subParser("hashCodeTags",(function(e,n,r){return e=r.converter._dispatch("hashCodeTags.before",e,n,r),e=t.helper.replaceRecursiveRegExp(e,(function(e,o,a,i){return i=a+t.subParser("encodeCode")(o,n,r)+i,"¨C"+(r.gHtmlSpans.push(i)-1)+"C"}),"<code\\b[^>]*>","</code>","gim"),r.converter._dispatch("hashCodeTags.after",e,n,r)})),t.subParser("hashElement",(function(e,n,t){return function(e,n){return n=(n=(n=n.replace(/\n\n/g,"\n")).replace(/^\n/,"")).replace(/\n+$/g,""),"\n\n¨K"+(t.gHtmlBlocks.push(n)-1)+"K\n\n"}})),t.subParser("hashHTMLBlocks",(function(e,n,r){function o(e,n,t,o){return-1!==t.search(/\bmarkdown\b/)&&(e=t+r.converter.makeHtml(n)+o),"\n\n¨K"+(r.gHtmlBlocks.push(e)-1)+"K\n\n"}e=r.converter._dispatch("hashHTMLBlocks.before",e,n,r);var a=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"];n.backslashEscapesHTMLTags&&(e=e.replace(/\\<(\/?[^>]+?)>/g,(function(e,n){return"&lt;"+n+"&gt;"})));for(var i=0;i<a.length;++i)for(var s=new RegExp("^ {0,3}(<"+a[i]+"\\b[^>]*>)","im"),l="<"+a[i]+"\\b[^>]*>",c="</"+a[i]+">";-1!==(u=t.helper.regexIndexOf(e,s));){var d=t.helper.splitAtIndex(e,u),u=t.helper.replaceRecursiveRegExp(d[1],o,l,c,"im");if(u===d[1])break;e=d[0].concat(u)}return e=e.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,t.subParser("hashElement")(e,n,r)),e=(e=t.helper.replaceRecursiveRegExp(e,(function(e){return"\n\n¨K"+(r.gHtmlBlocks.push(e)-1)+"K\n\n"}),"^ {0,3}\x3c!--","--\x3e","gm")).replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,t.subParser("hashElement")(e,n,r)),r.converter._dispatch("hashHTMLBlocks.after",e,n,r)})),t.subParser("hashHTMLSpans",(function(e,n,t){function r(e){return"¨C"+(t.gHtmlSpans.push(e)-1)+"C"}return e=(e=(e=(e=(e=t.converter._dispatch("hashHTMLSpans.before",e,n,t)).replace(/<[^>]+?\/>/gi,r)).replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,r)).replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,r)).replace(/<[^>]+?>/gi,r),t.converter._dispatch("hashHTMLSpans.after",e,n,t)})),t.subParser("unhashHTMLSpans",(function(e,n,t){e=t.converter._dispatch("unhashHTMLSpans.before",e,n,t);for(var r=0;r<t.gHtmlSpans.length;++r){for(var o=t.gHtmlSpans[r],a=0;/¨C(\d+)C/.test(o);){var i=RegExp.$1;o=o.replace("¨C"+i+"C",t.gHtmlSpans[i]);if(10===a){console.error("maximum nesting of 10 spans reached!!!");break}++a}e=e.replace("¨C"+r+"C",o)}return t.converter._dispatch("unhashHTMLSpans.after",e,n,t)})),t.subParser("hashPreCodeTags",(function(e,n,r){return e=r.converter._dispatch("hashPreCodeTags.before",e,n,r),e=t.helper.replaceRecursiveRegExp(e,(function(e,o,a,i){return i=a+t.subParser("encodeCode")(o,n,r)+i,"\n\n¨G"+(r.ghCodeBlocks.push({text:e,codeblock:i})-1)+"G\n\n"}),"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),r.converter._dispatch("hashPreCodeTags.after",e,n,r)})),t.subParser("headers",(function(e,n,r){e=r.converter._dispatch("headers.before",e,n,r);var o=isNaN(parseInt(n.headerLevelStart))?1:parseInt(n.headerLevelStart),a=n.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,i=n.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm;function s(e){var o;return!n.customizedHeaderId||(o=e.match(/\{([^{]+?)}\s*$/))&&o[1]&&(e=o[1]),o=e,e=t.helper.isString(n.prefixHeaderId)?n.prefixHeaderId:!0===n.prefixHeaderId?"section-":"",n.rawPrefixHeaderId||(o=e+o),o=(n.ghCompatibleHeaderId?o.replace(/ /g,"-").replace(/&amp;/g,"").replace(/¨T/g,"").replace(/¨D/g,"").replace(/[&+$,\/:;=?@"#{}|^¨~\[\]`\\*)(%.!'<>]/g,""):n.rawHeaderId?o.replace(/ /g,"-").replace(/&amp;/g,"&").replace(/¨T/g,"¨").replace(/¨D/g,"$").replace(/["']/g,"-"):o.replace(/[^\w]/g,"")).toLowerCase(),n.rawPrefixHeaderId&&(o=e+o),r.hashLinkCounts[o]?o=o+"-"+r.hashLinkCounts[o]++:r.hashLinkCounts[o]=1,o}return e=(e=e.replace(a,(function(e,a){var i=t.subParser("spanGamut")(a,n,r);a=n.noHeaderId?"":' id="'+s(a)+'"',i="<h"+o+a+">"+i+"</h"+o+">";return t.subParser("hashBlock")(i,n,r)}))).replace(i,(function(e,a){var i=t.subParser("spanGamut")(a,n,r),l=n.noHeaderId?"":' id="'+s(a)+'"';a=o+1,a="<h"+a+l+">"+i+"</h"+a+">";return t.subParser("hashBlock")(a,n,r)})),i=n.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm,e=e.replace(i,(function(e,a,i){var l=i;return n.customizedHeaderId&&(l=i.replace(/\s?\{([^{]+?)}\s*$/,"")),l=t.subParser("spanGamut")(l,n,r),i=n.noHeaderId?"":' id="'+s(i)+'"',a=o-1+a.length,a="<h"+a+i+">"+l+"</h"+a+">",t.subParser("hashBlock")(a,n,r)})),r.converter._dispatch("headers.after",e,n,r)})),t.subParser("horizontalRule",(function(e,n,r){e=r.converter._dispatch("horizontalRule.before",e,n,r);var o=t.subParser("hashBlock")("<hr />",n,r);return e=(e=(e=e.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,o)).replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,o)).replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,o),r.converter._dispatch("horizontalRule.after",e,n,r)})),t.subParser("images",(function(e,n,r){function o(e,n,o,a,i,s,l,c){var d=r.gUrls,u=r.gTitles,p=r.gDimensions;if(o=o.toLowerCase(),c=c||"",-1<e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m))a="";else if(""===a||null===a){if(a="#"+(o=""===o||null===o?n.toLowerCase().replace(/ ?\n/g," "):o),t.helper.isUndefined(d[o]))return e;a=d[o],t.helper.isUndefined(u[o])||(c=u[o]),t.helper.isUndefined(p[o])||(i=p[o].width,s=p[o].height)}return n=n.replace(/"/g,"&quot;").replace(t.helper.regexes.asteriskDashAndColon,t.helper.escapeCharactersCallback),n='<img src="'+(a=a.replace(t.helper.regexes.asteriskDashAndColon,t.helper.escapeCharactersCallback))+'" alt="'+n+'"',c&&t.helper.isString(c)&&(n+=' title="'+(c=c.replace(/"/g,"&quot;").replace(t.helper.regexes.asteriskDashAndColon,t.helper.escapeCharactersCallback))+'"'),i&&s&&(n+=' width="'+(i="*"===i?"auto":i)+'"',n+=' height="'+(s="*"===s?"auto":s)+'"'),n+" />"}return e=(e=(e=(e=(e=(e=r.converter._dispatch("images.before",e,n,r)).replace(/!\[([^\]]*?)] ?(?:\n *)?\[([\s\S]*?)]()()()()()/g,o)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,(function(e,n,t,r,a,i,s,l){return o(e,n,t,r=r.replace(/\s/g,""),a,i,0,l)}))).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,o)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,o)).replace(/!\[([^\[\]]+)]()()()()()/g,o),r.converter._dispatch("images.after",e,n,r)})),t.subParser("italicsAndBold",(function(e,n,t){return e=t.converter._dispatch("italicsAndBold.before",e,n,t),e=n.literalMidWordUnderscores?(e=(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,(function(e,n){return"<strong><em>"+n+"</em></strong>"}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(e,n){return"<strong>"+n+"</strong>"}))).replace(/\b_(\S[\s\S]*?)_\b/g,(function(e,n){return"<em>"+n+"</em>"})):(e=(e=e.replace(/___(\S[\s\S]*?)___/g,(function(e,n){return/\S$/.test(n)?"<strong><em>"+n+"</em></strong>":e}))).replace(/__(\S[\s\S]*?)__/g,(function(e,n){return/\S$/.test(n)?"<strong>"+n+"</strong>":e}))).replace(/_([^\s_][\s\S]*?)_/g,(function(e,n){return/\S$/.test(n)?"<em>"+n+"</em>":e})),e=n.literalMidWordAsterisks?(e=(e=e.replace(/([^*]|^)\B\*\*\*(\S[\s\S]*?)\*\*\*\B(?!\*)/g,(function(e,n,t){return n+"<strong><em>"+t+"</em></strong>"}))).replace(/([^*]|^)\B\*\*(\S[\s\S]*?)\*\*\B(?!\*)/g,(function(e,n,t){return n+"<strong>"+t+"</strong>"}))).replace(/([^*]|^)\B\*(\S[\s\S]*?)\*\B(?!\*)/g,(function(e,n,t){return n+"<em>"+t+"</em>"})):(e=(e=e.replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,(function(e,n){return/\S$/.test(n)?"<strong><em>"+n+"</em></strong>":e}))).replace(/\*\*(\S[\s\S]*?)\*\*/g,(function(e,n){return/\S$/.test(n)?"<strong>"+n+"</strong>":e}))).replace(/\*([^\s*][\s\S]*?)\*/g,(function(e,n){return/\S$/.test(n)?"<em>"+n+"</em>":e})),t.converter._dispatch("italicsAndBold.after",e,n,t)})),t.subParser("lists",(function(e,n,r){function o(e,o){r.gListLevel++,e=e.replace(/\n{2,}$/,"\n");var a=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0| {0,3}([*+-]|\d+[.])[ \t]+))/gm,i=/\n[ \t]*\n(?!¨0)/.test(e+="¨0");return n.disableForced4SpacesIndentedSublists&&(a=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0|\2([*+-]|\d+[.])[ \t]+))/gm),e=(e=e.replace(a,(function(e,o,a,s,l,c,d){d=d&&""!==d.trim();var u=t.subParser("outdent")(l,n,r);l="";return c&&n.tasklists&&(l=' class="task-list-item" style="list-style-type: none;"',u=u.replace(/^[ \t]*\[(x|X| )?]/m,(function(){var e='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return d&&(e+=" checked"),e+">"}))),u=u.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,(function(e){return"¨A"+e})),"<li"+l+">"+(u=(u=o||-1<u.search(/\n{2,}/)?(u=t.subParser("githubCodeBlocks")(u,n,r),t.subParser("blockGamut")(u,n,r)):(u=(u=t.subParser("lists")(u,n,r)).replace(/\n$/,""),u=(u=t.subParser("hashHTMLBlocks")(u,n,r)).replace(/\n\n+/g,"\n\n"),(i?t.subParser("paragraphs"):t.subParser("spanGamut"))(u,n,r))).replace("¨A",""))+"</li>\n"}))).replace(/¨0/g,""),r.gListLevel--,o?e.replace(/\s+$/,""):e}function a(e,n){return"ol"===n&&(e=e.match(/^ *(\d+)\./),e&&"1"!==e[1])?' start="'+e[1]+'"':""}function i(e,t,r){var i,s=n.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,l=n.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,c="ul"===t?s:l,d="";return-1!==e.search(c)?function n(i){var u=i.search(c),p=a(e,t);-1!==u?(d+="\n\n<"+t+p+">\n"+o(i.slice(0,u),!!r)+"</"+t+">\n",c="ul"===(t="ul"===t?"ol":"ul")?s:l,n(i.slice(u))):d+="\n\n<"+t+p+">\n"+o(i,!!r)+"</"+t+">\n"}(e):(i=a(e,t),d="\n\n<"+t+i+">\n"+o(e,!!r)+"</"+t+">\n"),d}return e=r.converter._dispatch("lists.before",e,n,r),e+="¨0",e=(e=r.gListLevel?e.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,n,t){return i(n,-1<t.search(/[*+-]/g)?"ul":"ol",!0)})):e.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,n,t,r){return i(t,-1<r.search(/[*+-]/g)?"ul":"ol",!1)}))).replace(/¨0/,""),r.converter._dispatch("lists.after",e,n,r)})),t.subParser("metadata",(function(e,n,t){return n.metadata?(e=(e=(e=(e=t.converter._dispatch("metadata.before",e,n,t)).replace(/^\s*«««+(\S*?)\n([\s\S]+?)\n»»»+\n/,(function(e,n,t){return r(t),"¨M"}))).replace(/^\s*---+(\S*?)\n([\s\S]+?)\n---+\n/,(function(e,n,o){return n&&(t.metadata.format=n),r(o),"¨M"}))).replace(/¨M/g,""),e=t.converter._dispatch("metadata.after",e,n,t)):e;function r(e){(e=(e=(t.metadata.raw=e).replace(/&/g,"&amp;").replace(/"/g,"&quot;")).replace(/\n {4}/g," ")).replace(/^([\S ]+): +([\s\S]+?)$/gm,(function(e,n,r){return t.metadata.parsed[n]=r,""}))}})),t.subParser("outdent",(function(e,n,t){return e=(e=(e=t.converter._dispatch("outdent.before",e,n,t)).replace(/^(\t|[ ]{1,4})/gm,"¨0")).replace(/¨0/g,""),t.converter._dispatch("outdent.after",e,n,t)})),t.subParser("paragraphs",(function(e,n,r){for(var o=(e=(e=(e=r.converter._dispatch("paragraphs.before",e,n,r)).replace(/^\n+/g,"")).replace(/\n+$/g,"")).split(/\n{2,}/g),a=[],i=o.length,s=0;s<i;s++){var l=o[s];0<=l.search(/¨(K|G)(\d+)\1/g)?a.push(l):0<=l.search(/\S/)&&(l=(l=t.subParser("spanGamut")(l,n,r)).replace(/^([ \t]*)/g,"<p>"),l+="</p>",a.push(l))}for(i=a.length,s=0;s<i;s++){for(var c="",d=a[s],u=!1;/¨(K|G)(\d+)\1/.test(d);){var p=RegExp.$1,m=RegExp.$2;c=(c="K"===p?r.gHtmlBlocks[m]:u?t.subParser("encodeCode")(r.ghCodeBlocks[m].text,n,r):r.ghCodeBlocks[m].codeblock).replace(/\$/g,"$$$$"),d=d.replace(/(\n\n)?¨(K|G)\d+\2(\n\n)?/,c),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(d)&&(u=!0)}a[s]=d}return e=(e=(e=a.join("\n")).replace(/^\n+/g,"")).replace(/\n+$/g,""),r.converter._dispatch("paragraphs.after",e,n,r)})),t.subParser("runExtension",(function(e,n,t,r){return e.filter?n=e.filter(n,r.converter,t):e.regex&&((t=e.regex)instanceof RegExp||(t=new RegExp(t,"g")),n=n.replace(t,e.replace)),n})),t.subParser("spanGamut",(function(e,n,r){return e=r.converter._dispatch("spanGamut.before",e,n,r),e=t.subParser("codeSpans")(e,n,r),e=t.subParser("escapeSpecialCharsWithinTagAttributes")(e,n,r),e=t.subParser("encodeBackslashEscapes")(e,n,r),e=t.subParser("images")(e,n,r),e=t.subParser("anchors")(e,n,r),e=t.subParser("autoLinks")(e,n,r),e=t.subParser("simplifiedAutoLinks")(e,n,r),e=t.subParser("emoji")(e,n,r),e=t.subParser("underline")(e,n,r),e=t.subParser("italicsAndBold")(e,n,r),e=t.subParser("strikethrough")(e,n,r),e=t.subParser("ellipsis")(e,n,r),e=t.subParser("hashHTMLSpans")(e,n,r),e=t.subParser("encodeAmpsAndAngles")(e,n,r),n.simpleLineBreaks?/\n\n¨K/.test(e)||(e=e.replace(/\n+/g,"<br />\n")):e=e.replace(/  +\n/g,"<br />\n"),r.converter._dispatch("spanGamut.after",e,n,r)})),t.subParser("strikethrough",(function(e,n,r){return n.strikethrough&&(e=(e=r.converter._dispatch("strikethrough.before",e,n,r)).replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,(function(e,o){return o=o,"<del>"+(o=n.simplifiedAutoLink?t.subParser("simplifiedAutoLinks")(o,n,r):o)+"</del>"})),e=r.converter._dispatch("strikethrough.after",e,n,r)),e})),t.subParser("stripLinkDefinitions",(function(e,n,r){function o(o,a,i,s,l,c,d){return a=a.toLowerCase(),e.toLowerCase().split(a).length-1<2?o:(i.match(/^data:.+?\/.+?;base64,/)?r.gUrls[a]=i.replace(/\s/g,""):r.gUrls[a]=t.subParser("encodeAmpsAndAngles")(i,n,r),c?c+d:(d&&(r.gTitles[a]=d.replace(/"|'/g,"&quot;")),n.parseImgDimensions&&s&&l&&(r.gDimensions[a]={width:s,height:l}),""))}return e=(e=(e=(e+="¨0").replace(/^ {0,3}\[([^\]]+)]:[ \t]*\n?[ \t]*<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n\n|(?=¨0)|(?=\n\[))/gm,o)).replace(/^ {0,3}\[([^\]]+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=¨0))/gm,o)).replace(/¨0/,"")})),t.subParser("tables",(function(e,n,r){if(!n.tables)return e;function o(e){for(var o=e.split("\n"),a=0;a<o.length;++a)/^ {0,3}\|/.test(o[a])&&(o[a]=o[a].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(o[a])&&(o[a]=o[a].replace(/\|[ \t]*$/,"")),o[a]=t.subParser("codeSpans")(o[a],n,r);var i,s,l,c,d,u=o[0].split("|").map((function(e){return e.trim()})),p=o[1].split("|").map((function(e){return e.trim()})),m=[],f=[],h=[],w=[];for(o.shift(),o.shift(),a=0;a<o.length;++a)""!==o[a].trim()&&m.push(o[a].split("|").map((function(e){return e.trim()})));if(u.length<p.length)return e;for(a=0;a<p.length;++a)h.push((i=p[a],/^:[ \t]*--*$/.test(i)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(i)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(i)?' style="text-align:center;"':""));for(a=0;a<u.length;++a)t.helper.isUndefined(h[a])&&(h[a]=""),f.push((s=u[a],l=h[a],c="",s=s.trim(),"<th"+(c=n.tablesHeaderId||n.tableHeaderId?' id="'+s.replace(/ /g,"_").toLowerCase()+'"':c)+l+">"+(s=t.subParser("spanGamut")(s,n,r))+"</th>\n"));for(a=0;a<m.length;++a){for(var b=[],g=0;g<f.length;++g)t.helper.isUndefined(m[a][g]),b.push((d=m[a][g],"<td"+h[g]+">"+t.subParser("spanGamut")(d,n,r)+"</td>\n"));w.push(b)}return function(e,n){for(var t="<table>\n<thead>\n<tr>\n",r=e.length,o=0;o<r;++o)t+=e[o];for(t+="</tr>\n</thead>\n<tbody>\n",o=0;o<n.length;++o){t+="<tr>\n";for(var a=0;a<r;++a)t+=n[o][a];t+="</tr>\n"}return t+"</tbody>\n</table>\n"}(f,w)}return e=(e=(e=(e=r.converter._dispatch("tables.before",e,n,r)).replace(/\\(\|)/g,t.helper.escapeCharactersCallback)).replace(/^ {0,3}\|?.+\|.+\n {0,3}\|?[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:[-=]){2,}[\s\S]+?(?:\n\n|¨0)/gm,o)).replace(/^ {0,3}\|.+\|[ \t]*\n {0,3}\|[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*\n( {0,3}\|.+\|[ \t]*\n)*(?:\n|¨0)/gm,o),r.converter._dispatch("tables.after",e,n,r)})),t.subParser("underline",(function(e,n,r){return n.underline?(e=r.converter._dispatch("underline.before",e,n,r),e=(e=n.literalMidWordUnderscores?(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,(function(e,n){return"<u>"+n+"</u>"}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(e,n){return"<u>"+n+"</u>"})):(e=e.replace(/___(\S[\s\S]*?)___/g,(function(e,n){return/\S$/.test(n)?"<u>"+n+"</u>":e}))).replace(/__(\S[\s\S]*?)__/g,(function(e,n){return/\S$/.test(n)?"<u>"+n+"</u>":e}))).replace(/(_)/g,t.helper.escapeCharactersCallback),e=r.converter._dispatch("underline.after",e,n,r)):e})),t.subParser("unescapeSpecialChars",(function(e,n,t){return e=(e=t.converter._dispatch("unescapeSpecialChars.before",e,n,t)).replace(/¨E(\d+)E/g,(function(e,n){return n=parseInt(n),String.fromCharCode(n)})),t.converter._dispatch("unescapeSpecialChars.after",e,n,t)})),t.subParser("makeMarkdown.blockquote",(function(e,n){var r="";if(e.hasChildNodes())for(var o=e.childNodes,a=o.length,i=0;i<a;++i){var s=t.subParser("makeMarkdown.node")(o[i],n);""!==s&&(r+=s)}return"> "+(r=r.trim()).split("\n").join("\n> ")})),t.subParser("makeMarkdown.codeBlock",(function(e,n){var t=e.getAttribute("language");e=e.getAttribute("precodenum");return"```"+t+"\n"+n.preList[e]+"\n```"})),t.subParser("makeMarkdown.codeSpan",(function(e){return"`"+e.innerHTML+"`"})),t.subParser("makeMarkdown.emphasis",(function(e,n){var r="";if(e.hasChildNodes()){r+="*";for(var o=e.childNodes,a=o.length,i=0;i<a;++i)r+=t.subParser("makeMarkdown.node")(o[i],n);r+="*"}return r})),t.subParser("makeMarkdown.header",(function(e,n,r){r=new Array(r+1).join("#");var o="";if(e.hasChildNodes()){o=r+" ";for(var a=e.childNodes,i=a.length,s=0;s<i;++s)o+=t.subParser("makeMarkdown.node")(a[s],n)}return o})),t.subParser("makeMarkdown.hr",(function(){return"---"})),t.subParser("makeMarkdown.image",(function(e){var n="";return e.hasAttribute("src")&&(n+="!["+e.getAttribute("alt")+"](",n+="<"+e.getAttribute("src")+">",e.hasAttribute("width")&&e.hasAttribute("height")&&(n+=" ="+e.getAttribute("width")+"x"+e.getAttribute("height")),e.hasAttribute("title")&&(n+=' "'+e.getAttribute("title")+'"'),n+=")"),n})),t.subParser("makeMarkdown.links",(function(e,n){var r="";if(e.hasChildNodes()&&e.hasAttribute("href")){for(var o=e.childNodes,a=o.length,i=(r="[",0);i<a;++i)r+=t.subParser("makeMarkdown.node")(o[i],n);r+="](",r+="<"+e.getAttribute("href")+">",e.hasAttribute("title")&&(r+=' "'+e.getAttribute("title")+'"'),r+=")"}return r})),t.subParser("makeMarkdown.list",(function(e,n,r){var o="";if(!e.hasChildNodes())return"";for(var a=e.childNodes,i=a.length,s=e.getAttribute("start")||1,l=0;l<i;++l)void 0!==a[l].tagName&&"li"===a[l].tagName.toLowerCase()&&(o+=("ol"===r?s.toString()+". ":"- ")+t.subParser("makeMarkdown.listItem")(a[l],n),++s);return(o+="\n\x3c!-- --\x3e\n").trim()})),t.subParser("makeMarkdown.listItem",(function(e,n){for(var r="",o=e.childNodes,a=o.length,i=0;i<a;++i)r+=t.subParser("makeMarkdown.node")(o[i],n);return/\n$/.test(r)?r=r.split("\n").join("\n    ").replace(/^ {4}$/gm,"").replace(/\n\n+/g,"\n\n"):r+="\n",r})),t.subParser("makeMarkdown.node",(function(e,n,r){r=r||!1;var o="";if(3===e.nodeType)return t.subParser("makeMarkdown.txt")(e,n);if(8===e.nodeType)return"\x3c!--"+e.data+"--\x3e\n\n";if(1!==e.nodeType)return"";switch(e.tagName.toLowerCase()){case"h1":r||(o=t.subParser("makeMarkdown.header")(e,n,1)+"\n\n");break;case"h2":r||(o=t.subParser("makeMarkdown.header")(e,n,2)+"\n\n");break;case"h3":r||(o=t.subParser("makeMarkdown.header")(e,n,3)+"\n\n");break;case"h4":r||(o=t.subParser("makeMarkdown.header")(e,n,4)+"\n\n");break;case"h5":r||(o=t.subParser("makeMarkdown.header")(e,n,5)+"\n\n");break;case"h6":r||(o=t.subParser("makeMarkdown.header")(e,n,6)+"\n\n");break;case"p":r||(o=t.subParser("makeMarkdown.paragraph")(e,n)+"\n\n");break;case"blockquote":r||(o=t.subParser("makeMarkdown.blockquote")(e,n)+"\n\n");break;case"hr":r||(o=t.subParser("makeMarkdown.hr")(e,n)+"\n\n");break;case"ol":r||(o=t.subParser("makeMarkdown.list")(e,n,"ol")+"\n\n");break;case"ul":r||(o=t.subParser("makeMarkdown.list")(e,n,"ul")+"\n\n");break;case"precode":r||(o=t.subParser("makeMarkdown.codeBlock")(e,n)+"\n\n");break;case"pre":r||(o=t.subParser("makeMarkdown.pre")(e,n)+"\n\n");break;case"table":r||(o=t.subParser("makeMarkdown.table")(e,n)+"\n\n");break;case"code":o=t.subParser("makeMarkdown.codeSpan")(e,n);break;case"em":case"i":o=t.subParser("makeMarkdown.emphasis")(e,n);break;case"strong":case"b":o=t.subParser("makeMarkdown.strong")(e,n);break;case"del":o=t.subParser("makeMarkdown.strikethrough")(e,n);break;case"a":o=t.subParser("makeMarkdown.links")(e,n);break;case"img":o=t.subParser("makeMarkdown.image")(e,n);break;default:o=e.outerHTML+"\n\n"}return o})),t.subParser("makeMarkdown.paragraph",(function(e,n){var r="";if(e.hasChildNodes())for(var o=e.childNodes,a=o.length,i=0;i<a;++i)r+=t.subParser("makeMarkdown.node")(o[i],n);return r.trim()})),t.subParser("makeMarkdown.pre",(function(e,n){return e=e.getAttribute("prenum"),"<pre>"+n.preList[e]+"</pre>"})),t.subParser("makeMarkdown.strikethrough",(function(e,n){var r="";if(e.hasChildNodes()){r+="~~";for(var o=e.childNodes,a=o.length,i=0;i<a;++i)r+=t.subParser("makeMarkdown.node")(o[i],n);r+="~~"}return r})),t.subParser("makeMarkdown.strong",(function(e,n){var r="";if(e.hasChildNodes()){r+="**";for(var o=e.childNodes,a=o.length,i=0;i<a;++i)r+=t.subParser("makeMarkdown.node")(o[i],n);r+="**"}return r})),t.subParser("makeMarkdown.table",(function(e,n){for(var r="",o=[[],[]],a=e.querySelectorAll("thead>tr>th"),i=e.querySelectorAll("tbody>tr"),s=0;s<a.length;++s){var l=t.subParser("makeMarkdown.tableCell")(a[s],n),c="---";if(a[s].hasAttribute("style"))switch(a[s].getAttribute("style").toLowerCase().replace(/\s/g,"")){case"text-align:left;":c=":---";break;case"text-align:right;":c="---:";break;case"text-align:center;":c=":---:"}o[0][s]=l.trim(),o[1][s]=c}for(s=0;s<i.length;++s)for(var d=o.push([])-1,u=i[s].getElementsByTagName("td"),p=0;p<a.length;++p){var m=" ";void 0!==u[p]&&(m=t.subParser("makeMarkdown.tableCell")(u[p],n)),o[d].push(m)}var f=3;for(s=0;s<o.length;++s)for(p=0;p<o[s].length;++p){var h=o[s][p].length;f<h&&(f=h)}for(s=0;s<o.length;++s){for(p=0;p<o[s].length;++p)1===s?":"===o[s][p].slice(-1)?o[s][p]=t.helper.padEnd(o[s][p].slice(-1),f-1,"-")+":":o[s][p]=t.helper.padEnd(o[s][p],f,"-"):o[s][p]=t.helper.padEnd(o[s][p],f);r+="| "+o[s].join(" | ")+" |\n"}return r.trim()})),t.subParser("makeMarkdown.tableCell",(function(e,n){var r="";if(!e.hasChildNodes())return"";for(var o=e.childNodes,a=o.length,i=0;i<a;++i)r+=t.subParser("makeMarkdown.node")(o[i],n,!0);return r.trim()})),t.subParser("makeMarkdown.txt",(function(e){return e=e.nodeValue,e=(e=e.replace(/ +/g," ")).replace(/¨NBSP;/g," "),(e=(e=(e=(e=(e=(e=(e=(e=t.helper.unescapeHTMLEntities(e)).replace(/([*_~|`])/g,"\\$1")).replace(/^(\s*)>/g,"\\$1>")).replace(/^#/gm,"\\#")).replace(/^(\s*)([-=]{3,})(\s*)$/,"$1\\$2$3")).replace(/^( {0,3}\d+)\./gm,"$1\\.")).replace(/^( {0,3})([+-])/gm,"$1\\$2")).replace(/]([\s]*)\(/g,"\\]$1\\(")).replace(/^ {0,3}\[([\S \t]*?)]:/gm,"\\[$1]:")})),e.exports?e.exports=t:this.showdown=t}).call(F)}($);var R=$.exports,I=z(R);const N=(0,r.aZ)({name:"VueShowdown",props:{markdown:{type:String,required:!1,default:null},tag:{type:String,required:!1,default:"div"},flavor:{type:String,required:!1,default:null},options:{type:Object,required:!1,default:()=>({})},extensions:{type:Array,required:!1,default:null},vueTemplate:{type:Boolean,required:!1,default:!1},vueTemplateComponents:{type:Object,required:!1,default:()=>({})},vueTemplateData:{type:Object,required:!1,default:()=>({})}},setup(e,{slots:n}){const t=(0,o.Fl)((()=>{const n=new I.Converter({extensions:e.extensions??void 0});return null!==e.flavor&&n.setFlavor(e.flavor),Object.entries(e.options).forEach((([e,t])=>{n.setOption(e,t)})),n})),a=(0,o.Fl)((()=>{if(null!==e.markdown)return e.markdown;var t=n.default?.()[0];return t?.type===r.xv?t.children:""})),i=(0,o.Fl)((()=>t.value.makeHtml(a.value)));return()=>e.vueTemplate?(0,r.h)({components:e.vueTemplateComponents,setup:()=>e.vueTemplateData,template:`<${e.tag}>${i.value}</${e.tag}>`}):(0,r.h)(e.tag,{innerHTML:i.value})}}),q={class:"q-pa-sm"},H={name:"SSMarkDownRenderer"};var B=Object.assign(H,{props:{markdown:{type:String,required:!0},loading:{type:Boolean,default:!1}},emits:["clickExternalLink"],setup(e,{emit:n}){const t=n,a=e=>{e.preventDefault();let n=e.target.closest("a");if(n&&"a"===n.tagName.toLowerCase()){let r=n.href;r.startsWith(window.location.origin)?r.includes("#")?(e=r.split("#").pop(),document.getElementById(e)?.scrollIntoView()):alert("Relative links are not supported."):t("clickExternalLink",r)}};return(n,t)=>((0,r.wg)(),(0,r.iD)("div",q,[(0,r._)("div",{onClick:a,class:"markdown-wrapper"},[(0,r.Wm)((0,o.SU)(N),{markdown:e.markdown,class:"markdown-body",flavor:"github"},null,8,["markdown"])]),(0,r.Wm)((0,o.SU)(u),{showing:e.loading},{default:(0,r.w5)((()=>[(0,r.Wm)((0,o.SU)(i.Z),{color:"primary",size:"50px"})])),_:1},8,["showing"])]))}});function D(e,n){var t,r=(n=void 0===n?{}:n).insertAt;"undefined"!=typeof document&&(t=document.head||document.getElementsByTagName("head")[0],(n=document.createElement("style")).type="text/css","top"===r&&t.firstChild?t.insertBefore(n,t.firstChild):t.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e)))}var U='.markdown-wrapper {\n  /*light*/\n}\n.markdown-wrapper .markdown-body {\n  -ms-text-size-adjust: 100%;\n  -webkit-text-size-adjust: 100%;\n  margin: 0;\n  color: #1f2328;\n  background-color: #ffffff;\n  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";\n  font-size: 16px;\n  line-height: 1.5;\n  word-wrap: break-word;\n  scroll-behavior: auto;\n}\n.markdown-wrapper .markdown-body .octicon {\n  display: inline-block;\n  fill: currentColor;\n  vertical-align: text-bottom;\n}\n.markdown-wrapper .markdown-body h1:hover .anchor .octicon-link:before,\n.markdown-wrapper .markdown-body h2:hover .anchor .octicon-link:before,\n.markdown-wrapper .markdown-body h3:hover .anchor .octicon-link:before,\n.markdown-wrapper .markdown-body h4:hover .anchor .octicon-link:before,\n.markdown-wrapper .markdown-body h5:hover .anchor .octicon-link:before,\n.markdown-wrapper .markdown-body h6:hover .anchor .octicon-link:before {\n  width: 16px;\n  height: 16px;\n  content: " ";\n  display: inline-block;\n  background-color: currentColor;\n  -webkit-mask-image: url("data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' version=\'1.1\' aria-hidden=\'true\'><path fill-rule=\'evenodd\' d=\'M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z\'></path></svg>");\n  mask-image: url("data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' version=\'1.1\' aria-hidden=\'true\'><path fill-rule=\'evenodd\' d=\'M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z\'></path></svg>");\n}\n.markdown-wrapper .markdown-body details,\n.markdown-wrapper .markdown-body figcaption,\n.markdown-wrapper .markdown-body figure {\n  display: block;\n}\n.markdown-wrapper .markdown-body summary {\n  display: list-item;\n}\n.markdown-wrapper .markdown-body [hidden] {\n  display: none !important;\n}\n.markdown-wrapper .markdown-body a {\n  background-color: transparent;\n  color: #0969da;\n  text-decoration: none;\n}\n.markdown-wrapper .markdown-body abbr[title] {\n  border-bottom: none;\n  -webkit-text-decoration: underline dotted;\n  text-decoration: underline dotted;\n}\n.markdown-wrapper .markdown-body b,\n.markdown-wrapper .markdown-body strong {\n  font-weight: 600;\n}\n.markdown-wrapper .markdown-body dfn {\n  font-style: italic;\n}\n.markdown-wrapper .markdown-body h1 {\n  margin: 0.67em 0;\n  font-weight: 600;\n  padding-bottom: 0.3em;\n  font-size: 2em;\n  border-bottom: 1px solid #d0d7deb3;\n}\n.markdown-wrapper .markdown-body mark {\n  background-color: #fff8c5;\n  color: #1f2328;\n}\n.markdown-wrapper .markdown-body small {\n  font-size: 90%;\n}\n.markdown-wrapper .markdown-body sub,\n.markdown-wrapper .markdown-body sup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n.markdown-wrapper .markdown-body sub {\n  bottom: -0.25em;\n}\n.markdown-wrapper .markdown-body sup {\n  top: -0.5em;\n}\n.markdown-wrapper .markdown-body img {\n  border-style: none;\n  max-width: 100%;\n  box-sizing: content-box;\n  background-color: #ffffff;\n}\n.markdown-wrapper .markdown-body code,\n.markdown-wrapper .markdown-body kbd,\n.markdown-wrapper .markdown-body pre,\n.markdown-wrapper .markdown-body samp {\n  font-family: monospace;\n  font-size: 1em;\n}\n.markdown-wrapper .markdown-body figure {\n  margin: 1em 40px;\n}\n.markdown-wrapper .markdown-body hr {\n  box-sizing: content-box;\n  overflow: hidden;\n  background: transparent;\n  border-bottom: 1px solid #d0d7deb3;\n  height: 0.25em;\n  padding: 0;\n  margin: 24px 0;\n  background-color: #d0d7de;\n  border: 0;\n}\n.markdown-wrapper .markdown-body input {\n  font: inherit;\n  margin: 0;\n  overflow: visible;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n.markdown-wrapper .markdown-body [type=button],\n.markdown-wrapper .markdown-body [type=reset],\n.markdown-wrapper .markdown-body [type=submit] {\n  -webkit-appearance: button;\n  appearance: button;\n}\n.markdown-wrapper .markdown-body [type=checkbox],\n.markdown-wrapper .markdown-body [type=radio] {\n  box-sizing: border-box;\n  padding: 0;\n}\n.markdown-wrapper .markdown-body [type=number]::-webkit-inner-spin-button,\n.markdown-wrapper .markdown-body [type=number]::-webkit-outer-spin-button {\n  height: auto;\n}\n.markdown-wrapper .markdown-body [type=search]::-webkit-search-cancel-button,\n.markdown-wrapper .markdown-body [type=search]::-webkit-search-decoration {\n  -webkit-appearance: none;\n  appearance: none;\n}\n.markdown-wrapper .markdown-body ::-webkit-input-placeholder {\n  color: inherit;\n  opacity: 0.54;\n}\n.markdown-wrapper .markdown-body ::-webkit-file-upload-button {\n  -webkit-appearance: button;\n  appearance: button;\n  font: inherit;\n}\n.markdown-wrapper .markdown-body a:hover {\n  text-decoration: underline;\n}\n.markdown-wrapper .markdown-body ::placeholder {\n  color: #636c76;\n  opacity: 1;\n}\n.markdown-wrapper .markdown-body hr::before {\n  display: table;\n  content: "";\n}\n.markdown-wrapper .markdown-body hr::after {\n  display: table;\n  clear: both;\n  content: "";\n}\n.markdown-wrapper .markdown-body table {\n  border-spacing: 0;\n  border-collapse: collapse;\n  display: block;\n  width: max-content;\n  max-width: 100%;\n  overflow: auto;\n}\n.markdown-wrapper .markdown-body td,\n.markdown-wrapper .markdown-body th {\n  padding: 0;\n}\n.markdown-wrapper .markdown-body details summary {\n  cursor: pointer;\n}\n.markdown-wrapper .markdown-body details:not([open]) > *:not(summary) {\n  display: none;\n}\n.markdown-wrapper .markdown-body a:focus,\n.markdown-wrapper .markdown-body [role=button]:focus,\n.markdown-wrapper .markdown-body input[type=radio]:focus,\n.markdown-wrapper .markdown-body input[type=checkbox]:focus {\n  outline: 2px solid #0969da;\n  outline-offset: -2px;\n  box-shadow: none;\n}\n.markdown-wrapper .markdown-body a:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body [role=button]:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body input[type=radio]:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body input[type=checkbox]:focus:not(:focus-visible) {\n  outline: solid 1px transparent;\n}\n.markdown-wrapper .markdown-body a:focus-visible,\n.markdown-wrapper .markdown-body [role=button]:focus-visible,\n.markdown-wrapper .markdown-body input[type=radio]:focus-visible,\n.markdown-wrapper .markdown-body input[type=checkbox]:focus-visible {\n  outline: 2px solid #0969da;\n  outline-offset: -2px;\n  box-shadow: none;\n}\n.markdown-wrapper .markdown-body a:not([class]):focus,\n.markdown-wrapper .markdown-body a:not([class]):focus-visible,\n.markdown-wrapper .markdown-body input[type=radio]:focus,\n.markdown-wrapper .markdown-body input[type=radio]:focus-visible,\n.markdown-wrapper .markdown-body input[type=checkbox]:focus,\n.markdown-wrapper .markdown-body input[type=checkbox]:focus-visible {\n  outline-offset: 0;\n}\n.markdown-wrapper .markdown-body kbd {\n  display: inline-block;\n  padding: 3px 5px;\n  font: 11px ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  line-height: 10px;\n  color: #1f2328;\n  vertical-align: middle;\n  background-color: #f6f8fa;\n  border: solid 1px #afb8c133;\n  border-bottom-color: #afb8c133;\n  border-radius: 6px;\n  box-shadow: inset 0 -1px 0 #afb8c133;\n}\n.markdown-wrapper .markdown-body h1,\n.markdown-wrapper .markdown-body h2,\n.markdown-wrapper .markdown-body h3,\n.markdown-wrapper .markdown-body h4,\n.markdown-wrapper .markdown-body h5,\n.markdown-wrapper .markdown-body h6 {\n  margin-top: 24px;\n  margin-bottom: 16px;\n  font-weight: 600;\n  line-height: 1.25;\n}\n.markdown-wrapper .markdown-body h2 {\n  font-weight: 600;\n  padding-bottom: 0.3em;\n  font-size: 1.5em;\n  border-bottom: 1px solid #d0d7deb3;\n}\n.markdown-wrapper .markdown-body h3 {\n  font-weight: 600;\n  font-size: 1.25em;\n}\n.markdown-wrapper .markdown-body h4 {\n  font-weight: 600;\n  font-size: 1em;\n}\n.markdown-wrapper .markdown-body h5 {\n  font-weight: 600;\n  font-size: 0.875em;\n}\n.markdown-wrapper .markdown-body h6 {\n  font-weight: 600;\n  font-size: 0.85em;\n  color: #636c76;\n}\n.markdown-wrapper .markdown-body p {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n.markdown-wrapper .markdown-body blockquote {\n  margin: 0;\n  padding: 0 1em;\n  color: #636c76;\n  border-left: 0.25em solid #d0d7de;\n}\n.markdown-wrapper .markdown-body ul,\n.markdown-wrapper .markdown-body ol {\n  margin-top: 0;\n  margin-bottom: 0;\n  padding-left: 2em;\n}\n.markdown-wrapper .markdown-body ol ol,\n.markdown-wrapper .markdown-body ul ol {\n  list-style-type: lower-roman;\n}\n.markdown-wrapper .markdown-body ul ul ol,\n.markdown-wrapper .markdown-body ul ol ol,\n.markdown-wrapper .markdown-body ol ul ol,\n.markdown-wrapper .markdown-body ol ol ol {\n  list-style-type: lower-alpha;\n}\n.markdown-wrapper .markdown-body dd {\n  margin-left: 0;\n}\n.markdown-wrapper .markdown-body tt,\n.markdown-wrapper .markdown-body code,\n.markdown-wrapper .markdown-body samp {\n  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  font-size: 12px;\n}\n.markdown-wrapper .markdown-body pre {\n  margin-top: 0;\n  margin-bottom: 0;\n  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  font-size: 12px;\n  word-wrap: normal;\n}\n.markdown-wrapper .markdown-body .octicon {\n  display: inline-block;\n  overflow: visible !important;\n  vertical-align: text-bottom;\n  fill: currentColor;\n}\n.markdown-wrapper .markdown-body input::-webkit-outer-spin-button,\n.markdown-wrapper .markdown-body input::-webkit-inner-spin-button {\n  margin: 0;\n  -webkit-appearance: none;\n  appearance: none;\n}\n.markdown-wrapper .markdown-body .mr-2 {\n  margin-right: 0.5rem !important;\n}\n.markdown-wrapper .markdown-body::before {\n  display: table;\n  content: "";\n}\n.markdown-wrapper .markdown-body::after {\n  display: table;\n  clear: both;\n  content: "";\n}\n.markdown-wrapper .markdown-body > *:first-child {\n  margin-top: 0 !important;\n}\n.markdown-wrapper .markdown-body > *:last-child {\n  margin-bottom: 0 !important;\n}\n.markdown-wrapper .markdown-body a:not([href]) {\n  color: inherit;\n  text-decoration: none;\n}\n.markdown-wrapper .markdown-body .absent {\n  color: #d1242f;\n}\n.markdown-wrapper .markdown-body .anchor {\n  float: left;\n  padding-right: 4px;\n  margin-left: -20px;\n  line-height: 1;\n}\n.markdown-wrapper .markdown-body .anchor:focus {\n  outline: none;\n}\n.markdown-wrapper .markdown-body p,\n.markdown-wrapper .markdown-body blockquote,\n.markdown-wrapper .markdown-body ul,\n.markdown-wrapper .markdown-body ol,\n.markdown-wrapper .markdown-body dl,\n.markdown-wrapper .markdown-body table,\n.markdown-wrapper .markdown-body pre,\n.markdown-wrapper .markdown-body details {\n  margin-top: 0;\n  margin-bottom: 16px;\n}\n.markdown-wrapper .markdown-body blockquote > :first-child {\n  margin-top: 0;\n}\n.markdown-wrapper .markdown-body blockquote > :last-child {\n  margin-bottom: 0;\n}\n.markdown-wrapper .markdown-body h1 .octicon-link,\n.markdown-wrapper .markdown-body h2 .octicon-link,\n.markdown-wrapper .markdown-body h3 .octicon-link,\n.markdown-wrapper .markdown-body h4 .octicon-link,\n.markdown-wrapper .markdown-body h5 .octicon-link,\n.markdown-wrapper .markdown-body h6 .octicon-link {\n  color: #1f2328;\n  vertical-align: middle;\n  visibility: hidden;\n}\n.markdown-wrapper .markdown-body h1:hover .anchor,\n.markdown-wrapper .markdown-body h2:hover .anchor,\n.markdown-wrapper .markdown-body h3:hover .anchor,\n.markdown-wrapper .markdown-body h4:hover .anchor,\n.markdown-wrapper .markdown-body h5:hover .anchor,\n.markdown-wrapper .markdown-body h6:hover .anchor {\n  text-decoration: none;\n}\n.markdown-wrapper .markdown-body h1:hover .anchor .octicon-link,\n.markdown-wrapper .markdown-body h2:hover .anchor .octicon-link,\n.markdown-wrapper .markdown-body h3:hover .anchor .octicon-link,\n.markdown-wrapper .markdown-body h4:hover .anchor .octicon-link,\n.markdown-wrapper .markdown-body h5:hover .anchor .octicon-link,\n.markdown-wrapper .markdown-body h6:hover .anchor .octicon-link {\n  visibility: visible;\n}\n.markdown-wrapper .markdown-body h1 tt,\n.markdown-wrapper .markdown-body h1 code,\n.markdown-wrapper .markdown-body h2 tt,\n.markdown-wrapper .markdown-body h2 code,\n.markdown-wrapper .markdown-body h3 tt,\n.markdown-wrapper .markdown-body h3 code,\n.markdown-wrapper .markdown-body h4 tt,\n.markdown-wrapper .markdown-body h4 code,\n.markdown-wrapper .markdown-body h5 tt,\n.markdown-wrapper .markdown-body h5 code,\n.markdown-wrapper .markdown-body h6 tt,\n.markdown-wrapper .markdown-body h6 code {\n  padding: 0 0.2em;\n  font-size: inherit;\n}\n.markdown-wrapper .markdown-body summary h1,\n.markdown-wrapper .markdown-body summary h2,\n.markdown-wrapper .markdown-body summary h3,\n.markdown-wrapper .markdown-body summary h4,\n.markdown-wrapper .markdown-body summary h5,\n.markdown-wrapper .markdown-body summary h6 {\n  display: inline-block;\n}\n.markdown-wrapper .markdown-body summary h1 .anchor,\n.markdown-wrapper .markdown-body summary h2 .anchor,\n.markdown-wrapper .markdown-body summary h3 .anchor,\n.markdown-wrapper .markdown-body summary h4 .anchor,\n.markdown-wrapper .markdown-body summary h5 .anchor,\n.markdown-wrapper .markdown-body summary h6 .anchor {\n  margin-left: -40px;\n}\n.markdown-wrapper .markdown-body summary h1,\n.markdown-wrapper .markdown-body summary h2 {\n  padding-bottom: 0;\n  border-bottom: 0;\n}\n.markdown-wrapper .markdown-body ul.no-list,\n.markdown-wrapper .markdown-body ol.no-list {\n  padding: 0;\n  list-style-type: none;\n}\n.markdown-wrapper .markdown-body ol[type="a s"] {\n  list-style-type: lower-alpha;\n}\n.markdown-wrapper .markdown-body ol[type="A s"] {\n  list-style-type: upper-alpha;\n}\n.markdown-wrapper .markdown-body ol[type="i s"] {\n  list-style-type: lower-roman;\n}\n.markdown-wrapper .markdown-body ol[type="I s"] {\n  list-style-type: upper-roman;\n}\n.markdown-wrapper .markdown-body ol[type="1"] {\n  list-style-type: decimal;\n}\n.markdown-wrapper .markdown-body div > ol:not([type]) {\n  list-style-type: decimal;\n}\n.markdown-wrapper .markdown-body ul ul,\n.markdown-wrapper .markdown-body ul ol,\n.markdown-wrapper .markdown-body ol ol,\n.markdown-wrapper .markdown-body ol ul {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.markdown-wrapper .markdown-body li > p {\n  margin-top: 16px;\n}\n.markdown-wrapper .markdown-body li + li {\n  margin-top: 0.25em;\n}\n.markdown-wrapper .markdown-body dl {\n  padding: 0;\n}\n.markdown-wrapper .markdown-body dl dt {\n  padding: 0;\n  margin-top: 16px;\n  font-size: 1em;\n  font-style: italic;\n  font-weight: 600;\n}\n.markdown-wrapper .markdown-body dl dd {\n  padding: 0 16px;\n  margin-bottom: 16px;\n}\n.markdown-wrapper .markdown-body table th {\n  font-weight: 600;\n}\n.markdown-wrapper .markdown-body table th,\n.markdown-wrapper .markdown-body table td {\n  padding: 6px 13px;\n  border: 1px solid #d0d7de;\n}\n.markdown-wrapper .markdown-body table td > :last-child {\n  margin-bottom: 0;\n}\n.markdown-wrapper .markdown-body table tr {\n  background-color: #ffffff;\n  border-top: 1px solid #d0d7deb3;\n}\n.markdown-wrapper .markdown-body table tr:nth-child(2n) {\n  background-color: #f6f8fa;\n}\n.markdown-wrapper .markdown-body table img {\n  background-color: transparent;\n}\n.markdown-wrapper .markdown-body img[align=right] {\n  padding-left: 20px;\n}\n.markdown-wrapper .markdown-body img[align=left] {\n  padding-right: 20px;\n}\n.markdown-wrapper .markdown-body .emoji {\n  max-width: none;\n  vertical-align: text-top;\n  background-color: transparent;\n}\n.markdown-wrapper .markdown-body span.frame {\n  display: block;\n  overflow: hidden;\n}\n.markdown-wrapper .markdown-body span.frame > span {\n  display: block;\n  float: left;\n  width: auto;\n  padding: 7px;\n  margin: 13px 0 0;\n  overflow: hidden;\n  border: 1px solid #d0d7de;\n}\n.markdown-wrapper .markdown-body span.frame span img {\n  display: block;\n  float: left;\n}\n.markdown-wrapper .markdown-body span.frame span span {\n  display: block;\n  padding: 5px 0 0;\n  clear: both;\n  color: #1f2328;\n}\n.markdown-wrapper .markdown-body span.align-center {\n  display: block;\n  overflow: hidden;\n  clear: both;\n}\n.markdown-wrapper .markdown-body span.align-center > span {\n  display: block;\n  margin: 13px auto 0;\n  overflow: hidden;\n  text-align: center;\n}\n.markdown-wrapper .markdown-body span.align-center span img {\n  margin: 0 auto;\n  text-align: center;\n}\n.markdown-wrapper .markdown-body span.align-right {\n  display: block;\n  overflow: hidden;\n  clear: both;\n}\n.markdown-wrapper .markdown-body span.align-right > span {\n  display: block;\n  margin: 13px 0 0;\n  overflow: hidden;\n  text-align: right;\n}\n.markdown-wrapper .markdown-body span.align-right span img {\n  margin: 0;\n  text-align: right;\n}\n.markdown-wrapper .markdown-body span.float-left {\n  display: block;\n  float: left;\n  margin-right: 13px;\n  overflow: hidden;\n}\n.markdown-wrapper .markdown-body span.float-left span {\n  margin: 13px 0 0;\n}\n.markdown-wrapper .markdown-body span.float-right {\n  display: block;\n  float: right;\n  margin-left: 13px;\n  overflow: hidden;\n}\n.markdown-wrapper .markdown-body span.float-right > span {\n  display: block;\n  margin: 13px auto 0;\n  overflow: hidden;\n  text-align: right;\n}\n.markdown-wrapper .markdown-body code,\n.markdown-wrapper .markdown-body tt {\n  padding: 0.2em 0.4em;\n  margin: 0;\n  font-size: 85%;\n  white-space: break-spaces;\n  background-color: #afb8c133;\n  border-radius: 6px;\n}\n.markdown-wrapper .markdown-body code br,\n.markdown-wrapper .markdown-body tt br {\n  display: none;\n}\n.markdown-wrapper .markdown-body del code {\n  text-decoration: inherit;\n}\n.markdown-wrapper .markdown-body samp {\n  font-size: 85%;\n}\n.markdown-wrapper .markdown-body pre code {\n  font-size: 100%;\n}\n.markdown-wrapper .markdown-body pre > code {\n  padding: 0;\n  margin: 0;\n  word-break: normal;\n  white-space: pre;\n  background: transparent;\n  border: 0;\n}\n.markdown-wrapper .markdown-body .highlight {\n  margin-bottom: 16px;\n}\n.markdown-wrapper .markdown-body .highlight pre {\n  margin-bottom: 0;\n  word-break: normal;\n}\n.markdown-wrapper .markdown-body .highlight pre,\n.markdown-wrapper .markdown-body pre {\n  padding: 16px;\n  overflow: auto;\n  font-size: 85%;\n  line-height: 1.45;\n  color: #1f2328;\n  background-color: #f6f8fa;\n  border-radius: 6px;\n}\n.markdown-wrapper .markdown-body pre code,\n.markdown-wrapper .markdown-body pre tt {\n  display: inline;\n  max-width: auto;\n  padding: 0;\n  margin: 0;\n  overflow: visible;\n  line-height: inherit;\n  word-wrap: normal;\n  background-color: transparent;\n  border: 0;\n}\n.markdown-wrapper .markdown-body .csv-data td,\n.markdown-wrapper .markdown-body .csv-data th {\n  padding: 5px;\n  overflow: hidden;\n  font-size: 12px;\n  line-height: 1;\n  text-align: left;\n  white-space: nowrap;\n}\n.markdown-wrapper .markdown-body .csv-data .blob-num {\n  padding: 10px 8px 9px;\n  text-align: right;\n  background: #ffffff;\n  border: 0;\n}\n.markdown-wrapper .markdown-body .csv-data tr {\n  border-top: 0;\n}\n.markdown-wrapper .markdown-body .csv-data th {\n  font-weight: 600;\n  background: #f6f8fa;\n  border-top: 0;\n}\n.markdown-wrapper .markdown-body [data-footnote-ref]::before {\n  content: "[";\n}\n.markdown-wrapper .markdown-body [data-footnote-ref]::after {\n  content: "]";\n}\n.markdown-wrapper .markdown-body .footnotes {\n  font-size: 12px;\n  color: #636c76;\n  border-top: 1px solid #d0d7de;\n}\n.markdown-wrapper .markdown-body .footnotes ol {\n  padding-left: 16px;\n}\n.markdown-wrapper .markdown-body .footnotes ol ul {\n  display: inline-block;\n  padding-left: 16px;\n  margin-top: 16px;\n}\n.markdown-wrapper .markdown-body .footnotes li {\n  position: relative;\n}\n.markdown-wrapper .markdown-body .footnotes li:target::before {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  bottom: -8px;\n  left: -24px;\n  pointer-events: none;\n  content: "";\n  border: 2px solid #0969da;\n  border-radius: 6px;\n}\n.markdown-wrapper .markdown-body .footnotes li:target {\n  color: #1f2328;\n}\n.markdown-wrapper .markdown-body .footnotes .data-footnote-backref g-emoji {\n  font-family: monospace;\n}\n.markdown-wrapper .markdown-body .pl-c {\n  color: #57606a;\n}\n.markdown-wrapper .markdown-body .pl-c1,\n.markdown-wrapper .markdown-body .pl-s .pl-v {\n  color: #0550ae;\n}\n.markdown-wrapper .markdown-body .pl-e,\n.markdown-wrapper .markdown-body .pl-en {\n  color: #6639ba;\n}\n.markdown-wrapper .markdown-body .pl-smi,\n.markdown-wrapper .markdown-body .pl-s .pl-s1 {\n  color: #24292f;\n}\n.markdown-wrapper .markdown-body .pl-ent {\n  color: #0550ae;\n}\n.markdown-wrapper .markdown-body .pl-k {\n  color: #cf222e;\n}\n.markdown-wrapper .markdown-body .pl-s,\n.markdown-wrapper .markdown-body .pl-pds,\n.markdown-wrapper .markdown-body .pl-s .pl-pse .pl-s1,\n.markdown-wrapper .markdown-body .pl-sr,\n.markdown-wrapper .markdown-body .pl-sr .pl-cce,\n.markdown-wrapper .markdown-body .pl-sr .pl-sre,\n.markdown-wrapper .markdown-body .pl-sr .pl-sra {\n  color: #0a3069;\n}\n.markdown-wrapper .markdown-body .pl-v,\n.markdown-wrapper .markdown-body .pl-smw {\n  color: #953800;\n}\n.markdown-wrapper .markdown-body .pl-bu {\n  color: #82071e;\n}\n.markdown-wrapper .markdown-body .pl-ii {\n  color: #f6f8fa;\n  background-color: #82071e;\n}\n.markdown-wrapper .markdown-body .pl-c2 {\n  color: #f6f8fa;\n  background-color: #cf222e;\n}\n.markdown-wrapper .markdown-body .pl-sr .pl-cce {\n  font-weight: bold;\n  color: #116329;\n}\n.markdown-wrapper .markdown-body .pl-ml {\n  color: #3b2300;\n}\n.markdown-wrapper .markdown-body .pl-mh,\n.markdown-wrapper .markdown-body .pl-mh .pl-en,\n.markdown-wrapper .markdown-body .pl-ms {\n  font-weight: bold;\n  color: #0550ae;\n}\n.markdown-wrapper .markdown-body .pl-mi {\n  font-style: italic;\n  color: #24292f;\n}\n.markdown-wrapper .markdown-body .pl-mb {\n  font-weight: bold;\n  color: #24292f;\n}\n.markdown-wrapper .markdown-body .pl-md {\n  color: #82071e;\n  background-color: #ffebe9;\n}\n.markdown-wrapper .markdown-body .pl-mi1 {\n  color: #116329;\n  background-color: #dafbe1;\n}\n.markdown-wrapper .markdown-body .pl-mc {\n  color: #953800;\n  background-color: #ffd8b5;\n}\n.markdown-wrapper .markdown-body .pl-mi2 {\n  color: #eaeef2;\n  background-color: #0550ae;\n}\n.markdown-wrapper .markdown-body .pl-mdr {\n  font-weight: bold;\n  color: #8250df;\n}\n.markdown-wrapper .markdown-body .pl-ba {\n  color: #57606a;\n}\n.markdown-wrapper .markdown-body .pl-sg {\n  color: #8c959f;\n}\n.markdown-wrapper .markdown-body .pl-corl {\n  text-decoration: underline;\n  color: #0a3069;\n}\n.markdown-wrapper .markdown-body [role=button]:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body [role=tabpanel][tabindex="0"]:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body button:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body summary:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body a:focus:not(:focus-visible) {\n  outline: none;\n  box-shadow: none;\n}\n.markdown-wrapper .markdown-body [tabindex="0"]:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body details-dialog:focus:not(:focus-visible) {\n  outline: none;\n}\n.markdown-wrapper .markdown-body g-emoji {\n  display: inline-block;\n  min-width: 1ch;\n  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";\n  font-size: 1em;\n  font-style: normal !important;\n  font-weight: 400;\n  line-height: 1;\n  vertical-align: -0.075em;\n}\n.markdown-wrapper .markdown-body g-emoji img {\n  width: 1em;\n  height: 1em;\n}\n.markdown-wrapper .markdown-body .task-list-item {\n  list-style-type: none;\n}\n.markdown-wrapper .markdown-body .task-list-item label {\n  font-weight: 400;\n}\n.markdown-wrapper .markdown-body .task-list-item.enabled label {\n  cursor: pointer;\n}\n.markdown-wrapper .markdown-body .task-list-item + .task-list-item {\n  margin-top: 0.25rem;\n}\n.markdown-wrapper .markdown-body .task-list-item .handle {\n  display: none;\n}\n.markdown-wrapper .markdown-body .task-list-item-checkbox {\n  margin: 0 0.2em 0.25em -1.4em;\n  vertical-align: middle;\n}\n.markdown-wrapper .markdown-body .contains-task-list:dir(rtl) .task-list-item-checkbox {\n  margin: 0 -1.6em 0.25em 0.2em;\n}\n.markdown-wrapper .markdown-body .contains-task-list {\n  position: relative;\n}\n.markdown-wrapper .markdown-body .contains-task-list:hover .task-list-item-convert-container,\n.markdown-wrapper .markdown-body .contains-task-list:focus-within .task-list-item-convert-container {\n  display: block;\n  width: auto;\n  height: 24px;\n  overflow: visible;\n  clip: auto;\n}\n.markdown-wrapper .markdown-body ::-webkit-calendar-picker-indicator {\n  filter: invert(50%);\n}\n.markdown-wrapper .markdown-body .markdown-alert {\n  padding: 0.5rem 1rem;\n  margin-bottom: 1rem;\n  color: inherit;\n  border-left: 0.25em solid #d0d7de;\n}\n.markdown-wrapper .markdown-body .markdown-alert > :first-child {\n  margin-top: 0;\n}\n.markdown-wrapper .markdown-body .markdown-alert > :last-child {\n  margin-bottom: 0;\n}\n.markdown-wrapper .markdown-body .markdown-alert .markdown-alert-title {\n  display: flex;\n  font-weight: 500;\n  align-items: center;\n  line-height: 1;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-note {\n  border-left-color: #0969da;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-note .markdown-alert-title {\n  color: #0969da;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-important {\n  border-left-color: #8250df;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-important .markdown-alert-title {\n  color: #8250df;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-warning {\n  border-left-color: #bf8700;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-warning .markdown-alert-title {\n  color: #9a6700;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-tip {\n  border-left-color: #1a7f37;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-tip .markdown-alert-title {\n  color: #1a7f37;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-caution {\n  border-left-color: #cf222e;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-caution .markdown-alert-title {\n  color: #d1242f;\n}\n.markdown-wrapper .markdown-body > *:first-child > .heading-element:first-child {\n  margin-top: 0 !important;\n}\n.markdown-wrapper .markdown-body {\n  background-color: inherit !important;\n}\n.body--dark .markdown-wrapper {\n  /*dark*/\n}\n.body--dark .markdown-wrapper .markdown-body {\n  color-scheme: dark;\n  -ms-text-size-adjust: 100%;\n  -webkit-text-size-adjust: 100%;\n  margin: 0;\n  color: #e6edf3;\n  background-color: #0d1117;\n  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";\n  font-size: 16px;\n  line-height: 1.5;\n  word-wrap: break-word;\n  scroll-behavior: auto;\n}\n.body--dark .markdown-wrapper .markdown-body .octicon {\n  display: inline-block;\n  fill: currentColor;\n  vertical-align: text-bottom;\n}\n.body--dark .markdown-wrapper .markdown-body h1:hover .anchor .octicon-link:before,\n.body--dark .markdown-wrapper .markdown-body h2:hover .anchor .octicon-link:before,\n.body--dark .markdown-wrapper .markdown-body h3:hover .anchor .octicon-link:before,\n.body--dark .markdown-wrapper .markdown-body h4:hover .anchor .octicon-link:before,\n.body--dark .markdown-wrapper .markdown-body h5:hover .anchor .octicon-link:before,\n.body--dark .markdown-wrapper .markdown-body h6:hover .anchor .octicon-link:before {\n  width: 16px;\n  height: 16px;\n  content: " ";\n  display: inline-block;\n  background-color: currentColor;\n  -webkit-mask-image: url("data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' version=\'1.1\' aria-hidden=\'true\'><path fill-rule=\'evenodd\' d=\'M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z\'></path></svg>");\n  mask-image: url("data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' version=\'1.1\' aria-hidden=\'true\'><path fill-rule=\'evenodd\' d=\'M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z\'></path></svg>");\n}\n.body--dark .markdown-wrapper .markdown-body details,\n.body--dark .markdown-wrapper .markdown-body figcaption,\n.body--dark .markdown-wrapper .markdown-body figure {\n  display: block;\n}\n.body--dark .markdown-wrapper .markdown-body summary {\n  display: list-item;\n}\n.body--dark .markdown-wrapper .markdown-body [hidden] {\n  display: none !important;\n}\n.body--dark .markdown-wrapper .markdown-body a {\n  background-color: transparent;\n  color: #4493f8;\n  text-decoration: none;\n}\n.body--dark .markdown-wrapper .markdown-body abbr[title] {\n  border-bottom: none;\n  -webkit-text-decoration: underline dotted;\n  text-decoration: underline dotted;\n}\n.body--dark .markdown-wrapper .markdown-body b,\n.body--dark .markdown-wrapper .markdown-body strong {\n  font-weight: 600;\n}\n.body--dark .markdown-wrapper .markdown-body dfn {\n  font-style: italic;\n}\n.body--dark .markdown-wrapper .markdown-body h1 {\n  margin: 0.67em 0;\n  font-weight: 600;\n  padding-bottom: 0.3em;\n  font-size: 2em;\n  border-bottom: 1px solid #30363db3;\n}\n.body--dark .markdown-wrapper .markdown-body mark {\n  background-color: #bb800926;\n  color: #e6edf3;\n}\n.body--dark .markdown-wrapper .markdown-body small {\n  font-size: 90%;\n}\n.body--dark .markdown-wrapper .markdown-body sub,\n.body--dark .markdown-wrapper .markdown-body sup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n.body--dark .markdown-wrapper .markdown-body sub {\n  bottom: -0.25em;\n}\n.body--dark .markdown-wrapper .markdown-body sup {\n  top: -0.5em;\n}\n.body--dark .markdown-wrapper .markdown-body img {\n  border-style: none;\n  max-width: 100%;\n  box-sizing: content-box;\n  background-color: #0d1117;\n}\n.body--dark .markdown-wrapper .markdown-body code,\n.body--dark .markdown-wrapper .markdown-body kbd,\n.body--dark .markdown-wrapper .markdown-body pre,\n.body--dark .markdown-wrapper .markdown-body samp {\n  font-family: monospace;\n  font-size: 1em;\n}\n.body--dark .markdown-wrapper .markdown-body figure {\n  margin: 1em 40px;\n}\n.body--dark .markdown-wrapper .markdown-body hr {\n  box-sizing: content-box;\n  overflow: hidden;\n  background: transparent;\n  border-bottom: 1px solid #30363db3;\n  height: 0.25em;\n  padding: 0;\n  margin: 24px 0;\n  background-color: #30363d;\n  border: 0;\n}\n.body--dark .markdown-wrapper .markdown-body input {\n  font: inherit;\n  margin: 0;\n  overflow: visible;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n.body--dark .markdown-wrapper .markdown-body [type=button],\n.body--dark .markdown-wrapper .markdown-body [type=reset],\n.body--dark .markdown-wrapper .markdown-body [type=submit] {\n  -webkit-appearance: button;\n  appearance: button;\n}\n.body--dark .markdown-wrapper .markdown-body [type=checkbox],\n.body--dark .markdown-wrapper .markdown-body [type=radio] {\n  box-sizing: border-box;\n  padding: 0;\n}\n.body--dark .markdown-wrapper .markdown-body [type=number]::-webkit-inner-spin-button,\n.body--dark .markdown-wrapper .markdown-body [type=number]::-webkit-outer-spin-button {\n  height: auto;\n}\n.body--dark .markdown-wrapper .markdown-body [type=search]::-webkit-search-cancel-button,\n.body--dark .markdown-wrapper .markdown-body [type=search]::-webkit-search-decoration {\n  -webkit-appearance: none;\n  appearance: none;\n}\n.body--dark .markdown-wrapper .markdown-body ::-webkit-input-placeholder {\n  color: inherit;\n  opacity: 0.54;\n}\n.body--dark .markdown-wrapper .markdown-body ::-webkit-file-upload-button {\n  -webkit-appearance: button;\n  appearance: button;\n  font: inherit;\n}\n.body--dark .markdown-wrapper .markdown-body a:hover {\n  text-decoration: underline;\n}\n.body--dark .markdown-wrapper .markdown-body ::placeholder {\n  color: #8d96a0;\n  opacity: 1;\n}\n.body--dark .markdown-wrapper .markdown-body hr::before {\n  display: table;\n  content: "";\n}\n.body--dark .markdown-wrapper .markdown-body hr::after {\n  display: table;\n  clear: both;\n  content: "";\n}\n.body--dark .markdown-wrapper .markdown-body table {\n  border-spacing: 0;\n  border-collapse: collapse;\n  display: block;\n  width: max-content;\n  max-width: 100%;\n  overflow: auto;\n}\n.body--dark .markdown-wrapper .markdown-body td,\n.body--dark .markdown-wrapper .markdown-body th {\n  padding: 0;\n}\n.body--dark .markdown-wrapper .markdown-body details summary {\n  cursor: pointer;\n}\n.body--dark .markdown-wrapper .markdown-body details:not([open]) > *:not(summary) {\n  display: none;\n}\n.body--dark .markdown-wrapper .markdown-body a:focus,\n.body--dark .markdown-wrapper .markdown-body [role=button]:focus,\n.body--dark .markdown-wrapper .markdown-body input[type=radio]:focus,\n.body--dark .markdown-wrapper .markdown-body input[type=checkbox]:focus {\n  outline: 2px solid #1f6feb;\n  outline-offset: -2px;\n  box-shadow: none;\n}\n.body--dark .markdown-wrapper .markdown-body a:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body [role=button]:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body input[type=radio]:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body input[type=checkbox]:focus:not(:focus-visible) {\n  outline: solid 1px transparent;\n}\n.body--dark .markdown-wrapper .markdown-body a:focus-visible,\n.body--dark .markdown-wrapper .markdown-body [role=button]:focus-visible,\n.body--dark .markdown-wrapper .markdown-body input[type=radio]:focus-visible,\n.body--dark .markdown-wrapper .markdown-body input[type=checkbox]:focus-visible {\n  outline: 2px solid #1f6feb;\n  outline-offset: -2px;\n  box-shadow: none;\n}\n.body--dark .markdown-wrapper .markdown-body a:not([class]):focus,\n.body--dark .markdown-wrapper .markdown-body a:not([class]):focus-visible,\n.body--dark .markdown-wrapper .markdown-body input[type=radio]:focus,\n.body--dark .markdown-wrapper .markdown-body input[type=radio]:focus-visible,\n.body--dark .markdown-wrapper .markdown-body input[type=checkbox]:focus,\n.body--dark .markdown-wrapper .markdown-body input[type=checkbox]:focus-visible {\n  outline-offset: 0;\n}\n.body--dark .markdown-wrapper .markdown-body kbd {\n  display: inline-block;\n  padding: 3px 5px;\n  font: 11px ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  line-height: 10px;\n  color: #e6edf3;\n  vertical-align: middle;\n  background-color: #161b22;\n  border: solid 1px #6e768166;\n  border-bottom-color: #6e768166;\n  border-radius: 6px;\n  box-shadow: inset 0 -1px 0 #6e768166;\n}\n.body--dark .markdown-wrapper .markdown-body h1,\n.body--dark .markdown-wrapper .markdown-body h2,\n.body--dark .markdown-wrapper .markdown-body h3,\n.body--dark .markdown-wrapper .markdown-body h4,\n.body--dark .markdown-wrapper .markdown-body h5,\n.body--dark .markdown-wrapper .markdown-body h6 {\n  margin-top: 24px;\n  margin-bottom: 16px;\n  font-weight: 600;\n  line-height: 1.25;\n}\n.body--dark .markdown-wrapper .markdown-body h2 {\n  font-weight: 600;\n  padding-bottom: 0.3em;\n  font-size: 1.5em;\n  border-bottom: 1px solid #30363db3;\n}\n.body--dark .markdown-wrapper .markdown-body h3 {\n  font-weight: 600;\n  font-size: 1.25em;\n}\n.body--dark .markdown-wrapper .markdown-body h4 {\n  font-weight: 600;\n  font-size: 1em;\n}\n.body--dark .markdown-wrapper .markdown-body h5 {\n  font-weight: 600;\n  font-size: 0.875em;\n}\n.body--dark .markdown-wrapper .markdown-body h6 {\n  font-weight: 600;\n  font-size: 0.85em;\n  color: #8d96a0;\n}\n.body--dark .markdown-wrapper .markdown-body p {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n.body--dark .markdown-wrapper .markdown-body blockquote {\n  margin: 0;\n  padding: 0 1em;\n  color: #8d96a0;\n  border-left: 0.25em solid #30363d;\n}\n.body--dark .markdown-wrapper .markdown-body ul,\n.body--dark .markdown-wrapper .markdown-body ol {\n  margin-top: 0;\n  margin-bottom: 0;\n  padding-left: 2em;\n}\n.body--dark .markdown-wrapper .markdown-body ol ol,\n.body--dark .markdown-wrapper .markdown-body ul ol {\n  list-style-type: lower-roman;\n}\n.body--dark .markdown-wrapper .markdown-body ul ul ol,\n.body--dark .markdown-wrapper .markdown-body ul ol ol,\n.body--dark .markdown-wrapper .markdown-body ol ul ol,\n.body--dark .markdown-wrapper .markdown-body ol ol ol {\n  list-style-type: lower-alpha;\n}\n.body--dark .markdown-wrapper .markdown-body dd {\n  margin-left: 0;\n}\n.body--dark .markdown-wrapper .markdown-body tt,\n.body--dark .markdown-wrapper .markdown-body code,\n.body--dark .markdown-wrapper .markdown-body samp {\n  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  font-size: 12px;\n}\n.body--dark .markdown-wrapper .markdown-body pre {\n  margin-top: 0;\n  margin-bottom: 0;\n  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  font-size: 12px;\n  word-wrap: normal;\n}\n.body--dark .markdown-wrapper .markdown-body .octicon {\n  display: inline-block;\n  overflow: visible !important;\n  vertical-align: text-bottom;\n  fill: currentColor;\n}\n.body--dark .markdown-wrapper .markdown-body input::-webkit-outer-spin-button,\n.body--dark .markdown-wrapper .markdown-body input::-webkit-inner-spin-button {\n  margin: 0;\n  -webkit-appearance: none;\n  appearance: none;\n}\n.body--dark .markdown-wrapper .markdown-body .mr-2 {\n  margin-right: 0.5rem !important;\n}\n.body--dark .markdown-wrapper .markdown-body::before {\n  display: table;\n  content: "";\n}\n.body--dark .markdown-wrapper .markdown-body::after {\n  display: table;\n  clear: both;\n  content: "";\n}\n.body--dark .markdown-wrapper .markdown-body > *:first-child {\n  margin-top: 0 !important;\n}\n.body--dark .markdown-wrapper .markdown-body > *:last-child {\n  margin-bottom: 0 !important;\n}\n.body--dark .markdown-wrapper .markdown-body a:not([href]) {\n  color: inherit;\n  text-decoration: none;\n}\n.body--dark .markdown-wrapper .markdown-body .absent {\n  color: #f85149;\n}\n.body--dark .markdown-wrapper .markdown-body .anchor {\n  float: left;\n  padding-right: 4px;\n  margin-left: -20px;\n  line-height: 1;\n}\n.body--dark .markdown-wrapper .markdown-body .anchor:focus {\n  outline: none;\n}\n.body--dark .markdown-wrapper .markdown-body p,\n.body--dark .markdown-wrapper .markdown-body blockquote,\n.body--dark .markdown-wrapper .markdown-body ul,\n.body--dark .markdown-wrapper .markdown-body ol,\n.body--dark .markdown-wrapper .markdown-body dl,\n.body--dark .markdown-wrapper .markdown-body table,\n.body--dark .markdown-wrapper .markdown-body pre,\n.body--dark .markdown-wrapper .markdown-body details {\n  margin-top: 0;\n  margin-bottom: 16px;\n}\n.body--dark .markdown-wrapper .markdown-body blockquote > :first-child {\n  margin-top: 0;\n}\n.body--dark .markdown-wrapper .markdown-body blockquote > :last-child {\n  margin-bottom: 0;\n}\n.body--dark .markdown-wrapper .markdown-body h1 .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h2 .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h3 .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h4 .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h5 .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h6 .octicon-link {\n  color: #e6edf3;\n  vertical-align: middle;\n  visibility: hidden;\n}\n.body--dark .markdown-wrapper .markdown-body h1:hover .anchor,\n.body--dark .markdown-wrapper .markdown-body h2:hover .anchor,\n.body--dark .markdown-wrapper .markdown-body h3:hover .anchor,\n.body--dark .markdown-wrapper .markdown-body h4:hover .anchor,\n.body--dark .markdown-wrapper .markdown-body h5:hover .anchor,\n.body--dark .markdown-wrapper .markdown-body h6:hover .anchor {\n  text-decoration: none;\n}\n.body--dark .markdown-wrapper .markdown-body h1:hover .anchor .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h2:hover .anchor .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h3:hover .anchor .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h4:hover .anchor .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h5:hover .anchor .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h6:hover .anchor .octicon-link {\n  visibility: visible;\n}\n.body--dark .markdown-wrapper .markdown-body h1 tt,\n.body--dark .markdown-wrapper .markdown-body h1 code,\n.body--dark .markdown-wrapper .markdown-body h2 tt,\n.body--dark .markdown-wrapper .markdown-body h2 code,\n.body--dark .markdown-wrapper .markdown-body h3 tt,\n.body--dark .markdown-wrapper .markdown-body h3 code,\n.body--dark .markdown-wrapper .markdown-body h4 tt,\n.body--dark .markdown-wrapper .markdown-body h4 code,\n.body--dark .markdown-wrapper .markdown-body h5 tt,\n.body--dark .markdown-wrapper .markdown-body h5 code,\n.body--dark .markdown-wrapper .markdown-body h6 tt,\n.body--dark .markdown-wrapper .markdown-body h6 code {\n  padding: 0 0.2em;\n  font-size: inherit;\n}\n.body--dark .markdown-wrapper .markdown-body summary h1,\n.body--dark .markdown-wrapper .markdown-body summary h2,\n.body--dark .markdown-wrapper .markdown-body summary h3,\n.body--dark .markdown-wrapper .markdown-body summary h4,\n.body--dark .markdown-wrapper .markdown-body summary h5,\n.body--dark .markdown-wrapper .markdown-body summary h6 {\n  display: inline-block;\n}\n.body--dark .markdown-wrapper .markdown-body summary h1 .anchor,\n.body--dark .markdown-wrapper .markdown-body summary h2 .anchor,\n.body--dark .markdown-wrapper .markdown-body summary h3 .anchor,\n.body--dark .markdown-wrapper .markdown-body summary h4 .anchor,\n.body--dark .markdown-wrapper .markdown-body summary h5 .anchor,\n.body--dark .markdown-wrapper .markdown-body summary h6 .anchor {\n  margin-left: -40px;\n}\n.body--dark .markdown-wrapper .markdown-body summary h1,\n.body--dark .markdown-wrapper .markdown-body summary h2 {\n  padding-bottom: 0;\n  border-bottom: 0;\n}\n.body--dark .markdown-wrapper .markdown-body ul.no-list,\n.body--dark .markdown-wrapper .markdown-body ol.no-list {\n  padding: 0;\n  list-style-type: none;\n}\n.body--dark .markdown-wrapper .markdown-body ol[type="a s"] {\n  list-style-type: lower-alpha;\n}\n.body--dark .markdown-wrapper .markdown-body ol[type="A s"] {\n  list-style-type: upper-alpha;\n}\n.body--dark .markdown-wrapper .markdown-body ol[type="i s"] {\n  list-style-type: lower-roman;\n}\n.body--dark .markdown-wrapper .markdown-body ol[type="I s"] {\n  list-style-type: upper-roman;\n}\n.body--dark .markdown-wrapper .markdown-body ol[type="1"] {\n  list-style-type: decimal;\n}\n.body--dark .markdown-wrapper .markdown-body div > ol:not([type]) {\n  list-style-type: decimal;\n}\n.body--dark .markdown-wrapper .markdown-body ul ul,\n.body--dark .markdown-wrapper .markdown-body ul ol,\n.body--dark .markdown-wrapper .markdown-body ol ol,\n.body--dark .markdown-wrapper .markdown-body ol ul {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.body--dark .markdown-wrapper .markdown-body li > p {\n  margin-top: 16px;\n}\n.body--dark .markdown-wrapper .markdown-body li + li {\n  margin-top: 0.25em;\n}\n.body--dark .markdown-wrapper .markdown-body dl {\n  padding: 0;\n}\n.body--dark .markdown-wrapper .markdown-body dl dt {\n  padding: 0;\n  margin-top: 16px;\n  font-size: 1em;\n  font-style: italic;\n  font-weight: 600;\n}\n.body--dark .markdown-wrapper .markdown-body dl dd {\n  padding: 0 16px;\n  margin-bottom: 16px;\n}\n.body--dark .markdown-wrapper .markdown-body table th {\n  font-weight: 600;\n}\n.body--dark .markdown-wrapper .markdown-body table th,\n.body--dark .markdown-wrapper .markdown-body table td {\n  padding: 6px 13px;\n  border: 1px solid #30363d;\n}\n.body--dark .markdown-wrapper .markdown-body table td > :last-child {\n  margin-bottom: 0;\n}\n.body--dark .markdown-wrapper .markdown-body table tr {\n  background-color: #0d1117;\n  border-top: 1px solid #30363db3;\n}\n.body--dark .markdown-wrapper .markdown-body table tr:nth-child(2n) {\n  background-color: #161b22;\n}\n.body--dark .markdown-wrapper .markdown-body table img {\n  background-color: transparent;\n}\n.body--dark .markdown-wrapper .markdown-body img[align=right] {\n  padding-left: 20px;\n}\n.body--dark .markdown-wrapper .markdown-body img[align=left] {\n  padding-right: 20px;\n}\n.body--dark .markdown-wrapper .markdown-body .emoji {\n  max-width: none;\n  vertical-align: text-top;\n  background-color: transparent;\n}\n.body--dark .markdown-wrapper .markdown-body span.frame {\n  display: block;\n  overflow: hidden;\n}\n.body--dark .markdown-wrapper .markdown-body span.frame > span {\n  display: block;\n  float: left;\n  width: auto;\n  padding: 7px;\n  margin: 13px 0 0;\n  overflow: hidden;\n  border: 1px solid #30363d;\n}\n.body--dark .markdown-wrapper .markdown-body span.frame span img {\n  display: block;\n  float: left;\n}\n.body--dark .markdown-wrapper .markdown-body span.frame span span {\n  display: block;\n  padding: 5px 0 0;\n  clear: both;\n  color: #e6edf3;\n}\n.body--dark .markdown-wrapper .markdown-body span.align-center {\n  display: block;\n  overflow: hidden;\n  clear: both;\n}\n.body--dark .markdown-wrapper .markdown-body span.align-center > span {\n  display: block;\n  margin: 13px auto 0;\n  overflow: hidden;\n  text-align: center;\n}\n.body--dark .markdown-wrapper .markdown-body span.align-center span img {\n  margin: 0 auto;\n  text-align: center;\n}\n.body--dark .markdown-wrapper .markdown-body span.align-right {\n  display: block;\n  overflow: hidden;\n  clear: both;\n}\n.body--dark .markdown-wrapper .markdown-body span.align-right > span {\n  display: block;\n  margin: 13px 0 0;\n  overflow: hidden;\n  text-align: right;\n}\n.body--dark .markdown-wrapper .markdown-body span.align-right span img {\n  margin: 0;\n  text-align: right;\n}\n.body--dark .markdown-wrapper .markdown-body span.float-left {\n  display: block;\n  float: left;\n  margin-right: 13px;\n  overflow: hidden;\n}\n.body--dark .markdown-wrapper .markdown-body span.float-left span {\n  margin: 13px 0 0;\n}\n.body--dark .markdown-wrapper .markdown-body span.float-right {\n  display: block;\n  float: right;\n  margin-left: 13px;\n  overflow: hidden;\n}\n.body--dark .markdown-wrapper .markdown-body span.float-right > span {\n  display: block;\n  margin: 13px auto 0;\n  overflow: hidden;\n  text-align: right;\n}\n.body--dark .markdown-wrapper .markdown-body code,\n.body--dark .markdown-wrapper .markdown-body tt {\n  padding: 0.2em 0.4em;\n  margin: 0;\n  font-size: 85%;\n  white-space: break-spaces;\n  background-color: #6e768166;\n  border-radius: 6px;\n}\n.body--dark .markdown-wrapper .markdown-body code br,\n.body--dark .markdown-wrapper .markdown-body tt br {\n  display: none;\n}\n.body--dark .markdown-wrapper .markdown-body del code {\n  text-decoration: inherit;\n}\n.body--dark .markdown-wrapper .markdown-body samp {\n  font-size: 85%;\n}\n.body--dark .markdown-wrapper .markdown-body pre code {\n  font-size: 100%;\n}\n.body--dark .markdown-wrapper .markdown-body pre > code {\n  padding: 0;\n  margin: 0;\n  word-break: normal;\n  white-space: pre;\n  background: transparent;\n  border: 0;\n}\n.body--dark .markdown-wrapper .markdown-body .highlight {\n  margin-bottom: 16px;\n}\n.body--dark .markdown-wrapper .markdown-body .highlight pre {\n  margin-bottom: 0;\n  word-break: normal;\n}\n.body--dark .markdown-wrapper .markdown-body .highlight pre,\n.body--dark .markdown-wrapper .markdown-body pre {\n  padding: 16px;\n  overflow: auto;\n  font-size: 85%;\n  line-height: 1.45;\n  color: #e6edf3;\n  background-color: #161b22;\n  border-radius: 6px;\n}\n.body--dark .markdown-wrapper .markdown-body pre code,\n.body--dark .markdown-wrapper .markdown-body pre tt {\n  display: inline;\n  max-width: auto;\n  padding: 0;\n  margin: 0;\n  overflow: visible;\n  line-height: inherit;\n  word-wrap: normal;\n  background-color: transparent;\n  border: 0;\n}\n.body--dark .markdown-wrapper .markdown-body .csv-data td,\n.body--dark .markdown-wrapper .markdown-body .csv-data th {\n  padding: 5px;\n  overflow: hidden;\n  font-size: 12px;\n  line-height: 1;\n  text-align: left;\n  white-space: nowrap;\n}\n.body--dark .markdown-wrapper .markdown-body .csv-data .blob-num {\n  padding: 10px 8px 9px;\n  text-align: right;\n  background: #0d1117;\n  border: 0;\n}\n.body--dark .markdown-wrapper .markdown-body .csv-data tr {\n  border-top: 0;\n}\n.body--dark .markdown-wrapper .markdown-body .csv-data th {\n  font-weight: 600;\n  background: #161b22;\n  border-top: 0;\n}\n.body--dark .markdown-wrapper .markdown-body [data-footnote-ref]::before {\n  content: "[";\n}\n.body--dark .markdown-wrapper .markdown-body [data-footnote-ref]::after {\n  content: "]";\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes {\n  font-size: 12px;\n  color: #8d96a0;\n  border-top: 1px solid #30363d;\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes ol {\n  padding-left: 16px;\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes ol ul {\n  display: inline-block;\n  padding-left: 16px;\n  margin-top: 16px;\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes li {\n  position: relative;\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes li:target::before {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  bottom: -8px;\n  left: -24px;\n  pointer-events: none;\n  content: "";\n  border: 2px solid #1f6feb;\n  border-radius: 6px;\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes li:target {\n  color: #e6edf3;\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes .data-footnote-backref g-emoji {\n  font-family: monospace;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-c {\n  color: #8b949e;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-c1,\n.body--dark .markdown-wrapper .markdown-body .pl-s .pl-v {\n  color: #79c0ff;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-e,\n.body--dark .markdown-wrapper .markdown-body .pl-en {\n  color: #d2a8ff;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-smi,\n.body--dark .markdown-wrapper .markdown-body .pl-s .pl-s1 {\n  color: #c9d1d9;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-ent {\n  color: #7ee787;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-k {\n  color: #ff7b72;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-s,\n.body--dark .markdown-wrapper .markdown-body .pl-pds,\n.body--dark .markdown-wrapper .markdown-body .pl-s .pl-pse .pl-s1,\n.body--dark .markdown-wrapper .markdown-body .pl-sr,\n.body--dark .markdown-wrapper .markdown-body .pl-sr .pl-cce,\n.body--dark .markdown-wrapper .markdown-body .pl-sr .pl-sre,\n.body--dark .markdown-wrapper .markdown-body .pl-sr .pl-sra {\n  color: #a5d6ff;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-v,\n.body--dark .markdown-wrapper .markdown-body .pl-smw {\n  color: #ffa657;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-bu {\n  color: #f85149;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-ii {\n  color: #f0f6fc;\n  background-color: #8e1519;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-c2 {\n  color: #f0f6fc;\n  background-color: #b62324;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-sr .pl-cce {\n  font-weight: bold;\n  color: #7ee787;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-ml {\n  color: #f2cc60;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mh,\n.body--dark .markdown-wrapper .markdown-body .pl-mh .pl-en,\n.body--dark .markdown-wrapper .markdown-body .pl-ms {\n  font-weight: bold;\n  color: #1f6feb;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mi {\n  font-style: italic;\n  color: #c9d1d9;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mb {\n  font-weight: bold;\n  color: #c9d1d9;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-md {\n  color: #ffdcd7;\n  background-color: #67060c;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mi1 {\n  color: #aff5b4;\n  background-color: #033a16;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mc {\n  color: #ffdfb6;\n  background-color: #5a1e02;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mi2 {\n  color: #c9d1d9;\n  background-color: #1158c7;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mdr {\n  font-weight: bold;\n  color: #d2a8ff;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-ba {\n  color: #8b949e;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-sg {\n  color: #484f58;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-corl {\n  text-decoration: underline;\n  color: #a5d6ff;\n}\n.body--dark .markdown-wrapper .markdown-body [role=button]:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body [role=tabpanel][tabindex="0"]:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body button:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body summary:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body a:focus:not(:focus-visible) {\n  outline: none;\n  box-shadow: none;\n}\n.body--dark .markdown-wrapper .markdown-body [tabindex="0"]:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body details-dialog:focus:not(:focus-visible) {\n  outline: none;\n}\n.body--dark .markdown-wrapper .markdown-body g-emoji {\n  display: inline-block;\n  min-width: 1ch;\n  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";\n  font-size: 1em;\n  font-style: normal !important;\n  font-weight: 400;\n  line-height: 1;\n  vertical-align: -0.075em;\n}\n.body--dark .markdown-wrapper .markdown-body g-emoji img {\n  width: 1em;\n  height: 1em;\n}\n.body--dark .markdown-wrapper .markdown-body .task-list-item {\n  list-style-type: none;\n}\n.body--dark .markdown-wrapper .markdown-body .task-list-item label {\n  font-weight: 400;\n}\n.body--dark .markdown-wrapper .markdown-body .task-list-item.enabled label {\n  cursor: pointer;\n}\n.body--dark .markdown-wrapper .markdown-body .task-list-item + .task-list-item {\n  margin-top: 0.25rem;\n}\n.body--dark .markdown-wrapper .markdown-body .task-list-item .handle {\n  display: none;\n}\n.body--dark .markdown-wrapper .markdown-body .task-list-item-checkbox {\n  margin: 0 0.2em 0.25em -1.4em;\n  vertical-align: middle;\n}\n.body--dark .markdown-wrapper .markdown-body .contains-task-list:dir(rtl) .task-list-item-checkbox {\n  margin: 0 -1.6em 0.25em 0.2em;\n}\n.body--dark .markdown-wrapper .markdown-body .contains-task-list {\n  position: relative;\n}\n.body--dark .markdown-wrapper .markdown-body .contains-task-list:hover .task-list-item-convert-container,\n.body--dark .markdown-wrapper .markdown-body .contains-task-list:focus-within .task-list-item-convert-container {\n  display: block;\n  width: auto;\n  height: 24px;\n  overflow: visible;\n  clip: auto;\n}\n.body--dark .markdown-wrapper .markdown-body ::-webkit-calendar-picker-indicator {\n  filter: invert(50%);\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert {\n  padding: 0.5rem 1rem;\n  margin-bottom: 1rem;\n  color: inherit;\n  border-left: 0.25em solid #30363d;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert > :first-child {\n  margin-top: 0;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert > :last-child {\n  margin-bottom: 0;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert .markdown-alert-title {\n  display: flex;\n  font-weight: 500;\n  align-items: center;\n  line-height: 1;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-note {\n  border-left-color: #1f6feb;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-note .markdown-alert-title {\n  color: #4493f8;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-important {\n  border-left-color: #8957e5;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-important .markdown-alert-title {\n  color: #ab7df8;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-warning {\n  border-left-color: #9e6a03;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-warning .markdown-alert-title {\n  color: #d29922;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-tip {\n  border-left-color: #238636;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-tip .markdown-alert-title {\n  color: #3fb950;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-caution {\n  border-left-color: #da3633;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-caution .markdown-alert-title {\n  color: #f85149;\n}\n.body--dark .markdown-wrapper .markdown-body > *:first-child > .heading-element:first-child {\n  margin-top: 0 !important;\n}';D(U);const W="data-theme",V="com.silabs.ss.platform.theme.dark",G=function(e){e.getAttribute(W)===V?p.Z.set(!0):p.Z.set(!1)},J=function(e){return new MutationObserver((n=>{n.forEach((n=>{"attributes"===n.type&&n.attributeName===W&&G(e)}))}))},Z=function(){var e=document.documentElement;G(e);const n=J(e);return n.observe(e,{attributes:!0,attributeFilter:[W],subtree:!1}),n};var K={setQuasarTheme:G,createThemeObserver:J,observe:Z};class Y{constructor(e,n,t=""){this.path=e,this.mutator=n,this.prefix=t,this.attemptReconnect=!1,this.timeout=5e3,window.addEventListener("beforeunload",(()=>{this.close()}))}setReconnectTimeout(e){this.timeout=e}getReadyState(){return this.socket.readyState}reconnect(){return new Promise(((e,n)=>{!this.socket||this.getReadyState()!==WebSocket.CONNECTING&&this.getReadyState()!==WebSocket.OPEN?this.init().then((n=>{e(n)})).catch((e=>{n(e)})):e()}))}mutatorSocketEvent(e,n){this.prefix&&(e=e.charAt(0).toUpperCase()+e.slice(1)),e=this.prefix+e,this.mutator[e]&&this.mutator[e](n)}init(){return new Promise((e=>{this.socket=new WebSocket(this.path),this.socket.onmessage=e=>{this.mutatorSocketEvent("onMessage",e)},this.socket.onopen=n=>{this.attemptReconnect=!0,this.mutatorSocketEvent("onOpen",n),e()},this.socket.onclose=n=>{this.mutatorSocketEvent("onClose",n),this.attemptReconnect?setTimeout((()=>{window.console.log(`${this.prefix} socket connection lost, attempting reconnect`),this.reconnect()}),this.timeout):e()},this.socket.onerror=e=>{this.mutatorSocketEvent("onError",e)}}))}close(){this.socket&&(this.socket.onclose=()=>{},this.socket.close()),this.socket=null,this.attemptReconnect=!1}}class X{constructor(){this.notifications={},this.cancelTaskHandler=null,this.quasarNotification=A}static default(){return this.defaultInstance||(this.defaultInstance=new X),this.defaultInstance}closeAll(){for(const e in this.notifications)this.notifications[e](),delete this.notifications[e]}setCancelTaskHandler(e){this.cancelTaskHandler=e}handleNotificationMessage(e){switch(e.type){case"BeginTask":var n=[{label:"Cancel",color:"white",handler:()=>{this.cancelTaskHandler&&this.cancelTaskHandler(e.id),this.closeNotification(e.id)}}];this.createNotification(e.name,e.id,e.totalWork,n);break;case"SetTaskName":this.updateNotificationName(e.name,e.id);break;case"SubTask":this.updateNotificationSubTask(e.name,e.id);break;case"Worked":this.updateNotificationProgress(e.work,e.id);break;case"Done":this.closeNotification(e.id)}}createNotification(e,n,t,r){const o=this.quasarNotification.create({message:e,caption:0/t+" %",group:!1,position:"bottom-right",color:"primary",timeout:0,actions:r});this.notifications[n]=o,o.taskName="",o.currentProgress=0,o.totalProgress=t}updateNotificationName(e,n){const t=this.notifications[n];t&&t({message:e})}updateNotificationSubTask(e,n){const t=this.notifications[n];t&&(t.taskName=e,t({caption:t.taskName+" "+100*t.currentProgress/t.totalProgress+"%"}))}updateNotificationProgress(e,n){const t=this.notifications[n];t&&(t.currentProgress=e,t({caption:t.taskName+" "+100*t.currentProgress/t.totalProgress+"%"}))}closeNotification(e){const n=this.notifications[e];n&&(n(),delete this.notifications[e])}}class Q{constructor(){if(Q._instance)return Q._instance;(Q._instance=this)._transceivers=new Map,window.addEventListener("message",this.receiveMessage.bind(this),!1)}static instance(){return new Q}addTransceiver(e,n=null){e&&this._transceivers.set((n={window:e,origin:n,events:{}}).window,n)}removeTransceiver(e){this._transceivers.has(e)&&this._transceivers.delete(e)}addListener(e,n,t){const r=this._transceivers.get(e);r&&(r.events[n]||(r.events[n]=[]),r.events[n].push(t))}removeListener(e,n,t){const r=this._transceivers.get(e);r&&(!r.events[n]||-1!==(t=r.events[n].indexOf(t))&&r.events[n].splice(t,1))}sendMessage(e,n){var t;this._transceivers.get(e)&&(t=this.origin||"*",n.senderLocation=window.location.href,e.postMessage(n,t))}receiveMessage(e){const n=this._transceivers.get(e.source);if(n&&(n.origin||(n.origin=e.origin),n.origin===e.origin)){const t=e.data;n.events[t.eventId]&&n.events[t.eventId].forEach((e=>{e(t.eventData)}))}}destroy(){this._transceivers.clear(),window.removeEventListener("message",this.receiveMessage.bind(this),!1),Q._instance=void 0}}const ee="close",ne=e=>{},te=e=>({eventId:ee,eventData:{shouldClose:e}});var re={EVENT_ID:ee,defaultHandler:ne,message:te};const oe="dirty",ae=e=>{},ie=e=>({eventId:oe,eventData:{isDirty:e}});var se={EVENT_ID:oe,defaultHandler:ae,message:ie};const le="focus",ce=e=>{},de=e=>({eventId:le,eventData:{shouldFocus:e}});var ue={EVENT_ID:le,defaultHandler:ce,message:de};const pe="location",me=e=>{},fe=e=>({eventId:pe,eventData:{location:e}});var he={EVENT_ID:pe,defaultHandler:me,message:fe};const we="mounted",be=e=>{},ge=e=>({eventId:we,eventData:{hasMounted:e}});var ke={EVENT_ID:we,defaultHandler:be,message:ge};const ye="save",ve=e=>{},_e=e=>({eventId:ye,eventData:{shouldSave:e}});var xe={EVENT_ID:ye,defaultHandler:ve,message:_e};const Se="theme",Ce=e=>{"dark"===e.theme?p.Z.set(!0):p.Z.set(!1)},Ee=e=>({eventId:Se,eventData:{theme:e}});var Pe={EVENT_ID:Se,defaultHandler:Ce,message:Ee};const je="open-file",Le=e=>{},Oe=e=>({eventId:je,eventData:e});var Te={EVENT_ID:je,defaultHandler:Le,message:Oe};function Me(e,n,t,r,o){let a;return window.swt&&window.swt.openFileDialog?a=window.swt.openFileDialog(e,n,t,r,o):(a=[],window.alert("HTML openFileDialog not implemented!")),a}function Ae(e,n,t,r){let o;return window.swt&&window.swt.saveFileDialog?o=window.swt.saveFileDialog(e,n,t,r):(o=[],window.alert("HTML saveFileDialog not implemented!")),o}function Fe(e,n){let t;return window.swt&&window.swt.openDirectoryDialog?t=window.swt.openDirectoryDialog(e,n):window.alert("HTML openDirectoryDialog not implemented!"),t}function ze(e){e&&e.use($e)}function $e(e){return window.uiId?e.headers.uiId=window.uiId:frameElement&&frameElement.id&&(e.headers.uiId=frameElement.id),e}function Re(e,n,t){e&&e.use(n=n||Ie,t=t||Ne)}function Ie(e){return e}function Ne(e){if(X.default().closeAll(),e&&e.response&&e.response.data&&e.response.data.primaryMessage){var n=e.response.data,t=`<div style="font-weight:bolder">Error: ${n.primaryMessage}</div>`;if(n&&n.errors)for(var r=0;r<n.errors.length;r++)t+=`<div style="margin-left: 2em"><strong>${n.errors[r]}</strong><br/>${n.callStack[r]}</div>`;const o=A.create({type:"negative",message:t,html:!0,group:!1,position:"bottom-right",color:"red",timeout:0,actions:[{icon:"close",color:"white",round:!0,handler:()=>{o()}}]})}return Promise.reject(e)}const qe="2.4.37";function He(e){e.component(B.name,B)}var Be=Object.freeze({__proto__:null,CloseMessageEvent:re,DirtyMessageEvent:se,FocusMessageEvent:ue,LocationMessageEvent:he,MountedMessageEvent:ke,NotificationService:X,OpenFileMessageEvent:Te,PostMessageEmitter:Q,SSMarkDownRenderer:B,SaveMessageEvent:xe,StudioThemeObserver:K,ThemeMessageEvent:Pe,WebSocketWrapper:Y,addDefaultRequestInterceptor:ze,addDefaultResponseInterceptor:Re,defaultRequestInterceptor:$e,defaultResponseErrorHandler:Ne,defaultResponseSuccesshandler:Ie,install:He,openDirectoryDialog:Fe,openFileDialog:Me,saveFileDialog:Ae,version:qe})},5948:(e,n,t)=>{"use strict";t.d(n,{o:()=>Bn});const r="function"===typeof Symbol&&"symbol"===typeof Symbol.toStringTag,o=e=>r?Symbol(e):e,a=(e,n,t)=>i({l:e,k:n,s:t}),i=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),s=e=>"number"===typeof e&&isFinite(e),l=e=>"[object Date]"===_(e),c=e=>"[object RegExp]"===_(e),d=e=>x(e)&&0===Object.keys(e).length;function u(e,n){"undefined"!==typeof console&&(console.warn("[intlify] "+e),n&&console.warn(n.stack))}const p=Object.assign;function m(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const f=Object.prototype.hasOwnProperty;function h(e,n){return f.call(e,n)}const w=Array.isArray,b=e=>"function"===typeof e,g=e=>"string"===typeof e,k=e=>"boolean"===typeof e,y=e=>null!==e&&"object"===typeof e,v=Object.prototype.toString,_=e=>v.call(e),x=e=>"[object Object]"===_(e),S=e=>null==e?"":w(e)||x(e)&&e.toString===v?JSON.stringify(e,null,2):String(e);const C=Object.prototype.hasOwnProperty;function E(e,n){return C.call(e,n)}const P=e=>null!==e&&"object"===typeof e,j=[];j[0]={["w"]:[0],["i"]:[3,0],["["]:[4],["o"]:[7]},j[1]={["w"]:[1],["."]:[2],["["]:[4],["o"]:[7]},j[2]={["w"]:[2],["i"]:[3,0],["0"]:[3,0]},j[3]={["i"]:[3,0],["0"]:[3,0],["w"]:[1,1],["."]:[2,1],["["]:[4,1],["o"]:[7,1]},j[4]={["'"]:[5,0],['"']:[6,0],["["]:[4,2],["]"]:[1,3],["o"]:8,["l"]:[4,0]},j[5]={["'"]:[4,0],["o"]:8,["l"]:[5,0]},j[6]={['"']:[4,0],["o"]:8,["l"]:[6,0]};const L=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function O(e){return L.test(e)}function T(e){const n=e.charCodeAt(0),t=e.charCodeAt(e.length-1);return n!==t||34!==n&&39!==n?e:e.slice(1,-1)}function M(e){if(void 0===e||null===e)return"o";const n=e.charCodeAt(0);switch(n){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function A(e){const n=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(O(n)?T(n):"*"+n)}function F(e){const n=[];let t,r,o,a,i,s,l,c=-1,d=0,u=0;const p=[];function m(){const n=e[c+1];if(5===d&&"'"===n||6===d&&'"'===n)return c++,o="\\"+n,p[0](),!0}p[0]=()=>{void 0===r?r=o:r+=o},p[1]=()=>{void 0!==r&&(n.push(r),r=void 0)},p[2]=()=>{p[0](),u++},p[3]=()=>{if(u>0)u--,d=4,p[0]();else{if(u=0,void 0===r)return!1;if(r=A(r),!1===r)return!1;p[1]()}};while(null!==d)if(c++,t=e[c],"\\"!==t||!m()){if(a=M(t),l=j[d],i=l[a]||l["l"]||8,8===i)return;if(d=i[0],void 0!==i[1]&&(s=p[i[1]],s&&(o=t,!1===s())))return;if(7===d)return n}}const z=new Map;function $(e,n){if(!P(e))return null;let t=z.get(n);if(t||(t=F(n),t&&z.set(n,t)),!t)return null;const r=t.length;let o=e,a=0;while(a<r){const e=o[t[a]];if(void 0===e)return null;o=e,a++}return o}function R(e){if(!P(e))return e;for(const n in e)if(E(e,n))if(n.includes(".")){const t=n.split("."),r=t.length-1;let o=e;for(let e=0;e<r;e++)t[e]in o||(o[t[e]]={}),o=o[t[e]];o[t[r]]=e[n],delete e[n],P(o[t[r]])&&R(o[t[r]])}else P(e[n])&&R(e[n]);return e}
/*!
  * @intlify/runtime v9.1.9
  * (c) 2021 kazuya kawaguchi
  * Released under the MIT License.
  */
const I=e=>e,N=e=>"",q="text",H=e=>0===e.length?"":e.join(""),B=S;function D(e,n){return e=Math.abs(e),2===n?e?e>1?1:0:1:e?Math.min(e,2):0}function U(e){const n=s(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(s(e.named.count)||s(e.named.n))?s(e.named.count)?e.named.count:s(e.named.n)?e.named.n:n:n}function W(e,n){n.count||(n.count=e),n.n||(n.n=e)}function V(e={}){const n=e.locale,t=U(e),r=y(e.pluralRules)&&g(n)&&b(e.pluralRules[n])?e.pluralRules[n]:D,o=y(e.pluralRules)&&g(n)&&b(e.pluralRules[n])?D:void 0,a=e=>e[r(t,e.length,o)],i=e.list||[],l=e=>i[e],c=e.named||{};s(e.pluralIndex)&&W(t,c);const d=e=>c[e];function u(n){const t=b(e.messages)?e.messages(n):!!y(e.messages)&&e.messages[n];return t||(e.parent?e.parent.message(n):N)}const p=n=>e.modifiers?e.modifiers[n]:I,m=x(e.processor)&&b(e.processor.normalize)?e.processor.normalize:H,f=x(e.processor)&&b(e.processor.interpolate)?e.processor.interpolate:B,h=x(e.processor)&&g(e.processor.type)?e.processor.type:q,w={["list"]:l,["named"]:d,["plural"]:a,["linked"]:(e,n)=>{const t=u(e)(w);return g(n)?p(n)(t):t},["message"]:u,["type"]:h,["interpolate"]:f,["normalize"]:m};return w}function G(e,n,t={}){const{domain:r,messages:o,args:a}=t,i=e,s=new SyntaxError(String(i));return s.code=e,n&&(s.location=n),s.domain=r,s}function J(e){throw e}function Z(e,n,t){return{line:e,column:n,offset:t}}function K(e,n,t){const r={start:e,end:n};return null!=t&&(r.source=t),r}const Y=" ",X="\r",Q="\n",ee=String.fromCharCode(8232),ne=String.fromCharCode(8233);function te(e){const n=e;let t=0,r=1,o=1,a=0;const i=e=>n[e]===X&&n[e+1]===Q,s=e=>n[e]===Q,l=e=>n[e]===ne,c=e=>n[e]===ee,d=e=>i(e)||s(e)||l(e)||c(e),u=()=>t,p=()=>r,m=()=>o,f=()=>a,h=e=>i(e)||l(e)||c(e)?Q:n[e],w=()=>h(t),b=()=>h(t+a);function g(){return a=0,d(t)&&(r++,o=0),i(t)&&t++,t++,o++,n[t]}function k(){return i(t+a)&&a++,a++,n[t+a]}function y(){t=0,r=1,o=1,a=0}function v(e=0){a=e}function _(){const e=t+a;while(e!==t)g();a=0}return{index:u,line:p,column:m,peekOffset:f,charAt:h,currentChar:w,currentPeek:b,next:g,peek:k,reset:y,resetPeek:v,skipToPeek:_}}const re=void 0,oe="'",ae="tokenizer";function ie(e,n={}){const t=!1!==n.location,r=te(e),o=()=>r.index(),a=()=>Z(r.line(),r.column(),r.index()),i=a(),s=o(),l={currentType:14,offset:s,startLoc:i,endLoc:i,lastType:14,lastOffset:s,lastStartLoc:i,lastEndLoc:i,braceNest:0,inLinked:!1,text:""},c=()=>l,{onError:d}=n;function u(e,n,t,...r){const o=c();if(n.column+=t,n.offset+=t,d){const t=K(o.startLoc,n),a=G(e,t,{domain:ae,args:r});d(a)}}function p(e,n,r){e.endLoc=a(),e.currentType=n;const o={type:n};return t&&(o.loc=K(e.startLoc,e.endLoc)),null!=r&&(o.value=r),o}const m=e=>p(e,14);function f(e,n){return e.currentChar()===n?(e.next(),n):(u(0,a(),0,n),"")}function h(e){let n="";while(e.currentPeek()===Y||e.currentPeek()===Q)n+=e.currentPeek(),e.peek();return n}function w(e){const n=h(e);return e.skipToPeek(),n}function b(e){if(e===re)return!1;const n=e.charCodeAt(0);return n>=97&&n<=122||n>=65&&n<=90||95===n}function g(e){if(e===re)return!1;const n=e.charCodeAt(0);return n>=48&&n<=57}function k(e,n){const{currentType:t}=n;if(2!==t)return!1;h(e);const r=b(e.currentPeek());return e.resetPeek(),r}function y(e,n){const{currentType:t}=n;if(2!==t)return!1;h(e);const r="-"===e.currentPeek()?e.peek():e.currentPeek(),o=g(r);return e.resetPeek(),o}function v(e,n){const{currentType:t}=n;if(2!==t)return!1;h(e);const r=e.currentPeek()===oe;return e.resetPeek(),r}function _(e,n){const{currentType:t}=n;if(8!==t)return!1;h(e);const r="."===e.currentPeek();return e.resetPeek(),r}function x(e,n){const{currentType:t}=n;if(9!==t)return!1;h(e);const r=b(e.currentPeek());return e.resetPeek(),r}function S(e,n){const{currentType:t}=n;if(8!==t&&12!==t)return!1;h(e);const r=":"===e.currentPeek();return e.resetPeek(),r}function C(e,n){const{currentType:t}=n;if(10!==t)return!1;const r=()=>{const n=e.currentPeek();return"{"===n?b(e.peek()):!("@"===n||"%"===n||"|"===n||":"===n||"."===n||n===Y||!n)&&(n===Q?(e.peek(),r()):b(n))},o=r();return e.resetPeek(),o}function E(e){h(e);const n="|"===e.currentPeek();return e.resetPeek(),n}function P(e,n=!0){const t=(n=!1,r="",o=!1)=>{const a=e.currentPeek();return"{"===a?"%"!==r&&n:"@"!==a&&a?"%"===a?(e.peek(),t(n,"%",!0)):"|"===a?!("%"!==r&&!o)||!(r===Y||r===Q):a===Y?(e.peek(),t(!0,Y,o)):a!==Q||(e.peek(),t(!0,Q,o)):"%"===r||n},r=t();return n&&e.resetPeek(),r}function j(e,n){const t=e.currentChar();return t===re?re:n(t)?(e.next(),t):null}function L(e){const n=e=>{const n=e.charCodeAt(0);return n>=97&&n<=122||n>=65&&n<=90||n>=48&&n<=57||95===n||36===n};return j(e,n)}function O(e){const n=e=>{const n=e.charCodeAt(0);return n>=48&&n<=57};return j(e,n)}function T(e){const n=e=>{const n=e.charCodeAt(0);return n>=48&&n<=57||n>=65&&n<=70||n>=97&&n<=102};return j(e,n)}function M(e){let n="",t="";while(n=O(e))t+=n;return t}function A(e){let n="";while(1){const t=e.currentChar();if("{"===t||"}"===t||"@"===t||"|"===t||!t)break;if("%"===t){if(!P(e))break;n+=t,e.next()}else if(t===Y||t===Q)if(P(e))n+=t,e.next();else{if(E(e))break;n+=t,e.next()}else n+=t,e.next()}return n}function F(e){w(e);let n="",t="";while(n=L(e))t+=n;return e.currentChar()===re&&u(6,a(),0),t}function z(e){w(e);let n="";return"-"===e.currentChar()?(e.next(),n+=`-${M(e)}`):n+=M(e),e.currentChar()===re&&u(6,a(),0),n}function $(e){w(e),f(e,"'");let n="",t="";const r=e=>e!==oe&&e!==Q;while(n=j(e,r))t+="\\"===n?R(e):n;const o=e.currentChar();return o===Q||o===re?(u(2,a(),0),o===Q&&(e.next(),f(e,"'")),t):(f(e,"'"),t)}function R(e){const n=e.currentChar();switch(n){case"\\":case"'":return e.next(),`\\${n}`;case"u":return I(e,n,4);case"U":return I(e,n,6);default:return u(3,a(),0,n),""}}function I(e,n,t){f(e,n);let r="";for(let o=0;o<t;o++){const t=T(e);if(!t){u(4,a(),0,`\\${n}${r}${e.currentChar()}`);break}r+=t}return`\\${n}${r}`}function N(e){w(e);let n="",t="";const r=e=>"{"!==e&&"}"!==e&&e!==Y&&e!==Q;while(n=j(e,r))t+=n;return t}function q(e){let n="",t="";while(n=L(e))t+=n;return t}function H(e){const n=(t=!1,r)=>{const o=e.currentChar();return"{"!==o&&"%"!==o&&"@"!==o&&"|"!==o&&o?o===Y?r:o===Q?(r+=o,e.next(),n(t,r)):(r+=o,e.next(),n(!0,r)):r};return n(!1,"")}function B(e){w(e);const n=f(e,"|");return w(e),n}function D(e,n){let t=null;const r=e.currentChar();switch(r){case"{":return n.braceNest>=1&&u(8,a(),0),e.next(),t=p(n,2,"{"),w(e),n.braceNest++,t;case"}":return n.braceNest>0&&2===n.currentType&&u(7,a(),0),e.next(),t=p(n,3,"}"),n.braceNest--,n.braceNest>0&&w(e),n.inLinked&&0===n.braceNest&&(n.inLinked=!1),t;case"@":return n.braceNest>0&&u(6,a(),0),t=U(e,n)||m(n),n.braceNest=0,t;default:let r=!0,o=!0,i=!0;if(E(e))return n.braceNest>0&&u(6,a(),0),t=p(n,1,B(e)),n.braceNest=0,n.inLinked=!1,t;if(n.braceNest>0&&(5===n.currentType||6===n.currentType||7===n.currentType))return u(6,a(),0),n.braceNest=0,W(e,n);if(r=k(e,n))return t=p(n,5,F(e)),w(e),t;if(o=y(e,n))return t=p(n,6,z(e)),w(e),t;if(i=v(e,n))return t=p(n,7,$(e)),w(e),t;if(!r&&!o&&!i)return t=p(n,13,N(e)),u(1,a(),0,t.value),w(e),t;break}return t}function U(e,n){const{currentType:t}=n;let r=null;const o=e.currentChar();switch(8!==t&&9!==t&&12!==t&&10!==t||o!==Q&&o!==Y||u(9,a(),0),o){case"@":return e.next(),r=p(n,8,"@"),n.inLinked=!0,r;case".":return w(e),e.next(),p(n,9,".");case":":return w(e),e.next(),p(n,10,":");default:return E(e)?(r=p(n,1,B(e)),n.braceNest=0,n.inLinked=!1,r):_(e,n)||S(e,n)?(w(e),U(e,n)):x(e,n)?(w(e),p(n,12,q(e))):C(e,n)?(w(e),"{"===o?D(e,n)||r:p(n,11,H(e))):(8===t&&u(9,a(),0),n.braceNest=0,n.inLinked=!1,W(e,n))}}function W(e,n){let t={type:14};if(n.braceNest>0)return D(e,n)||m(n);if(n.inLinked)return U(e,n)||m(n);const r=e.currentChar();switch(r){case"{":return D(e,n)||m(n);case"}":return u(5,a(),0),e.next(),p(n,3,"}");case"@":return U(e,n)||m(n);default:if(E(e))return t=p(n,1,B(e)),n.braceNest=0,n.inLinked=!1,t;if(P(e))return p(n,0,A(e));if("%"===r)return e.next(),p(n,4,"%");break}return t}function V(){const{currentType:e,offset:n,startLoc:t,endLoc:i}=l;return l.lastType=e,l.lastOffset=n,l.lastStartLoc=t,l.lastEndLoc=i,l.offset=o(),l.startLoc=a(),r.currentChar()===re?p(l,14):W(r,l)}return{nextToken:V,currentOffset:o,currentPosition:a,context:c}}const se="parser",le=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function ce(e,n,t){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(n||t,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function de(e={}){const n=!1!==e.location,{onError:t}=e;function r(e,n,r,o,...a){const i=e.currentPosition();if(i.offset+=o,i.column+=o,t){const e=K(r,i),o=G(n,e,{domain:se,args:a});t(o)}}function o(e,t,r){const o={type:e,start:t,end:t};return n&&(o.loc={start:r,end:r}),o}function a(e,t,r,o){e.end=t,o&&(e.type=o),n&&e.loc&&(e.loc.end=r)}function i(e,n){const t=e.context(),r=o(3,t.offset,t.startLoc);return r.value=n,a(r,e.currentOffset(),e.currentPosition()),r}function s(e,n){const t=e.context(),{lastOffset:r,lastStartLoc:i}=t,s=o(5,r,i);return s.index=parseInt(n,10),e.nextToken(),a(s,e.currentOffset(),e.currentPosition()),s}function l(e,n){const t=e.context(),{lastOffset:r,lastStartLoc:i}=t,s=o(4,r,i);return s.key=n,e.nextToken(),a(s,e.currentOffset(),e.currentPosition()),s}function c(e,n){const t=e.context(),{lastOffset:r,lastStartLoc:i}=t,s=o(9,r,i);return s.value=n.replace(le,ce),e.nextToken(),a(s,e.currentOffset(),e.currentPosition()),s}function d(e){const n=e.nextToken(),t=e.context(),{lastOffset:i,lastStartLoc:s}=t,l=o(8,i,s);return 12!==n.type?(r(e,11,t.lastStartLoc,0),l.value="",a(l,i,s),{nextConsumeToken:n,node:l}):(null==n.value&&r(e,13,t.lastStartLoc,0,ue(n)),l.value=n.value||"",a(l,e.currentOffset(),e.currentPosition()),{node:l})}function u(e,n){const t=e.context(),r=o(7,t.offset,t.startLoc);return r.value=n,a(r,e.currentOffset(),e.currentPosition()),r}function m(e){const n=e.context(),t=o(6,n.offset,n.startLoc);let i=e.nextToken();if(9===i.type){const n=d(e);t.modifier=n.node,i=n.nextConsumeToken||e.nextToken()}switch(10!==i.type&&r(e,13,n.lastStartLoc,0,ue(i)),i=e.nextToken(),2===i.type&&(i=e.nextToken()),i.type){case 11:null==i.value&&r(e,13,n.lastStartLoc,0,ue(i)),t.key=u(e,i.value||"");break;case 5:null==i.value&&r(e,13,n.lastStartLoc,0,ue(i)),t.key=l(e,i.value||"");break;case 6:null==i.value&&r(e,13,n.lastStartLoc,0,ue(i)),t.key=s(e,i.value||"");break;case 7:null==i.value&&r(e,13,n.lastStartLoc,0,ue(i)),t.key=c(e,i.value||"");break;default:r(e,12,n.lastStartLoc,0);const d=e.context(),p=o(7,d.offset,d.startLoc);return p.value="",a(p,d.offset,d.startLoc),t.key=p,a(t,d.offset,d.startLoc),{nextConsumeToken:i,node:t}}return a(t,e.currentOffset(),e.currentPosition()),{node:t}}function f(e){const n=e.context(),t=1===n.currentType?e.currentOffset():n.offset,d=1===n.currentType?n.endLoc:n.startLoc,u=o(2,t,d);u.items=[];let p=null;do{const t=p||e.nextToken();switch(p=null,t.type){case 0:null==t.value&&r(e,13,n.lastStartLoc,0,ue(t)),u.items.push(i(e,t.value||""));break;case 6:null==t.value&&r(e,13,n.lastStartLoc,0,ue(t)),u.items.push(s(e,t.value||""));break;case 5:null==t.value&&r(e,13,n.lastStartLoc,0,ue(t)),u.items.push(l(e,t.value||""));break;case 7:null==t.value&&r(e,13,n.lastStartLoc,0,ue(t)),u.items.push(c(e,t.value||""));break;case 8:const o=m(e);u.items.push(o.node),p=o.nextConsumeToken||null;break}}while(14!==n.currentType&&1!==n.currentType);const f=1===n.currentType?n.lastOffset:e.currentOffset(),h=1===n.currentType?n.lastEndLoc:e.currentPosition();return a(u,f,h),u}function h(e,n,t,i){const s=e.context();let l=0===i.items.length;const c=o(1,n,t);c.cases=[],c.cases.push(i);do{const n=f(e);l||(l=0===n.items.length),c.cases.push(n)}while(14!==s.currentType);return l&&r(e,10,t,0),a(c,e.currentOffset(),e.currentPosition()),c}function w(e){const n=e.context(),{offset:t,startLoc:r}=n,o=f(e);return 14===n.currentType?o:h(e,t,r,o)}function b(t){const i=ie(t,p({},e)),s=i.context(),l=o(0,s.offset,s.startLoc);return n&&l.loc&&(l.loc.source=t),l.body=w(i),14!==s.currentType&&r(i,13,s.lastStartLoc,0,t[s.offset]||""),a(l,i.currentOffset(),i.currentPosition()),l}return{parse:b}}function ue(e){if(14===e.type)return"EOF";const n=(e.value||"").replace(/\r?\n/gu,"\\n");return n.length>10?n.slice(0,9)+"…":n}function pe(e,n={}){const t={ast:e,helpers:new Set},r=()=>t,o=e=>(t.helpers.add(e),e);return{context:r,helper:o}}function me(e,n){for(let t=0;t<e.length;t++)fe(e[t],n)}function fe(e,n){switch(e.type){case 1:me(e.cases,n),n.helper("plural");break;case 2:me(e.items,n);break;case 6:const t=e;fe(t.key,n),n.helper("linked");break;case 5:n.helper("interpolate"),n.helper("list");break;case 4:n.helper("interpolate"),n.helper("named");break}}function he(e,n={}){const t=pe(e);t.helper("normalize"),e.body&&fe(e.body,t);const r=t.context();e.helpers=Array.from(r.helpers)}function we(e,n){const{sourceMap:t,filename:r,breakLineCode:o,needIndent:a}=n,i={source:e.loc.source,filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:o,needIndent:a,indentLevel:0},s=()=>i;function l(e,n){i.code+=e}function c(e,n=!0){const t=n?o:"";l(a?t+"  ".repeat(e):t)}function d(e=!0){const n=++i.indentLevel;e&&c(n)}function u(e=!0){const n=--i.indentLevel;e&&c(n)}function p(){c(i.indentLevel)}const m=e=>`_${e}`,f=()=>i.needIndent;return{context:s,push:l,indent:d,deindent:u,newline:p,helper:m,needIndent:f}}function be(e,n){const{helper:t}=e;e.push(`${t("linked")}(`),ve(e,n.key),n.modifier&&(e.push(", "),ve(e,n.modifier)),e.push(")")}function ge(e,n){const{helper:t,needIndent:r}=e;e.push(`${t("normalize")}([`),e.indent(r());const o=n.items.length;for(let a=0;a<o;a++){if(ve(e,n.items[a]),a===o-1)break;e.push(", ")}e.deindent(r()),e.push("])")}function ke(e,n){const{helper:t,needIndent:r}=e;if(n.cases.length>1){e.push(`${t("plural")}([`),e.indent(r());const o=n.cases.length;for(let t=0;t<o;t++){if(ve(e,n.cases[t]),t===o-1)break;e.push(", ")}e.deindent(r()),e.push("])")}}function ye(e,n){n.body?ve(e,n.body):e.push("null")}function ve(e,n){const{helper:t}=e;switch(n.type){case 0:ye(e,n);break;case 1:ke(e,n);break;case 2:ge(e,n);break;case 6:be(e,n);break;case 8:e.push(JSON.stringify(n.value),n);break;case 7:e.push(JSON.stringify(n.value),n);break;case 5:e.push(`${t("interpolate")}(${t("list")}(${n.index}))`,n);break;case 4:e.push(`${t("interpolate")}(${t("named")}(${JSON.stringify(n.key)}))`,n);break;case 9:e.push(JSON.stringify(n.value),n);break;case 3:e.push(JSON.stringify(n.value),n);break;default:0}}const _e=(e,n={})=>{const t=g(n.mode)?n.mode:"normal",r=g(n.filename)?n.filename:"message.intl",o=!!n.sourceMap,a=null!=n.breakLineCode?n.breakLineCode:"arrow"===t?";":"\n",i=n.needIndent?n.needIndent:"arrow"!==t,s=e.helpers||[],l=we(e,{mode:t,filename:r,sourceMap:o,breakLineCode:a,needIndent:i});l.push("normal"===t?"function __msg__ (ctx) {":"(ctx) => {"),l.indent(i),s.length>0&&(l.push(`const { ${s.map((e=>`${e}: _${e}`)).join(", ")} } = ctx`),l.newline()),l.push("return "),ve(l,e),l.deindent(i),l.push("}");const{code:c,map:d}=l.context();return{ast:e,code:c,map:d?d.toJSON():void 0}};function xe(e,n={}){const t=p({},n),r=de(t),o=r.parse(e);return he(o,t),_e(o,t)}
/*!
  * @intlify/devtools-if v9.1.9
  * (c) 2021 kazuya kawaguchi
  * Released under the MIT License.
  */
const Se={I18nInit:"i18n:init",FunctionTranslate:"function:translate"};
/*!
  * @intlify/core-base v9.1.9
  * (c) 2021 kazuya kawaguchi
  * Released under the MIT License.
  */
let Ce=null;Se.FunctionTranslate;function Ee(e){return n=>Ce&&Ce.emit(e,n)}const Pe="9.1.9",je=-1,Le="";function Oe(){return{upper:e=>g(e)?e.toUpperCase():e,lower:e=>g(e)?e.toLowerCase():e,capitalize:e=>g(e)?`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`:e}}let Te;function Me(e){Te=e}let Ae=0;function Fe(e={}){const n=g(e.version)?e.version:Pe,t=g(e.locale)?e.locale:"en-US",r=w(e.fallbackLocale)||x(e.fallbackLocale)||g(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,o=x(e.messages)?e.messages:{[t]:{}},a=x(e.datetimeFormats)?e.datetimeFormats:{[t]:{}},i=x(e.numberFormats)?e.numberFormats:{[t]:{}},s=p({},e.modifiers||{},Oe()),l=e.pluralRules||{},d=b(e.missing)?e.missing:null,m=!k(e.missingWarn)&&!c(e.missingWarn)||e.missingWarn,f=!k(e.fallbackWarn)&&!c(e.fallbackWarn)||e.fallbackWarn,h=!!e.fallbackFormat,v=!!e.unresolving,_=b(e.postTranslation)?e.postTranslation:null,S=x(e.processor)?e.processor:null,C=!k(e.warnHtmlMessage)||e.warnHtmlMessage,E=!!e.escapeParameter,P=b(e.messageCompiler)?e.messageCompiler:Te,j=b(e.onWarn)?e.onWarn:u,L=e,O=y(L.__datetimeFormatters)?L.__datetimeFormatters:new Map,T=y(L.__numberFormatters)?L.__numberFormatters:new Map,M=y(L.__meta)?L.__meta:{};Ae++;const A={version:n,cid:Ae,locale:t,fallbackLocale:r,messages:o,datetimeFormats:a,numberFormats:i,modifiers:s,pluralRules:l,missing:d,missingWarn:m,fallbackWarn:f,fallbackFormat:h,unresolving:v,postTranslation:_,processor:S,warnHtmlMessage:C,escapeParameter:E,messageCompiler:P,onWarn:j,__datetimeFormatters:O,__numberFormatters:T,__meta:M};return A}function ze(e,n,t,r,o){const{missing:a,onWarn:i}=e;if(null!==a){const r=a(e,t,n,o);return g(r)?r:n}return n}function $e(e,n,t){const r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let o=r.__localeChainCache.get(t);if(!o){o=[];let e=[t];while(w(e))e=Re(o,e,n);const a=w(n)?n:x(n)?n["default"]?n["default"]:null:n;e=g(a)?[a]:a,w(e)&&Re(o,e,!1),r.__localeChainCache.set(t,o)}return o}function Re(e,n,t){let r=!0;for(let o=0;o<n.length&&k(r);o++){const a=n[o];g(a)&&(r=Ie(e,n[o],t))}return r}function Ie(e,n,t){let r;const o=n.split("-");do{const n=o.join("-");r=Ne(e,n,t),o.splice(-1,1)}while(o.length&&!0===r);return r}function Ne(e,n,t){let r=!1;if(!e.includes(n)&&(r=!0,n)){r="!"!==n[n.length-1];const o=n.replace(/!/g,"");e.push(o),(w(t)||x(t))&&t[o]&&(r=t[o])}return r}function qe(e,n,t){const r=e;r.__localeChainCache=new Map,$e(e,t,n)}const He=e=>e;let Be=Object.create(null);function De(e,n={}){{const t=n.onCacheKey||He,r=t(e),o=Be[r];if(o)return o;let a=!1;const i=n.onError||J;n.onError=e=>{a=!0,i(e)};const{code:s}=xe(e,n),l=new Function(`return ${s}`)();return a?l:Be[r]=l}}function Ue(e){return G(e,null,void 0)}const We=()=>"",Ve=e=>b(e);function Ge(e,...n){const{fallbackFormat:t,postTranslation:r,unresolving:o,fallbackLocale:a,messages:i}=e,[s,l]=Xe(...n),c=k(l.missingWarn)?l.missingWarn:e.missingWarn,d=k(l.fallbackWarn)?l.fallbackWarn:e.fallbackWarn,u=k(l.escapeParameter)?l.escapeParameter:e.escapeParameter,p=!!l.resolvedMessage,m=g(l.default)||k(l.default)?k(l.default)?s:l.default:t?s:"",f=t||""!==m,h=g(l.locale)?l.locale:e.locale;u&&Je(l);let[w,b,y]=p?[s,h,i[h]||{}]:Ze(e,s,h,a,d,c),v=s;if(p||g(w)||Ve(w)||f&&(w=m,v=w),!p&&(!g(w)&&!Ve(w)||!g(b)))return o?je:s;let _=!1;const x=()=>{_=!0},S=Ve(w)?w:Ke(e,s,b,w,v,x);if(_)return w;const C=en(e,b,y,l),E=V(C),P=Ye(e,S,E),j=r?r(P):P;return j}function Je(e){w(e.list)?e.list=e.list.map((e=>g(e)?m(e):e)):y(e.named)&&Object.keys(e.named).forEach((n=>{g(e.named[n])&&(e.named[n]=m(e.named[n]))}))}function Ze(e,n,t,r,o,a){const{messages:i,onWarn:s}=e,l=$e(e,r,t);let c,d={},u=null,p=t,m=null;const f="translate";for(let h=0;h<l.length;h++){c=m=l[h],d=i[c]||{};if(null===(u=$(d,n))&&(u=d[n]),g(u)||b(u))break;const t=ze(e,n,c,a,f);t!==n&&(u=t),p=m}return[u,c,d]}function Ke(e,n,t,r,o,a){const{messageCompiler:i,warnHtmlMessage:s}=e;if(Ve(r)){const e=r;return e.locale=e.locale||t,e.key=e.key||n,e}const l=i(r,Qe(e,t,o,r,s,a));return l.locale=t,l.key=n,l.source=r,l}function Ye(e,n,t){const r=n(t);return r}function Xe(...e){const[n,t,r]=e,o={};if(!g(n)&&!s(n)&&!Ve(n))throw Ue(14);const a=s(n)?String(n):(Ve(n),n);return s(t)?o.plural=t:g(t)?o.default=t:x(t)&&!d(t)?o.named=t:w(t)&&(o.list=t),s(r)?o.plural=r:g(r)?o.default=r:x(r)&&p(o,r),[a,o]}function Qe(e,n,t,r,o,i){return{warnHtmlMessage:o,onError:e=>{throw i&&i(e),e},onCacheKey:e=>a(n,t,e)}}function en(e,n,t,r){const{modifiers:o,pluralRules:a}=e,i=r=>{const o=$(t,r);if(g(o)){let t=!1;const a=()=>{t=!0},i=Ke(e,r,n,o,r,a);return t?We:i}return Ve(o)?o:We},l={locale:n,modifiers:o,pluralRules:a,messages:i};return e.processor&&(l.processor=e.processor),r.list&&(l.list=r.list),r.named&&(l.named=r.named),s(r.plural)&&(l.pluralIndex=r.plural),l}const nn="undefined"!==typeof Intl;nn&&Intl.DateTimeFormat,nn&&Intl.NumberFormat;function tn(e,...n){const{datetimeFormats:t,unresolving:r,fallbackLocale:o,onWarn:a}=e,{__datetimeFormatters:i}=e;const[s,l,c,u]=rn(...n),m=k(c.missingWarn)?c.missingWarn:e.missingWarn,f=(k(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,!!c.part),h=g(c.locale)?c.locale:e.locale,w=$e(e,o,h);if(!g(s)||""===s)return new Intl.DateTimeFormat(h).format(l);let b,y={},v=null,_=h,S=null;const C="datetime format";for(let d=0;d<w.length;d++){if(b=S=w[d],y=t[b]||{},v=y[s],x(v))break;ze(e,s,b,m,C),_=S}if(!x(v)||!g(b))return r?je:s;let E=`${b}__${s}`;d(u)||(E=`${E}__${JSON.stringify(u)}`);let P=i.get(E);return P||(P=new Intl.DateTimeFormat(b,p({},v,u)),i.set(E,P)),f?P.formatToParts(l):P.format(l)}function rn(...e){const[n,t,r,o]=e;let a,i={},c={};if(g(n)){if(!/\d{4}-\d{2}-\d{2}(T.*)?/.test(n))throw Ue(16);a=new Date(n);try{a.toISOString()}catch(d){throw Ue(16)}}else if(l(n)){if(isNaN(n.getTime()))throw Ue(15);a=n}else{if(!s(n))throw Ue(14);a=n}return g(t)?i.key=t:x(t)&&(i=t),g(r)?i.locale=r:x(r)&&(c=r),x(o)&&(c=o),[i.key||"",a,i,c]}function on(e,n,t){const r=e;for(const o in t){const e=`${n}__${o}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}}function an(e,...n){const{numberFormats:t,unresolving:r,fallbackLocale:o,onWarn:a}=e,{__numberFormatters:i}=e;const[s,l,c,u]=sn(...n),m=k(c.missingWarn)?c.missingWarn:e.missingWarn,f=(k(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,!!c.part),h=g(c.locale)?c.locale:e.locale,w=$e(e,o,h);if(!g(s)||""===s)return new Intl.NumberFormat(h).format(l);let b,y={},v=null,_=h,S=null;const C="number format";for(let d=0;d<w.length;d++){if(b=S=w[d],y=t[b]||{},v=y[s],x(v))break;ze(e,s,b,m,C),_=S}if(!x(v)||!g(b))return r?je:s;let E=`${b}__${s}`;d(u)||(E=`${E}__${JSON.stringify(u)}`);let P=i.get(E);return P||(P=new Intl.NumberFormat(b,p({},v,u)),i.set(E,P)),f?P.formatToParts(l):P.format(l)}function sn(...e){const[n,t,r,o]=e;let a={},i={};if(!s(n))throw Ue(14);const l=n;return g(t)?a.key=t:x(t)&&(a=t),g(r)?a.locale=r:x(r)&&(i=r),x(o)&&(i=o),[a.key||"",l,a,i]}function ln(e,n,t){const r=e;for(const o in t){const e=`${n}__${o}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}}var cn=t(3673),dn=t(1959);
/*!
  * vue-i18n v9.1.9
  * (c) 2021 kazuya kawaguchi
  * Released under the MIT License.
  */
const un="9.1.9";function pn(){}function mn(e,...n){return G(e,null,void 0)}const fn=o("__transrateVNode"),hn=o("__datetimeParts"),wn=o("__numberParts"),bn=(o("__enableEmitter"),o("__disableEmitter"),o("__setPluralRules"));o("__intlifyMeta");const gn=o("__injectWithOption");let kn=0;function yn(e){return(n,t,r,o)=>e(t,r,(0,cn.FN)()||void 0,o)}function vn(e,n){const{messages:t,__i18n:r}=n,o=x(t)?t:w(r)?{}:{[e]:{}};if(w(r)&&r.forEach((({locale:e,resource:n})=>{e?(o[e]=o[e]||{},xn(n,o[e])):xn(n,o)})),n.flatJson)for(const a in o)h(o,a)&&R(o[a]);return o}const _n=e=>!y(e)||w(e);function xn(e,n){if(_n(e)||_n(n))throw mn(20);for(const t in e)h(e,t)&&(_n(e[t])||_n(n[t])?n[t]=e[t]:xn(e[t],n[t]))}function Sn(e={}){const{__root:n}=e,t=void 0===n;let r=!k(e.inheritLocale)||e.inheritLocale;const o=(0,dn.iH)(n&&r?n.locale.value:g(e.locale)?e.locale:"en-US"),a=(0,dn.iH)(n&&r?n.fallbackLocale.value:g(e.fallbackLocale)||w(e.fallbackLocale)||x(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:o.value),i=(0,dn.iH)(vn(o.value,e)),l=(0,dn.iH)(x(e.datetimeFormats)?e.datetimeFormats:{[o.value]:{}}),d=(0,dn.iH)(x(e.numberFormats)?e.numberFormats:{[o.value]:{}});let u=n?n.missingWarn:!k(e.missingWarn)&&!c(e.missingWarn)||e.missingWarn,m=n?n.fallbackWarn:!k(e.fallbackWarn)&&!c(e.fallbackWarn)||e.fallbackWarn,f=n?n.fallbackRoot:!k(e.fallbackRoot)||e.fallbackRoot,h=!!e.fallbackFormat,v=b(e.missing)?e.missing:null,_=b(e.missing)?yn(e.missing):null,S=b(e.postTranslation)?e.postTranslation:null,C=!k(e.warnHtmlMessage)||e.warnHtmlMessage,E=!!e.escapeParameter;const P=n?n.modifiers:x(e.modifiers)?e.modifiers:{};let j,L=e.pluralRules||n&&n.pluralRules;function O(){return Fe({version:un,locale:o.value,fallbackLocale:a.value,messages:i.value,datetimeFormats:l.value,numberFormats:d.value,modifiers:P,pluralRules:L,missing:null===_?void 0:_,missingWarn:u,fallbackWarn:m,fallbackFormat:h,unresolving:!0,postTranslation:null===S?void 0:S,warnHtmlMessage:C,escapeParameter:E,__datetimeFormatters:x(j)?j.__datetimeFormatters:void 0,__numberFormatters:x(j)?j.__numberFormatters:void 0,__v_emitter:x(j)?j.__v_emitter:void 0,__meta:{framework:"vue"}})}function T(){return[o.value,a.value,i.value,l.value,d.value]}j=O(),qe(j,o.value,a.value);const M=(0,dn.Fl)({get:()=>o.value,set:e=>{o.value=e,j.locale=o.value}}),A=(0,dn.Fl)({get:()=>a.value,set:e=>{a.value=e,j.fallbackLocale=a.value,qe(j,o.value,e)}}),F=(0,dn.Fl)((()=>i.value)),z=(0,dn.Fl)((()=>l.value)),R=(0,dn.Fl)((()=>d.value));function I(){return b(S)?S:null}function N(e){S=e,j.postTranslation=e}function q(){return v}function H(e){null!==e&&(_=yn(e)),v=e,j.missing=_}function B(e,t,r,o,a,i){let l;if(T(),l=e(j),s(l)&&l===je){const[e,r]=t();return n&&f?o(n):a(e)}if(i(l))return l;throw mn(14)}function D(...e){return B((n=>Ge(n,...e)),(()=>Xe(...e)),"translate",(n=>n.t(...e)),(e=>e),(e=>g(e)))}function U(...e){const[n,t,r]=e;if(r&&!y(r))throw mn(15);return D(n,t,p({resolvedMessage:!0},r||{}))}function W(...e){return B((n=>tn(n,...e)),(()=>rn(...e)),"datetime format",(n=>n.d(...e)),(()=>Le),(e=>g(e)))}function V(...e){return B((n=>an(n,...e)),(()=>sn(...e)),"number format",(n=>n.n(...e)),(()=>Le),(e=>g(e)))}function G(e){return e.map((e=>g(e)?(0,cn.Wm)(cn.xv,null,e,0):e))}const J=e=>e,Z={normalize:G,interpolate:J,type:"vnode"};function K(...e){return B((n=>{let t;const r=n;try{r.processor=Z,t=Ge(r,...e)}finally{r.processor=null}return t}),(()=>Xe(...e)),"translate",(n=>n[fn](...e)),(e=>[(0,cn.Wm)(cn.xv,null,e,0)]),(e=>w(e)))}function Y(...e){return B((n=>an(n,...e)),(()=>sn(...e)),"number format",(n=>n[wn](...e)),(()=>[]),(e=>g(e)||w(e)))}function X(...e){return B((n=>tn(n,...e)),(()=>rn(...e)),"datetime format",(n=>n[hn](...e)),(()=>[]),(e=>g(e)||w(e)))}function Q(e){L=e,j.pluralRules=L}function ee(e,n){const t=g(n)?n:o.value,r=re(t);return null!==$(r,e)}function ne(e){let n=null;const t=$e(j,a.value,o.value);for(let r=0;r<t.length;r++){const o=i.value[t[r]]||{},a=$(o,e);if(null!=a){n=a;break}}return n}function te(e){const t=ne(e);return null!=t?t:n&&n.tm(e)||{}}function re(e){return i.value[e]||{}}function oe(e,n){i.value[e]=n,j.messages=i.value}function ae(e,n){i.value[e]=i.value[e]||{},xn(n,i.value[e]),j.messages=i.value}function ie(e){return l.value[e]||{}}function se(e,n){l.value[e]=n,j.datetimeFormats=l.value,on(j,e,n)}function le(e,n){l.value[e]=p(l.value[e]||{},n),j.datetimeFormats=l.value,on(j,e,n)}function ce(e){return d.value[e]||{}}function de(e,n){d.value[e]=n,j.numberFormats=d.value,ln(j,e,n)}function ue(e,n){d.value[e]=p(d.value[e]||{},n),j.numberFormats=d.value,ln(j,e,n)}kn++,n&&((0,cn.YP)(n.locale,(e=>{r&&(o.value=e,j.locale=e,qe(j,o.value,a.value))})),(0,cn.YP)(n.fallbackLocale,(e=>{r&&(a.value=e,j.fallbackLocale=e,qe(j,o.value,a.value))})));const pe={id:kn,locale:M,fallbackLocale:A,get inheritLocale(){return r},set inheritLocale(e){r=e,e&&n&&(o.value=n.locale.value,a.value=n.fallbackLocale.value,qe(j,o.value,a.value))},get availableLocales(){return Object.keys(i.value).sort()},messages:F,datetimeFormats:z,numberFormats:R,get modifiers(){return P},get pluralRules(){return L||{}},get isGlobal(){return t},get missingWarn(){return u},set missingWarn(e){u=e,j.missingWarn=u},get fallbackWarn(){return m},set fallbackWarn(e){m=e,j.fallbackWarn=m},get fallbackRoot(){return f},set fallbackRoot(e){f=e},get fallbackFormat(){return h},set fallbackFormat(e){h=e,j.fallbackFormat=h},get warnHtmlMessage(){return C},set warnHtmlMessage(e){C=e,j.warnHtmlMessage=e},get escapeParameter(){return E},set escapeParameter(e){E=e,j.escapeParameter=e},t:D,rt:U,d:W,n:V,te:ee,tm:te,getLocaleMessage:re,setLocaleMessage:oe,mergeLocaleMessage:ae,getDateTimeFormat:ie,setDateTimeFormat:se,mergeDateTimeFormat:le,getNumberFormat:ce,setNumberFormat:de,mergeNumberFormat:ue,getPostTranslationHandler:I,setPostTranslationHandler:N,getMissingHandler:q,setMissingHandler:H,[fn]:K,[wn]:Y,[hn]:X,[bn]:Q,[gn]:e.__injectWithOption};return pe}function Cn(e){const n=g(e.locale)?e.locale:"en-US",t=g(e.fallbackLocale)||w(e.fallbackLocale)||x(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:n,r=b(e.missing)?e.missing:void 0,o=!k(e.silentTranslationWarn)&&!c(e.silentTranslationWarn)||!e.silentTranslationWarn,a=!k(e.silentFallbackWarn)&&!c(e.silentFallbackWarn)||!e.silentFallbackWarn,i=!k(e.fallbackRoot)||e.fallbackRoot,s=!!e.formatFallbackMessages,l=x(e.modifiers)?e.modifiers:{},d=e.pluralizationRules,u=b(e.postTranslation)?e.postTranslation:void 0,m=!g(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,f=!!e.escapeParameterHtml,h=!k(e.sync)||e.sync;let y=e.messages;if(x(e.sharedMessages)){const n=e.sharedMessages,t=Object.keys(n);y=t.reduce(((e,t)=>{const r=e[t]||(e[t]={});return p(r,n[t]),e}),y||{})}const{__i18n:v,__root:_,__injectWithOption:S}=e,C=e.datetimeFormats,E=e.numberFormats,P=e.flatJson;return{locale:n,fallbackLocale:t,messages:y,flatJson:P,datetimeFormats:C,numberFormats:E,missing:r,missingWarn:o,fallbackWarn:a,fallbackRoot:i,fallbackFormat:s,modifiers:l,pluralRules:d,postTranslation:u,warnHtmlMessage:m,escapeParameter:f,inheritLocale:h,__i18n:v,__root:_,__injectWithOption:S}}function En(e={}){const n=Sn(Cn(e)),t={id:n.id,get locale(){return n.locale.value},set locale(e){n.locale.value=e},get fallbackLocale(){return n.fallbackLocale.value},set fallbackLocale(e){n.fallbackLocale.value=e},get messages(){return n.messages.value},get datetimeFormats(){return n.datetimeFormats.value},get numberFormats(){return n.numberFormats.value},get availableLocales(){return n.availableLocales},get formatter(){return{interpolate(){return[]}}},set formatter(e){},get missing(){return n.getMissingHandler()},set missing(e){n.setMissingHandler(e)},get silentTranslationWarn(){return k(n.missingWarn)?!n.missingWarn:n.missingWarn},set silentTranslationWarn(e){n.missingWarn=k(e)?!e:e},get silentFallbackWarn(){return k(n.fallbackWarn)?!n.fallbackWarn:n.fallbackWarn},set silentFallbackWarn(e){n.fallbackWarn=k(e)?!e:e},get modifiers(){return n.modifiers},get formatFallbackMessages(){return n.fallbackFormat},set formatFallbackMessages(e){n.fallbackFormat=e},get postTranslation(){return n.getPostTranslationHandler()},set postTranslation(e){n.setPostTranslationHandler(e)},get sync(){return n.inheritLocale},set sync(e){n.inheritLocale=e},get warnHtmlInMessage(){return n.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){n.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return n.escapeParameter},set escapeParameterHtml(e){n.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return n.pluralRules||{}},__composer:n,t(...e){const[t,r,o]=e,a={};let i=null,s=null;if(!g(t))throw mn(15);const l=t;return g(r)?a.locale=r:w(r)?i=r:x(r)&&(s=r),w(o)?i=o:x(o)&&(s=o),n.t(l,i||s||{},a)},rt(...e){return n.rt(...e)},tc(...e){const[t,r,o]=e,a={plural:1};let i=null,l=null;if(!g(t))throw mn(15);const c=t;return g(r)?a.locale=r:s(r)?a.plural=r:w(r)?i=r:x(r)&&(l=r),g(o)?a.locale=o:w(o)?i=o:x(o)&&(l=o),n.t(c,i||l||{},a)},te(e,t){return n.te(e,t)},tm(e){return n.tm(e)},getLocaleMessage(e){return n.getLocaleMessage(e)},setLocaleMessage(e,t){n.setLocaleMessage(e,t)},mergeLocaleMessage(e,t){n.mergeLocaleMessage(e,t)},d(...e){return n.d(...e)},getDateTimeFormat(e){return n.getDateTimeFormat(e)},setDateTimeFormat(e,t){n.setDateTimeFormat(e,t)},mergeDateTimeFormat(e,t){n.mergeDateTimeFormat(e,t)},n(...e){return n.n(...e)},getNumberFormat(e){return n.getNumberFormat(e)},setNumberFormat(e,t){n.setNumberFormat(e,t)},mergeNumberFormat(e,t){n.mergeNumberFormat(e,t)},getChoiceIndex(e,n){return-1},__onComponentInstanceCreated(n){const{componentInstanceCreatedListener:r}=e;r&&r(n,t)}};return t}const Pn={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}},jn={name:"i18n-t",props:p({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>s(e)||!isNaN(e)}},Pn),setup(e,n){const{slots:t,attrs:r}=n,o=e.i18n||Dn({useScope:e.scope,__useComponent:!0}),a=Object.keys(t).filter((e=>"_"!==e));return()=>{const t={};e.locale&&(t.locale=e.locale),void 0!==e.plural&&(t.plural=g(e.plural)?+e.plural:e.plural);const i=Ln(n,a),s=o[fn](e.keypath,i,t),l=p({},r);return g(e.tag)||y(e.tag)?(0,cn.h)(e.tag,l,s):(0,cn.h)(cn.HY,l,s)}}};function Ln({slots:e},n){return 1===n.length&&"default"===n[0]?e.default?e.default():[]:n.reduce(((n,t)=>{const r=e[t];return r&&(n[t]=r()),n}),{})}function On(e,n,t,r){const{slots:o,attrs:a}=n;return()=>{const n={part:!0};let i={};e.locale&&(n.locale=e.locale),g(e.format)?n.key=e.format:y(e.format)&&(g(e.format.key)&&(n.key=e.format.key),i=Object.keys(e.format).reduce(((n,r)=>t.includes(r)?p({},n,{[r]:e.format[r]}):n),{}));const s=r(e.value,n,i);let l=[n.key];w(s)?l=s.map(((e,n)=>{const t=o[e.type];return t?t({[e.type]:e.value,index:n,parts:s}):[e.value]})):g(s)&&(l=[s]);const c=p({},a);return g(e.tag)||y(e.tag)?(0,cn.h)(e.tag,c,l):(0,cn.h)(cn.HY,c,l)}}const Tn=["localeMatcher","style","unit","unitDisplay","currency","currencyDisplay","useGrouping","numberingSystem","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","notation","formatMatcher"],Mn={name:"i18n-n",props:p({value:{type:Number,required:!0},format:{type:[String,Object]}},Pn),setup(e,n){const t=e.i18n||Dn({useScope:"parent",__useComponent:!0});return On(e,n,Tn,((...e)=>t[wn](...e)))}},An=["dateStyle","timeStyle","fractionalSecondDigits","calendar","dayPeriod","numberingSystem","localeMatcher","timeZone","hour12","hourCycle","formatMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName"],Fn={name:"i18n-d",props:p({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Pn),setup(e,n){const t=e.i18n||Dn({useScope:"parent",__useComponent:!0});return On(e,n,An,((...e)=>t[hn](...e)))}};function zn(e,n){const t=e;if("composition"===e.mode)return t.__getInstance(n)||e.global;{const r=t.__getInstance(n);return null!=r?r.__composer:e.global.__composer}}function $n(e){const n=(n,{instance:t,value:r,modifiers:o})=>{if(!t||!t.$)throw mn(22);const a=zn(e,t.$);const i=Rn(r);n.textContent=a.t(...In(i))};return{beforeMount:n,beforeUpdate:n}}function Rn(e){if(g(e))return{path:e};if(x(e)){if(!("path"in e))throw mn(19,"path");return e}throw mn(20)}function In(e){const{path:n,locale:t,args:r,choice:o,plural:a}=e,i={},l=r||{};return g(t)&&(i.locale=t),s(o)&&(i.plural=o),s(a)&&(i.plural=a),[n,l,i]}function Nn(e,n,...t){const r=x(t[0])?t[0]:{},o=!!r.useI18nComponentName,a=!k(r.globalInstall)||r.globalInstall;a&&(e.component(o?"i18n":jn.name,jn),e.component(Mn.name,Mn),e.component(Fn.name,Fn)),e.directive("t",$n(n))}function qn(e,n,t){return{beforeCreate(){const r=(0,cn.FN)();if(!r)throw mn(22);const o=this.$options;if(o.i18n){const t=o.i18n;o.__i18n&&(t.__i18n=o.__i18n),t.__root=n,this===this.$root?this.$i18n=Hn(e,t):(t.__injectWithOption=!0,this.$i18n=En(t))}else o.__i18n?this===this.$root?this.$i18n=Hn(e,o):this.$i18n=En({__i18n:o.__i18n,__injectWithOption:!0,__root:n}):this.$i18n=e;e.__onComponentInstanceCreated(this.$i18n),t.__setInstance(r,this.$i18n),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,n)=>this.$i18n.te(e,n),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e)},mounted(){0},beforeUnmount(){const e=(0,cn.FN)();if(!e)throw mn(22);delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,t.__deleteInstance(e),delete this.$i18n}}}function Hn(e,n){e.locale=n.locale||e.locale,e.fallbackLocale=n.fallbackLocale||e.fallbackLocale,e.missing=n.missing||e.missing,e.silentTranslationWarn=n.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=n.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=n.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=n.postTranslation||e.postTranslation,e.warnHtmlInMessage=n.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=n.escapeParameterHtml||e.escapeParameterHtml,e.sync=n.sync||e.sync,e.__composer[bn](n.pluralizationRules||e.pluralizationRules);const t=vn(e.locale,{messages:n.messages,__i18n:n.__i18n});return Object.keys(t).forEach((n=>e.mergeLocaleMessage(n,t[n]))),n.datetimeFormats&&Object.keys(n.datetimeFormats).forEach((t=>e.mergeDateTimeFormat(t,n.datetimeFormats[t]))),n.numberFormats&&Object.keys(n.numberFormats).forEach((t=>e.mergeNumberFormat(t,n.numberFormats[t]))),e}function Bn(e={}){const n=!k(e.legacy)||e.legacy,t=!!e.globalInjection,r=new Map,a=n?En(e):Sn(e),i=o(""),s={get mode(){return n?"legacy":"composition"},async install(e,...r){e.__VUE_I18N_SYMBOL__=i,e.provide(e.__VUE_I18N_SYMBOL__,s),!n&&t&&Jn(e,s.global),Nn(e,s,...r),n&&e.mixin(qn(a,a.__composer,s))},get global(){return a},__instances:r,__getInstance(e){return r.get(e)||null},__setInstance(e,n){r.set(e,n)},__deleteInstance(e){r.delete(e)}};return s}function Dn(e={}){const n=(0,cn.FN)();if(null==n)throw mn(16);if(!n.appContext.app.__VUE_I18N_SYMBOL__)throw mn(17);const t=(0,cn.f3)(n.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw mn(22);const r="composition"===t.mode?t.global:t.global.__composer,o=d(e)?"__i18n"in n.type?"local":"global":e.useScope?e.useScope:"local";if("global"===o){let t=y(e.messages)?e.messages:{};"__i18nGlobal"in n.type&&(t=vn(r.locale.value,{messages:t,__i18n:n.type.__i18nGlobal}));const o=Object.keys(t);if(o.length&&o.forEach((e=>{r.mergeLocaleMessage(e,t[e])})),y(e.datetimeFormats)){const n=Object.keys(e.datetimeFormats);n.length&&n.forEach((n=>{r.mergeDateTimeFormat(n,e.datetimeFormats[n])}))}if(y(e.numberFormats)){const n=Object.keys(e.numberFormats);n.length&&n.forEach((n=>{r.mergeNumberFormat(n,e.numberFormats[n])}))}return r}if("parent"===o){let o=Un(t,n,e.__useComponent);return null==o&&(o=r),o}if("legacy"===t.mode)throw mn(18);const a=t;let i=a.__getInstance(n);if(null==i){const t=n.type,o=p({},e);t.__i18n&&(o.__i18n=t.__i18n),r&&(o.__root=r),i=Sn(o),Wn(a,n,i),a.__setInstance(n,i)}return i}function Un(e,n,t=!1){let r=null;const o=n.root;let a=n.parent;while(null!=a){const n=e;if("composition"===e.mode)r=n.__getInstance(a);else{const e=n.__getInstance(a);null!=e&&(r=e.__composer),t&&r&&!r[gn]&&(r=null)}if(null!=r)break;if(o===a)break;a=a.parent}return r}function Wn(e,n,t){(0,cn.bv)((()=>{0}),n),(0,cn.Ah)((()=>{e.__deleteInstance(n)}),n)}const Vn=["locale","fallbackLocale","availableLocales"],Gn=["t","rt","d","n","tm"];function Jn(e,n){const t=Object.create(null);Vn.forEach((e=>{const r=Object.getOwnPropertyDescriptor(n,e);if(!r)throw mn(22);const o=(0,dn.dq)(r.value)?{get(){return r.value.value},set(e){r.value.value=e}}:{get(){return r.get&&r.get()}};Object.defineProperty(t,e,o)})),e.config.globalProperties.$i18n=t,Gn.forEach((t=>{const r=Object.getOwnPropertyDescriptor(n,t);if(!r||!r.value)throw mn(22);Object.defineProperty(e.config.globalProperties,`$${t}`,r)}))}Me(De),pn()},4260:(e,n)=>{"use strict";n.Z=(e,n)=>{for(const[t,r]of n)e[t]=r;return e}},9582:(e,n,t)=>{"use strict";t.d(n,{p7:()=>nn,r5:()=>V});var r=t(3673),o=t(1959);
/*!
  * vue-router v4.0.12
  * (c) 2021 Eduardo San Martin Morote
  * @license MIT
  */
const a="function"===typeof Symbol&&"symbol"===typeof Symbol.toStringTag,i=e=>a?Symbol(e):"_vr_"+e,s=i("rvlm"),l=i("rvd"),c=i("r"),d=i("rl"),u=i("rvl"),p="undefined"!==typeof window;function m(e){return e.__esModule||a&&"Module"===e[Symbol.toStringTag]}const f=Object.assign;function h(e,n){const t={};for(const r in n){const o=n[r];t[r]=Array.isArray(o)?o.map(e):e(o)}return t}const w=()=>{};const b=/\/$/,g=e=>e.replace(b,"");function k(e,n,t="/"){let r,o={},a="",i="";const s=n.indexOf("?"),l=n.indexOf("#",s>-1?s:0);return s>-1&&(r=n.slice(0,s),a=n.slice(s+1,l>-1?l:n.length),o=e(a)),l>-1&&(r=r||n.slice(0,l),i=n.slice(l,n.length)),r=P(null!=r?r:n,t),{fullPath:r+(a&&"?")+a+i,path:r,query:o,hash:i}}function y(e,n){const t=n.query?e(n.query):"";return n.path+(t&&"?")+t+(n.hash||"")}function v(e,n){return n&&e.toLowerCase().startsWith(n.toLowerCase())?e.slice(n.length)||"/":e}function _(e,n,t){const r=n.matched.length-1,o=t.matched.length-1;return r>-1&&r===o&&x(n.matched[r],t.matched[o])&&S(n.params,t.params)&&e(n.query)===e(t.query)&&n.hash===t.hash}function x(e,n){return(e.aliasOf||e)===(n.aliasOf||n)}function S(e,n){if(Object.keys(e).length!==Object.keys(n).length)return!1;for(const t in e)if(!C(e[t],n[t]))return!1;return!0}function C(e,n){return Array.isArray(e)?E(e,n):Array.isArray(n)?E(n,e):e===n}function E(e,n){return Array.isArray(n)?e.length===n.length&&e.every(((e,t)=>e===n[t])):1===e.length&&e[0]===n}function P(e,n){if(e.startsWith("/"))return e;if(!e)return n;const t=n.split("/"),r=e.split("/");let o,a,i=t.length-1;for(o=0;o<r.length;o++)if(a=r[o],1!==i&&"."!==a){if(".."!==a)break;i--}return t.slice(0,i).join("/")+"/"+r.slice(o-(o===r.length?1:0)).join("/")}var j,L;(function(e){e["pop"]="pop",e["push"]="push"})(j||(j={})),function(e){e["back"]="back",e["forward"]="forward",e["unknown"]=""}(L||(L={}));function O(e){if(!e)if(p){const n=document.querySelector("base");e=n&&n.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),g(e)}const T=/^[^#]+#/;function M(e,n){return e.replace(T,"#")+n}function A(e,n){const t=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:n.behavior,left:r.left-t.left-(n.left||0),top:r.top-t.top-(n.top||0)}}const F=()=>({left:window.pageXOffset,top:window.pageYOffset});function z(e){let n;if("el"in e){const t=e.el,r="string"===typeof t&&t.startsWith("#");0;const o="string"===typeof t?r?document.getElementById(t.slice(1)):document.querySelector(t):t;if(!o)return;n=A(o,e)}else n=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(n):window.scrollTo(null!=n.left?n.left:window.pageXOffset,null!=n.top?n.top:window.pageYOffset)}function $(e,n){const t=history.state?history.state.position-n:-1;return t+e}const R=new Map;function I(e,n){R.set(e,n)}function N(e){const n=R.get(e);return R.delete(e),n}let q=()=>location.protocol+"//"+location.host;function H(e,n){const{pathname:t,search:r,hash:o}=n,a=e.indexOf("#");if(a>-1){let n=o.includes(e.slice(a))?e.slice(a).length:1,t=o.slice(n);return"/"!==t[0]&&(t="/"+t),v(t,"")}const i=v(t,e);return i+r+o}function B(e,n,t,r){let o=[],a=[],i=null;const s=({state:a})=>{const s=H(e,location),l=t.value,c=n.value;let d=0;if(a){if(t.value=s,n.value=a,i&&i===l)return void(i=null);d=c?a.position-c.position:0}else r(s);o.forEach((e=>{e(t.value,l,{delta:d,type:j.pop,direction:d?d>0?L.forward:L.back:L.unknown})}))};function l(){i=t.value}function c(e){o.push(e);const n=()=>{const n=o.indexOf(e);n>-1&&o.splice(n,1)};return a.push(n),n}function d(){const{history:e}=window;e.state&&e.replaceState(f({},e.state,{scroll:F()}),"")}function u(){for(const e of a)e();a=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",d)}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",d),{pauseListeners:l,listen:c,destroy:u}}function D(e,n,t,r=!1,o=!1){return{back:e,current:n,forward:t,replaced:r,position:window.history.length,scroll:o?F():null}}function U(e){const{history:n,location:t}=window,r={value:H(e,t)},o={value:n.state};function a(r,a,i){const s=e.indexOf("#"),l=s>-1?(t.host&&document.querySelector("base")?e:e.slice(s))+r:q()+e+r;try{n[i?"replaceState":"pushState"](a,"",l),o.value=a}catch(c){console.error(c),t[i?"replace":"assign"](l)}}function i(e,t){const i=f({},n.state,D(o.value.back,e,o.value.forward,!0),t,{position:o.value.position});a(e,i,!0),r.value=e}function s(e,t){const i=f({},o.value,n.state,{forward:e,scroll:F()});a(i.current,i,!0);const s=f({},D(r.value,e,null),{position:i.position+1},t);a(e,s,!1),r.value=e}return o.value||a(r.value,{back:null,current:r.value,forward:null,position:n.length-1,replaced:!0,scroll:null},!0),{location:r,state:o,push:s,replace:i}}function W(e){e=O(e);const n=U(e),t=B(e,n.state,n.location,n.replace);function r(e,n=!0){n||t.pauseListeners(),history.go(e)}const o=f({location:"",base:e,go:r,createHref:M.bind(null,e)},n,t);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>n.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>n.state.value}),o}function V(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),W(e)}function G(e){return"string"===typeof e||e&&"object"===typeof e}function J(e){return"string"===typeof e||"symbol"===typeof e}const Z={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},K=i("nf");var Y;(function(e){e[e["aborted"]=4]="aborted",e[e["cancelled"]=8]="cancelled",e[e["duplicated"]=16]="duplicated"})(Y||(Y={}));function X(e,n){return f(new Error,{type:e,[K]:!0},n)}function Q(e,n){return e instanceof Error&&K in e&&(null==n||!!(e.type&n))}const ee="[^/]+?",ne={sensitive:!1,strict:!1,start:!0,end:!0},te=/[.+*?^${}()[\]/\\]/g;function re(e,n){const t=f({},ne,n),r=[];let o=t.start?"^":"";const a=[];for(const d of e){const e=d.length?[]:[90];t.strict&&!d.length&&(o+="/");for(let n=0;n<d.length;n++){const r=d[n];let i=40+(t.sensitive?.25:0);if(0===r.type)n||(o+="/"),o+=r.value.replace(te,"\\$&"),i+=40;else if(1===r.type){const{value:e,repeatable:t,optional:s,regexp:l}=r;a.push({name:e,repeatable:t,optional:s});const u=l||ee;if(u!==ee){i+=10;try{new RegExp(`(${u})`)}catch(c){throw new Error(`Invalid custom RegExp for param "${e}" (${u}): `+c.message)}}let p=t?`((?:${u})(?:/(?:${u}))*)`:`(${u})`;n||(p=s&&d.length<2?`(?:/${p})`:"/"+p),s&&(p+="?"),o+=p,i+=20,s&&(i+=-8),t&&(i+=-20),".*"===u&&(i+=-50)}e.push(i)}r.push(e)}if(t.strict&&t.end){const e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}t.strict||(o+="/?"),t.end?o+="$":t.strict&&(o+="(?:/|$)");const i=new RegExp(o,t.sensitive?"":"i");function s(e){const n=e.match(i),t={};if(!n)return null;for(let r=1;r<n.length;r++){const e=n[r]||"",o=a[r-1];t[o.name]=e&&o.repeatable?e.split("/"):e}return t}function l(n){let t="",r=!1;for(const o of e){r&&t.endsWith("/")||(t+="/"),r=!1;for(const e of o)if(0===e.type)t+=e.value;else if(1===e.type){const{value:a,repeatable:i,optional:s}=e,l=a in n?n[a]:"";if(Array.isArray(l)&&!i)throw new Error(`Provided param "${a}" is an array but it is not repeatable (* or + modifiers)`);const c=Array.isArray(l)?l.join("/"):l;if(!c){if(!s)throw new Error(`Missing required param "${a}"`);o.length<2&&(t.endsWith("/")?t=t.slice(0,-1):r=!0)}t+=c}}return t}return{re:i,score:r,keys:a,parse:s,stringify:l}}function oe(e,n){let t=0;while(t<e.length&&t<n.length){const r=n[t]-e[t];if(r)return r;t++}return e.length<n.length?1===e.length&&80===e[0]?-1:1:e.length>n.length?1===n.length&&80===n[0]?1:-1:0}function ae(e,n){let t=0;const r=e.score,o=n.score;while(t<r.length&&t<o.length){const e=oe(r[t],o[t]);if(e)return e;t++}return o.length-r.length}const ie={type:0,value:""},se=/[a-zA-Z0-9_]/;function le(e){if(!e)return[[]];if("/"===e)return[[ie]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function n(e){throw new Error(`ERR (${t})/"${c}": ${e}`)}let t=0,r=t;const o=[];let a;function i(){a&&o.push(a),a=[]}let s,l=0,c="",d="";function u(){c&&(0===t?a.push({type:0,value:c}):1===t||2===t||3===t?(a.length>1&&("*"===s||"+"===s)&&n(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:c,regexp:d,repeatable:"*"===s||"+"===s,optional:"*"===s||"?"===s})):n("Invalid state to consume buffer"),c="")}function p(){c+=s}while(l<e.length)if(s=e[l++],"\\"!==s||2===t)switch(t){case 0:"/"===s?(c&&u(),i()):":"===s?(u(),t=1):p();break;case 4:p(),t=r;break;case 1:"("===s?t=2:se.test(s)?p():(u(),t=0,"*"!==s&&"?"!==s&&"+"!==s&&l--);break;case 2:")"===s?"\\"==d[d.length-1]?d=d.slice(0,-1)+s:t=3:d+=s;break;case 3:u(),t=0,"*"!==s&&"?"!==s&&"+"!==s&&l--,d="";break;default:n("Unknown state");break}else r=t,t=4;return 2===t&&n(`Unfinished custom RegExp for param "${c}"`),u(),i(),o}function ce(e,n,t){const r=re(le(e.path),t);const o=f(r,{record:e,parent:n,children:[],alias:[]});return n&&!o.record.aliasOf===!n.record.aliasOf&&n.children.push(o),o}function de(e,n){const t=[],r=new Map;function o(e){return r.get(e)}function a(e,t,r){const o=!r,s=pe(e);s.aliasOf=r&&r.record;const c=we(n,e),d=[s];if("alias"in e){const n="string"===typeof e.alias?[e.alias]:e.alias;for(const e of n)d.push(f({},s,{components:r?r.record.components:s.components,path:e,aliasOf:r?r.record:s}))}let u,p;for(const n of d){const{path:d}=n;if(t&&"/"!==d[0]){const e=t.record.path,r="/"===e[e.length-1]?"":"/";n.path=t.record.path+(d&&r+d)}if(u=ce(n,t,c),r?r.alias.push(u):(p=p||u,p!==u&&p.alias.push(u),o&&e.name&&!fe(u)&&i(e.name)),"children"in s){const e=s.children;for(let n=0;n<e.length;n++)a(e[n],u,r&&r.children[n])}r=r||u,l(u)}return p?()=>{i(p)}:w}function i(e){if(J(e)){const n=r.get(e);n&&(r.delete(e),t.splice(t.indexOf(n),1),n.children.forEach(i),n.alias.forEach(i))}else{const n=t.indexOf(e);n>-1&&(t.splice(n,1),e.record.name&&r.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function s(){return t}function l(e){let n=0;while(n<t.length&&ae(e,t[n])>=0)n++;t.splice(n,0,e),e.record.name&&!fe(e)&&r.set(e.record.name,e)}function c(e,n){let o,a,i,s={};if("name"in e&&e.name){if(o=r.get(e.name),!o)throw X(1,{location:e});i=o.record.name,s=f(ue(n.params,o.keys.filter((e=>!e.optional)).map((e=>e.name))),e.params),a=o.stringify(s)}else if("path"in e)a=e.path,o=t.find((e=>e.re.test(a))),o&&(s=o.parse(a),i=o.record.name);else{if(o=n.name?r.get(n.name):t.find((e=>e.re.test(n.path))),!o)throw X(1,{location:e,currentLocation:n});i=o.record.name,s=f({},n.params,e.params),a=o.stringify(s)}const l=[];let c=o;while(c)l.unshift(c.record),c=c.parent;return{name:i,path:a,params:s,matched:l,meta:he(l)}}return n=we({strict:!1,end:!0,sensitive:!1},n),e.forEach((e=>a(e))),{addRoute:a,resolve:c,removeRoute:i,getRoutes:s,getRecordMatcher:o}}function ue(e,n){const t={};for(const r of n)r in e&&(t[r]=e[r]);return t}function pe(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:me(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||{}:{default:e.component}}}function me(e){const n={},t=e.props||!1;if("component"in e)n.default=t;else for(const r in e.components)n[r]="boolean"===typeof t?t:t[r];return n}function fe(e){while(e){if(e.record.aliasOf)return!0;e=e.parent}return!1}function he(e){return e.reduce(((e,n)=>f(e,n.meta)),{})}function we(e,n){const t={};for(const r in e)t[r]=r in n?n[r]:e[r];return t}const be=/#/g,ge=/&/g,ke=/\//g,ye=/=/g,ve=/\?/g,_e=/\+/g,xe=/%5B/g,Se=/%5D/g,Ce=/%5E/g,Ee=/%60/g,Pe=/%7B/g,je=/%7C/g,Le=/%7D/g,Oe=/%20/g;function Te(e){return encodeURI(""+e).replace(je,"|").replace(xe,"[").replace(Se,"]")}function Me(e){return Te(e).replace(Pe,"{").replace(Le,"}").replace(Ce,"^")}function Ae(e){return Te(e).replace(_e,"%2B").replace(Oe,"+").replace(be,"%23").replace(ge,"%26").replace(Ee,"`").replace(Pe,"{").replace(Le,"}").replace(Ce,"^")}function Fe(e){return Ae(e).replace(ye,"%3D")}function ze(e){return Te(e).replace(be,"%23").replace(ve,"%3F")}function $e(e){return null==e?"":ze(e).replace(ke,"%2F")}function Re(e){try{return decodeURIComponent(""+e)}catch(n){}return""+e}function Ie(e){const n={};if(""===e||"?"===e)return n;const t="?"===e[0],r=(t?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const e=r[o].replace(_e," "),t=e.indexOf("="),a=Re(t<0?e:e.slice(0,t)),i=t<0?null:Re(e.slice(t+1));if(a in n){let e=n[a];Array.isArray(e)||(e=n[a]=[e]),e.push(i)}else n[a]=i}return n}function Ne(e){let n="";for(let t in e){const r=e[t];if(t=Fe(t),null==r){void 0!==r&&(n+=(n.length?"&":"")+t);continue}const o=Array.isArray(r)?r.map((e=>e&&Ae(e))):[r&&Ae(r)];o.forEach((e=>{void 0!==e&&(n+=(n.length?"&":"")+t,null!=e&&(n+="="+e))}))}return n}function qe(e){const n={};for(const t in e){const r=e[t];void 0!==r&&(n[t]=Array.isArray(r)?r.map((e=>null==e?null:""+e)):null==r?r:""+r)}return n}function He(){let e=[];function n(n){return e.push(n),()=>{const t=e.indexOf(n);t>-1&&e.splice(t,1)}}function t(){e=[]}return{add:n,list:()=>e,reset:t}}function Be(e,n,t,r,o){const a=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise(((i,s)=>{const l=e=>{!1===e?s(X(4,{from:t,to:n})):e instanceof Error?s(e):G(e)?s(X(2,{from:n,to:e})):(a&&r.enterCallbacks[o]===a&&"function"===typeof e&&a.push(e),i())},c=e.call(r&&r.instances[o],n,t,l);let d=Promise.resolve(c);e.length<3&&(d=d.then(l)),d.catch((e=>s(e)))}))}function De(e,n,t,r){const o=[];for(const a of e)for(const e in a.components){let i=a.components[e];if("beforeRouteEnter"===n||a.instances[e])if(Ue(i)){const s=i.__vccOpts||i,l=s[n];l&&o.push(Be(l,t,r,a,e))}else{let s=i();0,o.push((()=>s.then((o=>{if(!o)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${a.path}"`));const i=m(o)?o.default:o;a.components[e]=i;const s=i.__vccOpts||i,l=s[n];return l&&Be(l,t,r,a,e)()}))))}}return o}function Ue(e){return"object"===typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}function We(e){const n=(0,r.f3)(c),t=(0,r.f3)(d),a=(0,o.Fl)((()=>n.resolve((0,o.SU)(e.to)))),i=(0,o.Fl)((()=>{const{matched:e}=a.value,{length:n}=e,r=e[n-1],o=t.matched;if(!r||!o.length)return-1;const i=o.findIndex(x.bind(null,r));if(i>-1)return i;const s=Ke(e[n-2]);return n>1&&Ke(r)===s&&o[o.length-1].path!==s?o.findIndex(x.bind(null,e[n-2])):i})),s=(0,o.Fl)((()=>i.value>-1&&Ze(t.params,a.value.params))),l=(0,o.Fl)((()=>i.value>-1&&i.value===t.matched.length-1&&S(t.params,a.value.params)));function u(t={}){return Je(t)?n[(0,o.SU)(e.replace)?"replace":"push"]((0,o.SU)(e.to)).catch(w):Promise.resolve()}return{route:a,href:(0,o.Fl)((()=>a.value.href)),isActive:s,isExactActive:l,navigate:u}}const Ve=(0,r.aZ)({name:"RouterLink",props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:We,setup(e,{slots:n}){const t=(0,o.qj)(We(e)),{options:a}=(0,r.f3)(c),i=(0,o.Fl)((()=>({[Ye(e.activeClass,a.linkActiveClass,"router-link-active")]:t.isActive,[Ye(e.exactActiveClass,a.linkExactActiveClass,"router-link-exact-active")]:t.isExactActive})));return()=>{const o=n.default&&n.default(t);return e.custom?o:(0,r.h)("a",{"aria-current":t.isExactActive?e.ariaCurrentValue:null,href:t.href,onClick:t.navigate,class:i.value},o)}}}),Ge=Ve;function Je(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&(void 0===e.button||0===e.button)){if(e.currentTarget&&e.currentTarget.getAttribute){const n=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(n))return}return e.preventDefault&&e.preventDefault(),!0}}function Ze(e,n){for(const t in n){const r=n[t],o=e[t];if("string"===typeof r){if(r!==o)return!1}else if(!Array.isArray(o)||o.length!==r.length||r.some(((e,n)=>e!==o[n])))return!1}return!0}function Ke(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ye=(e,n,t)=>null!=e?e:null!=n?n:t,Xe=(0,r.aZ)({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},setup(e,{attrs:n,slots:t}){const a=(0,r.f3)(u),i=(0,o.Fl)((()=>e.route||a.value)),c=(0,r.f3)(l,0),d=(0,o.Fl)((()=>i.value.matched[c]));(0,r.JJ)(l,c+1),(0,r.JJ)(s,d),(0,r.JJ)(u,i);const p=(0,o.iH)();return(0,r.YP)((()=>[p.value,d.value,e.name]),(([e,n,t],[r,o,a])=>{n&&(n.instances[t]=e,o&&o!==n&&e&&e===r&&(n.leaveGuards.size||(n.leaveGuards=o.leaveGuards),n.updateGuards.size||(n.updateGuards=o.updateGuards))),!e||!n||o&&x(n,o)&&r||(n.enterCallbacks[t]||[]).forEach((n=>n(e)))}),{flush:"post"}),()=>{const o=i.value,a=d.value,s=a&&a.components[e.name],l=e.name;if(!s)return Qe(t.default,{Component:s,route:o});const c=a.props[e.name],u=c?!0===c?o.params:"function"===typeof c?c(o):c:null,m=e=>{e.component.isUnmounted&&(a.instances[l]=null)},h=(0,r.h)(s,f({},u,n,{onVnodeUnmounted:m,ref:p}));return Qe(t.default,{Component:h,route:o})||h}}});function Qe(e,n){if(!e)return null;const t=e(n);return 1===t.length?t[0]:t}const en=Xe;function nn(e){const n=de(e.routes,e),t=e.parseQuery||Ie,a=e.stringifyQuery||Ne,i=e.history;const s=He(),l=He(),m=He(),b=(0,o.XI)(Z);let g=Z;p&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const v=h.bind(null,(e=>""+e)),x=h.bind(null,$e),S=h.bind(null,Re);function C(e,t){let r,o;return J(e)?(r=n.getRecordMatcher(e),o=t):o=e,n.addRoute(o,r)}function E(e){const t=n.getRecordMatcher(e);t&&n.removeRoute(t)}function P(){return n.getRoutes().map((e=>e.record))}function L(e){return!!n.getRecordMatcher(e)}function O(e,r){if(r=f({},r||b.value),"string"===typeof e){const o=k(t,e,r.path),a=n.resolve({path:o.path},r),s=i.createHref(o.fullPath);return f(o,a,{params:S(a.params),hash:Re(o.hash),redirectedFrom:void 0,href:s})}let o;if("path"in e)o=f({},e,{path:k(t,e.path,r.path).path});else{const n=f({},e.params);for(const e in n)null==n[e]&&delete n[e];o=f({},e,{params:x(e.params)}),r.params=x(r.params)}const s=n.resolve(o,r),l=e.hash||"";s.params=v(S(s.params));const c=y(a,f({},e,{hash:Me(l),path:s.path})),d=i.createHref(c);return f({fullPath:c,hash:l,query:a===Ne?qe(e.query):e.query||{}},s,{redirectedFrom:void 0,href:d})}function T(e){return"string"===typeof e?k(t,e,b.value.path):f({},e)}function M(e,n){if(g!==e)return X(8,{from:n,to:e})}function A(e){return H(e)}function R(e){return A(f(T(e),{replace:!0}))}function q(e){const n=e.matched[e.matched.length-1];if(n&&n.redirect){const{redirect:t}=n;let r="function"===typeof t?t(e):t;return"string"===typeof r&&(r=r.includes("?")||r.includes("#")?r=T(r):{path:r},r.params={}),f({query:e.query,hash:e.hash,params:e.params},r)}}function H(e,n){const t=g=O(e),r=b.value,o=e.state,i=e.force,s=!0===e.replace,l=q(t);if(l)return H(f(T(l),{state:o,force:i,replace:s}),n||t);const c=t;let d;return c.redirectedFrom=n,!i&&_(a,r,t)&&(d=X(16,{to:c,from:r}),oe(r,r,!0,!1)),(d?Promise.resolve(d):D(c,r)).catch((e=>Q(e)?e:ne(e,c,r))).then((e=>{if(e){if(Q(e,2))return H(f(T(e.to),{state:o,force:i,replace:s}),n||c)}else e=W(c,r,!0,s,o);return U(c,r,e),e}))}function B(e,n){const t=M(e,n);return t?Promise.reject(t):Promise.resolve()}function D(e,n){let t;const[r,o,a]=rn(e,n);t=De(r.reverse(),"beforeRouteLeave",e,n);for(const s of r)s.leaveGuards.forEach((r=>{t.push(Be(r,e,n))}));const i=B.bind(null,e,n);return t.push(i),tn(t).then((()=>{t=[];for(const r of s.list())t.push(Be(r,e,n));return t.push(i),tn(t)})).then((()=>{t=De(o,"beforeRouteUpdate",e,n);for(const r of o)r.updateGuards.forEach((r=>{t.push(Be(r,e,n))}));return t.push(i),tn(t)})).then((()=>{t=[];for(const r of e.matched)if(r.beforeEnter&&!n.matched.includes(r))if(Array.isArray(r.beforeEnter))for(const o of r.beforeEnter)t.push(Be(o,e,n));else t.push(Be(r.beforeEnter,e,n));return t.push(i),tn(t)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),t=De(a,"beforeRouteEnter",e,n),t.push(i),tn(t)))).then((()=>{t=[];for(const r of l.list())t.push(Be(r,e,n));return t.push(i),tn(t)})).catch((e=>Q(e,8)?e:Promise.reject(e)))}function U(e,n,t){for(const r of m.list())r(e,n,t)}function W(e,n,t,r,o){const a=M(e,n);if(a)return a;const s=n===Z,l=p?history.state:{};t&&(r||s?i.replace(e.fullPath,f({scroll:s&&l&&l.scroll},o)):i.push(e.fullPath,o)),b.value=e,oe(e,n,t,s),re()}let V;function G(){V=i.listen(((e,n,t)=>{const r=O(e),o=q(r);if(o)return void H(f(o,{replace:!0}),r).catch(w);g=r;const a=b.value;p&&I($(a.fullPath,t.delta),F()),D(r,a).catch((e=>Q(e,12)?e:Q(e,2)?(H(e.to,r).then((e=>{Q(e,20)&&!t.delta&&t.type===j.pop&&i.go(-1,!1)})).catch(w),Promise.reject()):(t.delta&&i.go(-t.delta,!1),ne(e,r,a)))).then((e=>{e=e||W(r,a,!1),e&&(t.delta?i.go(-t.delta,!1):t.type===j.pop&&Q(e,20)&&i.go(-1,!1)),U(r,a,e)})).catch(w)}))}let K,Y=He(),ee=He();function ne(e,n,t){re(e);const r=ee.list();return r.length?r.forEach((r=>r(e,n,t))):console.error(e),Promise.reject(e)}function te(){return K&&b.value!==Z?Promise.resolve():new Promise(((e,n)=>{Y.add([e,n])}))}function re(e){K||(K=!0,G(),Y.list().forEach((([n,t])=>e?t(e):n())),Y.reset())}function oe(n,t,o,a){const{scrollBehavior:i}=e;if(!p||!i)return Promise.resolve();const s=!o&&N($(n.fullPath,0))||(a||!o)&&history.state&&history.state.scroll||null;return(0,r.Y3)().then((()=>i(n,t,s))).then((e=>e&&z(e))).catch((e=>ne(e,n,t)))}const ae=e=>i.go(e);let ie;const se=new Set,le={currentRoute:b,addRoute:C,removeRoute:E,hasRoute:L,getRoutes:P,resolve:O,options:e,push:A,replace:R,go:ae,back:()=>ae(-1),forward:()=>ae(1),beforeEach:s.add,beforeResolve:l.add,afterEach:m.add,onError:ee.add,isReady:te,install(e){const n=this;e.component("RouterLink",Ge),e.component("RouterView",en),e.config.globalProperties.$router=n,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>(0,o.SU)(b)}),p&&!ie&&b.value===Z&&(ie=!0,A(i.location).catch((e=>{0})));const t={};for(const a in Z)t[a]=(0,o.Fl)((()=>b.value[a]));e.provide(c,n),e.provide(d,(0,o.qj)(t)),e.provide(u,b);const r=e.unmount;se.add(e),e.unmount=function(){se.delete(e),se.size<1&&(g=Z,V&&V(),b.value=Z,ie=!1,K=!1),r()}}};return le}function tn(e){return e.reduce(((e,n)=>e.then((()=>n()))),Promise.resolve())}function rn(e,n){const t=[],r=[],o=[],a=Math.max(n.matched.length,e.matched.length);for(let i=0;i<a;i++){const a=n.matched[i];a&&(e.matched.find((e=>x(e,a)))?r.push(a):t.push(a));const s=e.matched[i];s&&(n.matched.find((e=>x(e,s)))||o.push(s))}return[t,r,o]}},3617:(e,n,t)=>{"use strict";t.d(n,{MT:()=>Y,oR:()=>m});var r=t(3673),o=t(1959);function a(){return i().__VUE_DEVTOOLS_GLOBAL_HOOK__}function i(){return"undefined"!==typeof navigator&&"undefined"!==typeof window?window:"undefined"!==typeof t.g?t.g:{}}const s="function"===typeof Proxy,l="devtools-plugin:setup",c="plugin:settings:set";class d{constructor(e,n){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=n;const t={};if(e.settings)for(const i in e.settings){const n=e.settings[i];t[i]=n.defaultValue}const r=`__vue-devtools-plugin-settings__${e.id}`;let o={...t};try{const e=localStorage.getItem(r),n=JSON.parse(e);Object.assign(o,n)}catch(a){}this.fallbacks={getSettings(){return o},setSettings(e){try{localStorage.setItem(r,JSON.stringify(e))}catch(a){}o=e}},n.on(c,((e,n)=>{e===this.plugin.id&&this.fallbacks.setSettings(n)})),this.proxiedOn=new Proxy({},{get:(e,n)=>this.target?this.target.on[n]:(...e)=>{this.onQueue.push({method:n,args:e})}}),this.proxiedTarget=new Proxy({},{get:(e,n)=>this.target?this.target[n]:"on"===n?this.proxiedOn:Object.keys(this.fallbacks).includes(n)?(...e)=>(this.targetQueue.push({method:n,args:e,resolve:()=>{}}),this.fallbacks[n](...e)):(...e)=>new Promise((t=>{this.targetQueue.push({method:n,args:e,resolve:t})}))})}async setRealTarget(e){this.target=e;for(const n of this.onQueue)this.target.on[n.method](...n.args);for(const n of this.targetQueue)n.resolve(await this.target[n.method](...n.args))}}function u(e,n){const t=i(),r=a(),o=s&&e.enableEarlyProxy;if(!r||!t.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&o){const a=o?new d(e,r):null,i=t.__VUE_DEVTOOLS_PLUGINS__=t.__VUE_DEVTOOLS_PLUGINS__||[];i.push({pluginDescriptor:e,setupFn:n,proxy:a}),a&&n(a.proxiedTarget)}else r.emit(l,e,n)}
/*!
 * vuex v4.0.2
 * (c) 2021 Evan You
 * @license MIT
 */
var p="store";function m(e){return void 0===e&&(e=null),(0,r.f3)(null!==e?e:p)}function f(e,n){Object.keys(e).forEach((function(t){return n(e[t],t)}))}function h(e){return null!==e&&"object"===typeof e}function w(e){return e&&"function"===typeof e.then}function b(e,n){return function(){return e(n)}}function g(e,n,t){return n.indexOf(e)<0&&(t&&t.prepend?n.unshift(e):n.push(e)),function(){var t=n.indexOf(e);t>-1&&n.splice(t,1)}}function k(e,n){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var t=e.state;v(e,t,[],e._modules.root,!0),y(e,t,n)}function y(e,n,t){var r=e._state;e.getters={},e._makeLocalGettersCache=Object.create(null);var a=e._wrappedGetters,i={};f(a,(function(n,t){i[t]=b(n,e),Object.defineProperty(e.getters,t,{get:function(){return i[t]()},enumerable:!0})})),e._state=(0,o.qj)({data:n}),e.strict&&P(e),r&&t&&e._withCommit((function(){r.data=null}))}function v(e,n,t,r,o){var a=!t.length,i=e._modules.getNamespace(t);if(r.namespaced&&(e._modulesNamespaceMap[i],e._modulesNamespaceMap[i]=r),!a&&!o){var s=j(n,t.slice(0,-1)),l=t[t.length-1];e._withCommit((function(){s[l]=r.state}))}var c=r.context=_(e,i,t);r.forEachMutation((function(n,t){var r=i+t;S(e,r,n,c)})),r.forEachAction((function(n,t){var r=n.root?t:i+t,o=n.handler||n;C(e,r,o,c)})),r.forEachGetter((function(n,t){var r=i+t;E(e,r,n,c)})),r.forEachChild((function(r,a){v(e,n,t.concat(a),r,o)}))}function _(e,n,t){var r=""===n,o={dispatch:r?e.dispatch:function(t,r,o){var a=L(t,r,o),i=a.payload,s=a.options,l=a.type;return s&&s.root||(l=n+l),e.dispatch(l,i)},commit:r?e.commit:function(t,r,o){var a=L(t,r,o),i=a.payload,s=a.options,l=a.type;s&&s.root||(l=n+l),e.commit(l,i,s)}};return Object.defineProperties(o,{getters:{get:r?function(){return e.getters}:function(){return x(e,n)}},state:{get:function(){return j(e.state,t)}}}),o}function x(e,n){if(!e._makeLocalGettersCache[n]){var t={},r=n.length;Object.keys(e.getters).forEach((function(o){if(o.slice(0,r)===n){var a=o.slice(r);Object.defineProperty(t,a,{get:function(){return e.getters[o]},enumerable:!0})}})),e._makeLocalGettersCache[n]=t}return e._makeLocalGettersCache[n]}function S(e,n,t,r){var o=e._mutations[n]||(e._mutations[n]=[]);o.push((function(n){t.call(e,r.state,n)}))}function C(e,n,t,r){var o=e._actions[n]||(e._actions[n]=[]);o.push((function(n){var o=t.call(e,{dispatch:r.dispatch,commit:r.commit,getters:r.getters,state:r.state,rootGetters:e.getters,rootState:e.state},n);return w(o)||(o=Promise.resolve(o)),e._devtoolHook?o.catch((function(n){throw e._devtoolHook.emit("vuex:error",n),n})):o}))}function E(e,n,t,r){e._wrappedGetters[n]||(e._wrappedGetters[n]=function(e){return t(r.state,r.getters,e.state,e.getters)})}function P(e){(0,r.YP)((function(){return e._state.data}),(function(){0}),{deep:!0,flush:"sync"})}function j(e,n){return n.reduce((function(e,n){return e[n]}),e)}function L(e,n,t){return h(e)&&e.type&&(t=n,n=e,e=e.type),{type:e,payload:n,options:t}}var O="vuex bindings",T="vuex:mutations",M="vuex:actions",A="vuex",F=0;function z(e,n){u({id:"org.vuejs.vuex",app:e,label:"Vuex",homepage:"https://next.vuex.vuejs.org/",logo:"https://vuejs.org/images/icons/favicon-96x96.png",packageName:"vuex",componentStateTypes:[O]},(function(t){t.addTimelineLayer({id:T,label:"Vuex Mutations",color:$}),t.addTimelineLayer({id:M,label:"Vuex Actions",color:$}),t.addInspector({id:A,label:"Vuex",icon:"storage",treeFilterPlaceholder:"Filter stores..."}),t.on.getInspectorTree((function(t){if(t.app===e&&t.inspectorId===A)if(t.filter){var r=[];B(r,n._modules.root,t.filter,""),t.rootNodes=r}else t.rootNodes=[H(n._modules.root,"")]})),t.on.getInspectorState((function(t){if(t.app===e&&t.inspectorId===A){var r=t.nodeId;x(n,r),t.state=D(W(n._modules,r),"root"===r?n.getters:n._makeLocalGettersCache,r)}})),t.on.editInspectorState((function(t){if(t.app===e&&t.inspectorId===A){var r=t.nodeId,o=t.path;"root"!==r&&(o=r.split("/").filter(Boolean).concat(o)),n._withCommit((function(){t.set(n._state.data,o,t.state.value)}))}})),n.subscribe((function(e,n){var r={};e.payload&&(r.payload=e.payload),r.state=n,t.notifyComponentUpdate(),t.sendInspectorTree(A),t.sendInspectorState(A),t.addTimelineEvent({layerId:T,event:{time:Date.now(),title:e.type,data:r}})})),n.subscribeAction({before:function(e,n){var r={};e.payload&&(r.payload=e.payload),e._id=F++,e._time=Date.now(),r.state=n,t.addTimelineEvent({layerId:M,event:{time:e._time,title:e.type,groupId:e._id,subtitle:"start",data:r}})},after:function(e,n){var r={},o=Date.now()-e._time;r.duration={_custom:{type:"duration",display:o+"ms",tooltip:"Action duration",value:o}},e.payload&&(r.payload=e.payload),r.state=n,t.addTimelineEvent({layerId:M,event:{time:Date.now(),title:e.type,groupId:e._id,subtitle:"end",data:r}})}})}))}var $=8702998,R=6710886,I=16777215,N={label:"namespaced",textColor:I,backgroundColor:R};function q(e){return e&&"root"!==e?e.split("/").slice(-2,-1)[0]:"Root"}function H(e,n){return{id:n||"root",label:q(n),tags:e.namespaced?[N]:[],children:Object.keys(e._children).map((function(t){return H(e._children[t],n+t+"/")}))}}function B(e,n,t,r){r.includes(t)&&e.push({id:r||"root",label:r.endsWith("/")?r.slice(0,r.length-1):r||"Root",tags:n.namespaced?[N]:[]}),Object.keys(n._children).forEach((function(o){B(e,n._children[o],t,r+o+"/")}))}function D(e,n,t){n="root"===t?n:n[t];var r=Object.keys(n),o={state:Object.keys(e.state).map((function(n){return{key:n,editable:!0,value:e.state[n]}}))};if(r.length){var a=U(n);o.getters=Object.keys(a).map((function(e){return{key:e.endsWith("/")?q(e):e,editable:!1,value:V((function(){return a[e]}))}}))}return o}function U(e){var n={};return Object.keys(e).forEach((function(t){var r=t.split("/");if(r.length>1){var o=n,a=r.pop();r.forEach((function(e){o[e]||(o[e]={_custom:{value:{},display:e,tooltip:"Module",abstract:!0}}),o=o[e]._custom.value})),o[a]=V((function(){return e[t]}))}else n[t]=V((function(){return e[t]}))})),n}function W(e,n){var t=n.split("/").filter((function(e){return e}));return t.reduce((function(e,r,o){var a=e[r];if(!a)throw new Error('Missing module "'+r+'" for path "'+n+'".');return o===t.length-1?a:a._children}),"root"===n?e:e.root._children)}function V(e){try{return e()}catch(n){return n}}var G=function(e,n){this.runtime=n,this._children=Object.create(null),this._rawModule=e;var t=e.state;this.state=("function"===typeof t?t():t)||{}},J={namespaced:{configurable:!0}};J.namespaced.get=function(){return!!this._rawModule.namespaced},G.prototype.addChild=function(e,n){this._children[e]=n},G.prototype.removeChild=function(e){delete this._children[e]},G.prototype.getChild=function(e){return this._children[e]},G.prototype.hasChild=function(e){return e in this._children},G.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},G.prototype.forEachChild=function(e){f(this._children,e)},G.prototype.forEachGetter=function(e){this._rawModule.getters&&f(this._rawModule.getters,e)},G.prototype.forEachAction=function(e){this._rawModule.actions&&f(this._rawModule.actions,e)},G.prototype.forEachMutation=function(e){this._rawModule.mutations&&f(this._rawModule.mutations,e)},Object.defineProperties(G.prototype,J);var Z=function(e){this.register([],e,!1)};function K(e,n,t){if(n.update(t),t.modules)for(var r in t.modules){if(!n.getChild(r))return void 0;K(e.concat(r),n.getChild(r),t.modules[r])}}Z.prototype.get=function(e){return e.reduce((function(e,n){return e.getChild(n)}),this.root)},Z.prototype.getNamespace=function(e){var n=this.root;return e.reduce((function(e,t){return n=n.getChild(t),e+(n.namespaced?t+"/":"")}),"")},Z.prototype.update=function(e){K([],this.root,e)},Z.prototype.register=function(e,n,t){var r=this;void 0===t&&(t=!0);var o=new G(n,t);if(0===e.length)this.root=o;else{var a=this.get(e.slice(0,-1));a.addChild(e[e.length-1],o)}n.modules&&f(n.modules,(function(n,o){r.register(e.concat(o),n,t)}))},Z.prototype.unregister=function(e){var n=this.get(e.slice(0,-1)),t=e[e.length-1],r=n.getChild(t);r&&r.runtime&&n.removeChild(t)},Z.prototype.isRegistered=function(e){var n=this.get(e.slice(0,-1)),t=e[e.length-1];return!!n&&n.hasChild(t)};function Y(e){return new X(e)}var X=function(e){var n=this;void 0===e&&(e={});var t=e.plugins;void 0===t&&(t=[]);var r=e.strict;void 0===r&&(r=!1);var o=e.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new Z(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._devtools=o;var a=this,i=this,s=i.dispatch,l=i.commit;this.dispatch=function(e,n){return s.call(a,e,n)},this.commit=function(e,n,t){return l.call(a,e,n,t)},this.strict=r;var c=this._modules.root.state;v(this,c,[],this._modules.root),y(this,c),t.forEach((function(e){return e(n)}))},Q={state:{configurable:!0}};X.prototype.install=function(e,n){e.provide(n||p,this),e.config.globalProperties.$store=this;var t=void 0!==this._devtools&&this._devtools;t&&z(e,this)},Q.state.get=function(){return this._state.data},Q.state.set=function(e){0},X.prototype.commit=function(e,n,t){var r=this,o=L(e,n,t),a=o.type,i=o.payload,s=(o.options,{type:a,payload:i}),l=this._mutations[a];l&&(this._withCommit((function(){l.forEach((function(e){e(i)}))})),this._subscribers.slice().forEach((function(e){return e(s,r.state)})))},X.prototype.dispatch=function(e,n){var t=this,r=L(e,n),o=r.type,a=r.payload,i={type:o,payload:a},s=this._actions[o];if(s){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(i,t.state)}))}catch(c){0}var l=s.length>1?Promise.all(s.map((function(e){return e(a)}))):s[0](a);return new Promise((function(e,n){l.then((function(n){try{t._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(i,t.state)}))}catch(c){0}e(n)}),(function(e){try{t._actionSubscribers.filter((function(e){return e.error})).forEach((function(n){return n.error(i,t.state,e)}))}catch(c){0}n(e)}))}))}},X.prototype.subscribe=function(e,n){return g(e,this._subscribers,n)},X.prototype.subscribeAction=function(e,n){var t="function"===typeof e?{before:e}:e;return g(t,this._actionSubscribers,n)},X.prototype.watch=function(e,n,t){var o=this;return(0,r.YP)((function(){return e(o.state,o.getters)}),n,Object.assign({},t))},X.prototype.replaceState=function(e){var n=this;this._withCommit((function(){n._state.data=e}))},X.prototype.registerModule=function(e,n,t){void 0===t&&(t={}),"string"===typeof e&&(e=[e]),this._modules.register(e,n),v(this,this.state,e,this._modules.get(e),t.preserveState),y(this,this.state)},X.prototype.unregisterModule=function(e){var n=this;"string"===typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit((function(){var t=j(n.state,e.slice(0,-1));delete t[e[e.length-1]]})),k(this)},X.prototype.hasModule=function(e){return"string"===typeof e&&(e=[e]),this._modules.isRegistered(e)},X.prototype.hotUpdate=function(e){this._modules.update(e),k(this,!0)},X.prototype._withCommit=function(e){var n=this._committing;this._committing=!0,e(),this._committing=n},Object.defineProperties(X.prototype,Q);te((function(e,n){var t={};return ee(n).forEach((function(n){var r=n.key,o=n.val;t[r]=function(){var n=this.$store.state,t=this.$store.getters;if(e){var r=re(this.$store,"mapState",e);if(!r)return;n=r.context.state,t=r.context.getters}return"function"===typeof o?o.call(this,n,t):n[o]},t[r].vuex=!0})),t})),te((function(e,n){var t={};return ee(n).forEach((function(n){var r=n.key,o=n.val;t[r]=function(){var n=[],t=arguments.length;while(t--)n[t]=arguments[t];var r=this.$store.commit;if(e){var a=re(this.$store,"mapMutations",e);if(!a)return;r=a.context.commit}return"function"===typeof o?o.apply(this,[r].concat(n)):r.apply(this.$store,[o].concat(n))}})),t})),te((function(e,n){var t={};return ee(n).forEach((function(n){var r=n.key,o=n.val;o=e+o,t[r]=function(){if(!e||re(this.$store,"mapGetters",e))return this.$store.getters[o]},t[r].vuex=!0})),t})),te((function(e,n){var t={};return ee(n).forEach((function(n){var r=n.key,o=n.val;t[r]=function(){var n=[],t=arguments.length;while(t--)n[t]=arguments[t];var r=this.$store.dispatch;if(e){var a=re(this.$store,"mapActions",e);if(!a)return;r=a.context.dispatch}return"function"===typeof o?o.apply(this,[r].concat(n)):r.apply(this.$store,[o].concat(n))}})),t}));function ee(e){return ne(e)?Array.isArray(e)?e.map((function(e){return{key:e,val:e}})):Object.keys(e).map((function(n){return{key:n,val:e[n]}})):[]}function ne(e){return Array.isArray(e)||h(e)}function te(e){return function(n,t){return"string"!==typeof n?(t=n,n=""):"/"!==n.charAt(n.length-1)&&(n+="/"),e(n,t)}}function re(e,n,t){var r=e._modulesNamespaceMap[t];return r}},8593:e=>{"use strict";e.exports=JSON.parse('{"name":"axios","version":"0.21.4","description":"Promise based HTTP client for the browser and node.js","main":"index.js","scripts":{"test":"grunt test","start":"node ./sandbox/server.js","build":"NODE_ENV=production grunt build","preversion":"npm test","version":"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json","postversion":"git push && git push --tags","examples":"node ./examples/server.js","coveralls":"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js","fix":"eslint --fix lib/**/*.js"},"repository":{"type":"git","url":"https://github.com/axios/axios.git"},"keywords":["xhr","http","ajax","promise","node"],"author":"Matt Zabriskie","license":"MIT","bugs":{"url":"https://github.com/axios/axios/issues"},"homepage":"https://axios-http.com","devDependencies":{"coveralls":"^3.0.0","es6-promise":"^4.2.4","grunt":"^1.3.0","grunt-banner":"^0.6.0","grunt-cli":"^1.2.0","grunt-contrib-clean":"^1.1.0","grunt-contrib-watch":"^1.0.0","grunt-eslint":"^23.0.0","grunt-karma":"^4.0.0","grunt-mocha-test":"^0.13.3","grunt-ts":"^6.0.0-beta.19","grunt-webpack":"^4.0.2","istanbul-instrumenter-loader":"^1.0.0","jasmine-core":"^2.4.1","karma":"^6.3.2","karma-chrome-launcher":"^3.1.0","karma-firefox-launcher":"^2.1.0","karma-jasmine":"^1.1.1","karma-jasmine-ajax":"^0.1.13","karma-safari-launcher":"^1.0.0","karma-sauce-launcher":"^4.3.6","karma-sinon":"^1.0.5","karma-sourcemap-loader":"^0.3.8","karma-webpack":"^4.0.2","load-grunt-tasks":"^3.5.2","minimist":"^1.2.0","mocha":"^8.2.1","sinon":"^4.5.0","terser-webpack-plugin":"^4.2.3","typescript":"^4.0.5","url-search-params":"^0.10.0","webpack":"^4.44.2","webpack-dev-server":"^3.11.0"},"browser":{"./lib/adapters/http.js":"./lib/adapters/xhr.js"},"jsdelivr":"dist/axios.min.js","unpkg":"dist/axios.min.js","typings":"./index.d.ts","dependencies":{"follow-redirects":"^1.14.0"},"bundlesize":[{"path":"./dist/axios.min.js","threshold":"5kB"}],"_resolved":"https://registry.npmjs.org/axios/-/axios-0.21.4.tgz","_integrity":"sha512-ut5vewkiu8jjGBdqpM44XxjuCjq9LAKeHVmoVfHVzy8eHgxxq8SbAVQNovDA8mVi05kP0Ea/n/UzcSHcTJQfNg==","_from":"axios@0.21.4"}')}}]);