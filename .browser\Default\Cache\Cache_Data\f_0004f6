{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>System<span id=\"system\" class=\"self-anchor\"><a class=\"perm\" href=\"#system\">#</a></span></h1><p style=\"color:inherit\">System. </p><p style=\"color:inherit\">Commands and events in this class can be used to access and query the local device. </p><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-boot\" target=\"_blank\" rel=\"\">sl_bt_evt_system_boot</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-stopped\" target=\"_blank\" rel=\"\">sl_bt_evt_system_stopped</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-error\" target=\"_blank\" rel=\"\">sl_bt_evt_system_error</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-hardware-error\" target=\"_blank\" rel=\"\">sl_bt_evt_system_hardware_error</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-resource-exhausted\" target=\"_blank\" rel=\"\">sl_bt_evt_system_resource_exhausted</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-external-signal\" target=\"_blank\" rel=\"\">sl_bt_evt_system_external_signal</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-awake\" target=\"_blank\" rel=\"\">sl_bt_evt_system_awake</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-soft-timer\" target=\"_blank\" rel=\"\">sl_bt_evt_system_soft_timer</a></p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-boot-mode-t\">sl_bt_system_boot_mode_t</a> {</div><div class=\"enum\">sl_bt_system_boot_mode_normal = 0x0</div><div class=\"enum\">sl_bt_system_boot_mode_uart_dfu = 0x1</div><div class=\"enum\">sl_bt_system_boot_mode_ota_dfu = 0x2</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Specifies the mode that the system will boot into. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-linklayer-config-key-t\">sl_bt_system_linklayer_config_key_t</a> {</div><div class=\"enum\">sl_bt_system_linklayer_config_key_halt = 0x1</div><div class=\"enum\">sl_bt_system_linklayer_config_key_priority_range = 0x2</div><div class=\"enum\">sl_bt_system_linklayer_config_key_scan_channels = 0x3</div><div class=\"enum\">sl_bt_system_linklayer_config_key_set_flags = 0x4</div><div class=\"enum\">sl_bt_system_linklayer_config_key_clr_flags = 0x5</div><div class=\"enum\">sl_bt_system_linklayer_config_key_set_afh_interval = 0x7</div><div class=\"enum\">sl_bt_system_linklayer_config_key_set_priority_table = 0x9</div><div class=\"enum\">sl_bt_system_linklayer_config_key_set_rx_packet_filtering = 0xa</div><div class=\"enum\">sl_bt_system_linklayer_config_key_set_simultaneous_scanning = 0xb</div><div class=\"enum\">sl_bt_system_linklayer_config_key_set_channelmap_flags = 0xc</div><div class=\"enum\">sl_bt_system_linklayer_config_key_power_control_golden_range = 0x10</div><div class=\"enum\">sl_bt_system_linklayer_config_key_active_scanner_backoff_upper_limit = 0x11</div><div class=\"enum\">sl_bt_system_linklayer_config_key_afh_rssi_threshold = 0x12</div><div class=\"enum\">sl_bt_system_linklayer_config_key_afh_channel_cooldown = 0x13</div><div class=\"enum\">sl_bt_system_linklayer_config_key_set_report_all_scan_rsps = 0x14</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These Keys are used to configure Link Layer Operation. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-hello\">sl_bt_system_hello</a>()</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-start-bluetooth\">sl_bt_system_start_bluetooth</a>()</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-stop-bluetooth\">sl_bt_system_stop_bluetooth</a>()</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-forcefully-stop-bluetooth\">sl_bt_system_forcefully_stop_bluetooth</a>()</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-get-version\">sl_bt_system_get_version</a>(uint16_t *major, uint16_t *minor, uint16_t *patch, uint16_t *build, uint32_t *bootloader, uint32_t *hash)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-reset\">sl_bt_system_reset</a>(uint8_t dfu)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-halt\">sl_bt_system_halt</a>(uint8_t halt)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-linklayer-configure\">sl_bt_system_linklayer_configure</a>(uint8_t key, size_t data_len, const uint8_t *data)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-set-tx-power\">sl_bt_system_set_tx_power</a>(int16_t min_power, int16_t max_power, int16_t *set_min, int16_t *set_max)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-get-tx-power-setting\">sl_bt_system_get_tx_power_setting</a>(int16_t *support_min, int16_t *support_max, int16_t *set_min, int16_t *set_max, int16_t *rf_path_gain)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-set-identity-address\">sl_bt_system_set_identity_address</a>(bd_addr address, uint8_t type)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-get-identity-address\">sl_bt_system_get_identity_address</a>(bd_addr *address, uint8_t *type)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-get-random-data\">sl_bt_system_get_random_data</a>(uint8_t length, size_t max_data_size, size_t *data_len, uint8_t *data)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-data-buffer-write\">sl_bt_system_data_buffer_write</a>(size_t data_len, const uint8_t *data)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-data-buffer-clear\">sl_bt_system_data_buffer_clear</a>()</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-get-counters\">sl_bt_system_get_counters</a>(uint8_t reset, uint16_t *tx_packets, uint16_t *rx_packets, uint16_t *crc_errors, uint16_t *failures)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-system-set-lazy-soft-timer\">sl_bt_system_set_lazy_soft_timer</a>(uint32_t time, uint32_t slack, uint8_t handle, uint8_t single_shot)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-hello-id\">sl_bt_cmd_system_hello_id</a> 0x00010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-start-bluetooth-id\">sl_bt_cmd_system_start_bluetooth_id</a> 0x1c010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-stop-bluetooth-id\">sl_bt_cmd_system_stop_bluetooth_id</a> 0x1d010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-forcefully-stop-bluetooth-id\">sl_bt_cmd_system_forcefully_stop_bluetooth_id</a> 0x1e010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-get-version-id\">sl_bt_cmd_system_get_version_id</a> 0x1b010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-reset-id\">sl_bt_cmd_system_reset_id</a> 0x01010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-halt-id\">sl_bt_cmd_system_halt_id</a> 0x0c010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-linklayer-configure-id\">sl_bt_cmd_system_linklayer_configure_id</a> 0x0e010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-set-tx-power-id\">sl_bt_cmd_system_set_tx_power_id</a> 0x17010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-get-tx-power-setting-id\">sl_bt_cmd_system_get_tx_power_setting_id</a> 0x18010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-set-identity-address-id\">sl_bt_cmd_system_set_identity_address_id</a> 0x13010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-get-identity-address-id\">sl_bt_cmd_system_get_identity_address_id</a> 0x15010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-get-random-data-id\">sl_bt_cmd_system_get_random_data_id</a> 0x0b010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-data-buffer-write-id\">sl_bt_cmd_system_data_buffer_write_id</a> 0x12010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-data-buffer-clear-id\">sl_bt_cmd_system_data_buffer_clear_id</a> 0x14010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-get-counters-id\">sl_bt_cmd_system_get_counters_id</a> 0x0f010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-system-set-lazy-soft-timer-id\">sl_bt_cmd_system_set_lazy_soft_timer_id</a> 0x1a010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-hello-id\">sl_bt_rsp_system_hello_id</a> 0x00010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-start-bluetooth-id\">sl_bt_rsp_system_start_bluetooth_id</a> 0x1c010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-stop-bluetooth-id\">sl_bt_rsp_system_stop_bluetooth_id</a> 0x1d010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-forcefully-stop-bluetooth-id\">sl_bt_rsp_system_forcefully_stop_bluetooth_id</a> 0x1e010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-get-version-id\">sl_bt_rsp_system_get_version_id</a> 0x1b010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-reset-id\">sl_bt_rsp_system_reset_id</a> 0x01010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-halt-id\">sl_bt_rsp_system_halt_id</a> 0x0c010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-linklayer-configure-id\">sl_bt_rsp_system_linklayer_configure_id</a> 0x0e010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-set-tx-power-id\">sl_bt_rsp_system_set_tx_power_id</a> 0x17010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-get-tx-power-setting-id\">sl_bt_rsp_system_get_tx_power_setting_id</a> 0x18010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-set-identity-address-id\">sl_bt_rsp_system_set_identity_address_id</a> 0x13010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-get-identity-address-id\">sl_bt_rsp_system_get_identity_address_id</a> 0x15010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-get-random-data-id\">sl_bt_rsp_system_get_random_data_id</a> 0x0b010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-data-buffer-write-id\">sl_bt_rsp_system_data_buffer_write_id</a> 0x12010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-data-buffer-clear-id\">sl_bt_rsp_system_data_buffer_clear_id</a> 0x14010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-get-counters-id\">sl_bt_rsp_system_get_counters_id</a> 0x0f010020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-system-set-lazy-soft-timer-id\">sl_bt_rsp_system_set_lazy_soft_timer_id</a> 0x1a010020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>sl_bt_system_boot_mode_t<span id=\"sl-bt-system-boot-mode-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-boot-mode-t\">#</a></span></h3><blockquote>sl_bt_system_boot_mode_t</blockquote><p style=\"color:inherit\">Specifies the mode that the system will boot into. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_system_boot_mode_normal</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Boot to normal mode </p></td></tr><tr><td class=\"fieldname\">sl_bt_system_boot_mode_uart_dfu</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Boot to UART DFU mode </p></td></tr><tr><td class=\"fieldname\">sl_bt_system_boot_mode_ota_dfu</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) Boot to OTA DFU mode </p></td></tr></tbody></table><br><div>Definition at line <code>272</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_linklayer_config_key_t<span id=\"sl-bt-system-linklayer-config-key-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-linklayer-config-key-t\">#</a></span></h3><blockquote>sl_bt_system_linklayer_config_key_t</blockquote><p style=\"color:inherit\">These Keys are used to configure Link Layer Operation. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_halt</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Same as system_halt command, value-0 Stop Radio 1- Start Radio </p></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_priority_range</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) Sets the RAIL priority_mapping offset field of the link layer priority configuration structure to the first byte of the value field. </p></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_scan_channels</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x3) Sets channels to scan on. The first byte of the value is the channel map. 0x1 = Channel 37, 0x2 = Channel 38, 0x4 = Channel 39 </p></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_set_flags</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x4) Sets the link layer configuration flags. The value is a little endian 32-bit integer. Flag Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">0x00000001 - Disable Feature Exchange in peripheral role of the connection</p></li><li><p style=\"color:inherit\">0x00000002 - Disable Feature Exchange in central role of the connection </p></li></ul></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_clr_flags</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x5) The value is flags to clear. Flags are the same as in SET_FLAGS command. </p></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_set_afh_interval</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x7) Set the afh_scan_interval. Value is in units of 10 ms. Setting the interval to 0 will result in using the default value of 1 second. </p></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_set_priority_table</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x9) The value contains a priority table to be copied over the existing table. If the value is smaller than the full table, only those values are updated. See sl_bt_bluetooth_ll_priorities struct for the definition of a priority table. </p></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_set_rx_packet_filtering</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0xa) Configure and enable or disable RX packet filtering feature. Value: &gt;= 5 bytes.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Byte 1 - The filter count</p></li><li><p style=\"color:inherit\">Byte 2 - The filter offset</p></li><li><p style=\"color:inherit\">Byte 3 - The length of the filter list</p></li><li><p style=\"color:inherit\">Byte 4 - The bitmask flags</p></li><li><p style=\"color:inherit\">Rest of the data - The filter list </p></li></ul></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_set_simultaneous_scanning</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0xb) Enable or disable simultaneous scanning on the 1M and Coded PHYs. Value: 1 byte.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">0 - Disable simultaneous scanning.</p></li><li><p style=\"color:inherit\">1 - Enable simultaneous scanning. </p></li></ul></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_set_channelmap_flags</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0xc) Configure channelmap adaptivity flags. Value: 4 bytes. </p></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_power_control_golden_range</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x10) Set Power Control golden range parameters. The value is a 8-bytes long array that consists of 4 pairs of golden range configurations. In each pair, the first byte is the lower RSSI boundary and the second byte is the upper RSSI boundary. RSSI values are in dBm. This configuration is not allowed if there are active Bluetooth connections.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Byte 1 - Minimal RSSI on 1M PHY</p></li><li><p style=\"color:inherit\">Byte 2 - Maximal RSSI on 1M PHY</p></li><li><p style=\"color:inherit\">Byte 3 - Minimal RSSI on 2M PHY</p></li><li><p style=\"color:inherit\">Byte 4 - Maximal RSSI on 2M PHY</p></li><li><p style=\"color:inherit\">Byte 5 - Minimal RSSI on Coded PHY S=8</p></li><li><p style=\"color:inherit\">Byte 6 - Maximal RSSI on Coded PHY S=8</p></li><li><p style=\"color:inherit\">Byte 7 - Minimal RSSI on Coded PHY S=2</p></li><li><p style=\"color:inherit\">Byte 8 - Maximal RSSI on Coded PHY S=2 </p></li></ul></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_active_scanner_backoff_upper_limit</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x11) Value: uint16_t Adjust upper limit for backoff counter. If 0 restores default value of 256 Value must be between 16 - 256 </p></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_afh_rssi_threshold</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x12) Value: int8_t Configures RSSI limit for AFH channel blocking </p></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_afh_channel_cooldown</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x13) Value: int16_t Configures how long channel is blocked after activity is detected Default: 8000 </p></td></tr><tr><td class=\"fieldname\">sl_bt_system_linklayer_config_key_set_report_all_scan_rsps</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x14) Value: uint8_t 0 - default, only reports scan responses that is received after sending scan_req nonzero - Will report all scan responses that are received on primary advertising channels </p></td></tr></tbody></table><br><div>Definition at line <code>282</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_system_hello<span id=\"sl-bt-system-hello\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-hello\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_hello ()</blockquote><p style=\"color:inherit\">Verify whether the communication between the host and the device is functional.</p><p style=\"color:inherit\"><strong>NOTE:</strong> This command is available even if the Bluetooth stack has not been started. See <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-start-bluetooth\" target=\"_blank\" rel=\"\">sl_bt_system_start_bluetooth</a> for description of how the Bluetooth stack is started.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>996</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_start_bluetooth<span id=\"sl-bt-system-start-bluetooth\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-start-bluetooth\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_start_bluetooth ()</blockquote><p style=\"color:inherit\">If the Bluetooth on-demand start component is not included in the application build, the Bluetooth stack is automatically started when the device boots up. In this configuration, the on-demand start command is not available and the command returns the error SL_STATUS_NOT_AVAILABLE.</p><p style=\"color:inherit\">When the Bluetooth on-demand start component is included in the application build, this command is used by the application to request starting the Bluetooth stack when the application needs it. If the command returns a success result, the stack starts to asynchronously allocate the resources and configure the Bluetooth stack based on the configuration passed at initialization time.</p><p style=\"color:inherit\">The Bluetooth stack cannot be restarted while it's still stopping after issuing the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-stop-bluetooth\" target=\"_blank\" rel=\"\">sl_bt_system_stop_bluetooth</a>. If <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-start-bluetooth\" target=\"_blank\" rel=\"\">sl_bt_system_start_bluetooth</a> is called when stopping is still on-going the command returns the error SL_STATUS_INVALID_STATE. The application must wait for the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-stopped\" target=\"_blank\" rel=\"\">sl_bt_evt_system_stopped</a> event before attempting to restart the stack.</p><p style=\"color:inherit\">Successful start of the stack is indicated by the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-boot\" target=\"_blank\" rel=\"\">sl_bt_evt_system_boot</a> event. The configured classes and Bluetooth stack features are available after the application has received the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-boot\" target=\"_blank\" rel=\"\">sl_bt_evt_system_boot</a> event. If starting the Bluetooth stack fails, the error is indicated to the application with the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-error\" target=\"_blank\" rel=\"\">sl_bt_evt_system_error</a> event.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-boot\" target=\"_blank\" rel=\"\">sl_bt_evt_system_boot</a> - Triggered when the Bluetooth stack has succesfully started and is ready to accept commands from the application</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-error\" target=\"_blank\" rel=\"\">sl_bt_evt_system_error</a> - Triggered if the command to start the Bluetooth stack was accepted but the asynchronous starting of the stack has failed </p></li></ul><br><div>Definition at line <code>1036</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_stop_bluetooth<span id=\"sl-bt-system-stop-bluetooth\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-stop-bluetooth\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_stop_bluetooth ()</blockquote><p style=\"color:inherit\">If the Bluetooth on-demand start component is not included in the application build, the Bluetooth stack is automatically started when the device boots up. In this configuration, the stop command is not available and the command returns the error SL_STATUS_NOT_AVAILABLE.</p><p style=\"color:inherit\">When the Bluetooth on-demand start component is included in the application build, this command is used by the application to stop the Bluetooth stack when the application no longer needs it. This command gracefully restores Bluetooth to an idle state by disconnecting any active connections and stopping any on-going advertising and scanning. Any resources that were allocated when the stack was started are freed when the stack has finished stopping. After this command, the BGAPI classes other than <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system\" target=\"_blank\" rel=\"\">System</a> become unavailable.</p><p style=\"color:inherit\">Stopping the Bluetooth stack with this command is asynchronous and the completion is indicated by the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-stopped\" target=\"_blank\" rel=\"\">sl_bt_evt_system_stopped</a> event. The application can use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-start-bluetooth\" target=\"_blank\" rel=\"\">sl_bt_system_start_bluetooth</a> to restart the stack any time after it has received the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-stopped\" target=\"_blank\" rel=\"\">sl_bt_evt_system_stopped</a> event. If the application needs to stop the Bluetooth stack immediately, use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-forcefully-stop-bluetooth\" target=\"_blank\" rel=\"\">sl_bt_system_forcefully_stop_bluetooth</a>. That command can also be used to immediately complete the asynchronous stopping if the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-stop-bluetooth\" target=\"_blank\" rel=\"\">sl_bt_system_stop_bluetooth</a> has not completed in expected time period.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-stopped\" target=\"_blank\" rel=\"\">sl_bt_evt_system_stopped</a> - Triggered when stopping the Bluetooth stack has completed </p></li></ul><br><div>Definition at line <code>1071</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_forcefully_stop_bluetooth<span id=\"sl-bt-system-forcefully-stop-bluetooth\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-forcefully-stop-bluetooth\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_forcefully_stop_bluetooth ()</blockquote><p style=\"color:inherit\">If the Bluetooth on-demand start component is not included in the application build, the Bluetooth stack is automatically started when the device boots up. In this configuration, the stop command is not available and the command returns the error SL_STATUS_NOT_AVAILABLE.</p><p style=\"color:inherit\">When the Bluetooth on-demand start component is included in the application build, this command is used by the application to forcefully stop the Bluetooth stack when the application no longer needs it. This command immediately stops all active Bluetooth operations such as advertising, scanning, and connections. Active connections are forcefully closed without performing the ACL Termination procedure. This can result in the observation of connection loss or supervision timeout on the remote device. Only use this command for special cases, for example, when stopping Bluetooth with <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-stop-bluetooth\" target=\"_blank\" rel=\"\">sl_bt_system_stop_bluetooth</a> did not complete in expected time period.</p><p style=\"color:inherit\">Stopping the Bluetooth stack with this command is immediate and it directly triggers the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-stopped\" target=\"_blank\" rel=\"\">sl_bt_evt_system_stopped</a> event. Any resources that were allocated when the stack was started are freed. After this command, the BGAPI classes other than <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system\" target=\"_blank\" rel=\"\">System</a> become unavailable. The application can use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-start-bluetooth\" target=\"_blank\" rel=\"\">sl_bt_system_start_bluetooth</a> to continue using Bluetooth later.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-stopped\" target=\"_blank\" rel=\"\">sl_bt_evt_system_stopped</a> - Triggered immediately to indicate the Bluetooth stack has stopped </p></li></ul><br><div>Definition at line <code>1105</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_get_version<span id=\"sl-bt-system-get-version\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-get-version\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_get_version (uint16_t * major, uint16_t * minor, uint16_t * patch, uint16_t * build, uint32_t * bootloader, uint32_t * hash)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[out]</td><td class=\"paramname\">major</td><td><p style=\"color:inherit\">Major release version </p></td></tr><tr><td>[out]</td><td class=\"paramname\">minor</td><td><p style=\"color:inherit\">Minor release version </p></td></tr><tr><td>[out]</td><td class=\"paramname\">patch</td><td><p style=\"color:inherit\">Patch release number </p></td></tr><tr><td>[out]</td><td class=\"paramname\">build</td><td><p style=\"color:inherit\">Build number </p></td></tr><tr><td>[out]</td><td class=\"paramname\">bootloader</td><td><p style=\"color:inherit\">Unused. Ignore this field. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">hash</td><td><p style=\"color:inherit\">Version hash</p></td></tr></tbody></table></div><p style=\"color:inherit\">Get the firmware version information.</p><p style=\"color:inherit\"><strong>NOTE:</strong> This command is available even if the Bluetooth stack has not been started. See <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-start-bluetooth\" target=\"_blank\" rel=\"\">sl_bt_system_start_bluetooth</a> for description of how the Bluetooth stack is started.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>1125</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_reset<span id=\"sl-bt-system-reset\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-reset\">#</a></span></h3><blockquote>void sl_bt_system_reset (uint8_t dfu)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">dfu</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-boot-mode-t\" target=\"_blank\" rel=\"\">sl_bt_system_boot_mode_t</a>. Boot mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_system_boot_mode_normal (0x0):</strong> Boot to normal mode</p></li><li><p style=\"color:inherit\"><strong>sl_bt_system_boot_mode_uart_dfu (0x1):</strong> Boot to UART DFU mode</p></li><li><p style=\"color:inherit\"><strong>sl_bt_system_boot_mode_ota_dfu (0x2):</strong> Boot to OTA DFU mode</p></li></ul><p style=\"color:inherit\">This parameter is ignored on EFR series 2 devices. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Reset the system. This command does not have a response.</p><p style=\"color:inherit\">On EFR series 1 devices, this command boots into the given mode and triggers one of the boot events (normal reset or boot to DFU mode) depending on the given boot mode.</p><p style=\"color:inherit\">On EFR series 2 devices, the <code>dfu</code> parameter is ignored and this command always boots the user application. To boot into a DFU mode on series 2, use the Bootloader API <code>bootloader_rebootAndInstall</code>.</p><p style=\"color:inherit\"><strong>NOTE:</strong> This command is available even if the Bluetooth stack has not been started. See <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-start-bluetooth\" target=\"_blank\" rel=\"\">sl_bt_system_start_bluetooth</a> for description of how the Bluetooth stack is started.</p><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-boot\" target=\"_blank\" rel=\"\">sl_bt_evt_system_boot</a> - Sent after the device has booted in normal mode.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-dfu-boot\" target=\"_blank\" rel=\"\">sl_bt_evt_dfu_boot</a> - Sent after the device has booted in UART DFU mode. </p></li></ul><br><div>Definition at line <code>1164</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_halt<span id=\"sl-bt-system-halt\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-halt\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_halt (uint8_t halt)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">halt</td><td><p style=\"color:inherit\">Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>1:</strong> halt</p></li><li><p style=\"color:inherit\"><strong>0:</strong> resume</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Force radio to idle state and allow device to sleep. Advertising, scanning, connections, and software timers are halted by this command. Halted operations resume after calling this command with parameter 0. Connections stay alive if the system is resumed before connection supervision timeout.</p><p style=\"color:inherit\">Use this command only for a short time period (maximum few seconds). Although it halts Bluetooth activity, all tasks and operations still exist inside the stack with their own concepts of time. Halting the system for a long time period may have negative consequences on stack's internal states.</p><p style=\"color:inherit\"><strong>NOTE:</strong> The software timer is also halted. Hardware interrupts are the only way to wake up from energy mode 2 when the system is halted.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>1188</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_linklayer_configure<span id=\"sl-bt-system-linklayer-configure\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-linklayer-configure\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_linklayer_configure (uint8_t key, size_t data_len, const uint8_t * data)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">key</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-linklayer-config-key-t\" target=\"_blank\" rel=\"\">sl_bt_system_linklayer_config_key_t</a>. Key to configure. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_halt (0x1):</strong> Same as system_halt command, value-0 Stop Radio 1- Start Radio</p></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_priority_range (0x2):</strong> Sets the RAIL priority_mapping offset field of the link layer priority configuration structure to the first byte of the value field.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_scan_channels (0x3):</strong> Sets channels to scan on. The first byte of the value is the channel map. 0x1 = Channel 37, 0x2 = Channel 38, 0x4 = Channel 39</p></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_set_flags (0x4):</strong> Sets the link layer configuration flags. The value is a little endian 32-bit integer. Flag Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">0x00000001 - Disable Feature Exchange in peripheral role of the connection</p></li><li><p style=\"color:inherit\">0x00000002 - Disable Feature Exchange in central role of the connection</p></li></ul></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_clr_flags (0x5):</strong> The value is flags to clear. Flags are the same as in SET_FLAGS command.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_set_afh_interval (0x7):</strong> Set the afh_scan_interval. Value is in units of 10 ms. Setting the interval to 0 will result in using the default value of 1 second.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_set_priority_table (0x9):</strong> The value contains a priority table to be copied over the existing table. If the value is smaller than the full table, only those values are updated. See sl_bt_bluetooth_ll_priorities struct for the definition of a priority table.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_set_rx_packet_filtering (0xa):</strong> Configure and enable or disable RX packet filtering feature. Value: &gt;= 5 bytes.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Byte 1 - The filter count</p></li><li><p style=\"color:inherit\">Byte 2 - The filter offset</p></li><li><p style=\"color:inherit\">Byte 3 - The length of the filter list</p></li><li><p style=\"color:inherit\">Byte 4 - The bitmask flags</p></li><li><p style=\"color:inherit\">Rest of the data - The filter list</p></li></ul></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_set_simultaneous_scanning (0xb):</strong> Enable or disable simultaneous scanning on the 1M and Coded PHYs. Value: 1 byte.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">0 - Disable simultaneous scanning.</p></li><li><p style=\"color:inherit\">1 - Enable simultaneous scanning.</p></li></ul></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_set_channelmap_flags (0xc):</strong> Configure channelmap adaptivity flags. Value: 4 bytes.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_power_control_golden_range (0x10):</strong> Set Power Control golden range parameters. The value is a 8-bytes long array that consists of 4 pairs of golden range configurations. In each pair, the first byte is the lower RSSI boundary and the second byte is the upper RSSI boundary. RSSI values are in dBm. This configuration is not allowed if there are active Bluetooth connections.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Byte 1 - Minimal RSSI on 1M PHY</p></li><li><p style=\"color:inherit\">Byte 2 - Maximal RSSI on 1M PHY</p></li><li><p style=\"color:inherit\">Byte 3 - Minimal RSSI on 2M PHY</p></li><li><p style=\"color:inherit\">Byte 4 - Maximal RSSI on 2M PHY</p></li><li><p style=\"color:inherit\">Byte 5 - Minimal RSSI on Coded PHY S=8</p></li><li><p style=\"color:inherit\">Byte 6 - Maximal RSSI on Coded PHY S=8</p></li><li><p style=\"color:inherit\">Byte 7 - Minimal RSSI on Coded PHY S=2</p></li><li><p style=\"color:inherit\">Byte 8 - Maximal RSSI on Coded PHY S=2</p></li></ul></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_active_scanner_backoff_upper_limit (0x11):</strong> Value: uint16_t Adjust upper limit for backoff counter. If 0 restores default value of 256 Value must be between 16 - 256</p></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_afh_rssi_threshold (0x12):</strong> Value: int8_t Configures RSSI limit for AFH channel blocking</p></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_afh_channel_cooldown (0x13):</strong> Value: int16_t Configures how long channel is blocked after activity is detected Default: 8000</p></li><li><p style=\"color:inherit\"><strong>sl_bt_system_linklayer_config_key_set_report_all_scan_rsps (0x14):</strong> Value: uint8_t 0 - default, only reports scan responses that is received after sending scan_req nonzero - Will report all scan responses that are received on primary advertising channels </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">data_len</td><td><p style=\"color:inherit\">Length of data in <code>data</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">data</td><td><p style=\"color:inherit\">Configuration data. Length and contents of the data field depend on the key value used.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Send configuration data to the link layer. This command fine tunes low-level Bluetooth operations.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>1276</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_set_tx_power<span id=\"sl-bt-system-set-tx-power\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-set-tx-power\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_set_tx_power (int16_t min_power, int16_t max_power, int16_t * set_min, int16_t * set_max)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">min_power</td><td><p style=\"color:inherit\">Minimum radiated TX power. Unit: 0.1 dBm. For example, the value 10 means 1 dBm. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">max_power</td><td><p style=\"color:inherit\">Maximum radiated TX power. Unit: 0.1 dBm. For example, the value 10 means 1 dBm. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">set_min</td><td><p style=\"color:inherit\">The selected minimum radiated TX power. Unit: 0.1 dBm </p></td></tr><tr><td>[out]</td><td class=\"paramname\">set_max</td><td><p style=\"color:inherit\">The selected maximum radiated TX power. Unit: 0.1 dBm</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set the global minimum and maximum radiated TX power levels for Bluetooth. This returns selected power levels that are radiated from the antenna at TX. The transmitter power at antenna pin will apply the RF TX path gain to match this setting. RF TX path gain can be set in the Bluetooth configuration. If the GATT server contains a TX power service, the TX Power Level attribute will be updated with the selected maximum power level.</p><p style=\"color:inherit\">A selected power level may be different than the requested value because of Bluetooth feature restrictions or the device's radio characteristics. For Bluetooth connections, the maximum radiated TX power is limited to 10 dBm if Adaptive Frequency Hopping (AFH) is not enabled.</p><p style=\"color:inherit\">The minimum TX power setting is used by LE power control. It has no effect in Bluetooth stack if the LE power control feature is not enabled. However, the application may still use this setting for other purposes, e.g., setting the minimum TX power for DTM transmitter test.</p><p style=\"color:inherit\">The minimum and maximum radiated TX power levels can also be configured in the Bluetooth configuration and passed into the Bluetooth stack initialization. By default, the minimum radiated TX power level is configured to -3 dBm and the maximum radiated TX power level to 8 dBm.</p><p style=\"color:inherit\"><strong>NOTE:</strong> Do not use this command while advertising or scanning. Furthermore, the stack does not allow setting TX powers during connections.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>1317</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_get_tx_power_setting<span id=\"sl-bt-system-get-tx-power-setting\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-get-tx-power-setting\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_get_tx_power_setting (int16_t * support_min, int16_t * support_max, int16_t * set_min, int16_t * set_max, int16_t * rf_path_gain)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[out]</td><td class=\"paramname\">support_min</td><td><p style=\"color:inherit\">The minimum radiated TX power the device supports. Unit: 0.1 dBm </p></td></tr><tr><td>[out]</td><td class=\"paramname\">support_max</td><td><p style=\"color:inherit\">The maximum radiated TX power the device supports. Unit: 0.1 dBm </p></td></tr><tr><td>[out]</td><td class=\"paramname\">set_min</td><td><p style=\"color:inherit\">The minimum radiated TX power currently set in stack. Unit: 0.1 dBm </p></td></tr><tr><td>[out]</td><td class=\"paramname\">set_max</td><td><p style=\"color:inherit\">The maximum radiated TX power currently set in stack. Unit: 0.1 dBm </p></td></tr><tr><td>[out]</td><td class=\"paramname\">rf_path_gain</td><td><p style=\"color:inherit\">TX RF path gain. Unit: 0.1 dBm</p></td></tr></tbody></table></div><p style=\"color:inherit\">Get TX power settings including the minimum and maximum radiated TX power levels the device supports, the minimum and maximum radiated TX power levels currently set in the stack, and the TX RF path gain configuration.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>1341</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_set_identity_address<span id=\"sl-bt-system-set-identity-address\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-set-identity-address\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_set_identity_address (<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> address, uint8_t type)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">address</td><td><p style=\"color:inherit\">Bluetooth identity address in little endian format </p></td></tr><tr><td>[in]</td><td class=\"paramname\">type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>. Identity address type. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address (0x0):</strong> Public device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Store the device's Bluetooth identity address in persistent storage using NVM keys. The address can be a public device address or a static device address. The stack returns an error if the static device address does not conform to the Bluetooth specification.</p><p style=\"color:inherit\">The new address will be effective in the next system reboot. The stack will use the address in the NVM keys when present. Otherwise, it uses the default Bluetooth public device address which is programmed at production.</p><p style=\"color:inherit\">The stack treats 00:00:00:00:00:00 and ff:ff:ff:ff:ff:ff as invalid addresses. Therefore, passing one of them into this command will cause the stack to delete the NVM keys and use the default address in the next system reboot.</p><p style=\"color:inherit\"><strong>Note:</strong> Because the NVM keys are located in flash and flash wearing can occur, avoid calling this command regularly.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>1375</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_get_identity_address<span id=\"sl-bt-system-get-identity-address\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-get-identity-address\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_get_identity_address (<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> * address, uint8_t * type)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[out]</td><td class=\"paramname\">address</td><td><p style=\"color:inherit\">Bluetooth identity address in little endian format </p></td></tr><tr><td>[out]</td><td class=\"paramname\">type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>. Identity address type. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address (0x0):</strong> Public device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Read the Bluetooth identity address used by the device, which can be a public or random static device address.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>1391</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_get_random_data<span id=\"sl-bt-system-get-random-data\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-get-random-data\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_get_random_data (uint8_t length, size_t max_data_size, size_t * data_len, uint8_t * data)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">length</td><td><p style=\"color:inherit\">Length of random data. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">max_data_size</td><td><p style=\"color:inherit\">Size of output buffer passed in <code>data</code></p></td></tr><tr><td>[out]</td><td class=\"paramname\">data_len</td><td><p style=\"color:inherit\">On return, set to the length of output data written to <code>data</code></p></td></tr><tr><td>[out]</td><td class=\"paramname\">data</td><td><p style=\"color:inherit\">Random data</p></td></tr></tbody></table></div><p style=\"color:inherit\">Get random data.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>1406</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_data_buffer_write<span id=\"sl-bt-system-data-buffer-write\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-data-buffer-write\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_data_buffer_write (size_t data_len, const uint8_t * data)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">data_len</td><td><p style=\"color:inherit\">Length of data in <code>data</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">data</td><td><p style=\"color:inherit\">Data to write</p></td></tr></tbody></table></div><p style=\"color:inherit\">Write data into the system data buffer. Data will be appended to the end of existing data.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>1422</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_data_buffer_clear<span id=\"sl-bt-system-data-buffer-clear\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-data-buffer-clear\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_data_buffer_clear ()</blockquote><p style=\"color:inherit\">Remove all data from the system data buffer.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>1433</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_get_counters<span id=\"sl-bt-system-get-counters\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-get-counters\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_get_counters (uint8_t reset, uint16_t * tx_packets, uint16_t * rx_packets, uint16_t * crc_errors, uint16_t * failures)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">reset</td><td><p style=\"color:inherit\">Reset counters if the parameter value is not zero. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">tx_packets</td><td><p style=\"color:inherit\">The number of successfully transmitted packets </p></td></tr><tr><td>[out]</td><td class=\"paramname\">rx_packets</td><td><p style=\"color:inherit\">The number of successfully received packets </p></td></tr><tr><td>[out]</td><td class=\"paramname\">crc_errors</td><td><p style=\"color:inherit\">The number of received packets with CRC errors </p></td></tr><tr><td>[out]</td><td class=\"paramname\">failures</td><td><p style=\"color:inherit\">The number of radio failures, such as aborted TX/RX packets, scheduling failures, and so on.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Get packet and error counters. Passing a non-zero value also resets counters.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>1449</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_system_set_lazy_soft_timer<span id=\"sl-bt-system-set-lazy-soft-timer\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-system-set-lazy-soft-timer\">#</a></span></h3><blockquote>sl_status_t sl_bt_system_set_lazy_soft_timer (uint32_t time, uint32_t slack, uint8_t handle, uint8_t single_shot)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">time</td><td><p style=\"color:inherit\">An interval between how often to send events in hardware clock ticks (1 second is equal to 32768 ticks).</p><p style=\"color:inherit\">The smallest interval value supported is 328, which is around 10 milliseconds. Any parameters between 0 and 328 will be rounded up to 328. The maximum value is 2147483647, which corresponds to about 18.2 hours.</p><p style=\"color:inherit\">If <code>time</code> is 0, removes the scheduled timer with the same handle. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">slack</td><td><p style=\"color:inherit\">Slack time in hardware clock ticks </p></td></tr><tr><td>[in]</td><td class=\"paramname\">handle</td><td><p style=\"color:inherit\">Timer handle to use, which is returned in timeout event </p></td></tr><tr><td>[in]</td><td class=\"paramname\">single_shot</td><td><p style=\"color:inherit\">Timer mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> false (timer is repeating)</p></li><li><p style=\"color:inherit\"><strong>1:</strong> true (timer runs only once)</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> . Use the sleeptimer component (in platform services category) for timers. Because the sleeptimer does not support a timer with slack yet, the Bluetooth stack will continue to support this command until another component provides the functionality.</p><p style=\"color:inherit\">Start a software timer with slack. The slack parameter allows the stack to optimize wakeups and save power. The timer event is triggered between time and time + <code>slack</code>.</p><p style=\"color:inherit\">Multiple concurrent timers can be running simultaneously. 256 unique timer handles (IDs) are available. The maximum number of concurrent timers is configurable at device initialization. Up to 16 concurrent timers can be configured. The default configuration is 4. As the RAM for storing timer data is pre-allocated at initialization, an application should not configure the amount more than it needs for minimizing RAM usage.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-system-soft-timer\" target=\"_blank\" rel=\"\">sl_bt_evt_system_soft_timer</a> - Sent after this timer has lapsed. </p></li></ul><br><div>Definition at line <code>1495</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_system_hello_id<span id=\"sl-bt-cmd-system-hello-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-hello-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_hello_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00010020</pre><br><div>Definition at line <code>234</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_start_bluetooth_id<span id=\"sl-bt-cmd-system-start-bluetooth-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-start-bluetooth-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_start_bluetooth_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1c010020</pre><br><div>Definition at line <code>235</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_stop_bluetooth_id<span id=\"sl-bt-cmd-system-stop-bluetooth-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-stop-bluetooth-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_stop_bluetooth_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1d010020</pre><br><div>Definition at line <code>236</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_forcefully_stop_bluetooth_id<span id=\"sl-bt-cmd-system-forcefully-stop-bluetooth-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-forcefully-stop-bluetooth-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_forcefully_stop_bluetooth_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1e010020</pre><br><div>Definition at line <code>237</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_get_version_id<span id=\"sl-bt-cmd-system-get-version-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-get-version-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_get_version_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1b010020</pre><br><div>Definition at line <code>238</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_reset_id<span id=\"sl-bt-cmd-system-reset-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-reset-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_reset_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01010020</pre><br><div>Definition at line <code>239</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_halt_id<span id=\"sl-bt-cmd-system-halt-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-halt-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_halt_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0c010020</pre><br><div>Definition at line <code>240</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_linklayer_configure_id<span id=\"sl-bt-cmd-system-linklayer-configure-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-linklayer-configure-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_linklayer_configure_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0e010020</pre><br><div>Definition at line <code>241</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_set_tx_power_id<span id=\"sl-bt-cmd-system-set-tx-power-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-set-tx-power-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_set_tx_power_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x17010020</pre><br><div>Definition at line <code>242</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_get_tx_power_setting_id<span id=\"sl-bt-cmd-system-get-tx-power-setting-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-get-tx-power-setting-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_get_tx_power_setting_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x18010020</pre><br><div>Definition at line <code>243</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_set_identity_address_id<span id=\"sl-bt-cmd-system-set-identity-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-set-identity-address-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_set_identity_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x13010020</pre><br><div>Definition at line <code>244</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_get_identity_address_id<span id=\"sl-bt-cmd-system-get-identity-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-get-identity-address-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_get_identity_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x15010020</pre><br><div>Definition at line <code>245</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_get_random_data_id<span id=\"sl-bt-cmd-system-get-random-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-get-random-data-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_get_random_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0b010020</pre><br><div>Definition at line <code>246</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_data_buffer_write_id<span id=\"sl-bt-cmd-system-data-buffer-write-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-data-buffer-write-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_data_buffer_write_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x12010020</pre><br><div>Definition at line <code>247</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_data_buffer_clear_id<span id=\"sl-bt-cmd-system-data-buffer-clear-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-data-buffer-clear-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_data_buffer_clear_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x14010020</pre><br><div>Definition at line <code>248</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_get_counters_id<span id=\"sl-bt-cmd-system-get-counters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-get-counters-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_get_counters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0f010020</pre><br><div>Definition at line <code>249</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_system_set_lazy_soft_timer_id<span id=\"sl-bt-cmd-system-set-lazy-soft-timer-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-system-set-lazy-soft-timer-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_system_set_lazy_soft_timer_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1a010020</pre><br><div>Definition at line <code>250</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_hello_id<span id=\"sl-bt-rsp-system-hello-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-hello-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_hello_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00010020</pre><br><div>Definition at line <code>251</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_start_bluetooth_id<span id=\"sl-bt-rsp-system-start-bluetooth-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-start-bluetooth-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_start_bluetooth_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1c010020</pre><br><div>Definition at line <code>252</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_stop_bluetooth_id<span id=\"sl-bt-rsp-system-stop-bluetooth-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-stop-bluetooth-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_stop_bluetooth_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1d010020</pre><br><div>Definition at line <code>253</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_forcefully_stop_bluetooth_id<span id=\"sl-bt-rsp-system-forcefully-stop-bluetooth-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-forcefully-stop-bluetooth-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_forcefully_stop_bluetooth_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1e010020</pre><br><div>Definition at line <code>254</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_get_version_id<span id=\"sl-bt-rsp-system-get-version-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-get-version-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_get_version_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1b010020</pre><br><div>Definition at line <code>255</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_reset_id<span id=\"sl-bt-rsp-system-reset-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-reset-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_reset_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01010020</pre><br><div>Definition at line <code>256</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_halt_id<span id=\"sl-bt-rsp-system-halt-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-halt-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_halt_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0c010020</pre><br><div>Definition at line <code>257</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_linklayer_configure_id<span id=\"sl-bt-rsp-system-linklayer-configure-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-linklayer-configure-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_linklayer_configure_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0e010020</pre><br><div>Definition at line <code>258</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_set_tx_power_id<span id=\"sl-bt-rsp-system-set-tx-power-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-set-tx-power-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_set_tx_power_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x17010020</pre><br><div>Definition at line <code>259</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_get_tx_power_setting_id<span id=\"sl-bt-rsp-system-get-tx-power-setting-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-get-tx-power-setting-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_get_tx_power_setting_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x18010020</pre><br><div>Definition at line <code>260</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_set_identity_address_id<span id=\"sl-bt-rsp-system-set-identity-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-set-identity-address-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_set_identity_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x13010020</pre><br><div>Definition at line <code>261</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_get_identity_address_id<span id=\"sl-bt-rsp-system-get-identity-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-get-identity-address-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_get_identity_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x15010020</pre><br><div>Definition at line <code>262</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_get_random_data_id<span id=\"sl-bt-rsp-system-get-random-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-get-random-data-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_get_random_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0b010020</pre><br><div>Definition at line <code>263</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_data_buffer_write_id<span id=\"sl-bt-rsp-system-data-buffer-write-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-data-buffer-write-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_data_buffer_write_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x12010020</pre><br><div>Definition at line <code>264</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_data_buffer_clear_id<span id=\"sl-bt-rsp-system-data-buffer-clear-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-data-buffer-clear-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_data_buffer_clear_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x14010020</pre><br><div>Definition at line <code>265</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_get_counters_id<span id=\"sl-bt-rsp-system-get-counters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-get-counters-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_get_counters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0f010020</pre><br><div>Definition at line <code>266</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_system_set_lazy_soft_timer_id<span id=\"sl-bt-rsp-system-set-lazy-soft-timer-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-system-set-lazy-soft-timer-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_system_set_lazy_soft_timer_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1a010020</pre><br><div>Definition at line <code>267</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system", "status": "success"}