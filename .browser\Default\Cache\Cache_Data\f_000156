(globalThis["webpackChunklauncher"]=globalThis["webpackChunklauncher"]||[]).push([[736],{9984:e=>{e.exports=function(e,t,n){const r=void 0!==e.__vccOpts?e.__vccOpts:e,o=r[t];if(void 0===o)r[t]=n;else for(const a in n)void 0===o[a]&&(o[a]=n[a])}},499:(e,t,n)=>{"use strict";n.d(t,{Bj:()=>a,Fl:()=>$e,IU:()=>Ee,Jd:()=>x,PG:()=>xe,SU:()=>ze,Um:()=>ye,WL:()=>Ie,X$:()=>E,X3:()=>Le,XI:()=>Re,Xl:()=>Ae,dq:()=>Oe,iH:()=>Me,j:()=>C,lk:()=>S,qj:()=>we,qq:()=>w,yT:()=>Ce});var r=n(6970);let o;class a{constructor(e=!1){this.active=!0,this.effects=[],this.cleanups=[],!e&&o&&(this.parent=o,this.index=(o.scopes||(o.scopes=[])).push(this)-1)}run(e){if(this.active){const t=o;try{return o=this,e()}finally{o=t}}else 0}on(){o=this}off(){o=this.parent}stop(e){if(this.active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.active=!1}}}function i(e,t=o){t&&t.active&&t.effects.push(e)}const l=e=>{const t=new Set(e);return t.w=0,t.n=0,t},s=e=>(e.w&h)>0,c=e=>(e.n&h)>0,d=({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=h},u=e=>{const{deps:t}=e;if(t.length){let n=0;for(let r=0;r<t.length;r++){const o=t[r];s(o)&&!c(o)?o.delete(e):t[n++]=o,o.w&=~h,o.n&=~h}t.length=n}},p=new WeakMap;let f=0,h=1;const m=30;let v;const g=Symbol(""),b=Symbol("");class w{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,i(this,n)}run(){if(!this.active)return this.fn();let e=v,t=k;while(e){if(e===this)return;e=e.parent}try{return this.parent=v,v=this,k=!0,h=1<<++f,f<=m?d(this):y(this),this.fn()}finally{f<=m&&u(this),h=1<<--f,v=this.parent,k=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){v===this?this.deferStop=!0:this.active&&(y(this),this.onStop&&this.onStop(),this.active=!1)}}function y(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let k=!0;const _=[];function x(){_.push(k),k=!1}function S(){const e=_.pop();k=void 0===e||e}function C(e,t,n){if(k&&v){let t=p.get(e);t||p.set(e,t=new Map);let r=t.get(n);r||t.set(n,r=l());const o=void 0;L(r,o)}}function L(e,t){let n=!1;f<=m?c(e)||(e.n|=h,n=!s(e)):n=!e.has(v),n&&(e.add(v),v.deps.push(e))}function E(e,t,n,o,a,i){const s=p.get(e);if(!s)return;let c=[];if("clear"===t)c=[...s.values()];else if("length"===n&&(0,r.kJ)(e))s.forEach(((e,t)=>{("length"===t||t>=o)&&c.push(e)}));else switch(void 0!==n&&c.push(s.get(n)),t){case"add":(0,r.kJ)(e)?(0,r.S0)(n)&&c.push(s.get("length")):(c.push(s.get(g)),(0,r._N)(e)&&c.push(s.get(b)));break;case"delete":(0,r.kJ)(e)||(c.push(s.get(g)),(0,r._N)(e)&&c.push(s.get(b)));break;case"set":(0,r._N)(e)&&c.push(s.get(g));break}if(1===c.length)c[0]&&A(c[0]);else{const e=[];for(const t of c)t&&e.push(...t);A(l(e))}}function A(e,t){for(const n of(0,r.kJ)(e)?e:[...e])(n!==v||n.allowRecurse)&&(n.scheduler?n.scheduler():n.run())}const q=(0,r.fY)("__proto__,__v_isRef,__isVue"),P=new Set(Object.getOwnPropertyNames(Symbol).map((e=>Symbol[e])).filter(r.yk)),T=F(),j=F(!1,!0),O=F(!0),M=R();function R(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Ee(this);for(let t=0,o=this.length;t<o;t++)C(n,"get",t+"");const r=n[t](...e);return-1===r||!1===r?n[t](...e.map(Ee)):r}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){x();const n=Ee(this)[t].apply(this,e);return S(),n}})),e}function F(e=!1,t=!1){return function(n,o,a){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_isShallow"===o)return t;if("__v_raw"===o&&a===(e?t?ve:me:t?he:fe).get(n))return n;const i=(0,r.kJ)(n);if(!e&&i&&(0,r.RI)(M,o))return Reflect.get(M,o,a);const l=Reflect.get(n,o,a);if((0,r.yk)(o)?P.has(o):q(o))return l;if(e||C(n,"get",o),t)return l;if(Oe(l)){const e=!i||!(0,r.S0)(o);return e?l.value:l}return(0,r.Kn)(l)?e?ke(l):we(l):l}}const H=B(),z=B(!0);function B(e=!1){return function(t,n,o,a){let i=t[n];if(Se(i)&&Oe(i)&&!Oe(o))return!1;if(!e&&!Se(o)&&(Ce(o)||(o=Ee(o),i=Ee(i)),!(0,r.kJ)(t)&&Oe(i)&&!Oe(o)))return i.value=o,!0;const l=(0,r.kJ)(t)&&(0,r.S0)(n)?Number(n)<t.length:(0,r.RI)(t,n),s=Reflect.set(t,n,o,a);return t===Ee(a)&&(l?(0,r.aU)(o,i)&&E(t,"set",n,o,i):E(t,"add",n,o)),s}}function I(e,t){const n=(0,r.RI)(e,t),o=e[t],a=Reflect.deleteProperty(e,t);return a&&n&&E(e,"delete",t,void 0,o),a}function V(e,t){const n=Reflect.has(e,t);return(0,r.yk)(t)&&P.has(t)||C(e,"has",t),n}function $(e){return C(e,"iterate",(0,r.kJ)(e)?"length":g),Reflect.ownKeys(e)}const N={get:T,set:H,deleteProperty:I,has:V,ownKeys:$},U={get:O,set(e,t){return!0},deleteProperty(e,t){return!0}},D=(0,r.l7)({},N,{get:j,set:z}),Z=e=>e,W=e=>Reflect.getPrototypeOf(e);function K(e,t,n=!1,r=!1){e=e["__v_raw"];const o=Ee(e),a=Ee(t);t!==a&&!n&&C(o,"get",t),!n&&C(o,"get",a);const{has:i}=W(o),l=r?Z:n?Pe:qe;return i.call(o,t)?l(e.get(t)):i.call(o,a)?l(e.get(a)):void(e!==o&&e.get(t))}function G(e,t=!1){const n=this["__v_raw"],r=Ee(n),o=Ee(e);return e!==o&&!t&&C(r,"has",e),!t&&C(r,"has",o),e===o?n.has(e):n.has(e)||n.has(o)}function J(e,t=!1){return e=e["__v_raw"],!t&&C(Ee(e),"iterate",g),Reflect.get(e,"size",e)}function Y(e){e=Ee(e);const t=Ee(this),n=W(t),r=n.has.call(t,e);return r||(t.add(e),E(t,"add",e,e)),this}function Q(e,t){t=Ee(t);const n=Ee(this),{has:o,get:a}=W(n);let i=o.call(n,e);i||(e=Ee(e),i=o.call(n,e));const l=a.call(n,e);return n.set(e,t),i?(0,r.aU)(t,l)&&E(n,"set",e,t,l):E(n,"add",e,t),this}function X(e){const t=Ee(this),{has:n,get:r}=W(t);let o=n.call(t,e);o||(e=Ee(e),o=n.call(t,e));const a=r?r.call(t,e):void 0,i=t.delete(e);return o&&E(t,"delete",e,void 0,a),i}function ee(){const e=Ee(this),t=0!==e.size,n=void 0,r=e.clear();return t&&E(e,"clear",void 0,void 0,n),r}function te(e,t){return function(n,r){const o=this,a=o["__v_raw"],i=Ee(a),l=t?Z:e?Pe:qe;return!e&&C(i,"iterate",g),a.forEach(((e,t)=>n.call(r,l(e),l(t),o)))}}function ne(e,t,n){return function(...o){const a=this["__v_raw"],i=Ee(a),l=(0,r._N)(i),s="entries"===e||e===Symbol.iterator&&l,c="keys"===e&&l,d=a[e](...o),u=n?Z:t?Pe:qe;return!t&&C(i,"iterate",c?b:g),{next(){const{value:e,done:t}=d.next();return t?{value:e,done:t}:{value:s?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function re(e){return function(...t){return"delete"!==e&&this}}function oe(){const e={get(e){return K(this,e)},get size(){return J(this)},has:G,add:Y,set:Q,delete:X,clear:ee,forEach:te(!1,!1)},t={get(e){return K(this,e,!1,!0)},get size(){return J(this)},has:G,add:Y,set:Q,delete:X,clear:ee,forEach:te(!1,!0)},n={get(e){return K(this,e,!0)},get size(){return J(this,!0)},has(e){return G.call(this,e,!0)},add:re("add"),set:re("set"),delete:re("delete"),clear:re("clear"),forEach:te(!0,!1)},r={get(e){return K(this,e,!0,!0)},get size(){return J(this,!0)},has(e){return G.call(this,e,!0)},add:re("add"),set:re("set"),delete:re("delete"),clear:re("clear"),forEach:te(!0,!0)},o=["keys","values","entries",Symbol.iterator];return o.forEach((o=>{e[o]=ne(o,!1,!1),n[o]=ne(o,!0,!1),t[o]=ne(o,!1,!0),r[o]=ne(o,!0,!0)})),[e,n,t,r]}const[ae,ie,le,se]=oe();function ce(e,t){const n=t?e?se:le:e?ie:ae;return(t,o,a)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get((0,r.RI)(n,o)&&o in t?n:t,o,a)}const de={get:ce(!1,!1)},ue={get:ce(!1,!0)},pe={get:ce(!0,!1)};const fe=new WeakMap,he=new WeakMap,me=new WeakMap,ve=new WeakMap;function ge(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function be(e){return e["__v_skip"]||!Object.isExtensible(e)?0:ge((0,r.W7)(e))}function we(e){return Se(e)?e:_e(e,!1,N,de,fe)}function ye(e){return _e(e,!1,D,ue,he)}function ke(e){return _e(e,!0,U,pe,me)}function _e(e,t,n,o,a){if(!(0,r.Kn)(e))return e;if(e["__v_raw"]&&(!t||!e["__v_isReactive"]))return e;const i=a.get(e);if(i)return i;const l=be(e);if(0===l)return e;const s=new Proxy(e,2===l?o:n);return a.set(e,s),s}function xe(e){return Se(e)?xe(e["__v_raw"]):!(!e||!e["__v_isReactive"])}function Se(e){return!(!e||!e["__v_isReadonly"])}function Ce(e){return!(!e||!e["__v_isShallow"])}function Le(e){return xe(e)||Se(e)}function Ee(e){const t=e&&e["__v_raw"];return t?Ee(t):e}function Ae(e){return(0,r.Nj)(e,"__v_skip",!0),e}const qe=e=>(0,r.Kn)(e)?we(e):e,Pe=e=>(0,r.Kn)(e)?ke(e):e;function Te(e){k&&v&&(e=Ee(e),L(e.dep||(e.dep=l())))}function je(e,t){e=Ee(e),e.dep&&A(e.dep)}function Oe(e){return!(!e||!0!==e.__v_isRef)}function Me(e){return Fe(e,!1)}function Re(e){return Fe(e,!0)}function Fe(e,t){return Oe(e)?e:new He(e,t)}class He{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Ee(e),this._value=t?e:qe(e)}get value(){return Te(this),this._value}set value(e){e=this.__v_isShallow?e:Ee(e),(0,r.aU)(e,this._rawValue)&&(this._rawValue=e,this._value=this.__v_isShallow?e:qe(e),je(this,e))}}function ze(e){return Oe(e)?e.value:e}const Be={get:(e,t,n)=>ze(Reflect.get(e,t,n)),set:(e,t,n,r)=>{const o=e[t];return Oe(o)&&!Oe(n)?(o.value=n,!0):Reflect.set(e,t,n,r)}};function Ie(e){return xe(e)?e:new Proxy(e,Be)}class Ve{constructor(e,t,n,r){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this._dirty=!0,this.effect=new w(e,(()=>{this._dirty||(this._dirty=!0,je(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!r,this["__v_isReadonly"]=n}get value(){const e=Ee(this);return Te(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function $e(e,t,n=!1){let o,a;const i=(0,r.mf)(e);i?(o=e,a=r.dG):(o=e.get,a=e.set);const l=new Ve(o,a,i||!a,n);return l}},9835:(e,t,n)=>{"use strict";n.d(t,{$d:()=>i,Ah:()=>je,Cn:()=>V,F4:()=>cn,FN:()=>qn,Fl:()=>Dn,HY:()=>Vt,JJ:()=>Y,Jd:()=>Te,Ko:()=>bn,Nv:()=>wn,P$:()=>se,Q2:()=>zt,Q6:()=>he,U2:()=>de,Uk:()=>un,Us:()=>kt,WI:()=>yn,Wm:()=>ln,Xn:()=>qe,Y3:()=>_,Y8:()=>ae,YP:()=>ee,_:()=>an,aZ:()=>me,bv:()=>Ae,dD:()=>I,dG:()=>vn,dl:()=>we,f3:()=>Q,h:()=>Zn,iD:()=>Qt,ic:()=>Pe,j4:()=>Xt,kq:()=>pn,lR:()=>Ot,nK:()=>fe,se:()=>ye,up:()=>Ft,w5:()=>$,wF:()=>Ee,wg:()=>Wt,wy:()=>ft,xv:()=>$t});var r=n(499),o=n(6970);function a(e,t,n,r){let o;try{o=r?e(...r):e()}catch(a){l(a,t,n)}return o}function i(e,t,n,r){if((0,o.mf)(e)){const i=a(e,t,n,r);return i&&(0,o.tI)(i)&&i.catch((e=>{l(e,t,n)})),i}const s=[];for(let o=0;o<e.length;o++)s.push(i(e[o],t,n,r));return s}function l(e,t,n,r=!0){const o=t?t.vnode:null;if(t){let r=t.parent;const o=t.proxy,i=n;while(r){const t=r.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,o,i))return;r=r.parent}const l=t.appContext.config.errorHandler;if(l)return void a(l,null,10,[e,o,i])}s(e,n,o,r)}function s(e,t,n,r=!0){console.error(e)}let c=!1,d=!1;const u=[];let p=0;const f=[];let h=null,m=0;const v=[];let g=null,b=0;const w=Promise.resolve();let y=null,k=null;function _(e){const t=y||w;return e?t.then(this?e.bind(this):e):t}function x(e){let t=p+1,n=u.length;while(t<n){const r=t+n>>>1,o=j(u[r]);o<e?t=r+1:n=r}return t}function S(e){u.length&&u.includes(e,c&&e.allowRecurse?p+1:p)||e===k||(null==e.id?u.push(e):u.splice(x(e.id),0,e),C())}function C(){c||d||(d=!0,y=w.then(O))}function L(e){const t=u.indexOf(e);t>p&&u.splice(t,1)}function E(e,t,n,r){(0,o.kJ)(e)?n.push(...e):t&&t.includes(e,e.allowRecurse?r+1:r)||n.push(e),C()}function A(e){E(e,h,f,m)}function q(e){E(e,g,v,b)}function P(e,t=null){if(f.length){for(k=t,h=[...new Set(f)],f.length=0,m=0;m<h.length;m++)h[m]();h=null,m=0,k=null,P(e,t)}}function T(e){if(v.length){const e=[...new Set(v)];if(v.length=0,g)return void g.push(...e);for(g=e,g.sort(((e,t)=>j(e)-j(t))),b=0;b<g.length;b++)g[b]();g=null,b=0}}const j=e=>null==e.id?1/0:e.id;function O(e){d=!1,c=!0,P(e),u.sort(((e,t)=>j(e)-j(t)));o.dG;try{for(p=0;p<u.length;p++){const e=u[p];e&&!1!==e.active&&a(e,null,14)}}finally{p=0,u.length=0,T(e),c=!1,y=null,(u.length||f.length||v.length)&&O(e)}}new Set;new Map;function M(e,t,...n){if(e.isUnmounted)return;const r=e.vnode.props||o.kT;let a=n;const l=t.startsWith("update:"),s=l&&t.slice(7);if(s&&s in r){const e=`${"modelValue"===s?"model":s}Modifiers`,{number:t,trim:i}=r[e]||o.kT;i?a=n.map((e=>e.trim())):t&&(a=n.map(o.He))}let c;let d=r[c=(0,o.hR)(t)]||r[c=(0,o.hR)((0,o._A)(t))];!d&&l&&(d=r[c=(0,o.hR)((0,o.rs)(t))]),d&&i(d,e,6,a);const u=r[c+"Once"];if(u){if(e.emitted){if(e.emitted[c])return}else e.emitted={};e.emitted[c]=!0,i(u,e,6,a)}}function R(e,t,n=!1){const r=t.emitsCache,a=r.get(e);if(void 0!==a)return a;const i=e.emits;let l={},s=!1;if(!(0,o.mf)(e)){const r=e=>{const n=R(e,t,!0);n&&(s=!0,(0,o.l7)(l,n))};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}return i||s?((0,o.kJ)(i)?i.forEach((e=>l[e]=null)):(0,o.l7)(l,i),r.set(e,l),l):(r.set(e,null),null)}function F(e,t){return!(!e||!(0,o.F7)(t))&&(t=t.slice(2).replace(/Once$/,""),(0,o.RI)(e,t[0].toLowerCase()+t.slice(1))||(0,o.RI)(e,(0,o.rs)(t))||(0,o.RI)(e,t))}let H=null,z=null;function B(e){const t=H;return H=e,z=e&&e.type.__scopeId||null,t}function I(e){z=e}function V(){z=null}function $(e,t=H,n){if(!t)return e;if(e._n)return e;const r=(...n)=>{r._d&&Jt(-1);const o=B(t),a=e(...n);return B(o),r._d&&Jt(1),a};return r._n=!0,r._c=!0,r._d=!0,r}function N(e){const{type:t,vnode:n,proxy:r,withProxy:a,props:i,propsOptions:[s],slots:c,attrs:d,emit:u,render:p,renderCache:f,data:h,setupState:m,ctx:v,inheritAttrs:g}=e;let b,w;const y=B(e);try{if(4&n.shapeFlag){const e=a||r;b=fn(p.call(e,e,f,i,m,h,v)),w=d}else{const e=t;0,b=fn(e.length>1?e(i,{attrs:d,slots:c,emit:u}):e(i,null)),w=t.props?d:U(d)}}catch(_){Dt.length=0,l(_,e,1),b=ln(Nt)}let k=b;if(w&&!1!==g){const e=Object.keys(w),{shapeFlag:t}=k;e.length&&7&t&&(s&&e.some(o.tR)&&(w=D(w,s)),k=dn(k,w))}return n.dirs&&(k.dirs=k.dirs?k.dirs.concat(n.dirs):n.dirs),n.transition&&(k.transition=n.transition),b=k,B(y),b}const U=e=>{let t;for(const n in e)("class"===n||"style"===n||(0,o.F7)(n))&&((t||(t={}))[n]=e[n]);return t},D=(e,t)=>{const n={};for(const r in e)(0,o.tR)(r)&&r.slice(9)in t||(n[r]=e[r]);return n};function Z(e,t,n){const{props:r,children:o,component:a}=e,{props:i,children:l,patchFlag:s}=t,c=a.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&s>=0))return!(!o&&!l||l&&l.$stable)||r!==i&&(r?!i||W(r,i,c):!!i);if(1024&s)return!0;if(16&s)return r?W(r,i,c):!!i;if(8&s){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(i[n]!==r[n]&&!F(c,n))return!0}}return!1}function W(e,t,n){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let o=0;o<r.length;o++){const a=r[o];if(t[a]!==e[a]&&!F(n,a))return!0}return!1}function K({vnode:e,parent:t},n){while(t&&t.subTree===e)(e=t.vnode).el=n,t=t.parent}const G=e=>e.__isSuspense;function J(e,t){t&&t.pendingBranch?(0,o.kJ)(e)?t.effects.push(...e):t.effects.push(e):q(e)}function Y(e,t){if(An){let n=An.provides;const r=An.parent&&An.parent.provides;r===n&&(n=An.provides=Object.create(r)),n[e]=t}else 0}function Q(e,t,n=!1){const r=An||H;if(r){const a=null==r.parent?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides;if(a&&e in a)return a[e];if(arguments.length>1)return n&&(0,o.mf)(t)?t.call(r.proxy):t}else 0}const X={};function ee(e,t,n){return te(e,t,n)}function te(e,t,{immediate:n,deep:l,flush:s,onTrack:c,onTrigger:d}=o.kT){const u=An;let p,f,h=!1,m=!1;if((0,r.dq)(e)?(p=()=>e.value,h=(0,r.yT)(e)):(0,r.PG)(e)?(p=()=>e,l=!0):(0,o.kJ)(e)?(m=!0,h=e.some(r.PG),p=()=>e.map((e=>(0,r.dq)(e)?e.value:(0,r.PG)(e)?oe(e):(0,o.mf)(e)?a(e,u,2):void 0))):p=(0,o.mf)(e)?t?()=>a(e,u,2):()=>{if(!u||!u.isUnmounted)return f&&f(),i(e,u,3,[v])}:o.dG,t&&l){const e=p;p=()=>oe(e())}let v=e=>{f=y.onStop=()=>{a(e,u,4)}};if(Rn)return v=o.dG,t?n&&i(t,u,3,[p(),m?[]:void 0,v]):p(),o.dG;let g=m?[]:X;const b=()=>{if(y.active)if(t){const e=y.run();(l||h||(m?e.some(((e,t)=>(0,o.aU)(e,g[t]))):(0,o.aU)(e,g)))&&(f&&f(),i(t,u,3,[e,g===X?void 0:g,v]),g=e)}else y.run()};let w;b.allowRecurse=!!t,w="sync"===s?b:"post"===s?()=>yt(b,u&&u.suspense):()=>{!u||u.isMounted?A(b):b()};const y=new r.qq(p,w);return t?n?b():g=y.run():"post"===s?yt(y.run.bind(y),u&&u.suspense):y.run(),()=>{y.stop(),u&&u.scope&&(0,o.Od)(u.scope.effects,y)}}function ne(e,t,n){const r=this.proxy,a=(0,o.HD)(e)?e.includes(".")?re(r,e):()=>r[e]:e.bind(r,r);let i;(0,o.mf)(t)?i=t:(i=t.handler,n=t);const l=An;Pn(this);const s=te(a,i.bind(r),n);return l?Pn(l):Tn(),s}function re(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function oe(e,t){if(!(0,o.Kn)(e)||e["__v_skip"])return e;if(t=t||new Set,t.has(e))return e;if(t.add(e),(0,r.dq)(e))oe(e.value,t);else if((0,o.kJ)(e))for(let n=0;n<e.length;n++)oe(e[n],t);else if((0,o.DM)(e)||(0,o._N)(e))e.forEach((e=>{oe(e,t)}));else if((0,o.PO)(e))for(const n in e)oe(e[n],t);return e}function ae(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Ae((()=>{e.isMounted=!0})),Te((()=>{e.isUnmounting=!0})),e}const ie=[Function,Array],le={name:"BaseTransition",props:{mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:ie,onEnter:ie,onAfterEnter:ie,onEnterCancelled:ie,onBeforeLeave:ie,onLeave:ie,onAfterLeave:ie,onLeaveCancelled:ie,onBeforeAppear:ie,onAppear:ie,onAfterAppear:ie,onAppearCancelled:ie},setup(e,{slots:t}){const n=qn(),o=ae();let a;return()=>{const i=t.default&&he(t.default(),!0);if(!i||!i.length)return;let l=i[0];if(i.length>1){let e=!1;for(const t of i)if(t.type!==Nt){0,l=t,e=!0;break}}const s=(0,r.IU)(e),{mode:c}=s;if(o.isLeaving)return ue(l);const d=pe(l);if(!d)return ue(l);const u=de(d,s,o,n);fe(d,u);const p=n.subTree,f=p&&pe(p);let h=!1;const{getTransitionKey:m}=d.type;if(m){const e=m();void 0===a?a=e:e!==a&&(a=e,h=!0)}if(f&&f.type!==Nt&&(!tn(d,f)||h)){const e=de(f,s,o,n);if(fe(f,e),"out-in"===c)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,n.update()},ue(l);"in-out"===c&&d.type!==Nt&&(e.delayLeave=(e,t,n)=>{const r=ce(o,f);r[String(f.key)]=f,e._leaveCb=()=>{t(),e._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=n})}return l}}},se=le;function ce(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function de(e,t,n,r){const{appear:o,mode:a,persisted:l=!1,onBeforeEnter:s,onEnter:c,onAfterEnter:d,onEnterCancelled:u,onBeforeLeave:p,onLeave:f,onAfterLeave:h,onLeaveCancelled:m,onBeforeAppear:v,onAppear:g,onAfterAppear:b,onAppearCancelled:w}=t,y=String(e.key),k=ce(n,e),_=(e,t)=>{e&&i(e,r,9,t)},x={mode:a,persisted:l,beforeEnter(t){let r=s;if(!n.isMounted){if(!o)return;r=v||s}t._leaveCb&&t._leaveCb(!0);const a=k[y];a&&tn(e,a)&&a.el._leaveCb&&a.el._leaveCb(),_(r,[t])},enter(e){let t=c,r=d,a=u;if(!n.isMounted){if(!o)return;t=g||c,r=b||d,a=w||u}let i=!1;const l=e._enterCb=t=>{i||(i=!0,_(t?a:r,[e]),x.delayedLeave&&x.delayedLeave(),e._enterCb=void 0)};t?(t(e,l),t.length<=1&&l()):l()},leave(t,r){const o=String(e.key);if(t._enterCb&&t._enterCb(!0),n.isUnmounting)return r();_(p,[t]);let a=!1;const i=t._leaveCb=n=>{a||(a=!0,r(),_(n?m:h,[t]),t._leaveCb=void 0,k[o]===e&&delete k[o])};k[o]=e,f?(f(t,i),f.length<=1&&i()):i()},clone(e){return de(e,t,n,r)}};return x}function ue(e){if(ge(e))return e=dn(e),e.children=null,e}function pe(e){return ge(e)?e.children?e.children[0]:void 0:e}function fe(e,t){6&e.shapeFlag&&e.component?fe(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function he(e,t=!1,n){let r=[],o=0;for(let a=0;a<e.length;a++){let i=e[a];const l=null==n?i.key:String(n)+String(null!=i.key?i.key:a);i.type===Vt?(128&i.patchFlag&&o++,r=r.concat(he(i.children,t,l))):(t||i.type!==Nt)&&r.push(null!=l?dn(i,{key:l}):i)}if(o>1)for(let a=0;a<r.length;a++)r[a].patchFlag=-2;return r}function me(e){return(0,o.mf)(e)?{setup:e,name:e.name}:e}const ve=e=>!!e.type.__asyncLoader;const ge=e=>e.type.__isKeepAlive;RegExp,RegExp;function be(e,t){return(0,o.kJ)(e)?e.some((e=>be(e,t))):(0,o.HD)(e)?e.split(",").includes(t):!!e.test&&e.test(t)}function we(e,t){ke(e,"a",t)}function ye(e,t){ke(e,"da",t)}function ke(e,t,n=An){const r=e.__wdc||(e.__wdc=()=>{let t=n;while(t){if(t.isDeactivated)return;t=t.parent}return e()});if(Ce(t,r,n),n){let e=n.parent;while(e&&e.parent)ge(e.parent.vnode)&&_e(r,t,n,e),e=e.parent}}function _e(e,t,n,r){const a=Ce(t,e,r,!0);je((()=>{(0,o.Od)(r[t],a)}),n)}function xe(e){let t=e.shapeFlag;256&t&&(t-=256),512&t&&(t-=512),e.shapeFlag=t}function Se(e){return 128&e.shapeFlag?e.ssContent:e}function Ce(e,t,n=An,o=!1){if(n){const a=n[e]||(n[e]=[]),l=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;(0,r.Jd)(),Pn(n);const a=i(t,n,e,o);return Tn(),(0,r.lk)(),a});return o?a.unshift(l):a.push(l),l}}const Le=e=>(t,n=An)=>(!Rn||"sp"===e)&&Ce(e,t,n),Ee=Le("bm"),Ae=Le("m"),qe=Le("bu"),Pe=Le("u"),Te=Le("bum"),je=Le("um"),Oe=Le("sp"),Me=Le("rtg"),Re=Le("rtc");function Fe(e,t=An){Ce("ec",e,t)}let He=!0;function ze(e){const t=$e(e),n=e.proxy,a=e.ctx;He=!1,t.beforeCreate&&Ie(t.beforeCreate,e,"bc");const{data:i,computed:l,methods:s,watch:c,provide:d,inject:u,created:p,beforeMount:f,mounted:h,beforeUpdate:m,updated:v,activated:g,deactivated:b,beforeDestroy:w,beforeUnmount:y,destroyed:k,unmounted:_,render:x,renderTracked:S,renderTriggered:C,errorCaptured:L,serverPrefetch:E,expose:A,inheritAttrs:q,components:P,directives:T,filters:j}=t,O=null;if(u&&Be(u,a,O,e.appContext.config.unwrapInjectedRef),s)for(const r in s){const e=s[r];(0,o.mf)(e)&&(a[r]=e.bind(n))}if(i){0;const t=i.call(n,n);0,(0,o.Kn)(t)&&(e.data=(0,r.qj)(t))}if(He=!0,l)for(const r in l){const e=l[r],t=(0,o.mf)(e)?e.bind(n,n):(0,o.mf)(e.get)?e.get.bind(n,n):o.dG;0;const i=!(0,o.mf)(e)&&(0,o.mf)(e.set)?e.set.bind(n):o.dG,s=Dn({get:t,set:i});Object.defineProperty(a,r,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(c)for(const r in c)Ve(c[r],a,n,r);if(d){const e=(0,o.mf)(d)?d.call(n):d;Reflect.ownKeys(e).forEach((t=>{Y(t,e[t])}))}function M(e,t){(0,o.kJ)(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(p&&Ie(p,e,"c"),M(Ee,f),M(Ae,h),M(qe,m),M(Pe,v),M(we,g),M(ye,b),M(Fe,L),M(Re,S),M(Me,C),M(Te,y),M(je,_),M(Oe,E),(0,o.kJ)(A))if(A.length){const t=e.exposed||(e.exposed={});A.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});x&&e.render===o.dG&&(e.render=x),null!=q&&(e.inheritAttrs=q),P&&(e.components=P),T&&(e.directives=T)}function Be(e,t,n=o.dG,a=!1){(0,o.kJ)(e)&&(e=We(e));for(const i in e){const n=e[i];let l;l=(0,o.Kn)(n)?"default"in n?Q(n.from||i,n.default,!0):Q(n.from||i):Q(n),(0,r.dq)(l)&&a?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>l.value,set:e=>l.value=e}):t[i]=l}}function Ie(e,t,n){i((0,o.kJ)(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function Ve(e,t,n,r){const a=r.includes(".")?re(n,r):()=>n[r];if((0,o.HD)(e)){const n=t[e];(0,o.mf)(n)&&ee(a,n)}else if((0,o.mf)(e))ee(a,e.bind(n));else if((0,o.Kn)(e))if((0,o.kJ)(e))e.forEach((e=>Ve(e,t,n,r)));else{const r=(0,o.mf)(e.handler)?e.handler.bind(n):t[e.handler];(0,o.mf)(r)&&ee(a,r,e)}else 0}function $e(e){const t=e.type,{mixins:n,extends:r}=t,{mixins:o,optionsCache:a,config:{optionMergeStrategies:i}}=e.appContext,l=a.get(t);let s;return l?s=l:o.length||n||r?(s={},o.length&&o.forEach((e=>Ne(s,e,i,!0))),Ne(s,t,i)):s=t,a.set(t,s),s}function Ne(e,t,n,r=!1){const{mixins:o,extends:a}=t;a&&Ne(e,a,n,!0),o&&o.forEach((t=>Ne(e,t,n,!0)));for(const i in t)if(r&&"expose"===i);else{const r=Ue[i]||n&&n[i];e[i]=r?r(e[i],t[i]):t[i]}return e}const Ue={data:De,props:Ge,emits:Ge,methods:Ge,computed:Ge,beforeCreate:Ke,created:Ke,beforeMount:Ke,mounted:Ke,beforeUpdate:Ke,updated:Ke,beforeDestroy:Ke,beforeUnmount:Ke,destroyed:Ke,unmounted:Ke,activated:Ke,deactivated:Ke,errorCaptured:Ke,serverPrefetch:Ke,components:Ge,directives:Ge,watch:Je,provide:De,inject:Ze};function De(e,t){return t?e?function(){return(0,o.l7)((0,o.mf)(e)?e.call(this,this):e,(0,o.mf)(t)?t.call(this,this):t)}:t:e}function Ze(e,t){return Ge(We(e),We(t))}function We(e){if((0,o.kJ)(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Ke(e,t){return e?[...new Set([].concat(e,t))]:t}function Ge(e,t){return e?(0,o.l7)((0,o.l7)(Object.create(null),e),t):t}function Je(e,t){if(!e)return t;if(!t)return e;const n=(0,o.l7)(Object.create(null),e);for(const r in t)n[r]=Ke(e[r],t[r]);return n}function Ye(e,t,n,a=!1){const i={},l={};(0,o.Nj)(l,nn,1),e.propsDefaults=Object.create(null),Xe(e,t,i,l);for(const r in e.propsOptions[0])r in i||(i[r]=void 0);n?e.props=a?i:(0,r.Um)(i):e.type.props?e.props=i:e.props=l,e.attrs=l}function Qe(e,t,n,a){const{props:i,attrs:l,vnode:{patchFlag:s}}=e,c=(0,r.IU)(i),[d]=e.propsOptions;let u=!1;if(!(a||s>0)||16&s){let r;Xe(e,t,i,l)&&(u=!0);for(const a in c)t&&((0,o.RI)(t,a)||(r=(0,o.rs)(a))!==a&&(0,o.RI)(t,r))||(d?!n||void 0===n[a]&&void 0===n[r]||(i[a]=et(d,c,a,void 0,e,!0)):delete i[a]);if(l!==c)for(const e in l)t&&(0,o.RI)(t,e)||(delete l[e],u=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let r=0;r<n.length;r++){let a=n[r];if(F(e.emitsOptions,a))continue;const s=t[a];if(d)if((0,o.RI)(l,a))s!==l[a]&&(l[a]=s,u=!0);else{const t=(0,o._A)(a);i[t]=et(d,c,t,s,e,!1)}else s!==l[a]&&(l[a]=s,u=!0)}}u&&(0,r.X$)(e,"set","$attrs")}function Xe(e,t,n,a){const[i,l]=e.propsOptions;let s,c=!1;if(t)for(let r in t){if((0,o.Gg)(r))continue;const d=t[r];let u;i&&(0,o.RI)(i,u=(0,o._A)(r))?l&&l.includes(u)?(s||(s={}))[u]=d:n[u]=d:F(e.emitsOptions,r)||r in a&&d===a[r]||(a[r]=d,c=!0)}if(l){const t=(0,r.IU)(n),a=s||o.kT;for(let r=0;r<l.length;r++){const s=l[r];n[s]=et(i,t,s,a[s],e,!(0,o.RI)(a,s))}}return c}function et(e,t,n,r,a,i){const l=e[n];if(null!=l){const e=(0,o.RI)(l,"default");if(e&&void 0===r){const e=l.default;if(l.type!==Function&&(0,o.mf)(e)){const{propsDefaults:o}=a;n in o?r=o[n]:(Pn(a),r=o[n]=e.call(null,t),Tn())}else r=e}l[0]&&(i&&!e?r=!1:!l[1]||""!==r&&r!==(0,o.rs)(n)||(r=!0))}return r}function tt(e,t,n=!1){const r=t.propsCache,a=r.get(e);if(a)return a;const i=e.props,l={},s=[];let c=!1;if(!(0,o.mf)(e)){const r=e=>{c=!0;const[n,r]=tt(e,t,!0);(0,o.l7)(l,n),r&&s.push(...r)};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}if(!i&&!c)return r.set(e,o.Z6),o.Z6;if((0,o.kJ)(i))for(let u=0;u<i.length;u++){0;const e=(0,o._A)(i[u]);nt(e)&&(l[e]=o.kT)}else if(i){0;for(const e in i){const t=(0,o._A)(e);if(nt(t)){const n=i[e],r=l[t]=(0,o.kJ)(n)||(0,o.mf)(n)?{type:n}:n;if(r){const e=at(Boolean,r.type),n=at(String,r.type);r[0]=e>-1,r[1]=n<0||e<n,(e>-1||(0,o.RI)(r,"default"))&&s.push(t)}}}}const d=[l,s];return r.set(e,d),d}function nt(e){return"$"!==e[0]}function rt(e){const t=e&&e.toString().match(/^\s*function (\w+)/);return t?t[1]:null===e?"null":""}function ot(e,t){return rt(e)===rt(t)}function at(e,t){return(0,o.kJ)(t)?t.findIndex((t=>ot(t,e))):(0,o.mf)(t)&&ot(t,e)?0:-1}const it=e=>"_"===e[0]||"$stable"===e,lt=e=>(0,o.kJ)(e)?e.map(fn):[fn(e)],st=(e,t,n)=>{const r=$(((...e)=>lt(t(...e))),n);return r._c=!1,r},ct=(e,t,n)=>{const r=e._ctx;for(const a in e){if(it(a))continue;const n=e[a];if((0,o.mf)(n))t[a]=st(a,n,r);else if(null!=n){0;const e=lt(n);t[a]=()=>e}}},dt=(e,t)=>{const n=lt(t);e.slots.default=()=>n},ut=(e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=(0,r.IU)(t),(0,o.Nj)(t,"_",n)):ct(t,e.slots={})}else e.slots={},t&&dt(e,t);(0,o.Nj)(e.slots,nn,1)},pt=(e,t,n)=>{const{vnode:r,slots:a}=e;let i=!0,l=o.kT;if(32&r.shapeFlag){const e=t._;e?n&&1===e?i=!1:((0,o.l7)(a,t),n||1!==e||delete a._):(i=!t.$stable,ct(t,a)),l=t}else t&&(dt(e,t),l={default:1});if(i)for(const o in a)it(o)||o in l||delete a[o]};function ft(e,t){const n=H;if(null===n)return e;const r=$n(n)||n.proxy,a=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,n,l,s=o.kT]=t[i];(0,o.mf)(e)&&(e={mounted:e,updated:e}),e.deep&&oe(n),a.push({dir:e,instance:r,value:n,oldValue:void 0,arg:l,modifiers:s})}return e}function ht(e,t,n,o){const a=e.dirs,l=t&&t.dirs;for(let s=0;s<a.length;s++){const c=a[s];l&&(c.oldValue=l[s].value);let d=c.dir[o];d&&((0,r.Jd)(),i(d,n,8,[e.el,c,e,t]),(0,r.lk)())}}function mt(){return{app:null,config:{isNativeTag:o.NO,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let vt=0;function gt(e,t){return function(n,r=null){(0,o.mf)(n)||(n=Object.assign({},n)),null==r||(0,o.Kn)(r)||(r=null);const a=mt(),i=new Set;let l=!1;const s=a.app={_uid:vt++,_component:n,_props:r,_container:null,_context:a,_instance:null,version:Wn,get config(){return a.config},set config(e){0},use(e,...t){return i.has(e)||(e&&(0,o.mf)(e.install)?(i.add(e),e.install(s,...t)):(0,o.mf)(e)&&(i.add(e),e(s,...t))),s},mixin(e){return a.mixins.includes(e)||a.mixins.push(e),s},component(e,t){return t?(a.components[e]=t,s):a.components[e]},directive(e,t){return t?(a.directives[e]=t,s):a.directives[e]},mount(o,i,c){if(!l){const d=ln(n,r);return d.appContext=a,i&&t?t(d,o):e(d,o,c),l=!0,s._container=o,o.__vue_app__=s,$n(d.component)||d.component.proxy}},unmount(){l&&(e(null,s._container),delete s._container.__vue_app__)},provide(e,t){return a.provides[e]=t,s}};return s}}function bt(e,t,n,i,l=!1){if((0,o.kJ)(e))return void e.forEach(((e,r)=>bt(e,t&&((0,o.kJ)(t)?t[r]:t),n,i,l)));if(ve(i)&&!l)return;const s=4&i.shapeFlag?$n(i.component)||i.component.proxy:i.el,c=l?null:s,{i:d,r:u}=e;const p=t&&t.r,f=d.refs===o.kT?d.refs={}:d.refs,h=d.setupState;if(null!=p&&p!==u&&((0,o.HD)(p)?(f[p]=null,(0,o.RI)(h,p)&&(h[p]=null)):(0,r.dq)(p)&&(p.value=null)),(0,o.mf)(u))a(u,d,12,[c,f]);else{const t=(0,o.HD)(u),a=(0,r.dq)(u);if(t||a){const a=()=>{if(e.f){const n=t?f[u]:u.value;l?(0,o.kJ)(n)&&(0,o.Od)(n,s):(0,o.kJ)(n)?n.includes(s)||n.push(s):t?(f[u]=[s],(0,o.RI)(h,u)&&(h[u]=f[u])):(u.value=[s],e.k&&(f[e.k]=u.value))}else t?(f[u]=c,(0,o.RI)(h,u)&&(h[u]=c)):(0,r.dq)(u)&&(u.value=c,e.k&&(f[e.k]=c))};c?(a.id=-1,yt(a,n)):a()}else 0}}function wt(){}const yt=J;function kt(e){return _t(e)}function _t(e,t){wt();const n=(0,o.E9)();n.__VUE__=!0;const{insert:a,remove:i,patchProp:l,createElement:s,createText:c,createComment:d,setText:u,setElementText:p,parentNode:f,nextSibling:h,setScopeId:m=o.dG,cloneNode:v,insertStaticContent:g}=e,b=(e,t,n,r=null,o=null,a=null,i=!1,l=null,s=!!t.dynamicChildren)=>{if(e===t)return;e&&!tn(e,t)&&(r=X(e),W(e,o,a,!0),e=null),-2===t.patchFlag&&(s=!1,t.dynamicChildren=null);const{type:c,ref:d,shapeFlag:u}=t;switch(c){case $t:w(e,t,n,r);break;case Nt:y(e,t,n,r);break;case Ut:null==e&&k(t,n,r,i);break;case Vt:R(e,t,n,r,o,a,i,l,s);break;default:1&u?C(e,t,n,r,o,a,i,l,s):6&u?F(e,t,n,r,o,a,i,l,s):(64&u||128&u)&&c.process(e,t,n,r,o,a,i,l,s,te)}null!=d&&o&&bt(d,e&&e.ref,a,t||e,!t)},w=(e,t,n,r)=>{if(null==e)a(t.el=c(t.children),n,r);else{const n=t.el=e.el;t.children!==e.children&&u(n,t.children)}},y=(e,t,n,r)=>{null==e?a(t.el=d(t.children||""),n,r):t.el=e.el},k=(e,t,n,r)=>{[e.el,e.anchor]=g(e.children,t,n,r,e.el,e.anchor)},_=({el:e,anchor:t},n,r)=>{let o;while(e&&e!==t)o=h(e),a(e,n,r),e=o;a(t,n,r)},x=({el:e,anchor:t})=>{let n;while(e&&e!==t)n=h(e),i(e),e=n;i(t)},C=(e,t,n,r,o,a,i,l,s)=>{i=i||"svg"===t.type,null==e?E(t,n,r,o,a,i,l,s):j(e,t,o,a,i,l,s)},E=(e,t,n,r,i,c,d,u)=>{let f,h;const{type:m,props:g,shapeFlag:b,transition:w,patchFlag:y,dirs:k}=e;if(e.el&&void 0!==v&&-1===y)f=e.el=v(e.el);else{if(f=e.el=s(e.type,c,g&&g.is,g),8&b?p(f,e.children):16&b&&q(e.children,f,null,r,i,c&&"foreignObject"!==m,d,u),k&&ht(e,null,r,"created"),g){for(const t in g)"value"===t||(0,o.Gg)(t)||l(f,t,null,g[t],c,e.children,r,i,Q);"value"in g&&l(f,"value",null,g.value),(h=g.onVnodeBeforeMount)&&gn(h,r,e)}A(f,e,e.scopeId,d,r)}k&&ht(e,null,r,"beforeMount");const _=(!i||i&&!i.pendingBranch)&&w&&!w.persisted;_&&w.beforeEnter(f),a(f,t,n),((h=g&&g.onVnodeMounted)||_||k)&&yt((()=>{h&&gn(h,r,e),_&&w.enter(f),k&&ht(e,null,r,"mounted")}),i)},A=(e,t,n,r,o)=>{if(n&&m(e,n),r)for(let a=0;a<r.length;a++)m(e,r[a]);if(o){let n=o.subTree;if(t===n){const t=o.vnode;A(e,t,t.scopeId,t.slotScopeIds,o.parent)}}},q=(e,t,n,r,o,a,i,l,s=0)=>{for(let c=s;c<e.length;c++){const s=e[c]=l?hn(e[c]):fn(e[c]);b(null,s,t,n,r,o,a,i,l)}},j=(e,t,n,r,a,i,s)=>{const c=t.el=e.el;let{patchFlag:d,dynamicChildren:u,dirs:f}=t;d|=16&e.patchFlag;const h=e.props||o.kT,m=t.props||o.kT;let v;n&&xt(n,!1),(v=m.onVnodeBeforeUpdate)&&gn(v,n,t,e),f&&ht(t,e,n,"beforeUpdate"),n&&xt(n,!0);const g=a&&"foreignObject"!==t.type;if(u?O(e.dynamicChildren,u,c,n,r,g,i):s||V(e,t,c,null,n,r,g,i,!1),d>0){if(16&d)M(c,t,h,m,n,r,a);else if(2&d&&h.class!==m.class&&l(c,"class",null,m.class,a),4&d&&l(c,"style",h.style,m.style,a),8&d){const o=t.dynamicProps;for(let t=0;t<o.length;t++){const i=o[t],s=h[i],d=m[i];d===s&&"value"!==i||l(c,i,s,d,a,e.children,n,r,Q)}}1&d&&e.children!==t.children&&p(c,t.children)}else s||null!=u||M(c,t,h,m,n,r,a);((v=m.onVnodeUpdated)||f)&&yt((()=>{v&&gn(v,n,t,e),f&&ht(t,e,n,"updated")}),r)},O=(e,t,n,r,o,a,i)=>{for(let l=0;l<t.length;l++){const s=e[l],c=t[l],d=s.el&&(s.type===Vt||!tn(s,c)||70&s.shapeFlag)?f(s.el):n;b(s,c,d,null,r,o,a,i,!0)}},M=(e,t,n,r,a,i,s)=>{if(n!==r){for(const c in r){if((0,o.Gg)(c))continue;const d=r[c],u=n[c];d!==u&&"value"!==c&&l(e,c,u,d,s,t.children,a,i,Q)}if(n!==o.kT)for(const c in n)(0,o.Gg)(c)||c in r||l(e,c,n[c],null,s,t.children,a,i,Q);"value"in r&&l(e,"value",n.value,r.value)}},R=(e,t,n,r,o,i,l,s,d)=>{const u=t.el=e?e.el:c(""),p=t.anchor=e?e.anchor:c("");let{patchFlag:f,dynamicChildren:h,slotScopeIds:m}=t;m&&(s=s?s.concat(m):m),null==e?(a(u,n,r),a(p,n,r),q(t.children,n,p,o,i,l,s,d)):f>0&&64&f&&h&&e.dynamicChildren?(O(e.dynamicChildren,h,n,o,i,l,s),(null!=t.key||o&&t===o.subTree)&&St(e,t,!0)):V(e,t,n,p,o,i,l,s,d)},F=(e,t,n,r,o,a,i,l,s)=>{t.slotScopeIds=l,null==e?512&t.shapeFlag?o.ctx.activate(t,n,r,i,s):H(t,n,r,o,a,i,s):z(e,t,s)},H=(e,t,n,r,o,a,i)=>{const l=e.component=En(e,r,o);if(ge(e)&&(l.ctx.renderer=te),Fn(l),l.asyncDep){if(o&&o.registerDep(l,B),!e.el){const e=l.subTree=ln(Nt);y(null,e,t,n)}}else B(l,e,t,n,o,a,i)},z=(e,t,n)=>{const r=t.component=e.component;if(Z(e,t,n)){if(r.asyncDep&&!r.asyncResolved)return void I(r,t,n);r.next=t,L(r.update),r.update()}else t.component=e.component,t.el=e.el,r.vnode=t},B=(e,t,n,a,i,l,s)=>{const c=()=>{if(e.isMounted){let t,{next:n,bu:r,u:a,parent:c,vnode:d}=e,u=n;0,xt(e,!1),n?(n.el=d.el,I(e,n,s)):n=d,r&&(0,o.ir)(r),(t=n.props&&n.props.onVnodeBeforeUpdate)&&gn(t,c,n,d),xt(e,!0);const p=N(e);0;const h=e.subTree;e.subTree=p,b(h,p,f(h.el),X(h),e,i,l),n.el=p.el,null===u&&K(e,p.el),a&&yt(a,i),(t=n.props&&n.props.onVnodeUpdated)&&yt((()=>gn(t,c,n,d)),i)}else{let r;const{el:s,props:c}=t,{bm:d,m:u,parent:p}=e,f=ve(t);if(xt(e,!1),d&&(0,o.ir)(d),!f&&(r=c&&c.onVnodeBeforeMount)&&gn(r,p,t),xt(e,!0),s&&re){const n=()=>{e.subTree=N(e),re(s,e.subTree,e,i,null)};f?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{0;const r=e.subTree=N(e);0,b(null,r,n,a,e,i,l),t.el=r.el}if(u&&yt(u,i),!f&&(r=c&&c.onVnodeMounted)){const e=t;yt((()=>gn(r,p,e)),i)}256&t.shapeFlag&&e.a&&yt(e.a,i),e.isMounted=!0,t=n=a=null}},d=e.effect=new r.qq(c,(()=>S(e.update)),e.scope),u=e.update=d.run.bind(d);u.id=e.uid,xt(e,!0),u()},I=(e,t,n)=>{t.component=e;const o=e.vnode.props;e.vnode=t,e.next=null,Qe(e,t.props,o,n),pt(e,t.children,n),(0,r.Jd)(),P(void 0,e.update),(0,r.lk)()},V=(e,t,n,r,o,a,i,l,s=!1)=>{const c=e&&e.children,d=e?e.shapeFlag:0,u=t.children,{patchFlag:f,shapeFlag:h}=t;if(f>0){if(128&f)return void U(c,u,n,r,o,a,i,l,s);if(256&f)return void $(c,u,n,r,o,a,i,l,s)}8&h?(16&d&&Q(c,o,a),u!==c&&p(n,u)):16&d?16&h?U(c,u,n,r,o,a,i,l,s):Q(c,o,a,!0):(8&d&&p(n,""),16&h&&q(u,n,r,o,a,i,l,s))},$=(e,t,n,r,a,i,l,s,c)=>{e=e||o.Z6,t=t||o.Z6;const d=e.length,u=t.length,p=Math.min(d,u);let f;for(f=0;f<p;f++){const r=t[f]=c?hn(t[f]):fn(t[f]);b(e[f],r,n,null,a,i,l,s,c)}d>u?Q(e,a,i,!0,!1,p):q(t,n,r,a,i,l,s,c,p)},U=(e,t,n,r,a,i,l,s,c)=>{let d=0;const u=t.length;let p=e.length-1,f=u-1;while(d<=p&&d<=f){const r=e[d],o=t[d]=c?hn(t[d]):fn(t[d]);if(!tn(r,o))break;b(r,o,n,null,a,i,l,s,c),d++}while(d<=p&&d<=f){const r=e[p],o=t[f]=c?hn(t[f]):fn(t[f]);if(!tn(r,o))break;b(r,o,n,null,a,i,l,s,c),p--,f--}if(d>p){if(d<=f){const e=f+1,o=e<u?t[e].el:r;while(d<=f)b(null,t[d]=c?hn(t[d]):fn(t[d]),n,o,a,i,l,s,c),d++}}else if(d>f)while(d<=p)W(e[d],a,i,!0),d++;else{const h=d,m=d,v=new Map;for(d=m;d<=f;d++){const e=t[d]=c?hn(t[d]):fn(t[d]);null!=e.key&&v.set(e.key,d)}let g,w=0;const y=f-m+1;let k=!1,_=0;const x=new Array(y);for(d=0;d<y;d++)x[d]=0;for(d=h;d<=p;d++){const r=e[d];if(w>=y){W(r,a,i,!0);continue}let o;if(null!=r.key)o=v.get(r.key);else for(g=m;g<=f;g++)if(0===x[g-m]&&tn(r,t[g])){o=g;break}void 0===o?W(r,a,i,!0):(x[o-m]=d+1,o>=_?_=o:k=!0,b(r,t[o],n,null,a,i,l,s,c),w++)}const S=k?Ct(x):o.Z6;for(g=S.length-1,d=y-1;d>=0;d--){const e=m+d,o=t[e],p=e+1<u?t[e+1].el:r;0===x[d]?b(null,o,n,p,a,i,l,s,c):k&&(g<0||d!==S[g]?D(o,n,p,2):g--)}}},D=(e,t,n,r,o=null)=>{const{el:i,type:l,transition:s,children:c,shapeFlag:d}=e;if(6&d)return void D(e.component.subTree,t,n,r);if(128&d)return void e.suspense.move(t,n,r);if(64&d)return void l.move(e,t,n,te);if(l===Vt){a(i,t,n);for(let e=0;e<c.length;e++)D(c[e],t,n,r);return void a(e.anchor,t,n)}if(l===Ut)return void _(e,t,n);const u=2!==r&&1&d&&s;if(u)if(0===r)s.beforeEnter(i),a(i,t,n),yt((()=>s.enter(i)),o);else{const{leave:e,delayLeave:r,afterLeave:o}=s,l=()=>a(i,t,n),c=()=>{e(i,(()=>{l(),o&&o()}))};r?r(i,l,c):c()}else a(i,t,n)},W=(e,t,n,r=!1,o=!1)=>{const{type:a,props:i,ref:l,children:s,dynamicChildren:c,shapeFlag:d,patchFlag:u,dirs:p}=e;if(null!=l&&bt(l,null,n,e,!0),256&d)return void t.ctx.deactivate(e);const f=1&d&&p,h=!ve(e);let m;if(h&&(m=i&&i.onVnodeBeforeUnmount)&&gn(m,t,e),6&d)Y(e.component,n,r);else{if(128&d)return void e.suspense.unmount(n,r);f&&ht(e,null,t,"beforeUnmount"),64&d?e.type.remove(e,t,n,o,te,r):c&&(a!==Vt||u>0&&64&u)?Q(c,t,n,!1,!0):(a===Vt&&384&u||!o&&16&d)&&Q(s,t,n),r&&G(e)}(h&&(m=i&&i.onVnodeUnmounted)||f)&&yt((()=>{m&&gn(m,t,e),f&&ht(e,null,t,"unmounted")}),n)},G=e=>{const{type:t,el:n,anchor:r,transition:o}=e;if(t===Vt)return void J(n,r);if(t===Ut)return void x(e);const a=()=>{i(n),o&&!o.persisted&&o.afterLeave&&o.afterLeave()};if(1&e.shapeFlag&&o&&!o.persisted){const{leave:t,delayLeave:r}=o,i=()=>t(n,a);r?r(e.el,a,i):i()}else a()},J=(e,t)=>{let n;while(e!==t)n=h(e),i(e),e=n;i(t)},Y=(e,t,n)=>{const{bum:r,scope:a,update:i,subTree:l,um:s}=e;r&&(0,o.ir)(r),a.stop(),i&&(i.active=!1,W(l,e,t,n)),s&&yt(s,t),yt((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},Q=(e,t,n,r=!1,o=!1,a=0)=>{for(let i=a;i<e.length;i++)W(e[i],t,n,r,o)},X=e=>6&e.shapeFlag?X(e.component.subTree):128&e.shapeFlag?e.suspense.next():h(e.anchor||e.el),ee=(e,t,n)=>{null==e?t._vnode&&W(t._vnode,null,null,!0):b(t._vnode||null,e,t,null,null,null,n),T(),t._vnode=e},te={p:b,um:W,m:D,r:G,mt:H,mc:q,pc:V,pbc:O,n:X,o:e};let ne,re;return t&&([ne,re]=t(te)),{render:ee,hydrate:ne,createApp:gt(ee,ne)}}function xt({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function St(e,t,n=!1){const r=e.children,a=t.children;if((0,o.kJ)(r)&&(0,o.kJ)(a))for(let o=0;o<r.length;o++){const e=r[o];let t=a[o];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=a[o]=hn(a[o]),t.el=e.el),n||St(e,t))}}function Ct(e){const t=e.slice(),n=[0];let r,o,a,i,l;const s=e.length;for(r=0;r<s;r++){const s=e[r];if(0!==s){if(o=n[n.length-1],e[o]<s){t[r]=o,n.push(r);continue}a=0,i=n.length-1;while(a<i)l=a+i>>1,e[n[l]]<s?a=l+1:i=l;s<e[n[a]]&&(a>0&&(t[r]=n[a-1]),n[a]=r)}}a=n.length,i=n[a-1];while(a-- >0)n[a]=i,i=t[i];return n}const Lt=e=>e.__isTeleport,Et=e=>e&&(e.disabled||""===e.disabled),At=e=>"undefined"!==typeof SVGElement&&e instanceof SVGElement,qt=(e,t)=>{const n=e&&e.to;if((0,o.HD)(n)){if(t){const e=t(n);return e}return null}return n},Pt={__isTeleport:!0,process(e,t,n,r,o,a,i,l,s,c){const{mc:d,pc:u,pbc:p,o:{insert:f,querySelector:h,createText:m,createComment:v}}=c,g=Et(t.props);let{shapeFlag:b,children:w,dynamicChildren:y}=t;if(null==e){const e=t.el=m(""),c=t.anchor=m("");f(e,n,r),f(c,n,r);const u=t.target=qt(t.props,h),p=t.targetAnchor=m("");u&&(f(p,u),i=i||At(u));const v=(e,t)=>{16&b&&d(w,e,t,o,a,i,l,s)};g?v(n,c):u&&v(u,p)}else{t.el=e.el;const r=t.anchor=e.anchor,d=t.target=e.target,f=t.targetAnchor=e.targetAnchor,m=Et(e.props),v=m?n:d,b=m?r:f;if(i=i||At(d),y?(p(e.dynamicChildren,y,v,o,a,i,l),St(e,t,!0)):s||u(e,t,v,b,o,a,i,l,!1),g)m||Tt(t,n,r,c,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const e=t.target=qt(t.props,h);e&&Tt(t,e,null,c,0)}else m&&Tt(t,d,f,c,1)}},remove(e,t,n,r,{um:o,o:{remove:a}},i){const{shapeFlag:l,children:s,anchor:c,targetAnchor:d,target:u,props:p}=e;if(u&&a(d),(i||!Et(p))&&(a(c),16&l))for(let f=0;f<s.length;f++){const e=s[f];o(e,t,n,!0,!!e.dynamicChildren)}},move:Tt,hydrate:jt};function Tt(e,t,n,{o:{insert:r},m:o},a=2){0===a&&r(e.targetAnchor,t,n);const{el:i,anchor:l,shapeFlag:s,children:c,props:d}=e,u=2===a;if(u&&r(i,t,n),(!u||Et(d))&&16&s)for(let p=0;p<c.length;p++)o(c[p],t,n,2);u&&r(l,t,n)}function jt(e,t,n,r,o,a,{o:{nextSibling:i,parentNode:l,querySelector:s}},c){const d=t.target=qt(t.props,s);if(d){const s=d._lpa||d.firstChild;16&t.shapeFlag&&(Et(t.props)?(t.anchor=c(i(e),t,l(e),n,r,o,a),t.targetAnchor=s):(t.anchor=i(e),t.targetAnchor=c(s,t,d,n,r,o,a)),d._lpa=t.targetAnchor&&i(t.targetAnchor))}return t.anchor&&i(t.anchor)}const Ot=Pt,Mt="components",Rt="directives";function Ft(e,t){return Bt(Mt,e,!0,t)||e}const Ht=Symbol();function zt(e){return Bt(Rt,e)}function Bt(e,t,n=!0,r=!1){const a=H||An;if(a){const n=a.type;if(e===Mt){const e=Nn(n);if(e&&(e===t||e===(0,o._A)(t)||e===(0,o.kC)((0,o._A)(t))))return n}const i=It(a[e]||n[e],t)||It(a.appContext[e],t);return!i&&r?n:i}}function It(e,t){return e&&(e[t]||e[(0,o._A)(t)]||e[(0,o.kC)((0,o._A)(t))])}const Vt=Symbol(void 0),$t=Symbol(void 0),Nt=Symbol(void 0),Ut=Symbol(void 0),Dt=[];let Zt=null;function Wt(e=!1){Dt.push(Zt=e?null:[])}function Kt(){Dt.pop(),Zt=Dt[Dt.length-1]||null}let Gt=1;function Jt(e){Gt+=e}function Yt(e){return e.dynamicChildren=Gt>0?Zt||o.Z6:null,Kt(),Gt>0&&Zt&&Zt.push(e),e}function Qt(e,t,n,r,o,a){return Yt(an(e,t,n,r,o,a,!0))}function Xt(e,t,n,r,o){return Yt(ln(e,t,n,r,o,!0))}function en(e){return!!e&&!0===e.__v_isVNode}function tn(e,t){return e.type===t.type&&e.key===t.key}const nn="__vInternal",rn=({key:e})=>null!=e?e:null,on=({ref:e,ref_key:t,ref_for:n})=>null!=e?(0,o.HD)(e)||(0,r.dq)(e)||(0,o.mf)(e)?{i:H,r:e,k:t,f:!!n}:e:null;function an(e,t=null,n=null,r=0,a=null,i=(e===Vt?0:1),l=!1,s=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&rn(t),ref:t&&on(t),scopeId:z,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:a,dynamicChildren:null,appContext:null};return s?(mn(c,n),128&i&&e.normalize(c)):n&&(c.shapeFlag|=(0,o.HD)(n)?8:16),Gt>0&&!l&&Zt&&(c.patchFlag>0||6&i)&&32!==c.patchFlag&&Zt.push(c),c}const ln=sn;function sn(e,t=null,n=null,a=0,i=null,l=!1){if(e&&e!==Ht||(e=Nt),en(e)){const r=dn(e,t,!0);return n&&mn(r,n),r}if(Un(e)&&(e=e.__vccOpts),t){t=cn(t);let{class:e,style:n}=t;e&&!(0,o.HD)(e)&&(t.class=(0,o.C_)(e)),(0,o.Kn)(n)&&((0,r.X3)(n)&&!(0,o.kJ)(n)&&(n=(0,o.l7)({},n)),t.style=(0,o.j5)(n))}const s=(0,o.HD)(e)?1:G(e)?128:Lt(e)?64:(0,o.Kn)(e)?4:(0,o.mf)(e)?2:0;return an(e,t,n,a,i,s,l,!0)}function cn(e){return e?(0,r.X3)(e)||nn in e?(0,o.l7)({},e):e:null}function dn(e,t,n=!1){const{props:r,ref:a,patchFlag:i,children:l}=e,s=t?vn(r||{},t):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&rn(s),ref:t&&t.ref?n&&a?(0,o.kJ)(a)?a.concat(on(t)):[a,on(t)]:on(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Vt?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&dn(e.ssContent),ssFallback:e.ssFallback&&dn(e.ssFallback),el:e.el,anchor:e.anchor};return c}function un(e=" ",t=0){return ln($t,null,e,t)}function pn(e="",t=!1){return t?(Wt(),Xt(Nt,null,e)):ln(Nt,null,e)}function fn(e){return null==e||"boolean"===typeof e?ln(Nt):(0,o.kJ)(e)?ln(Vt,null,e.slice()):"object"===typeof e?hn(e):ln($t,null,String(e))}function hn(e){return null===e.el||e.memo?e:dn(e)}function mn(e,t){let n=0;const{shapeFlag:r}=e;if(null==t)t=null;else if((0,o.kJ)(t))n=16;else if("object"===typeof t){if(65&r){const n=t.default;return void(n&&(n._c&&(n._d=!1),mn(e,n()),n._c&&(n._d=!0)))}{n=32;const r=t._;r||nn in t?3===r&&H&&(1===H.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=H}}else(0,o.mf)(t)?(t={default:t,_ctx:H},n=32):(t=String(t),64&r?(n=16,t=[un(t)]):n=8);e.children=t,e.shapeFlag|=n}function vn(...e){const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const e in r)if("class"===e)t.class!==r.class&&(t.class=(0,o.C_)([t.class,r.class]));else if("style"===e)t.style=(0,o.j5)([t.style,r.style]);else if((0,o.F7)(e)){const n=t[e],a=r[e];!a||n===a||(0,o.kJ)(n)&&n.includes(a)||(t[e]=n?[].concat(n,a):a)}else""!==e&&(t[e]=r[e])}return t}function gn(e,t,n,r=null){i(e,t,7,[n,r])}function bn(e,t,n,r){let a;const i=n&&n[r];if((0,o.kJ)(e)||(0,o.HD)(e)){a=new Array(e.length);for(let n=0,r=e.length;n<r;n++)a[n]=t(e[n],n,void 0,i&&i[n])}else if("number"===typeof e){0,a=new Array(e);for(let n=0;n<e;n++)a[n]=t(n+1,n,void 0,i&&i[n])}else if((0,o.Kn)(e))if(e[Symbol.iterator])a=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);a=new Array(n.length);for(let r=0,o=n.length;r<o;r++){const o=n[r];a[r]=t(e[o],o,r,i&&i[r])}}else a=[];return n&&(n[r]=a),a}function wn(e,t){for(let n=0;n<t.length;n++){const r=t[n];if((0,o.kJ)(r))for(let t=0;t<r.length;t++)e[r[t].name]=r[t].fn;else r&&(e[r.name]=r.fn)}return e}function yn(e,t,n={},r,o){if(H.isCE||H.parent&&ve(H.parent)&&H.parent.isCE)return ln("slot","default"===t?null:{name:t},r&&r());let a=e[t];a&&a._c&&(a._d=!1),Wt();const i=a&&kn(a(n)),l=Xt(Vt,{key:n.key||`_${t}`},i||(r?r():[]),i&&1===e._?64:-2);return!o&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),a&&a._c&&(a._d=!0),l}function kn(e){return e.some((e=>!en(e)||e.type!==Nt&&!(e.type===Vt&&!kn(e.children))))?e:null}const _n=e=>e?jn(e)?$n(e)||e.proxy:_n(e.parent):null,xn=(0,o.l7)(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>_n(e.parent),$root:e=>_n(e.root),$emit:e=>e.emit,$options:e=>$e(e),$forceUpdate:e=>()=>S(e.update),$nextTick:e=>_.bind(e.proxy),$watch:e=>ne.bind(e)}),Sn={get({_:e},t){const{ctx:n,setupState:a,data:i,props:l,accessCache:s,type:c,appContext:d}=e;let u;if("$"!==t[0]){const r=s[t];if(void 0!==r)switch(r){case 1:return a[t];case 2:return i[t];case 4:return n[t];case 3:return l[t]}else{if(a!==o.kT&&(0,o.RI)(a,t))return s[t]=1,a[t];if(i!==o.kT&&(0,o.RI)(i,t))return s[t]=2,i[t];if((u=e.propsOptions[0])&&(0,o.RI)(u,t))return s[t]=3,l[t];if(n!==o.kT&&(0,o.RI)(n,t))return s[t]=4,n[t];He&&(s[t]=0)}}const p=xn[t];let f,h;return p?("$attrs"===t&&(0,r.j)(e,"get",t),p(e)):(f=c.__cssModules)&&(f=f[t])?f:n!==o.kT&&(0,o.RI)(n,t)?(s[t]=4,n[t]):(h=d.config.globalProperties,(0,o.RI)(h,t)?h[t]:void 0)},set({_:e},t,n){const{data:r,setupState:a,ctx:i}=e;return a!==o.kT&&(0,o.RI)(a,t)?(a[t]=n,!0):r!==o.kT&&(0,o.RI)(r,t)?(r[t]=n,!0):!(0,o.RI)(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(i[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:r,appContext:a,propsOptions:i}},l){let s;return!!n[l]||e!==o.kT&&(0,o.RI)(e,l)||t!==o.kT&&(0,o.RI)(t,l)||(s=i[0])&&(0,o.RI)(s,l)||(0,o.RI)(r,l)||(0,o.RI)(xn,l)||(0,o.RI)(a.config.globalProperties,l)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:(0,o.RI)(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};const Cn=mt();let Ln=0;function En(e,t,n){const a=e.type,i=(t?t.appContext:e.appContext)||Cn,l={uid:Ln++,vnode:e,type:a,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,scope:new r.Bj(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:tt(a,i),emitsOptions:R(a,i),emit:null,emitted:null,propsDefaults:o.kT,inheritAttrs:a.inheritAttrs,ctx:o.kT,data:o.kT,props:o.kT,attrs:o.kT,slots:o.kT,refs:o.kT,setupState:o.kT,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return l.ctx={_:l},l.root=t?t.root:l,l.emit=M.bind(null,l),e.ce&&e.ce(l),l}let An=null;const qn=()=>An||H,Pn=e=>{An=e,e.scope.on()},Tn=()=>{An&&An.scope.off(),An=null};function jn(e){return 4&e.vnode.shapeFlag}let On,Mn,Rn=!1;function Fn(e,t=!1){Rn=t;const{props:n,children:r}=e.vnode,o=jn(e);Ye(e,n,o,t),ut(e,r);const a=o?Hn(e,t):void 0;return Rn=!1,a}function Hn(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=(0,r.Xl)(new Proxy(e.ctx,Sn));const{setup:i}=n;if(i){const n=e.setupContext=i.length>1?Vn(e):null;Pn(e),(0,r.Jd)();const s=a(i,e,0,[e.props,n]);if((0,r.lk)(),Tn(),(0,o.tI)(s)){if(s.then(Tn,Tn),t)return s.then((n=>{zn(e,n,t)})).catch((t=>{l(t,e,0)}));e.asyncDep=s}else zn(e,s,t)}else Bn(e,t)}function zn(e,t,n){(0,o.mf)(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:(0,o.Kn)(t)&&(e.setupState=(0,r.WL)(t)),Bn(e,n)}function Bn(e,t,n){const a=e.type;if(!e.render){if(!t&&On&&!a.render){const t=a.template;if(t){0;const{isCustomElement:n,compilerOptions:r}=e.appContext.config,{delimiters:i,compilerOptions:l}=a,s=(0,o.l7)((0,o.l7)({isCustomElement:n,delimiters:i},r),l);a.render=On(t,s)}}e.render=a.render||o.dG,Mn&&Mn(e)}Pn(e),(0,r.Jd)(),ze(e),(0,r.lk)(),Tn()}function In(e){return new Proxy(e.attrs,{get(t,n){return(0,r.j)(e,"get","$attrs"),t[n]}})}function Vn(e){const t=t=>{e.exposed=t||{}};let n;return{get attrs(){return n||(n=In(e))},slots:e.slots,emit:e.emit,expose:t}}function $n(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy((0,r.WL)((0,r.Xl)(e.exposed)),{get(t,n){return n in t?t[n]:n in xn?xn[n](e):void 0}}))}function Nn(e){return(0,o.mf)(e)&&e.displayName||e.name}function Un(e){return(0,o.mf)(e)&&"__vccOpts"in e}const Dn=(e,t)=>(0,r.Fl)(e,t,Rn);function Zn(e,t,n){const r=arguments.length;return 2===r?(0,o.Kn)(t)&&!(0,o.kJ)(t)?en(t)?ln(e,null,[t]):ln(e,t):ln(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):3===r&&en(n)&&(n=[n]),ln(e,t,n))}Symbol("");const Wn="3.2.33"},1957:(e,t,n)=>{"use strict";n.d(t,{F8:()=>ce,W3:()=>oe,ri:()=>he,uT:()=>z});var r=n(6970),o=n(9835),a=n(499);const i="http://www.w3.org/2000/svg",l="undefined"!==typeof document?document:null,s=l&&l.createElement("template"),c={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o=t?l.createElementNS(i,e):l.createElement(e,n?{is:n}:void 0);return"select"===e&&r&&null!=r.multiple&&o.setAttribute("multiple",r.multiple),o},createText:e=>l.createTextNode(e),createComment:e=>l.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>l.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},cloneNode(e){const t=e.cloneNode(!0);return"_value"in e&&(t._value=e._value),t},insertStaticContent(e,t,n,r,o,a){const i=n?n.previousSibling:t.lastChild;if(o&&(o===a||o.nextSibling)){while(1)if(t.insertBefore(o.cloneNode(!0),n),o===a||!(o=o.nextSibling))break}else{s.innerHTML=r?`<svg>${e}</svg>`:e;const o=s.content;if(r){const e=o.firstChild;while(e.firstChild)o.appendChild(e.firstChild);o.removeChild(e)}t.insertBefore(o,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};function d(e,t,n){const r=e._vtc;r&&(t=(t?[t,...r]:[...r]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}function u(e,t,n){const o=e.style,a=(0,r.HD)(n);if(n&&!a){for(const e in n)f(o,e,n[e]);if(t&&!(0,r.HD)(t))for(const e in t)null==n[e]&&f(o,e,"")}else{const r=o.display;a?t!==n&&(o.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(o.display=r)}}const p=/\s*!important$/;function f(e,t,n){if((0,r.kJ)(n))n.forEach((n=>f(e,t,n)));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const o=v(e,t);p.test(n)?e.setProperty((0,r.rs)(o),n.replace(p,""),"important"):e[o]=n}}const h=["Webkit","Moz","ms"],m={};function v(e,t){const n=m[t];if(n)return n;let o=(0,r._A)(t);if("filter"!==o&&o in e)return m[t]=o;o=(0,r.kC)(o);for(let r=0;r<h.length;r++){const n=h[r]+o;if(n in e)return m[t]=n}return t}const g="http://www.w3.org/1999/xlink";function b(e,t,n,o,a){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(g,t.slice(6,t.length)):e.setAttributeNS(g,t,n);else{const o=(0,r.Pq)(t);null==n||o&&!(0,r.yA)(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}function w(e,t,n,o,a,i,l){if("innerHTML"===t||"textContent"===t)return o&&l(o,a,i),void(e[t]=null==n?"":n);if("value"===t&&"PROGRESS"!==e.tagName&&!e.tagName.includes("-")){e._value=n;const r=null==n?"":n;return e.value===r&&"OPTION"!==e.tagName||(e.value=r),void(null==n&&e.removeAttribute(t))}let s=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=(0,r.yA)(n):null==n&&"string"===o?(n="",s=!0):"number"===o&&(n=0,s=!0)}try{e[t]=n}catch(c){0}s&&e.removeAttribute(t)}const[y,k]=(()=>{let e=Date.now,t=!1;if("undefined"!==typeof window){Date.now()>document.createEvent("Event").timeStamp&&(e=()=>performance.now());const n=navigator.userAgent.match(/firefox\/(\d+)/i);t=!!(n&&Number(n[1])<=53)}return[e,t]})();let _=0;const x=Promise.resolve(),S=()=>{_=0},C=()=>_||(x.then(S),_=y());function L(e,t,n,r){e.addEventListener(t,n,r)}function E(e,t,n,r){e.removeEventListener(t,n,r)}function A(e,t,n,r,o=null){const a=e._vei||(e._vei={}),i=a[t];if(r&&i)i.value=r;else{const[n,l]=P(t);if(r){const i=a[t]=T(r,o);L(e,n,i,l)}else i&&(E(e,n,i,l),a[t]=void 0)}}const q=/(?:Once|Passive|Capture)$/;function P(e){let t;if(q.test(e)){let n;t={};while(n=e.match(q))e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[(0,r.rs)(e.slice(2)),t]}function T(e,t){const n=e=>{const r=e.timeStamp||y();(k||r>=n.attached-1)&&(0,o.$d)(j(e,n.value),t,5,[e])};return n.value=e,n.attached=C(),n}function j(e,t){if((0,r.kJ)(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e&&e(t)))}return t}const O=/^on[a-z]/,M=(e,t,n,o,a=!1,i,l,s,c)=>{"class"===t?d(e,o,a):"style"===t?u(e,n,o):(0,r.F7)(t)?(0,r.tR)(t)||A(e,t,n,o,l):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):R(e,t,o,a))?w(e,t,o,i,l,s,c):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),b(e,t,o,a))};function R(e,t,n,o){return o?"innerHTML"===t||"textContent"===t||!!(t in e&&O.test(t)&&(0,r.mf)(n)):"spellcheck"!==t&&"draggable"!==t&&"translate"!==t&&("form"!==t&&(("list"!==t||"INPUT"!==e.tagName)&&(("type"!==t||"TEXTAREA"!==e.tagName)&&((!O.test(t)||!(0,r.HD)(n))&&t in e))))}"undefined"!==typeof HTMLElement&&HTMLElement;const F="transition",H="animation",z=(e,{slots:t})=>(0,o.h)(o.P$,N(e),t);z.displayName="Transition";const B={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},I=z.props=(0,r.l7)({},o.P$.props,B),V=(e,t=[])=>{(0,r.kJ)(e)?e.forEach((e=>e(...t))):e&&e(...t)},$=e=>!!e&&((0,r.kJ)(e)?e.some((e=>e.length>1)):e.length>1);function N(e){const t={};for(const r in e)r in B||(t[r]=e[r]);if(!1===e.css)return t;const{name:n="v",type:o,duration:a,enterFromClass:i=`${n}-enter-from`,enterActiveClass:l=`${n}-enter-active`,enterToClass:s=`${n}-enter-to`,appearFromClass:c=i,appearActiveClass:d=l,appearToClass:u=s,leaveFromClass:p=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:h=`${n}-leave-to`}=e,m=U(a),v=m&&m[0],g=m&&m[1],{onBeforeEnter:b,onEnter:w,onEnterCancelled:y,onLeave:k,onLeaveCancelled:_,onBeforeAppear:x=b,onAppear:S=w,onAppearCancelled:C=y}=t,L=(e,t,n)=>{W(e,t?u:s),W(e,t?d:l),n&&n()},E=(e,t)=>{W(e,h),W(e,f),t&&t()},A=e=>(t,n)=>{const r=e?S:w,a=()=>L(t,e,n);V(r,[t,a]),K((()=>{W(t,e?c:i),Z(t,e?u:s),$(r)||J(t,o,v,a)}))};return(0,r.l7)(t,{onBeforeEnter(e){V(b,[e]),Z(e,i),Z(e,l)},onBeforeAppear(e){V(x,[e]),Z(e,c),Z(e,d)},onEnter:A(!1),onAppear:A(!0),onLeave(e,t){const n=()=>E(e,t);Z(e,p),ee(),Z(e,f),K((()=>{W(e,p),Z(e,h),$(k)||J(e,o,g,n)})),V(k,[e,n])},onEnterCancelled(e){L(e,!1),V(y,[e])},onAppearCancelled(e){L(e,!0),V(C,[e])},onLeaveCancelled(e){E(e),V(_,[e])}})}function U(e){if(null==e)return null;if((0,r.Kn)(e))return[D(e.enter),D(e.leave)];{const t=D(e);return[t,t]}}function D(e){const t=(0,r.He)(e);return t}function Z(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e._vtc||(e._vtc=new Set)).add(t)}function W(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function K(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let G=0;function J(e,t,n,r){const o=e._endId=++G,a=()=>{o===e._endId&&r()};if(n)return setTimeout(a,n);const{type:i,timeout:l,propCount:s}=Y(e,t);if(!i)return r();const c=i+"end";let d=0;const u=()=>{e.removeEventListener(c,p),a()},p=t=>{t.target===e&&++d>=s&&u()};setTimeout((()=>{d<s&&u()}),l+1),e.addEventListener(c,p)}function Y(e,t){const n=window.getComputedStyle(e),r=e=>(n[e]||"").split(", "),o=r(F+"Delay"),a=r(F+"Duration"),i=Q(o,a),l=r(H+"Delay"),s=r(H+"Duration"),c=Q(l,s);let d=null,u=0,p=0;t===F?i>0&&(d=F,u=i,p=a.length):t===H?c>0&&(d=H,u=c,p=s.length):(u=Math.max(i,c),d=u>0?i>c?F:H:null,p=d?d===F?a.length:s.length:0);const f=d===F&&/\b(transform|all)(,|$)/.test(n[F+"Property"]);return{type:d,timeout:u,propCount:p,hasTransform:f}}function Q(e,t){while(e.length<t.length)e=e.concat(e);return Math.max(...t.map(((t,n)=>X(t)+X(e[n]))))}function X(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}function ee(){return document.body.offsetHeight}const te=new WeakMap,ne=new WeakMap,re={name:"TransitionGroup",props:(0,r.l7)({},I,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=(0,o.FN)(),r=(0,o.Y8)();let i,l;return(0,o.ic)((()=>{if(!i.length)return;const t=e.moveClass||`${e.name||"v"}-move`;if(!se(i[0].el,n.vnode.el,t))return;i.forEach(ae),i.forEach(ie);const r=i.filter(le);ee(),r.forEach((e=>{const n=e.el,r=n.style;Z(n,t),r.transform=r.webkitTransform=r.transitionDuration="";const o=n._moveCb=e=>{e&&e.target!==n||e&&!/transform$/.test(e.propertyName)||(n.removeEventListener("transitionend",o),n._moveCb=null,W(n,t))};n.addEventListener("transitionend",o)}))})),()=>{const s=(0,a.IU)(e),c=N(s);let d=s.tag||o.HY;i=l,l=t.default?(0,o.Q6)(t.default()):[];for(let e=0;e<l.length;e++){const t=l[e];null!=t.key&&(0,o.nK)(t,(0,o.U2)(t,c,r,n))}if(i)for(let e=0;e<i.length;e++){const t=i[e];(0,o.nK)(t,(0,o.U2)(t,c,r,n)),te.set(t,t.el.getBoundingClientRect())}return(0,o.Wm)(d,null,l)}}},oe=re;function ae(e){const t=e.el;t._moveCb&&t._moveCb(),t._enterCb&&t._enterCb()}function ie(e){ne.set(e,e.el.getBoundingClientRect())}function le(e){const t=te.get(e),n=ne.get(e),r=t.left-n.left,o=t.top-n.top;if(r||o){const t=e.el.style;return t.transform=t.webkitTransform=`translate(${r}px,${o}px)`,t.transitionDuration="0s",e}}function se(e,t,n){const r=e.cloneNode();e._vtc&&e._vtc.forEach((e=>{e.split(/\s+/).forEach((e=>e&&r.classList.remove(e)))})),n.split(/\s+/).forEach((e=>e&&r.classList.add(e))),r.style.display="none";const o=1===t.nodeType?t:t.parentNode;o.appendChild(r);const{hasTransform:a}=Y(r);return o.removeChild(r),a}const ce={beforeMount(e,{value:t},{transition:n}){e._vod="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):de(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:r}){!t!==!n&&(r?t?(r.beforeEnter(e),de(e,!0),r.enter(e)):r.leave(e,(()=>{de(e,!1)})):de(e,t))},beforeUnmount(e,{value:t}){de(e,t)}};function de(e,t){e.style.display=t?e._vod:"none"}const ue=(0,r.l7)({patchProp:M},c);let pe;function fe(){return pe||(pe=(0,o.Us)(ue))}const he=(...e)=>{const t=fe().createApp(...e);const{mount:n}=t;return t.mount=e=>{const o=me(e);if(!o)return;const a=t._component;(0,r.mf)(a)||a.render||a.template||(a.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};function me(e){if((0,r.HD)(e)){const t=document.querySelector(e);return t}return e}},6970:(e,t,n)=>{"use strict";function r(e,t){const n=Object.create(null),r=e.split(",");for(let o=0;o<r.length;o++)n[r[o]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}n.d(t,{C_:()=>f,DM:()=>O,E9:()=>oe,F7:()=>C,Gg:()=>D,HD:()=>F,He:()=>ne,Kn:()=>z,NO:()=>x,Nj:()=>te,Od:()=>A,PO:()=>N,Pq:()=>l,RI:()=>P,S0:()=>U,W7:()=>$,WV:()=>v,Z6:()=>k,_A:()=>K,_N:()=>j,aU:()=>X,dG:()=>_,e1:()=>a,fY:()=>r,hR:()=>Q,hq:()=>g,ir:()=>ee,j5:()=>c,kC:()=>Y,kJ:()=>T,kT:()=>y,l7:()=>E,mf:()=>R,rs:()=>J,tI:()=>B,tR:()=>L,vs:()=>h,yA:()=>s,yk:()=>H,zw:()=>b});const o="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt",a=r(o);const i="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",l=r(i);function s(e){return!!e||""===e}function c(e){if(T(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=F(r)?p(r):c(r);if(o)for(const e in o)t[e]=o[e]}return t}return F(e)||z(e)?e:void 0}const d=/;(?![^(]*\))/g,u=/:(.+)/;function p(e){const t={};return e.split(d).forEach((e=>{if(e){const n=e.split(u);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function f(e){let t="";if(F(e))t=e;else if(T(e))for(let n=0;n<e.length;n++){const r=f(e[n]);r&&(t+=r+" ")}else if(z(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function h(e){if(!e)return null;let{class:t,style:n}=e;return t&&!F(t)&&(e.class=f(t)),n&&(e.style=c(n)),e}function m(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=v(e[r],t[r]);return n}function v(e,t){if(e===t)return!0;let n=M(e),r=M(t);if(n||r)return!(!n||!r)&&e.getTime()===t.getTime();if(n=T(e),r=T(t),n||r)return!(!n||!r)&&m(e,t);if(n=z(e),r=z(t),n||r){if(!n||!r)return!1;const o=Object.keys(e).length,a=Object.keys(t).length;if(o!==a)return!1;for(const n in e){const r=e.hasOwnProperty(n),o=t.hasOwnProperty(n);if(r&&!o||!r&&o||!v(e[n],t[n]))return!1}}return String(e)===String(t)}function g(e,t){return e.findIndex((e=>v(e,t)))}const b=e=>F(e)?e:null==e?"":T(e)||z(e)&&(e.toString===I||!R(e.toString))?JSON.stringify(e,w,2):String(e),w=(e,t)=>t&&t.__v_isRef?w(e,t.value):j(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n])=>(e[`${t} =>`]=n,e)),{})}:O(t)?{[`Set(${t.size})`]:[...t.values()]}:!z(t)||T(t)||N(t)?t:String(t),y={},k=[],_=()=>{},x=()=>!1,S=/^on[^a-z]/,C=e=>S.test(e),L=e=>e.startsWith("onUpdate:"),E=Object.assign,A=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},q=Object.prototype.hasOwnProperty,P=(e,t)=>q.call(e,t),T=Array.isArray,j=e=>"[object Map]"===V(e),O=e=>"[object Set]"===V(e),M=e=>e instanceof Date,R=e=>"function"===typeof e,F=e=>"string"===typeof e,H=e=>"symbol"===typeof e,z=e=>null!==e&&"object"===typeof e,B=e=>z(e)&&R(e.then)&&R(e.catch),I=Object.prototype.toString,V=e=>I.call(e),$=e=>V(e).slice(8,-1),N=e=>"[object Object]"===V(e),U=e=>F(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,D=r(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Z=e=>{const t=Object.create(null);return n=>{const r=t[n];return r||(t[n]=e(n))}},W=/-(\w)/g,K=Z((e=>e.replace(W,((e,t)=>t?t.toUpperCase():"")))),G=/\B([A-Z])/g,J=Z((e=>e.replace(G,"-$1").toLowerCase())),Y=Z((e=>e.charAt(0).toUpperCase()+e.slice(1))),Q=Z((e=>e?`on${Y(e)}`:"")),X=(e,t)=>!Object.is(e,t),ee=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},te=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},ne=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let re;const oe=()=>re||(re="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:{})},9981:(e,t,n)=>{e.exports=n(6148)},6857:(e,t,n)=>{"use strict";var r=n(6031),o=n(8117),a=n(6139),i=n(9395),l=n(7187),s=n(7758),c=n(4908),d=n(7381);e.exports=function(e){return new Promise((function(t,n){var u=e.data,p=e.headers,f=e.responseType;r.isFormData(u)&&delete p["Content-Type"];var h=new XMLHttpRequest;if(e.auth){var m=e.auth.username||"",v=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";p.Authorization="Basic "+btoa(m+":"+v)}var g=l(e.baseURL,e.url);function b(){if(h){var r="getAllResponseHeaders"in h?s(h.getAllResponseHeaders()):null,a=f&&"text"!==f&&"json"!==f?h.response:h.responseText,i={data:a,status:h.status,statusText:h.statusText,headers:r,config:e,request:h};o(t,n,i),h=null}}if(h.open(e.method.toUpperCase(),i(g,e.params,e.paramsSerializer),!0),h.timeout=e.timeout,"onloadend"in h?h.onloadend=b:h.onreadystatechange=function(){h&&4===h.readyState&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))&&setTimeout(b)},h.onabort=function(){h&&(n(d("Request aborted",e,"ECONNABORTED",h)),h=null)},h.onerror=function(){n(d("Network Error",e,null,h)),h=null},h.ontimeout=function(){var t="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(d(t,e,e.transitional&&e.transitional.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",h)),h=null},r.isStandardBrowserEnv()){var w=(e.withCredentials||c(g))&&e.xsrfCookieName?a.read(e.xsrfCookieName):void 0;w&&(p[e.xsrfHeaderName]=w)}"setRequestHeader"in h&&r.forEach(p,(function(e,t){"undefined"===typeof u&&"content-type"===t.toLowerCase()?delete p[t]:h.setRequestHeader(t,e)})),r.isUndefined(e.withCredentials)||(h.withCredentials=!!e.withCredentials),f&&"json"!==f&&(h.responseType=e.responseType),"function"===typeof e.onDownloadProgress&&h.addEventListener("progress",e.onDownloadProgress),"function"===typeof e.onUploadProgress&&h.upload&&h.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){h&&(h.abort(),n(e),h=null)})),u||(u=null),h.send(u)}))}},6148:(e,t,n)=>{"use strict";var r=n(6031),o=n(4009),a=n(7237),i=n(8342),l=n(9860);function s(e){var t=new a(e),n=o(a.prototype.request,t);return r.extend(n,a.prototype,t),r.extend(n,t),n}var c=s(l);c.Axios=a,c.create=function(e){return s(i(c.defaults,e))},c.Cancel=n(5838),c.CancelToken=n(5e3),c.isCancel=n(2649),c.all=function(e){return Promise.all(e)},c.spread=n(7615),c.isAxiosError=n(6794),e.exports=c,e.exports["default"]=c},5838:e=>{"use strict";function t(e){this.message=e}t.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},t.prototype.__CANCEL__=!0,e.exports=t},5e3:(e,t,n)=>{"use strict";var r=n(5838);function o(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;e((function(e){n.reason||(n.reason=new r(e),t(n.reason))}))}o.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},o.source=function(){var e,t=new o((function(t){e=t}));return{token:t,cancel:e}},e.exports=o},2649:e=>{"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},7237:(e,t,n)=>{"use strict";var r=n(6031),o=n(9395),a=n(7332),i=n(1014),l=n(8342),s=n(9206),c=s.validators;function d(e){this.defaults=e,this.interceptors={request:new a,response:new a}}d.prototype.request=function(e){"string"===typeof e?(e=arguments[1]||{},e.url=arguments[0]):e=e||{},e=l(this.defaults,e),e.method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=e.transitional;void 0!==t&&s.assertOptions(t,{silentJSONParsing:c.transitional(c.boolean,"1.0.0"),forcedJSONParsing:c.transitional(c.boolean,"1.0.0"),clarifyTimeoutError:c.transitional(c.boolean,"1.0.0")},!1);var n=[],r=!0;this.interceptors.request.forEach((function(t){"function"===typeof t.runWhen&&!1===t.runWhen(e)||(r=r&&t.synchronous,n.unshift(t.fulfilled,t.rejected))}));var o,a=[];if(this.interceptors.response.forEach((function(e){a.push(e.fulfilled,e.rejected)})),!r){var d=[i,void 0];Array.prototype.unshift.apply(d,n),d=d.concat(a),o=Promise.resolve(e);while(d.length)o=o.then(d.shift(),d.shift());return o}var u=e;while(n.length){var p=n.shift(),f=n.shift();try{u=p(u)}catch(h){f(h);break}}try{o=i(u)}catch(h){return Promise.reject(h)}while(a.length)o=o.then(a.shift(),a.shift());return o},d.prototype.getUri=function(e){return e=l(this.defaults,e),o(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},r.forEach(["delete","get","head","options"],(function(e){d.prototype[e]=function(t,n){return this.request(l(n||{},{method:e,url:t,data:(n||{}).data}))}})),r.forEach(["post","put","patch"],(function(e){d.prototype[e]=function(t,n,r){return this.request(l(r||{},{method:e,url:t,data:n}))}})),e.exports=d},7332:(e,t,n)=>{"use strict";var r=n(6031);function o(){this.handlers=[]}o.prototype.use=function(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1},o.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},o.prototype.forEach=function(e){r.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=o},7187:(e,t,n)=>{"use strict";var r=n(6847),o=n(6560);e.exports=function(e,t){return e&&!r(t)?o(e,t):t}},7381:(e,t,n)=>{"use strict";var r=n(4918);e.exports=function(e,t,n,o,a){var i=new Error(e);return r(i,t,n,o,a)}},1014:(e,t,n)=>{"use strict";var r=n(6031),o=n(2297),a=n(2649),i=n(9860);function l(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){l(e),e.headers=e.headers||{},e.data=o.call(e,e.data,e.headers,e.transformRequest),e.headers=r.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),r.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]}));var t=e.adapter||i.adapter;return t(e).then((function(t){return l(e),t.data=o.call(e,t.data,t.headers,e.transformResponse),t}),(function(t){return a(t)||(l(e),t&&t.response&&(t.response.data=o.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},4918:e=>{"use strict";e.exports=function(e,t,n,r,o){return e.config=t,n&&(e.code=n),e.request=r,e.response=o,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}},8342:(e,t,n)=>{"use strict";var r=n(6031);e.exports=function(e,t){t=t||{};var n={},o=["url","method","data"],a=["headers","auth","proxy","params"],i=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],l=["validateStatus"];function s(e,t){return r.isPlainObject(e)&&r.isPlainObject(t)?r.merge(e,t):r.isPlainObject(t)?r.merge({},t):r.isArray(t)?t.slice():t}function c(o){r.isUndefined(t[o])?r.isUndefined(e[o])||(n[o]=s(void 0,e[o])):n[o]=s(e[o],t[o])}r.forEach(o,(function(e){r.isUndefined(t[e])||(n[e]=s(void 0,t[e]))})),r.forEach(a,c),r.forEach(i,(function(o){r.isUndefined(t[o])?r.isUndefined(e[o])||(n[o]=s(void 0,e[o])):n[o]=s(void 0,t[o])})),r.forEach(l,(function(r){r in t?n[r]=s(e[r],t[r]):r in e&&(n[r]=s(void 0,e[r]))}));var d=o.concat(a).concat(i).concat(l),u=Object.keys(e).concat(Object.keys(t)).filter((function(e){return-1===d.indexOf(e)}));return r.forEach(u,c),n}},8117:(e,t,n)=>{"use strict";var r=n(7381);e.exports=function(e,t,n){var o=n.config.validateStatus;n.status&&o&&!o(n.status)?t(r("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},2297:(e,t,n)=>{"use strict";var r=n(6031),o=n(9860);e.exports=function(e,t,n){var a=this||o;return r.forEach(n,(function(n){e=n.call(a,e,t)})),e}},9860:(e,t,n)=>{"use strict";var r=n(6031),o=n(4129),a=n(4918),i={"Content-Type":"application/x-www-form-urlencoded"};function l(e,t){!r.isUndefined(e)&&r.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}function s(){var e;return("undefined"!==typeof XMLHttpRequest||"undefined"!==typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(e=n(6857)),e}function c(e,t,n){if(r.isString(e))try{return(t||JSON.parse)(e),r.trim(e)}catch(o){if("SyntaxError"!==o.name)throw o}return(n||JSON.stringify)(e)}var d={transitional:{silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},adapter:s(),transformRequest:[function(e,t){return o(t,"Accept"),o(t,"Content-Type"),r.isFormData(e)||r.isArrayBuffer(e)||r.isBuffer(e)||r.isStream(e)||r.isFile(e)||r.isBlob(e)?e:r.isArrayBufferView(e)?e.buffer:r.isURLSearchParams(e)?(l(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):r.isObject(e)||t&&"application/json"===t["Content-Type"]?(l(t,"application/json"),c(e)):e}],transformResponse:[function(e){var t=this.transitional,n=t&&t.silentJSONParsing,o=t&&t.forcedJSONParsing,i=!n&&"json"===this.responseType;if(i||o&&r.isString(e)&&e.length)try{return JSON.parse(e)}catch(l){if(i){if("SyntaxError"===l.name)throw a(l,this,"E_JSON_PARSE");throw l}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};r.forEach(["delete","get","head"],(function(e){d.headers[e]={}})),r.forEach(["post","put","patch"],(function(e){d.headers[e]=r.merge(i)})),e.exports=d},4009:e=>{"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}}},9395:(e,t,n)=>{"use strict";var r=n(6031);function o(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var a;if(n)a=n(t);else if(r.isURLSearchParams(t))a=t.toString();else{var i=[];r.forEach(t,(function(e,t){null!==e&&"undefined"!==typeof e&&(r.isArray(e)?t+="[]":e=[e],r.forEach(e,(function(e){r.isDate(e)?e=e.toISOString():r.isObject(e)&&(e=JSON.stringify(e)),i.push(o(t)+"="+o(e))})))})),a=i.join("&")}if(a){var l=e.indexOf("#");-1!==l&&(e=e.slice(0,l)),e+=(-1===e.indexOf("?")?"?":"&")+a}return e}},6560:e=>{"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},6139:(e,t,n)=>{"use strict";var r=n(6031);e.exports=r.isStandardBrowserEnv()?function(){return{write:function(e,t,n,o,a,i){var l=[];l.push(e+"="+encodeURIComponent(t)),r.isNumber(n)&&l.push("expires="+new Date(n).toGMTString()),r.isString(o)&&l.push("path="+o),r.isString(a)&&l.push("domain="+a),!0===i&&l.push("secure"),document.cookie=l.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}()},6847:e=>{"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},6794:e=>{"use strict";e.exports=function(e){return"object"===typeof e&&!0===e.isAxiosError}},4908:(e,t,n)=>{"use strict";var r=n(6031);e.exports=r.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function o(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=o(window.location.href),function(t){var n=r.isString(t)?o(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return function(){return!0}}()},4129:(e,t,n)=>{"use strict";var r=n(6031);e.exports=function(e,t){r.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))}},7758:(e,t,n)=>{"use strict";var r=n(6031),o=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,a,i={};return e?(r.forEach(e.split("\n"),(function(e){if(a=e.indexOf(":"),t=r.trim(e.substr(0,a)).toLowerCase(),n=r.trim(e.substr(a+1)),t){if(i[t]&&o.indexOf(t)>=0)return;i[t]="set-cookie"===t?(i[t]?i[t]:[]).concat([n]):i[t]?i[t]+", "+n:n}})),i):i}},7615:e=>{"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},9206:(e,t,n)=>{"use strict";var r=n(8593),o={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){o[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));var a={},i=r.version.split(".");function l(e,t){for(var n=t?t.split("."):i,r=e.split("."),o=0;o<3;o++){if(n[o]>r[o])return!0;if(n[o]<r[o])return!1}return!1}function s(e,t,n){if("object"!==typeof e)throw new TypeError("options must be an object");var r=Object.keys(e),o=r.length;while(o-- >0){var a=r[o],i=t[a];if(i){var l=e[a],s=void 0===l||i(l,a,e);if(!0!==s)throw new TypeError("option "+a+" must be "+s)}else if(!0!==n)throw Error("Unknown option "+a)}}o.transitional=function(e,t,n){var o=t&&l(t);function i(e,t){return"[Axios v"+r.version+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return function(n,r,l){if(!1===e)throw new Error(i(r," has been removed in "+t));return o&&!a[r]&&(a[r]=!0,console.warn(i(r," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,r,l)}},e.exports={isOlderVersion:l,assertOptions:s,validators:o}},6031:(e,t,n)=>{"use strict";var r=n(4009),o=Object.prototype.toString;function a(e){return"[object Array]"===o.call(e)}function i(e){return"undefined"===typeof e}function l(e){return null!==e&&!i(e)&&null!==e.constructor&&!i(e.constructor)&&"function"===typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}function s(e){return"[object ArrayBuffer]"===o.call(e)}function c(e){return"undefined"!==typeof FormData&&e instanceof FormData}function d(e){var t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer,t}function u(e){return"string"===typeof e}function p(e){return"number"===typeof e}function f(e){return null!==e&&"object"===typeof e}function h(e){if("[object Object]"!==o.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function m(e){return"[object Date]"===o.call(e)}function v(e){return"[object File]"===o.call(e)}function g(e){return"[object Blob]"===o.call(e)}function b(e){return"[object Function]"===o.call(e)}function w(e){return f(e)&&b(e.pipe)}function y(e){return"undefined"!==typeof URLSearchParams&&e instanceof URLSearchParams}function k(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function _(){return("undefined"===typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!==typeof window&&"undefined"!==typeof document)}function x(e,t){if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),a(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}function S(){var e={};function t(t,n){h(e[n])&&h(t)?e[n]=S(e[n],t):h(t)?e[n]=S({},t):a(t)?e[n]=t.slice():e[n]=t}for(var n=0,r=arguments.length;n<r;n++)x(arguments[n],t);return e}function C(e,t,n){return x(t,(function(t,o){e[o]=n&&"function"===typeof t?r(t,n):t})),e}function L(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}e.exports={isArray:a,isArrayBuffer:s,isBuffer:l,isFormData:c,isArrayBufferView:d,isString:u,isNumber:p,isObject:f,isPlainObject:h,isUndefined:i,isDate:m,isFile:v,isBlob:g,isFunction:b,isStream:w,isURLSearchParams:y,isStandardBrowserEnv:_,forEach:x,merge:S,extend:C,trim:k,stripBOM:L}},5529:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});var r=n(3340),o=n(9357);const a=(0,r.xr)((({app:e})=>{e.use(o.ZP)}))},990:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});n(6727);var r=n(9835),o=n(5987),a=n(2026);const i=["top","middle","bottom"],l=(0,o.L)({name:"QBadge",props:{color:String,textColor:String,floating:Boolean,transparent:Boolean,multiLine:Boolean,outline:Boolean,rounded:Boolean,label:[Number,String],align:{type:String,validator:e=>i.includes(e)}},setup(e,{slots:t}){const n=(0,r.Fl)((()=>void 0!==e.align?{verticalAlign:e.align}:null)),o=(0,r.Fl)((()=>{const t=!0===e.outline&&e.color||e.textColor;return`q-badge flex inline items-center no-wrap q-badge--${!0===e.multiLine?"multi":"single"}-line`+(!0===e.outline?" q-badge--outline":void 0!==e.color?` bg-${e.color}`:"")+(void 0!==t?` text-${t}`:"")+(!0===e.floating?" q-badge--floating":"")+(!0===e.rounded?" q-badge--rounded":"")+(!0===e.transparent?" q-badge--transparent":"")}));return()=>(0,r.h)("div",{class:o.value,style:n.value,role:"alert","aria-label":e.label},void 0!==e.label?e.label:(0,a.KR)(t.default))}})},7128:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(9835),o=n(5987),a=n(8234),i=n(2026);const l=(0,o.L)({name:"QBanner",props:{...a.S,inlineActions:Boolean,dense:Boolean,rounded:Boolean},setup(e,{slots:t}){const n=(0,r.FN)(),o=(0,a.Z)(e,n.proxy.$q),l=(0,r.Fl)((()=>"q-banner row items-center"+(!0===e.dense?" q-banner--dense":"")+(!0===o.value?" q-banner--dark q-dark":"")+(!0===e.rounded?" rounded-borders":""))),s=(0,r.Fl)((()=>"q-banner__actions row items-center justify-end col-"+(!0===e.inlineActions?"auto":"all")));return()=>{const n=[(0,r.h)("div",{class:"q-banner__avatar col-auto row items-center self-start"},(0,i.KR)(t.avatar)),(0,r.h)("div",{class:"q-banner__content col text-body2"},(0,i.KR)(t.default))],o=(0,i.KR)(t.action);return void 0!==o&&n.push((0,r.h)("div",{class:s.value},o)),(0,r.h)("div",{class:l.value+(!1===e.inlineActions&&void 0!==o?" q-banner--top-padding":""),role:"alert"},n)}}})},8983:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var r=n(9835),o=n(9379),a=n(5987),i=n(2026);const l=(0,a.L)({name:"QBtnGroup",props:{unelevated:Boolean,outline:Boolean,flat:Boolean,rounded:Boolean,push:Boolean,stretch:Boolean,glossy:Boolean,spread:Boolean},setup(e,{slots:t}){const n=(0,r.Fl)((()=>{const t=["unelevated","outline","flat","rounded","push","stretch","glossy"].filter((t=>!0===e[t])).map((e=>`q-btn-group--${e}`)).join(" ");return"q-btn-group row no-wrap"+(t.length>0?" "+t:"")+(!0===e.spread?" q-btn-group--spread":" inline")}));return()=>(0,r.h)("div",{class:n.value},(0,i.KR)(t.default))}});var s=n(9256);const c=(0,a.L)({name:"QBtnToggle",props:{...s.Fz,modelValue:{required:!0},options:{type:Array,required:!0,validator:e=>e.every((e=>("label"in e||"icon"in e||"slot"in e)&&"value"in e))},color:String,textColor:String,toggleColor:{type:String,default:"primary"},toggleTextColor:String,outline:Boolean,flat:Boolean,unelevated:Boolean,rounded:Boolean,push:Boolean,glossy:Boolean,size:String,padding:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,readonly:Boolean,disable:Boolean,stack:Boolean,stretch:Boolean,spread:Boolean,clearable:Boolean,ripple:{type:[Boolean,Object],default:!0}},emits:["update:modelValue","clear","click"],setup(e,{slots:t,emit:n}){const a=(0,r.Fl)((()=>void 0!==e.options.find((t=>t.value===e.modelValue)))),c=(0,r.Fl)((()=>({type:"hidden",name:e.name,value:e.modelValue}))),d=(0,s.eX)(c),u=(0,r.Fl)((()=>e.options.map(((t,n)=>{const{attrs:r,value:o,slot:a,...i}=t;return{slot:a,props:{key:n,onClick(e){p(o,t,e)},"aria-pressed":o===e.modelValue?"true":"false",...r,...i,outline:e.outline,flat:e.flat,rounded:e.rounded,push:e.push,unelevated:e.unelevated,dense:e.dense,disable:!0===e.disable||!0===i.disable,color:o===e.modelValue?f(i,"toggleColor"):f(i,"color"),textColor:o===e.modelValue?f(i,"toggleTextColor"):f(i,"textColor"),noCaps:!0===f(i,"noCaps"),noWrap:!0===f(i,"noWrap"),size:f(i,"size"),padding:f(i,"padding"),ripple:f(i,"ripple"),stack:!0===f(i,"stack"),stretch:!0===f(i,"stretch")}}}))));function p(t,r,o){!0!==e.readonly&&(e.modelValue===t?!0===e.clearable&&(n("update:modelValue",null,null),n("clear")):n("update:modelValue",t,r),n("click",o))}function f(t,n){return void 0===t[n]?e[n]:t[n]}function h(){const n=u.value.map((e=>(0,r.h)(o.Z,e.props,void 0!==e.slot?t[e.slot]:void 0)));return void 0!==e.name&&!0!==e.disable&&!0===a.value&&d(n,"push"),(0,i.vs)(t.default,n)}return()=>(0,r.h)(l,{class:"q-btn-toggle",outline:e.outline,flat:e.flat,rounded:e.rounded,push:e.push,stretch:e.stretch,unelevated:e.unelevated,glossy:e.glossy,spread:e.spread},h)}})},9379:(e,t,n)=>{"use strict";n.d(t,{Z:()=>q});var r=n(9835),o=n(499),a=n(1957),i=n(2857),l=n(3940),s=n(1136);n(6727);const c={left:"start",center:"center",right:"end",between:"between",around:"around",evenly:"evenly",stretch:"stretch"},d=Object.keys(c),u={align:{type:String,validator:e=>d.includes(e)}};function p(e){return(0,r.Fl)((()=>{const t=void 0===e.align?!0===e.vertical?"stretch":"left":e.align;return`${!0===e.vertical?"items":"justify"}-${c[t]}`}))}var f=n(244),h=n(945);const m={none:0,xs:4,sm:8,md:16,lg:24,xl:32},v={xs:8,sm:10,md:14,lg:20,xl:24},g=["button","submit","reset"],b=/[^\s]\/[^\s]/,w={...f.LU,...h.$,type:{type:String,default:"button"},label:[Number,String],icon:String,iconRight:String,round:Boolean,outline:Boolean,flat:Boolean,unelevated:Boolean,rounded:Boolean,push:Boolean,glossy:Boolean,size:String,fab:Boolean,fabMini:Boolean,padding:String,color:String,textColor:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,tabindex:[Number,String],ripple:{type:[Boolean,Object],default:!0},align:{...u.align,default:"center"},stack:Boolean,stretch:Boolean,loading:{type:Boolean,default:null},disable:Boolean};function y(e){const t=(0,f.ZP)(e,v),n=p(e),{hasRouterLink:o,hasLink:a,linkTag:i,linkProps:l,navigateToRouterLink:s}=(0,h.Z)("button"),c=(0,r.Fl)((()=>{const n=!1===e.fab&&!1===e.fabMini?t.value:{};return void 0!==e.padding?Object.assign({},n,{padding:e.padding.split(/\s+/).map((e=>e in m?m[e]+"px":e)).join(" "),minWidth:"0",minHeight:"0"}):n})),d=(0,r.Fl)((()=>!0===e.rounded||!0===e.fab||!0===e.fabMini)),u=(0,r.Fl)((()=>!0!==e.disable&&!0!==e.loading)),w=(0,r.Fl)((()=>!0===u.value?e.tabindex||0:-1)),y=(0,r.Fl)((()=>!0===e.flat?"flat":!0===e.outline?"outline":!0===e.push?"push":!0===e.unelevated?"unelevated":"standard")),k=(0,r.Fl)((()=>{const t={tabindex:w.value};return!0===a.value?Object.assign(t,l.value):!0===g.includes(e.type)&&(t.type=e.type),"a"===i.value?(!0===e.disable?t["aria-disabled"]="true":void 0===t.href&&(t.role="button"),!0!==o.value&&!0===b.test(e.type)&&(t.type=e.type)):!0===e.disable&&(t.disabled="",t["aria-disabled"]="true"),!0===e.loading&&void 0!==e.percentage&&Object.assign(t,{role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":e.percentage}),t})),_=(0,r.Fl)((()=>{let t;return void 0!==e.color?t=!0===e.flat||!0===e.outline?`text-${e.textColor||e.color}`:`bg-${e.color} text-${e.textColor||"white"}`:e.textColor&&(t=`text-${e.textColor}`),`q-btn--${y.value} q-btn--`+(!0===e.round?"round":"rectangle"+(!0===d.value?" q-btn--rounded":""))+(void 0!==t?" "+t:"")+(!0===u.value?" q-btn--actionable q-focusable q-hoverable":!0===e.disable?" disabled":"")+(!0===e.fab?" q-btn--fab":!0===e.fabMini?" q-btn--fab-mini":"")+(!0===e.noCaps?" q-btn--no-uppercase":"")+(!0===e.dense?" q-btn--dense":"")+(!0===e.stretch?" no-border-radius self-stretch":"")+(!0===e.glossy?" glossy":"")})),x=(0,r.Fl)((()=>n.value+(!0===e.stack?" column":" row")+(!0===e.noWrap?" no-wrap text-no-wrap":"")+(!0===e.loading?" q-btn__content--hidden":"")));return{classes:_,style:c,innerClasses:x,attributes:k,hasRouterLink:o,hasLink:a,linkTag:i,navigateToRouterLink:s,isActionable:u}}var k=n(5987),_=n(2026),x=n(1384),S=n(1705);const{passiveCapture:C}=x.rU;let L=null,E=null,A=null;const q=(0,k.L)({name:"QBtn",props:{...w,percentage:Number,darkPercentage:Boolean},emits:["click","keydown","touchstart","mousedown","keyup"],setup(e,{slots:t,emit:n}){const{proxy:c}=(0,r.FN)(),{classes:d,style:u,innerClasses:p,attributes:f,hasRouterLink:h,hasLink:m,linkTag:v,navigateToRouterLink:g,isActionable:b}=y(e),w=(0,o.iH)(null),k=(0,o.iH)(null);let q,P,T=null;const j=(0,r.Fl)((()=>void 0!==e.label&&null!==e.label&&""!==e.label)),O=(0,r.Fl)((()=>!0!==e.disable&&!1!==e.ripple&&{keyCodes:!0===m.value?[13,32]:[13],...!0===e.ripple?{}:e.ripple})),M=(0,r.Fl)((()=>({center:e.round}))),R=(0,r.Fl)((()=>{const t=Math.max(0,Math.min(100,e.percentage));return t>0?{transition:"transform 0.6s",transform:`translateX(${t-100}%)`}:{}})),F=(0,r.Fl)((()=>!0===e.loading?{onMousedown:U,onTouchstartPassive:U,onClick:U,onKeydown:U,onKeyup:U}:!0===b.value?{onClick:z,onKeydown:B,onMousedown:V,onTouchstart:I}:{onClick:x.NS})),H=(0,r.Fl)((()=>({ref:w,class:"q-btn q-btn-item non-selectable no-outline "+d.value,style:u.value,...f.value,...F.value})));function z(t){if(null!==w.value){if(void 0!==t){if(!0===t.defaultPrevented)return;const n=document.activeElement;if("submit"===e.type&&n!==document.body&&!1===w.value.contains(n)&&!1===n.contains(w.value)){w.value.focus();const e=()=>{document.removeEventListener("keydown",x.NS,!0),document.removeEventListener("keyup",e,C),null!==w.value&&w.value.removeEventListener("blur",e,C)};document.addEventListener("keydown",x.NS,!0),document.addEventListener("keyup",e,C),w.value.addEventListener("blur",e,C)}}if(!0===h.value){const e=()=>{t.__qNavigate=!0,g(t)};n("click",t,e),!0!==t.defaultPrevented&&e()}else n("click",t)}}function B(e){null!==w.value&&(n("keydown",e),!0===(0,S.So)(e,[13,32])&&E!==w.value&&(null!==E&&N(),!0!==e.defaultPrevented&&(w.value.focus(),E=w.value,w.value.classList.add("q-btn--active"),document.addEventListener("keyup",$,!0),w.value.addEventListener("blur",$,C)),(0,x.NS)(e)))}function I(e){null!==w.value&&(n("touchstart",e),!0!==e.defaultPrevented&&(L!==w.value&&(null!==L&&N(),L=w.value,T=e.target,T.addEventListener("touchcancel",$,C),T.addEventListener("touchend",$,C)),q=!0,clearTimeout(P),P=setTimeout((()=>{q=!1}),200)))}function V(e){null!==w.value&&(e.qSkipRipple=!0===q,n("mousedown",e),!0!==e.defaultPrevented&&A!==w.value&&(null!==A&&N(),A=w.value,w.value.classList.add("q-btn--active"),document.addEventListener("mouseup",$,C)))}function $(e){if(null!==w.value&&(void 0===e||"blur"!==e.type||document.activeElement!==w.value)){if(void 0!==e&&"keyup"===e.type){if(E===w.value&&!0===(0,S.So)(e,[13,32])){const t=new MouseEvent("click",e);t.qKeyEvent=!0,!0===e.defaultPrevented&&(0,x.X$)(t),!0===e.cancelBubble&&(0,x.sT)(t),w.value.dispatchEvent(t),(0,x.NS)(e),e.qKeyEvent=!0}n("keyup",e)}N()}}function N(e){const t=k.value;!0===e||L!==w.value&&A!==w.value||null===t||t===document.activeElement||(t.setAttribute("tabindex",-1),t.focus()),L===w.value&&(null!==T&&(T.removeEventListener("touchcancel",$,C),T.removeEventListener("touchend",$,C)),L=T=null),A===w.value&&(document.removeEventListener("mouseup",$,C),A=null),E===w.value&&(document.removeEventListener("keyup",$,!0),null!==w.value&&w.value.removeEventListener("blur",$,C),E=null),null!==w.value&&w.value.classList.remove("q-btn--active")}function U(e){(0,x.NS)(e),e.qSkipRipple=!0}return(0,r.Jd)((()=>{N(!0)})),Object.assign(c,{click:z}),()=>{let n=[];void 0!==e.icon&&n.push((0,r.h)(i.Z,{name:e.icon,left:!1===e.stack&&!0===j.value,role:"img","aria-hidden":"true"})),!0===j.value&&n.push((0,r.h)("span",{class:"block"},[e.label])),n=(0,_.vs)(t.default,n),void 0!==e.iconRight&&!1===e.round&&n.push((0,r.h)(i.Z,{name:e.iconRight,right:!1===e.stack&&!0===j.value,role:"img","aria-hidden":"true"}));const o=[(0,r.h)("span",{class:"q-focus-helper",ref:k})];return!0===e.loading&&void 0!==e.percentage&&o.push((0,r.h)("span",{class:"q-btn__progress absolute-full overflow-hidden"},[(0,r.h)("span",{class:"q-btn__progress-indicator fit block"+(!0===e.darkPercentage?" q-btn__progress--dark":""),style:R.value})])),o.push((0,r.h)("span",{class:"q-btn__content text-center col items-center q-anchor--skip "+p.value},n)),null!==e.loading&&o.push((0,r.h)(a.uT,{name:"q-transition--fade"},(()=>!0===e.loading?[(0,r.h)("span",{key:"loading",class:"absolute-full flex flex-center"},void 0!==t.loading?t.loading():[(0,r.h)(l.Z)])]:null))),(0,r.wy)((0,r.h)(v.value,H.value,o),[[s.Z,O.value,void 0,M.value]])}}})},4458:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(9835),o=n(8234),a=n(5987),i=n(2026);const l=(0,a.L)({name:"QCard",props:{...o.S,tag:{type:String,default:"div"},square:Boolean,flat:Boolean,bordered:Boolean},setup(e,{slots:t}){const n=(0,r.FN)(),a=(0,o.Z)(e,n.proxy.$q),l=(0,r.Fl)((()=>"q-card"+(!0===a.value?" q-card--dark q-dark":"")+(!0===e.bordered?" q-card--bordered":"")+(!0===e.square?" q-card--square no-border-radius":"")+(!0===e.flat?" q-card--flat no-shadow":"")));return()=>(0,r.h)(e.tag,{class:l.value},(0,i.KR)(t.default))}})},3190:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(9835),o=n(5987),a=n(2026);const i=(0,o.L)({name:"QCardSection",props:{tag:{type:String,default:"div"},horizontal:Boolean},setup(e,{slots:t}){const n=(0,r.Fl)((()=>"q-card__section q-card__section--"+(!0===e.horizontal?"horiz row no-wrap":"vert")));return()=>(0,r.h)(e.tag,{class:n.value},(0,a.KR)(t.default))}})},1221:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(9835),o=n(2857),a=n(5987),i=n(1926);const l=(0,r.h)("div",{key:"svg",class:"q-checkbox__bg absolute"},[(0,r.h)("svg",{class:"q-checkbox__svg fit absolute-full",viewBox:"0 0 24 24","aria-hidden":"true"},[(0,r.h)("path",{class:"q-checkbox__truthy",fill:"none",d:"M1.73,12.91 8.1,19.28 22.79,4.59"}),(0,r.h)("path",{class:"q-checkbox__indet",d:"M4,14H20V10H4"})])]),s=(0,a.L)({name:"QCheckbox",props:i.Fz,emits:i.ZB,setup(e){function t(t,n){const a=(0,r.Fl)((()=>(!0===t.value?e.checkedIcon:!0===n.value?e.indeterminateIcon:e.uncheckedIcon)||null));return()=>null!==a.value?[(0,r.h)("div",{key:"icon",class:"q-checkbox__icon-container absolute-full flex flex-center no-wrap"},[(0,r.h)(o.Z,{class:"q-checkbox__icon",name:a.value})])]:[l]}return(0,i.ZP)("checkbox",t)}})},1926:(e,t,n)=>{"use strict";n.d(t,{Fz:()=>p,ZB:()=>f,ZP:()=>h});var r=n(9835),o=n(499),a=n(8234),i=n(244),l=n(5917),s=n(9256),c=n(9480),d=n(1384),u=n(2026);const p={...a.S,...i.LU,...s.Fz,modelValue:{required:!0,default:null},val:{},trueValue:{default:!0},falseValue:{default:!1},indeterminateValue:{default:null},checkedIcon:String,uncheckedIcon:String,indeterminateIcon:String,toggleOrder:{type:String,validator:e=>"tf"===e||"ft"===e},toggleIndeterminate:Boolean,label:String,leftLabel:Boolean,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},f=["update:modelValue"];function h(e,t){const{props:n,slots:p,emit:f,proxy:h}=(0,r.FN)(),{$q:m}=h,v=(0,a.Z)(n,m),g=(0,o.iH)(null),{refocusTargetEl:b,refocusTarget:w}=(0,l.Z)(n,g),y=(0,i.ZP)(n,c.Z),k=(0,r.Fl)((()=>void 0!==n.val&&Array.isArray(n.modelValue))),_=(0,r.Fl)((()=>!0===k.value?n.modelValue.indexOf(n.val):-1)),x=(0,r.Fl)((()=>!0===k.value?_.value>-1:n.modelValue===n.trueValue)),S=(0,r.Fl)((()=>!0===k.value?-1===_.value:n.modelValue===n.falseValue)),C=(0,r.Fl)((()=>!1===x.value&&!1===S.value)),L=(0,r.Fl)((()=>!0===n.disable?-1:n.tabindex||0)),E=(0,r.Fl)((()=>`q-${e} cursor-pointer no-outline row inline no-wrap items-center`+(!0===n.disable?" disabled":"")+(!0===v.value?` q-${e}--dark`:"")+(!0===n.dense?` q-${e}--dense`:"")+(!0===n.leftLabel?" reverse":""))),A=(0,r.Fl)((()=>{const t=!0===x.value?"truthy":!0===S.value?"falsy":"indet",r=void 0===n.color||!0!==n.keepColor&&("toggle"===e?!0!==x.value:!0===S.value)?"":` text-${n.color}`;return`q-${e}__inner relative-position non-selectable q-${e}__inner--${t}${r}`})),q=(0,r.Fl)((()=>{const e={type:"checkbox"};return void 0!==n.name&&Object.assign(e,{"^checked":!0===x.value?"checked":void 0,name:n.name,value:!0===k.value?n.val:n.trueValue}),e})),P=(0,s.eX)(q),T=(0,r.Fl)((()=>{const e={tabindex:L.value,role:"checkbox","aria-label":n.label,"aria-checked":!0===C.value?"mixed":!0===x.value?"true":"false"};return!0===n.disable&&(e["aria-disabled"]="true"),e}));function j(e){void 0!==e&&((0,d.NS)(e),w(e)),!0!==n.disable&&f("update:modelValue",O(),e)}function O(){if(!0===k.value){if(!0===x.value){const e=n.modelValue.slice();return e.splice(_.value,1),e}return n.modelValue.concat([n.val])}if(!0===x.value){if("ft"!==n.toggleOrder||!1===n.toggleIndeterminate)return n.falseValue}else{if(!0!==S.value)return"ft"!==n.toggleOrder?n.trueValue:n.falseValue;if("ft"===n.toggleOrder||!1===n.toggleIndeterminate)return n.trueValue}return n.indeterminateValue}function M(e){13!==e.keyCode&&32!==e.keyCode||(0,d.NS)(e)}function R(e){13!==e.keyCode&&32!==e.keyCode||j(e)}const F=t(x,C);return Object.assign(h,{toggle:j}),()=>{const t=F();!0!==n.disable&&P(t,"unshift",` q-${e}__native absolute q-ma-none q-pa-none`);const o=[(0,r.h)("div",{class:A.value,style:y.value},t)];null!==b.value&&o.push(b.value);const a=void 0!==n.label?(0,u.vs)(p.default,[n.label]):(0,u.KR)(p.default);return void 0!==a&&o.push((0,r.h)("div",{class:`q-${e}__label q-anchor--skip`},a)),(0,r.h)("div",{ref:g,class:E.value,...T.value,onClick:j,onKeydown:M,onKeyup:R},o)}}},3706:(e,t,n)=>{"use strict";n.d(t,{Z:()=>S});n(6727),n(702);var r=n(9835),o=n(499),a=n(1957),i=n(5310);function l(e,t,n){let o;function a(){void 0!==o&&(i.Z.remove(o),o=void 0)}return(0,r.Jd)((()=>{!0===e.value&&a()})),{removeFromHistory:a,addToHistory(){o={condition:()=>!0===n.value,handler:t},i.Z.add(o)}}}var s=n(2695),c=n(6916),d=n(3842),u=n(431),p=n(1518),f=n(5984);function h(){let e;return{preventBodyScroll(t){t===e||void 0===e&&!0!==t||(e=t,(0,f.Z)(t))}}}var m=n(5987),v=n(223),g=n(2026),b=n(6532),w=n(4173),y=n(7026);let k=0;const _={standard:"fixed-full flex-center",top:"fixed-top justify-center",bottom:"fixed-bottom justify-center",right:"fixed-right items-center",left:"fixed-left items-center"},x={standard:["scale","scale"],top:["slide-down","slide-up"],bottom:["slide-up","slide-down"],right:["slide-left","slide-right"],left:["slide-right","slide-left"]},S=(0,m.L)({name:"QDialog",inheritAttrs:!1,props:{...d.vr,...u.D,transitionShow:String,transitionHide:String,persistent:Boolean,autoClose:Boolean,noEscDismiss:Boolean,noBackdropDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,noShake:Boolean,seamless:Boolean,maximized:Boolean,fullWidth:Boolean,fullHeight:Boolean,square:Boolean,position:{type:String,default:"standard",validator:e=>"standard"===e||["top","bottom","left","right"].includes(e)}},emits:[...d.gH,"shake","click","escape-key"],setup(e,{slots:t,emit:n,attrs:i}){const u=(0,r.FN)(),f=(0,o.iH)(null),m=(0,o.iH)(!1),S=(0,o.iH)(!1),C=(0,o.iH)(!1);let L,E,A,q=null;const P=(0,r.Fl)((()=>!0!==e.persistent&&!0!==e.noRouteDismiss&&!0!==e.seamless)),{preventBodyScroll:T}=h(),{registerTimeout:j,removeTimeout:O}=(0,s.Z)(),{registerTick:M,removeTick:R}=(0,c.Z)(),{showPortal:F,hidePortal:H,portalIsAccessible:z,renderPortal:B}=(0,p.Z)(u,f,le,!0),{hide:I}=(0,d.ZP)({showing:m,hideOnRouteChange:P,handleShow:Y,handleHide:Q,processOnMount:!0}),{addToHistory:V,removeFromHistory:$}=l(m,I,P),N=(0,r.Fl)((()=>"q-dialog__inner flex no-pointer-events q-dialog__inner--"+(!0===e.maximized?"maximized":"minimized")+` q-dialog__inner--${e.position} ${_[e.position]}`+(!0===C.value?" q-dialog__inner--animating":"")+(!0===e.fullWidth?" q-dialog__inner--fullwidth":"")+(!0===e.fullHeight?" q-dialog__inner--fullheight":"")+(!0===e.square?" q-dialog__inner--square":""))),U=(0,r.Fl)((()=>"q-transition--"+(void 0===e.transitionShow?x[e.position][0]:e.transitionShow))),D=(0,r.Fl)((()=>"q-transition--"+(void 0===e.transitionHide?x[e.position][1]:e.transitionHide))),Z=(0,r.Fl)((()=>!0===S.value?D.value:U.value)),W=(0,r.Fl)((()=>`--q-transition-duration: ${e.transitionDuration}ms`)),K=(0,r.Fl)((()=>!0===m.value&&!0!==e.seamless)),G=(0,r.Fl)((()=>!0===e.autoClose?{onClick:oe}:{})),J=(0,r.Fl)((()=>["q-dialog fullscreen no-pointer-events q-dialog--"+(!0===K.value?"modal":"seamless"),i.class]));function Y(t){O(),R(),V(),q=!1===e.noRefocus&&null!==document.activeElement?document.activeElement:null,re(e.maximized),F(),C.value=!0,!0!==e.noFocus&&(null!==document.activeElement&&document.activeElement.blur(),M(X)),j((()=>{if(!0===u.proxy.$q.platform.is.ios){if(!0!==e.seamless&&document.activeElement){const{top:e,bottom:t}=document.activeElement.getBoundingClientRect(),{innerHeight:n}=window,r=void 0!==window.visualViewport?window.visualViewport.height:n;e>0&&t>r/2&&(document.scrollingElement.scrollTop=Math.min(document.scrollingElement.scrollHeight-r,t>=n?1/0:Math.ceil(document.scrollingElement.scrollTop+t-r/2))),document.activeElement.scrollIntoView()}A=!0,f.value.click(),A=!1}F(!0),C.value=!1,n("show",t)}),e.transitionDuration)}function Q(t){O(),R(),$(),ne(!0),C.value=!0,H(),null!==q&&(q.focus(),q=null),j((()=>{H(!0),C.value=!1,n("hide",t)}),e.transitionDuration)}function X(e){(0,y.jd)((()=>{let t=f.value;null!==t&&!0!==t.contains(document.activeElement)&&(t=t.querySelector(e||"[autofocus], [data-autofocus]")||t,t.focus({preventScroll:!0}))}))}function ee(){X(),n("shake");const e=f.value;null!==e&&(e.classList.remove("q-animate--scale"),e.classList.add("q-animate--scale"),clearTimeout(L),L=setTimeout((()=>{null!==f.value&&(e.classList.remove("q-animate--scale"),X())}),170))}function te(){!0!==e.seamless&&(!0===e.persistent||!0===e.noEscDismiss?!0!==e.maximized&&!0!==e.noShake&&ee():(n("escape-key"),I()))}function ne(t){clearTimeout(L),!0!==t&&!0!==m.value||(re(!1),!0!==e.seamless&&(T(!1),(0,w.H)(ie),(0,b.k)(te))),!0!==t&&(q=null)}function re(e){!0===e?!0!==E&&(k<1&&document.body.classList.add("q-body--dialog"),k++,E=!0):!0===E&&(k<2&&document.body.classList.remove("q-body--dialog"),k--,E=!1)}function oe(e){!0!==A&&(I(e),n("click",e))}function ae(t){!0!==e.persistent&&!0!==e.noBackdropDismiss?I(t):!0!==e.noShake&&ee()}function ie(e){!0===z.value&&!0!==(0,v.mY)(f.value,e.target)&&X('[tabindex]:not([tabindex="-1"])')}function le(){return(0,r.h)("div",{...i,class:J.value},[(0,r.h)(a.uT,{name:"q-transition--fade",appear:!0},(()=>!0===K.value?(0,r.h)("div",{class:"q-dialog__backdrop fixed-full",style:W.value,"aria-hidden":"true",onMousedown:ae}):null)),(0,r.h)(a.uT,{name:Z.value,appear:!0},(()=>!0===m.value?(0,r.h)("div",{ref:f,class:N.value,style:W.value,tabindex:-1,...G.value},(0,g.KR)(t.default)):null))])}return(0,r.YP)(m,(e=>{(0,r.Y3)((()=>{S.value=e}))})),(0,r.YP)((()=>e.maximized),(e=>{!0===m.value&&re(e)})),(0,r.YP)(K,(e=>{T(e),!0===e?((0,w.i)(ie),(0,b.c)(te)):((0,w.H)(ie),(0,b.k)(te))})),Object.assign(u.proxy,{focus:X,shake:ee,__updateRefocusTarget(e){q=e||null}}),(0,r.Jd)(ne),B}})},9542:(e,t,n)=>{"use strict";n.d(t,{Z:()=>x});n(702);var r=n(499),o=n(9835),a=n(1957),i=n(490),l=n(1233),s=n(3115),c=n(2857),d=n(5987);const u=(0,d.L)({name:"QSlideTransition",props:{appear:Boolean,duration:{type:Number,default:300}},emits:["show","hide"],setup(e,{slots:t,emit:n}){let r,i,l,s,c,d,u=!1;function p(){r&&r(),r=null,u=!1,clearTimeout(l),clearTimeout(s),void 0!==i&&i.removeEventListener("transitionend",c),c=null}function f(t,n,o){t.style.overflowY="hidden",void 0!==n&&(t.style.height=`${n}px`),t.style.transition=`height ${e.duration}ms cubic-bezier(.25, .8, .50, 1)`,u=!0,r=o}function h(e,t){e.style.overflowY=null,e.style.height=null,e.style.transition=null,p(),t!==d&&n(t)}function m(t,n){let r=0;i=t,!0===u?(p(),r=t.offsetHeight===t.scrollHeight?0:void 0):d="hide",f(t,r,n),l=setTimeout((()=>{t.style.height=`${t.scrollHeight}px`,c=e=>{Object(e)===e&&e.target!==t||h(t,"show")},t.addEventListener("transitionend",c),s=setTimeout(c,1.1*e.duration)}),100)}function v(t,n){let r;i=t,!0===u?p():(d="show",r=t.scrollHeight),f(t,r,n),l=setTimeout((()=>{t.style.height=0,c=e=>{Object(e)===e&&e.target!==t||h(t,"hide")},t.addEventListener("transitionend",c),s=setTimeout(c,1.1*e.duration)}),100)}return(0,o.Jd)((()=>{!0===u&&p()})),()=>(0,o.h)(a.uT,{css:!1,appear:e.appear,onEnter:m,onLeave:v},t.default)}});var p=n(8234);const f={true:"inset",item:"item-inset","item-thumbnail":"item-thumbnail-inset"},h={xs:2,sm:4,md:8,lg:16,xl:24},m=(0,d.L)({name:"QSeparator",props:{...p.S,spaced:[Boolean,String],inset:[Boolean,String],vertical:Boolean,color:String,size:String},setup(e){const t=(0,o.FN)(),n=(0,p.Z)(e,t.proxy.$q),r=(0,o.Fl)((()=>!0===e.vertical?"vertical":"horizontal")),a=(0,o.Fl)((()=>` q-separator--${r.value}`)),i=(0,o.Fl)((()=>!1!==e.inset?`${a.value}-${f[e.inset]}`:"")),l=(0,o.Fl)((()=>`q-separator${a.value}${i.value}`+(void 0!==e.color?` bg-${e.color}`:"")+(!0===n.value?" q-separator--dark":""))),s=(0,o.Fl)((()=>{const t={};if(void 0!==e.size&&(t[!0===e.vertical?"width":"height"]=e.size),!1!==e.spaced){const n=!0===e.spaced?`${h.md}px`:e.spaced in h?`${h[e.spaced]}px`:e.spaced,r=!0===e.vertical?["Left","Right"]:["Top","Bottom"];t[`margin${r[0]}`]=t[`margin${r[1]}`]=n}return t}));return()=>(0,o.h)("hr",{class:l.value,style:s.value,"aria-orientation":r.value})}});var v=n(945),g=n(3842),b=n(1384),w=n(2026),y=n(796);const k=(0,r.Um)({}),_=Object.keys(v.$),x=(0,d.L)({name:"QExpansionItem",props:{...v.$,...g.vr,...p.S,icon:String,label:String,labelLines:[Number,String],caption:String,captionLines:[Number,String],dense:Boolean,expandIcon:String,expandedIcon:String,expandIconClass:[Array,String,Object],duration:Number,headerInsetLevel:Number,contentInsetLevel:Number,expandSeparator:Boolean,defaultOpened:Boolean,expandIconToggle:Boolean,switchToggleSide:Boolean,denseToggle:Boolean,group:String,popup:Boolean,headerStyle:[Array,String,Object],headerClass:[Array,String,Object]},emits:[...g.gH,"click","after-show","after-hide"],setup(e,{slots:t,emit:n}){const{proxy:{$q:d}}=(0,o.FN)(),f=(0,p.Z)(e,d),h=(0,r.iH)(null!==e.modelValue?e.modelValue:e.defaultOpened),v=(0,r.iH)(null),{hide:x,toggle:S}=(0,g.ZP)({showing:h});let C,L;const E=(0,o.Fl)((()=>"q-expansion-item q-item-type q-expansion-item--"+(!0===h.value?"expanded":"collapsed")+" q-expansion-item--"+(!0===e.popup?"popup":"standard"))),A=(0,o.Fl)((()=>{if(void 0===e.contentInsetLevel)return null;const t=!0===d.lang.rtl?"Right":"Left";return{["padding"+t]:56*e.contentInsetLevel+"px"}})),q=(0,o.Fl)((()=>!0!==e.disable&&(void 0!==e.href||void 0!==e.to&&null!==e.to&&""!==e.to))),P=(0,o.Fl)((()=>{const t={};return _.forEach((n=>{t[n]=e[n]})),t})),T=(0,o.Fl)((()=>!0===q.value||!0!==e.expandIconToggle)),j=(0,o.Fl)((()=>void 0!==e.expandedIcon&&!0===h.value?e.expandedIcon:e.expandIcon||d.iconSet.expansionItem[!0===e.denseToggle?"denseIcon":"icon"])),O=(0,o.Fl)((()=>!0!==e.disable&&(!0===q.value||!0===e.expandIconToggle)));function M(e){!0!==q.value&&S(e),n("click",e)}function R(e){13===e.keyCode&&F(e,!0)}function F(e,t){!0!==t&&null!==v.value&&v.value.focus(),S(e),(0,b.NS)(e)}function H(){n("after-show")}function z(){n("after-hide")}function B(){void 0===C&&(C=(0,y.Z)()),!0===h.value&&(k[e.group]=C);const t=(0,o.YP)(h,(t=>{!0===t?k[e.group]=C:k[e.group]===C&&delete k[e.group]})),n=(0,o.YP)((()=>k[e.group]),((e,t)=>{t===C&&void 0!==e&&e!==C&&x()}));L=()=>{t(),n(),k[e.group]===C&&delete k[e.group],L=void 0}}function I(){const t={class:["q-focusable relative-position cursor-pointer"+(!0===e.denseToggle&&!0===e.switchToggleSide?" items-end":""),e.expandIconClass],side:!0!==e.switchToggleSide,avatar:e.switchToggleSide},n=[(0,o.h)(c.Z,{class:"q-expansion-item__toggle-icon"+(void 0===e.expandedIcon&&!0===h.value?" q-expansion-item__toggle-icon--rotated":""),name:j.value})];return!0===O.value&&(Object.assign(t,{tabindex:0,onClick:F,onKeyup:R}),n.unshift((0,o.h)("div",{ref:v,class:"q-expansion-item__toggle-focus q-icon q-focus-helper q-focus-helper--rounded",tabindex:-1}))),(0,o.h)(l.Z,t,(()=>n))}function V(){let n;return void 0!==t.header?n=[].concat(t.header()):(n=[(0,o.h)(l.Z,(()=>[(0,o.h)(s.Z,{lines:e.labelLines},(()=>e.label||"")),e.caption?(0,o.h)(s.Z,{lines:e.captionLines,caption:!0},(()=>e.caption)):null]))],e.icon&&n[!0===e.switchToggleSide?"push":"unshift"]((0,o.h)(l.Z,{side:!0===e.switchToggleSide,avatar:!0!==e.switchToggleSide},(()=>(0,o.h)(c.Z,{name:e.icon}))))),!0!==e.disable&&n[!0===e.switchToggleSide?"unshift":"push"](I()),n}function $(){const t={ref:"item",style:e.headerStyle,class:e.headerClass,dark:f.value,disable:e.disable,dense:e.dense,insetLevel:e.headerInsetLevel};return!0===T.value&&(t.clickable=!0,t.onClick=M,!0===q.value&&Object.assign(t,P.value)),(0,o.h)(i.Z,t,V)}function N(){return(0,o.wy)((0,o.h)("div",{key:"e-content",class:"q-expansion-item__content relative-position",style:A.value},(0,w.KR)(t.default)),[[a.F8,h.value]])}function U(){const t=[$(),(0,o.h)(u,{duration:e.duration,onShow:H,onHide:z},N)];return!0===e.expandSeparator&&t.push((0,o.h)(m,{class:"q-expansion-item__border q-expansion-item__border--top absolute-top",dark:f.value}),(0,o.h)(m,{class:"q-expansion-item__border q-expansion-item__border--bottom absolute-bottom",dark:f.value})),t}return(0,o.YP)((()=>e.group),(e=>{void 0!==L&&L(),void 0!==e&&B()})),void 0!==e.group&&B(),(0,o.Jd)((()=>{void 0!==L&&L()})),()=>(0,o.h)("div",{class:E.value},[(0,o.h)("div",{class:"q-expansion-item__container relative-position"},U())])}})},2857:(e,t,n)=>{"use strict";n.d(t,{Z:()=>w});n(702);var r=n(9835),o=n(244),a=n(5987),i=n(2026);const l="0 0 24 24",s=e=>e,c=e=>`ionicons ${e}`,d={"mdi-":e=>`mdi ${e}`,"icon-":s,"bt-":e=>`bt ${e}`,"eva-":e=>`eva ${e}`,"ion-md":c,"ion-ios":c,"ion-logo":c,"iconfont ":s,"ti-":e=>`themify-icon ${e}`,"bi-":e=>`bootstrap-icons ${e}`},u={o_:"-outlined",r_:"-round",s_:"-sharp"},p=new RegExp("^("+Object.keys(d).join("|")+")"),f=new RegExp("^("+Object.keys(u).join("|")+")"),h=/^[Mm]\s?[-+]?\.?\d/,m=/^img:/,v=/^svguse:/,g=/^ion-/,b=/^(fa-(solid|regular|light|brands|duotone|thin)|[lf]a[srlbdk]?) /,w=(0,a.L)({name:"QIcon",props:{...o.LU,tag:{type:String,default:"i"},name:String,color:String,left:Boolean,right:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=(0,r.FN)(),a=(0,o.ZP)(e),s=(0,r.Fl)((()=>"q-icon"+(!0===e.left?" on-left":"")+(!0===e.right?" on-right":"")+(void 0!==e.color?` text-${e.color}`:""))),c=(0,r.Fl)((()=>{let t,o=e.name;if("none"===o||!o)return{none:!0};if(null!==n.iconMapFn){const e=n.iconMapFn(o);if(void 0!==e){if(void 0===e.icon)return{cls:e.cls,content:void 0!==e.content?e.content:" "};if(o=e.icon,"none"===o||!o)return{none:!0}}}if(!0===h.test(o)){const[e,t=l]=o.split("|");return{svg:!0,viewBox:t,nodes:e.split("&&").map((e=>{const[t,n,o]=e.split("@@");return(0,r.h)("path",{style:n,d:t,transform:o})}))}}if(!0===m.test(o))return{img:!0,src:o.substring(4)};if(!0===v.test(o)){const[e,t=l]=o.split("|");return{svguse:!0,src:e.substring(7),viewBox:t}}let a=" ";const i=o.match(p);if(null!==i)t=d[i[1]](o);else if(!0===b.test(o))t=o;else if(!0===g.test(o))t=`ionicons ion-${!0===n.platform.is.ios?"ios":"md"}${o.substring(3)}`;else{t="notranslate material-icons";const e=o.match(f);null!==e&&(o=o.substring(2),t+=u[e[1]]),a=o}return{cls:t,content:a}}));return()=>{const n={class:s.value,style:a.value,"aria-hidden":"true",role:"presentation"};return!0===c.value.none?(0,r.h)(e.tag,n,(0,i.KR)(t.default)):!0===c.value.img?(0,r.h)("span",n,(0,i.vs)(t.default,[(0,r.h)("img",{src:c.value.src})])):!0===c.value.svg?(0,r.h)("span",n,(0,i.vs)(t.default,[(0,r.h)("svg",{viewBox:c.value.viewBox},c.value.nodes)])):!0===c.value.svguse?(0,r.h)("span",n,(0,i.vs)(t.default,[(0,r.h)("svg",{viewBox:c.value.viewBox},[(0,r.h)("use",{"xlink:href":c.value.src})])])):(void 0!==c.value.cls&&(n.class+=" "+c.value.cls),(0,r.h)(e.tag,n,(0,i.vs)(t.default,[c.value.content])))}}})},854:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var r=n(9835),o=n(1957),a=n(3940),i=n(5987),l=n(8234),s=n(431);const c=(0,i.L)({name:"QInnerLoading",props:{...l.S,...s.D,showing:Boolean,color:String,size:{type:[String,Number],default:42},label:String,labelClass:String,labelStyle:[String,Array,Object]},setup(e,{slots:t}){const n=(0,r.FN)(),i=(0,l.Z)(e,n.proxy.$q),{transition:c,transitionStyle:d}=(0,s.Z)(e,(0,r.Fl)((()=>e.showing))),u=(0,r.Fl)((()=>"q-inner-loading absolute-full column flex-center"+(!0===i.value?" q-inner-loading--dark":""))),p=(0,r.Fl)((()=>"q-inner-loading__label"+(void 0!==e.labelClass?` ${e.labelClass}`:"")));function f(){const t=[(0,r.h)(a.Z,{size:e.size,color:e.color})];return void 0!==e.label&&t.push((0,r.h)("div",{class:p.value,style:e.labelStyle},[e.label])),t}function h(){return!0===e.showing?(0,r.h)("div",{class:u.value,style:d.value},void 0!==t.default?t.default():f()):null}return()=>(0,r.h)(o.uT,{name:c.value,appear:!0},h)}})},490:(e,t,n)=>{"use strict";n.d(t,{Z:()=>u});var r=n(9835),o=n(499),a=n(8234),i=n(945),l=n(5987),s=n(2026),c=n(1384),d=n(1705);const u=(0,l.L)({name:"QItem",props:{...a.S,...i.$,tag:{type:String,default:"div"},active:Boolean,clickable:Boolean,dense:Boolean,insetLevel:Number,tabindex:[String,Number],focused:Boolean,manualFocus:Boolean},emits:["click","keyup"],setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=(0,r.FN)(),u=(0,a.Z)(e,l),{hasRouterLink:p,hasLink:f,linkProps:h,linkClass:m,linkTag:v,navigateToRouterLink:g}=(0,i.Z)(),b=(0,o.iH)(null),w=(0,o.iH)(null),y=(0,r.Fl)((()=>!0===e.clickable||!0===f.value||"label"===e.tag)),k=(0,r.Fl)((()=>!0!==e.disable&&!0===y.value)),_=(0,r.Fl)((()=>"q-item q-item-type row no-wrap"+(!0===e.dense?" q-item--dense":"")+(!0===u.value?" q-item--dark":"")+(!0===f.value?m.value:!0===e.active?(void 0!==e.activeClass?` ${e.activeClass}`:"")+" q-item--active":"")+(!0===e.disable?" disabled":"")+(!0===k.value?" q-item--clickable q-link cursor-pointer "+(!0===e.manualFocus?"q-manual-focusable":"q-focusable q-hoverable")+(!0===e.focused?" q-manual-focusable--focused":""):""))),x=(0,r.Fl)((()=>{if(void 0===e.insetLevel)return null;const t=!0===l.lang.rtl?"Right":"Left";return{["padding"+t]:16+56*e.insetLevel+"px"}}));function S(e){!0===k.value&&(null!==w.value&&(!0!==e.qKeyEvent&&document.activeElement===b.value?w.value.focus():document.activeElement===w.value&&b.value.focus()),!0===p.value&&g(e),n("click",e))}function C(e){if(!0===k.value&&!0===(0,d.So)(e,13)){(0,c.NS)(e),e.qKeyEvent=!0;const t=new MouseEvent("click",e);t.qKeyEvent=!0,b.value.dispatchEvent(t)}n("keyup",e)}function L(){const e=(0,s.Bl)(t.default,[]);return!0===k.value&&e.unshift((0,r.h)("div",{class:"q-focus-helper",tabindex:-1,ref:w})),e}return()=>{const t={ref:b,class:_.value,style:x.value,onClick:S,onKeyup:C};return!0===k.value?(t.tabindex=e.tabindex||"0",Object.assign(t,h.value)):!0===y.value&&(t["aria-disabled"]="true"),(0,r.h)(v.value,t,L())}}})},3115:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(9835),o=n(5987),a=n(2026);const i=(0,o.L)({name:"QItemLabel",props:{overline:Boolean,caption:Boolean,header:Boolean,lines:[Number,String]},setup(e,{slots:t}){const n=(0,r.Fl)((()=>parseInt(e.lines,10))),o=(0,r.Fl)((()=>"q-item__label"+(!0===e.overline?" q-item__label--overline text-overline":"")+(!0===e.caption?" q-item__label--caption text-caption":"")+(!0===e.header?" q-item__label--header":"")+(1===n.value?" ellipsis":""))),i=(0,r.Fl)((()=>void 0!==e.lines&&n.value>1?{overflow:"hidden",display:"-webkit-box","-webkit-box-orient":"vertical","-webkit-line-clamp":n.value}:null));return()=>(0,r.h)("div",{style:i.value,class:o.value},(0,a.KR)(t.default))}})},1233:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(9835),o=n(5987),a=n(2026);const i=(0,o.L)({name:"QItemSection",props:{avatar:Boolean,thumbnail:Boolean,side:Boolean,top:Boolean,noWrap:Boolean},setup(e,{slots:t}){const n=(0,r.Fl)((()=>"q-item__section column q-item__section--"+(!0===e.avatar||!0===e.side||!0===e.thumbnail?"side":"main")+(!0===e.top?" q-item__section--top justify-start":" justify-center")+(!0===e.avatar?" q-item__section--avatar":"")+(!0===e.thumbnail?" q-item__section--thumbnail":"")+(!0===e.noWrap?" q-item__section--nowrap":"")));return()=>(0,r.h)("div",{class:n.value},(0,a.KR)(t.default))}})},7605:(e,t,n)=>{"use strict";n.d(t,{Z:()=>m});var r=n(9835),o=n(499),a=n(7506),i=(n(6727),n(702),n(5987)),l=n(3701),s=n(1384);const{passive:c}=s.rU,d=["both","horizontal","vertical"],u=(0,i.L)({name:"QScrollObserver",props:{axis:{type:String,validator:e=>d.includes(e),default:"vertical"},debounce:[String,Number],scrollTarget:{default:void 0}},emits:["scroll"],setup(e,{emit:t}){const n={position:{top:0,left:0},direction:"down",directionChanged:!1,delta:{top:0,left:0},inflectionPoint:{top:0,left:0}};let o,a,i=null;function d(){null!==i&&i();const r=Math.max(0,(0,l.u3)(o)),a=(0,l.OI)(o),s={top:r-n.position.top,left:a-n.position.left};if("vertical"===e.axis&&0===s.top||"horizontal"===e.axis&&0===s.left)return;const c=Math.abs(s.top)>=Math.abs(s.left)?s.top<0?"up":"down":s.left<0?"left":"right";n.position={top:r,left:a},n.directionChanged=n.direction!==c,n.delta=s,!0===n.directionChanged&&(n.direction=c,n.inflectionPoint=n.position),t("scroll",{...n})}function u(){o=(0,l.b0)(a,e.scrollTarget),o.addEventListener("scroll",f,c),f(!0)}function p(){void 0!==o&&(o.removeEventListener("scroll",f,c),o=void 0)}function f(t){if(!0===t||0===e.debounce||"0"===e.debounce)d();else if(null===i){const[t,n]=e.debounce?[setTimeout(d,e.debounce),clearTimeout]:[requestAnimationFrame(d),cancelAnimationFrame];i=()=>{n(t),i=null}}}(0,r.YP)((()=>e.scrollTarget),(()=>{p(),u()}));const h=(0,r.FN)();return(0,r.bv)((()=>{a=h.proxy.$el.parentNode,u()})),(0,r.Jd)((()=>{null!==i&&i(),p()})),Object.assign(h.proxy,{trigger:f,getPosition:()=>n}),s.ZT}});var p=n(883),f=n(2026),h=n(5439);const m=(0,i.L)({name:"QLayout",props:{container:Boolean,view:{type:String,default:"hhh lpr fff",validator:e=>/^(h|l)h(h|r) lpr (f|l)f(f|r)$/.test(e.toLowerCase())},onScroll:Function,onScrollHeight:Function,onResize:Function},setup(e,{slots:t,emit:n}){const{proxy:{$q:i}}=(0,r.FN)(),s=(0,o.iH)(null),c=(0,o.iH)(i.screen.height),d=(0,o.iH)(!0===e.container?0:i.screen.width),m=(0,o.iH)({position:0,direction:"down",inflectionPoint:0}),v=(0,o.iH)(0),g=(0,o.iH)(!0===a.uX.value?0:(0,l.np)()),b=(0,r.Fl)((()=>"q-layout q-layout--"+(!0===e.container?"containerized":"standard"))),w=(0,r.Fl)((()=>!1===e.container?{minHeight:i.screen.height+"px"}:null)),y=(0,r.Fl)((()=>0!==g.value?{[!0===i.lang.rtl?"left":"right"]:`${g.value}px`}:null)),k=(0,r.Fl)((()=>0!==g.value?{[!0===i.lang.rtl?"right":"left"]:0,[!0===i.lang.rtl?"left":"right"]:`-${g.value}px`,width:`calc(100% + ${g.value}px)`}:null));function _(t){if(!0===e.container||!0!==document.qScrollPrevented){const r={position:t.position.top,direction:t.direction,directionChanged:t.directionChanged,inflectionPoint:t.inflectionPoint.top,delta:t.delta.top};m.value=r,void 0!==e.onScroll&&n("scroll",r)}}function x(t){const{height:r,width:o}=t;let a=!1;c.value!==r&&(a=!0,c.value=r,void 0!==e.onScrollHeight&&n("scroll-height",r),C()),d.value!==o&&(a=!0,d.value=o),!0===a&&void 0!==e.onResize&&n("resize",t)}function S({height:e}){v.value!==e&&(v.value=e,C())}function C(){if(!0===e.container){const e=c.value>v.value?(0,l.np)():0;g.value!==e&&(g.value=e)}}let L;const E={instances:{},view:(0,r.Fl)((()=>e.view)),isContainer:(0,r.Fl)((()=>e.container)),rootRef:s,height:c,containerHeight:v,scrollbarWidth:g,totalWidth:(0,r.Fl)((()=>d.value+g.value)),rows:(0,r.Fl)((()=>{const t=e.view.toLowerCase().split(" ");return{top:t[0].split(""),middle:t[1].split(""),bottom:t[2].split("")}})),header:(0,o.qj)({size:0,offset:0,space:!1}),right:(0,o.qj)({size:300,offset:0,space:!1}),footer:(0,o.qj)({size:0,offset:0,space:!1}),left:(0,o.qj)({size:300,offset:0,space:!1}),scroll:m,animate(){void 0!==L?clearTimeout(L):document.body.classList.add("q-body--layout-animate"),L=setTimeout((()=>{document.body.classList.remove("q-body--layout-animate"),L=void 0}),155)},update(e,t,n){E[e][t]=n}};if((0,r.JJ)(h.YE,E),(0,l.np)()>0){let A=null;const q=document.body;function P(){A=null,q.classList.remove("hide-scrollbar")}function T(){if(null===A){if(q.scrollHeight>i.screen.height)return;q.classList.add("hide-scrollbar")}else clearTimeout(A);A=setTimeout(P,300)}function j(e){null!==A&&"remove"===e&&(clearTimeout(A),P()),window[`${e}EventListener`]("resize",T)}(0,r.YP)((()=>!0!==e.container?"add":"remove"),j),!0!==e.container&&j("add"),(0,r.Ah)((()=>{j("remove")}))}return()=>{const n=(0,f.vs)(t.default,[(0,r.h)(u,{onScroll:_}),(0,r.h)(p.Z,{onResize:x})]),o=(0,r.h)("div",{class:b.value,style:w.value,ref:!0===e.container?void 0:s},n);return!0===e.container?(0,r.h)("div",{class:"q-layout-container overflow-hidden",ref:s},[(0,r.h)(p.Z,{onResize:S}),(0,r.h)("div",{class:"absolute-full",style:y.value},[(0,r.h)("div",{class:"scroll",style:k.value},[o])])]):o}}})},6362:(e,t,n)=>{"use strict";n.d(t,{Z:()=>C});n(702);var r=n(9835),o=n(499),a=n(1957),i=n(4397),l=n(4088),s=n(3842),c=n(8234),d=n(1518),u=n(431),p=n(6916),f=n(2695),h=n(5987),m=n(2909),v=n(3701),g=n(1384),b=n(2026),w=n(6532),y=n(4173),k=n(223),_=n(9092),x=n(7026),S=n(9388);const C=(0,h.L)({name:"QMenu",inheritAttrs:!1,props:{...i.u,...s.vr,...c.S,...u.D,persistent:Boolean,autoClose:Boolean,separateClosePopup:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,fit:Boolean,cover:Boolean,square:Boolean,anchor:{type:String,validator:S.$},self:{type:String,validator:S.$},offset:{type:Array,validator:S.io},scrollTarget:{default:void 0},touchPosition:Boolean,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null}},emits:[...s.gH,"click","escape-key"],setup(e,{slots:t,emit:n,attrs:h}){let C,L,E,A=null;const q=(0,r.FN)(),{proxy:P}=q,{$q:T}=P,j=(0,o.iH)(null),O=(0,o.iH)(!1),M=(0,r.Fl)((()=>!0!==e.persistent&&!0!==e.noRouteDismiss)),R=(0,c.Z)(e,T),{registerTick:F,removeTick:H}=(0,p.Z)(),{registerTimeout:z,removeTimeout:B}=(0,f.Z)(),{transition:I,transitionStyle:V}=(0,u.Z)(e,O),{localScrollTarget:$,changeScrollEvent:N,unconfigureScrollTarget:U}=(0,l.Z)(e,le),{anchorEl:D,canShow:Z}=(0,i.Z)({showing:O}),{hide:W}=(0,s.ZP)({showing:O,canShow:Z,handleShow:oe,handleHide:ae,hideOnRouteChange:M,processOnMount:!0}),{showPortal:K,hidePortal:G,renderPortal:J}=(0,d.Z)(q,j,pe),Y={anchorEl:D,innerRef:j,onClickOutside(t){if(!0!==e.persistent&&!0===O.value)return W(t),("touchstart"===t.type||t.target.classList.contains("q-dialog__backdrop"))&&(0,g.NS)(t),!0}},Q=(0,r.Fl)((()=>(0,S.li)(e.anchor||(!0===e.cover?"center middle":"bottom start"),T.lang.rtl))),X=(0,r.Fl)((()=>!0===e.cover?Q.value:(0,S.li)(e.self||"top start",T.lang.rtl))),ee=(0,r.Fl)((()=>(!0===e.square?" q-menu--square":"")+(!0===R.value?" q-menu--dark q-dark":""))),te=(0,r.Fl)((()=>!0===e.autoClose?{onClick:se}:{})),ne=(0,r.Fl)((()=>!0===O.value&&!0!==e.persistent));function re(){(0,x.jd)((()=>{let e=j.value;e&&!0!==e.contains(document.activeElement)&&(e=e.querySelector("[autofocus], [data-autofocus]")||e,e.focus({preventScroll:!0}))}))}function oe(t){if(H(),B(),A=!1===e.noRefocus?document.activeElement:null,(0,y.i)(ce),K(),le(),C=void 0,void 0!==t&&(e.touchPosition||e.contextMenu)){const e=(0,g.FK)(t);if(void 0!==e.left){const{top:t,left:n}=D.value.getBoundingClientRect();C={left:e.left-n,top:e.top-t}}}void 0===L&&(L=(0,r.YP)((()=>T.screen.width+"|"+T.screen.height+"|"+e.self+"|"+e.anchor+"|"+T.lang.rtl),ue)),!0!==e.noFocus&&document.activeElement.blur(),F((()=>{ue(),!0!==e.noFocus&&re()})),z((()=>{!0===T.platform.is.ios&&(E=e.autoClose,j.value.click()),ue(),K(!0),n("show",t)}),e.transitionDuration)}function ae(t){H(),B(),G(),ie(!0),null===A||void 0!==t&&!0===t.qClickOutside||(A.focus(),A=null),z((()=>{G(!0),n("hide",t)}),e.transitionDuration)}function ie(e){C=void 0,void 0!==L&&(L(),L=void 0),!0!==e&&!0!==O.value||((0,y.H)(ce),U(),(0,_.D)(Y),(0,w.k)(de)),!0!==e&&(A=null)}function le(){null===D.value&&void 0===e.scrollTarget||($.value=(0,v.b0)(D.value,e.scrollTarget),N($.value,ue))}function se(e){!0!==E?((0,m.AH)(P,e),n("click",e)):E=!1}function ce(t){!0===ne.value&&!0!==e.noFocus&&!0!==(0,k.mY)(j.value,t.target)&&re()}function de(e){n("escape-key"),W(e)}function ue(){const t=j.value;null!==t&&null!==D.value&&(0,S.wq)({el:t,offset:e.offset,anchorEl:D.value,anchorOrigin:Q.value,selfOrigin:X.value,absoluteOffset:C,fit:e.fit,cover:e.cover,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}function pe(){return(0,r.h)(a.uT,{name:I.value,appear:!0},(()=>!0===O.value?(0,r.h)("div",{...h,ref:j,tabindex:-1,class:["q-menu q-position-engine scroll"+ee.value,h.class],style:[h.style,V.value],...te.value},(0,b.KR)(t.default)):null))}return(0,r.YP)(ne,(e=>{!0===e?((0,w.c)(de),(0,_.m)(Y)):((0,w.k)(de),(0,_.D)(Y))})),(0,r.Jd)(ie),Object.assign(P,{focus:re,updatePosition:ue}),J}})},8830:(e,t,n)=>{"use strict";n.d(t,{Z:()=>y});n(6727);var r=n(9835),o=n(499),a=n(2857),i=n(8234),l=n(244),s=n(5917),c=n(9256),d=n(5987),u=n(9480),p=n(1384),f=n(2026);const h=(0,r.h)("svg",{key:"svg",class:"q-radio__bg absolute non-selectable",viewBox:"0 0 24 24","aria-hidden":"true"},[(0,r.h)("path",{d:"M12,22a10,10 0 0 1 -10,-10a10,10 0 0 1 10,-10a10,10 0 0 1 10,10a10,10 0 0 1 -10,10m0,-22a12,12 0 0 0 -12,12a12,12 0 0 0 12,12a12,12 0 0 0 12,-12a12,12 0 0 0 -12,-12"}),(0,r.h)("path",{class:"q-radio__check",d:"M12,6a6,6 0 0 0 -6,6a6,6 0 0 0 6,6a6,6 0 0 0 6,-6a6,6 0 0 0 -6,-6"})]),m=(0,d.L)({name:"QRadio",props:{...i.S,...l.LU,...c.Fz,modelValue:{required:!0},val:{required:!0},label:String,leftLabel:Boolean,checkedIcon:String,uncheckedIcon:String,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},emits:["update:modelValue"],setup(e,{slots:t,emit:n}){const{proxy:d}=(0,r.FN)(),m=(0,i.Z)(e,d.$q),v=(0,l.ZP)(e,u.Z),g=(0,o.iH)(null),{refocusTargetEl:b,refocusTarget:w}=(0,s.Z)(e,g),y=(0,r.Fl)((()=>e.modelValue===e.val)),k=(0,r.Fl)((()=>"q-radio cursor-pointer no-outline row inline no-wrap items-center"+(!0===e.disable?" disabled":"")+(!0===m.value?" q-radio--dark":"")+(!0===e.dense?" q-radio--dense":"")+(!0===e.leftLabel?" reverse":""))),_=(0,r.Fl)((()=>{const t=void 0===e.color||!0!==e.keepColor&&!0!==y.value?"":` text-${e.color}`;return`q-radio__inner relative-position q-radio__inner--${!0===y.value?"truthy":"falsy"}${t}`})),x=(0,r.Fl)((()=>(!0===y.value?e.checkedIcon:e.uncheckedIcon)||null)),S=(0,r.Fl)((()=>!0===e.disable?-1:e.tabindex||0)),C=(0,r.Fl)((()=>{const t={type:"radio"};return void 0!==e.name&&Object.assign(t,{"^checked":!0===y.value?"checked":void 0,name:e.name,value:e.val}),t})),L=(0,c.eX)(C);function E(t){void 0!==t&&((0,p.NS)(t),w(t)),!0!==e.disable&&!0!==y.value&&n("update:modelValue",e.val,t)}function A(e){13!==e.keyCode&&32!==e.keyCode||(0,p.NS)(e)}function q(e){13!==e.keyCode&&32!==e.keyCode||E(e)}return Object.assign(d,{set:E}),()=>{const n=null!==x.value?[(0,r.h)("div",{key:"icon",class:"q-radio__icon-container absolute-full flex flex-center no-wrap"},[(0,r.h)(a.Z,{class:"q-radio__icon",name:x.value})])]:[h];!0!==e.disable&&L(n,"unshift"," q-radio__native q-ma-none q-pa-none");const o=[(0,r.h)("div",{class:_.value,style:v.value},n)];null!==b.value&&o.push(b.value);const i=void 0!==e.label?(0,f.vs)(t.default,[e.label]):(0,f.KR)(t.default);return void 0!==i&&o.push((0,r.h)("div",{class:"q-radio__label q-anchor--skip"},i)),(0,r.h)("div",{ref:g,class:k.value,tabindex:S.value,role:"radio","aria-label":e.label,"aria-checked":!0===y.value?"true":"false","aria-disabled":!0===e.disable?"true":void 0,onClick:E,onKeydown:A,onKeyup:q},o)}}});var v=n(1221),g=n(3175);const b={radio:m,checkbox:v.Z,toggle:g.Z},w=Object.keys(b),y=(0,d.L)({name:"QOptionGroup",props:{...i.S,modelValue:{required:!0},options:{type:Array,validator:e=>e.every((e=>"value"in e&&"label"in e))},name:String,type:{default:"radio",validator:e=>w.includes(e)},color:String,keepColor:Boolean,dense:Boolean,size:String,leftLabel:Boolean,inline:Boolean,disable:Boolean},emits:["update:modelValue"],setup(e,{emit:t,slots:n}){const{proxy:{$q:o}}=(0,r.FN)(),a=Array.isArray(e.modelValue);"radio"===e.type?!0===a&&console.error("q-option-group: model should not be array"):!1===a&&console.error("q-option-group: model should be array in your case");const l=(0,i.Z)(e,o),s=(0,r.Fl)((()=>b[e.type])),c=(0,r.Fl)((()=>"q-option-group q-gutter-x-sm"+(!0===e.inline?" q-option-group--inline":""))),d=(0,r.Fl)((()=>{const t={};return"radio"===e.type&&(t.role="radiogroup",!0===e.disable&&(t["aria-disabled"]="true")),t}));function u(e){t("update:modelValue",e)}return()=>(0,r.h)("div",{class:c.value,...d.value},e.options.map(((t,o)=>{const a=void 0!==n["label-"+o]?()=>n["label-"+o](t):void 0!==n.label?()=>n.label(t):void 0;return(0,r.h)("div",[(0,r.h)(s.value,{modelValue:e.modelValue,val:t.value,name:void 0===t.name?e.name:t.name,disable:e.disable||t.disable,label:void 0===a?t.label:null,leftLabel:void 0===t.leftLabel?e.leftLabel:t.leftLabel,color:void 0===t.color?e.color:t.color,checkedIcon:t.checkedIcon,uncheckedIcon:t.uncheckedIcon,dark:t.dark||l.value,size:void 0===t.size?e.size:t.size,dense:e.dense,keepColor:void 0===t.keepColor?e.keepColor:t.keepColor,"onUpdate:modelValue":u},a)])})))}})},9885:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(9835),o=n(5987),a=n(2026),i=n(5439);const l=(0,o.L)({name:"QPage",props:{padding:Boolean,styleFn:Function},setup(e,{slots:t}){const{proxy:{$q:n}}=(0,r.FN)(),o=(0,r.f3)(i.YE);(0,r.f3)(i.Mw,(()=>{console.error("QPage needs to be child of QPageContainer")}));const l=(0,r.Fl)((()=>{const t=(!0===o.header.space?o.header.size:0)+(!0===o.footer.space?o.footer.size:0);if("function"===typeof e.styleFn){const r=!0===o.isContainer.value?o.containerHeight.value:n.screen.height;return e.styleFn(t,r)}return{minHeight:!0===o.isContainer.value?o.containerHeight.value-t+"px":0===n.screen.height?0!==t?`calc(100vh - ${t}px)`:"100vh":n.screen.height-t+"px"}})),s=(0,r.Fl)((()=>"q-page "+(!0===e.padding?" q-layout-padding":"")));return()=>(0,r.h)("main",{class:s.value,style:l.value},(0,a.KR)(t.default))}})},2133:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(9835),o=n(5987),a=n(2026),i=n(5439);const l=(0,o.L)({name:"QPageContainer",setup(e,{slots:t}){const{proxy:{$q:n}}=(0,r.FN)(),o=(0,r.f3)(i.YE,(()=>{console.error("QPageContainer needs to be child of QLayout")}));(0,r.JJ)(i.Mw,!0);const l=(0,r.Fl)((()=>{const e={};return!0===o.header.space&&(e.paddingTop=`${o.header.size}px`),!0===o.right.space&&(e["padding"+(!0===n.lang.rtl?"Left":"Right")]=`${o.right.size}px`),!0===o.footer.space&&(e.paddingBottom=`${o.footer.size}px`),!0===o.left.space&&(e["padding"+(!0===n.lang.rtl?"Right":"Left")]=`${o.left.size}px`),e}));return()=>(0,r.h)("div",{class:"q-page-container",style:l.value},(0,a.KR)(t.default))}})},2765:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var r=n(9835),o=n(499),a=n(3706),i=n(6362),l=n(4397),s=n(5987);const c=(0,s.L)({name:"QPopupProxy",props:{...l.u,breakpoint:{type:[String,Number],default:450}},emits:["show","hide"],setup(e,{slots:t,emit:n,attrs:s}){const{proxy:c}=(0,r.FN)(),{$q:d}=c,u=(0,o.iH)(!1),p=(0,o.iH)(null),f=(0,r.Fl)((()=>parseInt(e.breakpoint,10))),{canShow:h}=(0,l.Z)({showing:u});function m(){return d.screen.width<f.value||d.screen.height<f.value?"dialog":"menu"}const v=(0,o.iH)(m()),g=(0,r.Fl)((()=>"menu"===v.value?{maxHeight:"99vh"}:{}));function b(e){u.value=!0,n("show",e)}function w(e){u.value=!1,v.value=m(),n("hide",e)}return(0,r.YP)((()=>m()),(e=>{!0!==u.value&&(v.value=e)})),Object.assign(c,{show(e){!0===h(e)&&p.value.show(e)},hide(e){p.value.hide(e)},toggle(e){p.value.toggle(e)}}),()=>{const n={ref:p,...g.value,...s,onShow:b,onHide:w};let o;return"dialog"===v.value?o=a.Z:(o=i.Z,Object.assign(n,{target:e.target,contextMenu:e.contextMenu,noParentEvent:!0,separateClosePopup:!0})),(0,r.h)(o,n,t.default)}}})},883:(e,t,n)=>{"use strict";n.d(t,{Z:()=>u});var r=n(9835),o=n(499),a=n(7506);function i(){const e=(0,o.iH)(!a.uX.value);return!1===e.value&&(0,r.bv)((()=>{e.value=!0})),e}var l=n(5987),s=n(1384);const c="undefined"!==typeof ResizeObserver,d=!0===c?{}:{style:"display:block;position:absolute;top:0;left:0;right:0;bottom:0;height:100%;width:100%;overflow:hidden;pointer-events:none;z-index:-1;",url:"about:blank"},u=(0,l.L)({name:"QResizeObserver",props:{debounce:{type:[String,Number],default:100}},emits:["resize"],setup(e,{emit:t}){let n,o=null,a={width:-1,height:-1};function l(t){!0===t||0===e.debounce||"0"===e.debounce?u():null===o&&(o=setTimeout(u,e.debounce))}function u(){if(clearTimeout(o),o=null,n){const{offsetWidth:e,offsetHeight:r}=n;e===a.width&&r===a.height||(a={width:e,height:r},t("resize",a))}}const p=(0,r.FN)();if(Object.assign(p.proxy,{trigger:l}),!0===c){let f;return(0,r.bv)((()=>{(0,r.Y3)((()=>{n=p.proxy.$el.parentNode,n&&(f=new ResizeObserver(l),f.observe(n),u())}))})),(0,r.Jd)((()=>{clearTimeout(o),void 0!==f&&(void 0!==f.disconnect?f.disconnect():n&&f.unobserve(n))})),s.ZT}{const h=i();let m;function v(){clearTimeout(o),void 0!==m&&(void 0!==m.removeEventListener&&m.removeEventListener("resize",l,s.rU.passive),m=void 0)}function g(){v(),n&&n.contentDocument&&(m=n.contentDocument.defaultView,m.addEventListener("resize",l,s.rU.passive),u())}return(0,r.bv)((()=>{(0,r.Y3)((()=>{n=p.proxy.$el,n&&g()}))})),(0,r.Jd)(v),()=>{if(!0===h.value)return(0,r.h)("object",{style:d.style,tabindex:-1,type:"text/html",data:d.url,"aria-hidden":"true",onLoad:g})}}}})},5837:(e,t,n)=>{"use strict";n.d(t,{Z:()=>Le});n(6727),n(702);var r=n(9835),o=n(499),a=n(1957),i=n(7506),l=n(2857),s=n(3940),c=n(8234),d=n(5439);function u({validate:e,resetValidation:t,requiresQForm:n}){const o=(0,r.f3)(d.vh,!1);if(!1!==o){const{props:n,proxy:a}=(0,r.FN)();Object.assign(a,{validate:e,resetValidation:t}),(0,r.YP)((()=>n.disable),(e=>{!0===e?("function"===typeof t&&t(),o.unbindComponent(a)):o.bindComponent(a)})),!0!==n.disable&&o.bindComponent(a),(0,r.Jd)((()=>{!0!==n.disable&&o.unbindComponent(a)}))}else!0===n&&console.error("Parent QForm not found on useFormChild()!")}const p=/^#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?$/,f=/^#[0-9a-fA-F]{4}([0-9a-fA-F]{4})?$/,h=/^#([0-9a-fA-F]{3}|[0-9a-fA-F]{4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/,m=/^rgb\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5])\)$/,v=/^rgba\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/,g={date:e=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(e),time:e=>/^([0-1]?\d|2[0-3]):[0-5]\d$/.test(e),fulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(e),timeOrFulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/.test(e),email:e=>/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(e),hexColor:e=>p.test(e),hexaColor:e=>f.test(e),hexOrHexaColor:e=>h.test(e),rgbColor:e=>m.test(e),rgbaColor:e=>v.test(e),rgbOrRgbaColor:e=>m.test(e)||v.test(e),hexOrRgbColor:e=>p.test(e)||m.test(e),hexaOrRgbaColor:e=>f.test(e)||v.test(e),anyColor:e=>h.test(e)||m.test(e)||v.test(e)};n(3122);n(6822),n(8964);n(5019);n(4641),n(3269),n(9379);var b=n(244);const w={...b.LU,min:{type:Number,default:0},max:{type:Number,default:100},color:String,centerColor:String,trackColor:String,fontSize:String,thickness:{type:Number,default:.2,validator:e=>e>=0&&e<=1},angle:{type:Number,default:0},showValue:Boolean,reverse:Boolean,instantFeedback:Boolean};var y=n(5987),k=n(2026);function _(e,t,n){return n<=t?t:Math.min(n,Math.max(t,e))}function x(e,t,n){if(n<=t)return t;const r=n-t+1;let o=t+(e-t)%r;return o<t&&(o=r+o),0===o?0:o}const S=50,C=2*S,L=C*Math.PI,E=Math.round(1e3*L)/1e3;(0,y.L)({name:"QCircularProgress",props:{...w,value:{type:Number,default:0},animationSpeed:{type:[String,Number],default:600},indeterminate:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=(0,r.FN)(),o=(0,b.ZP)(e),a=(0,r.Fl)((()=>{const t=(!0===n.lang.rtl?-1:1)*e.angle;return{transform:e.reverse!==(!0===n.lang.rtl)?`scale3d(-1, 1, 1) rotate3d(0, 0, 1, ${-90-t}deg)`:`rotate3d(0, 0, 1, ${t-90}deg)`}})),i=(0,r.Fl)((()=>!0!==e.instantFeedback&&!0!==e.indeterminate?{transition:`stroke-dashoffset ${e.animationSpeed}ms ease 0s, stroke ${e.animationSpeed}ms ease`}:"")),l=(0,r.Fl)((()=>C/(1-e.thickness/2))),s=(0,r.Fl)((()=>`${l.value/2} ${l.value/2} ${l.value} ${l.value}`)),c=(0,r.Fl)((()=>_(e.value,e.min,e.max))),d=(0,r.Fl)((()=>L*(1-(c.value-e.min)/(e.max-e.min)))),u=(0,r.Fl)((()=>e.thickness/2*l.value));function p({thickness:e,offset:t,color:n,cls:o}){return(0,r.h)("circle",{class:"q-circular-progress__"+o+(void 0!==n?` text-${n}`:""),style:i.value,fill:"transparent",stroke:"currentColor","stroke-width":e,"stroke-dasharray":E,"stroke-dashoffset":t,cx:l.value,cy:l.value,r:S})}return()=>{const n=[];void 0!==e.centerColor&&"transparent"!==e.centerColor&&n.push((0,r.h)("circle",{class:`q-circular-progress__center text-${e.centerColor}`,fill:"currentColor",r:S-u.value/2,cx:l.value,cy:l.value})),void 0!==e.trackColor&&"transparent"!==e.trackColor&&n.push(p({cls:"track",thickness:u.value,offset:0,color:e.trackColor})),n.push(p({cls:"circle",thickness:u.value,offset:d.value,color:e.color}));const i=[(0,r.h)("svg",{class:"q-circular-progress__svg",style:a.value,viewBox:s.value,"aria-hidden":"true"},n)];return!0===e.showValue&&i.push((0,r.h)("div",{class:"q-circular-progress__text absolute-full row flex-center content-center",style:{fontSize:e.fontSize}},void 0!==t.default?t.default():[(0,r.h)("div",c.value)])),(0,r.h)("div",{class:`q-circular-progress q-circular-progress--${!0===e.indeterminate?"in":""}determinate`,style:o.value,role:"progressbar","aria-valuemin":e.min,"aria-valuemax":e.max,"aria-valuenow":!0===e.indeterminate?void 0:c.value},(0,k.pf)(t.internal,i))}}});var A=n(1384);const q={multiple:Boolean,accept:String,capture:String,maxFileSize:[Number,String],maxTotalSize:[Number,String],maxFiles:[Number,String],filter:Function},P=["rejected"];c.S,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean,Boolean;const T=[...P,"start","finish","added","removed"];const j=()=>!0;function O(e){const t={};return e.forEach((e=>{t[e]=j})),t}var M=n(6254);O(T);n(3558);var R=n(899);n(223);n(3701),n(7674);var F=n(796),H=n(3251);const z=[!0,!1,"ondemand"],B={modelValue:{},error:{type:Boolean,default:null},errorMessage:String,noErrorIcon:Boolean,rules:Array,reactiveRules:Boolean,lazyRules:{type:[Boolean,String],validator:e=>z.includes(e)}};function I(e,t){const{props:n,proxy:a}=(0,r.FN)(),i=(0,o.iH)(!1),l=(0,o.iH)(null),s=(0,o.iH)(null);u({validate:b,resetValidation:v});let c,d=0;const p=(0,r.Fl)((()=>void 0!==n.rules&&null!==n.rules&&n.rules.length>0)),f=(0,r.Fl)((()=>!0!==n.disable&&!0===p.value)),h=(0,r.Fl)((()=>!0===n.error||!0===i.value)),m=(0,r.Fl)((()=>"string"===typeof n.errorMessage&&n.errorMessage.length>0?n.errorMessage:l.value));function v(){d++,t.value=!1,s.value=null,i.value=!1,l.value=null,y.cancel()}function b(e=n.modelValue){if(!0!==f.value)return!0;const r=++d;!0!==t.value&&!0!==n.lazyRules&&(s.value=!0);const o=(e,n)=>{i.value!==e&&(i.value=e);const r=n||void 0;l.value!==r&&(l.value=r),t.value=!1},a=[];for(let t=0;t<n.rules.length;t++){const r=n.rules[t];let i;if("function"===typeof r?i=r(e):"string"===typeof r&&void 0!==g[r]&&(i=g[r](e)),!1===i||"string"===typeof i)return o(!0,i),!1;!0!==i&&void 0!==i&&a.push(i)}return 0===a.length?(o(!1),!0):(t.value=!0,Promise.all(a).then((e=>{if(void 0===e||!1===Array.isArray(e)||0===e.length)return r===d&&o(!1),!0;const t=e.find((e=>!1===e||"string"===typeof e));return r===d&&o(void 0!==t,t),void 0===t}),(e=>(r===d&&(console.error(e),o(!0)),!1))))}function w(e){!0===f.value&&"ondemand"!==n.lazyRules&&(!0===s.value||!0!==n.lazyRules&&!0!==e)&&y()}(0,r.YP)((()=>n.modelValue),(()=>{w()})),(0,r.YP)((()=>n.reactiveRules),(e=>{!0===e?void 0===c&&(c=(0,r.YP)((()=>n.rules),(()=>{w(!0)}))):void 0!==c&&(c(),c=void 0)}),{immediate:!0}),(0,r.YP)(e,(e=>{!0===e?null===s.value&&(s.value=!1):!1===s.value&&(s.value=!0,!0===f.value&&"ondemand"!==n.lazyRules&&!1===t.value&&y())}));const y=(0,R.Z)(b,0);return(0,r.Jd)((()=>{void 0!==c&&c(),y.cancel()})),Object.assign(a,{resetValidation:v,validate:b}),(0,H.g)(a,"hasError",(()=>h.value)),{isDirtyModel:s,hasRules:p,hasError:h,errorMessage:m,validate:b,resetValidation:v}}const V=/^on[A-Z]/;function $(e,t){const n={listeners:(0,o.iH)({}),attributes:(0,o.iH)({})};function a(){const r={},o={};for(const t in e)"class"!==t&&"style"!==t&&!1===V.test(t)&&(r[t]=e[t]);for(const e in t.props)!0===V.test(e)&&(o[e]=t.props[e]);n.attributes.value=r,n.listeners.value=o}return(0,r.Xn)(a),a(),n}var N=n(7026);function U(e){return void 0===e?`f_${(0,F.Z)()}`:e}function D(e){return void 0!==e&&null!==e&&(""+e).length>0}const Z={...c.S,...B,label:String,stackLabel:Boolean,hint:String,hideHint:Boolean,prefix:String,suffix:String,labelColor:String,color:String,bgColor:String,filled:Boolean,outlined:Boolean,borderless:Boolean,standout:[Boolean,String],square:Boolean,loading:Boolean,labelSlot:Boolean,bottomSlots:Boolean,hideBottomSpace:Boolean,rounded:Boolean,dense:Boolean,itemAligned:Boolean,counter:Boolean,clearable:Boolean,clearIcon:String,disable:Boolean,readonly:Boolean,autofocus:Boolean,for:String,maxlength:[Number,String]},W=["update:modelValue","clear","focus","blur","popup-show","popup-hide"];function K(){const{props:e,attrs:t,proxy:n,vnode:a}=(0,r.FN)(),i=(0,c.Z)(e,n.$q);return{isDark:i,editable:(0,r.Fl)((()=>!0!==e.disable&&!0!==e.readonly)),innerLoading:(0,o.iH)(!1),focused:(0,o.iH)(!1),hasPopupOpen:!1,splitAttrs:$(t,a),targetUid:(0,o.iH)(U(e.for)),rootRef:(0,o.iH)(null),targetRef:(0,o.iH)(null),controlRef:(0,o.iH)(null)}}function G(e){const{props:t,emit:n,slots:o,attrs:c,proxy:d}=(0,r.FN)(),{$q:u}=d;let p;void 0===e.hasValue&&(e.hasValue=(0,r.Fl)((()=>D(t.modelValue)))),void 0===e.emitValue&&(e.emitValue=e=>{n("update:modelValue",e)}),void 0===e.controlEvents&&(e.controlEvents={onFocusin:j,onFocusout:O}),Object.assign(e,{clearValue:M,onControlFocusin:j,onControlFocusout:O,focus:P}),void 0===e.computedCounter&&(e.computedCounter=(0,r.Fl)((()=>{if(!1!==t.counter){const e="string"===typeof t.modelValue||"number"===typeof t.modelValue?(""+t.modelValue).length:!0===Array.isArray(t.modelValue)?t.modelValue.length:0,n=void 0!==t.maxlength?t.maxlength:t.maxValues;return e+(void 0!==n?" / "+n:"")}})));const{isDirtyModel:f,hasRules:h,hasError:m,errorMessage:v,resetValidation:g}=I(e.focused,e.innerLoading),b=void 0!==e.floatingLabel?(0,r.Fl)((()=>!0===t.stackLabel||!0===e.focused.value||!0===e.floatingLabel.value)):(0,r.Fl)((()=>!0===t.stackLabel||!0===e.focused.value||!0===e.hasValue.value)),w=(0,r.Fl)((()=>!0===t.bottomSlots||void 0!==t.hint||!0===h.value||!0===t.counter||null!==t.error)),y=(0,r.Fl)((()=>!0===t.filled?"filled":!0===t.outlined?"outlined":!0===t.borderless?"borderless":t.standout?"standout":"standard")),_=(0,r.Fl)((()=>`q-field row no-wrap items-start q-field--${y.value}`+(void 0!==e.fieldClass?` ${e.fieldClass.value}`:"")+(!0===t.rounded?" q-field--rounded":"")+(!0===t.square?" q-field--square":"")+(!0===b.value?" q-field--float":"")+(!0===S.value?" q-field--labeled":"")+(!0===t.dense?" q-field--dense":"")+(!0===t.itemAligned?" q-field--item-aligned q-item-type":"")+(!0===e.isDark.value?" q-field--dark":"")+(void 0===e.getControl?" q-field--auto-height":"")+(!0===e.focused.value?" q-field--focused":"")+(!0===m.value?" q-field--error":"")+(!0===m.value||!0===e.focused.value?" q-field--highlighted":"")+(!0!==t.hideBottomSpace&&!0===w.value?" q-field--with-bottom":"")+(!0===t.disable?" q-field--disabled":!0===t.readonly?" q-field--readonly":""))),x=(0,r.Fl)((()=>"q-field__control relative-position row no-wrap"+(void 0!==t.bgColor?` bg-${t.bgColor}`:"")+(!0===m.value?" text-negative":"string"===typeof t.standout&&t.standout.length>0&&!0===e.focused.value?` ${t.standout}`:void 0!==t.color?` text-${t.color}`:""))),S=(0,r.Fl)((()=>!0===t.labelSlot||void 0!==t.label)),C=(0,r.Fl)((()=>"q-field__label no-pointer-events absolute ellipsis"+(void 0!==t.labelColor&&!0!==m.value?` text-${t.labelColor}`:""))),L=(0,r.Fl)((()=>({id:e.targetUid.value,editable:e.editable.value,focused:e.focused.value,floatingLabel:b.value,modelValue:t.modelValue,emitValue:e.emitValue}))),E=(0,r.Fl)((()=>{const n={for:e.targetUid.value};return!0===t.disable?n["aria-disabled"]="true":!0===t.readonly&&(n["aria-readonly"]="true"),n}));function q(){const t=document.activeElement;let n=void 0!==e.targetRef&&e.targetRef.value;!n||null!==t&&t.id===e.targetUid.value||(!0===n.hasAttribute("tabindex")||(n=n.querySelector("[tabindex]")),n&&n!==t&&n.focus({preventScroll:!0}))}function P(){(0,N.jd)(q)}function T(){(0,N.fP)(q);const t=document.activeElement;null!==t&&e.rootRef.value.contains(t)&&t.blur()}function j(t){clearTimeout(p),!0===e.editable.value&&!1===e.focused.value&&(e.focused.value=!0,n("focus",t))}function O(t,r){clearTimeout(p),p=setTimeout((()=>{(!0!==document.hasFocus()||!0!==e.hasPopupOpen&&void 0!==e.controlRef&&null!==e.controlRef.value&&!1===e.controlRef.value.contains(document.activeElement))&&(!0===e.focused.value&&(e.focused.value=!1,n("blur",t)),void 0!==r&&r())}))}function M(o){if((0,A.NS)(o),!0!==u.platform.is.mobile){const t=void 0!==e.targetRef&&e.targetRef.value||e.rootRef.value;t.focus()}else!0===e.rootRef.value.contains(document.activeElement)&&document.activeElement.blur();"file"===t.type&&(e.inputRef.value.value=null),n("update:modelValue",null),n("clear",t.modelValue),(0,r.Y3)((()=>{g(),!0!==u.platform.is.mobile&&(f.value=!1)}))}function R(){const n=[];return void 0!==o.prepend&&n.push((0,r.h)("div",{class:"q-field__prepend q-field__marginal row no-wrap items-center",key:"prepend",onClick:A.X$},o.prepend())),n.push((0,r.h)("div",{class:"q-field__control-container col relative-position row no-wrap q-anchor--skip"},F())),!0===m.value&&!1===t.noErrorIcon&&n.push(z("error",[(0,r.h)(l.Z,{name:u.iconSet.field.error,color:"negative"})])),!0===t.loading||!0===e.innerLoading.value?n.push(z("inner-loading-append",void 0!==o.loading?o.loading():[(0,r.h)(s.Z,{color:t.color})])):!0===t.clearable&&!0===e.hasValue.value&&!0===e.editable.value&&n.push(z("inner-clearable-append",[(0,r.h)(l.Z,{class:"q-field__focusable-action",tag:"button",name:t.clearIcon||u.iconSet.field.clear,tabindex:0,type:"button","aria-hidden":null,role:null,onClick:M})])),void 0!==o.append&&n.push((0,r.h)("div",{class:"q-field__append q-field__marginal row no-wrap items-center",key:"append",onClick:A.X$},o.append())),void 0!==e.getInnerAppend&&n.push(z("inner-append",e.getInnerAppend())),void 0!==e.getControlChild&&n.push(e.getControlChild()),n}function F(){const n=[];return void 0!==t.prefix&&null!==t.prefix&&n.push((0,r.h)("div",{class:"q-field__prefix no-pointer-events row items-center"},t.prefix)),void 0!==e.getShadowControl&&!0===e.hasShadow.value&&n.push(e.getShadowControl()),void 0!==e.getControl?n.push(e.getControl()):void 0!==o.rawControl?n.push(o.rawControl()):void 0!==o.control&&n.push((0,r.h)("div",{ref:e.targetRef,class:"q-field__native row",...e.splitAttrs.attributes.value,"data-autofocus":!0===t.autofocus||void 0},o.control(L.value))),!0===S.value&&n.push((0,r.h)("div",{class:C.value},(0,k.KR)(o.label,t.label))),void 0!==t.suffix&&null!==t.suffix&&n.push((0,r.h)("div",{class:"q-field__suffix no-pointer-events row items-center"},t.suffix)),n.concat((0,k.KR)(o.default))}function H(){let n,i;!0===m.value?null!==v.value?(n=[(0,r.h)("div",{role:"alert"},v.value)],i=`q--slot-error-${v.value}`):(n=(0,k.KR)(o.error),i="q--slot-error"):!0===t.hideHint&&!0!==e.focused.value||(void 0!==t.hint?(n=[(0,r.h)("div",t.hint)],i=`q--slot-hint-${t.hint}`):(n=(0,k.KR)(o.hint),i="q--slot-hint"));const l=!0===t.counter||void 0!==o.counter;if(!0===t.hideBottomSpace&&!1===l&&void 0===n)return;const s=(0,r.h)("div",{key:i,class:"q-field__messages col"},n);return(0,r.h)("div",{class:"q-field__bottom row items-start q-field__bottom--"+(!0!==t.hideBottomSpace?"animated":"stale")},[!0===t.hideBottomSpace?s:(0,r.h)(a.uT,{name:"q-transition--field-message"},(()=>s)),!0===l?(0,r.h)("div",{class:"q-field__counter"},void 0!==o.counter?o.counter():e.computedCounter.value):null])}function z(e,t){return null===t?null:(0,r.h)("div",{key:e,class:"q-field__append q-field__marginal row no-wrap items-center q-anchor--skip"},t)}(0,r.YP)((()=>t.for),(t=>{e.targetUid.value=U(t)})),Object.assign(d,{focus:P,blur:T});let B=!1;return(0,r.se)((()=>{B=!0})),(0,r.dl)((()=>{!0===B&&!0===t.autofocus&&d.focus()})),(0,r.bv)((()=>{!0===i.uX.value&&void 0===t.for&&(e.targetUid.value=U()),!0===t.autofocus&&d.focus()})),(0,r.Jd)((()=>{clearTimeout(p)})),function(){const n=void 0===e.getControl&&void 0===o.control?{...e.splitAttrs.attributes.value,"data-autofocus":t.autofocus,...E.value}:E.value;return(0,r.h)("label",{ref:e.rootRef,class:[_.value,c.class],style:c.style,...n},[void 0!==o.before?(0,r.h)("div",{class:"q-field__before q-field__marginal row no-wrap items-center",onClick:A.X$},o.before()):null,(0,r.h)("div",{class:"q-field__inner relative-position col self-stretch"},[(0,r.h)("div",{ref:e.controlRef,class:x.value,tabindex:-1,...e.controlEvents},R()),!0===w.value?H():null]),void 0!==o.after?(0,r.h)("div",{class:"q-field__after q-field__marginal row no-wrap items-center",onClick:A.X$},o.after()):null])}}const J=(0,y.L)({name:"QField",inheritAttrs:!1,props:Z,emits:W,setup(){return G(K())}});var Y=n(1136);const Q={xs:8,sm:10,md:14,lg:20,xl:24},X=(0,y.L)({name:"QChip",props:{...c.S,...b.LU,dense:Boolean,icon:String,iconRight:String,iconRemove:String,iconSelected:String,label:[String,Number],color:String,textColor:String,modelValue:{type:Boolean,default:!0},selected:{type:Boolean,default:null},square:Boolean,outline:Boolean,clickable:Boolean,removable:Boolean,tabindex:[String,Number],disable:Boolean,ripple:{type:[Boolean,Object],default:!0}},emits:["update:modelValue","update:selected","remove","click"],setup(e,{slots:t,emit:n}){const{proxy:{$q:o}}=(0,r.FN)(),a=(0,c.Z)(e,o),i=(0,b.ZP)(e,Q),s=(0,r.Fl)((()=>!0===e.selected||void 0!==e.icon)),d=(0,r.Fl)((()=>!0===e.selected?e.iconSelected||o.iconSet.chip.selected:e.icon)),u=(0,r.Fl)((()=>e.iconRemove||o.iconSet.chip.remove)),p=(0,r.Fl)((()=>!1===e.disable&&(!0===e.clickable||null!==e.selected))),f=(0,r.Fl)((()=>{const t=!0===e.outline&&e.color||e.textColor;return"q-chip row inline no-wrap items-center"+(!1===e.outline&&void 0!==e.color?` bg-${e.color}`:"")+(t?` text-${t} q-chip--colored`:"")+(!0===e.disable?" disabled":"")+(!0===e.dense?" q-chip--dense":"")+(!0===e.outline?" q-chip--outline":"")+(!0===e.selected?" q-chip--selected":"")+(!0===p.value?" q-chip--clickable cursor-pointer non-selectable q-hoverable":"")+(!0===e.square?" q-chip--square":"")+(!0===a.value?" q-chip--dark q-dark":"")})),h=(0,r.Fl)((()=>!0===e.disable?{tabindex:-1,"aria-disabled":"true"}:{tabindex:e.tabindex||0}));function m(e){13===e.keyCode&&v(e)}function v(t){e.disable||(n("update:selected",!e.selected),n("click",t))}function g(t){void 0!==t.keyCode&&13!==t.keyCode||((0,A.NS)(t),!1===e.disable&&(n("update:modelValue",!1),n("remove")))}function w(){const n=[];!0===p.value&&n.push((0,r.h)("div",{class:"q-focus-helper"})),!0===s.value&&n.push((0,r.h)(l.Z,{class:"q-chip__icon q-chip__icon--left",name:d.value}));const o=void 0!==e.label?[(0,r.h)("div",{class:"ellipsis"},[e.label])]:void 0;return n.push((0,r.h)("div",{class:"q-chip__content col row no-wrap items-center q-anchor--skip"},(0,k.pf)(t.default,o))),e.iconRight&&n.push((0,r.h)(l.Z,{class:"q-chip__icon q-chip__icon--right",name:e.iconRight})),!0===e.removable&&n.push((0,r.h)(l.Z,{class:"q-chip__icon q-chip__icon--remove cursor-pointer",name:u.value,...h.value,onClick:g,onKeyup:g})),n}return()=>{if(!1===e.modelValue)return;const t={class:f.value,style:i.value};return!0===p.value&&Object.assign(t,h.value,{onClick:v,onKeyup:m}),(0,k.Jl)("div",t,w(),"ripple",!1!==e.ripple&&!0!==e.disable,(()=>[[Y.Z,e.ripple]]))}}});var ee=n(490),te=n(1233),ne=n(3115),re=n(6362),oe=n(3706),ae=n(8383);const ie=1e3,le=["start","center","end","start-force","center-force","end-force"],se=Array.prototype.filter,ce=void 0===window.getComputedStyle(document.body).overflowAnchor?A.ZT:function(e,t){requestAnimationFrame((()=>{if(void 0===e)return;const n=e.children||[];se.call(n,(e=>e.dataset&&void 0!==e.dataset.qVsAnchor)).forEach((e=>{delete e.dataset.qVsAnchor}));const r=n[t];r&&r.dataset&&(r.dataset.qVsAnchor="")}))};function de(e,t){return e+t}function ue(e,t,n,r,o,a,i,l){const s=e===window?document.scrollingElement||document.documentElement:e,c=!0===o?"offsetWidth":"offsetHeight",d={scrollStart:0,scrollViewSize:-i-l,scrollMaxSize:0,offsetStart:-i,offsetEnd:-l};if(!0===o?(e===window?(d.scrollStart=window.pageXOffset||window.scrollX||document.body.scrollLeft||0,d.scrollViewSize+=document.documentElement.clientWidth):(d.scrollStart=s.scrollLeft,d.scrollViewSize+=s.clientWidth),d.scrollMaxSize=s.scrollWidth,!0===a&&(d.scrollStart=(!0===ae.e?d.scrollMaxSize-d.scrollViewSize:0)-d.scrollStart)):(e===window?(d.scrollStart=window.pageYOffset||window.scrollY||document.body.scrollTop||0,d.scrollViewSize+=document.documentElement.clientHeight):(d.scrollStart=s.scrollTop,d.scrollViewSize+=s.clientHeight),d.scrollMaxSize=s.scrollHeight),null!==n)for(let u=n.previousElementSibling;null!==u;u=u.previousElementSibling)!1===u.classList.contains("q-virtual-scroll--skip")&&(d.offsetStart+=u[c]);if(null!==r)for(let u=r.nextElementSibling;null!==u;u=u.nextElementSibling)!1===u.classList.contains("q-virtual-scroll--skip")&&(d.offsetEnd+=u[c]);if(t!==e){const n=s.getBoundingClientRect(),r=t.getBoundingClientRect();!0===o?(d.offsetStart+=r.left-n.left,d.offsetEnd-=r.width):(d.offsetStart+=r.top-n.top,d.offsetEnd-=r.height),e!==window&&(d.offsetStart+=d.scrollStart),d.offsetEnd+=d.scrollMaxSize-d.offsetStart}return d}function pe(e,t,n,r){"end"===t&&(t=(e===window?document.body:e)[!0===n?"scrollWidth":"scrollHeight"]),e===window?!0===n?(!0===r&&(t=(!0===ae.e?document.body.scrollWidth-document.documentElement.clientWidth:0)-t),window.scrollTo(t,window.pageYOffset||window.scrollY||document.body.scrollTop||0)):window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,t):!0===n?(!0===r&&(t=(!0===ae.e?e.scrollWidth-e.offsetWidth:0)-t),e.scrollLeft=t):e.scrollTop=t}function fe(e,t,n,r){if(n>=r)return 0;const o=t.length,a=Math.floor(n/ie),i=Math.floor((r-1)/ie)+1;let l=e.slice(a,i).reduce(de,0);return n%ie!==0&&(l-=t.slice(a*ie,n).reduce(de,0)),r%ie!==0&&r!==o&&(l-=t.slice(r,i*ie).reduce(de,0)),l}const he={virtualScrollSliceSize:{type:[Number,String],default:null},virtualScrollSliceRatioBefore:{type:[Number,String],default:1},virtualScrollSliceRatioAfter:{type:[Number,String],default:1},virtualScrollItemSize:{type:[Number,String],default:24},virtualScrollStickySizeStart:{type:[Number,String],default:0},virtualScrollStickySizeEnd:{type:[Number,String],default:0},tableColspan:[Number,String]},me=(Object.keys(he),{virtualScrollHorizontal:Boolean,onVirtualScroll:Function,...he});function ve({virtualScrollLength:e,getVirtualScrollTarget:t,getVirtualScrollEl:n,virtualScrollItemSizeComputed:a}){const i=(0,r.FN)(),{props:l,emit:s,proxy:c}=i,{$q:d}=c;let u,p,f,h,m=[];const v=(0,o.iH)(0),g=(0,o.iH)(0),b=(0,o.iH)({}),w=(0,o.iH)(null),y=(0,o.iH)(null),k=(0,o.iH)(null),_=(0,o.iH)({from:0,to:0}),x=(0,r.Fl)((()=>void 0!==l.tableColspan?l.tableColspan:100));void 0===a&&(a=(0,r.Fl)((()=>l.virtualScrollItemSize)));const S=(0,r.Fl)((()=>a.value+";"+l.virtualScrollHorizontal)),C=(0,r.Fl)((()=>S.value+";"+l.virtualScrollSliceRatioBefore+";"+l.virtualScrollSliceRatioAfter));function L(){O(p,!0)}function E(e){O(void 0===e?p:e)}function A(r,o){const a=t();if(void 0===a||null===a||8===a.nodeType)return;const i=ue(a,n(),w.value,y.value,l.virtualScrollHorizontal,d.lang.rtl,l.virtualScrollStickySizeStart,l.virtualScrollStickySizeEnd);f!==i.scrollViewSize&&M(i.scrollViewSize),P(a,i,Math.min(e.value-1,Math.max(0,parseInt(r,10)||0)),0,le.indexOf(o)>-1?o:p>-1&&r>p?"end":"start")}function q(){const r=t();if(void 0===r||null===r||8===r.nodeType)return;const o=ue(r,n(),w.value,y.value,l.virtualScrollHorizontal,d.lang.rtl,l.virtualScrollStickySizeStart,l.virtualScrollStickySizeEnd),a=e.value-1,i=o.scrollMaxSize-o.offsetStart-o.offsetEnd-g.value;if(u===o.scrollStart)return;if(o.scrollMaxSize<=0)return void P(r,o,0,0);f!==o.scrollViewSize&&M(o.scrollViewSize),T(_.value.from);const s=Math.floor(o.scrollMaxSize-Math.max(o.scrollViewSize,o.offsetEnd)-Math.min(h[a],o.scrollViewSize/2));if(s>0&&Math.ceil(o.scrollStart)>=s)return void P(r,o,a,o.scrollMaxSize-o.offsetEnd-m.reduce(de,0));let c=0,p=o.scrollStart-o.offsetStart,b=p;if(p<=i&&p+o.scrollViewSize>=v.value)p-=v.value,c=_.value.from,b=p;else for(let e=0;p>=m[e]&&c<a;e++)p-=m[e],c+=ie;while(p>0&&c<a)p-=h[c],p>-o.scrollViewSize?(c++,b=p):b=h[c]+p;P(r,o,c,b)}function P(t,n,r,o,a){const i="string"===typeof a&&a.indexOf("-force")>-1,s=!0===i?a.replace("-force",""):a,c=void 0!==s?s:"start";let p=Math.max(0,r-b.value[c]),f=p+b.value.total;f>e.value&&(f=e.value,p=Math.max(0,f-b.value.total)),u=n.scrollStart;const w=p!==_.value.from||f!==_.value.to;if(!1===w&&void 0===s)return void H(r);const{activeElement:y}=document,x=k.value;!0===w&&null!==x&&x!==y&&!0===x.contains(y)&&(x.addEventListener("focusout",j),setTimeout((()=>{void 0!==x&&x.removeEventListener("focusout",j)}))),ce(x,r-p);const S=void 0!==s?h.slice(p,r).reduce(de,0):0;if(!0===w){const t=f>=_.value.from&&p<=_.value.to?_.value.to:f;_.value={from:p,to:t},v.value=fe(m,h,0,p),g.value=fe(m,h,f,e.value),requestAnimationFrame((()=>{_.value.to!==f&&u===n.scrollStart&&(_.value={from:_.value.from,to:f},g.value=fe(m,h,f,e.value))}))}requestAnimationFrame((()=>{if(u!==n.scrollStart)return;!0===w&&T(p);const e=h.slice(p,r).reduce(de,0),a=e+n.offsetStart+v.value,c=a+h[r];let f=a+o;if(void 0!==s){const t=e-S,o=n.scrollStart+t;f=!0!==i&&o<a&&c<o+n.scrollViewSize?o:"end"===s?c-n.scrollViewSize:a-("start"===s?0:Math.round((n.scrollViewSize-h[r])/2))}u=f,pe(t,f,l.virtualScrollHorizontal,d.lang.rtl),H(r)}))}function T(e){const t=k.value;if(t){const n=se.call(t.children,(e=>e.classList&&!1===e.classList.contains("q-virtual-scroll--skip"))),r=n.length,o=!0===l.virtualScrollHorizontal?e=>e.getBoundingClientRect().width:e=>e.offsetHeight;let a,i,s=e;for(let e=0;e<r;){a=o(n[e]),e++;while(e<r&&!0===n[e].classList.contains("q-virtual-scroll--with-prev"))a+=o(n[e]),e++;i=a-h[s],0!==i&&(h[s]+=i,m[Math.floor(s/ie)]+=i),s++}}}function j(){void 0!==k.value&&k.value.focus()}function O(t,n){const o=1*a.value;!0!==n&&!1!==Array.isArray(h)||(h=[]);const i=h.length;h.length=e.value;for(let r=e.value-1;r>=i;r--)h[r]=o;const l=Math.floor((e.value-1)/ie);m=[];for(let r=0;r<=l;r++){let t=0;const n=Math.min((r+1)*ie,e.value);for(let e=r*ie;e<n;e++)t+=h[e];m.push(t)}p=-1,u=void 0,v.value=fe(m,h,0,_.value.from),g.value=fe(m,h,_.value.to,e.value),t>=0?(T(_.value.from),(0,r.Y3)((()=>{A(t)}))):z()}function M(e){if(void 0===e&&"undefined"!==typeof window){const r=t();void 0!==r&&null!==r&&8!==r.nodeType&&(e=ue(r,n(),w.value,y.value,l.virtualScrollHorizontal,d.lang.rtl,l.virtualScrollStickySizeStart,l.virtualScrollStickySizeEnd).scrollViewSize)}f=e;const r=parseFloat(l.virtualScrollSliceRatioBefore)||0,o=parseFloat(l.virtualScrollSliceRatioAfter)||0,i=1+r+o,s=void 0===e||e<=0?1:Math.ceil(e/a.value),c=Math.max(1,s,Math.ceil((l.virtualScrollSliceSize>0?l.virtualScrollSliceSize:10)/i));b.value={total:Math.ceil(c*i),start:Math.ceil(c*r),center:Math.ceil(c*(.5+r)),end:Math.ceil(c*(1+r)),view:s}}function F(e,t){const n=!0===l.virtualScrollHorizontal?"width":"height",o={["--q-virtual-scroll-item-"+n]:a.value+"px"};return["tbody"===e?(0,r.h)(e,{class:"q-virtual-scroll__padding",key:"before",ref:w},[(0,r.h)("tr",[(0,r.h)("td",{style:{[n]:`${v.value}px`,...o},colspan:x.value})])]):(0,r.h)(e,{class:"q-virtual-scroll__padding",key:"before",ref:w,style:{[n]:`${v.value}px`,...o}}),(0,r.h)(e,{class:"q-virtual-scroll__content",key:"content",ref:k,tabindex:-1},t.flat()),"tbody"===e?(0,r.h)(e,{class:"q-virtual-scroll__padding",key:"after",ref:y},[(0,r.h)("tr",[(0,r.h)("td",{style:{[n]:`${g.value}px`,...o},colspan:x.value})])]):(0,r.h)(e,{class:"q-virtual-scroll__padding",key:"after",ref:y,style:{[n]:`${g.value}px`,...o}})]}function H(e){p!==e&&(void 0!==l.onVirtualScroll&&s("virtual-scroll",{index:e,from:_.value.from,to:_.value.to-1,direction:e<p?"decrease":"increase",ref:c}),p=e)}(0,r.YP)(C,(()=>{M()})),(0,r.YP)(S,L),M();const z=(0,R.Z)(q,!0===d.platform.is.ios?120:35);(0,r.wF)((()=>{M()}));let B=!1;return(0,r.se)((()=>{B=!0})),(0,r.dl)((()=>{if(!0!==B)return;const e=t();void 0!==u&&void 0!==e&&null!==e&&8!==e.nodeType?pe(e,u,l.virtualScrollHorizontal,d.lang.rtl):A(p)})),(0,r.Jd)((()=>{z.cancel()})),Object.assign(c,{scrollTo:A,reset:L,refresh:E}),{virtualScrollSliceRange:_,virtualScrollSliceSizeComputed:b,setVirtualScrollSize:M,onVirtualScrollEvt:z,localResetVirtualScroll:O,padVirtualScroll:F,scrollTo:A,reset:L,refresh:E}}var ge=n(9256);const be=/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/,we=/[\u4e00-\u9fff\u3400-\u4dbf\u{20000}-\u{2a6df}\u{2a700}-\u{2b73f}\u{2b740}-\u{2b81f}\u{2b820}-\u{2ceaf}\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u{2f800}-\u{2fa1f}]/u,ye=/[\u3131-\u314e\u314f-\u3163\uac00-\ud7a3]/;function ke(e){return function(t){if("compositionend"===t.type||"change"===t.type){if(!0!==t.target.composing)return;t.target.composing=!1,e(t)}else"compositionupdate"===t.type?"string"===typeof t.data&&!1===be.test(t.data)&&!1===we.test(t.data)&&!1===ye.test(t.data)&&(t.target.composing=!1):t.target.composing=!0}}var _e=n(1705);const xe=e=>["add","add-unique","toggle"].includes(e),Se=".*+?^${}()|[]\\",Ce=Object.keys(Z),Le=(0,y.L)({name:"QSelect",inheritAttrs:!1,props:{...me,...ge.Fz,...Z,modelValue:{required:!0},multiple:Boolean,displayValue:[String,Number],displayValueHtml:Boolean,dropdownIcon:String,options:{type:Array,default:()=>[]},optionValue:[Function,String],optionLabel:[Function,String],optionDisable:[Function,String],hideSelected:Boolean,hideDropdownIcon:Boolean,fillInput:Boolean,maxValues:[Number,String],optionsDense:Boolean,optionsDark:{type:Boolean,default:null},optionsSelectedClass:String,optionsHtml:Boolean,optionsCover:Boolean,menuShrink:Boolean,menuAnchor:String,menuSelf:String,menuOffset:Array,popupContentClass:String,popupContentStyle:[String,Array,Object],useInput:Boolean,useChips:Boolean,newValueMode:{type:String,validator:xe},mapOptions:Boolean,emitValue:Boolean,inputDebounce:{type:[Number,String],default:500},inputClass:[Array,String,Object],inputStyle:[Array,String,Object],tabindex:{type:[String,Number],default:0},autocomplete:String,transitionShow:String,transitionHide:String,transitionDuration:[String,Number],behavior:{type:String,validator:e=>["default","menu","dialog"].includes(e),default:"default"},virtualScrollItemSize:{type:[Number,String],default:void 0},onNewValue:Function,onFilter:Function},emits:[...W,"add","remove","input-value","keyup","keypress","keydown","filter-abort"],setup(e,{slots:t,emit:n}){const{proxy:a}=(0,r.FN)(),{$q:i}=a,s=(0,o.iH)(!1),c=(0,o.iH)(!1),d=(0,o.iH)(-1),u=(0,o.iH)(""),p=(0,o.iH)(!1),f=(0,o.iH)(!1);let h,m,v,g,b,w,y,_,S;const C=(0,o.iH)(null),L=(0,o.iH)(null),E=(0,o.iH)(null),q=(0,o.iH)(null),P=(0,o.iH)(null),T=(0,ge.Do)(e),j=ke(Ye),O=(0,r.Fl)((()=>Array.isArray(e.options)?e.options.length:0)),R=(0,r.Fl)((()=>void 0===e.virtualScrollItemSize?!0===e.dense?24:48:e.virtualScrollItemSize)),{virtualScrollSliceRange:F,virtualScrollSliceSizeComputed:H,localResetVirtualScroll:z,padVirtualScroll:B,onVirtualScrollEvt:I,reset:V,scrollTo:$,setVirtualScrollSize:N}=ve({virtualScrollLength:O,getVirtualScrollTarget:We,getVirtualScrollEl:Ze,virtualScrollItemSizeComputed:R}),U=K(),Z=(0,r.Fl)((()=>{const t=!0===e.mapOptions&&!0!==e.multiple,n=void 0===e.modelValue||null===e.modelValue&&!0!==t?[]:!0===e.multiple&&Array.isArray(e.modelValue)?e.modelValue:[e.modelValue];if(!0===e.mapOptions&&!0===Array.isArray(e.options)){const r=!0===e.mapOptions&&void 0!==m?m:[],o=n.map((e=>ze(e,r)));return null===e.modelValue&&!0===t?o.filter((e=>null!==e)):o}return n})),W=(0,r.Fl)((()=>{const t={};return Ce.forEach((n=>{const r=e[n];void 0!==r&&(t[n]=r)})),t})),Y=(0,r.Fl)((()=>null===e.optionsDark?U.isDark.value:e.optionsDark)),Q=(0,r.Fl)((()=>D(Z.value))),ae=(0,r.Fl)((()=>{let t="q-field__input q-placeholder col";return!0===e.hideSelected||0===Z.value.length?[t,e.inputClass]:(t+=" q-field__input--padding",void 0===e.inputClass?t:[t,e.inputClass])})),ie=(0,r.Fl)((()=>(!0===e.virtualScrollHorizontal?"q-virtual-scroll--horizontal":"")+(e.popupContentClass?" "+e.popupContentClass:""))),le=(0,r.Fl)((()=>0===O.value)),se=(0,r.Fl)((()=>Z.value.map((e=>Ee.value(e))).join(", "))),ce=(0,r.Fl)((()=>!0===e.optionsHtml?()=>!0:e=>void 0!==e&&null!==e&&!0===e.html)),de=(0,r.Fl)((()=>!0===e.displayValueHtml||void 0===e.displayValue&&(!0===e.optionsHtml||Z.value.some(ce.value)))),ue=(0,r.Fl)((()=>!0===U.focused.value?e.tabindex:-1)),pe=(0,r.Fl)((()=>({tabindex:e.tabindex,role:"combobox","aria-label":e.label,"aria-autocomplete":!0===e.useInput?"list":"none","aria-expanded":!0===s.value?"true":"false","aria-owns":`${U.targetUid.value}_lb`,"aria-controls":`${U.targetUid.value}_lb`}))),fe=(0,r.Fl)((()=>{const t={id:`${U.targetUid.value}_lb`,role:"listbox","aria-multiselectable":!0===e.multiple?"true":"false"};return d.value>=0&&(t["aria-activedescendant"]=`${U.targetUid.value}_${d.value}`),t})),he=(0,r.Fl)((()=>Z.value.map(((e,t)=>({index:t,opt:e,html:ce.value(e),selected:!0,removeAtIndex:Oe,toggleOption:Re,tabindex:ue.value}))))),me=(0,r.Fl)((()=>{if(0===O.value)return[];const{from:t,to:n}=F.value;return e.options.slice(t,n).map(((n,r)=>{const o=!0===Ae.value(n),a=t+r,l={clickable:!0,active:!1,activeClass:ye.value,manualFocus:!0,focused:!1,disable:o,tabindex:-1,dense:e.optionsDense,dark:Y.value,role:"option",id:`${U.targetUid.value}_${a}`,onClick:()=>{Re(n)}};return!0!==o&&(!0===Ie(n)&&(l.active=!0),d.value===a&&(l.focused=!0),l["aria-selected"]=!0===l.active?"true":"false",!0===i.platform.is.desktop&&(l.onMousemove=()=>{!0===s.value&&Fe(a)})),{index:a,opt:n,html:ce.value(n),label:Ee.value(n),selected:l.active,focused:l.focused,toggleOption:Re,setOptionIndex:Fe,itemProps:l}}))})),be=(0,r.Fl)((()=>void 0!==e.dropdownIcon?e.dropdownIcon:i.iconSet.arrow.dropdown)),we=(0,r.Fl)((()=>!1===e.optionsCover&&!0!==e.outlined&&!0!==e.standout&&!0!==e.borderless&&!0!==e.rounded)),ye=(0,r.Fl)((()=>void 0!==e.optionsSelectedClass?e.optionsSelectedClass:void 0!==e.color?`text-${e.color}`:"")),Le=(0,r.Fl)((()=>Be(e.optionValue,"value"))),Ee=(0,r.Fl)((()=>Be(e.optionLabel,"label"))),Ae=(0,r.Fl)((()=>Be(e.optionDisable,"disable"))),qe=(0,r.Fl)((()=>Z.value.map((e=>Le.value(e))))),Pe=(0,r.Fl)((()=>{const e={onInput:Ye,onChange:j,onKeydown:De,onKeyup:Ne,onKeypress:Ue,onFocus:Ve,onClick(e){!0===v&&(0,A.sT)(e)}};return e.onCompositionstart=e.onCompositionupdate=e.onCompositionend=j,e}));function Te(t){return!0===e.emitValue?Le.value(t):t}function je(t){if(t>-1&&t<Z.value.length)if(!0===e.multiple){const r=e.modelValue.slice();n("remove",{index:t,value:r.splice(t,1)[0]}),n("update:modelValue",r)}else n("update:modelValue",null)}function Oe(e){je(e),U.focus()}function Me(t,r){const o=Te(t);if(!0!==e.multiple)return!0===e.fillInput&&Xe(Ee.value(t),!0,!0),void n("update:modelValue",o);if(0===Z.value.length)return n("add",{index:0,value:o}),void n("update:modelValue",!0===e.multiple?[o]:o);if(!0===r&&!0===Ie(t))return;if(void 0!==e.maxValues&&e.modelValue.length>=e.maxValues)return;const a=e.modelValue.slice();n("add",{index:a.length,value:o}),a.push(o),n("update:modelValue",a)}function Re(t,r){if(!0!==U.editable.value||void 0===t||!0===Ae.value(t))return;const o=Le.value(t);if(!0!==e.multiple)return!0!==r&&(Xe(!0===e.fillInput?Ee.value(t):"",!0,!0),pt()),null!==L.value&&L.value.focus(),void(0!==Z.value.length&&!0===(0,M.xb)(Le.value(Z.value[0]),o)||n("update:modelValue",!0===e.emitValue?o:t));if((!0!==v||!0===p.value)&&U.focus(),Ve(),0===Z.value.length){const r=!0===e.emitValue?o:t;return n("add",{index:0,value:r}),void n("update:modelValue",!0===e.multiple?[r]:r)}const a=e.modelValue.slice(),i=qe.value.findIndex((e=>(0,M.xb)(e,o)));if(i>-1)n("remove",{index:i,value:a.splice(i,1)[0]});else{if(void 0!==e.maxValues&&a.length>=e.maxValues)return;const r=!0===e.emitValue?o:t;n("add",{index:a.length,value:r}),a.push(r)}n("update:modelValue",a)}function Fe(e){if(!0!==i.platform.is.desktop)return;const t=e>-1&&e<O.value?e:-1;d.value!==t&&(d.value=t)}function He(t=1,n){if(!0===s.value){let r=d.value;do{r=x(r+t,-1,O.value-1)}while(-1!==r&&r!==d.value&&!0===Ae.value(e.options[r]));d.value!==r&&(Fe(r),$(r),!0!==n&&!0===e.useInput&&!0===e.fillInput&&Qe(r>=0?Ee.value(e.options[r]):w))}}function ze(t,n){const r=e=>(0,M.xb)(Le.value(e),t);return e.options.find(r)||n.find(r)||t}function Be(e,t){const n=void 0!==e?e:t;return"function"===typeof n?n:e=>null!==e&&"object"===typeof e&&n in e?e[n]:e}function Ie(e){const t=Le.value(e);return void 0!==qe.value.find((e=>(0,M.xb)(e,t)))}function Ve(t){!0===e.useInput&&null!==L.value&&(void 0===t||L.value===t.target&&t.target.value===se.value)&&L.value.select()}function $e(e){!0===(0,_e.So)(e,27)&&!0===s.value&&((0,A.sT)(e),pt(),ft()),n("keyup",e)}function Ne(t){const{value:n}=t.target;if(void 0===t.keyCode)if(t.target.value="",clearTimeout(h),ft(),"string"===typeof n&&n.length>0){const t=n.toLocaleLowerCase(),r=n=>{const r=e.options.find((e=>n.value(e).toLocaleLowerCase()===t));return void 0!==r&&(-1===Z.value.indexOf(r)?Re(r):pt(),!0)},o=e=>{!0!==r(Le)&&!0!==r(Ee)&&!0!==e&&et(n,!0,(()=>o(!0)))};o()}else U.clearValue(t);else $e(t)}function Ue(e){n("keypress",e)}function De(t){if(n("keydown",t),!0===(0,_e.Wm)(t))return;const o=u.value.length>0&&(void 0!==e.newValueMode||void 0!==e.onNewValue),a=!0!==t.shiftKey&&!0!==e.multiple&&(d.value>-1||!0===o);if(27===t.keyCode)return void(0,A.X$)(t);if(9===t.keyCode&&!1===a)return void dt();if(void 0===t.target||t.target.id!==U.targetUid.value)return;if(40===t.keyCode&&!0!==U.innerLoading.value&&!1===s.value)return(0,A.NS)(t),void ut();if(8===t.keyCode&&!0!==e.hideSelected&&0===u.value.length)return void(!0===e.multiple&&!0===Array.isArray(e.modelValue)?je(e.modelValue.length-1):!0!==e.multiple&&null!==e.modelValue&&n("update:modelValue",null));35!==t.keyCode&&36!==t.keyCode||"string"===typeof u.value&&0!==u.value.length||((0,A.NS)(t),d.value=-1,He(36===t.keyCode?1:-1,e.multiple)),33!==t.keyCode&&34!==t.keyCode||void 0===H.value||((0,A.NS)(t),d.value=Math.max(-1,Math.min(O.value,d.value+(33===t.keyCode?-1:1)*H.value.view)),He(33===t.keyCode?1:-1,e.multiple)),38!==t.keyCode&&40!==t.keyCode||((0,A.NS)(t),He(38===t.keyCode?-1:1,e.multiple));const i=O.value;if((void 0===_||S<Date.now())&&(_=""),i>0&&!0!==e.useInput&&void 0!==t.key&&1===t.key.length&&t.altKey===t.ctrlKey&&(32!==t.keyCode||_.length>0)){!0!==s.value&&ut(t);const n=t.key.toLocaleLowerCase(),o=1===_.length&&_[0]===n;S=Date.now()+1500,!1===o&&((0,A.NS)(t),_+=n);const a=new RegExp("^"+_.split("").map((e=>Se.indexOf(e)>-1?"\\"+e:e)).join(".*"),"i");let l=d.value;if(!0===o||l<0||!0!==a.test(Ee.value(e.options[l])))do{l=x(l+1,-1,i-1)}while(l!==d.value&&(!0===Ae.value(e.options[l])||!0!==a.test(Ee.value(e.options[l]))));d.value!==l&&(0,r.Y3)((()=>{Fe(l),$(l),l>=0&&!0===e.useInput&&!0===e.fillInput&&Qe(Ee.value(e.options[l]))}))}else if(13===t.keyCode||32===t.keyCode&&!0!==e.useInput&&""===_||9===t.keyCode&&!1!==a)if(9!==t.keyCode&&(0,A.NS)(t),d.value>-1&&d.value<i)Re(e.options[d.value]);else{if(!0===o){const t=(t,n)=>{if(n){if(!0!==xe(n))return}else n=e.newValueMode;if(void 0===t||null===t)return;Xe("",!0!==e.multiple,!0);const r="toggle"===n?Re:Me;r(t,"add-unique"===n),!0!==e.multiple&&(null!==L.value&&L.value.focus(),pt())};if(void 0!==e.onNewValue?n("new-value",u.value,t):t(u.value),!0!==e.multiple)return}!0===s.value?dt():!0!==U.innerLoading.value&&ut()}}function Ze(){return!0===v?P.value:null!==E.value&&null!==E.value.__qPortalInnerRef.value?E.value.__qPortalInnerRef.value:void 0}function We(){return Ze()}function Ke(){return!0===e.hideSelected?[]:void 0!==t["selected-item"]?he.value.map((e=>t["selected-item"](e))).slice():void 0!==t.selected?[].concat(t.selected()):!0===e.useChips?he.value.map(((t,n)=>(0,r.h)(X,{key:"option-"+n,removable:!0===U.editable.value&&!0!==Ae.value(t.opt),dense:!0,textColor:e.color,tabindex:ue.value,onRemove(){t.removeAtIndex(n)}},(()=>(0,r.h)("span",{class:"ellipsis",[!0===t.html?"innerHTML":"textContent"]:Ee.value(t.opt)}))))):[(0,r.h)("span",{[!0===de.value?"innerHTML":"textContent"]:void 0!==e.displayValue?e.displayValue:se.value})]}function Ge(){if(!0===le.value)return void 0!==t["no-option"]?t["no-option"]({inputValue:u.value}):void 0;const e=void 0!==t.option?t.option:e=>(0,r.h)(ee.Z,{key:e.index,...e.itemProps},(()=>(0,r.h)(te.Z,(()=>(0,r.h)(ne.Z,(()=>(0,r.h)("span",{[!0===e.html?"innerHTML":"textContent"]:e.label})))))));let n=B("div",me.value.map(e));return void 0!==t["before-options"]&&(n=t["before-options"]().concat(n)),(0,k.vs)(t["after-options"],n)}function Je(t,n){const o=!0===n?{...pe.value,...U.splitAttrs.attributes.value}:void 0,a={ref:!0===n?L:void 0,key:"i_t",class:ae.value,style:e.inputStyle,value:void 0!==u.value?u.value:"",type:"search",...o,id:!0===n?U.targetUid.value:void 0,maxlength:e.maxlength,autocomplete:e.autocomplete,"data-autofocus":!0!==t&&!0===e.autofocus||void 0,disabled:!0===e.disable,readonly:!0===e.readonly,...Pe.value};return!0!==t&&!0===v&&(!0===Array.isArray(a.class)?a.class=[...a.class,"no-pointer-events"]:a.class+=" no-pointer-events"),(0,r.h)("input",a)}function Ye(t){clearTimeout(h),t&&t.target&&!0===t.target.composing||(Qe(t.target.value||""),g=!0,w=u.value,!0===U.focused.value||!0===v&&!0!==p.value||U.focus(),void 0!==e.onFilter&&(h=setTimeout((()=>{et(u.value)}),e.inputDebounce)))}function Qe(e){u.value!==e&&(u.value=e,n("input-value",e))}function Xe(t,n,r){g=!0!==r,!0===e.useInput&&(Qe(t),!0!==n&&!0===r||(w=t),!0!==n&&et(t))}function et(t,o,i){if(void 0===e.onFilter||!0!==o&&!0!==U.focused.value)return;!0===U.innerLoading.value?n("filter-abort"):(U.innerLoading.value=!0,f.value=!0),""!==t&&!0!==e.multiple&&Z.value.length>0&&!0!==g&&t===Ee.value(Z.value[0])&&(t="");const l=setTimeout((()=>{!0===s.value&&(s.value=!1)}),10);clearTimeout(b),b=l,n("filter",t,((e,t)=>{!0!==o&&!0!==U.focused.value||b!==l||(clearTimeout(b),"function"===typeof e&&e(),f.value=!1,(0,r.Y3)((()=>{U.innerLoading.value=!1,!0===U.editable.value&&(!0===o?!0===s.value&&pt():!0===s.value?ht(!0):s.value=!0),"function"===typeof t&&(0,r.Y3)((()=>{t(a)})),"function"===typeof i&&(0,r.Y3)((()=>{i(a)}))})))}),(()=>{!0===U.focused.value&&b===l&&(clearTimeout(b),U.innerLoading.value=!1,f.value=!1),!0===s.value&&(s.value=!1)}))}function tt(){return(0,r.h)(re.Z,{ref:E,class:ie.value,style:e.popupContentStyle,modelValue:s.value,fit:!0!==e.menuShrink,cover:!0===e.optionsCover&&!0!==le.value&&!0!==e.useInput,anchor:e.menuAnchor,self:e.menuSelf,offset:e.menuOffset,dark:Y.value,noParentEvent:!0,noRefocus:!0,noFocus:!0,square:we.value,transitionShow:e.transitionShow,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,separateClosePopup:!0,...fe.value,onScrollPassive:I,onBeforeShow:gt,onBeforeHide:nt,onShow:rt},Ge)}function nt(e){bt(e),dt()}function rt(){N()}function ot(e){(0,A.sT)(e),null!==L.value&&L.value.focus(),p.value=!0,window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,0)}function at(e){(0,A.sT)(e),(0,r.Y3)((()=>{p.value=!1}))}function it(){const n=[(0,r.h)(J,{class:`col-auto ${U.fieldClass.value}`,...W.value,for:U.targetUid.value,dark:Y.value,square:!0,loading:f.value,itemAligned:!1,filled:!0,stackLabel:u.value.length>0,...U.splitAttrs.listeners.value,onFocus:ot,onBlur:at},{...t,rawControl:()=>U.getControl(!0),before:void 0,after:void 0})];return!0===s.value&&n.push((0,r.h)("div",{ref:P,class:ie.value+" scroll",style:e.popupContentStyle,...fe.value,onClick:A.X$,onScrollPassive:I},Ge())),(0,r.h)(oe.Z,{ref:q,modelValue:c.value,position:!0===e.useInput?"top":void 0,transitionShow:y,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,onBeforeShow:gt,onBeforeHide:lt,onHide:st,onShow:ct},(()=>(0,r.h)("div",{class:"q-select__dialog"+(!0===Y.value?" q-select__dialog--dark q-dark":"")+(!0===p.value?" q-select__dialog--focused":"")},n)))}function lt(e){bt(e),null!==q.value&&q.value.__updateRefocusTarget(U.rootRef.value.querySelector(".q-field__native > [tabindex]:last-child")),U.focused.value=!1}function st(e){pt(),!1===U.focused.value&&n("blur",e),ft()}function ct(){const e=document.activeElement;null!==e&&e.id===U.targetUid.value||null===L.value||L.value===e||L.value.focus(),N()}function dt(){!0!==c.value&&(d.value=-1,!0===s.value&&(s.value=!1),!1===U.focused.value&&(clearTimeout(b),b=void 0,!0===U.innerLoading.value&&(n("filter-abort"),U.innerLoading.value=!1,f.value=!1)))}function ut(n){!0===U.editable.value&&(!0===v?(U.onControlFocusin(n),c.value=!0,(0,r.Y3)((()=>{U.focus()}))):U.focus(),void 0!==e.onFilter?et(u.value):!0===le.value&&void 0===t["no-option"]||(s.value=!0))}function pt(){c.value=!1,dt()}function ft(){!0===e.useInput&&Xe(!0!==e.multiple&&!0===e.fillInput&&Z.value.length>0&&Ee.value(Z.value[0])||"",!0,!0)}function ht(t){let n=-1;if(!0===t){if(Z.value.length>0){const t=Le.value(Z.value[0]);n=e.options.findIndex((e=>(0,M.xb)(Le.value(e),t)))}z(n)}Fe(n)}function mt(){!0===s.value&&!1===U.innerLoading.value&&(V(),(0,r.Y3)((()=>{!0===s.value&&!1===U.innerLoading.value&&ht(!0)})))}function vt(){!1===c.value&&null!==E.value&&E.value.updatePosition()}function gt(e){void 0!==e&&(0,A.sT)(e),n("popup-show",e),U.hasPopupOpen=!0,U.onControlFocusin(e)}function bt(e){void 0!==e&&(0,A.sT)(e),n("popup-hide",e),U.hasPopupOpen=!1,U.onControlFocusout(e)}function wt(){v=(!0===i.platform.is.mobile||"dialog"===e.behavior)&&("menu"!==e.behavior&&(!0!==e.useInput||(void 0!==t["no-option"]||void 0!==e.onFilter||!1===le.value))),y=!0===i.platform.is.ios&&!0===v&&!0===e.useInput?"fade":e.transitionShow}return(0,r.YP)(Z,(t=>{m=t,!0===e.useInput&&!0===e.fillInput&&!0!==e.multiple&&!0!==U.innerLoading.value&&(!0!==c.value&&!0!==s.value||!0!==Q.value)&&(!0!==g&&ft(),!0!==c.value&&!0!==s.value||et(""))}),{immediate:!0}),(0,r.YP)((()=>e.fillInput),ft),(0,r.YP)(s,ht),(0,r.YP)(O,mt),(0,r.Xn)(wt),(0,r.ic)(vt),wt(),(0,r.Jd)((()=>{clearTimeout(h)})),Object.assign(a,{showPopup:ut,hidePopup:pt,removeAtIndex:je,add:Me,toggleOption:Re,getOptionIndex:()=>d.value,setOptionIndex:Fe,moveOptionSelection:He,filter:et,updateMenuPosition:vt,updateInputValue:Xe,isOptionSelected:Ie,getEmittingOptionValue:Te,isOptionDisabled:(...e)=>!0===Ae.value.apply(null,e),getOptionValue:(...e)=>Le.value.apply(null,e),getOptionLabel:(...e)=>Ee.value.apply(null,e)}),Object.assign(U,{innerValue:Z,fieldClass:(0,r.Fl)((()=>`q-select q-field--auto-height q-select--with${!0!==e.useInput?"out":""}-input q-select--with${!0!==e.useChips?"out":""}-chips q-select--`+(!0===e.multiple?"multiple":"single"))),inputRef:C,targetRef:L,hasValue:Q,showPopup:ut,floatingLabel:(0,r.Fl)((()=>(!0===e.hideSelected?u.value.length>0:!0===Q.value)||D(e.displayValue))),getControlChild:()=>{if(!1!==U.editable.value&&(!0===c.value||!0!==le.value||void 0!==t["no-option"]))return!0===v?it():tt();!0===U.hasPopupOpen&&(U.hasPopupOpen=!1)},controlEvents:{onFocusin(e){U.onControlFocusin(e)},onFocusout(e){U.onControlFocusout(e,(()=>{ft(),dt()}))},onClick(e){if((0,A.X$)(e),!0!==v&&!0===s.value)return dt(),void(null!==L.value&&L.value.focus());ut(e)}},getControl:t=>{const n=Ke(),o=!0===t||!0!==c.value||!0!==v;if(!0===e.useInput)n.push(Je(t,o));else if(!0===U.editable.value){const t=!0===o?pe.value:void 0;n.push((0,r.h)("input",{ref:!0===o?L:void 0,key:"d_t",class:"q-select__focus-target",id:!0===o?U.targetUid.value:void 0,readonly:!0,...t,onKeydown:De,onKeyup:$e,onKeypress:Ue})),!0===o&&"string"===typeof e.autocomplete&&e.autocomplete.length>0&&n.push((0,r.h)("input",{class:"q-select__autocomplete-input",autocomplete:e.autocomplete,onKeyup:Ne}))}if(void 0!==T.value&&!0!==e.disable&&qe.value.length>0){const t=qe.value.map((e=>(0,r.h)("option",{value:e,selected:!0})));n.push((0,r.h)("select",{class:"hidden",name:T.value,multiple:e.multiple},t))}const a=!0===e.useInput||!0!==o?void 0:U.splitAttrs.attributes.value;return(0,r.h)("div",{class:"q-field__native row items-center",...a},n)},getInnerAppend:()=>!0!==e.loading&&!0!==f.value&&!0!==e.hideDropdownIcon?[(0,r.h)(l.Z,{class:"q-select__dropdown-icon"+(!0===s.value?" rotate-180":""),name:be.value})]:null}),G(U)}})},136:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(9835),o=n(5987);const a=(0,r.h)("div",{class:"q-space"}),i=(0,o.L)({name:"QSpace",setup(){return()=>a}})},3940:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var r=n(9835),o=n(244);const a={size:{type:[Number,String],default:"1em"},color:String};function i(e){return{cSize:(0,r.Fl)((()=>e.size in o.Ok?`${o.Ok[e.size]}px`:e.size)),classes:(0,r.Fl)((()=>"q-spinner"+(e.color?` text-${e.color}`:"")))}}var l=n(5987);const s=(0,l.L)({name:"QSpinner",props:{...a,thickness:{type:Number,default:5}},setup(e){const{cSize:t,classes:n}=i(e);return()=>(0,r.h)("svg",{class:n.value+" q-spinner-mat",width:t.value,height:t.value,viewBox:"25 25 50 50"},[(0,r.h)("circle",{class:"path",cx:"50",cy:"50",r:"20",fill:"none",stroke:"currentColor","stroke-width":e.thickness,"stroke-miterlimit":"10"})])}})},9546:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(9835),o=n(5987),a=n(2026);const i=(0,o.L)({name:"QTr",props:{props:Object,noHover:Boolean},setup(e,{slots:t}){const n=(0,r.Fl)((()=>"q-tr"+(void 0===e.props||!0===e.props.header?"":" "+e.props.__trClass)+(!0===e.noHover?" q-tr--no-hover":"")));return()=>(0,r.h)("tr",{class:n.value},(0,a.KR)(t.default))}})},3333:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(9835),o=n(945),a=n(6951),i=n(5987);const l=(0,i.L)({name:"QRouteTab",props:{...o.$,...a.zY},emits:a.LZ,setup(e,{slots:t,emit:n}){const i=(0,o.Z)(),{renderTab:l,$tabs:s}=(0,a.ZP)(e,t,n,{exact:(0,r.Fl)((()=>e.exact)),...i});return(0,r.YP)((()=>e.name+e.exact+(i.linkRoute.value||{}).href),(()=>{s.verifyRouteModel()})),()=>l(i.linkTag.value,i.linkProps.value)}})},7661:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(6951),o=n(5987);const a=(0,o.L)({name:"QTab",props:r.zY,emits:r.LZ,setup(e,{slots:t,emit:n}){const{renderTab:o}=(0,r.ZP)(e,t,n);return()=>o("div")}})},7817:(e,t,n)=>{"use strict";n.d(t,{Z:()=>g});n(6727),n(702);var r=n(9835),o=n(499),a=n(2857),i=n(883),l=n(6916),s=n(2695),c=n(5987),d=n(1384),u=n(2026),p=n(5439),f=n(8383);function h(e,t,n){const r=!0===n?["left","right"]:["top","bottom"];return`absolute-${!0===t?r[0]:r[1]}${e?` text-${e}`:""}`}const m=["left","center","right","justify"],v=()=>{},g=(0,c.L)({name:"QTabs",props:{modelValue:[Number,String],align:{type:String,default:"center",validator:e=>m.includes(e)},breakpoint:{type:[String,Number],default:600},vertical:Boolean,shrink:Boolean,stretch:Boolean,activeClass:String,activeColor:String,activeBgColor:String,indicatorColor:String,leftIcon:String,rightIcon:String,outsideArrows:Boolean,mobileArrows:Boolean,switchIndicator:Boolean,narrowIndicator:Boolean,inlineLabel:Boolean,noCaps:Boolean,dense:Boolean,contentClass:String,"onUpdate:modelValue":[Function,Array]},setup(e,{slots:t,emit:n}){const c=(0,r.FN)(),{proxy:{$q:m}}=c,{registerTick:g}=(0,l.Z)(),{registerTimeout:b,removeTimeout:w}=(0,s.Z)(),{registerTimeout:y}=(0,s.Z)(),k=(0,o.iH)(null),_=(0,o.iH)(null),x=(0,o.iH)(e.modelValue),S=(0,o.iH)(!1),C=(0,o.iH)(!0),L=(0,o.iH)(!1),E=(0,o.iH)(!1),A=(0,r.Fl)((()=>!0===m.platform.is.desktop||!0===e.mobileArrows)),q=[],P=(0,o.iH)(!1);let T,j,O,M=!1,R=!0===A.value?K:d.ZT;const F=(0,r.Fl)((()=>({activeClass:e.activeClass,activeColor:e.activeColor,activeBgColor:e.activeBgColor,indicatorClass:h(e.indicatorColor,e.switchIndicator,e.vertical),narrowIndicator:e.narrowIndicator,inlineLabel:e.inlineLabel,noCaps:e.noCaps}))),H=(0,r.Fl)((()=>{const t=!0===S.value?"left":!0===E.value?"justify":e.align;return`q-tabs__content--align-${t}`})),z=(0,r.Fl)((()=>`q-tabs row no-wrap items-center q-tabs--${!0===S.value?"":"not-"}scrollable q-tabs--`+(!0===e.vertical?"vertical":"horizontal")+" q-tabs__arrows--"+(!0===A.value&&!0===e.outsideArrows?"outside":"inside")+(!0===e.dense?" q-tabs--dense":"")+(!0===e.shrink?" col-shrink":"")+(!0===e.stretch?" self-stretch":""))),B=(0,r.Fl)((()=>"q-tabs__content row no-wrap items-center self-stretch hide-scrollbar relative-position "+H.value+(void 0!==e.contentClass?` ${e.contentClass}`:"")+(!0===m.platform.is.mobile?" scroll":""))),I=(0,r.Fl)((()=>!0===e.vertical?{container:"height",content:"offsetHeight",scroll:"scrollHeight"}:{container:"width",content:"offsetWidth",scroll:"scrollWidth"})),V=(0,r.Fl)((()=>!0!==e.vertical&&!0===m.lang.rtl)),$=(0,r.Fl)((()=>!1===f.e&&!0===V.value));function N({name:t,setCurrent:r,skipEmit:o,fromRoute:a}){x.value!==t&&(!0!==o&&n("update:modelValue",t),!0!==r&&void 0!==e["onUpdate:modelValue"]||(Z(x.value,t),x.value=t)),void 0!==a&&(M=a)}function U(){g((()=>{!0!==c.isDeactivated&&!0!==c.isUnmounted&&D({width:k.value.offsetWidth,height:k.value.offsetHeight})}))}function D(t){if(void 0===I.value||null===_.value)return;const n=t[I.value.container],o=Math.min(_.value[I.value.scroll],Array.prototype.reduce.call(_.value.children,((e,t)=>e+(t[I.value.content]||0)),0)),a=n>0&&o>n;S.value!==a&&(S.value=a),!0===a&&(0,r.Y3)(R);const i=n<parseInt(e.breakpoint,10);E.value!==i&&(E.value=i)}function Z(t,n){const o=void 0!==t&&null!==t&&""!==t?q.find((e=>e.name.value===t)):null,a=void 0!==n&&null!==n&&""!==n?q.find((e=>e.name.value===n)):null;if(o&&a){const t=o.tabIndicatorRef.value,n=a.tabIndicatorRef.value;clearTimeout(T),t.style.transition="none",t.style.transform="none",n.style.transition="none",n.style.transform="none";const i=t.getBoundingClientRect(),l=n.getBoundingClientRect();n.style.transform=!0===e.vertical?`translate3d(0,${i.top-l.top}px,0) scale3d(1,${l.height?i.height/l.height:1},1)`:`translate3d(${i.left-l.left}px,0,0) scale3d(${l.width?i.width/l.width:1},1,1)`,(0,r.Y3)((()=>{T=setTimeout((()=>{n.style.transition="transform .25s cubic-bezier(.4, 0, .2, 1)",n.style.transform="none"}),70)}))}a&&!0===S.value&&W(a.rootRef.value)}function W(t){const{left:n,width:r,top:o,height:a}=_.value.getBoundingClientRect(),i=t.getBoundingClientRect();let l=!0===e.vertical?i.top-o:i.left-n;if(l<0)return _.value[!0===e.vertical?"scrollTop":"scrollLeft"]+=Math.floor(l),void R();l+=!0===e.vertical?i.height-a:i.width-r,l>0&&(_.value[!0===e.vertical?"scrollTop":"scrollLeft"]+=Math.ceil(l),R())}function K(){const t=_.value;if(null!==t){const n=t.getBoundingClientRect(),r=!0===e.vertical?t.scrollTop:Math.abs(t.scrollLeft);!0===V.value?(C.value=Math.ceil(r+n.width)<t.scrollWidth-1,L.value=r>0):(C.value=r>0,L.value=!0===e.vertical?Math.ceil(r+n.height)<t.scrollHeight:Math.ceil(r+n.width)<t.scrollWidth)}}function G(e){Q(),te(e),j=setInterval((()=>{!0===te(e)&&Q()}),5)}function J(){G(!0===$.value?Number.MAX_SAFE_INTEGER:0)}function Y(){G(!0===$.value?0:Number.MAX_SAFE_INTEGER)}function Q(){clearInterval(j)}function X(t,n){const r=Array.prototype.filter.call(_.value.children,(e=>e===n||e.matches&&!0===e.matches(".q-tab.q-focusable"))),o=r.length;if(0===o)return;if(36===t)return W(r[0]),!0;if(35===t)return W(r[o-1]),!0;const a=t===(!0===e.vertical?38:37),i=t===(!0===e.vertical?40:39),l=!0===a?-1:!0===i?1:void 0;if(void 0!==l){const e=!0===V.value?-1:1,t=r.indexOf(n)+l*e;return t>=0&&t<o&&(W(r[t]),r[t].focus({preventScroll:!0})),!0}}(0,r.YP)(V,R),(0,r.YP)((()=>e.modelValue),(e=>{N({name:e,setCurrent:!0,skipEmit:!0})})),(0,r.YP)((()=>e.outsideArrows),(()=>{(0,r.Y3)(U())})),(0,r.YP)(A,(e=>{R=!0===e?K:d.ZT,(0,r.Y3)(U())}));const ee=(0,r.Fl)((()=>!0===$.value?{get:e=>Math.abs(e.scrollLeft),set:(e,t)=>{e.scrollLeft=-t}}:!0===e.vertical?{get:e=>e.scrollTop,set:(e,t)=>{e.scrollTop=t}}:{get:e=>e.scrollLeft,set:(e,t)=>{e.scrollLeft=t}}));function te(e){const t=_.value,{get:n,set:r}=ee.value;let o=!1,a=n(t);const i=e<a?-1:1;return a+=5*i,a<0?(o=!0,a=0):(-1===i&&a<=e||1===i&&a>=e)&&(o=!0,a=e),r(t,a),R(),o}function ne(){return q.filter((e=>void 0!==e.routerProps&&!0===e.routerProps.hasRouterLink.value))}function re(){let e=null,t=M;const n={matchedLen:0,hrefLen:0,exact:!1,found:!1},{hash:r}=c.proxy.$route,o=x.value;let a=!0===t?v:e=>{o===e.name.value&&(t=!0,a=v)};const i=ne();for(const l of i){const t=!0===l.routerProps.exact.value;if(!0!==l.routerProps[!0===t?"linkIsExactActive":"linkIsActive"].value||!0===n.exact&&!0!==t){a(l);continue}const o=l.routerProps.linkRoute.value,i=o.hash;if(!0===t){if(r===i){e=l.name.value;break}if(""!==r&&""!==i){a(l);continue}}const s=o.matched.length,c=o.href.length-i.length;(s===n.matchedLen?c>n.hrefLen:s>n.matchedLen)?(e=l.name.value,Object.assign(n,{matchedLen:s,hrefLen:c,exact:t})):a(l)}!0!==t&&null===e||N({name:e,setCurrent:!0,fromRoute:!0})}function oe(e){if(w(),!0!==P.value&&null!==k.value&&e.target&&"function"===typeof e.target.closest){const t=e.target.closest(".q-tab");t&&!0===k.value.contains(t)&&(P.value=!0)}}function ae(){b((()=>{P.value=!1}),30)}function ie(){!0!==ce.avoidRouteWatcher&&y(re)}function le(e){q.push(e);const t=ne();t.length>0&&(void 0===O&&(O=(0,r.YP)((()=>c.proxy.$route),ie)),ie())}function se(e){if(q.splice(q.indexOf(e),1),void 0!==O){const e=ne();0===e.length&&(O(),O=void 0),ie()}}const ce={currentModel:x,tabProps:F,hasFocus:P,registerTab:le,unregisterTab:se,verifyRouteModel:ie,updateModel:N,recalculateScroll:U,onKbdNavigate:X,avoidRouteWatcher:!1};(0,r.JJ)(p.Nd,ce),(0,r.Jd)((()=>{clearTimeout(T),void 0!==O&&O()}));let de=!1;return(0,r.se)((()=>{de=!0})),(0,r.dl)((()=>{!0===de&&U()})),()=>{const n=[(0,r.h)(i.Z,{onResize:D}),(0,r.h)("div",{ref:_,class:B.value,onScroll:R},(0,u.KR)(t.default))];return!0===A.value&&n.push((0,r.h)(a.Z,{class:"q-tabs__arrow q-tabs__arrow--left absolute q-tab__icon"+(!0===C.value?"":" q-tabs__arrow--faded"),name:e.leftIcon||m.iconSet.tabs[!0===e.vertical?"up":"left"],onMousedown:J,onTouchstartPassive:J,onMouseup:Q,onMouseleave:Q,onTouchend:Q}),(0,r.h)(a.Z,{class:"q-tabs__arrow q-tabs__arrow--right absolute q-tab__icon"+(!0===L.value?"":" q-tabs__arrow--faded"),name:e.rightIcon||m.iconSet.tabs[!0===e.vertical?"down":"right"],onMousedown:Y,onTouchstartPassive:Y,onMouseup:Q,onMouseleave:Q,onTouchend:Q})),(0,r.h)("div",{ref:k,class:z.value,role:"tablist",onFocusin:oe,onFocusout:ae},n)}}})},6951:(e,t,n)=>{"use strict";n.d(t,{LZ:()=>p,ZP:()=>h,zY:()=>f});var r=n(9835),o=n(499),a=n(2857),i=n(1136),l=n(2026),s=n(1705),c=n(5439),d=n(1384);let u=0;const p=["click","keydown"],f={icon:String,label:[Number,String],alert:[Boolean,String],alertIcon:String,name:{type:[Number,String],default:()=>"t_"+u++},noCaps:Boolean,tabindex:[String,Number],disable:Boolean,contentClass:String,ripple:{type:[Boolean,Object],default:!0}};function h(e,t,n,u){const p=(0,r.f3)(c.Nd,(()=>{console.error("QTab/QRouteTab component needs to be child of QTabs")})),{proxy:f}=(0,r.FN)(),h=(0,o.iH)(null),m=(0,o.iH)(null),v=(0,o.iH)(null),g=(0,r.Fl)((()=>!0!==e.disable&&!1!==e.ripple&&Object.assign({keyCodes:[13,32],early:!0},!0===e.ripple?{}:e.ripple))),b=(0,r.Fl)((()=>p.currentModel.value===e.name)),w=(0,r.Fl)((()=>"q-tab relative-position self-stretch flex flex-center text-center"+(!0===b.value?" q-tab--active"+(p.tabProps.value.activeClass?" "+p.tabProps.value.activeClass:"")+(p.tabProps.value.activeColor?` text-${p.tabProps.value.activeColor}`:"")+(p.tabProps.value.activeBgColor?` bg-${p.tabProps.value.activeBgColor}`:""):" q-tab--inactive")+(e.icon&&e.label&&!1===p.tabProps.value.inlineLabel?" q-tab--full":"")+(!0===e.noCaps||!0===p.tabProps.value.noCaps?" q-tab--no-caps":"")+(!0===e.disable?" disabled":" q-focusable q-hoverable cursor-pointer")+(void 0!==u&&""!==u.linkClass.value?` ${u.linkClass.value}`:""))),y=(0,r.Fl)((()=>"q-tab__content self-stretch flex-center relative-position q-anchor--skip non-selectable "+(!0===p.tabProps.value.inlineLabel?"row no-wrap q-tab__content--inline":"column")+(void 0!==e.contentClass?` ${e.contentClass}`:""))),k=(0,r.Fl)((()=>!0===e.disable||!0===p.hasFocus.value?-1:e.tabindex||0));function _(t,r){if(!0!==r&&null!==h.value&&h.value.focus(),!0!==e.disable){let r;if(void 0!==u){if(!0!==u.hasRouterLink.value)return void n("click",t);r=()=>{t.__qNavigate=!0,p.avoidRouteWatcher=!0;const n=u.navigateToRouterLink(t);!1===n?p.avoidRouteWatcher=!1:n.then((t=>{p.avoidRouteWatcher=!1,void 0===t&&p.updateModel({name:e.name,fromRoute:!0})}))}}else r=()=>{p.updateModel({name:e.name,fromRoute:!1})};n("click",t,r),!0!==t.defaultPrevented&&r()}}function x(e){(0,s.So)(e,[13,32])?_(e,!0):!0!==(0,s.Wm)(e)&&e.keyCode>=35&&e.keyCode<=40&&!0===p.onKbdNavigate(e.keyCode,f.$el)&&(0,d.NS)(e),n("keydown",e)}function S(){const n=p.tabProps.value.narrowIndicator,o=[],i=(0,r.h)("div",{ref:v,class:["q-tab__indicator",p.tabProps.value.indicatorClass]});void 0!==e.icon&&o.push((0,r.h)(a.Z,{class:"q-tab__icon",name:e.icon})),void 0!==e.label&&o.push((0,r.h)("div",{class:"q-tab__label"},e.label)),!1!==e.alert&&o.push(void 0!==e.alertIcon?(0,r.h)(a.Z,{class:"q-tab__alert-icon",color:!0!==e.alert?e.alert:void 0,name:e.alertIcon}):(0,r.h)("div",{class:"q-tab__alert"+(!0!==e.alert?` text-${e.alert}`:"")})),!0===n&&o.push(i);const s=[(0,r.h)("div",{class:"q-focus-helper",tabindex:-1,ref:h}),(0,r.h)("div",{class:y.value},(0,l.vs)(t.default,o))];return!1===n&&s.push(i),s}const C={name:(0,r.Fl)((()=>e.name)),rootRef:m,tabIndicatorRef:v,routerProps:u};function L(t,n){const o={ref:m,class:w.value,tabindex:k.value,role:"tab","aria-selected":!0===b.value?"true":"false","aria-disabled":!0===e.disable?"true":void 0,onClick:_,onKeydown:x,...n};return(0,r.wy)((0,r.h)(t,o,S()),[[i.Z,g.value]])}return(0,r.Jd)((()=>{p.unregisterTab(C),p.recalculateScroll()})),(0,r.bv)((()=>{p.registerTab(C),p.recalculateScroll()})),{renderTab:L,$tabs:p}}},3175:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(9835),o=n(2857),a=n(1926),i=n(5987);const l=(0,i.L)({name:"QToggle",props:{...a.Fz,icon:String,iconColor:String},emits:a.ZB,setup(e){function t(t,n){const a=(0,r.Fl)((()=>(!0===t.value?e.checkedIcon:!0===n.value?e.indeterminateIcon:e.uncheckedIcon)||e.icon)),i=(0,r.Fl)((()=>!0===t.value?e.iconColor:null));return()=>[(0,r.h)("div",{class:"q-toggle__track"}),(0,r.h)("div",{class:"q-toggle__thumb absolute flex flex-center no-wrap"},void 0!==a.value?[(0,r.h)(o.Z,{name:a.value,color:i.value})]:void 0)]}return(0,a.ZP)("toggle",t)}})},6858:(e,t,n)=>{"use strict";n.d(t,{Z:()=>y});n(702);var r=n(9835),o=n(499),a=n(1957),i=n(4397),l=n(4088),s=n(3842),c=n(1518),d=n(431),u=n(6916),p=n(2695),f=n(5987),h=n(3701),m=n(1384),v=n(2589),g=n(2026),b=n(9092),w=n(9388);const y=(0,f.L)({name:"QTooltip",inheritAttrs:!1,props:{...i.u,...s.vr,...d.D,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null},transitionShow:{default:"jump-down"},transitionHide:{default:"jump-up"},anchor:{type:String,default:"bottom middle",validator:w.$},self:{type:String,default:"top middle",validator:w.$},offset:{type:Array,default:()=>[14,14],validator:w.io},scrollTarget:{default:void 0},delay:{type:Number,default:0},hideDelay:{type:Number,default:0}},emits:[...s.gH],setup(e,{slots:t,emit:n,attrs:f}){let y,k;const _=(0,r.FN)(),{proxy:{$q:x}}=_,S=(0,o.iH)(null),C=(0,o.iH)(!1),L=(0,r.Fl)((()=>(0,w.li)(e.anchor,x.lang.rtl))),E=(0,r.Fl)((()=>(0,w.li)(e.self,x.lang.rtl))),A=(0,r.Fl)((()=>!0!==e.persistent)),{registerTick:q,removeTick:P}=(0,u.Z)(),{registerTimeout:T,removeTimeout:j}=(0,p.Z)(),{transition:O,transitionStyle:M}=(0,d.Z)(e,C),{localScrollTarget:R,changeScrollEvent:F,unconfigureScrollTarget:H}=(0,l.Z)(e,X),{anchorEl:z,canShow:B,anchorEvents:I}=(0,i.Z)({showing:C,configureAnchorEl:Q}),{show:V,hide:$}=(0,s.ZP)({showing:C,canShow:B,handleShow:Z,handleHide:W,hideOnRouteChange:A,processOnMount:!0});Object.assign(I,{delayShow:J,delayHide:Y});const{showPortal:N,hidePortal:U,renderPortal:D}=(0,c.Z)(_,S,te);if(!0===x.platform.is.mobile){const t={anchorEl:z,innerRef:S,onClickOutside(e){return $(e),e.target.classList.contains("q-dialog__backdrop")&&(0,m.NS)(e),!0}},n=(0,r.Fl)((()=>null===e.modelValue&&!0!==e.persistent&&!0===C.value));(0,r.YP)(n,(e=>{const n=!0===e?b.m:b.D;n(t)})),(0,r.Jd)((()=>{(0,b.D)(t)}))}function Z(t){P(),j(),N(),q((()=>{k=new MutationObserver((()=>G())),k.observe(S.value,{attributes:!1,childList:!0,characterData:!0,subtree:!0}),G(),X()})),void 0===y&&(y=(0,r.YP)((()=>x.screen.width+"|"+x.screen.height+"|"+e.self+"|"+e.anchor+"|"+x.lang.rtl),G)),T((()=>{N(!0),n("show",t)}),e.transitionDuration)}function W(t){P(),j(),U(),K(),T((()=>{U(!0),n("hide",t)}),e.transitionDuration)}function K(){void 0!==k&&(k.disconnect(),k=void 0),void 0!==y&&(y(),y=void 0),H(),(0,m.ul)(I,"tooltipTemp")}function G(){const t=S.value;null!==z.value&&t&&(0,w.wq)({el:t,offset:e.offset,anchorEl:z.value,anchorOrigin:L.value,selfOrigin:E.value,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}function J(t){if(!0===x.platform.is.mobile){(0,v.M)(),document.body.classList.add("non-selectable");const e=z.value,t=["touchmove","touchcancel","touchend","click"].map((t=>[e,t,"delayHide","passiveCapture"]));(0,m.M0)(I,"tooltipTemp",t)}T((()=>{V(t)}),e.delay)}function Y(t){j(),!0===x.platform.is.mobile&&((0,m.ul)(I,"tooltipTemp"),(0,v.M)(),setTimeout((()=>{document.body.classList.remove("non-selectable")}),10)),T((()=>{$(t)}),e.hideDelay)}function Q(){if(!0===e.noParentEvent||null===z.value)return;const t=!0===x.platform.is.mobile?[[z.value,"touchstart","delayShow","passive"]]:[[z.value,"mouseenter","delayShow","passive"],[z.value,"mouseleave","delayHide","passive"]];(0,m.M0)(I,"anchor",t)}function X(){if(null!==z.value||void 0!==e.scrollTarget){R.value=(0,h.b0)(z.value,e.scrollTarget);const t=!0===e.noParentEvent?G:$;F(R.value,t)}}function ee(){return!0===C.value?(0,r.h)("div",{...f,ref:S,class:["q-tooltip q-tooltip--style q-position-engine no-pointer-events",f.class],style:[f.style,M.value],role:"complementary"},(0,g.KR)(t.default)):null}function te(){return(0,r.h)(a.uT,{name:O.value,appear:!0},ee)}return(0,r.Jd)(K),Object.assign(_.proxy,{updatePosition:G}),D}})},4397:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c,u:()=>s});var r=n(9835),o=n(499),a=n(2589),i=n(1384),l=n(1705);const s={target:{default:!0},noParentEvent:Boolean,contextMenu:Boolean};function c({showing:e,avoidEmit:t,configureAnchorEl:n}){const{props:s,proxy:c,emit:d}=(0,r.FN)(),u=(0,o.iH)(null);let p;function f(e){return null!==u.value&&(void 0===e||void 0===e.touches||e.touches.length<=1)}const h={};function m(){(0,i.ul)(h,"anchor")}function v(e){u.value=e;while(u.value.classList.contains("q-anchor--skip"))u.value=u.value.parentNode;n()}function g(){if(!1===s.target||""===s.target)u.value=null;else if(!0===s.target)v(c.$el.parentNode);else{let t=s.target;if("string"===typeof s.target)try{t=document.querySelector(s.target)}catch(e){t=void 0}void 0!==t&&null!==t?(u.value=t.$el||t,n()):(u.value=null,console.error(`Anchor: target "${s.target}" not found`))}}return void 0===n&&(Object.assign(h,{hide(e){c.hide(e)},toggle(e){c.toggle(e),e.qAnchorHandled=!0},toggleKey(e){!0===(0,l.So)(e,13)&&h.toggle(e)},contextClick(e){c.hide(e),(0,i.X$)(e),(0,r.Y3)((()=>{c.show(e),e.qAnchorHandled=!0}))},prevent:i.X$,mobileTouch(e){if(h.mobileCleanup(e),!0!==f(e))return;c.hide(e),u.value.classList.add("non-selectable");const t=e.target;(0,i.M0)(h,"anchor",[[t,"touchmove","mobileCleanup","passive"],[t,"touchend","mobileCleanup","passive"],[t,"touchcancel","mobileCleanup","passive"],[u.value,"contextmenu","prevent","notPassive"]]),p=setTimeout((()=>{c.show(e),e.qAnchorHandled=!0}),300)},mobileCleanup(t){u.value.classList.remove("non-selectable"),clearTimeout(p),!0===e.value&&void 0!==t&&(0,a.M)()}}),n=function(e=s.contextMenu){if(!0===s.noParentEvent||null===u.value)return;let t;t=!0===e?!0===c.$q.platform.is.mobile?[[u.value,"touchstart","mobileTouch","passive"]]:[[u.value,"mousedown","hide","passive"],[u.value,"contextmenu","contextClick","notPassive"]]:[[u.value,"click","toggle","passive"],[u.value,"keyup","toggleKey","passive"]],(0,i.M0)(h,"anchor",t)}),(0,r.YP)((()=>s.contextMenu),(e=>{null!==u.value&&(m(),n(e))})),(0,r.YP)((()=>s.target),(()=>{null!==u.value&&m(),g()})),(0,r.YP)((()=>s.noParentEvent),(e=>{null!==u.value&&(!0===e?m():n())})),(0,r.bv)((()=>{g(),!0!==t&&!0===s.modelValue&&null===u.value&&d("update:modelValue",!1)})),(0,r.Jd)((()=>{clearTimeout(p),m()})),{anchorEl:u,canShow:f,anchorEvents:h}}},8234:(e,t,n)=>{"use strict";n.d(t,{S:()=>o,Z:()=>a});var r=n(9835);const o={dark:{type:Boolean,default:null}};function a(e,t){return(0,r.Fl)((()=>null===e.dark?t.dark.isActive:e.dark))}},9256:(e,t,n)=>{"use strict";n.d(t,{Do:()=>i,Fz:()=>o,eX:()=>a});var r=n(9835);const o={name:String};function a(e={}){return(t,n,o)=>{t[n]((0,r.h)("input",{class:"hidden"+(o||""),...e.value}))}}function i(e){return(0,r.Fl)((()=>e.name||e.for))}},3842:(e,t,n)=>{"use strict";n.d(t,{ZP:()=>l,gH:()=>i,vr:()=>a});var r=n(9835),o=n(2046);const a={modelValue:{type:Boolean,default:null},"onUpdate:modelValue":[Function,Array]},i=["before-show","show","before-hide","hide"];function l({showing:e,canShow:t,hideOnRouteChange:n,handleShow:a,handleHide:i,processOnMount:l}){const s=(0,r.FN)(),{props:c,emit:d,proxy:u}=s;let p;function f(t){!0===e.value?v(t):h(t)}function h(e){if(!0===c.disable||void 0!==e&&!0===e.qAnchorHandled||void 0!==t&&!0!==t(e))return;const n=void 0!==c["onUpdate:modelValue"];!0===n&&(d("update:modelValue",!0),p=e,(0,r.Y3)((()=>{p===e&&(p=void 0)}))),null!==c.modelValue&&!1!==n||m(e)}function m(t){!0!==e.value&&(e.value=!0,d("before-show",t),void 0!==a?a(t):d("show",t))}function v(e){if(!0===c.disable)return;const t=void 0!==c["onUpdate:modelValue"];!0===t&&(d("update:modelValue",!1),p=e,(0,r.Y3)((()=>{p===e&&(p=void 0)}))),null!==c.modelValue&&!1!==t||g(e)}function g(t){!1!==e.value&&(e.value=!1,d("before-hide",t),void 0!==i?i(t):d("hide",t))}function b(t){if(!0===c.disable&&!0===t)void 0!==c["onUpdate:modelValue"]&&d("update:modelValue",!1);else if(!0===t!==e.value){const e=!0===t?m:g;e(p)}}(0,r.YP)((()=>c.modelValue),b),void 0!==n&&!0===(0,o.Rb)(s)&&(0,r.YP)((()=>u.$route.fullPath),(()=>{!0===n.value&&!0===e.value&&v()})),!0===l&&(0,r.bv)((()=>{b(c.modelValue)}));const w={show:h,hide:v,toggle:f};return Object.assign(u,w),w}},1518:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var r=n(499),o=n(9835),a=(n(1384),n(7026)),i=n(6669),l=n(2909);function s(e){e=e.parent;while(void 0!==e&&null!==e){if("QGlobalDialog"===e.type.name)return!0;if("QDialog"===e.type.name||"QMenu"===e.type.name)return!1;e=e.parent}return!1}function c(e,t,n,c){const d=(0,r.iH)(!1),u=(0,r.iH)(!1);let p=null;const f={},h=!0===c&&s(e);function m(t){if(!0===t)return(0,a.xF)(f),void(u.value=!0);u.value=!1,!1===d.value&&(!1===h&&null===p&&(p=(0,i.q_)()),d.value=!0,l.wN.push(e.proxy),(0,a.YX)(f))}function v(t){if(u.value=!1,!0!==t)return;(0,a.xF)(f),d.value=!1;const n=l.wN.indexOf(e.proxy);n>-1&&l.wN.splice(n,1),null!==p&&((0,i.pB)(p),p=null)}return(0,o.Ah)((()=>{v(!0)})),Object.assign(e.proxy,{__qPortalInnerRef:t}),{showPortal:m,hidePortal:v,portalIsActive:d,portalIsAccessible:u,renderPortal:()=>!0===h?n():!0===d.value?[(0,o.h)(o.lR,{to:p},n())]:void 0}}},5917:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(499),o=n(9835);function a(e,t){const n=(0,r.iH)(null),a=(0,o.Fl)((()=>!0!==e.disable?null:(0,o.h)("span",{ref:n,class:"no-outline",tabindex:-1})));function i(e){const r=t.value;void 0!==e&&0===e.type.indexOf("key")?null!==r&&document.activeElement!==r&&!0===r.contains(document.activeElement)&&r.focus():null!==n.value&&(void 0===e||null!==r&&!0===r.contains(e.target))&&n.value.focus()}return{refocusTargetEl:a,refocusTarget:i}}},945:(e,t,n)=>{"use strict";n.d(t,{$:()=>p,Z:()=>f});n(8964);var r=n(9835),o=n(1384),a=n(2046);function i(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}function l(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function s(e,t){for(const n in t){const r=t[n],o=e[n];if("string"===typeof r){if(r!==o)return!1}else if(!1===Array.isArray(o)||o.length!==r.length||r.some(((e,t)=>e!==o[t])))return!1}return!0}function c(e,t){return!0===Array.isArray(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}function d(e,t){return!0===Array.isArray(e)?c(e,t):!0===Array.isArray(t)?c(t,e):e===t}function u(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!1===d(e[n],t[n]))return!1;return!0}const p={to:[String,Object],replace:Boolean,exact:Boolean,activeClass:{type:String,default:"q-router-link--active"},exactActiveClass:{type:String,default:"q-router-link--exact-active"},href:String,target:String,disable:Boolean};function f(e){const t=(0,r.FN)(),{props:n,proxy:c}=t,d=(0,a.Rb)(t),p=(0,r.Fl)((()=>!0!==n.disable&&void 0!==n.href)),f=(0,r.Fl)((()=>!0===d&&!0!==n.disable&&!0!==p.value&&void 0!==n.to&&null!==n.to&&""!==n.to)),h=(0,r.Fl)((()=>{if(!0===f.value)try{return c.$router.resolve(n.to)}catch(e){}return null})),m=(0,r.Fl)((()=>null!==h.value)),v=(0,r.Fl)((()=>!0===p.value||!0===m.value)),g=(0,r.Fl)((()=>"a"===n.type||!0===v.value?"a":n.tag||e||"div")),b=(0,r.Fl)((()=>!0===p.value?{href:n.href,target:n.target}:!0===m.value?{href:h.value.href,target:n.target}:{})),w=(0,r.Fl)((()=>{if(!1===m.value)return null;const{matched:e}=h.value,{length:t}=e,n=e[t-1];if(void 0===n)return-1;const r=c.$route.matched;if(0===r.length)return-1;const o=r.findIndex(l.bind(null,n));if(o>-1)return o;const a=i(e[t-2]);return t>1&&i(n)===a&&r[r.length-1].path!==a?r.findIndex(l.bind(null,e[t-2])):o})),y=(0,r.Fl)((()=>!0===m.value&&w.value>-1&&s(c.$route.params,h.value.params))),k=(0,r.Fl)((()=>!0===y.value&&w.value===c.$route.matched.length-1&&u(c.$route.params,h.value.params))),_=(0,r.Fl)((()=>!0===m.value?!0===k.value?` ${n.exactActiveClass} ${n.activeClass}`:!0===n.exact?"":!0===y.value?` ${n.activeClass}`:"":""));function x(e){return!(!0===n.disable||e.metaKey||e.altKey||e.ctrlKey||e.shiftKey||!0!==e.__qNavigate&&!0===e.defaultPrevented||void 0!==e.button&&0!==e.button||"_blank"===n.target)&&((0,o.X$)(e),c.$router[!0===n.replace?"replace":"push"](n.to).catch((e=>e)))}return{hasRouterLink:m,hasHrefLink:p,hasLink:v,linkTag:g,linkRoute:h,linkIsActive:y,linkIsExactActive:k,linkClass:_,linkProps:b,navigateToRouterLink:x}}},4088:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(499),o=n(9835),a=n(1384);function i(e,t){const n=(0,r.iH)(null);let i;function l(e,t){const n=(void 0!==t?"add":"remove")+"EventListener",r=void 0!==t?t:i;e!==window&&e[n]("scroll",r,a.rU.passive),window[n]("scroll",r,a.rU.passive),i=t}function s(){null!==n.value&&(l(n.value),n.value=null)}const c=(0,o.YP)((()=>e.noParentEvent),(()=>{null!==n.value&&(s(),t())}));return(0,o.Jd)(c),{localScrollTarget:n,unconfigureScrollTarget:s,changeScrollEvent:l}}},244:(e,t,n)=>{"use strict";n.d(t,{LU:()=>a,Ok:()=>o,ZP:()=>i});var r=n(9835);const o={xs:18,sm:24,md:32,lg:38,xl:46},a={size:String};function i(e,t=o){return(0,r.Fl)((()=>void 0!==e.size?{fontSize:e.size in t?`${t[e.size]}px`:e.size}:null))}},6916:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=n(9835);function o(){let e;return(0,r.Jd)((()=>{e=void 0})),{registerTick(t){e=t,(0,r.Y3)((()=>{e===t&&(e(),e=void 0)}))},removeTick(){e=void 0}}}},2695:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});var r=n(9835);function o(){let e;return(0,r.Jd)((()=>{clearTimeout(e)})),{registerTimeout(t,n){clearTimeout(e),e=setTimeout(t,n)},removeTimeout(){clearTimeout(e)}}}},431:(e,t,n)=>{"use strict";n.d(t,{D:()=>a,Z:()=>i});var r=n(499),o=n(9835);const a={transitionShow:{type:String,default:"fade"},transitionHide:{type:String,default:"fade"},transitionDuration:{type:[String,Number],default:300}};function i(e,t){const n=(0,r.iH)(t.value);return(0,o.YP)(t,(e=>{(0,o.Y3)((()=>{n.value=e}))})),{transition:(0,o.Fl)((()=>"q-transition--"+(!0===n.value?e.transitionHide:e.transitionShow))),transitionStyle:(0,o.Fl)((()=>`--q-transition-duration: ${e.transitionDuration}ms`))}}},9302:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(9835),o=n(5439);function a(){return(0,r.f3)(o.Ng)}},2146:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(5987),o=n(2909),a=n(1705);function i(e){if(!1===e)return 0;if(!0===e||void 0===e)return 1;const t=parseInt(e,10);return isNaN(t)?0:t}const l=(0,r.f)({name:"close-popup",beforeMount(e,{value:t}){const n={depth:i(t),handler(t){0!==n.depth&&setTimeout((()=>{const r=(0,o.HW)(e);void 0!==r&&(0,o.S7)(r,t,n.depth)}))},handlerKey(e){!0===(0,a.So)(e,13)&&n.handler(e)}};e.__qclosepopup=n,e.addEventListener("click",n.handler),e.addEventListener("keyup",n.handlerKey)},updated(e,{value:t,oldValue:n}){t!==n&&(e.__qclosepopup.depth=i(t))},beforeUnmount(e){const t=e.__qclosepopup;e.removeEventListener("click",t.handler),e.removeEventListener("keyup",t.handlerKey),delete e.__qclosepopup}})},1136:(e,t,n)=>{"use strict";n.d(t,{Z:()=>d});n(6727);var r=n(5987),o=n(223),a=n(1384),i=n(1705);function l(e,t=250){let n,r=!1;return function(){return!1===r&&(r=!0,setTimeout((()=>{r=!1}),t),n=e.apply(this,arguments)),n}}function s(e,t,n,r){!0===n.modifiers.stop&&(0,a.sT)(e);const i=n.modifiers.color;let l=n.modifiers.center;l=!0===l||!0===r;const s=document.createElement("span"),c=document.createElement("span"),d=(0,a.FK)(e),{left:u,top:p,width:f,height:h}=t.getBoundingClientRect(),m=Math.sqrt(f*f+h*h),v=m/2,g=(f-m)/2+"px",b=l?g:d.left-u-v+"px",w=(h-m)/2+"px",y=l?w:d.top-p-v+"px";c.className="q-ripple__inner",(0,o.iv)(c,{height:`${m}px`,width:`${m}px`,transform:`translate3d(${b},${y},0) scale3d(.2,.2,1)`,opacity:0}),s.className="q-ripple"+(i?" text-"+i:""),s.setAttribute("dir","ltr"),s.appendChild(c),t.appendChild(s);const k=()=>{s.remove(),clearTimeout(_)};n.abort.push(k);let _=setTimeout((()=>{c.classList.add("q-ripple__inner--enter"),c.style.transform=`translate3d(${g},${w},0) scale3d(1,1,1)`,c.style.opacity=.2,_=setTimeout((()=>{c.classList.remove("q-ripple__inner--enter"),c.classList.add("q-ripple__inner--leave"),c.style.opacity=0,_=setTimeout((()=>{s.remove(),n.abort.splice(n.abort.indexOf(k),1)}),275)}),250)}),50)}function c(e,{modifiers:t,value:n,arg:r,instance:o}){const a=Object.assign({},o.$q.config.ripple,t,n);e.modifiers={early:!0===a.early,stop:!0===a.stop,center:!0===a.center,color:a.color||r,keyCodes:[].concat(a.keyCodes||13)}}const d=(0,r.f)({name:"ripple",beforeMount(e,t){const n={enabled:!1!==t.value,modifiers:{},abort:[],start(t){!0===n.enabled&&!0!==t.qSkipRipple&&(!0===n.modifiers.early?!0===["mousedown","touchstart"].includes(t.type):"click"===t.type)&&s(t,e,n,!0===t.qKeyEvent)},keystart:l((t=>{!0===n.enabled&&!0!==t.qSkipRipple&&!0===(0,i.So)(t,n.modifiers.keyCodes)&&t.type==="key"+(!0===n.modifiers.early?"down":"up")&&s(t,e,n,!0)}),300)};c(n,t),e.__qripple=n,(0,a.M0)(n,"main",[[e,"mousedown","start","passive"],[e,"touchstart","start","passive"],[e,"click","start","passive"],[e,"keydown","keystart","passive"],[e,"keyup","keystart","passive"]])},updated(e,t){if(t.oldValue!==t.value){const n=e.__qripple;n.enabled=!1!==t.value,!0===n.enabled&&Object(t.value)===t.value&&c(n,t)}},beforeUnmount(e){const t=e.__qripple;t.abort.forEach((e=>{e()})),(0,a.ul)(t,"main"),delete e._qripple}})},5310:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});n(702),n(6727);var r=n(7506),o=n(1384);const a=()=>!0;function i(e){return"string"===typeof e&&""!==e&&"/"!==e&&"#/"!==e}function l(e){return!0===e.startsWith("#")&&(e=e.substring(1)),!1===e.startsWith("/")&&(e="/"+e),!0===e.endsWith("/")&&(e=e.substring(0,e.length-1)),"#"+e}function s(e){if(!1===e.backButtonExit)return()=>!1;if("*"===e.backButtonExit)return a;const t=["#/"];return!0===Array.isArray(e.backButtonExit)&&t.push(...e.backButtonExit.filter(i).map(l)),()=>t.includes(window.location.hash)}const c={__history:[],add:o.ZT,remove:o.ZT,install({$q:e}){if(!0===this.__installed)return;const{cordova:t,capacitor:n}=r.Lp.is;if(!0!==t&&!0!==n)return;const o=e.config[!0===t?"cordova":"capacitor"];if(void 0!==o&&!1===o.backButton)return;if(!0===n&&(void 0===window.Capacitor||void 0===window.Capacitor.Plugins.App))return;this.add=e=>{void 0===e.condition&&(e.condition=a),this.__history.push(e)},this.remove=e=>{const t=this.__history.indexOf(e);t>=0&&this.__history.splice(t,1)};const i=s(Object.assign({backButtonExit:!0},o)),l=()=>{if(this.__history.length){const e=this.__history[this.__history.length-1];!0===e.condition()&&(this.__history.pop(),e.handler())}else!0===i()?navigator.app.exitApp():window.history.back()};!0===t?document.addEventListener("deviceready",(()=>{document.addEventListener("backbutton",l,!1)})):window.Capacitor.Plugins.App.addListener("backButton",l)}}},2016:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var r=n(4124),o=n(3251),a=n(5448);const i=(0,r.Z)({iconMapFn:null,__icons:{}},{set(e,t){const n={...e,rtl:!0===e.rtl};n.set=i.set,Object.assign(i.__icons,n)},install({$q:e,iconSet:t,ssrContext:n}){void 0!==e.config.iconMapFn&&(this.iconMapFn=e.config.iconMapFn),e.iconSet=this.__icons,(0,o.g)(e,"iconMapFn",(()=>this.iconMapFn),(e=>{this.iconMapFn=e})),!0===this.__installed?void 0!==t&&this.set(t):this.set(t||a.Z)}}),l=i},5879:(e,t,n)=>{"use strict";n.d(t,{$:()=>L,Z:()=>q});n(6727);var r=n(1957),o=n(7506),a=(n(702),n(4124)),i=n(1384),l=n(899);const s=["sm","md","lg","xl"],{passive:c}=i.rU,d=(0,a.Z)({width:0,height:0,name:"xs",sizes:{sm:600,md:1024,lg:1440,xl:1920},lt:{sm:!0,md:!0,lg:!0,xl:!0},gt:{xs:!1,sm:!1,md:!1,lg:!1},xs:!0,sm:!1,md:!1,lg:!1,xl:!1},{setSizes:i.ZT,setDebounce:i.ZT,install({$q:e,onSSRHydrated:t}){if(e.screen=this,!0===this.__installed)return void(void 0!==e.config.screen&&(!1===e.config.screen.bodyClasses?document.body.classList.remove(`screen--${this.name}`):this.__update(!0)));const{visualViewport:n}=window,r=n||window,a=document.scrollingElement||document.documentElement,i=void 0===n||!0===o.Lp.is.mobile?()=>[Math.max(window.innerWidth,a.clientWidth),Math.max(window.innerHeight,a.clientHeight)]:()=>[n.width*n.scale+window.innerWidth-a.clientWidth,n.height*n.scale+window.innerHeight-a.clientHeight],d=void 0!==e.config.screen&&!0===e.config.screen.bodyClasses;this.__update=e=>{const[t,n]=i();if(n!==this.height&&(this.height=n),t!==this.width)this.width=t;else if(!0!==e)return;let r=this.sizes;this.gt.xs=t>=r.sm,this.gt.sm=t>=r.md,this.gt.md=t>=r.lg,this.gt.lg=t>=r.xl,this.lt.sm=t<r.sm,this.lt.md=t<r.md,this.lt.lg=t<r.lg,this.lt.xl=t<r.xl,this.xs=this.lt.sm,this.sm=!0===this.gt.xs&&!0===this.lt.md,this.md=!0===this.gt.sm&&!0===this.lt.lg,this.lg=!0===this.gt.md&&!0===this.lt.xl,this.xl=this.gt.lg,r=(!0===this.xs?"xs":!0===this.sm&&"sm")||!0===this.md&&"md"||!0===this.lg&&"lg"||"xl",r!==this.name&&(!0===d&&(document.body.classList.remove(`screen--${this.name}`),document.body.classList.add(`screen--${r}`)),this.name=r)};let u,p={},f=16;this.setSizes=e=>{s.forEach((t=>{void 0!==e[t]&&(p[t]=e[t])}))},this.setDebounce=e=>{f=e};const h=()=>{const e=getComputedStyle(document.body);e.getPropertyValue("--q-size-sm")&&s.forEach((t=>{this.sizes[t]=parseInt(e.getPropertyValue(`--q-size-${t}`),10)})),this.setSizes=e=>{s.forEach((t=>{e[t]&&(this.sizes[t]=e[t])})),this.__update(!0)},this.setDebounce=e=>{void 0!==u&&r.removeEventListener("resize",u,c),u=e>0?(0,l.Z)(this.__update,e):this.__update,r.addEventListener("resize",u,c)},this.setDebounce(f),Object.keys(p).length>0?(this.setSizes(p),p=void 0):this.__update(),!0===d&&"xs"===this.name&&document.body.classList.add("screen--xs")};!0===o.uX.value?t.push(h):h()}});var u=n(9114),p=n(5310),f=n(3558),h=(n(8964),n(7674)),m=n(1705);function v(e){return!0===e.ios?"ios":!0===e.android?"android":void 0}function g({is:e,has:t,within:n},r){const o=[!0===e.desktop?"desktop":"mobile",(!1===t.touch?"no-":"")+"touch"];if(!0===e.mobile){const t=v(e);void 0!==t&&o.push("platform-"+t)}if(!0===e.nativeMobile){const t=e.nativeMobileWrapper;o.push(t),o.push("native-mobile"),!0!==e.ios||void 0!==r[t]&&!1===r[t].iosStatusBarPadding||o.push("q-ios-padding")}else!0===e.electron?o.push("electron"):!0===e.bex&&o.push("bex");return!0===n.iframe&&o.push("within-iframe"),o}function b(){const e=document.body.className;let t=e;void 0!==o.aG&&(t=t.replace("desktop","platform-ios mobile")),!0===o.Lp.has.touch&&(t=t.replace("no-touch","touch")),!0===o.Lp.within.iframe&&(t+=" within-iframe"),e!==t&&(document.body.className=t)}function w(e){for(const t in e)(0,h.Z)(t,e[t])}const y={install(e){if(!0!==this.__installed){if(!0===o.uX.value)b();else{const{$q:t}=e;void 0!==t.config.brand&&w(t.config.brand);const n=g(o.Lp,t.config);document.body.classList.add.apply(document.body.classList,n)}!0===o.Lp.is.ios&&document.body.addEventListener("touchstart",i.ZT),window.addEventListener("keydown",m.ZK,!0)}}};var k=n(2016),_=n(5439),x=n(7495),S=n(6254);const C=[o.ZP,y,u.Z,d,p.Z,f.Z,k.Z];function L(e,t){const n=(0,r.ri)(e);n.config.globalProperties=t.config.globalProperties;const{reload:o,...a}=t._context;return Object.assign(n._context,a),n}function E(e,t){t.forEach((t=>{t.install(e),t.__installed=!0}))}function A(e,t,n){e.config.globalProperties.$q=n.$q,e.provide(_.Ng,n.$q),E(n,C),void 0!==t.components&&Object.values(t.components).forEach((t=>{!0===(0,S.Kn)(t)&&void 0!==t.name&&e.component(t.name,t)})),void 0!==t.directives&&Object.values(t.directives).forEach((t=>{!0===(0,S.Kn)(t)&&void 0!==t.name&&e.directive(t.name,t)})),void 0!==t.plugins&&E(n,Object.values(t.plugins).filter((e=>"function"===typeof e.install&&!1===C.includes(e)))),!0===o.uX.value&&(n.$q.onSSRHydrated=()=>{n.onSSRHydrated.forEach((e=>{e()})),n.$q.onSSRHydrated=()=>{}})}const q=function(e,t={}){const n={version:"2.6.6"};!1===x.Uf?(void 0!==t.config&&Object.assign(x.w6,t.config),n.config={...x.w6},(0,x.tP)()):n.config=t.config||{},A(e,t,{parentApp:e,$q:n,lang:t.lang,iconSet:t.iconSet,onSSRHydrated:[]})}},3558:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l,F:()=>o});n(8964);var r=n(4124);const o={isoName:"en-US",nativeName:"English (US)",label:{clear:"Clear",ok:"OK",cancel:"Cancel",close:"Close",set:"Set",select:"Select",reset:"Reset",remove:"Remove",update:"Update",create:"Create",search:"Search",filter:"Filter",refresh:"Refresh"},date:{days:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),daysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),firstDayOfWeek:0,format24h:!1,pluralDay:"days"},table:{noData:"No data available",noResults:"No matching records found",loading:"Loading...",selectedRecords:e=>1===e?"1 record selected.":(0===e?"No":e)+" records selected.",recordsPerPage:"Records per page:",allRows:"All",pagination:(e,t,n)=>e+"-"+t+" of "+n,columns:"Columns"},editor:{url:"URL",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",underline:"Underline",unorderedList:"Unordered List",orderedList:"Ordered List",subscript:"Subscript",superscript:"Superscript",hyperlink:"Hyperlink",toggleFullscreen:"Toggle Fullscreen",quote:"Quote",left:"Left align",center:"Center align",right:"Right align",justify:"Justify align",print:"Print",outdent:"Decrease indentation",indent:"Increase indentation",removeFormat:"Remove formatting",formatting:"Formatting",fontSize:"Font Size",align:"Align",hr:"Insert Horizontal Rule",undo:"Undo",redo:"Redo",heading1:"Heading 1",heading2:"Heading 2",heading3:"Heading 3",heading4:"Heading 4",heading5:"Heading 5",heading6:"Heading 6",paragraph:"Paragraph",code:"Code",size1:"Very small",size2:"A bit small",size3:"Normal",size4:"Medium-large",size5:"Big",size6:"Very big",size7:"Maximum",defaultFont:"Default Font",viewSource:"View Source"},tree:{noNodes:"No nodes available",noResults:"No matching nodes found"}};function a(){const e=!0===Array.isArray(navigator.languages)&&navigator.languages.length>0?navigator.languages[0]:navigator.language;if("string"===typeof e)return e.split(/[-_]/).map(((e,t)=>0===t?e.toLowerCase():t>1||e.length<4?e.toUpperCase():e[0].toUpperCase()+e.slice(1).toLowerCase())).join("-")}const i=(0,r.Z)({__langPack:{}},{getLocale:a,set(e=o,t){const n={...e,rtl:!0===e.rtl,getLocale:a};{const e=document.documentElement;e.setAttribute("dir",!0===n.rtl?"rtl":"ltr"),e.setAttribute("lang",n.isoName),n.set=i.set,Object.assign(i.__langPack,n),i.props=n,i.isoName=n.isoName,i.nativeName=n.nativeName}},install({$q:e,lang:t,ssrContext:n}){e.lang=i.__langPack,!0===this.__installed?void 0!==t&&this.set(t):this.set(t||o)}}),l=i},9114:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});n(8964);var r=n(4124),o=n(7506);const a=(0,r.Z)({isActive:!1,mode:!1},{__media:void 0,set(e){a.mode=e,"auto"===e?(void 0===a.__media&&(a.__media=window.matchMedia("(prefers-color-scheme: dark)"),a.__updateMedia=()=>{a.set("auto")},a.__media.addListener(a.__updateMedia)),e=a.__media.matches):void 0!==a.__media&&(a.__media.removeListener(a.__updateMedia),a.__media=void 0),a.isActive=!0===e,document.body.classList.remove("body--"+(!0===e?"light":"dark")),document.body.classList.add("body--"+(!0===e?"dark":"light"))},toggle(){a.set(!1===a.isActive)},install({$q:e,onSSRHydrated:t,ssrContext:n}){const{dark:r}=e.config;if(e.dark=this,!0===this.__installed&&void 0===r)return;this.isActive=!0===r;const a=void 0!==r&&r;if(!0===o.uX.value){const e=e=>{this.__fromSSR=e},n=this.set;this.set=e,e(a),t.push((()=>{this.set=n,this.set(this.__fromSSR)}))}else this.set(a)}}),i=a},6950:(e,t,n)=>{"use strict";n.d(t,{Z:()=>b});var r=n(1957),o=n(9835),a=n(3940),i=n(4124),l=n(6669),s=n(5984),c=n(6254);let d,u,p,f=0,h={};const m={delay:0,message:!1,html:!1,spinnerSize:80,spinnerColor:"",messageColor:"",backgroundColor:"",boxClass:"",spinner:a.Z,customClass:""},v={...m},g=(0,i.Z)({isActive:!1},{show(e){if(h=!0===(0,c.Kn)(e)&&!0===e.ignoreDefaults?{...m,...e}:{...v,...e},g.isActive=!0,void 0!==d)return h.uid=f,void u.$forceUpdate();h.uid=++f,clearTimeout(p),p=setTimeout((()=>{p=void 0;const e=(0,l.q_)("q-loading");d=(0,r.ri)({name:"QLoading",setup(){function t(){!0!==g.isActive&&void 0!==d&&((0,s.Z)(!1),d.unmount(e),(0,l.pB)(e),d=void 0,u=void 0)}function n(){if(!0!==g.isActive)return null;const e=[(0,o.h)(h.spinner,{class:"q-loading__spinner",color:h.spinnerColor,size:h.spinnerSize})];return h.message&&e.push((0,o.h)("div",{class:"q-loading__message"+(h.messageColor?` text-${h.messageColor}`:""),[!0===h.html?"innerHTML":"textContent"]:h.message})),(0,o.h)("div",{class:"q-loading fullscreen flex flex-center z-max "+h.customClass.trim(),key:h.uid},[(0,o.h)("div",{class:"q-loading__backdrop"+(h.backgroundColor?` bg-${h.backgroundColor}`:"")}),(0,o.h)("div",{class:"q-loading__box column items-center "+h.boxClass},e)])}return(0,o.bv)((()=>{(0,s.Z)(!0)})),()=>(0,o.h)(r.uT,{name:"q-transition--fade",appear:!0,onAfterLeave:t},n)}}),u=d.mount(e)}),h.delay)},hide(){!0===g.isActive&&(void 0!==p&&(clearTimeout(p),p=void 0),g.isActive=!1)},setDefaults(e){!0===(0,c.Kn)(e)&&Object.assign(v,e)},install({$q:e}){e.loading=this,void 0!==e.config.loading&&this.setDefaults(e.config.loading)}}),b=g},3703:(e,t,n)=>{"use strict";n.d(t,{Z:()=>p});var r=n(7506),o=n(1384),a=n(6254);function i(e){return!0===(0,a.J_)(e)?"__q_date|"+e.toUTCString():!0===(0,a.Gf)(e)?"__q_expr|"+e.source:"number"===typeof e?"__q_numb|"+e:"boolean"===typeof e?"__q_bool|"+(e?"1":"0"):"string"===typeof e?"__q_strn|"+e:"function"===typeof e?"__q_strn|"+e.toString():e===Object(e)?"__q_objt|"+JSON.stringify(e):e}function l(e){const t=e.length;if(t<9)return e;const n=e.substring(0,8),r=e.substring(9);switch(n){case"__q_date":return new Date(r);case"__q_expr":return new RegExp(r);case"__q_numb":return Number(r);case"__q_bool":return Boolean("1"===r);case"__q_strn":return""+r;case"__q_objt":return JSON.parse(r);default:return e}}function s(){const e=()=>null;return{has:()=>!1,getLength:()=>0,getItem:e,getIndex:e,getKey:e,getAll:()=>{},getAllKeys:()=>[],set:o.ZT,remove:o.ZT,clear:o.ZT,isEmpty:()=>!0}}function c(e){const t=window[e+"Storage"],n=e=>{const n=t.getItem(e);return n?l(n):null};return{has:e=>null!==t.getItem(e),getLength:()=>t.length,getItem:n,getIndex:e=>e<t.length?n(t.key(e)):null,getKey:e=>e<t.length?t.key(e):null,getAll:()=>{let e;const r={},o=t.length;for(let a=0;a<o;a++)e=t.key(a),r[e]=n(e);return r},getAllKeys:()=>{const e=[],n=t.length;for(let r=0;r<n;r++)e.push(t.key(r));return e},set:(e,n)=>{t.setItem(e,i(n))},remove:e=>{t.removeItem(e)},clear:()=>{t.clear()},isEmpty:()=>0===t.length}}const d=!1===r.Lp.has.webStorage?s():c("local"),u={install({$q:e}){e.localStorage=d}};Object.assign(u,d);const p=u},4328:(e,t,n)=>{"use strict";n.d(t,{Z:()=>T});n(6727);var r=n(499),o=n(9835),a=n(1957),i=n(2857),l=n(244),s=n(5987),c=n(2026);const d=(0,s.L)({name:"QAvatar",props:{...l.LU,fontSize:String,color:String,textColor:String,icon:String,square:Boolean,rounded:Boolean},setup(e,{slots:t}){const n=(0,l.ZP)(e),r=(0,o.Fl)((()=>"q-avatar"+(e.color?` bg-${e.color}`:"")+(e.textColor?` text-${e.textColor} q-chip--colored`:"")+(!0===e.square?" q-avatar--square":!0===e.rounded?" rounded-borders":""))),a=(0,o.Fl)((()=>e.fontSize?{fontSize:e.fontSize}:null));return()=>{const l=void 0!==e.icon?[(0,o.h)(i.Z,{name:e.icon})]:void 0;return(0,o.h)("div",{class:r.value,style:n.value},[(0,o.h)("div",{class:"q-avatar__content row flex-center overflow-hidden",style:a.value},(0,c.pf)(t.default,l))])}}});var u=n(9379),p=n(3940),f=(n(1384),n(6669)),h=n(5879),m=n(6254);let v=0;const g={},b={},w={},y={},k=/^\s*$/,_=[],x=["top-left","top-right","bottom-left","bottom-right","top","bottom","left","right","center"],S=["top-left","top-right","bottom-left","bottom-right"],C={positive:{icon:e=>e.iconSet.type.positive,color:"positive"},negative:{icon:e=>e.iconSet.type.negative,color:"negative"},warning:{icon:e=>e.iconSet.type.warning,color:"warning",textColor:"dark"},info:{icon:e=>e.iconSet.type.info,color:"info"},ongoing:{group:!1,timeout:0,spinner:!0,color:"grey-8"}};function L(e,t,n){if(!e)return q("parameter required");let o;const a={textColor:"white"};if(!0!==e.ignoreDefaults&&Object.assign(a,g),!1===(0,m.Kn)(e)&&(a.type&&Object.assign(a,C[a.type]),e={message:e}),Object.assign(a,C[e.type||a.type],e),"function"===typeof a.icon&&(a.icon=a.icon(t)),a.spinner?(!0===a.spinner&&(a.spinner=p.Z),a.spinner=(0,r.Xl)(a.spinner)):a.spinner=!1,a.meta={hasMedia:Boolean(!1!==a.spinner||a.icon||a.avatar),hasText:A(a.message)||A(a.caption)},a.position){if(!1===x.includes(a.position))return q("wrong position",e)}else a.position="bottom";if(void 0===a.timeout)a.timeout=5e3;else{const t=parseInt(a.timeout,10);if(isNaN(t)||t<0)return q("wrong timeout",e);a.timeout=t}0===a.timeout?a.progress=!1:!0===a.progress&&(a.meta.progressClass="q-notification__progress"+(a.progressClass?` ${a.progressClass}`:""),a.meta.progressStyle={animationDuration:`${a.timeout+1e3}ms`});const i=(!0===Array.isArray(e.actions)?e.actions:[]).concat(!0!==e.ignoreDefaults&&!0===Array.isArray(g.actions)?g.actions:[]).concat(void 0!==C[e.type]&&!0===Array.isArray(C[e.type].actions)?C[e.type].actions:[]),{closeBtn:l}=a;if(l&&i.push({label:"string"===typeof l?l:t.lang.label.close}),a.actions=i.map((({handler:e,noDismiss:t,...n})=>({flat:!0,...n,onClick:"function"===typeof e?()=>{e(),!0!==t&&s()}:()=>{s()}}))),void 0===a.multiLine&&(a.multiLine=a.actions.length>1),Object.assign(a.meta,{class:"q-notification row items-stretch q-notification--"+(!0===a.multiLine?"multi-line":"standard")+(void 0!==a.color?` bg-${a.color}`:"")+(void 0!==a.textColor?` text-${a.textColor}`:"")+(void 0!==a.classes?` ${a.classes}`:""),wrapperClass:"q-notification__wrapper col relative-position border-radius-inherit "+(!0===a.multiLine?"column no-wrap justify-center":"row items-center"),contentClass:"q-notification__content row items-center"+(!0===a.multiLine?"":" col"),leftClass:!0===a.meta.hasText?"additional":"single",attrs:{role:"alert",...a.attrs}}),!1===a.group?(a.group=void 0,a.meta.group=void 0):(void 0!==a.group&&!0!==a.group||(a.group=[a.message,a.caption,a.multiline].concat(a.actions.map((e=>`${e.label}*${e.icon}`))).join("|")),a.meta.group=a.group+"|"+a.position),0===a.actions.length?a.actions=void 0:a.meta.actionsClass="q-notification__actions row items-center "+(!0===a.multiLine?"justify-end":"col-auto")+(!0===a.meta.hasMedia?" q-notification__actions--with-media":""),void 0!==n){clearTimeout(n.notif.meta.timer),a.meta.uid=n.notif.meta.uid;const e=w[a.position].value.indexOf(n.notif);w[a.position].value[e]=a}else{const t=b[a.meta.group];if(void 0===t){if(a.meta.uid=v++,a.meta.badge=1,-1!==["left","right","center"].indexOf(a.position))w[a.position].value.splice(Math.floor(w[a.position].value.length/2),0,a);else{const e=a.position.indexOf("top")>-1?"unshift":"push";w[a.position].value[e](a)}void 0!==a.group&&(b[a.meta.group]=a)}else{if(clearTimeout(t.meta.timer),void 0!==a.badgePosition){if(!1===S.includes(a.badgePosition))return q("wrong badgePosition",e)}else a.badgePosition="top-"+(a.position.indexOf("left")>-1?"right":"left");a.meta.uid=t.meta.uid,a.meta.badge=t.meta.badge+1,a.meta.badgeClass=`q-notification__badge q-notification__badge--${a.badgePosition}`+(void 0!==a.badgeColor?` bg-${a.badgeColor}`:"")+(void 0!==a.badgeTextColor?` text-${a.badgeTextColor}`:"")+(a.badgeClass?` ${a.badgeClass}`:"");const n=w[a.position].value.indexOf(t);w[a.position].value[n]=b[a.meta.group]=a}}const s=()=>{E(a),o=void 0};return a.timeout>0&&(a.meta.timer=setTimeout((()=>{s()}),a.timeout+1e3)),void 0!==a.group?t=>{void 0!==t?q("trying to update a grouped one which is forbidden",e):s()}:(o={dismiss:s,config:e,notif:a},void 0===n?e=>{if(void 0!==o)if(void 0===e)o.dismiss();else{const n=Object.assign({},o.config,e,{group:!1,position:a.position});L(n,t,o)}}:void Object.assign(n,o))}function E(e){clearTimeout(e.meta.timer);const t=w[e.position].value.indexOf(e);if(-1!==t){void 0!==e.group&&delete b[e.meta.group];const n=_[""+e.meta.uid];if(n){const{width:e,height:t}=getComputedStyle(n);n.style.left=`${n.offsetLeft}px`,n.style.width=e,n.style.height=t}w[e.position].value.splice(t,1),"function"===typeof e.onDismiss&&e.onDismiss()}}function A(e){return void 0!==e&&null!==e&&!0!==k.test(e)}function q(e,t){return console.error(`Notify: ${e}`,t),!1}function P(){return(0,s.L)({name:"QNotifications",devtools:{hide:!0},setup(){return()=>(0,o.h)("div",{class:"q-notifications"},x.map((e=>(0,o.h)(a.W3,{key:e,class:y[e],tag:"div",name:`q-notification--${e}`},(()=>w[e].value.map((e=>{const t=e.meta,n=[];if(!0===t.hasMedia&&(!1!==e.spinner?n.push((0,o.h)(e.spinner,{class:"q-notification__spinner q-notification__spinner--"+t.leftClass,color:e.spinnerColor,size:e.spinnerSize})):e.icon?n.push((0,o.h)(i.Z,{class:"q-notification__icon q-notification__icon--"+t.leftClass,name:e.icon,color:e.iconColor,size:e.iconSize,role:"img"})):e.avatar&&n.push((0,o.h)(d,{class:"q-notification__avatar q-notification__avatar--"+t.leftClass},(()=>(0,o.h)("img",{src:e.avatar,"aria-hidden":"true"}))))),!0===t.hasText){let t;const r={class:"q-notification__message col"};if(!0===e.html)r.innerHTML=e.caption?`<div>${e.message}</div><div class="q-notification__caption">${e.caption}</div>`:e.message;else{const n=[e.message];t=e.caption?[(0,o.h)("div",n),(0,o.h)("div",{class:"q-notification__caption"},[e.caption])]:n}n.push((0,o.h)("div",r,t))}const r=[(0,o.h)("div",{class:t.contentClass},n)];return!0===e.progress&&r.push((0,o.h)("div",{key:`${t.uid}|p|${t.badge}`,class:t.progressClass,style:t.progressStyle})),void 0!==e.actions&&r.push((0,o.h)("div",{class:t.actionsClass},e.actions.map((e=>(0,o.h)(u.Z,e))))),t.badge>1&&r.push((0,o.h)("div",{key:`${t.uid}|${t.badge}`,class:e.meta.badgeClass,style:e.badgeStyle},[t.badge])),(0,o.h)("div",{ref:e=>{_[""+t.uid]=e},key:t.uid,class:t.class,...t.attrs},[(0,o.h)("div",{class:t.wrapperClass},r)])})))))))}})}const T={setDefaults(e){!0===(0,m.Kn)(e)&&Object.assign(g,e)},registerType(e,t){!0===(0,m.Kn)(t)&&(C[e]=t)},install({$q:e,parentApp:t}){if(e.notify=this.create=t=>L(t,e),e.notify.setDefaults=this.setDefaults,e.notify.registerType=this.registerType,void 0!==e.config.notify&&this.setDefaults(e.config.notify),!0!==this.__installed){x.forEach((e=>{w[e]=(0,r.iH)([]);const t=!0===["left","center","right"].includes(e)?"center":e.indexOf("top")>-1?"top":"bottom",n=e.indexOf("left")>-1?"start":e.indexOf("right")>-1?"end":"center",o=["left","right"].includes(e)?`items-${"left"===e?"start":"end"} justify-center`:"center"===e?"flex-center":`items-${n}`;y[e]=`q-notifications__list q-notifications__list--${t} fixed column no-wrap ${o}`}));const e=(0,f.q_)("q-notify");(0,h.$)(P(),t).mount(e)}}}},7506:(e,t,n)=>{"use strict";n.d(t,{Lp:()=>m,ZP:()=>g,aG:()=>i,uX:()=>a});var r=n(499),o=n(3251);const a=(0,r.iH)(!1);let i,l=!1;function s(e,t){const n=/(edg|edge|edga|edgios)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(vivaldi)[\/]([\w.]+)/.exec(e)||/(chrome|crios)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+).*(version)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(firefox|fxios)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[\/]([\w.]+)/.exec(e)||[];return{browser:n[5]||n[3]||n[1]||"",version:n[2]||n[4]||"0",versionNumber:n[4]||n[2]||"0",platform:t[0]||""}}function c(e){return/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(silk)/.exec(e)||/(android)/.exec(e)||/(win)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||/(playbook)/.exec(e)||/(bb)/.exec(e)||/(blackberry)/.exec(e)||[]}const d="ontouchstart"in window||window.navigator.maxTouchPoints>0;function u(e){i={is:{...e}},delete e.mac,delete e.desktop;const t=Math.min(window.innerHeight,window.innerWidth)>414?"ipad":"iphone";Object.assign(e,{mobile:!0,ios:!0,platform:t,[t]:!0})}function p(e){const t=e.toLowerCase(),n=c(t),r=s(t,n),o={};r.browser&&(o[r.browser]=!0,o.version=r.version,o.versionNumber=parseInt(r.versionNumber,10)),r.platform&&(o[r.platform]=!0);const a=o.android||o.ios||o.bb||o.blackberry||o.ipad||o.iphone||o.ipod||o.kindle||o.playbook||o.silk||o["windows phone"];return!0===a||t.indexOf("mobile")>-1?(o.mobile=!0,o.edga||o.edgios?(o.edge=!0,r.browser="edge"):o.crios?(o.chrome=!0,r.browser="chrome"):o.fxios&&(o.firefox=!0,r.browser="firefox")):o.desktop=!0,(o.ipod||o.ipad||o.iphone)&&(o.ios=!0),o["windows phone"]&&(o.winphone=!0,delete o["windows phone"]),(o.chrome||o.opr||o.safari||o.vivaldi||!0===o.mobile&&!0!==o.ios&&!0!==a)&&(o.webkit=!0),o.edg&&(r.browser="edgechromium",o.edgeChromium=!0),(o.safari&&o.blackberry||o.bb)&&(r.browser="blackberry",o.blackberry=!0),o.safari&&o.playbook&&(r.browser="playbook",o.playbook=!0),o.opr&&(r.browser="opera",o.opera=!0),o.safari&&o.android&&(r.browser="android",o.android=!0),o.safari&&o.kindle&&(r.browser="kindle",o.kindle=!0),o.safari&&o.silk&&(r.browser="silk",o.silk=!0),o.vivaldi&&(r.browser="vivaldi",o.vivaldi=!0),o.name=r.browser,o.platform=r.platform,t.indexOf("electron")>-1?o.electron=!0:document.location.href.indexOf("-extension://")>-1?o.bex=!0:(void 0!==window.Capacitor?(o.capacitor=!0,o.nativeMobile=!0,o.nativeMobileWrapper="capacitor"):void 0===window._cordovaNative&&void 0===window.cordova||(o.cordova=!0,o.nativeMobile=!0,o.nativeMobileWrapper="cordova"),!0===d&&!0===o.mac&&(!0===o.desktop&&!0===o.safari||!0===o.nativeMobile&&!0!==o.android&&!0!==o.ios&&!0!==o.ipad)&&u(o)),o}const f=navigator.userAgent||navigator.vendor||window.opera,h={has:{touch:!1,webStorage:!1},within:{iframe:!1}},m={userAgent:f,is:p(f),has:{touch:d},within:{iframe:window.self!==window.top}},v={install(e){const{$q:t}=e;!0===a.value?(e.onSSRHydrated.push((()=>{a.value=!1,Object.assign(t.platform,m),i=void 0})),t.platform=(0,r.qj)(this)):t.platform=this}};{let e;(0,o.g)(m.has,"webStorage",(()=>{if(void 0!==e)return e;try{if(window.localStorage)return e=!0,!0}catch(t){}return e=!1,!1})),l=!0===m.is.ios&&-1===window.navigator.vendor.toLowerCase().indexOf("apple"),!0===a.value?Object.assign(v,m,i,h):Object.assign(v,m)}const g=v},899:(e,t,n)=>{"use strict";function r(e,t=250,n){let r;function o(){const o=arguments,a=()=>{r=void 0,!0!==n&&e.apply(this,o)};clearTimeout(r),!0===n&&void 0===r&&e.apply(this,o),r=setTimeout(a,t)}return o.cancel=()=>{clearTimeout(r)},o}n.d(t,{Z:()=>r})},223:(e,t,n)=>{"use strict";n.d(t,{iv:()=>o,mY:()=>i,sb:()=>a});var r=n(499);function o(e,t){const n=e.style;for(const r in t)n[r]=t[r]}function a(e){if(void 0===e||null===e)return;if("string"===typeof e)try{return document.querySelector(e)||void 0}catch(n){return}const t=!0===(0,r.dq)(e)?e.value:e;return t?t.$el||t:void 0}function i(e,t){if(void 0===e||null===e||!0===e.contains(t))return!0;for(let n=e.nextElementSibling;null!==n;n=n.nextElementSibling)if(n.contains(t))return!0;return!1}},1384:(e,t,n)=>{"use strict";n.d(t,{AZ:()=>i,FK:()=>a,M0:()=>d,NS:()=>c,X$:()=>s,ZT:()=>o,rU:()=>r,sT:()=>l,ul:()=>u});n(702);const r={hasPassive:!1,passiveCapture:!0,notPassiveCapture:!0};try{const e=Object.defineProperty({},"passive",{get(){Object.assign(r,{hasPassive:!0,passive:{passive:!0},notPassive:{passive:!1},passiveCapture:{passive:!0,capture:!0},notPassiveCapture:{passive:!1,capture:!0}})}});window.addEventListener("qtest",null,e),window.removeEventListener("qtest",null,e)}catch(p){}function o(){}function a(e){return e.touches&&e.touches[0]?e=e.touches[0]:e.changedTouches&&e.changedTouches[0]?e=e.changedTouches[0]:e.targetTouches&&e.targetTouches[0]&&(e=e.targetTouches[0]),{top:e.clientY,left:e.clientX}}function i(e){if(e.path)return e.path;if(e.composedPath)return e.composedPath();const t=[];let n=e.target;while(n){if(t.push(n),"HTML"===n.tagName)return t.push(document),t.push(window),t;n=n.parentElement}}function l(e){e.stopPropagation()}function s(e){!1!==e.cancelable&&e.preventDefault()}function c(e){!1!==e.cancelable&&e.preventDefault(),e.stopPropagation()}function d(e,t,n){const o=`__q_${t}_evt`;e[o]=void 0!==e[o]?e[o].concat(n):n,n.forEach((t=>{t[0].addEventListener(t[1],e[t[2]],r[t[3]])}))}function u(e,t){const n=`__q_${t}_evt`;void 0!==e[n]&&(e[n].forEach((t=>{t[0].removeEventListener(t[1],e[t[2]],r[t[3]])})),e[n]=void 0)}},5019:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});const r=Object.prototype.toString,o=Object.prototype.hasOwnProperty,a={};function i(e){return null===e?String(e):a[r.call(e)]||"object"}function l(e){if(!e||"object"!==i(e))return!1;if(e.constructor&&!o.call(e,"constructor")&&!o.call(e.constructor.prototype,"isPrototypeOf"))return!1;let t;for(t in e);return void 0===t||o.call(e,t)}function s(){let e,t,n,r,o,a,c=arguments[0]||{},d=1,u=!1;const p=arguments.length;for("boolean"===typeof c&&(u=c,c=arguments[1]||{},d=2),Object(c)!==c&&"function"!==i(c)&&(c={}),p===d&&(c=this,d--);d<p;d++)if(null!==(e=arguments[d]))for(t in e)n=c[t],r=e[t],c!==r&&(u&&r&&(l(r)||(o="array"===i(r)))?(o?(o=!1,a=n&&"array"===i(n)?n:[]):a=n&&l(n)?n:{},c[t]=s(u,a,r)):void 0!==r&&(c[t]=r));return c}"Boolean Number String Function Array Date RegExp Object".split(" ").forEach((e=>{a["[object "+e+"]"]=e.toLowerCase()}))},5984:(e,t,n)=>{"use strict";n.d(t,{Z:()=>w});var r=n(1384),o=n(3701),a=n(7506);let i,l,s,c,d,u,p=0,f=!1;function h(e){m(e)&&(0,r.NS)(e)}function m(e){if(e.target===document.body||e.target.classList.contains("q-layout__backdrop"))return!0;const t=(0,r.AZ)(e),n=e.shiftKey&&!e.deltaX,a=!n&&Math.abs(e.deltaX)<=Math.abs(e.deltaY),i=n||a?e.deltaY:e.deltaX;for(let r=0;r<t.length;r++){const e=t[r];if((0,o.QA)(e,a))return a?i<0&&0===e.scrollTop||i>0&&e.scrollTop+e.clientHeight===e.scrollHeight:i<0&&0===e.scrollLeft||i>0&&e.scrollLeft+e.clientWidth===e.scrollWidth}return!0}function v(e){e.target===document&&(document.scrollingElement.scrollTop=document.scrollingElement.scrollTop)}function g(e){!0!==f&&(f=!0,requestAnimationFrame((()=>{f=!1;const{height:t}=e.target,{clientHeight:n,scrollTop:r}=document.scrollingElement;void 0!==s&&t===window.innerHeight||(s=n-t,document.scrollingElement.scrollTop=r),r>s&&(document.scrollingElement.scrollTop-=Math.ceil((r-s)/8))})))}function b(e){const t=document.body,n=void 0!==window.visualViewport;if("add"===e){const{overflowY:e,overflowX:s}=window.getComputedStyle(t);i=(0,o.OI)(window),l=(0,o.u3)(window),c=t.style.left,d=t.style.top,t.style.left=`-${i}px`,t.style.top=`-${l}px`,"hidden"!==s&&("scroll"===s||t.scrollWidth>window.innerWidth)&&t.classList.add("q-body--force-scrollbar-x"),"hidden"!==e&&("scroll"===e||t.scrollHeight>window.innerHeight)&&t.classList.add("q-body--force-scrollbar-y"),t.classList.add("q-body--prevent-scroll"),document.qScrollPrevented=!0,!0===a.Lp.is.ios&&(!0===n?(window.scrollTo(0,0),window.visualViewport.addEventListener("resize",g,r.rU.passiveCapture),window.visualViewport.addEventListener("scroll",g,r.rU.passiveCapture),window.scrollTo(0,0)):window.addEventListener("scroll",v,r.rU.passiveCapture))}!0===a.Lp.is.desktop&&!0===a.Lp.is.mac&&window[`${e}EventListener`]("wheel",h,r.rU.notPassive),"remove"===e&&(!0===a.Lp.is.ios&&(!0===n?(window.visualViewport.removeEventListener("resize",g,r.rU.passiveCapture),window.visualViewport.removeEventListener("scroll",g,r.rU.passiveCapture)):window.removeEventListener("scroll",v,r.rU.passiveCapture)),t.classList.remove("q-body--prevent-scroll"),t.classList.remove("q-body--force-scrollbar-x"),t.classList.remove("q-body--force-scrollbar-y"),document.qScrollPrevented=!1,t.style.left=c,t.style.top=d,window.scrollTo(i,l),s=void 0)}function w(e){let t="add";if(!0===e){if(p++,void 0!==u)return clearTimeout(u),void(u=void 0);if(p>1)return}else{if(0===p)return;if(p--,p>0)return;if(t="remove",!0===a.Lp.is.ios&&!0===a.Lp.is.nativeMobile)return clearTimeout(u),void(u=setTimeout((()=>{b(t),u=void 0}),100))}b(t)}},9092:(e,t,n)=>{"use strict";n.d(t,{D:()=>d,m:()=>c});var r=n(1384),o=n(2909);let a;const{notPassiveCapture:i}=r.rU,l=[];function s(e){clearTimeout(a);const t=e.target;if(void 0===t||8===t.nodeType||!0===t.classList.contains("no-pointer-events"))return;let n=o.wN.length-1;while(n>=0){const e=o.wN[n].$;if("QDialog"!==e.type.name)break;if(!0!==e.props.seamless)return;n--}for(let r=l.length-1;r>=0;r--){const n=l[r];if(null!==n.anchorEl.value&&!1!==n.anchorEl.value.contains(t)||t!==document.body&&(null===n.innerRef.value||!1!==n.innerRef.value.contains(t)))return;e.qClickOutside=!0,n.onClickOutside(e)}}function c(e){l.push(e),1===l.length&&(document.addEventListener("mousedown",s,i),document.addEventListener("touchstart",s,i))}function d(e){const t=l.findIndex((t=>t===e));t>-1&&(l.splice(t,1),0===l.length&&(clearTimeout(a),document.removeEventListener("mousedown",s,i),document.removeEventListener("touchstart",s,i)))}},5987:(e,t,n)=>{"use strict";n.d(t,{L:()=>a,f:()=>i});var r=n(499),o=n(9835);const a=e=>(0,r.Xl)((0,o.aZ)(e)),i=e=>(0,r.Xl)(e)},4124:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(499),o=n(3251);const a=(e,t)=>{const n=(0,r.qj)(e);for(const r in e)(0,o.g)(t,r,(()=>n[r]),(e=>{n[r]=e}));return t}},6532:(e,t,n)=>{"use strict";n.d(t,{c:()=>u,k:()=>p});var r=n(7506),o=n(1705);const a=[];let i;function l(e){i=27===e.keyCode}function s(){!0===i&&(i=!1)}function c(e){!0===i&&(i=!1,!0===(0,o.So)(e,27)&&a[a.length-1](e))}function d(e){window[e]("keydown",l),window[e]("blur",s),window[e]("keyup",c),i=!1}function u(e){!0===r.Lp.is.desktop&&(a.push(e),1===a.length&&d("addEventListener"))}function p(e){const t=a.indexOf(e);t>-1&&(a.splice(t,1),0===a.length&&d("removeEventListener"))}},7026:(e,t,n)=>{"use strict";n.d(t,{YX:()=>i,fP:()=>c,jd:()=>s,xF:()=>l});let r=[],o=[];function a(e){o=o.filter((t=>t!==e))}function i(e){a(e),o.push(e)}function l(e){a(e),0===o.length&&r.length>0&&(r[r.length-1](),r=[])}function s(e){0===o.length?e():r.push(e)}function c(e){r=r.filter((t=>t!==e))}},4173:(e,t,n)=>{"use strict";n.d(t,{H:()=>l,i:()=>i});var r=n(7506);const o=[];function a(e){o[o.length-1](e)}function i(e){!0===r.Lp.is.desktop&&(o.push(e),1===o.length&&document.body.addEventListener("focusin",a))}function l(e){const t=o.indexOf(e);t>-1&&(o.splice(t,1),0===o.length&&document.body.removeEventListener("focusin",a))}},7495:(e,t,n)=>{"use strict";n.d(t,{Uf:()=>o,tP:()=>a,w6:()=>r});const r={};let o=!1;function a(){o=!0}},6669:(e,t,n)=>{"use strict";n.d(t,{pB:()=>l,q_:()=>i});var r=n(7495);const o=[];let a=document.body;function i(e){const t=document.createElement("div");if(void 0!==e&&(t.id=e),void 0!==r.w6.globalNodes){const e=r.w6.globalNodes["class"];void 0!==e&&(t.className=e)}return a.appendChild(t),o.push(t),t}function l(e){o.splice(o.indexOf(e),1),e.remove()}},3251:(e,t,n)=>{"use strict";function r(e,t,n,r){Object.defineProperty(e,t,{get:n,set:r,enumerable:!0})}n.d(t,{g:()=>r})},6254:(e,t,n)=>{"use strict";n.d(t,{Gf:()=>c,J_:()=>s,Kn:()=>l,xb:()=>i});n(702),n(3122);const r="function"===typeof Map,o="function"===typeof Set,a="function"===typeof ArrayBuffer;function i(e,t){if(e===t)return!0;if(null!==e&&null!==t&&"object"===typeof e&&"object"===typeof t){if(e.constructor!==t.constructor)return!1;let n,l;if(e.constructor===Array){if(n=e.length,n!==t.length)return!1;for(l=n;0!==l--;)if(!0!==i(e[l],t[l]))return!1;return!0}if(!0===r&&e.constructor===Map){if(e.size!==t.size)return!1;l=e.entries().next();while(!0!==l.done){if(!0!==t.has(l.value[0]))return!1;l=l.next()}l=e.entries().next();while(!0!==l.done){if(!0!==i(l.value[1],t.get(l.value[0])))return!1;l=l.next()}return!0}if(!0===o&&e.constructor===Set){if(e.size!==t.size)return!1;l=e.entries().next();while(!0!==l.done){if(!0!==t.has(l.value[0]))return!1;l=l.next()}return!0}if(!0===a&&null!=e.buffer&&e.buffer.constructor===ArrayBuffer){if(n=e.length,n!==t.length)return!1;for(l=n;0!==l--;)if(e[l]!==t[l])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const s=Object.keys(e).filter((t=>void 0!==e[t]));if(n=s.length,n!==Object.keys(t).filter((e=>void 0!==t[e])).length)return!1;for(l=n;0!==l--;){const n=s[l];if(!0!==i(e[n],t[n]))return!1}return!0}return e!==e&&t!==t}function l(e){return null!==e&&"object"===typeof e&&!0!==Array.isArray(e)}function s(e){return"[object Date]"===Object.prototype.toString.call(e)}function c(e){return"[object RegExp]"===Object.prototype.toString.call(e)}},1705:(e,t,n)=>{"use strict";n.d(t,{So:()=>i,Wm:()=>a,ZK:()=>o});n(6727);let r=!1;function o(e){r=!0===e.isComposing}function a(e){return!0===r||e!==Object(e)||!0===e.isComposing||!0===e.qKeyEvent}function i(e,t){return!0!==a(e)&&[].concat(t).includes(e.keyCode)}},9480:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});const r={xs:30,sm:35,md:40,lg:50,xl:60}},2909:(e,t,n)=>{"use strict";n.d(t,{AH:()=>i,HW:()=>a,S7:()=>l,wN:()=>o});var r=n(2046);const o=[];function a(e){return o.find((t=>null!==t.__qPortalInnerRef.value&&t.__qPortalInnerRef.value.contains(e)))}function i(e,t){do{if("QMenu"===e.$options.name){if(e.hide(t),!0===e.$props.separateClosePopup)return(0,r.Kq)(e)}else if(void 0!==e.__qPortalInnerRef){const n=(0,r.Kq)(e);return void 0!==n&&"QPopupProxy"===n.$options.name?(e.hide(t),n):e}e=(0,r.Kq)(e)}while(void 0!==e&&null!==e)}function l(e,t,n){while(0!==n&&void 0!==e&&null!==e){if(void 0!==e.__qPortalInnerRef){if(n--,"QMenu"===e.$options.name){e=i(e,t);continue}e.hide(t)}e=(0,r.Kq)(e)}}},9388:(e,t,n)=>{"use strict";n.d(t,{$:()=>l,io:()=>s,li:()=>d,wq:()=>f});n(6727);var r=n(3701),o=n(7506);let a,i;function l(e){const t=e.split(" ");return 2===t.length&&(!0!==["top","center","bottom"].includes(t[0])?(console.error("Anchor/Self position must start with one of top/center/bottom"),!1):!0===["left","middle","right","start","end"].includes(t[1])||(console.error("Anchor/Self position must end with one of left/middle/right/start/end"),!1))}function s(e){return!e||2===e.length&&("number"===typeof e[0]&&"number"===typeof e[1])}const c={"start#ltr":"left","start#rtl":"right","end#ltr":"right","end#rtl":"left"};function d(e,t){const n=e.split(" ");return{vertical:n[0],horizontal:c[`${n[1]}#${!0===t?"rtl":"ltr"}`]}}function u(e,t){let{top:n,left:r,right:o,bottom:a,width:i,height:l}=e.getBoundingClientRect();return void 0!==t&&(n-=t[1],r-=t[0],a+=t[1],o+=t[0],i+=t[0],l+=t[1]),{top:n,left:r,right:o,bottom:a,width:i,height:l,middle:r+(o-r)/2,center:n+(a-n)/2}}function p(e){return{top:0,center:e.offsetHeight/2,bottom:e.offsetHeight,left:0,middle:e.offsetWidth/2,right:e.offsetWidth}}function f(e){if(!0===o.Lp.is.ios&&void 0!==window.visualViewport){const e=document.body.style,{offsetLeft:t,offsetTop:n}=window.visualViewport;t!==a&&(e.setProperty("--q-pe-left",t+"px"),a=t),n!==i&&(e.setProperty("--q-pe-top",n+"px"),i=n)}let t;const{scrollLeft:n,scrollTop:r}=e.el;if(void 0===e.absoluteOffset)t=u(e.anchorEl,!0===e.cover?[0,0]:e.offset);else{const{top:n,left:r}=e.anchorEl.getBoundingClientRect(),o=n+e.absoluteOffset.top,a=r+e.absoluteOffset.left;t={top:o,left:a,width:1,height:1,right:a+1,center:o,middle:a,bottom:o+1}}let l={maxHeight:e.maxHeight,maxWidth:e.maxWidth,visibility:"visible"};!0!==e.fit&&!0!==e.cover||(l.minWidth=t.width+"px",!0===e.cover&&(l.minHeight=t.height+"px")),Object.assign(e.el.style,l);const s=p(e.el),c={top:t[e.anchorOrigin.vertical]-s[e.selfOrigin.vertical],left:t[e.anchorOrigin.horizontal]-s[e.selfOrigin.horizontal]};h(c,t,s,e.anchorOrigin,e.selfOrigin),l={top:c.top+"px",left:c.left+"px"},void 0!==c.maxHeight&&(l.maxHeight=c.maxHeight+"px",t.height>c.maxHeight&&(l.minHeight=l.maxHeight)),void 0!==c.maxWidth&&(l.maxWidth=c.maxWidth+"px",t.width>c.maxWidth&&(l.minWidth=l.maxWidth)),Object.assign(e.el.style,l),e.el.scrollTop!==r&&(e.el.scrollTop=r),e.el.scrollLeft!==n&&(e.el.scrollLeft=n)}function h(e,t,n,o,a){const i=n.bottom,l=n.right,s=(0,r.np)(),c=window.innerHeight-s,d=document.body.clientWidth;if(e.top<0||e.top+i>c)if("center"===a.vertical)e.top=t[o.vertical]>c/2?Math.max(0,c-i):0,e.maxHeight=Math.min(i,c);else if(t[o.vertical]>c/2){const n=Math.min(c,"center"===o.vertical?t.center:o.vertical===a.vertical?t.bottom:t.top);e.maxHeight=Math.min(i,n),e.top=Math.max(0,n-i)}else e.top=Math.max(0,"center"===o.vertical?t.center:o.vertical===a.vertical?t.top:t.bottom),e.maxHeight=Math.min(i,c-e.top);if(e.left<0||e.left+l>d)if(e.maxWidth=Math.min(l,d),"middle"===a.horizontal)e.left=t[o.horizontal]>d/2?Math.max(0,d-l):0;else if(t[o.horizontal]>d/2){const n=Math.min(d,"middle"===o.horizontal?t.middle:o.horizontal===a.horizontal?t.right:t.left);e.maxWidth=Math.min(l,n),e.left=Math.max(0,n-e.maxWidth)}else e.left=Math.max(0,"middle"===o.horizontal?t.middle:o.horizontal===a.horizontal?t.left:t.right),e.maxWidth=Math.min(l,d-e.left)}["left","middle","right"].forEach((e=>{c[`${e}#ltr`]=e,c[`${e}#rtl`]=e}))},2026:(e,t,n)=>{"use strict";n.d(t,{Bl:()=>a,Jl:()=>s,KR:()=>o,pf:()=>l,vs:()=>i});var r=n(9835);function o(e,t){return void 0!==e&&e()||t}function a(e,t){if(void 0!==e){const t=e();if(void 0!==t&&null!==t)return t.slice()}return t}function i(e,t){return void 0!==e?t.concat(e()):t}function l(e,t){return void 0===e?t:void 0!==t?t.concat(e()):e()}function s(e,t,n,o,a,i){t.key=o+a;const l=(0,r.h)(e,t,n);return!0===a?(0,r.wy)(l,i()):l}},8383:(e,t,n)=>{"use strict";n.d(t,{e:()=>r});let r=!1;{const e=document.createElement("div"),t=document.createElement("div");e.setAttribute("dir","rtl"),e.style.width="1px",e.style.height="1px",e.style.overflow="auto",t.style.width="1000px",t.style.height="1px",document.body.appendChild(e),e.appendChild(t),e.scrollLeft=-1e3,r=e.scrollLeft>=0,e.remove()}},2589:(e,t,n)=>{"use strict";n.d(t,{M:()=>o});var r=n(7506);function o(){if(void 0!==window.getSelection){const e=window.getSelection();void 0!==e.empty?e.empty():void 0!==e.removeAllRanges&&(e.removeAllRanges(),!0!==r.ZP.is.mobile&&e.addRange(document.createRange()))}else void 0!==document.selection&&document.selection.empty()}},5439:(e,t,n)=>{"use strict";n.d(t,{Mw:()=>a,Nd:()=>l,Ng:()=>r,YE:()=>o,vh:()=>i});const r="_q_",o="_q_l_",a="_q_pc_",i="_q_fo_",l="_q_tabs_"},2046:(e,t,n)=>{"use strict";n.d(t,{Kq:()=>r,Rb:()=>o});n(702);function r(e){if(Object(e.$parent)===e.$parent)return e.$parent;e=e.$.parent;while(Object(e)===e){if(Object(e.proxy)===e.proxy)return e.proxy;e=e.parent}}function o(e){return void 0!==e.appContext.config.globalProperties.$router}},3701:(e,t,n)=>{"use strict";n.d(t,{OI:()=>l,QA:()=>d,b0:()=>a,np:()=>c,u3:()=>i});n(6727);var r=n(223);const o=[null,document,document.body,document.scrollingElement,document.documentElement];function a(e,t){let n=(0,r.sb)(t);if(void 0===n){if(void 0===e||null===e)return window;n=e.closest(".scroll,.scroll-y,.overflow-auto")}return o.includes(n)?window:n}function i(e){return e===window?window.pageYOffset||window.scrollY||document.body.scrollTop||0:e.scrollTop}function l(e){return e===window?window.pageXOffset||window.scrollX||document.body.scrollLeft||0:e.scrollLeft}let s;function c(){if(void 0!==s)return s;const e=document.createElement("p"),t=document.createElement("div");(0,r.iv)(e,{width:"100%",height:"200px"}),(0,r.iv)(t,{position:"absolute",top:"0px",left:"0px",visibility:"hidden",width:"200px",height:"150px",overflow:"hidden"}),t.appendChild(e),document.body.appendChild(t);const n=e.offsetWidth;t.style.overflow="scroll";let o=e.offsetWidth;return n===o&&(o=t.clientWidth),t.remove(),s=n-o,s}function d(e,t=!0){return!(!e||e.nodeType!==Node.ELEMENT_NODE)&&(t?e.scrollHeight>e.clientHeight&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-y"])):e.scrollWidth>e.clientWidth&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-x"])))}},7674:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});n(6822);function r(e,t,n=document.body){if("string"!==typeof e)throw new TypeError("Expected a string as propName");if("string"!==typeof t)throw new TypeError("Expected a string as value");if(!(n instanceof Element))throw new TypeError("Expected a DOM element");n.style.setProperty(`--q-${e}`,t)}},796:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});n(8170),n(5231),n(7725),n(9359),n(6408),n(940),n(3667);let r,o=0;const a=new Array(256);for(let c=0;c<256;c++)a[c]=(c+256).toString(16).substring(1);const i=(()=>{const e="undefined"!==typeof crypto?crypto:"undefined"!==typeof window?window.crypto||window.msCrypto:void 0;if(void 0!==e){if(void 0!==e.randomBytes)return e.randomBytes;if(void 0!==e.getRandomValues)return t=>{const n=new Uint8Array(t);return e.getRandomValues(n),n}}return e=>{const t=[];for(let n=e;n>0;n--)t.push(Math.floor(256*Math.random()));return t}})(),l=4096;function s(){(void 0===r||o+16>l)&&(o=0,r=i(l));const e=Array.prototype.slice.call(r,o,o+=16);return e[6]=15&e[6]|64,e[8]=63&e[8]|128,a[e[0]]+a[e[1]]+a[e[2]]+a[e[3]]+"-"+a[e[4]]+a[e[5]]+"-"+a[e[6]]+a[e[7]]+"-"+a[e[8]]+a[e[9]]+"-"+a[e[10]]+a[e[11]]+a[e[12]]+a[e[13]]+a[e[14]]+a[e[15]]}},1947:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var r=n(5879),o=n(3558),a=n(2016);const i={version:"2.6.6",install:r.Z,lang:o.Z,iconSet:a.Z}},8762:(e,t,n)=>{var r=n(3834),o=n(6107),a=n(7545),i=r.TypeError;e.exports=function(e){if(o(e))return e;throw i(a(e)+" is not a function")}},9667:(e,t,n)=>{var r=n(3834),o=n(9627),a=n(7545),i=r.TypeError;e.exports=function(e){if(o(e))return e;throw i(a(e)+" is not a constructor")}},9220:(e,t,n)=>{var r=n(3834),o=n(6107),a=r.String,i=r.TypeError;e.exports=function(e){if("object"==typeof e||o(e))return e;throw i("Can't set "+a(e)+" as a prototype")}},5323:(e,t,n)=>{var r=n(4103),o=n(5267),a=n(1012),i=r("unscopables"),l=Array.prototype;void 0==l[i]&&a.f(l,i,{configurable:!0,value:o(null)}),e.exports=function(e){l[i][e]=!0}},3366:(e,t,n)=>{"use strict";var r=n(6823).charAt;e.exports=function(e,t,n){return t+(n?r(e,t).length:1)}},8406:(e,t,n)=>{var r=n(3834),o=n(6123),a=r.TypeError;e.exports=function(e,t){if(o(t,e))return e;throw a("Incorrect invocation")}},616:(e,t,n)=>{var r=n(3834),o=n(1419),a=r.String,i=r.TypeError;e.exports=function(e){if(o(e))return e;throw i(a(e)+" is not an object")}},2884:e=>{e.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},8086:(e,t,n)=>{"use strict";var r,o,a,i=n(2884),l=n(4133),s=n(3834),c=n(6107),d=n(1419),u=n(2924),p=n(4239),f=n(7545),h=n(4722),m=n(4076),v=n(1012).f,g=n(6123),b=n(7886),w=n(6534),y=n(4103),k=n(3965),_=s.Int8Array,x=_&&_.prototype,S=s.Uint8ClampedArray,C=S&&S.prototype,L=_&&b(_),E=x&&b(x),A=Object.prototype,q=s.TypeError,P=y("toStringTag"),T=k("TYPED_ARRAY_TAG"),j=k("TYPED_ARRAY_CONSTRUCTOR"),O=i&&!!w&&"Opera"!==p(s.opera),M=!1,R={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},F={BigInt64Array:8,BigUint64Array:8},H=function(e){if(!d(e))return!1;var t=p(e);return"DataView"===t||u(R,t)||u(F,t)},z=function(e){if(!d(e))return!1;var t=p(e);return u(R,t)||u(F,t)},B=function(e){if(z(e))return e;throw q("Target is not a typed array")},I=function(e){if(c(e)&&(!w||g(L,e)))return e;throw q(f(e)+" is not a typed array constructor")},V=function(e,t,n,r){if(l){if(n)for(var o in R){var a=s[o];if(a&&u(a.prototype,e))try{delete a.prototype[e]}catch(i){try{a.prototype[e]=t}catch(c){}}}E[e]&&!n||m(E,e,n?t:O&&x[e]||t,r)}},$=function(e,t,n){var r,o;if(l){if(w){if(n)for(r in R)if(o=s[r],o&&u(o,e))try{delete o[e]}catch(a){}if(L[e]&&!n)return;try{return m(L,e,n?t:O&&L[e]||t)}catch(a){}}for(r in R)o=s[r],!o||o[e]&&!n||m(o,e,t)}};for(r in R)o=s[r],a=o&&o.prototype,a?h(a,j,o):O=!1;for(r in F)o=s[r],a=o&&o.prototype,a&&h(a,j,o);if((!O||!c(L)||L===Function.prototype)&&(L=function(){throw q("Incorrect invocation")},O))for(r in R)s[r]&&w(s[r],L);if((!O||!E||E===A)&&(E=L.prototype,O))for(r in R)s[r]&&w(s[r].prototype,E);if(O&&b(C)!==E&&w(C,E),l&&!u(E,P))for(r in M=!0,v(E,P,{get:function(){return d(this)?this[T]:void 0}}),R)s[r]&&h(s[r],T,r);e.exports={NATIVE_ARRAY_BUFFER_VIEWS:O,TYPED_ARRAY_CONSTRUCTOR:j,TYPED_ARRAY_TAG:M&&T,aTypedArray:B,aTypedArrayConstructor:I,exportTypedArrayMethod:V,exportTypedArrayStaticMethod:$,isView:H,isTypedArray:z,TypedArray:L,TypedArrayPrototype:E}},2248:(e,t,n)=>{"use strict";var r=n(3834),o=n(1636),a=n(4133),i=n(2884),l=n(9104),s=n(4722),c=n(2714),d=n(8814),u=n(8406),p=n(6675),f=n(7302),h=n(4686),m=n(9798),v=n(7886),g=n(6534),b=n(3450).f,w=n(1012).f,y=n(5408),k=n(6378),_=n(2365),x=n(780),S=l.PROPER,C=l.CONFIGURABLE,L=x.get,E=x.set,A="ArrayBuffer",q="DataView",P="prototype",T="Wrong length",j="Wrong index",O=r[A],M=O,R=M&&M[P],F=r[q],H=F&&F[P],z=Object.prototype,B=r.Array,I=r.RangeError,V=o(y),$=o([].reverse),N=m.pack,U=m.unpack,D=function(e){return[255&e]},Z=function(e){return[255&e,e>>8&255]},W=function(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]},K=function(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]},G=function(e){return N(e,23,4)},J=function(e){return N(e,52,8)},Y=function(e,t){w(e[P],t,{get:function(){return L(this)[t]}})},Q=function(e,t,n,r){var o=h(n),a=L(e);if(o+t>a.byteLength)throw I(j);var i=L(a.buffer).bytes,l=o+a.byteOffset,s=k(i,l,l+t);return r?s:$(s)},X=function(e,t,n,r,o,a){var i=h(n),l=L(e);if(i+t>l.byteLength)throw I(j);for(var s=L(l.buffer).bytes,c=i+l.byteOffset,d=r(+o),u=0;u<t;u++)s[c+u]=d[a?u:t-u-1]};if(i){var ee=S&&O.name!==A;if(d((function(){O(1)}))&&d((function(){new O(-1)}))&&!d((function(){return new O,new O(1.5),new O(NaN),ee&&!C})))ee&&C&&s(O,"name",A);else{M=function(e){return u(this,R),new O(h(e))},M[P]=R;for(var te,ne=b(O),re=0;ne.length>re;)(te=ne[re++])in M||s(M,te,O[te]);R.constructor=M}g&&v(H)!==z&&g(H,z);var oe=new F(new M(2)),ae=o(H.setInt8);oe.setInt8(0,2147483648),oe.setInt8(1,2147483649),!oe.getInt8(0)&&oe.getInt8(1)||c(H,{setInt8:function(e,t){ae(this,e,t<<24>>24)},setUint8:function(e,t){ae(this,e,t<<24>>24)}},{unsafe:!0})}else M=function(e){u(this,R);var t=h(e);E(this,{bytes:V(B(t),0),byteLength:t}),a||(this.byteLength=t)},R=M[P],F=function(e,t,n){u(this,H),u(e,R);var r=L(e).byteLength,o=p(t);if(o<0||o>r)throw I("Wrong offset");if(n=void 0===n?r-o:f(n),o+n>r)throw I(T);E(this,{buffer:e,byteLength:n,byteOffset:o}),a||(this.buffer=e,this.byteLength=n,this.byteOffset=o)},H=F[P],a&&(Y(M,"byteLength"),Y(F,"buffer"),Y(F,"byteLength"),Y(F,"byteOffset")),c(H,{getInt8:function(e){return Q(this,1,e)[0]<<24>>24},getUint8:function(e){return Q(this,1,e)[0]},getInt16:function(e){var t=Q(this,2,e,arguments.length>1?arguments[1]:void 0);return(t[1]<<8|t[0])<<16>>16},getUint16:function(e){var t=Q(this,2,e,arguments.length>1?arguments[1]:void 0);return t[1]<<8|t[0]},getInt32:function(e){return K(Q(this,4,e,arguments.length>1?arguments[1]:void 0))},getUint32:function(e){return K(Q(this,4,e,arguments.length>1?arguments[1]:void 0))>>>0},getFloat32:function(e){return U(Q(this,4,e,arguments.length>1?arguments[1]:void 0),23)},getFloat64:function(e){return U(Q(this,8,e,arguments.length>1?arguments[1]:void 0),52)},setInt8:function(e,t){X(this,1,e,D,t)},setUint8:function(e,t){X(this,1,e,D,t)},setInt16:function(e,t){X(this,2,e,Z,t,arguments.length>2?arguments[2]:void 0)},setUint16:function(e,t){X(this,2,e,Z,t,arguments.length>2?arguments[2]:void 0)},setInt32:function(e,t){X(this,4,e,W,t,arguments.length>2?arguments[2]:void 0)},setUint32:function(e,t){X(this,4,e,W,t,arguments.length>2?arguments[2]:void 0)},setFloat32:function(e,t){X(this,4,e,G,t,arguments.length>2?arguments[2]:void 0)},setFloat64:function(e,t){X(this,8,e,J,t,arguments.length>2?arguments[2]:void 0)}});_(M,A),_(F,q),e.exports={ArrayBuffer:M,DataView:F}},5408:(e,t,n)=>{"use strict";var r=n(8332),o=n(2661),a=n(8600);e.exports=function(e){var t=r(this),n=a(t),i=arguments.length,l=o(i>1?arguments[1]:void 0,n),s=i>2?arguments[2]:void 0,c=void 0===s?n:o(s,n);while(c>l)t[l++]=e;return t}},7508:(e,t,n)=>{"use strict";var r=n(3834),o=n(6158),a=n(6654),i=n(8332),l=n(1108),s=n(5712),c=n(9627),d=n(8600),u=n(5976),p=n(4021),f=n(3395),h=r.Array;e.exports=function(e){var t=i(e),n=c(this),r=arguments.length,m=r>1?arguments[1]:void 0,v=void 0!==m;v&&(m=o(m,r>2?arguments[2]:void 0));var g,b,w,y,k,_,x=f(t),S=0;if(!x||this==h&&s(x))for(g=d(t),b=n?new this(g):h(g);g>S;S++)_=v?m(t[S],S):t[S],u(b,S,_);else for(y=p(t,x),k=y.next,b=n?new this:[];!(w=a(k,y)).done;S++)_=v?l(y,m,[w.value,S],!0):w.value,u(b,S,_);return b.length=S,b}},7714:(e,t,n)=>{var r=n(7447),o=n(2661),a=n(8600),i=function(e){return function(t,n,i){var l,s=r(t),c=a(s),d=o(i,c);if(e&&n!=n){while(c>d)if(l=s[d++],l!=l)return!0}else for(;c>d;d++)if((e||d in s)&&s[d]===n)return e||d||0;return!e&&-1}};e.exports={includes:i(!0),indexOf:i(!1)}},9275:(e,t,n)=>{var r=n(6158),o=n(3972),a=n(8332),i=n(8600),l=function(e){var t=1==e;return function(n,l,s){var c,d,u=a(n),p=o(u),f=r(l,s),h=i(p);while(h-- >0)if(c=p[h],d=f(c,h,u),d)switch(e){case 0:return c;case 1:return h}return t?-1:void 0}};e.exports={findLast:l(0),findLastIndex:l(1)}},9226:(e,t,n)=>{var r=n(6158),o=n(1636),a=n(3972),i=n(8332),l=n(8600),s=n(4837),c=o([].push),d=function(e){var t=1==e,n=2==e,o=3==e,d=4==e,u=6==e,p=7==e,f=5==e||u;return function(h,m,v,g){for(var b,w,y=i(h),k=a(y),_=r(m,v),x=l(k),S=0,C=g||s,L=t?C(h,x):n||p?C(h,0):void 0;x>S;S++)if((f||S in k)&&(b=k[S],w=_(b,S,y),e))if(t)L[S]=w;else if(w)switch(e){case 3:return!0;case 5:return b;case 6:return S;case 2:c(L,b)}else switch(e){case 4:return!1;case 7:c(L,b)}return u?-1:o||d?d:L}};e.exports={forEach:d(0),map:d(1),filter:d(2),some:d(3),every:d(4),find:d(5),findIndex:d(6),filterReject:d(7)}},6378:(e,t,n)=>{var r=n(3834),o=n(2661),a=n(8600),i=n(5976),l=r.Array,s=Math.max;e.exports=function(e,t,n){for(var r=a(e),c=o(t,r),d=o(void 0===n?r:n,r),u=l(s(d-c,0)),p=0;c<d;c++,p++)i(u,p,e[c]);return u.length=p,u}},7085:(e,t,n)=>{var r=n(6378),o=Math.floor,a=function(e,t){var n=e.length,s=o(n/2);return n<8?i(e,t):l(e,a(r(e,0,s),t),a(r(e,s),t),t)},i=function(e,t){var n,r,o=e.length,a=1;while(a<o){r=a,n=e[a];while(r&&t(e[r-1],n)>0)e[r]=e[--r];r!==a++&&(e[r]=n)}return e},l=function(e,t,n,r){var o=t.length,a=n.length,i=0,l=0;while(i<o||l<a)e[i+l]=i<o&&l<a?r(t[i],n[l])<=0?t[i++]:n[l++]:i<o?t[i++]:n[l++];return e};e.exports=a},4622:(e,t,n)=>{var r=n(3834),o=n(6555),a=n(9627),i=n(1419),l=n(4103),s=l("species"),c=r.Array;e.exports=function(e){var t;return o(e)&&(t=e.constructor,a(t)&&(t===c||o(t.prototype))?t=void 0:i(t)&&(t=t[s],null===t&&(t=void 0))),void 0===t?c:t}},4837:(e,t,n)=>{var r=n(4622);e.exports=function(e,t){return new(r(e))(0===t?0:t)}},1108:(e,t,n)=>{var r=n(616),o=n(4829);e.exports=function(e,t,n,a){try{return a?t(r(n)[0],n[1]):t(n)}catch(i){o(e,"throw",i)}}},8272:(e,t,n)=>{var r=n(4103),o=r("iterator"),a=!1;try{var i=0,l={next:function(){return{done:!!i++}},return:function(){a=!0}};l[o]=function(){return this},Array.from(l,(function(){throw 2}))}catch(s){}e.exports=function(e,t){if(!t&&!a)return!1;var n=!1;try{var r={};r[o]=function(){return{next:function(){return{done:n=!0}}}},e(r)}catch(s){}return n}},6749:(e,t,n)=>{var r=n(1636),o=r({}.toString),a=r("".slice);e.exports=function(e){return a(o(e),8,-1)}},4239:(e,t,n)=>{var r=n(3834),o=n(4130),a=n(6107),i=n(6749),l=n(4103),s=l("toStringTag"),c=r.Object,d="Arguments"==i(function(){return arguments}()),u=function(e,t){try{return e[t]}catch(n){}};e.exports=o?i:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=u(t=c(e),s))?n:d?i(t):"Object"==(r=i(t))&&a(t.callee)?"Arguments":r}},1328:(e,t,n)=>{var r=n(1636),o=Error,a=r("".replace),i=function(e){return String(o(e).stack)}("zxcasd"),l=/\n\s*at [^:]*:[^\n]*/,s=l.test(i);e.exports=function(e,t){if(s&&"string"==typeof e&&!o.prepareStackTrace)while(t--)e=a(e,l,"");return e}},7366:(e,t,n)=>{var r=n(2924),o=n(1240),a=n(863),i=n(1012);e.exports=function(e,t,n){for(var l=o(t),s=i.f,c=a.f,d=0;d<l.length;d++){var u=l[d];r(e,u)||n&&r(n,u)||s(e,u,c(t,u))}}},911:(e,t,n)=>{var r=n(8814);e.exports=!r((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},1551:(e,t,n)=>{"use strict";var r=n(619).IteratorPrototype,o=n(5267),a=n(3386),i=n(2365),l=n(1366),s=function(){return this};e.exports=function(e,t,n,c){var d=t+" Iterator";return e.prototype=o(r,{next:a(+!c,n)}),i(e,d,!1,!0),l[d]=s,e}},4722:(e,t,n)=>{var r=n(4133),o=n(1012),a=n(3386);e.exports=r?function(e,t,n){return o.f(e,t,a(1,n))}:function(e,t,n){return e[t]=n,e}},3386:e=>{e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},5976:(e,t,n)=>{"use strict";var r=n(1017),o=n(1012),a=n(3386);e.exports=function(e,t,n){var i=r(t);i in e?o.f(e,i,a(0,n)):e[i]=n}},9570:(e,t,n)=>{var r=n(2358),o=n(1012);e.exports=function(e,t,n){return n.get&&r(n.get,t,{getter:!0}),n.set&&r(n.set,t,{setter:!0}),o.f(e,t,n)}},4076:(e,t,n)=>{var r=n(3834),o=n(6107),a=n(4722),i=n(2358),l=n(4650);e.exports=function(e,t,n,s){var c=!!s&&!!s.unsafe,d=!!s&&!!s.enumerable,u=!!s&&!!s.noTargetGet,p=s&&void 0!==s.name?s.name:t;return o(n)&&i(n,p,s),e===r?(d?e[t]=n:l(t,n),e):(c?!u&&e[t]&&(d=!0):delete e[t],d?e[t]=n:a(e,t,n),e)}},2714:(e,t,n)=>{var r=n(4076);e.exports=function(e,t,n){for(var o in t)r(e,o,t[o],n);return e}},3532:(e,t,n)=>{"use strict";var r=n(6943),o=n(6654),a=n(200),i=n(9104),l=n(6107),s=n(1551),c=n(7886),d=n(6534),u=n(2365),p=n(4722),f=n(4076),h=n(4103),m=n(1366),v=n(619),g=i.PROPER,b=i.CONFIGURABLE,w=v.IteratorPrototype,y=v.BUGGY_SAFARI_ITERATORS,k=h("iterator"),_="keys",x="values",S="entries",C=function(){return this};e.exports=function(e,t,n,i,h,v,L){s(n,t,i);var E,A,q,P=function(e){if(e===h&&R)return R;if(!y&&e in O)return O[e];switch(e){case _:return function(){return new n(this,e)};case x:return function(){return new n(this,e)};case S:return function(){return new n(this,e)}}return function(){return new n(this)}},T=t+" Iterator",j=!1,O=e.prototype,M=O[k]||O["@@iterator"]||h&&O[h],R=!y&&M||P(h),F="Array"==t&&O.entries||M;if(F&&(E=c(F.call(new e)),E!==Object.prototype&&E.next&&(a||c(E)===w||(d?d(E,w):l(E[k])||f(E,k,C)),u(E,T,!0,!0),a&&(m[T]=C))),g&&h==x&&M&&M.name!==x&&(!a&&b?p(O,"name",x):(j=!0,R=function(){return o(M,this)})),h)if(A={values:P(x),keys:v?R:P(_),entries:P(S)},L)for(q in A)(y||j||!(q in O))&&f(O,q,A[q]);else r({target:t,proto:!0,forced:y||j},A);return a&&!L||O[k]===R||f(O,k,R,{name:h}),m[t]=R,A}},4133:(e,t,n)=>{var r=n(8814);e.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},1657:(e,t,n)=>{var r=n(3834),o=n(1419),a=r.document,i=o(a)&&o(a.createElement);e.exports=function(e){return i?a.createElement(e):{}}},5243:e=>{e.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},210:(e,t,n)=>{var r=n(1657),o=r("span").classList,a=o&&o.constructor&&o.constructor.prototype;e.exports=a===Object.prototype?void 0:a},259:(e,t,n)=>{var r=n(322),o=r.match(/firefox\/(\d+)/i);e.exports=!!o&&+o[1]},1280:(e,t,n)=>{var r=n(322);e.exports=/MSIE|Trident/.test(r)},322:(e,t,n)=>{var r=n(7859);e.exports=r("navigator","userAgent")||""},1418:(e,t,n)=>{var r,o,a=n(3834),i=n(322),l=a.process,s=a.Deno,c=l&&l.versions||s&&s.version,d=c&&c.v8;d&&(r=d.split("."),o=r[0]>0&&r[0]<4?1:+(r[0]+r[1])),!o&&i&&(r=i.match(/Edge\/(\d+)/),(!r||r[1]>=74)&&(r=i.match(/Chrome\/(\d+)/),r&&(o=+r[1]))),e.exports=o},7433:(e,t,n)=>{var r=n(322),o=r.match(/AppleWebKit\/(\d+)\./);e.exports=!!o&&+o[1]},203:e=>{e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},9277:(e,t,n)=>{var r=n(8814),o=n(3386);e.exports=!r((function(){var e=Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",o(1,7)),7!==e.stack)}))},6943:(e,t,n)=>{var r=n(3834),o=n(863).f,a=n(4722),i=n(4076),l=n(4650),s=n(7366),c=n(2764);e.exports=function(e,t){var n,d,u,p,f,h,m=e.target,v=e.global,g=e.stat;if(d=v?r:g?r[m]||l(m,{}):(r[m]||{}).prototype,d)for(u in t){if(f=t[u],e.noTargetGet?(h=o(d,u),p=h&&h.value):p=d[u],n=c(v?u:m+(g?".":"#")+u,e.forced),!n&&void 0!==p){if(typeof f==typeof p)continue;s(f,p)}(e.sham||p&&p.sham)&&a(f,"sham",!0),i(d,u,f,e)}}},8814:e=>{e.exports=function(e){try{return!!e()}catch(t){return!0}}},3218:(e,t,n)=>{"use strict";n(1476);var r=n(1636),o=n(4076),a=n(738),i=n(8814),l=n(4103),s=n(4722),c=l("species"),d=RegExp.prototype;e.exports=function(e,t,n,u){var p=l(e),f=!i((function(){var t={};return t[p]=function(){return 7},7!=""[e](t)})),h=f&&!i((function(){var t=!1,n=/a/;return"split"===e&&(n={},n.constructor={},n.constructor[c]=function(){return n},n.flags="",n[p]=/./[p]),n.exec=function(){return t=!0,null},n[p](""),!t}));if(!f||!h||n){var m=r(/./[p]),v=t(p,""[e],(function(e,t,n,o,i){var l=r(e),s=t.exec;return s===a||s===d.exec?f&&!i?{done:!0,value:m(t,n,o)}:{done:!0,value:l(n,t,o)}:{done:!1}}));o(String.prototype,e,v[0]),o(d,p,v[1])}u&&s(d[p],"sham",!0)}},6112:(e,t,n)=>{var r=n(9793),o=Function.prototype,a=o.apply,i=o.call;e.exports="object"==typeof Reflect&&Reflect.apply||(r?i.bind(a):function(){return i.apply(a,arguments)})},6158:(e,t,n)=>{var r=n(1636),o=n(8762),a=n(9793),i=r(r.bind);e.exports=function(e,t){return o(e),void 0===t?e:a?i(e,t):function(){return e.apply(t,arguments)}}},9793:(e,t,n)=>{var r=n(8814);e.exports=!r((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},6654:(e,t,n)=>{var r=n(9793),o=Function.prototype.call;e.exports=r?o.bind(o):function(){return o.apply(o,arguments)}},9104:(e,t,n)=>{var r=n(4133),o=n(2924),a=Function.prototype,i=r&&Object.getOwnPropertyDescriptor,l=o(a,"name"),s=l&&"something"===function(){}.name,c=l&&(!r||r&&i(a,"name").configurable);e.exports={EXISTS:l,PROPER:s,CONFIGURABLE:c}},1636:(e,t,n)=>{var r=n(9793),o=Function.prototype,a=o.bind,i=o.call,l=r&&a.bind(i,i);e.exports=r?function(e){return e&&l(e)}:function(e){return e&&function(){return i.apply(e,arguments)}}},7859:(e,t,n)=>{var r=n(3834),o=n(6107),a=function(e){return o(e)?e:void 0};e.exports=function(e,t){return arguments.length<2?a(r[e]):r[e]&&r[e][t]}},3395:(e,t,n)=>{var r=n(4239),o=n(7689),a=n(1366),i=n(4103),l=i("iterator");e.exports=function(e){if(void 0!=e)return o(e,l)||o(e,"@@iterator")||a[r(e)]}},4021:(e,t,n)=>{var r=n(3834),o=n(6654),a=n(8762),i=n(616),l=n(7545),s=n(3395),c=r.TypeError;e.exports=function(e,t){var n=arguments.length<2?s(e):t;if(a(n))return i(o(n,e));throw c(l(e)+" is not iterable")}},7689:(e,t,n)=>{var r=n(8762);e.exports=function(e,t){var n=e[t];return null==n?void 0:r(n)}},3075:(e,t,n)=>{var r=n(1636),o=n(8332),a=Math.floor,i=r("".charAt),l=r("".replace),s=r("".slice),c=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,d=/\$([$&'`]|\d{1,2})/g;e.exports=function(e,t,n,r,u,p){var f=n+e.length,h=r.length,m=d;return void 0!==u&&(u=o(u),m=c),l(p,m,(function(o,l){var c;switch(i(l,0)){case"$":return"$";case"&":return e;case"`":return s(t,0,n);case"'":return s(t,f);case"<":c=u[s(l,1,-1)];break;default:var d=+l;if(0===d)return o;if(d>h){var p=a(d/10);return 0===p?o:p<=h?void 0===r[p-1]?i(l,1):r[p-1]+i(l,1):o}c=r[d-1]}return void 0===c?"":c}))}},3834:(e,t,n)=>{var r=function(e){return e&&e.Math==Math&&e};e.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof n.g&&n.g)||function(){return this}()||Function("return this")()},2924:(e,t,n)=>{var r=n(1636),o=n(8332),a=r({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return a(o(e),t)}},1999:e=>{e.exports={}},6052:(e,t,n)=>{var r=n(7859);e.exports=r("document","documentElement")},6335:(e,t,n)=>{var r=n(4133),o=n(8814),a=n(1657);e.exports=!r&&!o((function(){return 7!=Object.defineProperty(a("div"),"a",{get:function(){return 7}}).a}))},9798:(e,t,n)=>{var r=n(3834),o=r.Array,a=Math.abs,i=Math.pow,l=Math.floor,s=Math.log,c=Math.LN2,d=function(e,t,n){var r,d,u,p=o(n),f=8*n-t-1,h=(1<<f)-1,m=h>>1,v=23===t?i(2,-24)-i(2,-77):0,g=e<0||0===e&&1/e<0?1:0,b=0;e=a(e),e!=e||e===1/0?(d=e!=e?1:0,r=h):(r=l(s(e)/c),u=i(2,-r),e*u<1&&(r--,u*=2),e+=r+m>=1?v/u:v*i(2,1-m),e*u>=2&&(r++,u/=2),r+m>=h?(d=0,r=h):r+m>=1?(d=(e*u-1)*i(2,t),r+=m):(d=e*i(2,m-1)*i(2,t),r=0));while(t>=8)p[b++]=255&d,d/=256,t-=8;r=r<<t|d,f+=t;while(f>0)p[b++]=255&r,r/=256,f-=8;return p[--b]|=128*g,p},u=function(e,t){var n,r=e.length,o=8*r-t-1,a=(1<<o)-1,l=a>>1,s=o-7,c=r-1,d=e[c--],u=127&d;d>>=7;while(s>0)u=256*u+e[c--],s-=8;n=u&(1<<-s)-1,u>>=-s,s+=t;while(s>0)n=256*n+e[c--],s-=8;if(0===u)u=1-l;else{if(u===a)return n?NaN:d?-1/0:1/0;n+=i(2,t),u-=l}return(d?-1:1)*n*i(2,u-t)};e.exports={pack:d,unpack:u}},3972:(e,t,n)=>{var r=n(3834),o=n(1636),a=n(8814),i=n(6749),l=r.Object,s=o("".split);e.exports=a((function(){return!l("z").propertyIsEnumerable(0)}))?function(e){return"String"==i(e)?s(e,""):l(e)}:l},2511:(e,t,n)=>{var r=n(6107),o=n(1419),a=n(6534);e.exports=function(e,t,n){var i,l;return a&&r(i=t.constructor)&&i!==n&&o(l=i.prototype)&&l!==n.prototype&&a(e,l),e}},6461:(e,t,n)=>{var r=n(1636),o=n(6107),a=n(6081),i=r(Function.toString);o(a.inspectSource)||(a.inspectSource=function(e){return i(e)}),e.exports=a.inspectSource},6270:(e,t,n)=>{var r=n(1419),o=n(4722);e.exports=function(e,t){r(t)&&"cause"in t&&o(e,"cause",t.cause)}},780:(e,t,n)=>{var r,o,a,i=n(4825),l=n(3834),s=n(1636),c=n(1419),d=n(4722),u=n(2924),p=n(6081),f=n(5315),h=n(1999),m="Object already initialized",v=l.TypeError,g=l.WeakMap,b=function(e){return a(e)?o(e):r(e,{})},w=function(e){return function(t){var n;if(!c(t)||(n=o(t)).type!==e)throw v("Incompatible receiver, "+e+" required");return n}};if(i||p.state){var y=p.state||(p.state=new g),k=s(y.get),_=s(y.has),x=s(y.set);r=function(e,t){if(_(y,e))throw new v(m);return t.facade=e,x(y,e,t),t},o=function(e){return k(y,e)||{}},a=function(e){return _(y,e)}}else{var S=f("state");h[S]=!0,r=function(e,t){if(u(e,S))throw new v(m);return t.facade=e,d(e,S,t),t},o=function(e){return u(e,S)?e[S]:{}},a=function(e){return u(e,S)}}e.exports={set:r,get:o,has:a,enforce:b,getterFor:w}},5712:(e,t,n)=>{var r=n(4103),o=n(1366),a=r("iterator"),i=Array.prototype;e.exports=function(e){return void 0!==e&&(o.Array===e||i[a]===e)}},6555:(e,t,n)=>{var r=n(6749);e.exports=Array.isArray||function(e){return"Array"==r(e)}},6107:e=>{e.exports=function(e){return"function"==typeof e}},9627:(e,t,n)=>{var r=n(1636),o=n(8814),a=n(6107),i=n(4239),l=n(7859),s=n(6461),c=function(){},d=[],u=l("Reflect","construct"),p=/^\s*(?:class|function)\b/,f=r(p.exec),h=!p.exec(c),m=function(e){if(!a(e))return!1;try{return u(c,d,e),!0}catch(t){return!1}},v=function(e){if(!a(e))return!1;switch(i(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return h||!!f(p,s(e))}catch(t){return!0}};v.sham=!0,e.exports=!u||o((function(){var e;return m(m.call)||!m(Object)||!m((function(){e=!0}))||e}))?v:m},2764:(e,t,n)=>{var r=n(8814),o=n(6107),a=/#|\.prototype\./,i=function(e,t){var n=s[l(e)];return n==d||n!=c&&(o(t)?r(t):!!t)},l=i.normalize=function(e){return String(e).replace(a,".").toLowerCase()},s=i.data={},c=i.NATIVE="N",d=i.POLYFILL="P";e.exports=i},3903:(e,t,n)=>{var r=n(1419),o=Math.floor;e.exports=Number.isInteger||function(e){return!r(e)&&isFinite(e)&&o(e)===e}},1419:(e,t,n)=>{var r=n(6107);e.exports=function(e){return"object"==typeof e?null!==e:r(e)}},200:e=>{e.exports=!1},1637:(e,t,n)=>{var r=n(3834),o=n(7859),a=n(6107),i=n(6123),l=n(49),s=r.Object;e.exports=l?function(e){return"symbol"==typeof e}:function(e){var t=o("Symbol");return a(t)&&i(t.prototype,s(e))}},4829:(e,t,n)=>{var r=n(6654),o=n(616),a=n(7689);e.exports=function(e,t,n){var i,l;o(e);try{if(i=a(e,"return"),!i){if("throw"===t)throw n;return n}i=r(i,e)}catch(s){l=!0,i=s}if("throw"===t)throw n;if(l)throw i;return o(i),n}},619:(e,t,n)=>{"use strict";var r,o,a,i=n(8814),l=n(6107),s=n(5267),c=n(7886),d=n(4076),u=n(4103),p=n(200),f=u("iterator"),h=!1;[].keys&&(a=[].keys(),"next"in a?(o=c(c(a)),o!==Object.prototype&&(r=o)):h=!0);var m=void 0==r||i((function(){var e={};return r[f].call(e)!==e}));m?r={}:p&&(r=s(r)),l(r[f])||d(r,f,(function(){return this})),e.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:h}},1366:e=>{e.exports={}},8600:(e,t,n)=>{var r=n(7302);e.exports=function(e){return r(e.length)}},2358:(e,t,n)=>{var r=n(8814),o=n(6107),a=n(2924),i=n(1012).f,l=n(9104).CONFIGURABLE,s=n(6461),c=n(780),d=c.enforce,u=c.get,p=!r((function(){return 8!==i((function(){}),"length",{value:8}).length})),f=String(String).split("String"),h=e.exports=function(e,t,n){"Symbol("===String(t).slice(0,7)&&(t="["+String(t).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!a(e,"name")||l&&e.name!==t)&&i(e,"name",{value:t,configurable:!0}),p&&n&&a(n,"arity")&&e.length!==n.arity&&i(e,"length",{value:n.arity});var r=d(e);return a(r,"source")||(r.source=f.join("string"==typeof t?t:"")),e};Function.prototype.toString=h((function(){return o(this)&&u(this).source||s(this)}),"toString")},1368:(e,t,n)=>{var r=n(1418),o=n(8814);e.exports=!!Object.getOwnPropertySymbols&&!o((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},211:(e,t,n)=>{var r=n(8814),o=n(4103),a=n(200),i=o("iterator");e.exports=!r((function(){var e=new URL("b?a=1&b=2&c=3","http://a"),t=e.searchParams,n="";return e.pathname="c%20d",t.forEach((function(e,r){t["delete"]("b"),n+=r+e})),a&&!e.toJSON||!t.sort||"http://a/c%20d?a=1&c=3"!==e.href||"3"!==t.get("c")||"a=1"!==String(new URLSearchParams("?a=1"))||!t[i]||"a"!==new URL("https://a@b").username||"b"!==new URLSearchParams(new URLSearchParams("a=b")).get("a")||"xn--e1aybc"!==new URL("http://тест").host||"#%D0%B1"!==new URL("http://a#б").hash||"a1c3"!==n||"x"!==new URL("http://x",void 0).host}))},4825:(e,t,n)=>{var r=n(3834),o=n(6107),a=n(6461),i=r.WeakMap;e.exports=o(i)&&/native code/.test(a(i))},1356:(e,t,n)=>{var r=n(6975);e.exports=function(e,t){return void 0===e?arguments.length<2?"":t:r(e)}},9804:(e,t,n)=>{"use strict";var r=n(4133),o=n(1636),a=n(6654),i=n(8814),l=n(4315),s=n(1996),c=n(8068),d=n(8332),u=n(3972),p=Object.assign,f=Object.defineProperty,h=o([].concat);e.exports=!p||i((function(){if(r&&1!==p({b:1},p(f({},"a",{enumerable:!0,get:function(){f(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},t={},n=Symbol(),o="abcdefghijklmnopqrst";return e[n]=7,o.split("").forEach((function(e){t[e]=e})),7!=p({},e)[n]||l(p({},t)).join("")!=o}))?function(e,t){var n=d(e),o=arguments.length,i=1,p=s.f,f=c.f;while(o>i){var m,v=u(arguments[i++]),g=p?h(l(v),p(v)):l(v),b=g.length,w=0;while(b>w)m=g[w++],r&&!a(f,v,m)||(n[m]=v[m])}return n}:p},5267:(e,t,n)=>{var r,o=n(616),a=n(6029),i=n(203),l=n(1999),s=n(6052),c=n(1657),d=n(5315),u=">",p="<",f="prototype",h="script",m=d("IE_PROTO"),v=function(){},g=function(e){return p+h+u+e+p+"/"+h+u},b=function(e){e.write(g("")),e.close();var t=e.parentWindow.Object;return e=null,t},w=function(){var e,t=c("iframe"),n="java"+h+":";return t.style.display="none",s.appendChild(t),t.src=String(n),e=t.contentWindow.document,e.open(),e.write(g("document.F=Object")),e.close(),e.F},y=function(){try{r=new ActiveXObject("htmlfile")}catch(t){}y="undefined"!=typeof document?document.domain&&r?b(r):w():b(r);var e=i.length;while(e--)delete y[f][i[e]];return y()};l[m]=!0,e.exports=Object.create||function(e,t){var n;return null!==e?(v[f]=o(e),n=new v,v[f]=null,n[m]=e):n=y(),void 0===t?n:a.f(n,t)}},6029:(e,t,n)=>{var r=n(4133),o=n(64),a=n(1012),i=n(616),l=n(7447),s=n(4315);t.f=r&&!o?Object.defineProperties:function(e,t){i(e);var n,r=l(t),o=s(t),c=o.length,d=0;while(c>d)a.f(e,n=o[d++],r[n]);return e}},1012:(e,t,n)=>{var r=n(3834),o=n(4133),a=n(6335),i=n(64),l=n(616),s=n(1017),c=r.TypeError,d=Object.defineProperty,u=Object.getOwnPropertyDescriptor,p="enumerable",f="configurable",h="writable";t.f=o?i?function(e,t,n){if(l(e),t=s(t),l(n),"function"===typeof e&&"prototype"===t&&"value"in n&&h in n&&!n[h]){var r=u(e,t);r&&r[h]&&(e[t]=n.value,n={configurable:f in n?n[f]:r[f],enumerable:p in n?n[p]:r[p],writable:!1})}return d(e,t,n)}:d:function(e,t,n){if(l(e),t=s(t),l(n),a)try{return d(e,t,n)}catch(r){}if("get"in n||"set"in n)throw c("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},863:(e,t,n)=>{var r=n(4133),o=n(6654),a=n(8068),i=n(3386),l=n(7447),s=n(1017),c=n(2924),d=n(6335),u=Object.getOwnPropertyDescriptor;t.f=r?u:function(e,t){if(e=l(e),t=s(t),d)try{return u(e,t)}catch(n){}if(c(e,t))return i(!o(a.f,e,t),e[t])}},3450:(e,t,n)=>{var r=n(6682),o=n(203),a=o.concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,a)}},1996:(e,t)=>{t.f=Object.getOwnPropertySymbols},7886:(e,t,n)=>{var r=n(3834),o=n(2924),a=n(6107),i=n(8332),l=n(5315),s=n(911),c=l("IE_PROTO"),d=r.Object,u=d.prototype;e.exports=s?d.getPrototypeOf:function(e){var t=i(e);if(o(t,c))return t[c];var n=t.constructor;return a(n)&&t instanceof n?n.prototype:t instanceof d?u:null}},6123:(e,t,n)=>{var r=n(1636);e.exports=r({}.isPrototypeOf)},6682:(e,t,n)=>{var r=n(1636),o=n(2924),a=n(7447),i=n(7714).indexOf,l=n(1999),s=r([].push);e.exports=function(e,t){var n,r=a(e),c=0,d=[];for(n in r)!o(l,n)&&o(r,n)&&s(d,n);while(t.length>c)o(r,n=t[c++])&&(~i(d,n)||s(d,n));return d}},4315:(e,t,n)=>{var r=n(6682),o=n(203);e.exports=Object.keys||function(e){return r(e,o)}},8068:(e,t)=>{"use strict";var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,o=r&&!n.call({1:2},1);t.f=o?function(e){var t=r(this,e);return!!t&&t.enumerable}:n},6534:(e,t,n)=>{var r=n(1636),o=n(616),a=n(9220);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{e=r(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set),e(n,[]),t=n instanceof Array}catch(i){}return function(n,r){return o(n),a(r),t?e(n,r):n.__proto__=r,n}}():void 0)},9370:(e,t,n)=>{var r=n(3834),o=n(6654),a=n(6107),i=n(1419),l=r.TypeError;e.exports=function(e,t){var n,r;if("string"===t&&a(n=e.toString)&&!i(r=o(n,e)))return r;if(a(n=e.valueOf)&&!i(r=o(n,e)))return r;if("string"!==t&&a(n=e.toString)&&!i(r=o(n,e)))return r;throw l("Can't convert object to primitive value")}},1240:(e,t,n)=>{var r=n(7859),o=n(1636),a=n(3450),i=n(1996),l=n(616),s=o([].concat);e.exports=r("Reflect","ownKeys")||function(e){var t=a.f(l(e)),n=i.f;return n?s(t,n(e)):t}},4569:(e,t,n)=>{var r=n(1012).f;e.exports=function(e,t,n){n in e||r(e,n,{configurable:!0,get:function(){return t[n]},set:function(e){t[n]=e}})}},3808:(e,t,n)=>{var r=n(3834),o=n(6654),a=n(616),i=n(6107),l=n(6749),s=n(738),c=r.TypeError;e.exports=function(e,t){var n=e.exec;if(i(n)){var r=o(n,e,t);return null!==r&&a(r),r}if("RegExp"===l(e))return o(s,e,t);throw c("RegExp#exec called on incompatible receiver")}},738:(e,t,n)=>{"use strict";var r=n(6654),o=n(1636),a=n(6975),i=n(9592),l=n(9165),s=n(8850),c=n(5267),d=n(780).get,u=n(3425),p=n(10),f=s("native-string-replace",String.prototype.replace),h=RegExp.prototype.exec,m=h,v=o("".charAt),g=o("".indexOf),b=o("".replace),w=o("".slice),y=function(){var e=/a/,t=/b*/g;return r(h,e,"a"),r(h,t,"a"),0!==e.lastIndex||0!==t.lastIndex}(),k=l.BROKEN_CARET,_=void 0!==/()??/.exec("")[1],x=y||_||k||u||p;x&&(m=function(e){var t,n,o,l,s,u,p,x=this,S=d(x),C=a(e),L=S.raw;if(L)return L.lastIndex=x.lastIndex,t=r(m,L,C),x.lastIndex=L.lastIndex,t;var E=S.groups,A=k&&x.sticky,q=r(i,x),P=x.source,T=0,j=C;if(A&&(q=b(q,"y",""),-1===g(q,"g")&&(q+="g"),j=w(C,x.lastIndex),x.lastIndex>0&&(!x.multiline||x.multiline&&"\n"!==v(C,x.lastIndex-1))&&(P="(?: "+P+")",j=" "+j,T++),n=new RegExp("^(?:"+P+")",q)),_&&(n=new RegExp("^"+P+"$(?!\\s)",q)),y&&(o=x.lastIndex),l=r(h,A?n:x,j),A?l?(l.input=w(l.input,T),l[0]=w(l[0],T),l.index=x.lastIndex,x.lastIndex+=l[0].length):x.lastIndex=0:y&&l&&(x.lastIndex=x.global?l.index+l[0].length:o),_&&l&&l.length>1&&r(f,l[0],n,(function(){for(s=1;s<arguments.length-2;s++)void 0===arguments[s]&&(l[s]=void 0)})),l&&E)for(l.groups=u=c(null),s=0;s<E.length;s++)p=E[s],u[p[0]]=l[p[1]];return l}),e.exports=m},9592:(e,t,n)=>{"use strict";var r=n(616);e.exports=function(){var e=r(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t}},9165:(e,t,n)=>{var r=n(8814),o=n(3834),a=o.RegExp,i=r((function(){var e=a("a","y");return e.lastIndex=2,null!=e.exec("abcd")})),l=i||r((function(){return!a("a","y").sticky})),s=i||r((function(){var e=a("^r","gy");return e.lastIndex=2,null!=e.exec("str")}));e.exports={BROKEN_CARET:s,MISSED_STICKY:l,UNSUPPORTED_Y:i}},3425:(e,t,n)=>{var r=n(8814),o=n(3834),a=o.RegExp;e.exports=r((function(){var e=a(".","s");return!(e.dotAll&&e.exec("\n")&&"s"===e.flags)}))},10:(e,t,n)=>{var r=n(8814),o=n(3834),a=o.RegExp;e.exports=r((function(){var e=a("(?<a>b)","g");return"b"!==e.exec("b").groups.a||"bc"!=="b".replace(e,"$<a>c")}))},5177:(e,t,n)=>{var r=n(3834),o=r.TypeError;e.exports=function(e){if(void 0==e)throw o("Can't call method on "+e);return e}},4650:(e,t,n)=>{var r=n(3834),o=Object.defineProperty;e.exports=function(e,t){try{o(r,e,{value:t,configurable:!0,writable:!0})}catch(n){r[e]=t}return t}},7104:(e,t,n)=>{"use strict";var r=n(7859),o=n(1012),a=n(4103),i=n(4133),l=a("species");e.exports=function(e){var t=r(e),n=o.f;i&&t&&!t[l]&&n(t,l,{configurable:!0,get:function(){return this}})}},2365:(e,t,n)=>{var r=n(1012).f,o=n(2924),a=n(4103),i=a("toStringTag");e.exports=function(e,t,n){e&&!n&&(e=e.prototype),e&&!o(e,i)&&r(e,i,{configurable:!0,value:t})}},5315:(e,t,n)=>{var r=n(8850),o=n(3965),a=r("keys");e.exports=function(e){return a[e]||(a[e]=o(e))}},6081:(e,t,n)=>{var r=n(3834),o=n(4650),a="__core-js_shared__",i=r[a]||o(a,{});e.exports=i},8850:(e,t,n)=>{var r=n(200),o=n(6081);(e.exports=function(e,t){return o[e]||(o[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.22.4",mode:r?"pure":"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.22.4/LICENSE",source:"https://github.com/zloirock/core-js"})},6823:(e,t,n)=>{var r=n(1636),o=n(6675),a=n(6975),i=n(5177),l=r("".charAt),s=r("".charCodeAt),c=r("".slice),d=function(e){return function(t,n){var r,d,u=a(i(t)),p=o(n),f=u.length;return p<0||p>=f?e?"":void 0:(r=s(u,p),r<55296||r>56319||p+1===f||(d=s(u,p+1))<56320||d>57343?e?l(u,p):r:e?c(u,p,p+2):d-56320+(r-55296<<10)+65536)}};e.exports={codeAt:d(!1),charAt:d(!0)}},2552:(e,t,n)=>{"use strict";var r=n(3834),o=n(1636),a=**********,i=36,l=1,s=26,c=38,d=700,u=72,p=128,f="-",h=/[^\0-\u007E]/,m=/[.\u3002\uFF0E\uFF61]/g,v="Overflow: input needs wider integers to process",g=i-l,b=r.RangeError,w=o(m.exec),y=Math.floor,k=String.fromCharCode,_=o("".charCodeAt),x=o([].join),S=o([].push),C=o("".replace),L=o("".split),E=o("".toLowerCase),A=function(e){var t=[],n=0,r=e.length;while(n<r){var o=_(e,n++);if(o>=55296&&o<=56319&&n<r){var a=_(e,n++);56320==(64512&a)?S(t,((1023&o)<<10)+(1023&a)+65536):(S(t,o),n--)}else S(t,o)}return t},q=function(e){return e+22+75*(e<26)},P=function(e,t,n){var r=0;e=n?y(e/d):e>>1,e+=y(e/t);while(e>g*s>>1)e=y(e/g),r+=i;return y(r+(g+1)*e/(e+c))},T=function(e){var t=[];e=A(e);var n,r,o=e.length,c=p,d=0,h=u;for(n=0;n<e.length;n++)r=e[n],r<128&&S(t,k(r));var m=t.length,g=m;m&&S(t,f);while(g<o){var w=a;for(n=0;n<e.length;n++)r=e[n],r>=c&&r<w&&(w=r);var _=g+1;if(w-c>y((a-d)/_))throw b(v);for(d+=(w-c)*_,c=w,n=0;n<e.length;n++){if(r=e[n],r<c&&++d>a)throw b(v);if(r==c){var C=d,L=i;while(1){var E=L<=h?l:L>=h+s?s:L-h;if(C<E)break;var T=C-E,j=i-E;S(t,k(q(E+T%j))),C=y(T/j),L+=i}S(t,k(q(C))),h=P(d,_,g==m),d=0,g++}}d++,c++}return x(t,"")};e.exports=function(e){var t,n,r=[],o=L(C(E(e),m,"."),".");for(t=0;t<o.length;t++)n=o[t],S(r,w(h,n)?"xn--"+T(n):n);return x(r,".")}},2661:(e,t,n)=>{var r=n(6675),o=Math.max,a=Math.min;e.exports=function(e,t){var n=r(e);return n<0?o(n+t,0):a(n,t)}},4686:(e,t,n)=>{var r=n(3834),o=n(6675),a=n(7302),i=r.RangeError;e.exports=function(e){if(void 0===e)return 0;var t=o(e),n=a(t);if(t!==n)throw i("Wrong length or index");return n}},7447:(e,t,n)=>{var r=n(3972),o=n(5177);e.exports=function(e){return r(o(e))}},6675:e=>{var t=Math.ceil,n=Math.floor;e.exports=function(e){var r=+e;return r!==r||0===r?0:(r>0?n:t)(r)}},7302:(e,t,n)=>{var r=n(6675),o=Math.min;e.exports=function(e){return e>0?o(r(e),9007199254740991):0}},8332:(e,t,n)=>{var r=n(3834),o=n(5177),a=r.Object;e.exports=function(e){return a(o(e))}},4084:(e,t,n)=>{var r=n(3834),o=n(859),a=r.RangeError;e.exports=function(e,t){var n=o(e);if(n%t)throw a("Wrong offset");return n}},859:(e,t,n)=>{var r=n(3834),o=n(6675),a=r.RangeError;e.exports=function(e){var t=o(e);if(t<0)throw a("The argument can't be less than 0");return t}},4384:(e,t,n)=>{var r=n(3834),o=n(6654),a=n(1419),i=n(1637),l=n(7689),s=n(9370),c=n(4103),d=r.TypeError,u=c("toPrimitive");e.exports=function(e,t){if(!a(e)||i(e))return e;var n,r=l(e,u);if(r){if(void 0===t&&(t="default"),n=o(r,e,t),!a(n)||i(n))return n;throw d("Can't convert object to primitive value")}return void 0===t&&(t="number"),s(e,t)}},1017:(e,t,n)=>{var r=n(4384),o=n(1637);e.exports=function(e){var t=r(e,"string");return o(t)?t:t+""}},4130:(e,t,n)=>{var r=n(4103),o=r("toStringTag"),a={};a[o]="z",e.exports="[object z]"===String(a)},6975:(e,t,n)=>{var r=n(3834),o=n(4239),a=r.String;e.exports=function(e){if("Symbol"===o(e))throw TypeError("Cannot convert a Symbol value to a string");return a(e)}},7545:(e,t,n)=>{var r=n(3834),o=r.String;e.exports=function(e){try{return o(e)}catch(t){return"Object"}}},8532:(e,t,n)=>{"use strict";var r=n(6943),o=n(3834),a=n(6654),i=n(4133),l=n(5136),s=n(8086),c=n(2248),d=n(8406),u=n(3386),p=n(4722),f=n(3903),h=n(7302),m=n(4686),v=n(4084),g=n(1017),b=n(2924),w=n(4239),y=n(1419),k=n(1637),_=n(5267),x=n(6123),S=n(6534),C=n(3450).f,L=n(1157),E=n(9226).forEach,A=n(7104),q=n(1012),P=n(863),T=n(780),j=n(2511),O=T.get,M=T.set,R=q.f,F=P.f,H=Math.round,z=o.RangeError,B=c.ArrayBuffer,I=B.prototype,V=c.DataView,$=s.NATIVE_ARRAY_BUFFER_VIEWS,N=s.TYPED_ARRAY_CONSTRUCTOR,U=s.TYPED_ARRAY_TAG,D=s.TypedArray,Z=s.TypedArrayPrototype,W=s.aTypedArrayConstructor,K=s.isTypedArray,G="BYTES_PER_ELEMENT",J="Wrong length",Y=function(e,t){W(e);var n=0,r=t.length,o=new e(r);while(r>n)o[n]=t[n++];return o},Q=function(e,t){R(e,t,{get:function(){return O(this)[t]}})},X=function(e){var t;return x(I,e)||"ArrayBuffer"==(t=w(e))||"SharedArrayBuffer"==t},ee=function(e,t){return K(e)&&!k(t)&&t in e&&f(+t)&&t>=0},te=function(e,t){return t=g(t),ee(e,t)?u(2,e[t]):F(e,t)},ne=function(e,t,n){return t=g(t),!(ee(e,t)&&y(n)&&b(n,"value"))||b(n,"get")||b(n,"set")||n.configurable||b(n,"writable")&&!n.writable||b(n,"enumerable")&&!n.enumerable?R(e,t,n):(e[t]=n.value,e)};i?($||(P.f=te,q.f=ne,Q(Z,"buffer"),Q(Z,"byteOffset"),Q(Z,"byteLength"),Q(Z,"length")),r({target:"Object",stat:!0,forced:!$},{getOwnPropertyDescriptor:te,defineProperty:ne}),e.exports=function(e,t,n){var i=e.match(/\d+$/)[0]/8,s=e+(n?"Clamped":"")+"Array",c="get"+e,u="set"+e,f=o[s],g=f,b=g&&g.prototype,w={},k=function(e,t){var n=O(e);return n.view[c](t*i+n.byteOffset,!0)},x=function(e,t,r){var o=O(e);n&&(r=(r=H(r))<0?0:r>255?255:255&r),o.view[u](t*i+o.byteOffset,r,!0)},q=function(e,t){R(e,t,{get:function(){return k(this,t)},set:function(e){return x(this,t,e)},enumerable:!0})};$?l&&(g=t((function(e,t,n,r){return d(e,b),j(function(){return y(t)?X(t)?void 0!==r?new f(t,v(n,i),r):void 0!==n?new f(t,v(n,i)):new f(t):K(t)?Y(g,t):a(L,g,t):new f(m(t))}(),e,g)})),S&&S(g,D),E(C(f),(function(e){e in g||p(g,e,f[e])})),g.prototype=b):(g=t((function(e,t,n,r){d(e,b);var o,l,s,c=0,u=0;if(y(t)){if(!X(t))return K(t)?Y(g,t):a(L,g,t);o=t,u=v(n,i);var p=t.byteLength;if(void 0===r){if(p%i)throw z(J);if(l=p-u,l<0)throw z(J)}else if(l=h(r)*i,l+u>p)throw z(J);s=l/i}else s=m(t),l=s*i,o=new B(l);M(e,{buffer:o,byteOffset:u,byteLength:l,length:s,view:new V(o)});while(c<s)q(e,c++)})),S&&S(g,D),b=g.prototype=_(Z)),b.constructor!==g&&p(b,"constructor",g),p(b,N,g),U&&p(b,U,s),w[s]=g,r({global:!0,forced:g!=f,sham:!$},w),G in g||p(g,G,i),G in b||p(b,G,i),A(s)}):e.exports=function(){}},5136:(e,t,n)=>{var r=n(3834),o=n(8814),a=n(8272),i=n(8086).NATIVE_ARRAY_BUFFER_VIEWS,l=r.ArrayBuffer,s=r.Int8Array;e.exports=!i||!o((function(){s(1)}))||!o((function(){new s(-1)}))||!a((function(e){new s,new s(null),new s(1.5),new s(e)}),!0)||o((function(){return 1!==new s(new l(2),1,void 0).length}))},1157:(e,t,n)=>{var r=n(6158),o=n(6654),a=n(9667),i=n(8332),l=n(8600),s=n(4021),c=n(3395),d=n(5712),u=n(8086).aTypedArrayConstructor;e.exports=function(e){var t,n,p,f,h,m,v=a(this),g=i(e),b=arguments.length,w=b>1?arguments[1]:void 0,y=void 0!==w,k=c(g);if(k&&!d(k)){h=s(g,k),m=h.next,g=[];while(!(f=o(m,h)).done)g.push(f.value)}for(y&&b>2&&(w=r(w,arguments[2])),n=l(g),p=new(u(v))(n),t=0;n>t;t++)p[t]=y?w(g[t],t):g[t];return p}},3965:(e,t,n)=>{var r=n(1636),o=0,a=Math.random(),i=r(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+i(++o+a,36)}},49:(e,t,n)=>{var r=n(1368);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},64:(e,t,n)=>{var r=n(4133),o=n(8814);e.exports=r&&o((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},5809:(e,t,n)=>{var r=n(3834),o=r.TypeError;e.exports=function(e,t){if(e<t)throw o("Not enough arguments");return e}},4103:(e,t,n)=>{var r=n(3834),o=n(8850),a=n(2924),i=n(3965),l=n(1368),s=n(49),c=o("wks"),d=r.Symbol,u=d&&d["for"],p=s?d:d&&d.withoutSetter||i;e.exports=function(e){if(!a(c,e)||!l&&"string"!=typeof c[e]){var t="Symbol."+e;l&&a(d,e)?c[e]=d[e]:c[e]=s&&u?u(t):p(t)}return c[e]}},8376:(e,t,n)=>{"use strict";var r=n(7859),o=n(2924),a=n(4722),i=n(6123),l=n(6534),s=n(7366),c=n(4569),d=n(2511),u=n(1356),p=n(6270),f=n(1328),h=n(9277),m=n(4133),v=n(200);e.exports=function(e,t,n,g){var b="stackTraceLimit",w=g?2:1,y=e.split("."),k=y[y.length-1],_=r.apply(null,y);if(_){var x=_.prototype;if(!v&&o(x,"cause")&&delete x.cause,!n)return _;var S=r("Error"),C=t((function(e,t){var n=u(g?t:e,void 0),r=g?new _(e):new _;return void 0!==n&&a(r,"message",n),h&&a(r,"stack",f(r.stack,2)),this&&i(x,this)&&d(r,this,C),arguments.length>w&&p(r,arguments[w]),r}));if(C.prototype=x,"Error"!==k?l?l(C,S):s(C,S,{name:!0}):m&&b in _&&(c(C,_,b),c(C,_,"prepareStackTrace")),s(C,_),!v)try{x.name!==k&&a(x,"name",k),x.constructor=C}catch(L){}return C}}},6727:(e,t,n)=>{"use strict";var r=n(6943),o=n(7714).includes,a=n(8814),i=n(5323),l=a((function(){return!Array(1).includes()}));r({target:"Array",proto:!0,forced:l},{includes:function(e){return o(this,e,arguments.length>1?arguments[1]:void 0)}}),i("includes")},8998:(e,t,n)=>{"use strict";var r=n(7447),o=n(5323),a=n(1366),i=n(780),l=n(1012).f,s=n(3532),c=n(200),d=n(4133),u="Array Iterator",p=i.set,f=i.getterFor(u);e.exports=s(Array,"Array",(function(e,t){p(this,{type:u,target:r(e),index:0,kind:t})}),(function(){var e=f(this),t=e.target,n=e.kind,r=e.index++;return!t||r>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:t[r],done:!1}:{value:[r,t[r]],done:!1}}),"values");var h=a.Arguments=a.Array;if(o("keys"),o("values"),o("entries"),!c&&d&&"values"!==h.name)try{l(h,"name",{value:"values"})}catch(m){}},6822:(e,t,n)=>{var r=n(6943),o=n(3834),a=n(6112),i=n(8376),l="WebAssembly",s=o[l],c=7!==Error("e",{cause:7}).cause,d=function(e,t){var n={};n[e]=i(e,t,c),r({global:!0,arity:1,forced:c},n)},u=function(e,t){if(s&&s[e]){var n={};n[e]=i(l+"."+e,t,c),r({target:l,stat:!0,arity:1,forced:c},n)}};d("Error",(function(e){return function(t){return a(e,this,arguments)}})),d("EvalError",(function(e){return function(t){return a(e,this,arguments)}})),d("RangeError",(function(e){return function(t){return a(e,this,arguments)}})),d("ReferenceError",(function(e){return function(t){return a(e,this,arguments)}})),d("SyntaxError",(function(e){return function(t){return a(e,this,arguments)}})),d("TypeError",(function(e){return function(t){return a(e,this,arguments)}})),d("URIError",(function(e){return function(t){return a(e,this,arguments)}})),u("CompileError",(function(e){return function(t){return a(e,this,arguments)}})),u("LinkError",(function(e){return function(t){return a(e,this,arguments)}})),u("RuntimeError",(function(e){return function(t){return a(e,this,arguments)}}))},1476:(e,t,n)=>{"use strict";var r=n(6943),o=n(738);r({target:"RegExp",proto:!0,forced:/./.exec!==o},{exec:o})},3122:(e,t,n)=>{var r=n(4133),o=n(9570),a=n(9592),i=n(8814),l=RegExp.prototype,s=r&&i((function(){return"sy"!==Object.getOwnPropertyDescriptor(l,"flags").get.call({dotAll:!0,sticky:!0})}));s&&o(l,"flags",{configurable:!0,get:a})},7280:(e,t,n)=>{"use strict";var r=n(6823).charAt,o=n(6975),a=n(780),i=n(3532),l="String Iterator",s=a.set,c=a.getterFor(l);i(String,"String",(function(e){s(this,{type:l,string:o(e),index:0})}),(function(){var e,t=c(this),n=t.string,o=t.index;return o>=n.length?{value:void 0,done:!0}:(e=r(n,o),t.index+=e.length,{value:e,done:!1})}))},8964:(e,t,n)=>{"use strict";var r=n(6112),o=n(6654),a=n(1636),i=n(3218),l=n(8814),s=n(616),c=n(6107),d=n(6675),u=n(7302),p=n(6975),f=n(5177),h=n(3366),m=n(7689),v=n(3075),g=n(3808),b=n(4103),w=b("replace"),y=Math.max,k=Math.min,_=a([].concat),x=a([].push),S=a("".indexOf),C=a("".slice),L=function(e){return void 0===e?e:String(e)},E=function(){return"$0"==="a".replace(/./,"$0")}(),A=function(){return!!/./[w]&&""===/./[w]("a","$0")}(),q=!l((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")}));i("replace",(function(e,t,n){var a=A?"$":"$0";return[function(e,n){var r=f(this),a=void 0==e?void 0:m(e,w);return a?o(a,e,r,n):o(t,p(r),e,n)},function(e,o){var i=s(this),l=p(e);if("string"==typeof o&&-1===S(o,a)&&-1===S(o,"$<")){var f=n(t,i,l,o);if(f.done)return f.value}var m=c(o);m||(o=p(o));var b=i.global;if(b){var w=i.unicode;i.lastIndex=0}var E=[];while(1){var A=g(i,l);if(null===A)break;if(x(E,A),!b)break;var q=p(A[0]);""===q&&(i.lastIndex=h(l,u(i.lastIndex),w))}for(var P="",T=0,j=0;j<E.length;j++){A=E[j];for(var O=p(A[0]),M=y(k(d(A.index),l.length),0),R=[],F=1;F<A.length;F++)x(R,L(A[F]));var H=A.groups;if(m){var z=_([O],R,M,l);void 0!==H&&x(z,H);var B=p(r(o,void 0,z))}else B=v(O,l,M,R,H,o);M>=T&&(P+=C(l,T,M)+B,T=M+O.length)}return P+C(l,T)}]}),!q||!E||A)},5231:(e,t,n)=>{"use strict";var r=n(8086),o=n(8600),a=n(6675),i=r.aTypedArray,l=r.exportTypedArrayMethod;l("at",(function(e){var t=i(this),n=o(t),r=a(e),l=r>=0?r:n+r;return l<0||l>=n?void 0:t[l]}))},7725:(e,t,n)=>{"use strict";var r=n(8086),o=n(6654),a=n(5408),i=r.aTypedArray,l=r.exportTypedArrayMethod;l("fill",(function(e){var t=arguments.length;return o(a,i(this),e,t>1?arguments[1]:void 0,t>2?arguments[2]:void 0)}))},9359:(e,t,n)=>{"use strict";var r=n(3834),o=n(6654),a=n(8086),i=n(8600),l=n(4084),s=n(8332),c=n(8814),d=r.RangeError,u=r.Int8Array,p=u&&u.prototype,f=p&&p.set,h=a.aTypedArray,m=a.exportTypedArrayMethod,v=!c((function(){var e=new Uint8ClampedArray(2);return o(f,e,{length:1,0:3},1),3!==e[1]})),g=v&&a.NATIVE_ARRAY_BUFFER_VIEWS&&c((function(){var e=new u(2);return e.set(1),e.set("2",1),0!==e[0]||2!==e[1]}));m("set",(function(e){h(this);var t=l(arguments.length>1?arguments[1]:void 0,1),n=s(e);if(v)return o(f,this,n,t);var r=this.length,a=i(n),c=0;if(a+t>r)throw d("Wrong length");while(c<a)this[t+c]=n[c++]}),!v||g)},6408:(e,t,n)=>{"use strict";var r=n(3834),o=n(1636),a=n(8814),i=n(8762),l=n(7085),s=n(8086),c=n(259),d=n(1280),u=n(1418),p=n(7433),f=s.aTypedArray,h=s.exportTypedArrayMethod,m=r.Uint16Array,v=m&&o(m.prototype.sort),g=!!v&&!(a((function(){v(new m(2),null)}))&&a((function(){v(new m(2),{})}))),b=!!v&&!a((function(){if(u)return u<74;if(c)return c<67;if(d)return!0;if(p)return p<602;var e,t,n=new m(516),r=Array(516);for(e=0;e<516;e++)t=e%4,n[e]=515-e,r[e]=e-2*t+3;for(v(n,(function(e,t){return(e/4|0)-(t/4|0)})),e=0;e<516;e++)if(n[e]!==r[e])return!0})),w=function(e){return function(t,n){return void 0!==e?+e(t,n)||0:n!==n?-1:t!==t?1:0===t&&0===n?1/t>0&&1/n<0?1:-1:t>n}};h("sort",(function(e){return void 0!==e&&i(e),b?v(this,e):l(f(this),w(e))}),!b||g)},8170:(e,t,n)=>{var r=n(8532);r("Uint8",(function(e){return function(t,n,r){return e(this,t,n,r)}}))},3667:(e,t,n)=>{"use strict";var r=n(8086),o=n(9275).findLastIndex,a=r.aTypedArray,i=r.exportTypedArrayMethod;i("findLastIndex",(function(e){return o(a(this),e,arguments.length>1?arguments[1]:void 0)}))},940:(e,t,n)=>{"use strict";var r=n(8086),o=n(9275).findLast,a=r.aTypedArray,i=r.exportTypedArrayMethod;i("findLast",(function(e){return o(a(this),e,arguments.length>1?arguments[1]:void 0)}))},702:(e,t,n)=>{var r=n(3834),o=n(5243),a=n(210),i=n(8998),l=n(4722),s=n(4103),c=s("iterator"),d=s("toStringTag"),u=i.values,p=function(e,t){if(e){if(e[c]!==u)try{l(e,c,u)}catch(r){e[c]=u}if(e[d]||l(e,d,t),o[t])for(var n in i)if(e[n]!==i[n])try{l(e,n,i[n])}catch(r){e[n]=i[n]}}};for(var f in o)p(r[f]&&r[f].prototype,f);p(a,"DOMTokenList")},8623:(e,t,n)=>{"use strict";n(8998);var r=n(6943),o=n(3834),a=n(6654),i=n(1636),l=n(4133),s=n(211),c=n(4076),d=n(2714),u=n(2365),p=n(1551),f=n(780),h=n(8406),m=n(6107),v=n(2924),g=n(6158),b=n(4239),w=n(616),y=n(1419),k=n(6975),_=n(5267),x=n(3386),S=n(4021),C=n(3395),L=n(5809),E=n(4103),A=n(7085),q=E("iterator"),P="URLSearchParams",T=P+"Iterator",j=f.set,O=f.getterFor(P),M=f.getterFor(T),R=Object.getOwnPropertyDescriptor,F=function(e){if(!l)return o[e];var t=R(o,e);return t&&t.value},H=F("fetch"),z=F("Request"),B=F("Headers"),I=z&&z.prototype,V=B&&B.prototype,$=o.RegExp,N=o.TypeError,U=o.decodeURIComponent,D=o.encodeURIComponent,Z=i("".charAt),W=i([].join),K=i([].push),G=i("".replace),J=i([].shift),Y=i([].splice),Q=i("".split),X=i("".slice),ee=/\+/g,te=Array(4),ne=function(e){return te[e-1]||(te[e-1]=$("((?:%[\\da-f]{2}){"+e+"})","gi"))},re=function(e){try{return U(e)}catch(t){return e}},oe=function(e){var t=G(e,ee," "),n=4;try{return U(t)}catch(r){while(n)t=G(t,ne(n--),re);return t}},ae=/[!'()~]|%20/g,ie={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+"},le=function(e){return ie[e]},se=function(e){return G(D(e),ae,le)},ce=p((function(e,t){j(this,{type:T,iterator:S(O(e).entries),kind:t})}),"Iterator",(function(){var e=M(this),t=e.kind,n=e.iterator.next(),r=n.value;return n.done||(n.value="keys"===t?r.key:"values"===t?r.value:[r.key,r.value]),n}),!0),de=function(e){this.entries=[],this.url=null,void 0!==e&&(y(e)?this.parseObject(e):this.parseQuery("string"==typeof e?"?"===Z(e,0)?X(e,1):e:k(e)))};de.prototype={type:P,bindURL:function(e){this.url=e,this.update()},parseObject:function(e){var t,n,r,o,i,l,s,c=C(e);if(c){t=S(e,c),n=t.next;while(!(r=a(n,t)).done){if(o=S(w(r.value)),i=o.next,(l=a(i,o)).done||(s=a(i,o)).done||!a(i,o).done)throw N("Expected sequence with length 2");K(this.entries,{key:k(l.value),value:k(s.value)})}}else for(var d in e)v(e,d)&&K(this.entries,{key:d,value:k(e[d])})},parseQuery:function(e){if(e){var t,n,r=Q(e,"&"),o=0;while(o<r.length)t=r[o++],t.length&&(n=Q(t,"="),K(this.entries,{key:oe(J(n)),value:oe(W(n,"="))}))}},serialize:function(){var e,t=this.entries,n=[],r=0;while(r<t.length)e=t[r++],K(n,se(e.key)+"="+se(e.value));return W(n,"&")},update:function(){this.entries.length=0,this.parseQuery(this.url.query)},updateURL:function(){this.url&&this.url.update()}};var ue=function(){h(this,pe);var e=arguments.length>0?arguments[0]:void 0;j(this,new de(e))},pe=ue.prototype;if(d(pe,{append:function(e,t){L(arguments.length,2);var n=O(this);K(n.entries,{key:k(e),value:k(t)}),n.updateURL()},delete:function(e){L(arguments.length,1);var t=O(this),n=t.entries,r=k(e),o=0;while(o<n.length)n[o].key===r?Y(n,o,1):o++;t.updateURL()},get:function(e){L(arguments.length,1);for(var t=O(this).entries,n=k(e),r=0;r<t.length;r++)if(t[r].key===n)return t[r].value;return null},getAll:function(e){L(arguments.length,1);for(var t=O(this).entries,n=k(e),r=[],o=0;o<t.length;o++)t[o].key===n&&K(r,t[o].value);return r},has:function(e){L(arguments.length,1);var t=O(this).entries,n=k(e),r=0;while(r<t.length)if(t[r++].key===n)return!0;return!1},set:function(e,t){L(arguments.length,1);for(var n,r=O(this),o=r.entries,a=!1,i=k(e),l=k(t),s=0;s<o.length;s++)n=o[s],n.key===i&&(a?Y(o,s--,1):(a=!0,n.value=l));a||K(o,{key:i,value:l}),r.updateURL()},sort:function(){var e=O(this);A(e.entries,(function(e,t){return e.key>t.key?1:-1})),e.updateURL()},forEach:function(e){var t,n=O(this).entries,r=g(e,arguments.length>1?arguments[1]:void 0),o=0;while(o<n.length)t=n[o++],r(t.value,t.key,this)},keys:function(){return new ce(this,"keys")},values:function(){return new ce(this,"values")},entries:function(){return new ce(this,"entries")}},{enumerable:!0}),c(pe,q,pe.entries,{name:"entries"}),c(pe,"toString",(function(){return O(this).serialize()}),{enumerable:!0}),u(ue,P),r({global:!0,forced:!s},{URLSearchParams:ue}),!s&&m(B)){var fe=i(V.has),he=i(V.set),me=function(e){if(y(e)){var t,n=e.body;if(b(n)===P)return t=e.headers?new B(e.headers):new B,fe(t,"content-type")||he(t,"content-type","application/x-www-form-urlencoded;charset=UTF-8"),_(e,{body:x(0,k(n)),headers:x(0,t)})}return e};if(m(H)&&r({global:!0,enumerable:!0,noTargetGet:!0,forced:!0},{fetch:function(e){return H(e,arguments.length>1?me(arguments[1]):{})}}),m(z)){var ve=function(e){return h(this,I),new z(e,arguments.length>1?me(arguments[1]):{})};I.constructor=ve,ve.prototype=I,r({global:!0,forced:!0,noTargetGet:!0},{Request:ve})}}e.exports={URLSearchParams:ue,getState:O}},3269:(e,t,n)=>{n(8623)},6614:(e,t,n)=>{"use strict";n(7280);var r,o=n(6943),a=n(4133),i=n(211),l=n(3834),s=n(6158),c=n(1636),d=n(4076),u=n(9570),p=n(8406),f=n(2924),h=n(9804),m=n(7508),v=n(6378),g=n(6823).codeAt,b=n(2552),w=n(6975),y=n(2365),k=n(5809),_=n(8623),x=n(780),S=x.set,C=x.getterFor("URL"),L=_.URLSearchParams,E=_.getState,A=l.URL,q=l.TypeError,P=l.parseInt,T=Math.floor,j=Math.pow,O=c("".charAt),M=c(/./.exec),R=c([].join),F=c(1..toString),H=c([].pop),z=c([].push),B=c("".replace),I=c([].shift),V=c("".split),$=c("".slice),N=c("".toLowerCase),U=c([].unshift),D="Invalid authority",Z="Invalid scheme",W="Invalid host",K="Invalid port",G=/[a-z]/i,J=/[\d+-.a-z]/i,Y=/\d/,Q=/^0x/i,X=/^[0-7]+$/,ee=/^\d+$/,te=/^[\da-f]+$/i,ne=/[\0\t\n\r #%/:<>?@[\\\]^|]/,re=/[\0\t\n\r #/:<>?@[\\\]^|]/,oe=/^[\u0000-\u0020]+|[\u0000-\u0020]+$/g,ae=/[\t\n\r]/g,ie=function(e){var t,n,r,o,a,i,l,s=V(e,".");if(s.length&&""==s[s.length-1]&&s.length--,t=s.length,t>4)return e;for(n=[],r=0;r<t;r++){if(o=s[r],""==o)return e;if(a=10,o.length>1&&"0"==O(o,0)&&(a=M(Q,o)?16:8,o=$(o,8==a?1:2)),""===o)i=0;else{if(!M(10==a?ee:8==a?X:te,o))return e;i=P(o,a)}z(n,i)}for(r=0;r<t;r++)if(i=n[r],r==t-1){if(i>=j(256,5-t))return null}else if(i>255)return null;for(l=H(n),r=0;r<n.length;r++)l+=n[r]*j(256,3-r);return l},le=function(e){var t,n,r,o,a,i,l,s=[0,0,0,0,0,0,0,0],c=0,d=null,u=0,p=function(){return O(e,u)};if(":"==p()){if(":"!=O(e,1))return;u+=2,c++,d=c}while(p()){if(8==c)return;if(":"!=p()){t=n=0;while(n<4&&M(te,p()))t=16*t+P(p(),16),u++,n++;if("."==p()){if(0==n)return;if(u-=n,c>6)return;r=0;while(p()){if(o=null,r>0){if(!("."==p()&&r<4))return;u++}if(!M(Y,p()))return;while(M(Y,p())){if(a=P(p(),10),null===o)o=a;else{if(0==o)return;o=10*o+a}if(o>255)return;u++}s[c]=256*s[c]+o,r++,2!=r&&4!=r||c++}if(4!=r)return;break}if(":"==p()){if(u++,!p())return}else if(p())return;s[c++]=t}else{if(null!==d)return;u++,c++,d=c}}if(null!==d){i=c-d,c=7;while(0!=c&&i>0)l=s[c],s[c--]=s[d+i-1],s[d+--i]=l}else if(8!=c)return;return s},se=function(e){for(var t=null,n=1,r=null,o=0,a=0;a<8;a++)0!==e[a]?(o>n&&(t=r,n=o),r=null,o=0):(null===r&&(r=a),++o);return o>n&&(t=r,n=o),t},ce=function(e){var t,n,r,o;if("number"==typeof e){for(t=[],n=0;n<4;n++)U(t,e%256),e=T(e/256);return R(t,".")}if("object"==typeof e){for(t="",r=se(e),n=0;n<8;n++)o&&0===e[n]||(o&&(o=!1),r===n?(t+=n?":":"::",o=!0):(t+=F(e[n],16),n<7&&(t+=":")));return"["+t+"]"}return e},de={},ue=h({},de,{" ":1,'"':1,"<":1,">":1,"`":1}),pe=h({},ue,{"#":1,"?":1,"{":1,"}":1}),fe=h({},pe,{"/":1,":":1,";":1,"=":1,"@":1,"[":1,"\\":1,"]":1,"^":1,"|":1}),he=function(e,t){var n=g(e,0);return n>32&&n<127&&!f(t,e)?e:encodeURIComponent(e)},me={ftp:21,file:null,http:80,https:443,ws:80,wss:443},ve=function(e,t){var n;return 2==e.length&&M(G,O(e,0))&&(":"==(n=O(e,1))||!t&&"|"==n)},ge=function(e){var t;return e.length>1&&ve($(e,0,2))&&(2==e.length||"/"===(t=O(e,2))||"\\"===t||"?"===t||"#"===t)},be=function(e){return"."===e||"%2e"===N(e)},we=function(e){return e=N(e),".."===e||"%2e."===e||".%2e"===e||"%2e%2e"===e},ye={},ke={},_e={},xe={},Se={},Ce={},Le={},Ee={},Ae={},qe={},Pe={},Te={},je={},Oe={},Me={},Re={},Fe={},He={},ze={},Be={},Ie={},Ve=function(e,t,n){var r,o,a,i=w(e);if(t){if(o=this.parse(i),o)throw q(o);this.searchParams=null}else{if(void 0!==n&&(r=new Ve(n,!0)),o=this.parse(i,null,r),o)throw q(o);a=E(new L),a.bindURL(this),this.searchParams=a}};Ve.prototype={type:"URL",parse:function(e,t,n){var o,a,i,l,s=this,c=t||ye,d=0,u="",p=!1,h=!1,g=!1;e=w(e),t||(s.scheme="",s.username="",s.password="",s.host=null,s.port=null,s.path=[],s.query=null,s.fragment=null,s.cannotBeABaseURL=!1,e=B(e,oe,"")),e=B(e,ae,""),o=m(e);while(d<=o.length){switch(a=o[d],c){case ye:if(!a||!M(G,a)){if(t)return Z;c=_e;continue}u+=N(a),c=ke;break;case ke:if(a&&(M(J,a)||"+"==a||"-"==a||"."==a))u+=N(a);else{if(":"!=a){if(t)return Z;u="",c=_e,d=0;continue}if(t&&(s.isSpecial()!=f(me,u)||"file"==u&&(s.includesCredentials()||null!==s.port)||"file"==s.scheme&&!s.host))return;if(s.scheme=u,t)return void(s.isSpecial()&&me[s.scheme]==s.port&&(s.port=null));u="","file"==s.scheme?c=Oe:s.isSpecial()&&n&&n.scheme==s.scheme?c=xe:s.isSpecial()?c=Ee:"/"==o[d+1]?(c=Se,d++):(s.cannotBeABaseURL=!0,z(s.path,""),c=ze)}break;case _e:if(!n||n.cannotBeABaseURL&&"#"!=a)return Z;if(n.cannotBeABaseURL&&"#"==a){s.scheme=n.scheme,s.path=v(n.path),s.query=n.query,s.fragment="",s.cannotBeABaseURL=!0,c=Ie;break}c="file"==n.scheme?Oe:Ce;continue;case xe:if("/"!=a||"/"!=o[d+1]){c=Ce;continue}c=Ae,d++;break;case Se:if("/"==a){c=qe;break}c=He;continue;case Ce:if(s.scheme=n.scheme,a==r)s.username=n.username,s.password=n.password,s.host=n.host,s.port=n.port,s.path=v(n.path),s.query=n.query;else if("/"==a||"\\"==a&&s.isSpecial())c=Le;else if("?"==a)s.username=n.username,s.password=n.password,s.host=n.host,s.port=n.port,s.path=v(n.path),s.query="",c=Be;else{if("#"!=a){s.username=n.username,s.password=n.password,s.host=n.host,s.port=n.port,s.path=v(n.path),s.path.length--,c=He;continue}s.username=n.username,s.password=n.password,s.host=n.host,s.port=n.port,s.path=v(n.path),s.query=n.query,s.fragment="",c=Ie}break;case Le:if(!s.isSpecial()||"/"!=a&&"\\"!=a){if("/"!=a){s.username=n.username,s.password=n.password,s.host=n.host,s.port=n.port,c=He;continue}c=qe}else c=Ae;break;case Ee:if(c=Ae,"/"!=a||"/"!=O(u,d+1))continue;d++;break;case Ae:if("/"!=a&&"\\"!=a){c=qe;continue}break;case qe:if("@"==a){p&&(u="%40"+u),p=!0,i=m(u);for(var b=0;b<i.length;b++){var y=i[b];if(":"!=y||g){var k=he(y,fe);g?s.password+=k:s.username+=k}else g=!0}u=""}else if(a==r||"/"==a||"?"==a||"#"==a||"\\"==a&&s.isSpecial()){if(p&&""==u)return D;d-=m(u).length+1,u="",c=Pe}else u+=a;break;case Pe:case Te:if(t&&"file"==s.scheme){c=Re;continue}if(":"!=a||h){if(a==r||"/"==a||"?"==a||"#"==a||"\\"==a&&s.isSpecial()){if(s.isSpecial()&&""==u)return W;if(t&&""==u&&(s.includesCredentials()||null!==s.port))return;if(l=s.parseHost(u),l)return l;if(u="",c=Fe,t)return;continue}"["==a?h=!0:"]"==a&&(h=!1),u+=a}else{if(""==u)return W;if(l=s.parseHost(u),l)return l;if(u="",c=je,t==Te)return}break;case je:if(!M(Y,a)){if(a==r||"/"==a||"?"==a||"#"==a||"\\"==a&&s.isSpecial()||t){if(""!=u){var _=P(u,10);if(_>65535)return K;s.port=s.isSpecial()&&_===me[s.scheme]?null:_,u=""}if(t)return;c=Fe;continue}return K}u+=a;break;case Oe:if(s.scheme="file","/"==a||"\\"==a)c=Me;else{if(!n||"file"!=n.scheme){c=He;continue}if(a==r)s.host=n.host,s.path=v(n.path),s.query=n.query;else if("?"==a)s.host=n.host,s.path=v(n.path),s.query="",c=Be;else{if("#"!=a){ge(R(v(o,d),""))||(s.host=n.host,s.path=v(n.path),s.shortenPath()),c=He;continue}s.host=n.host,s.path=v(n.path),s.query=n.query,s.fragment="",c=Ie}}break;case Me:if("/"==a||"\\"==a){c=Re;break}n&&"file"==n.scheme&&!ge(R(v(o,d),""))&&(ve(n.path[0],!0)?z(s.path,n.path[0]):s.host=n.host),c=He;continue;case Re:if(a==r||"/"==a||"\\"==a||"?"==a||"#"==a){if(!t&&ve(u))c=He;else if(""==u){if(s.host="",t)return;c=Fe}else{if(l=s.parseHost(u),l)return l;if("localhost"==s.host&&(s.host=""),t)return;u="",c=Fe}continue}u+=a;break;case Fe:if(s.isSpecial()){if(c=He,"/"!=a&&"\\"!=a)continue}else if(t||"?"!=a)if(t||"#"!=a){if(a!=r&&(c=He,"/"!=a))continue}else s.fragment="",c=Ie;else s.query="",c=Be;break;case He:if(a==r||"/"==a||"\\"==a&&s.isSpecial()||!t&&("?"==a||"#"==a)){if(we(u)?(s.shortenPath(),"/"==a||"\\"==a&&s.isSpecial()||z(s.path,"")):be(u)?"/"==a||"\\"==a&&s.isSpecial()||z(s.path,""):("file"==s.scheme&&!s.path.length&&ve(u)&&(s.host&&(s.host=""),u=O(u,0)+":"),z(s.path,u)),u="","file"==s.scheme&&(a==r||"?"==a||"#"==a))while(s.path.length>1&&""===s.path[0])I(s.path);"?"==a?(s.query="",c=Be):"#"==a&&(s.fragment="",c=Ie)}else u+=he(a,pe);break;case ze:"?"==a?(s.query="",c=Be):"#"==a?(s.fragment="",c=Ie):a!=r&&(s.path[0]+=he(a,de));break;case Be:t||"#"!=a?a!=r&&("'"==a&&s.isSpecial()?s.query+="%27":s.query+="#"==a?"%23":he(a,de)):(s.fragment="",c=Ie);break;case Ie:a!=r&&(s.fragment+=he(a,ue));break}d++}},parseHost:function(e){var t,n,r;if("["==O(e,0)){if("]"!=O(e,e.length-1))return W;if(t=le($(e,1,-1)),!t)return W;this.host=t}else if(this.isSpecial()){if(e=b(e),M(ne,e))return W;if(t=ie(e),null===t)return W;this.host=t}else{if(M(re,e))return W;for(t="",n=m(e),r=0;r<n.length;r++)t+=he(n[r],de);this.host=t}},cannotHaveUsernamePasswordPort:function(){return!this.host||this.cannotBeABaseURL||"file"==this.scheme},includesCredentials:function(){return""!=this.username||""!=this.password},isSpecial:function(){return f(me,this.scheme)},shortenPath:function(){var e=this.path,t=e.length;!t||"file"==this.scheme&&1==t&&ve(e[0],!0)||e.length--},serialize:function(){var e=this,t=e.scheme,n=e.username,r=e.password,o=e.host,a=e.port,i=e.path,l=e.query,s=e.fragment,c=t+":";return null!==o?(c+="//",e.includesCredentials()&&(c+=n+(r?":"+r:"")+"@"),c+=ce(o),null!==a&&(c+=":"+a)):"file"==t&&(c+="//"),c+=e.cannotBeABaseURL?i[0]:i.length?"/"+R(i,"/"):"",null!==l&&(c+="?"+l),null!==s&&(c+="#"+s),c},setHref:function(e){var t=this.parse(e);if(t)throw q(t);this.searchParams.update()},getOrigin:function(){var e=this.scheme,t=this.port;if("blob"==e)try{return new $e(e.path[0]).origin}catch(n){return"null"}return"file"!=e&&this.isSpecial()?e+"://"+ce(this.host)+(null!==t?":"+t:""):"null"},getProtocol:function(){return this.scheme+":"},setProtocol:function(e){this.parse(w(e)+":",ye)},getUsername:function(){return this.username},setUsername:function(e){var t=m(w(e));if(!this.cannotHaveUsernamePasswordPort()){this.username="";for(var n=0;n<t.length;n++)this.username+=he(t[n],fe)}},getPassword:function(){return this.password},setPassword:function(e){var t=m(w(e));if(!this.cannotHaveUsernamePasswordPort()){this.password="";for(var n=0;n<t.length;n++)this.password+=he(t[n],fe)}},getHost:function(){var e=this.host,t=this.port;return null===e?"":null===t?ce(e):ce(e)+":"+t},setHost:function(e){this.cannotBeABaseURL||this.parse(e,Pe)},getHostname:function(){var e=this.host;return null===e?"":ce(e)},setHostname:function(e){this.cannotBeABaseURL||this.parse(e,Te)},getPort:function(){var e=this.port;return null===e?"":w(e)},setPort:function(e){this.cannotHaveUsernamePasswordPort()||(e=w(e),""==e?this.port=null:this.parse(e,je))},getPathname:function(){var e=this.path;return this.cannotBeABaseURL?e[0]:e.length?"/"+R(e,"/"):""},setPathname:function(e){this.cannotBeABaseURL||(this.path=[],this.parse(e,Fe))},getSearch:function(){var e=this.query;return e?"?"+e:""},setSearch:function(e){e=w(e),""==e?this.query=null:("?"==O(e,0)&&(e=$(e,1)),this.query="",this.parse(e,Be)),this.searchParams.update()},getSearchParams:function(){return this.searchParams.facade},getHash:function(){var e=this.fragment;return e?"#"+e:""},setHash:function(e){e=w(e),""!=e?("#"==O(e,0)&&(e=$(e,1)),this.fragment="",this.parse(e,Ie)):this.fragment=null},update:function(){this.query=this.searchParams.serialize()||null}};var $e=function(e){var t=p(this,Ne),n=k(arguments.length,1)>1?arguments[1]:void 0,r=S(t,new Ve(e,!1,n));a||(t.href=r.serialize(),t.origin=r.getOrigin(),t.protocol=r.getProtocol(),t.username=r.getUsername(),t.password=r.getPassword(),t.host=r.getHost(),t.hostname=r.getHostname(),t.port=r.getPort(),t.pathname=r.getPathname(),t.search=r.getSearch(),t.searchParams=r.getSearchParams(),t.hash=r.getHash())},Ne=$e.prototype,Ue=function(e,t){return{get:function(){return C(this)[e]()},set:t&&function(e){return C(this)[t](e)},configurable:!0,enumerable:!0}};if(a&&(u(Ne,"href",Ue("serialize","setHref")),u(Ne,"origin",Ue("getOrigin")),u(Ne,"protocol",Ue("getProtocol","setProtocol")),u(Ne,"username",Ue("getUsername","setUsername")),u(Ne,"password",Ue("getPassword","setPassword")),u(Ne,"host",Ue("getHost","setHost")),u(Ne,"hostname",Ue("getHostname","setHostname")),u(Ne,"port",Ue("getPort","setPort")),u(Ne,"pathname",Ue("getPathname","setPathname")),u(Ne,"search",Ue("getSearch","setSearch")),u(Ne,"searchParams",Ue("getSearchParams")),u(Ne,"hash",Ue("getHash","setHash"))),d(Ne,"toJSON",(function(){return C(this).serialize()}),{enumerable:!0}),d(Ne,"toString",(function(){return C(this).serialize()}),{enumerable:!0}),A){var De=A.createObjectURL,Ze=A.revokeObjectURL;De&&d($e,"createObjectURL",s(De,A)),Ze&&d($e,"revokeObjectURL",s(Ze,A))}y($e,"URL"),o({global:!0,forced:!i,sham:!a},{URL:$e})},4641:(e,t,n)=>{n(6614)},9357:(e,t,n)=>{"use strict";n.d(t,{KM:()=>C,ZP:()=>ge,sh:()=>L});var r=n(9835),o=n(499),a=n(854),i=n(3940),l=n(9114),s=n(4328),c="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof n.g?n.g:"undefined"!=typeof self?self:{};function d(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var u={exports:{}};!function(e){(function(){function t(e){var t={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic 'section-' prefix",type:"string"},rawPrefixHeaderId:{defaultValue:!1,describe:'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',type:"boolean"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},rawHeaderId:{defaultValue:!1,describe:"Remove only spaces, ' and \" from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids",type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},excludeTrailingPunctuationFromURLs:{defaultValue:!1,describe:"Excludes trailing punctuation from links generated with autoLinking",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,describe:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,describe:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,describe:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,describe:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,describe:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",describe:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,describe:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,describe:"Open all links in new windows",type:"boolean"},backslashEscapesHTMLTags:{defaultValue:!1,describe:"Support for HTML Tag escaping. ex: <div>foo</div>",type:"boolean"},emoji:{defaultValue:!1,describe:"Enable emoji support. Ex: `this is a :smile: emoji`",type:"boolean"},underline:{defaultValue:!1,describe:"Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`",type:"boolean"},ellipsis:{defaultValue:!0,describe:"Replaces three dots with the ellipsis unicode character",type:"boolean"},completeHTMLDocument:{defaultValue:!1,describe:"Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags",type:"boolean"},metadata:{defaultValue:!1,describe:"Enable support for document metadata (defined at the top of the document between `«««` and `»»»` or between `---` and `---`).",type:"boolean"},splitAdjacentBlockquotes:{defaultValue:!1,describe:"Split adjacent blockquote blocks",type:"boolean"}};if(!1===e)return JSON.parse(JSON.stringify(t));var n,r={};for(n in t)t.hasOwnProperty(n)&&(r[n]=t[n].defaultValue);return r}var n={},r={},o={},a=t(!0),i="vanilla",l={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0,backslashEscapesHTMLTags:!0,emoji:!0,splitAdjacentBlockquotes:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,excludeTrailingPunctuationFromURLs:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:t(!0),allOn:function(){var e,n=t(!0),r={};for(e in n)n.hasOwnProperty(e)&&(r[e]=!0);return r}()};function s(e,t){var r=t?"Error in "+t+" extension->":"Error in unnamed extension",o={valid:!0,error:""};n.helper.isArray(e)||(e=[e]);for(var a=0;a<e.length;++a){var i=r+" sub-extension "+a+": ",l=e[a];if("object"!=typeof l)return o.valid=!1,o.error=i+"must be an object, but "+typeof l+" given",o;if(!n.helper.isString(l.type))return o.valid=!1,o.error=i+'property "type" must be a string, but '+typeof l.type+" given",o;var s=l.type=l.type.toLowerCase();if("lang"!==(s="html"===(s="language"===s?l.type="lang":s)?l.type="output":s)&&"output"!==s&&"listener"!==s)return o.valid=!1,o.error=i+"type "+s+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',o;if("listener"===s){if(n.helper.isUndefined(l.listeners))return o.valid=!1,o.error=i+'. Extensions of type "listener" must have a property called "listeners"',o}else if(n.helper.isUndefined(l.filter)&&n.helper.isUndefined(l.regex))return o.valid=!1,o.error=i+s+' extensions must define either a "regex" property or a "filter" method',o;if(l.listeners){if("object"!=typeof l.listeners)return o.valid=!1,o.error=i+'"listeners" property must be an object but '+typeof l.listeners+" given",o;for(var c in l.listeners)if(l.listeners.hasOwnProperty(c)&&"function"!=typeof l.listeners[c])return o.valid=!1,o.error=i+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+c+" must be a function but "+typeof l.listeners[c]+" given",o}if(l.filter){if("function"!=typeof l.filter)return o.valid=!1,o.error=i+'"filter" must be a function, but '+typeof l.filter+" given",o}else if(l.regex){if(n.helper.isString(l.regex)&&(l.regex=new RegExp(l.regex,"g")),!(l.regex instanceof RegExp))return o.valid=!1,o.error=i+'"regex" property must either be a string or a RegExp object, but '+typeof l.regex+" given",o;if(n.helper.isUndefined(l.replace))return o.valid=!1,o.error=i+'"regex" extensions must implement a replace string or function',o}}return o}function c(e,t){return"¨E"+t.charCodeAt(0)+"E"}function d(e,t,n,r){var o,a,i,l,s=-1<(r=r||"").indexOf("g"),c=new RegExp(t+"|"+n,"g"+r.replace(/g/g,"")),d=new RegExp(t,r.replace(/g/g,"")),u=[];do{for(o=0;i=c.exec(e);)if(d.test(i[0]))o++||(l=(a=c.lastIndex)-i[0].length);else if(o&&!--o){var p=i.index+i[0].length;p={left:{start:l,end:a},match:{start:a,end:i.index},right:{start:i.index,end:p},wholeMatch:{start:l,end:p}};if(u.push(p),!s)return u}}while(o&&(c.lastIndex=a));return u}function u(e){return function(t,r,o,a,i,l,s){var c=o=o.replace(n.helper.regexes.asteriskDashAndColon,n.helper.escapeCharactersCallback),d="",u="";r=r||"",s=s||"";return/^www\./i.test(o)&&(o=o.replace(/^www\./i,"http://www.")),e.excludeTrailingPunctuationFromURLs&&l&&(d=l),r+'<a href="'+o+'"'+(u=e.openLinksInNewWindow?' rel="noopener noreferrer" target="¨E95Eblank"':u)+">"+c+"</a>"+d+s}}function p(e,t){return function(r,o,a){var i="mailto:";return o=o||"",a=n.subParser("unescapeSpecialChars")(a,e,t),e.encodeEmails?(i=n.helper.encodeEmailAddress(i+a),a=n.helper.encodeEmailAddress(a)):i+=a,o+'<a href="'+i+'">'+a+"</a>"}}n.helper={},n.extensions={},n.setOption=function(e,t){return a[e]=t,this},n.getOption=function(e){return a[e]},n.getOptions=function(){return a},n.resetOptions=function(){a=t(!0)},n.setFlavor=function(e){if(!l.hasOwnProperty(e))throw Error(e+" flavor was not found");n.resetOptions();var t,r=l[e];for(t in i=e,r)r.hasOwnProperty(t)&&(a[t]=r[t])},n.getFlavor=function(){return i},n.getFlavorOptions=function(e){if(l.hasOwnProperty(e))return l[e]},n.getDefaultOptions=t,n.subParser=function(e,t){if(n.helper.isString(e)){if(void 0===t){if(r.hasOwnProperty(e))return r[e];throw Error("SubParser named "+e+" not registered!")}r[e]=t}},n.extension=function(e,t){if(!n.helper.isString(e))throw Error("Extension 'name' must be a string");if(e=n.helper.stdExtName(e),n.helper.isUndefined(t)){if(!o.hasOwnProperty(e))throw Error("Extension named "+e+" is not registered!");return o[e]}"function"==typeof t&&(t=t());var r=s(t=n.helper.isArray(t)?t:[t],e);if(!r.valid)throw Error(r.error);o[e]=t},n.getAllExtensions=function(){return o},n.removeExtension=function(e){delete o[e]},n.resetExtensions=function(){o={}},n.validateExtension=function(e){return e=s(e,null),!!e.valid||(console.warn(e.error),!1)},n.hasOwnProperty("helper")||(n.helper={}),n.helper.isString=function(e){return"string"==typeof e||e instanceof String},n.helper.isFunction=function(e){return e&&"[object Function]"==={}.toString.call(e)},n.helper.isArray=function(e){return Array.isArray(e)},n.helper.isUndefined=function(e){return void 0===e},n.helper.forEach=function(e,t){if(n.helper.isUndefined(e))throw new Error("obj param is required");if(n.helper.isUndefined(t))throw new Error("callback param is required");if(!n.helper.isFunction(t))throw new Error("callback param must be a function/closure");if("function"==typeof e.forEach)e.forEach(t);else if(n.helper.isArray(e))for(var r=0;r<e.length;r++)t(e[r],r,e);else{if("object"!=typeof e)throw new Error("obj does not seem to be an array or an iterable object");for(var o in e)e.hasOwnProperty(o)&&t(e[o],o,e)}},n.helper.stdExtName=function(e){return e.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()},n.helper.escapeCharactersCallback=c,n.helper.escapeCharacters=function(e,t,n){return t="(["+t.replace(/([\[\]\\])/g,"\\$1")+"])",n&&(t="\\\\"+t),t=new RegExp(t,"g"),e.replace(t,c)},n.helper.unescapeHTMLEntities=function(e){return e.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")},n.helper.matchRecursiveRegExp=function(e,t,n,r){for(var o=d(e,t,n,r),a=[],i=0;i<o.length;++i)a.push([e.slice(o[i].wholeMatch.start,o[i].wholeMatch.end),e.slice(o[i].match.start,o[i].match.end),e.slice(o[i].left.start,o[i].left.end),e.slice(o[i].right.start,o[i].right.end)]);return a},n.helper.replaceRecursiveRegExp=function(e,t,r,o,a){var i;n.helper.isFunction(t)||(i=t,t=function(){return i});var l=d(e,r,o,a),s=(a=e,l.length);if(0<s){var c=[];0!==l[0].wholeMatch.start&&c.push(e.slice(0,l[0].wholeMatch.start));for(var u=0;u<s;++u)c.push(t(e.slice(l[u].wholeMatch.start,l[u].wholeMatch.end),e.slice(l[u].match.start,l[u].match.end),e.slice(l[u].left.start,l[u].left.end),e.slice(l[u].right.start,l[u].right.end))),u<s-1&&c.push(e.slice(l[u].wholeMatch.end,l[u+1].wholeMatch.start));l[s-1].wholeMatch.end<e.length&&c.push(e.slice(l[s-1].wholeMatch.end)),a=c.join("")}return a},n.helper.regexIndexOf=function(e,t,r){if(!n.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(t instanceof RegExp==0)throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";return t=e.substring(r||0).search(t),0<=t?t+(r||0):t},n.helper.splitAtIndex=function(e,t){if(!n.helper.isString(e))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";return[e.substring(0,t),e.substring(t)]},n.helper.encodeEmailAddress=function(e){var t=[function(e){return"&#"+e.charCodeAt(0)+";"},function(e){return"&#x"+e.charCodeAt(0).toString(16)+";"},function(e){return e}];return e.replace(/./g,(function(e){var n;return"@"===e?t[Math.floor(2*Math.random())](e):.9<(n=Math.random())?t[2](e):.45<n?t[1](e):t[0](e)}))},n.helper.padEnd=function(e,t,n){return t>>=0,n=String(n||" "),e.length>t?String(e):((t-=e.length)>n.length&&(n+=n.repeat(t/n.length)),String(e)+n.slice(0,t))},"undefined"==typeof console&&(console={warn:function(e){alert(e)},log:function(e){alert(e)},error:function(e){throw e}}),n.helper.regexes={asteriskDashAndColon:/([*_:~])/g},n.helper.emojis={"+1":"👍","-1":"👎",100:"💯",1234:"🔢","1st_place_medal":"🥇","2nd_place_medal":"🥈","3rd_place_medal":"🥉","8ball":"🎱",a:"🅰️",ab:"🆎",abc:"🔤",abcd:"🔡",accept:"🉑",aerial_tramway:"🚡",airplane:"✈️",alarm_clock:"⏰",alembic:"⚗️",alien:"👽",ambulance:"🚑",amphora:"🏺",anchor:"⚓️",angel:"👼",anger:"💢",angry:"😠",anguished:"😧",ant:"🐜",apple:"🍎",aquarius:"♒️",aries:"♈️",arrow_backward:"◀️",arrow_double_down:"⏬",arrow_double_up:"⏫",arrow_down:"⬇️",arrow_down_small:"🔽",arrow_forward:"▶️",arrow_heading_down:"⤵️",arrow_heading_up:"⤴️",arrow_left:"⬅️",arrow_lower_left:"↙️",arrow_lower_right:"↘️",arrow_right:"➡️",arrow_right_hook:"↪️",arrow_up:"⬆️",arrow_up_down:"↕️",arrow_up_small:"🔼",arrow_upper_left:"↖️",arrow_upper_right:"↗️",arrows_clockwise:"🔃",arrows_counterclockwise:"🔄",art:"🎨",articulated_lorry:"🚛",artificial_satellite:"🛰",astonished:"😲",athletic_shoe:"👟",atm:"🏧",atom_symbol:"⚛️",avocado:"🥑",b:"🅱️",baby:"👶",baby_bottle:"🍼",baby_chick:"🐤",baby_symbol:"🚼",back:"🔙",bacon:"🥓",badminton:"🏸",baggage_claim:"🛄",baguette_bread:"🥖",balance_scale:"⚖️",balloon:"🎈",ballot_box:"🗳",ballot_box_with_check:"☑️",bamboo:"🎍",banana:"🍌",bangbang:"‼️",bank:"🏦",bar_chart:"📊",barber:"💈",baseball:"⚾️",basketball:"🏀",basketball_man:"⛹️",basketball_woman:"⛹️&zwj;♀️",bat:"🦇",bath:"🛀",bathtub:"🛁",battery:"🔋",beach_umbrella:"🏖",bear:"🐻",bed:"🛏",bee:"🐝",beer:"🍺",beers:"🍻",beetle:"🐞",beginner:"🔰",bell:"🔔",bellhop_bell:"🛎",bento:"🍱",biking_man:"🚴",bike:"🚲",biking_woman:"🚴&zwj;♀️",bikini:"👙",biohazard:"☣️",bird:"🐦",birthday:"🎂",black_circle:"⚫️",black_flag:"🏴",black_heart:"🖤",black_joker:"🃏",black_large_square:"⬛️",black_medium_small_square:"◾️",black_medium_square:"◼️",black_nib:"✒️",black_small_square:"▪️",black_square_button:"🔲",blonde_man:"👱",blonde_woman:"👱&zwj;♀️",blossom:"🌼",blowfish:"🐡",blue_book:"📘",blue_car:"🚙",blue_heart:"💙",blush:"😊",boar:"🐗",boat:"⛵️",bomb:"💣",book:"📖",bookmark:"🔖",bookmark_tabs:"📑",books:"📚",boom:"💥",boot:"👢",bouquet:"💐",bowing_man:"🙇",bow_and_arrow:"🏹",bowing_woman:"🙇&zwj;♀️",bowling:"🎳",boxing_glove:"🥊",boy:"👦",bread:"🍞",bride_with_veil:"👰",bridge_at_night:"🌉",briefcase:"💼",broken_heart:"💔",bug:"🐛",building_construction:"🏗",bulb:"💡",bullettrain_front:"🚅",bullettrain_side:"🚄",burrito:"🌯",bus:"🚌",business_suit_levitating:"🕴",busstop:"🚏",bust_in_silhouette:"👤",busts_in_silhouette:"👥",butterfly:"🦋",cactus:"🌵",cake:"🍰",calendar:"📆",call_me_hand:"🤙",calling:"📲",camel:"🐫",camera:"📷",camera_flash:"📸",camping:"🏕",cancer:"♋️",candle:"🕯",candy:"🍬",canoe:"🛶",capital_abcd:"🔠",capricorn:"♑️",car:"🚗",card_file_box:"🗃",card_index:"📇",card_index_dividers:"🗂",carousel_horse:"🎠",carrot:"🥕",cat:"🐱",cat2:"🐈",cd:"💿",chains:"⛓",champagne:"🍾",chart:"💹",chart_with_downwards_trend:"📉",chart_with_upwards_trend:"📈",checkered_flag:"🏁",cheese:"🧀",cherries:"🍒",cherry_blossom:"🌸",chestnut:"🌰",chicken:"🐔",children_crossing:"🚸",chipmunk:"🐿",chocolate_bar:"🍫",christmas_tree:"🎄",church:"⛪️",cinema:"🎦",circus_tent:"🎪",city_sunrise:"🌇",city_sunset:"🌆",cityscape:"🏙",cl:"🆑",clamp:"🗜",clap:"👏",clapper:"🎬",classical_building:"🏛",clinking_glasses:"🥂",clipboard:"📋",clock1:"🕐",clock10:"🕙",clock1030:"🕥",clock11:"🕚",clock1130:"🕦",clock12:"🕛",clock1230:"🕧",clock130:"🕜",clock2:"🕑",clock230:"🕝",clock3:"🕒",clock330:"🕞",clock4:"🕓",clock430:"🕟",clock5:"🕔",clock530:"🕠",clock6:"🕕",clock630:"🕡",clock7:"🕖",clock730:"🕢",clock8:"🕗",clock830:"🕣",clock9:"🕘",clock930:"🕤",closed_book:"📕",closed_lock_with_key:"🔐",closed_umbrella:"🌂",cloud:"☁️",cloud_with_lightning:"🌩",cloud_with_lightning_and_rain:"⛈",cloud_with_rain:"🌧",cloud_with_snow:"🌨",clown_face:"🤡",clubs:"♣️",cocktail:"🍸",coffee:"☕️",coffin:"⚰️",cold_sweat:"😰",comet:"☄️",computer:"💻",computer_mouse:"🖱",confetti_ball:"🎊",confounded:"😖",confused:"😕",congratulations:"㊗️",construction:"🚧",construction_worker_man:"👷",construction_worker_woman:"👷&zwj;♀️",control_knobs:"🎛",convenience_store:"🏪",cookie:"🍪",cool:"🆒",policeman:"👮",copyright:"©️",corn:"🌽",couch_and_lamp:"🛋",couple:"👫",couple_with_heart_woman_man:"💑",couple_with_heart_man_man:"👨&zwj;❤️&zwj;👨",couple_with_heart_woman_woman:"👩&zwj;❤️&zwj;👩",couplekiss_man_man:"👨&zwj;❤️&zwj;💋&zwj;👨",couplekiss_man_woman:"💏",couplekiss_woman_woman:"👩&zwj;❤️&zwj;💋&zwj;👩",cow:"🐮",cow2:"🐄",cowboy_hat_face:"🤠",crab:"🦀",crayon:"🖍",credit_card:"💳",crescent_moon:"🌙",cricket:"🏏",crocodile:"🐊",croissant:"🥐",crossed_fingers:"🤞",crossed_flags:"🎌",crossed_swords:"⚔️",crown:"👑",cry:"😢",crying_cat_face:"😿",crystal_ball:"🔮",cucumber:"🥒",cupid:"💘",curly_loop:"➰",currency_exchange:"💱",curry:"🍛",custard:"🍮",customs:"🛃",cyclone:"🌀",dagger:"🗡",dancer:"💃",dancing_women:"👯",dancing_men:"👯&zwj;♂️",dango:"🍡",dark_sunglasses:"🕶",dart:"🎯",dash:"💨",date:"📅",deciduous_tree:"🌳",deer:"🦌",department_store:"🏬",derelict_house:"🏚",desert:"🏜",desert_island:"🏝",desktop_computer:"🖥",male_detective:"🕵️",diamond_shape_with_a_dot_inside:"💠",diamonds:"♦️",disappointed:"😞",disappointed_relieved:"😥",dizzy:"💫",dizzy_face:"😵",do_not_litter:"🚯",dog:"🐶",dog2:"🐕",dollar:"💵",dolls:"🎎",dolphin:"🐬",door:"🚪",doughnut:"🍩",dove:"🕊",dragon:"🐉",dragon_face:"🐲",dress:"👗",dromedary_camel:"🐪",drooling_face:"🤤",droplet:"💧",drum:"🥁",duck:"🦆",dvd:"📀","e-mail":"📧",eagle:"🦅",ear:"👂",ear_of_rice:"🌾",earth_africa:"🌍",earth_americas:"🌎",earth_asia:"🌏",egg:"🥚",eggplant:"🍆",eight_pointed_black_star:"✴️",eight_spoked_asterisk:"✳️",electric_plug:"🔌",elephant:"🐘",email:"✉️",end:"🔚",envelope_with_arrow:"📩",euro:"💶",european_castle:"🏰",european_post_office:"🏤",evergreen_tree:"🌲",exclamation:"❗️",expressionless:"😑",eye:"👁",eye_speech_bubble:"👁&zwj;🗨",eyeglasses:"👓",eyes:"👀",face_with_head_bandage:"🤕",face_with_thermometer:"🤒",fist_oncoming:"👊",factory:"🏭",fallen_leaf:"🍂",family_man_woman_boy:"👪",family_man_boy:"👨&zwj;👦",family_man_boy_boy:"👨&zwj;👦&zwj;👦",family_man_girl:"👨&zwj;👧",family_man_girl_boy:"👨&zwj;👧&zwj;👦",family_man_girl_girl:"👨&zwj;👧&zwj;👧",family_man_man_boy:"👨&zwj;👨&zwj;👦",family_man_man_boy_boy:"👨&zwj;👨&zwj;👦&zwj;👦",family_man_man_girl:"👨&zwj;👨&zwj;👧",family_man_man_girl_boy:"👨&zwj;👨&zwj;👧&zwj;👦",family_man_man_girl_girl:"👨&zwj;👨&zwj;👧&zwj;👧",family_man_woman_boy_boy:"👨&zwj;👩&zwj;👦&zwj;👦",family_man_woman_girl:"👨&zwj;👩&zwj;👧",family_man_woman_girl_boy:"👨&zwj;👩&zwj;👧&zwj;👦",family_man_woman_girl_girl:"👨&zwj;👩&zwj;👧&zwj;👧",family_woman_boy:"👩&zwj;👦",family_woman_boy_boy:"👩&zwj;👦&zwj;👦",family_woman_girl:"👩&zwj;👧",family_woman_girl_boy:"👩&zwj;👧&zwj;👦",family_woman_girl_girl:"👩&zwj;👧&zwj;👧",family_woman_woman_boy:"👩&zwj;👩&zwj;👦",family_woman_woman_boy_boy:"👩&zwj;👩&zwj;👦&zwj;👦",family_woman_woman_girl:"👩&zwj;👩&zwj;👧",family_woman_woman_girl_boy:"👩&zwj;👩&zwj;👧&zwj;👦",family_woman_woman_girl_girl:"👩&zwj;👩&zwj;👧&zwj;👧",fast_forward:"⏩",fax:"📠",fearful:"😨",feet:"🐾",female_detective:"🕵️&zwj;♀️",ferris_wheel:"🎡",ferry:"⛴",field_hockey:"🏑",file_cabinet:"🗄",file_folder:"📁",film_projector:"📽",film_strip:"🎞",fire:"🔥",fire_engine:"🚒",fireworks:"🎆",first_quarter_moon:"🌓",first_quarter_moon_with_face:"🌛",fish:"🐟",fish_cake:"🍥",fishing_pole_and_fish:"🎣",fist_raised:"✊",fist_left:"🤛",fist_right:"🤜",flags:"🎏",flashlight:"🔦",fleur_de_lis:"⚜️",flight_arrival:"🛬",flight_departure:"🛫",floppy_disk:"💾",flower_playing_cards:"🎴",flushed:"😳",fog:"🌫",foggy:"🌁",football:"🏈",footprints:"👣",fork_and_knife:"🍴",fountain:"⛲️",fountain_pen:"🖋",four_leaf_clover:"🍀",fox_face:"🦊",framed_picture:"🖼",free:"🆓",fried_egg:"🍳",fried_shrimp:"🍤",fries:"🍟",frog:"🐸",frowning:"😦",frowning_face:"☹️",frowning_man:"🙍&zwj;♂️",frowning_woman:"🙍",middle_finger:"🖕",fuelpump:"⛽️",full_moon:"🌕",full_moon_with_face:"🌝",funeral_urn:"⚱️",game_die:"🎲",gear:"⚙️",gem:"💎",gemini:"♊️",ghost:"👻",gift:"🎁",gift_heart:"💝",girl:"👧",globe_with_meridians:"🌐",goal_net:"🥅",goat:"🐐",golf:"⛳️",golfing_man:"🏌️",golfing_woman:"🏌️&zwj;♀️",gorilla:"🦍",grapes:"🍇",green_apple:"🍏",green_book:"📗",green_heart:"💚",green_salad:"🥗",grey_exclamation:"❕",grey_question:"❔",grimacing:"😬",grin:"😁",grinning:"😀",guardsman:"💂",guardswoman:"💂&zwj;♀️",guitar:"🎸",gun:"🔫",haircut_woman:"💇",haircut_man:"💇&zwj;♂️",hamburger:"🍔",hammer:"🔨",hammer_and_pick:"⚒",hammer_and_wrench:"🛠",hamster:"🐹",hand:"✋",handbag:"👜",handshake:"🤝",hankey:"💩",hatched_chick:"🐥",hatching_chick:"🐣",headphones:"🎧",hear_no_evil:"🙉",heart:"❤️",heart_decoration:"💟",heart_eyes:"😍",heart_eyes_cat:"😻",heartbeat:"💓",heartpulse:"💗",hearts:"♥️",heavy_check_mark:"✔️",heavy_division_sign:"➗",heavy_dollar_sign:"💲",heavy_heart_exclamation:"❣️",heavy_minus_sign:"➖",heavy_multiplication_x:"✖️",heavy_plus_sign:"➕",helicopter:"🚁",herb:"🌿",hibiscus:"🌺",high_brightness:"🔆",high_heel:"👠",hocho:"🔪",hole:"🕳",honey_pot:"🍯",horse:"🐴",horse_racing:"🏇",hospital:"🏥",hot_pepper:"🌶",hotdog:"🌭",hotel:"🏨",hotsprings:"♨️",hourglass:"⌛️",hourglass_flowing_sand:"⏳",house:"🏠",house_with_garden:"🏡",houses:"🏘",hugs:"🤗",hushed:"😯",ice_cream:"🍨",ice_hockey:"🏒",ice_skate:"⛸",icecream:"🍦",id:"🆔",ideograph_advantage:"🉐",imp:"👿",inbox_tray:"📥",incoming_envelope:"📨",tipping_hand_woman:"💁",information_source:"ℹ️",innocent:"😇",interrobang:"⁉️",iphone:"📱",izakaya_lantern:"🏮",jack_o_lantern:"🎃",japan:"🗾",japanese_castle:"🏯",japanese_goblin:"👺",japanese_ogre:"👹",jeans:"👖",joy:"😂",joy_cat:"😹",joystick:"🕹",kaaba:"🕋",key:"🔑",keyboard:"⌨️",keycap_ten:"🔟",kick_scooter:"🛴",kimono:"👘",kiss:"💋",kissing:"😗",kissing_cat:"😽",kissing_closed_eyes:"😚",kissing_heart:"😘",kissing_smiling_eyes:"😙",kiwi_fruit:"🥝",koala:"🐨",koko:"🈁",label:"🏷",large_blue_circle:"🔵",large_blue_diamond:"🔷",large_orange_diamond:"🔶",last_quarter_moon:"🌗",last_quarter_moon_with_face:"🌜",latin_cross:"✝️",laughing:"😆",leaves:"🍃",ledger:"📒",left_luggage:"🛅",left_right_arrow:"↔️",leftwards_arrow_with_hook:"↩️",lemon:"🍋",leo:"♌️",leopard:"🐆",level_slider:"🎚",libra:"♎️",light_rail:"🚈",link:"🔗",lion:"🦁",lips:"👄",lipstick:"💄",lizard:"🦎",lock:"🔒",lock_with_ink_pen:"🔏",lollipop:"🍭",loop:"➿",loud_sound:"🔊",loudspeaker:"📢",love_hotel:"🏩",love_letter:"💌",low_brightness:"🔅",lying_face:"🤥",m:"Ⓜ️",mag:"🔍",mag_right:"🔎",mahjong:"🀄️",mailbox:"📫",mailbox_closed:"📪",mailbox_with_mail:"📬",mailbox_with_no_mail:"📭",man:"👨",man_artist:"👨&zwj;🎨",man_astronaut:"👨&zwj;🚀",man_cartwheeling:"🤸&zwj;♂️",man_cook:"👨&zwj;🍳",man_dancing:"🕺",man_facepalming:"🤦&zwj;♂️",man_factory_worker:"👨&zwj;🏭",man_farmer:"👨&zwj;🌾",man_firefighter:"👨&zwj;🚒",man_health_worker:"👨&zwj;⚕️",man_in_tuxedo:"🤵",man_judge:"👨&zwj;⚖️",man_juggling:"🤹&zwj;♂️",man_mechanic:"👨&zwj;🔧",man_office_worker:"👨&zwj;💼",man_pilot:"👨&zwj;✈️",man_playing_handball:"🤾&zwj;♂️",man_playing_water_polo:"🤽&zwj;♂️",man_scientist:"👨&zwj;🔬",man_shrugging:"🤷&zwj;♂️",man_singer:"👨&zwj;🎤",man_student:"👨&zwj;🎓",man_teacher:"👨&zwj;🏫",man_technologist:"👨&zwj;💻",man_with_gua_pi_mao:"👲",man_with_turban:"👳",tangerine:"🍊",mans_shoe:"👞",mantelpiece_clock:"🕰",maple_leaf:"🍁",martial_arts_uniform:"🥋",mask:"😷",massage_woman:"💆",massage_man:"💆&zwj;♂️",meat_on_bone:"🍖",medal_military:"🎖",medal_sports:"🏅",mega:"📣",melon:"🍈",memo:"📝",men_wrestling:"🤼&zwj;♂️",menorah:"🕎",mens:"🚹",metal:"🤘",metro:"🚇",microphone:"🎤",microscope:"🔬",milk_glass:"🥛",milky_way:"🌌",minibus:"🚐",minidisc:"💽",mobile_phone_off:"📴",money_mouth_face:"🤑",money_with_wings:"💸",moneybag:"💰",monkey:"🐒",monkey_face:"🐵",monorail:"🚝",moon:"🌔",mortar_board:"🎓",mosque:"🕌",motor_boat:"🛥",motor_scooter:"🛵",motorcycle:"🏍",motorway:"🛣",mount_fuji:"🗻",mountain:"⛰",mountain_biking_man:"🚵",mountain_biking_woman:"🚵&zwj;♀️",mountain_cableway:"🚠",mountain_railway:"🚞",mountain_snow:"🏔",mouse:"🐭",mouse2:"🐁",movie_camera:"🎥",moyai:"🗿",mrs_claus:"🤶",muscle:"💪",mushroom:"🍄",musical_keyboard:"🎹",musical_note:"🎵",musical_score:"🎼",mute:"🔇",nail_care:"💅",name_badge:"📛",national_park:"🏞",nauseated_face:"🤢",necktie:"👔",negative_squared_cross_mark:"❎",nerd_face:"🤓",neutral_face:"😐",new:"🆕",new_moon:"🌑",new_moon_with_face:"🌚",newspaper:"📰",newspaper_roll:"🗞",next_track_button:"⏭",ng:"🆖",no_good_man:"🙅&zwj;♂️",no_good_woman:"🙅",night_with_stars:"🌃",no_bell:"🔕",no_bicycles:"🚳",no_entry:"⛔️",no_entry_sign:"🚫",no_mobile_phones:"📵",no_mouth:"😶",no_pedestrians:"🚷",no_smoking:"🚭","non-potable_water":"🚱",nose:"👃",notebook:"📓",notebook_with_decorative_cover:"📔",notes:"🎶",nut_and_bolt:"🔩",o:"⭕️",o2:"🅾️",ocean:"🌊",octopus:"🐙",oden:"🍢",office:"🏢",oil_drum:"🛢",ok:"🆗",ok_hand:"👌",ok_man:"🙆&zwj;♂️",ok_woman:"🙆",old_key:"🗝",older_man:"👴",older_woman:"👵",om:"🕉",on:"🔛",oncoming_automobile:"🚘",oncoming_bus:"🚍",oncoming_police_car:"🚔",oncoming_taxi:"🚖",open_file_folder:"📂",open_hands:"👐",open_mouth:"😮",open_umbrella:"☂️",ophiuchus:"⛎",orange_book:"📙",orthodox_cross:"☦️",outbox_tray:"📤",owl:"🦉",ox:"🐂",package:"📦",page_facing_up:"📄",page_with_curl:"📃",pager:"📟",paintbrush:"🖌",palm_tree:"🌴",pancakes:"🥞",panda_face:"🐼",paperclip:"📎",paperclips:"🖇",parasol_on_ground:"⛱",parking:"🅿️",part_alternation_mark:"〽️",partly_sunny:"⛅️",passenger_ship:"🛳",passport_control:"🛂",pause_button:"⏸",peace_symbol:"☮️",peach:"🍑",peanuts:"🥜",pear:"🍐",pen:"🖊",pencil2:"✏️",penguin:"🐧",pensive:"😔",performing_arts:"🎭",persevere:"😣",person_fencing:"🤺",pouting_woman:"🙎",phone:"☎️",pick:"⛏",pig:"🐷",pig2:"🐖",pig_nose:"🐽",pill:"💊",pineapple:"🍍",ping_pong:"🏓",pisces:"♓️",pizza:"🍕",place_of_worship:"🛐",plate_with_cutlery:"🍽",play_or_pause_button:"⏯",point_down:"👇",point_left:"👈",point_right:"👉",point_up:"☝️",point_up_2:"👆",police_car:"🚓",policewoman:"👮&zwj;♀️",poodle:"🐩",popcorn:"🍿",post_office:"🏣",postal_horn:"📯",postbox:"📮",potable_water:"🚰",potato:"🥔",pouch:"👝",poultry_leg:"🍗",pound:"💷",rage:"😡",pouting_cat:"😾",pouting_man:"🙎&zwj;♂️",pray:"🙏",prayer_beads:"📿",pregnant_woman:"🤰",previous_track_button:"⏮",prince:"🤴",princess:"👸",printer:"🖨",purple_heart:"💜",purse:"👛",pushpin:"📌",put_litter_in_its_place:"🚮",question:"❓",rabbit:"🐰",rabbit2:"🐇",racehorse:"🐎",racing_car:"🏎",radio:"📻",radio_button:"🔘",radioactive:"☢️",railway_car:"🚃",railway_track:"🛤",rainbow:"🌈",rainbow_flag:"🏳️&zwj;🌈",raised_back_of_hand:"🤚",raised_hand_with_fingers_splayed:"🖐",raised_hands:"🙌",raising_hand_woman:"🙋",raising_hand_man:"🙋&zwj;♂️",ram:"🐏",ramen:"🍜",rat:"🐀",record_button:"⏺",recycle:"♻️",red_circle:"🔴",registered:"®️",relaxed:"☺️",relieved:"😌",reminder_ribbon:"🎗",repeat:"🔁",repeat_one:"🔂",rescue_worker_helmet:"⛑",restroom:"🚻",revolving_hearts:"💞",rewind:"⏪",rhinoceros:"🦏",ribbon:"🎀",rice:"🍚",rice_ball:"🍙",rice_cracker:"🍘",rice_scene:"🎑",right_anger_bubble:"🗯",ring:"💍",robot:"🤖",rocket:"🚀",rofl:"🤣",roll_eyes:"🙄",roller_coaster:"🎢",rooster:"🐓",rose:"🌹",rosette:"🏵",rotating_light:"🚨",round_pushpin:"📍",rowing_man:"🚣",rowing_woman:"🚣&zwj;♀️",rugby_football:"🏉",running_man:"🏃",running_shirt_with_sash:"🎽",running_woman:"🏃&zwj;♀️",sa:"🈂️",sagittarius:"♐️",sake:"🍶",sandal:"👡",santa:"🎅",satellite:"📡",saxophone:"🎷",school:"🏫",school_satchel:"🎒",scissors:"✂️",scorpion:"🦂",scorpius:"♏️",scream:"😱",scream_cat:"🙀",scroll:"📜",seat:"💺",secret:"㊙️",see_no_evil:"🙈",seedling:"🌱",selfie:"🤳",shallow_pan_of_food:"🥘",shamrock:"☘️",shark:"🦈",shaved_ice:"🍧",sheep:"🐑",shell:"🐚",shield:"🛡",shinto_shrine:"⛩",ship:"🚢",shirt:"👕",shopping:"🛍",shopping_cart:"🛒",shower:"🚿",shrimp:"🦐",signal_strength:"📶",six_pointed_star:"🔯",ski:"🎿",skier:"⛷",skull:"💀",skull_and_crossbones:"☠️",sleeping:"😴",sleeping_bed:"🛌",sleepy:"😪",slightly_frowning_face:"🙁",slightly_smiling_face:"🙂",slot_machine:"🎰",small_airplane:"🛩",small_blue_diamond:"🔹",small_orange_diamond:"🔸",small_red_triangle:"🔺",small_red_triangle_down:"🔻",smile:"😄",smile_cat:"😸",smiley:"😃",smiley_cat:"😺",smiling_imp:"😈",smirk:"😏",smirk_cat:"😼",smoking:"🚬",snail:"🐌",snake:"🐍",sneezing_face:"🤧",snowboarder:"🏂",snowflake:"❄️",snowman:"⛄️",snowman_with_snow:"☃️",sob:"😭",soccer:"⚽️",soon:"🔜",sos:"🆘",sound:"🔉",space_invader:"👾",spades:"♠️",spaghetti:"🍝",sparkle:"❇️",sparkler:"🎇",sparkles:"✨",sparkling_heart:"💖",speak_no_evil:"🙊",speaker:"🔈",speaking_head:"🗣",speech_balloon:"💬",speedboat:"🚤",spider:"🕷",spider_web:"🕸",spiral_calendar:"🗓",spiral_notepad:"🗒",spoon:"🥄",squid:"🦑",stadium:"🏟",star:"⭐️",star2:"🌟",star_and_crescent:"☪️",star_of_david:"✡️",stars:"🌠",station:"🚉",statue_of_liberty:"🗽",steam_locomotive:"🚂",stew:"🍲",stop_button:"⏹",stop_sign:"🛑",stopwatch:"⏱",straight_ruler:"📏",strawberry:"🍓",stuck_out_tongue:"😛",stuck_out_tongue_closed_eyes:"😝",stuck_out_tongue_winking_eye:"😜",studio_microphone:"🎙",stuffed_flatbread:"🥙",sun_behind_large_cloud:"🌥",sun_behind_rain_cloud:"🌦",sun_behind_small_cloud:"🌤",sun_with_face:"🌞",sunflower:"🌻",sunglasses:"😎",sunny:"☀️",sunrise:"🌅",sunrise_over_mountains:"🌄",surfing_man:"🏄",surfing_woman:"🏄&zwj;♀️",sushi:"🍣",suspension_railway:"🚟",sweat:"😓",sweat_drops:"💦",sweat_smile:"😅",sweet_potato:"🍠",swimming_man:"🏊",swimming_woman:"🏊&zwj;♀️",symbols:"🔣",synagogue:"🕍",syringe:"💉",taco:"🌮",tada:"🎉",tanabata_tree:"🎋",taurus:"♉️",taxi:"🚕",tea:"🍵",telephone_receiver:"📞",telescope:"🔭",tennis:"🎾",tent:"⛺️",thermometer:"🌡",thinking:"🤔",thought_balloon:"💭",ticket:"🎫",tickets:"🎟",tiger:"🐯",tiger2:"🐅",timer_clock:"⏲",tipping_hand_man:"💁&zwj;♂️",tired_face:"😫",tm:"™️",toilet:"🚽",tokyo_tower:"🗼",tomato:"🍅",tongue:"👅",top:"🔝",tophat:"🎩",tornado:"🌪",trackball:"🖲",tractor:"🚜",traffic_light:"🚥",train:"🚋",train2:"🚆",tram:"🚊",triangular_flag_on_post:"🚩",triangular_ruler:"📐",trident:"🔱",triumph:"😤",trolleybus:"🚎",trophy:"🏆",tropical_drink:"🍹",tropical_fish:"🐠",truck:"🚚",trumpet:"🎺",tulip:"🌷",tumbler_glass:"🥃",turkey:"🦃",turtle:"🐢",tv:"📺",twisted_rightwards_arrows:"🔀",two_hearts:"💕",two_men_holding_hands:"👬",two_women_holding_hands:"👭",u5272:"🈹",u5408:"🈴",u55b6:"🈺",u6307:"🈯️",u6708:"🈷️",u6709:"🈶",u6e80:"🈵",u7121:"🈚️",u7533:"🈸",u7981:"🈲",u7a7a:"🈳",umbrella:"☔️",unamused:"😒",underage:"🔞",unicorn:"🦄",unlock:"🔓",up:"🆙",upside_down_face:"🙃",v:"✌️",vertical_traffic_light:"🚦",vhs:"📼",vibration_mode:"📳",video_camera:"📹",video_game:"🎮",violin:"🎻",virgo:"♍️",volcano:"🌋",volleyball:"🏐",vs:"🆚",vulcan_salute:"🖖",walking_man:"🚶",walking_woman:"🚶&zwj;♀️",waning_crescent_moon:"🌘",waning_gibbous_moon:"🌖",warning:"⚠️",wastebasket:"🗑",watch:"⌚️",water_buffalo:"🐃",watermelon:"🍉",wave:"👋",wavy_dash:"〰️",waxing_crescent_moon:"🌒",wc:"🚾",weary:"😩",wedding:"💒",weight_lifting_man:"🏋️",weight_lifting_woman:"🏋️&zwj;♀️",whale:"🐳",whale2:"🐋",wheel_of_dharma:"☸️",wheelchair:"♿️",white_check_mark:"✅",white_circle:"⚪️",white_flag:"🏳️",white_flower:"💮",white_large_square:"⬜️",white_medium_small_square:"◽️",white_medium_square:"◻️",white_small_square:"▫️",white_square_button:"🔳",wilted_flower:"🥀",wind_chime:"🎐",wind_face:"🌬",wine_glass:"🍷",wink:"😉",wolf:"🐺",woman:"👩",woman_artist:"👩&zwj;🎨",woman_astronaut:"👩&zwj;🚀",woman_cartwheeling:"🤸&zwj;♀️",woman_cook:"👩&zwj;🍳",woman_facepalming:"🤦&zwj;♀️",woman_factory_worker:"👩&zwj;🏭",woman_farmer:"👩&zwj;🌾",woman_firefighter:"👩&zwj;🚒",woman_health_worker:"👩&zwj;⚕️",woman_judge:"👩&zwj;⚖️",woman_juggling:"🤹&zwj;♀️",woman_mechanic:"👩&zwj;🔧",woman_office_worker:"👩&zwj;💼",woman_pilot:"👩&zwj;✈️",woman_playing_handball:"🤾&zwj;♀️",woman_playing_water_polo:"🤽&zwj;♀️",woman_scientist:"👩&zwj;🔬",woman_shrugging:"🤷&zwj;♀️",woman_singer:"👩&zwj;🎤",woman_student:"👩&zwj;🎓",woman_teacher:"👩&zwj;🏫",woman_technologist:"👩&zwj;💻",woman_with_turban:"👳&zwj;♀️",womans_clothes:"👚",womans_hat:"👒",women_wrestling:"🤼&zwj;♀️",womens:"🚺",world_map:"🗺",worried:"😟",wrench:"🔧",writing_hand:"✍️",x:"❌",yellow_heart:"💛",yen:"💴",yin_yang:"☯️",yum:"😋",zap:"⚡️",zipper_mouth_face:"🤐",zzz:"💤",octocat:'<img alt=":octocat:" height="20" width="20" align="absmiddle" src="https://assets-cdn.github.com/images/icons/emoji/octocat.png">',showdown:"<span style=\"font-family: 'Anonymous Pro', monospace; text-decoration: underline; text-decoration-style: dashed; text-decoration-color: #3e8b8a;text-underline-position: under;\">S</span>"},n.Converter=function(e){var t={},r=[],c=[],d={},u=i,p={parsed:{},raw:"",format:""};function f(e,t){if(t=t||null,n.helper.isString(e)){if(t=e=n.helper.stdExtName(e),n.extensions[e])return console.warn("DEPRECATION WARNING: "+e+" is an old extension that uses a deprecated loading method.Please inform the developer that the extension should be updated!"),void function(e,t){if("function"==typeof e&&(e=e(new n.Converter)),n.helper.isArray(e)||(e=[e]),t=s(e,t),!t.valid)throw Error(t.error);for(var o=0;o<e.length;++o)switch(e[o].type){case"lang":r.push(e[o]);break;case"output":c.push(e[o]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}}(n.extensions[e],e);if(n.helper.isUndefined(o[e]))throw Error('Extension "'+e+'" could not be loaded. It was either not found or is not a valid extension.');e=o[e]}if("function"==typeof e&&(e=e()),t=s(e=n.helper.isArray(e)?e:[e],t),!t.valid)throw Error(t.error);for(var a=0;a<e.length;++a){switch(e[a].type){case"lang":r.push(e[a]);break;case"output":c.push(e[a])}if(e[a].hasOwnProperty("listeners"))for(var i in e[a].listeners)e[a].listeners.hasOwnProperty(i)&&h(i,e[a].listeners[i])}}function h(e,t){if(!n.helper.isString(e))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+typeof e+" given");if("function"!=typeof t)throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+typeof t+" given");d.hasOwnProperty(e)||(d[e]=[]),d[e].push(t)}!function(){for(var r in e=e||{},a)a.hasOwnProperty(r)&&(t[r]=a[r]);if("object"!=typeof e)throw Error("Converter expects the passed parameter to be an object, but "+typeof e+" was passed instead.");for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o]);t.extensions&&n.helper.forEach(t.extensions,f)}(),this._dispatch=function(e,t,n,r){if(d.hasOwnProperty(e))for(var o=0;o<d[e].length;++o){var a=d[e][o](e,t,this,n,r);a&&void 0!==a&&(t=a)}return t},this.listen=function(e,t){return h(e,t),this},this.makeHtml=function(e){if(!e)return e;var o,a,i={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:r,outputModifiers:c,converter:this,ghCodeBlocks:[],metadata:{parsed:{},raw:"",format:""}};return e=(e=(e=(e=(e=e.replace(/¨/g,"¨T")).replace(/\$/g,"¨D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/\u00A0/g,"&nbsp;"),t.smartIndentationFix&&(a=(o=e).match(/^\s*/)[0].length,a=new RegExp("^\\s{0,"+a+"}","gm"),e=o.replace(a,"")),e="\n\n"+e+"\n\n",e=(e=n.subParser("detab")(e,t,i)).replace(/^[ \t]+$/gm,""),n.helper.forEach(r,(function(r){e=n.subParser("runExtension")(r,e,t,i)})),e=n.subParser("metadata")(e,t,i),e=n.subParser("hashPreCodeTags")(e,t,i),e=n.subParser("githubCodeBlocks")(e,t,i),e=n.subParser("hashHTMLBlocks")(e,t,i),e=n.subParser("hashCodeTags")(e,t,i),e=n.subParser("stripLinkDefinitions")(e,t,i),e=n.subParser("blockGamut")(e,t,i),e=n.subParser("unhashHTMLSpans")(e,t,i),e=(e=(e=n.subParser("unescapeSpecialChars")(e,t,i)).replace(/¨D/g,"$$")).replace(/¨T/g,"¨"),e=n.subParser("completeHTMLDocument")(e,t,i),n.helper.forEach(c,(function(r){e=n.subParser("runExtension")(r,e,t,i)})),p=i.metadata,e},this.makeMarkdown=this.makeMd=function(e,t){if(e=(e=(e=e.replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/>[ \t]+</,">¨NBSP;<"),!t){if(!window||!window.document)throw new Error("HTMLParser is undefined. If in a webworker or nodejs environment, you need to provide a WHATWG DOM and HTML such as JSDOM");t=window.document}t=t.createElement("div"),t.innerHTML=e;var r={preList:function(e){for(var t=e.querySelectorAll("pre"),r=[],o=0;o<t.length;++o)if(1===t[o].childElementCount&&"code"===t[o].firstChild.tagName.toLowerCase()){var a=t[o].firstChild.innerHTML.trim(),i=t[o].firstChild.getAttribute("data-language")||"";if(""===i)for(var l=t[o].firstChild.className.split(" "),s=0;s<l.length;++s){var c=l[s].match(/^language-(.+)$/);if(null!==c){i=c[1];break}}a=n.helper.unescapeHTMLEntities(a),r.push(a),t[o].outerHTML='<precode language="'+i+'" precodenum="'+o.toString()+'"></precode>'}else r.push(t[o].innerHTML),t[o].innerHTML="",t[o].setAttribute("prenum",o.toString());return r}(t)};!function e(t){for(var n=0;n<t.childNodes.length;++n){var r=t.childNodes[n];3===r.nodeType?/\S/.test(r.nodeValue)||/^[ ]+$/.test(r.nodeValue)?(r.nodeValue=r.nodeValue.split("\n").join(" "),r.nodeValue=r.nodeValue.replace(/(\s)+/g,"$1")):(t.removeChild(r),--n):1===r.nodeType&&e(r)}}(t);for(var o=t.childNodes,a="",i=0;i<o.length;i++)a+=n.subParser("makeMarkdown.node")(o[i],r);return a},this.setOption=function(e,n){t[e]=n},this.getOption=function(e){return t[e]},this.getOptions=function(){return t},this.addExtension=function(e,t){f(e,t=t||null)},this.useExtension=function(e){f(e)},this.setFlavor=function(e){if(!l.hasOwnProperty(e))throw Error(e+" flavor was not found");var n,r=l[e];for(n in u=e,r)r.hasOwnProperty(n)&&(t[n]=r[n])},this.getFlavor=function(){return u},this.removeExtension=function(e){n.helper.isArray(e)||(e=[e]);for(var t=0;t<e.length;++t){for(var o=e[t],a=0;a<r.length;++a)r[a]===o&&r.splice(a,1);for(var i=0;i<c.length;++i)c[i]===o&&c.splice(i,1)}},this.getAllExtensions=function(){return{language:r,output:c}},this.getMetadata=function(e){return e?p.raw:p.parsed},this.getMetadataFormat=function(){return p.format},this._setMetadataPair=function(e,t){p.parsed[e]=t},this._setMetadataFormat=function(e){p.format=e},this._setMetadataRaw=function(e){p.raw=e}},n.subParser("anchors",(function(e,t,r){function o(e,o,a,i,l,s,c){if(n.helper.isUndefined(c)&&(c=""),a=a.toLowerCase(),-1<e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m))i="";else if(!i){if(i="#"+(a=a||o.toLowerCase().replace(/ ?\n/g," ")),n.helper.isUndefined(r.gUrls[a]))return e;i=r.gUrls[a],n.helper.isUndefined(r.gTitles[a])||(c=r.gTitles[a])}return a='<a href="'+(i=i.replace(n.helper.regexes.asteriskDashAndColon,n.helper.escapeCharactersCallback))+'"',""!==c&&null!==c&&(a+=' title="'+(c=(c=c.replace(/"/g,"&quot;")).replace(n.helper.regexes.asteriskDashAndColon,n.helper.escapeCharactersCallback))+'"'),t.openLinksInNewWindow&&!/^#/.test(i)&&(a+=' rel="noopener noreferrer" target="¨E95Eblank"'),a+">"+o+"</a>"}return e=(e=(e=(e=(e=r.converter._dispatch("anchors.before",e,t,r)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g,o)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,o)).replace(/\[((?:\[[^\]]*]|[^\[\]])*)]()[ \t]*\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,o)).replace(/\[([^\[\]]+)]()()()()()/g,o),t.ghMentions&&(e=e.replace(/(^|\s)(\\)?(@([a-z\d]+(?:[a-z\d.-]+?[a-z\d]+)*))/gim,(function(e,r,o,a,i){if("\\"===o)return r+a;if(!n.helper.isString(t.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");return o="",r+'<a href="'+t.ghMentionsLink.replace(/\{u}/g,i)+'"'+(o=t.openLinksInNewWindow?' rel="noopener noreferrer" target="¨E95Eblank"':o)+">"+a+"</a>"}))),r.converter._dispatch("anchors.after",e,t,r)}));var f=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+?\.[^'">\s]+?)()(\1)?(?=\s|$)(?!["<>])/gi,h=/([*~_]+|\b)(((https?|ftp|dict):\/\/|www\.)[^'">\s]+\.[^'">\s]+?)([.!?,()\[\]])?(\1)?(?=\s|$)(?!["<>])/gi,m=/()<(((https?|ftp|dict):\/\/|www\.)[^'">\s]+)()>()/gi,v=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gim,g=/<()(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi;n.subParser("autoLinks",(function(e,t,n){return e=(e=(e=n.converter._dispatch("autoLinks.before",e,t,n)).replace(m,u(t))).replace(g,p(t,n)),n.converter._dispatch("autoLinks.after",e,t,n)})),n.subParser("simplifiedAutoLinks",(function(e,t,n){return t.simplifiedAutoLink?(e=n.converter._dispatch("simplifiedAutoLinks.before",e,t,n),e=(e=t.excludeTrailingPunctuationFromURLs?e.replace(h,u(t)):e.replace(f,u(t))).replace(v,p(t,n)),e=n.converter._dispatch("simplifiedAutoLinks.after",e,t,n)):e})),n.subParser("blockGamut",(function(e,t,r){return e=r.converter._dispatch("blockGamut.before",e,t,r),e=n.subParser("blockQuotes")(e,t,r),e=n.subParser("headers")(e,t,r),e=n.subParser("horizontalRule")(e,t,r),e=n.subParser("lists")(e,t,r),e=n.subParser("codeBlocks")(e,t,r),e=n.subParser("tables")(e,t,r),e=n.subParser("hashHTMLBlocks")(e,t,r),e=n.subParser("paragraphs")(e,t,r),r.converter._dispatch("blockGamut.after",e,t,r)})),n.subParser("blockQuotes",(function(e,t,r){e=r.converter._dispatch("blockQuotes.before",e,t,r);var o=/(^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+/gm;return t.splitAdjacentBlockquotes&&(o=/^ {0,3}>[\s\S]*?(?:\n\n)/gm),e=(e+="\n\n").replace(o,(function(e){return e=(e=(e=e.replace(/^[ \t]*>[ \t]?/gm,"")).replace(/¨0/g,"")).replace(/^[ \t]+$/gm,""),e=n.subParser("githubCodeBlocks")(e,t,r),e=(e=(e=n.subParser("blockGamut")(e,t,r)).replace(/(^|\n)/g,"$1  ")).replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,(function(e,t){return t.replace(/^  /gm,"¨0").replace(/¨0/g,"")})),n.subParser("hashBlock")("<blockquote>\n"+e+"\n</blockquote>",t,r)})),r.converter._dispatch("blockQuotes.after",e,t,r)})),n.subParser("codeBlocks",(function(e,t,r){return e=r.converter._dispatch("codeBlocks.before",e,t,r),e=(e=(e+="¨0").replace(/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=¨0))/g,(function(e,o,a){var i=o;o=a,a="\n",i=n.subParser("outdent")(i,t,r);return i=n.subParser("encodeCode")(i,t,r),i="<pre><code>"+(i=(i=(i=n.subParser("detab")(i,t,r)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+(a=t.omitExtraWLInCodeBlocks?"":a)+"</code></pre>",n.subParser("hashBlock")(i,t,r)+o}))).replace(/¨0/,""),r.converter._dispatch("codeBlocks.after",e,t,r)})),n.subParser("codeSpans",(function(e,t,r){return e=(e=void 0===(e=r.converter._dispatch("codeSpans.before",e,t,r))?"":e).replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,(function(e,o,a,i){return i=(i=i.replace(/^([ \t]*)/g,"")).replace(/[ \t]*$/g,""),i=o+"<code>"+(i=n.subParser("encodeCode")(i,t,r))+"</code>",n.subParser("hashHTMLSpans")(i,t,r)})),r.converter._dispatch("codeSpans.after",e,t,r)})),n.subParser("completeHTMLDocument",(function(e,t,n){if(!t.completeHTMLDocument)return e;e=n.converter._dispatch("completeHTMLDocument.before",e,t,n);var r,o="html",a="<!DOCTYPE HTML>\n",i="",l='<meta charset="utf-8">\n',s="",c="";for(r in void 0!==n.metadata.parsed.doctype&&(a="<!DOCTYPE "+n.metadata.parsed.doctype+">\n","html"!==(o=n.metadata.parsed.doctype.toString().toLowerCase())&&"html5"!==o||(l='<meta charset="utf-8">')),n.metadata.parsed)if(n.metadata.parsed.hasOwnProperty(r))switch(r.toLowerCase()){case"doctype":break;case"title":i="<title>"+n.metadata.parsed.title+"</title>\n";break;case"charset":l="html"===o||"html5"===o?'<meta charset="'+n.metadata.parsed.charset+'">\n':'<meta name="charset" content="'+n.metadata.parsed.charset+'">\n';break;case"language":case"lang":s=' lang="'+n.metadata.parsed[r]+'"',c+='<meta name="'+r+'" content="'+n.metadata.parsed[r]+'">\n';break;default:c+='<meta name="'+r+'" content="'+n.metadata.parsed[r]+'">\n'}return e=a+"<html"+s+">\n<head>\n"+i+l+c+"</head>\n<body>\n"+e.trim()+"\n</body>\n</html>",n.converter._dispatch("completeHTMLDocument.after",e,t,n)})),n.subParser("detab",(function(e,t,n){return e=(e=(e=(e=(e=(e=n.converter._dispatch("detab.before",e,t,n)).replace(/\t(?=\t)/g,"    ")).replace(/\t/g,"¨A¨B")).replace(/¨B(.+?)¨A/g,(function(e,t){for(var n=t,r=4-n.length%4,o=0;o<r;o++)n+=" ";return n}))).replace(/¨A/g,"    ")).replace(/¨B/g,""),n.converter._dispatch("detab.after",e,t,n)})),n.subParser("ellipsis",(function(e,t,n){return t.ellipsis?(e=(e=n.converter._dispatch("ellipsis.before",e,t,n)).replace(/\.\.\./g,"…"),e=n.converter._dispatch("ellipsis.after",e,t,n)):e})),n.subParser("emoji",(function(e,t,r){return t.emoji?(e=(e=r.converter._dispatch("emoji.before",e,t,r)).replace(/:([\S]+?):/g,(function(e,t){return n.helper.emojis.hasOwnProperty(t)?n.helper.emojis[t]:e})),r.converter._dispatch("emoji.after",e,t,r)):e})),n.subParser("encodeAmpsAndAngles",(function(e,t,n){return e=(e=(e=(e=(e=n.converter._dispatch("encodeAmpsAndAngles.before",e,t,n)).replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;")).replace(/<(?![a-z\/?$!])/gi,"&lt;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;"),n.converter._dispatch("encodeAmpsAndAngles.after",e,t,n)})),n.subParser("encodeBackslashEscapes",(function(e,t,r){return e=(e=(e=r.converter._dispatch("encodeBackslashEscapes.before",e,t,r)).replace(/\\(\\)/g,n.helper.escapeCharactersCallback)).replace(/\\([`*_{}\[\]()>#+.!~=|:-])/g,n.helper.escapeCharactersCallback),r.converter._dispatch("encodeBackslashEscapes.after",e,t,r)})),n.subParser("encodeCode",(function(e,t,r){return e=(e=r.converter._dispatch("encodeCode.before",e,t,r)).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,n.helper.escapeCharactersCallback),r.converter._dispatch("encodeCode.after",e,t,r)})),n.subParser("escapeSpecialCharsWithinTagAttributes",(function(e,t,r){return e=(e=(e=r.converter._dispatch("escapeSpecialCharsWithinTagAttributes.before",e,t,r)).replace(/<\/?[a-z\d_:-]+(?:[\s]+[\s\S]+?)?>/gi,(function(e){return e.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,n.helper.escapeCharactersCallback)}))).replace(/<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi,(function(e){return e.replace(/([\\`*_~=|])/g,n.helper.escapeCharactersCallback)})),r.converter._dispatch("escapeSpecialCharsWithinTagAttributes.after",e,t,r)})),n.subParser("githubCodeBlocks",(function(e,t,r){return t.ghCodeBlocks?(e=r.converter._dispatch("githubCodeBlocks.before",e,t,r),e=(e=(e+="¨0").replace(/(?:^|\n)(?: {0,3})(```+|~~~+)(?: *)([^\s`~]*)\n([\s\S]*?)\n(?: {0,3})\1/g,(function(e,o,a,i){var l=t.omitExtraWLInCodeBlocks?"":"\n";return i=n.subParser("encodeCode")(i,t,r),i="<pre><code"+(a?' class="'+a+" language-"+a+'"':"")+">"+(i=(i=(i=n.subParser("detab")(i,t,r)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+l+"</code></pre>",i=n.subParser("hashBlock")(i,t,r),"\n\n¨G"+(r.ghCodeBlocks.push({text:e,codeblock:i})-1)+"G\n\n"}))).replace(/¨0/,""),r.converter._dispatch("githubCodeBlocks.after",e,t,r)):e})),n.subParser("hashBlock",(function(e,t,n){return e=(e=n.converter._dispatch("hashBlock.before",e,t,n)).replace(/(^\n+|\n+$)/g,""),e="\n\n¨K"+(n.gHtmlBlocks.push(e)-1)+"K\n\n",n.converter._dispatch("hashBlock.after",e,t,n)})),n.subParser("hashCodeTags",(function(e,t,r){return e=r.converter._dispatch("hashCodeTags.before",e,t,r),e=n.helper.replaceRecursiveRegExp(e,(function(e,o,a,i){return i=a+n.subParser("encodeCode")(o,t,r)+i,"¨C"+(r.gHtmlSpans.push(i)-1)+"C"}),"<code\\b[^>]*>","</code>","gim"),r.converter._dispatch("hashCodeTags.after",e,t,r)})),n.subParser("hashElement",(function(e,t,n){return function(e,t){return t=(t=(t=t.replace(/\n\n/g,"\n")).replace(/^\n/,"")).replace(/\n+$/g,""),"\n\n¨K"+(n.gHtmlBlocks.push(t)-1)+"K\n\n"}})),n.subParser("hashHTMLBlocks",(function(e,t,r){function o(e,t,n,o){return-1!==n.search(/\bmarkdown\b/)&&(e=n+r.converter.makeHtml(t)+o),"\n\n¨K"+(r.gHtmlBlocks.push(e)-1)+"K\n\n"}e=r.converter._dispatch("hashHTMLBlocks.before",e,t,r);var a=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"];t.backslashEscapesHTMLTags&&(e=e.replace(/\\<(\/?[^>]+?)>/g,(function(e,t){return"&lt;"+t+"&gt;"})));for(var i=0;i<a.length;++i)for(var l=new RegExp("^ {0,3}(<"+a[i]+"\\b[^>]*>)","im"),s="<"+a[i]+"\\b[^>]*>",c="</"+a[i]+">";-1!==(u=n.helper.regexIndexOf(e,l));){var d=n.helper.splitAtIndex(e,u),u=n.helper.replaceRecursiveRegExp(d[1],o,s,c,"im");if(u===d[1])break;e=d[0].concat(u)}return e=e.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,n.subParser("hashElement")(e,t,r)),e=(e=n.helper.replaceRecursiveRegExp(e,(function(e){return"\n\n¨K"+(r.gHtmlBlocks.push(e)-1)+"K\n\n"}),"^ {0,3}\x3c!--","--\x3e","gm")).replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,n.subParser("hashElement")(e,t,r)),r.converter._dispatch("hashHTMLBlocks.after",e,t,r)})),n.subParser("hashHTMLSpans",(function(e,t,n){function r(e){return"¨C"+(n.gHtmlSpans.push(e)-1)+"C"}return e=(e=(e=(e=(e=n.converter._dispatch("hashHTMLSpans.before",e,t,n)).replace(/<[^>]+?\/>/gi,r)).replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,r)).replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,r)).replace(/<[^>]+?>/gi,r),n.converter._dispatch("hashHTMLSpans.after",e,t,n)})),n.subParser("unhashHTMLSpans",(function(e,t,n){e=n.converter._dispatch("unhashHTMLSpans.before",e,t,n);for(var r=0;r<n.gHtmlSpans.length;++r){for(var o=n.gHtmlSpans[r],a=0;/¨C(\d+)C/.test(o);){var i=RegExp.$1;o=o.replace("¨C"+i+"C",n.gHtmlSpans[i]);if(10===a){console.error("maximum nesting of 10 spans reached!!!");break}++a}e=e.replace("¨C"+r+"C",o)}return n.converter._dispatch("unhashHTMLSpans.after",e,t,n)})),n.subParser("hashPreCodeTags",(function(e,t,r){return e=r.converter._dispatch("hashPreCodeTags.before",e,t,r),e=n.helper.replaceRecursiveRegExp(e,(function(e,o,a,i){return i=a+n.subParser("encodeCode")(o,t,r)+i,"\n\n¨G"+(r.ghCodeBlocks.push({text:e,codeblock:i})-1)+"G\n\n"}),"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),r.converter._dispatch("hashPreCodeTags.after",e,t,r)})),n.subParser("headers",(function(e,t,r){e=r.converter._dispatch("headers.before",e,t,r);var o=isNaN(parseInt(t.headerLevelStart))?1:parseInt(t.headerLevelStart),a=t.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,i=t.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm;function l(e){var o;return!t.customizedHeaderId||(o=e.match(/\{([^{]+?)}\s*$/))&&o[1]&&(e=o[1]),o=e,e=n.helper.isString(t.prefixHeaderId)?t.prefixHeaderId:!0===t.prefixHeaderId?"section-":"",t.rawPrefixHeaderId||(o=e+o),o=(t.ghCompatibleHeaderId?o.replace(/ /g,"-").replace(/&amp;/g,"").replace(/¨T/g,"").replace(/¨D/g,"").replace(/[&+$,\/:;=?@"#{}|^¨~\[\]`\\*)(%.!'<>]/g,""):t.rawHeaderId?o.replace(/ /g,"-").replace(/&amp;/g,"&").replace(/¨T/g,"¨").replace(/¨D/g,"$").replace(/["']/g,"-"):o.replace(/[^\w]/g,"")).toLowerCase(),t.rawPrefixHeaderId&&(o=e+o),r.hashLinkCounts[o]?o=o+"-"+r.hashLinkCounts[o]++:r.hashLinkCounts[o]=1,o}return e=(e=e.replace(a,(function(e,a){var i=n.subParser("spanGamut")(a,t,r);a=t.noHeaderId?"":' id="'+l(a)+'"',i="<h"+o+a+">"+i+"</h"+o+">";return n.subParser("hashBlock")(i,t,r)}))).replace(i,(function(e,a){var i=n.subParser("spanGamut")(a,t,r),s=t.noHeaderId?"":' id="'+l(a)+'"';a=o+1,a="<h"+a+s+">"+i+"</h"+a+">";return n.subParser("hashBlock")(a,t,r)})),i=t.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm,e=e.replace(i,(function(e,a,i){var s=i;return t.customizedHeaderId&&(s=i.replace(/\s?\{([^{]+?)}\s*$/,"")),s=n.subParser("spanGamut")(s,t,r),i=t.noHeaderId?"":' id="'+l(i)+'"',a=o-1+a.length,a="<h"+a+i+">"+s+"</h"+a+">",n.subParser("hashBlock")(a,t,r)})),r.converter._dispatch("headers.after",e,t,r)})),n.subParser("horizontalRule",(function(e,t,r){e=r.converter._dispatch("horizontalRule.before",e,t,r);var o=n.subParser("hashBlock")("<hr />",t,r);return e=(e=(e=e.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,o)).replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,o)).replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,o),r.converter._dispatch("horizontalRule.after",e,t,r)})),n.subParser("images",(function(e,t,r){function o(e,t,o,a,i,l,s,c){var d=r.gUrls,u=r.gTitles,p=r.gDimensions;if(o=o.toLowerCase(),c=c||"",-1<e.search(/\(<?\s*>? ?(['"].*['"])?\)$/m))a="";else if(""===a||null===a){if(a="#"+(o=""===o||null===o?t.toLowerCase().replace(/ ?\n/g," "):o),n.helper.isUndefined(d[o]))return e;a=d[o],n.helper.isUndefined(u[o])||(c=u[o]),n.helper.isUndefined(p[o])||(i=p[o].width,l=p[o].height)}return t=t.replace(/"/g,"&quot;").replace(n.helper.regexes.asteriskDashAndColon,n.helper.escapeCharactersCallback),t='<img src="'+(a=a.replace(n.helper.regexes.asteriskDashAndColon,n.helper.escapeCharactersCallback))+'" alt="'+t+'"',c&&n.helper.isString(c)&&(t+=' title="'+(c=c.replace(/"/g,"&quot;").replace(n.helper.regexes.asteriskDashAndColon,n.helper.escapeCharactersCallback))+'"'),i&&l&&(t+=' width="'+(i="*"===i?"auto":i)+'"',t+=' height="'+(l="*"===l?"auto":l)+'"'),t+" />"}return e=(e=(e=(e=(e=(e=r.converter._dispatch("images.before",e,t,r)).replace(/!\[([^\]]*?)] ?(?:\n *)?\[([\s\S]*?)]()()()()()/g,o)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,(function(e,t,n,r,a,i,l,s){return o(e,t,n,r=r.replace(/\s/g,""),a,i,0,s)}))).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,o)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,o)).replace(/!\[([^\[\]]+)]()()()()()/g,o),r.converter._dispatch("images.after",e,t,r)})),n.subParser("italicsAndBold",(function(e,t,n){return e=n.converter._dispatch("italicsAndBold.before",e,t,n),e=t.literalMidWordUnderscores?(e=(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,(function(e,t){return"<strong><em>"+t+"</em></strong>"}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(e,t){return"<strong>"+t+"</strong>"}))).replace(/\b_(\S[\s\S]*?)_\b/g,(function(e,t){return"<em>"+t+"</em>"})):(e=(e=e.replace(/___(\S[\s\S]*?)___/g,(function(e,t){return/\S$/.test(t)?"<strong><em>"+t+"</em></strong>":e}))).replace(/__(\S[\s\S]*?)__/g,(function(e,t){return/\S$/.test(t)?"<strong>"+t+"</strong>":e}))).replace(/_([^\s_][\s\S]*?)_/g,(function(e,t){return/\S$/.test(t)?"<em>"+t+"</em>":e})),e=t.literalMidWordAsterisks?(e=(e=e.replace(/([^*]|^)\B\*\*\*(\S[\s\S]*?)\*\*\*\B(?!\*)/g,(function(e,t,n){return t+"<strong><em>"+n+"</em></strong>"}))).replace(/([^*]|^)\B\*\*(\S[\s\S]*?)\*\*\B(?!\*)/g,(function(e,t,n){return t+"<strong>"+n+"</strong>"}))).replace(/([^*]|^)\B\*(\S[\s\S]*?)\*\B(?!\*)/g,(function(e,t,n){return t+"<em>"+n+"</em>"})):(e=(e=e.replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,(function(e,t){return/\S$/.test(t)?"<strong><em>"+t+"</em></strong>":e}))).replace(/\*\*(\S[\s\S]*?)\*\*/g,(function(e,t){return/\S$/.test(t)?"<strong>"+t+"</strong>":e}))).replace(/\*([^\s*][\s\S]*?)\*/g,(function(e,t){return/\S$/.test(t)?"<em>"+t+"</em>":e})),n.converter._dispatch("italicsAndBold.after",e,t,n)})),n.subParser("lists",(function(e,t,r){function o(e,o){r.gListLevel++,e=e.replace(/\n{2,}$/,"\n");var a=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0| {0,3}([*+-]|\d+[.])[ \t]+))/gm,i=/\n[ \t]*\n(?!¨0)/.test(e+="¨0");return t.disableForced4SpacesIndentedSublists&&(a=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0|\2([*+-]|\d+[.])[ \t]+))/gm),e=(e=e.replace(a,(function(e,o,a,l,s,c,d){d=d&&""!==d.trim();var u=n.subParser("outdent")(s,t,r);s="";return c&&t.tasklists&&(s=' class="task-list-item" style="list-style-type: none;"',u=u.replace(/^[ \t]*\[(x|X| )?]/m,(function(){var e='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return d&&(e+=" checked"),e+">"}))),u=u.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,(function(e){return"¨A"+e})),"<li"+s+">"+(u=(u=o||-1<u.search(/\n{2,}/)?(u=n.subParser("githubCodeBlocks")(u,t,r),n.subParser("blockGamut")(u,t,r)):(u=(u=n.subParser("lists")(u,t,r)).replace(/\n$/,""),u=(u=n.subParser("hashHTMLBlocks")(u,t,r)).replace(/\n\n+/g,"\n\n"),(i?n.subParser("paragraphs"):n.subParser("spanGamut"))(u,t,r))).replace("¨A",""))+"</li>\n"}))).replace(/¨0/g,""),r.gListLevel--,o?e.replace(/\s+$/,""):e}function a(e,t){return"ol"===t&&(e=e.match(/^ *(\d+)\./),e&&"1"!==e[1])?' start="'+e[1]+'"':""}function i(e,n,r){var i,l=t.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,s=t.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,c="ul"===n?l:s,d="";return-1!==e.search(c)?function t(i){var u=i.search(c),p=a(e,n);-1!==u?(d+="\n\n<"+n+p+">\n"+o(i.slice(0,u),!!r)+"</"+n+">\n",c="ul"===(n="ul"===n?"ol":"ul")?l:s,t(i.slice(u))):d+="\n\n<"+n+p+">\n"+o(i,!!r)+"</"+n+">\n"}(e):(i=a(e,n),d="\n\n<"+n+i+">\n"+o(e,!!r)+"</"+n+">\n"),d}return e=r.converter._dispatch("lists.before",e,t,r),e+="¨0",e=(e=r.gListLevel?e.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,t,n){return i(t,-1<n.search(/[*+-]/g)?"ul":"ol",!0)})):e.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(e,t,n,r){return i(n,-1<r.search(/[*+-]/g)?"ul":"ol",!1)}))).replace(/¨0/,""),r.converter._dispatch("lists.after",e,t,r)})),n.subParser("metadata",(function(e,t,n){return t.metadata?(e=(e=(e=(e=n.converter._dispatch("metadata.before",e,t,n)).replace(/^\s*«««+(\S*?)\n([\s\S]+?)\n»»»+\n/,(function(e,t,n){return r(n),"¨M"}))).replace(/^\s*---+(\S*?)\n([\s\S]+?)\n---+\n/,(function(e,t,o){return t&&(n.metadata.format=t),r(o),"¨M"}))).replace(/¨M/g,""),e=n.converter._dispatch("metadata.after",e,t,n)):e;function r(e){(e=(e=(n.metadata.raw=e).replace(/&/g,"&amp;").replace(/"/g,"&quot;")).replace(/\n {4}/g," ")).replace(/^([\S ]+): +([\s\S]+?)$/gm,(function(e,t,r){return n.metadata.parsed[t]=r,""}))}})),n.subParser("outdent",(function(e,t,n){return e=(e=(e=n.converter._dispatch("outdent.before",e,t,n)).replace(/^(\t|[ ]{1,4})/gm,"¨0")).replace(/¨0/g,""),n.converter._dispatch("outdent.after",e,t,n)})),n.subParser("paragraphs",(function(e,t,r){for(var o=(e=(e=(e=r.converter._dispatch("paragraphs.before",e,t,r)).replace(/^\n+/g,"")).replace(/\n+$/g,"")).split(/\n{2,}/g),a=[],i=o.length,l=0;l<i;l++){var s=o[l];0<=s.search(/¨(K|G)(\d+)\1/g)?a.push(s):0<=s.search(/\S/)&&(s=(s=n.subParser("spanGamut")(s,t,r)).replace(/^([ \t]*)/g,"<p>"),s+="</p>",a.push(s))}for(i=a.length,l=0;l<i;l++){for(var c="",d=a[l],u=!1;/¨(K|G)(\d+)\1/.test(d);){var p=RegExp.$1,f=RegExp.$2;c=(c="K"===p?r.gHtmlBlocks[f]:u?n.subParser("encodeCode")(r.ghCodeBlocks[f].text,t,r):r.ghCodeBlocks[f].codeblock).replace(/\$/g,"$$$$"),d=d.replace(/(\n\n)?¨(K|G)\d+\2(\n\n)?/,c),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(d)&&(u=!0)}a[l]=d}return e=(e=(e=a.join("\n")).replace(/^\n+/g,"")).replace(/\n+$/g,""),r.converter._dispatch("paragraphs.after",e,t,r)})),n.subParser("runExtension",(function(e,t,n,r){return e.filter?t=e.filter(t,r.converter,n):e.regex&&((n=e.regex)instanceof RegExp||(n=new RegExp(n,"g")),t=t.replace(n,e.replace)),t})),n.subParser("spanGamut",(function(e,t,r){return e=r.converter._dispatch("spanGamut.before",e,t,r),e=n.subParser("codeSpans")(e,t,r),e=n.subParser("escapeSpecialCharsWithinTagAttributes")(e,t,r),e=n.subParser("encodeBackslashEscapes")(e,t,r),e=n.subParser("images")(e,t,r),e=n.subParser("anchors")(e,t,r),e=n.subParser("autoLinks")(e,t,r),e=n.subParser("simplifiedAutoLinks")(e,t,r),e=n.subParser("emoji")(e,t,r),e=n.subParser("underline")(e,t,r),e=n.subParser("italicsAndBold")(e,t,r),e=n.subParser("strikethrough")(e,t,r),e=n.subParser("ellipsis")(e,t,r),e=n.subParser("hashHTMLSpans")(e,t,r),e=n.subParser("encodeAmpsAndAngles")(e,t,r),t.simpleLineBreaks?/\n\n¨K/.test(e)||(e=e.replace(/\n+/g,"<br />\n")):e=e.replace(/  +\n/g,"<br />\n"),r.converter._dispatch("spanGamut.after",e,t,r)})),n.subParser("strikethrough",(function(e,t,r){return t.strikethrough&&(e=(e=r.converter._dispatch("strikethrough.before",e,t,r)).replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,(function(e,o){return"<del>"+(o=t.simplifiedAutoLink?n.subParser("simplifiedAutoLinks")(o,t,r):o)+"</del>"})),e=r.converter._dispatch("strikethrough.after",e,t,r)),e})),n.subParser("stripLinkDefinitions",(function(e,t,r){function o(o,a,i,l,s,c,d){return a=a.toLowerCase(),e.toLowerCase().split(a).length-1<2?o:(i.match(/^data:.+?\/.+?;base64,/)?r.gUrls[a]=i.replace(/\s/g,""):r.gUrls[a]=n.subParser("encodeAmpsAndAngles")(i,t,r),c?c+d:(d&&(r.gTitles[a]=d.replace(/"|'/g,"&quot;")),t.parseImgDimensions&&l&&s&&(r.gDimensions[a]={width:l,height:s}),""))}return e=(e=(e=(e+="¨0").replace(/^ {0,3}\[([^\]]+)]:[ \t]*\n?[ \t]*<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n\n|(?=¨0)|(?=\n\[))/gm,o)).replace(/^ {0,3}\[([^\]]+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=¨0))/gm,o)).replace(/¨0/,"")})),n.subParser("tables",(function(e,t,r){if(!t.tables)return e;function o(e){for(var o=e.split("\n"),a=0;a<o.length;++a)/^ {0,3}\|/.test(o[a])&&(o[a]=o[a].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(o[a])&&(o[a]=o[a].replace(/\|[ \t]*$/,"")),o[a]=n.subParser("codeSpans")(o[a],t,r);var i,l,s,c,d,u=o[0].split("|").map((function(e){return e.trim()})),p=o[1].split("|").map((function(e){return e.trim()})),f=[],h=[],m=[],v=[];for(o.shift(),o.shift(),a=0;a<o.length;++a)""!==o[a].trim()&&f.push(o[a].split("|").map((function(e){return e.trim()})));if(u.length<p.length)return e;for(a=0;a<p.length;++a)m.push((i=p[a],/^:[ \t]*--*$/.test(i)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(i)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(i)?' style="text-align:center;"':""));for(a=0;a<u.length;++a)n.helper.isUndefined(m[a])&&(m[a]=""),h.push((l=u[a],s=m[a],c="",l=l.trim(),"<th"+(c=t.tablesHeaderId||t.tableHeaderId?' id="'+l.replace(/ /g,"_").toLowerCase()+'"':c)+s+">"+(l=n.subParser("spanGamut")(l,t,r))+"</th>\n"));for(a=0;a<f.length;++a){for(var g=[],b=0;b<h.length;++b)n.helper.isUndefined(f[a][b]),g.push((d=f[a][b],"<td"+m[b]+">"+n.subParser("spanGamut")(d,t,r)+"</td>\n"));v.push(g)}return function(e,t){for(var n="<table>\n<thead>\n<tr>\n",r=e.length,o=0;o<r;++o)n+=e[o];for(n+="</tr>\n</thead>\n<tbody>\n",o=0;o<t.length;++o){n+="<tr>\n";for(var a=0;a<r;++a)n+=t[o][a];n+="</tr>\n"}return n+"</tbody>\n</table>\n"}(h,v)}return e=(e=(e=(e=r.converter._dispatch("tables.before",e,t,r)).replace(/\\(\|)/g,n.helper.escapeCharactersCallback)).replace(/^ {0,3}\|?.+\|.+\n {0,3}\|?[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:[-=]){2,}[\s\S]+?(?:\n\n|¨0)/gm,o)).replace(/^ {0,3}\|.+\|[ \t]*\n {0,3}\|[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*\n( {0,3}\|.+\|[ \t]*\n)*(?:\n|¨0)/gm,o),r.converter._dispatch("tables.after",e,t,r)})),n.subParser("underline",(function(e,t,r){return t.underline?(e=r.converter._dispatch("underline.before",e,t,r),e=(e=t.literalMidWordUnderscores?(e=e.replace(/\b___(\S[\s\S]*?)___\b/g,(function(e,t){return"<u>"+t+"</u>"}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(e,t){return"<u>"+t+"</u>"})):(e=e.replace(/___(\S[\s\S]*?)___/g,(function(e,t){return/\S$/.test(t)?"<u>"+t+"</u>":e}))).replace(/__(\S[\s\S]*?)__/g,(function(e,t){return/\S$/.test(t)?"<u>"+t+"</u>":e}))).replace(/(_)/g,n.helper.escapeCharactersCallback),e=r.converter._dispatch("underline.after",e,t,r)):e})),n.subParser("unescapeSpecialChars",(function(e,t,n){return e=(e=n.converter._dispatch("unescapeSpecialChars.before",e,t,n)).replace(/¨E(\d+)E/g,(function(e,t){return t=parseInt(t),String.fromCharCode(t)})),n.converter._dispatch("unescapeSpecialChars.after",e,t,n)})),n.subParser("makeMarkdown.blockquote",(function(e,t){var r="";if(e.hasChildNodes())for(var o=e.childNodes,a=o.length,i=0;i<a;++i){var l=n.subParser("makeMarkdown.node")(o[i],t);""!==l&&(r+=l)}return"> "+(r=r.trim()).split("\n").join("\n> ")})),n.subParser("makeMarkdown.codeBlock",(function(e,t){var n=e.getAttribute("language");e=e.getAttribute("precodenum");return"```"+n+"\n"+t.preList[e]+"\n```"})),n.subParser("makeMarkdown.codeSpan",(function(e){return"`"+e.innerHTML+"`"})),n.subParser("makeMarkdown.emphasis",(function(e,t){var r="";if(e.hasChildNodes()){r+="*";for(var o=e.childNodes,a=o.length,i=0;i<a;++i)r+=n.subParser("makeMarkdown.node")(o[i],t);r+="*"}return r})),n.subParser("makeMarkdown.header",(function(e,t,r){r=new Array(r+1).join("#");var o="";if(e.hasChildNodes()){o=r+" ";for(var a=e.childNodes,i=a.length,l=0;l<i;++l)o+=n.subParser("makeMarkdown.node")(a[l],t)}return o})),n.subParser("makeMarkdown.hr",(function(){return"---"})),n.subParser("makeMarkdown.image",(function(e){var t="";return e.hasAttribute("src")&&(t+="!["+e.getAttribute("alt")+"](",t+="<"+e.getAttribute("src")+">",e.hasAttribute("width")&&e.hasAttribute("height")&&(t+=" ="+e.getAttribute("width")+"x"+e.getAttribute("height")),e.hasAttribute("title")&&(t+=' "'+e.getAttribute("title")+'"'),t+=")"),t})),n.subParser("makeMarkdown.links",(function(e,t){var r="";if(e.hasChildNodes()&&e.hasAttribute("href")){for(var o=e.childNodes,a=o.length,i=(r="[",0);i<a;++i)r+=n.subParser("makeMarkdown.node")(o[i],t);r+="](",r+="<"+e.getAttribute("href")+">",e.hasAttribute("title")&&(r+=' "'+e.getAttribute("title")+'"'),r+=")"}return r})),n.subParser("makeMarkdown.list",(function(e,t,r){var o="";if(!e.hasChildNodes())return"";for(var a=e.childNodes,i=a.length,l=e.getAttribute("start")||1,s=0;s<i;++s)void 0!==a[s].tagName&&"li"===a[s].tagName.toLowerCase()&&(o+=("ol"===r?l.toString()+". ":"- ")+n.subParser("makeMarkdown.listItem")(a[s],t),++l);return(o+="\n\x3c!-- --\x3e\n").trim()})),n.subParser("makeMarkdown.listItem",(function(e,t){for(var r="",o=e.childNodes,a=o.length,i=0;i<a;++i)r+=n.subParser("makeMarkdown.node")(o[i],t);return/\n$/.test(r)?r=r.split("\n").join("\n    ").replace(/^ {4}$/gm,"").replace(/\n\n+/g,"\n\n"):r+="\n",r})),n.subParser("makeMarkdown.node",(function(e,t,r){r=r||!1;var o="";if(3===e.nodeType)return n.subParser("makeMarkdown.txt")(e,t);if(8===e.nodeType)return"\x3c!--"+e.data+"--\x3e\n\n";if(1!==e.nodeType)return"";switch(e.tagName.toLowerCase()){case"h1":r||(o=n.subParser("makeMarkdown.header")(e,t,1)+"\n\n");break;case"h2":r||(o=n.subParser("makeMarkdown.header")(e,t,2)+"\n\n");break;case"h3":r||(o=n.subParser("makeMarkdown.header")(e,t,3)+"\n\n");break;case"h4":r||(o=n.subParser("makeMarkdown.header")(e,t,4)+"\n\n");break;case"h5":r||(o=n.subParser("makeMarkdown.header")(e,t,5)+"\n\n");break;case"h6":r||(o=n.subParser("makeMarkdown.header")(e,t,6)+"\n\n");break;case"p":r||(o=n.subParser("makeMarkdown.paragraph")(e,t)+"\n\n");break;case"blockquote":r||(o=n.subParser("makeMarkdown.blockquote")(e,t)+"\n\n");break;case"hr":r||(o=n.subParser("makeMarkdown.hr")(e,t)+"\n\n");break;case"ol":r||(o=n.subParser("makeMarkdown.list")(e,t,"ol")+"\n\n");break;case"ul":r||(o=n.subParser("makeMarkdown.list")(e,t,"ul")+"\n\n");break;case"precode":r||(o=n.subParser("makeMarkdown.codeBlock")(e,t)+"\n\n");break;case"pre":r||(o=n.subParser("makeMarkdown.pre")(e,t)+"\n\n");break;case"table":r||(o=n.subParser("makeMarkdown.table")(e,t)+"\n\n");break;case"code":o=n.subParser("makeMarkdown.codeSpan")(e,t);break;case"em":case"i":o=n.subParser("makeMarkdown.emphasis")(e,t);break;case"strong":case"b":o=n.subParser("makeMarkdown.strong")(e,t);break;case"del":o=n.subParser("makeMarkdown.strikethrough")(e,t);break;case"a":o=n.subParser("makeMarkdown.links")(e,t);break;case"img":o=n.subParser("makeMarkdown.image")(e,t);break;default:o=e.outerHTML+"\n\n"}return o})),n.subParser("makeMarkdown.paragraph",(function(e,t){var r="";if(e.hasChildNodes())for(var o=e.childNodes,a=o.length,i=0;i<a;++i)r+=n.subParser("makeMarkdown.node")(o[i],t);return r.trim()})),n.subParser("makeMarkdown.pre",(function(e,t){return e=e.getAttribute("prenum"),"<pre>"+t.preList[e]+"</pre>"})),n.subParser("makeMarkdown.strikethrough",(function(e,t){var r="";if(e.hasChildNodes()){r+="~~";for(var o=e.childNodes,a=o.length,i=0;i<a;++i)r+=n.subParser("makeMarkdown.node")(o[i],t);r+="~~"}return r})),n.subParser("makeMarkdown.strong",(function(e,t){var r="";if(e.hasChildNodes()){r+="**";for(var o=e.childNodes,a=o.length,i=0;i<a;++i)r+=n.subParser("makeMarkdown.node")(o[i],t);r+="**"}return r})),n.subParser("makeMarkdown.table",(function(e,t){for(var r="",o=[[],[]],a=e.querySelectorAll("thead>tr>th"),i=e.querySelectorAll("tbody>tr"),l=0;l<a.length;++l){var s=n.subParser("makeMarkdown.tableCell")(a[l],t),c="---";if(a[l].hasAttribute("style"))switch(a[l].getAttribute("style").toLowerCase().replace(/\s/g,"")){case"text-align:left;":c=":---";break;case"text-align:right;":c="---:";break;case"text-align:center;":c=":---:"}o[0][l]=s.trim(),o[1][l]=c}for(l=0;l<i.length;++l)for(var d=o.push([])-1,u=i[l].getElementsByTagName("td"),p=0;p<a.length;++p){var f=" ";void 0!==u[p]&&(f=n.subParser("makeMarkdown.tableCell")(u[p],t)),o[d].push(f)}var h=3;for(l=0;l<o.length;++l)for(p=0;p<o[l].length;++p){var m=o[l][p].length;h<m&&(h=m)}for(l=0;l<o.length;++l){for(p=0;p<o[l].length;++p)1===l?":"===o[l][p].slice(-1)?o[l][p]=n.helper.padEnd(o[l][p].slice(-1),h-1,"-")+":":o[l][p]=n.helper.padEnd(o[l][p],h,"-"):o[l][p]=n.helper.padEnd(o[l][p],h);r+="| "+o[l].join(" | ")+" |\n"}return r.trim()})),n.subParser("makeMarkdown.tableCell",(function(e,t){var r="";if(!e.hasChildNodes())return"";for(var o=e.childNodes,a=o.length,i=0;i<a;++i)r+=n.subParser("makeMarkdown.node")(o[i],t,!0);return r.trim()})),n.subParser("makeMarkdown.txt",(function(e){return e=e.nodeValue,e=(e=e.replace(/ +/g," ")).replace(/¨NBSP;/g," "),(e=(e=(e=(e=(e=(e=(e=(e=n.helper.unescapeHTMLEntities(e)).replace(/([*_~|`])/g,"\\$1")).replace(/^(\s*)>/g,"\\$1>")).replace(/^#/gm,"\\#")).replace(/^(\s*)([-=]{3,})(\s*)$/,"$1\\$2$3")).replace(/^( {0,3}\d+)\./gm,"$1\\.")).replace(/^( {0,3})([+-])/gm,"$1\\$2")).replace(/]([\s]*)\(/g,"\\]$1\\(")).replace(/^ {0,3}\[([\S \t]*?)]:/gm,"\\[$1]:")})),e.exports?e.exports=n:this.showdown=n}).call(c)}(u);var p=u.exports,f=d(p);const h=(0,r.aZ)({name:"VueShowdown",props:{markdown:{type:String,required:!1,default:null},tag:{type:String,required:!1,default:"div"},flavor:{type:String,required:!1,default:null},options:{type:Object,required:!1,default:()=>({})},extensions:{type:Array,required:!1,default:null},vueTemplate:{type:Boolean,required:!1,default:!1},vueTemplateComponents:{type:Object,required:!1,default:()=>({})},vueTemplateData:{type:Object,required:!1,default:()=>({})}},setup(e,{slots:t}){const n=(0,r.Fl)((()=>{const t=new f.Converter({extensions:e.extensions??void 0});return null!==e.flavor&&t.setFlavor(e.flavor),Object.entries(e.options).forEach((([e,n])=>{t.setOption(e,n)})),t})),o=(0,r.Fl)((()=>{if(null!==e.markdown)return e.markdown;var n=t.default?.()[0];return n?.type===r.xv?n.children:""})),a=(0,r.Fl)((()=>n.value.makeHtml(o.value)));return()=>e.vueTemplate?(0,r.h)({components:e.vueTemplateComponents,setup:()=>e.vueTemplateData,template:`<${e.tag}>${a.value}</${e.tag}>`}):(0,r.h)(e.tag,{innerHTML:a.value})}}),m={class:"q-pa-sm"},v={name:"SSMarkDownRenderer"};var g=Object.assign(v,{props:{markdown:{type:String,required:!0},loading:{type:Boolean,default:!1}},emits:["clickExternalLink"],setup(e,{emit:t}){const n=t,l=e=>{e.preventDefault();let t=e.target.closest("a");if(t&&"a"===t.tagName.toLowerCase()){let r=t.href;r.startsWith(window.location.origin)?r.includes("#")?(e=r.split("#").pop(),document.getElementById(e)?.scrollIntoView()):alert("Relative links are not supported."):n("clickExternalLink",r)}};return(t,n)=>((0,r.wg)(),(0,r.iD)("div",m,[(0,r._)("div",{onClick:l,class:"markdown-wrapper"},[(0,r.Wm)((0,o.SU)(h),{markdown:e.markdown,class:"markdown-body",flavor:"github"},null,8,["markdown"])]),(0,r.Wm)((0,o.SU)(a.Z),{showing:e.loading},{default:(0,r.w5)((()=>[(0,r.Wm)((0,o.SU)(i.Z),{color:"primary",size:"50px"})])),_:1},8,["showing"])]))}});function b(e,t){var n,r=(t=void 0===t?{}:t).insertAt;"undefined"!=typeof document&&(n=document.head||document.getElementsByTagName("head")[0],(t=document.createElement("style")).type="text/css","top"===r&&n.firstChild?n.insertBefore(t,n.firstChild):n.appendChild(t),t.styleSheet?t.styleSheet.cssText=e:t.appendChild(document.createTextNode(e)))}var w='.markdown-wrapper {\n  /*light*/\n}\n.markdown-wrapper .markdown-body {\n  -ms-text-size-adjust: 100%;\n  -webkit-text-size-adjust: 100%;\n  margin: 0;\n  color: #1f2328;\n  background-color: #ffffff;\n  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";\n  font-size: 16px;\n  line-height: 1.5;\n  word-wrap: break-word;\n  scroll-behavior: auto;\n}\n.markdown-wrapper .markdown-body .octicon {\n  display: inline-block;\n  fill: currentColor;\n  vertical-align: text-bottom;\n}\n.markdown-wrapper .markdown-body h1:hover .anchor .octicon-link:before,\n.markdown-wrapper .markdown-body h2:hover .anchor .octicon-link:before,\n.markdown-wrapper .markdown-body h3:hover .anchor .octicon-link:before,\n.markdown-wrapper .markdown-body h4:hover .anchor .octicon-link:before,\n.markdown-wrapper .markdown-body h5:hover .anchor .octicon-link:before,\n.markdown-wrapper .markdown-body h6:hover .anchor .octicon-link:before {\n  width: 16px;\n  height: 16px;\n  content: " ";\n  display: inline-block;\n  background-color: currentColor;\n  -webkit-mask-image: url("data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' version=\'1.1\' aria-hidden=\'true\'><path fill-rule=\'evenodd\' d=\'M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z\'></path></svg>");\n  mask-image: url("data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' version=\'1.1\' aria-hidden=\'true\'><path fill-rule=\'evenodd\' d=\'M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z\'></path></svg>");\n}\n.markdown-wrapper .markdown-body details,\n.markdown-wrapper .markdown-body figcaption,\n.markdown-wrapper .markdown-body figure {\n  display: block;\n}\n.markdown-wrapper .markdown-body summary {\n  display: list-item;\n}\n.markdown-wrapper .markdown-body [hidden] {\n  display: none !important;\n}\n.markdown-wrapper .markdown-body a {\n  background-color: transparent;\n  color: #0969da;\n  text-decoration: none;\n}\n.markdown-wrapper .markdown-body abbr[title] {\n  border-bottom: none;\n  -webkit-text-decoration: underline dotted;\n  text-decoration: underline dotted;\n}\n.markdown-wrapper .markdown-body b,\n.markdown-wrapper .markdown-body strong {\n  font-weight: 600;\n}\n.markdown-wrapper .markdown-body dfn {\n  font-style: italic;\n}\n.markdown-wrapper .markdown-body h1 {\n  margin: 0.67em 0;\n  font-weight: 600;\n  padding-bottom: 0.3em;\n  font-size: 2em;\n  border-bottom: 1px solid #d0d7deb3;\n}\n.markdown-wrapper .markdown-body mark {\n  background-color: #fff8c5;\n  color: #1f2328;\n}\n.markdown-wrapper .markdown-body small {\n  font-size: 90%;\n}\n.markdown-wrapper .markdown-body sub,\n.markdown-wrapper .markdown-body sup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n.markdown-wrapper .markdown-body sub {\n  bottom: -0.25em;\n}\n.markdown-wrapper .markdown-body sup {\n  top: -0.5em;\n}\n.markdown-wrapper .markdown-body img {\n  border-style: none;\n  max-width: 100%;\n  box-sizing: content-box;\n  background-color: #ffffff;\n}\n.markdown-wrapper .markdown-body code,\n.markdown-wrapper .markdown-body kbd,\n.markdown-wrapper .markdown-body pre,\n.markdown-wrapper .markdown-body samp {\n  font-family: monospace;\n  font-size: 1em;\n}\n.markdown-wrapper .markdown-body figure {\n  margin: 1em 40px;\n}\n.markdown-wrapper .markdown-body hr {\n  box-sizing: content-box;\n  overflow: hidden;\n  background: transparent;\n  border-bottom: 1px solid #d0d7deb3;\n  height: 0.25em;\n  padding: 0;\n  margin: 24px 0;\n  background-color: #d0d7de;\n  border: 0;\n}\n.markdown-wrapper .markdown-body input {\n  font: inherit;\n  margin: 0;\n  overflow: visible;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n.markdown-wrapper .markdown-body [type=button],\n.markdown-wrapper .markdown-body [type=reset],\n.markdown-wrapper .markdown-body [type=submit] {\n  -webkit-appearance: button;\n  appearance: button;\n}\n.markdown-wrapper .markdown-body [type=checkbox],\n.markdown-wrapper .markdown-body [type=radio] {\n  box-sizing: border-box;\n  padding: 0;\n}\n.markdown-wrapper .markdown-body [type=number]::-webkit-inner-spin-button,\n.markdown-wrapper .markdown-body [type=number]::-webkit-outer-spin-button {\n  height: auto;\n}\n.markdown-wrapper .markdown-body [type=search]::-webkit-search-cancel-button,\n.markdown-wrapper .markdown-body [type=search]::-webkit-search-decoration {\n  -webkit-appearance: none;\n  appearance: none;\n}\n.markdown-wrapper .markdown-body ::-webkit-input-placeholder {\n  color: inherit;\n  opacity: 0.54;\n}\n.markdown-wrapper .markdown-body ::-webkit-file-upload-button {\n  -webkit-appearance: button;\n  appearance: button;\n  font: inherit;\n}\n.markdown-wrapper .markdown-body a:hover {\n  text-decoration: underline;\n}\n.markdown-wrapper .markdown-body ::placeholder {\n  color: #636c76;\n  opacity: 1;\n}\n.markdown-wrapper .markdown-body hr::before {\n  display: table;\n  content: "";\n}\n.markdown-wrapper .markdown-body hr::after {\n  display: table;\n  clear: both;\n  content: "";\n}\n.markdown-wrapper .markdown-body table {\n  border-spacing: 0;\n  border-collapse: collapse;\n  display: block;\n  width: max-content;\n  max-width: 100%;\n  overflow: auto;\n}\n.markdown-wrapper .markdown-body td,\n.markdown-wrapper .markdown-body th {\n  padding: 0;\n}\n.markdown-wrapper .markdown-body details summary {\n  cursor: pointer;\n}\n.markdown-wrapper .markdown-body details:not([open]) > *:not(summary) {\n  display: none;\n}\n.markdown-wrapper .markdown-body a:focus,\n.markdown-wrapper .markdown-body [role=button]:focus,\n.markdown-wrapper .markdown-body input[type=radio]:focus,\n.markdown-wrapper .markdown-body input[type=checkbox]:focus {\n  outline: 2px solid #0969da;\n  outline-offset: -2px;\n  box-shadow: none;\n}\n.markdown-wrapper .markdown-body a:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body [role=button]:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body input[type=radio]:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body input[type=checkbox]:focus:not(:focus-visible) {\n  outline: solid 1px transparent;\n}\n.markdown-wrapper .markdown-body a:focus-visible,\n.markdown-wrapper .markdown-body [role=button]:focus-visible,\n.markdown-wrapper .markdown-body input[type=radio]:focus-visible,\n.markdown-wrapper .markdown-body input[type=checkbox]:focus-visible {\n  outline: 2px solid #0969da;\n  outline-offset: -2px;\n  box-shadow: none;\n}\n.markdown-wrapper .markdown-body a:not([class]):focus,\n.markdown-wrapper .markdown-body a:not([class]):focus-visible,\n.markdown-wrapper .markdown-body input[type=radio]:focus,\n.markdown-wrapper .markdown-body input[type=radio]:focus-visible,\n.markdown-wrapper .markdown-body input[type=checkbox]:focus,\n.markdown-wrapper .markdown-body input[type=checkbox]:focus-visible {\n  outline-offset: 0;\n}\n.markdown-wrapper .markdown-body kbd {\n  display: inline-block;\n  padding: 3px 5px;\n  font: 11px ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  line-height: 10px;\n  color: #1f2328;\n  vertical-align: middle;\n  background-color: #f6f8fa;\n  border: solid 1px #afb8c133;\n  border-bottom-color: #afb8c133;\n  border-radius: 6px;\n  box-shadow: inset 0 -1px 0 #afb8c133;\n}\n.markdown-wrapper .markdown-body h1,\n.markdown-wrapper .markdown-body h2,\n.markdown-wrapper .markdown-body h3,\n.markdown-wrapper .markdown-body h4,\n.markdown-wrapper .markdown-body h5,\n.markdown-wrapper .markdown-body h6 {\n  margin-top: 24px;\n  margin-bottom: 16px;\n  font-weight: 600;\n  line-height: 1.25;\n}\n.markdown-wrapper .markdown-body h2 {\n  font-weight: 600;\n  padding-bottom: 0.3em;\n  font-size: 1.5em;\n  border-bottom: 1px solid #d0d7deb3;\n}\n.markdown-wrapper .markdown-body h3 {\n  font-weight: 600;\n  font-size: 1.25em;\n}\n.markdown-wrapper .markdown-body h4 {\n  font-weight: 600;\n  font-size: 1em;\n}\n.markdown-wrapper .markdown-body h5 {\n  font-weight: 600;\n  font-size: 0.875em;\n}\n.markdown-wrapper .markdown-body h6 {\n  font-weight: 600;\n  font-size: 0.85em;\n  color: #636c76;\n}\n.markdown-wrapper .markdown-body p {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n.markdown-wrapper .markdown-body blockquote {\n  margin: 0;\n  padding: 0 1em;\n  color: #636c76;\n  border-left: 0.25em solid #d0d7de;\n}\n.markdown-wrapper .markdown-body ul,\n.markdown-wrapper .markdown-body ol {\n  margin-top: 0;\n  margin-bottom: 0;\n  padding-left: 2em;\n}\n.markdown-wrapper .markdown-body ol ol,\n.markdown-wrapper .markdown-body ul ol {\n  list-style-type: lower-roman;\n}\n.markdown-wrapper .markdown-body ul ul ol,\n.markdown-wrapper .markdown-body ul ol ol,\n.markdown-wrapper .markdown-body ol ul ol,\n.markdown-wrapper .markdown-body ol ol ol {\n  list-style-type: lower-alpha;\n}\n.markdown-wrapper .markdown-body dd {\n  margin-left: 0;\n}\n.markdown-wrapper .markdown-body tt,\n.markdown-wrapper .markdown-body code,\n.markdown-wrapper .markdown-body samp {\n  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  font-size: 12px;\n}\n.markdown-wrapper .markdown-body pre {\n  margin-top: 0;\n  margin-bottom: 0;\n  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  font-size: 12px;\n  word-wrap: normal;\n}\n.markdown-wrapper .markdown-body .octicon {\n  display: inline-block;\n  overflow: visible !important;\n  vertical-align: text-bottom;\n  fill: currentColor;\n}\n.markdown-wrapper .markdown-body input::-webkit-outer-spin-button,\n.markdown-wrapper .markdown-body input::-webkit-inner-spin-button {\n  margin: 0;\n  -webkit-appearance: none;\n  appearance: none;\n}\n.markdown-wrapper .markdown-body .mr-2 {\n  margin-right: 0.5rem !important;\n}\n.markdown-wrapper .markdown-body::before {\n  display: table;\n  content: "";\n}\n.markdown-wrapper .markdown-body::after {\n  display: table;\n  clear: both;\n  content: "";\n}\n.markdown-wrapper .markdown-body > *:first-child {\n  margin-top: 0 !important;\n}\n.markdown-wrapper .markdown-body > *:last-child {\n  margin-bottom: 0 !important;\n}\n.markdown-wrapper .markdown-body a:not([href]) {\n  color: inherit;\n  text-decoration: none;\n}\n.markdown-wrapper .markdown-body .absent {\n  color: #d1242f;\n}\n.markdown-wrapper .markdown-body .anchor {\n  float: left;\n  padding-right: 4px;\n  margin-left: -20px;\n  line-height: 1;\n}\n.markdown-wrapper .markdown-body .anchor:focus {\n  outline: none;\n}\n.markdown-wrapper .markdown-body p,\n.markdown-wrapper .markdown-body blockquote,\n.markdown-wrapper .markdown-body ul,\n.markdown-wrapper .markdown-body ol,\n.markdown-wrapper .markdown-body dl,\n.markdown-wrapper .markdown-body table,\n.markdown-wrapper .markdown-body pre,\n.markdown-wrapper .markdown-body details {\n  margin-top: 0;\n  margin-bottom: 16px;\n}\n.markdown-wrapper .markdown-body blockquote > :first-child {\n  margin-top: 0;\n}\n.markdown-wrapper .markdown-body blockquote > :last-child {\n  margin-bottom: 0;\n}\n.markdown-wrapper .markdown-body h1 .octicon-link,\n.markdown-wrapper .markdown-body h2 .octicon-link,\n.markdown-wrapper .markdown-body h3 .octicon-link,\n.markdown-wrapper .markdown-body h4 .octicon-link,\n.markdown-wrapper .markdown-body h5 .octicon-link,\n.markdown-wrapper .markdown-body h6 .octicon-link {\n  color: #1f2328;\n  vertical-align: middle;\n  visibility: hidden;\n}\n.markdown-wrapper .markdown-body h1:hover .anchor,\n.markdown-wrapper .markdown-body h2:hover .anchor,\n.markdown-wrapper .markdown-body h3:hover .anchor,\n.markdown-wrapper .markdown-body h4:hover .anchor,\n.markdown-wrapper .markdown-body h5:hover .anchor,\n.markdown-wrapper .markdown-body h6:hover .anchor {\n  text-decoration: none;\n}\n.markdown-wrapper .markdown-body h1:hover .anchor .octicon-link,\n.markdown-wrapper .markdown-body h2:hover .anchor .octicon-link,\n.markdown-wrapper .markdown-body h3:hover .anchor .octicon-link,\n.markdown-wrapper .markdown-body h4:hover .anchor .octicon-link,\n.markdown-wrapper .markdown-body h5:hover .anchor .octicon-link,\n.markdown-wrapper .markdown-body h6:hover .anchor .octicon-link {\n  visibility: visible;\n}\n.markdown-wrapper .markdown-body h1 tt,\n.markdown-wrapper .markdown-body h1 code,\n.markdown-wrapper .markdown-body h2 tt,\n.markdown-wrapper .markdown-body h2 code,\n.markdown-wrapper .markdown-body h3 tt,\n.markdown-wrapper .markdown-body h3 code,\n.markdown-wrapper .markdown-body h4 tt,\n.markdown-wrapper .markdown-body h4 code,\n.markdown-wrapper .markdown-body h5 tt,\n.markdown-wrapper .markdown-body h5 code,\n.markdown-wrapper .markdown-body h6 tt,\n.markdown-wrapper .markdown-body h6 code {\n  padding: 0 0.2em;\n  font-size: inherit;\n}\n.markdown-wrapper .markdown-body summary h1,\n.markdown-wrapper .markdown-body summary h2,\n.markdown-wrapper .markdown-body summary h3,\n.markdown-wrapper .markdown-body summary h4,\n.markdown-wrapper .markdown-body summary h5,\n.markdown-wrapper .markdown-body summary h6 {\n  display: inline-block;\n}\n.markdown-wrapper .markdown-body summary h1 .anchor,\n.markdown-wrapper .markdown-body summary h2 .anchor,\n.markdown-wrapper .markdown-body summary h3 .anchor,\n.markdown-wrapper .markdown-body summary h4 .anchor,\n.markdown-wrapper .markdown-body summary h5 .anchor,\n.markdown-wrapper .markdown-body summary h6 .anchor {\n  margin-left: -40px;\n}\n.markdown-wrapper .markdown-body summary h1,\n.markdown-wrapper .markdown-body summary h2 {\n  padding-bottom: 0;\n  border-bottom: 0;\n}\n.markdown-wrapper .markdown-body ul.no-list,\n.markdown-wrapper .markdown-body ol.no-list {\n  padding: 0;\n  list-style-type: none;\n}\n.markdown-wrapper .markdown-body ol[type="a s"] {\n  list-style-type: lower-alpha;\n}\n.markdown-wrapper .markdown-body ol[type="A s"] {\n  list-style-type: upper-alpha;\n}\n.markdown-wrapper .markdown-body ol[type="i s"] {\n  list-style-type: lower-roman;\n}\n.markdown-wrapper .markdown-body ol[type="I s"] {\n  list-style-type: upper-roman;\n}\n.markdown-wrapper .markdown-body ol[type="1"] {\n  list-style-type: decimal;\n}\n.markdown-wrapper .markdown-body div > ol:not([type]) {\n  list-style-type: decimal;\n}\n.markdown-wrapper .markdown-body ul ul,\n.markdown-wrapper .markdown-body ul ol,\n.markdown-wrapper .markdown-body ol ol,\n.markdown-wrapper .markdown-body ol ul {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.markdown-wrapper .markdown-body li > p {\n  margin-top: 16px;\n}\n.markdown-wrapper .markdown-body li + li {\n  margin-top: 0.25em;\n}\n.markdown-wrapper .markdown-body dl {\n  padding: 0;\n}\n.markdown-wrapper .markdown-body dl dt {\n  padding: 0;\n  margin-top: 16px;\n  font-size: 1em;\n  font-style: italic;\n  font-weight: 600;\n}\n.markdown-wrapper .markdown-body dl dd {\n  padding: 0 16px;\n  margin-bottom: 16px;\n}\n.markdown-wrapper .markdown-body table th {\n  font-weight: 600;\n}\n.markdown-wrapper .markdown-body table th,\n.markdown-wrapper .markdown-body table td {\n  padding: 6px 13px;\n  border: 1px solid #d0d7de;\n}\n.markdown-wrapper .markdown-body table td > :last-child {\n  margin-bottom: 0;\n}\n.markdown-wrapper .markdown-body table tr {\n  background-color: #ffffff;\n  border-top: 1px solid #d0d7deb3;\n}\n.markdown-wrapper .markdown-body table tr:nth-child(2n) {\n  background-color: #f6f8fa;\n}\n.markdown-wrapper .markdown-body table img {\n  background-color: transparent;\n}\n.markdown-wrapper .markdown-body img[align=right] {\n  padding-left: 20px;\n}\n.markdown-wrapper .markdown-body img[align=left] {\n  padding-right: 20px;\n}\n.markdown-wrapper .markdown-body .emoji {\n  max-width: none;\n  vertical-align: text-top;\n  background-color: transparent;\n}\n.markdown-wrapper .markdown-body span.frame {\n  display: block;\n  overflow: hidden;\n}\n.markdown-wrapper .markdown-body span.frame > span {\n  display: block;\n  float: left;\n  width: auto;\n  padding: 7px;\n  margin: 13px 0 0;\n  overflow: hidden;\n  border: 1px solid #d0d7de;\n}\n.markdown-wrapper .markdown-body span.frame span img {\n  display: block;\n  float: left;\n}\n.markdown-wrapper .markdown-body span.frame span span {\n  display: block;\n  padding: 5px 0 0;\n  clear: both;\n  color: #1f2328;\n}\n.markdown-wrapper .markdown-body span.align-center {\n  display: block;\n  overflow: hidden;\n  clear: both;\n}\n.markdown-wrapper .markdown-body span.align-center > span {\n  display: block;\n  margin: 13px auto 0;\n  overflow: hidden;\n  text-align: center;\n}\n.markdown-wrapper .markdown-body span.align-center span img {\n  margin: 0 auto;\n  text-align: center;\n}\n.markdown-wrapper .markdown-body span.align-right {\n  display: block;\n  overflow: hidden;\n  clear: both;\n}\n.markdown-wrapper .markdown-body span.align-right > span {\n  display: block;\n  margin: 13px 0 0;\n  overflow: hidden;\n  text-align: right;\n}\n.markdown-wrapper .markdown-body span.align-right span img {\n  margin: 0;\n  text-align: right;\n}\n.markdown-wrapper .markdown-body span.float-left {\n  display: block;\n  float: left;\n  margin-right: 13px;\n  overflow: hidden;\n}\n.markdown-wrapper .markdown-body span.float-left span {\n  margin: 13px 0 0;\n}\n.markdown-wrapper .markdown-body span.float-right {\n  display: block;\n  float: right;\n  margin-left: 13px;\n  overflow: hidden;\n}\n.markdown-wrapper .markdown-body span.float-right > span {\n  display: block;\n  margin: 13px auto 0;\n  overflow: hidden;\n  text-align: right;\n}\n.markdown-wrapper .markdown-body code,\n.markdown-wrapper .markdown-body tt {\n  padding: 0.2em 0.4em;\n  margin: 0;\n  font-size: 85%;\n  white-space: break-spaces;\n  background-color: #afb8c133;\n  border-radius: 6px;\n}\n.markdown-wrapper .markdown-body code br,\n.markdown-wrapper .markdown-body tt br {\n  display: none;\n}\n.markdown-wrapper .markdown-body del code {\n  text-decoration: inherit;\n}\n.markdown-wrapper .markdown-body samp {\n  font-size: 85%;\n}\n.markdown-wrapper .markdown-body pre code {\n  font-size: 100%;\n}\n.markdown-wrapper .markdown-body pre > code {\n  padding: 0;\n  margin: 0;\n  word-break: normal;\n  white-space: pre;\n  background: transparent;\n  border: 0;\n}\n.markdown-wrapper .markdown-body .highlight {\n  margin-bottom: 16px;\n}\n.markdown-wrapper .markdown-body .highlight pre {\n  margin-bottom: 0;\n  word-break: normal;\n}\n.markdown-wrapper .markdown-body .highlight pre,\n.markdown-wrapper .markdown-body pre {\n  padding: 16px;\n  overflow: auto;\n  font-size: 85%;\n  line-height: 1.45;\n  color: #1f2328;\n  background-color: #f6f8fa;\n  border-radius: 6px;\n}\n.markdown-wrapper .markdown-body pre code,\n.markdown-wrapper .markdown-body pre tt {\n  display: inline;\n  max-width: auto;\n  padding: 0;\n  margin: 0;\n  overflow: visible;\n  line-height: inherit;\n  word-wrap: normal;\n  background-color: transparent;\n  border: 0;\n}\n.markdown-wrapper .markdown-body .csv-data td,\n.markdown-wrapper .markdown-body .csv-data th {\n  padding: 5px;\n  overflow: hidden;\n  font-size: 12px;\n  line-height: 1;\n  text-align: left;\n  white-space: nowrap;\n}\n.markdown-wrapper .markdown-body .csv-data .blob-num {\n  padding: 10px 8px 9px;\n  text-align: right;\n  background: #ffffff;\n  border: 0;\n}\n.markdown-wrapper .markdown-body .csv-data tr {\n  border-top: 0;\n}\n.markdown-wrapper .markdown-body .csv-data th {\n  font-weight: 600;\n  background: #f6f8fa;\n  border-top: 0;\n}\n.markdown-wrapper .markdown-body [data-footnote-ref]::before {\n  content: "[";\n}\n.markdown-wrapper .markdown-body [data-footnote-ref]::after {\n  content: "]";\n}\n.markdown-wrapper .markdown-body .footnotes {\n  font-size: 12px;\n  color: #636c76;\n  border-top: 1px solid #d0d7de;\n}\n.markdown-wrapper .markdown-body .footnotes ol {\n  padding-left: 16px;\n}\n.markdown-wrapper .markdown-body .footnotes ol ul {\n  display: inline-block;\n  padding-left: 16px;\n  margin-top: 16px;\n}\n.markdown-wrapper .markdown-body .footnotes li {\n  position: relative;\n}\n.markdown-wrapper .markdown-body .footnotes li:target::before {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  bottom: -8px;\n  left: -24px;\n  pointer-events: none;\n  content: "";\n  border: 2px solid #0969da;\n  border-radius: 6px;\n}\n.markdown-wrapper .markdown-body .footnotes li:target {\n  color: #1f2328;\n}\n.markdown-wrapper .markdown-body .footnotes .data-footnote-backref g-emoji {\n  font-family: monospace;\n}\n.markdown-wrapper .markdown-body .pl-c {\n  color: #57606a;\n}\n.markdown-wrapper .markdown-body .pl-c1,\n.markdown-wrapper .markdown-body .pl-s .pl-v {\n  color: #0550ae;\n}\n.markdown-wrapper .markdown-body .pl-e,\n.markdown-wrapper .markdown-body .pl-en {\n  color: #6639ba;\n}\n.markdown-wrapper .markdown-body .pl-smi,\n.markdown-wrapper .markdown-body .pl-s .pl-s1 {\n  color: #24292f;\n}\n.markdown-wrapper .markdown-body .pl-ent {\n  color: #0550ae;\n}\n.markdown-wrapper .markdown-body .pl-k {\n  color: #cf222e;\n}\n.markdown-wrapper .markdown-body .pl-s,\n.markdown-wrapper .markdown-body .pl-pds,\n.markdown-wrapper .markdown-body .pl-s .pl-pse .pl-s1,\n.markdown-wrapper .markdown-body .pl-sr,\n.markdown-wrapper .markdown-body .pl-sr .pl-cce,\n.markdown-wrapper .markdown-body .pl-sr .pl-sre,\n.markdown-wrapper .markdown-body .pl-sr .pl-sra {\n  color: #0a3069;\n}\n.markdown-wrapper .markdown-body .pl-v,\n.markdown-wrapper .markdown-body .pl-smw {\n  color: #953800;\n}\n.markdown-wrapper .markdown-body .pl-bu {\n  color: #82071e;\n}\n.markdown-wrapper .markdown-body .pl-ii {\n  color: #f6f8fa;\n  background-color: #82071e;\n}\n.markdown-wrapper .markdown-body .pl-c2 {\n  color: #f6f8fa;\n  background-color: #cf222e;\n}\n.markdown-wrapper .markdown-body .pl-sr .pl-cce {\n  font-weight: bold;\n  color: #116329;\n}\n.markdown-wrapper .markdown-body .pl-ml {\n  color: #3b2300;\n}\n.markdown-wrapper .markdown-body .pl-mh,\n.markdown-wrapper .markdown-body .pl-mh .pl-en,\n.markdown-wrapper .markdown-body .pl-ms {\n  font-weight: bold;\n  color: #0550ae;\n}\n.markdown-wrapper .markdown-body .pl-mi {\n  font-style: italic;\n  color: #24292f;\n}\n.markdown-wrapper .markdown-body .pl-mb {\n  font-weight: bold;\n  color: #24292f;\n}\n.markdown-wrapper .markdown-body .pl-md {\n  color: #82071e;\n  background-color: #ffebe9;\n}\n.markdown-wrapper .markdown-body .pl-mi1 {\n  color: #116329;\n  background-color: #dafbe1;\n}\n.markdown-wrapper .markdown-body .pl-mc {\n  color: #953800;\n  background-color: #ffd8b5;\n}\n.markdown-wrapper .markdown-body .pl-mi2 {\n  color: #eaeef2;\n  background-color: #0550ae;\n}\n.markdown-wrapper .markdown-body .pl-mdr {\n  font-weight: bold;\n  color: #8250df;\n}\n.markdown-wrapper .markdown-body .pl-ba {\n  color: #57606a;\n}\n.markdown-wrapper .markdown-body .pl-sg {\n  color: #8c959f;\n}\n.markdown-wrapper .markdown-body .pl-corl {\n  text-decoration: underline;\n  color: #0a3069;\n}\n.markdown-wrapper .markdown-body [role=button]:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body [role=tabpanel][tabindex="0"]:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body button:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body summary:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body a:focus:not(:focus-visible) {\n  outline: none;\n  box-shadow: none;\n}\n.markdown-wrapper .markdown-body [tabindex="0"]:focus:not(:focus-visible),\n.markdown-wrapper .markdown-body details-dialog:focus:not(:focus-visible) {\n  outline: none;\n}\n.markdown-wrapper .markdown-body g-emoji {\n  display: inline-block;\n  min-width: 1ch;\n  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";\n  font-size: 1em;\n  font-style: normal !important;\n  font-weight: 400;\n  line-height: 1;\n  vertical-align: -0.075em;\n}\n.markdown-wrapper .markdown-body g-emoji img {\n  width: 1em;\n  height: 1em;\n}\n.markdown-wrapper .markdown-body .task-list-item {\n  list-style-type: none;\n}\n.markdown-wrapper .markdown-body .task-list-item label {\n  font-weight: 400;\n}\n.markdown-wrapper .markdown-body .task-list-item.enabled label {\n  cursor: pointer;\n}\n.markdown-wrapper .markdown-body .task-list-item + .task-list-item {\n  margin-top: 0.25rem;\n}\n.markdown-wrapper .markdown-body .task-list-item .handle {\n  display: none;\n}\n.markdown-wrapper .markdown-body .task-list-item-checkbox {\n  margin: 0 0.2em 0.25em -1.4em;\n  vertical-align: middle;\n}\n.markdown-wrapper .markdown-body .contains-task-list:dir(rtl) .task-list-item-checkbox {\n  margin: 0 -1.6em 0.25em 0.2em;\n}\n.markdown-wrapper .markdown-body .contains-task-list {\n  position: relative;\n}\n.markdown-wrapper .markdown-body .contains-task-list:hover .task-list-item-convert-container,\n.markdown-wrapper .markdown-body .contains-task-list:focus-within .task-list-item-convert-container {\n  display: block;\n  width: auto;\n  height: 24px;\n  overflow: visible;\n  clip: auto;\n}\n.markdown-wrapper .markdown-body ::-webkit-calendar-picker-indicator {\n  filter: invert(50%);\n}\n.markdown-wrapper .markdown-body .markdown-alert {\n  padding: 0.5rem 1rem;\n  margin-bottom: 1rem;\n  color: inherit;\n  border-left: 0.25em solid #d0d7de;\n}\n.markdown-wrapper .markdown-body .markdown-alert > :first-child {\n  margin-top: 0;\n}\n.markdown-wrapper .markdown-body .markdown-alert > :last-child {\n  margin-bottom: 0;\n}\n.markdown-wrapper .markdown-body .markdown-alert .markdown-alert-title {\n  display: flex;\n  font-weight: 500;\n  align-items: center;\n  line-height: 1;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-note {\n  border-left-color: #0969da;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-note .markdown-alert-title {\n  color: #0969da;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-important {\n  border-left-color: #8250df;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-important .markdown-alert-title {\n  color: #8250df;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-warning {\n  border-left-color: #bf8700;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-warning .markdown-alert-title {\n  color: #9a6700;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-tip {\n  border-left-color: #1a7f37;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-tip .markdown-alert-title {\n  color: #1a7f37;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-caution {\n  border-left-color: #cf222e;\n}\n.markdown-wrapper .markdown-body .markdown-alert.markdown-alert-caution .markdown-alert-title {\n  color: #d1242f;\n}\n.markdown-wrapper .markdown-body > *:first-child > .heading-element:first-child {\n  margin-top: 0 !important;\n}\n.markdown-wrapper .markdown-body {\n  background-color: inherit !important;\n}\n.body--dark .markdown-wrapper {\n  /*dark*/\n}\n.body--dark .markdown-wrapper .markdown-body {\n  color-scheme: dark;\n  -ms-text-size-adjust: 100%;\n  -webkit-text-size-adjust: 100%;\n  margin: 0;\n  color: #e6edf3;\n  background-color: #0d1117;\n  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";\n  font-size: 16px;\n  line-height: 1.5;\n  word-wrap: break-word;\n  scroll-behavior: auto;\n}\n.body--dark .markdown-wrapper .markdown-body .octicon {\n  display: inline-block;\n  fill: currentColor;\n  vertical-align: text-bottom;\n}\n.body--dark .markdown-wrapper .markdown-body h1:hover .anchor .octicon-link:before,\n.body--dark .markdown-wrapper .markdown-body h2:hover .anchor .octicon-link:before,\n.body--dark .markdown-wrapper .markdown-body h3:hover .anchor .octicon-link:before,\n.body--dark .markdown-wrapper .markdown-body h4:hover .anchor .octicon-link:before,\n.body--dark .markdown-wrapper .markdown-body h5:hover .anchor .octicon-link:before,\n.body--dark .markdown-wrapper .markdown-body h6:hover .anchor .octicon-link:before {\n  width: 16px;\n  height: 16px;\n  content: " ";\n  display: inline-block;\n  background-color: currentColor;\n  -webkit-mask-image: url("data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' version=\'1.1\' aria-hidden=\'true\'><path fill-rule=\'evenodd\' d=\'M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z\'></path></svg>");\n  mask-image: url("data:image/svg+xml,<svg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 16 16\' version=\'1.1\' aria-hidden=\'true\'><path fill-rule=\'evenodd\' d=\'M7.775 3.275a.75.75 0 001.06 1.06l1.25-1.25a2 2 0 112.83 2.83l-2.5 2.5a2 2 0 01-2.83 0 .75.75 0 00-1.06 1.06 3.5 3.5 0 004.95 0l2.5-2.5a3.5 3.5 0 00-4.95-4.95l-1.25 1.25zm-4.69 9.64a2 2 0 010-2.83l2.5-2.5a2 2 0 012.83 0 .75.75 0 001.06-1.06 3.5 3.5 0 00-4.95 0l-2.5 2.5a3.5 3.5 0 004.95 4.95l1.25-1.25a.75.75 0 00-1.06-1.06l-1.25 1.25a2 2 0 01-2.83 0z\'></path></svg>");\n}\n.body--dark .markdown-wrapper .markdown-body details,\n.body--dark .markdown-wrapper .markdown-body figcaption,\n.body--dark .markdown-wrapper .markdown-body figure {\n  display: block;\n}\n.body--dark .markdown-wrapper .markdown-body summary {\n  display: list-item;\n}\n.body--dark .markdown-wrapper .markdown-body [hidden] {\n  display: none !important;\n}\n.body--dark .markdown-wrapper .markdown-body a {\n  background-color: transparent;\n  color: #4493f8;\n  text-decoration: none;\n}\n.body--dark .markdown-wrapper .markdown-body abbr[title] {\n  border-bottom: none;\n  -webkit-text-decoration: underline dotted;\n  text-decoration: underline dotted;\n}\n.body--dark .markdown-wrapper .markdown-body b,\n.body--dark .markdown-wrapper .markdown-body strong {\n  font-weight: 600;\n}\n.body--dark .markdown-wrapper .markdown-body dfn {\n  font-style: italic;\n}\n.body--dark .markdown-wrapper .markdown-body h1 {\n  margin: 0.67em 0;\n  font-weight: 600;\n  padding-bottom: 0.3em;\n  font-size: 2em;\n  border-bottom: 1px solid #30363db3;\n}\n.body--dark .markdown-wrapper .markdown-body mark {\n  background-color: #bb800926;\n  color: #e6edf3;\n}\n.body--dark .markdown-wrapper .markdown-body small {\n  font-size: 90%;\n}\n.body--dark .markdown-wrapper .markdown-body sub,\n.body--dark .markdown-wrapper .markdown-body sup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n.body--dark .markdown-wrapper .markdown-body sub {\n  bottom: -0.25em;\n}\n.body--dark .markdown-wrapper .markdown-body sup {\n  top: -0.5em;\n}\n.body--dark .markdown-wrapper .markdown-body img {\n  border-style: none;\n  max-width: 100%;\n  box-sizing: content-box;\n  background-color: #0d1117;\n}\n.body--dark .markdown-wrapper .markdown-body code,\n.body--dark .markdown-wrapper .markdown-body kbd,\n.body--dark .markdown-wrapper .markdown-body pre,\n.body--dark .markdown-wrapper .markdown-body samp {\n  font-family: monospace;\n  font-size: 1em;\n}\n.body--dark .markdown-wrapper .markdown-body figure {\n  margin: 1em 40px;\n}\n.body--dark .markdown-wrapper .markdown-body hr {\n  box-sizing: content-box;\n  overflow: hidden;\n  background: transparent;\n  border-bottom: 1px solid #30363db3;\n  height: 0.25em;\n  padding: 0;\n  margin: 24px 0;\n  background-color: #30363d;\n  border: 0;\n}\n.body--dark .markdown-wrapper .markdown-body input {\n  font: inherit;\n  margin: 0;\n  overflow: visible;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: inherit;\n}\n.body--dark .markdown-wrapper .markdown-body [type=button],\n.body--dark .markdown-wrapper .markdown-body [type=reset],\n.body--dark .markdown-wrapper .markdown-body [type=submit] {\n  -webkit-appearance: button;\n  appearance: button;\n}\n.body--dark .markdown-wrapper .markdown-body [type=checkbox],\n.body--dark .markdown-wrapper .markdown-body [type=radio] {\n  box-sizing: border-box;\n  padding: 0;\n}\n.body--dark .markdown-wrapper .markdown-body [type=number]::-webkit-inner-spin-button,\n.body--dark .markdown-wrapper .markdown-body [type=number]::-webkit-outer-spin-button {\n  height: auto;\n}\n.body--dark .markdown-wrapper .markdown-body [type=search]::-webkit-search-cancel-button,\n.body--dark .markdown-wrapper .markdown-body [type=search]::-webkit-search-decoration {\n  -webkit-appearance: none;\n  appearance: none;\n}\n.body--dark .markdown-wrapper .markdown-body ::-webkit-input-placeholder {\n  color: inherit;\n  opacity: 0.54;\n}\n.body--dark .markdown-wrapper .markdown-body ::-webkit-file-upload-button {\n  -webkit-appearance: button;\n  appearance: button;\n  font: inherit;\n}\n.body--dark .markdown-wrapper .markdown-body a:hover {\n  text-decoration: underline;\n}\n.body--dark .markdown-wrapper .markdown-body ::placeholder {\n  color: #8d96a0;\n  opacity: 1;\n}\n.body--dark .markdown-wrapper .markdown-body hr::before {\n  display: table;\n  content: "";\n}\n.body--dark .markdown-wrapper .markdown-body hr::after {\n  display: table;\n  clear: both;\n  content: "";\n}\n.body--dark .markdown-wrapper .markdown-body table {\n  border-spacing: 0;\n  border-collapse: collapse;\n  display: block;\n  width: max-content;\n  max-width: 100%;\n  overflow: auto;\n}\n.body--dark .markdown-wrapper .markdown-body td,\n.body--dark .markdown-wrapper .markdown-body th {\n  padding: 0;\n}\n.body--dark .markdown-wrapper .markdown-body details summary {\n  cursor: pointer;\n}\n.body--dark .markdown-wrapper .markdown-body details:not([open]) > *:not(summary) {\n  display: none;\n}\n.body--dark .markdown-wrapper .markdown-body a:focus,\n.body--dark .markdown-wrapper .markdown-body [role=button]:focus,\n.body--dark .markdown-wrapper .markdown-body input[type=radio]:focus,\n.body--dark .markdown-wrapper .markdown-body input[type=checkbox]:focus {\n  outline: 2px solid #1f6feb;\n  outline-offset: -2px;\n  box-shadow: none;\n}\n.body--dark .markdown-wrapper .markdown-body a:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body [role=button]:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body input[type=radio]:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body input[type=checkbox]:focus:not(:focus-visible) {\n  outline: solid 1px transparent;\n}\n.body--dark .markdown-wrapper .markdown-body a:focus-visible,\n.body--dark .markdown-wrapper .markdown-body [role=button]:focus-visible,\n.body--dark .markdown-wrapper .markdown-body input[type=radio]:focus-visible,\n.body--dark .markdown-wrapper .markdown-body input[type=checkbox]:focus-visible {\n  outline: 2px solid #1f6feb;\n  outline-offset: -2px;\n  box-shadow: none;\n}\n.body--dark .markdown-wrapper .markdown-body a:not([class]):focus,\n.body--dark .markdown-wrapper .markdown-body a:not([class]):focus-visible,\n.body--dark .markdown-wrapper .markdown-body input[type=radio]:focus,\n.body--dark .markdown-wrapper .markdown-body input[type=radio]:focus-visible,\n.body--dark .markdown-wrapper .markdown-body input[type=checkbox]:focus,\n.body--dark .markdown-wrapper .markdown-body input[type=checkbox]:focus-visible {\n  outline-offset: 0;\n}\n.body--dark .markdown-wrapper .markdown-body kbd {\n  display: inline-block;\n  padding: 3px 5px;\n  font: 11px ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  line-height: 10px;\n  color: #e6edf3;\n  vertical-align: middle;\n  background-color: #161b22;\n  border: solid 1px #6e768166;\n  border-bottom-color: #6e768166;\n  border-radius: 6px;\n  box-shadow: inset 0 -1px 0 #6e768166;\n}\n.body--dark .markdown-wrapper .markdown-body h1,\n.body--dark .markdown-wrapper .markdown-body h2,\n.body--dark .markdown-wrapper .markdown-body h3,\n.body--dark .markdown-wrapper .markdown-body h4,\n.body--dark .markdown-wrapper .markdown-body h5,\n.body--dark .markdown-wrapper .markdown-body h6 {\n  margin-top: 24px;\n  margin-bottom: 16px;\n  font-weight: 600;\n  line-height: 1.25;\n}\n.body--dark .markdown-wrapper .markdown-body h2 {\n  font-weight: 600;\n  padding-bottom: 0.3em;\n  font-size: 1.5em;\n  border-bottom: 1px solid #30363db3;\n}\n.body--dark .markdown-wrapper .markdown-body h3 {\n  font-weight: 600;\n  font-size: 1.25em;\n}\n.body--dark .markdown-wrapper .markdown-body h4 {\n  font-weight: 600;\n  font-size: 1em;\n}\n.body--dark .markdown-wrapper .markdown-body h5 {\n  font-weight: 600;\n  font-size: 0.875em;\n}\n.body--dark .markdown-wrapper .markdown-body h6 {\n  font-weight: 600;\n  font-size: 0.85em;\n  color: #8d96a0;\n}\n.body--dark .markdown-wrapper .markdown-body p {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n.body--dark .markdown-wrapper .markdown-body blockquote {\n  margin: 0;\n  padding: 0 1em;\n  color: #8d96a0;\n  border-left: 0.25em solid #30363d;\n}\n.body--dark .markdown-wrapper .markdown-body ul,\n.body--dark .markdown-wrapper .markdown-body ol {\n  margin-top: 0;\n  margin-bottom: 0;\n  padding-left: 2em;\n}\n.body--dark .markdown-wrapper .markdown-body ol ol,\n.body--dark .markdown-wrapper .markdown-body ul ol {\n  list-style-type: lower-roman;\n}\n.body--dark .markdown-wrapper .markdown-body ul ul ol,\n.body--dark .markdown-wrapper .markdown-body ul ol ol,\n.body--dark .markdown-wrapper .markdown-body ol ul ol,\n.body--dark .markdown-wrapper .markdown-body ol ol ol {\n  list-style-type: lower-alpha;\n}\n.body--dark .markdown-wrapper .markdown-body dd {\n  margin-left: 0;\n}\n.body--dark .markdown-wrapper .markdown-body tt,\n.body--dark .markdown-wrapper .markdown-body code,\n.body--dark .markdown-wrapper .markdown-body samp {\n  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  font-size: 12px;\n}\n.body--dark .markdown-wrapper .markdown-body pre {\n  margin-top: 0;\n  margin-bottom: 0;\n  font-family: ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace;\n  font-size: 12px;\n  word-wrap: normal;\n}\n.body--dark .markdown-wrapper .markdown-body .octicon {\n  display: inline-block;\n  overflow: visible !important;\n  vertical-align: text-bottom;\n  fill: currentColor;\n}\n.body--dark .markdown-wrapper .markdown-body input::-webkit-outer-spin-button,\n.body--dark .markdown-wrapper .markdown-body input::-webkit-inner-spin-button {\n  margin: 0;\n  -webkit-appearance: none;\n  appearance: none;\n}\n.body--dark .markdown-wrapper .markdown-body .mr-2 {\n  margin-right: 0.5rem !important;\n}\n.body--dark .markdown-wrapper .markdown-body::before {\n  display: table;\n  content: "";\n}\n.body--dark .markdown-wrapper .markdown-body::after {\n  display: table;\n  clear: both;\n  content: "";\n}\n.body--dark .markdown-wrapper .markdown-body > *:first-child {\n  margin-top: 0 !important;\n}\n.body--dark .markdown-wrapper .markdown-body > *:last-child {\n  margin-bottom: 0 !important;\n}\n.body--dark .markdown-wrapper .markdown-body a:not([href]) {\n  color: inherit;\n  text-decoration: none;\n}\n.body--dark .markdown-wrapper .markdown-body .absent {\n  color: #f85149;\n}\n.body--dark .markdown-wrapper .markdown-body .anchor {\n  float: left;\n  padding-right: 4px;\n  margin-left: -20px;\n  line-height: 1;\n}\n.body--dark .markdown-wrapper .markdown-body .anchor:focus {\n  outline: none;\n}\n.body--dark .markdown-wrapper .markdown-body p,\n.body--dark .markdown-wrapper .markdown-body blockquote,\n.body--dark .markdown-wrapper .markdown-body ul,\n.body--dark .markdown-wrapper .markdown-body ol,\n.body--dark .markdown-wrapper .markdown-body dl,\n.body--dark .markdown-wrapper .markdown-body table,\n.body--dark .markdown-wrapper .markdown-body pre,\n.body--dark .markdown-wrapper .markdown-body details {\n  margin-top: 0;\n  margin-bottom: 16px;\n}\n.body--dark .markdown-wrapper .markdown-body blockquote > :first-child {\n  margin-top: 0;\n}\n.body--dark .markdown-wrapper .markdown-body blockquote > :last-child {\n  margin-bottom: 0;\n}\n.body--dark .markdown-wrapper .markdown-body h1 .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h2 .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h3 .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h4 .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h5 .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h6 .octicon-link {\n  color: #e6edf3;\n  vertical-align: middle;\n  visibility: hidden;\n}\n.body--dark .markdown-wrapper .markdown-body h1:hover .anchor,\n.body--dark .markdown-wrapper .markdown-body h2:hover .anchor,\n.body--dark .markdown-wrapper .markdown-body h3:hover .anchor,\n.body--dark .markdown-wrapper .markdown-body h4:hover .anchor,\n.body--dark .markdown-wrapper .markdown-body h5:hover .anchor,\n.body--dark .markdown-wrapper .markdown-body h6:hover .anchor {\n  text-decoration: none;\n}\n.body--dark .markdown-wrapper .markdown-body h1:hover .anchor .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h2:hover .anchor .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h3:hover .anchor .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h4:hover .anchor .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h5:hover .anchor .octicon-link,\n.body--dark .markdown-wrapper .markdown-body h6:hover .anchor .octicon-link {\n  visibility: visible;\n}\n.body--dark .markdown-wrapper .markdown-body h1 tt,\n.body--dark .markdown-wrapper .markdown-body h1 code,\n.body--dark .markdown-wrapper .markdown-body h2 tt,\n.body--dark .markdown-wrapper .markdown-body h2 code,\n.body--dark .markdown-wrapper .markdown-body h3 tt,\n.body--dark .markdown-wrapper .markdown-body h3 code,\n.body--dark .markdown-wrapper .markdown-body h4 tt,\n.body--dark .markdown-wrapper .markdown-body h4 code,\n.body--dark .markdown-wrapper .markdown-body h5 tt,\n.body--dark .markdown-wrapper .markdown-body h5 code,\n.body--dark .markdown-wrapper .markdown-body h6 tt,\n.body--dark .markdown-wrapper .markdown-body h6 code {\n  padding: 0 0.2em;\n  font-size: inherit;\n}\n.body--dark .markdown-wrapper .markdown-body summary h1,\n.body--dark .markdown-wrapper .markdown-body summary h2,\n.body--dark .markdown-wrapper .markdown-body summary h3,\n.body--dark .markdown-wrapper .markdown-body summary h4,\n.body--dark .markdown-wrapper .markdown-body summary h5,\n.body--dark .markdown-wrapper .markdown-body summary h6 {\n  display: inline-block;\n}\n.body--dark .markdown-wrapper .markdown-body summary h1 .anchor,\n.body--dark .markdown-wrapper .markdown-body summary h2 .anchor,\n.body--dark .markdown-wrapper .markdown-body summary h3 .anchor,\n.body--dark .markdown-wrapper .markdown-body summary h4 .anchor,\n.body--dark .markdown-wrapper .markdown-body summary h5 .anchor,\n.body--dark .markdown-wrapper .markdown-body summary h6 .anchor {\n  margin-left: -40px;\n}\n.body--dark .markdown-wrapper .markdown-body summary h1,\n.body--dark .markdown-wrapper .markdown-body summary h2 {\n  padding-bottom: 0;\n  border-bottom: 0;\n}\n.body--dark .markdown-wrapper .markdown-body ul.no-list,\n.body--dark .markdown-wrapper .markdown-body ol.no-list {\n  padding: 0;\n  list-style-type: none;\n}\n.body--dark .markdown-wrapper .markdown-body ol[type="a s"] {\n  list-style-type: lower-alpha;\n}\n.body--dark .markdown-wrapper .markdown-body ol[type="A s"] {\n  list-style-type: upper-alpha;\n}\n.body--dark .markdown-wrapper .markdown-body ol[type="i s"] {\n  list-style-type: lower-roman;\n}\n.body--dark .markdown-wrapper .markdown-body ol[type="I s"] {\n  list-style-type: upper-roman;\n}\n.body--dark .markdown-wrapper .markdown-body ol[type="1"] {\n  list-style-type: decimal;\n}\n.body--dark .markdown-wrapper .markdown-body div > ol:not([type]) {\n  list-style-type: decimal;\n}\n.body--dark .markdown-wrapper .markdown-body ul ul,\n.body--dark .markdown-wrapper .markdown-body ul ol,\n.body--dark .markdown-wrapper .markdown-body ol ol,\n.body--dark .markdown-wrapper .markdown-body ol ul {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.body--dark .markdown-wrapper .markdown-body li > p {\n  margin-top: 16px;\n}\n.body--dark .markdown-wrapper .markdown-body li + li {\n  margin-top: 0.25em;\n}\n.body--dark .markdown-wrapper .markdown-body dl {\n  padding: 0;\n}\n.body--dark .markdown-wrapper .markdown-body dl dt {\n  padding: 0;\n  margin-top: 16px;\n  font-size: 1em;\n  font-style: italic;\n  font-weight: 600;\n}\n.body--dark .markdown-wrapper .markdown-body dl dd {\n  padding: 0 16px;\n  margin-bottom: 16px;\n}\n.body--dark .markdown-wrapper .markdown-body table th {\n  font-weight: 600;\n}\n.body--dark .markdown-wrapper .markdown-body table th,\n.body--dark .markdown-wrapper .markdown-body table td {\n  padding: 6px 13px;\n  border: 1px solid #30363d;\n}\n.body--dark .markdown-wrapper .markdown-body table td > :last-child {\n  margin-bottom: 0;\n}\n.body--dark .markdown-wrapper .markdown-body table tr {\n  background-color: #0d1117;\n  border-top: 1px solid #30363db3;\n}\n.body--dark .markdown-wrapper .markdown-body table tr:nth-child(2n) {\n  background-color: #161b22;\n}\n.body--dark .markdown-wrapper .markdown-body table img {\n  background-color: transparent;\n}\n.body--dark .markdown-wrapper .markdown-body img[align=right] {\n  padding-left: 20px;\n}\n.body--dark .markdown-wrapper .markdown-body img[align=left] {\n  padding-right: 20px;\n}\n.body--dark .markdown-wrapper .markdown-body .emoji {\n  max-width: none;\n  vertical-align: text-top;\n  background-color: transparent;\n}\n.body--dark .markdown-wrapper .markdown-body span.frame {\n  display: block;\n  overflow: hidden;\n}\n.body--dark .markdown-wrapper .markdown-body span.frame > span {\n  display: block;\n  float: left;\n  width: auto;\n  padding: 7px;\n  margin: 13px 0 0;\n  overflow: hidden;\n  border: 1px solid #30363d;\n}\n.body--dark .markdown-wrapper .markdown-body span.frame span img {\n  display: block;\n  float: left;\n}\n.body--dark .markdown-wrapper .markdown-body span.frame span span {\n  display: block;\n  padding: 5px 0 0;\n  clear: both;\n  color: #e6edf3;\n}\n.body--dark .markdown-wrapper .markdown-body span.align-center {\n  display: block;\n  overflow: hidden;\n  clear: both;\n}\n.body--dark .markdown-wrapper .markdown-body span.align-center > span {\n  display: block;\n  margin: 13px auto 0;\n  overflow: hidden;\n  text-align: center;\n}\n.body--dark .markdown-wrapper .markdown-body span.align-center span img {\n  margin: 0 auto;\n  text-align: center;\n}\n.body--dark .markdown-wrapper .markdown-body span.align-right {\n  display: block;\n  overflow: hidden;\n  clear: both;\n}\n.body--dark .markdown-wrapper .markdown-body span.align-right > span {\n  display: block;\n  margin: 13px 0 0;\n  overflow: hidden;\n  text-align: right;\n}\n.body--dark .markdown-wrapper .markdown-body span.align-right span img {\n  margin: 0;\n  text-align: right;\n}\n.body--dark .markdown-wrapper .markdown-body span.float-left {\n  display: block;\n  float: left;\n  margin-right: 13px;\n  overflow: hidden;\n}\n.body--dark .markdown-wrapper .markdown-body span.float-left span {\n  margin: 13px 0 0;\n}\n.body--dark .markdown-wrapper .markdown-body span.float-right {\n  display: block;\n  float: right;\n  margin-left: 13px;\n  overflow: hidden;\n}\n.body--dark .markdown-wrapper .markdown-body span.float-right > span {\n  display: block;\n  margin: 13px auto 0;\n  overflow: hidden;\n  text-align: right;\n}\n.body--dark .markdown-wrapper .markdown-body code,\n.body--dark .markdown-wrapper .markdown-body tt {\n  padding: 0.2em 0.4em;\n  margin: 0;\n  font-size: 85%;\n  white-space: break-spaces;\n  background-color: #6e768166;\n  border-radius: 6px;\n}\n.body--dark .markdown-wrapper .markdown-body code br,\n.body--dark .markdown-wrapper .markdown-body tt br {\n  display: none;\n}\n.body--dark .markdown-wrapper .markdown-body del code {\n  text-decoration: inherit;\n}\n.body--dark .markdown-wrapper .markdown-body samp {\n  font-size: 85%;\n}\n.body--dark .markdown-wrapper .markdown-body pre code {\n  font-size: 100%;\n}\n.body--dark .markdown-wrapper .markdown-body pre > code {\n  padding: 0;\n  margin: 0;\n  word-break: normal;\n  white-space: pre;\n  background: transparent;\n  border: 0;\n}\n.body--dark .markdown-wrapper .markdown-body .highlight {\n  margin-bottom: 16px;\n}\n.body--dark .markdown-wrapper .markdown-body .highlight pre {\n  margin-bottom: 0;\n  word-break: normal;\n}\n.body--dark .markdown-wrapper .markdown-body .highlight pre,\n.body--dark .markdown-wrapper .markdown-body pre {\n  padding: 16px;\n  overflow: auto;\n  font-size: 85%;\n  line-height: 1.45;\n  color: #e6edf3;\n  background-color: #161b22;\n  border-radius: 6px;\n}\n.body--dark .markdown-wrapper .markdown-body pre code,\n.body--dark .markdown-wrapper .markdown-body pre tt {\n  display: inline;\n  max-width: auto;\n  padding: 0;\n  margin: 0;\n  overflow: visible;\n  line-height: inherit;\n  word-wrap: normal;\n  background-color: transparent;\n  border: 0;\n}\n.body--dark .markdown-wrapper .markdown-body .csv-data td,\n.body--dark .markdown-wrapper .markdown-body .csv-data th {\n  padding: 5px;\n  overflow: hidden;\n  font-size: 12px;\n  line-height: 1;\n  text-align: left;\n  white-space: nowrap;\n}\n.body--dark .markdown-wrapper .markdown-body .csv-data .blob-num {\n  padding: 10px 8px 9px;\n  text-align: right;\n  background: #0d1117;\n  border: 0;\n}\n.body--dark .markdown-wrapper .markdown-body .csv-data tr {\n  border-top: 0;\n}\n.body--dark .markdown-wrapper .markdown-body .csv-data th {\n  font-weight: 600;\n  background: #161b22;\n  border-top: 0;\n}\n.body--dark .markdown-wrapper .markdown-body [data-footnote-ref]::before {\n  content: "[";\n}\n.body--dark .markdown-wrapper .markdown-body [data-footnote-ref]::after {\n  content: "]";\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes {\n  font-size: 12px;\n  color: #8d96a0;\n  border-top: 1px solid #30363d;\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes ol {\n  padding-left: 16px;\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes ol ul {\n  display: inline-block;\n  padding-left: 16px;\n  margin-top: 16px;\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes li {\n  position: relative;\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes li:target::before {\n  position: absolute;\n  top: -8px;\n  right: -8px;\n  bottom: -8px;\n  left: -24px;\n  pointer-events: none;\n  content: "";\n  border: 2px solid #1f6feb;\n  border-radius: 6px;\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes li:target {\n  color: #e6edf3;\n}\n.body--dark .markdown-wrapper .markdown-body .footnotes .data-footnote-backref g-emoji {\n  font-family: monospace;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-c {\n  color: #8b949e;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-c1,\n.body--dark .markdown-wrapper .markdown-body .pl-s .pl-v {\n  color: #79c0ff;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-e,\n.body--dark .markdown-wrapper .markdown-body .pl-en {\n  color: #d2a8ff;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-smi,\n.body--dark .markdown-wrapper .markdown-body .pl-s .pl-s1 {\n  color: #c9d1d9;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-ent {\n  color: #7ee787;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-k {\n  color: #ff7b72;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-s,\n.body--dark .markdown-wrapper .markdown-body .pl-pds,\n.body--dark .markdown-wrapper .markdown-body .pl-s .pl-pse .pl-s1,\n.body--dark .markdown-wrapper .markdown-body .pl-sr,\n.body--dark .markdown-wrapper .markdown-body .pl-sr .pl-cce,\n.body--dark .markdown-wrapper .markdown-body .pl-sr .pl-sre,\n.body--dark .markdown-wrapper .markdown-body .pl-sr .pl-sra {\n  color: #a5d6ff;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-v,\n.body--dark .markdown-wrapper .markdown-body .pl-smw {\n  color: #ffa657;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-bu {\n  color: #f85149;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-ii {\n  color: #f0f6fc;\n  background-color: #8e1519;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-c2 {\n  color: #f0f6fc;\n  background-color: #b62324;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-sr .pl-cce {\n  font-weight: bold;\n  color: #7ee787;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-ml {\n  color: #f2cc60;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mh,\n.body--dark .markdown-wrapper .markdown-body .pl-mh .pl-en,\n.body--dark .markdown-wrapper .markdown-body .pl-ms {\n  font-weight: bold;\n  color: #1f6feb;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mi {\n  font-style: italic;\n  color: #c9d1d9;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mb {\n  font-weight: bold;\n  color: #c9d1d9;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-md {\n  color: #ffdcd7;\n  background-color: #67060c;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mi1 {\n  color: #aff5b4;\n  background-color: #033a16;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mc {\n  color: #ffdfb6;\n  background-color: #5a1e02;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mi2 {\n  color: #c9d1d9;\n  background-color: #1158c7;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-mdr {\n  font-weight: bold;\n  color: #d2a8ff;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-ba {\n  color: #8b949e;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-sg {\n  color: #484f58;\n}\n.body--dark .markdown-wrapper .markdown-body .pl-corl {\n  text-decoration: underline;\n  color: #a5d6ff;\n}\n.body--dark .markdown-wrapper .markdown-body [role=button]:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body [role=tabpanel][tabindex="0"]:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body button:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body summary:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body a:focus:not(:focus-visible) {\n  outline: none;\n  box-shadow: none;\n}\n.body--dark .markdown-wrapper .markdown-body [tabindex="0"]:focus:not(:focus-visible),\n.body--dark .markdown-wrapper .markdown-body details-dialog:focus:not(:focus-visible) {\n  outline: none;\n}\n.body--dark .markdown-wrapper .markdown-body g-emoji {\n  display: inline-block;\n  min-width: 1ch;\n  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";\n  font-size: 1em;\n  font-style: normal !important;\n  font-weight: 400;\n  line-height: 1;\n  vertical-align: -0.075em;\n}\n.body--dark .markdown-wrapper .markdown-body g-emoji img {\n  width: 1em;\n  height: 1em;\n}\n.body--dark .markdown-wrapper .markdown-body .task-list-item {\n  list-style-type: none;\n}\n.body--dark .markdown-wrapper .markdown-body .task-list-item label {\n  font-weight: 400;\n}\n.body--dark .markdown-wrapper .markdown-body .task-list-item.enabled label {\n  cursor: pointer;\n}\n.body--dark .markdown-wrapper .markdown-body .task-list-item + .task-list-item {\n  margin-top: 0.25rem;\n}\n.body--dark .markdown-wrapper .markdown-body .task-list-item .handle {\n  display: none;\n}\n.body--dark .markdown-wrapper .markdown-body .task-list-item-checkbox {\n  margin: 0 0.2em 0.25em -1.4em;\n  vertical-align: middle;\n}\n.body--dark .markdown-wrapper .markdown-body .contains-task-list:dir(rtl) .task-list-item-checkbox {\n  margin: 0 -1.6em 0.25em 0.2em;\n}\n.body--dark .markdown-wrapper .markdown-body .contains-task-list {\n  position: relative;\n}\n.body--dark .markdown-wrapper .markdown-body .contains-task-list:hover .task-list-item-convert-container,\n.body--dark .markdown-wrapper .markdown-body .contains-task-list:focus-within .task-list-item-convert-container {\n  display: block;\n  width: auto;\n  height: 24px;\n  overflow: visible;\n  clip: auto;\n}\n.body--dark .markdown-wrapper .markdown-body ::-webkit-calendar-picker-indicator {\n  filter: invert(50%);\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert {\n  padding: 0.5rem 1rem;\n  margin-bottom: 1rem;\n  color: inherit;\n  border-left: 0.25em solid #30363d;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert > :first-child {\n  margin-top: 0;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert > :last-child {\n  margin-bottom: 0;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert .markdown-alert-title {\n  display: flex;\n  font-weight: 500;\n  align-items: center;\n  line-height: 1;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-note {\n  border-left-color: #1f6feb;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-note .markdown-alert-title {\n  color: #4493f8;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-important {\n  border-left-color: #8957e5;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-important .markdown-alert-title {\n  color: #ab7df8;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-warning {\n  border-left-color: #9e6a03;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-warning .markdown-alert-title {\n  color: #d29922;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-tip {\n  border-left-color: #238636;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-tip .markdown-alert-title {\n  color: #3fb950;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-caution {\n  border-left-color: #da3633;\n}\n.body--dark .markdown-wrapper .markdown-body .markdown-alert.markdown-alert-caution .markdown-alert-title {\n  color: #f85149;\n}\n.body--dark .markdown-wrapper .markdown-body > *:first-child > .heading-element:first-child {\n  margin-top: 0 !important;\n}';b(w);const y="data-theme",k="com.silabs.ss.platform.theme.dark",_=function(e){e.getAttribute(y)===k?l.Z.set(!0):l.Z.set(!1)},x=function(e){return new MutationObserver((t=>{t.forEach((t=>{"attributes"===t.type&&t.attributeName===y&&_(e)}))}))},S=function(){var e=document.documentElement;_(e);const t=x(e);return t.observe(e,{attributes:!0,attributeFilter:[y],subtree:!1}),t};var C={setQuasarTheme:_,createThemeObserver:x,observe:S};class L{constructor(e,t,n=""){this.path=e,this.mutator=t,this.prefix=n,this.attemptReconnect=!1,this.timeout=5e3,window.addEventListener("beforeunload",(()=>{this.close()}))}setReconnectTimeout(e){this.timeout=e}getReadyState(){return this.socket.readyState}reconnect(){return new Promise(((e,t)=>{!this.socket||this.getReadyState()!==WebSocket.CONNECTING&&this.getReadyState()!==WebSocket.OPEN?this.init().then((t=>{e(t)})).catch((e=>{t(e)})):e()}))}mutatorSocketEvent(e,t){this.prefix&&(e=e.charAt(0).toUpperCase()+e.slice(1)),e=this.prefix+e,this.mutator[e]&&this.mutator[e](t)}init(){return new Promise((e=>{this.socket=new WebSocket(this.path),this.socket.onmessage=e=>{this.mutatorSocketEvent("onMessage",e)},this.socket.onopen=t=>{this.attemptReconnect=!0,this.mutatorSocketEvent("onOpen",t),e()},this.socket.onclose=t=>{this.mutatorSocketEvent("onClose",t),this.attemptReconnect?setTimeout((()=>{window.console.log(`${this.prefix} socket connection lost, attempting reconnect`),this.reconnect()}),this.timeout):e()},this.socket.onerror=e=>{this.mutatorSocketEvent("onError",e)}}))}close(){this.socket&&(this.socket.onclose=()=>{},this.socket.close()),this.socket=null,this.attemptReconnect=!1}}class E{constructor(){this.notifications={},this.cancelTaskHandler=null,this.quasarNotification=s.Z}static default(){return this.defaultInstance||(this.defaultInstance=new E),this.defaultInstance}closeAll(){for(const e in this.notifications)this.notifications[e](),delete this.notifications[e]}setCancelTaskHandler(e){this.cancelTaskHandler=e}handleNotificationMessage(e){switch(e.type){case"BeginTask":var t=[{label:"Cancel",color:"white",handler:()=>{this.cancelTaskHandler&&this.cancelTaskHandler(e.id),this.closeNotification(e.id)}}];this.createNotification(e.name,e.id,e.totalWork,t);break;case"SetTaskName":this.updateNotificationName(e.name,e.id);break;case"SubTask":this.updateNotificationSubTask(e.name,e.id);break;case"Worked":this.updateNotificationProgress(e.work,e.id);break;case"Done":this.closeNotification(e.id)}}createNotification(e,t,n,r){const o=this.quasarNotification.create({message:e,caption:0/n+" %",group:!1,position:"bottom-right",color:"primary",timeout:0,actions:r});this.notifications[t]=o,o.taskName="",o.currentProgress=0,o.totalProgress=n}updateNotificationName(e,t){const n=this.notifications[t];n&&n({message:e})}updateNotificationSubTask(e,t){const n=this.notifications[t];n&&(n.taskName=e,n({caption:n.taskName+" "+100*n.currentProgress/n.totalProgress+"%"}))}updateNotificationProgress(e,t){const n=this.notifications[t];n&&(n.currentProgress=e,n({caption:n.taskName+" "+100*n.currentProgress/n.totalProgress+"%"}))}closeNotification(e){const t=this.notifications[e];t&&(t(),delete this.notifications[e])}}class A{constructor(){if(A._instance)return A._instance;(A._instance=this)._transceivers=new Map,window.addEventListener("message",this.receiveMessage.bind(this),!1)}static instance(){return new A}addTransceiver(e,t=null){e&&this._transceivers.set((t={window:e,origin:t,events:{}}).window,t)}removeTransceiver(e){this._transceivers.has(e)&&this._transceivers.delete(e)}addListener(e,t,n){const r=this._transceivers.get(e);r&&(r.events[t]||(r.events[t]=[]),r.events[t].push(n))}removeListener(e,t,n){const r=this._transceivers.get(e);r&&(!r.events[t]||-1!==(n=r.events[t].indexOf(n))&&r.events[t].splice(n,1))}sendMessage(e,t){var n;this._transceivers.get(e)&&(n=this.origin||"*",t.senderLocation=window.location.href,e.postMessage(t,n))}receiveMessage(e){const t=this._transceivers.get(e.source);if(t&&(t.origin||(t.origin=e.origin),t.origin===e.origin)){const n=e.data;t.events[n.eventId]&&t.events[n.eventId].forEach((e=>{e(n.eventData)}))}}destroy(){this._transceivers.clear(),window.removeEventListener("message",this.receiveMessage.bind(this),!1),A._instance=void 0}}const q="close",P=e=>{},T=e=>({eventId:q,eventData:{shouldClose:e}});var j={EVENT_ID:q,defaultHandler:P,message:T};const O="dirty",M=e=>{},R=e=>({eventId:O,eventData:{isDirty:e}});var F={EVENT_ID:O,defaultHandler:M,message:R};const H="focus",z=e=>{},B=e=>({eventId:H,eventData:{shouldFocus:e}});var I={EVENT_ID:H,defaultHandler:z,message:B};const V="location",$=e=>{},N=e=>({eventId:V,eventData:{location:e}});var U={EVENT_ID:V,defaultHandler:$,message:N};const D="mounted",Z=e=>{},W=e=>({eventId:D,eventData:{hasMounted:e}});var K={EVENT_ID:D,defaultHandler:Z,message:W};const G="save",J=e=>{},Y=e=>({eventId:G,eventData:{shouldSave:e}});var Q={EVENT_ID:G,defaultHandler:J,message:Y};const X="theme",ee=e=>{"dark"===e.theme?l.Z.set(!0):l.Z.set(!1)},te=e=>({eventId:X,eventData:{theme:e}});var ne={EVENT_ID:X,defaultHandler:ee,message:te};const re="open-file",oe=e=>{},ae=e=>({eventId:re,eventData:e});var ie={EVENT_ID:re,defaultHandler:oe,message:ae};function le(e,t,n,r,o){let a;return window.swt&&window.swt.openFileDialog?a=window.swt.openFileDialog(e,t,n,r,o):(a=[],window.alert("HTML openFileDialog not implemented!")),a}function se(e,t,n,r){let o;return window.swt&&window.swt.saveFileDialog?o=window.swt.saveFileDialog(e,t,n,r):(o=[],window.alert("HTML saveFileDialog not implemented!")),o}function ce(e,t){let n;return window.swt&&window.swt.openDirectoryDialog?n=window.swt.openDirectoryDialog(e,t):window.alert("HTML openDirectoryDialog not implemented!"),n}function de(e){e&&e.use(ue)}function ue(e){return window.uiId?e.headers.uiId=window.uiId:frameElement&&frameElement.id&&(e.headers.uiId=frameElement.id),e}function pe(e,t,n){e&&e.use(t=t||fe,n=n||he)}function fe(e){return e}function he(e){if(E.default().closeAll(),e&&e.response&&e.response.data&&e.response.data.primaryMessage){var t=e.response.data,n=`<div style="font-weight:bolder">Error: ${t.primaryMessage}</div>`;if(t&&t.errors)for(var r=0;r<t.errors.length;r++)n+=`<div style="margin-left: 2em"><strong>${t.errors[r]}</strong><br/>${t.callStack[r]}</div>`;const o=s.Z.create({type:"negative",message:n,html:!0,group:!1,position:"bottom-right",color:"red",timeout:0,actions:[{icon:"close",color:"white",round:!0,handler:()=>{o()}}]})}return Promise.reject(e)}const me="2.4.37";function ve(e){e.component(g.name,g)}var ge=Object.freeze({__proto__:null,CloseMessageEvent:j,DirtyMessageEvent:F,FocusMessageEvent:I,LocationMessageEvent:U,MountedMessageEvent:K,NotificationService:E,OpenFileMessageEvent:ie,PostMessageEmitter:A,SSMarkDownRenderer:g,SaveMessageEvent:Q,StudioThemeObserver:C,ThemeMessageEvent:ne,WebSocketWrapper:L,addDefaultRequestInterceptor:de,addDefaultResponseInterceptor:pe,defaultRequestInterceptor:ue,defaultResponseErrorHandler:he,defaultResponseSuccesshandler:fe,install:ve,openDirectoryDialog:ce,openFileDialog:le,saveFileDialog:se,version:me})},1639:(e,t)=>{"use strict";t.Z=(e,t)=>{const n=e.__vccOpts||e;for(const[r,o]of t)n[r]=o;return n}},8910:(e,t,n)=>{"use strict";n.d(t,{p7:()=>nt,r5:()=>Z});var r=n(9835),o=n(499);
/*!
  * vue-router v4.0.15
  * (c) 2022 Eduardo San Martin Morote
  * @license MIT
  */
const a="function"===typeof Symbol&&"symbol"===typeof Symbol.toStringTag,i=e=>a?Symbol(e):"_vr_"+e,l=i("rvlm"),s=i("rvd"),c=i("r"),d=i("rl"),u=i("rvl"),p="undefined"!==typeof window;function f(e){return e.__esModule||a&&"Module"===e[Symbol.toStringTag]}const h=Object.assign;function m(e,t){const n={};for(const r in t){const o=t[r];n[r]=Array.isArray(o)?o.map(e):e(o)}return n}const v=()=>{};const g=/\/$/,b=e=>e.replace(g,"");function w(e,t,n="/"){let r,o={},a="",i="";const l=t.indexOf("?"),s=t.indexOf("#",l>-1?l:0);return l>-1&&(r=t.slice(0,l),a=t.slice(l+1,s>-1?s:t.length),o=e(a)),s>-1&&(r=r||t.slice(0,s),i=t.slice(s,t.length)),r=E(null!=r?r:t,n),{fullPath:r+(a&&"?")+a+i,path:r,query:o,hash:i}}function y(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function k(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function _(e,t,n){const r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&x(t.matched[r],n.matched[o])&&S(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function x(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function S(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!C(e[n],t[n]))return!1;return!0}function C(e,t){return Array.isArray(e)?L(e,t):Array.isArray(t)?L(t,e):e===t}function L(e,t){return Array.isArray(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}function E(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/");let o,a,i=n.length-1;for(o=0;o<r.length;o++)if(a=r[o],1!==i&&"."!==a){if(".."!==a)break;i--}return n.slice(0,i).join("/")+"/"+r.slice(o-(o===r.length?1:0)).join("/")}var A,q;(function(e){e["pop"]="pop",e["push"]="push"})(A||(A={})),function(e){e["back"]="back",e["forward"]="forward",e["unknown"]=""}(q||(q={}));function P(e){if(!e)if(p){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),b(e)}const T=/^[^#]+#/;function j(e,t){return e.replace(T,"#")+t}function O(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const M=()=>({left:window.pageXOffset,top:window.pageYOffset});function R(e){let t;if("el"in e){const n=e.el,r="string"===typeof n&&n.startsWith("#");0;const o="string"===typeof n?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=O(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.pageXOffset,null!=t.top?t.top:window.pageYOffset)}function F(e,t){const n=history.state?history.state.position-t:-1;return n+e}const H=new Map;function z(e,t){H.set(e,t)}function B(e){const t=H.get(e);return H.delete(e),t}let I=()=>location.protocol+"//"+location.host;function V(e,t){const{pathname:n,search:r,hash:o}=t,a=e.indexOf("#");if(a>-1){let t=o.includes(e.slice(a))?e.slice(a).length:1,n=o.slice(t);return"/"!==n[0]&&(n="/"+n),k(n,"")}const i=k(n,e);return i+r+o}function $(e,t,n,r){let o=[],a=[],i=null;const l=({state:a})=>{const l=V(e,location),s=n.value,c=t.value;let d=0;if(a){if(n.value=l,t.value=a,i&&i===s)return void(i=null);d=c?a.position-c.position:0}else r(l);o.forEach((e=>{e(n.value,s,{delta:d,type:A.pop,direction:d?d>0?q.forward:q.back:q.unknown})}))};function s(){i=n.value}function c(e){o.push(e);const t=()=>{const t=o.indexOf(e);t>-1&&o.splice(t,1)};return a.push(t),t}function d(){const{history:e}=window;e.state&&e.replaceState(h({},e.state,{scroll:M()}),"")}function u(){for(const e of a)e();a=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",d)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",d),{pauseListeners:s,listen:c,destroy:u}}function N(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?M():null}}function U(e){const{history:t,location:n}=window,r={value:V(e,n)},o={value:t.state};function a(r,a,i){const l=e.indexOf("#"),s=l>-1?(n.host&&document.querySelector("base")?e:e.slice(l))+r:I()+e+r;try{t[i?"replaceState":"pushState"](a,"",s),o.value=a}catch(c){console.error(c),n[i?"replace":"assign"](s)}}function i(e,n){const i=h({},t.state,N(o.value.back,e,o.value.forward,!0),n,{position:o.value.position});a(e,i,!0),r.value=e}function l(e,n){const i=h({},o.value,t.state,{forward:e,scroll:M()});a(i.current,i,!0);const l=h({},N(r.value,e,null),{position:i.position+1},n);a(e,l,!1),r.value=e}return o.value||a(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:r,state:o,push:l,replace:i}}function D(e){e=P(e);const t=U(e),n=$(e,t.state,t.location,t.replace);function r(e,t=!0){t||n.pauseListeners(),history.go(e)}const o=h({location:"",base:e,go:r,createHref:j.bind(null,e)},t,n);return Object.defineProperty(o,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(o,"state",{enumerable:!0,get:()=>t.state.value}),o}function Z(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),D(e)}function W(e){return"string"===typeof e||e&&"object"===typeof e}function K(e){return"string"===typeof e||"symbol"===typeof e}const G={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},J=i("nf");var Y;(function(e){e[e["aborted"]=4]="aborted",e[e["cancelled"]=8]="cancelled",e[e["duplicated"]=16]="duplicated"})(Y||(Y={}));function Q(e,t){return h(new Error,{type:e,[J]:!0},t)}function X(e,t){return e instanceof Error&&J in e&&(null==t||!!(e.type&t))}const ee="[^/]+?",te={sensitive:!1,strict:!1,start:!0,end:!0},ne=/[.+*?^${}()[\]/\\]/g;function re(e,t){const n=h({},te,t),r=[];let o=n.start?"^":"";const a=[];for(const d of e){const e=d.length?[]:[90];n.strict&&!d.length&&(o+="/");for(let t=0;t<d.length;t++){const r=d[t];let i=40+(n.sensitive?.25:0);if(0===r.type)t||(o+="/"),o+=r.value.replace(ne,"\\$&"),i+=40;else if(1===r.type){const{value:e,repeatable:n,optional:l,regexp:s}=r;a.push({name:e,repeatable:n,optional:l});const u=s||ee;if(u!==ee){i+=10;try{new RegExp(`(${u})`)}catch(c){throw new Error(`Invalid custom RegExp for param "${e}" (${u}): `+c.message)}}let p=n?`((?:${u})(?:/(?:${u}))*)`:`(${u})`;t||(p=l&&d.length<2?`(?:/${p})`:"/"+p),l&&(p+="?"),o+=p,i+=20,l&&(i+=-8),n&&(i+=-20),".*"===u&&(i+=-50)}e.push(i)}r.push(e)}if(n.strict&&n.end){const e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");function l(e){const t=e.match(i),n={};if(!t)return null;for(let r=1;r<t.length;r++){const e=t[r]||"",o=a[r-1];n[o.name]=e&&o.repeatable?e.split("/"):e}return n}function s(t){let n="",r=!1;for(const o of e){r&&n.endsWith("/")||(n+="/"),r=!1;for(const a of o)if(0===a.type)n+=a.value;else if(1===a.type){const{value:i,repeatable:l,optional:s}=a,c=i in t?t[i]:"";if(Array.isArray(c)&&!l)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const d=Array.isArray(c)?c.join("/"):c;if(!d){if(!s)throw new Error(`Missing required param "${i}"`);o.length<2&&e.length>1&&(n.endsWith("/")?n=n.slice(0,-1):r=!0)}n+=d}}return n}return{re:i,score:r,keys:a,parse:l,stringify:s}}function oe(e,t){let n=0;while(n<e.length&&n<t.length){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function ae(e,t){let n=0;const r=e.score,o=t.score;while(n<r.length&&n<o.length){const e=oe(r[n],o[n]);if(e)return e;n++}return o.length-r.length}const ie={type:0,value:""},le=/[a-zA-Z0-9_]/;function se(e){if(!e)return[[]];if("/"===e)return[[ie]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${c}": ${e}`)}let n=0,r=n;const o=[];let a;function i(){a&&o.push(a),a=[]}let l,s=0,c="",d="";function u(){c&&(0===n?a.push({type:0,value:c}):1===n||2===n||3===n?(a.length>1&&("*"===l||"+"===l)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:c,regexp:d,repeatable:"*"===l||"+"===l,optional:"*"===l||"?"===l})):t("Invalid state to consume buffer"),c="")}function p(){c+=l}while(s<e.length)if(l=e[s++],"\\"!==l||2===n)switch(n){case 0:"/"===l?(c&&u(),i()):":"===l?(u(),n=1):p();break;case 4:p(),n=r;break;case 1:"("===l?n=2:le.test(l)?p():(u(),n=0,"*"!==l&&"?"!==l&&"+"!==l&&s--);break;case 2:")"===l?"\\"==d[d.length-1]?d=d.slice(0,-1)+l:n=3:d+=l;break;case 3:u(),n=0,"*"!==l&&"?"!==l&&"+"!==l&&s--,d="";break;default:t("Unknown state");break}else r=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${c}"`),u(),i(),o}function ce(e,t,n){const r=re(se(e.path),n);const o=h(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf===!t.record.aliasOf&&t.children.push(o),o}function de(e,t){const n=[],r=new Map;function o(e){return r.get(e)}function a(e,n,r){const o=!r,l=pe(e);l.aliasOf=r&&r.record;const c=ve(t,e),d=[l];if("alias"in e){const t="string"===typeof e.alias?[e.alias]:e.alias;for(const e of t)d.push(h({},l,{components:r?r.record.components:l.components,path:e,aliasOf:r?r.record:l}))}let u,p;for(const t of d){const{path:d}=t;if(n&&"/"!==d[0]){const e=n.record.path,r="/"===e[e.length-1]?"":"/";t.path=n.record.path+(d&&r+d)}if(u=ce(t,n,c),r?r.alias.push(u):(p=p||u,p!==u&&p.alias.push(u),o&&e.name&&!he(u)&&i(e.name)),"children"in l){const e=l.children;for(let t=0;t<e.length;t++)a(e[t],u,r&&r.children[t])}r=r||u,s(u)}return p?()=>{i(p)}:v}function i(e){if(K(e)){const t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function l(){return n}function s(e){let t=0;while(t<n.length&&ae(e,n[t])>=0&&(e.record.path!==n[t].record.path||!ge(e,n[t])))t++;n.splice(t,0,e),e.record.name&&!he(e)&&r.set(e.record.name,e)}function c(e,t){let o,a,i,l={};if("name"in e&&e.name){if(o=r.get(e.name),!o)throw Q(1,{location:e});i=o.record.name,l=h(ue(t.params,o.keys.filter((e=>!e.optional)).map((e=>e.name))),e.params),a=o.stringify(l)}else if("path"in e)a=e.path,o=n.find((e=>e.re.test(a))),o&&(l=o.parse(a),i=o.record.name);else{if(o=t.name?r.get(t.name):n.find((e=>e.re.test(t.path))),!o)throw Q(1,{location:e,currentLocation:t});i=o.record.name,l=h({},t.params,e.params),a=o.stringify(l)}const s=[];let c=o;while(c)s.unshift(c.record),c=c.parent;return{name:i,path:a,params:l,matched:s,meta:me(s)}}return t=ve({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>a(e))),{addRoute:a,resolve:c,removeRoute:i,getRoutes:l,getRecordMatcher:o}}function ue(e,t){const n={};for(const r of t)r in e&&(n[r]=e[r]);return n}function pe(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:fe(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||{}:{default:e.component}}}function fe(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const r in e.components)t[r]="boolean"===typeof n?n:n[r];return t}function he(e){while(e){if(e.record.aliasOf)return!0;e=e.parent}return!1}function me(e){return e.reduce(((e,t)=>h(e,t.meta)),{})}function ve(e,t){const n={};for(const r in e)n[r]=r in t?t[r]:e[r];return n}function ge(e,t){return t.children.some((t=>t===e||ge(e,t)))}const be=/#/g,we=/&/g,ye=/\//g,ke=/=/g,_e=/\?/g,xe=/\+/g,Se=/%5B/g,Ce=/%5D/g,Le=/%5E/g,Ee=/%60/g,Ae=/%7B/g,qe=/%7C/g,Pe=/%7D/g,Te=/%20/g;function je(e){return encodeURI(""+e).replace(qe,"|").replace(Se,"[").replace(Ce,"]")}function Oe(e){return je(e).replace(Ae,"{").replace(Pe,"}").replace(Le,"^")}function Me(e){return je(e).replace(xe,"%2B").replace(Te,"+").replace(be,"%23").replace(we,"%26").replace(Ee,"`").replace(Ae,"{").replace(Pe,"}").replace(Le,"^")}function Re(e){return Me(e).replace(ke,"%3D")}function Fe(e){return je(e).replace(be,"%23").replace(_e,"%3F")}function He(e){return null==e?"":Fe(e).replace(ye,"%2F")}function ze(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Be(e){const t={};if(""===e||"?"===e)return t;const n="?"===e[0],r=(n?e.slice(1):e).split("&");for(let o=0;o<r.length;++o){const e=r[o].replace(xe," "),n=e.indexOf("="),a=ze(n<0?e:e.slice(0,n)),i=n<0?null:ze(e.slice(n+1));if(a in t){let e=t[a];Array.isArray(e)||(e=t[a]=[e]),e.push(i)}else t[a]=i}return t}function Ie(e){let t="";for(let n in e){const r=e[n];if(n=Re(n),null==r){void 0!==r&&(t+=(t.length?"&":"")+n);continue}const o=Array.isArray(r)?r.map((e=>e&&Me(e))):[r&&Me(r)];o.forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Ve(e){const t={};for(const n in e){const r=e[n];void 0!==r&&(t[n]=Array.isArray(r)?r.map((e=>null==e?null:""+e)):null==r?r:""+r)}return t}function $e(){let e=[];function t(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}}function n(){e=[]}return{add:t,list:()=>e,reset:n}}function Ne(e,t,n,r,o){const a=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise(((i,l)=>{const s=e=>{!1===e?l(Q(4,{from:n,to:t})):e instanceof Error?l(e):W(e)?l(Q(2,{from:t,to:e})):(a&&r.enterCallbacks[o]===a&&"function"===typeof e&&a.push(e),i())},c=e.call(r&&r.instances[o],t,n,s);let d=Promise.resolve(c);e.length<3&&(d=d.then(s)),d.catch((e=>l(e)))}))}function Ue(e,t,n,r){const o=[];for(const a of e)for(const e in a.components){let i=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if(De(i)){const l=i.__vccOpts||i,s=l[t];s&&o.push(Ne(s,n,r,a,e))}else{let l=i();0,o.push((()=>l.then((o=>{if(!o)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${a.path}"`));const i=f(o)?o.default:o;a.components[e]=i;const l=i.__vccOpts||i,s=l[t];return s&&Ne(s,n,r,a,e)()}))))}}return o}function De(e){return"object"===typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}function Ze(e){const t=(0,r.f3)(c),n=(0,r.f3)(d),a=(0,r.Fl)((()=>t.resolve((0,o.SU)(e.to)))),i=(0,r.Fl)((()=>{const{matched:e}=a.value,{length:t}=e,r=e[t-1],o=n.matched;if(!r||!o.length)return-1;const i=o.findIndex(x.bind(null,r));if(i>-1)return i;const l=Ye(e[t-2]);return t>1&&Ye(r)===l&&o[o.length-1].path!==l?o.findIndex(x.bind(null,e[t-2])):i})),l=(0,r.Fl)((()=>i.value>-1&&Je(n.params,a.value.params))),s=(0,r.Fl)((()=>i.value>-1&&i.value===n.matched.length-1&&S(n.params,a.value.params)));function u(n={}){return Ge(n)?t[(0,o.SU)(e.replace)?"replace":"push"]((0,o.SU)(e.to)).catch(v):Promise.resolve()}return{route:a,href:(0,r.Fl)((()=>a.value.href)),isActive:l,isExactActive:s,navigate:u}}const We=(0,r.aZ)({name:"RouterLink",props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Ze,setup(e,{slots:t}){const n=(0,o.qj)(Ze(e)),{options:a}=(0,r.f3)(c),i=(0,r.Fl)((()=>({[Qe(e.activeClass,a.linkActiveClass,"router-link-active")]:n.isActive,[Qe(e.exactActiveClass,a.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const o=t.default&&t.default(n);return e.custom?o:(0,r.h)("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},o)}}}),Ke=We;function Ge(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&(void 0===e.button||0===e.button)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Je(e,t){for(const n in t){const r=t[n],o=e[n];if("string"===typeof r){if(r!==o)return!1}else if(!Array.isArray(o)||o.length!==r.length||r.some(((e,t)=>e!==o[t])))return!1}return!0}function Ye(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Qe=(e,t,n)=>null!=e?e:null!=t?t:n,Xe=(0,r.aZ)({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const a=(0,r.f3)(u),i=(0,r.Fl)((()=>e.route||a.value)),c=(0,r.f3)(s,0),d=(0,r.Fl)((()=>i.value.matched[c]));(0,r.JJ)(s,c+1),(0,r.JJ)(l,d),(0,r.JJ)(u,i);const p=(0,o.iH)();return(0,r.YP)((()=>[p.value,d.value,e.name]),(([e,t,n],[r,o,a])=>{t&&(t.instances[n]=e,o&&o!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=o.leaveGuards),t.updateGuards.size||(t.updateGuards=o.updateGuards))),!e||!t||o&&x(t,o)&&r||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=i.value,a=d.value,l=a&&a.components[e.name],s=e.name;if(!l)return et(n.default,{Component:l,route:o});const c=a.props[e.name],u=c?!0===c?o.params:"function"===typeof c?c(o):c:null,f=e=>{e.component.isUnmounted&&(a.instances[s]=null)},m=(0,r.h)(l,h({},u,t,{onVnodeUnmounted:f,ref:p}));return et(n.default,{Component:m,route:o})||m}}});function et(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const tt=Xe;function nt(e){const t=de(e.routes,e),n=e.parseQuery||Be,a=e.stringifyQuery||Ie,i=e.history;const l=$e(),s=$e(),f=$e(),g=(0,o.XI)(G);let b=G;p&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const k=m.bind(null,(e=>""+e)),x=m.bind(null,He),S=m.bind(null,ze);function C(e,n){let r,o;return K(e)?(r=t.getRecordMatcher(e),o=n):o=e,t.addRoute(o,r)}function L(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)}function E(){return t.getRoutes().map((e=>e.record))}function q(e){return!!t.getRecordMatcher(e)}function P(e,r){if(r=h({},r||g.value),"string"===typeof e){const o=w(n,e,r.path),a=t.resolve({path:o.path},r),l=i.createHref(o.fullPath);return h(o,a,{params:S(a.params),hash:ze(o.hash),redirectedFrom:void 0,href:l})}let o;if("path"in e)o=h({},e,{path:w(n,e.path,r.path).path});else{const t=h({},e.params);for(const e in t)null==t[e]&&delete t[e];o=h({},e,{params:x(e.params)}),r.params=x(r.params)}const l=t.resolve(o,r),s=e.hash||"";l.params=k(S(l.params));const c=y(a,h({},e,{hash:Oe(s),path:l.path})),d=i.createHref(c);return h({fullPath:c,hash:s,query:a===Ie?Ve(e.query):e.query||{}},l,{redirectedFrom:void 0,href:d})}function T(e){return"string"===typeof e?w(n,e,g.value.path):h({},e)}function j(e,t){if(b!==e)return Q(8,{from:t,to:e})}function O(e){return V(e)}function H(e){return O(h(T(e),{replace:!0}))}function I(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let r="function"===typeof n?n(e):n;return"string"===typeof r&&(r=r.includes("?")||r.includes("#")?r=T(r):{path:r},r.params={}),h({query:e.query,hash:e.hash,params:e.params},r)}}function V(e,t){const n=b=P(e),r=g.value,o=e.state,i=e.force,l=!0===e.replace,s=I(n);if(s)return V(h(T(s),{state:o,force:i,replace:l}),t||n);const c=n;let d;return c.redirectedFrom=t,!i&&_(a,r,n)&&(d=Q(16,{to:c,from:r}),oe(r,r,!0,!1)),(d?Promise.resolve(d):N(c,r)).catch((e=>X(e)?X(e,2)?e:re(e):te(e,c,r))).then((e=>{if(e){if(X(e,2))return V(h(T(e.to),{state:o,force:i,replace:l}),t||c)}else e=D(c,r,!0,l,o);return U(c,r,e),e}))}function $(e,t){const n=j(e,t);return n?Promise.reject(n):Promise.resolve()}function N(e,t){let n;const[r,o,a]=ot(e,t);n=Ue(r.reverse(),"beforeRouteLeave",e,t);for(const l of r)l.leaveGuards.forEach((r=>{n.push(Ne(r,e,t))}));const i=$.bind(null,e,t);return n.push(i),rt(n).then((()=>{n=[];for(const r of l.list())n.push(Ne(r,e,t));return n.push(i),rt(n)})).then((()=>{n=Ue(o,"beforeRouteUpdate",e,t);for(const r of o)r.updateGuards.forEach((r=>{n.push(Ne(r,e,t))}));return n.push(i),rt(n)})).then((()=>{n=[];for(const r of e.matched)if(r.beforeEnter&&!t.matched.includes(r))if(Array.isArray(r.beforeEnter))for(const o of r.beforeEnter)n.push(Ne(o,e,t));else n.push(Ne(r.beforeEnter,e,t));return n.push(i),rt(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Ue(a,"beforeRouteEnter",e,t),n.push(i),rt(n)))).then((()=>{n=[];for(const r of s.list())n.push(Ne(r,e,t));return n.push(i),rt(n)})).catch((e=>X(e,8)?e:Promise.reject(e)))}function U(e,t,n){for(const r of f.list())r(e,t,n)}function D(e,t,n,r,o){const a=j(e,t);if(a)return a;const l=t===G,s=p?history.state:{};n&&(r||l?i.replace(e.fullPath,h({scroll:l&&s&&s.scroll},o)):i.push(e.fullPath,o)),g.value=e,oe(e,t,n,l),re()}let Z;function W(){Z||(Z=i.listen(((e,t,n)=>{const r=P(e),o=I(r);if(o)return void V(h(o,{replace:!0}),r).catch(v);b=r;const a=g.value;p&&z(F(a.fullPath,n.delta),M()),N(r,a).catch((e=>X(e,12)?e:X(e,2)?(V(e.to,r).then((e=>{X(e,20)&&!n.delta&&n.type===A.pop&&i.go(-1,!1)})).catch(v),Promise.reject()):(n.delta&&i.go(-n.delta,!1),te(e,r,a)))).then((e=>{e=e||D(r,a,!1),e&&(n.delta?i.go(-n.delta,!1):n.type===A.pop&&X(e,20)&&i.go(-1,!1)),U(r,a,e)})).catch(v)})))}let J,Y=$e(),ee=$e();function te(e,t,n){re(e);const r=ee.list();return r.length?r.forEach((r=>r(e,t,n))):console.error(e),Promise.reject(e)}function ne(){return J&&g.value!==G?Promise.resolve():new Promise(((e,t)=>{Y.add([e,t])}))}function re(e){return J||(J=!e,W(),Y.list().forEach((([t,n])=>e?n(e):t())),Y.reset()),e}function oe(t,n,o,a){const{scrollBehavior:i}=e;if(!p||!i)return Promise.resolve();const l=!o&&B(F(t.fullPath,0))||(a||!o)&&history.state&&history.state.scroll||null;return(0,r.Y3)().then((()=>i(t,n,l))).then((e=>e&&R(e))).catch((e=>te(e,t,n)))}const ae=e=>i.go(e);let ie;const le=new Set,se={currentRoute:g,addRoute:C,removeRoute:L,hasRoute:q,getRoutes:E,resolve:P,options:e,push:O,replace:H,go:ae,back:()=>ae(-1),forward:()=>ae(1),beforeEach:l.add,beforeResolve:s.add,afterEach:f.add,onError:ee.add,isReady:ne,install(e){const t=this;e.component("RouterLink",Ke),e.component("RouterView",tt),e.config.globalProperties.$router=t,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>(0,o.SU)(g)}),p&&!ie&&g.value===G&&(ie=!0,O(i.location).catch((e=>{0})));const n={};for(const o in G)n[o]=(0,r.Fl)((()=>g.value[o]));e.provide(c,t),e.provide(d,(0,o.qj)(n)),e.provide(u,g);const a=e.unmount;le.add(e),e.unmount=function(){le.delete(e),le.size<1&&(b=G,Z&&Z(),Z=null,g.value=G,ie=!1,J=!1),a()}}};return se}function rt(e){return e.reduce(((e,t)=>e.then((()=>t()))),Promise.resolve())}function ot(e,t){const n=[],r=[],o=[],a=Math.max(t.matched.length,e.matched.length);for(let i=0;i<a;i++){const a=t.matched[i];a&&(e.matched.find((e=>x(e,a)))?r.push(a):n.push(a));const l=e.matched[i];l&&(t.matched.find((e=>x(e,l)))||o.push(l))}return[n,r,o]}},3100:(e,t,n)=>{"use strict";n.d(t,{MT:()=>te,rn:()=>oe,oR:()=>g});var r=n(9835),o=n(499);function a(){return i().__VUE_DEVTOOLS_GLOBAL_HOOK__}function i(){return"undefined"!==typeof navigator&&"undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:{}}const l="function"===typeof Proxy,s="devtools-plugin:setup",c="plugin:settings:set";let d,u;function p(){var e;return void 0!==d||("undefined"!==typeof window&&window.performance?(d=!0,u=window.performance):"undefined"!==typeof n.g&&(null===(e=n.g.perf_hooks)||void 0===e?void 0:e.performance)?(d=!0,u=n.g.perf_hooks.performance):d=!1),d}function f(){return p()?u.now():Date.now()}class h{constructor(e,t){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=t;const n={};if(e.settings)for(const i in e.settings){const t=e.settings[i];n[i]=t.defaultValue}const r=`__vue-devtools-plugin-settings__${e.id}`;let o=Object.assign({},n);try{const e=localStorage.getItem(r),t=JSON.parse(e);Object.assign(o,t)}catch(a){}this.fallbacks={getSettings(){return o},setSettings(e){try{localStorage.setItem(r,JSON.stringify(e))}catch(a){}o=e},now(){return f()}},t&&t.on(c,((e,t)=>{e===this.plugin.id&&this.fallbacks.setSettings(t)})),this.proxiedOn=new Proxy({},{get:(e,t)=>this.target?this.target.on[t]:(...e)=>{this.onQueue.push({method:t,args:e})}}),this.proxiedTarget=new Proxy({},{get:(e,t)=>this.target?this.target[t]:"on"===t?this.proxiedOn:Object.keys(this.fallbacks).includes(t)?(...e)=>(this.targetQueue.push({method:t,args:e,resolve:()=>{}}),this.fallbacks[t](...e)):(...e)=>new Promise((n=>{this.targetQueue.push({method:t,args:e,resolve:n})}))})}async setRealTarget(e){this.target=e;for(const t of this.onQueue)this.target.on[t.method](...t.args);for(const t of this.targetQueue)t.resolve(await this.target[t.method](...t.args))}}function m(e,t){const n=e,r=i(),o=a(),c=l&&n.enableEarlyProxy;if(!o||!r.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&c){const e=c?new h(n,o):null,a=r.__VUE_DEVTOOLS_PLUGINS__=r.__VUE_DEVTOOLS_PLUGINS__||[];a.push({pluginDescriptor:n,setupFn:t,proxy:e}),e&&t(e.proxiedTarget)}else o.emit(s,e,t)}
/*!
 * vuex v4.0.2
 * (c) 2021 Evan You
 * @license MIT
 */
var v="store";function g(e){return void 0===e&&(e=null),(0,r.f3)(null!==e?e:v)}function b(e,t){Object.keys(e).forEach((function(n){return t(e[n],n)}))}function w(e){return null!==e&&"object"===typeof e}function y(e){return e&&"function"===typeof e.then}function k(e,t){return function(){return e(t)}}function _(e,t,n){return t.indexOf(e)<0&&(n&&n.prepend?t.unshift(e):t.push(e)),function(){var n=t.indexOf(e);n>-1&&t.splice(n,1)}}function x(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var n=e.state;C(e,n,[],e._modules.root,!0),S(e,n,t)}function S(e,t,n){var r=e._state;e.getters={},e._makeLocalGettersCache=Object.create(null);var a=e._wrappedGetters,i={};b(a,(function(t,n){i[n]=k(t,e),Object.defineProperty(e.getters,n,{get:function(){return i[n]()},enumerable:!0})})),e._state=(0,o.qj)({data:t}),e.strict&&T(e),r&&n&&e._withCommit((function(){r.data=null}))}function C(e,t,n,r,o){var a=!n.length,i=e._modules.getNamespace(n);if(r.namespaced&&(e._modulesNamespaceMap[i],e._modulesNamespaceMap[i]=r),!a&&!o){var l=j(t,n.slice(0,-1)),s=n[n.length-1];e._withCommit((function(){l[s]=r.state}))}var c=r.context=L(e,i,n);r.forEachMutation((function(t,n){var r=i+n;A(e,r,t,c)})),r.forEachAction((function(t,n){var r=t.root?n:i+n,o=t.handler||t;q(e,r,o,c)})),r.forEachGetter((function(t,n){var r=i+n;P(e,r,t,c)})),r.forEachChild((function(r,a){C(e,t,n.concat(a),r,o)}))}function L(e,t,n){var r=""===t,o={dispatch:r?e.dispatch:function(n,r,o){var a=O(n,r,o),i=a.payload,l=a.options,s=a.type;return l&&l.root||(s=t+s),e.dispatch(s,i)},commit:r?e.commit:function(n,r,o){var a=O(n,r,o),i=a.payload,l=a.options,s=a.type;l&&l.root||(s=t+s),e.commit(s,i,l)}};return Object.defineProperties(o,{getters:{get:r?function(){return e.getters}:function(){return E(e,t)}},state:{get:function(){return j(e.state,n)}}}),o}function E(e,t){if(!e._makeLocalGettersCache[t]){var n={},r=t.length;Object.keys(e.getters).forEach((function(o){if(o.slice(0,r)===t){var a=o.slice(r);Object.defineProperty(n,a,{get:function(){return e.getters[o]},enumerable:!0})}})),e._makeLocalGettersCache[t]=n}return e._makeLocalGettersCache[t]}function A(e,t,n,r){var o=e._mutations[t]||(e._mutations[t]=[]);o.push((function(t){n.call(e,r.state,t)}))}function q(e,t,n,r){var o=e._actions[t]||(e._actions[t]=[]);o.push((function(t){var o=n.call(e,{dispatch:r.dispatch,commit:r.commit,getters:r.getters,state:r.state,rootGetters:e.getters,rootState:e.state},t);return y(o)||(o=Promise.resolve(o)),e._devtoolHook?o.catch((function(t){throw e._devtoolHook.emit("vuex:error",t),t})):o}))}function P(e,t,n,r){e._wrappedGetters[t]||(e._wrappedGetters[t]=function(e){return n(r.state,r.getters,e.state,e.getters)})}function T(e){(0,r.YP)((function(){return e._state.data}),(function(){0}),{deep:!0,flush:"sync"})}function j(e,t){return t.reduce((function(e,t){return e[t]}),e)}function O(e,t,n){return w(e)&&e.type&&(n=t,t=e,e=e.type),{type:e,payload:t,options:n}}var M="vuex bindings",R="vuex:mutations",F="vuex:actions",H="vuex",z=0;function B(e,t){m({id:"org.vuejs.vuex",app:e,label:"Vuex",homepage:"https://next.vuex.vuejs.org/",logo:"https://vuejs.org/images/icons/favicon-96x96.png",packageName:"vuex",componentStateTypes:[M]},(function(n){n.addTimelineLayer({id:R,label:"Vuex Mutations",color:I}),n.addTimelineLayer({id:F,label:"Vuex Actions",color:I}),n.addInspector({id:H,label:"Vuex",icon:"storage",treeFilterPlaceholder:"Filter stores..."}),n.on.getInspectorTree((function(n){if(n.app===e&&n.inspectorId===H)if(n.filter){var r=[];Z(r,t._modules.root,n.filter,""),n.rootNodes=r}else n.rootNodes=[D(t._modules.root,"")]})),n.on.getInspectorState((function(n){if(n.app===e&&n.inspectorId===H){var r=n.nodeId;E(t,r),n.state=W(G(t._modules,r),"root"===r?t.getters:t._makeLocalGettersCache,r)}})),n.on.editInspectorState((function(n){if(n.app===e&&n.inspectorId===H){var r=n.nodeId,o=n.path;"root"!==r&&(o=r.split("/").filter(Boolean).concat(o)),t._withCommit((function(){n.set(t._state.data,o,n.state.value)}))}})),t.subscribe((function(e,t){var r={};e.payload&&(r.payload=e.payload),r.state=t,n.notifyComponentUpdate(),n.sendInspectorTree(H),n.sendInspectorState(H),n.addTimelineEvent({layerId:R,event:{time:Date.now(),title:e.type,data:r}})})),t.subscribeAction({before:function(e,t){var r={};e.payload&&(r.payload=e.payload),e._id=z++,e._time=Date.now(),r.state=t,n.addTimelineEvent({layerId:F,event:{time:e._time,title:e.type,groupId:e._id,subtitle:"start",data:r}})},after:function(e,t){var r={},o=Date.now()-e._time;r.duration={_custom:{type:"duration",display:o+"ms",tooltip:"Action duration",value:o}},e.payload&&(r.payload=e.payload),r.state=t,n.addTimelineEvent({layerId:F,event:{time:Date.now(),title:e.type,groupId:e._id,subtitle:"end",data:r}})}})}))}var I=8702998,V=6710886,$=16777215,N={label:"namespaced",textColor:$,backgroundColor:V};function U(e){return e&&"root"!==e?e.split("/").slice(-2,-1)[0]:"Root"}function D(e,t){return{id:t||"root",label:U(t),tags:e.namespaced?[N]:[],children:Object.keys(e._children).map((function(n){return D(e._children[n],t+n+"/")}))}}function Z(e,t,n,r){r.includes(n)&&e.push({id:r||"root",label:r.endsWith("/")?r.slice(0,r.length-1):r||"Root",tags:t.namespaced?[N]:[]}),Object.keys(t._children).forEach((function(o){Z(e,t._children[o],n,r+o+"/")}))}function W(e,t,n){t="root"===n?t:t[n];var r=Object.keys(t),o={state:Object.keys(e.state).map((function(t){return{key:t,editable:!0,value:e.state[t]}}))};if(r.length){var a=K(t);o.getters=Object.keys(a).map((function(e){return{key:e.endsWith("/")?U(e):e,editable:!1,value:J((function(){return a[e]}))}}))}return o}function K(e){var t={};return Object.keys(e).forEach((function(n){var r=n.split("/");if(r.length>1){var o=t,a=r.pop();r.forEach((function(e){o[e]||(o[e]={_custom:{value:{},display:e,tooltip:"Module",abstract:!0}}),o=o[e]._custom.value})),o[a]=J((function(){return e[n]}))}else t[n]=J((function(){return e[n]}))})),t}function G(e,t){var n=t.split("/").filter((function(e){return e}));return n.reduce((function(e,r,o){var a=e[r];if(!a)throw new Error('Missing module "'+r+'" for path "'+t+'".');return o===n.length-1?a:a._children}),"root"===t?e:e.root._children)}function J(e){try{return e()}catch(t){return t}}var Y=function(e,t){this.runtime=t,this._children=Object.create(null),this._rawModule=e;var n=e.state;this.state=("function"===typeof n?n():n)||{}},Q={namespaced:{configurable:!0}};Q.namespaced.get=function(){return!!this._rawModule.namespaced},Y.prototype.addChild=function(e,t){this._children[e]=t},Y.prototype.removeChild=function(e){delete this._children[e]},Y.prototype.getChild=function(e){return this._children[e]},Y.prototype.hasChild=function(e){return e in this._children},Y.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},Y.prototype.forEachChild=function(e){b(this._children,e)},Y.prototype.forEachGetter=function(e){this._rawModule.getters&&b(this._rawModule.getters,e)},Y.prototype.forEachAction=function(e){this._rawModule.actions&&b(this._rawModule.actions,e)},Y.prototype.forEachMutation=function(e){this._rawModule.mutations&&b(this._rawModule.mutations,e)},Object.defineProperties(Y.prototype,Q);var X=function(e){this.register([],e,!1)};function ee(e,t,n){if(t.update(n),n.modules)for(var r in n.modules){if(!t.getChild(r))return void 0;ee(e.concat(r),t.getChild(r),n.modules[r])}}X.prototype.get=function(e){return e.reduce((function(e,t){return e.getChild(t)}),this.root)},X.prototype.getNamespace=function(e){var t=this.root;return e.reduce((function(e,n){return t=t.getChild(n),e+(t.namespaced?n+"/":"")}),"")},X.prototype.update=function(e){ee([],this.root,e)},X.prototype.register=function(e,t,n){var r=this;void 0===n&&(n=!0);var o=new Y(t,n);if(0===e.length)this.root=o;else{var a=this.get(e.slice(0,-1));a.addChild(e[e.length-1],o)}t.modules&&b(t.modules,(function(t,o){r.register(e.concat(o),t,n)}))},X.prototype.unregister=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1],r=t.getChild(n);r&&r.runtime&&t.removeChild(n)},X.prototype.isRegistered=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1];return!!t&&t.hasChild(n)};function te(e){return new ne(e)}var ne=function(e){var t=this;void 0===e&&(e={});var n=e.plugins;void 0===n&&(n=[]);var r=e.strict;void 0===r&&(r=!1);var o=e.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new X(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._devtools=o;var a=this,i=this,l=i.dispatch,s=i.commit;this.dispatch=function(e,t){return l.call(a,e,t)},this.commit=function(e,t,n){return s.call(a,e,t,n)},this.strict=r;var c=this._modules.root.state;C(this,c,[],this._modules.root),S(this,c),n.forEach((function(e){return e(t)}))},re={state:{configurable:!0}};ne.prototype.install=function(e,t){e.provide(t||v,this),e.config.globalProperties.$store=this;var n=void 0!==this._devtools&&this._devtools;n&&B(e,this)},re.state.get=function(){return this._state.data},re.state.set=function(e){0},ne.prototype.commit=function(e,t,n){var r=this,o=O(e,t,n),a=o.type,i=o.payload,l=(o.options,{type:a,payload:i}),s=this._mutations[a];s&&(this._withCommit((function(){s.forEach((function(e){e(i)}))})),this._subscribers.slice().forEach((function(e){return e(l,r.state)})))},ne.prototype.dispatch=function(e,t){var n=this,r=O(e,t),o=r.type,a=r.payload,i={type:o,payload:a},l=this._actions[o];if(l){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(i,n.state)}))}catch(c){0}var s=l.length>1?Promise.all(l.map((function(e){return e(a)}))):l[0](a);return new Promise((function(e,t){s.then((function(t){try{n._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(i,n.state)}))}catch(c){0}e(t)}),(function(e){try{n._actionSubscribers.filter((function(e){return e.error})).forEach((function(t){return t.error(i,n.state,e)}))}catch(c){0}t(e)}))}))}},ne.prototype.subscribe=function(e,t){return _(e,this._subscribers,t)},ne.prototype.subscribeAction=function(e,t){var n="function"===typeof e?{before:e}:e;return _(n,this._actionSubscribers,t)},ne.prototype.watch=function(e,t,n){var o=this;return(0,r.YP)((function(){return e(o.state,o.getters)}),t,Object.assign({},n))},ne.prototype.replaceState=function(e){var t=this;this._withCommit((function(){t._state.data=e}))},ne.prototype.registerModule=function(e,t,n){void 0===n&&(n={}),"string"===typeof e&&(e=[e]),this._modules.register(e,t),C(this,this.state,e,this._modules.get(e),n.preserveState),S(this,this.state)},ne.prototype.unregisterModule=function(e){var t=this;"string"===typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit((function(){var n=j(t.state,e.slice(0,-1));delete n[e[e.length-1]]})),x(this)},ne.prototype.hasModule=function(e){return"string"===typeof e&&(e=[e]),this._modules.isRegistered(e)},ne.prototype.hotUpdate=function(e){this._modules.update(e),x(this,!0)},ne.prototype._withCommit=function(e){var t=this._committing;this._committing=!0,e(),this._committing=t},Object.defineProperties(ne.prototype,re);var oe=le((function(e,t){var n={};return ae(t).forEach((function(t){var r=t.key,o=t.val;n[r]=function(){var t=this.$store.state,n=this.$store.getters;if(e){var r=se(this.$store,"mapState",e);if(!r)return;t=r.context.state,n=r.context.getters}return"function"===typeof o?o.call(this,t,n):t[o]},n[r].vuex=!0})),n}));le((function(e,t){var n={};return ae(t).forEach((function(t){var r=t.key,o=t.val;n[r]=function(){var t=[],n=arguments.length;while(n--)t[n]=arguments[n];var r=this.$store.commit;if(e){var a=se(this.$store,"mapMutations",e);if(!a)return;r=a.context.commit}return"function"===typeof o?o.apply(this,[r].concat(t)):r.apply(this.$store,[o].concat(t))}})),n})),le((function(e,t){var n={};return ae(t).forEach((function(t){var r=t.key,o=t.val;o=e+o,n[r]=function(){if(!e||se(this.$store,"mapGetters",e))return this.$store.getters[o]},n[r].vuex=!0})),n})),le((function(e,t){var n={};return ae(t).forEach((function(t){var r=t.key,o=t.val;n[r]=function(){var t=[],n=arguments.length;while(n--)t[n]=arguments[n];var r=this.$store.dispatch;if(e){var a=se(this.$store,"mapActions",e);if(!a)return;r=a.context.dispatch}return"function"===typeof o?o.apply(this,[r].concat(t)):r.apply(this.$store,[o].concat(t))}})),n}));function ae(e){return ie(e)?Array.isArray(e)?e.map((function(e){return{key:e,val:e}})):Object.keys(e).map((function(t){return{key:t,val:e[t]}})):[]}function ie(e){return Array.isArray(e)||w(e)}function le(e){return function(t,n){return"string"!==typeof t?(n=t,t=""):"/"!==t.charAt(t.length-1)&&(t+="/"),e(t,n)}}function se(e,t,n){var r=e._modulesNamespaceMap[n];return r}},2670:(e,t,n)=>{"use strict";n.d(t,{MTK:()=>u,OGU:()=>s,cjC:()=>l,fOx:()=>f,geb:()=>p,kr9:()=>m,lY3:()=>o,pTk:()=>d,pcj:()=>i,r5M:()=>a,r9:()=>h,sVq:()=>r,z1j:()=>c});const r="M6,4H18V5H21V7H18V9H21V11H18V13H21V15H18V17H21V19H18V20H6V19H3V17H6V15H3V13H6V11H3V9H6V7H3V5H6V4M11,15V18H12V15H11M13,15V18H14V15H13M15,15V18H16V15H15Z",o="M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z",a="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z",i="M15.9,18.45C17.25,18.45 18.35,17.35 18.35,16C18.35,14.65 17.25,13.55 15.9,13.55C14.54,13.55 13.45,14.65 13.45,16C13.45,17.35 14.54,18.45 15.9,18.45M21.1,16.68L22.58,17.84C22.71,17.95 22.75,18.13 22.66,18.29L21.26,20.71C21.17,20.86 21,20.92 20.83,20.86L19.09,20.16C18.73,20.44 18.33,20.67 17.91,20.85L17.64,22.7C17.62,22.87 17.47,23 17.3,23H14.5C14.32,23 14.18,22.87 14.15,22.7L13.89,20.85C13.46,20.67 13.07,20.44 12.71,20.16L10.96,20.86C10.81,20.92 10.62,20.86 10.54,20.71L9.14,18.29C9.05,18.13 9.09,17.95 9.22,17.84L10.7,16.68L10.65,16L10.7,15.31L9.22,14.16C9.09,14.05 9.05,13.86 9.14,13.71L10.54,11.29C10.62,11.13 10.81,11.07 10.96,11.13L12.71,11.84C13.07,11.56 13.46,11.32 13.89,11.15L14.15,9.29C14.18,9.13 14.32,9 14.5,9H17.3C17.47,9 17.62,9.13 17.64,9.29L17.91,11.15C18.33,11.32 18.73,11.56 19.09,11.84L20.83,11.13C21,11.07 21.17,11.13 21.26,11.29L22.66,13.71C22.75,13.86 22.71,14.05 22.58,14.16L21.1,15.31L21.15,16L21.1,16.68M6.69,8.07C7.56,8.07 8.26,7.37 8.26,6.5C8.26,5.63 7.56,4.92 6.69,4.92A1.58,1.58 0 0,0 5.11,6.5C5.11,7.37 5.82,8.07 6.69,8.07M10.03,6.94L11,7.68C11.07,7.75 11.09,7.87 11.03,7.97L10.13,9.53C10.08,9.63 9.96,9.67 9.86,9.63L8.74,9.18L8,9.62L7.81,10.81C7.79,10.92 7.7,11 7.59,11H5.79C5.67,11 5.58,10.92 5.56,10.81L5.4,9.62L4.64,9.18L3.5,9.63C3.41,9.67 3.3,9.63 3.24,9.53L2.34,7.97C2.28,7.87 2.31,7.75 2.39,7.68L3.34,6.94L3.31,6.5L3.34,6.06L2.39,5.32C2.31,5.25 2.28,5.13 2.34,5.03L3.24,3.47C3.3,3.37 3.41,3.33 3.5,3.37L4.63,3.82L5.4,3.38L5.56,2.19C5.58,2.08 5.67,2 5.79,2H7.59C7.7,2 7.79,2.08 7.81,2.19L8,3.38L8.74,3.82L9.86,3.37C9.96,3.33 10.08,3.37 10.13,3.47L11.03,5.03C11.09,5.13 11.07,5.25 11,5.32L10.03,6.06L10.06,6.5L10.03,6.94Z",l="M22,9V7H20V5A2,2 0 0,0 18,3H4A2,2 0 0,0 2,5V19A2,2 0 0,0 4,21H18A2,2 0 0,0 20,19V17H22V15H20V13H22V11H20V9H22M18,19H4V5H18V19M6,13H11V17H6V13M12,7H16V10H12V7M6,7H11V12H6V7M12,11H16V17H12V11Z",s="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z",c="M7,15H9V18H11V15H13V18H15V15H17V18H19V9H15V6H9V9H5V18H7V15M4.38,3H19.63C20.94,3 22,4.06 22,5.38V19.63A2.37,2.37 0 0,1 19.63,22H4.38C3.06,22 2,20.94 2,19.63V5.38C2,4.06 3.06,3 4.38,3Z",d="M2 7H4.5V17H3V8.5H2M22 7V16H14V17H7V16H6V7M10 9H8V12H10M13 9H11V12H13M20 9H15V14H20V9Z",u="M14,2L20,8V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V4A2,2 0 0,1 6,2H14M18,20V9H13V4H6V20H18M10.92,12.31C10.68,11.54 10.15,9.08 11.55,9.04C12.95,9 12.03,12.16 12.03,12.16C12.42,13.65 14.05,14.72 14.05,14.72C14.55,14.57 17.4,14.24 17,15.72C16.57,17.2 13.5,15.81 13.5,15.81C11.55,15.95 10.09,16.47 10.09,16.47C8.96,18.58 7.64,19.5 7.1,18.61C6.43,17.5 9.23,16.07 9.23,16.07C10.68,13.72 10.9,12.35 10.92,12.31M11.57,13.15C11.17,14.45 10.37,15.84 10.37,15.84C11.22,15.5 13.08,15.11 13.08,15.11C11.94,14.11 11.59,13.16 11.57,13.15M14.71,15.32C14.71,15.32 16.46,15.97 16.5,15.71C16.57,15.44 15.17,15.2 14.71,15.32M9.05,16.81C8.28,17.11 7.54,18.39 7.72,18.39C7.9,18.4 8.63,17.79 9.05,16.81M11.57,11.26C11.57,11.21 12,9.58 11.57,9.53C11.27,9.5 11.56,11.22 11.57,11.26Z",p="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z",f="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z",h="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z",m="M15,7V11H16V13H13V5H15L12,1L9,5H11V13H8V10.93C8.7,10.56 9.2,9.85 9.2,9C9.2,7.78 8.21,6.8 7,6.8C5.78,6.8 4.8,7.78 4.8,9C4.8,9.85 5.3,10.56 6,10.93V13A2,2 0 0,0 8,15H11V18.05C10.29,18.41 9.8,19.15 9.8,20A2.2,2.2 0 0,0 12,22.2A2.2,2.2 0 0,0 14.2,20C14.2,19.15 13.71,18.41 13,18.05V15H16A2,2 0 0,0 18,13V11H19V7H15Z"},9448:(e,t,n)=>{"use strict";n.d(t,{FRq:()=>l,Ka7:()=>o,MTK:()=>a,pkj:()=>s,yyh:()=>i,zr:()=>r});const r="M16 17V19H2V17S2 13 9 13 16 17 16 17M12.5 7.5A3.5 3.5 0 1 0 9 11A3.5 3.5 0 0 0 12.5 7.5M15.94 13A5.32 5.32 0 0 1 18 17V19H22V17S22 13.37 15.94 13M15 4A3.39 3.39 0 0 0 13.07 4.59A5 5 0 0 1 13.07 10.41A3.39 3.39 0 0 0 15 11A3.5 3.5 0 0 0 15 4Z",o="M18.72,14.76C19.07,13.91 19.26,13 19.26,12C19.26,11.28 19.15,10.59 18.96,9.95C18.31,10.1 17.63,10.18 16.92,10.18C13.86,10.18 11.15,8.67 9.5,6.34C8.61,8.5 6.91,10.26 4.77,11.22C4.73,11.47 4.73,11.74 4.73,12A7.27,7.27 0 0,0 12,19.27C13.05,19.27 14.06,19.04 14.97,18.63C15.54,19.72 15.8,20.26 15.78,20.26C14.14,20.81 12.87,21.08 12,21.08C9.58,21.08 7.27,20.13 5.57,18.42C4.53,17.38 3.76,16.11 3.33,14.73H2V10.18H3.09C3.93,6.04 7.6,2.92 12,2.92C14.4,2.92 16.71,3.87 18.42,5.58C19.69,6.84 20.54,8.45 20.89,10.18H22V14.67H22V14.69L22,14.73H21.94L18.38,18L13.08,17.4V15.73H17.91L18.72,14.76M9.27,11.77C9.57,11.77 9.86,11.89 10.07,12.11C10.28,12.32 10.4,12.61 10.4,12.91C10.4,13.21 10.28,13.5 10.07,13.71C9.86,13.92 9.57,14.04 9.27,14.04C8.64,14.04 8.13,13.54 8.13,12.91C8.13,12.28 8.64,11.77 9.27,11.77M14.72,11.77C15.35,11.77 15.85,12.28 15.85,12.91C15.85,13.54 15.35,14.04 14.72,14.04C14.09,14.04 13.58,13.54 13.58,12.91A1.14,1.14 0 0,1 14.72,11.77Z",a="M14,2L20,8V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V4A2,2 0 0,1 6,2H14M18,20V9H13V4H6V20H18M10.92,12.31C10.68,11.54 10.15,9.08 11.55,9.04C12.95,9 12.03,12.16 12.03,12.16C12.42,13.65 14.05,14.72 14.05,14.72C14.55,14.57 17.4,14.24 17,15.72C16.57,17.2 13.5,15.81 13.5,15.81C11.55,15.95 10.09,16.47 10.09,16.47C8.96,18.58 7.64,19.5 7.1,18.61C6.43,17.5 9.23,16.07 9.23,16.07C10.68,13.72 10.9,12.35 10.92,12.31M11.57,13.15C11.17,14.45 10.37,15.84 10.37,15.84C11.22,15.5 13.08,15.11 13.08,15.11C11.94,14.11 11.59,13.16 11.57,13.15M14.71,15.32C14.71,15.32 16.46,15.97 16.5,15.71C16.57,15.44 15.17,15.2 14.71,15.32M9.05,16.81C8.28,17.11 7.54,18.39 7.72,18.39C7.9,18.4 8.63,17.79 9.05,16.81M11.57,11.26C11.57,11.21 12,9.58 11.57,9.53C11.27,9.5 11.56,11.22 11.57,11.26Z",i="M20,11H23V13H20V11M1,11H4V13H1V11M13,1V4H11V1H13M4.92,3.5L7.05,5.64L5.63,7.05L3.5,4.93L4.92,3.5M16.95,5.63L19.07,3.5L20.5,4.93L18.37,7.05L16.95,5.63M12,6A6,6 0 0,1 18,12C18,14.22 16.79,16.16 15,17.2V19A1,1 0 0,1 14,20H10A1,1 0 0,1 9,19V17.2C7.21,16.16 6,14.22 6,12A6,6 0 0,1 12,6M14,21V22A1,1 0 0,1 13,23H11A1,1 0 0,1 10,22V21H14M11,18H13V15.87C14.73,15.43 16,13.86 16,12A4,4 0 0,0 12,8A4,4 0 0,0 8,12C8,13.86 9.27,15.43 11,15.87V18Z",l="M20,17A2,2 0 0,0 22,15V4A2,2 0 0,0 20,2H9.46C9.81,2.61 10,3.3 10,4H20V15H11V17M15,7V9H9V22H7V16H5V22H3V14H1.5V9A2,2 0 0,1 3.5,7H15M8,4A2,2 0 0,1 6,6A2,2 0 0,1 4,4A2,2 0 0,1 6,2A2,2 0 0,1 8,4Z",s="M18,16L14,12.8V16H6V8H14V11.2L18,8M20,4H4A2,2 0 0,0 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4Z"},5448:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});const r={name:"material-icons",type:{positive:"check_circle",negative:"warning",info:"info",warning:"priority_high"},arrow:{up:"arrow_upward",right:"arrow_forward",down:"arrow_downward",left:"arrow_back",dropdown:"arrow_drop_down"},chevron:{left:"chevron_left",right:"chevron_right"},colorPicker:{spectrum:"gradient",tune:"tune",palette:"style"},pullToRefresh:{icon:"refresh"},carousel:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down",navigationIcon:"lens"},chip:{remove:"cancel",selected:"check"},datetime:{arrowLeft:"chevron_left",arrowRight:"chevron_right",now:"access_time",today:"today"},editor:{bold:"format_bold",italic:"format_italic",strikethrough:"strikethrough_s",underline:"format_underlined",unorderedList:"format_list_bulleted",orderedList:"format_list_numbered",subscript:"vertical_align_bottom",superscript:"vertical_align_top",hyperlink:"link",toggleFullscreen:"fullscreen",quote:"format_quote",left:"format_align_left",center:"format_align_center",right:"format_align_right",justify:"format_align_justify",print:"print",outdent:"format_indent_decrease",indent:"format_indent_increase",removeFormat:"format_clear",formatting:"text_format",fontSize:"format_size",align:"format_align_left",hr:"remove",undo:"undo",redo:"redo",heading:"format_size",code:"code",size:"format_size",font:"font_download",viewSource:"code"},expansionItem:{icon:"keyboard_arrow_down",denseIcon:"arrow_drop_down"},fab:{icon:"add",activeIcon:"close"},field:{clear:"cancel",error:"error"},pagination:{first:"first_page",prev:"keyboard_arrow_left",next:"keyboard_arrow_right",last:"last_page"},rating:{icon:"grade"},stepper:{done:"check",active:"edit",error:"warning"},tabs:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down"},table:{arrowUp:"arrow_upward",warning:"warning",firstPage:"first_page",prevPage:"chevron_left",nextPage:"chevron_right",lastPage:"last_page"},tree:{icon:"play_arrow"},uploader:{done:"done",clear:"clear",add:"add_box",upload:"cloud_upload",removeQueue:"clear_all",removeUploaded:"done_all"}}},3340:(e,t,n)=>{"use strict";function r(e){return e}n.d(t,{xr:()=>r})},8593:e=>{"use strict";e.exports=JSON.parse('{"name":"axios","version":"0.21.4","description":"Promise based HTTP client for the browser and node.js","main":"index.js","scripts":{"test":"grunt test","start":"node ./sandbox/server.js","build":"NODE_ENV=production grunt build","preversion":"npm test","version":"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json","postversion":"git push && git push --tags","examples":"node ./examples/server.js","coveralls":"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js","fix":"eslint --fix lib/**/*.js"},"repository":{"type":"git","url":"https://github.com/axios/axios.git"},"keywords":["xhr","http","ajax","promise","node"],"author":"Matt Zabriskie","license":"MIT","bugs":{"url":"https://github.com/axios/axios/issues"},"homepage":"https://axios-http.com","devDependencies":{"coveralls":"^3.0.0","es6-promise":"^4.2.4","grunt":"^1.3.0","grunt-banner":"^0.6.0","grunt-cli":"^1.2.0","grunt-contrib-clean":"^1.1.0","grunt-contrib-watch":"^1.0.0","grunt-eslint":"^23.0.0","grunt-karma":"^4.0.0","grunt-mocha-test":"^0.13.3","grunt-ts":"^6.0.0-beta.19","grunt-webpack":"^4.0.2","istanbul-instrumenter-loader":"^1.0.0","jasmine-core":"^2.4.1","karma":"^6.3.2","karma-chrome-launcher":"^3.1.0","karma-firefox-launcher":"^2.1.0","karma-jasmine":"^1.1.1","karma-jasmine-ajax":"^0.1.13","karma-safari-launcher":"^1.0.0","karma-sauce-launcher":"^4.3.6","karma-sinon":"^1.0.5","karma-sourcemap-loader":"^0.3.8","karma-webpack":"^4.0.2","load-grunt-tasks":"^3.5.2","minimist":"^1.2.0","mocha":"^8.2.1","sinon":"^4.5.0","terser-webpack-plugin":"^4.2.3","typescript":"^4.0.5","url-search-params":"^0.10.0","webpack":"^4.44.2","webpack-dev-server":"^3.11.0"},"browser":{"./lib/adapters/http.js":"./lib/adapters/xhr.js"},"jsdelivr":"dist/axios.min.js","unpkg":"dist/axios.min.js","typings":"./index.d.ts","dependencies":{"follow-redirects":"^1.14.0"},"bundlesize":[{"path":"./dist/axios.min.js","threshold":"5kB"}],"_resolved":"https://registry.npmjs.org/axios/-/axios-0.21.4.tgz","_integrity":"sha512-ut5vewkiu8jjGBdqpM44XxjuCjq9LAKeHVmoVfHVzy8eHgxxq8SbAVQNovDA8mVi05kP0Ea/n/UzcSHcTJQfNg==","_from":"axios@0.21.4"}')}}]);