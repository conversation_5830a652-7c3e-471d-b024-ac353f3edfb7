{"sdkCategories": [{"installed": [], "legacy": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.8051.v4.3.1.feature.metadata.resource.feature.group/icon_sdk_40x40.png", "repositoryType": "p2", "permission": true, "id": "8051", "label": "8051 SDK", "installable": [{"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.8051.v4.3.1.feature.metadata.resource.feature.group/icon_sdk_40x40.png", "id": "com.silabs.sdk.8051.v4.3.1.feature", "label": "8051 SDK 4.3.1", "version": "4.3.1", "releaseNote": "4.3.1\n\tAdded support for EFM8SB1 SLSTK2010A BRD5101B\n\tAdded support for EFM8SB2 SLSTK2011A BRD5100B\n4.3.0\n\tAdded EFM8BB50 support\n\tFixed delay implementation in EFM8 Flash examples\n\tFixed bug in EFM8BB51 and EFM8BB52 PCA frequency output examples\n\tAdded EFM8BB51 and EFM8BB52 blinky demo for EKs\n4.2.5\n\tAdded support for EFM8BB1 SLSTK2020A BRD5200B\n\tAdded support for EFM8BB2 SLSTK2021A BRD5201B\n\tAdded support for EFM8BB3 SLSTK2022A BRD5202B\n\tAdded support for EFM8LB1 SLSTK2030A BRD5300B\n\tAdded support for EFM8UB1 SLSTK2000A BRD5000B\n\tAdded support for EFM8UB2 SLSTK2001A BRD5001B\n4.2.4\n\tUpdated EFM8BB52 and EFM8BB51 header files and impacted libraries\n\tAdded Configurator projects for all EFM8BB52 and EFM8BB51 Pro Kit examples\n\tRegenerated initialization code for all EFM8BB52 and EFM8BB51 examples using Configurator\n4.2.3\n\tUpdated header files for EFM8BB52 and EFM8BB51\n\tUpdated PWM examples for EFM8BB52 and EFM8BB51\n\tFixed SFRPAGE issue in pwr.c for EFM8BB52 and EFM8BB51\n4.2.2\n\tUpdated EFM8BB51 and EFM8BB52 header files\n\tUpdated EFM8BB51 and EFM8BB52 Pro Kit examples for PK520xB boards\n\tAdded blinky example for EFM8BB51 and EFM8BB52 Explorer Kits\n4.2.1\n\tUpdated EFM8BB51 and EFM8BB52 header files for Rev C\n\tAdded peripheral driver libraries for EFM8BB51 and EFM8BB52\n\tAdded demos and examples for EFM8BB51 and EFM8BB52\n4.2.0\n\tAdded preliminary support for EFM8BB51 and EFM8BB52\n\tAdded GPIO Expander example for EFM8 devices\n\tAdded header files for EFM8BB3 Rev D\n4.1.7\n\tAdded Configurator support for F970\n\tUpdated examples to use \"si_toolchain.h\"\n\tRemoved /an examples from SDK\n4.1.6\n\tAdded EFM8BB1 LCK examples\n\tUpdated VCPXpress Library\n4.1.5\n\tUpdated USB Library to 1.0.4 - fix potential enumeration issue\n4.1.4\n\tAdded WGX Demo to EFM8UB1 kit examples\n\tAdded WGX Demo to EFM8UB2 kit examples\n4.1.3\n\tAdded BGX Demo to EFM8SB2 kit examples\n\tAdded BGX Demo to EFM8UB1 kit examples\n\tFixed uart_1 driver from always #define-ing IS_DOXYGEN\n4.1.2\n\tFixed F91x examples\n\tFixed pathing issue in the efm8 'shared' module\n\tFixed warnings in smb_0.c\n\tAdded 'S1\" EFM8LB1 bootloaders\n\tUpdated UB3 'blinky' example to enter bootloader mode\n4.1.1\n\tFixed issue with not being able to view demos for 8051 parts\n4.1.0\n\tThunderboard EFM8UB3 launch\n    EFM8BB3 Type C High Power Charger Reference Design launch\n4.0.9\n\tUpdate CSLIB and examples for F970, F990, and SB1 to include support for per-channel thresholds including profiler support\n4.0.6\n4.0.5\n\tUpdate new project templates to include SiLabs_Startup as a function to disable the watchdog timer\n\tVarious updates and improvements to example projects\n4.0.4\n  Retested and fixed build errors in all projects\n4.0.3\n\tSupport for IEC 60730 launch\n4.0.2\n\tMinor support changes\n4.0.1\n\tFix a few project build and import issues\n4.0.0\n\tInitial release", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\8051\\v4.3.1", "desc": "Silicon Labs 8051 SDK"}, {"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.8051.v4.3.0.feature.metadata.resource.feature.group/icon_sdk_40x40.png", "id": "com.silabs.sdk.8051.v4.3.0.feature", "label": "8051 SDK 4.3.0", "version": "4.3.0", "releaseNote": "4.3.0\n\tAdded EFM8BB50 support\n\tFixed delay implementation in EFM8 Flash examples\n\tFixed bug in EFM8BB51 and EFM8BB52 PCA frequency output examples\n\tAdded EFM8BB51 and EFM8BB52 blinky demo for EKs\n4.2.5\n\tAdded support for EFM8BB1 SLSTK2020A BRD5200B\n\tAdded support for EFM8BB2 SLSTK2021A BRD5201B\n\tAdded support for EFM8BB3 SLSTK2022A BRD5202B\n\tAdded support for EFM8LB1 SLSTK2030A BRD5300B\n\tAdded support for EFM8UB1 SLSTK2000A BRD5000B\n\tAdded support for EFM8UB2 SLSTK2001A BRD5001B\n4.2.4\n\tUpdated EFM8BB52 and EFM8BB51 header files and impacted libraries\n\tAdded Configurator projects for all EFM8BB52 and EFM8BB51 Pro Kit examples\n\tRegenerated initialization code for all EFM8BB52 and EFM8BB51 examples using Configurator\n4.2.3\n\tUpdated header files for EFM8BB52 and EFM8BB51\n\tUpdated PWM examples for EFM8BB52 and EFM8BB51\n\tFixed SFRPAGE issue in pwr.c for EFM8BB52 and EFM8BB51\n4.2.2\n\tUpdated EFM8BB51 and EFM8BB52 header files\n\tUpdated EFM8BB51 and EFM8BB52 Pro Kit examples for PK520xB boards\n\tAdded blinky example for EFM8BB51 and EFM8BB52 Explorer Kits\n4.2.1\n\tUpdated EFM8BB51 and EFM8BB52 header files for Rev C\n\tAdded peripheral driver libraries for EFM8BB51 and EFM8BB52\n\tAdded demos and examples for EFM8BB51 and EFM8BB52\n4.2.0\n\tAdded preliminary support for EFM8BB51 and EFM8BB52\n\tAdded GPIO Expander example for EFM8 devices\n\tAdded header files for EFM8BB3 Rev D\n4.1.7\n\tAdded Configurator support for F970\n\tUpdated examples to use \"si_toolchain.h\"\n\tRemoved /an examples from SDK\n4.1.6\n\tAdded EFM8BB1 LCK examples\n\tUpdated VCPXpress Library\n4.1.5\n\tUpdated USB Library to 1.0.4 - fix potential enumeration issue\n4.1.4\n\tAdded WGX Demo to EFM8UB1 kit examples\n\tAdded WGX Demo to EFM8UB2 kit examples\n4.1.3\n\tAdded BGX Demo to EFM8SB2 kit examples\n\tAdded BGX Demo to EFM8UB1 kit examples\n\tFixed uart_1 driver from always #define-ing IS_DOXYGEN\n4.1.2\n\tFixed F91x examples\n\tFixed pathing issue in the efm8 'shared' module\n\tFixed warnings in smb_0.c\n\tAdded 'S1\" EFM8LB1 bootloaders\n\tUpdated UB3 'blinky' example to enter bootloader mode\n4.1.1\n\tFixed issue with not being able to view demos for 8051 parts\n4.1.0\n\tThunderboard EFM8UB3 launch\n    EFM8BB3 Type C High Power Charger Reference Design launch\n4.0.9\n\tUpdate CSLIB and examples for F970, F990, and SB1 to include support for per-channel thresholds including profiler support\n4.0.6\n4.0.5\n\tUpdate new project templates to include SiLabs_Startup as a function to disable the watchdog timer\n\tVarious updates and improvements to example projects\n4.0.4\n  Retested and fixed build errors in all projects\n4.0.3\n\tSupport for IEC 60730 launch\n4.0.2\n\tMinor support changes\n4.0.1\n\tFix a few project build and import issues\n4.0.0\n\tInitial release", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\8051\\v4.3.0", "desc": "Silicon Labs 8051 SDK"}, {"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.8051.v4.2.5.feature.metadata.resource.feature.group/icon_sdk_40x40.png", "id": "com.silabs.sdk.8051.v4.2.5.feature", "label": "8051 SDK 4.2.5", "version": "4.2.5", "releaseNote": "4.2.5\n\tAdded support for EFM8BB1 SLSTK2020A BRD5200B\n\tAdded support for EFM8BB2 SLSTK2021A BRD5201B\n\tAdded support for EFM8BB3 SLSTK2022A BRD5202B\n\tAdded support for EFM8LB1 SLSTK2030A BRD5300B\n\tAdded support for EFM8UB1 SLSTK2000A BRD5000B\n\tAdded support for EFM8UB2 SLSTK2001A BRD5001B\n4.2.4\n\tUpdated EFM8BB52 and EFM8BB51 header files and impacted libraries\n\tAdded Configurator projects for all EFM8BB52 and EFM8BB51 Pro Kit examples\n\tRegenerated initialization code for all EFM8BB52 and EFM8BB51 examples using Configurator\n4.2.3\n\tUpdated header files for EFM8BB52 and EFM8BB51\n\tUpdated PWM examples for EFM8BB52 and EFM8BB51\n\tFixed SFRPAGE issue in pwr.c for EFM8BB52 and EFM8BB51\n4.2.2\n\tUpdated EFM8BB51 and EFM8BB52 header files\n\tUpdated EFM8BB51 and EFM8BB52 Pro Kit examples for PK520xB boards\n\tAdded blinky example for EFM8BB51 and EFM8BB52 Explorer Kits\n4.2.1\n\tUpdated EFM8BB51 and EFM8BB52 header files for Rev C\n\tAdded peripheral driver libraries for EFM8BB51 and EFM8BB52\n\tAdded demos and examples for EFM8BB51 and EFM8BB52\n4.2.0\n\tAdded preliminary support for EFM8BB51 and EFM8BB52\n\tAdded GPIO Expander example for EFM8 devices\n\tAdded header files for EFM8BB3 Rev D\n4.1.7\n\tAdded Configurator support for F970\n\tUpdated examples to use \"si_toolchain.h\"\n\tRemoved /an examples from SDK\n4.1.6\n\tAdded EFM8BB1 LCK examples\n\tUpdated VCPXpress Library\n4.1.5\n\tUpdated USB Library to 1.0.4 - fix potential enumeration issue\n4.1.4\n\tAdded WGX Demo to EFM8UB1 kit examples\n\tAdded WGX Demo to EFM8UB2 kit examples\n4.1.3\n\tAdded BGX Demo to EFM8SB2 kit examples\n\tAdded BGX Demo to EFM8UB1 kit examples\n\tFixed uart_1 driver from always #define-ing IS_DOXYGEN\n4.1.2\n\tFixed F91x examples\n\tFixed pathing issue in the efm8 'shared' module\n\tFixed warnings in smb_0.c\n\tAdded 'S1\" EFM8LB1 bootloaders\n\tUpdated UB3 'blinky' example to enter bootloader mode\n4.1.1\n\tFixed issue with not being able to view demos for 8051 parts\n4.1.0\n\tThunderboard EFM8UB3 launch\n    EFM8BB3 Type C High Power Charger Reference Design launch\n4.0.9\n\tUpdate CSLIB and examples for F970, F990, and SB1 to include support for per-channel thresholds including profiler support\n4.0.6\n4.0.5\n\tUpdate new project templates to include SiLabs_Startup as a function to disable the watchdog timer\n\tVarious updates and improvements to example projects\n4.0.4\n  Retested and fixed build errors in all projects\n4.0.3\n\tSupport for IEC 60730 launch\n4.0.2\n\tMinor support changes\n4.0.1\n\tFix a few project build and import issues\n4.0.0\n\tInitial release", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\8051\\v4.2.5", "desc": "Silicon Labs 8051 SDK"}, {"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.8051.v4.2.4.feature.metadata.resource.feature.group/icon_sdk_40x40.png", "id": "com.silabs.sdk.8051.v4.2.4.feature", "label": "8051 SDK 4.2.4", "version": "4.2.4", "releaseNote": "4.2.4\n\tUpdated EFM8BB52 and EFM8BB51 header files and impacted libraries\n\tAdded Configurator projects for all EFM8BB52 and EFM8BB51 Pro Kit examples\n\tRegenerated initialization code for all EFM8BB52 and EFM8BB51 examples using Configurator\n4.2.3\n\tUpdated header files for EFM8BB52 and EFM8BB51\n\tUpdated PWM examples for EFM8BB52 and EFM8BB51\n\tFixed SFRPAGE issue in pwr.c for EFM8BB52 and EFM8BB51\n4.2.2\n\tUpdated EFM8BB51 and EFM8BB52 header files\n\tUpdated EFM8BB51 and EFM8BB52 Pro Kit examples for PK520xB boards\n\tAdded blinky example for EFM8BB51 and EFM8BB52 Explorer Kits\n4.2.1\n\tUpdated EFM8BB51 and EFM8BB52 header files for Rev C\n\tAdded peripheral driver libraries for EFM<PERSON>BB51 and EFM8BB52\n\tAdded demos and examples for EFM8BB51 and EFM8BB52\n4.2.0\n\tAdded preliminary support for EFM8BB51 and EFM8BB52\n\tAdded GPIO Expander example for EFM8 devices\n\tAdded header files for EFM8BB3 Rev D\n4.1.7\n\tAdded Configurator support for F970\n\tUpdated examples to use \"si_toolchain.h\"\n\tRemoved /an examples from SDK\n4.1.6\n\tAdded EFM8BB1 LCK examples\n\tUpdated VCPXpress Library\n4.1.5\n\tUpdated USB Library to 1.0.4 - fix potential enumeration issue\n4.1.4\n\tAdded WGX Demo to EFM8UB1 kit examples\n\tAdded WGX Demo to EFM8UB2 kit examples\n4.1.3\n\tAdded BGX Demo to EFM8SB2 kit examples\n\tAdded BGX Demo to EFM8UB1 kit examples\n\tFixed uart_1 driver from always #define-ing IS_DOXYGEN\n4.1.2\n\tFixed F91x examples\n\tFixed pathing issue in the efm8 'shared' module\n\tFixed warnings in smb_0.c\n\tAdded 'S1\" EFM8LB1 bootloaders\n\tUpdated UB3 'blinky' example to enter bootloader mode\n4.1.1\n\tFixed issue with not being able to view demos for 8051 parts\n4.1.0\n\tThunderboard EFM8UB3 launch\n    EFM8BB3 Type C High Power Charger Reference Design launch\n4.0.9\n\tUpdate CSLIB and examples for F970, F990, and SB1 to include support for per-channel thresholds including profiler support\n4.0.6\n4.0.5\n\tUpdate new project templates to include SiLabs_Startup as a function to disable the watchdog timer\n\tVarious updates and improvements to example projects\n4.0.4\n  Retested and fixed build errors in all projects\n4.0.3\n\tSupport for IEC 60730 launch\n4.0.2\n\tMinor support changes\n4.0.1\n\tFix a few project build and import issues\n4.0.0\n\tInitial release", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\8051\\v4.2.4", "desc": "Silicon Labs 8051 SDK"}, {"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.8051.v4.2.3.feature.metadata.resource.feature.group/icon_sdk_40x40.png", "id": "com.silabs.sdk.8051.v4.2.3.feature", "label": "8051 SDK 4.2.3", "version": "4.2.3", "releaseNote": "4.2.3\n\tUpdated header files for EFM8BB52 and EFM8BB51\n\tUpdated PWM examples for EFM8BB52 and EFM8BB51\n\tFixed SFRPAGE issue in pwr.c for EFM8BB52 and EFM8BB51\n4.2.2\n\tUpdated EFM8BB51 and EFM8BB52 header files\n\tUpdated EFM8BB51 and EFM8BB52 Pro Kit examples for PK520xB boards\n\tAdded blinky example for EFM8BB51 and EFM8BB52 Explorer Kits\n4.2.1\n\tUpdated EFM8BB51 and EFM8BB52 header files for Rev C\n\tAdded peripheral driver libraries for EFM8BB51 and EFM8BB52\n\tAdded demos and examples for EFM8BB51 and EFM8BB52\n4.2.0\n\tAdded preliminary support for EFM8BB51 and EFM8BB52\n\tAdded GPIO Expander example for EFM8 devices\n\tAdded header files for EFM8BB3 Rev D\n4.1.7\n\tAdded Configurator support for F970\n\tUpdated examples to use \"si_toolchain.h\"\n\tRemoved /an examples from SDK\n4.1.6\n\tAdded EFM8BB1 LCK examples\n\tUpdated VCPXpress Library\n4.1.5\n\tUpdated USB Library to 1.0.4 - fix potential enumeration issue\n4.1.4\n\tAdded WGX Demo to EFM8UB1 kit examples\n\tAdded WGX Demo to EFM8UB2 kit examples\n4.1.3\n\tAdded BGX Demo to EFM8SB2 kit examples\n\tAdded BGX Demo to EFM8UB1 kit examples\n\tFixed uart_1 driver from always #define-ing IS_DOXYGEN\n4.1.2\n\tFixed F91x examples\n\tFixed pathing issue in the efm8 'shared' module\n\tFixed warnings in smb_0.c\n\tAdded 'S1\" EFM8LB1 bootloaders\n\tUpdated UB3 'blinky' example to enter bootloader mode\n4.1.1\n\tFixed issue with not being able to view demos for 8051 parts\n4.1.0\n\tThunderboard EFM8UB3 launch\n    EFM8BB3 Type C High Power Charger Reference Design launch\n4.0.9\n\tUpdate CSLIB and examples for F970, F990, and SB1 to include support for per-channel thresholds including profiler support\n4.0.6\n4.0.5\n\tUpdate new project templates to include SiLabs_Startup as a function to disable the watchdog timer\n\tVarious updates and improvements to example projects\n4.0.4\n  Retested and fixed build errors in all projects\n4.0.3\n\tSupport for IEC 60730 launch\n4.0.2\n\tMinor support changes\n4.0.1\n\tFix a few project build and import issues\n4.0.0\n\tInitial release", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\8051\\v4.2.3", "desc": "Silicon Labs 8051 SDK"}, {"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.8051.v4.2.2.feature.metadata.resource.feature.group/icon_sdk_40x40.png", "id": "com.silabs.sdk.8051.v4.2.2.feature", "label": "8051 SDK 4.2.2", "version": "4.2.2", "releaseNote": "4.2.2\n\tUpdated EFM8BB51 and EFM8BB52 header files\n\tUpdated EFM8BB51 and EFM8BB52 Pro Kit examples for PK520xB boards\n\tAdded blinky example for EFM8BB51 and EFM8BB52 Explorer Kits\n4.2.1\n\tUpdated EFM8BB51 and EFM8BB52 header files for Rev C\n\tAdded peripheral driver libraries for EFM8BB51 and EFM8BB52\n\tAdded demos and examples for EFM8BB51 and EFM8BB52\n4.2.0\n\tAdded preliminary support for EFM8BB51 and EFM8BB52\n\tAdded GPIO Expander example for EFM8 devices\n\tAdded header files for EFM8BB3 Rev D\n4.1.7\n\tAdded Configurator support for F970\n\tUpdated examples to use \"si_toolchain.h\"\n\tRemoved /an examples from SDK\n4.1.6\n\tAdded EFM8BB1 LCK examples\n\tUpdated VCPXpress Library\n4.1.5\n\tUpdated USB Library to 1.0.4 - fix potential enumeration issue\n4.1.4\n\tAdded WGX Demo to EFM8UB1 kit examples\n\tAdded WGX Demo to EFM8UB2 kit examples\n4.1.3\n\tAdded BGX Demo to EFM8SB2 kit examples\n\tAdded BGX Demo to EFM8UB1 kit examples\n\tFixed uart_1 driver from always #define-ing IS_DOXYGEN\n4.1.2\n\tFixed F91x examples\n\tFixed pathing issue in the efm8 'shared' module\n\tFixed warnings in smb_0.c\n\tAdded 'S1\" EFM8LB1 bootloaders\n\tUpdated UB3 'blinky' example to enter bootloader mode\n4.1.1\n\tFixed issue with not being able to view demos for 8051 parts\n4.1.0\n\tThunderboard EFM8UB3 launch\n    EFM8BB3 Type C High Power Charger Reference Design launch\n4.0.9\n\tUpdate CSLIB and examples for F970, F990, and SB1 to include support for per-channel thresholds including profiler support\n4.0.6\n4.0.5\n\tUpdate new project templates to include SiLabs_Startup as a function to disable the watchdog timer\n\tVarious updates and improvements to example projects\n4.0.4\n  Retested and fixed build errors in all projects\n4.0.3\n\tSupport for IEC 60730 launch\n4.0.2\n\tMinor support changes\n4.0.1\n\tFix a few project build and import issues\n4.0.0\n\tInitial release", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\8051\\v4.2.2", "desc": "Silicon Labs 8051 SDK"}, {"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.8051.v4.2.1.feature.metadata.resource.feature.group/icon_sdk_40x40.png", "id": "com.silabs.sdk.8051.v4.2.1.feature", "label": "8051 SDK 4.2.1", "version": "4.2.1", "releaseNote": "4.2.1\n\tUpdated EFM8BB51 and EFM8BB52 header files for Rev C\n\tAdded peripheral driver libraries for EFM8BB51 and EFM8BB52\n\tAdded demos and examples for EFM8BB51 and EFM8BB52\n4.2.0\n\tAdded preliminary support for EFM8BB51 and EFM8BB52\n\tAdded GPIO Expander example for EFM8 devices\n\tAdded header files for EFM8BB3 Rev D\n4.1.7\n\tAdded Configurator support for F970\n\tUpdated examples to use \"si_toolchain.h\"\n\tRemoved /an examples from SDK\n4.1.6\n\tAdded EFM8BB1 LCK examples\n\tUpdated VCPXpress Library\n4.1.5\n\tUpdated USB Library to 1.0.4 - fix potential enumeration issue\n4.1.4\n\tAdded WGX Demo to EFM8UB1 kit examples\n\tAdded WGX Demo to EFM8UB2 kit examples\n4.1.3\n\tAdded BGX Demo to EFM8SB2 kit examples\n\tAdded BGX Demo to EFM8UB1 kit examples\n\tFixed uart_1 driver from always #define-ing IS_DOXYGEN\n4.1.2\n\tFixed F91x examples\n\tFixed pathing issue in the efm8 'shared' module\n\tFixed warnings in smb_0.c\n\tAdded 'S1\" EFM8LB1 bootloaders\n\tUpdated UB3 'blinky' example to enter bootloader mode\n4.1.1\n\tFixed issue with not being able to view demos for 8051 parts\n4.1.0\n\tThunderboard EFM8UB3 launch\n    EFM8BB3 Type C High Power Charger Reference Design launch\n4.0.9\n\tUpdate CSLIB and examples for F970, F990, and SB1 to include support for per-channel thresholds including profiler support\n4.0.6\n4.0.5\n\tUpdate new project templates to include SiLabs_Startup as a function to disable the watchdog timer\n\tVarious updates and improvements to example projects\n4.0.4\n  Retested and fixed build errors in all projects\n4.0.3\n\tSupport for IEC 60730 launch\n4.0.2\n\tMinor support changes\n4.0.1\n\tFix a few project build and import issues\n4.0.0\n\tInitial release", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\8051\\v4.2.1", "desc": "Silicon Labs 8051 SDK"}, {"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.8051.v4.2.0.feature.metadata.resource.feature.group/icon_sdk_40x40.png", "id": "com.silabs.sdk.8051.v4.2.0.feature", "label": "8051 SDK 4.2.0", "version": "4.2.0", "releaseNote": "4.2.0\n\tAdded preliminary support for EFM8BB51 and EFM8BB52\n\tAdded GPIO Expander example for EFM8 devices\n\tAdded header files for EFM8BB3 Rev D\n4.1.7\n\tAdded Configurator support for F970\n\tUpdated examples to use \"si_toolchain.h\"\n\tRemoved /an examples from SDK\n4.1.6\n\tAdded EFM8BB1 LCK examples\n\tUpdated VCPXpress Library\n4.1.5\n\tUpdated USB Library to 1.0.4 - fix potential enumeration issue\n4.1.4\n\tAdded WGX Demo to EFM8UB1 kit examples\n\tAdded WGX Demo to EFM8UB2 kit examples\n4.1.3\n\tAdded BGX Demo to EFM8SB2 kit examples\n\tAdded BGX Demo to EFM8UB1 kit examples\n\tFixed uart_1 driver from always #define-ing IS_DOXYGEN\n4.1.2\n\tFixed F91x examples\n\tFixed pathing issue in the efm8 'shared' module\n\tFixed warnings in smb_0.c\n\tAdded 'S1\" EFM8LB1 bootloaders\n\tUpdated UB3 'blinky' example to enter bootloader mode\n4.1.1\n\tFixed issue with not being able to view demos for 8051 parts\n4.1.0\n\tThunderboard EFM8UB3 launch\n    EFM8BB3 Type C High Power Charger Reference Design launch\n4.0.9\n\tUpdate CSLIB and examples for F970, F990, and SB1 to include support for per-channel thresholds including profiler support\n4.0.6\n4.0.5\n\tUpdate new project templates to include SiLabs_Startup as a function to disable the watchdog timer\n\tVarious updates and improvements to example projects\n4.0.4\n  Retested and fixed build errors in all projects\n4.0.3\n\tSupport for IEC 60730 launch\n4.0.2\n\tMinor support changes\n4.0.1\n\tFix a few project build and import issues\n4.0.0\n\tInitial release", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\8051\\v4.2.0", "desc": "Silicon Labs 8051 SDK"}, {"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.8051.v4.1.7.feature.metadata.resource.feature.group/icon_sdk_40x40.png", "id": "com.silabs.sdk.8051.v4.1.7.feature", "label": "8051 SDK 4.1.7", "version": "4.1.7", "releaseNote": "4.1.7\n\tAdded Configurator support for F970\n\tUpdated examples to use \"si_toolchain.h\"\n\tRemoved /an examples from SDK\n4.1.6\n\tAdded EFM8BB1 LCK examples\n\tUpdated VCPXpress Library\n4.1.5\n\tUpdated USB Library to 1.0.4 - fix potential enumeration issue\n4.1.4\n\tAdded WGX Demo to EFM8UB1 kit examples\n\tAdded WGX Demo to EFM8UB2 kit examples\n4.1.3\n\tAdded BGX Demo to EFM8SB2 kit examples\n\tAdded BGX Demo to EFM8UB1 kit examples\n\tFixed uart_1 driver from always #define-ing IS_DOXYGEN\n4.1.2\n\tFixed F91x examples\n\tFixed pathing issue in the efm8 'shared' module\n\tFixed warnings in smb_0.c\n\tAdded 'S1\" EFM8LB1 bootloaders\n\tUpdated UB3 'blinky' example to enter bootloader mode\n4.1.1\n\tFixed issue with not being able to view demos for 8051 parts\n4.1.0\n\tThunderboard EFM8UB3 launch\n    EFM8BB3 Type C High Power Charger Reference Design launch\n4.0.9\n\tUpdate CSLIB and examples for F970, F990, and SB1 to include support for per-channel thresholds including profiler support\n4.0.6\n4.0.5\n\tUpdate new project templates to include SiLabs_Startup as a function to disable the watchdog timer\n\tVarious updates and improvements to example projects\n4.0.4\n  Retested and fixed build errors in all projects\n4.0.3\n\tSupport for IEC 60730 launch\n4.0.2\n\tMinor support changes\n4.0.1\n\tFix a few project build and import issues\n4.0.0\n\tInitial release", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\8051\\v4.1.7", "desc": "Silicon Labs 8051 SDK"}], "desc": "Silicon Labs 8051 SDK", "latest": {"installed": true, "version": "0.0.0"}}, {"installed": [], "legacy": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/offline/examples/icon_stack_40x40.png", "repositoryType": "git", "permission": true, "id": "gitrepo.https-..github.com.siliconlabs.simplicity_sdk.git", "label": "Simplicity SDK - 32-bit and Wireless MCUs", "showNew": true, "installable": [{"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.3.4", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.4.0", "desc": "Matter extension for Simplicity SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "2.2.1", "desc": "Amazon Sidewalk SDK extension for Simplicity SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.simplicity_sdk.git-verv2024.6.2", "label": "Simplicity SDK - 32-bit and Wireless MCUs v2024.6.2", "releaseNote": "Simplicity SDK 2024.6.2\n\nBluetooth SDK *******\n- Bluetooth\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol\n  - Targeted quality improvements and bug fixes.\n\nBluetooth Location Services *******\n- Targeted quality improvements and bug fixes.\n\nBluetooth Mesh SDK *******\n- Targeted quality improvements and bug fixes.\n\nPlatform 5.0.2\n- Targeted quality improvements and bug fixes.\n\nSilicon Labs OpenThread SDK *******\n- Thread\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol\n  - Targeted quality improvements and bug fixes.\n  \nFlex SDK *******\n- RAIL Apps and Library\n  - Targeted quality improvements and bug fixes.\n- Connect Apps and Stack\n  - Targeted quality improvements and bug fixes.\n\nUSB Device Stack *******\n- Same as previous release\n\nWi-SUN SDK *******\n- Wi-SUN Stack\n  - Targeted quality improvements and bug fixes.\n- Wi-SUN Applications\n  - Targeted quality improvements and bug fixes.\n\nZ-Wave and Z-Wave Long Range 800 SDK 7.22.2 GA is self-certified according to the approved 2024A Specification test suite.\n- User Credential Command Class implementation is adapted based on the state of the Z-Wave Specification at the time of the 7.22.2 GA release, which may change until the 2024-B1 Z-Wave Specification release.\n- 7.22.0 GA is officially certified according to the approved 2023B Specification test suite and can be selected on Z-Wave Alliance Certification Portal for End Device product certification.\n\nZigbee EmberZNet SDK *******\n- Zigbee\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol\n  - Targeted quality improvements and bug fixes.", "version": "2024.6.2", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\simplicity_sdk", "extensionsInfo": [{"recommendedSdkVersion": "2024.12.2", "sdkVersion": "2024.6.2", "id": "com.silabs.sdk.aiml", "label": "AI/ML"}]}, {"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.3.2", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.3.1", "desc": "Matter extension for Simplicity SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "2.2.0", "desc": "Amazon Sidewalk SDK extension for Simplicity SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.simplicity_sdk.git-verv2024.6.1-0", "label": "Simplicity SDK - 32-bit and Wireless MCUs v2024.6.1-0", "releaseNote": "Simplicity SDK 2024.6.1\n\nBluetooth SDK *******\n- Bluetooth\n  - New APIs and Channel Sounding stability improvements.\n- Multiprotocol\n  - Targeted quality improvements and bug fixes.\n\nBluetooth Location Services *******\n- Channel Sounding based distance estimation support.\n\nBluetooth Mesh SDK *******\n- Support for delta-compressed firmware updates.\n\nPlatform 5.0.1\n- Added missing EFR32xG26 devices to pintool.\n- Added support for EFR32BG27C320F768IJ39.\n- Gecko Bootloader\n  - Added Delta DFU for Application upgrade.\n- RAIL 5.0.1\n  - Added support of automatic LNA bypass on the EFR32xG25 parts.\n\nSilicon Labs OpenThread SDK *******\n- Thread\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol\n  - Targeted quality improvements and bug fixes.\n  \nFlex SDK *******\n- RAIL Apps and Library\n  - Targeted quality improvements and bug fixes.\n- Connect Apps and Stack\n  - Targeted quality improvements and bug fixes.\n\nUSB Device Stack *******\n- Underlying platform changes only\n\nWi-SUN SDK 2.1.0\n- Wi-SUN Stack\n  - Targeted quality improvements and bug fixes.\n- Wi-SUN Applications\n  - Targeted quality improvements and bug fixes.\n\nZ-Wave and Z-Wave Long Range 800 SDK 7.22.1 GA is self-certified according to the approved 2023B Specification test suite.\n- 7.22.0 GA is officially certified according to the approved 2023B Specification test suite and can be selected on Z-Wave Alliance Certification Portal for End Device product certification.\n\nZigbee EmberZNet SDK *******\n- Zigbee\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol\n  - Targeted quality improvements and bug fixes.", "version": "2024.6.1.0", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\simplicity_sdk", "extensionsInfo": [{"recommendedSdkVersion": "2024.12.2", "sdkVersion": "2024.6.1.0", "id": "com.silabs.sdk.aiml", "label": "AI/ML"}]}, {"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.3.0", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.3.0", "desc": "Matter extension for Simplicity SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "2.1.0", "desc": "Amazon Sidewalk SDK extension for Simplicity SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.simplicity_sdk.git-verv2024.6.0", "label": "Simplicity SDK - 32-bit and Wireless MCUs v2024.6.0", "releaseNote": "Simplicity SDK 2024.6.0\n\nBluetooth SDK *******\n- Bluetooth\n  - Auto-connect feature enables connecting to any device on the accept list\n  - Common Memory and Clock Manager integration\n  - Electronic Shelf Label (ESL) related improvements\n  - Removed support for Series 0/1\n- Multiprotocol\n  - Alpha support for OpenWRT on host processor of multiprotocol RCP solution\n  - Alpha support for Concurrent Zigbee and Matter over OpenThread, with DMP BLE\n\nBluetooth Location Services *******\n- Underlying code changes only\n- Removed support for Series 0/1\n\nBluetooth Mesh SDK *******\n- Migration to Simplicity SDK Suite\n- Removed support for Series 0/1\n\nPlatform 5.0.0\n- Migration to Simplicity SDK Suite\n- Removed support for Series 0/1\n- Peripherals\n  - New API names introduced for low-level drivers (with compatibility layer for old names)\n- Services\n  - Memory Manager, APIs to manage embedded dynamic memory allocations\n  - Clock Manager, for clock tree initialization\n  - Event System, for inter-process communication.\n  - Interrupt manager, APIs to manage embedded interrupts\n  - APIs for accessing value of tokens added to Token Manager\n- CPC\n  - Small improvement and bug fixes\n- Security\n  - Mbed TLS upgraded (to version 3.5.0)\n- RAIL 5.0.0\n  - Added support for collision detection feature on the EFR32xG25 parts\n  - Added support for additional Coex TX and RX metrics events to RAIL coexistence utility\n  - Updated some RAIL APIs to better prepare for future API changes while retaining backwards compile-time compatibility\n- Other Components\n  - Compilers upgraded (to GCC 12.2.1 and IAR 9.40.1)\n\nSilicon Labs OpenThread SDK *******\n- Thread\n  - Thread 1.3.0 certification compliance with Thread Test Harness v59.0 for SoC and Host-RCP architectures\n  - Thread 1.4 features - Alpha / Experimental\n  - Coex Metrics enhancements\n  - OpenThread Mesh Performance Results (AN1408)\n  - New Part support:\n    - MG26 support - Alpha\n  - Removed support for Series 0/1\n- Multiprotocol\n  - ZigbeeD and OTBR support on OpenWRT - Alpha\n  - DMP BLE + CMP ZB & Matter/OT - Alpha\n  - Removed support for Series 0/1\n  \nFlex SDK *******\n- RAIL Apps and Library\n  - Flex-RAIL SDK support for EFR32xG22E Devices\n  - LCD Display usage in WM-BUS Meter Sample Application on DK2600\n  - Collision Detection support in RAIL Library in concurrent mode on FG25\n  - Added support for additional Coex TX and RX metrics events to RAIL coexistence utility\n  - Updated some RAIL APIs to better prepare for future API changes while retaining backwards compile-time compatibility\n- Connect Apps and Stack\n  - Flex-Connect SDK support for EFR32xG22E Devices\n  - Removed support for Series 0/1\n\nUSB Device Stack *******\n- Underlying platform changes only\n\nWi-SUN SDK 2.0.0\n- Wi-SUN Stack\n  - Dropped the legacy socket API\n  - Stability improvements\n  - Removed support for Series 0/1\n- Wi-SUN Applications\n  - Underlying platform changes only\n\nZ-Wave and Z-Wave Long Range 800 SDK 7.22.0 GA is self-certified according to the approved 2023B Specification test suite.\n- 7.22.0 and future updates support the 800 Series platform\n- 700 Series platform will continue to be supported through the upcoming 7.21.x releases\n- Introducing ZWLR Europe support in experimental quality\n- Z-Wave NCP controller stability improvements\n- Introducing User Credential Command Class beta\n- Introducing CLI support in sample apps\n- Removed support for Series 0/1\n\nZigbee EmberZNet SDK *******\n- Zigbee\n  - Zigbee re-architecture to support stack and framework to be fully RTOS based\n  - Memory management update\n  - New clock manager component integrated in Zigbee applications\n  - Removed support for Series 0/1\n  - Added support for series 3 – Alpha\n  - Added support for new platform xG26, xG22E\n  - Zigbee GP 1.1.2\n  - Support Sleepy NCP use case\n  - New application for Zigbee Unified Test Harness - Alpha\n- Multiprotocol\n  - Alpha support for OpenWRT on host processor of multiprotocol RCP solution\n  - Alpha support for Concurrent Zigbee and Matter over OpenThread, with DMP BLE", "version": "2024.6.0", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\simplicity_sdk", "extensionsInfo": [{"recommendedSdkVersion": "2024.12.2", "sdkVersion": "2024.6.0", "id": "com.silabs.sdk.aiml", "label": "AI/ML"}]}, {"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.4.2", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.5.2", "desc": "Matter extension for Simplicity SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "2.4.0", "desc": "Amazon Sidewalk SDK extension for Simplicity SDK Suite"}, {"id": "com.silabs.sdk.aiml", "label": "AI/ML", "version": "2.0.1", "desc": "AI/ML extension for Simplicity SDK"}], "id": "gitrepo.https-..github.com.siliconlabs.simplicity_sdk.git-verv2024.12.2", "label": "Simplicity SDK - 32-bit and Wireless MCUs v2024.12.2", "releaseNote": "Simplicity SDK 2024.12.2\n\nBluetooth SDK *******\n- Bluetooth\n  - BG22L and BG24L enablement.\n  - Targeted quality improvements and bug fixes. \n- Multiprotocol\n  - Targeted quality improvements and bug fixes.\n\nBluetooth Location Services *******\n- Underlying platform changes only.\n\nBluetooth Mesh SDK *******\n- Bug fixes and minor enhancements.\n\nPlatform 5.1.2\n CMSIS Device\n  - Added support for EFR32BG22L and EFR32BG24L families.\n- CPC\n  - Targeted quality improvements and bug fixes.\n- RAIL 5.1.2\n  - Targeted quality improvements and bug fixes.\n\nSilicon Labs OpenThread SDK *******\n- Thread\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol\n  - Targeted quality improvements and bug fixes.\n\nRAIL SDK ********\n- RAIL Apps and Library\n  -  Underlying platform changes only.\n\nConnect SDK *******\n- Connect Apps and Stack\n  - Underlying platform changes only.\n\nUSB Device Stack *******\n- Same as previous release.\n\nWi-SUN SDK *******\n- Wi-SUN Stack\n  -  Targeted quality improvements and bug fixes.\n- Wi-SUN Applications\n  - Targeted quality improvements and bug fixes.\n\nZ-Wave and Z-Wave Long Range 800 SDK 7.23.2 GA is self-certified according to the 2024B-1 Z-Wave Alliance Cert Program.\n  - Targeted quality improvements and bug fixes.\n\nZigbee EmberZNet SDK *******\n- Zigbee\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol\n  - Targeted quality improvements and bug fixes.", "version": "2024.12.2", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\simplicity_sdk"}, {"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.4.1", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.5.1", "desc": "Matter extension for Simplicity SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "2.4.0", "desc": "Amazon Sidewalk SDK extension for Simplicity SDK Suite"}, {"id": "com.silabs.sdk.aiml", "label": "AI/ML", "version": "2.0.0", "desc": "AI/ML extension for Simplicity SDK"}], "id": "gitrepo.https-..github.com.siliconlabs.simplicity_sdk.git-verv2024.12.1-0", "label": "Simplicity SDK - 32-bit and Wireless MCUs v2024.12.1-0", "releaseNote": "Simplicity SDK 2024.12.1\n\nBluetooth SDK *******\n- Bluetooth\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol\n  - Targeted quality improvements and bug fixes.\n\nBluetooth Location Services *******\n- Underlying platform changes only.\n\nBluetooth Mesh SDK *******\n- Bug fixes and minor enhancements.\n\nPlatform 5.1.1\n- CMSIS Device\n  - Added support for xGM260 modules.\n- CPC\n  - Fixed a potential security vulnerability.\n- Security\n  - Targeted quality improvements and bug fixes. \n- Boards and External Devices\n  - Added support for BRD2505A, BRD2713A, BRD4120A, BRD4121A, BRD4402C, BRD4403C\n- RAIL 5.1.1\n  - Added support for the xGM260P modules\n\nSilicon Labs OpenThread SDK *******\n- Thread\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol\n  - Targeted quality improvements and bug fixes.\n\nRAIL SDK ********\n- RAIL Apps and Library\n  - Targeted quality improvements and bug fixes.\n\nConnect SDK *******\n- Connect Apps and Stack\n  - Underlying platform changes only.\n\nUSB Device Stack *******\n- Underlying platform changes only.\n\nWi-SUN SDK *******\n- Wi-<PERSON><PERSON> Stack\n  - Introducted a new API to select a preferred PAN.\n  - Targeted quality improvements and bug fixes.  \n- Wi-SUN Applications\n  - Targeted quality improvements and bug fixes.\n\nZ-Wave and Z-Wave Long Range 800 SDK 7.23.1 GA is self-certified according to the 2024B-1 Z-Wave Alliance Cert Program.\n  - Z-Wave Long Range European frequency has shifted to align the implementation to the specification change.\n  - A firmware upgrade over-the-air issue is fixed. An end device is required to be included again after the OTA process.\n\nZigbee EmberZNet SDK *******\n- Zigbee\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol\n  - Targeted quality improvements and bug fixes.", "version": "2024.12.1.0", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\simplicity_sdk"}, {"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.4.0", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.5.0", "desc": "Matter extension for Simplicity SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "2.3.0", "desc": "Amazon Sidewalk SDK extension for Simplicity SDK Suite"}, {"id": "com.silabs.sdk.aiml", "label": "AI/ML", "version": "2.0.0", "desc": "AI/ML extension for Simplicity SDK"}], "id": "gitrepo.https-..github.com.siliconlabs.simplicity_sdk.git-verv2024.12.0", "label": "Simplicity SDK - 32-bit and Wireless MCUs v2024.12.0", "releaseNote": "Simplicity SDK 2024.12.0\n\nBluetooth SDK *******\n- Bluetooth\n  - GA release of Periodic Advertisement BGAPI event\n  - BT LE Connection Subrating\n  - Accept List Based Auto-connect\n  - BT Atlanta (v6.0) LL and Host Qualification\n  - Channel Sounding sparse channel map support\n  - Channel Sounding antenna switching support\n  - CBAP - CPMS integration\n  - Note: Work will continue in 25Q2 to support multi-level certificates and further enhancements\n- Multiprotocol\n  - ZigbeeD and OTBR support on OpenWRT – GA\n  - DMP BLE + CMP ZB & Matter/OT with Concurrent Listening on MG26 for SoC – GA\n  - 802.15.4 Unified radio scheduler priority component\n  - Debian packaging support for MP host applications - Alpha\n\nBluetooth Location Services *******\n- Channel Sounding sparse channel map support\n- Channel Sounding antenna switching support\n\nBluetooth Mesh SDK *******\n- Support added for Micrium and FreeRTOS.\n- Bug fixes and minor enhancements.\n\nPlatform 5.1.0\n- CMSIS Device\n  - Added support for new EFR32xG27 OPN, EFR32xG26 OPN's, Module OPN's (MGM260PB22VNA2,MGM260PB32VNA2,MGM260PB32VNN2)\n- CPC\n  - Experimental support of NETLINK-SDIO interface on the SiWx917m platform\n- Security\n  - <PERSON><PERSON> TLS upgraded to latest version 3.6.2\n- Boards and External Devices\n  - Added support for board OPN's BRD2709A, BRD2711A, BRD4350A, BRD4351A, BRD4412A, BRD4413A.\n- RAIL 5.1.0\n  - Added support for Bluetooth Low Energy (BLE) Channel Sounding (CS) on the EFR32xG24. \n    This feature enables accurate distance measurement between two devices when used with the Silicon Labs BLE stack and distance measurement libraries.\n  - Added support for Concurrent listening feature on the EFR32xG26 part.\n  - Added support for 1 Mbps and 2 Mbps custom 802.15.4 SUN PHYs on the EFR32xG26 part.\n\nSilicon Labs OpenThread SDK *******\n- Thread\n  - Thread 1.4 certification compliance for Thread devices (SoC)\n  - Thread 1.3 certification compliance, and 1.4 Alpha support for OTBR \n  - OTBR NCP mode support - Alpha\n  - OTBR RCP mode with MCU host (x917) - Alpha\n  - KNX IOT API support (delivered in Silabs GitHub)\n  - xG26 Module support\n  - TrustZone secure key storage support for Thread / MP SoC projects\n- Multiprotocol\n  - ZigbeeD and OTBR support on OpenWRT – GA\n  - DMP BLE + CMP ZB & Matter/OT with Concurrent Listening on MG26 for SoC – GA\n  - 802.15.4 Unified radio scheduler priority component\n  - Debian packaging support for MP host applications - Alpha\n\nRAIL SDK ********\n- RAIL Apps and Library\n  - RAIL Tutorial moved to docs.silabs.com: https://docs.silabs.com/rail/latest/rail-start/rail-training\n  - RAIL SDK supported on BRD4276A radio board with EFR32FG25 and SKY66122-11 frontend module for high TX power applications\n  - Improved RangeTest Sample Application to fully support multi-PHY configurations and added new feature to control measurements on RX side – in alpha quality\n  - Sigfox TX PHYs and RX PHYs supported on EFR32FG23 and EFR32FG28 parts for EU and NA region\n\nConnect SDK *******\n- Connect Apps and Stack\n  - PSA Crypto hardware acceleration for payload encryption enabled in Connect Stack on Series-2 parts\n  - Connect stack and Connect SDK enabled on BRD4276A radio board with EFR32FG25 and SKY66122-11 frontend module for high TX power applications\n\nUSB Device Stack *******\n- Underlying platform changes only \n\nWi-SUN SDK *******\n- Wi-SUN Stack\n  - Direct Connect\n  - PAN Defect\n- Wi-SUN Applications\n  - Border Router CLI source code\n  - Border Router CLI with Wi-Fi Backhaul\n\nZ-Wave and Z-Wave Long Range 800 SDK 7.23.0 GA is self-certified according to the 2024B-1 Z-Wave Alliance Cert Program.\n  - Z-Wave Long Range European region is officially supported.\n  - The Z-Wave stack includes an S2V2 Alpha implementation.\n  - Introduced certifiable User Credential Command Class implementation.\n  - Simplified GPIO handling.\n\nZigbee EmberZNet SDK 8.1\n- Zigbee\n  - 250+ entries in APS link key table\n  - ZigbeeD support on Android 12 (v21.0.6113669) and Tizen (v0.1-13.1)\n  - xG26 Module support\n- Multiprotocol\n  - ZigbeeD and OTBR support on OpenWRT – GA\n  - DMP BLE + CMP ZB & Matter/OT with Concurrent Listening on MG26 for SoC – GA\n  - 802.15.4 Unified radio scheduler priority component\n  - Debian packaging support for MP host applications - Alpha", "version": "2024.12.0", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\simplicity_sdk"}], "desc": "Silicon Labs Simplicity SDK", "latest": {"installed": false, "id": "gitrepo.https-..github.com.siliconlabs.simplicity_sdk.git-verv2024.12.2", "version": "2024.12.2"}}, {"installed": [], "legacy": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/offline/examples/icon_stack_40x40.png", "repositoryType": "git", "permission": true, "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git", "label": "Gecko SDK - 32-bit and Wireless MCUs", "installable": [{"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.2.0", "desc": "WiSeConnect SDK 3"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.4.6", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.4.6", "releaseNote": "GSDK *******\n\nWi-SUN SDK ********\n- The current version of the Wi-SUN SDK has undergone limited testing and has not been fully validated with the latest Gecko SDK. We strongly recommend using the SiSDK instead. Please note that the Wi-SUN SDK will be removed in the next Gecko SDK releases.\n\n32-Bit MCU SDK *******\n- Underlying platform changes only.  \n\nBluetooth SDK *******\n- Bluetooth: \n  - Targeted quality improvements and bug fixes.\n- Multiprotocol:\n  - Targeted quality improvements and bug fixes.\n\nBluetooth Location Services *******\n- Underlying platform changes only.\n\nBluetooth Mesh SDK *******\n- Targeted quality improvements and bug fixes.\n\nGecko Platform *******\n- Targeted quality improvements and bug fixes.\n\nSilicon Labs OpenThread SDK *******\n- OpenThread:\n  - Targeted quality improvements and bug fixes. \n- Multiprotocol:\n  - Targeted quality improvements and bug fixes. \n  \nFlex SDK *******\n- RAIL targeted quality improvements and bug fixes.\n\nUSB Device Stack *******\n- Same as previous release.\n\nZ-Wave and Z-Wave Long Range 700/800 SDK 7.21.6 GA is certified according to the approved 2023B Specification test suite.\n- Targeted quality improvements and bug fixes\n- Fixed an issue introduced in 7.21.4 impacting the effectiveness of a wake-up beam targeted at a FLiRS device.\n- For Series 800 products, it is highly recommended to move to the Simplicity SDK release stream.\n\nZigbee EmberZNet SDK *******\n - Zigbee:\n  - Limited support of GCC LTO.\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol:\n  - Targeted quality improvements and bug fixes.", "version": "4.4.6", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.5", "sdkVersion": "4.4.6", "id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.4.6", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.2.0", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.2.2", "desc": "Matter extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.4.5", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.4.5", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only.  \n\nBluetooth SDK *******\n- Bluetooth: \n  - New Scanner Option\n  - Large Accept List Size\n  - HCI Event filtering\n- Multiprotocol:\n  - Targeted quality improvements and bug fixes.\n\nBluetooth Location Services *******\n- Underlying platform changes only.\n\nBluetooth Mesh SDK *******\n- Targeted quality improvements and bug fixes.\n\nGecko Platform *******\n- Targeted quality improvements and bug fixes.\n\nSilicon Labs OpenThread SDK *******\n- OpenThread:\n  - Includes conformance and support for Thread 1.4 Credential sharing, Network diagnostics features, and improvements made for border router Public Internet Connectivity. Support for 1.4 commercial TCAT (Thread over Authenticated TLS) feature is present but not tested.\n- Multiprotocol:\n  - Targeted quality improvements and bug fixes. \n  \nFlex SDK *******\n- Targeted quality improvements and bug fixes.\n\nUSB Device Stack *******\n- Same as previous release.\n\nWi-SUN SDK ********\n- Targeted quality improvements and bug fixes.\n\nZ-Wave and Z-Wave Long Range 700/800 SDK 7.21.5 GA is certified according to the approved 2023B Specification test suite.\n- Fixed an issue introduced in 7.21.4 impacting the effectiveness of a wake-up beam targeted at a FLiRS device.\n\nZigbee EmberZNet SDK *******\n - Zigbee:\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol:\n  - Targeted quality improvements and bug fixes.", "version": "4.4.5", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.4.5", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.2.0", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.2.2", "desc": "Matter extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.4.4", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.4.4", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only.  \n\nBluetooth SDK *******\n- Bluetooth: \n  - Targeted quality improvements and bug fixes.\n- Multiprotocol:\n  - Targeted quality improvements and bug fixes.\n\nBluetooth Location Services *******\n- Underlying platform changes only.\n\nBluetooth Mesh SDK *******\n- Targeted quality improvements and bug fixes.\n\nGecko Platform *******\n- Targeted quality improvements and bug fixes.\n\nSilicon Labs OpenThread SDK *******\n- OpenThread:\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol:\n  - Targeted quality improvements and bug fixes. \n  \nFlex SDK *******\n- Targeted quality improvements and bug fixes.\n\nUSB Device Stack *******\n- Underlying platform changes only.\n\nWi-SUN SDK ********\n- Targeted quality improvements and bug fixes.\n\nZ-Wave and Z-Wave Long Range 700/800 SDK 7.21.4 GA is certified according to the approved 2023B Specification test suite.\n- Various bug fixes, refer to release notes.\n\nZigbee EmberZNet SDK *******\n - Zigbee:\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol:\n  - Targeted quality improvements and bug fixes.", "version": "4.4.4", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.4.4", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.2.0", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.2.2", "desc": "Matter extension for Gecko SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "2.0.1", "desc": "Amazon Sidewalk SDK extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.4.3", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.4.3", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only.  \n\nBluetooth SDK *******\n- Bluetooth: \n  - Underlying platform changes only.\n- Multiprotocol:\n  - Underlying platform changes only.\n\nBluetooth Location Services *******\n- Underlying platform changes only.\n\nBluetooth Mesh SDK *******\n- Underlying platform changes only.\n\nGecko Platform *******\n- Fixed the Pin Assignment issue which prevents the configuration header files from being created/updated when using PinTool.\n\nSilicon Labs OpenThread SDK *******\n- OpenThread:\n  - Underlying platform changes only.\n- Multiprotocol:\n  - Underlying platform changes only. \n  \nFlex SDK *******\n- Underlying platform changes only.\n\nUSB Device Stack *******\n- Underlying platform changes only.\n\nWi-SUN SDK ********\n- Underlying platform changes only.\n\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** GA is certified according to the approved 2023B Specification test suite.\n- Various bug fixes, refer to release notes.\n\nZigbee EmberZNet SDK *******\n - Zigbee:\n  - Underlying platform changes only.\n- Multiprotocol:\n  - Underlying platform changes only.", "version": "4.4.3", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7"}, {"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.1.4", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.2.1", "desc": "Matter extension for Gecko SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "2.0.1", "desc": "Amazon Sidewalk SDK extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.4.2", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.4.2", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only.  \n\nBluetooth SDK 7.1.0\n- Bluetooth: \n  - New Bluetooth stack event for signaling memory exhaustion\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol:\n  - Targeted quality improvements and bug fixes.\n\nBluetooth Location Services 7.1.0\n- Underlying code changes only.\n\nBluetooth Mesh SDK 6.1.0\n- Targeted quality improvements and bugfixes.\n\nGecko Platform *******\n- Targeted quality improvements and bug fixes\n\nSilicon Labs OpenThread SDK *******\n- OpenThread:\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol:\n  - Targeted quality improvements and bug fixes. \n  \nFlex SDK *******\n- Targeted quality improvements and bug fixes.\n\nUSB Device Stack *******\n- Targeted quality improvements and bug fixes.\n\nWi-SUN SDK 1.10.0\n- Targeted quality improvements and bug fixes.\n\nZ-Wave and Z-Wave Long Range 700/800 SDK 7.21.2 GA is certified according to the approved 2023B Specification test suite.\n- Various bug fixes, refer to release notes.\n\nZigbee EmberZNet SDK *******\n - Zigbee:\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol:\n  - Targeted quality improvements and bug fixes.", "version": "4.4.2", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7"}, {"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.1.4", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.2.1", "desc": "Matter extension for Gecko SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "2.0.1", "desc": "Amazon Sidewalk SDK extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.4.1", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.4.1", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only. \n\nBluetooth SDK 7.0.1\n- Targeted quality improvements and bug fixes\n\nBluetooth Location Services 7.0.1\n- Underlying code changes only\n\nBluetooth Mesh SDK 6.0.1\n- Targeted quality improvements and bugfixes.\n\nGecko Platform *******\n- Targeted quality improvements and bug fixes\n\nSilicon Labs OpenThread SDK 2.4.1.0\n- Targeted quality improvements and bug fixes\n  \nFlex SDK 3.7.1.0\n- Targeted quality improvements and bug fixes\n\nUSB Device Stack *******\n- Unchanged from previous release\n\nWi-SUN SDK 1.9.0\n- Targeted quality improvements and bug fixes\n\nZ-Wave and Z-Wave Long Range 700/800 SDK 7.21.1 GA is certified according to the approved 2023B Specification test suite.\n- Various bug fixes, refer to release notes.\n\nZigbee EmberZNet SDK 7.4.1.0\n- Targeted quality improvements and bug fixes.", "version": "4.4.1", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7"}, {"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.1.2", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.2.0", "desc": "Matter extension for Gecko SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "2.0.0", "desc": "Amazon Sidewalk SDK extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.4.0", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.4.0", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Added support for new OPNs\n- Upgrade compilers to GCC 12.2.1 and IAR 9.40.1\n\nBluetooth SDK *******\n- Bluetooth Connection Analyzer can retrieve detailed connection parameters of a local connection and allows non-connected devices to track the RSSI of the connected devices\n- Multiprotocol\n  - Concurrent Listening support (RCP) – MG21 and MG24 \n  - Concurrent Multiprotocol (CMP) Zigbee NCP + OpenThread RCP – production\n  - Dynamic Multiprotocol Bluetooth + Concurrent Multiprotocol (CMP) Zigbee and OpenThread support on SoC\n\nBluetooth Location Services *******\n- Underlying code changes only\n\nBluetooth Mesh SDK *******\n- Qualified implementation of Bluetooth Mesh 1.1\n- Added Network Lighting Control (NLC) profiles\n\nGecko Platform *******\n- Peripherals\n  - New API names introduced for low-level drivers (with compatibility layer for old names)\n- Services\n  - APIs for accessing value of tokens added to Token Manager\n- CPC\n  - NVM3 module, enabling Host access to Secondary device's non-volatile memory, released at production quality\n  - CPC Primary, for use with MCU Hosts, released at Experimental quality\n  - Numerous optimizations and performance improvements made\n- Security\n  - Mbed TLS upgraded (to version 3.5.0)\n- RAIL\n  - Several new features added for EFR32xG25 devices, including a new component for selecting modulations supported by the software modem\n  - Support added for several new PHYs, including Sidewalk PHYs on EFR32xG23 and EFR32xG28\n- Other components\n  - Compilers upgraded (to GCC 12.2.1 and IAR 9.40.1)\n\nSilicon Labs OpenThread SDK *******\n- Thread\n  - Thread 1.3.0 certification compliance with Thread Test Harness v59.0 for SoC and Host-RCP architectures \n  - Thread 1.3.1 feature support - Experimental\n  - Crash Handler support\n  - TrustZone Evaluation support\n  - MR21 support for OpenThread RCP – Production\n- Multiprotocol\n  - Concurrent Listening support (RCP) – MG21 and MG24 \n  - Concurrent Multiprotocol (CMP) Zigbee NCP + OpenThread RCP – production quality\n  - Dynamic Multiprotocol Bluetooth + Concurrent Multiprotocol (CMP) Zigbee and OpenThread support on SoC\n\nFlex SDK *******\n- Connect\n  - Support of SUN-FSK and SUN-OFDM\n  - Hardware Abstraction Layer update\n  - Added Hardware Support: MG24 QFN40, EFRBG22-E, EFR32xG28 Explorer Kit\n- RAIL SDK\n  - Connect OFDM support for some Applications\n  - EFR32xG28 Proprietary 2.4 GHz 15.4 Standard PHY Support\n  - Added Hardware Support: MG24 QFN40, EFRBG22-E, EFR32xG28 Explorer Kit\n\nUSB Device Stack *******\n- Targeted quality improvements and bug fixes\n\nWi-SUN SDK *******\n- Wi-SUN Stack\n  - Added support for LFN multicast reception\n  - Added support for non-standard PHY configurations\n  - Added support for blocking sockets\n- Wi-SUN Application Improvements\n  - Configurable LFN Support for all the applications\n  - Wi-SUN – SoC Network Measurement\n  - Wi-SUN – SoC (CoAP) Meter and Wi-SUN – SoC (CoAP) Collector\n\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** GA\n- BRD2705 Explorer kit includes new applications\n- Various quality improvements\n- Z-Wave PC-based Zniffer v6.48 released with CSV export feature\n\nZigbee EmberZNet SDK *******\n- Zigbee\n  - Zigbee R23 compliance\n  - Zigbee Smart Energy 1.4a compliance - production\n  - Zigbee GP 1.1.2 compliance - Alpha\n  - MG27 support - production\n  - Improved support for Secure Vault parts\n  - Sleepy support on NCP SPI (non-CPC) applications – Alpha\n- Multiprotocol\n  - Concurrent Listening support (RCP) – MG21 and MG24 \n  - Concurrent Multiprotocol (CMP) Zigbee NCP + OpenThread RCP – production\n  - Dynamic Multiprotocol Bluetooth + Concurrent Multiprotocol (CMP) Zigbee and OpenThread support on SoC", "version": "4.4.0", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7"}, {"extensions": [{"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.1.1", "desc": "Matter extension for Gecko SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "1.2.2", "desc": "Amazon Sidewalk SDK extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.3.3", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.3.3", "releaseNote": "GSDK *******\n\nThis release of GSDK supports the Arm Embedded Toolchain version 10.3-2021.10 and IAR Embedded Workbench version 9.20.4. One of these toolchains must be installed.\n\n32-Bit MCU SDK *******\n- Underlying platform changes only\n\nBluetooth SDK *******\n- Bluetooth\n - Targeted quality improvements and bug fixes\n- Multi-Protocol\n - Zigbee/OpenThread Concurrent Multiprotocol SoC sample app\n - CPC GPIO expander module\n - Zigbeed enhancements\n\nBluetooth Location Services *******\n- Underlying code changes only\n\nBluetooth Mesh SDK *******\n- Targeted quality improvements and bug fixes\n\nGecko Platform *******\n- Targeted quality improvements and bug fixes\n\nSilicon Labs OpenThread SDK *******\n- Thread\n - Targeted quality improvements and bug fixes\n- Multi-Protocol\n - Zigbee/OpenThread Concurrent Multiprotocol SoC sample app\n - CPC GPIO expander module\n - Zigbeed enhancements\n  \nFlex SDK *******\n- Targeted quality improvements and bug fixes\n\nUSB Device Stack *******\n- Unchanged from previous release\n\nWi-SUN SDK *******\n- Targeted quality improvements and bug fixes\n\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** GA is certified according to the approved 2023A Specification test suite.\n- Targeted quality improvements and bug fixes.\n\nZigbee EmberZNet SDK *******\n- Zigbee\n - Targeted quality improvements and bug fixes\n- Multi-Protocol\n - Zigbee/OpenThread Concurrent Multiprotocol SoC sample app\n - CPC GPIO expander module\n - Zigbeed enhancements", "version": "4.3.3", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.3.3", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}]}, {"extensions": [{"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.1.1", "desc": "Matter extension for Gecko SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "1.2.2", "desc": "Amazon Sidewalk SDK extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.3.2", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.3.2", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only\n\nBluetooth SDK *******\n- Connection Statistics feature\n- ESL system supports opening and maintaining multiple parallel connections between Access Point and Tags\n- Targeted quality improvements and bug fixes\n\nBluetooth Location Services *******\n- Underlying code changes only\n\nBluetooth Mesh SDK *******\n- Targeted quality improvements and bug fixes\n\nGecko Platform *******\n- Targeted quality improvements and bug fixes\n\nSilicon Labs OpenThread SDK *******\n- Targeted quality improvements and bug fixes\n  \nFlex SDK *******\n- Targeted quality improvements and bug fixes\n\nUSB Device Stack *******\n- Unchanged from previous release\n\nWi-SUN SDK *******\n- Targeted quality improvements and bug fixes \n\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** GA is certified according to the approved 2023A Specification test suite.\n- Various bug fixes, refer to release notes.\n\nZigbee EmberZNet SDK *******\n- Targeted quality improvements and bug fixes", "version": "4.3.2", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.3.2", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}]}, {"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.1.0", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.1.0", "desc": "Matter extension for Gecko SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "1.2.1", "desc": "Amazon Sidewalk SDK extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.3.1", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.3.1", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only\n\nBluetooth SDK *******\n- Bluetooth\n  - User-controlled Tx Power\n  - Host example for testing NCP communication\n- Multiprotocol\n  - Targeted quality improvements and bug fixes\n\nBluetooth Location Services *******\n- Underlying code changes only\n\nBluetooth Mesh SDK *******\n- Added support for BRD4194A and BRD4187C radio boards for BT Mesh IOP Test Demos \n\nGecko Platform *******\n-Targeted quality improvements and bug fixes\n\nSilicon Labs OpenThread SDK *******\n- Targeted quality improvements and bug fixes\n  \nFlex SDK *******\n- RAIL Library: Targeted quality improvements and bug fixes\n\nUSB Device Stack *******\n- Unchanged from previous release\n\nWi-SUN SDK *******\n- Wi-SUN Stack\n  - LFN devices can now enter in EM2\n\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** GA is certified according to the approved 2022 Specification test suite.\n- Various bug fixes, refer to release notes\n\nZigbee EmberZNet SDK *******\n- Targeted quality improvements and bug fixes", "version": "4.3.1", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7"}, {"extensions": [{"id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect", "version": "3.0.9", "desc": "WiSeConnect SDK 3"}, {"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "2.0.0", "desc": "Matter extension for Gecko SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "1.1.0", "desc": "Amazon Sidewalk SDK extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.3.0", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.3.0", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Added support for new OPNs\nBluetooth SDK *******\n- Bluetooth\n  - Bluetooth 5.4 support including Periodic Advertisements with Responses (PAwR) and Encrypted Advertisement Data (EAD) features\n  - Electronic Shelf Label (ESL) Service / Profile support - both Tag and Access Point (AP) roles\n  - Object Transfer Service / Profile support\n  - Periodic Advertisement Sync Transfer (PAST) support\n  - LE Privacy 1.2\n- Multiprotocol\n  - Zigbee/OpenThread Concurrent Multiprotocol SoC sample app\n  - CPC GPIO expander module\n  - Zigbeed enhancements\nBluetooth Location Services *******\n- Some library variants now compile with Position Independent Code flags\nBluetooth Mesh SDK *******\n- Support for Mesh Protocol 1.1\n- Support for Mesh Model 1.1\n- Support for Mesh Binary Large Object Transfer\n- Support for Mesh Device Firmware Update \nGecko Platform *******\n-Support for EFR32xG27 devices\n-Power Manager update for optimized return from EM2\n-CPC support for multiple SPI clients\n-CPC remote peripheral (GPIO) access\n-License change (to open-source zlib) for various platform files\nSilicon Labs OpenThread SDK *******\n- OpenThread\n  - Thread 1.3.1 (experimental)\n\t- IPv4/v6 public internet connectivity: NAT64 improvements, optimization of published routes and prefixes in network data\n\t- DNS enhancements for OTBR\n\t- Thread over Infrastructure (TREL)\n  - Network Diagnostics (experimental)\n\t- Child supervision by parent\n\t- Additional link quality information in child table\n\t- Uptime for routers\n- Multiprotocol\n  - Zigbee/OpenThread Concurrent Multiprotocol SoC sample app\n  - CPC GPIO expander module\n  - Zigbeed enhancements\n  \nFlex SDK *******\n- RAIL Apps and Library:\n  - FG23 Direct Mode settings in Radio Configurator\n  - WM-BUS T+C PHY support\n  - FGM230S WM-BUS PHYs and Application support\n  - RAIL NCP Sample Applications\n  - PSM support for DSSS-OQPSK Long Range PHYs\n- Connect Apps and Stack:\n  - Connect NCP support\n  - FGM230S Connect support\nUSB Device Stack *******\n- Internal modifications to reduce USB stack code size\nWi-SUN SDK *******\n- Wi-SUN Stack\n  - EFR32FG28 support \n  - Connection time improvements\n  - LFN support improvements\n- Wi-SUN Applications\n  - Firmware over-the-air update\n  - Wi-SUN configurator update\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** Pre-Certified GA\n- FG28/ZG28-based radio boards BRD4401A/B supported\n- Z-Wave FLiRS inclusion performance improved in large networks\n- Z-Wave Long Range wakeup beam performance improved\n- Simplified application development by moving logic from the Apps to ZAF\n- 800 DevKit (BRD2603) includes new applications and Multilevelsensor extended with new features\n- Improved documentation to better support development from idea to certification\n- Z-Wave Simulator available for Z-Wave Alliance members\n- Z-Wave PC-based Zniffer v4.67 released\nZigbee EmberZNet SDK *******\n- Zigbee\n  - Zigbee R23 compliance, with these Security enhancements among others:\n\t- Dynamic link key negotiation\n\t- Device interview to query devices before they are allowed to join\n\t- Trust Center Swap Out to replace an existing Trust Center with a new one\n\t- Frame Counter Synchronization \n  - Zigbee Direct Device (ZDD) support for:\n\t- Onboarding/commissioning\n\t- Communication to all Zigbee devices without a hub (Alpha), using Bluetooth LE\n  - Zigbee Smart Energy 1.4a compliance (Alpha)\n  - Enhancements to Zigbee GP APIs\n  - New Zigbee Security upgrade component for moving encryption keys from cleartext NVM3 tokens into secure storage\nMultiprotocol\n  - Zigbee/OpenThread Concurrent Multiprotocol SoC sample app\n  - CPC GPIO expander module\n  - Zigbeed enhancements", "version": "4.3.0", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7"}, {"extensions": [{"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "1.0.5", "desc": "Matter extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.2.7", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.2.7", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only.\n\nBluetooth SDK *******\n- Bluetooth\n  - Targeted quality improvement.\n- Multiprotocol\n  - No change from previous release. \n\nBluetooth Location Services *******\n- Underlying code changes only.\n\nBluetooth Mesh SDK *******\n-No change from previous release.\n\nGecko Platform *******\n- Underlying code changes only.\n\nSilicon Labs OpenThread SDK *******\n- Thread\n  - Underlying platform changes only.\n- Multiprotocol\n  - No change from previous release. \n\nFlex SDK *******\n- Underlying platform changes only.\n\nUSB Device Stack *******\n- No change from previous release.\n\nWi-SUN SDK *******\n- No change from previous release.\n\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** GA is certified according to the approved 2022 Specification test suite.\n- Targeted quality improvements and bug fixes.\n\nZigbee EmberZNet SDK *******\n- Zigbee\n  - Targeted quality improvements and bug fixes.\n- Multiprotocol\n  - No change from previous release.", "version": "4.2.7", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.2.7", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.2.7", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"extensions": [{"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "1.0.5", "desc": "Matter extension for Gecko SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "1.0.3", "desc": "Amazon Sidewalk SDK extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.2.6", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.2.6", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only\n\nBluetooth SDK *******\n- Bluetooth\n  - Underlying platform changes only\n- Multiprotocol\n  - Targeted quality improvements and bug fixes\n\nBluetooth Location Services *******\n- Underlying code changes only\n\nBluetooth Mesh SDK *******\n-Targeted quality improvements and bug fixes\n\nGecko Platform *******\n- Targeted quality improvements and bug fixes\n\nSilicon Labs OpenThread SDK *******\n- Thread\n  - Targeted quality improvements and bug fixes\n- Multiprotocol\n  - Targeted quality improvements and bug fixes\n\nFlex SDK *******\n- Underlying platform changes only\n\nUSB Device Stack *******\n- Same as previous release\n\nWi-SUN SDK *******\n- Same as previous release\n\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** GA is certified according to the approved 2022 Specification test suite.\n- Same as previous release\n\nZigbee EmberZNet SDK *******\n- Zigbee\n  - Targeted quality improvements and bug fixes\n- Multiprotocol\n  - Targeted quality improvements and bug fixes", "version": "4.2.6", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.2.6", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}]}, {"extensions": [{"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "1.0.5", "desc": "Matter extension for Gecko SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "1.0.3", "desc": "Amazon Sidewalk SDK extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.2.5", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.2.5", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only\n\nBluetooth SDK *******\n- Targeted quality improvements and bug fixes\n\nBluetooth Location Services *******\n- Underlying code changes and documentation fix\n\nBluetooth Mesh SDK *******\n-Targeted quality improvements and bug fixes\n\nGecko Platform *******\n- CPC: Fixed one issue\n\nSilicon Labs OpenThread SDK 2.2.5.0\n- Targeted quality improvements and bug fixes\n\nFlex SDK 3.5.5.0\n- Underlying platform changes only\n\nUSB Device Stack *******\n- Targeted quality improvements and bug fixes\n\nWi-SUN SDK *******\n- Targeted quality improvements and bug fixes\n\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** GA is certified according to the approved 2022 Specification test suite.\n- Targeted quality improvements and bug fixes\n\nZigbee EmberZNet SDK 7.2.5.0\n- Zigbee: Targeted quality improvements and bug fixes\n", "version": "4.2.5", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.2.5", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}]}, {"extensions": [{"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "1.0.5", "desc": "Matter extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.2.4", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.2.4", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only\n\nBluetooth SDK *******\n- Bluetooth: Support for EFR32xG21, Revision C and later\n- Multiprotocol: Targeted quality improvements and bug fixes\n\nBluetooth Location Services *******\n- Underlying code changes only\n\nBluetooth Mesh SDK *******\n- Targeted quality improvements and bugfixes\n\nGecko Platform *******\n- Boards and External Devices: Additional boards supported\n- Gecko Bootloader: Targeted quality improvements and bug fixes\n\nSilicon Labs OpenThread SDK *******\n- OpenThread: Support for EFR32xG21, Revision C and later\n- Multiprotocol: Targeted quality improvements and bug fixes\n\nFlex SDK *******\n- RAIL library: Targeted quality improvements and bug fixes\n\nUSB Device Stack *******\n- Targeted quality improvements and bug fixes\n\nWi-SUN SDK *******\n- Targeted quality improvements and bug fixes\n\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** GA is certified according to the approved 2022 Specification test suite.\n- Underlying code changes only\n\nZigbee EmberZNet SDK *******\n- Zigbee: \n  - Support for EFR32xG21, Revision C and later\n  - Targeted quality improvements and bug fixes\n- Multiprotocol: Targeted quality improvements and bug fixes\n", "version": "4.2.4", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.2.4", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.2.4", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"extensions": [{"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "1.0.5", "desc": "Matter extension for Gecko SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "1.0.3", "desc": "Amazon Sidewalk SDK extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.2.3", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.2.3", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only\nBluetooth SDK *******\n- Targeted quality improvements and bug fixes\nBluetooth Location Services *******\n- Underlying code changes only\nBluetooth Mesh SDK 4.2.1.0\n- Targeted quality improvements and bug fixes\nGecko Platform *******\n- CMSIS Device: Added support for EFR32xG21 Rev. C and Rev. D\n- Boards and External Devices: Additional boards supported\n- RAIL library: Targeted quality improvements and bug fixes\nSilicon Labs OpenThread SDK 2.2.3.0\n- Targeted quality improvements and bug fixes\nFlex SDK 3.5.3.0\n- RAIL Library: Targeted quality improvements and bug fixes\nUSB Device Stack *******\n- Targeted quality improvements and bug fixes\nWi-SUN SDK *******\n- Targeted quality improvements and bug fixes\nZ-Wave and Z-Wave Long Range 700/800 SDK 7.19.3.0 GA is certified according to the approved 2022 Specification test suite.\n- Underlying code changes only\nZigbee EmberZNet SDK 7.2.3.0\n- Targeted quality improvements and bug fixes\n", "version": "4.2.3", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.2.3", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}]}, {"extensions": [{"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "1.0.5", "desc": "Matter extension for Gecko SDK Suite"}, {"id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK", "version": "1.0.2", "desc": "Amazon Sidewalk SDK extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.2.2", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.2.2", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK 6.4.2.0\n- Underlying platform changes only\n\nBluetooth SDK 5.1.1.0\n- TrustZone solution is now available on all Bluetooth devices. See AN1374 for implementation details.\n- Other targeted quality improvements and bug fixes\n\nBluetooth Location Services 5.1.1.0\n- Underlying code changes only\n\nBluetooth Mesh SDK *******\n- Support for Mesh draft specification 1.1\n  - Mesh Protocol\n  - Mesh Binary Large Object Transfer Model (MBT)\n  - Mesh Device Firmware Update Model (DFU)\n- Reduced project flash consumption by optimizing Mesh stack code size\n- Support added for xGM240P PCB Modules and BG22/BGM220 Explorer Kits\n- Support added for GCC version 10.3-2021.10 and IAR version 9.20.4\n\nGecko Platform *******\n- Security: Released software support for TrustZone, GA quality\n- Other targeted quality improvements and bug fixes\n\nSilicon Labs OpenThread SDK *******\n- Targeted quality improvements and bug fixes\n\nFlex SDK 3.5.2.0\n- RAIL Library: Targeted quality improvements and bug fixes\n\nUSB Device Stack *******\n- Targeted quality improvements and bug fixes\n\nWi-SUN SDK 1.5.1.0\n- Targeted quality improvements and bug fixes\n\nZ-Wave and Z-Wave Long Range 700/800 SDK 7.19.2.0 GA is certified according to the approved 2022 Specification test suite.\n- Various bug fixes, refer to release notes.\n\nZigbee EmberZNet SDK 7.2.2.0\n- Targeted quality improvements and bug fixes", "version": "4.2.2", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.2.2", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}]}, {"extensions": [{"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "1.0.4", "desc": "Matter extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.2.1", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.2.1", "releaseNote": "GSDK 4.2.1.0\n\nWARNING: The Bluetooth mesh SDK is not included in GSDK 4.2.1.0, because this version of the Bluetooth mesh SDK contains material that is not yet released to the public by the Bluetooth SIG.\n \nCurrent Bluetooth mesh users should not update to this version, as your current Bluetooth mesh SDK will be deleted. If you do install it, you can revert to version 4.1.4 or earlier by selecting Install > Manage Installed Packages. Click the SDKs tab.  Next to Gecko SDK – 32-bit and Wireless MCUs 4.2.1.0 click ... and select Change Version. Select the earlier version, and click Finish.\n \nAlternatively, if you are a Bluetooth SIG member, you can obtain a version based on the upcoming Bluetooth mesh 1.1 specification by raising a Salesforce ticket with BTMesh as the Software Solution. \n32-Bit MCU SDK 6.4.1.0\n- Underlying platform changes only\nBluetooth SDK 5.1.0.0\n- Targeted quality improvements and bug fixes\nBluetooth Location Services 5.1.0.0\n- Underlying code changes only\nGecko Platform 4.2.1.0\n- Targeted quality improvements and bug fixes\nSilicon Labs OpenThread SDK *******\n- Targeted quality improvements and bug fixes\nFlex SDK *******\n- Targeted quality improvements and bug fixes\nUSB Device Stack *******\n- Targeted quality improvements and bug fixes\nWi-SUN SDK *******\n- EFR32FG25 launch support\n- Enhancements to the Border Router RCP\n- Other targeted quality improvements and bug fixes\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** GA is certified according to the approved 2022 Specification test suite.\n- Various bug fixes, refer to release notes.\nZigbee EmberZNet SDK *******\n- Multiprotocol: Zigbeed now supports coex EZSP commands\n- Other targeted quality improvements and bug fixes", "version": "4.2.1", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.2.1", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.2.1", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"extensions": [{"id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter", "version": "1.0.1", "desc": "Matter extension for Gecko SDK Suite"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.2.0", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.2.0", "releaseNote": "GSDK *******\n\nWARNING: The Bluetooth mesh SDK is not included in GSDK *******, because this version of the Bluetooth mesh SDK contains material that is not yet released to the public by the Bluetooth SIG.\n \nCurrent Bluetooth mesh users should not update to this version, as your current Bluetooth mesh SDK will be deleted. If you do install it, you can revert to version 4.1.3 by selecting Install > Manage Installed Packages. Click the SDKs tab.  Next to Gecko SDK – 32-bit and Wireless MCUs ******* click ... and select Change Version. Select version 4.1.3, and click Finish.\n \nAlternatively, if you are a Bluetooth SIG member, you can obtain a version based on the upcoming Bluetooth mesh 1.1 specification by raising a Salesforce ticket with BTMesh as the Software Solution. \n32-Bit MCU SDK *******\n- Added support for new OPNs\nBluetooth SDK *******\n- Bluetooth\n  - External bonding database to support infinite number of bondings and key sharing\n  - BGAPI event for Bluetooth buffer exhaustion signaling\n  - Active scan improvements\n  - Three-Wire UART (H5) Transport Layer for HCI (experimental)\n  - Certification-Based Authentication and Pairing for High- and Mid-Vault devices (experimental)\n- Multiprotocol\n  - Dynamic Multiprotocol Bluetooth and multi-PAN 802.15.4 in RCP mode\n  - BLE de-init and re-init for multiprotocol use cases\n  - Dynamic Multiprotocol Bluetooth and Zigbee NCP (experimental)\nBluetooth Location Services *******\n- Targeted quality improvements and bug fixes\nGecko Platform *******\n- Support for EFR32xG25 devices\n- Support for MGM240Dx modules\n- Support for BGM24x modules\n- Improved CPC UART drivers reliability\n- Many CPC improvements\n- Android support for CPCd\n- Security:\n  - Updates the Mbed TLS library to version 3.2.1, with support for CBAP\n  - Updated ITS driver (version 3) for optimised key look up time\n  - Updated software support for TrustZone, Evaluation quality\nSilicon Labs OpenThread SDK *******\n- OpenThread\n  - SPI support for OpenThread RCP without CPC\n  - Thread 1.3.0 GA, and ******* support for OpenThread and Matter 1.0 (experimental)\n  - Support for CPC on Android Host (experimental)\n  - MGM240S SiP Module support\n  - MG24 Explorer kit support\n- Multiprotocol\n  - Dynamic Multiprotocol Bluetooth and multi-PAN 802.15.4 in RCP mode\n  - Dynamic Multiprotocol Bluetooth and Zigbee NCP (experimental)\n  - Manufacturing Library (MfgLib) support for Concurrent Multiprotocol RCP\n  - Zigbee + OpenThread Concurrent Listening on MG24 parts (experimental)\nFlex SDK *******\n- RAIL Apps and Library:\n  - FG25 Flex-RAIL GA support\n  - New Long Range PHYs support for 490 MHz and 915 MHz\n  - xG12 dynamic mode switching support in RAIL\n  - xG22 extended band support\n- Connect Apps and Stack:\n  - xG24 Connect support\nUSB Device Stack *******\n- The USB driver can now compile with the -Wundef gcc compiler option without any warnings.\nUSB Device Stack *******\n- The USB driver can now compile with the -Wundef gcc compiler option without any warnings.\nWi-SUN SDK *******\n- Wi-SUN Stack\n  - Added minimal support for FAN1.1 LFN (Limited Functional Node), including an API to configure the device role, and a new st of libraries supporting both LFN and FFN device types\n  - Added support for FAN 1.1 PHY mode switch \n  - Added support for FSK FEC\n  - Added support for EFR32FG25 - it supports all FAN1.1 OFDM  modulation schemes and all FAN1.0 FSK configurations\n  - Added support for EFF01\n- Wi-SUN Applications\n  - Added support for EFR32FG25\n  - Added FAN 1.1 PHY support (settings and CLI)\n  - Added a CoAP Advanced Resource Handler\n  - Added support for iPerf multicast\n  - Added support for CPC in Wi-SUN RCP\nZ-Wave and Z-Wave Long Range 700/800Z-Wave and Z-Wave Long Range 700/800 SDK ******** Pre-Certified GA\n- Simplified application development due to 80% reduction in application code lines\n- Advanced configuration for Command Classes and Z-Wave Application Framework\n- Improved documentation to better support development from idea to certification\n- Z-Wave Long Range Dynamic Channel selection algorithm implemented\n- Z-Wave Alpha release on radio boards BRD4400B/4401B\n- Simplicity Studio now has Pin Tool support for Z-Wave radio boards\n- Important_changes.md that contains all the breaking API changes introduced since last release\n- LTO enabled in released software\n- Tamper protection now available for Z-Wave\n- S0 takes now advantage of the security features of SE\n- New PC based Controller v5.54 release\n- Z-Wave Source Code available for Z-Wave Alliance members\n- Improved 800 SDK demo experience independent of Simplicity Studio SDK ******** GA\nZigbee EmberZNet SDK *******\n- Zigbee\n  - Secure key storage support for MG2x parts that support Secure Vault-High\n  - MG24+Si4468 Dual-PHY Zigbee Smart Energy support\n  - MG12 Dual-Band 2.4GHz + SubGHz Zigbee Smart Energy support\n  - MGM240S SiP Module Support\n  - Zigbee on Host (ZigbeeD) support for 32 bit and 64 bit x86 architecture (experimental)\n- Multiprotocol\n  - Dynamic Multiprotocol Bluetooth and multi-PAN 802.15.4 in RCP mode\n  - Dynamic Multiprotocol Bluetooth and Zigbee NCP (experimental)\n  - Manufacturing Library (MfgLib) support for Concurrent Multiprotocol RCP\n  - Zigbee + OpenThread Concurrent Lis-tening on MG24 parts (experimental)", "version": "4.2.0", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.2.0", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.2.0", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.1.6", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.1.6", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only\nBluetooth SDK *******\n- Underlying platform changes only\nBluetooth Location Services *******\n- Underlying platform changes only\nBluetooth Mesh SDK *******\n- Underlying platform changes only\nGecko Platform *******\n- Bootloader: One update\nSilicon Labs OpenThread SDK *******\n- Underlying platform changes only\nProprietary Flex SDK 3.4.6.0\n- Underlying platform changes only\nUSB Device Stack *******\n- No change from previous release\nWi-SUN SDK *******\n- No change from previous release\nZ-Wave and Z-Wave Long Range 700/800 SDK 7.18.8.0 GA\n- Underlying platform changes only\nZigbee EmberZNet SDK 7.1.6.0\n- Underlying platform changes only", "version": "4.1.6", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.1.6", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.5", "sdkVersion": "4.1.6", "id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.1.6", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.1.5", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.1.5", "releaseNote": "GSDK 4.1.5.0\n\n32-Bit MCU SDK 6.3.5.0\n- Underlying platform changes only\nBluetooth SDK *******\n- Support for EFR32xG21, Revision C and later plus targeted quality improvements and bug fixes\nBluetooth Location Services *******\n- Underlying platform changes only\nBluetooth Mesh SDK 3.0.5.0\n- Support for EFR32xG21, Revision C and later\nGecko Platform 4.1.5.0\n- Support for EFR32xG21, Revision C and later\nSilicon Labs OpenThread SDK 2.1.5.0\n- Support for EFR32xG21, Revision C and later\nProprietary Flex SDK 3.4.5.0\n- RAIL Library: Support for EFR32xG21, Revision C and later\nUSB Device Stack *******\n- No change from previous release\nWi-SUN SDK *******\n- No change from previous release\nZ-Wave and Z-Wave Long Range 700/800 SDK 7.18.6.0 GA\n- Underlying platform changes only\nZigbee EmberZNet SDK 7.1.5.0\n- Support for EFR32xG21, Revision C and later, plus bug fixes", "version": "4.1.5", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.1.5", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.5", "sdkVersion": "4.1.5", "id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.1.5", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"extensions": [{"id": "com.silabs.sdk.matter_demo", "label": "Matter Enablement Package", "version": "1.0.0", "desc": "Matter Demos & Docs"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.1.4", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.1.4", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only\nBluetooth SDK *******\n- Targeted quality improvements and bug fixes\nBluetooth Location Services *******\n- Underlying platform changes only\nBluetooth Mesh SDK *******\n- Targeted quality improvements and bug fixes\nGecko Platform *******\n- RAIL Library: Fixed one issue\nSilicon Labs OpenThread SDK *******\n- Multiprotocol-related updates\nProprietary Flex SDK *******\n- RAIL Library: Fixed one issue\nUSB Device Stack *******\n- No change from previous release\nWi-SUN SDK *******\n- No change from previous release\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** GA\n- One issue fixed\nZigbee EmberZNet SDK *******\n- Targeted quality improvements and bug fixes", "version": "4.1.4", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.1.4", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.5", "sdkVersion": "4.1.4", "id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.1.4", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"extensions": [{"id": "com.silabs.sdk.matter_demo", "label": "Matter Enablement Package", "version": "1.0.0", "desc": "Matter Demos & Docs"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.1.3", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.1.3", "releaseNote": "GSDK 4.1.3.0\n\n32-Bit MCU SDK 6.3.3.0\n- Underlying platform changes only\nBluetooth SDK 4.2.1.0\n- Early access part support\n- Targeted bug fixes\nBluetooth Location Services 4.2.1.0\n- Underlying platform changes only\nBluetooth Mesh SDK 3.0.3.0\n- Early access part support\nGecko Platform 4.1.3.0\n- Early access part support\n- RAIL Library: Targeted bug fixes\nSilicon Labs OpenThread SDK 2.1.3.0\n- Early access part support\nProprietary Flex SDK 3.4.3.0\n- Early access part support\n- RAIL Library: Targeted bug fixes\nUSB Device Stack *******\n- No change from previous release\nWi-SUN SDK *******\n- Underlying platform changes only\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** GA\n- Nothing changed in v7.18.3 compared to v7.18.2 with respect to new features and fixed bugs. Some of the platform components are updated but have no impact on the functionality.\nZigbee EmberZNet SDK 7.1.3.0\n- Early access part support", "version": "4.1.3", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.1.3", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.5", "sdkVersion": "4.1.3", "id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.1.3", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"extensions": [{"id": "com.silabs.sdk.matter_demo", "label": "Matter Enablement Package", "version": "1.0.0", "desc": "Matter Demos & Docs"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.1.2", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.1.2", "releaseNote": "GSDK 4.1.2.0\n\n32-Bit MCU SDK 6.3.2.0\n- Underlying platform changes only\nBluetooth SDK *******\n- Targeted quality improvements and bug fixes\nBluetooth Location Services *******\n- Support added for CoreHW 4x4 and 2x2 URA antennas\nBluetooth Mesh SDK *******\n- Targeted quality improvements and bug fixes\nGecko Platform 4.1.2.0\n- CPC: New musl libc support and configuration options\n- RAIL: Improved PA configurations for the xGM240 modules based on additional test data\nSilicon Labs OpenThread SDK 2.1.2.0\n- Added ot_cert_libs - OpenThread Certification Libraries\n- Added ot_coap_cert_libs - OpenThread CoAP Certification Libraries\nProprietary Flex SDK 3.4.2.0\n- RAIL Library: Improved PA configurations for the xGM240 modules based on additional test data\nUSB Device Stack *******\n- No change from previous release\nWi-SUN SDK 1.3.2.0\n- Targeted quality improvements and bug fixes\nZ-Wave and Z-Wave Long Range 700/800 SDK 7.18.2.0 GA\n- Certified according to the approved 2022 Specification test suite\n- Various bug fixes, refer to release notes\nZigbee EmberZNet SDK *******\n- Targeted quality improvements and bug fixes", "version": "4.1.2", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.1.2", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.5", "sdkVersion": "4.1.2", "id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.1.2", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"extensions": [{"id": "com.silabs.sdk.matter_demo", "label": "Matter Enablement Package", "version": "1.0.0", "desc": "Matter Demos & Docs"}], "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.1.1", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.1.1", "releaseNote": "GSDK 4.1.1.0\n\n32-Bit MCU SDK 6.3.1.0\n- Underlying platform changes only\nBluetooth SDK *******\n- Targeted quality improvements and bug fixes\nBluetooth Location Services *******\n- Underlying platform changes only\nBluetooth Mesh SDK *******\n- Targeted quality improvements and bug fixes\nGecko Platform 4.1.1.0\n- Targeted quality improvements and bug fixes\nSilicon Labs OpenThread SDK 2.1.1.0\n- Targeted quality improvements and bug fixes\nProprietary Flex SDK 3.4.1.0\n- RAIL Library: Targeted quality improvements and bug fixes\nUSB Device Stack *******\n- No change from previous release\nWi-SUN SDK *******\n- Targeted quality improvements and bug fixes\nZ-Wave and Z-Wave Long Range 700/800 SDK 7.18.1.0 GA\n- Certified according to the approved 2022 Specification test suite\n- Various bug fixes, refer to release notes\nZigbee EmberZNet SDK *******\n- Targeted quality improvements and bug fixes", "version": "4.1.1", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.1.1", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.5", "sdkVersion": "4.1.1", "id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.1.1", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.1.0", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.1.0", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Added support for BRD2204C board\nBluetooth SDK *******\n- Support added for Directed Advertising \n- Support added for L2CAP connection-oriented channel\n- Alpha Periodic Advertising Synchronization Transfer\n- Apploader merged with Bootloader as communication plugin\n- Alpha Dynamic Multiprotocol Bluetooth and multi-PAN 802.15.4 in RCP mode\nBluetooth Location Services *******\n- Support added for new dual-polarized antenna array board BRD4191A\nBluetooth Mesh SDK *******\n- Reduced project flash consumption by optimizing Mesh stack code size\n- Support added for xGM240P PCB Modules and BG22/BGM220 Explorer Kits\n- Support added for GCC version 10.3-2021.10 and IAR version 9.20.4\nGecko Platform *******\n- Initial release of CPC\n- Added support for EFR32xG24; MGM240, EFR32MR21 and FGM230\n- Several code size improvements related to Power Manager, HFXO Manager and em_crypto\n- Tools and Dependencies\n  - Updated compiler support to GCC 10.3-2021.10 and IAR 9.20.4\n  - Updated CMSIS to version 5.8.0\n- Drivers\n  - Added a new component to synchronize UART/PTI settings between WSTK mainboard and the radio board\n  - Added support for Analog Joystick driver to use the joystick functionality on mainboard v2\n- Security\n  - Mbed TLS is updated to version 3.1.0\n  - Added software support for TrustZone, BETA quality\n- Bootloader\n  - Jedec driver support for external SPI flash \n  - Added a new bootloader sample application for devices with external SPI flash\nSilicon Labs OpenThread SDK *******\n- Alpha SPI support for OpenThread RCP without CPC\n- Thread 1.2 and 1.3 support for OpenThread\n- Updated GCC compiler version to 10.3.1\n- Alpha Concurrent Multiprotocol Zigbee in NCP mode and OpenThread in RCP mode\n- Alpha Dynamic Multiprotocol Bluetooth and multi-PAN 802.15.4 in RCP mode\nProprietary Flex SDK *******\n- EFR32xG24 GA with Antenna Diversity support\n- FGM230S proprietary module GA\n- Secure Vault integration to Connect stack\n- BGM220 Range Test + DMP  pre-compiled demo with EFR Connect Mobile Application\nUSB Device Stack *******\n- No change from previous release\nWi-SUN SDK *******\n- FAN 1.0 certified Router & Border Router\n- Perf throughput test tool\n- Wi-SUN Configurator \n- CLI for certification \n- ARIB T108 support\nZ-Wave and Z-Wave Long Range 700/800 SDK \n- Keyfob application\n- New Z-Wave 800 SDK containing preprogrammed radio boards for quick IOT demos and Z-Wave technology evaluations\n- Z-Wave 800 S2 protocol uses secure vault for cryptographic key storage and hardware acceleration\n- White paper about large network performance\n- Serial API Controller and End Device source code available enabling customization of I/O etc.\n- Z-Wave Long Range now supports both 250 ms and 1000 ms wakeup beams\n- Improved SmartStart inclusion time on Z-Wave Long Range\n- Streamlined hardware dependencies in Z-Wave applications\n- Z-Wave Region stored in MFG token\n- Support of WSTK v2 mainboard BRD4002A\n- GCC Compiler 10.3.1\nZigbee EmberZNet SDK *******\n- 2.4GHz Zigbee Smart Energy support for xG24\n- 802.15.4 Signal Identifier and MAC CCA Mode 2 and 3 support for xG24\n- Zigbee Green Power Gateway Backup\n- Updated GCC and IAR compiler version\n- Alpha Concurrent Multiprotocol Zigbee in NCP mode and Open-Thread in RCP mode \n- Alpha Dynamic Multiprotocol Bluetooth and multi-PAN 802.15.4 in RCP mode", "version": "4.1.0", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.1.0", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.5", "sdkVersion": "4.1.0", "id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.1.0", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.0.2", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.0.2", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only\n\nBluetooth SDK *******\n- EFR32xG24 support\n- Targeted quality improvements and bug fixes\n\nBluetooth Location Services *******\n- Targeted quality improvements and bug fixes\n\nBluetooth Mesh SDK *******\n- EFR32xG24 support\n- Added support for controlling the advertising interval for unprovisioned device beacons, GATT provisioning service advertisements, and GATT proxy service advertisements. \n- Made a number of small optimizations reducing the flash consumption of projects; exact amount saved depends on the feature set a project uses.\n- Targeted quality improvements and bug fixes\n\nGecko Platform *******\n- Peripherals: Added support for the IADC input (as positive or negative inputs) for DAC 0/1 and PADANA 0/1/2/3\n- Gecko Bootloader: Added support for MX25R3235F SPI flash part\n- Boards and External Devices: Added support for new OPNs BRD4330A and BRD4331A\n- RAIL library:\n  - EFR32xG24 support\n  - Added a new component to enable the built-in PHYs to operate with either 38.4 MHz or 39 MHz crystals on EFR32xG24 devices\n  - Added support for additional IEEE 802.15.4 CCA modes on platforms that support signal detection (EFR32xG24)\n  - Added support for IEEE 802.15.4 and Bluetooth LE Signal Identifier hardware on the EFR32xG24 (tested to alpha quality)\n- Targeted quality improvements and bug fixes throughout\n\nSilicon Labs OpenThread SDK *******\n- EFR32MG24 support\n- Targeted quality improvements and bug fixes\n\nProprietary Flex SDK *******\n- EFR32xG24 support\n- Targeted quality improvements and bug fixes\n\nUSB Device Stack *******\n- No change from previous release\n\nWi-SUN SDK *******\n- Applications: \n  - Moved Wi-SUN - SoC CLI and Wi-SUN - RCP from app/wisun to protocol/wisun\n  - Minimum heap size reduction (application configuration)\n- Targeted quality improvements and bug fixes\n\nZ-Wave and Z-Wave Long Range 700/800 SDK v7.17.2.0\n- Targeted quality improvements and bug fixes\n\nZigbee EmberZNet SDK *******\n- EFR32MG24 support\n- New APIs including setting the power descriptor dynamic value\n- Targeted quality improvements and bug fixes", "version": "4.0.2", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.0.2", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.5", "sdkVersion": "4.0.2", "id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.0.2", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.0.1", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.0.1", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Underlying platform changes only\n\nBluetooth SDK *******\n- Selected quality improvements and bug fixes\n\nBluetooth Location Services *******\n- Selected quality improvements and bug fixes\n\nBluetooth Mesh SDK *******\n- Selected quality improvements and bug fixes\n\nGecko Platform *******\n- CMSIS: Added module- and part-specific DCDC initialization headers to have more control on DCDC configuration.\n- Security: X25519 and Ed25519 algorithms, along with related key management functionality, are now accelerated on Series-2 Secure Vault Mid devices.\n- Operating System: Added new errno module. \n- Boards and External Devices: Created pressure driver that abstracts bmp280 and bmp3xx.\n- RAIL Library:\n  - Restricted the SL_RAIL_UTIL_PA_RAMP_TIME_US to 10 us on some EFR32 modules to match the certification conditions.\n  - Updated the Z-Wave PHYs for the EFR32xG23 to prevent a sensitivity degradation on the R2 (9.6 kbps) PHY.\n- Selected quality improvements and bug fixes throughout.\n\nOpenThread SDK 2.0.1.0\n- Selected quality improvements and bug fixes\n\nProprietary Flex SDK *******\n- Selected quality improvements and bug fixes \n\nUSB Device Stack *******\n- No change in GSDK *******\n\nWi-SUN SDK *******\n- Stack: Reduced the stack log verbosity\n- Other quality improvements and bug fixes\n\nZ-Wave and Z-Wave Long Range SDK v7.17.1.0 Pre-Certified GA is pre-Certified according to the 2021D Specification Release.\nZ-Wave and Z-Wave Long Range 700/800 SDK v7.17.1.0\n- Various bug fixes, refer to release notes\n\nZigbee EmberZNet SDK *******\n- Added sl_set_passive_ack_config, which allows the higher layers to control the broadcast behavior of  a routing device.\n- The Known Issues table in the release notes has been scrubbed. Issues fixed in version ******* are now reflected in the Fixed Issues section. \n- Other selected quality improvements and bug fixes.\n", "version": "4.0.1", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.0.1", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.5", "sdkVersion": "4.0.1", "id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.0.1", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}, {"id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.0.0", "label": "Gecko SDK - 32-bit and Wireless MCUs v4.0.0", "releaseNote": "GSDK *******\n\n32-Bit MCU SDK *******\n- Gecko USB has been deprecated\n\nBluetooth SDK *******\n- Bluetooth v5.3 qualified\n- New Co-Processor Communication (CPC) transport for RCP/HCI\n- RTOS support in RCP mode\n- Improved tools for Angle-of-Arrival evaluation and development\n- Interoperability testing example added to the SDK\n\nBluetooth Location Services *******\n- Support for additional hardware platforms and operating systems\n\nBluetooth Mesh SDK *******\n- New example embedded Provisioner application\n- NCP Commander support for Mesh\n- Multiple improvements to the Mesh BGAPI\n- Support for Amazon Bluetooth Mesh Simple Setup (BSS)\n\nGecko Platform *******\n- CMSIS Device\n  - Added support for ZGM230S modules\n- Drivers\n  - Added support for RBG LEDs in the LED driver\n- Services\n  - Added password and session protection in the CLI\n- Middleware\n  - Fixed vulnerabilities in Micrium OS Net\n  - Deprecated Micrium OS USB Device and USB Host\n  - Deprecated Gecko USB\n- Security\n  - Mbed TLS is updated to version 3.0.0\n- Operating System\n  - Changed default configurations of the Micrium OS Kernel to reduce code size\n- Gecko Bootloader\n  - Projects now supported in Project Configurator\n  - Provided as a full-source delivery\n- Machine Learning\n  - Updated TensorFlow Lite Micro version and updated quality to production level\n  - Added accelerated kernels and automatic initialization of TensorFlow Lite Micro\n- Examples\n  - Added emode demo\n  - Added new machine learning examples\n\nProprietary Flex SDK *******\n- Added support for Z-Wave on EFR32xG23 parts\n- Added support for new ZGM230 modules\n- Updated the default PA curves for EFR32xG23 parts to be more accurate on Silicon Labs radio boards\n\nOpenThread SDK *******\n- Multiprotocol and multi-PAN radio coprocessor (RCP) model\n- Thread Duckhorn feature support\n\nUSB Device Stack *******\n- Initial release\n\nWi-SUN SDK *******\n- FAN 1.0 Certified Wi-SUN Border Router reference design\n- Wi-SUN Network Performance Application\n\nZ-Wave and Z-Wave Long Range 700/800 SDK ******** Pre-Certified GA\n- Z-Wave 800 - Lower power compared to 700\n  - 55 % reduction in Rx current\n  - 35% reduction in Tx current\n- Z-Wave 800 - Longer range compared to 700\n  - Integrated +20 dBm PA\n  - 9 dBm sensitivity improvement for Z-Wave Mesh\n  - 3 dBm sensitivity improvement for Z-Wave Long Range\n- Z-Wave 800 - Best-in-class security\n  - S2 and Secure Vault\n\nZigbee EmberZNet SDK *******\n- Integrated with Gecko Platform component-based architecture \n- 802.15.4 Radio Co-Processor (RCP)\n- RCP Host for Raspberry Pi (Docker image)\n", "version": "4.0.0", "preferredLocation": "C:\\Users\\<USER>\\SimplicityStudio\\SDKs\\gecko_sdk_7", "extensionsInfo": [{"recommendedSdkVersion": "4.4.6", "sdkVersion": "4.0.0", "id": "uc.extension.wiseconnect3_sdk", "label": "WiSeConnect"}, {"recommendedSdkVersion": "4.4.5", "sdkVersion": "4.0.0", "id": "com.silabs.sdk.matter", "label": "Silicon Labs Matter"}, {"recommendedSdkVersion": "4.4.3", "sdkVersion": "4.0.0", "id": "com.silabs.sdk.sidewalk", "label": "Silicon Labs Amazon Sidewalk SDK"}]}], "desc": "Silicon Labs Gecko SDK", "latest": {"installed": true, "id": "gitrepo.https-..github.com.siliconlabs.gecko_sdk.git-verv4.4.6", "version": "4.4.6"}}, {"installed": [], "legacy": true, "repositoryType": "p2", "permission": true, "id": "Gecko SDK 3.2", "label": "Gecko SDK 3.2", "installable": [{"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.exx32.v6.1.feature.metadata.resource.feature.group/icon_stack_40x40.png", "id": "com.silabs.sdk.exx32.v6.1.feature", "label": "32-bit MCU SDK *******", "version": "*******", "releaseNote": "32-bit MCU SDK *******\nrequires Gecko Platform 3.2.9\nWhat's new in 32-Bit MCU SDK *******\n* Underlying Gecko Platform changes only\n\nWhat's new in 32-Bit MCU SDK *******\n* Underlying Gecko Platform changes only\n\nWhat's new in 32-Bit MCU SDK *******\n* Underlying Gecko Platform changes only\n\nWhat's new in 32-Bit MCU SDK *******\n* Underlying Gecko Platform changes only\n\nWhat's new in 32-Bit MCU SDK ******* (Limited Access)\n* Early access part support\n\nWhat's new in 32-Bit MCU SDK *******\n* Underlying Gecko platform changes only\n\nWhat's new in 32-Bit MCU SDK *******\n* Underlying Gecko platform changes only\n\nWhat's new in 32-Bit MCU SDK *******\n* Underlying Gecko platform changes only\n\nWhat's new in 32-Bit MCU SDK *******\n* Underlying Gecko platform changes only", "desc": "Silicon Labs 32-bit MCU SDK for EFM32 and EZR32"}, {"dirty": false, "dependency": true, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.gecko_platform.v3.2.feature.metadata.resource.feature.group/icon_stack_40x40.png", "id": "com.silabs.sdk.gecko_platform.v3.2.feature", "label": "Gecko Platform 3.2.9", "version": "3.2.9", "releaseNote": "Gecko Platform 3.2.9\nWhat's new in Gecko Platform *******\n* Support for EFR32xG22, Revision D\n\nWhat's new in Gecko Platform *******\n* Bootloader: One update\n\nWhat's new in Gecko Platform *******\n* Support for EFR32xG21, Revision C and later\n\nWhat's new in Gecko Platform *******\n* Early access part support\n\nWhat's new in Gecko Platform ******* (Limited Access)\n* Early access part support\n\nWhat's new in Gecko Platform *******\n* Selected quality improvements and bug fixes\n\nWhat's new in Gecko Platform *******\n* Introduced support for EFR32xG23 devices\n\nWhat's new in Gecko Platform *******\n* Selected quality improvements and bug fixes\n\nWhat's new in Gecko Platform *******\n* Peripherals\n  * EFP Voltage scaling and Direct mode on EFR32xG22 and EFM32xG22\n  * EFP Coulomb counter\n* Services\n  * Several fixes related to Power Manager\n* Middleware\n  * CSLIB can now be used with the Gecko SDK components\n* Security\n  * Mbed TLS library updated to v2.26.0\n* Operating System\n  * Added more NOR Flash options for Micrium OS FS\n* Examples\n  * Coulomb Counter\n  * CLI & I2C\n* Boards and External Devices\n  * Added support for several new boards\n* Other Platform Components\n  * Updated IAR compiler to version 8.50.9.\n  * Updated GCC compiler to version 10.2.\n  * Amazon FreeRTOS Libraries have been added to the Gecko SDK.", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\gecko_sdk_suite\\v3.2", "desc": "Silicon Labs Gecko Platform"}], "desc": "Gecko SDK 3.2", "latest": {"installed": true, "id": "com.silabs.sdk.exx32.v6.1.feature.metadata.resource.feature.group", "version": "*******"}}, {"installed": [], "legacy": true, "repositoryType": "p2", "permission": true, "id": "Gecko SDK 3.1", "label": "Gecko SDK 3.1", "installable": [{"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.exx32.v6.0.feature.metadata.resource.feature.group/icon_stack_40x40.png", "id": "com.silabs.sdk.exx32.v6.0.feature", "label": "32-bit MCU SDK *******", "version": "*******", "releaseNote": "32-bit MCU SDK *******\nrequires Gecko Platform 3.1.2\nWhat's new in 32-Bit MCU SDK *******\n* Added Security Information section to release notes\n* Re-added source for the 'factory-demo' sample\n\nWhat's new in 32-Bit MCU SDK *******\n* Underlying platform changes only\n\nWhat's new in 32-Bit MCU SDK *******\n* Added platform examples for EFM32 series 0 and series 1 devices\n* Reduced number of sample applications for EFM32 hardware kits\n* Reduced pre-compiled demo list for EFM32 hardware kits", "desc": "Silicon Labs 32-bit MCU SDK for EFM32 and EZR32"}, {"dirty": false, "dependency": true, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.gecko_platform.v3.1.feature.metadata.resource.feature.group/icon_stack_40x40.png", "id": "com.silabs.sdk.gecko_platform.v3.1.feature", "label": "Gecko Platform 3.1.2", "version": "3.1.2", "releaseNote": "Gecko Platform 3.1.2\nWhat's new in Gecko Platform *******\n* Added support for EFM32PG22 products.\n* mbed TLS code size and performance updates.\n\nWhat's new in Gecko Platform *******\n* Fixed an issue that could prevent the MX25 Flash to shut down properly.\n* Some bug fixes in Power Manager.\n* Added support for new ZG14 OPN.\n\nWhat's new in Gecko Platform *******\n* Peripherals: Include handling pcntModeOvsQuadx modes in PCNT_Init\n* Services: New NVM3 function that enables reading only parts of a data object\n* Common:\n  * Platform converted to use BASEPRI interrupt levels, vs. PRIMASK interrupt enable/disable\n  * MCU SDK 6.0 integrated into Common platform.\n* Security:\n  * Mbed TLS updated to version 2.24.0\n  * Added attestation support in SE Manager\n* Operating System: Platform components are now compatible with any kernel providing a CMSIS-RTOS2 implementation.\n* Examples:\n  * Added alpha quality Hello World and Micro Speech examples for TensorFlow Lite for Microcontrollers.\n  * Added several common sample apps for EFM and EFR devices.\n  * Removed several MCU sample apps.\n* Boards and External Devices: Added better support for 60 Ohm ESR crystals.\n* Other Platform Components: Added alpha support for TensorFlow Lite for Microcontrollers version 2.3.1.", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\gecko_sdk_suite\\v3.1", "desc": "Silicon Labs Gecko Platform"}], "desc": "Gecko SDK 3.1", "latest": {"installed": true, "id": "com.silabs.sdk.exx32.v6.0.feature.metadata.resource.feature.group", "version": "*******"}}, {"installed": [], "legacy": true, "repositoryType": "p2", "permission": true, "id": "Gecko SDK 3.0", "label": "Gecko SDK 3.0", "installable": [{"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.exx32.v5.10.feature.metadata.resource.feature.group/icon_stack_40x40.png", "id": "com.silabs.sdk.exx32.v5.10.feature", "label": "32-bit MCU SDK ********", "version": "********", "releaseNote": "32-bit MCU SDK ********\nrequires Gecko Platform 3.0.2\nWhat's new in 32-Bit MCU SDK ********\n  * Underlying platform changes only\nWhat's new in 32-Bit MCU SDK ********\n  * Added missing empty examples for EFM32/EZR32 in Simplicity Studio\nWhat's new in 32-Bit MCU SDK ********\n  * Deleted examples for EFM32G-DK3550, EFM32LG-DK3650, EFM32GG-DK3750 and EFM32WG-DK3850\n  * Examples adapted to changes in underlying platform\n  * Restructured FreeRTOS examples", "desc": "Silicon Labs 32-bit MCU SDK for EFM32 and EZR32"}, {"dirty": false, "dependency": true, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.sdk.gecko_platform.v3.0.feature.metadata.resource.feature.group/icon_stack_40x40.png", "id": "com.silabs.sdk.gecko_platform.v3.0.feature", "label": "Gecko Platform 3.0.2", "version": "3.0.2", "releaseNote": "Gecko Platform 3.0.2\nWhat's new in Gecko Platform *******\n* NVM3: Additional improvements to worst case execution time of nvm3_repack() by using cache. \n\nWhat's new in Gecko Platform *******\n* Example Applications\n  * Added applications to demonstrate using Secure Element Manager\n* Drivers\n  * NVM3: Improved worst case execution time of nvm3_repack()\n\nWhat's new in Gecko Platform *******\n* Services\n  * New Power Manager service replacing SLEEP driver\n  * Deprecated RTCDRV in favor of Sleep Timer service\n* Security\n  * Mbed TLS updated to v2.16.6\n  * Added hardware acceleration for AES-GCM\n* Gecko Bootloader\n  * Added rollback prevention feature\n  * Upgrade process speedup\n* RAIL Library\n  * Studio V5/GSDK 3.0 support for RAIL\n  * New channel hopping (Preamble Sense) and low duty cycle (LDC) modes to help performance in noisy environments \n  * Optional support for the Silicon Labs Power Manager\n  * New and updated Sample Applications", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\gecko_sdk_suite\\v3.0", "desc": "Silicon Labs Gecko Platform"}], "desc": "Gecko SDK 3.0", "latest": {"installed": true, "id": "com.silabs.sdk.exx32.v5.10.feature.metadata.resource.feature.group", "version": "********"}}, {"installed": [], "legacy": true, "repositoryType": "p2", "permission": true, "id": "Z-Ware and Z/IP Gateway", "label": "Z-Ware and Z/IP Gateway", "installable": [{"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.stack.zware.v7.18.3.feature.metadata.resource.feature.group/icon_stack_40x40.png", "id": "com.silabs.stack.zware.v7.18.3.feature", "label": "Z-Ware SDK ********", "version": "********", "releaseNote": "Z-Ware SDK ********", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\zwave\\zware\\v7.18.3", "desc": "Z-Wave controller middleware running over Z-Wave over IP (Z-IP) interface as a Web Gateway"}, {"dirty": false, "dependency": false, "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.stack.zipgw.v7.18.3.feature.metadata.resource.feature.group/icon_stack_40x40.png", "id": "com.silabs.stack.zipgw.v7.18.3.feature", "label": "Z-Wave Z-IP Gateway SDK ********", "version": "********", "releaseNote": "Z-Wave Z-IP Gateway SDK ********", "preferredLocation": "C:\\SiliconLabs\\SimplicityStudio\\v5\\developer\\sdks\\zwave\\zip_gateway\\v7.18.3", "desc": "Z-IP Gateway Software Development Kit"}], "desc": "Z-Ware and Z/IP Gateway", "latest": {"installed": true, "id": "com.silabs.stack.zware.v7.18.3.feature.metadata.resource.feature.group", "version": "********"}}]}