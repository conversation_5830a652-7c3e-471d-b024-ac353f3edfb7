(()=>{"use strict";var e={3968:(e,t,o)=>{var a=o(1957),r=o(1947),s=o(499),n=o(9835);function i(e,t,o,r,s,i){const c=(0,n.up)("router-view");return(0,n.wg)(),(0,n.iD)("div",{id:"q-app",onKeyup:[t[0]||(t[0]=(0,a.D2)(((...e)=>i.onEnter&&i.onEnter(...e)),["enter"])),t[1]||(t[1]=(0,a.D2)(((...e)=>i.onEsc&&i.onEsc(...e)),["esc"]))]},[(0,n.Wm)(c)],32)}o(5516),o(3036),o(2309),o(9665);var c=o(3100),d=o(7524),l=o(7674),p=o(6475);const u={name:"App",methods:{...(0,c.nv)({init:"page1/init",setDefaultSdkId:"page1/setDefaultSdkId",setDefaultPartId:"page1/setDefaultPartId",setDefaultBoardIds:"page1/setDefaultBoardIds",setCurrentPage:"navigator/setCurrentPage",updateFinishButton:"navigator/updateFinishButton",setIsDarkTheme:"navigator/setIsDarkTheme",setHardwareContext:"page1/setHardwareContext",updateIdeToolchain:"page1/updateIdeToolchain",setSelectedResource:"page2/setSelectedResource",loadPrefs:"page1/loadPrefs",setStartedPage:"navigator/setStartedPage",setOs:"page3/setOs"}),setThemeMode(e){const t=e.getAttribute("data-theme");"com.silabs.ss.platform.theme.dark"===t?(this.$q.dark.set(!0),this.setIsDarkTheme(!0)):(this.$q.dark.set(!1),this.setIsDarkTheme(!1))},onEnter(){p.bus.emit("onEnterPressed")},onEsc(){d.Z.post("/rest/npwmanager/quit").catch((e=>{window.console.log(`App.vue; userQuit: ${e.message}`)}))}},async created(){window.console.log(`App.vue; 6/30/20 9:47; npw started new: ${window.location.href}`),this.$q.loading.show({delay:400});var e=document.documentElement;this.setThemeMode(e),new MutationObserver((t=>{t.forEach((t=>{"attributes"===t.type&&"data-theme"===t.attributeName&&this.setThemeMode(e)}))})).observe(e,{attributes:!0,attributeFilter:["data-theme"],subtree:!1}),await this.init(),(0,l.Z)("templatesColor","#000000"),(0,l.Z)("templatesColorDark","#FFFFFF"),(0,l.Z)("templatesBgColor","#555555"),(0,l.Z)("templatesSelectedColor","#dee3e7"),(0,l.Z)("templatesBgColorDark","#F9F9F9"),-1!=window.navigator.userAgent.toLowerCase().indexOf("windows")?this.setOs("windows"):-1!=window.navigator.userAgent.toLowerCase().indexOf("Mac")?this.setOs("mac"):this.setOs("linux");let t=window.location.href.split("?");if(t&&t.length>1){const e=new URLSearchParams(t[1]),a=decodeURIComponent(e.get("sdk")),r=decodeURIComponent(e.get("boards")),s=decodeURIComponent(e.get("part")),n=decodeURIComponent(e.get("template"));if(Boolean(a)&&"null"!==a&&Boolean(r)&&"null"!==r&&Boolean(s)&&"null"!==s){this.setDefaultSdkId(a),this.setDefaultBoardIds(r),this.setDefaultPartId(s);let e={};e["boards"]=r,e["part"]=s,e["sdk"]=a;try{await(0,d.Z)({method:"post",url:"/rest/npw/savepage1",params:e})}catch(o){return this.$q.loading.hide(),void window.console.log(`App.vue; savePageState err: ${o.message}`)}if(Boolean(n)&&"null"!==n){await this.setSelectedResource(n),await this.updateIdeToolchain(s);let e=this.$store.state.page1.ideToolchainModel,t={boardsId:r,partId:s,sdkId:a,ideToolchain:e};await this.setHardwareContext(t),this.setStartedPage("3_ProjectLocationPage"),this.$router.push({path:"3_ProjectLocationPage"}),this.setCurrentPage("3_ProjectLocationPage")}else{await this.updateIdeToolchain(s);let e=this.$store.state.page1.ideToolchainModel,t={boardsId:r,partId:s,sdkId:a,ideToolchain:e};await this.setHardwareContext(t),this.setStartedPage("2_ExampleProjectSelectionPage"),this.$router.push({path:"2_ExampleProjectSelectionPage"}),this.setCurrentPage("2_ExampleProjectSelectionPage")}return this.updateFinishButton(),void this.$q.loading.hide()}}await this.loadPrefs(),this.setStartedPage("1_ProjectSetupPage"),this.$router.push({path:"1_ProjectSetupPage"}),this.setCurrentPage("1_ProjectSetupPage"),this.updateFinishButton(),this.$q.loading.hide()}};var h=o(1639);const m=(0,h.Z)(u,[["render",i]]),g=m;var S=o(593),E=o(3340),T=o(8339);const P=[{path:"/",component:()=>Promise.all([o.e(736),o.e(486)]).then(o.bind(o,8486)),children:[{path:"/1_ProjectSetupPage",component:()=>Promise.all([o.e(736),o.e(35)]).then(o.bind(o,9035))},{path:"/2_ExampleProjectSelectionPage",name:"examples",component:()=>Promise.all([o.e(736),o.e(963)]).then(o.bind(o,9963))},{path:"/3_ProjectLocationPage",component:()=>Promise.all([o.e(736),o.e(7)]).then(o.bind(o,3007))}]},{path:"/:catchAll(.*)*",component:()=>Promise.all([o.e(736),o.e(494)]).then(o.bind(o,494))}],f=P,_=(0,E.BC)((function(){const e=T.r5,t=(0,T.p7)({scrollBehavior:()=>({left:0,top:0}),routes:f,history:e("")});return t}));async function w(e,t){const a=e(g);a.use(r.Z,t);const n="function"===typeof S.Z?await(0,S.Z)({}):S.Z,{storeKey:i}=await Promise.resolve().then(o.bind(o,593)),c=(0,s.Xl)("function"===typeof _?await _({store:n}):_);return n.$router=c,{app:a,store:n,storeKey:i,router:c}}var I=o(7396),b=o(4328),v=o(6950);const D={config:{},iconSet:I.Z,plugins:{Notify:b.Z,Loading:v.Z}},O="";async function y({app:e,router:t,store:o,storeKey:a},r){let s=!1;const n=e=>{try{return t.resolve(e).href}catch(o){}return Object(e)===e?null:e},i=e=>{if(s=!0,"string"===typeof e&&/^https?:\/\//.test(e))return void(window.location.href=e);const t=n(e);null!==t&&(window.location.href=t,window.location.reload())},c=window.location.href.replace(window.location.origin,"");for(let l=0;!1===s&&l<r.length;l++)try{await r[l]({app:e,router:t,store:o,ssrContext:null,redirect:i,urlPath:c,publicPath:O})}catch(d){return d&&d.url?void i(d.url):void console.error("[Quasar] boot error:",d)}!0!==s&&(e.use(t),e.use(o,a),e.mount("#q-app"))}w(a.ri,D).then((e=>{const[t,a]=void 0!==Promise.allSettled?["allSettled",e=>e.map((e=>{if("rejected"!==e.status)return e.value.default;console.error("[Quasar] boot error:",e.reason)}))]:["all",e=>e.map((e=>e.default))];return Promise[t]([Promise.resolve().then(o.bind(o,6288)),Promise.resolve().then(o.bind(o,1569)),Promise.resolve().then(o.bind(o,6475))]).then((t=>{const o=a(t).filter((e=>"function"===typeof e));y(e,o)}))}))},1569:(e,t,o)=>{o.r(t),o.d(t,{api:()=>s,default:()=>n});var a=o(3340),r=o(7524);const s=r.Z.create({baseURL:""}),n=(0,a.xr)((({app:e})=>{e.config.globalProperties.$axios=r.Z,e.config.globalProperties.$api=s}))},6475:(e,t,o)=>{o.r(t),o.d(t,{bus:()=>s,default:()=>n});var a=o(3340),r=o(687);const s=(0,r.Z)(),n=(0,a.xr)((({app:e})=>{e.config.globalProperties.$bus=s,e.provide("$bus",s)}))},6288:(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});var a=o(3340),r=o(7712);const s={failed:"Action failed",success:"Action was successful"},n={"en-US":s},i=(0,a.xr)((({app:e})=>{const t=(0,r.o)({locale:"en-US",globalInjection:!0,messages:n});e.use(t)}))},8700:(e,t,o)=>{o.d(t,{Z:()=>s});var a=o(7524);const r={"Content-Type":"application/json"},s={get(e,t){return(0,a.Z)({method:"get",url:e,params:t,headers:r})},post(e,t,o){return(0,a.Z)({method:"post",url:e,params:o,data:t,headers:r})},async postProjectSetting(e,t){let o={};o[e]=t;try{await(0,a.Z)({method:"post",url:"/rest/npw/locationsetting",params:o})}catch(r){window.console.log(`locationsetting err: ${r.message}`)}}}},593:(e,t,o)=>{o.d(t,{Z:()=>y});var a=o(3340),r=o(3100);const s={title:"",subTitle:"",currentPage:"",nextPage:"",previousPage:"",backBtnDisabled:!1,nextBtnDisabled:!1,finishBtnDisabled:!1,subTitleColor:"black",stepperStep:1,completedStep:1,projectCreationInProgress:!1,isDark:!1,startedPage:"",os:"",pathSlash:""},n={},i={SET_CURRENT_PAGE:(e,t)=>{switch(e.currentPage=t,e.currentPage){case"1_ProjectSetupPage":e.title="Target, SDK, and Toolchain Selection",e.subTitle="Select the target board, device, SDK, and IDE/toolchain to use for the project.",e.backBtnDisabled=!0,e.nextBtnDisabled=!1,e.previousPage="1_ProjectSetupPage",e.nextPage="2_ExampleProjectSelectionPage",e.stepperStep=1;break;case"2_ExampleProjectSelectionPage":e.title="Example Project Selection",e.subTitle="Select the project template to open in Simplicity IDE.",e.backBtnDisabled=!1,e.nextBtnDisabled=!1,e.previousPage="1_ProjectSetupPage",e.nextPage="3_ProjectLocationPage",e.stepperStep=2,e.completedStep=2;break;case"3_ProjectLocationPage":e.title="Project Configuration",e.subTitle="Select the project name and location.",e.backBtnDisabled=!1,e.nextBtnDisabled=!0,e.previousPage="2_ExampleProjectSelectionPage",e.nextPage="3_ProjectLocationPage",e.stepperStep=3,e.completedStep=5;break}},SET_NEXT_DISABLED(e,t){e.nextBtnDisabled=t},SET_FINISH_DISABLED(e,t){e.finishBtnDisabled=t},SET_CREATION_IN_PROGRESS(e,t){e.projectCreationInProgress=t,e.finishBtnDisabled=t,e.nextBtnDisabled=t},SET_COMPLETED_STEP(e,t){e.completedStep=t,e.stepperStep=t},SET_IS_DARK_THEME(e,t){e.isDark=t},SET_STARTED_PAGE(e,t){e.startedPage=t}},c={setCurrentPage({commit:e},t){e("SET_CURRENT_PAGE",t)},setNextDisabled({commit:e},t){e("SET_NEXT_DISABLED",t)},setFinishDisabled({commit:e},t){e("SET_FINISH_DISABLED",t)},setCreationInProgress({commit:e},t){e("SET_CREATION_IN_PROGRESS",t)},setCompletedStep({commit:e},t){e("SET_COMPLETED_STEP",t)},updateFinishButton({commit:e}){let t=!1;Boolean(this.state.page3.projectName)&&!1!==this.state.page3.isValidLocation||(t=!0),0===this.state.page2.selectedResourceId.length&&(t=!0),e("SET_FINISH_DISABLED",t)},setIsDarkTheme({commit:e},t){e("SET_IS_DARK_THEME",t)},setStartedPage({commit:e},t){e("SET_STARTED_PAGE",t)}},d={namespaced:!0,getters:n,mutations:i,actions:c,state:s},l={targetBoards:[],parts:[],boardModel:null,partModel:null,page1Saved:!1,sdks:[],sdkModel:null,defaultBoardIds:[],defaultSdkId:null,defaultPartId:null,projectType:"",hardwareContext:"",initialized:!1,ideToolchainModel:null,ideToolchainOptions:[],selectedPartId:"",testing:!1},p={},u={SET_BOARDS(e,t){e.targetBoards=t},SET_PARTS(e,t){e.parts=t},SET_SDKS(e,t){e.sdks=t},SAVE_PAGE(e,t){e.boardModel=JSON.parse(JSON.stringify(t.boardModel)),e.partModel=JSON.parse(JSON.stringify(t.partModel)),e.sdkModel=JSON.parse(JSON.stringify(t.sdkModel)),e.ideToolchainModel=t.ideToolchain,e.page1Saved=!0},SET_SDK_ID(e,t){e.defaultSdkId=t},SET_PART_ID(e,t){e.defaultPartId=t},SET_BOARDS_IDS(e,t){e.defaultBoardIds=t.split(",")},SET_CONTEXT(e,t){e.hardwareContext=t.context},SET_INITIALIZED(e,t){e.initialized=t},UPDATE_IDE_TOOLCHAIN(e,t){e.ideToolchainOptions=t,e.ideToolchainOptions.sort((function(e,t){return e.toolchainId&&t.toolchainId?e.label>t.label?1:-1:e.toolchainId&&!t.toolchainId?-1:1})),e.ideToolchainModel=e.ideToolchainOptions.find((e=>!0===e.selected))},SET_IDE_TOOLCHAIN(e,t){e.ideToolchainModel=t},SET_SELECTED_PART_ID(e,t){e.selectedPartId=t},SET_SDK_MODEL(e,t){e.sdkModel=t},SET_PREFS(e,t){"none"!==t[0]&&(e.defaultBoardIds=t[0].split(",")),"none"!==t[1]&&(e.defaultPartId=t[1]),"none"!==t[2]&&(e.defaultSdkId=t[2])}};o(9665);var h=o(7524);const m={async init({commit:e}){if(window.console.log(`store init; initialized: ${this.state.page1.initialized}`),!1===this.state.page1.initialized){e("SET_INITIALIZED",!0);try{await h.Z.post("/rest/npw/init")}catch(t){window.console.log(`init err: ${t.message}`)}}},async fetchBoards({commit:e}){try{window.console.log("Page 1 fetchBoards: /rest/studio/boards/all");const t=await h.Z.get("/rest/studio/boards/all");window.console.log(`Page 1 fetchBoards length: ${t.data.length}`),e("SET_BOARDS",t.data)}catch(t){window.console.log(`Page 1  fetchBoards err: ${t.message}`)}},fetchParts({commit:e},t){var o="/rest/studio/parts",a=g._createParamsForMap(t);return a&&a.length>0&&(o=o+"?"+a),window.console.log(`Page 1 fetchParts: ${o}`),h.Z.get(o).then((t=>(e("SET_PARTS",t.data),t.data))).catch((e=>{window.console.log(`Page 1 fetchParts err: ${e.message}`)}))},async fetchSdks({commit:e},t){var o="/rest/sdk/sdks",a=g._createParamsForMap(t);a&&a.length>0&&(o=o+"?"+a),window.console.log(`Page 1 fetchSdks: ${o}`);try{let t=await h.Z.get(o);return e("SET_SDKS",t.data),t.data}catch(r){window.console.log(`Page 1 fetchSdks err: ${r.message}`)}return null},async savePageState({commit:e},t){e("SAVE_PAGE",t);let o={};t&&("boardModel"in t&&t.boardModel&&(o["boards"]=t.boardModel.map((e=>e.id)).join(",")),"partModel"in t&&t.partModel&&(o["part"]=t.partModel.id),"sdkModel"in t&&t.sdkModel&&(o["sdk"]=t.sdkModel.id));try{await(0,h.Z)({method:"post",url:"/rest/npw/savepage1",params:o})}catch(d){return void window.console.log(`Page 1 savePageState err: ${d.message}`)}if(!t)return;let a=[],r=[],s=[],n=[],i=[];t.partModel&&a.push(t.partModel.id),t.boardModel&&t.boardModel.forEach((e=>r.push(e.id))),t.sdkModel&&s.push(t.sdkModel.id),t.ideToolchain&&(t.ideToolchain.toolchainId&&n.push(t.ideToolchain.toolchainId),t.ideToolchain.ideId&&i.push(t.ideToolchain.ideId));let c={part:a,board:r,sdk:s,toolchain:n,ide:i};try{let t=await(0,h.Z)({method:"post",data:{add:c},url:"/rest/launcher/gethwcontext"});window.console.log(`Page 1 gethwcontext res: ${JSON.stringify(t.data)}`),e("SET_CONTEXT",t.data)}catch(d){return void window.console.log(`Page 1 gethwcontext err: ${d.message}`)}},async setHardwareContext({commit:e},t){if(!t)return;let o=[],a=[],r=[],s=[],n=[];t.partId&&o.push(t.partId),t.boardsId&&t.boardsId.split(",").forEach((e=>a.push(e))),t.sdkId&&r.push(t.sdkId),t.ideToolchain&&(t.ideToolchain.toolchainId&&s.push(t.ideToolchain.toolchainId),t.ideToolchain.ideId&&n.push(t.ideToolchain.ideId));let i={part:o,board:a,sdk:r,toolchain:s,ide:n};try{let t=await(0,h.Z)({method:"post",data:{add:i},url:"/rest/launcher/gethwcontext"});window.console.log(`Page 1 gethwcontext res: ${JSON.stringify(t.data)}`),e("SET_CONTEXT",t.data)}catch(c){return void window.console.log(`Page 1 gethwcontext err: ${c.message}`)}},setDefaultSdkId({commit:e},t){e("SET_SDK_ID",t)},setDefaultPartId({commit:e},t){e("SET_PART_ID",t)},setDefaultBoardIds({commit:e},t){e("SET_BOARDS_IDS",t)},async updateIdeToolchain({commit:e},t){try{let o=await(0,h.Z)({method:"get",url:"/rest/npw?filteridetoolchain="+t});e("UPDATE_IDE_TOOLCHAIN",o.data)}catch(o){window.console.log(`Page 1 updateIdeToolchain err: ${o.message}`)}},setIdeToolchain({commit:e},t){try{let o={};o["id"]=t.id,(0,h.Z)({method:"post",url:"/rest/npw/idetoolchain",params:o}),e("SET_IDE_TOOLCHAIN",t)}catch(o){window.console.log(`Page 1 setIdeToolchain err: ${o.message}`)}},setSelectedPartId({commit:e},t){e("SET_SELECTED_PART_ID",t)},setSdkModel({commit:e},t){e("SET_SDK_MODEL",t)},async loadPrefs({commit:e}){try{const t=await h.Z.get("/rest/npwmanager?loadprefs");e("SET_PREFS",t.data)}catch(t){window.console.log(`Page 1 loadPrefs err: ${t.message}`)}}},g={_createParamsForMap(e){if(e){var t=[];return Object.keys(e).forEach((o=>{t.push(o+"="+e[o])})),t.join("&")}}},S={namespaced:!0,getters:p,mutations:u,actions:m,state:l},E={resource:{userState:"",resources:[],filters:[],totalCount:0},datalist:[],selectedResourceId:"",separatorId:""},T={},P={SET_RESOURCE(e,t){if(e.resource=t,e.resource&&e.resource.resources&&e.resource.resources.length>0){var o;for(e.resource.resources.sort(((e,t)=>e.priority===t.priority?0:e.priority>t.priority?1:-1)),e.separatorId="",o=0;o<t.resources.length-1;o++)1===t.resources[o].priority&&1!==t.resources[o+1].priority&&(e.separatorId=t.resources[o].id);if(""!==e.selectedResourceId){const t=e.resource.resources.find((t=>t.id===e.selectedResourceId));t||(e.selectedResourceId="")}}},SET_DATALIST(e,t){e.datalist=t},SET_SELECTED_RESOURCE(e,t){e.selectedResourceId=t}};var f=o(8700);const _={getResource(e,t){var o="/rest/launcher/resource";return new Promise(((a,r)=>f.Z.get(o,t).then((t=>{e.commit("SET_RESOURCE",t.data),a(t)})).catch((e=>{r(e)}))))},postResource(e,t){const o="/rest/launcher/resource";return new Promise(((a,r)=>f.Z.post(o,t).then((t=>{e.commit("SET_RESOURCE",t.data),a(t)})).catch((e=>{r(e)}))))},postFilter(e,t){var o="/rest/launcher/resource";return new Promise(((a,r)=>f.Z.post(o,t).then((t=>{e.commit("SET_RESOURCE",t.data),a(t)})).catch((e=>{r(e)}))))},postTypedTerm(e,t){var o="/rest/launcher/resource";return new Promise(((a,r)=>f.Z.post(o,t).then((t=>{e.commit("SET_DATALIST",t.data),a(t)})).catch((e=>{r(e)}))))},postSearchTerm(e,t){var o="/rest/launcher/resource";return new Promise(((a,r)=>f.Z.post(o,t).then((t=>{e.commit("SET_RESOURCE",t.data),a(t)})).catch((e=>{r(e)}))))},postLaunchable(e,t){var o="/rest/launcher/launchable";return new Promise(((e,a)=>f.Z.post(o,t).then((t=>{e(t)})).catch((e=>{a(e)}))))},async setSelectedResource(e,t){if(this.state.page2.selectedResourceId!==t){e.commit("SET_SELECTED_RESOURCE",t);let o={};o["templateId"]=this.state.page2.selectedResourceId,await(0,h.Z)({method:"post",url:"/rest/npw/template",params:o})}}},w={namespaced:!0,getters:T,mutations:P,actions:_,state:E},I={projectName:"",useDefaultLocation:!0,projectLocation:"",workspaceLocation:null,importMode:"",importModes:[],isSolution:!1,subProjects:[],isValidLocation:!0,error:"",testing:!1},b={},v={SET_PAGE(e,t){e.projectName=t.projectName,e.projectLocation=t.projectLocation,!0===Boolean(e.projectLocation)&&e.projectLocation.length>1&&e.projectLocation[e.projectLocation.length-1]===e.pathSlash&&(e.projectLocation=e.projectLocation.substring(0,e.projectLocation.length-1)),e.useDefaultLocation=t.useDefaultLocation,e.importModes=t.importModes,e.importMode=t.importMode,e.subProjects=t.subProjects,e.isSolution=t.isSolution,t.workspaceLocation&&(e.workspaceLocation=t.workspaceLocation)},FORM_VALIDATED:(e,t)=>{e.error=t.error,e.subProjects=t.subProjects},SET_PROJECT_NAME:(e,t)=>{e.projectName=t},SET_SUB_PROJECT_NAME:(e,t)=>{e.subProjects[t.id].name=t.name},SET_USE_DEFAULT_LOCATION:(e,t)=>{e.useDefaultLocation=t},SET_PROJECT_LOCATION:(e,t)=>{e.projectLocation=t},SET_IMPORT_MODE:(e,t)=>{e.importMode=t},SET_IS_VALID_LOCATION:(e,t)=>{e.isValidLocation=t},SET_OS(e,t){switch(e.os=t,t){case"windows":e.pathSlash="\\";break;case"linux":case"mac":e.pathSlash="/";break}}},D={async fetchPage({commit:e}){try{let t="/rest/npw?fetchpage3",o=await f.Z.get(t);e("SET_PAGE",o.data)}catch(t){window.console.log(`fetchpage3 err: ${t.message}`)}},postValidateInput(e,t){var o="/rest/npw/validateinput";return new Promise(((a,r)=>f.Z.post(o,t).then((t=>{e.commit("FORM_VALIDATED",t.data),a(t)})).catch((e=>{r(e)}))))},postProjectName(e,t){let o={name:t.name};var a="/rest/npw/locationsetting";return new Promise(((r,s)=>f.Z.post(a,t,o).then((o=>{t.isSubProject?e.commit("SET_SUB_PROJECT_NAME",t):e.commit("SET_PROJECT_NAME",t.name),r(o)})).catch((e=>{s(e)}))))},setProjectName({commit:e},t){e("SET_PROJECT_NAME",t)},setUseDefaultLocation({commit:e},t){e("SET_USE_DEFAULT_LOCATION",t)},setProjectLocation({commit:e},t){e("SET_PROJECT_LOCATION",t)},setImportMode({commit:e},t){e("SET_IMPORT_MODE",t)},setIsValidLocation({commit:e},t){e("SET_IS_VALID_LOCATION",t)},setOs({commit:e},t){e("SET_OS",t)}},O={namespaced:!0,getters:b,mutations:v,actions:D,state:I},y=(0,a.h)((function(){const e=(0,r.MT)({modules:{navigator:d,page1:S,page2:w,page3:O},strict:!1});return e}))}},t={};function o(a){var r=t[a];if(void 0!==r)return r.exports;var s=t[a]={exports:{}};return e[a].call(s.exports,s,s.exports,o),s.exports}o.m=e,(()=>{var e=[];o.O=(t,a,r,s)=>{if(!a){var n=1/0;for(l=0;l<e.length;l++){for(var[a,r,s]=e[l],i=!0,c=0;c<a.length;c++)(!1&s||n>=s)&&Object.keys(o.O).every((e=>o.O[e](a[c])))?a.splice(c--,1):(i=!1,s<n&&(n=s));if(i){e.splice(l--,1);var d=r();void 0!==d&&(t=d)}}return t}s=s||0;for(var l=e.length;l>0&&e[l-1][2]>s;l--)e[l]=e[l-1];e[l]=[a,r,s]}})(),(()=>{o.n=e=>{var t=e&&e.__esModule?()=>e["default"]:()=>e;return o.d(t,{a:t}),t}})(),(()=>{o.d=(e,t)=>{for(var a in t)o.o(t,a)&&!o.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}})(),(()=>{o.f={},o.e=e=>Promise.all(Object.keys(o.f).reduce(((t,a)=>(o.f[a](e,t),t)),[]))})(),(()=>{o.u=e=>"js/"+e+"."+{7:"b7844fc1",35:"d46d8ff1",486:"16368bfc",494:"e2109568",963:"90a6c6a7"}[e]+".js"})(),(()=>{o.miniCssF=e=>"css/"+e+"."+{7:"6490e30e",35:"4d8b2d4c",486:"1c860b75",963:"f1cfcab3"}[e]+".css"})(),(()=>{o.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{var e={},t="new_project_wizard:";o.l=(a,r,s,n)=>{if(e[a])e[a].push(r);else{var i,c;if(void 0!==s)for(var d=document.getElementsByTagName("script"),l=0;l<d.length;l++){var p=d[l];if(p.getAttribute("src")==a||p.getAttribute("data-webpack")==t+s){i=p;break}}i||(c=!0,i=document.createElement("script"),i.charset="utf-8",i.timeout=120,o.nc&&i.setAttribute("nonce",o.nc),i.setAttribute("data-webpack",t+s),i.src=a),e[a]=[r];var u=(t,o)=>{i.onerror=i.onload=null,clearTimeout(h);var r=e[a];if(delete e[a],i.parentNode&&i.parentNode.removeChild(i),r&&r.forEach((e=>e(o))),t)return t(o)},h=setTimeout(u.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=u.bind(null,i.onerror),i.onload=u.bind(null,i.onload),c&&document.head.appendChild(i)}}})(),(()=>{o.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{o.p=""})(),(()=>{if("undefined"!==typeof document){var e=(e,t,o,a,r)=>{var s=document.createElement("link");s.rel="stylesheet",s.type="text/css";var n=o=>{if(s.onerror=s.onload=null,"load"===o.type)a();else{var n=o&&("load"===o.type?"missing":o.type),i=o&&o.target&&o.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+i+")");c.code="CSS_CHUNK_LOAD_FAILED",c.type=n,c.request=i,s.parentNode.removeChild(s),r(c)}};return s.onerror=s.onload=n,s.href=t,o?o.parentNode.insertBefore(s,o.nextSibling):document.head.appendChild(s),s},t=(e,t)=>{for(var o=document.getElementsByTagName("link"),a=0;a<o.length;a++){var r=o[a],s=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(s===e||s===t))return r}var n=document.getElementsByTagName("style");for(a=0;a<n.length;a++){r=n[a],s=r.getAttribute("data-href");if(s===e||s===t)return r}},a=a=>new Promise(((r,s)=>{var n=o.miniCssF(a),i=o.p+n;if(t(n,i))return r();e(a,i,null,r,s)})),r={143:0};o.f.miniCss=(e,t)=>{var o={7:1,35:1,486:1,963:1};r[e]?t.push(r[e]):0!==r[e]&&o[e]&&t.push(r[e]=a(e).then((()=>{r[e]=0}),(t=>{throw delete r[e],t})))}}})(),(()=>{var e={143:0};o.f.j=(t,a)=>{var r=o.o(e,t)?e[t]:void 0;if(0!==r)if(r)a.push(r[2]);else{var s=new Promise(((o,a)=>r=e[t]=[o,a]));a.push(r[2]=s);var n=o.p+o.u(t),i=new Error,c=a=>{if(o.o(e,t)&&(r=e[t],0!==r&&(e[t]=void 0),r)){var s=a&&("load"===a.type?"missing":a.type),n=a&&a.target&&a.target.src;i.message="Loading chunk "+t+" failed.\n("+s+": "+n+")",i.name="ChunkLoadError",i.type=s,i.request=n,r[1](i)}};o.l(n,c,"chunk-"+t,t)}},o.O.j=t=>0===e[t];var t=(t,a)=>{var r,s,[n,i,c]=a,d=0;if(n.some((t=>0!==e[t]))){for(r in i)o.o(i,r)&&(o.m[r]=i[r]);if(c)var l=c(o)}for(t&&t(a);d<n.length;d++)s=n[d],o.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return o.O(l)},a=globalThis["webpackChunknew_project_wizard"]=globalThis["webpackChunknew_project_wizard"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})();var a=o.O(void 0,[736],(()=>o(3968)));a=o.O(a)})();