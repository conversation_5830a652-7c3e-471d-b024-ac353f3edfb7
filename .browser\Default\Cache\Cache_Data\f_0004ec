{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>GATT Server<span id=\"gatt-server\" class=\"self-anchor\"><a class=\"perm\" href=\"#gatt-server\">#</a></span></h1><p style=\"color:inherit\">GATT Server. </p><p style=\"color:inherit\">These commands and events are used for accessing to the local GATT server and database. </p><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-attribute-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_attribute_value</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-user-read-request\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_user_read_request</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-user-write-request\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_user_write_request</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-characteristic-status\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_characteristic_status</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-execute-write-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_execute_write_completed</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-indication-timeout\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_indication_timeout</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-notification-tx-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_notification_tx_completed</a></p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-client-configuration-t\">sl_bt_gatt_server_client_configuration_t</a> {</div><div class=\"enum\">sl_bt_gatt_server_disable = 0x0</div><div class=\"enum\">sl_bt_gatt_server_notification = 0x1</div><div class=\"enum\">sl_bt_gatt_server_indication = 0x2</div><div class=\"enum\">sl_bt_gatt_server_notification_and_indication = 0x3</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values define whether the server is to sent notifications or indications to a remote GATT server. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-characteristic-status-flag-t\">sl_bt_gatt_server_characteristic_status_flag_t</a> {</div><div class=\"enum\">sl_bt_gatt_server_client_config = 0x1</div><div class=\"enum\">sl_bt_gatt_server_confirmation = 0x2</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values describe whether the characteristic client configuration was changed or whether a characteristic confirmation was received. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-set-max-mtu\">sl_bt_gatt_server_set_max_mtu</a>(uint16_t max_mtu, uint16_t *max_mtu_out)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-get-mtu\">sl_bt_gatt_server_get_mtu</a>(uint8_t connection, uint16_t *mtu)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-find-attribute\">sl_bt_gatt_server_find_attribute</a>(uint16_t start, size_t type_len, const uint8_t *type, uint16_t *attribute)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-read-attribute-value\">sl_bt_gatt_server_read_attribute_value</a>(uint16_t attribute, uint16_t offset, size_t max_value_size, size_t *value_len, uint8_t *value)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-read-attribute-type\">sl_bt_gatt_server_read_attribute_type</a>(uint16_t attribute, size_t max_type_size, size_t *type_len, uint8_t *type)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-write-attribute-value\">sl_bt_gatt_server_write_attribute_value</a>(uint16_t attribute, uint16_t offset, size_t value_len, const uint8_t *value)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-send-user-read-response\">sl_bt_gatt_server_send_user_read_response</a>(uint8_t connection, uint16_t characteristic, uint8_t att_errorcode, size_t value_len, const uint8_t *value, uint16_t *sent_len)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-send-user-write-response\">sl_bt_gatt_server_send_user_write_response</a>(uint8_t connection, uint16_t characteristic, uint8_t att_errorcode)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-send-notification\">sl_bt_gatt_server_send_notification</a>(uint8_t connection, uint16_t characteristic, size_t value_len, const uint8_t *value)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-send-indication\">sl_bt_gatt_server_send_indication</a>(uint8_t connection, uint16_t characteristic, size_t value_len, const uint8_t *value)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-notify-all\">sl_bt_gatt_server_notify_all</a>(uint16_t characteristic, size_t value_len, const uint8_t *value)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-read-client-configuration\">sl_bt_gatt_server_read_client_configuration</a>(uint8_t connection, uint16_t characteristic, uint16_t *client_config_flags)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-send-user-prepare-write-response\">sl_bt_gatt_server_send_user_prepare_write_response</a>(uint8_t connection, uint16_t characteristic, uint8_t att_errorcode, uint16_t offset, size_t value_len, const uint8_t *value)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-set-capabilities\">sl_bt_gatt_server_set_capabilities</a>(uint32_t caps, uint32_t reserved)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-enable-capabilities\">sl_bt_gatt_server_enable_capabilities</a>(uint32_t caps)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-disable-capabilities\">sl_bt_gatt_server_disable_capabilities</a>(uint32_t caps)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-get-enabled-capabilities\">sl_bt_gatt_server_get_enabled_capabilities</a>(uint32_t *caps)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-server-read-client-supported-features\">sl_bt_gatt_server_read_client_supported_features</a>(uint8_t connection, uint8_t *client_features)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-set-max-mtu-id\">sl_bt_cmd_gatt_server_set_max_mtu_id</a> 0x0a0a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-get-mtu-id\">sl_bt_cmd_gatt_server_get_mtu_id</a> 0x0b0a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-find-attribute-id\">sl_bt_cmd_gatt_server_find_attribute_id</a> 0x060a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-read-attribute-value-id\">sl_bt_cmd_gatt_server_read_attribute_value_id</a> 0x000a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-read-attribute-type-id\">sl_bt_cmd_gatt_server_read_attribute_type_id</a> 0x010a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-write-attribute-value-id\">sl_bt_cmd_gatt_server_write_attribute_value_id</a> 0x020a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-send-user-read-response-id\">sl_bt_cmd_gatt_server_send_user_read_response_id</a> 0x030a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-send-user-write-response-id\">sl_bt_cmd_gatt_server_send_user_write_response_id</a> 0x040a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-send-notification-id\">sl_bt_cmd_gatt_server_send_notification_id</a> 0x0f0a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-send-indication-id\">sl_bt_cmd_gatt_server_send_indication_id</a> 0x100a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-notify-all-id\">sl_bt_cmd_gatt_server_notify_all_id</a> 0x110a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-read-client-configuration-id\">sl_bt_cmd_gatt_server_read_client_configuration_id</a> 0x120a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-send-user-prepare-write-response-id\">sl_bt_cmd_gatt_server_send_user_prepare_write_response_id</a> 0x140a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-set-capabilities-id\">sl_bt_cmd_gatt_server_set_capabilities_id</a> 0x080a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-enable-capabilities-id\">sl_bt_cmd_gatt_server_enable_capabilities_id</a> 0x0c0a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-disable-capabilities-id\">sl_bt_cmd_gatt_server_disable_capabilities_id</a> 0x0d0a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-get-enabled-capabilities-id\">sl_bt_cmd_gatt_server_get_enabled_capabilities_id</a> 0x0e0a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-server-read-client-supported-features-id\">sl_bt_cmd_gatt_server_read_client_supported_features_id</a> 0x150a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-set-max-mtu-id\">sl_bt_rsp_gatt_server_set_max_mtu_id</a> 0x0a0a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-get-mtu-id\">sl_bt_rsp_gatt_server_get_mtu_id</a> 0x0b0a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-find-attribute-id\">sl_bt_rsp_gatt_server_find_attribute_id</a> 0x060a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-read-attribute-value-id\">sl_bt_rsp_gatt_server_read_attribute_value_id</a> 0x000a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-read-attribute-type-id\">sl_bt_rsp_gatt_server_read_attribute_type_id</a> 0x010a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-write-attribute-value-id\">sl_bt_rsp_gatt_server_write_attribute_value_id</a> 0x020a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-send-user-read-response-id\">sl_bt_rsp_gatt_server_send_user_read_response_id</a> 0x030a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-send-user-write-response-id\">sl_bt_rsp_gatt_server_send_user_write_response_id</a> 0x040a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-send-notification-id\">sl_bt_rsp_gatt_server_send_notification_id</a> 0x0f0a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-send-indication-id\">sl_bt_rsp_gatt_server_send_indication_id</a> 0x100a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-notify-all-id\">sl_bt_rsp_gatt_server_notify_all_id</a> 0x110a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-read-client-configuration-id\">sl_bt_rsp_gatt_server_read_client_configuration_id</a> 0x120a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-send-user-prepare-write-response-id\">sl_bt_rsp_gatt_server_send_user_prepare_write_response_id</a> 0x140a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-set-capabilities-id\">sl_bt_rsp_gatt_server_set_capabilities_id</a> 0x080a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-enable-capabilities-id\">sl_bt_rsp_gatt_server_enable_capabilities_id</a> 0x0c0a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-disable-capabilities-id\">sl_bt_rsp_gatt_server_disable_capabilities_id</a> 0x0d0a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-get-enabled-capabilities-id\">sl_bt_rsp_gatt_server_get_enabled_capabilities_id</a> 0x0e0a0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-server-read-client-supported-features-id\">sl_bt_rsp_gatt_server_read_client_supported_features_id</a> 0x150a0020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>sl_bt_gatt_server_client_configuration_t<span id=\"sl-bt-gatt-server-client-configuration-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-client-configuration-t\">#</a></span></h3><blockquote>sl_bt_gatt_server_client_configuration_t</blockquote><p style=\"color:inherit\">These values define whether the server is to sent notifications or indications to a remote GATT server. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_gatt_server_disable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Disable notifications and indications. </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_server_notification</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) The characteristic value shall be notified. </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_server_indication</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) The characteristic value shall be indicated. </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_server_notification_and_indication</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x3) The characteristic value notification and indication are enabled, application decides which one to send. </p></td></tr></tbody></table><br><div>Definition at line <code>9529</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_characteristic_status_flag_t<span id=\"sl-bt-gatt-server-characteristic-status-flag-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-characteristic-status-flag-t\">#</a></span></h3><blockquote>sl_bt_gatt_server_characteristic_status_flag_t</blockquote><p style=\"color:inherit\">These values describe whether the characteristic client configuration was changed or whether a characteristic confirmation was received. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_gatt_server_client_config</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Characteristic client configuration has been changed. </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_server_confirmation</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) Characteristic confirmation has been received. </p></td></tr></tbody></table><br><div>Definition at line <code>9553</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_gatt_server_set_max_mtu<span id=\"sl-bt-gatt-server-set-max-mtu\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-set-max-mtu\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_set_max_mtu (uint16_t max_mtu, uint16_t * max_mtu_out)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">max_mtu</td><td><p style=\"color:inherit\">Maximum size of Message Transfer Units (MTU) allowed</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 23 to 250</p></li></ul><p style=\"color:inherit\">Default: 247 </p></td></tr><tr><td>[out]</td><td class=\"paramname\">max_mtu_out</td><td><p style=\"color:inherit\">The maximum ATT_MTU selected by the system if this command succeeded</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set the maximum size of ATT Message Transfer Units (MTU). The functionality is the same as <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-set-max-mtu\" target=\"_blank\" rel=\"\">sl_bt_gatt_set_max_mtu</a> and this setting applies to both GATT client and server. If the given value is too large according to the maximum BGAPI payload size, the system will select the maximum possible value as the maximum ATT_MTU. If the maximum ATT_MTU is larger than 23, the GATT client in the stack will automatically send an MTU exchange request after a Bluetooth connection was established.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>9828</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_get_mtu<span id=\"sl-bt-gatt-server-get-mtu\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-get-mtu\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_get_mtu (uint8_t connection, uint16_t * mtu)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[out]</td><td class=\"paramname\">mtu</td><td><p style=\"color:inherit\">The maximum ATT_MTU used by the connection</p></td></tr></tbody></table></div><p style=\"color:inherit\">Get the size of ATT Message Transfer Units (MTU) for a connection.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>9841</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_find_attribute<span id=\"sl-bt-gatt-server-find-attribute\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-find-attribute\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_find_attribute (uint16_t start, size_t type_len, const uint8_t * type, uint16_t * attribute)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">start</td><td><p style=\"color:inherit\">Search start handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">type_len</td><td><p style=\"color:inherit\">Length of data in <code>type</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">type</td><td><p style=\"color:inherit\">The attribute type UUID </p></td></tr><tr><td>[out]</td><td class=\"paramname\">attribute</td><td><p style=\"color:inherit\">Attribute handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Find attributes of a certain type from a local GATT database. The type is usually given as a 16-bit or 128-bit UUID in little endian format.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>9856</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_read_attribute_value<span id=\"sl-bt-gatt-server-read-attribute-value\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-read-attribute-value\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_read_attribute_value (uint16_t attribute, uint16_t offset, size_t max_value_size, size_t * value_len, uint8_t * value)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">attribute</td><td><p style=\"color:inherit\">Attribute handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">offset</td><td><p style=\"color:inherit\">Value offset </p></td></tr><tr><td>[in]</td><td class=\"paramname\">max_value_size</td><td><p style=\"color:inherit\">Size of output buffer passed in <code>value</code></p></td></tr><tr><td>[out]</td><td class=\"paramname\">value_len</td><td><p style=\"color:inherit\">On return, set to the length of output data written to <code>value</code></p></td></tr><tr><td>[out]</td><td class=\"paramname\">value</td><td><p style=\"color:inherit\">The attribute value</p></td></tr></tbody></table></div><p style=\"color:inherit\">Read the value of an attribute from a local GATT database. Only (maximum BGAPI payload size - 3) amount of data can be read at once. The application can continue reading with increased offset value if it receives (maximum BGAPI payload size - 3) number of bytes.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>9878</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_read_attribute_type<span id=\"sl-bt-gatt-server-read-attribute-type\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-read-attribute-type\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_read_attribute_type (uint16_t attribute, size_t max_type_size, size_t * type_len, uint8_t * type)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">attribute</td><td><p style=\"color:inherit\">Attribute handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">max_type_size</td><td><p style=\"color:inherit\">Size of output buffer passed in <code>type</code></p></td></tr><tr><td>[out]</td><td class=\"paramname\">type_len</td><td><p style=\"color:inherit\">On return, set to the length of output data written to <code>type</code></p></td></tr><tr><td>[out]</td><td class=\"paramname\">type</td><td><p style=\"color:inherit\">The attribute type UUID</p></td></tr></tbody></table></div><p style=\"color:inherit\">Read the type of an attribute from a local GATT database. The type is a UUID, usually 16 or 128 bits long in little endian format.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>9898</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_write_attribute_value<span id=\"sl-bt-gatt-server-write-attribute-value\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-write-attribute-value\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_write_attribute_value (uint16_t attribute, uint16_t offset, size_t value_len, const uint8_t * value)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">attribute</td><td><p style=\"color:inherit\">Attribute handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">offset</td><td><p style=\"color:inherit\">Value offset </p></td></tr><tr><td>[in]</td><td class=\"paramname\">value_len</td><td><p style=\"color:inherit\">Length of data in <code>value</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">value</td><td><p style=\"color:inherit\">Value</p></td></tr></tbody></table></div><p style=\"color:inherit\">Write the value of an attribute in the local GATT database. Writing the value of a characteristic of the local GATT database will not trigger notifications or indications to the remote GATT client if the characteristic has a property to indicate or notify and the client has enabled notification or indication. Notifications and indications are sent to the remote GATT client using <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt-server#sl-bt-gatt-server-send-notification\" target=\"_blank\" rel=\"\">sl_bt_gatt_server_send_notification</a> or <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt-server#sl-bt-gatt-server-send-indication\" target=\"_blank\" rel=\"\">sl_bt_gatt_server_send_indication</a> commands.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>9921</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_send_user_read_response<span id=\"sl-bt-gatt-server-send-user-read-response\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-send-user-read-response\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_send_user_read_response (uint8_t connection, uint16_t characteristic, uint8_t att_errorcode, size_t value_len, const uint8_t * value, uint16_t * sent_len)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">GATT characteristic handle received in the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-user-read-request\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_user_read_request</a> event. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">att_errorcode</td><td><p style=\"color:inherit\">Attribute protocol error code</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> No error</p></li><li><p style=\"color:inherit\"><strong>Non-zero:</strong> See Bluetooth specification, Host volume, Attribute Protocol, Error Codes table. </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">value_len</td><td><p style=\"color:inherit\">Length of data in <code>value</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">value</td><td><p style=\"color:inherit\">Characteristic value to send to the GATT client. Ignored if att_errorcode is not 0. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">sent_len</td><td><p style=\"color:inherit\">The length of data sent to the remote GATT client</p></td></tr></tbody></table></div><p style=\"color:inherit\">Send a response to a <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-user-read-request\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_user_read_request</a> event. The response needs to be sent within 30 seconds, otherwise no more GATT transactions are allowed by the remote side. If attr_errorcode is set to 0, the characteristic value is sent to the remote GATT client in the standard way. Other attr_errorcode values will cause the local GATT server to send an attribute protocol error response instead of the actual data. Maximum number of bytes this command can send depends on the value of parameter att_opcode in <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-user-read-request\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_user_read_request</a> event:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">ATT_MTU - 1 if the opcode is <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-read-request\" target=\"_blank\" rel=\"\">sl_bt_gatt_read_request</a> or <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-read-blob-request\" target=\"_blank\" rel=\"\">sl_bt_gatt_read_blob_request</a></p></li><li><p style=\"color:inherit\">ATT_MTU - 4 if the opcode is <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-read-by-type-request\" target=\"_blank\" rel=\"\">sl_bt_gatt_read_by_type_request</a></p></li></ul><p style=\"color:inherit\">If the data length in <code>value</code> exceeds the limit, the first maximum number of bytes will be sent and rest data is ignored. The actual number of bytes that was sent is retruned in the response of this command.</p><p style=\"color:inherit\">The client will continue reading by sending a new read blob request with an increased offset value if it receives the maximum amount of attribute data the read respond packet can contain.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>9963</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_send_user_write_response<span id=\"sl-bt-gatt-server-send-user-write-response\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-send-user-write-response\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_send_user_write_response (uint8_t connection, uint16_t characteristic, uint8_t att_errorcode)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">GATT characteristic handle received in the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-user-write-request\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_user_write_request</a> event </p></td></tr><tr><td>[in]</td><td class=\"paramname\">att_errorcode</td><td><p style=\"color:inherit\">Attribute protocol error code</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> No error</p></li><li><p style=\"color:inherit\"><strong>Non-zero:</strong> See Bluetooth specification, Host volume, Attribute Protocol, Error Codes table.</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Send a response to a <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-user-write-request\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_user_write_request</a> event when parameter <code>att_opcode</code> in the event is <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-write-request\" target=\"_blank\" rel=\"\">sl_bt_gatt_write_request</a> or <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-execute-write-request\" target=\"_blank\" rel=\"\">sl_bt_gatt_execute_write_request</a> (see <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-att-opcode-t\" target=\"_blank\" rel=\"\">sl_bt_gatt_att_opcode_t</a>). The response needs to be sent within 30 seconds, otherwise no more GATT transactions are allowed by the remote side. When responding to <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-execute-write-request\" target=\"_blank\" rel=\"\">sl_bt_gatt_execute_write_request</a>, the value of parameter <code>characteristic</code> is ignored. If attr_errorcode is set to 0, the ATT protocol's write response is sent to indicate to the remote GATT client that the write operation was processed successfully. Other values will cause the local GATT server to send an ATT protocol error response.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>9994</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_send_notification<span id=\"sl-bt-gatt-server-send-notification\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-send-notification\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_send_notification (uint8_t connection, uint16_t characteristic, size_t value_len, const uint8_t * value)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">A handle of the connection over which the notification is sent. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">Characteristic handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">value_len</td><td><p style=\"color:inherit\">Length of data in <code>value</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">value</td><td><p style=\"color:inherit\">Value to be notified</p></td></tr></tbody></table></div><p style=\"color:inherit\">Send a notification to a remote GATT client. At most, ATT_MTU - 3 number of bytes can be sent in a notification. An error SL_STATUS_COMMAND_TOO_LONG is returned if the value length exceeds ATT_MTU - 3.</p><p style=\"color:inherit\">A notification is sent only if the client has enabled it by setting the corresponding flag to the Client Characteristic Configuration descriptor. The error SL_STATUS_INVALID_PARAMETER is returned if the characteristic does not have the notification property. The error SL_STATUS_INVALID_STATE is returned if the client has not enabled the notification.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>10019</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_send_indication<span id=\"sl-bt-gatt-server-send-indication\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-send-indication\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_send_indication (uint8_t connection, uint16_t characteristic, size_t value_len, const uint8_t * value)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">A handle of the connection over which the indication is sent. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">Characteristic handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">value_len</td><td><p style=\"color:inherit\">Length of data in <code>value</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">value</td><td><p style=\"color:inherit\">Value to be indicated</p></td></tr></tbody></table></div><p style=\"color:inherit\">Send an indication to a remote GATT client. At most, ATT_MTU - 3 number of bytes can be sent in an indication. An error SL_STATUS_COMMAND_TOO_LONG is returned if the value length exceeds ATT_MTU - 3.</p><p style=\"color:inherit\">An indication is sent only if the client has enabled it by setting the corresponding flag to the Client Characteristic Configuration descriptor. The error SL_STATUS_INVALID_PARAMETER is returned if the characteristic does not have the indication property. The error SL_STATUS_INVALID_STATE is returned if the client has not enabled the indication.</p><p style=\"color:inherit\">A new indication to a GATT client can't be sent until an outstanding indication procedure with the same client has completed. The procedure is completed when a confirmation from the client is received. The confirmation is indicated by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-characteristic-status\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_characteristic_status</a>.</p><p style=\"color:inherit\">The error SL_STATUS_IN_PROGRESS is returned if an indication procedure with the same client is outstanding. Always wait for confirmation for previous indication before sending a new indication.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-characteristic-status\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_characteristic_status</a> - This event is triggered after the confirmation from the client is received.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-indication-timeout\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_indication_timeout</a> - This event indicates confirmation from the remote GATT client has not been received within 30 seconds after an indication was sent. Further GATT transactions over this connection are not allowed by the stack. </p></li></ul><br><div>Definition at line <code>10062</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_notify_all<span id=\"sl-bt-gatt-server-notify-all\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-notify-all\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_notify_all (uint16_t characteristic, size_t value_len, const uint8_t * value)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">Characteristic handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">value_len</td><td><p style=\"color:inherit\">Length of data in <code>value</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">value</td><td><p style=\"color:inherit\">Value to be notified or indicated</p></td></tr></tbody></table></div><p style=\"color:inherit\">Send notifications or indications to all connected remote GATT clients. At most, ATT_MTU - 3 number of bytes can be sent in a notification or indication. If the value length exceeds the limit on a connection, the first ATT_MTU - 3 bytes will be sent and rest of data is ignored.</p><p style=\"color:inherit\">A notification or indication is sent only if the client has enabled it by setting the corresponding flag to the Client Characteristic Configuration descriptor. If the Client Characteristic Configuration descriptor supports both notifications and indications, the stack will always send a notification even when the client has enabled both.</p><p style=\"color:inherit\">A new indication to a GATT client can't be sent until an outstanding indication procedure with the same client has completed, and the operation will continue for the next client. The procedure is completed when a confirmation from the client is received. The confirmation is indicated by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-characteristic-status\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_characteristic_status</a>.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>10093</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_read_client_configuration<span id=\"sl-bt-gatt-server-read-client-configuration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-read-client-configuration\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_read_client_configuration (uint8_t connection, uint16_t characteristic, uint16_t * client_config_flags)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">A handle of the connection to a remote client. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">Characteristic handle </p></td></tr><tr><td>[out]</td><td class=\"paramname\">client_config_flags</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt-server#sl-bt-gatt-server-client-configuration-t\" target=\"_blank\" rel=\"\">sl_bt_gatt_server_client_configuration_t</a>. Client characteristic configuration of a remote client.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Read client characteristic configuration of a remote GATT client.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>10110</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_send_user_prepare_write_response<span id=\"sl-bt-gatt-server-send-user-prepare-write-response\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-send-user-prepare-write-response\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_send_user_prepare_write_response (uint8_t connection, uint16_t characteristic, uint8_t att_errorcode, uint16_t offset, size_t value_len, const uint8_t * value)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">GATT characteristic handle. This value is normally received from the gatt_characteristic event. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">att_errorcode</td><td><p style=\"color:inherit\">Attribute protocol error code</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> No error</p></li><li><p style=\"color:inherit\"><strong>Non-zero:</strong> See Bluetooth specification, Host volume, Attribute Protocol, Error Codes table. </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">offset</td><td><p style=\"color:inherit\">Value offset </p></td></tr><tr><td>[in]</td><td class=\"paramname\">value_len</td><td><p style=\"color:inherit\">Length of data in <code>value</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">value</td><td><p style=\"color:inherit\">Value</p></td></tr></tbody></table></div><p style=\"color:inherit\">Send a response to a <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-user-write-request\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_user_write_request</a> event when parameter <code>att_opcode</code> in the event is <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-prepare-write-request\" target=\"_blank\" rel=\"\">sl_bt_gatt_prepare_write_request</a> (see <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-att-opcode-t\" target=\"_blank\" rel=\"\">sl_bt_gatt_att_opcode_t</a>). The response needs to be sent within 30 seconds, otherwise no more GATT transactions are allowed by the remote side. If <code>att_errorcode</code> is set to 0, the ATT protocol's prepare write response is sent to indicate to the remote GATT client that the write operation was processed successfully. Other values will cause the local GATT server to send an ATT protocol error response. The application should set values of parameters <code>offset</code> and <code>value</code> to identical values from the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-server-user-write-request\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_server_user_write_request</a> event. The values will be verified on the client side in case the request is a reliable write (by Bluetooth Core Specification Volume 3, Part G, 4.9.5).</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>10143</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_set_capabilities<span id=\"sl-bt-gatt-server-set-capabilities\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-set-capabilities\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_set_capabilities (uint32_t caps, uint32_t reserved)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">caps</td><td><p style=\"color:inherit\">Bit flags of capabilities to reset. Value 0 sets the default database capabilities. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">reserved</td><td><p style=\"color:inherit\">Use the value 0 on this reserved field. Do not use none-zero values because they are reserved for future use.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Reset capabilities that should be enabled by the GATT database. A service is visible to remote GATT clients if at least one of its capabilities is enabled. The same applies to a characteristic and its attributes. Capability identifiers and their corresponding bit flag values are in the auto-generated database header file. See UG118: Blue Gecko Bluetooth Profile Toolkit Developer's Guide for how to declare capabilities in the GATT database.</p><p style=\"color:inherit\">Changing the capabilities of a database effectively causes a database change (attributes being added or removed) from a remote GATT client point of view. If the database has a Generic Attribute service and Service Changed characteristic, the stack will monitor the local database change status and manage service changed indications for a GATT client that has enabled the indication configuration of the Service Changed characteristic.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>10174</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_enable_capabilities<span id=\"sl-bt-gatt-server-enable-capabilities\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-enable-capabilities\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_enable_capabilities (uint32_t caps)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">caps</td><td><p style=\"color:inherit\">Capabilities to enable</p></td></tr></tbody></table></div><p style=\"color:inherit\">Enable additional capabilities in the local GATT database. Already enabled capabilities keep unchanged after this command. See <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt-server#sl-bt-gatt-server-set-capabilities\" target=\"_blank\" rel=\"\">sl_bt_gatt_server_set_capabilities</a> for more information.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>10188</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_disable_capabilities<span id=\"sl-bt-gatt-server-disable-capabilities\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-disable-capabilities\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_disable_capabilities (uint32_t caps)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">caps</td><td><p style=\"color:inherit\">Capabilities to disable</p></td></tr></tbody></table></div><p style=\"color:inherit\">Disable the given capabilities in the local GATT database. See <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt-server#sl-bt-gatt-server-set-capabilities\" target=\"_blank\" rel=\"\">sl_bt_gatt_server_set_capabilities</a> for more information.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>10200</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_get_enabled_capabilities<span id=\"sl-bt-gatt-server-get-enabled-capabilities\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-get-enabled-capabilities\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_get_enabled_capabilities (uint32_t * caps)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[out]</td><td class=\"paramname\">caps</td><td><p style=\"color:inherit\">Enabled capabilities</p></td></tr></tbody></table></div><p style=\"color:inherit\">Get capabilities currently enabled in the local GATT database.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>10211</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_server_read_client_supported_features<span id=\"sl-bt-gatt-server-read-client-supported-features\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-server-read-client-supported-features\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_server_read_client_supported_features (uint8_t connection, uint8_t * client_features)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">A handle of the connection to a remote client. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">client_features</td><td><p style=\"color:inherit\">Bit field describing client supported features of a remote client. See Bluetooth specification Vol 3, Part G, 7.2 for the values.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Read client supported features of a remote GATT client.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>10225</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_gatt_server_set_max_mtu_id<span id=\"sl-bt-cmd-gatt-server-set-max-mtu-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-set-max-mtu-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_set_max_mtu_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0a0a0020</pre><br><div>Definition at line <code>9488</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_get_mtu_id<span id=\"sl-bt-cmd-gatt-server-get-mtu-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-get-mtu-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_get_mtu_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0b0a0020</pre><br><div>Definition at line <code>9489</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_find_attribute_id<span id=\"sl-bt-cmd-gatt-server-find-attribute-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-find-attribute-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_find_attribute_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x060a0020</pre><br><div>Definition at line <code>9490</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_read_attribute_value_id<span id=\"sl-bt-cmd-gatt-server-read-attribute-value-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-read-attribute-value-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_read_attribute_value_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x000a0020</pre><br><div>Definition at line <code>9491</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_read_attribute_type_id<span id=\"sl-bt-cmd-gatt-server-read-attribute-type-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-read-attribute-type-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_read_attribute_type_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x010a0020</pre><br><div>Definition at line <code>9492</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_write_attribute_value_id<span id=\"sl-bt-cmd-gatt-server-write-attribute-value-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-write-attribute-value-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_write_attribute_value_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x020a0020</pre><br><div>Definition at line <code>9493</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_send_user_read_response_id<span id=\"sl-bt-cmd-gatt-server-send-user-read-response-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-send-user-read-response-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_send_user_read_response_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x030a0020</pre><br><div>Definition at line <code>9494</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_send_user_write_response_id<span id=\"sl-bt-cmd-gatt-server-send-user-write-response-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-send-user-write-response-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_send_user_write_response_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x040a0020</pre><br><div>Definition at line <code>9495</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_send_notification_id<span id=\"sl-bt-cmd-gatt-server-send-notification-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-send-notification-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_send_notification_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0f0a0020</pre><br><div>Definition at line <code>9496</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_send_indication_id<span id=\"sl-bt-cmd-gatt-server-send-indication-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-send-indication-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_send_indication_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x100a0020</pre><br><div>Definition at line <code>9497</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_notify_all_id<span id=\"sl-bt-cmd-gatt-server-notify-all-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-notify-all-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_notify_all_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x110a0020</pre><br><div>Definition at line <code>9498</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_read_client_configuration_id<span id=\"sl-bt-cmd-gatt-server-read-client-configuration-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-read-client-configuration-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_read_client_configuration_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x120a0020</pre><br><div>Definition at line <code>9499</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_send_user_prepare_write_response_id<span id=\"sl-bt-cmd-gatt-server-send-user-prepare-write-response-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-send-user-prepare-write-response-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_send_user_prepare_write_response_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x140a0020</pre><br><div>Definition at line <code>9500</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_set_capabilities_id<span id=\"sl-bt-cmd-gatt-server-set-capabilities-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-set-capabilities-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_set_capabilities_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x080a0020</pre><br><div>Definition at line <code>9501</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_enable_capabilities_id<span id=\"sl-bt-cmd-gatt-server-enable-capabilities-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-enable-capabilities-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_enable_capabilities_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0c0a0020</pre><br><div>Definition at line <code>9502</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_disable_capabilities_id<span id=\"sl-bt-cmd-gatt-server-disable-capabilities-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-disable-capabilities-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_disable_capabilities_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0d0a0020</pre><br><div>Definition at line <code>9503</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_get_enabled_capabilities_id<span id=\"sl-bt-cmd-gatt-server-get-enabled-capabilities-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-get-enabled-capabilities-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_get_enabled_capabilities_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0e0a0020</pre><br><div>Definition at line <code>9504</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_server_read_client_supported_features_id<span id=\"sl-bt-cmd-gatt-server-read-client-supported-features-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-server-read-client-supported-features-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_server_read_client_supported_features_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x150a0020</pre><br><div>Definition at line <code>9505</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_set_max_mtu_id<span id=\"sl-bt-rsp-gatt-server-set-max-mtu-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-set-max-mtu-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_set_max_mtu_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0a0a0020</pre><br><div>Definition at line <code>9506</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_get_mtu_id<span id=\"sl-bt-rsp-gatt-server-get-mtu-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-get-mtu-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_get_mtu_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0b0a0020</pre><br><div>Definition at line <code>9507</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_find_attribute_id<span id=\"sl-bt-rsp-gatt-server-find-attribute-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-find-attribute-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_find_attribute_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x060a0020</pre><br><div>Definition at line <code>9508</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_read_attribute_value_id<span id=\"sl-bt-rsp-gatt-server-read-attribute-value-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-read-attribute-value-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_read_attribute_value_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x000a0020</pre><br><div>Definition at line <code>9509</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_read_attribute_type_id<span id=\"sl-bt-rsp-gatt-server-read-attribute-type-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-read-attribute-type-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_read_attribute_type_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x010a0020</pre><br><div>Definition at line <code>9510</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_write_attribute_value_id<span id=\"sl-bt-rsp-gatt-server-write-attribute-value-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-write-attribute-value-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_write_attribute_value_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x020a0020</pre><br><div>Definition at line <code>9511</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_send_user_read_response_id<span id=\"sl-bt-rsp-gatt-server-send-user-read-response-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-send-user-read-response-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_send_user_read_response_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x030a0020</pre><br><div>Definition at line <code>9512</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_send_user_write_response_id<span id=\"sl-bt-rsp-gatt-server-send-user-write-response-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-send-user-write-response-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_send_user_write_response_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x040a0020</pre><br><div>Definition at line <code>9513</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_send_notification_id<span id=\"sl-bt-rsp-gatt-server-send-notification-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-send-notification-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_send_notification_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0f0a0020</pre><br><div>Definition at line <code>9514</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_send_indication_id<span id=\"sl-bt-rsp-gatt-server-send-indication-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-send-indication-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_send_indication_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x100a0020</pre><br><div>Definition at line <code>9515</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_notify_all_id<span id=\"sl-bt-rsp-gatt-server-notify-all-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-notify-all-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_notify_all_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x110a0020</pre><br><div>Definition at line <code>9516</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_read_client_configuration_id<span id=\"sl-bt-rsp-gatt-server-read-client-configuration-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-read-client-configuration-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_read_client_configuration_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x120a0020</pre><br><div>Definition at line <code>9517</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_send_user_prepare_write_response_id<span id=\"sl-bt-rsp-gatt-server-send-user-prepare-write-response-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-send-user-prepare-write-response-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_send_user_prepare_write_response_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x140a0020</pre><br><div>Definition at line <code>9518</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_set_capabilities_id<span id=\"sl-bt-rsp-gatt-server-set-capabilities-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-set-capabilities-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_set_capabilities_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x080a0020</pre><br><div>Definition at line <code>9519</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_enable_capabilities_id<span id=\"sl-bt-rsp-gatt-server-enable-capabilities-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-enable-capabilities-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_enable_capabilities_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0c0a0020</pre><br><div>Definition at line <code>9520</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_disable_capabilities_id<span id=\"sl-bt-rsp-gatt-server-disable-capabilities-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-disable-capabilities-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_disable_capabilities_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0d0a0020</pre><br><div>Definition at line <code>9521</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_get_enabled_capabilities_id<span id=\"sl-bt-rsp-gatt-server-get-enabled-capabilities-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-get-enabled-capabilities-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_get_enabled_capabilities_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0e0a0020</pre><br><div>Definition at line <code>9522</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_server_read_client_supported_features_id<span id=\"sl-bt-rsp-gatt-server-read-client-supported-features-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-server-read-client-supported-features-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_server_read_client_supported_features_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x150a0020</pre><br><div>Definition at line <code>9523</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt-server", "status": "success"}