{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Connection<span id=\"connection\" class=\"self-anchor\"><a class=\"perm\" href=\"#connection\">#</a></span></h1><p style=\"color:inherit\">Connection. </p><p style=\"color:inherit\">The commands and events in this class are related to managing connection establishment, parameter setting, and disconnection procedures. </p><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection-rssi-const\" target=\"_blank\" rel=\"\">RSSI Value Constants</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection-tx-power-const\" target=\"_blank\" rel=\"\">Transmit Power Reporting Constants</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-parameters\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_parameters</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-set-parameters-failed\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_set_parameters_failed</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-phy-status\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_phy_status</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-get-remote-tx-power-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_get_remote_tx_power_completed</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-tx-power\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_tx_power</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-remote-tx-power\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_remote_tx_power</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-remote-used-features\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_remote_used_features</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-data-length\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_data_length</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-statistics\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_statistics</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-closed\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_closed</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-rssi\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_rssi</a></p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-role-t\">sl_bt_connection_role_t</a> {</div><div class=\"enum\">sl_bt_connection_role_peripheral = 0x0</div><div class=\"enum\">sl_bt_connection_role_central = 0x1</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Types of device roles in a Bluetooth connection. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-security-t\">sl_bt_connection_security_t</a> {</div><div class=\"enum\">sl_bt_connection_mode1_level1 = 0x0</div><div class=\"enum\">sl_bt_connection_mode1_level2 = 0x1</div><div class=\"enum\">sl_bt_connection_mode1_level3 = 0x2</div><div class=\"enum\">sl_bt_connection_mode1_level4 = 0x3</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Indicate the Bluetooth Security Mode. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-power-reporting-mode-t\">sl_bt_connection_power_reporting_mode_t</a> {</div><div class=\"enum\">sl_bt_connection_power_reporting_disable = 0x0</div><div class=\"enum\">sl_bt_connection_power_reporting_enable = 0x1</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values define transmit power reporting modes in LE power control. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-tx-power-flag-t\">sl_bt_connection_tx_power_flag_t</a> {</div><div class=\"enum\">sl_bt_connection_tx_power_flag_none = 0x0</div><div class=\"enum\">sl_bt_connection_tx_power_at_minimum = 0x1</div><div class=\"enum\">sl_bt_connection_tx_power_at_maximum = 0x2</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">This enum defines the flag values for a reported transmit power level. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-set-default-parameters\">sl_bt_connection_set_default_parameters</a>(uint16_t min_interval, uint16_t max_interval, uint16_t latency, uint16_t timeout, uint16_t min_ce_length, uint16_t max_ce_length)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-set-default-preferred-phy\">sl_bt_connection_set_default_preferred_phy</a>(uint8_t preferred_phy, uint8_t accepted_phy)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-set-default-data-length\">sl_bt_connection_set_default_data_length</a>(uint16_t tx_data_len)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-open\">sl_bt_connection_open</a>(bd_addr address, uint8_t address_type, uint8_t initiating_phy, uint8_t *connection)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-set-parameters\">sl_bt_connection_set_parameters</a>(uint8_t connection, uint16_t min_interval, uint16_t max_interval, uint16_t latency, uint16_t timeout, uint16_t min_ce_length, uint16_t max_ce_length)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-set-preferred-phy\">sl_bt_connection_set_preferred_phy</a>(uint8_t connection, uint8_t preferred_phy, uint8_t accepted_phy)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-disable-slave-latency\">sl_bt_connection_disable_slave_latency</a>(uint8_t connection, uint8_t disable)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-get-median-rssi\">sl_bt_connection_get_median_rssi</a>(uint8_t connection, int8_t *rssi)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-read-channel-map\">sl_bt_connection_read_channel_map</a>(uint8_t connection, size_t max_channel_map_size, size_t *channel_map_len, uint8_t *channel_map)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-set-power-reporting\">sl_bt_connection_set_power_reporting</a>(uint8_t connection, uint8_t mode)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-set-remote-power-reporting\">sl_bt_connection_set_remote_power_reporting</a>(uint8_t connection, uint8_t mode)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-get-tx-power\">sl_bt_connection_get_tx_power</a>(uint8_t connection, uint8_t phy, int8_t *current_level, int8_t *max_level)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-get-remote-tx-power\">sl_bt_connection_get_remote_tx_power</a>(uint8_t connection, uint8_t phy)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-set-tx-power\">sl_bt_connection_set_tx_power</a>(uint8_t connection, int16_t tx_power, int16_t *tx_power_out)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-read-remote-used-features\">sl_bt_connection_read_remote_used_features</a>(uint8_t connection)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-get-security-status\">sl_bt_connection_get_security_status</a>(uint8_t connection, uint8_t *security_mode, uint8_t *key_size, uint8_t *bonding_handle)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-set-data-length\">sl_bt_connection_set_data_length</a>(uint8_t connection, uint16_t tx_data_len, uint16_t tx_time_us)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-read-statistics\">sl_bt_connection_read_statistics</a>(uint8_t connection, uint8_t reset)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-get-scheduling-details\">sl_bt_connection_get_scheduling_details</a>(uint8_t connection, uint32_t *access_address, uint8_t *role, uint32_t *crc_init, uint16_t *interval, uint16_t *supervision_timeout, uint8_t *central_clock_accuracy, uint8_t *central_phy, uint8_t *peripheral_phy, uint8_t *channel_selection_algorithm, uint8_t *hop, sl_bt_connection_channel_map_t *channel_map, uint8_t *channel, uint16_t *event_counter, uint32_t *start_time_us)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-close\">sl_bt_connection_close</a>(uint8_t connection)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-forcefully-close\">sl_bt_connection_forcefully_close</a>(uint8_t connection)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-connection-get-rssi\">sl_bt_connection_get_rssi</a>(uint8_t connection)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-set-default-parameters-id\">sl_bt_cmd_connection_set_default_parameters_id</a> 0x00060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-set-default-preferred-phy-id\">sl_bt_cmd_connection_set_default_preferred_phy_id</a> 0x01060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-set-default-data-length-id\">sl_bt_cmd_connection_set_default_data_length_id</a> 0x10060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-open-id\">sl_bt_cmd_connection_open_id</a> 0x04060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-set-parameters-id\">sl_bt_cmd_connection_set_parameters_id</a> 0x06060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-set-preferred-phy-id\">sl_bt_cmd_connection_set_preferred_phy_id</a> 0x08060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-disable-slave-latency-id\">sl_bt_cmd_connection_disable_slave_latency_id</a> 0x03060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-get-median-rssi-id\">sl_bt_cmd_connection_get_median_rssi_id</a> 0x15060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-read-channel-map-id\">sl_bt_cmd_connection_read_channel_map_id</a> 0x07060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-set-power-reporting-id\">sl_bt_cmd_connection_set_power_reporting_id</a> 0x09060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-set-remote-power-reporting-id\">sl_bt_cmd_connection_set_remote_power_reporting_id</a> 0x0a060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-get-tx-power-id\">sl_bt_cmd_connection_get_tx_power_id</a> 0x0b060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-get-remote-tx-power-id\">sl_bt_cmd_connection_get_remote_tx_power_id</a> 0x0c060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-set-tx-power-id\">sl_bt_cmd_connection_set_tx_power_id</a> 0x12060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-read-remote-used-features-id\">sl_bt_cmd_connection_read_remote_used_features_id</a> 0x0d060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-get-security-status-id\">sl_bt_cmd_connection_get_security_status_id</a> 0x0e060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-set-data-length-id\">sl_bt_cmd_connection_set_data_length_id</a> 0x11060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-read-statistics-id\">sl_bt_cmd_connection_read_statistics_id</a> 0x13060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-get-scheduling-details-id\">sl_bt_cmd_connection_get_scheduling_details_id</a> 0x14060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-close-id\">sl_bt_cmd_connection_close_id</a> 0x05060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-forcefully-close-id\">sl_bt_cmd_connection_forcefully_close_id</a> 0x0f060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-connection-get-rssi-id\">sl_bt_cmd_connection_get_rssi_id</a> 0x02060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-set-default-parameters-id\">sl_bt_rsp_connection_set_default_parameters_id</a> 0x00060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-set-default-preferred-phy-id\">sl_bt_rsp_connection_set_default_preferred_phy_id</a> 0x01060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-set-default-data-length-id\">sl_bt_rsp_connection_set_default_data_length_id</a> 0x10060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-open-id\">sl_bt_rsp_connection_open_id</a> 0x04060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-set-parameters-id\">sl_bt_rsp_connection_set_parameters_id</a> 0x06060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-set-preferred-phy-id\">sl_bt_rsp_connection_set_preferred_phy_id</a> 0x08060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-disable-slave-latency-id\">sl_bt_rsp_connection_disable_slave_latency_id</a> 0x03060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-get-median-rssi-id\">sl_bt_rsp_connection_get_median_rssi_id</a> 0x15060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-read-channel-map-id\">sl_bt_rsp_connection_read_channel_map_id</a> 0x07060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-set-power-reporting-id\">sl_bt_rsp_connection_set_power_reporting_id</a> 0x09060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-set-remote-power-reporting-id\">sl_bt_rsp_connection_set_remote_power_reporting_id</a> 0x0a060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-get-tx-power-id\">sl_bt_rsp_connection_get_tx_power_id</a> 0x0b060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-get-remote-tx-power-id\">sl_bt_rsp_connection_get_remote_tx_power_id</a> 0x0c060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-set-tx-power-id\">sl_bt_rsp_connection_set_tx_power_id</a> 0x12060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-read-remote-used-features-id\">sl_bt_rsp_connection_read_remote_used_features_id</a> 0x0d060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-get-security-status-id\">sl_bt_rsp_connection_get_security_status_id</a> 0x0e060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-set-data-length-id\">sl_bt_rsp_connection_set_data_length_id</a> 0x11060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-read-statistics-id\">sl_bt_rsp_connection_read_statistics_id</a> 0x13060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-get-scheduling-details-id\">sl_bt_rsp_connection_get_scheduling_details_id</a> 0x14060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-close-id\">sl_bt_rsp_connection_close_id</a> 0x05060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-forcefully-close-id\">sl_bt_rsp_connection_forcefully_close_id</a> 0x0f060020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-connection-get-rssi-id\">sl_bt_rsp_connection_get_rssi_id</a> 0x02060020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>sl_bt_connection_role_t<span id=\"sl-bt-connection-role-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-role-t\">#</a></span></h3><blockquote>sl_bt_connection_role_t</blockquote><p style=\"color:inherit\">Types of device roles in a Bluetooth connection. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_connection_role_peripheral</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) The Peripheral role </p></td></tr><tr><td class=\"fieldname\">sl_bt_connection_role_central</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) The Central role </p></td></tr></tbody></table><br><div>Definition at line <code>6422</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_security_t<span id=\"sl-bt-connection-security-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-security-t\">#</a></span></h3><blockquote>sl_bt_connection_security_t</blockquote><p style=\"color:inherit\">Indicate the Bluetooth Security Mode. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_connection_mode1_level1</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) No security </p></td></tr><tr><td class=\"fieldname\">sl_bt_connection_mode1_level2</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Unauthenticated pairing with encryption </p></td></tr><tr><td class=\"fieldname\">sl_bt_connection_mode1_level3</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) Authenticated pairing with encryption </p></td></tr><tr><td class=\"fieldname\">sl_bt_connection_mode1_level4</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x3) Authenticated Secure Connections pairing with encryption using a 128-bit strength encryption key </p></td></tr></tbody></table><br><div>Definition at line <code>6431</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_power_reporting_mode_t<span id=\"sl-bt-connection-power-reporting-mode-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-power-reporting-mode-t\">#</a></span></h3><blockquote>sl_bt_connection_power_reporting_mode_t</blockquote><p style=\"color:inherit\">These values define transmit power reporting modes in LE power control. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_connection_power_reporting_disable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Disable transmit power reporting </p></td></tr><tr><td class=\"fieldname\">sl_bt_connection_power_reporting_enable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Enable transmit power reporting </p></td></tr></tbody></table><br><div>Definition at line <code>6448</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_tx_power_flag_t<span id=\"sl-bt-connection-tx-power-flag-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-tx-power-flag-t\">#</a></span></h3><blockquote>sl_bt_connection_tx_power_flag_t</blockquote><p style=\"color:inherit\">This enum defines the flag values for a reported transmit power level. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_connection_tx_power_flag_none</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) No flag is defined for the reported TX power level </p></td></tr><tr><td class=\"fieldname\">sl_bt_connection_tx_power_at_minimum</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Transmit power level is at minimum level. </p></td></tr><tr><td class=\"fieldname\">sl_bt_connection_tx_power_at_maximum</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) Transmit power level is at maximum level. </p></td></tr></tbody></table><br><div>Definition at line <code>6459</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_connection_set_default_parameters<span id=\"sl-bt-connection-set-default-parameters\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-set-default-parameters\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_set_default_parameters (uint16_t min_interval, uint16_t max_interval, uint16_t latency, uint16_t timeout, uint16_t min_ce_length, uint16_t max_ce_length)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">min_interval</td><td><p style=\"color:inherit\">Minimum value for the connection event interval. This must be set less than or equal to <code>max_interval</code>.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 1.25 ms</p></li><li><p style=\"color:inherit\">Range: 0x0006 to 0x0c80</p></li><li><p style=\"color:inherit\">Time Range: 7.5 ms to 4 s</p></li></ul><p style=\"color:inherit\">Default value: 20 ms </p></td></tr><tr><td>[in]</td><td class=\"paramname\">max_interval</td><td><p style=\"color:inherit\">Maximum value for the connection event interval. This must be set greater than or equal to <code>min_interval</code>.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 1.25 ms</p></li><li><p style=\"color:inherit\">Range: 0x0006 to 0x0c80</p></li><li><p style=\"color:inherit\">Time Range: 7.5 ms to 4 s</p></li></ul><p style=\"color:inherit\">Default value: 50 ms </p></td></tr><tr><td>[in]</td><td class=\"paramname\">latency</td><td><p style=\"color:inherit\">Peripheral latency, which defines how many connection intervals the peripheral can skip if it has no data to send</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x0000 to 0x01f3</p></li></ul><p style=\"color:inherit\">Default value: 0 </p></td></tr><tr><td>[in]</td><td class=\"paramname\">timeout</td><td><p style=\"color:inherit\">Supervision timeout, which defines the time that the connection is maintained although the devices can't communicate at the currently configured connection intervals.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x000a to 0x0c80</p></li><li><p style=\"color:inherit\">Time = Value x 10 ms</p></li><li><p style=\"color:inherit\">Time Range: 100 ms to 32 s</p></li><li><p style=\"color:inherit\">The value in milliseconds must be larger than (1 + <code>latency</code>) * <code>max_interval</code> * 2, where <code>max_interval</code> is given in milliseconds</p></li></ul><p style=\"color:inherit\">Set the supervision timeout at a value which allows communication attempts over at least a few connection intervals.</p><p style=\"color:inherit\">Default value: 1000 ms </p></td></tr><tr><td>[in]</td><td class=\"paramname\">min_ce_length</td><td><p style=\"color:inherit\">Minimum length of the connection event. It must be less than or equal to <code>max_ce_length</code>.</p><p style=\"color:inherit\">This value defines the minimum time that should be given to the connection event in a situation where other tasks need to run immediately after the connection event. When the value is very small, the connection event still has at least one TX/RX operation. If this value is increased, more time is reserved for the connection event so it can transmit and receive more packets in a connection interval.</p><p style=\"color:inherit\">Use the default value if the application doesn't care about the connection event length or doesn't want to do fine tuning.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 0.625 ms</p></li><li><p style=\"color:inherit\">Range: 0x0000 to 0xffff</p></li></ul><p style=\"color:inherit\">Default value: 0x0000 </p></td></tr><tr><td>[in]</td><td class=\"paramname\">max_ce_length</td><td><p style=\"color:inherit\">Maximum length of the connection event. It must be greater than or equal to <code>min_ce_length</code>.</p><p style=\"color:inherit\">This value is used for limiting the connection event length so that a connection that has large amounts of data to transmit or receive doesn't block other tasks. Limiting the connection event is a hard stop. If there is no enough time to send or receive a packet, the connection event will be closed.</p><p style=\"color:inherit\">If the value is set to 0, the connection event still has at least one TX/RX operation. This is useful to limit power consumption or leave more time to other tasks.</p><p style=\"color:inherit\">Use the default value if the application doesn't care about the connection event length or doesn't want to do fine tuning.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 0.625 ms</p></li><li><p style=\"color:inherit\">Range: 0x0000 to 0xffff</p></li></ul><p style=\"color:inherit\">Default value: 0xffff </p></td></tr></tbody></table></div><p style=\"color:inherit\">Set default Bluetooth connection parameters. The values are valid for all subsequent connections initiated by this device.</p><p style=\"color:inherit\"><code>min_ce_length</code> and <code>max_ce_length</code> specify the preference of the connection event length so that the Link Layer can prioritize tasks accordingly in simultaneous connections, or scanning and so on. A connection event starts at an anchor point of a connection interval and lasts until the lesser of <code>max_ce_length</code> and the actual connection interval. Packets that do not fit into the connection event will be sent in the next connection interval.</p><p style=\"color:inherit\">To change parameters of an already established connection, use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-set-parameters\" target=\"_blank\" rel=\"\">sl_bt_connection_set_parameters</a>.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>7173</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_set_default_preferred_phy<span id=\"sl-bt-connection-set-default-preferred-phy\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-set-default-preferred-phy\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_set_default_preferred_phy (uint8_t preferred_phy, uint8_t accepted_phy)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">preferred_phy</td><td><p style=\"color:inherit\">Preferred PHYs. This parameter is a bitfield and multiple PHYs can be set.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0x01:</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>0x02:</strong> 2M PHY</p></li><li><p style=\"color:inherit\"><strong>0x04:</strong> Coded PHY</p></li><li><p style=\"color:inherit\"><strong>0xff:</strong> Any PHYs</p></li></ul><p style=\"color:inherit\">Default: 0xff (no preference) </p></td></tr><tr><td>[in]</td><td class=\"paramname\">accepted_phy</td><td><p style=\"color:inherit\">Accepted PHYs in remotely-initiated PHY update request. This parameter is a bitfield and multiple PHYs can be set.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0x01:</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>0x02:</strong> 2M PHY</p></li><li><p style=\"color:inherit\"><strong>0x04:</strong> Coded PHY</p></li><li><p style=\"color:inherit\"><strong>0xff:</strong> Any PHYs</p></li></ul><p style=\"color:inherit\">Default: 0xff (all PHYs accepted) </p></td></tr></tbody></table></div><p style=\"color:inherit\">Set default preferred and accepted PHYs. PHY settings will be used for all subsequent connections. Non-preferred PHY can also be set if the remote device does not accept any of the preferred PHYs.</p><p style=\"color:inherit\">The parameter <code>accepted_phy</code> is used to specify PHYs that the stack can accept in a remotely-initiated PHY update request. A PHY update will not happen if none of the accepted PHYs are present in the request.</p><p style=\"color:inherit\"><strong>NOTE:</strong> 2M and Coded PHYs are not supported by all devices.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>7215</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_set_default_data_length<span id=\"sl-bt-connection-set-default-data-length\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-set-default-data-length\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_set_default_data_length (uint16_t tx_data_len)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">tx_data_len</td><td><p style=\"color:inherit\">Preferred maximum payload octets of a packet that the local Controller will send</p><p style=\"color:inherit\">Range: Range: 27 (0x1B) to 251 (0xFB)</p><p style=\"color:inherit\">Default: 251 </p></td></tr></tbody></table></div><p style=\"color:inherit\">Set the default preferred maximum TX payload length to be used for new connections.</p><p style=\"color:inherit\">When a connection is open, the maximum TX payload length is 27. Either device could initiate a data length update procedure and event <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-data-length\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_data_length</a> is generated when the data length has been changed on the connection.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>7240</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_open<span id=\"sl-bt-connection-open\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-open\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_open (<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> address, uint8_t address_type, uint8_t initiating_phy, uint8_t * connection)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">address</td><td><p style=\"color:inherit\">Address of the device to connect to </p></td></tr><tr><td>[in]</td><td class=\"paramname\">address_type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>. Address type of the device to connect to. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address (0x0):</strong> Public device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_resolvable_address (0x2):</strong> Resolvable private random address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_nonresolvable_address (0x3):</strong> Non-resolvable private random address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address_resolved_from_rpa (0x4):</strong> Public identity address resolved from a resolvable private address (RPA)</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address_resolved_from_rpa (0x5):</strong> Static identity address resolved from a resolvable private address (RPA) </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">initiating_phy</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-phy-t\" target=\"_blank\" rel=\"\">sl_bt_gap_phy_t</a>. The initiating PHY. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_1m (0x1):</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coded (0x4):</strong> Coded PHY, 125k (S=8) or 500k (S=2) </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Handle that will be assigned to the connection after the connection is established. This handle is valid only if the result code of this response is 0 (zero).</p></td></tr></tbody></table></div><p style=\"color:inherit\">Connect to an advertising device with the specified initiating PHY on which connectable advertisements on primary advertising channels are received. The Bluetooth stack will enter a state where it continuously scans for the connectable advertising packets from the remote device, which matches the Bluetooth address given as a parameter. Scan parameters set in <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-set-timing\" target=\"_blank\" rel=\"\">sl_bt_scanner_set_timing</a> are used in this operation. Upon receiving the advertising packet, the module will send a connection request packet to the target device to initiate a Bluetooth connection. To cancel an ongoing connection process, use <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-close\" target=\"_blank\" rel=\"\">sl_bt_connection_close</a> command with the handle received in response from this command.</p><p style=\"color:inherit\">A connection is opened in no-security mode. If the GATT client needs to read or write the attributes on GATT server requiring encryption or authentication, it must first encrypt the connection using an appropriate authentication method.</p><p style=\"color:inherit\">If a connection can't be established, for example, the remote device has gone out of range, has entered into deep sleep, or is not advertising, the stack will try to connect forever. In this case, the application will not get an event related to the connection request. To recover from this situation, the application can implement a timeout and call <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-close\" target=\"_blank\" rel=\"\">sl_bt_connection_close</a> to cancel the connection request.</p><p style=\"color:inherit\">This command fails with the connection limit exceeded error if the number of connections attempted exceeds the configured MAX_CONNECTIONS value.</p><p style=\"color:inherit\">This command fails with the invalid parameter error if the initiating PHY value is invalid or the device does not support PHY.</p><p style=\"color:inherit\">Subsequent calls of this command have to wait for the ongoing command to complete. A received event <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> indicates that the connection opened successfully and a received event <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-closed\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_closed</a> indicates that connection failures have occurred.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> - This event is triggered after the connection is opened and indicates whether the devices are already bonded and the role of the device in this connection.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-parameters\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_parameters</a> - This event indicates the connection parameters and security mode of the connection. </p></li></ul><br><div>Definition at line <code>7309</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_set_parameters<span id=\"sl-bt-connection-set-parameters\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-set-parameters\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_set_parameters (uint8_t connection, uint16_t min_interval, uint16_t max_interval, uint16_t latency, uint16_t timeout, uint16_t min_ce_length, uint16_t max_ce_length)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection Handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">min_interval</td><td><p style=\"color:inherit\">Minimum value for the connection event interval. This must be set less than or equal to <code>max_interval</code>.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 1.25 ms</p></li><li><p style=\"color:inherit\">Range: 0x0006 to 0x0c80</p></li><li><p style=\"color:inherit\">Time Range: 7.5 ms to 4 s </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">max_interval</td><td><p style=\"color:inherit\">Maximum value for the connection event interval. This must be set greater than or equal to <code>min_interval</code>.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 1.25 ms</p></li><li><p style=\"color:inherit\">Range: 0x0006 to 0x0c80</p></li><li><p style=\"color:inherit\">Time Range: 7.5 ms to 4 s </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">latency</td><td><p style=\"color:inherit\">Peripheral latency, which defines how many connection intervals the peripheral can skip if it has no data to send</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x0000 to 0x01f3 </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">timeout</td><td><p style=\"color:inherit\">Supervision timeout, which defines the time that the connection is maintained although the devices can't communicate at the currently configured connection intervals.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x000a to 0x0c80</p></li><li><p style=\"color:inherit\">Time = Value x 10 ms</p></li><li><p style=\"color:inherit\">Time Range: 100 ms to 32 s</p></li><li><p style=\"color:inherit\">The value in milliseconds must be larger than (1 + <code>latency</code>) * <code>max_interval</code> * 2, where <code>max_interval</code> is given in milliseconds</p></li></ul><p style=\"color:inherit\">Set the supervision timeout at a value which allows communication attempts over at least a few connection intervals. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">min_ce_length</td><td><p style=\"color:inherit\">Minimum length of the connection event. It must be less than or equal to <code>max_ce_length</code>.</p><p style=\"color:inherit\">This value defines the minimum time that should be given to the connection event in a situation where other tasks need to run immediately after the connection event. When the value is very small, the connection event still has at least one TX/RX operation. If this value is increased, more time is reserved for the connection event so it can transmit and receive more packets in a connection interval.</p><p style=\"color:inherit\">Use the default value if the application doesn't care about the connection event length or doesn't want to do fine tuning.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 0.625 ms</p></li><li><p style=\"color:inherit\">Range: 0x0000 to 0xffff</p></li></ul><p style=\"color:inherit\">Default value: 0x0000 </p></td></tr><tr><td>[in]</td><td class=\"paramname\">max_ce_length</td><td><p style=\"color:inherit\">Maximum length of the connection event. It must be greater than or equal to <code>min_ce_length</code>.</p><p style=\"color:inherit\">This value is used for limiting the connection event length so that a connection that has large amounts of data to transmit or receive doesn't block other tasks. Limiting the connection event is a hard stop. If there is no enough time to send or receive a packet, the connection event will be closed.</p><p style=\"color:inherit\">If the value is set to 0, the connection event still has at least one TX/RX operation. This is useful to limit power consumption or leave more time to other tasks.</p><p style=\"color:inherit\">Use the default value if the application doesn't care about the connection event length or doesn't want to do fine tuning.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 0.625 ms</p></li><li><p style=\"color:inherit\">Range: 0x0000 to 0xffff</p></li></ul><p style=\"color:inherit\">Default value: 0xffff </p></td></tr></tbody></table></div><p style=\"color:inherit\">Request a change in the connection parameters of a Bluetooth connection.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">On the central device the HCI LE Connection Update command is used to update the parameters.</p></li><li><p style=\"color:inherit\">On the peripheral device an L2CAP connection parameter update request is sent to the peer device to update the parameters.</p></li></ul><p style=\"color:inherit\"><code>min_ce_length</code> and <code>max_ce_length</code> specify the preference of the connection event length so that the Link Layer can prioritize tasks accordingly in simultaneous connections, or scanning and so on. A connection event starts at an anchor point of a connection interval and lasts until the lesser of <code>max_ce_length</code> and the actual connection interval. Packets that do not fit into the connection event will be sent in the next connection interval.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-parameters\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_parameters</a> - Triggered after new connection parameters are applied on the connection.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-set-parameters-failed\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_set_parameters_failed</a> - Triggered when the peer device rejected an L2CAP connection parameter update request. </p></li></ul><br><div>Definition at line <code>7407</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_set_preferred_phy<span id=\"sl-bt-connection-set-preferred-phy\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-set-preferred-phy\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_set_preferred_phy (uint8_t connection, uint8_t preferred_phy, uint8_t accepted_phy)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">preferred_phy</td><td><p style=\"color:inherit\">Preferred PHYs. This parameter is a bitfield and multiple PHYs can be set.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0x01:</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>0x02:</strong> 2M PHY</p></li><li><p style=\"color:inherit\"><strong>0x04:</strong> 125k Coded PHY (S=8)</p></li><li><p style=\"color:inherit\"><strong>0x08:</strong> 500k Coded PHY (S=2)</p></li></ul><p style=\"color:inherit\">Default: 0xff (no preference) </p></td></tr><tr><td>[in]</td><td class=\"paramname\">accepted_phy</td><td><p style=\"color:inherit\">Accepted PHYs in remotely-initiated PHY update requests. This parameter is a bitfield and multiple PHYs can be set.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0x01:</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>0x02:</strong> 2M PHY</p></li><li><p style=\"color:inherit\"><strong>0x04:</strong> Coded PHY</p></li><li><p style=\"color:inherit\"><strong>0xff:</strong> Any PHYs</p></li></ul><p style=\"color:inherit\">Default: 0xff (all PHYs accepted) </p></td></tr></tbody></table></div><p style=\"color:inherit\">Set preferred and accepted PHYs for a given connection. Event <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-phy-status\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_phy_status</a> is received when PHY update procedure is completed. Non-preferred PHY can also be set if remote device does not accept any of the preferred PHYs.</p><p style=\"color:inherit\">The parameter <code>accepted_phy</code> is used for specifying the PHYs that the stack can accept in a remote initiated PHY update request. A PHY update will not occur if none of the accepted PHYs presents in the request.</p><p style=\"color:inherit\"><strong>NOTE:</strong> 2M and Coded PHYs are not supported by all devices.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-phy-status\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_phy_status</a></p></li></ul><br><div>Definition at line <code>7455</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_disable_slave_latency<span id=\"sl-bt-connection-disable-slave-latency\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-disable-slave-latency\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_disable_slave_latency (uint8_t connection, uint8_t disable)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection Handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">disable</td><td><p style=\"color:inherit\">0 enable, 1 disable peripheral latency. Default: 0</p></td></tr></tbody></table></div><p style=\"color:inherit\">Temporarily enable or disable peripheral latency. Used only when Bluetooth device is acting as a peripheral. When peripheral latency is disabled, the peripheral latency connection parameter is not set to 0 but the device will wake up on every connection interval to receive and send packets.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>7472</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_get_median_rssi<span id=\"sl-bt-connection-get-median-rssi\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-get-median-rssi\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_get_median_rssi (uint8_t connection, int8_t * rssi)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[out]</td><td class=\"paramname\">rssi</td><td><p style=\"color:inherit\">The median of the last seven measured RSSI values. Units: dBm. Range: -127 to +20.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Get the RSSI value measured on a connection.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>7486</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_read_channel_map<span id=\"sl-bt-connection-read-channel-map\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-read-channel-map\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_read_channel_map (uint8_t connection, size_t max_channel_map_size, size_t * channel_map_len, uint8_t * channel_map)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection Handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">max_channel_map_size</td><td><p style=\"color:inherit\">Size of output buffer passed in <code>channel_map</code></p></td></tr><tr><td>[out]</td><td class=\"paramname\">channel_map_len</td><td><p style=\"color:inherit\">On return, set to the length of output data written to <code>channel_map</code></p></td></tr><tr><td>[out]</td><td class=\"paramname\">channel_map</td><td><p style=\"color:inherit\">This parameter is 5 bytes and contains 37 1-bit fields.</p><p style=\"color:inherit\">The nth field (in the range 0 to 36) contains the value for the link layer channel index n.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Channel n is unused.</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Channel n is used.</p></li></ul><p style=\"color:inherit\">The most significant bits are reserved for future use. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Read channel map for a specified connection.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>7512</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_set_power_reporting<span id=\"sl-bt-connection-set-power-reporting\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-set-power-reporting\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_set_power_reporting (uint8_t connection, uint8_t mode)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Handle of the connection </p></td></tr><tr><td>[in]</td><td class=\"paramname\">mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-power-reporting-mode-t\" target=\"_blank\" rel=\"\">sl_bt_connection_power_reporting_mode_t</a>. Transmit power reporting mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_connection_power_reporting_disable (0x0):</strong> Disable transmit power reporting</p></li><li><p style=\"color:inherit\"><strong>sl_bt_connection_power_reporting_enable (0x1):</strong> Enable transmit power reporting</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Enable or disable the transmit power reporting for the local device on a connection. When transmit power reporting is enabled, event <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-tx-power\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_tx_power</a> is generated when transmit power on the local device changes.</p><p style=\"color:inherit\">The command is a built-in feature in the stack and is supported regardless of whether the LE Power Control feature is used. By default, power reporting for local device is enabled.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-tx-power\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_tx_power</a></p></li></ul><br><div>Definition at line <code>7542</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_set_remote_power_reporting<span id=\"sl-bt-connection-set-remote-power-reporting\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-set-remote-power-reporting\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_set_remote_power_reporting (uint8_t connection, uint8_t mode)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Handle of the connection </p></td></tr><tr><td>[in]</td><td class=\"paramname\">mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-power-reporting-mode-t\" target=\"_blank\" rel=\"\">sl_bt_connection_power_reporting_mode_t</a>. Transmit power reporting mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_connection_power_reporting_disable (0x0):</strong> Disable transmit power reporting</p></li><li><p style=\"color:inherit\"><strong>sl_bt_connection_power_reporting_enable (0x1):</strong> Enable transmit power reporting</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Enable or disable reporting the transmit power change on the remote device. The application must include the LE Power Control feature (bluetooth_feature_power_control) in order to use this command.</p><p style=\"color:inherit\">When the remote transmit power reporting is enabled, event <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-remote-tx-power\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_remote_tx_power</a> is generated when transmit power on the remote device changes.</p><p style=\"color:inherit\">By default, power reporting for the remote device is disabled.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-remote-tx-power\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_remote_tx_power</a></p></li></ul><br><div>Definition at line <code>7571</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_get_tx_power<span id=\"sl-bt-connection-get-tx-power\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-get-tx-power\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_get_tx_power (uint8_t connection, uint8_t phy, int8_t * current_level, int8_t * max_level)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Handle of the connection </p></td></tr><tr><td>[in]</td><td class=\"paramname\">phy</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-phy-coding-t\" target=\"_blank\" rel=\"\">sl_bt_gap_phy_coding_t</a>. The PHY. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coding_1m_uncoded (0x1):</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coding_2m_uncoded (0x2):</strong> 2M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coding_125k_coded (0x4):</strong> 125k Coded PHY (S=8)</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coding_500k_coded (0x8):</strong> 500k Coded PHY (S=2) </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">current_level</td><td><p style=\"color:inherit\">The current transmit power level of the PHY on the connection. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>Range -127 to 20:</strong> The transmit power level in dBm</p></li><li><p style=\"color:inherit\"><strong><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection-tx-power-const#sl-bt-connection-tx-power-unavailable\" target=\"_blank\" rel=\"\">SL_BT_CONNECTION_TX_POWER_UNAVAILABLE</a> (0x7F):</strong> Transmit power level is not available. </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">max_level</td><td><p style=\"color:inherit\">The maximum transmit power level of the PHY on the connection. Values: Range -127 to +20 in dBm.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Get the transmit power of the local device on the given connection and PHY. The application must include the LE Power Control feature (bluetooth_feature_power_control) in order to use this command.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>7597</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_get_remote_tx_power<span id=\"sl-bt-connection-get-remote-tx-power\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-get-remote-tx-power\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_get_remote_tx_power (uint8_t connection, uint8_t phy)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Handle of the connection </p></td></tr><tr><td>[in]</td><td class=\"paramname\">phy</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-phy-coding-t\" target=\"_blank\" rel=\"\">sl_bt_gap_phy_coding_t</a>. The PHY. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coding_1m_uncoded (0x1):</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coding_2m_uncoded (0x2):</strong> 2M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coding_125k_coded (0x4):</strong> 125k Coded PHY (S=8)</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coding_500k_coded (0x8):</strong> 500k Coded PHY (S=2)</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Get the transmit power of the remote device on the given connection and PHY. The application must include the LE Power Control feature (bluetooth_feature_power_control) in order to use this command. Transmit power levels are returned in event <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-get-remote-tx-power-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_get_remote_tx_power_completed</a> after the operation completed.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if the command is accepted. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-get-remote-tx-power-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_get_remote_tx_power_completed</a></p></li></ul><br><div>Definition at line <code>7624</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_set_tx_power<span id=\"sl-bt-connection-set-tx-power\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-set-tx-power\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_set_tx_power (uint8_t connection, int16_t tx_power, int16_t * tx_power_out)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">The connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">tx_power</td><td><p style=\"color:inherit\">The requested TX power. Unit: 0.1 dBm </p></td></tr><tr><td>[out]</td><td class=\"paramname\">tx_power_out</td><td><p style=\"color:inherit\">The selected TX power. Unit: 0.1 dBm</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set the transmit power of a connection. The application must include component bluetooth_feature_user_power_control in order to use this command for controlling the transmit power of the connection at application level. This command is unavailable if the standard Bluetooth feature LE power control (component bluetooth_feature_power_control) is used by the application.</p><p style=\"color:inherit\">The actual selected power level is returned from this command. The value may be different than the requested one because of Bluetooth feature restrictions or radio characteristics.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>7647</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_read_remote_used_features<span id=\"sl-bt-connection-read-remote-used-features\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-read-remote-used-features\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_read_remote_used_features (uint8_t connection)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection Handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Read link layer features supported by the remote device.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-remote-used-features\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_remote_used_features</a></p></li></ul><br><div>Definition at line <code>7663</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_get_security_status<span id=\"sl-bt-connection-get-security-status\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-get-security-status\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_get_security_status (uint8_t connection, uint8_t * security_mode, uint8_t * key_size, uint8_t * bonding_handle)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[out]</td><td class=\"paramname\">security_mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-security-t\" target=\"_blank\" rel=\"\">sl_bt_connection_security_t</a>. Connection security mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_connection_mode1_level1 (0x0):</strong> No security</p></li><li><p style=\"color:inherit\"><strong>sl_bt_connection_mode1_level2 (0x1):</strong> Unauthenticated pairing with encryption</p></li><li><p style=\"color:inherit\"><strong>sl_bt_connection_mode1_level3 (0x2):</strong> Authenticated pairing with encryption</p></li><li><p style=\"color:inherit\"><strong>sl_bt_connection_mode1_level4 (0x3):</strong> Authenticated Secure Connections pairing with encryption using a 128-bit strength encryption key </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">key_size</td><td><p style=\"color:inherit\">The size of encryption key </p></td></tr><tr><td>[out]</td><td class=\"paramname\">bonding_handle</td><td><p style=\"color:inherit\">Bonding handle. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>SL_BT_INVALID_BONDING_HANDLE (0xff):</strong> No bonding</p></li><li><p style=\"color:inherit\"><strong>Other:</strong> Bonding handle</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Get the security status of the connection.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>7688</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_set_data_length<span id=\"sl-bt-connection-set-data-length\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-set-data-length\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_set_data_length (uint8_t connection, uint16_t tx_data_len, uint16_t tx_time_us)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">tx_data_len</td><td><p style=\"color:inherit\">Preferred maximum payload octets of a packet that the local Controller will send</p><p style=\"color:inherit\">Range: 27 (0x1B) to 251 (0xFB) </p></td></tr><tr><td>[in]</td><td class=\"paramname\">tx_time_us</td><td><p style=\"color:inherit\">Preferred maximum TX time in microseconds that the local Controller will take to send a packet</p><p style=\"color:inherit\">Range: 328 (0x0148) to 17040 (0x4290) </p></td></tr></tbody></table></div><p style=\"color:inherit\">Request to update the maximum TX payload length and maximum packet TX time of a Bluetooth connection.</p><p style=\"color:inherit\">Event <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-data-length\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_data_length</a> is generated when the data length has been changed on the connection.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-data-length\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_data_length</a></p></li></ul><br><div>Definition at line <code>7721</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_read_statistics<span id=\"sl-bt-connection-read-statistics\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-read-statistics\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_read_statistics (uint8_t connection, uint8_t reset)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">The connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">reset</td><td><p style=\"color:inherit\">Reset statistics if parameter value is not zero</p></td></tr></tbody></table></div><p style=\"color:inherit\">Read the statistic values collected on the specified connection. The application must include component bluetooth_feature_connection_statistics in order to use this command. If the component is not included in the application this command returns the error SL_STATUS_NOT_AVAILABLE. If this command is called before the connection has opened, the command returns the error SL_STATUS_NOT_READY.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-statistics\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_statistics</a> - Triggered to deliver the statistic values of the connection at the time the command was issued. </p></li></ul><br><div>Definition at line <code>7744</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_get_scheduling_details<span id=\"sl-bt-connection-get-scheduling-details\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-get-scheduling-details\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_get_scheduling_details (uint8_t connection, uint32_t * access_address, uint8_t * role, uint32_t * crc_init, uint16_t * interval, uint16_t * supervision_timeout, uint8_t * central_clock_accuracy, uint8_t * central_phy, uint8_t * peripheral_phy, uint8_t * channel_selection_algorithm, uint8_t * hop, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection-channel-map-t\" target=\"_blank\" rel=\"\">sl_bt_connection_channel_map_t</a> * channel_map, uint8_t * channel, uint16_t * event_counter, uint32_t * start_time_us)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[out]</td><td class=\"paramname\">access_address</td><td><p style=\"color:inherit\">Access address of the connection </p></td></tr><tr><td>[out]</td><td class=\"paramname\">role</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-role-t\" target=\"_blank\" rel=\"\">sl_bt_connection_role_t</a>. The role the local device operates in the connection. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_connection_role_peripheral (0x0):</strong> The Peripheral role</p></li><li><p style=\"color:inherit\"><strong>sl_bt_connection_role_central (0x1):</strong> The Central role </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">crc_init</td><td><p style=\"color:inherit\">The CRC initialization value </p></td></tr><tr><td>[out]</td><td class=\"paramname\">interval</td><td><p style=\"color:inherit\">The connection interval. Time = Value x 1.25 ms </p></td></tr><tr><td>[out]</td><td class=\"paramname\">supervision_timeout</td><td><p style=\"color:inherit\">The connection supervision time. Time = Value x 10 ms </p></td></tr><tr><td>[out]</td><td class=\"paramname\">central_clock_accuracy</td><td><p style=\"color:inherit\">The central device's clock accuracy index value </p></td></tr><tr><td>[out]</td><td class=\"paramname\">central_phy</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-phy-t\" target=\"_blank\" rel=\"\">sl_bt_gap_phy_t</a>. The PHY that the Central device is transmitting on. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_1m (0x1):</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_2m (0x2):</strong> 2M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coded (0x4):</strong> Coded PHY, 125k (S=8) or 500k (S=2) </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">peripheral_phy</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-phy-t\" target=\"_blank\" rel=\"\">sl_bt_gap_phy_t</a>. The PHY that the Peripheral device is transmitting on. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_1m (0x1):</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_2m (0x2):</strong> 2M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coded (0x4):</strong> Coded PHY, 125k (S=8) or 500k (S=2) </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">channel_selection_algorithm</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-channel-selection-algorithm-t\" target=\"_blank\" rel=\"\">sl_bt_gap_channel_selection_algorithm_t</a>. The channel selection algorithm. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_channel_selection_algorithm_1 (0x0):</strong> Channel selection algorithm #1</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_channel_selection_algorithm_2 (0x1):</strong> Channel selection algorithm #2 </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">hop</td><td><p style=\"color:inherit\">The hop increment when the channel selection algorithm #1 is used on the connection. Ignore if <code>channel_selection_algorithm</code> indicates that the connection uses channel selection algorithm #2. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">channel_map</td><td><p style=\"color:inherit\">5 byte bit field in little endian format. Only the first 37 bits are used. Bit 0 of the first byte is channel 0, bit 0 of the second byte is channel 8, etc. Ignore bits 37-39 that are reserved for future use.</p><p style=\"color:inherit\">A channel is unused when its bit is 0. A channel is used when its bit is 1. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">channel</td><td><p style=\"color:inherit\">The data channel number that transmissions will use in the next connection event </p></td></tr><tr><td>[out]</td><td class=\"paramname\">event_counter</td><td><p style=\"color:inherit\">The event counter of the next connection event </p></td></tr><tr><td>[out]</td><td class=\"paramname\">start_time_us</td><td><p style=\"color:inherit\">Time in microseconds from the PROTIMER tick when the next connection event is scheduled to happen</p></td></tr></tbody></table></div><p style=\"color:inherit\">Get parameters and next connection event scheduling details of a connection. Returned values from this command provide the necessary information for following the packet transmissions of the connection on an external device. Note that the stack cannot ensure a determined timing latency from the time when issuing this command to the time when another device starts to follow the connection, and the stack cannot guarantee that a scheduled connection event will always happen.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>7804</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_close<span id=\"sl-bt-connection-close\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-close\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_close (uint8_t connection)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Handle of the connection to be closed</p></td></tr></tbody></table></div><p style=\"color:inherit\">Close a Bluetooth connection gracefully by performing the ACL Termination procedure or cancel an ongoing connection establishment process. The parameter is a connection handle which is reported in <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> event or <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-open\" target=\"_blank\" rel=\"\">sl_bt_connection_open</a> command response.</p><p style=\"color:inherit\">Disconnecting a connection is an asynchronous operation. The disconnection is completed when a <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-closed\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_closed</a> event for the given connection handle is received. To open a new connection to the same remote device, wait for the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-closed\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_closed</a> event and then initiate the connection.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-closed\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_closed</a></p></li></ul><br><div>Definition at line <code>7842</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_forcefully_close<span id=\"sl-bt-connection-forcefully-close\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-forcefully-close\">#</a></span></h3><blockquote>sl_status_t sl_bt_connection_forcefully_close (uint8_t connection)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Handle of the connection to be closed</p></td></tr></tbody></table></div><p style=\"color:inherit\">Forcefully close a Bluetooth connection without performing the ACL Termination procedure. The parameter is a connection handle which is reported in <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> event or <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-open\" target=\"_blank\" rel=\"\">sl_bt_connection_open</a> command response.</p><p style=\"color:inherit\">Closing a connection using this command could result in the observation of connection loss or supervision timeout on the remote device. Only use this command for special cases, for example, when disconnecting a connection with <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-close\" target=\"_blank\" rel=\"\">sl_bt_connection_close</a> did not complete in expected time period.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-closed\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_closed</a></p></li></ul><br><div>Definition at line <code>7864</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_connection_get_rssi<span id=\"sl-bt-connection-get-rssi\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-connection-get-rssi\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_connection_get_rssi (uint8_t connection)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-get-median-rssi\" target=\"_blank\" rel=\"\">sl_bt_connection_get_median_rssi</a> which synchronously returns the value.</p><p style=\"color:inherit\">Get the latest RSSI value of a Bluetooth connection. The RSSI value will be reported in a <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-rssi\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_rssi</a> event if the command returns SL_STATUS_OK.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-rssi\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_rssi</a> - Triggered when this command has completed. </p></li></ul><br><div>Definition at line <code>7884</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_connection_set_default_parameters_id<span id=\"sl-bt-cmd-connection-set-default-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-set-default-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_set_default_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00060020</pre><br><div>Definition at line <code>6374</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_set_default_preferred_phy_id<span id=\"sl-bt-cmd-connection-set-default-preferred-phy-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-set-default-preferred-phy-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_set_default_preferred_phy_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01060020</pre><br><div>Definition at line <code>6375</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_set_default_data_length_id<span id=\"sl-bt-cmd-connection-set-default-data-length-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-set-default-data-length-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_set_default_data_length_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x10060020</pre><br><div>Definition at line <code>6376</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_open_id<span id=\"sl-bt-cmd-connection-open-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-open-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_open_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x04060020</pre><br><div>Definition at line <code>6377</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_set_parameters_id<span id=\"sl-bt-cmd-connection-set-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-set-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_set_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x06060020</pre><br><div>Definition at line <code>6378</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_set_preferred_phy_id<span id=\"sl-bt-cmd-connection-set-preferred-phy-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-set-preferred-phy-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_set_preferred_phy_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x08060020</pre><br><div>Definition at line <code>6379</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_disable_slave_latency_id<span id=\"sl-bt-cmd-connection-disable-slave-latency-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-disable-slave-latency-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_disable_slave_latency_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03060020</pre><br><div>Definition at line <code>6380</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_get_median_rssi_id<span id=\"sl-bt-cmd-connection-get-median-rssi-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-get-median-rssi-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_get_median_rssi_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x15060020</pre><br><div>Definition at line <code>6381</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_read_channel_map_id<span id=\"sl-bt-cmd-connection-read-channel-map-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-read-channel-map-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_read_channel_map_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x07060020</pre><br><div>Definition at line <code>6382</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_set_power_reporting_id<span id=\"sl-bt-cmd-connection-set-power-reporting-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-set-power-reporting-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_set_power_reporting_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x09060020</pre><br><div>Definition at line <code>6383</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_set_remote_power_reporting_id<span id=\"sl-bt-cmd-connection-set-remote-power-reporting-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-set-remote-power-reporting-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_set_remote_power_reporting_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0a060020</pre><br><div>Definition at line <code>6384</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_get_tx_power_id<span id=\"sl-bt-cmd-connection-get-tx-power-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-get-tx-power-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_get_tx_power_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0b060020</pre><br><div>Definition at line <code>6385</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_get_remote_tx_power_id<span id=\"sl-bt-cmd-connection-get-remote-tx-power-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-get-remote-tx-power-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_get_remote_tx_power_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0c060020</pre><br><div>Definition at line <code>6386</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_set_tx_power_id<span id=\"sl-bt-cmd-connection-set-tx-power-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-set-tx-power-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_set_tx_power_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x12060020</pre><br><div>Definition at line <code>6387</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_read_remote_used_features_id<span id=\"sl-bt-cmd-connection-read-remote-used-features-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-read-remote-used-features-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_read_remote_used_features_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0d060020</pre><br><div>Definition at line <code>6388</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_get_security_status_id<span id=\"sl-bt-cmd-connection-get-security-status-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-get-security-status-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_get_security_status_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0e060020</pre><br><div>Definition at line <code>6389</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_set_data_length_id<span id=\"sl-bt-cmd-connection-set-data-length-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-set-data-length-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_set_data_length_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x11060020</pre><br><div>Definition at line <code>6390</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_read_statistics_id<span id=\"sl-bt-cmd-connection-read-statistics-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-read-statistics-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_read_statistics_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x13060020</pre><br><div>Definition at line <code>6391</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_get_scheduling_details_id<span id=\"sl-bt-cmd-connection-get-scheduling-details-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-get-scheduling-details-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_get_scheduling_details_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x14060020</pre><br><div>Definition at line <code>6392</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_close_id<span id=\"sl-bt-cmd-connection-close-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-close-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_close_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x05060020</pre><br><div>Definition at line <code>6393</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_forcefully_close_id<span id=\"sl-bt-cmd-connection-forcefully-close-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-forcefully-close-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_forcefully_close_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0f060020</pre><br><div>Definition at line <code>6394</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_connection_get_rssi_id<span id=\"sl-bt-cmd-connection-get-rssi-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-connection-get-rssi-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_connection_get_rssi_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02060020</pre><br><div>Definition at line <code>6395</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_set_default_parameters_id<span id=\"sl-bt-rsp-connection-set-default-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-set-default-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_set_default_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00060020</pre><br><div>Definition at line <code>6396</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_set_default_preferred_phy_id<span id=\"sl-bt-rsp-connection-set-default-preferred-phy-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-set-default-preferred-phy-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_set_default_preferred_phy_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01060020</pre><br><div>Definition at line <code>6397</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_set_default_data_length_id<span id=\"sl-bt-rsp-connection-set-default-data-length-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-set-default-data-length-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_set_default_data_length_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x10060020</pre><br><div>Definition at line <code>6398</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_open_id<span id=\"sl-bt-rsp-connection-open-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-open-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_open_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x04060020</pre><br><div>Definition at line <code>6399</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_set_parameters_id<span id=\"sl-bt-rsp-connection-set-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-set-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_set_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x06060020</pre><br><div>Definition at line <code>6400</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_set_preferred_phy_id<span id=\"sl-bt-rsp-connection-set-preferred-phy-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-set-preferred-phy-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_set_preferred_phy_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x08060020</pre><br><div>Definition at line <code>6401</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_disable_slave_latency_id<span id=\"sl-bt-rsp-connection-disable-slave-latency-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-disable-slave-latency-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_disable_slave_latency_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03060020</pre><br><div>Definition at line <code>6402</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_get_median_rssi_id<span id=\"sl-bt-rsp-connection-get-median-rssi-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-get-median-rssi-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_get_median_rssi_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x15060020</pre><br><div>Definition at line <code>6403</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_read_channel_map_id<span id=\"sl-bt-rsp-connection-read-channel-map-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-read-channel-map-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_read_channel_map_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x07060020</pre><br><div>Definition at line <code>6404</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_set_power_reporting_id<span id=\"sl-bt-rsp-connection-set-power-reporting-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-set-power-reporting-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_set_power_reporting_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x09060020</pre><br><div>Definition at line <code>6405</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_set_remote_power_reporting_id<span id=\"sl-bt-rsp-connection-set-remote-power-reporting-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-set-remote-power-reporting-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_set_remote_power_reporting_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0a060020</pre><br><div>Definition at line <code>6406</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_get_tx_power_id<span id=\"sl-bt-rsp-connection-get-tx-power-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-get-tx-power-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_get_tx_power_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0b060020</pre><br><div>Definition at line <code>6407</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_get_remote_tx_power_id<span id=\"sl-bt-rsp-connection-get-remote-tx-power-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-get-remote-tx-power-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_get_remote_tx_power_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0c060020</pre><br><div>Definition at line <code>6408</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_set_tx_power_id<span id=\"sl-bt-rsp-connection-set-tx-power-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-set-tx-power-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_set_tx_power_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x12060020</pre><br><div>Definition at line <code>6409</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_read_remote_used_features_id<span id=\"sl-bt-rsp-connection-read-remote-used-features-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-read-remote-used-features-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_read_remote_used_features_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0d060020</pre><br><div>Definition at line <code>6410</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_get_security_status_id<span id=\"sl-bt-rsp-connection-get-security-status-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-get-security-status-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_get_security_status_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0e060020</pre><br><div>Definition at line <code>6411</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_set_data_length_id<span id=\"sl-bt-rsp-connection-set-data-length-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-set-data-length-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_set_data_length_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x11060020</pre><br><div>Definition at line <code>6412</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_read_statistics_id<span id=\"sl-bt-rsp-connection-read-statistics-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-read-statistics-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_read_statistics_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x13060020</pre><br><div>Definition at line <code>6413</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_get_scheduling_details_id<span id=\"sl-bt-rsp-connection-get-scheduling-details-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-get-scheduling-details-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_get_scheduling_details_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x14060020</pre><br><div>Definition at line <code>6414</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_close_id<span id=\"sl-bt-rsp-connection-close-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-close-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_close_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x05060020</pre><br><div>Definition at line <code>6415</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_forcefully_close_id<span id=\"sl-bt-rsp-connection-forcefully-close-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-forcefully-close-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_forcefully_close_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0f060020</pre><br><div>Definition at line <code>6416</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_connection_get_rssi_id<span id=\"sl-bt-rsp-connection-get-rssi-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-connection-get-rssi-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_connection_get_rssi_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02060020</pre><br><div>Definition at line <code>6417</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection", "status": "success"}