{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>PAwR Advertiser<span id=\"p-aw-r-advertiser\" class=\"self-anchor\"><a class=\"perm\" href=\"#p-aw-r-advertiser\">#</a></span></h1><p style=\"color:inherit\">PAwR Advertiser. </p><p style=\"color:inherit\">Provides support for advertising with Periodic Advertising with Responses (PAwR) trains. </p><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-pawr-advertiser-subevent-data-request\" target=\"_blank\" rel=\"\">sl_bt_evt_pawr_advertiser_subevent_data_request</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-pawr-advertiser-subevent-tx-failed\" target=\"_blank\" rel=\"\">sl_bt_evt_pawr_advertiser_subevent_tx_failed</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-pawr-advertiser-response-report\" target=\"_blank\" rel=\"\">sl_bt_evt_pawr_advertiser_response_report</a></p><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-pawr-advertiser-start\">sl_bt_pawr_advertiser_start</a>(uint8_t advertising_set, uint16_t interval_min, uint16_t interval_max, uint32_t flags, uint8_t num_subevents, uint8_t subevent_interval, uint8_t response_slot_delay, uint8_t response_slot_spacing, uint8_t response_slots)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-pawr-advertiser-set-subevent-data\">sl_bt_pawr_advertiser_set_subevent_data</a>(uint8_t advertising_set, uint8_t subevent, uint8_t response_slot_start, uint8_t response_slot_count, size_t adv_data_len, const uint8_t *adv_data)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-pawr-advertiser-create-connection\">sl_bt_pawr_advertiser_create_connection</a>(uint8_t advertising_set, uint8_t subevent, bd_addr address, uint8_t address_type, uint8_t *connection)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-pawr-advertiser-stop\">sl_bt_pawr_advertiser_stop</a>(uint8_t advertising_set)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-pawr-advertiser-start-id\">sl_bt_cmd_pawr_advertiser_start_id</a> 0x00550020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-pawr-advertiser-set-subevent-data-id\">sl_bt_cmd_pawr_advertiser_set_subevent_data_id</a> 0x01550020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-pawr-advertiser-create-connection-id\">sl_bt_cmd_pawr_advertiser_create_connection_id</a> 0x02550020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-pawr-advertiser-stop-id\">sl_bt_cmd_pawr_advertiser_stop_id</a> 0x03550020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-pawr-advertiser-start-id\">sl_bt_rsp_pawr_advertiser_start_id</a> 0x00550020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-pawr-advertiser-set-subevent-data-id\">sl_bt_rsp_pawr_advertiser_set_subevent_data_id</a> 0x01550020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-pawr-advertiser-create-connection-id\">sl_bt_rsp_pawr_advertiser_create_connection_id</a> 0x02550020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-pawr-advertiser-stop-id\">sl_bt_rsp_pawr_advertiser_stop_id</a> 0x03550020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_pawr_advertiser_start<span id=\"sl-bt-pawr-advertiser-start\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-pawr-advertiser-start\">#</a></span></h3><blockquote>sl_status_t sl_bt_pawr_advertiser_start (uint8_t advertising_set, uint16_t interval_min, uint16_t interval_max, uint32_t flags, uint8_t num_subevents, uint8_t subevent_interval, uint8_t response_slot_delay, uint8_t response_slot_spacing, uint8_t response_slots)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">The PAwR advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">interval_min</td><td><p style=\"color:inherit\">Minimum periodic advertising interval. Value in units of 1.25 ms.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>Range:</strong> 0x06 to 0xFFFF</p></li><li><p style=\"color:inherit\">Time range: 7.5 ms to 81.92 s</p></li><li><p style=\"color:inherit\"><strong>Default</strong> : 100 ms </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">interval_max</td><td><p style=\"color:inherit\">Maximum periodic advertising interval. Value in units of 1.25 ms.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time range: 7.5 ms to 81.92 s</p></li><li><p style=\"color:inherit\">Note: interval_max should be bigger than interval_min </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">Additional periodic advertising options. Value: 0 or bitmask of <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-periodic-advertiser-flags\" target=\"_blank\" rel=\"\">Periodic Advertising Configuration Flags</a></p></td></tr><tr><td>[in]</td><td class=\"paramname\">num_subevents</td><td><p style=\"color:inherit\">The number of subevents.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>Range:</strong> 0x01 to 0x80 </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">subevent_interval</td><td><p style=\"color:inherit\">Subevent interval. Value in units of 1.25 ms.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>Range:</strong> 0x06 to 0xFF</p></li><li><p style=\"color:inherit\">Time range: 7.5 ms to 318.75 ms </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">response_slot_delay</td><td><p style=\"color:inherit\">Time between the advertising packet in a subevent and the first response slot. Value in units of 1.25 ms.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>Range:</strong> 0x01 to 0xFE</p></li><li><p style=\"color:inherit\">Time range: 1.25 ms to 317.5 ms </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">response_slot_spacing</td><td><p style=\"color:inherit\">Time between response slots. Value in units of 0.125 ms.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>Range:</strong> 0x02 to 0xFF</p></li><li><p style=\"color:inherit\">Time range: 0.25 ms to 31.875 ms </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">response_slots</td><td><p style=\"color:inherit\">Number of subevent response slots.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>Range:</strong> 0x01 to 0xFF</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Start PAwR advertising on the specified advertising set.</p><p style=\"color:inherit\">According to the Bluetooth Core specification, PAwR advertising PDUs cannot be transmitted until at least one extended advertising event has been completed. If the application needs exact control over the extended advertising data and parameters, use the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser\" target=\"_blank\" rel=\"\">Advertiser</a> class to configure the parameters of the advertising set and the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser\" target=\"_blank\" rel=\"\">Extended Advertiser</a> class to set or generate the desired extended advertising data payload. If the application does not configure the parameters or set the data, the default parameters and empty advertising data are used for the extended advertising.</p><p style=\"color:inherit\">If the application has not already started extended advertising and the flag <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-periodic-advertiser-flags#sl-bt-periodic-advertiser-auto-start-extended-advertising\" target=\"_blank\" rel=\"\">SL_BT_PERIODIC_ADVERTISER_AUTO_START_EXTENDED_ADVERTISING</a> is set in <code>flags</code>, the stack will automatically start extended advertising with the parameters and extended advertising data currently configured to the advertising set. The application may stop the automatically started extended advertising using the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-stop\" target=\"_blank\" rel=\"\">sl_bt_advertiser_stop</a> command.</p><p style=\"color:inherit\">If the application has not already started extended advertising and the flag <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-periodic-advertiser-flags#sl-bt-periodic-advertiser-auto-start-extended-advertising\" target=\"_blank\" rel=\"\">SL_BT_PERIODIC_ADVERTISER_AUTO_START_EXTENDED_ADVERTISING</a> is not set in <code>flags</code>, the stack will momentarily start extended advertising with the parameters and extended advertising data currently configured to the advertising set. Unless the application starts extended advertising before the first extended advertising event has completed, the stack will automatically stop the momentary extended advertising after the first extended advertising event.</p><p style=\"color:inherit\">PAwR advertising PDUs are transmitted on the secondary PHY configured for the advertising set with the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-set-phy\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_set_phy</a> command.</p><p style=\"color:inherit\">To stop PAwR advertising, use <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-pawr-advertiser#sl-bt-pawr-advertiser-stop\" target=\"_blank\" rel=\"\">sl_bt_pawr_advertiser_stop</a> command.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>5271</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_pawr_advertiser_set_subevent_data<span id=\"sl-bt-pawr-advertiser-set-subevent-data\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-pawr-advertiser-set-subevent-data\">#</a></span></h3><blockquote>sl_status_t sl_bt_pawr_advertiser_set_subevent_data (uint8_t advertising_set, uint8_t subevent, uint8_t response_slot_start, uint8_t response_slot_count, size_t adv_data_len, const uint8_t * adv_data)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">The PAwR advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">subevent</td><td><p style=\"color:inherit\">The subevent in which the data is to be sent </p></td></tr><tr><td>[in]</td><td class=\"paramname\">response_slot_start</td><td><p style=\"color:inherit\">The first response slot to be used in this subevent </p></td></tr><tr><td>[in]</td><td class=\"paramname\">response_slot_count</td><td><p style=\"color:inherit\">The number of response slots to be used </p></td></tr><tr><td>[in]</td><td class=\"paramname\">adv_data_len</td><td><p style=\"color:inherit\">Length of data in <code>adv_data</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">adv_data</td><td><p style=\"color:inherit\">Data to be sent in the specified subevent</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set data to be sent in the specified subevent of an active PAwR train. Data is transmitted only once and is discarded after it has been transmitted.</p><p style=\"color:inherit\">Data given to this command is passed to the Bluetooth controller, which will queue data and transmit it at the correct time. The application may always opportunistically try to queue more data with this command, but the controller may reject data and return an error if the queuing capacity is exceeded. In this case, the Bluetooth stack will trigger the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-pawr-advertiser-subevent-data-request\" target=\"_blank\" rel=\"\">sl_bt_evt_pawr_advertiser_subevent_data_request</a> event later when the controller is ready to accept more data.</p><p style=\"color:inherit\">To ensure effective use of the available memory, applications are encouraged to observe the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-pawr-advertiser-subevent-data-request\" target=\"_blank\" rel=\"\">sl_bt_evt_pawr_advertiser_subevent_data_request</a> events and set data for the subevents that are being requested and for which the application has data to send. Applications should also note that PAwR is an unreliable transport and cannot guarantee delivery. If reliability is required, the application must implement an acknowledgment mechanism using response slots of the PAwR train and set subevent data again for a re-transmission if it was not successfully delivered.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-pawr-advertiser-subevent-data-request\" target=\"_blank\" rel=\"\">sl_bt_evt_pawr_advertiser_subevent_data_request</a> - This event is triggered when the Bluetooth stack is ready to accept more subevent data.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-pawr-advertiser-subevent-tx-failed\" target=\"_blank\" rel=\"\">sl_bt_evt_pawr_advertiser_subevent_tx_failed</a> - This event is triggered if transmitting the subevent data has failed.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-pawr-advertiser-response-report\" target=\"_blank\" rel=\"\">sl_bt_evt_pawr_advertiser_response_report</a> - If the subevent data was successfully transmitted, this event is triggered for each response slot that was marked as used in this subevent. </p></li></ul><br><div>Definition at line <code>5323</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_pawr_advertiser_create_connection<span id=\"sl-bt-pawr-advertiser-create-connection\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-pawr-advertiser-create-connection\">#</a></span></h3><blockquote>sl_status_t sl_bt_pawr_advertiser_create_connection (uint8_t advertising_set, uint8_t subevent, <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> address, uint8_t address_type, uint8_t * connection)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">The PAwR advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">subevent</td><td><p style=\"color:inherit\">The subevent in which the connection request is to be sent </p></td></tr><tr><td>[in]</td><td class=\"paramname\">address</td><td><p style=\"color:inherit\">Address of the device to connect to </p></td></tr><tr><td>[in]</td><td class=\"paramname\">address_type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>. Address type of the device to connect to. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address (0x0):</strong> Public device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_resolvable_address (0x2):</strong> Resolvable private random address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_nonresolvable_address (0x3):</strong> Non-resolvable private random address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address_resolved_from_rpa (0x4):</strong> Public identity address resolved from a resolvable private address (RPA)</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address_resolved_from_rpa (0x5):</strong> Static identity address resolved from a resolvable private address (RPA) </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Handle that will be assigned to the connection after the connection is established. This handle is valid only if the result code of this command is SL_STATUS_OK.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Initiate a connection request to a device that is synchronized to the specified active PAwR train. The connection is established on the secondary PHY configured for the advertising set with the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-set-phy\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_set_phy</a> command. The connection uses the parameters configured with command <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-set-default-parameters\" target=\"_blank\" rel=\"\">sl_bt_connection_set_default_parameters</a>.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-connection-closed\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_closed</a> - This event is triggered if the connection failed to be created.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> - This event is triggered after the connection is opened and indicates whether the devices are already bonded and the role of the device in this connection.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-connection-parameters\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_parameters</a> - This event indicates the connection parameters and security mode of the connection. </p></li></ul><br><div>Definition at line <code>5370</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_pawr_advertiser_stop<span id=\"sl-bt-pawr-advertiser-stop\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-pawr-advertiser-stop\">#</a></span></h3><blockquote>sl_status_t sl_bt_pawr_advertiser_stop (uint8_t advertising_set)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">The PAwR advertising set handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Stop PAwR advertising on an advertising set. Counterpart to <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-pawr-advertiser#sl-bt-pawr-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_pawr_advertiser_start</a>.</p><p style=\"color:inherit\">This command does not affect the enable state of the advertising set, i.e., legacy or extended advertising is not stopped.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>5389</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_pawr_advertiser_start_id<span id=\"sl-bt-cmd-pawr-advertiser-start-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-pawr-advertiser-start-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_pawr_advertiser_start_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00550020</pre><br><div>Definition at line <code>5052</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_pawr_advertiser_set_subevent_data_id<span id=\"sl-bt-cmd-pawr-advertiser-set-subevent-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-pawr-advertiser-set-subevent-data-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_pawr_advertiser_set_subevent_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01550020</pre><br><div>Definition at line <code>5053</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_pawr_advertiser_create_connection_id<span id=\"sl-bt-cmd-pawr-advertiser-create-connection-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-pawr-advertiser-create-connection-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_pawr_advertiser_create_connection_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02550020</pre><br><div>Definition at line <code>5054</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_pawr_advertiser_stop_id<span id=\"sl-bt-cmd-pawr-advertiser-stop-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-pawr-advertiser-stop-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_pawr_advertiser_stop_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03550020</pre><br><div>Definition at line <code>5055</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_pawr_advertiser_start_id<span id=\"sl-bt-rsp-pawr-advertiser-start-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-pawr-advertiser-start-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_pawr_advertiser_start_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00550020</pre><br><div>Definition at line <code>5056</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_pawr_advertiser_set_subevent_data_id<span id=\"sl-bt-rsp-pawr-advertiser-set-subevent-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-pawr-advertiser-set-subevent-data-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_pawr_advertiser_set_subevent_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01550020</pre><br><div>Definition at line <code>5057</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_pawr_advertiser_create_connection_id<span id=\"sl-bt-rsp-pawr-advertiser-create-connection-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-pawr-advertiser-create-connection-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_pawr_advertiser_create_connection_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02550020</pre><br><div>Definition at line <code>5058</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_pawr_advertiser_stop_id<span id=\"sl-bt-rsp-pawr-advertiser-stop-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-pawr-advertiser-stop-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_pawr_advertiser_stop_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03550020</pre><br><div>Definition at line <code>5059</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-pawr-advertiser", "status": "success"}