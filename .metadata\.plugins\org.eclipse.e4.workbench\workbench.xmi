<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_gS6tMH2eEfCdZppseCV2hQ" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_gS6tMX2eEfCdZppseCV2hQ" bindingContexts="_gS9K532eEfCdZppseCV2hQ">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;pw_br/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/pw_br/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;bt_yali_2025/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_2025/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;jieshouqi_2025/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/jieshouqi_2025/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;bt_jieshouqi_2025/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;pt124b.c&quot; tooltip=&quot;bt_yali_2025/pt124b.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_2025/pt124b.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.h&quot; tooltip=&quot;bt_yali_2025/app.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_2025/app.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.bt_yali_2025.bluetooth_feature_connection.cedit&quot; tooltip=&quot;bt_yali_2025/.uceditor/.bt_yali_2025.bluetooth_feature_connection.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_2025/.uceditor/.bt_yali_2025.bluetooth_feature_connection.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.uc.editor.html.UCComponentSelectorEditor&quot; name=&quot;bt_yali_2025.slcp&quot; tooltip=&quot;bt_yali_2025/bt_yali_2025.slcp&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_2025/bt_yali_2025.slcp&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;em_emu.h&quot; tooltip=&quot;bt_yali_2025/gecko_sdk_4.1.2/platform/emlib/inc/em_emu.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_2025/gecko_sdk_4.1.2/platform/emlib/inc/em_emu.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;zm_bt_ob/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/zm_bt_ob/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.bt_yali_20250725.i2cspmi2c.cedit&quot; tooltip=&quot;bt_yali_20250725/.uceditor/.bt_yali_20250725.i2cspmi2c.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_20250725/.uceditor/.bt_yali_20250725.i2cspmi2c.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.uc.editor.html.UCComponentSelectorEditor&quot; name=&quot;bt_yali_20250725.slcp&quot; tooltip=&quot;bt_yali_20250725/bt_yali_20250725.slcp&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_20250725/bt_yali_20250725.slcp&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;bt_yali_20250725/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_20250725/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;pt124b.c&quot; tooltip=&quot;bt_yali_20250725/pt124b.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_20250725/pt124b.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;bt_qingjiao_2025/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_qingjiao_2025/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;bt_ceju_202505/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool_gatt_configurator.editor.GattConfiguratorEditor&quot; name=&quot;gatt_configuration.btconf&quot; tooltip=&quot;bt_jieshouqi_2025/config/btconf/gatt_configuration.btconf&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/config/btconf/gatt_configuration.btconf&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.uc.editor.html.UCComponentSelectorEditor&quot; name=&quot;bt_jieshouqi_2025.slcp&quot; tooltip=&quot;bt_jieshouqi_2025/bt_jieshouqi_2025.slcp&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/bt_jieshouqi_2025.slcp&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;kx1023.c&quot; tooltip=&quot;bt_qingjiao_2025/kx1023.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_qingjiao_2025/kx1023.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;pt124b.h&quot; tooltip=&quot;bt_yali_2025/pt124b.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_2025/pt124b.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;qingjiao_2025/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/qingjiao_2025/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.bt_ceju_202505.iostream_usartvcom.cedit&quot; tooltip=&quot;bt_ceju_202505/.uceditor/.bt_ceju_202505.iostream_usartvcom.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/.uceditor/.bt_ceju_202505.iostream_usartvcom.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.uc.editor.html.UCComponentSelectorEditor&quot; name=&quot;bt_ceju_202505.slcp&quot; tooltip=&quot;bt_ceju_202505/bt_ceju_202505.slcp&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/bt_ceju_202505.slcp&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_device_init_clocks.c&quot; tooltip=&quot;bt_ceju_202505/autogen/sl_device_init_clocks.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/autogen/sl_device_init_clocks.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_event_handler.c&quot; tooltip=&quot;bt_ceju_202505/autogen/sl_event_handler.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/autogen/sl_event_handler.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_system_init.c&quot; tooltip=&quot;bt_ceju_202505/gecko_sdk_4.1.2/platform/service/system/src/sl_system_init.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/gecko_sdk_4.1.2/platform/service/system/src/sl_system_init.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;bt_ceju_202505/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;bt_secure_spp_over_ble/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_secure_spp_over_ble/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_power_manager.h&quot; tooltip=&quot;bt_ceju_202505/gecko_sdk_4.1.2/platform/service/power_manager/inc/sl_power_manager.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/gecko_sdk_4.1.2/platform/service/power_manager/inc/sl_power_manager.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_iostream_init_usart_instances.h&quot; tooltip=&quot;bt_ceju_202505/autogen/sl_iostream_init_usart_instances.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/autogen/sl_iostream_init_usart_instances.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_iostream_init_usart_instances.c&quot; tooltip=&quot;bt_ceju_202505/autogen/sl_iostream_init_usart_instances.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/autogen/sl_iostream_init_usart_instances.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_power_manager_handler.c&quot; tooltip=&quot;bt_ceju_202505/autogen/sl_power_manager_handler.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/autogen/sl_power_manager_handler.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;em_gpio.h&quot; tooltip=&quot;bt_ceju_202505/gecko_sdk_4.1.2/platform/emlib/inc/em_gpio.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/gecko_sdk_4.1.2/platform/emlib/inc/em_gpio.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.bt_ceju_202505.simple_ledmos.cedit&quot; tooltip=&quot;bt_ceju_202505/.uceditor/.bt_ceju_202505.simple_ledmos.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/.uceditor/.bt_ceju_202505.simple_ledmos.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool_gatt_configurator.editor.GattConfiguratorEditor&quot; name=&quot;gatt_configuration.btconf&quot; tooltip=&quot;bt_yali_2025/config/btconf/gatt_configuration.btconf&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_2025/config/btconf/gatt_configuration.btconf&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_iostream_uart.c&quot; tooltip=&quot;bt_ceju_202505/gecko_sdk_4.1.2/platform/service/iostream/src/sl_iostream_uart.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/gecko_sdk_4.1.2/platform/service/iostream/src/sl_iostream_uart.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_iostream_usart_vcom_config.h&quot; tooltip=&quot;bt_ceju_202505/config/sl_iostream_usart_vcom_config.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/config/sl_iostream_usart_vcom_config.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;em_emu.c&quot; tooltip=&quot;bt_ceju_202505/gecko_sdk_4.1.2/platform/emlib/src/em_emu.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/gecko_sdk_4.1.2/platform/emlib/src/em_emu.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;em_emu.h&quot; tooltip=&quot;bt_ceju_202505/gecko_sdk_4.1.2/platform/emlib/inc/em_emu.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/gecko_sdk_4.1.2/platform/emlib/inc/em_emu.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_iostream.h&quot; tooltip=&quot;bt_ceju_202505/gecko_sdk_4.1.2/platform/service/iostream/inc/sl_iostream.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/gecko_sdk_4.1.2/platform/service/iostream/inc/sl_iostream.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_iostream.c&quot; tooltip=&quot;bt_ceju_202505/gecko_sdk_4.1.2/platform/service/iostream/src/sl_iostream.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/gecko_sdk_4.1.2/platform/service/iostream/src/sl_iostream.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_udelay.c&quot; tooltip=&quot;bt_ceju_202505/gecko_sdk_4.1.2/platform/service/udelay/src/sl_udelay.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/gecko_sdk_4.1.2/platform/service/udelay/src/sl_udelay.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_udelay.h&quot; tooltip=&quot;bt_ceju_202505/gecko_sdk_4.1.2/platform/service/udelay/inc/sl_udelay.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/gecko_sdk_4.1.2/platform/service/udelay/inc/sl_udelay.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.cdt.ui.ExternalEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;stdbool.h&quot; tooltip=&quot;C:\SiliconLabs\SimplicityStudio\v5\developer\toolchains\gnu_arm\10.3_2021.10\lib\gcc\arm-none-eabi\10.3.1\include\stdbool.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;C:\SiliconLabs\SimplicityStudio\v5\developer\toolchains\gnu_arm\10.3_2021.10\lib\gcc\arm-none-eabi\10.3.1\include\stdbool.h&quot; project=&quot;bt_ceju_202505&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_sleeptimer.c&quot; tooltip=&quot;bt_ceju_202505/gecko_sdk_4.1.2/platform/service/sleeptimer/src/sl_sleeptimer.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/gecko_sdk_4.1.2/platform/service/sleeptimer/src/sl_sleeptimer.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool_gatt_configurator.editor.GattConfiguratorEditor&quot; name=&quot;gatt_configuration.btconf&quot; tooltip=&quot;bt_ceju_202505/config/btconf/gatt_configuration.btconf&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/config/btconf/gatt_configuration.btconf&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;in_place_ota_dfu.xml&quot; tooltip=&quot;bt_ceju_202505/config/btconf/in_place_ota_dfu.xml&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_202505/config/btconf/in_place_ota_dfu.xml&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;bt_ceju_2025/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_2025/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;bt_jieshouqi_ceju_2025/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_ceju_2025/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_bt_api.h&quot; tooltip=&quot;bt_ceju_2025/gecko_sdk_4.1.2/protocol/bluetooth/inc/sl_bt_api.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_2025/gecko_sdk_4.1.2/protocol/bluetooth/inc/sl_bt_api.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.bt_ceju_2025.iostream_usartvcom.cedit&quot; tooltip=&quot;bt_ceju_2025/.uceditor/.bt_ceju_2025.iostream_usartvcom.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_2025/.uceditor/.bt_ceju_2025.iostream_usartvcom.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.uc.editor.html.UCComponentSelectorEditor&quot; name=&quot;bt_ceju_2025.slcp&quot; tooltip=&quot;bt_ceju_2025/bt_ceju_2025.slcp&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_2025/bt_ceju_2025.slcp&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool_gatt_configurator.editor.GattConfiguratorEditor&quot; name=&quot;gatt_configuration.btconf&quot; tooltip=&quot;bt_qingjiao_2025/config/btconf/gatt_configuration.btconf&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_qingjiao_2025/config/btconf/gatt_configuration.btconf&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.uc.editor.html.UCComponentSelectorEditor&quot; name=&quot;bt_qingjiao_2025.slcp&quot; tooltip=&quot;bt_qingjiao_2025/bt_qingjiao_2025.slcp&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_qingjiao_2025/bt_qingjiao_2025.slcp&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool_gatt_configurator.editor.GattConfiguratorEditor&quot; name=&quot;gatt_configuration.btconf&quot; tooltip=&quot;bt_ceju_2025/config/btconf/gatt_configuration.btconf&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_2025/config/btconf/gatt_configuration.btconf&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;voltage_measurement.c&quot; tooltip=&quot;bt_ceju_2025/voltage_measurement.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_ceju_2025/voltage_measurement.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.ZJZM_V3.01_DISTANCE_20230602_2.iostream_usartvcom.cedit&quot; tooltip=&quot;ZJZM_V3.01_DISTANCE_20230602_2/.uceditor/.ZJZM_V3.01_DISTANCE_20230602_2.iostream_usartvcom.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZJZM_V3.01_DISTANCE_20230602_2/.uceditor/.ZJZM_V3.01_DISTANCE_20230602_2.iostream_usartvcom.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.uc.editor.html.UCComponentSelectorEditor&quot; name=&quot;ZJZM_V3.01_DISTANCE_20230602_2.slcp&quot; tooltip=&quot;ZJZM_V3.01_DISTANCE_20230602_2/ZJZM_V3.01_DISTANCE_20230602_2.slcp&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZJZM_V3.01_DISTANCE_20230602_2/ZJZM_V3.01_DISTANCE_20230602_2.slcp&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;ZJZM_V3.01_DISTANCE_20230602_2.slps&quot; tooltip=&quot;ZJZM_V3.01_DISTANCE_20230602_2/ZJZM_V3.01_DISTANCE_20230602_2.slps&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZJZM_V3.01_DISTANCE_20230602_2/ZJZM_V3.01_DISTANCE_20230602_2.slps&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;ZJZM_V3.01_DISTANCE_20230602_2/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZJZM_V3.01_DISTANCE_20230602_2/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.bt_yali_2025.i2cspmi2c.cedit&quot; tooltip=&quot;bt_yali_2025/.uceditor/.bt_yali_2025.i2cspmi2c.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_2025/.uceditor/.bt_yali_2025.i2cspmi2c.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.ZJZM_V3.01_PRESS_20250222.i2cspmi2c.cedit&quot; tooltip=&quot;ZJZM_V3.01_PRESS_20250222/.uceditor/.ZJZM_V3.01_PRESS_20250222.i2cspmi2c.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZJZM_V3.01_PRESS_20250222/.uceditor/.ZJZM_V3.01_PRESS_20250222.i2cspmi2c.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.uc.editor.html.UCComponentSelectorEditor&quot; name=&quot;ZJZM_V3.01_PRESS_20250222.slcp&quot; tooltip=&quot;ZJZM_V3.01_PRESS_20250222/ZJZM_V3.01_PRESS_20250222.slcp&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZJZM_V3.01_PRESS_20250222/ZJZM_V3.01_PRESS_20250222.slcp&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;ZJZM_V3.01_PRESS_20250222/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZJZM_V3.01_PRESS_20250222/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;bt_yali_2025/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_yali_2025/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;bt_multicentral_multiperipheral_dual_topology_4.1.2/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_multicentral_multiperipheral_dual_topology_4.1.2/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c.bk&quot; tooltip=&quot;bt_jieshouqi_2025/app.c.bk&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/app.c.bk&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.bt_qingjiao_2025.bluetooth_stack.cedit&quot; tooltip=&quot;bt_qingjiao_2025/.uceditor/.bt_qingjiao_2025.bluetooth_stack.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_qingjiao_2025/.uceditor/.bt_qingjiao_2025.bluetooth_stack.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.bt_jieshouqi_2025.gatt_configuration.cedit&quot; tooltip=&quot;bt_jieshouqi_2025/.uceditor/.bt_jieshouqi_2025.gatt_configuration.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/.uceditor/.bt_jieshouqi_2025.gatt_configuration.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_bt_api.h&quot; tooltip=&quot;bt_jieshouqi_2025/gecko_sdk_4.1.2/protocol/bluetooth/inc/sl_bt_api.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/gecko_sdk_4.1.2/protocol/bluetooth/inc/sl_bt_api.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app2.h.bk&quot; tooltip=&quot;bt_jieshouqi_2025/app2.h.bk&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/app2.h.bk&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.h&quot; tooltip=&quot;bt_jieshouqi_2025/app.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/app.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;bt_serial_port_profile_server/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_serial_port_profile_server/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_simple_timer.h&quot; tooltip=&quot;bt_qingjiao_2025/gecko_sdk_4.1.2/app/bluetooth/common/simple_timer/sl_simple_timer.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_qingjiao_2025/gecko_sdk_4.1.2/app/bluetooth/common/simple_timer/sl_simple_timer.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_simple_timer.c&quot; tooltip=&quot;bt_qingjiao_2025/gecko_sdk_4.1.2/app/bluetooth/common/simple_timer/sl_simple_timer.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_qingjiao_2025/gecko_sdk_4.1.2/app/bluetooth/common/simple_timer/sl_simple_timer.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;bt_controlling_led_from_smartphone/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_controlling_led_from_smartphone/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.bt_controlling_led_from_smartphone.app_log.cedit&quot; tooltip=&quot;bt_controlling_led_from_smartphone/.uceditor/.bt_controlling_led_from_smartphone.app_log.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_controlling_led_from_smartphone/.uceditor/.bt_controlling_led_from_smartphone.app_log.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.bt_controlling_led_from_smartphone.iostream_usartvcom.cedit&quot; tooltip=&quot;bt_controlling_led_from_smartphone/.uceditor/.bt_controlling_led_from_smartphone.iostream_usartvcom.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_controlling_led_from_smartphone/.uceditor/.bt_controlling_led_from_smartphone.iostream_usartvcom.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.uc.editor.html.UCComponentSelectorEditor&quot; name=&quot;bt_controlling_led_from_smartphone.slcp&quot; tooltip=&quot;bt_controlling_led_from_smartphone/bt_controlling_led_from_smartphone.slcp&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_controlling_led_from_smartphone/bt_controlling_led_from_smartphone.slcp&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.bt_jieshouqi_2025.iostream_usartvcom.cedit&quot; tooltip=&quot;bt_jieshouqi_2025/.uceditor/.bt_jieshouqi_2025.iostream_usartvcom.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/.uceditor/.bt_jieshouqi_2025.iostream_usartvcom.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool_gatt_configurator.editor.GattConfiguratorEditor&quot; name=&quot;gatt_configuration.btconf&quot; tooltip=&quot;bt_controlling_led_from_smartphone/config/btconf/gatt_configuration.btconf&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_controlling_led_from_smartphone/config/btconf/gatt_configuration.btconf&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.bt_controlling_led_from_smartphone.simple_ledled0.cedit&quot; tooltip=&quot;bt_controlling_led_from_smartphone/.uceditor/.bt_controlling_led_from_smartphone.simple_ledled0.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_controlling_led_from_smartphone/.uceditor/.bt_controlling_led_from_smartphone.simple_ledled0.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.framework.ide.internal.ui.BrowserFileViewer&quot; name=&quot;README.md&quot; tooltip=&quot;bt_controlling_led_from_smartphone/README.md&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_controlling_led_from_smartphone/README.md&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool_gatt_configurator.editor.GattConfiguratorEditor&quot; name=&quot;gatt_configuration.btconf&quot; tooltip=&quot;bt_secure_spp_over_ble/config/btconf/gatt_configuration.btconf&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_secure_spp_over_ble/config/btconf/gatt_configuration.btconf&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.uc.editor.html.UCComponentSelectorEditor&quot; name=&quot;bt_secure_spp_over_ble.slcp&quot; tooltip=&quot;bt_secure_spp_over_ble/bt_secure_spp_over_ble.slcp&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_secure_spp_over_ble/bt_secure_spp_over_ble.slcp&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.framework.ide.internal.ui.BrowserFileViewer&quot; name=&quot;README.md&quot; tooltip=&quot;bt_secure_spp_over_ble/README.md&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_secure_spp_over_ble/README.md&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.uc.editor.html.UCComponentSelectorEditor&quot; name=&quot;bt_soc_thermometer_mock.slcp&quot; tooltip=&quot;bt_soc_thermometer_mock/bt_soc_thermometer_mock.slcp&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_soc_thermometer_mock/bt_soc_thermometer_mock.slcp&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;bt_soc_thermometer_mock/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_soc_thermometer_mock/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_simple_led_instances.c&quot; tooltip=&quot;bt_jieshouqi_2025/autogen/sl_simple_led_instances.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/autogen/sl_simple_led_instances.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_bt_api.h&quot; tooltip=&quot;bt_qingjiao_2025/gecko_sdk_4.1.2/protocol/bluetooth/inc/sl_bt_api.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_qingjiao_2025/gecko_sdk_4.1.2/protocol/bluetooth/inc/sl_bt_api.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_bt_api_compatibility.h&quot; tooltip=&quot;bt_qingjiao_2025/gecko_sdk_4.1.2/protocol/bluetooth/inc/sl_bt_api_compatibility.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_qingjiao_2025/gecko_sdk_4.1.2/protocol/bluetooth/inc/sl_bt_api_compatibility.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_sleeptimer.c&quot; tooltip=&quot;bt_jieshouqi_2025/gecko_sdk_4.1.2/platform/service/sleeptimer/src/sl_sleeptimer.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/gecko_sdk_4.1.2/platform/service/sleeptimer/src/sl_sleeptimer.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_sleeptimer.h&quot; tooltip=&quot;bt_jieshouqi_2025/gecko_sdk_4.1.2/platform/service/sleeptimer/inc/sl_sleeptimer.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/gecko_sdk_4.1.2/platform/service/sleeptimer/inc/sl_sleeptimer.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;bt_jieshouqi_2025.map&quot; tooltip=&quot;bt_jieshouqi_2025/GNU ARM v10.3.1 - Default/bt_jieshouqi_2025.map&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/GNU ARM v10.3.1 - Default/bt_jieshouqi_2025.map&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;bt_multicentral_multiperipheral_dual_topology/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_multicentral_multiperipheral_dual_topology/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sl_iostream.c&quot; tooltip=&quot;bt_jieshouqi_2025/gecko_sdk_4.1.2/platform/service/iostream/src/sl_iostream.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_jieshouqi_2025/gecko_sdk_4.1.2/platform/service/iostream/src/sl_iostream.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.silabs.ss.tool.uc.editor.html.UCComponentEditor&quot; name=&quot;.bt_qingjiao_2025.rail_util_pa.cedit&quot; tooltip=&quot;bt_qingjiao_2025/.uceditor/.bt_qingjiao_2025.rail_util_pa.cedit&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_qingjiao_2025/.uceditor/.bt_qingjiao_2025.rail_util_pa.cedit&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.binaryEditor&quot; name=&quot;bt_qingjiao_2025.s37&quot; tooltip=&quot;bt_qingjiao_2025/GNU ARM v10.3.1 - Default/bt_qingjiao_2025.s37&quot;>&#xD;&#xA;&lt;persistable path=&quot;/bt_qingjiao_2025/GNU ARM v10.3.1 - Default/bt_qingjiao_2025.s37&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;pt124b.c&quot; tooltip=&quot;ZJZM_V3.01_PRESS_20250222/pt124b.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZJZM_V3.01_PRESS_20250222/pt124b.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; tooltip=&quot;ZJZM_V3.01_ANGLE_20230602/app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/ZJZM_V3.01_ANGLE_20230602/app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_gS6tMX2eEfCdZppseCV2hQ" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_gS6tMn2eEfCdZppseCV2hQ" x="0" y="0" width="1703" height="1399">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets>&#xD;&#xA;&lt;workingSet IMemento.internal.id=&quot;bt_ceju_2026&quot;/>&#xD;&#xA;&lt;/workingSets>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1740189097058"/>
    <tags>topLevel</tags>
    <tags>shellMinimized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tMn2eEfCdZppseCV2hQ" selectedElement="_gS6tM32eEfCdZppseCV2hQ" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_gS6tM32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_gS6tPn2eEfCdZppseCV2hQ">
        <children xsi:type="advanced:Perspective" xmi:id="_gS6tNH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.api.ui.SimplicityPerspective" selectedElement="_gS6tNX2eEfCdZppseCV2hQ" label="Launcher" iconURI="platform:/plugin/com.silabs.ss.tool.launcher.ui/icons/launcher.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideMenuSC:org.eclipse.ui.edit.text.openLocalFile,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.silabs.ss.tool.launcher.product.actionSet2</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:com.silabs.ss.framework.debugger.ui.attachActionSet</tags>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tNX2eEfCdZppseCV2hQ" selectedElement="_gS6tNn2eEfCdZppseCV2hQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tNn2eEfCdZppseCV2hQ" containerData="2500" selectedElement="_gS6tN32eEfCdZppseCV2hQ">
              <children xsi:type="basic:PartStack" xmi:id="_gS6tN32eEfCdZppseCV2hQ" elementId="topLeft" containerData="5000" selectedElement="_gS6tOH2eEfCdZppseCV2hQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_gS6tOH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.internal.api.device.ui.DeviceView" ref="_gS778H2eEfCdZppseCV2hQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Simplicity Studio</tags>
                  <tags>NoClose</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_gS6tOX2eEfCdZppseCV2hQ" elementId="bottomLeft" containerData="5000" selectedElement="_gS6tOn2eEfCdZppseCV2hQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_gS6tOn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.internal.ui.ProductsView" ref="_gS77_32eEfCdZppseCV2hQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Simplicity Studio</tags>
                  <tags>NoClose</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tO32eEfCdZppseCV2hQ" containerData="7500" selectedElement="_gS6tPX2eEfCdZppseCV2hQ" horizontal="true">
              <children xsi:type="advanced:Placeholder" xmi:id="_gS6tPH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.editorss" toBeRendered="false" containerData="3000" ref="_gS774X2eEfCdZppseCV2hQ"/>
              <children xsi:type="advanced:Placeholder" xmi:id="_gS6tPX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.internal.ui.VueLauncherView" containerData="7000" ref="_gS78An2eEfCdZppseCV2hQ" closeable="true">
                <tags>View</tags>
                <tags>categoryTag:Simplicity Studio</tags>
                <tags>Standalone</tags>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_gS6tPn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.framework.ide.ui.MCUDevelopmentPerspective" selectedElement="_gS6tP32eEfCdZppseCV2hQ" label="Simplicity IDE" iconURI="platform:/plugin/com.silabs.ss.framework.ide.ui/icons/simplicity_ide.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:com.silabs.ss.tool.launcher.product.actionSet2</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:com.silabs.ss.framework.debugger.ui.attachActionSet</tags>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.actionSet:com.silabs.ss.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.profileActionSet</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.folder</tags>
          <tags>persp.newWizSC:org.eclipse.ui.wizards.new.file</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:com.silabs.ss.framework.ide.ui.MCUCompactDevelopmentPerspective</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.callHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.typeHierarchy</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.BookmarkView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tP32eEfCdZppseCV2hQ" selectedElement="_gS6tQH2eEfCdZppseCV2hQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tQH2eEfCdZppseCV2hQ" containerData="1757" selectedElement="_gS6tQX2eEfCdZppseCV2hQ">
              <children xsi:type="basic:PartStack" xmi:id="_gS6tQX2eEfCdZppseCV2hQ" elementId="PartStack@6a8695f8" containerData="7231" selectedElement="_gS6tQn2eEfCdZppseCV2hQ">
                <tags>active</tags>
                <tags>noFocus</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_gS6tQn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_gS78BX2eEfCdZppseCV2hQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_gS6tQ32eEfCdZppseCV2hQ" elementId="bottomLeft" containerData="2769" selectedElement="_gS6tRH2eEfCdZppseCV2hQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_gS6tRH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.internal.api.device.ui.DeviceView" ref="_gS778H2eEfCdZppseCV2hQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Simplicity Studio</tags>
                  <tags>active</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_gS6tRX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_gS8ieH2eEfCdZppseCV2hQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_gS6tRn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_gS8isH2eEfCdZppseCV2hQ" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tR32eEfCdZppseCV2hQ" containerData="8243" selectedElement="_gS6tSH2eEfCdZppseCV2hQ" horizontal="true">
              <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tSH2eEfCdZppseCV2hQ" containerData="1500" selectedElement="_gS6tSX2eEfCdZppseCV2hQ">
                <children xsi:type="advanced:Placeholder" xmi:id="_gS6tSX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.editorss" containerData="6740" ref="_gS774X2eEfCdZppseCV2hQ"/>
                <children xsi:type="basic:PartStack" xmi:id="_gS6tSn2eEfCdZppseCV2hQ" elementId="bottomRight" containerData="3260" selectedElement="_gS6tTH2eEfCdZppseCV2hQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_gS6tS32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.ProblemView" ref="_gS8ien2eEfCdZppseCV2hQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gS6tTH2eEfCdZppseCV2hQ" elementId="org.eclipse.search.ui.views.SearchView" ref="_gS8ifX2eEfCdZppseCV2hQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gS6tTX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.callHierarchy" ref="_gS8ioH2eEfCdZppseCV2hQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:C/C++</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_gS6tTn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_gS8io32eEfCdZppseCV2hQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tT32eEfCdZppseCV2hQ" toBeRendered="false" containerData="8500">
                <children xsi:type="basic:PartStack" xmi:id="_gS6tUH2eEfCdZppseCV2hQ" elementId="topRight" toBeRendered="false" containerData="1500">
                  <children xsi:type="advanced:Placeholder" xmi:id="_gS6tUX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.ContentOutline" toBeRendered="false" ref="_gS8ieH2eEfCdZppseCV2hQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tUn2eEfCdZppseCV2hQ" toBeRendered="false" containerData="8500">
                  <children xsi:type="basic:PartStack" xmi:id="_gS6tU32eEfCdZppseCV2hQ" elementId="middleRight" toBeRendered="false" containerData="1500"/>
                  <children xsi:type="basic:PartStack" xmi:id="_gS6tVH2eEfCdZppseCV2hQ" elementId="bottomRight" toBeRendered="false" containerData="8500">
                    <children xsi:type="advanced:Placeholder" xmi:id="_gS6tVX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_gS8ieX2eEfCdZppseCV2hQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                    </children>
                  </children>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_gS6tVn2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.NetworkAnalyzerPerspective" selectedElement="_gS6tV32eEfCdZppseCV2hQ" label="Network Analyzer" iconURI="platform:/plugin/com.silabs.na.ui/icon/perspective_na.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,"/>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:com.silabs.ss.framework.debugger.ui.attachActionSet</tags>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:com.silabs.ss.tool.launcher.product.actionSet2</tags>
          <tags>persp.viewSC:com.ember.workbench.gui.view.AdapterView</tags>
          <tags>persp.viewSC:com.ember.workbench.gui.view.FilterManager</tags>
          <tags>persp.viewSC:com.ember.workbench.gui.view.ConnectivityView</tags>
          <tags>persp.viewSC:com.ember.workbench.gui.view.RadioInfoView</tags>
          <tags>persp.viewSC:com.ember.workbench.gui.view.DiffView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProgressView</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.viewSC:com.ember.workbench.scenario.gui.ScenarioView</tags>
          <tags>persp.viewSC:com.ember.workbench.appcontroller.gui.AppControllerView</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tV32eEfCdZppseCV2hQ" selectedElement="_gS6tWH2eEfCdZppseCV2hQ" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tWH2eEfCdZppseCV2hQ" containerData="2500" selectedElement="_gS6tWX2eEfCdZppseCV2hQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tWX2eEfCdZppseCV2hQ" containerData="5000" selectedElement="_gS6tWn2eEfCdZppseCV2hQ">
                <children xsi:type="basic:PartStack" xmi:id="_gS6tWn2eEfCdZppseCV2hQ" elementId="left" containerData="5000" selectedElement="_gS6tW32eEfCdZppseCV2hQ">
                  <children xsi:type="advanced:Placeholder" xmi:id="_gS6tW32eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.internal.api.device.ui.DeviceView" ref="_gS778H2eEfCdZppseCV2hQ" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:Simplicity Studio</tags>
                  </children>
                </children>
                <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tXH2eEfCdZppseCV2hQ" containerData="5000">
                  <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tXX2eEfCdZppseCV2hQ" toBeRendered="false" containerData="5000">
                    <children xsi:type="basic:PartStack" xmi:id="_gS6tXn2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.AdapterViewMStack" toBeRendered="false" containerData="5000">
                      <children xsi:type="advanced:Placeholder" xmi:id="_gS6tX32eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.AdapterView" toBeRendered="false" ref="_gS8i132eEfCdZppseCV2hQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:Ember Desktop</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_gS6tYH2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.LargeFileView" toBeRendered="false" ref="_gS8i3H2eEfCdZppseCV2hQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:Ember Desktop</tags>
                      </children>
                      <children xsi:type="advanced:Placeholder" xmi:id="_gS6tYX2eEfCdZppseCV2hQ" elementId="com.ember.workbench.appcontroller.gui.AppControllerView" toBeRendered="false" ref="_gS8i432eEfCdZppseCV2hQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:Sample Application Scenarios</tags>
                      </children>
                    </children>
                    <children xsi:type="basic:PartStack" xmi:id="_gS6tYn2eEfCdZppseCV2hQ" elementId="com.ember.workbench.testrunner.gui.TestRunnerViewMStack" toBeRendered="false" containerData="5000">
                      <children xsi:type="advanced:Placeholder" xmi:id="_gS6tY32eEfCdZppseCV2hQ" elementId="com.ember.workbench.testrunner.gui.TestRunnerView" toBeRendered="false" ref="_gS8i5H2eEfCdZppseCV2hQ" closeable="true">
                        <tags>View</tags>
                        <tags>categoryTag:Ember Desktop</tags>
                      </children>
                    </children>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_gS6tZH2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.RadioInfoViewMStack" containerData="5000" selectedElement="_gS6tZX2eEfCdZppseCV2hQ">
                    <children xsi:type="advanced:Placeholder" xmi:id="_gS6tZX2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.RadioInfoView" ref="_gS8i2H2eEfCdZppseCV2hQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ember Desktop</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_gS6tZn2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.DiffView" ref="_gS8i232eEfCdZppseCV2hQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ember Desktop</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_gS6tZ32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_gS8iuH2eEfCdZppseCV2hQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                      <tags>categoryTag:Ember Desktop</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_gS6taH2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.PacketView" toBeRendered="false" ref="_gS8i3X2eEfCdZppseCV2hQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ember Desktop</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_gS6taX2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.BytesView" toBeRendered="false" ref="_gS8i3n2eEfCdZppseCV2hQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ember Desktop</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_gS6tan2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.ConnectivityView" ref="_gS8i332eEfCdZppseCV2hQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ember Desktop</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_gS6ta32eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.LatencyView" toBeRendered="false" ref="_gS8i4H2eEfCdZppseCV2hQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ember Desktop</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_gS6tbH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_gS78BX2eEfCdZppseCV2hQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:General</tags>
                      <tags>active</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_gS6tbX2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.CaptureView" ref="_gS8i4X2eEfCdZppseCV2hQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ember Desktop</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_gS6tbn2eEfCdZppseCV2hQ" elementId="com.ember.workbench.scenario.gui.ScenarioView" toBeRendered="false" ref="_gS8i4n2eEfCdZppseCV2hQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Sample Application Scenarios</tags>
                    </children>
                    <children xsi:type="advanced:Placeholder" xmi:id="_gS6tb32eEfCdZppseCV2hQ" elementId="com.ember.workbench.ip.tcp.TcpStreamView" toBeRendered="false" ref="_gS8i5X2eEfCdZppseCV2hQ" closeable="true">
                      <tags>View</tags>
                      <tags>categoryTag:Ember Desktop</tags>
                    </children>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_gS6tcH2eEfCdZppseCV2hQ" elementId="bottomleft" toBeRendered="false" containerData="5000"/>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_gS6tcX2eEfCdZppseCV2hQ" containerData="7500">
              <children xsi:type="advanced:Placeholder" xmi:id="_gS6tcn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.editorss" containerData="5000" ref="_gS774X2eEfCdZppseCV2hQ"/>
              <children xsi:type="basic:PartStack" xmi:id="_gS6tc32eEfCdZppseCV2hQ" elementId="bottom" toBeRendered="false" containerData="5000"/>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_gS6tdH2eEfCdZppseCV2hQ" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_gS6tdX2eEfCdZppseCV2hQ" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_gS773H2eEfCdZppseCV2hQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_gS6tdn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_gS77332eEfCdZppseCV2hQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_gS6td32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_gS774H2eEfCdZppseCV2hQ" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS773H2eEfCdZppseCV2hQ" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view pageId=&quot;context-help-page&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
      <menus xmi:id="_gS773X2eEfCdZppseCV2hQ" elementId="org.eclipse.help.ui.HelpView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS773n2eEfCdZppseCV2hQ" elementId="org.eclipse.help.ui.HelpView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS77332eEfCdZppseCV2hQ" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS774H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_gS774X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.editorss" selectedElement="_gS774n2eEfCdZppseCV2hQ">
      <children xsi:type="basic:PartStack" xmi:id="_gS774n2eEfCdZppseCV2hQ" elementId="PartStack@3291bee8" selectedElement="_gS777H2eEfCdZppseCV2hQ">
        <children xsi:type="basic:Part" xmi:id="_gS77432eEfCdZppseCV2hQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="app.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; partName=&quot;app.c&quot; title=&quot;app.c&quot; tooltip=&quot;bt_jieshouqi_2025/app.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/bt_jieshouqi_2025/app.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;47936&quot; selectionTopPixel=&quot;18353&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_gS775H2eEfCdZppseCV2hQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="app.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; partName=&quot;app.c&quot; title=&quot;app.c&quot; tooltip=&quot;bt_yali_2025/app.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/bt_yali_2025/app.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;17322&quot; selectionTopPixel=&quot;6312&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_gS776H2eEfCdZppseCV2hQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="app.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; partName=&quot;app.c&quot; title=&quot;app.c&quot; tooltip=&quot;jieshouqi_2025/app.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/jieshouqi_2025/app.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;16778&quot; selectionTopPixel=&quot;15317&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_gS777H2eEfCdZppseCV2hQ" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="app.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;app.c&quot; partName=&quot;app.c&quot; title=&quot;app.c&quot; tooltip=&quot;pw_br/app.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/pw_br/app.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;0&quot; selectionTopPixel=&quot;1122&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS778H2eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.internal.api.device.ui.DeviceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Adapters" iconURI="platform:/plugin/com.silabs.ss.platform.device.ui/icons/devices_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.platform.internal.api.device.ui.DeviceView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.platform.device.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Simplicity Studio</tags>
      <menus xmi:id="_gS778X2eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.internal.api.device.ui.DeviceView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS77832eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.internal.api.device.ui.DeviceView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS77_32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.internal.ui.ProductsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="My Products" iconURI="platform:/plugin/com.silabs.ss.tool.launcher.ui/icons/folder.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.launcher.internal.ui.ProductsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.launcher.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Simplicity Studio</tags>
      <menus xmi:id="_gS78AH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.internal.ui.ProductsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS78AX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.internal.ui.ProductsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS78An2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.internal.ui.VueLauncherView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="VueLauncher" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.launcher.internal.ui.viewers.VueLauncherView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.launcher.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Simplicity Studio</tags>
      <menus xmi:id="_gS78A32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.internal.ui.VueLauncherView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS78BH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.internal.ui.VueLauncherView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS78BX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; currentWorkingSetName=&quot;Solutions&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;1&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <menus xmi:id="_gS78Bn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8ibn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8ieH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8ieX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8ien2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot; partName=&quot;Problems&quot;>&#xD;&#xA;&lt;expanded>&#xD;&#xA;&lt;category IMemento.internal.id=&quot;Errors&quot;/>&#xD;&#xA;&lt;category IMemento.internal.id=&quot;Warnings&quot;/>&#xD;&#xA;&lt;/expanded>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;90&quot; org.eclipse.ui.ide.markerType=&quot;90&quot; org.eclipse.ui.ide.pathField=&quot;120&quot; org.eclipse.ui.ide.resourceField=&quot;90&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;1013&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_gS8ie32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8ifH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8ifX2eEfCdZppseCV2hQ" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view isPinned=&quot;false&quot;>&#xD;&#xA;&lt;view IMemento.internal.id=&quot;&quot; org.eclipse.search.lastActivation=&quot;0&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_gS8ifn2eEfCdZppseCV2hQ" elementId="org.eclipse.search.ui.views.SearchView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8ijn2eEfCdZppseCV2hQ" elementId="org.eclipse.search.ui.views.SearchView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8ioH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.callHierarchy" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view showFilesInLabels=&quot;false&quot; variableFilter=&quot;false&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
      <menus xmi:id="_gS8ioX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.callHierarchy">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8ion2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.callHierarchy" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8io32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>highlighted</tags>
      <menus xmi:id="_gS8ipH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8ipn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.console.ConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8isH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8isX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gS8isn2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8is32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.DebugView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8itH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gS8itX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.RegisterView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8itn2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.RegisterView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8it32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8iuH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/com.ember.core/icons/progress.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.ExtensionFactory"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>categoryTag:Ember Desktop</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8iuX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gS8iun2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8iu32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8ivH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gS8ivX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8ivn2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.BreakpointView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8iv32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gS8iwH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.ExpressionView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8iwX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.ExpressionView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8iwn2eEfCdZppseCV2hQ" elementId="org.eclipse.pde.runtime.LogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8iw32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gS8ixH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.MemoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8ixX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.MemoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8ixn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.SignalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8ix32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.ModuleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gS8iyH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.ModuleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8iyX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.ModuleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8iyn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.executablesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8iy32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8izH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view disassembly.syncActiveContext=&quot;true&quot; disassembly.trackExpression=&quot;false&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gS8izX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8izn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8iz32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i0H2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gS8i0X2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8i0n2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i032eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i1H2eEfCdZppseCV2hQ" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_gS8i1X2eEfCdZppseCV2hQ" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8i1n2eEfCdZppseCV2hQ" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i132eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.AdapterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Adapters" iconURI="platform:/plugin/com.ember.core/icons/backchannel.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
      <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.AdapterView"/>
      <tags>View</tags>
      <tags>categoryTag:Ember Desktop</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i2H2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.RadioInfoView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Radio Info" iconURI="platform:/plugin/com.ember.core/icons/nodelqi.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
      <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.RadioInfoView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Ember Desktop</tags>
      <menus xmi:id="_gS8i2X2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.RadioInfoView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_gS8i2n2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.RadioInfoView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i232eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.DiffView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Event Difference" iconURI="platform:/plugin/com.ember.core/icons/diff.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
      <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.DiffView"/>
      <tags>View</tags>
      <tags>categoryTag:Ember Desktop</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i3H2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.LargeFileView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/com.ember.core/icons/ember_large.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
      <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.LargeFileView"/>
      <tags>View</tags>
      <tags>categoryTag:Ember Desktop</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i3X2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.PacketView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Event Detail" iconURI="platform:/plugin/com.ember.core/icons/packetview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
      <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.PacketView"/>
      <tags>View</tags>
      <tags>categoryTag:Ember Desktop</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i3n2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.BytesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Hex Dump" iconURI="platform:/plugin/com.ember.core/icons/binary.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
      <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.BytesView"/>
      <tags>View</tags>
      <tags>categoryTag:Ember Desktop</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i332eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.ConnectivityView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Connectivity" iconURI="platform:/plugin/com.ember.core/icons/connectivity.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
      <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.ConnectivityView"/>
      <tags>View</tags>
      <tags>categoryTag:Ember Desktop</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i4H2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.LatencyView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Latency" iconURI="platform:/plugin/com.ember.core/icons/stopwatch.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
      <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.LatencyView"/>
      <tags>View</tags>
      <tags>categoryTag:Ember Desktop</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i4X2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.CaptureView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Data Capture" iconURI="platform:/plugin/com.ember.core/icons/captureoptions.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
      <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.CaptureView"/>
      <tags>View</tags>
      <tags>categoryTag:Ember Desktop</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i4n2eEfCdZppseCV2hQ" elementId="com.ember.workbench.scenario.gui.ScenarioView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Sample Application Wizard" iconURI="platform:/plugin/com.silabs.foundation.scenario/icons/scenario_small.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.silabs.foundation.scenario"/>
      <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.scenario.gui.ScenarioView"/>
      <tags>View</tags>
      <tags>categoryTag:Sample Application Scenarios</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i432eEfCdZppseCV2hQ" elementId="com.ember.workbench.appcontroller.gui.AppControllerView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Application Controller" iconURI="platform:/plugin/com.silabs.foundation.scenario/icons/appcontroller.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.silabs.foundation.scenario"/>
      <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.appcontroller.gui.AppControllerView"/>
      <tags>View</tags>
      <tags>categoryTag:Sample Application Scenarios</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i5H2eEfCdZppseCV2hQ" elementId="com.ember.workbench.testrunner.gui.TestRunnerView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tests" iconURI="platform:/plugin/com.ember.testrunner/icons/view_test.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.ember.testrunner"/>
      <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.testrunner.gui.TestRunnerView"/>
      <tags>View</tags>
      <tags>categoryTag:Ember Desktop</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_gS8i5X2eEfCdZppseCV2hQ" elementId="com.ember.workbench.ip.tcp.TcpStreamView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TCP stream" iconURI="platform:/plugin/com.ember.ip/icons/tcp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.ember.ip"/>
      <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.ip.tcp.TcpStreamView"/>
      <tags>View</tags>
      <tags>categoryTag:Ember Desktop</tags>
    </sharedElements>
    <trimBars xmi:id="_gS8i5n2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.ui.workbench">
      <children xsi:type="menu:ToolBar" xmi:id="_gS8i532eEfCdZppseCV2hQ" elementId="group.file" toBeRendered="false" visible="false">
        <tags>toolbarSeparator</tags>
        <tags>Draggable</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_gS8i6H2eEfCdZppseCV2hQ" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gS8i6X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_gS8i8X2eEfCdZppseCV2hQ" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_gTD3SH2eEfCdZppseCV2hQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gS8i-n2eEfCdZppseCV2hQ" elementId="group.edit" toBeRendered="false" visible="false">
        <tags>toolbarSeparator</tags>
        <tags>Draggable</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_gS8i-32eEfCdZppseCV2hQ" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gS8i_H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.workbench.edit" toBeRendered="false" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gS8jAX2eEfCdZppseCV2hQ" elementId="additions" toBeRendered="false" visible="false">
        <tags>toolbarSeparator</tags>
        <tags>Draggable</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_gS8jAn2eEfCdZppseCV2hQ" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gS8jCH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gS8jDn2eEfCdZppseCV2hQ" elementId="org.eclipse.search.searchActionSet" toBeRendered="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gS8jEn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" toBeRendered="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gS8jFX2eEfCdZppseCV2hQ" elementId="group.nav" toBeRendered="false" visible="false">
        <tags>toolbarSeparator</tags>
        <tags>Draggable</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_gS8jFn2eEfCdZppseCV2hQ" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gS9JcH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.workbench.navigate" visible="false">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_gS9Jdn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_gTDQ-32eEfCdZppseCV2hQ"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gS9Je32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.editor.CEditor" toBeRendered="false" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gS9JfH2eEfCdZppseCV2hQ" elementId="group.editor" toBeRendered="false" visible="false">
        <tags>toolbarSeparator</tags>
        <tags>Draggable</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_gS9JfX2eEfCdZppseCV2hQ" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gS9Jfn2eEfCdZppseCV2hQ" elementId="group.help" toBeRendered="false" visible="false">
        <tags>toolbarSeparator</tags>
        <tags>Draggable</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_gS9Jf32eEfCdZppseCV2hQ" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_gS9JgH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.workbench.help" toBeRendered="false" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_gS9JpH2eEfCdZppseCV2hQ" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_gS9JqH2eEfCdZppseCV2hQ" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_gS9JqX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_gS9Jrn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_gS9Jr32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_gS9JsH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_gS9JtH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_gS9JtX2eEfCdZppseCV2hQ" elementId="topLeft(IDEWindow).(com.silabs.ss.framework.ide.ui.MCUDevelopmentPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_gS9Jtn2eEfCdZppseCV2hQ" elementId="bottomLeft(IDEWindow).(com.silabs.ss.framework.ide.ui.MCUDevelopmentPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_gS9Jt32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="_gS9JuH2eEfCdZppseCV2hQ" elementId="bottomRight(IDEWindow).(com.silabs.ss.framework.ide.ui.MCUDevelopmentPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
  </children>
  <bindingTables xmi:id="_gS9JuX2eEfCdZppseCV2hQ" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_gS9K532eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9Jun2eEfCdZppseCV2hQ" keySequence="CTRL+1" command="_gTDQ8n2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Ju32eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+L" command="_gTD3lX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9JvH2eEfCdZppseCV2hQ" keySequence="CTRL+V" command="_gTDQNX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9JvX2eEfCdZppseCV2hQ" keySequence="CTRL+A" command="_gTD3tn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Jvn2eEfCdZppseCV2hQ" keySequence="CTRL+C" command="_gS_lzn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Jv32eEfCdZppseCV2hQ" keySequence="CTRL+X" command="_gTD3O32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9JwH2eEfCdZppseCV2hQ" keySequence="CTRL+Y" command="_gTD36n2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9JwX2eEfCdZppseCV2hQ" keySequence="CTRL+Z" command="_gTD3Mn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Jwn2eEfCdZppseCV2hQ" keySequence="ALT+PAGE_UP" command="_gTD3_H2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Jw32eEfCdZppseCV2hQ" keySequence="ALT+PAGE_DOWN" command="_gTDQrX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9JxH2eEfCdZppseCV2hQ" keySequence="SHIFT+INSERT" command="_gTDQNX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9JxX2eEfCdZppseCV2hQ" keySequence="ALT+F11" command="_gTDQgH2eEfCdZppseCV2hQ">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_gS9Jxn2eEfCdZppseCV2hQ" keySequence="CTRL+F10" command="_gTDQXn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Jx32eEfCdZppseCV2hQ" keySequence="CTRL+INSERT" command="_gS_lzn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9JyH2eEfCdZppseCV2hQ" keySequence="CTRL+PAGE_UP" command="_gTD3YX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9JyX2eEfCdZppseCV2hQ" keySequence="CTRL+PAGE_DOWN" command="_gTDQ_H2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Jyn2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+F3" command="_gTD3Un2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Jy32eEfCdZppseCV2hQ" keySequence="SHIFT+DEL" command="_gTD3O32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9JzH2eEfCdZppseCV2hQ" keySequence="ALT+/" command="_gTDRMH2eEfCdZppseCV2hQ">
      <tags>locale:zh</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_gS9JzX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.textEditorScope" bindingContext="_gS9K7H2eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9Jzn2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+CR" command="_gTD3UH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Jz32eEfCdZppseCV2hQ" keySequence="CTRL+BS" command="_gS_lyH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J0H2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+Q" command="_gTDQ3X2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J0X2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+J" command="_gTDQyX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J0n2eEfCdZppseCV2hQ" keySequence="CTRL++" command="_gTDQfn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J032eEfCdZppseCV2hQ" keySequence="CTRL+-" command="_gTD3mH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J1H2eEfCdZppseCV2hQ" keySequence="ALT+CTRL+J" command="_gTDQ6X2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J1X2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+A" command="_gTDQKn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J1n2eEfCdZppseCV2hQ" keySequence="CTRL+J" command="_gTDQY32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J132eEfCdZppseCV2hQ" keySequence="CTRL+L" command="_gTD3Kn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J2H2eEfCdZppseCV2hQ" keySequence="CTRL+D" command="_gTDQcH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J2X2eEfCdZppseCV2hQ" keySequence="CTRL+=" command="_gTDQfn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J2n2eEfCdZppseCV2hQ" keySequence="ALT+CTRL+/" command="_gTD3pn2eEfCdZppseCV2hQ">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_gS9J232eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+Y" command="_gS_lwH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J3H2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+DEL" command="_gTDRNX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J3X2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+X" command="_gS_l2n2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J3n2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+Y" command="_gTD3ln2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J332eEfCdZppseCV2hQ" keySequence="CTRL+DEL" command="_gTD3LH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J4H2eEfCdZppseCV2hQ" keySequence="ALT+ARROW_UP" command="_gTD3-32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J4X2eEfCdZppseCV2hQ" keySequence="ALT+ARROW_DOWN" command="_gTDQun2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J4n2eEfCdZppseCV2hQ" keySequence="SHIFT+END" command="_gTD3oX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J432eEfCdZppseCV2hQ" keySequence="SHIFT+HOME" command="_gTD3fH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J5H2eEfCdZppseCV2hQ" keySequence="END" command="_gTD3Yn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J5X2eEfCdZppseCV2hQ" keySequence="INSERT" command="_gTDQV32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J5n2eEfCdZppseCV2hQ" keySequence="F2" command="_gTDQ_n2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J532eEfCdZppseCV2hQ" keySequence="HOME" command="_gTD3o32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J6H2eEfCdZppseCV2hQ" keySequence="ALT+CTRL+ARROW_UP" command="_gTD30n2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J6X2eEfCdZppseCV2hQ" keySequence="ALT+CTRL+ARROW_DOWN" command="_gTD3xn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J6n2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+INSERT" command="_gTDQsn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J632eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_gTD3pH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J7H2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_gTDQvX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J7X2eEfCdZppseCV2hQ" keySequence="CTRL+F10" command="_gTD3TH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J7n2eEfCdZppseCV2hQ" keySequence="CTRL+END" command="_gTDQwH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J732eEfCdZppseCV2hQ" keySequence="CTRL+ARROW_UP" command="_gTDQk32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J8H2eEfCdZppseCV2hQ" keySequence="CTRL+ARROW_DOWN" command="_gTD4EH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J8X2eEfCdZppseCV2hQ" keySequence="CTRL+ARROW_LEFT" command="_gS_lyX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J8n2eEfCdZppseCV2hQ" keySequence="CTRL+ARROW_RIGHT" command="_gTDQ132eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J832eEfCdZppseCV2hQ" keySequence="CTRL+HOME" command="_gTDQNH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J9H2eEfCdZppseCV2hQ" keySequence="CTRL+NUMPAD_MULTIPLY" command="_gTDQ232eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J9X2eEfCdZppseCV2hQ" keySequence="CTRL+NUMPAD_ADD" command="_gTD3wX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J9n2eEfCdZppseCV2hQ" keySequence="CTRL+NUMPAD_SUBTRACT" command="_gTD3T32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J932eEfCdZppseCV2hQ" keySequence="CTRL+NUMPAD_DIVIDE" command="_gTDQmH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J-H2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_gTDQ532eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J-X2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_gTDQWH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J-n2eEfCdZppseCV2hQ" keySequence="SHIFT+CR" command="_gTD3on2eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9J-32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_gS9K8H2eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9J_H2eEfCdZppseCV2hQ" keySequence="ALT+CTRL+SHIFT+C" command="_gTD4En2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J_X2eEfCdZppseCV2hQ" keySequence="CTRL+TAB" command="_gTD4DH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J_n2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+P" command="_gTD3cH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9J_32eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+T" command="_gTDRHH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KAH2eEfCdZppseCV2hQ" keySequence="CTRL+7" command="_gTDQOH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KAX2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+H" command="_gTDQaH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KAn2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+N" command="_gTDQ1n2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KA32eEfCdZppseCV2hQ" keySequence="CTRL+/" command="_gTDQOH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KBH2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+O" command="_gTD3TX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KBX2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+A" command="_gTD3VH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KBn2eEfCdZppseCV2hQ" keySequence="ALT+CTRL+S" command="_gTD38H2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KB32eEfCdZppseCV2hQ" keySequence="CTRL+#" command="_gTDRD32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KCH2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+C" command="_gTDQOH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KCX2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+F" command="_gTD4DX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KCn2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+G" command="_gS_lw32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KC32eEfCdZppseCV2hQ" keySequence="ALT+CTRL+H" command="_gTDQ-H2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KDH2eEfCdZppseCV2hQ" keySequence="ALT+CTRL+I" command="_gTDQPX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KDX2eEfCdZppseCV2hQ" keySequence="CTRL+T" command="_gTDQxH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KDn2eEfCdZppseCV2hQ" keySequence="CTRL+I" command="_gTDQyH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KD32eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+/" command="_gTD3v32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KEH2eEfCdZppseCV2hQ" keySequence="CTRL+O" command="_gTDQGn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KEX2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+R" command="_gTD3IH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KEn2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+S" command="_gTDQYH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KE32eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+T" command="_gTDREn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KFH2eEfCdZppseCV2hQ" keySequence="CTRL+G" command="_gTD35X2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KFX2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+L" command="_gTD3yH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KFn2eEfCdZppseCV2hQ" keySequence="CTRL+=" command="_gTDRD32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KF32eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+M" command="_gTDQK32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KGH2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+O" command="_gTD3P32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KGX2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+Z" command="_gTD3QH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KGn2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+\" command="_gTDQOX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KG32eEfCdZppseCV2hQ" keySequence="F3" command="_gTD4FX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KHH2eEfCdZppseCV2hQ" keySequence="F4" command="_gTD3uX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KHX2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+ARROW_UP" command="_gTD3J32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KHn2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_gTDQsH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KH32eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+ARROW_UP" command="_gTD3gH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KIH2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+ARROW_DOWN" command="_gTD4Dn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KIX2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+ARROW_LEFT" command="_gTDRMX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KIn2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_gTD3jH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KI32eEfCdZppseCV2hQ" keySequence="ALT+C" command="_gTDQOn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KJH2eEfCdZppseCV2hQ" keySequence="SHIFT+TAB" command="_gTDQaX2eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9KJX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.contexts.window" bindingContext="_gS9K6H2eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9KJn2eEfCdZppseCV2hQ" keySequence="ALT+CTRL+SHIFT+L" command="_gTDQRX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KJ32eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+Q O" command="_gTDQm32eEfCdZppseCV2hQ">
      <parameters xmi:id="_gS9KKH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_gS9KKX2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+R" command="_gTD4E32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KKn2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+Q Q" command="_gTDQm32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KK32eEfCdZppseCV2hQ" keySequence="ALT+CTRL+B" command="_gTDQo32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KLH2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+S" command="_gTDQdX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KLX2eEfCdZppseCV2hQ" keySequence="CTRL+3" command="_gTDQ_X2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KLn2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+Q S" command="_gTDQm32eEfCdZppseCV2hQ">
      <parameters xmi:id="_gS9KL32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_gS9KMH2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+W" command="_gTD3On2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KMX2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+Q V" command="_gTDQm32eEfCdZppseCV2hQ">
      <parameters xmi:id="_gS9KMn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_gS9KM32eEfCdZppseCV2hQ" keySequence="ALT+CTRL+G" command="_gTDQi32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KNH2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+Q H" command="_gTDQm32eEfCdZppseCV2hQ">
      <parameters xmi:id="_gS9KNX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_gS9KNn2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+K" command="_gTDQjX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KN32eEfCdZppseCV2hQ" keySequence="CTRL+," command="_gTDQO32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KOH2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+Q L" command="_gTDQm32eEfCdZppseCV2hQ">
      <parameters xmi:id="_gS9KOX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_gS9KOn2eEfCdZppseCV2hQ" keySequence="CTRL+-" command="_gTD3nH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KO32eEfCdZppseCV2hQ" keySequence="CTRL+." command="_gTD31n2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KPH2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+B" command="_gTDQjn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KPX2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+E" command="_gTDQqn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KPn2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+Q X" command="_gTDQm32eEfCdZppseCV2hQ">
      <parameters xmi:id="_gS9KP32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_gS9KQH2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+Q Y" command="_gTDQm32eEfCdZppseCV2hQ">
      <parameters xmi:id="_gS9KQX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_gS9KQn2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+Q Z" command="_gTDQm32eEfCdZppseCV2hQ">
      <parameters xmi:id="_gS9KQ32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_gS9KRH2eEfCdZppseCV2hQ" keySequence="CTRL+P" command="_gTD3SH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KRX2eEfCdZppseCV2hQ" keySequence="CTRL+Q" command="_gTD3Wn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KRn2eEfCdZppseCV2hQ" keySequence="CTRL+S" command="_gTD3mX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KR32eEfCdZppseCV2hQ" keySequence="CTRL+W" command="_gTD39X2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KSH2eEfCdZppseCV2hQ" keySequence="CTRL+H" command="_gTDRL32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KSX2eEfCdZppseCV2hQ" keySequence="CTRL+K" command="_gTDQpX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KSn2eEfCdZppseCV2hQ" keySequence="CTRL+M" command="_gTDRKX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KS32eEfCdZppseCV2hQ" keySequence="CTRL+N" command="_gTD38X2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KTH2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+?" command="_gTDRHX2eEfCdZppseCV2hQ">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_gS9KTX2eEfCdZppseCV2hQ" keySequence="CTRL+B" command="_gTDQP32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KTn2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+Q B" command="_gTDQm32eEfCdZppseCV2hQ">
      <parameters xmi:id="_gS9KT32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_gS9KUH2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+Q C" command="_gTDQm32eEfCdZppseCV2hQ">
      <parameters xmi:id="_gS9KUX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_gS9KUn2eEfCdZppseCV2hQ" keySequence="CTRL+E" command="_gTD3KH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KU32eEfCdZppseCV2hQ" keySequence="CTRL+F" command="_gTDQfX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KVH2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+W" command="_gTD30X2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KVX2eEfCdZppseCV2hQ" keySequence="CTRL+=" command="_gTD3WX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KVn2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+N" command="_gTD3NX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KV32eEfCdZppseCV2hQ" keySequence="CTRL+_" command="_gTDRIX2eEfCdZppseCV2hQ">
      <parameters xmi:id="_gS9KWH2eEfCdZppseCV2hQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_gS9KWX2eEfCdZppseCV2hQ" keySequence="CTRL+{" command="_gTDRIX2eEfCdZppseCV2hQ">
      <parameters xmi:id="_gS9KWn2eEfCdZppseCV2hQ" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_gS9KW32eEfCdZppseCV2hQ" keySequence="SHIFT+F9" command="_gTD3Sn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KXH2eEfCdZppseCV2hQ" keySequence="ALT+ARROW_LEFT" command="_gTDQYn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KXX2eEfCdZppseCV2hQ" keySequence="ALT+ARROW_RIGHT" command="_gTD3UX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KXn2eEfCdZppseCV2hQ" keySequence="SHIFT+F5" command="_gTD3zn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KX32eEfCdZppseCV2hQ" keySequence="ALT+F7" command="_gTDQNn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KYH2eEfCdZppseCV2hQ" keySequence="F9" command="_gTD3dX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KYX2eEfCdZppseCV2hQ" keySequence="F11" command="_gTD3sX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KYn2eEfCdZppseCV2hQ" keySequence="F12" command="_gTDRM32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KY32eEfCdZppseCV2hQ" keySequence="F2" command="_gTDQPH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KZH2eEfCdZppseCV2hQ" keySequence="F5" command="_gTD3bn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KZX2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+F7" command="_gTD3s32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KZn2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+F8" command="_gTDRH32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KZ32eEfCdZppseCV2hQ" keySequence="ALT+CTRL+ARROW_LEFT" command="_gTD3Wn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KaH2eEfCdZppseCV2hQ" keySequence="ALT+CTRL+ARROW_RIGHT" command="_gTDQmX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KaX2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+F4" command="_gTD3On2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kan2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+F6" command="_gTDQe32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Ka32eEfCdZppseCV2hQ" keySequence="CTRL+F7" command="_gS_lz32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KbH2eEfCdZppseCV2hQ" keySequence="CTRL+F8" command="_gTDQ9H2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KbX2eEfCdZppseCV2hQ" keySequence="CTRL+F11" command="_gTD3c32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kbn2eEfCdZppseCV2hQ" keySequence="CTRL+F4" command="_gTD39X2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kb32eEfCdZppseCV2hQ" keySequence="CTRL+F6" command="_gTDQhX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KcH2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+F7" command="_gTDQw32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KcX2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_gTDQdH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kcn2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_gTDRJX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kc32eEfCdZppseCV2hQ" keySequence="DEL" command="_gTDQiX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KdH2eEfCdZppseCV2hQ" keySequence="ALT+?" command="_gTDRHX2eEfCdZppseCV2hQ">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_gS9KdX2eEfCdZppseCV2hQ" keySequence="ALT+-" command="_gTDQHX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kdn2eEfCdZppseCV2hQ" keySequence="ALT+CR" command="_gTDREH2eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9Kd32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_gS9K-n2eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9KeH2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+T" command="_gTDRHH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KeX2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+H" command="_gTDQaH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Ken2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+G" command="_gS_lw32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Ke32eEfCdZppseCV2hQ" keySequence="ALT+CTRL+H" command="_gTDQ-H2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KfH2eEfCdZppseCV2hQ" keySequence="ALT+CTRL+I" command="_gTDQPX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KfX2eEfCdZppseCV2hQ" keySequence="ALT+SHIFT+R" command="_gTD3IH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kfn2eEfCdZppseCV2hQ" keySequence="CTRL+G" command="_gTD35X2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kf32eEfCdZppseCV2hQ" keySequence="F3" command="_gTD4FX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KgH2eEfCdZppseCV2hQ" keySequence="F4" command="_gTD3uX2eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9KgX2eEfCdZppseCV2hQ" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_gS9K6n2eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9Kgn2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+V" command="_gTD3iH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kg32eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+C" command="_gTDRBX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KhH2eEfCdZppseCV2hQ" keySequence="ALT+ARROW_UP" command="_gS_lx32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KhX2eEfCdZppseCV2hQ" keySequence="ALT+ARROW_RIGHT" command="_gTD3sH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Khn2eEfCdZppseCV2hQ" keySequence="SHIFT+INSERT" command="_gTD3iH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kh32eEfCdZppseCV2hQ" keySequence="CTRL+INSERT" command="_gTDRBX2eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9KiH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_gS9K7X2eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9KiX2eEfCdZppseCV2hQ" keySequence="CTRL+/" command="_gTDQoH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kin2eEfCdZppseCV2hQ" keySequence="F3" command="_gTDQbn2eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9Ki32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_gS9K732eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9KjH2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+G" command="_gTD3bX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KjX2eEfCdZppseCV2hQ" keySequence="F3" command="_gTD3W32eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9Kjn2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_gS9K9H2eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9Kj32eEfCdZppseCV2hQ" keySequence="ALT+CTRL+M" command="_gTD3tH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KkH2eEfCdZppseCV2hQ" keySequence="ALT+CTRL+N" command="_gTD3xH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KkX2eEfCdZppseCV2hQ" keySequence="CTRL+T" command="_gTDRB32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kkn2eEfCdZppseCV2hQ" keySequence="CTRL+W" command="_gTDQW32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kk32eEfCdZppseCV2hQ" keySequence="CTRL+N" command="_gTDQg32eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9KlH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.debugging" bindingContext="_gS9K9n2eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9KlX2eEfCdZppseCV2hQ" keySequence="CTRL+R" command="_gS_l1n2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kln2eEfCdZppseCV2hQ" keySequence="F7" command="_gTD33X2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kl32eEfCdZppseCV2hQ" keySequence="F8" command="_gTDQUX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KmH2eEfCdZppseCV2hQ" keySequence="F5" command="_gTDQVH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KmX2eEfCdZppseCV2hQ" keySequence="F6" command="_gTD3qn2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kmn2eEfCdZppseCV2hQ" keySequence="CTRL+F2" command="_gTDRN32eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9Km32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_gS9K932eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9KnH2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+," command="_gTD3XX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KnX2eEfCdZppseCV2hQ" keySequence="CTRL+SHIFT+." command="_gTDRJ32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Knn2eEfCdZppseCV2hQ" keySequence="CTRL+G" command="_gTDRKH2eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9Kn32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_gS9K-H2eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9KoH2eEfCdZppseCV2hQ" keySequence="CTRL+G" command="_gTD37H2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KoX2eEfCdZppseCV2hQ" keySequence="HOME" command="_gTDRAH2eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9Kon2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.console" bindingContext="_gS9K8X2eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9Ko32eEfCdZppseCV2hQ" keySequence="CTRL+Z" command="_gTD3z32eEfCdZppseCV2hQ">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_gS9KpH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_gS9K-X2eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9KpX2eEfCdZppseCV2hQ" keySequence="SHIFT+F7" command="_gTDQbX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kpn2eEfCdZppseCV2hQ" keySequence="SHIFT+F8" command="_gTDRIH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kp32eEfCdZppseCV2hQ" keySequence="SHIFT+F5" command="_gTD3q32eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KqH2eEfCdZppseCV2hQ" keySequence="SHIFT+F6" command="_gTDQQH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KqX2eEfCdZppseCV2hQ" keySequence="CTRL+F5" command="_gTD3qH2eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9Kqn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_gS9K_X2eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9Kq32eEfCdZppseCV2hQ" keySequence="ALT+ARROW_LEFT" command="_gTDQxX2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KrH2eEfCdZppseCV2hQ" keySequence="ALT+ARROW_RIGHT" command="_gTDQ4n2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KrX2eEfCdZppseCV2hQ" keySequence="F3" command="_gTD4FX2eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9Krn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_gS9K7n2eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9Kr32eEfCdZppseCV2hQ" keySequence="F3" command="_gTD4FX2eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9KsH2eEfCdZppseCV2hQ" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_gS9K-32eEfCdZppseCV2hQ">
    <bindings xmi:id="_gS9KsX2eEfCdZppseCV2hQ" keySequence="ALT+Y" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Ksn2eEfCdZppseCV2hQ" keySequence="ALT+A" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Ks32eEfCdZppseCV2hQ" keySequence="ALT+B" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KtH2eEfCdZppseCV2hQ" keySequence="ALT+C" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KtX2eEfCdZppseCV2hQ" keySequence="ALT+D" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Ktn2eEfCdZppseCV2hQ" keySequence="ALT+E" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kt32eEfCdZppseCV2hQ" keySequence="ALT+F" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KuH2eEfCdZppseCV2hQ" keySequence="ALT+G" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KuX2eEfCdZppseCV2hQ" keySequence="ALT+P" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kun2eEfCdZppseCV2hQ" keySequence="ALT+R" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Ku32eEfCdZppseCV2hQ" keySequence="ALT+S" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KvH2eEfCdZppseCV2hQ" keySequence="ALT+T" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KvX2eEfCdZppseCV2hQ" keySequence="ALT+V" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kvn2eEfCdZppseCV2hQ" keySequence="ALT+W" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9Kv32eEfCdZppseCV2hQ" keySequence="ALT+H" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KwH2eEfCdZppseCV2hQ" keySequence="ALT+L" command="_gTDQGH2eEfCdZppseCV2hQ"/>
    <bindings xmi:id="_gS9KwX2eEfCdZppseCV2hQ" keySequence="ALT+N" command="_gTDQGH2eEfCdZppseCV2hQ"/>
  </bindingTables>
  <bindingTables xmi:id="_gS9Kwn2eEfCdZppseCV2hQ" bindingContext="_gS9LAH2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9Kw32eEfCdZppseCV2hQ" bindingContext="_gS9LAX2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9KxH2eEfCdZppseCV2hQ" bindingContext="_gS9LAn2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9KxX2eEfCdZppseCV2hQ" bindingContext="_gS9LA32eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9Kxn2eEfCdZppseCV2hQ" bindingContext="_gS9LBH2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9Kx32eEfCdZppseCV2hQ" bindingContext="_gS9LBX2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9KyH2eEfCdZppseCV2hQ" bindingContext="_gS9LBn2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9KyX2eEfCdZppseCV2hQ" bindingContext="_gS9LB32eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9Kyn2eEfCdZppseCV2hQ" bindingContext="_gS9LCH2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9Ky32eEfCdZppseCV2hQ" bindingContext="_gS9LCX2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9KzH2eEfCdZppseCV2hQ" bindingContext="_gS9LCn2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9KzX2eEfCdZppseCV2hQ" bindingContext="_gS9LC32eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9Kzn2eEfCdZppseCV2hQ" bindingContext="_gS9LDH2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9Kz32eEfCdZppseCV2hQ" bindingContext="_gS9LDX2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K0H2eEfCdZppseCV2hQ" bindingContext="_gS9LDn2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K0X2eEfCdZppseCV2hQ" bindingContext="_gS9LD32eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K0n2eEfCdZppseCV2hQ" bindingContext="_gS9LEH2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K032eEfCdZppseCV2hQ" bindingContext="_gS9LEX2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K1H2eEfCdZppseCV2hQ" bindingContext="_gS9LEn2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K1X2eEfCdZppseCV2hQ" bindingContext="_gS9LE32eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K1n2eEfCdZppseCV2hQ" bindingContext="_gS9LFH2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K132eEfCdZppseCV2hQ" bindingContext="_gS9LFX2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K2H2eEfCdZppseCV2hQ" bindingContext="_gS9LFn2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K2X2eEfCdZppseCV2hQ" bindingContext="_gS9LF32eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K2n2eEfCdZppseCV2hQ" bindingContext="_gS9LGH2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K232eEfCdZppseCV2hQ" bindingContext="_gS9LGX2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K3H2eEfCdZppseCV2hQ" bindingContext="_gS9LGn2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K3X2eEfCdZppseCV2hQ" bindingContext="_gS9LG32eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K3n2eEfCdZppseCV2hQ" bindingContext="_gS9LHH2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K332eEfCdZppseCV2hQ" bindingContext="_gS9LHX2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K4H2eEfCdZppseCV2hQ" bindingContext="_gS9LHn2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K4X2eEfCdZppseCV2hQ" bindingContext="_gS9LH32eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K4n2eEfCdZppseCV2hQ" bindingContext="_gS9LIH2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K432eEfCdZppseCV2hQ" bindingContext="_gS9LIX2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K5H2eEfCdZppseCV2hQ" bindingContext="_gS9LIn2eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K5X2eEfCdZppseCV2hQ" bindingContext="_gS9LI32eEfCdZppseCV2hQ"/>
  <bindingTables xmi:id="_gS9K5n2eEfCdZppseCV2hQ" bindingContext="_gS9LJH2eEfCdZppseCV2hQ"/>
  <rootContext xmi:id="_gS9K532eEfCdZppseCV2hQ" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_gS9K6H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_gS9K6X2eEfCdZppseCV2hQ" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_gS9K6n2eEfCdZppseCV2hQ" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_gS9K632eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_gS9K7H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_gS9K7X2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_gS9K7n2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_gS9K732eEfCdZppseCV2hQ" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_gS9K8H2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_gS9K8X2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_gS9K8n2eEfCdZppseCV2hQ" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_gS9K832eEfCdZppseCV2hQ" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_gS9K9H2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_gS9K9X2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.edc.ui.context.SnapshotView" name="SnapshotViewContext" description="Context for snapshot view"/>
      <children xmi:id="_gS9K9n2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_gS9K932eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_gS9K-H2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_gS9K-X2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_gS9K-n2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
      <children xmi:id="_gS9K-32eEfCdZppseCV2hQ" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
    </children>
    <children xmi:id="_gS9K_H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_gS9K_X2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_gS9K_n2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_gS9K_32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_gS9LAH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.product.actionSet2" name="Auto::com.silabs.ss.tool.launcher.product.actionSet2"/>
  <rootContext xmi:id="_gS9LAX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_gS9LAn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_gS9LA32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_gS9LBH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_gS9LBX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_gS9LBn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_gS9LB32eEfCdZppseCV2hQ" elementId="com.silabs.ss.cdt.ui.buildConfigActionSet" name="Auto::com.silabs.ss.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_gS9LCH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.framework.debugger.ui.attachActionSet" name="Auto::com.silabs.ss.framework.debugger.ui.attachActionSet"/>
  <rootContext xmi:id="_gS9LCX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_gS9LCn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_gS9LC32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_gS9LDH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_gS9LDX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_gS9LDn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_gS9LD32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_gS9LEH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_gS9LEX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_gS9LEn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_gS9LE32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_gS9LFH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_gS9LFX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_gS9LFn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_gS9LF32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_gS9LGH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_gS9LGX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_gS9LGn2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_gS9LG32eEfCdZppseCV2hQ" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset" name="Auto::org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset"/>
  <rootContext xmi:id="_gS9LHH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_gS9LHX2eEfCdZppseCV2hQ" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_gS9LHn2eEfCdZppseCV2hQ" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_gS9LH32eEfCdZppseCV2hQ" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_gS9LIH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_gS9LIX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_gS9LIn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_gS9LI32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_gS9LJH2eEfCdZppseCV2hQ" elementId="ilg.gnumcueclipse.debug.gdbjtag.jlink.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::ilg.gnumcueclipse.debug.gdbjtag.jlink.launchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_gS9LJX2eEfCdZppseCV2hQ" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LJn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.internal.apack.ui.view.AdapterPackExecutionView" label="Adapter Pack Jobs" iconURI="platform:/plugin/com.silabs.ss.platform.apack.ui/icons/function.png" tooltip="" allowMultiple="true" category="Simplicity Studio" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.platform.internal.apack.ui.view.AdapterPackExecutionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.platform.apack.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Studio</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LJ32eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.internal.apack.ui.view.UsbManagerView" label="Usb Manager" iconURI="platform:/plugin/com.silabs.ss.platform.apack.ui/icons/usb_16.png" tooltip="" allowMultiple="true" category="Simplicity Studio" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.platform.internal.apack.ui.view.UsbManagerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.platform.apack.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Studio</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LKH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.internal.api.device.ui.DeviceView" label="Debug Adapters" iconURI="platform:/plugin/com.silabs.ss.platform.device.ui/icons/devices_view.gif" tooltip="" category="Simplicity Studio" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.platform.internal.api.device.ui.DeviceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.platform.device.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Studio</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LKX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.internal.ui.ProductsView" label="My Products" iconURI="platform:/plugin/com.silabs.ss.tool.launcher.ui/icons/folder.gif" tooltip="" category="Simplicity Studio" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.launcher.internal.ui.ProductsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.launcher.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Studio</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LKn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.internal.ui.VueLauncherView" label="VueLauncher" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Simplicity Studio" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.launcher.internal.ui.viewers.VueLauncherView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.launcher.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Studio</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LK32eEfCdZppseCV2hQ" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LLH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LLX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LLn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/com.ember.core/icons/progress.gif" tooltip="" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.ExtensionFactory"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LL32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator (Deprecated)" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.navigator.ResourceNavigator"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LMH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LMX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LMn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LM32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LNH2eEfCdZppseCV2hQ" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LNX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LNn2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.AdapterView" label="Adapters" iconURI="platform:/plugin/com.ember.core/icons/backchannel.gif" tooltip="" allowMultiple="true" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.AdapterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
    <tags>View</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LN32eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.PacketView" label="Event Detail" iconURI="platform:/plugin/com.ember.core/icons/packetview.gif" tooltip="" allowMultiple="true" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.PacketView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
    <tags>View</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LOH2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.BytesView" label="Hex Dump" iconURI="platform:/plugin/com.ember.core/icons/binary.png" tooltip="" allowMultiple="true" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.BytesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
    <tags>View</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LOX2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.FilterManager" label="Expression Manager" iconURI="platform:/plugin/com.ember.core/icons/filterman.gif" tooltip="" allowMultiple="true" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.FilterManager"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
    <tags>View</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LOn2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.RadioInfoView" label="Radio Info" iconURI="platform:/plugin/com.ember.core/icons/nodelqi.gif" tooltip="" allowMultiple="true" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.RadioInfoView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
    <tags>View</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LO32eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.DiffView" label="Event Difference" iconURI="platform:/plugin/com.ember.core/icons/diff.gif" tooltip="" allowMultiple="true" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.DiffView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
    <tags>View</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LPH2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.LargeFileView" label="Search" iconURI="platform:/plugin/com.ember.core/icons/ember_large.png" tooltip="" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.LargeFileView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
    <tags>View</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LPX2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.ConnectivityView" label="Connectivity" iconURI="platform:/plugin/com.ember.core/icons/connectivity.gif" tooltip="" allowMultiple="true" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.ConnectivityView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
    <tags>View</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LPn2eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.LatencyView" label="Latency" iconURI="platform:/plugin/com.ember.core/icons/stopwatch.png" tooltip="" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.LatencyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
    <tags>View</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LP32eEfCdZppseCV2hQ" elementId="com.ember.workbench.gui.view.CaptureView" label="Data Capture" iconURI="platform:/plugin/com.ember.core/icons/captureoptions.gif" tooltip="" allowMultiple="true" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.gui.view.CaptureView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.ember.core"/>
    <tags>View</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LQH2eEfCdZppseCV2hQ" elementId="com.ember.workbench.ip.tcp.TcpStreamView" label="TCP stream" iconURI="platform:/plugin/com.ember.ip/icons/tcp.png" tooltip="" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.ip.tcp.TcpStreamView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.ember.ip"/>
    <tags>View</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LQX2eEfCdZppseCV2hQ" elementId="com.ember.workbench.testrunner.gui.TestRunnerView" label="Tests" iconURI="platform:/plugin/com.ember.testrunner/icons/view_test.gif" tooltip="" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.testrunner.gui.TestRunnerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.ember.testrunner"/>
    <tags>View</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LQn2eEfCdZppseCV2hQ" elementId="com.iar.cdt.generic.toolchain.ui.sdkinfo2" label="IAR Toolchain Explorer" iconURI="platform:/plugin/com.iar.cdt.generic.toolchain.ui/icons/wrench.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.iar.cdt.generic.toolchain.ui.SDKBrowserNavigator"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.iar.cdt.generic.toolchain.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LQ32eEfCdZppseCV2hQ" elementId="com.ember.workbench.scenario.gui.ScenarioView" label="Sample Application Wizard" iconURI="platform:/plugin/com.silabs.foundation.scenario/icons/scenario_small.png" tooltip="" category="Sample Application Scenarios" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.scenario.gui.ScenarioView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.foundation.scenario"/>
    <tags>View</tags>
    <tags>categoryTag:Sample Application Scenarios</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LRH2eEfCdZppseCV2hQ" elementId="com.ember.workbench.appcontroller.gui.AppControllerView" label="Application Controller" iconURI="platform:/plugin/com.silabs.foundation.scenario/icons/appcontroller.png" tooltip="" category="Sample Application Scenarios" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.appcontroller.gui.AppControllerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.foundation.scenario"/>
    <tags>View</tags>
    <tags>categoryTag:Sample Application Scenarios</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LRX2eEfCdZppseCV2hQ" elementId="com.ember.workbench.shell.ShellView" label="Shell" iconURI="platform:/plugin/com.silabs.foundation.shell/icons/shell.gif" tooltip="" category="Ember Desktop" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.ember.workbench.shell.ShellView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.foundation.shell"/>
    <tags>View</tags>
    <tags>categoryTag:Ember Desktop</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LRn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.framework.ide.internal.project.ui.module.ProjectModuleView" label="Project Module View" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.framework.ide.internal.project.ui.module.ProjectModuleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.framework.ide.project.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LR32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.analysis.cs0.ui.views.CSRawDataView" label="Raw Data" iconURI="platform:/plugin/com.silabs.ss.tool.analysis.cs0.ui/icons/icon_ct_raw_16x16.png" tooltip="" category="Simplicity Capacitive Sense Profiler" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.analysis.cs0.ui.views.CSRawDataView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.analysis.cs0.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Capacitive Sense Profiler</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LSH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.analysis.cs0.ui.views.CSSlidersView" label="Sliders" iconURI="platform:/plugin/com.silabs.ss.tool.analysis.cs0.ui/icons/icon_ct_sliders_16x16.png" tooltip="" category="Simplicity Capacitive Sense Profiler" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.analysis.cs0.ui.views.CSSlidersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.analysis.cs0.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Capacitive Sense Profiler</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LSX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.analysis.cs0.ui.views.CSButtonsView" label="Buttons" iconURI="platform:/plugin/com.silabs.ss.tool.analysis.cs0.ui/icons/icon_ct_buttons_16x16.png" tooltip="" category="Simplicity Capacitive Sense Profiler" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.analysis.cs0.ui.views.CSButtonsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.analysis.cs0.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Capacitive Sense Profiler</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LSn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.analysis.cs0.ui.views.CSNoiseView" label="Noise" iconURI="platform:/plugin/com.silabs.ss.tool.analysis.cs0.ui/icons/icon_ct_noise_16x16.png" tooltip="" category="Simplicity Capacitive Sense Profiler" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.analysis.cs0.ui.views.CSNoiseView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.analysis.cs0.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Capacitive Sense Profiler</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LS32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.analysis.cs0.ui.views.CSControlPanelView" label="Control Panel" iconURI="platform:/plugin/com.silabs.ss.tool.analysis.cs0.ui/icons/icon_ct_control_panel_16x16.png" tooltip="" category="Simplicity Capacitive Sense Profiler" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.analysis.cs0.ui.views.CSControlPanelView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.analysis.cs0.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Capacitive Sense Profiler</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LTH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.analysis.cs0.ui.views.CSBoardView" label="Board" iconURI="platform:/plugin/com.silabs.ss.tool.analysis.cs0.ui/icons/icon_ct_board_16x16.png" tooltip="" category="Simplicity Capacitive Sense Profiler" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.analysis.cs0.ui.views.CSBoardView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.analysis.cs0.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Capacitive Sense Profiler</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LTX2eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.views.CurrentGraphView" label="Current Graph" iconURI="platform:/plugin/com.silabs.ss.tool.energyprofiler.core.ui/icons/profiler_graph_view.png" tooltip="" category="Simplicity Energy Profiler" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.energyprofiler.internal.core.ui.views.CurrentGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.energyprofiler.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Energy Profiler</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LTn2eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.views.LiveFunctionProfileView" label="Energy Profile (live)" iconURI="platform:/plugin/com.silabs.ss.tool.energyprofiler.core.ui/icons/function_energy_view.png" tooltip="" allowMultiple="true" category="Simplicity Energy Profiler" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.energyprofiler.internal.core.ui.views.LiveFunctionProfileView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.energyprofiler.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Energy Profiler</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LT32eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.views.RangeFunctionProfileView" label="Energy Profile (range)" iconURI="platform:/plugin/com.silabs.ss.tool.energyprofiler.core.ui/icons/function_energy_view.png" tooltip="" allowMultiple="true" category="Simplicity Energy Profiler" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.energyprofiler.internal.core.ui.views.RangeFunctionProfileView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.energyprofiler.core.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Energy Profiler</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LUH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.hwconfig.ui.views.PeripheralMapping" label="Port I/O Mapping" iconURI="platform:/plugin/com.silabs.ss.tool.hwconfig.ui/icons/crossbar.png" tooltip="" category="Simplicity Configurator" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.silabs.ss.tool.hwconfig.ui.views.PeripheralMappingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.silabs.ss.tool.hwconfig.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Simplicity Configurator</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LUX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LUn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.edc.TraceView" label="EDC TCF Trace" iconURI="platform:/plugin/org.eclipse.cdt.debug.edc.ui/icons/obj16/tcf.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.edc.internal.ui.TraceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.edc.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LU32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.edc.ui.views.SnapshotView" label="Snapshot Albums" iconURI="platform:/plugin/org.eclipse.cdt.debug.edc.ui/icons/etool16/create_snapshot.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.edc.internal.ui.views.SnapshotView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.edc.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LVH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LVX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LVn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LV32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LWH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LWX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LWn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LW32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LXH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LXX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LXn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LX32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LYH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LYX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LYn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LY32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LZH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LZX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LZn2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LZ32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LaH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LaX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9Lan2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9La32eEfCdZppseCV2hQ" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LbH2eEfCdZppseCV2hQ" elementId="org.eclipse.gef.ui.palette_view" label="Palette" iconURI="platform:/plugin/org.eclipse.gef/icons/palette_view.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.gef.ui.views.palette.PaletteView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.gef"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LbX2eEfCdZppseCV2hQ" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9Lbn2eEfCdZppseCV2hQ" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_gS9Lb32eEfCdZppseCV2hQ" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LcH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LcX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_gS9Lcn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9Lc32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_gS9LdH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <trimContributions xmi:id="_gS_AEH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_gS_AEX2eEfCdZppseCV2hQ" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_gS_lsH2eEfCdZppseCV2hQ" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_gS_lsX2eEfCdZppseCV2hQ" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_gS_lv32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_lwH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_lwX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_lwn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_lw32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_lxH2eEfCdZppseCV2hQ" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_gTFsVX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_lxX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_lxn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_lx32eEfCdZppseCV2hQ" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_gTFsU32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_lyH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_lyX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_lyn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_gTFsUn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_ly32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_gTEemn2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gS_lzH2eEfCdZppseCV2hQ" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_gS_lzX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_lzn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_lz32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_l0H2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.ui.toolsCommand" commandName="Tools" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_l0X2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_gTEejH2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gS_l0n2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_gS_l032eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_l1H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_l1X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_l1n2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_l132eEfCdZppseCV2hQ" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_gTFsWX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_l2H2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_l2X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gS_l2n2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQEH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQEX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.energyprofiler.multinode.commands.ExportCSVFileCommand" commandName="Export as CSV..." category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQEn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.segger.systemview.app.project.ui.loadRecordingFromFile" commandName="Load a Recording from File." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQE32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQFH2eEfCdZppseCV2hQ" elementId="org.eclipse.gef.ui.palette_view" commandName="Palette" category="_gTEein2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQFX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQFn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQF32eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.DisconnectProfilerCommand" commandName="Disconnect Profiler" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQGH2eEfCdZppseCV2hQ" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_gTFsU32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQGX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.installer.core.ui.installchoiceCommand" commandName="Install Choice Memory Tool" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQGn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQG32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.ui.FlashToDeviceCommand" commandName="Flash to Device..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQHH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_gTFsUn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQHX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQHn2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQH32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQIH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQIX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.pti.wireshark.integration.ui.StartWiresharkDataSource" commandName="Start Wireshark Data Source" category="_gTFsYH2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTDQIn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.pti.wireshark.integration.ui.StartWiresharkDataSource.cmdParam" name="StartWiresharkDataSource.cmdParam" optional="false"/>
  </commands>
  <commands xmi:id="_gTDQI32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_gTFsXX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQJH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_gTFsYX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQJX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQJn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.framework.ide.project.ui.newSolutionDialogCommand" commandName="New Solution..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQJ32eEfCdZppseCV2hQ" elementId="com.silabs.ss.framework.ide.ui.command.commandPrompt" commandName="Open Command Line Here" description="Open an external command line at the selected location" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQKH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQKX2eEfCdZppseCV2hQ" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_gTEemH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQKn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQK32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_gTEenH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQLH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQLX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQLn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.segger.systemview.app.project.ui.addSourceToProject" commandName="Add Source to Project" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQL32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQMH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.bluetooth.ncp_commander.editor.NcpCommanderLaunchCommand" commandName="Launch NCP Commander" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQMX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQMn2eEfCdZppseCV2hQ" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_gTEemH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQM32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.analysis.cs0.ui.commands.zoomReset" commandName="Zoom Reset Command" category="_gTEej32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQNH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQNX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQNn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQN32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQOH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQOX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQOn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_gTEenH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQO32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQPH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQPX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQPn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQP32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQQH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_gTFsX32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQQX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.analysis.cs0.ui.commands.zoomOut" commandName="Zoom Out Command" category="_gTEej32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQQn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.device.ui.RemoveSoftLock" commandName="RemoveSoftLock" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQQ32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQRH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.ui.settingsCommand" commandName="Settings" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQRX2eEfCdZppseCV2hQ" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_gTEemX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQRn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQR32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_gTEekX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQSH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQSX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.edc.ui.snapshotCreation" commandName="Snapshot Creation" description="Control snapshot creation" category="_gTEek32eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTDQSn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_gTDQS32eEfCdZppseCV2hQ" elementId="com.silabs.ss.framework.ide.project.ui.command.copySourceFile" commandName="Copy Linked File into Project" description="Copy this file into project" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQTH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQTX2eEfCdZppseCV2hQ" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_gTEem32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQTn2eEfCdZppseCV2hQ" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_gTFsWn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQT32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.ui.CANopenCommand" commandName="CANopen Configurator" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQUH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_gTFsYn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQUX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQUn2eEfCdZppseCV2hQ" elementId="na.cmd.editor" commandName="Main menu bridge command - enabled when trace file is opened" category="_gTFsYH2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTDQU32eEfCdZppseCV2hQ" elementId="na.action" name="Action enum"/>
  </commands>
  <commands xmi:id="_gTDQVH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQVX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQVn2eEfCdZppseCV2hQ" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQV32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQWH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQWX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQWn2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQW32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQXH2eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.CaptureStartCommand" commandName="com.silabs.studio.energyprofiler.ui.CaptureStartCommand" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQXX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQXn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQX32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQYH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQYX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQYn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQY32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQZH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQZX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQZn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQZ32eEfCdZppseCV2hQ" elementId="com.ember.workbench.sniffer.SnifferConfiguratorHandler" commandName="Sniffer Configurator" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQaH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQaX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQan2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQa32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQbH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.edc.ui.nextSnapshot" commandName="Next Snapshot" description="Show the next snapshot in the album" category="_gTEek32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQbX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_gTFsX32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQbn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_gTFsVH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQb32eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.SaveProfilerDataCommand" commandName="Save Profiler Data" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQcH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQcX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQcn2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_gTFsWX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQc32eEfCdZppseCV2hQ" elementId="com.silabs.ide.scripting.ui.commands.runScriptCommand" commandName="Run Script" category="_gTEeln2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQdH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQdX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQdn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.pti.wireshark.integration.ui.StopWiresharkDataSourceCmd" commandName="Stop Wireshark Data Source" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQd32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.edc.ui.toggleCustomFormatting" commandName="Toggle Custom Variable Formatting" description="Turn custom variable formatting on and off" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQeH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQeX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQen2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQe32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQfH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.framework.ide.project.ui.syncSolutionConfigurationCommand" commandName="Sync Solution configuration" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQfX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQfn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQf32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQgH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQgX2eEfCdZppseCV2hQ" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQgn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.energyprofiler.multinode.commands.AddBookmarkCommand" commandName="com.silabs.ss.tool.energyprofiler.multinode.commands.AddBookmarkCommmand" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQg32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQhH2eEfCdZppseCV2hQ" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_gTEei32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQhX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQhn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQh32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQiH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_gTEem32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQiX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQin2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQi32eEfCdZppseCV2hQ" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_gTFsVX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQjH2eEfCdZppseCV2hQ" elementId="com.silabs.ide.welcomepage.launch_command" commandName="Show Simplicity IDE Welcome" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQjX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQjn2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQj32eEfCdZppseCV2hQ" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_gTFsVn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQkH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQkX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_gTFsX32eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTDQkn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_gTDQk32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQlH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQlX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQln2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.setuptaskCommand" commandName="Setup Task" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQl32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQmH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQmX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQmn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQm32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_gTEein2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTDQnH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_gTDQnX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_gTDQnn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_gTDQn32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_gTEenH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQoH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_gTFsVH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQoX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_gTEelX2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTDQon2eEfCdZppseCV2hQ" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_gTDQo32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQpH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQpX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQpn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQp32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQqH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_gTEel32eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTDQqX2eEfCdZppseCV2hQ" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_gTDQqn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQq32eEfCdZppseCV2hQ" elementId="com.silabs.ss.bluetooth.hadm.analyzer.editor.HadmAnalyzerLaunchCommand" commandName="Launch CS Analyzer" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQrH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_gTEem32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQrX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQrn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQr32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.offlineInstallerCommand" commandName="Import Offline Installer Archive" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQsH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQsX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQsn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQs32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.ui.FlashProgrammerCommand" commandName="Flash Programmer" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQtH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_gTEem32eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTDQtX2eEfCdZppseCV2hQ" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_gTDQtn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQt32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQuH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.edc.ui.createSnapshot" commandName="Create Snapshot" description="Create a snapshot of the debug session" category="_gTEek32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQuX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQun2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQu32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.energyprofiler.multinode.commands.OpenISDFileCommand" commandName="Open File..." category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQvH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQvX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQvn2eEfCdZppseCV2hQ" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQv32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQwH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQwX2eEfCdZppseCV2hQ" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_gTFsVn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQwn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_gTEenH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQw32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQxH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQxX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQxn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQx32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQyH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQyX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQyn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQy32eEfCdZppseCV2hQ" elementId="com.silabs.ss.segger.systemview.app.project.ui.readRecordedDataFromTarget" commandName="Read Recorded Data from Target" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQzH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.segger.systemview.app.ide.ui.toolbarCommand" commandName="Segger SystemView" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQzX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQzn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_gTFsUn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQz32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_gTEekX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ0H2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_gTEelH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ0X2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ0n2eEfCdZppseCV2hQ" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_gTFsVX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ032eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ1H2eEfCdZppseCV2hQ" elementId="com.iar.cdt.generic.wizardPages.switchDevice" commandName="Select Device..." description="Change the device used by the current project" category="_gTFsXn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ1X2eEfCdZppseCV2hQ" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_gTFsVX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ1n2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ132eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ2H2eEfCdZppseCV2hQ" elementId="com.silabs.studio.demos.ui.command" commandName="Open Demos Dialog..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ2X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ2n2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.analysis.cs0.ui.commands.zoomToSelection" commandName="Zoom To Selection Command" category="_gTEej32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ232eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ3H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ3X2eEfCdZppseCV2hQ" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ3n2eEfCdZppseCV2hQ" elementId="com.silabs.ss.wireless.wifi_commander.editor.WifiCommanderLaunchCommand" commandName="Launch Wi-Fi Commander" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ332eEfCdZppseCV2hQ" elementId="com.silabs.ss.framework.ide.ui.command.fileBrowser" commandName="Browse Files Here" description="Open an external file browser at the selected location" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ4H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ4X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_gTEem32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ4n2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ432eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ5H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_gTEem32eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTDQ5X2eEfCdZppseCV2hQ" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_gTDQ5n2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ532eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ6H2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.energyprofiler.multinode.commands.SaveISDFileCommand" commandName="Save ISD File" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ6X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ6n2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ632eEfCdZppseCV2hQ" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_gTEem32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ7H2eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.device.ui.ConfigureDevice" commandName="ConfigureDevice" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ7X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ7n2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.energyprofiler.multinode.commands.RemoveBookmarkCommand" commandName="com.silabs.ss.tool.energyprofiler.multinode.commands.RemoveBookmarkCommmand" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ732eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_gTEekX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ8H2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.edc.ui.toggleShowAllVariables" commandName="Show Globals Defined In File" description="Show all variables globally defined by the current source file" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ8X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ8n2eEfCdZppseCV2hQ" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ832eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.DefineSearchCommand" commandName="com.silabs.studio.energyprofiler.ui.DefineSearchCommand" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ9H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ9X2eEfCdZppseCV2hQ" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_gTFsVn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ9n2eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.ConnectDeviceCommand" commandName="Start Energy Capture" category="_gTFsXH2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTDQ932eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.energyprofiler.device_type" name="Device Type"/>
  </commands>
  <commands xmi:id="_gTDQ-H2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ-X2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ-n2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ-32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ_H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ_X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ_n2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDQ_32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRAH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRAX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.framework.ide.project.ui.manageSolutionsCommand" commandName="Manage Solutions" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRAn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRA32eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.EnableFreezeCommand" commandName="com.silabs.studio.energyprofiler.ui.EnableFreezeCommand" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRBH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRBX2eEfCdZppseCV2hQ" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_gTFsU32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRBn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRB32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRCH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.ui.bugreport.command" commandName="Report Bug" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRCX2eEfCdZppseCV2hQ" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_gTEemH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRCn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.support.mcu.part.ui.SelectCryptoProfileHandler" commandName="SelectCryptoProfileHandler" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRC32eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.CaptureStopCommand" commandName="com.silabs.studio.energyprofiler.ui.CaptureStopCommand" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRDH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.ui.doc.properties.command" commandName="Document Properties Dialog" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRDX2eEfCdZppseCV2hQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_gTFsWn2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTDRDn2eEfCdZppseCV2hQ" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_gTDRD32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDREH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDREX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDREn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_gTEenH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRE32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_gTEemn2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTDRFH2eEfCdZppseCV2hQ" elementId="url" name="URL"/>
    <parameters xmi:id="_gTDRFX2eEfCdZppseCV2hQ" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_gTDRFn2eEfCdZppseCV2hQ" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_gTDRF32eEfCdZppseCV2hQ" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_gTDRGH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRGX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRGn2eEfCdZppseCV2hQ" elementId="com.ember.workbench.cockpit.action.CockpitHandler" commandName="Launch Console" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRG32eEfCdZppseCV2hQ" elementId="com.silabs.ss.support.dci.part.ui.ClearUnlockTokenHandler" commandName="ClearUnlockTokenHandler" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRHH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRHX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRHn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRH32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRIH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_gTFsX32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRIX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_gTEemn2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTDRIn2eEfCdZppseCV2hQ" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_gTDRI32eEfCdZppseCV2hQ" elementId="com.silabs.ss.support.dci.examples.ui.VerifySignatureHandler" commandName="VerifySignatureHandler" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRJH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRJX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRJn2eEfCdZppseCV2hQ" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_gTEemH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRJ32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRKH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRKX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRKn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRK32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRLH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRLX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRLn2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRL32eEfCdZppseCV2hQ" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_gTFsVX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRMH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRMX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRMn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRM32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRNH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRNX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRNn2eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.DataCollectionCommand" commandName="com.silabs.studio.energyprofiler.ui.DataCollectionCommand" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDRN32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDROH2eEfCdZppseCV2hQ" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_gTEem32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDROX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTDROn2eEfCdZppseCV2hQ" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_gTEemH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3IH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_gTEenH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3IX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_gTFsYn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3In2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3I32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3JH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_gTFsWX2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD3JX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_gTD3Jn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_gTD3J32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3KH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3KX2eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.FreezeCommand" commandName="com.silabs.studio.energyprofiler.ui.FreezeCommand" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Kn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3K32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3LH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3LX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Ln2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3L32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.ui.regkitCommand" commandName="Kit Registration" category="_gTFsYH2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD3MH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.useraccounts.dropdown.msg" name="showRegKit"/>
  </commands>
  <commands xmi:id="_gTD3MX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.bluetooth.aox.analyzer.editor.AoxAnalyzerLaunchCommand.Legacy" commandName="Legacy" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Mn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3M32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3NH2eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.CompareSessionStatsCommand" commandName="Session Statistics..." category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3NX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Nn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3N32eEfCdZppseCV2hQ" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_gTEemH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3OH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3OX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3On2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3O32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3PH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.analysis.cs0.ui.commands.zoomIn" commandName="Zoom In Command" category="_gTEej32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3PX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.support.dci.part.ui.ShowDeviceCertHandler" commandName="ShowDeviceCertHandler" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Pn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3P32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3QH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3QX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Qn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Q32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3RH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_gTEel32eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD3RX2eEfCdZppseCV2hQ" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_gTD3Rn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.installer.core.ui.licensemanager.command" commandName="License Manager" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3R32eEfCdZppseCV2hQ" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_gTEekn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3SH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3SX2eEfCdZppseCV2hQ" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_gTFsWn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Sn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3S32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3TH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3TX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Tn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3T32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3UH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3UX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Un2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3U32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_gTEem32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3VH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3VX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Vn2eEfCdZppseCV2hQ" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_gTFsVX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3V32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3WH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.device.ui.RemoveDeviceFromGroup" commandName="RemoveDeviceFromGroup" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3WX2eEfCdZppseCV2hQ" elementId="org.eclipse.gef.zoom_in" commandName="Zoom In" description="Zoom In" category="_gTFsW32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Wn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3W32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3XH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.bluetooth.aox.analyzer.editor.AoxAnalyzerLaunchCommand.Dual" commandName="Dual" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3XX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Xn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3X32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3YH2eEfCdZppseCV2hQ" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_gTFsVn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3YX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Yn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3Y32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_gTFsWH2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD3ZH2eEfCdZppseCV2hQ" elementId="title" name="Title"/>
    <parameters xmi:id="_gTD3ZX2eEfCdZppseCV2hQ" elementId="message" name="Message"/>
    <parameters xmi:id="_gTD3Zn2eEfCdZppseCV2hQ" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_gTD3Z32eEfCdZppseCV2hQ" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_gTD3aH2eEfCdZppseCV2hQ" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_gTD3aX2eEfCdZppseCV2hQ" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_gTD3an2eEfCdZppseCV2hQ" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_gTD3a32eEfCdZppseCV2hQ" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_gTD3bH2eEfCdZppseCV2hQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_gTD3bX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3bn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3b32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.logoCommand" commandName="Silicon Labs" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3cH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3cX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.support.dci.part.ui.ImportUnlockTokenHandler" commandName="ImportUnlockTokenHandler" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3cn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3c32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3dH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3dX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3dn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3d32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3eH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3eX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3en2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3e32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_gTFsYn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3fH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3fX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_gTFsUn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3fn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3f32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3gH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3gX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3gn2eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.ProfileProgramCommand" commandName="Profile Program" category="_gTFsXH2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD3g32eEfCdZppseCV2hQ" elementId="file_path" name="file_path"/>
    <parameters xmi:id="_gTD3hH2eEfCdZppseCV2hQ" elementId="board_id" name="board_id"/>
    <parameters xmi:id="_gTD3hX2eEfCdZppseCV2hQ" elementId="mcu_id" name="mcu_id"/>
    <parameters xmi:id="_gTD3hn2eEfCdZppseCV2hQ" elementId="enable_code_profiling" name="enable_code_profiling"/>
    <parameters xmi:id="_gTD3h32eEfCdZppseCV2hQ" elementId="bootloader_path" name="bootloader_path"/>
  </commands>
  <commands xmi:id="_gTD3iH2eEfCdZppseCV2hQ" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_gTFsU32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3iX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_gTEelX2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD3in2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_gTD3i32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3jH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3jX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.installer.core.ui.ExternalHelpCommand.command" commandName="Simplicity Studio 5 User's Guide" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3jn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3j32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3kH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_gTFsYH2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD3kX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_gTD3kn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_gTD3k32eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.useraccounts.dropdown.loginCommand" commandName="Log In" category="_gTFsYH2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD3lH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.useraccounts.dropdown.msg" name="Message"/>
  </commands>
  <commands xmi:id="_gTD3lX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3ln2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3l32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3mH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3mX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3mn2eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.ProfileDemoCommand" commandName="Profile Demo" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3m32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_gTFsYn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3nH2eEfCdZppseCV2hQ" elementId="org.eclipse.gef.zoom_out" commandName="Zoom Out" description="Zoom Out" category="_gTFsW32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3nX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.segger.systemview.app.project.ui.help" commandName="Help" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3nn2eEfCdZppseCV2hQ" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_gTEei32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3n32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_gTEem32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3oH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_gTFsYX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3oX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3on2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3o32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3pH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3pX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3pn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3p32eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.LoadProfilerDataCommand" commandName="Load Profiler Data" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3qH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3qX2eEfCdZppseCV2hQ" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_gTEei32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3qn2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3q32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_gTFsX32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3rH2eEfCdZppseCV2hQ" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_gTEemH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3rX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3rn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_gTEel32eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD3r32eEfCdZppseCV2hQ" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_gTD3sH2eEfCdZppseCV2hQ" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_gTFsU32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3sX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3sn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.ui.updatesCommand" commandName="Update Software" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3s32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3tH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3tX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.energyprofiler.multinode.commands.CloseISDFileCommand" commandName="Close File" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3tn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3t32eEfCdZppseCV2hQ" elementId="com.silabs.ss.segger.systemview.app.project.ui.saveRecordingToFile" commandName="Save Recording to File" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3uH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3uX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3un2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.edc.ui.playSnapshots" commandName="Play Snapshots" description="Show each snapshot in the album" category="_gTEek32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3u32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_gTEekX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3vH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3vX2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3vn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.edc.ui.previousSnapshot" commandName="Previous Snapshot" description="Show the previous snapshot in the album" category="_gTEek32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3v32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3wH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3wX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3wn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_gTFsXX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3w32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.energyprofiler.multinode.commands.ToggleSwitchPerspPref" commandName="com.silabs.ss.tool.energyprofiler.multinode.commands.ToggleSwitchPerspPref" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3xH2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3xX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.ui.homeCommand" commandName="Home" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3xn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3x32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_gTFsUH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3yH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_gTEenH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3yX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3yn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_gTEem32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3y32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3zH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3zX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3zn2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3z32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD30H2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_gTFsYn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD30X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD30n2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3032eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.energyprofiler.multinode.commands.RenameBookmarkCommand" commandName="com.silabs.ss.tool.energyprofiler.multinode.commands.RenameBookmarkCommmand" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD31H2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD31X2eEfCdZppseCV2hQ" elementId="com.silabs.ss.platform.device.ui.AddDeviceToGroup" commandName="AddDeviceToGroup" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD31n2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3132eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_gTEemn2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD32H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_gTD32X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_gTEem32eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD32n2eEfCdZppseCV2hQ" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_gTD3232eEfCdZppseCV2hQ" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_gTD33H2eEfCdZppseCV2hQ" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_gTD33X2eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD33n2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_gTEemn2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD3332eEfCdZppseCV2hQ" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_gTD34H2eEfCdZppseCV2hQ" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_gTD34X2eEfCdZppseCV2hQ" elementId="com.silabs.ss.framework.debugger.ui.command.reset" commandName="Reset" category="_gTFsYH2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD34n2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.commands.radioStateParameter" name="State" optional="false"/>
  </commands>
  <commands xmi:id="_gTD3432eEfCdZppseCV2hQ" elementId="na.cmd.alwayson" commandName="Main menu bridge command - always on" category="_gTFsYH2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD35H2eEfCdZppseCV2hQ" elementId="na.action" name="Action enum"/>
  </commands>
  <commands xmi:id="_gTD35X2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD35n2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_gTEem32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3532eEfCdZppseCV2hQ" elementId="com.silabs.ss.segger.systemview.app.project.ui.openUserManual" commandName="Open User Manual" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD36H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD36X2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_gTFsXX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD36n2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3632eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_gTEenH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD37H2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD37X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD37n2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.ui.recentCommand" commandName="Recent Projects" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3732eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_gTFsUX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD38H2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD38X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_gTEel32eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD38n2eEfCdZppseCV2hQ" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_gTD3832eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD39H2eEfCdZppseCV2hQ" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD39X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_gTEel32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD39n2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3932eEfCdZppseCV2hQ" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3-H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3-X2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3-n2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3-32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3_H2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_gTEelX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD3_X2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_gTEelX2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD3_n2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_gTD3_32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_gTFsWH2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD4AH2eEfCdZppseCV2hQ" elementId="title" name="Title"/>
    <parameters xmi:id="_gTD4AX2eEfCdZppseCV2hQ" elementId="message" name="Message"/>
    <parameters xmi:id="_gTD4An2eEfCdZppseCV2hQ" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_gTD4A32eEfCdZppseCV2hQ" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_gTD4BH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.uc.project.convert" commandName="convert" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4BX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4Bn2eEfCdZppseCV2hQ" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_gTEemH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4B32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4CH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_gTFsYX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4CX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4Cn2eEfCdZppseCV2hQ" elementId="com.silabs.studio.energyprofiler.ui.CancelSearchCommand" commandName="com.silabs.studio.energyprofiler.ui.CancelSearchCommand" category="_gTFsXH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4C32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4DH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4DX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4Dn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_gTEejH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4D32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_gTFsV32eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4EH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_gTEekH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4EX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_gTEemn2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4En2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4E32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_gTEelX2eEfCdZppseCV2hQ">
    <parameters xmi:id="_gTD4FH2eEfCdZppseCV2hQ" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_gTD4FX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_gTEejX2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4Fn2eEfCdZppseCV2hQ" elementId="com.silabs.ss.framework.ide.project.ui.deleteSolutionCommand" commandName="Delete Solution" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4F32eEfCdZppseCV2hQ" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_gTEemH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4GH2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.ide.arm.debug.solutions.debug" commandName="com.silabs.ss.tool.ide.arm.debug.solutions.debug"/>
  <commands xmi:id="_gTD4GX2eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.launcher.internal.ui.ExternalHelpCommand.command" commandName="com.silabs.ss.tool.launcher.internal.ui.ExternalHelpCommand.command"/>
  <commands xmi:id="_gTD4Gn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::com.silabs.ss.tool.launcher.product.actionSet2/com.silabs.ui.file.openWorkspace" commandName="Switch Workspace ..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4G32eEfCdZppseCV2hQ" elementId="AUTOGEN:::com.silabs.ss.cdt.ui.buildConfigActionSet/com.silabs.ss.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4HH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::com.silabs.ss.cdt.ui.buildConfigActionSet/com.silabs.ss.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4HX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::com.silabs.ss.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.manageConfigsAction2" commandName="Manage..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4Hn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::com.silabs.ss.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.wsselection" commandName="Manage Working Sets..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4H32eEfCdZppseCV2hQ" elementId="AUTOGEN:::com.silabs.ss.framework.debugger.ui.attachActionSet/org.eclipse.debug.ui.actions.OpenAttachConfigurations" commandName="Attach Configurations..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4IH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::com.silabs.ss.framework.debugger.ui.attachActionSet/com.silabs.ss.framework.debugger.internal.ui.AttachToAction" commandName="Attach to" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4IX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::com.silabs.ss.framework.debugger.ui.attachActionSet/com.silabs.ss.framework.debugger.internal.ui.AttachHistoryMenuAction" commandName="Attach History" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4In2eEfCdZppseCV2hQ" elementId="AUTOGEN:::com.silabs.ss.framework.debugger.ui.attachActionSet/org.eclipse.debug.ui.actions.OpenConnectConfigurations" commandName="Connect Configurations..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4I32eEfCdZppseCV2hQ" elementId="AUTOGEN:::com.silabs.ss.framework.debugger.ui.attachActionSet/com.silabs.ss.framework.debugger.internal.ui.ConnectToAction" commandName="Connect to" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4JH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::com.silabs.ss.framework.debugger.ui.attachActionSet/com.silabs.ss.framework.debugger.internal.ui.ConnectHistoryMenuAction" commandName="Connect History" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4JX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4Jn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4J32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4KH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4KX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4Kn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4K32eEfCdZppseCV2hQ" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4LH2eEfCdZppseCV2hQ" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4LX2eEfCdZppseCV2hQ" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4Ln2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTD4L32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeMH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeMX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeMn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeM32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeNH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeNX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeNn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeN32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeOH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeOX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeOn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeO32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEePH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEePX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEePn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeP32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeQH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeQX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeQn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeQ32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeRH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeRX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeRn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeR32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeSH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeSX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeSn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeS32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeTH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeTX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeTn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeT32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeUH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeUX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeUn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeU32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeVH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeVX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeVn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.pinDebugContext" commandName="Pin to Debug Context" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeV32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.clone" commandName="Open New View" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeWH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeWX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeWn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeW32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeXH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeXX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeXn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeX32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeYH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeYX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeYn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeY32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeZH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeZX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeZn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeZ32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeaH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeaX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEean2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEea32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEebH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEebX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEebn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeb32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEecH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEecX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEecn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEec32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEedH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEedX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEedn2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEed32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeeH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeeX2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEeen2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEee32eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <commands xmi:id="_gTEefH2eEfCdZppseCV2hQ" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_gTFsYH2eEfCdZppseCV2hQ"/>
  <addons xmi:id="_gTEefX2eEfCdZppseCV2hQ" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_gTEefn2eEfCdZppseCV2hQ" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_gTEef32eEfCdZppseCV2hQ" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_gTEegH2eEfCdZppseCV2hQ" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_gTEegX2eEfCdZppseCV2hQ" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_gTEegn2eEfCdZppseCV2hQ" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_gTEeg32eEfCdZppseCV2hQ" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_gTEehH2eEfCdZppseCV2hQ" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_gTEehX2eEfCdZppseCV2hQ" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_gTEehn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_gTEeh32eEfCdZppseCV2hQ" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_gTEeiH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_gTEeiX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_gTEein2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_gTEei32eEfCdZppseCV2hQ" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_gTEejH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_gTEejX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_gTEejn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_gTEej32eEfCdZppseCV2hQ" elementId="com.silabs.ss.tool.analysis.cs0.ui.commands.zoom.category" name="Zoom Actions"/>
  <categories xmi:id="_gTEekH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_gTEekX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_gTEekn2eEfCdZppseCV2hQ" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.category" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_gTEek32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.edc.ui.debugSnapshotCategory" name="Debug Snapshots" description="Set of commands for debug snapshots"/>
  <categories xmi:id="_gTEelH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_gTEelX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_gTEeln2eEfCdZppseCV2hQ" elementId="com.silabs.ide.scripting.ui.category" name="Studio Scripting"/>
  <categories xmi:id="_gTEel32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_gTEemH2eEfCdZppseCV2hQ" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_gTEemX2eEfCdZppseCV2hQ" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_gTEemn2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_gTEem32eEfCdZppseCV2hQ" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_gTEenH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_gTFsUH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_gTFsUX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_gTFsUn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_gTFsU32eEfCdZppseCV2hQ" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_gTFsVH2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_gTFsVX2eEfCdZppseCV2hQ" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_gTFsVn2eEfCdZppseCV2hQ" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_gTFsV32eEfCdZppseCV2hQ" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_gTFsWH2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_gTFsWX2eEfCdZppseCV2hQ" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_gTFsWn2eEfCdZppseCV2hQ" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_gTFsW32eEfCdZppseCV2hQ" elementId="org.eclipse.gef.category.view" name="View" description="View"/>
  <categories xmi:id="_gTFsXH2eEfCdZppseCV2hQ" elementId="com.silabs.ui.common.SilabsCommandCategory" name="Silicon Labs"/>
  <categories xmi:id="_gTFsXX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_gTFsXn2eEfCdZppseCV2hQ" elementId="com.iar.cdt.generic.wizardPages.category" name="C-SPY"/>
  <categories xmi:id="_gTFsX32eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_gTFsYH2eEfCdZppseCV2hQ" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_gTFsYX2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
  <categories xmi:id="_gTFsYn2eEfCdZppseCV2hQ" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
</application:Application>
