"use strict";(globalThis["webpackChunkpost_build"]=globalThis["webpackChunkpost_build"]||[]).push([[664],{9664:(e,t,n)=>{n.r(t),n.d(t,{default:()=>Ql});n(702);var l=n(9835),a=n(6970),o=n(499),u=(n(6727),n(8964),n(2809),n(9981)),s=n.n(u);const i={"Content-Type":"application/json",Accept:"application/json"},r=(e,t)=>s()({method:"get",url:e,params:t,headers:i}),c=(e,t,n)=>s()({method:"put",url:e,params:n,data:t,headers:i}),p=(e,t,n)=>s()({method:"post",url:e,params:n,data:t,headers:i}),d={getWorkspace(e){const t=`/rest/postbuild/${e}`;return new Promise(((e,n)=>r(t).then((t=>{e(t.data)})).catch((e=>{n(e)}))))},putWorkspace(e,t){const n=`/rest/postbuild/${e}/projects`;return c(n,t)},putDirty(e,t){const n=`/rest/postbuild/${e}/dirty`;return c(n,t)},postShowYaml(e,t,n){const l=`/rest/postbuild/${e}/opensource`;let a={path:t};return n&&(a["postbuild"]=n),c(l,a)},getApplicationInfo(e){let t="/rest/application/info/sections";return t=t+"?filePath="+encodeURIComponent(e),t+="&allocOnly=true",r(t)},postBrowseLocation(e,t,n=!0){const l="/rest/studio/ui/services/browse";return new Promise(((a,o)=>p(l,{location:e,message:t,browseFile:n}).then((e=>{a(e.data)})).catch((e=>{o(e)}))))}},v="ws::",m="_root_dir_",f="_postbuildpath_",w=function(e){return!!(e&&e.postbuild&&e.postbuild.parameters&&e.postbuild.parameters.length>0)},g=function(e){return!!(e&&e.postbuild&&e.postbuild.constants&&e.postbuild.constants.length>0)},b=function(e){return w(e)?e.postbuild.parameters:[]},y=function(e){return g(e)?e.postbuild.constants:[]},h=function(e){return e&&e.hasOwnProperty("task")?k(e.task):""},k=function(e){let t=D().find((t=>e.startsWith(t)));return t||e},_=function(e){return k(e)},j=function(e,t){if(!e||!t)return{};if(t.startsWith(v))return e;if(!e.projects)return{};let n=e.projects.find((e=>e.id===t));return null==n?{}:n},S=function(e,t){if(!e||!e.postbuild||!e.postbuild.steps)return{};let n=e.postbuild.steps.find((e=>e.task===t));return null==n?{}:n},C=function(e,t,n){let l=j(e,t),a=-1;if(!l||!l.postbuild||!l.postbuild.steps)return a;for(const[o,u]of l.postbuild.steps.entries())if(u.task===n){a=o;break}return a},W=function(e,t,n){const l=j(e,t);return!l||!l.postbuild||!l.postbuild.steps||l.postbuild.steps.length<=n?null:l.postbuild.steps[n]},U=function(e,t){let n=j(e,t);return n&&n.postbuild&&n.postbuild.steps?n.postbuild.steps.length:0},q=function(e,t,n){return 0==C(e,t,n)},V=function(e,t,n){let l=C(e,t,n);return!(l<0)&&l==U(e,t)-1},x=function(e,t,n){if(!V(e,t,n)){let l=C(e,t,n);if(l<0)return;let a=j(e,t);if(!a||!a.postbuild||!a.postbuild.steps)return;[a.postbuild.steps[l],a.postbuild.steps[l+1]]=[a.postbuild.steps[l+1],a.postbuild.steps[l]]}},Z=function(e,t,n){if(!q(e,t,n)){let l=C(e,t,n);if(l<0)return;let a=j(e,t);if(!a||!a.postbuild||!a.postbuild.steps)return;[a.postbuild.steps[l-1],a.postbuild.steps[l]]=[a.postbuild.steps[l],a.postbuild.steps[l-1]]}},H=function(e,t,n){let l=C(e,t,n);if(l<0)return;let a=j(e,t);a&&a.postbuild&&a.postbuild.steps&&a.postbuild.steps.splice(l,1)},I=function(e,t,n,l){return P(e,t,{task:n},l,!1)},Q=function(e,t,n){let l=C(e,t,n);if(!(l<0))return P(e,t,JSON.parse(JSON.stringify(S(j(e,t),n))),n,!0)},O=function(e){let t=0;for(let n of e){let e=n.task.split("_");e.length>1&&(t=Number(e[e.length-1]))}return t},P=function(e,t,n,l,a){let o,u="params"===l?0:C(e,t,l),s=j(e,t);if(s){if(s.hasOwnProperty("postbuild")||(s["postbuild"]={}),s.postbuild.hasOwnProperty("steps")||(s.postbuild["steps"]=[]),u<0)n.task=n.task+"_0",s.postbuild.steps.push(n),o=n;else{if(a)n.task=n.task+"1";else{let e=O(s.postbuild.steps);n.task=n.task+"_"+(e+1)}o=n,s.postbuild.steps.splice(u+1,0,n)}return o}},D=function(){return["create_gbl","create_ota","convert","copy"]},A=function(e,t){if(!e||!e.postbuild||!e.postbuild.steps)return!1;let n=!1;for(const l of e.postbuild.steps)for(const[e,a]of Object.entries(l)){if(Array.isArray(a)){for(const e of a)if(n=e.includes("{{"+t+"}}"),n)break}else"string"===typeof a&&(n=a.includes("{{"+t+"}}"));if(n)break}return n},B=function(e,t,n){return e&&e.hasOwnProperty(t)?e[t]:n},M=function(e,t){return e&&e.hasOwnProperty(t)?Array.isArray(e[t])?e[t].length:1:0},Y=function(e,t){return 0==M(e,t)?"":Array.isArray(e[t])?e[t][0]:e[t]},F=function(e,t){if(!e||!e.hasOwnProperty("variableMap")||!t)return null;for(let n of e.variableMap)if(n.variable.toUpperCase()===t.toUpperCase())return n.value;return null},L=function(e,t){if(!e||!e.hasOwnProperty("variableMap")||!t)return t;for(let n of e.variableMap)if(n.value.toUpperCase()===t.toUpperCase())return n.variable;return t},T=function(e,t){let n=F(e,t);return null===n?t:n},E=function(e,t){if(!t)return null;let n=N(e);return t=t.replaceAll("\\","/"),n&&t.startsWith(n)?t.replace(n,""):t},N=function(e){if(!e||!e.hasOwnProperty("variableMap"))return null;for(let t of e.variableMap)if(t.variable===m)return t.value.endsWith("/")?t.value:t.value+"/";return null},z=function(e){return e.includes(":")||e.startsWith("/")},R=function(e,t){if(!t)return t;let n=N(e);return n?z(t)?t:n+t:t},G=function(e,t,n=null){let l=e;return n&&(l=R(n,l)),l&&l.includes("/")&&(l=l.substring(0,l.lastIndexOf("/")+1)),d.postBrowseLocation(l,t).then((t=>(t.hasOwnProperty("browseLocation")&&(t.browseLocation===l?t.browseLocation=e:t.browseLocation=E(n,t.browseLocation),t.browseLocation=L(n,t.browseLocation)),t)))},J=function(e){return d.getApplicationInfo(e)},K=function(e,t,n=""){return e?n+e.id+"-"+t:""},$=function(e){const t=e.lastIndexOf("-");return-1===t||t===e.length-1?e:{project:e.substr(0,t),task:e.substr(t+1)}},X=function(e){return F(e,f)},ee=function(e,t,n){return d.postShowYaml(e,t,n)},te=function(e){return e&&e.hasOwnProperty("projects")},ne=(0,l.Uk)(" Parameters & Constants "),le={__name:"PostBuildSteps",props:{project:{type:Object,default:null},selected:{type:String,default:""},prefix:{type:String,default:""}},emits:["selectionChanged"],setup(e,{emit:t}){function n(e,n){t("selectionChanged",{project:e,stepId:n})}return(t,u)=>{const s=(0,l.up)("q-item-section"),i=(0,l.up)("q-item"),r=(0,l.up)("q-list"),c=(0,l.Q2)("ripple");return e.project?((0,l.wg)(),(0,l.j4)(r,{key:0,dense:"",bordered:"",separator:"",class:"project-child"},{default:(0,l.w5)((()=>[(0,l.wy)(((0,l.wg)(),(0,l.j4)(i,{dense:"",clickable:"",id:(0,o.SU)(K)(e.project,"params",e.prefix),active:e.selected===(0,o.SU)(K)(e.project,"params",e.prefix),onClick:u[0]||(u[0]=t=>n(e.project,"params")),"active-class":"selected-child"},{default:(0,l.w5)((()=>[(0,l.Wm)(s,null,{default:(0,l.w5)((()=>[ne])),_:1})])),_:1},8,["id","active"])),[[c]]),((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)(e.project.postbuild.steps,(t=>(0,l.wy)(((0,l.wg)(),(0,l.j4)(i,{key:t.task,dense:"",clickable:"",id:(0,o.SU)(K)(e.project,t.task,e.prefix),active:e.selected===(0,o.SU)(K)(e.project,t.task,e.prefix),onClick:l=>n(e.project,t.task),class:"project-child","active-class":"selected-child"},{default:(0,l.w5)((()=>[(0,l.Wm)(s,null,{default:(0,l.w5)((()=>[(0,l.Uk)((0,a.zw)((0,o.SU)(k)(t.task)),1)])),_:2},1024)])),_:2},1032,["id","active","onClick"])),[[c]]))),128))])),_:1})):(0,l.kq)("",!0)}}};var ae=n(3246),oe=n(490),ue=n(1233),se=n(1136),ie=n(9984),re=n.n(ie);const ce=le,pe=ce;re()(le,"components",{QList:ae.Z,QItem:oe.Z,QItemSection:ue.Z}),re()(le,"directives",{Ripple:se.Z});const de={class:"q-pa-sm"},ve=(0,l.Uk)('"No Projects Available"'),me={key:1},fe={__name:"ProjectList",props:{workspace:{type:Object,default:null}},emits:["selected"],setup(e,{expose:t,emit:n}){const a=e,u=(0,o.iH)(i()),s=(0,l.Fl)((()=>a.workspace&&a.workspace.projects?a.workspace.projects:[]));function i(){return a.workspace&&a.workspace.projects&&0!=a.workspace.projects.length?K(a.workspace.projects[0],"params"):""}function r(e,t=""){u.value=K(e.project,e.stepId,t)}function c(e,t,n){u.value=K(e,t,n?v:"")}return n("selected",$(u.value)),(0,l.YP)((()=>u.value),(e=>{n("selected",$(e))})),t({setSelected:c}),(t,n)=>{const a=(0,l.up)("q-item-section"),i=(0,l.up)("q-item"),c=(0,l.up)("q-expansion-item"),p=(0,l.up)("q-list");return(0,l.wg)(),(0,l.iD)("div",null,[(0,l._)("div",de,[(0,l.Wm)(p,{dense:"",separators:""},{default:(0,l.w5)((()=>[e.workspace&&e.workspace.projects&&e.workspace.projects.length?((0,l.wg)(),(0,l.iD)("div",me,[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,o.SU)(s),(e=>((0,l.wg)(),(0,l.j4)(c,{bordered:"",class:"project-header",key:e.id,"expand-separator":"","default-opened":"",label:e.name},{default:(0,l.w5)((()=>[(0,l.Wm)(pe,{project:e,selected:u.value,onSelectionChanged:n[0]||(n[0]=e=>r(e))},null,8,["project","selected"])])),_:2},1032,["label"])))),128)),e.workspace&&e.workspace.postbuild?((0,l.wg)(),(0,l.j4)(c,{bordered:"",class:"project-header",key:e.workspace.id,"expand-separator":"","default-opened":"",label:e.workspace.name},{default:(0,l.w5)((()=>[(0,l.Wm)(pe,{project:e.workspace,selected:u.value,prefix:(0,o.SU)(v),onSelectionChanged:n[1]||(n[1]=e=>r(e,(0,o.SU)(v)))},null,8,["project","selected","prefix"])])),_:1},8,["label"])):(0,l.kq)("",!0)])):((0,l.wg)(),(0,l.j4)(i,{key:0},{default:(0,l.w5)((()=>[(0,l.Wm)(a,null,{default:(0,l.w5)((()=>[ve])),_:1})])),_:1}))])),_:1})])])}}};var we=n(1639),ge=n(1123);const be=(0,we.Z)(fe,[["__scopeId","data-v-75efcf18"]]),ye=be;re()(fe,"components",{QList:ae.Z,QItem:oe.Z,QItemSection:ue.Z,QExpansionItem:ge.Z});const he={class:"col-12"},ke=(0,l.Uk)("Delete"),_e={key:0},je={__name:"ParamAndConstTable",props:{project:{type:Object,default:null},step:{type:Object,default:null},readOnly:{type:Boolean,default:!0},showExport:{type:Boolean,default:!1}},emits:["parametersChanged","constantsChanged"],setup(e,{expose:t,emit:n}){const u=e,s=(0,o.iH)({}),i=(0,o.iH)([...b(u.project)]),r=(0,o.iH)([...y(u.project)]),c=(0,o.iH)([...Z(b(u.project))]),p=(0,o.iH)([...Z(y(u.project))]),d=(0,o.iH)(null),v=(0,o.iH)({rowsPerPage:0});function m(e){return/\s/.test(e)}t({inputsRefs:s,addParameter:K,addConstant:$}),(0,l.Xn)((()=>{s.value={}})),(0,l.YP)((()=>u.project),(e=>{i.value=[...b(e)],r.value=[...y(e)],c.value=[...Z(b(e))],p.value=[...Z(y(e))]}));const f="This variable is read-only.  It is either referenced by the project steps, or it is a system managed variable.",w=[{name:"name",label:"Name",align:"center",field:e=>e.name},{name:"value",label:"Value",align:"center",field:e=>e.value}],g=(0,l.Fl)((()=>{let e=i.value,t=r.value,n=[];for(const[l,a]of e.entries())n.push(L(a,l,c.value.filter((e=>e!==c.value[l]))));for(let[l,a]of t.entries())n.push(T(a,l,p.value.filter((e=>e!==p.value[l]))));return h()&&n.push(E(k(),_(),n.length)),n}));function h(){return u.showExport&&k()&&_()}function k(){return u.step&&u.step.export?u.step.export:null}function _(){return u.step&&u.step.output?u.step.output:null}function j(e){e&&d.value&&("param"===d.value.type?(J(i.value,c.value,d.value.index),H(i.value)):"const"===d.value.type&&(J(r.value,p.value,d.value.index),I(r.value)))}function S(e){let t;const n=e.closest(".paramConstRow");if(n&&(t=n.id),t){const e=t.split("_");if(2==e.length)return C(e[0],e[1])}}function C(e,t){return g.value.find((n=>n.type==e&&n.index==t))}function W(e){d.value=S(e.target)}function U(e){d.value=void 0}function q(e){return e.type+"_"+e.index}function V(e){return"input_"+q(e)}function x(e){return s.value?s.value[V(e)]:void 0}function Z(e){return e?e.map((e=>e.name)):[]}function H(e){n("parametersChanged",e)}function I(e){n("constantsChanged",e)}function Q(e,t,n,l){D(i.value,e,"name",t,n,H)}function O(e,t,n,l){D(r.value,e,"name",t,n,I)}function P(e,t,n){D(r.value,e,"value",t,n,I)}function D(e,t,n,l,a,o){t<0||t>=e.length||(e[t][n]=a,o&&o(e))}function B(e,t){"param"===e.type?Q(e.index,e.name,t,x(e)):"const"===e.type&&O(e.index,e.name,t,x(e))}function M(e,t){"const"===e.type&&P(e.index,e.name,t)}function Y(e,t){return e.existingNames.includes(t)}function F(e){return!u.readOnly&&e}function L(e,t,n){let l=u.readOnly||N(e)&&!n.includes(e.name);return{name:e.name,value:"",type:"param",index:t,readonlyall:u.readOnly,readonlyname:l,readonlyvalue:!0,allowDelete:!u.readOnly&&!l,namemessage:F(l)?f:"",existingNames:n}}function T(e,t,n){let l=u.readOnly||z(e)&&!n.includes(e.name),a=u.readOnly||R(e);return{name:e.name,value:e.value,type:"const",index:t,readonlyall:u.readOnly,readonlyname:l,readonlyvalue:a,allowDelete:!u.readOnly&&!l&&!a,namemessage:F(l)?f:"",valuemessage:F(l)?f:e.value,existingNames:n}}function E(e,t,n){return{name:e,value:t,type:"export",index:n,readonlyall:u.readOnly,readonlyname:!0,readonlyvalue:!0,allowDelete:!1,existingNames:[],namemessage:e,valuemessage:t}}function N(e){return"build_dir"===e.name||A(u.project,e.name)}function z(e){return"project_name"===e.name||A(u.project,e.name)}function R(e){return z(e)}function G(e,t){if(!e.includes(t))return t;let n=1;while(e.includes(t+n))n+=1;return t+n}function J(e,t,n){e.splice(n,1),t.splice(n,1)}function K(){let e=G(b(u.project).map((e=>e.name)),"new_param");i.value.push({name:e}),H(i.value)}function $(){let e=G(y(u.project).map((e=>e.name)),"new_const");r.value.push({name:e,value:"new_value"}),I(r.value)}return(e,t)=>{const n=(0,l.up)("q-item-section"),u=(0,l.up)("q-item"),i=(0,l.up)("q-list"),r=(0,l.up)("q-menu"),c=(0,l.up)("q-tooltip"),p=(0,l.up)("q-input"),d=(0,l.up)("q-td"),f=(0,l.up)("q-tr"),b=(0,l.up)("q-table"),y=(0,l.Q2)("close-popup");return(0,l.wg)(),(0,l.iD)("div",he,[(0,l.Wm)(b,{columns:w,rows:(0,o.SU)(g),"hide-header":"","hide-bottom":"",dense:"",bordered:"",separator:"cell",pagination:v.value,"onUpdate:pagination":t[0]||(t[0]=e=>v.value=e)},{body:(0,l.w5)((e=>[(0,l.Wm)(f,{class:"paramConstRow",props:e,id:q(e.row)},{default:(0,l.w5)((()=>[e.row.readonlyall?(0,l.kq)("",!0):((0,l.wg)(),(0,l.j4)(r,{key:0,"touch-position":"","context-menu":"",onShow:W,onHide:U},{default:(0,l.w5)((()=>[(0,l.Wm)(i,{dense:"",class:"param-context-menu"},{default:(0,l.w5)((()=>[(0,l.wy)(((0,l.wg)(),(0,l.j4)(u,{clickable:"",disable:!e.row.allowDelete},{default:(0,l.w5)((()=>[(0,l.Wm)(n,{onClick:t=>j(e.row.allowDelete)},{default:(0,l.w5)((()=>[ke])),_:2},1032,["onClick"])])),_:2},1032,["disable"])),[[y]])])),_:2},1024)])),_:2},1024)),(0,l.Wm)(d,{key:"name",props:e},{default:(0,l.w5)((()=>[(0,l.Wm)(p,{ref:t=>{s.value[V(e.row)]=t},readonly:e.row.readonlyname,dense:"",borderless:"",type:"text","input-class":"text-center ellipsis","model-value":e.row.name,"onUpdate:modelValue":t=>B(e.row,t),rules:[t=>!Y(e.row,t)||"Already exists",e=>!!e||"Name required",e=>!m(e)||"No whitespace allowed"]},{default:(0,l.w5)((()=>[e.row.namemessage?((0,l.wg)(),(0,l.j4)(c,{key:0,"max-width":"200px"},{default:(0,l.w5)((()=>[(0,l.Uk)((0,a.zw)(e.row.namemessage),1)])),_:2},1024)):(0,l.kq)("",!0)])),_:2},1032,["readonly","model-value","onUpdate:modelValue","rules"])])),_:2},1032,["props"]),(0,l.Wm)(d,{key:"value",props:e},{default:(0,l.w5)((()=>["param"!==e.row.type?((0,l.wg)(),(0,l.iD)("div",_e,[(0,l.Wm)(p,{readonly:e.row.readonlyvalue,dense:"",borderless:"",type:"text","input-class":"text-center ellipsis","model-value":e.row.value,"onUpdate:modelValue":t=>M(e.row,t),rules:[e=>!!e||"Value required"]},{default:(0,l.w5)((()=>[e.row.valuemessage?((0,l.wg)(),(0,l.j4)(c,{key:0,"max-width":"200px"},{default:(0,l.w5)((()=>[(0,l.Uk)((0,a.zw)(e.row.valuemessage),1)])),_:2},1024)):(0,l.kq)("",!0)])),_:2},1032,["readonly","model-value","onUpdate:modelValue","rules"])])):(0,l.kq)("",!0)])),_:2},1032,["props"])])),_:2},1032,["props","id"])])),_:1},8,["rows","pagination"])])}}};var Se=n(4356),Ce=n(3532),We=n(6362),Ue=n(7220),qe=n(6611),Ve=n(6858),xe=n(6933),Ze=n(2146);const He=(0,we.Z)(je,[["__scopeId","data-v-f71f1800"]]),Ie=He;re()(je,"components",{QTable:Se.Z,QTr:Ce.Z,QMenu:We.Z,QList:ae.Z,QItem:oe.Z,QItemSection:ue.Z,QTd:Ue.Z,QInput:qe.Z,QTooltip:Ve.Z,QMarkupTable:xe.Z}),re()(je,"directives",{ClosePopup:Ze.Z});const Qe=e=>((0,l.dD)("data-v-4180e47c"),e=e(),(0,l.Cn)(),e),Oe={id:"param-editor-view",class:"row"},Pe=Qe((()=>(0,l._)("div",{class:"param-editor-title col-12"}," Available parameters & constants ",-1))),De={class:"col-6 param-editor-row"},Ae={class:"col-6 param-editor-row"},Be=Qe((()=>(0,l._)("div",{class:"text-h5 col-6 param-editor-row param-editor-row-header"}," Name ",-1))),Me=Qe((()=>(0,l._)("div",{class:"text-h5 col-6 param-editor-row param-editor-row-header"}," Value ",-1))),Ye={class:"col-12 row"},Fe={__name:"ParameterEditor",props:{project:{type:Object,default:null},step:{type:Object,default:null}},emits:["parametersChanged","constantsChanged"],setup(e,{emit:t}){const n=e,a=(0,o.iH)(null);function u(e){t("parametersChanged",e)}function s(e){t("constantsChanged",e)}function i(){a.value.addParameter()}function r(){a.value.addConstant()}return(e,t)=>{const o=(0,l.up)("q-btn");return(0,l.wg)(),(0,l.iD)("div",Oe,[Pe,(0,l._)("div",De,[(0,l.Wm)(o,{id:"param-add-button",color:"primary",label:"Add Parameter",onClick:t[0]||(t[0]=e=>i())})]),(0,l._)("div",Ae,[(0,l.Wm)(o,{id:"const-add-button",color:"primary",label:"Add Constant",onClick:t[1]||(t[1]=e=>r())})]),Be,Me,(0,l._)("div",Ye,[(0,l.Wm)(Ie,{ref_key:"paramAndConstTable",ref:a,project:n.project,step:n.step,readOnly:!1,onParametersChanged:t[2]||(t[2]=e=>u(e)),onConstantsChanged:t[3]||(t[3]=e=>s(e))},null,8,["project","step"])])])}}};var Le=n(9379);const Te=(0,we.Z)(Fe,[["__scopeId","data-v-4180e47c"]]),Ee=Te;re()(Fe,"components",{QBtn:Le.Z});var Ne=n(9448),ze=n(5019);const Re={__name:"EditableSelect",props:{inputs:{type:Array,default:()=>[]}},emits:["inputsChanged"],setup(e,{expose:t,emit:n}){const u=e;t({addNewValue:c,getCurrentSelection:p});const s=(0,o.iH)(d(u.inputs)),i=(0,o.iH)(d(u.inputs)),r=(0,o.iH)(u.inputs);function c(e){b(e)}function p(){return s.value}function d(e){return e.length>0?e[0]:null}function v(e){return s.value=e,!1}function m(e){return s.value?b(s.value):y(i.value),!1}function f(e){void 0!==e&&27===e.keyCode&&w(i.value)}function w(e){i.value=e,s.value=e}function g(e){y(e)}function b(e){r.value.indexOf(e)<0&&(r.value.push(e),w(e),n("inputsChanged",r.value))}function y(e){const t=r.value.indexOf(e);t>-1&&(r.value.splice(t,1),e===i.value&&w(d(r.value)),n("inputsChanged",r.value))}return(0,l.YP)((()=>u.inputs),(e=>{u.inputs.includes(s.value)||(s.value=d(e)),r.value=[...e]})),(t,n)=>{const u=(0,l.up)("q-item-section"),r=(0,l.up)("q-icon"),c=(0,l.up)("q-item"),p=(0,l.up)("q-select"),d=(0,l.up)("q-input");return(0,l.wg)(),(0,l.j4)(d,{dense:"",outlined:"",modelValue:s.value,"onUpdate:modelValue":n[1]||(n[1]=e=>s.value=e),onBlur:m,onChange:m,onKeydown:f,class:"col-grow custom-input"},{append:(0,l.w5)((()=>[(0,l.Wm)(p,{"hide-selected":"",dense:"",outlined:!1,flat:"","fill-input":"",modelValue:i.value,"onUpdate:modelValue":[n[0]||(n[0]=e=>i.value=e),v],options:e.inputs,class:"inner-select"},{option:(0,l.w5)((e=>[(0,l.Wm)(c,(0,a.vs)((0,l.F4)(e.itemProps)),{default:(0,l.w5)((()=>[(0,l.Wm)(u,null,{default:(0,l.w5)((()=>[(0,l.Uk)((0,a.zw)(e.label),1)])),_:2},1024),(0,l.Wm)(u,{avatar:""},{default:(0,l.w5)((()=>[(0,l.Wm)(r,{name:(0,o.SU)(Ne.DNZ),onClick:t=>g(e.label)},null,8,["name","onClick"])])),_:2},1024)])),_:2},1040)])),_:1},8,["modelValue","options"])])),_:1},8,["modelValue"])}}};var Ge=n(4581),Je=n(2857),Ke=n(8149);const $e=Re,Xe=$e;re()(Re,"components",{QInput:qe.Z,QSelect:Ge.Z,QItem:oe.Z,QItemSection:ue.Z,QIcon:Je.Z,QField:Ke.Z});const et={id:"copy-task-view",class:"row"},tt=(0,l._)("div",{class:"col-2"},"Post build step type",-1),nt={class:"col-grow"},lt={class:"col-12 row entry-row"},at={class:"col-2 center-row ellipsis"},ot=(0,l.Uk)(" Input name "),ut={class:"col-12 row entry-row"},st=(0,l._)("div",{class:"col-2 center-row ellipsis"},"Output name",-1),it={class:"col-12 row entry-row"},rt=(0,l._)("div",{class:"col-2 center-row ellipsis"},"Export output as",-1),ct={__name:"GenericStep",props:{project:{type:Object,default:null},step:{type:Object,default:null},singleInput:{type:Boolean,default:!1}},emits:["inputChange","outputChange","exportChange"],setup(e,{emit:t}){const n=e,u=(0,o.iH)(d(n.step)),s=(0,o.iH)(m(n.step)),i=(0,o.iH)(f(n.step)),r=((0,o.iH)(d(n.step)),(0,o.iH)((0,ze.Z)(!0,{},n.step))),c=(0,o.iH)(null);function p(e,n,l){JSON.stringify(e)!==JSON.stringify(n)&&t("inputChange",e),u.value=c.value.getCurrentSelection()}function d(e){return v(e).length>0?v(e)[0]:null}function v(e){let t=B(e,"input",[]);return Array.isArray(t)?t:[t]}function m(e){return B(e,"output","")}function f(e){return B(e,"export","")}function w(e){let l;n.singleInput&&(l=[e]),u.value=e,t("inputChange",l)}function g(e){t("outputChange",e)}function b(e){t("exportChange",e)}function y(e){G(e,"Select File",n.project).then((e=>{n.singleInput?w(e.browseLocation):c.value.addNewValue(e.browseLocation)}))}return(0,l.YP)((()=>n.step),(e=>{v(r.value).includes(u.value)||(u.value=d(e)),s.value=m(e),i.value=f(e),r.value=(0,ze.Z)(!0,{},e)})),(t,r)=>{const d=(0,l.up)("q-badge"),m=(0,l.up)("q-input"),f=(0,l.up)("q-btn");return(0,l.wg)(),(0,l.iD)("div",et,[tt,(0,l._)("div",nt,(0,a.zw)((0,o.SU)(h)(n.step)),1),(0,l._)("div",lt,[(0,l._)("div",at,[ot,e.singleInput?(0,l.kq)("",!0):((0,l.wg)(),(0,l.j4)(d,{key:0,transparent:"",align:"middle",color:"blue"},{default:(0,l.w5)((()=>[(0,l.Uk)((0,a.zw)(v(n.step).length),1)])),_:1}))]),e.singleInput?(0,l.kq)("",!0):((0,l.wg)(),(0,l.j4)(Xe,{key:0,ref_key:"inputSelector",ref:c,inputs:v(n.step),onInputsChanged:r[0]||(r[0]=e=>p(e,v(n.step),"input"))},null,8,["inputs"])),e.singleInput?((0,l.wg)(),(0,l.j4)(m,{key:1,dense:"",outlined:"",square:"",class:"col-grow",modelValue:u.value,"onUpdate:modelValue":[r[1]||(r[1]=e=>u.value=e),r[2]||(r[2]=e=>w(e))]},null,8,["modelValue"])):(0,l.kq)("",!0),(0,l.Wm)(f,{outline:"",push:"",label:"Browse...",class:"col-auto entry-row-button secondary-button",onClick:r[3]||(r[3]=e=>y(u.value))}),(0,l.WI)(t.$slots,"extraInput")]),(0,l._)("div",ut,[st,(0,l.Wm)(m,{modelValue:s.value,"onUpdate:modelValue":[r[4]||(r[4]=e=>s.value=e),r[5]||(r[5]=e=>g(e))],dense:"",outlined:"",square:"",class:"col-grow"},null,8,["modelValue"])]),(0,l._)("div",it,[rt,(0,l.Wm)(m,{modelValue:i.value,"onUpdate:modelValue":[r[6]||(r[6]=e=>i.value=e),r[7]||(r[7]=e=>b(e))],dense:"",outlined:"",square:"",class:"col-grow"},null,8,["modelValue"])])])}}};var pt=n(990);const dt=ct,vt=dt;re()(ct,"components",{QBadge:pt.Z,QInput:qe.Z,QBtn:Le.Z});const mt={class:"col-12 row entry-row"},ft={class:"col-2 center-row ellipsis"},wt={__name:"FileInput",props:{title:{type:String,default:""},inputValue:{type:String,default:""},buttonTitle:{type:String,default:"Browse..."},project:{type:Object,default:null}},emits:["changed"],setup(e,{emit:t}){const n=e,u=(0,o.iH)(n.inputValue);function s(e){t("changed",e)}function i(e){G(e,"Select File",n.project).then((e=>{u.value=e.browseLocation,s(u.value)}))}return(0,l.YP)((()=>n.inputValue),(e=>{u.value=e})),(e,t)=>{const o=(0,l.up)("q-input"),r=(0,l.up)("q-btn");return(0,l.wg)(),(0,l.iD)("div",mt,[(0,l._)("div",ft,(0,a.zw)(n.title),1),(0,l.Wm)(o,{dense:"",outlined:"",square:"",class:"col-grow",modelValue:u.value,"onUpdate:modelValue":[t[0]||(t[0]=e=>u.value=e),t[1]||(t[1]=e=>s(e))]},null,8,["modelValue"]),(0,l.Wm)(r,{push:"",outline:"",dense:"",class:"col-auto entry-row-button secondary-button",label:n.buttonTitle,onClick:t[2]||(t[2]=e=>i(u.value))},null,8,["label"])])}}},gt=wt,bt=gt;re()(wt,"components",{QInput:qe.Z,QBtn:Le.Z});const yt=e=>((0,l.dD)("data-v-6367b086"),e=e(),(0,l.Cn)(),e),ht=yt((()=>(0,l._)("div",{class:"col-11"}," Section information is not available for this file. Try adding a copy step that copies your axf/out/elf file to the same parent directory as this file. ",-1))),kt={class:"row"},_t=yt((()=>(0,l._)("div",{class:"text-h6"},"Manage Sections",-1))),jt={class:"row items-center centered"},St={class:"col-3 row"},Ct=yt((()=>(0,l._)("div",{class:"col-12"},"Included",-1))),Wt={class:"col-1"},Ut=(0,l.Uk)("Remove from included"),qt=(0,l.Uk)("Remove from included"),Vt=(0,l.Uk)("Add to included"),xt=(0,l.Uk)("Excluded section must be empty"),Zt={class:"col-4 row"},Ht={class:"col-12"},It=yt((()=>(0,l._)("div",null,"Available Sections",-1))),Qt={class:"col-1"},Ot=(0,l.Uk)("Add to excluded"),Pt=(0,l.Uk)("Included section must be empty"),Dt=(0,l.Uk)("Remove from included"),At=(0,l.Uk)("Add to included"),Bt={class:"col-3 row"},Mt=yt((()=>(0,l._)("div",{class:"col-12"},"Excluded",-1))),Yt={__name:"SectionsSelector",props:{app:{type:String,default:""},step:{type:Object,default:null}},emits:["includedChanged","excludedChanged"],setup(e,{expose:t,emit:n}){const u=e;function s(){r.value=!0,O()}function i(){r.value=!1}t({show:s,hide:i});let r=(0,o.iH)(!1);const c=(0,o.iH)([]),p=(0,o.iH)(W(u.step)),d=(0,o.iH)(U(u.step)),v=(0,o.iH)(""),m=(0,o.iH)(null),f=(0,o.iH)(!1),w=(0,l.Fl)((()=>null!=m.value)),g=(0,l.Fl)((()=>c.value.filter((e=>!p.value.includes(e.name)&&!d.value.includes(e.name))))),b=(0,l.Fl)((()=>S("included:"))),y=(0,l.Fl)((()=>S("excluded:"))),h=(0,l.Fl)((()=>S("available:"))),k=(0,l.Fl)((()=>C(p.value,j(v.value)))),_=(0,l.Fl)((()=>C(d.value,j(v.value))));function j(e){let t=e.split(":");return t.length<2?e:t[1]}function S(e){return v.value.startsWith(e)}function C(e,t){return e.includes(t)}function W(e){return B(e,"include-section",[])}function U(e){return B(e,"exclude-section",[])}function q(e){e=j(e),p.value.push(e),d.value=d.value.filter((t=>t!==e)),H(p.value)}function V(e){e=j(e),p.value=p.value.filter((t=>t!==e)),H(p.value)}function x(e){e=j(e),d.value.push(e),p.value=p.value.filter((t=>t!==e)),I(d.value)}function Z(e){e=j(e),d.value=d.value.filter((t=>t!==e)),I(d.value)}function H(e){n("includedChanged",e)}function I(e){n("excludedChanged",e)}function Q(e){c.value=(0,ze.Z)(!0,[],e.sections)}function O(){m.value=null,f.value=!0,J(u.app).then((e=>{Q(e.data),f.value=!1})).catch((e=>{m.value=e,f.value=!1}))}return(e,t)=>{const n=(0,l.up)("q-btn"),u=(0,l.up)("q-card-section"),s=(0,l.up)("q-space"),i=(0,l.up)("q-item-section"),c=(0,l.up)("q-item"),m=(0,l.up)("q-list"),j=(0,l.up)("q-tooltip"),S=(0,l.up)("q-inner-loading"),C=(0,l.up)("q-card"),W=(0,l.up)("q-dialog"),U=(0,l.Q2)("close-popup");return(0,l.wg)(),(0,l.j4)(W,{modelValue:(0,o.SU)(r),"onUpdate:modelValue":t[6]||(t[6]=e=>(0,o.dq)(r)?r.value=e:r=e),class:"q-pa-md doc-container"},{default:(0,l.w5)((()=>[(0,l.Wm)(C,{bordered:""},{default:(0,l.w5)((()=>[(0,o.SU)(w)?((0,l.wg)(),(0,l.j4)(u,{key:0,class:"row"},{default:(0,l.w5)((()=>[ht,(0,l.wy)((0,l.Wm)(n,{icon:(0,o.SU)(Ne.r5M),class:"col-1 self-start",flat:"",round:"",dense:""},null,8,["icon"]),[[U]])])),_:1})):((0,l.wg)(),(0,l.j4)(u,{key:1},{default:(0,l.w5)((()=>[(0,l._)("div",kt,[_t,(0,l.Wm)(s),(0,l.wy)((0,l.Wm)(n,{icon:(0,o.SU)(Ne.r5M),class:"col-1 self-start",flat:"",round:"",dense:""},null,8,["icon"]),[[U]])]),(0,l._)("div",jt,[(0,l._)("div",St,[Ct,(0,l.Wm)(m,{bordered:"",separator:"",class:"col-12"},{default:(0,l.w5)((()=>[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)(p.value,((e,t)=>((0,l.wg)(),(0,l.j4)(c,{key:t,clickable:"",active:v.value==="included:"+e,onClick:t=>v.value="included:"+e,"active-class":"selected-section"},{default:(0,l.w5)((()=>[(0,l.Wm)(i,null,{default:(0,l.w5)((()=>[(0,l.Uk)((0,a.zw)(e),1)])),_:2},1024)])),_:2},1032,["active","onClick"])))),128))])),_:1})]),(0,l._)("div",Wt,[(0,l._)("div",null,[(0,l.Wm)(n,{class:"section-button",dense:"",id:"removeSection",icon:(0,o.SU)(Ne.HWE),onClick:t[0]||(t[0]=e=>V(v.value)),disabled:!(0,o.SU)(b)},null,8,["icon","disabled"]),(0,l.Wm)(j,null,{default:(0,l.w5)((()=>[Ut])),_:1})]),(0,l._)("div",null,[(0,l.Wm)(n,{class:"section-button",dense:"",id:"deleteSection",icon:(0,o.SU)(Ne.r5M),onClick:t[1]||(t[1]=e=>V(v.value)),disabled:!(0,o.SU)(b)},null,8,["icon","disabled"]),(0,l.Wm)(j,null,{default:(0,l.w5)((()=>[qt])),_:1})]),(0,l._)("div",null,[(0,l.Wm)(n,{class:"section-button",dense:"",id:"addSection",icon:(0,o.SU)(Ne.fe9),onClick:t[2]||(t[2]=e=>q(v.value)),disabled:!(0,o.SU)(h)||(0,o.SU)(k)||0!==d.value.length},null,8,["icon","disabled"]),0===d.value.length?((0,l.wg)(),(0,l.j4)(j,{key:0},{default:(0,l.w5)((()=>[Vt])),_:1})):((0,l.wg)(),(0,l.j4)(j,{key:1},{default:(0,l.w5)((()=>[xt])),_:1}))])]),(0,l._)("div",Zt,[(0,l._)("div",Ht,[It,(0,l.Wm)(m,{bordered:"",separator:""},{default:(0,l.w5)((()=>[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,o.SU)(g),(e=>((0,l.wg)(),(0,l.j4)(c,{key:e.name,clickable:"",active:v.value==="available:"+e.name,onClick:t=>v.value="available:"+e.name,"active-class":"selected-section"},{default:(0,l.w5)((()=>[(0,l.Wm)(i,null,{default:(0,l.w5)((()=>[(0,l.Uk)((0,a.zw)(e.name),1)])),_:2},1024)])),_:2},1032,["active","onClick"])))),128))])),_:1})])]),(0,l._)("div",Qt,[(0,l._)("div",null,[(0,l.Wm)(n,{class:"section-button",dense:"",id:"excludeSection",icon:(0,o.SU)(Ne.HWE),onClick:t[3]||(t[3]=e=>x(v.value)),disabled:!(0,o.SU)(h)||(0,o.SU)(_)||0!==p.value.length},null,8,["icon","disabled"]),0===p.value.length?((0,l.wg)(),(0,l.j4)(j,{key:0},{default:(0,l.w5)((()=>[Ot])),_:1})):((0,l.wg)(),(0,l.j4)(j,{key:1},{default:(0,l.w5)((()=>[Pt])),_:1}))]),(0,l._)("div",null,[(0,l.Wm)(n,{class:"section-button",dense:"",id:"deleteExcludedSection",icon:(0,o.SU)(Ne.r5M),onClick:t[4]||(t[4]=e=>Z(v.value)),disabled:!(0,o.SU)(y)},null,8,["icon","disabled"]),(0,l.Wm)(j,null,{default:(0,l.w5)((()=>[Dt])),_:1})]),(0,l._)("div",null,[(0,l.Wm)(n,{class:"section-button",dense:"",id:"unExcludeSection",icon:(0,o.SU)(Ne.fe9),onClick:t[5]||(t[5]=e=>Z(v.value)),disabled:!(0,o.SU)(y)},null,8,["icon","disabled"]),(0,l.Wm)(j,null,{default:(0,l.w5)((()=>[At])),_:1})])]),(0,l._)("div",Bt,[Mt,(0,l.Wm)(m,{class:"col-12",bordered:"",separator:""},{default:(0,l.w5)((()=>[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)(d.value,((e,t)=>((0,l.wg)(),(0,l.j4)(c,{key:t,clickable:"",active:v.value==="excluded:"+e,onClick:t=>v.value="excluded:"+e,"active-class":"selected-section"},{default:(0,l.w5)((()=>[(0,l.Wm)(i,null,{default:(0,l.w5)((()=>[(0,l.Uk)((0,a.zw)(e),1)])),_:2},1024)])),_:2},1032,["active","onClick"])))),128))])),_:1})])]),(0,l.Wm)(S,{showing:f.value},null,8,["showing"])])),_:1}))])),_:1})])),_:1},8,["modelValue"])}}};var Ft=n(2074),Lt=n(4458),Tt=n(3190),Et=n(136),Nt=n(854);const zt=(0,we.Z)(Yt,[["__scopeId","data-v-6367b086"]]),Rt=zt;re()(Yt,"components",{QDialog:Ft.Z,QCard:Lt.Z,QCardSection:Tt.Z,QBtn:Le.Z,QSpace:Et.Z,QList:ae.Z,QItem:oe.Z,QItemSection:ue.Z,QTooltip:Ve.Z,QInnerLoading:Nt.Z}),re()(Yt,"directives",{ClosePopup:Ze.Z});const Gt={class:"col-auto"},Jt=(0,l.Uk)("Select sections to include"),Kt=(0,l.Uk)("Not available for multiple inputs"),$t={id:"convert-container",class:"row"},Xt=(0,l._)("div",{class:"col-12 entry-row"},"Secure boot",-1),en={__name:"ConvertTask",props:{project:{type:Object,default:null},step:{type:Object,default:null}},emits:["stepChanged"],setup(e,{emit:t}){const n=e,a=(0,o.iH)(null),u=(0,o.iH)(m(n.step)),s=(0,o.iH)(f(n.step)),i=(0,o.iH)(w(n.step)),r=(0,o.iH)(g(n.step)),c=(0,o.iH)(b(n.step)),p=(0,o.iH)((0,ze.Z)(!0,{},n.step));(0,l.YP)((()=>n.step),(e=>{p.value=(0,ze.Z)(!0,{},e),u.value=m(e),s.value=f(e),i.value=w(e),r.value=g(e),c.value=b(e)})),(0,l.YP)((()=>u.value),(e=>{h("crc",e,!0)}));const d=(0,l.Fl)((()=>R(n.project,T(n.project,Y(n.step,"input")))));function v(e){return 1==M(e,"input")}function m(e){return B(e,"crc",!1)}function f(e){return B(e,"keyfile","")}function w(e){return B(e,"certificate","")}function g(e){return B(e,"signature","")}function b(e){return B(e,"verify","")}function y(e){t("stepChanged",e)}function h(e,t,n=!1){p.value[e]=t,n&&!1===t&&delete p.value[e],y(p.value)}function k(){a.value.show()}return(t,p)=>{const m=(0,l.up)("q-btn"),f=(0,l.up)("q-tooltip"),w=(0,l.up)("q-checkbox"),g=(0,l.up)("q-card-section"),b=(0,l.up)("q-card");return(0,l.wg)(),(0,l.iD)(l.HY,null,[(0,l.Wm)(vt,{project:n.project,step:n.step,onInputChange:p[1]||(p[1]=e=>h("input",e)),onOutputChange:p[2]||(p[2]=e=>h("output",e)),onExportChange:p[3]||(p[3]=e=>h("export",e))},{extraInput:(0,l.w5)((()=>[(0,l._)("div",Gt,[(0,l.Wm)(m,{disabled:!v(n.step),outline:"",push:"",label:"Sections...",class:"entry-row-button secondary-button",onClick:p[0]||(p[0]=e=>k())},null,8,["disabled"]),v(n.step)?((0,l.wg)(),(0,l.j4)(f,{key:0},{default:(0,l.w5)((()=>[Jt])),_:1})):((0,l.wg)(),(0,l.j4)(f,{key:1},{default:(0,l.w5)((()=>[Kt])),_:1}))])])),_:1},8,["project","step"]),(0,l._)("div",$t,[(0,l.Wm)(w,{dense:"",modelValue:u.value,"onUpdate:modelValue":p[4]||(p[4]=e=>u.value=e),label:"Add CRC",class:"col-12"},null,8,["modelValue"]),(0,l.Wm)(b,{bordered:"",class:"col-12 row entry-row"},{default:(0,l.w5)((()=>[(0,l.Wm)(g,{class:"col-12 row"},{default:(0,l.w5)((()=>[Xt,(0,l.Wm)(bt,{title:"Key",inputValue:s.value,buttonTitle:"Browse...",project:e.project,onChanged:p[5]||(p[5]=e=>h("keyfile",e))},null,8,["inputValue","project"]),(0,l.Wm)(bt,{title:"Certificate",inputValue:i.value,buttonTitle:"Browse...",project:e.project,onChanged:p[6]||(p[6]=e=>h("certificate",e))},null,8,["inputValue","project"])])),_:1})])),_:1}),(0,l.Wm)(b,{bordered:"",class:"col-12 row entry-row"},{default:(0,l.w5)((()=>[(0,l.Wm)(g,{class:"col-12 row"},{default:(0,l.w5)((()=>[(0,l.Wm)(bt,{title:"Signature",inputValue:r.value,buttonTitle:"Browse...",project:e.project,onChanged:p[7]||(p[7]=e=>h("signature",e))},null,8,["inputValue","project"]),(0,l.Wm)(bt,{title:"Verify",inputValue:c.value,buttonTitle:"Browse...",project:e.project,onChanged:p[8]||(p[8]=e=>h("verify",e))},null,8,["inputValue","project"])])),_:1})])),_:1}),(0,l.Wm)(Rt,{ref_key:"sectionSelectorConvert",ref:a,step:n.step,app:(0,o.SU)(d),onIncludedChanged:p[9]||(p[9]=e=>h("include-section",e)),onExcludedChanged:p[10]||(p[10]=e=>h("exclude-section",e))},null,8,["step","app"])])],64)}}};var tn=n(1006);const nn=en,ln=nn;re()(en,"components",{QBtn:Le.Z,QTooltip:Ve.Z,QCheckbox:tn.Z,QCard:Lt.Z,QCardSection:Tt.Z});const an={__name:"CopyTask",props:{project:{type:Object,default:null},step:{type:Object,default:null}},emits:["stepChanged"],setup(e,{emit:t}){const n=e,a=(0,o.iH)((0,ze.Z)(!0,{},n.step));function u(e){t("stepChanged",e)}function s(e,t){let n=t;Array.isArray(t)&&(n=t.length>0?t[0]:""),"input"==e&&n&&!n.replace(/\s/g,"").startsWith("{{build_dir}}")&&(n=n.replaceAll("\\","/"),n.includes("/")&&!n.endsWith("/")&&(n=n.substring(n.lastIndexOf("/")+1)),n="{{build_dir}}/"+n),a.value[e]=n,u(a.value)}return(0,l.YP)((()=>n.step),(e=>{a.value=(0,ze.Z)(!0,{},e)})),(e,t)=>((0,l.wg)(),(0,l.j4)(vt,{project:n.project,step:n.step,singleInput:!0,onInputChange:t[0]||(t[0]=e=>s("input",e)),onOutputChange:t[1]||(t[1]=e=>s("output",e)),onExportChange:t[2]||(t[2]=e=>s("export",e))},null,8,["project","step"]))}},on=an,un=on,sn={class:"row entry-row"},rn={class:"col-2 center-row ellipsis"},cn={__name:"SimpleInput",props:{title:{type:String,default:""},inputValue:{type:String,default:""},readOnly:{type:Boolean,default:!1}},emits:["changed"],setup(e,{emit:t}){const n=e,u=(0,o.iH)(n.inputValue);function s(){return n.readOnly}function i(e){t("changed",e)}return(0,l.YP)((()=>n.inputValue),(e=>{u.value=e})),(e,t)=>{const o=(0,l.up)("q-input");return(0,l.wg)(),(0,l.iD)("div",sn,[(0,l._)("div",rn,(0,a.zw)(n.title),1),s()?(0,l.kq)("",!0):((0,l.wg)(),(0,l.j4)(o,{key:0,dense:"",outlined:"",square:"",class:"col-grow",modelValue:u.value,"onUpdate:modelValue":[t[0]||(t[0]=e=>u.value=e),t[1]||(t[1]=e=>i(e))]},null,8,["modelValue"])),s()?((0,l.wg)(),(0,l.j4)(o,{key:1,dense:"",outlined:"",square:"",class:"col-grow",value:"props.inputValue"})):(0,l.kq)("",!0)])}}},pn=cn,dn=pn;re()(cn,"components",{QInput:qe.Z});const vn=e=>((0,l.dD)("data-v-2779a6c8"),e=e(),(0,l.Cn)(),e),mn={class:"row entry-row"},fn=vn((()=>(0,l._)("div",{class:"col-2"},"Post build step type",-1))),wn={class:"col-grow"},gn={class:"row entry-row"},bn=vn((()=>(0,l._)("div",{class:"col-2 center-row ellipsis"},"Application Image",-1))),yn=(0,l.Uk)("Select sections to include"),hn=(0,l.Uk)("Not available for multiple inputs"),kn={class:"row entry-row"},_n=vn((()=>(0,l._)("div",{class:"col-2 center-row ellipsis"},"Bootloader",-1))),jn={class:"row entry-row"},Sn=vn((()=>(0,l._)("div",{class:"col-2 center-row ellipsis"},"SE upgrade",-1))),Cn={class:"row entry-row"},Wn=vn((()=>(0,l._)("div",{class:"col-2 center-row ellipsis"},"Metadata",-1))),Un={class:"row justify-left"},qn=vn((()=>(0,l._)("div",{class:"col-2 vcenter-label ellipsis"},"Compression",-1))),Vn={__name:"GBLTask",props:{project:{type:Object,default:null},step:{type:Object,default:null}},emits:["stepChanged"],setup(e,{emit:t}){const n=e,u=(0,o.iH)(_(n.step)),s=(0,o.iH)(j(n.step)),i=(0,o.iH)(S(n.step)),r=(0,o.iH)(C(n.step)),c=(0,o.iH)(W(n.step)),p=(0,o.iH)(U(n.step)),d=(0,o.iH)(q(n.step)),v=(0,o.iH)(V(n.step)),m=(0,o.iH)(x(n.step)),f=(0,o.iH)(Z(n.step)),w=(0,o.iH)(H(n.step)),g=(0,o.iH)(null),b=(0,o.iH)((0,ze.Z)(!0,{},n.step));(0,l.YP)((()=>n.step),(e=>{u.value=_(e),s.value=j(e),i.value=S(e),r.value=C(e),c.value=W(e),p.value=U(e),d.value=q(e),v.value=V(e),m.value=x(e),f.value=Z(e),w.value=H(e),b.value=(0,ze.Z)(!0,{},e)})),(0,l.YP)((()=>v.value),(e=>{Q("extsign",e,!0)}));const y=(0,l.Fl)((()=>R(n.project,T(n.project,Y(n.step,"app")))));function k(e){return 1==M(e,"app")}function _(e){let t=P(e);return t.length>0?t[0]:""}function j(e){let t=D(e);return t.length>0?t[0]:""}function S(e){let t=A(e);return t.length>0?t[0]:""}function C(e){let t=F(e);return t.length>0?t[0]:""}function W(e){let t=B(e,"compress","");return""===t?"none":t}function U(e){return B(e,"output","")}function q(e){return B(e,"export","")}function V(e){return B(e,"extsign",!1)}function x(e){return B(e,"sign","")}function Z(e){return B(e,"certificate","")}function H(e){return B(e,"encrypt","")}function I(e){t("stepChanged",e)}function Q(e,t,n=!1){b.value[e]=t,n&&!1===t&&delete b.value[e],I(b.value)}function O(e){Q("compress","none"===e?"":e)}function P(e){let t=B(e,"app",[]);return Array.isArray(t)?t:[t]}function D(e){let t=B(e,"bootloader",[]);return Array.isArray(t)?t:[t]}function A(e){let t=B(e,"seupgrade",[]);return Array.isArray(t)?t:[t]}function F(e){let t=B(e,"metadata",[]);return Array.isArray(t)?t:[t]}function L(e){return["none","lzma","lz4"]}function E(){g.value.show()}function N(e,t){G(t,"Select File",n.project).then((t=>{Q(e,t.browseLocation)}))}return(t,b)=>{const _=(0,l.up)("q-input"),j=(0,l.up)("q-btn"),S=(0,l.up)("q-tooltip"),C=(0,l.up)("q-select"),W=(0,l.up)("q-checkbox");return(0,l.wg)(),(0,l.iD)(l.HY,null,[(0,l._)("div",mn,[fn,(0,l._)("div",wn,(0,a.zw)((0,o.SU)(h)(n.step)),1)]),(0,l._)("div",gn,[bn,(0,l.Wm)(_,{dense:"",outlined:"",square:"",class:"col-grow",modelValue:u.value,"onUpdate:modelValue":[b[0]||(b[0]=e=>u.value=e),b[1]||(b[1]=e=>Q("app",e))]},null,8,["modelValue"]),(0,l.Wm)(j,{outline:"",push:"",label:"Browse...",class:"col-auto entry-row-button secondary-button",onClick:b[2]||(b[2]=e=>N("app",u.value))}),(0,l._)("div",null,[(0,l.Wm)(j,{disabled:!k(n.step),outline:"",push:"",label:"Sections...",class:"col-auto entry-row-button secondary-button",onClick:b[3]||(b[3]=e=>E())},null,8,["disabled"]),k(n.step)?((0,l.wg)(),(0,l.j4)(S,{key:0},{default:(0,l.w5)((()=>[yn])),_:1})):((0,l.wg)(),(0,l.j4)(S,{key:1},{default:(0,l.w5)((()=>[hn])),_:1}))])]),(0,l._)("div",kn,[_n,(0,l.Wm)(_,{dense:"",outlined:"",square:"",class:"col-grow",modelValue:s.value,"onUpdate:modelValue":[b[4]||(b[4]=e=>s.value=e),b[5]||(b[5]=e=>Q("bootloader",e))]},null,8,["modelValue"]),(0,l.Wm)(j,{outline:"",push:"",label:"Browse...",class:"col-auto entry-row-button secondary-button",onClick:b[6]||(b[6]=e=>N("bootloader",s.value))})]),(0,l._)("div",jn,[Sn,(0,l.Wm)(_,{dense:"",outlined:"",square:"",class:"col-grow",modelValue:i.value,"onUpdate:modelValue":[b[7]||(b[7]=e=>i.value=e),b[8]||(b[8]=e=>Q("seupgrade",e))]},null,8,["modelValue"]),(0,l.Wm)(j,{outline:"",push:"",label:"Browse...",class:"col-auto entry-row-button secondary-button",onClick:b[9]||(b[9]=e=>N("seupgrade",i.value))})]),(0,l._)("div",Cn,[Wn,(0,l.Wm)(_,{dense:"",outlined:"",square:"",class:"col-grow",modelValue:r.value,"onUpdate:modelValue":[b[10]||(b[10]=e=>r.value=e),b[11]||(b[11]=e=>Q("metadata",e))]},null,8,["modelValue"]),(0,l.Wm)(j,{outline:"",push:"",label:"Browse...",class:"col-auto entry-row-button secondary-button",onClick:b[12]||(b[12]=e=>N("metadata",r.value))})]),(0,l._)("div",Un,[qn,(0,l.Wm)(C,{modelValue:c.value,"onUpdate:modelValue":[b[13]||(b[13]=e=>c.value=e),b[14]||(b[14]=e=>O(e))],options:L(),dense:"",outlined:"",square:"",class:"col-4 items-start"},null,8,["modelValue","options"])]),(0,l.Wm)(dn,{title:"Output name",inputValue:p.value,onChanged:b[15]||(b[15]=e=>Q("output",e))},null,8,["inputValue"]),(0,l.Wm)(dn,{title:"Export output as",inputValue:d.value,onChanged:b[16]||(b[16]=e=>Q("export",e))},null,8,["inputValue"]),(0,l.Wm)(W,{dense:"",modelValue:v.value,"onUpdate:modelValue":b[17]||(b[17]=e=>v.value=e),label:"Sign using HWM",class:"col-12"},null,8,["modelValue"]),(0,l.Wm)(bt,{title:"Signing key",inputValue:m.value,project:e.project,onChanged:b[18]||(b[18]=e=>Q("sign",e))},null,8,["inputValue","project"]),(0,l.Wm)(bt,{title:"Signing certificate",inputValue:f.value,project:e.project,onChanged:b[19]||(b[19]=e=>Q("certificate",e))},null,8,["inputValue","project"]),(0,l.Wm)(bt,{title:"Encrypt",inputValue:w.value,project:e.project,onChanged:b[20]||(b[20]=e=>Q("encrypt",e))},null,8,["inputValue","project"]),(0,l.Wm)(Rt,{ref_key:"sectionSelectorGBL",ref:g,step:n.step,app:(0,o.SU)(y),onIncludedChanged:b[21]||(b[21]=e=>Q("include-section",e)),onExcludedChanged:b[22]||(b[22]=e=>Q("exclude-section",e))},null,8,["step","app"])],64)}}},xn=(0,we.Z)(Vn,[["__scopeId","data-v-2779a6c8"]]),Zn=xn;re()(Vn,"components",{QInput:qe.Z,QBtn:Le.Z,QTooltip:Ve.Z,QSelect:Ge.Z,QCheckbox:tn.Z});const Hn=e=>((0,l.dD)("data-v-38a7f6b9"),e=e(),(0,l.Cn)(),e),In={class:"row"},Qn=Hn((()=>(0,l._)("div",{class:"col-2 center-row ellipsis"},"Upgrade Image",-1))),On={class:"row"},Pn=Hn((()=>(0,l._)("div",{class:"col-2 center-row ellipsis"},"Min HW",-1))),Dn=Hn((()=>(0,l._)("div",{class:"col-2 center-row ellipsis max-hw-label"},"Max HW",-1))),An={__name:"OtaTask",props:{project:{type:Object,default:null},step:{type:Object,default:null}},emits:["stepChanged"],setup(e,{emit:t}){const n=e,a=(0,o.iH)(g(n.step)),u=(0,o.iH)(b(n.step)),s=(0,o.iH)(y(n.step)),i=(0,o.iH)(h(n.step)),r=(0,o.iH)(k(n.step)),c=(0,o.iH)(_(n.step)),p=(0,o.iH)(j(n.step)),d=(0,o.iH)(S(n.step)),v=(0,o.iH)(C(n.step)),m=(0,o.iH)(W(n.step)),f=(0,o.iH)(U(n.step)),w=(0,o.iH)((0,ze.Z)(!0,{},n.step));function g(e){return B(e,"manufacturer-id","")}function b(e){return B(e,"image-type","")}function y(e){return B(e,"string","")}function h(e){let t=Z(e);return t.length>0?t[0]:""}function k(e){return B(e,"firmware-version","")}function _(e){return B(e,"manufacturer-tag","")}function j(e){return B(e,"stack-version","")}function S(e){return B(e,"credentials","")}function C(e){return B(e,"destination","")}function W(e){return B(e,"min-hw","")}function U(e){return B(e,"max-hw","")}function q(e){t("stepChanged",e)}function V(e){let t=e;Array.isArray(e)&&(t=e.length>0?e[0]:""),x("input",t)}function x(e,t){w.value[e]=t,q(w.value)}function Z(e){let t=B(e,"upgrade-image",[]);return Array.isArray(t)?t:[t]}function H(e,t){G(t,"Select File",n.project).then((t=>{x(e,t.browseLocation)}))}return(0,l.YP)((()=>n.step),(e=>{i.value=h(e),a.value=g(e),u.value=b(e),s.value=y(e),r.value=k(e),c.value=_(e),p.value=j(e),d.value=S(e),v.value=C(e),m.value=W(e),f.value=U(e),w.value=(0,ze.Z)(!0,{},e)})),(e,t)=>{const o=(0,l.up)("q-input"),w=(0,l.up)("q-btn");return(0,l.wg)(),(0,l.iD)(l.HY,null,[(0,l.Wm)(vt,{project:n.project,step:n.step,singleInput:!0,onInputChange:t[0]||(t[0]=e=>V(e)),onOutputChange:t[1]||(t[1]=e=>x("output",e)),onExportChange:t[2]||(t[2]=e=>x("export",e))},null,8,["project","step"]),(0,l.Wm)(dn,{title:"Manufacturer ID",inputValue:a.value,onChanged:t[3]||(t[3]=e=>x("manufacturer-id",e))},null,8,["inputValue"]),(0,l.Wm)(dn,{title:"Image type",inputValue:u.value,onChanged:t[4]||(t[4]=e=>x("image-type",e))},null,8,["inputValue"]),(0,l.Wm)(dn,{title:"Header string",inputValue:s.value,onChanged:t[5]||(t[5]=e=>x("string",e))},null,8,["inputValue"]),(0,l._)("div",In,[Qn,(0,l.Wm)(o,{dense:"",outlined:"",square:"",class:"col-grow",modelValue:i.value,"onUpdate:modelValue":[t[6]||(t[6]=e=>i.value=e),t[7]||(t[7]=e=>x("upgrade-image",e))]},null,8,["modelValue"]),(0,l.Wm)(w,{outline:"",push:"",label:"Browse...",class:"col-quto entry-row-button secondary-button",onClick:t[8]||(t[8]=e=>H("upgrade-image",i.value))})]),(0,l.Wm)(dn,{title:"Firmware Version",inputValue:r.value,onChanged:t[9]||(t[9]=e=>x("firmware-version",e))},null,8,["inputValue"]),(0,l.Wm)(dn,{title:"Manufacturer tag",inputValue:c.value,onChanged:t[10]||(t[10]=e=>x("manufacturer-tag",e))},null,8,["inputValue"]),(0,l.Wm)(dn,{title:"Stack version",inputValue:p.value,onChanged:t[11]||(t[11]=e=>x("stack-version",e))},null,8,["inputValue"]),(0,l.Wm)(dn,{title:"Credentials",inputValue:d.value,onChanged:t[12]||(t[12]=e=>x("credentials",e))},null,8,["inputValue"]),(0,l.Wm)(dn,{title:"Target device EUI64",inputValue:v.value,onChanged:t[13]||(t[13]=e=>x("destination",e))},null,8,["inputValue"]),(0,l._)("div",On,[Pn,(0,l.Wm)(o,{dense:"",outlined:"",square:"",class:"col-grow",modelValue:m.value,"onUpdate:modelValue":[t[14]||(t[14]=e=>m.value=e),t[15]||(t[15]=e=>x("min-hw",e))]},null,8,["modelValue"]),Dn,(0,l.Wm)(o,{dense:"",outlined:"",square:"",class:"col-grow",modelValue:f.value,"onUpdate:modelValue":[t[16]||(t[16]=e=>f.value=e),t[17]||(t[17]=e=>x("max-hw",e))]},null,8,["modelValue"])])],64)}}},Bn=(0,we.Z)(An,[["__scopeId","data-v-38a7f6b9"]]),Mn=Bn;re()(An,"components",{QInput:qe.Z,QBtn:Le.Z});const Yn=e=>((0,l.dD)("data-v-c178b3ca"),e=e(),(0,l.Cn)(),e),Fn=Yn((()=>(0,l._)("div",{class:"constants-view"},"Available parameters & constants",-1))),Ln={__name:"ConstantsViewer",props:{project:{type:Object,default:null},step:{type:Object,default:null}},setup(e){const t=e;return(e,n)=>((0,l.wg)(),(0,l.iD)(l.HY,null,[Fn,(0,l.Wm)(Ie,{project:t.project,step:t.step,showExport:!0},null,8,["project","step"])],64))}},Tn=(0,we.Z)(Ln,[["__scopeId","data-v-c178b3ca"]]),En=Tn,Nn={id:"base-property-editor",class:"row"},zn={id:"base-property-editor-container",class:"col-9"},Rn={class:"no-margin"},Gn={class:"col-3"},Jn={__name:"PropertyEditor",props:{project:{type:Object,default:null},task:{type:String,default:"params"}},emits:["stepChanged","parametersChanged","constantsChanged"],setup(e,{emit:t}){const n=e;function a(e,n,l){t("stepChanged",{project:e,task:n,step:l})}function u(e,n){t("parametersChanged",{project:e,newParams:n})}function s(e,n){t("constantsChanged",{project:e,newConsts:n})}function i(e){return"params"!==e}return(e,t)=>((0,l.wg)(),(0,l.iD)("div",Nn,[(0,l._)("div",zn,[(0,l._)("div",Rn,["params"===n.task?((0,l.wg)(),(0,l.j4)(Ee,{key:0,project:n.project,step:(0,o.SU)(S)(n.project,n.task),onParametersChanged:t[0]||(t[0]=e=>u(n.project,e)),onConstantsChanged:t[1]||(t[1]=e=>s(n.project,e))},null,8,["project","step"])):"convert"===(0,o.SU)(_)(n.task)?((0,l.wg)(),(0,l.j4)(ln,{key:1,project:n.project,step:(0,o.SU)(S)(n.project,n.task),onStepChanged:t[2]||(t[2]=e=>a(n.project,n.task,e))},null,8,["project","step"])):"copy"===(0,o.SU)(_)(n.task)?((0,l.wg)(),(0,l.j4)(un,{key:2,project:n.project,step:(0,o.SU)(S)(n.project,n.task),onStepChanged:t[3]||(t[3]=e=>a(n.project,n.task,e))},null,8,["project","step"])):"create_gbl"===(0,o.SU)(_)(n.task)?((0,l.wg)(),(0,l.j4)(Zn,{key:3,project:n.project,step:(0,o.SU)(S)(n.project,n.task),onStepChanged:t[4]||(t[4]=e=>a(n.project,n.task,e))},null,8,["project","step"])):"create_ota"===(0,o.SU)(_)(n.task)?((0,l.wg)(),(0,l.j4)(Mn,{key:4,project:n.project,step:(0,o.SU)(S)(n.project,n.task),onStepChanged:t[5]||(t[5]=e=>a(n.project,n.task,e))},null,8,["project","step"])):((0,l.wg)(),(0,l.j4)(vt,{key:5,project:n.project,step:(0,o.SU)(S)(n.project,n.task)},null,8,["project","step"]))])]),(0,l._)("div",Gn,[i(n.task)?((0,l.wg)(),(0,l.j4)(En,{key:0,project:n.project,step:(0,o.SU)(S)(n.project,n.task)},null,8,["project","step"])):(0,l.kq)("",!0)])]))}},Kn=(0,we.Z)(Jn,[["__scopeId","data-v-10af8743"]]),$n=Kn,Xn={class:"row q-gutter-sm"},el=(0,l.Uk)("Delete Post-Build Step."),tl=(0,l.Uk)("Move post-build step down."),nl=(0,l.Uk)("Create new post-build step from copy."),ll=(0,l.Uk)("Move post-build step up."),al=(0,l.Uk)("Add new post-build step."),ol={__name:"ButtonBar",props:{canDelete:{type:Boolean,default:!1},canMoveDown:{type:Boolean,default:!1},canCopy:{type:Boolean,default:!1},canMoveUp:{type:Boolean,default:!1}},emits:["deleteStep","moveStepDown","copyStep","moveStepUp","addStep"],setup(e,{emit:t}){const n=e,u={color:"primary",outline:!0,dense:!0,size:"sm"};function s(){t("deleteStep")}function i(){t("moveStepDown")}function r(){t("copyStep")}function c(){t("moveStepUp")}function p(e){t("addStep",e)}return(e,t)=>{const d=(0,l.up)("q-tooltip"),v=(0,l.up)("q-btn"),m=(0,l.up)("q-item-section"),f=(0,l.up)("q-item"),w=(0,l.up)("q-list"),g=(0,l.up)("q-menu"),b=(0,l.Q2)("close-popup");return(0,l.wg)(),(0,l.iD)("div",null,[(0,l._)("div",Xn,[(0,l.Wm)(v,(0,l.dG)({id:"deleteStep"},u,{icon:(0,o.SU)(Ne.LxM),disabled:!n.canDelete,onClick:t[0]||(t[0]=e=>s())}),{default:(0,l.w5)((()=>[(0,l.Wm)(d,null,{default:(0,l.w5)((()=>[el])),_:1})])),_:1},16,["icon","disabled"]),(0,l.Wm)(v,(0,l.dG)({id:"moveStepDown"},u,{icon:(0,o.SU)(Ne.qID),disabled:!n.canMoveDown,onClick:t[1]||(t[1]=e=>i())}),{default:(0,l.w5)((()=>[(0,l.Wm)(d,null,{default:(0,l.w5)((()=>[tl])),_:1})])),_:1},16,["icon","disabled"]),(0,l.Wm)(v,(0,l.dG)({id:"copyStep"},u,{icon:(0,o.SU)(Ne.a0Z),disabled:!n.canCopy,onClick:t[2]||(t[2]=e=>r())}),{default:(0,l.w5)((()=>[(0,l.Wm)(d,null,{default:(0,l.w5)((()=>[nl])),_:1})])),_:1},16,["icon","disabled"]),(0,l.Wm)(v,(0,l.dG)({id:"moveStepUp"},u,{icon:(0,o.SU)(Ne.iLF),disabled:!n.canMoveUp,onClick:t[3]||(t[3]=e=>c())}),{default:(0,l.w5)((()=>[(0,l.Wm)(d,null,{default:(0,l.w5)((()=>[ll])),_:1})])),_:1},16,["icon","disabled"]),(0,l.Wm)(v,(0,l.dG)({id:"addStep"},u,{icon:(0,o.SU)(Ne.Q0_)}),{default:(0,l.w5)((()=>[(0,l.Wm)(d,null,{default:(0,l.w5)((()=>[al])),_:1}),(0,l.Wm)(g,null,{default:(0,l.w5)((()=>[(0,l.Wm)(w,{class:"add-step-menu"},{default:(0,l.w5)((()=>[((0,l.wg)(!0),(0,l.iD)(l.HY,null,(0,l.Ko)((0,o.SU)(D)(),(e=>(0,l.wy)(((0,l.wg)(),(0,l.j4)(f,{key:e,clickable:"",onClick:t=>p(e)},{default:(0,l.w5)((()=>[(0,l.Wm)(m,null,{default:(0,l.w5)((()=>[(0,l.Uk)((0,a.zw)(e),1)])),_:2},1024)])),_:2},1032,["onClick"])),[[b]]))),128))])),_:1})])),_:1})])),_:1},16,["icon"])])])}}},ul=(0,we.Z)(ol,[["__scopeId","data-v-0b124ad8"]]),sl=ul;re()(ol,"components",{QBtn:Le.Z,QTooltip:Ve.Z,QMenu:We.Z,QList:ae.Z,QItem:oe.Z,QItemSection:ue.Z}),re()(ol,"directives",{ClosePopup:Ze.Z});var il=n(3746),rl=n(9357);const cl=(0,il.Q_)("postBuild",{state:()=>({shouldSave:!1,reloaded:!1,workspace:{id:"",name:"",projects:[]},workspaceId:""}),getters:{},actions:{setWorkspaceId(e){this.workspaceId=e},getWorkspace(){return new Promise(((e,t)=>d.getWorkspace(this.workspaceId).then((t=>{this.shouldSave=!1,this.workspace=t,e(t)})).catch((e=>{t(e)}))))},putWorkspace(e){return new Promise(((t,n)=>d.putWorkspace(this.workspaceId,e).then((e=>{this.shouldSave=!1,this.workspace=e.data,t(e)})).catch((e=>{n(e)}))))},putDirty(e){d.putDirty(this.workspaceId,e)},postBuildSocketOnError(e){window.console.log(e)},postBuildSocketOnMessage(e){const t=JSON.parse(e.data);t.clientId===this.workspaceId&&("save"===t.action?this.shouldSave=!0:"reload"===t.action&&(this.reloaded=!0,this.shouldSave=!1,this.workspace=t.workspace))},resetReloaded(){this.reloaded=!1},createSaveStatusSocket(){const e=new rl.sh(`ws://${window.location.host}/ws/postbuild/server/notifications/${this.workspaceId}`,this,"postBuildSocket");return e.init(),e}}});var pl=n(9302),dl=n(8339);const vl=e=>((0,l.dD)("data-v-1add6d98"),e=e(),(0,l.Cn)(),e),ml={class:"row items-center"},fl=vl((()=>(0,l._)("div",{class:"app-header"},[(0,l._)("div",{class:"text-h6 q-py-sm q-px-lg"},"Post Build Editor")],-1))),wl={class:"justify-right"},gl={class:"app-footer"},bl={id:"app-file-button-row row fill-width"},yl=(0,l.Uk)(" Show YAML "),hl=(0,l.Uk)(" Save "),kl={class:"fit column"},_l={class:"row justify-between items-center q-py-xs"},jl={class:"col q-mini-drawer-hide"},Sl={class:"editor-container col-12"},Cl={__name:"IndexPage",setup(e){const t=(0,dl.tv)(),n=(0,dl.yj)(),u=(0,pl.Z)(),s=cl(),i=(0,o.iH)(null),r=(0,o.iH)(null),c=(0,o.iH)(null);let p=null;const d=(0,o.iH)(null),v=(0,o.iH)(null),m=(0,o.iH)(null);function f(){i.value=(0,ze.Z)(!0,{},s.workspace),r.value=(0,ze.Z)(!0,{},i.value)}function w(e){c.value=e}function g(e){return j(r.value,e)}function b(e){h(r.value,e.project,"parameters",e.newParams)}function y(e){h(r.value,e.project,"constants",e.newConsts)}function h(e,t,n,l){if(te(t))t.hasOwnProperty("postbuild")&&(e.postbuild[n]=[...l]);else for(const[a,o]of e.projects.entries())if(o.id===t.id&&e.projects[a].hasOwnProperty("postbuild")){e.projects[a].postbuild[n]=[...l];break}}function k(e){_(r.value,e.project,e.task,e.step)}function _(e,t,n,l){let a=te(t),o=-1,u=-1;if(Object.keys(l).forEach((e=>{"string"===typeof l[e]&&""===l[e].trim()&&delete l[e]})),a){if(t.hasOwnProperty("postbuild"))for(const[s,i]of t.postbuild.steps.entries())i.task==n&&(a=!0,u=s)}else for(const[s,i]of e.projects.entries())if(i.id===t.id){for(const[e,t]of i.postbuild.steps.entries())if(t.task===n){o=s,u=e;break}break}-1!=u&&(a?e.postbuild.steps[u]=(0,ze.Z)(!1,{},l):-1!=o&&(e.projects[o].postbuild.steps[u]=(0,ze.Z)(!1,{},l)))}(0,l.bv)((async()=>{u.loading.show({delay:400}),await t.isReady(),s.setWorkspaceId(n.query.workspace),p=s.createSaveStatusSocket(s),await s.getWorkspace().then((()=>{f()})).catch((e=>{u.notify({type:"negative",message:"Something went wrong getting workspace. Try reopening editor."+e.message})})),u.loading.hide()})),(0,l.Jd)((()=>{p&&p.close()}));const S=(0,l.Fl)((()=>{let e=!u.dark.isActive;return{"bg-white":e,"text-black":e,"bg-dark":!e}})),U=(0,o.iH)(!1),O=(0,l.Fl)((()=>null!==r.value&&JSON.stringify(s.workspace)!==JSON.stringify(r.value)));function P(){return null!=c.value&&null!=c.value.task}function D(){return"params"===c.value.task}const A=(0,l.Fl)((()=>!!P()&&!D())),B=(0,l.Fl)((()=>!!P()&&!D())),M=(0,l.Fl)((()=>!(!P()||D())&&!V(r.value,c.value.project,c.value.task))),Y=(0,l.Fl)((()=>!(!P()||D())&&!q(r.value,c.value.project,c.value.task)));function F(e,t){const n=j(r.value,e);n&&d.value&&d.value.setSelected(n,t,te(n))}function L(e,t){if(e&&t){const n=W(r.value,e,t);n&&n.task&&F(e,n.task)}}function T(){H(r.value,c.value.project,c.value.task),F(c.value.project,"params")}function E(){x(r.value,c.value.project,c.value.task)}function N(){const e=Q(r.value,c.value.project,c.value.task);e&&F(c.value.project,e.task)}function z(){Z(r.value,c.value.project,c.value.task)}function R(e){const t=I(r.value,c.value.project,e,c.value.task);t&&F(c.value.project,t.task)}function G(){c.value&&(v.value=c.value.project,m.value=C(r.value,c.value.project,c.value.task))}function J(){L(v.value,m.value),v.value=null,m.value=null}async function K(){u.loading.show({delay:400}),G(),await s.putWorkspace(r.value).then((()=>{f(),J(),u.notify({type:"positive",message:"Post Build(s) successfully saved!",actions:[{icon:Ne.r5M,color:"white"}]})})).catch((()=>{u.notify({type:"negative",message:"Something went wrong saving workspace!",actions:[{icon:Ne.r5M,color:"white"}]})})),u.loading.hide()}function $(){if(c.value&&i.value){let e=g(c.value.project);if(e){let t=X(e);t&&e.hasOwnProperty("postbuild")&&ee(i.value.id,t,O.value?e.postbuild:null)}}}return(0,l.YP)((()=>O.value),(async e=>{s.putDirty({isDirty:e})})),(0,l.YP)((()=>s.shouldSave),(async e=>{e&&K()})),(0,l.YP)((()=>s.reloaded),(async e=>{e&&(f(),u.notify({type:"positive",message:"Post Build(s) changed, data reloaded!",actions:[{icon:Ne.r5M,color:"white"}]}),s.resetReloaded())})),(e,t)=>{const n=(0,l.up)("q-separator"),u=(0,l.up)("q-header"),s=(0,l.up)("q-space"),i=(0,l.up)("q-btn"),p=(0,l.up)("q-footer"),v=(0,l.up)("q-scroll-area"),m=(0,l.up)("q-drawer"),f=(0,l.up)("q-card-section"),h=(0,l.up)("q-card"),_=(0,l.up)("q-page"),j=(0,l.up)("q-page-container");return(0,l.wg)(),(0,l.iD)(l.HY,null,[(0,l.Wm)(u,{elevated:"",class:(0,a.C_)((0,o.SU)(S))},{default:(0,l.w5)((()=>[(0,l._)("div",ml,[fl,(0,l.Wm)(n,{vertical:"",class:"gt-sm bg-grey-6 q-mr-md"})])])),_:1},8,["class"]),(0,l.Wm)(p,{elevated:"",class:(0,a.C_)((0,o.SU)(S))},{default:(0,l.w5)((()=>[(0,l._)("div",wl,[(0,l._)("div",gl,[(0,l._)("div",bl,[(0,l.Wm)(s),(0,l.Wm)(i,{class:"app-file-button",color:"primary",title:"Show YAML",onClick:$},{default:(0,l.w5)((()=>[yl])),_:1}),(0,l.Wm)(i,{class:"app-file-button",color:"primary",title:"Save",disabled:!(0,o.SU)(O),onClick:K},{default:(0,l.w5)((()=>[hl])),_:1},8,["disabled"])])]),(0,l.Wm)(n,{vertical:"",class:"gt-sm bg-grey-6 q-mr-md"})])])),_:1},8,["class"]),(0,l.Wm)(m,{value:!0,mini:U.value,"show-if-above":"",elevated:"",behavior:"desktop",width:300,"mini-width":32},{default:(0,l.w5)((()=>[(0,l._)("div",kl,[(0,l._)("div",_l,[(0,l.Wm)((0,o.SU)(sl),{class:"q-mini-drawer-hide q-pl-md",canDelete:(0,o.SU)(A),canCopy:(0,o.SU)(B),canMoveDown:(0,o.SU)(M),canMoveUp:(0,o.SU)(Y),onDeleteStep:t[0]||(t[0]=()=>T()),onMoveStepDown:t[1]||(t[1]=()=>E()),onCopyStep:t[2]||(t[2]=()=>N()),onMoveStepUp:t[3]||(t[3]=()=>z()),onAddStep:t[4]||(t[4]=e=>R(e))},null,8,["canDelete","canCopy","canMoveDown","canMoveUp"]),(0,l.Wm)(i,{dense:"",flat:"",color:"primary",icon:U.value?(0,o.SU)(Ne.DDS):(0,o.SU)(Ne.zAB),onClick:t[5]||(t[5]=e=>U.value=!U.value)},null,8,["icon"])]),(0,l.Wm)(n,{class:"q-mini-drawer-hide toolbar-separator"}),(0,l._)("div",jl,[(0,l.Wm)(v,{class:"fit"},{default:(0,l.w5)((()=>[r.value?((0,l.wg)(),(0,l.j4)(ye,{key:0,ref_key:"projectList",ref:d,workspace:r.value,onSelected:t[6]||(t[6]=e=>w(e))},null,8,["workspace"])):(0,l.kq)("",!0)])),_:1})])])])),_:1},8,["mini"]),(0,l.Wm)(j,null,{default:(0,l.w5)((()=>[(0,l.Wm)(_,{padding:"",class:"row items-stretch"},{default:(0,l.w5)((()=>[(0,l._)("div",Sl,[(0,l.Wm)(h,null,{default:(0,l.w5)((()=>[(0,l.Wm)(f,null,{default:(0,l.w5)((()=>[c.value?((0,l.wg)(),(0,l.j4)($n,{key:0,project:g(c.value.project),task:c.value.task,onStepChanged:t[7]||(t[7]=e=>k(e)),onParametersChanged:t[8]||(t[8]=e=>b(e)),onConstantsChanged:t[9]||(t[9]=e=>y(e))},null,8,["project","task"])):(0,l.kq)("",!0)])),_:1})])),_:1})])])),_:1})])),_:1})],64)}}};var Wl=n(6602),Ul=n(926),ql=n(1378),Vl=n(906),xl=n(6663),Zl=n(2133),Hl=n(9885);const Il=(0,we.Z)(Cl,[["__scopeId","data-v-1add6d98"]]),Ql=Il;re()(Cl,"components",{QHeader:Wl.Z,QSeparator:Ul.Z,QFooter:ql.Z,QSpace:Et.Z,QBtn:Le.Z,QDrawer:Vl.Z,QScrollArea:xl.Z,QPageContainer:Zl.Z,QPage:Hl.Z,QCard:Lt.Z,QCardSection:Tt.Z})}}]);