(function(t){function e(e){for(var a,n,l=e[0],o=e[1],c=e[2],u=0,p=[];u<l.length;u++)n=l[u],Object.prototype.hasOwnProperty.call(s,n)&&s[n]&&p.push(s[n][0]),s[n]=0;for(a in o)Object.prototype.hasOwnProperty.call(o,a)&&(t[a]=o[a]);d&&d(e);while(p.length)p.shift()();return r.push.apply(r,c||[]),i()}function i(){for(var t,e=0;e<r.length;e++){for(var i=r[e],a=!0,l=1;l<i.length;l++){var o=i[l];0!==s[o]&&(a=!1)}a&&(r.splice(e--,1),t=n(n.s=i[0]))}return t}var a={},s={0:0},r=[];function n(e){if(a[e])return a[e].exports;var i=a[e]={i:e,l:!1,exports:{}};return t[e].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=t,n.c=a,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var a in t)n.d(i,a,function(e){return t[e]}.bind(null,a));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="";var l=window["webpackJsonp"]=window["webpackJsonp"]||[],o=l.push.bind(l);l.push=e,l=l.slice();for(var c=0;c<l.length;c++)e(l[c]);var d=o;r.push([0,1]),i()})({0:function(t,e,i){t.exports=i("2f39")},"0836":function(t,e,i){"use strict";i.r(e),i.d(e,"openConfig",(function(){return l}));var a=i("b6cb"),s=i("4360");const r=i("853f").default;let n;{const t=i("12fe").default;n=()=>t.loadFileSync()}async function l(){const t=await n();if(!t.filename||!t.content)throw new Error("Configuration loading failed");{const e=a["a"].load(t.content);r.info("Configuration file loaded: "+t.filename),r.info({gatt_config:t.content},!0),s["a"].dispatch("MainView/setData",e,{root:!0}),s["a"].dispatch("MainView/setDirty",!0,{root:!0})}}},1:function(t,e){},"10eb":function(t,e,i){"use strict";i("640b")},"12fe":function(t,e,i){"use strict";i.r(e);var a=i("9523"),s=i.n(a);class r{constructor(){s()(this,"_returnFunction",(()=>{}))}loadFile(t){this._returnFunction=t,this.fileInput||(this.fileInput=document.createElement("input"),this.fileInput.style.display="none",this.fileInput.type="file",this.fileInput.name="file",this.fileInput.accept=".btconf,.xml",this.fileInput.addEventListener("change",(()=>this._loadFile()),!1)),this.fileInput.click()}_loadFile(){var t,e,i=null===(t=this.fileInput)||void 0===t||null===(e=t.files)||void 0===e?void 0:e[0],a=new FileReader;a.onload=t=>{var e;const a=null===t||void 0===t||null===(e=t.target)||void 0===e?void 0:e.result;let s={filename:null===i||void 0===i?void 0:i.name,content:a};this._returnFunction(s)},i&&a.readAsText(i,"UTF-8"),this.fileInput&&(this.fileInput.value="")}loadFileSync(){return new Promise(((t,e)=>{try{this.loadFile((e=>{t(e)}))}catch(i){e()}}))}}const n=new r;e["default"]=n},"19e1":function(t,e,i){"use strict";i.d(e,"a",(function(){return a}));const a={profile:"profile",service:"service",characteristic:"characteristic",descriptor:"descriptor"}},2:function(t,e){},"27d0":function(t,e,i){},"2f39":function(t,e,i){"use strict";i.r(e);i("7d6e"),i("e54f"),i("9f29"),i("985d"),i("31cd");var a=i("2b0e"),s=i("1f91"),r=i("42d2"),n=i("b05d"),l=i("2a19");a["default"].use(n["a"],{config:{dark:"auto"},lang:s["a"],iconSet:r["a"],plugins:{Notify:l["a"]}});var o=function(){var t=this,e=t._self._c;return e("div",{attrs:{id:"q-app"}},[e("AppWindow")],1)},c=[],d=function(){var t=this,e=t._self._c;return e("q-layout",{attrs:{view:"hHh Lpr lff"},on:{scroll:t.onScroll}},[e("q-header",{staticClass:"header",attrs:{elevated:""}},[e("div",{staticClass:"row items-center no-wrap"},[e("div",{staticStyle:{width:"400px"}},[e("div",{staticClass:"text-h6 q-py-sm q-px-lg",attrs:{"data-cy":"main-title"}},[t._v("\n          Bluetooth GATT Configurator\n        ")])]),e("q-separator",{staticClass:"gt-sm bg-grey-6",attrs:{vertical:""}}),t.sigLayout?e("q-btn",t._b({staticClass:"q-mx-md q-pr-sm",attrs:{dense:"","no-caps":"",icon:"chevron_left",color:"primary",label:"Back","data-cy":"btn-sig-back"},on:{click:function(e){return t.setSigLayout(!1)}}},"q-btn",t.BUTTON_PROPS,!1)):t._e(),e("ActivePageTitle",{staticClass:"col"}),t.standalone?e("q-btn",t._b({staticClass:"q-mr-sm q-px-xs",attrs:{dense:"",label:"Open",icon:"mdi-folder-open-outline",color:"primary",size:"m"},on:{click:t.openConfig}},"q-btn",t.BUTTON_PROPS,!1)):t._e(),t.standalone||t.platformHandler.v6?e("q-btn",t._b({staticClass:"q-mr-sm q-px-xs",attrs:{disable:!t.dirty,dense:"",label:"Save",icon:"mdi-content-save-outline",color:"primary",size:"m"},on:{click:function(e){return t.platformHandler.onSaveEvent()}}},"q-btn",t.BUTTON_PROPS,!1)):t._e(),e("q-btn",t._b({staticClass:"q-mr-sm q-px-xs",attrs:{dense:"",label:"View Manual",color:"primary",size:"m","data-cy":"btn-manual"},on:{click:t.showManual}},"q-btn",t.BUTTON_PROPS,!1))],1)]),e("q-drawer",{attrs:{value:!0,mini:t.mini,"show-if-above":"",elevated:"",behavior:"desktop",width:400,"mini-width":32}},[e("div",{staticClass:"fit column"},[e("div",{staticClass:"row justify-between items-center q-py-xs"},[e("ButtonBar",{staticClass:"q-mini-drawer-hide q-pl-md"}),e("q-btn",{attrs:{dense:"",flat:"",color:"primary",icon:t.mini?"mdi-chevron-double-right":"mdi-chevron-double-left","data-cy":"btn-drawer-open-close"},on:{click:function(e){t.mini=!t.mini}}})],1),e("q-separator",{staticClass:"q-mini-drawer-hide",staticStyle:{height:"1px"}}),e("div",{staticClass:"col q-mini-drawer-hide"},[e("q-scroll-area",{staticClass:"fit"},[e("GattTree")],1)],1)],1)]),e("q-page-container",[e("q-page",{staticClass:"column",attrs:{padding:""}},[t.sigLayout?e("SigLayout"):t.layout==t.GATT_TYPES.profile?e("ProfileLayout"):t.layout==t.GATT_TYPES.service?e("ServiceLayout"):t.layout==t.GATT_TYPES.characteristic?e("CharacteristicLayout"):t.layout==t.GATT_TYPES.descriptor?e("DescriptorLayout"):t._e()],1)],1),e("UpgradeRuleDialog",{attrs:{"upgrade-results":t.gatt.upgradeResults}}),e("Manual",{ref:"manual-dialog"}),e("ProfileValidators",{attrs:{"gatt-item":t.gatt}})],1)},u=[],p=i("2f62"),h=function(){var t=this,e=t._self._c;return t.sigLayout?e("div",{staticClass:"column q-pl-md sig-title"},[e("div",{staticClass:"gt-sm sig-title-main"},[t._v(t._s(t.SIG_META.title))]),e("div",{staticClass:"gt-md sig-title-details"},[t._v(t._s(t.SIG_META.title_details))])]):e("div",{staticClass:"row text-grey-7 items-center q-px-md"},t._l(t.pathElements,(function(i,a){return e("div",{key:a,staticClass:"gt-sm row items-center"},[a>0?e("q-icon",{staticClass:"items-center q-pa-sm",attrs:{name:"chevron_right",size:"sm"}}):t._e(),e("q-item",{directives:[{name:"ripple",rawName:"v-ripple"}],staticClass:"items-center q-pa-sm",attrs:{clickable:""},on:{click:()=>{t.setSelectedItem(t.itemByVueId(i.vueId))}}},[e("q-icon",{staticClass:"q-pr-sm",attrs:{name:i.icon,color:i.color,size:"sm"}}),t._v("\n      "+t._s(i.name)+"\n    ")],1)],1)})),0)},m=[],g=i("19e1");const b={outlined:!0,dense:!0,hideBottomSpace:!0},f={flat:!0,round:!0,dense:!0,color:"primary"},v={outline:!0,"no-caps":!0},y={[g["a"].profile]:{name:"mdi-alpha-p-box-outline",color:"blue"},[g["a"].service]:{name:"mdi-alpha-s-box-outline",color:"green"},[g["a"].characteristic]:{name:"mdi-alpha-c-box-outline",color:"orange"},[g["a"].descriptor]:{name:"mdi-alpha-d-box-outline",color:"red"}},I={name:{label:"Custom GATT name"},gattCaching:{label:"GATT Caching"},genericAttributeService:{label:"Generic Attribute Service"},capabilityDeclarations:{label:"Capability declarations",column_labels:{name:"Name",enable:"Enabled"}}},_={name:{label:"Name",description:""},uuid:{label:"UUID",description:""},sigType:{label:"SIG type",description:""},declarationType:{label:"Declaration type",description:"",items:["primary","secondary"]},id:{label:"ID",description:""},advertise:{label:"Advertise service",description:""},includes:{label:"Service includes",description:""},capabilities:{label:"Service capabilities",description:""},characteristics:{label:"",description:""},info:{label:"Info",description:""}},T={constant:{label:"Constant"},length:{label:"Value length",unit:"byte"},max_length:{label:"Value maximum length",unit:"byte"},variableLength:{label:"Variable length"},initialValue:{label:"Initial value"},type:{label:"Value type",items:["user","hex","utf-8"]}},C={name:{label:"Name"},id:{label:"ID"},uuid:{label:"UUID"},sigType:{label:"SIG type"},properties:{label:"Permission settings",column_labels:{enable:"",name:"Name",authenticated:"Authenticated",bonded:"Bonded",encrypted:"Encrypted"},property_names:{read:"Read",write:"Write",reliableWrite:"Reliable write",writeNoResponse:"Write without response",notify:"Notify",indicate:"Indicate"}},capabilities:{label:"Characteristic capabilities",description:""},description:{label:"User description"},info:{label:"Info",description:""}},w={name:{label:"Name"},id:{label:"ID"},uuid:{label:"UUID"},sigType:{label:"SIG type"},discoverable:{label:"Discoverable"},properties:{label:"Permission settings",column_labels:{enable:"",name:"Name",authenticated:"Authenticated",bonded:"Bonded",encrypted:"Encrypted"},property_names:{read:"Read",write:"Write"}},info:{label:"Info",description:""}},S={title:"Add Standard Bluetooth Services, Characteristics, Descriptors",title_details:"Drag and drop services, characteristics and descriptors from the right side panel to Custom Bluetooth GATT profile",source_labels:{sig:"BT",homekit:"Apple Homekit",silabs:"Silicon Labs",mesh:"BT Mesh"}},x={sig_gatt_item_added:{single:{service:" Service",characteristic:" Characteristic",descriptor:" Descriptor",item:" GATT item",added:" has been added successfully."},multiple:{service:" Services",characteristic:" Characteristics",descriptor:" Descriptors",item:" GATT items",added:" have been added successfully."}},new_custom_item:{custom:"Custom ",added:" has been added."},duplicate_item:{duplicated:" has been duplicated."},import_item:{success:"Import has been successful.",fail:"Import has been failed."}};var A={data:()=>({ICONS:y,SIG_META:S}),computed:{...Object(p["mapGetters"])("GattConfiguratorApp",["gatt","selectedItem","itemByVueId"]),...Object(p["mapGetters"])("MainView",["sigLayout"]),pathElements(){let t=[],e=this.selectedItem;while(null!=e["parent"])e=e.parent,t.unshift({name:e.name,icon:y[e.gattType].name,color:y[e.gattType].color,vueId:e.vueId});return t}},methods:{...Object(p["mapActions"])("GattConfiguratorApp",["setSelectedItem"])}},O=A,q=i("2877"),k=i("0016"),D=i("66e5"),E=i("714f"),P=i("eebe"),M=i.n(P),V=Object(q["a"])(O,h,m,!1,null,null,null),G=V.exports;M()(V,"components",{QIcon:k["a"],QItem:D["a"]}),M()(V,"directives",{Ripple:E["a"]});var N=function(){var t=this,e=t._self._c;return e("div",{staticClass:"q-pt-sm"},[e("div",{staticClass:"row treeNode",class:{selectedNode:t.gatt==t.selectedItem}},[e("div",{staticClass:"col-auto"},[e("q-icon",{staticClass:"invisible",attrs:{name:"mdi-menu-down",size:"sm"}})],1),e("GattTreeItem",{staticClass:"col cursor-pointer",attrs:{value:t.gatt},on:{click:t.setSelectedItem}}),e("GattTreeItemValidation",{attrs:{value:t.gatt}})],1),e("DragTree",{staticClass:"q-pl-md",attrs:{value:t.gatt.services,"set-children":t.setKids,"node-key":"vueId","expand-default":!0,groups:t.groups,level:1,selected:t.selectedItem},on:{input:e=>t.setKids(t.gatt,e)},scopedSlots:t._u([{key:"label",fn:function({node:i}){return[e("div",{staticClass:"row"},[e("GattTreeItem",{staticClass:"cursor-pointer col",class:{"capability-disabled":t.capabilityDisabled(i)},attrs:{value:i},on:{click:t.setSelectedItem}}),e("GattTreeItemValidation",{attrs:{value:i}})],1)]}}])}),t.serviceContributions.length?e("div",{staticClass:"row items-center q-px-md"},[e("q-separator",{staticClass:"col"}),e("div",{staticClass:"text-caption q-px-sm text-grey"},[t._v("\n      Contributed items\n    ")]),e("q-separator",{staticClass:"col"}),e("q-icon",{staticClass:"q-ml-sm",attrs:{name:"mdi-information-outline",size:"sm",color:"grey-5"}},[e("q-tooltip",[t._v("\n        The following items are contributed by software components "),e("br"),t._v("\n        and cannot be modified in the Bluetooth GATT Configurator.\n      ")])],1)],1):t._e(),t.serviceContributions.length?e("DragTree",{staticClass:"q-pl-md",attrs:{value:t.serviceContributions,"node-key":"vueId","expand-default":!0,"drag-options":{disabled:!0},"read-only":"",level:1,selected:t.selectedItem},scopedSlots:t._u([{key:"label",fn:function({node:i}){return[e("GattTreeItem",{staticClass:"cursor-pointer text-italic",class:{"capability-disabled":t.capabilityDisabled(i)},attrs:{value:i},on:{click:t.setSelectedItem}})]}}],null,!1,1943040851)}):t._e()],1)},R=[],j=function(){var t=this,e=t._self._c;return e("vuedraggable",t._g(t._b({staticClass:"item-container",attrs:{tag:"div",value:t.value,group:t.groups[0]||t.level},on:{input:t.emitter,change:t.changed}},"vuedraggable",t.dragOptions,!1),t.dragEvents),t._l(t.value,(function(i){return e("div",{directives:[{name:"show",rawName:"v-show",value:!t.hide&&!i.hide,expression:"!hide && !node.hide"}],key:i[t.nodeKey]},[e("div",{staticClass:"row treeNode",class:{selectedNode:i==t.selected},style:t.leftMargin},[e("div",{staticClass:"col-auto"},[e("q-icon",{staticClass:"cursor-pointer",class:{invisible:!(i.children&&i.children.length)},attrs:{name:t.isExpanded(i)?"mdi-menu-down":"mdi-menu-right",size:"sm"},on:{click:function(e){t.setExpanded(i,!t.isExpanded(i))}}})],1),e("div",{staticClass:"col"},[t._t("label",(function(){return[t._v("error")]}),{node:i})],2)]),!i.children||t.readOnly&&!t.isExpanded(i)?t._e():e("DragTree",{staticClass:"q-pl-md",attrs:{value:i.children,"node-key":t.nodeKey,groups:t.groups.slice(1),level:t.level+1,"expand-default":t.expandDefault,hide:!t.isExpanded(i),"set-children":t.setChildren,"drag-options":t.dragOptions,"drag-events":t.dragEvents,selected:t.selected,"read-only":t.readOnly},on:{input:e=>t.setChildren(i,e)},scopedSlots:t._u([{key:"label",fn:function(e){return[t._t("label",(function(){return[t._v("he")]}),{node:e.node})]}}],null,!0)})],1)})),0)},F=[],U=i("b76a"),L=i.n(U),B=!1,Q=!1,$=!1;function z(t,e){null==t||t.gattType!=e||B||Y(t.vueId)}function Y(t){if($)return void($=!1);const e=document.getElementById(t);if(null==e)return;const i=e.offsetTop-10;B=!0,Q=!0,window.scrollTo(0,i),B=!1}function W({position:t},e,i){if(B)return null;if(Q)return Q=!1,null;var a;switch(e.gattType){case g["a"].profile:return;case g["a"].service:a=i.services;break;default:if(null==e["parent"])return null;a=e["parent"].children;break}let s;for(const l in a){const e=a[l],i=document.getElementById(e.vueId);if(null!==i){if(i.offsetTop>=t+300){void 0===s&&(s=e);break}s=e}}var r,n;return r=document.body.scrollHeight,n=window.scrollY+window.innerHeight,n>=r&&(s=a[a.length-1]),void 0!==s?($=!0,setTimeout((()=>{$=!1}),1e3),s):null}var H={name:"DragTree",components:{vuedraggable:L.a},props:{nodeKey:{type:String,default:"vueId"},groups:{type:Array,default:()=>[]},level:{default:0,type:Number},expandDefault:{type:Boolean,default:!1},value:{default:null,type:Array},dragEvents:{type:Object,default:()=>{}},dragOptions:{type:Object,default:()=>{}},hide:Boolean,setChildren:{type:Function,default(t,e){t.children=e}},readOnly:{type:Boolean,default:!1},expandedItems:Set,selected:{type:Object,default:null}},data:()=>({shift:15,expanded_local:null}),computed:{...Object(p["mapGetters"])("GattConfiguratorApp",["selectedItem"]),leftMargin(){const t=16*this.level;return`margin-left: -${t}px; padding-left: ${t}px;`},expanded:{get(){return this.expandedItems||this.expanded_local},set(t){this.expandedItems||(this.expanded_local=t),this.$emit("update:expandedItems",t)}}},watch:{},created(){if(this.expandedItems||(this.expanded_local=new Set),this.expandDefault){let t=new Set(this.expanded);for(let e of this.value)t.add(e[this.nodeKey]);this.expanded=t}},methods:{emitter(t){this.$emit("input",t)},isExpanded(t){return this.expanded.has(t[this.nodeKey])},setExpanded(t,e){let i=new Set(this.expanded);e?i.add(t[this.nodeKey]):i.delete(t[this.nodeKey]),this.expanded=i},changed(){Y(this.selectedItem.vueId)}}},K=H,X=Object(q["a"])(K,j,F,!1,null,null,null),J=X.exports;M()(X,"components",{QIcon:k["a"]});var Z=function(){var t=this,e=t._self._c;return e("div",{staticClass:"row q-gutter-xs items-center",attrs:{"data-cy":"gatt-tree-item-"+t.value.gattType},on:{click:t.onClick,mousedown:t.onMouseDown}},[e("q-icon",{attrs:{size:"sm",name:t.ICONS[t.value.gattType].name,color:t.ICONS[t.value.gattType].color}},[e("q-tooltip",[e("div",{staticClass:"text-capitalize"},[t._v(t._s(t.value.gattType))])])],1),e("div",{staticClass:"col",staticStyle:{"overflow-wrap":"break-word"}},[t._v(t._s(t.value.name))])],1)},tt=[],et={name:"GattTreeItem",props:{value:{type:Object,required:!0}},data(){return{ICONS:y}},methods:{onClick(){this.$emit("click",this.value)},onMouseDown(){this.$emit("mousedown",this.value)}}},it=et,at=i("05c0"),st=Object(q["a"])(it,Z,tt,!1,null,null,null),rt=st.exports;M()(st,"components",{QIcon:k["a"],QTooltip:at["a"]});var nt=function(){var t=this,e=t._self._c;return"validationMessages"in t.value&&Object.keys(t.value["validationMessages"]).length>0?e("div",{staticClass:"q-mr-sm q-pr-xs"},[e("ValidationMarker",{attrs:{type:t.cumulatedValMsgs.type,messages:t.cumulatedValMsgs.messages}})],1):t._e()},lt=[],ot=(i("14d9"),i("c78e")),ct={name:"GattTreeItem",components:{ValidationMarker:ot["ValidationMarker"]},props:{value:{type:Object,required:!0}},computed:{cumulatedValMsgs(){let t={type:null,messages:[]},e=this.value.validationMessages;for(let i of["error","warning"])for(let a in e)if(i in e[a]&&(null==t.type||t.type==i))for(let s of e[a][i])t.type=i,-1==t.messages.indexOf(s)&&t.messages.push(s);return t}},methods:{}},dt=ct,ut=Object(q["a"])(dt,nt,lt,!1,null,null,null),pt=ut.exports,ht=i("853f"),mt=i("a3a1"),gt=i("af17"),bt=(i("dffb"),i("193c"));function ft({gattType:t,gattItem:e}){if(null==t)return;if(null==e)return;let i={};switch(Object.keys(g["a"]).forEach((t=>i[t]=0)),t){case g["a"].profile:i.profile=1,i.service=e.services.length,e.services.forEach((t=>{i.characteristic+=t.characteristics.length,t.characteristics.forEach((t=>i.descriptor+=t.descriptors.length))}));break;case g["a"].service:i.service=1,i.characteristic=e.characteristics.length,e.characteristics.forEach((t=>i.descriptor+=t.descriptors.length));break;case g["a"].characteristic:i.characteristic=1,i.descriptor=e.descriptors.length;break;case g["a"].descriptor:i.descriptor=1;break}return i}function vt(t){if(null==t)return null;let e={title:"",svgIcon:bt["a"],details:""};if(t.service+t.characteristic+t.descriptor==0)return null;if(t.service+t.characteristic+t.descriptor==1){e["title"]=1+x.sig_gatt_item_added.single.item+x.sig_gatt_item_added.single.added;for(const i in t)if(t[i]>0){e["details"]=1+x.sig_gatt_item_added.single[i]+".";break}}else e["title"]=t.service+t.characteristic+t.descriptor+x.sig_gatt_item_added.multiple.item+x.sig_gatt_item_added.multiple.added,t.service>0&&(1==t.service?e["details"]=1+x.sig_gatt_item_added.single.service:e["details"]=t.service+x.sig_gatt_item_added.multiple.service),t.characteristic>0&&(""!=e["details"]&&(e["details"]+=", "),1==t.characteristic?e["details"]+=1+x.sig_gatt_item_added.single.characteristic:e["details"]+=t.characteristic+x.sig_gatt_item_added.multiple.characteristic),t.descriptor>0&&(""!=e["details"]&&(e["details"]+=", "),1==t.descriptor?e["details"]+=1+x.sig_gatt_item_added.single.descriptor:e["details"]+=t.descriptor+x.sig_gatt_item_added.multiple.descriptor),e["details"]+=".";return e}function yt(t){let e={title:x.new_custom_item.custom+t+x.new_custom_item.added,svgIcon:bt["a"]};return e}function It(t){let e={title:"'"+t+"'"+x.duplicate_item.duplicated,svgIcon:bt["a"]};return e}const _t={title:x.import_item.success,svgIcon:bt["a"]},Tt={title:x.import_item.fail,svgIcon:bt["b"],iconColorType:"negative"};var Ct=i("7d5e"),wt={namespaced:!0,components:{DragTree:J,GattTreeItem:rt,GattTreeItemValidation:pt},data:()=>({ICONS:y,GATT_TYPES:g["a"],groups:[g["a"].service,g["a"].characteristic,g["a"].descriptor]}),computed:{...Object(p["mapGetters"])("GattConfiguratorApp",["gatt","mergedGatt","serviceContributions","selectedItem"]),...Object(p["mapGetters"])("Sig",["draggedItemPromise"])},methods:{setSigDraggedItem(t,e,i,a){if(null==a||""==a||!("gattType"in a)||!("gattItem"in a))throw"Faulty sig gatt item!";{const{gattType:s,gattItem:r}=a;if(Object(Ct["b"])(s,r),ht["default"].debug("SIG Gatt item:"),ht["default"].debug(a,!0),s==g["a"].profile){const a=new mt["g"](r);a.services.forEach((e=>e.parent=t)),e.splice(i,1,...a.services)}else s==g["a"].service?e[i]=new mt["h"](r,t):s==g["a"].characteristic?(r.id=Object(Ct["d"])(t.name)+"_"+r.id+"_"+Object(Ct["a"])(r.name,e),e[i]=new mt["e"](r,t)):s==g["a"].descriptor?(r.id=Object(Ct["d"])(t.name)+"_"+r.id+"_"+Object(Ct["a"])(r.name,e),e[i]=new mt["f"](r,t)):ht["default"].error("Wrong gatt type: "+s);const n=vt(ft(a));null!=(null===n||void 0===n?void 0:n.title)&&this.$q.notify(gt["Notifications"].qNotificationHtmlObj(gt["Notifications"].notificationHtmlMsg(n))),this.setChildren({object:t,children:e})}},setKids(t,e){const i=e.findIndex((t=>"gattType"in t&&"types"in t));if(-1==i){for(const i of e)i.parent=t;this.setChildren({object:t,children:e})}else this.draggedItemPromise.then((a=>{this.setSigDraggedItem(t,e,i,a)})).catch((t=>{ht["default"].error(t)}))},...Object(p["mapActions"])("GattConfiguratorApp",["setChildren","setSelectedItem"]),capabilityDisabled(t){if(t.gattType==g["a"].profile)return!1;if(0==this.mergedGatt.capabilityDeclarations.length)return!1;var e,i={},a=!1,s=!1,r=!1;for(e of this.mergedGatt.capabilityDeclarations)i[e.name]=e.enable;if(null!=t["parent"]&&null!=t["parent"]["parent"]&&t["parent"]["parent"].gattType==g["a"].service&&null!=t["parent"]["parent"].capabilities&&t["parent"]["parent"].capabilities.length>0)for(e of(r=!0,t["parent"]["parent"].capabilities))null!=i[e]&&i[e]&&(r=!1);if(null!=t["parent"]&&(t["parent"].gattType==g["a"].characteristic||t["parent"].gattType==g["a"].service)&&null!=t["parent"].capabilities&&t["parent"].capabilities.length>0)for(e of(s=!0,t["parent"].capabilities))null!=i[e]&&i[e]&&(s=!1);if(null!=t.capabilities&&t.capabilities.length>0)for(e of(a=!0,t.capabilities))null!=i[e]&&i[e]&&(a=!1);return r||s||a}}},St=wt,xt=(i("d538"),i("eb85")),At=Object(q["a"])(St,N,R,!1,null,null,null),Ot=At.exports;M()(At,"components",{QIcon:k["a"],QSeparator:xt["a"],QTooltip:at["a"]});var qt=function(){var t=this,e=t._self._c;return e("div",[e("div",{staticClass:"row q-gutter-sm"},[e("div",{attrs:{"data-cy":"btn-button-bar-custom-add"}},[e("q-btn",t._b({attrs:{icon:"mdi-file-plus-outline",disable:!t.newChildActive},on:{click:function(e){t.showTooltip=!1}}},"q-btn",t.BUTTON_BAR_PROPS,!1),[e("q-menu",[e("q-list",{attrs:{dense:""}},[e("q-item",{attrs:{dense:"",clickable:"","data-cy":"btn-button-bar-custom-add-service"},on:{click:function(e){return t.addItem(t.GATT_TYPES.service)}}},[e("q-item-section",[t._v("New Service")])],1),e("q-item",{attrs:{dense:"",clickable:"","data-cy":"btn-button-bar-custom-add-characteristic"},on:{click:function(e){return t.addItem(t.GATT_TYPES.characteristic)}}},[e("q-item-section",[t._v("New Characteristic ")])],1),e("q-item",{attrs:{dense:"",clickable:"","data-cy":"btn-button-bar-custom-add-descriptor"},on:{click:function(e){return t.addItem(t.GATT_TYPES.descriptor)}}},[e("q-item-section",[t._v("New Descriptor ")])],1)],1)],1)],1),e("q-tooltip",{model:{value:t.showTooltip,callback:function(e){t.showTooltip=e},expression:"showTooltip"}},[t._v("Add new item")])],1),e("div",{attrs:{"data-cy":"btn-button-bar-copy"}},[e("q-btn",t._b({attrs:{icon:"mdi-content-copy",disable:!t.duplicateActive},on:{click:e=>{t.duplicateSelectedItem(),t.duplicateItemNotify()}}},"q-btn",t.BUTTON_BAR_PROPS,!1)),e("q-tooltip",[t._v("Duplicate selected item")])],1),e("div",{attrs:{"data-cy":"btn-button-bar-move-up"}},[e("q-btn",t._b({attrs:{icon:"mdi-arrow-up",disable:!t.moveUpActive},on:{click:t.moveUpSelectedItem}},"q-btn",t.BUTTON_BAR_PROPS,!1)),e("q-tooltip",[t._v(" Move up selected item ")])],1),e("div",{attrs:{"data-cy":"btn-button-bar-move-down"}},[e("q-btn",t._b({attrs:{icon:"mdi-arrow-down",disable:!t.moveDownActive},on:{click:t.moveDownSelectedItem}},"q-btn",t.BUTTON_BAR_PROPS,!1)),e("q-tooltip",[t._v("Move down selected item")])],1),e("q-btn",t._b({attrs:{icon:"mdi-application-import","data-cy":"btn-button-bar-import"},on:{click:t.importConfig}},"q-btn",t.BUTTON_BAR_PROPS,!1),[e("q-tooltip",[t._v("Import")])],1),e("q-btn",t._b({class:{"button-text":t.sigLayout},attrs:{outline:!t.sigLayout,icon:"mdi-plus-thick","data-cy":"btn-button-bar-sig"},on:{click:function(e){return t.setSigLayout(!t.sigLayout)}}},"q-btn",t.BUTTON_BAR_PROPS,!1),[e("q-tooltip",[t._v("Toggle - Add Standard Bluetooth GATT items - view")])],1),e("div",{attrs:{"data-cy":"btn-button-bar-remove"}},[e("q-btn",t._b({attrs:{color:"negative",icon:"mdi-close-thick",disable:!t.removeActive},on:{click:function(e){t.deleteConfirm=!0}}},"q-btn",t.BUTTON_BAR_PROPS,!1)),e("q-tooltip",[t._v("Delete selected item (Ctrl + Del)")])],1)],1),e("q-dialog",{model:{value:t.deleteConfirm,callback:function(e){t.deleteConfirm=e},expression:"deleteConfirm"}},[e("q-card",[e("q-card-section",{staticClass:"row items-center q-gutter-md"},[e("q-icon",{attrs:{name:"mdi-alert-circle ",color:"negative",size:"md"}}),e("div",[t._v("\n          Are you sure you want to permanently delete the selected item?\n        ")])],1),e("q-card-actions",{attrs:{align:"right"}},[e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{"data-cy":"btn-button-bar-remove-pop-up-delete",outline:"",label:"Delete",color:"negative",autofocus:""},on:{click:t.removeSelectedItem}}),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{"data-cy":"btn-button-bar-remove-pop-up-cancel",flat:"",label:"Cancel",color:"primary"}})],1)],1)],1),e("KeyboardShortcut",{attrs:{ctrl:"",shortcut:"Delete",disable:!t.removeActive},on:{trigger:function(e){t.deleteConfirm=!0}}})],1)},kt=[],Dt=i("b923"),Et=i.n(Dt),Pt=i("0836"),Mt={namespaced:!0,components:{KeyboardShortcut:Et.a},data:()=>({showTooltip:!1,BUTTON_BAR_PROPS:{color:"primary",outline:!0,dense:!0,size:"sm"},ICONS:y,GATT_TYPES:g["a"],deleteConfirm:!1}),computed:{...Object(p["mapGetters"])("GattConfiguratorApp",["gatt","serviceContributions","capabilityContributions","selectedItem","itemByVueId"]),...Object(p["mapGetters"])("MainView",["sigLayout"]),newChildActive(){return!this.selectedItem.sourceFile},duplicateActive(){return Object.values(g["a"]).includes(this.selectedItem.gattType)&&this.selectedItem.gattType!=g["a"].profile&&!this.selectedItem.sourceFile||this.selectedItem.sourceFile&&this.selectedItem.gattType==g["a"].service},removeActive(){return Object.values(g["a"]).includes(this.selectedItem.gattType)&&this.selectedItem.gattType!=g["a"].profile&&!this.selectedItem.sourceFile},moveUpActive(){if(this.selectedItem.parent&&!this.selectedItem.sourceFile){let t=this.selectedItem.parent.children;return t.indexOf(this.selectedItem)>0}return!1},moveDownActive(){if(this.selectedItem.parent&&!this.selectedItem.sourceFile){let t=this.selectedItem.parent.children;return t.indexOf(this.selectedItem)<t.length-1}return!1}},watch:{deleteConfirm:function(){this.setDialogOn(this.deleteConfirm)}},methods:{...Object(p["mapActions"])("GattConfiguratorApp",["setSelectedItem","removeSelectedItem","duplicateSelectedItem","moveUpSelectedItem","moveDownSelectedItem","addGattItem"]),...Object(p["mapActions"])("MainView",["setData","setDirty","setDialogOn","setSigLayout"]),addItem(t){let e=null;if(t==g["a"].service)e=Object(mt["d"])();else if(t==g["a"].characteristic)e=Object(mt["a"])();else{if(t!=g["a"].descriptor)return;e=Object(mt["b"])()}this.addGattItem({gattItem:e,gattType:t}),this.$q.notify(gt["Notifications"].qNotificationHtmlObj(gt["Notifications"].notificationHtmlMsg(yt(t))))},duplicateItemNotify(){this.$q.notify(gt["Notifications"].qNotificationHtmlObj(gt["Notifications"].notificationHtmlMsg(It(this.selectedItem.name))))},async importConfig(){try{await Object(Pt["openConfig"])(),this.importNotifySuccess()}catch(Xe){if(20==Xe.code)return;this.importNotifyFail()}},importNotifySuccess(){this.$q.notify(gt["Notifications"].qNotificationHtmlObj(gt["Notifications"].notificationHtmlMsg(_t)))},importNotifyFail(){this.$q.notify(gt["Notifications"].qNotificationHtmlObj(gt["Notifications"].notificationHtmlMsg(Tt),"negative"))}}},Vt=Mt,Gt=i("9c40"),Nt=i("4e73"),Rt=i("1c1c"),jt=i("4074"),Ft=i("24e8"),Ut=i("f09f"),Lt=i("a370"),Bt=i("4b7e"),Qt=i("7f67"),$t=Object(q["a"])(Vt,qt,kt,!1,null,null,null),zt=$t.exports;M()($t,"components",{QBtn:Gt["a"],QMenu:Nt["a"],QList:Rt["a"],QItem:D["a"],QItemSection:jt["a"],QTooltip:at["a"],QDialog:Ft["a"],QCard:Ut["a"],QCardSection:Lt["a"],QIcon:k["a"],QCardActions:Bt["a"]}),M()($t,"directives",{ClosePopup:Qt["a"]});var Yt=function(){var t=this,e=t._self._c;return e("div",{staticClass:"column q-gutter-md"},[e("q-card",{staticClass:"card-selected",attrs:{"data-cy":"card-"+t.GATT_TYPES.profile}},[e("div",{staticClass:"row items-center q-gutter-sm q-pt-md"},[e("div",{staticClass:"col-auto"},[e("div",{staticClass:"card-title-block-bar"})]),e("q-icon",{attrs:{name:t.ICONS[t.GATT_TYPES.profile].name,color:t.ICONS[t.GATT_TYPES.profile].color,size:"lg"}}),e("div",{staticClass:"card-title-div"},[e("div",{staticClass:"card-title"},[t._v("\n          "+t._s(t.name)+"\n        ")]),e("em",{staticClass:"text-capitalize card-subtitle"},[t._v("\n          "+t._s(t.GATT_TYPES.profile)+"\n        ")])]),e("div",{staticClass:"col row justify-end items-center q-px-md"},[e("q-btn",{attrs:{icon:"mdi-help",color:"blue",round:"",dense:"",unelevated:"",size:"sm","data-cy":"card-help"},on:{click:t.showManualAtProfile}})],1)],1),e("div",{staticClass:"row q-pa-md q-col-gutter-md"},[e("div",{staticClass:"column q-col-gutter-md col-md-8 col-sm-12",staticStyle:{"max-width":"350px"}},[e("q-input",t._b({attrs:{label:t.PROFILE_META.name.label,"data-cy":"input-name"},model:{value:t.name,callback:function(e){t.name=e},expression:"name"}},"q-input",t.INPUT_PROPS,!1)),e("VToggle",t._b({attrs:{label:t.PROFILE_META.genericAttributeService.label,"validation-messages":t.genericAttributeServiceValMsgs,"data-cy":"input-generic-attribute-service"},model:{value:t.genericAttributeService,callback:function(e){t.genericAttributeService=e},expression:"genericAttributeService"}},"VToggle",t.INPUT_PROPS,!1)),e("VToggle",t._b({attrs:{label:t.PROFILE_META.gattCaching.label,"validation-messages":t.gattCachingValMsgs,"data-cy":"input-gatt-caching"},model:{value:t.gattCaching,callback:function(e){t.gattCaching=e},expression:"gattCaching"}},"VToggle",t.INPUT_PROPS,!1))],1),e("div",{staticClass:"self-start"},[e("CapabilityDeclarations")],1),e("div",{staticClass:"col-12"},[e("div",{staticClass:"row q-col-gutter-sm"},t._l(t.mergedGatt.services,(function(t){return e("div",{key:t.vueId,staticClass:"col-grow"},[e("SubGattItem",{attrs:{value:t}})],1)})),0)])])])],1)},Wt=[],Ht=function(){var t=this,e=t._self._c;return e("q-list",{staticClass:"rounded-borders sub-item-background"},[e("q-item",{staticClass:"rounded-borders q-pr-sm",attrs:{clickable:""},on:{click:function(e){return t.setSelectedItem(t.value)}}},[e("q-item-section",{attrs:{avatar:""}},[e("q-icon",{attrs:{name:t.ICONS[t.value.gattType].name,color:t.ICONS[t.value.gattType].color,size:"lg"}})],1),e("q-item-section",[e("q-item-label",[e("strong",[t._v(t._s(t.value.name))]),t.value.id?e("em",[t._v(t._s(" - "+t.value.id))]):t._e()]),e("q-item-label",{staticClass:"q-gutter-xs",attrs:{caption:""}},[""!=t.value.uuid?e("q-badge",{attrs:{color:"accent"}},[t._v("\n          "+t._s(t.value.uuid)+"\n        ")]):t._e(),""!=t.value.sigType?e("q-badge",{staticClass:"button-text",attrs:{color:"primary"}},[t._v("\n          "+t._s(t.value.sigType)+"\n        ")]):t._e(),null==t.value.sourceFile?e("q-badge",{staticClass:"text-primary",attrs:{color:"transparent"}},[e("q-icon",{staticClass:"q-pr-xs",attrs:{name:"mdi-pencil"}}),t._v("\n          Edit\n        ")],1):t._e()],1)],1)],1),t.value.children&&0!=t.value.children.length?e("q-item",{staticClass:"sub-item-background"},[e("div",{staticClass:"row"},t._l(t.value.children,(function(i){return e("q-chip",{key:i.vueId,staticClass:"chip-background",attrs:{dense:"",clickable:""},on:{click:function(e){return t.setSelectedItem(i)}}},[e("q-avatar",{attrs:{color:t.ICONS[i.gattType].color,"text-color":"white"}},[t._v("\n          "+t._s(i.gattType[0].toUpperCase())+"\n        ")]),t._v("\n        "+t._s(i.name)+"\n        "),null==t.value.sourceFile?e("q-badge",{staticClass:"text-primary",attrs:{color:"transparent"}},[e("q-icon",{staticClass:"q-px-sm",attrs:{name:"mdi-pencil"}}),t._v("\n          Edit\n        ")],1):t._e()],1)})),1)]):t._e()],1)},Kt=[],Xt={namespaced:!0,props:{value:{type:Object,required:!0}},data:()=>({ICONS:y}),methods:{...Object(p["mapActions"])("GattConfiguratorApp",["setSelectedItem"])}},Jt=Xt,Zt=(i("3b2f"),i("0170")),te=i("58a8"),ee=i("b047"),ie=i("cb32"),ae=Object(q["a"])(Jt,Ht,Kt,!1,null,null,null),se=ae.exports;M()(ae,"components",{QList:Rt["a"],QItem:D["a"],QItemSection:jt["a"],QIcon:k["a"],QItemLabel:Zt["a"],QBadge:te["a"],QChip:ee["a"],QAvatar:ie["a"]});var re=function(){var t=this,e=t._self._c;return e("q-markup-table",{class:{"alert-error-border":"error"==t.validationMessages.type,"alert-warning-border":"warning"==t.validationMessages.type},attrs:{flat:"",bordered:"",dense:""}},[e("thead",[e("tr",[e("th",{staticClass:"text-left text-h6",attrs:{colspan:"1",scope:"col"}},[t._v("\n        "+t._s(t.PROFILE_META.capabilityDeclarations.label)+"\n      ")]),e("th",{staticClass:"text-right",attrs:{colspan:"2",scope:"colgroup"}},[e("q-btn",t._b({attrs:{round:!1,"no-caps":"",color:"primary","data-cy":"add-capability-declaration"},on:{click:t.addCapabilityDeclaration}},"q-btn",t.ICON_BUTTON_PROPS,!1),[e("q-icon",{attrs:{name:"mdi-plus-thick",size:"xs"}}),t._v("Add\n        ")],1)],1),e("th",{attrs:{scope:"col"}},[e("ValidationMarker",t._b({attrs:{placeholder:""}},"ValidationMarker",t.validationMessages,!1))],1)]),e("tr",{directives:[{name:"show",rawName:"v-show",value:!!t.mergedGatt.capabilityDeclarations.length,expression:"!!mergedGatt.capabilityDeclarations.length"}]},[e("th",{attrs:{scope:"col"}},[t._v("\n        "+t._s(t.PROFILE_META.capabilityDeclarations.column_labels.name)+"\n      ")]),e("th",{attrs:{scope:"col"}},[t._v("\n        "+t._s(t.PROFILE_META.capabilityDeclarations.column_labels.enable)+"\n      ")]),e("th",{attrs:{scope:"col"}}),e("th",{attrs:{scope:"col"}})])]),e("tbody",t._l(t.mergedGatt.capabilityDeclarations,(function(i,a){return e("CapabilityDeclarationRow",{key:a,attrs:{"capability-declaration":i,"data-cy":"capability-declaration"},on:{delete:function(e){return t.handleCapabilityDeclarationDelete(a)}}})})),1)])},ne=[],le=i("4360");function oe(t){return Object.values(t).some((t=>!!t))}function ce(t){return(new TextEncoder).encode(t).length}function de(t){return`${t.substr(0,8)}-${t.substr(8,4)}-${t.substr(12,4)}-${t.substr(16,4)}-${t.substr(20,12)}`}function ue(){return/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform)}function pe(){this.isValueLengthNotNull=new si(He,"Calculated length, Initial Value's length will be used.",["value_$_length"],Fe),this.isValueHexLengthValid=new si(Ke,"Value length is greater than the length (*2) property. Value will be truncated.",["value_$_initialValue","value_$_length"],Ue),this.isValidInitialValueHexForm=new si(Xe,"Value is not a proper hex! Digit count has to be even.",["value_$_initialValue"],je),this.isValidValueLength=new si(Xe,"Value is not a positive integer!",["value_$_length"],Le),this.isNoneUserTypeValueMaxLengthValid=new si(Xe,"Value length for non-USER types should be between 1-255",["value_$_length"],Qe),this.isNoneUserTypeValueLengthValid=new si(Xe,"Initial value's length for non-USER types should be between 1-255",["value_$_initialValue"],$e),this.isValueUTF8LengthValid=new si(Ke,"Initial Value length is greater than the length property. Value will be truncated.",["value_$_initialValue","value_$_length"],ze),this.isValueHEXMinLengthValid=new si(He,"Initial Value length is less than the length property. Padding data will be added.",["value_$_initialValue","value_$_length"],Ye),this.isValueUTF8MinLengthValid=new si(He,"Initial Value length is less than the length property. Padding data will be added.",["value_$_initialValue","value_$_length"],We),this.hasInitialValueWhenLengthNull=new si(Xe,"Initial Value is compulsory when the length attribute is 0.",["value_$_initialValue"],Be),this.isValidUUID=_e()}function he(){this.isCapabilityDeclarationNameUnique=new si(Xe,"Capability Declaration names are not unique!",["capabilityDeclarations"],xe),this.isCapabilityDeclarationNameValid=new si(Xe,"Only alphanumeric and '_' characters are allowed.",["capabilityDeclarations"],Ae),this.isNotExceededMaxNoOfCapabilityDeclarations=new si(Xe,"The maximum number of Capabilities is 16.",["capabilityDeclarations"],Oe),this.isGattCachingValueValid=new si(Ke,"GATT Caching only allowed, when Generic Attribute Service is enabled. This setting will be ignored.",["gattCaching","genericAttributeService"],qe)}function me(){this.isIdUnique=new si(Xe,"Service ID is not unique !",["id"],Se),this.isIdValidCName=Te(),this.isValidUUID=_e()}function ge(){Object.assign(this,new pe),this.hasPropertyEnabled=new si(Xe,"At least one property should be configured.",["properties"],Pe),this.propertyForPersmissionIsSelected=new si(Ke,"Property was not selected for permissions",["properties"],Ge),this.isConstantAndWriteEnabledCharacteristic=new si(Xe,"Write properties are not applicable for constant values.",["properties"],ke),this.isContainsDuplicatedCharacterisitcsDescription=new si(Ke,"Characteristic user description cannot be added while the user description checkbox selected",["description"],De),this.isIdUnique=new si(Xe,"Characteristic ID is not unique !",["id"],Se),this.isIdValidCName=Te(),this.explicitCCCD=new si(Ke,"Note: since there is a CCCD explicitly defined for this characteristic, the notify and indicate properties set in the configurator will be ignored. Remove the explicit definition to get an autogenerated CCCD or remove the notify/indicate properties, and define them inside the explicitly defined CCCD.",["notify"],Ee),this.isValidUUID=_e()}function be(){Object.assign(this,new pe),this.hasPropertyEnabled=new si(Xe,"At least one property should be configured.",["properties"],Pe),this.propertyForPersmissionIsSelected=new si(Ke,"Property was not selected for permissions",["properties"],Me),this.isConstantAndWriteEnabledDescriptor=new si(Xe,"Write properties are not applicable for constant values.",["properties"],Re),this.descriptorDuplicationValidator=new si(Ke,"The descriptor is duplicated, which is not allowed by Bluetooth Core specification",["uuid"],Ve),this.isIdUnique=new si(Xe,"Descriptor ID is not unique",["id"],Se),this.isIdValidCName=Te()}function fe(t){var e=/^[0-9a-f]{4}$/i,i=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,a=/^[0-9a-f]{32}$/i;return e.test(t)||i.test(t)||a.test(t)}function ve(t){var e=/^([0-9a-f]{2})+$/gim;return e.test(t)}function ye(t){var e=/^(0|[1-9]\d*)$/gim;return e.test(t)}function Ie(t){var e=/^([_a-z][_0-9a-z]*)$/gim;return e.test(t)}const _e=()=>new si(Xe,"Invalid UUID value. It should be a 16-bit or 128-bit UUID number.",["uuid"],we),Te=()=>new si(Xe,"ID is not a valid C name containing alphanumerics and underscores only.",["id"],Ce);function Ce(){const t=Ie(this.gattItem.id);return t}function we(){return void 0===this.gattItem||void 0===this.gattItem.uuid||this.gattItem.uuid.length>0&&fe(this.gattItem.uuid)}function Se(){let t=this.$store.getters["GattConfiguratorApp/duplicatedIds"];return!t.has(this.gattItem.id)}function xe(){let t=!0;const e=this.$store.getters["GattConfiguratorApp/capabilityDeclarationNames"];if(e)for(let i of e)if(e.filter((t=>t==i)).length>1){t=!1;break}return t}function Ae(){let t=!0;const e=this.$store.getters["GattConfiguratorApp/capabilityDeclarationNames"];if(e)for(let i of e)if(!Ie(i)){t=!1;break}return t}function Oe(){let t=!0;const e=this.$store.getters["GattConfiguratorApp/capabilityDeclarationNames"];return e&&e.length>16&&(t=!1),t}function qe(){let t=!0;return this.gattItem.gattCaching&&!this.gattItem.genericAttributeService&&(t=!1),t}function ke(){let t=!0;return this.gattItem.value.constant&&oe(this.gattItem.permissions.write.properties)&&(t=!1),t}function De(){if(null!=this.gattItem.description&&this.gattItem.descriptors.length>0){const t="2901";for(const e of this.gattItem.descriptors)if(e.uuid===t)return!1}return!0}function Ee(){var t,e,i,a,s,r;if((null!==(t=this.gattItem.permissions)&&void 0!==t&&null!==(e=t.notify)&&void 0!==e&&null!==(i=e.properties)&&void 0!==i&&i.notify||null!==(a=this.gattItem.permissions)&&void 0!==a&&null!==(s=a.notify)&&void 0!==s&&null!==(r=s.properties)&&void 0!==r&&r.indicate)&&this.gattItem.descriptors.length>0){const t="2902";for(const e of this.gattItem.descriptors)if(e.uuid===t)return!1}return!0}function Pe(){const t=["read","write","notify"];let e=!1;for(let i in this.gattItem.permissions)if(t.includes(i)){const t=this.gattItem.permissions[i];if(oe(t.properties)){e=!0;break}}return e}function Me(){return Ne(this.gattItem,["read","write"])}function Ve(){const t="2901";let e=this.gattItem.parent.descriptors,i=new Set;null!=this.gattItem.parent.description&&i.add(t);for(const a of e){if(i.has(a.uuid))return!1;i.add(a.uuid)}return!0}function Ge(){return Ne(this.gattItem,["read","write","notify"])}function Ne(t,e){let i=!0;for(let a=0;a<e.length;a++){const s=e[a],r=t.permissions[s],{authenticated:n,bonded:l,encrypted:o,properties:c}=r;if((n||l||o)&&!oe(c)){i=!1;break}}return i}function Re(){let t=!0;return this.gattItem.value.constant&&this.gattItem.permissions.write.properties.write&&(t=!1),t}function je(){let t=!0;var e;"value"in this.gattItem&&"type"in this.gattItem["value"]&&"length"in this.gattItem["value"]&&"initialValue"in this.gattItem["value"]&&"hex"===this.gattItem["value"]["type"]&&(t=this.gattItem.value.length>0&&0==(null===(e=this.gattItem.value.initialValue)||void 0===e?void 0:e.length)||ve(this.gattItem.value.initialValue));return t}function Fe(){return null!=this.gattItem.value.length}function Ue(){let t=!0;if("hex"===this.gattItem["value"]["type"]&&ye(this.gattItem.value.length)){var e;let s=this.gattItem.value.initialValue;var i,a;if(null!==(e=s)&&void 0!==e&&e.startsWith("0x"))s=null===(i=s)||void 0===i?void 0:i.slice(2);if(this.gattItem.value.length>0)t=(null===(a=s)||void 0===a?void 0:a.length)<=2*this.gattItem.value.length}return t}function Le(){let t=!0;return null!=this.gattItem.value.length&&(t=ye(this.gattItem.value.length)&&this.gattItem.value.length>0),t}function Be(){var t;let e=!0;return"user"==this.gattItem.value.type||0!=this.gattItem.value.length&&null!=this.gattItem.value.length||0!=(null===(t=this.gattItem.value.initialValue)||void 0===t?void 0:t.length)||(e=!1),e}function Qe(){let t=!0;return"user"!=this.gattItem.value.type&&this.gattItem.value.length>255&&(t=!1),t}function $e(){var t;let e=!0;if(null!==(t=this.gattItem.value.initialValue)&&void 0!==t&&t.length){if("utf-8"==this.gattItem.value.type)return ce(this.gattItem.value.initialValue)<256;var i;if("hex"==this.gattItem.value.type)return(null===(i=this.gattItem.value.initialValue)||void 0===i?void 0:i.length)/2<256}return e}function ze(){var t;let e=!0;return"utf-8"==this.gattItem.value.type&&this.gattItem.value.length>0&&(null===(t=this.gattItem.value.initialValue)||void 0===t?void 0:t.length)>this.gattItem.value.length&&(e=!1),e}function Ye(){var t;let e=!0;return"hex"==this.gattItem.value.type&&(null===(t=this.gattItem.value.initialValue)||void 0===t?void 0:t.length)<2*this.gattItem.value.length&&(e=!1),e}function We(){var t;let e=!0;return"utf-8"==this.gattItem.value.type&&(null===(t=this.gattItem.value.initialValue)||void 0===t?void 0:t.length)<this.gattItem.value.length&&(e=!1),e}const He="info",Ke="warning",Xe="error",Je=[Xe,Ke,He],Ze="type",ti="validate",ei="message",ii="messageKey",ai="validationMessages";function si(t,e,i,a){this[Ze]=t,this[ei]=e,this[ii]=i,this[ti]=a}const ri={profile:new he,service:new me,characteristic:new ge,descriptor:new be};function ni(t,e,i){let a=ri[t.gattType];if(e in a){let s=a[e],r=JSON.parse(JSON.stringify(t[ai]));if(i)for(let t of s[ii])t in r&&(s[Ze]in r[t]&&Array.isArray(r[t][s[Ze]])&&r[t][s[Ze]].length>0&&(r[t][s[Ze]]=r[t][s[Ze]].filter((t=>t!=s[ei])),0==r[t][s[Ze]].length&&delete r[t][s[Ze]]),0==Object.keys(r[t]).length&&delete r[t]);else for(let t of s[ii])t in r||(r[t]={}),s[Ze]in r[t]||(r[t][s[Ze]]=[]),-1==r[t][s[Ze]].indexOf(s[ei])&&r[t][s[Ze]].push(s[ei]);le["a"].dispatch("GattConfiguratorApp/setValueNoDirty",{object:t,key:ai,value:r})}}function li(t){let e={type:null,messages:[]},i=t;if(void 0!=i&&Object.keys(i).length>0)for(let a of Je)if(a in i){for(let t of i[a])e.type=a,e.messages.push(t);if(e.messages.length>0)return e}return e}function oi(t){const e={};for(let i in ri[t])e[i]={get:ri[t][i].validate};return e}var ci=function(){var t=this,e=t._self._c;return e("tr",[e("td",{attrs:{"auto-width":""}},[e("FreeStyleInput",{attrs:{disabled:!!t.sourceFile,"comfort-zone":10,"min-width":"200px"},on:{focus:function(e){t.oldName=t.name},input:function(e){t.active=!0},blur:t.onBlur},model:{value:t.name,callback:function(e){t.name=e},expression:"name"}})],1),e("td",{staticClass:"text-center"},[e("q-checkbox",{attrs:{disable:!!t.sourceFile,dense:""},model:{value:t.enable,callback:function(e){t.enable=e},expression:"enable"}})],1),e("td",[e("q-btn",t._b({attrs:{icon:"mdi-delete",disable:!!t.sourceFile},on:{click:t.emitDelete}},"q-btn",t.ICON_BUTTON_PROPS,!1))],1),e("td",[t.active?e("ValidationMarker",t._b({attrs:{placeholder:""}},"ValidationMarker",t.validationMessages,!1)):t._e()],1)])},di=[];function ui(t,e){const i={};for(const a of e)i[a]={get(){return this[t][a]},set(e){const i=this[t];this.setValue({object:i,key:a,value:e})}};return i}var pi={components:{FreeStyleInput:ot["FreeStyleInput"],ValidationMarker:ot["ValidationMarker"]},props:{capabilityDeclaration:{type:Object,required:!0}},data:()=>({ICON_BUTTON_PROPS:f,oldName:null,active:!1}),computed:{...ui("capabilityDeclaration",["name","sourceFile","enable"]),...Object(p["mapGetters"])("GattConfiguratorApp",["mergedGatt"]),validationMessages(){return li(this.mergedGatt.validationMessages.capabilityDeclarations)}},methods:{...Object(p["mapActions"])("GattConfiguratorApp",["setValue","maintainCapabilities"]),emitDelete(){this.$emit("delete")},onBlur(){"error"==this.validationMessages.type?this.name=this.oldName:this.maintainCapabilities({oldVal:this.oldName,newVal:this.name}),this.active=!1}}},hi=pi,mi=i("8f8e"),gi=Object(q["a"])(hi,ci,di,!1,null,null,null),bi=gi.exports;M()(gi,"components",{QCheckbox:mi["a"],QBtn:Gt["a"]});var fi={components:{CapabilityDeclarationRow:bi,ValidationMarker:ot["ValidationMarker"]},data:()=>({ICON_BUTTON_PROPS:f,PROFILE_META:I,capDecNameTmpOnFocus:null,capDecNameTmpActive:null}),computed:{...Object(p["mapGetters"])("GattConfiguratorApp",["gatt","mergedGatt","capabilityDeclarationNames"]),validationMessages(){return li(this.mergedGatt.validationMessages.capabilityDeclarations)}},methods:{...Object(p["mapActions"])("GattConfiguratorApp",["addArrayItem","removeArrayItem","maintainCapabilities"]),addCapabilityDeclaration(){this.capabilityDeclarationNames.length<16&&this.addArrayItem({array_object:this.gatt.capabilityDeclarations,item:{name:this.freeFeatureName(),enable:!1}})},freeFeatureName(){let t;for(let a=1;a<=16;a++){var e="feature_"+a;for(var{name:i}of this.gatt.capabilityDeclarations){if(0==e.localeCompare(i)){t=!0;break}t=!1}if(!t)return e}return"feature"},handleCapabilityDeclarationDelete(t){this.removeArrayItem({array_object:this.gatt.capabilityDeclarations,index:t}),this.maintainCapabilities()}}},vi=fi,yi=i("2bb1"),Ii=Object(q["a"])(vi,re,ne,!1,null,null,null),_i=Ii.exports;M()(Ii,"components",{QMarkupTable:yi["a"],QBtn:Gt["a"],QIcon:k["a"]});var Ti=function(){var t=this,e=t._self._c;return e("div",{staticClass:"row justify-between"},[e("q-toggle",t._b({attrs:{"data-cy":t.dataCy},on:{input:t.onInput}},"q-toggle",t.$attrs,!1)),e("ValidationMarker",t._b({attrs:{placeholder:""}},"ValidationMarker",t.validationMessages,!1))],1)},Ci=[],wi={namespaced:!0,components:{ValidationMarker:ot["ValidationMarker"]},props:{validationMessages:{required:!1,type:Object,default:()=>({type:null,messages:null})},dataCy:{type:String,required:!0}},methods:{onInput(t){this.$emit("input",t)}}},Si=wi,xi=i("9564"),Ai=Object(q["a"])(Si,Ti,Ci,!1,null,null,null),Oi=Ai.exports;M()(Ai,"components",{QToggle:xi["a"]});var qi={namespaced:!0,components:{SubGattItem:se,CapabilityDeclarations:_i,VToggle:Oi},data:()=>({PROFILE_META:I,GATT_TYPES:g["a"],INPUT_PROPS:b,ICONS:y}),computed:{...Object(p["mapGetters"])("GattConfiguratorApp",["gatt","mergedGatt"]),name:{get(){return this.gatt.name},set(t){this.setValue({object:this.gatt,key:"name",value:t})}},genericAttributeService:{get(){return this.gatt.genericAttributeService},set(t){this.setValue({object:this.gatt,key:"genericAttributeService",value:t})}},gattCaching:{get(){return this.gatt.gattCaching},set(t){this.setValue({object:this.gatt,key:"gattCaching",value:t})}},genericAttributeServiceValMsgs(){return li(this.gatt.validationMessages.genericAttributeService)},gattCachingValMsgs(){return li(this.gatt.validationMessages.gattCaching)}},methods:{...Object(p["mapActions"])("GattConfiguratorApp",["setValue"]),...Object(p["mapActions"])("Manual",["setManualTarget","setManualVisible"]),showManualAtProfile(){this.setManualTarget("profile"),this.setManualVisible(!0)}}},ki=qi,Di=i("27f9"),Ei=Object(q["a"])(ki,Yt,Wt,!1,null,null,null),Pi=Ei.exports;M()(Ei,"components",{QCard:Ut["a"],QIcon:k["a"],QBtn:Gt["a"],QInput:Di["a"]});var Mi=function(){var t=this,e=t._self._c;return e("div",{staticClass:"column q-gutter-md"},t._l(t.mergedGatt.services,(function(i){return e("Service",{key:i.vueId,staticClass:"card",class:{"card-selected":i.vueId==t.selectedItem.vueId},attrs:{id:i.vueId,service:i}})})),1)},Vi=[],Gi=function(){var t=this,e=t._self._c;return e("q-card",{attrs:{"data-cy":"card-"+t.GATT_TYPES.service}},[e("div",{staticClass:"row items-center q-gutter-sm q-pt-md"},[e("div",{staticClass:"col-auto"},[e("div",{staticClass:"card-title-block-bar"})]),e("q-icon",{attrs:{name:t.ICONS[t.GATT_TYPES.service].name,color:t.ICONS[t.GATT_TYPES.service].color,size:"lg"}}),e("div",{staticClass:"card-title-div"},[e("div",{staticClass:"card-title"},[t._v("\n        "+t._s(t.name)+"\n      ")]),e("em",{staticClass:"text-capitalize card-subtitle"},[t._v("\n        "+t._s(t.GATT_TYPES.service)+"\n      ")])]),e("div",{staticClass:"col row justify-end items-center q-px-md"},[t.service.sourceFile?e("q-btn",t._b({staticClass:"q-px-xs q-mr-sm",attrs:{color:"primary",icon:"mdi-pencil",dense:"",label:"Edit","data-cy":"source-file-edit"},on:{click:t.editContributedItem}},"q-btn",t.BUTTON_PROPS,!1)):t._e(),e("q-btn",{attrs:{icon:"mdi-help",color:"blue",round:"",dense:"",unelevated:"",size:"sm","data-cy":"card-help"},on:{click:t.showManualAtService}})],1)],1),e("div",{staticClass:"row q-pa-md q-col-gutter-md"},[e("div",{staticClass:"col-grow",staticStyle:{width:"500px"}},[e("div",{staticClass:"row q-col-gutter-md"},[e("q-input",t._b({staticClass:"col-6",attrs:{label:t.SERVICE_META.name.label,"data-cy":"input-name"},model:{value:t.name,callback:function(e){t.name=e},expression:"name"}},"q-input",t.INPUT_PROPS,!1)),e("VInput",t._b({staticClass:"col-6",attrs:{label:t.SERVICE_META.uuid.label,"data-cy":"input-uuid","validation-messages":t.uuidValidationMessages},model:{value:t.uuid,callback:function(e){t.uuid=e},expression:"uuid"}},"VInput",t.INPUT_PROPS,!1)),e("VInput",t._b({staticClass:"col-6",attrs:{label:t.SERVICE_META.id.label,readonly:void 0==t.id,debounce:0,"validation-messages":t.idValMsgs,"data-cy":"input-id"},on:{focus:function(e){t.serviceIdTmp=t.id},blur:()=>{"error"==t.idValMsgs.type?t.id=t.serviceIdTmp:t.maintainServiceIncludes({oldVal:t.serviceIdTmp,newVal:t.service.id})}},scopedSlots:t._u([{key:"prepend",fn:function(){return[e("q-checkbox",{attrs:{dense:"",value:void 0!=t.service.id,"data-cy":"input-id-enable"},on:{input:e=>t.id=e?"":void 0}})]},proxy:!0}]),model:{value:t.id,callback:function(e){t.id=e},expression:"id"}},"VInput",t.INPUT_PROPS,!1)),e("q-input",t._b({staticClass:"col-6",attrs:{label:t.SERVICE_META.sigType.label,"data-cy":"input-sig-type"},model:{value:t.sigType,callback:function(e){t.sigType=e},expression:"sigType"}},"q-input",t.INPUT_PROPS,!1)),e("q-select",t._b({staticClass:"col-6",attrs:{label:t.SERVICE_META.declarationType.label,options:t.SERVICE_META.declarationType.items,"data-cy":"input-declaration-type"},on:{input:t.maintainServiceAdvertise},model:{value:t.declarationType,callback:function(e){t.declarationType=e},expression:"declarationType"}},"q-select",t.INPUT_PROPS,!1)),e("div",{staticClass:"col-6 row items-center"},[e("q-toggle",t._b({attrs:{disable:t.disableAdvertiseToggle,label:t.SERVICE_META.advertise.label,"data-cy":"input-advertise"},model:{value:t.advertise,callback:function(e){t.advertise=e},expression:"advertise"}},"q-toggle",t.INPUT_PROPS,!1))],1),e("q-select",t._b({staticClass:"col-6",attrs:{label:t.SERVICE_META.includes.label,multiple:"",options:t.filteredServiceIds,"use-chips":"","data-cy":"input-includes"},model:{value:t.includes,callback:function(e){t.includes=e},expression:"includes"}},"q-select",t.INPUT_PROPS,!1)),e("q-select",t._b({staticClass:"col-6",attrs:{label:t.SERVICE_META.capabilities.label,multiple:"",options:t.capabilityDeclarationNames,"use-chips":"","data-cy":"input-capabilities"},on:{input:t.maintainCapabilities},model:{value:t.capabilities,callback:function(e){t.capabilities=e},expression:"capabilities"}},"q-select",t.INPUT_PROPS,!1))],1)]),e("div",{staticClass:"col-grow",staticStyle:{width:"300px"}},[e("q-card",{staticClass:"fit",staticStyle:{"min-height":"100px"},attrs:{bordered:"",flat:""}},[e("q-scroll-area",{staticClass:"fit"},[e("q-input",t._b({staticClass:"q-px-sm",attrs:{autogrow:"",outlined:!1,borderless:"",label:t.SERVICE_META.info.label,"data-cy":"input-info"},model:{value:t.info,callback:function(e){t.info=e},expression:"info"}},"q-input",t.INPUT_PROPS,!1))],1)],1)],1),e("div",{staticClass:"col-12"},[e("div",{staticClass:"row q-col-gutter-sm"},t._l(t.service.characteristics,(function(t){return e("div",{key:t.vueId,staticClass:"col-grow"},[e("SubGattItem",{attrs:{value:t}})],1)})),0)]),e("q-dialog",{model:{value:t.saveConfirmDialogOn,callback:function(e){t.saveConfirmDialogOn=e},expression:"saveConfirmDialogOn"}},[e("q-card",[e("q-card-section",{staticClass:"row q-gutter-md"},[e("q-icon",{attrs:{name:"mdi-alert ",color:"warning",size:"md"}}),e("div",[t._v("\n            Before making the contributed item(s) to be editable,"),e("br"),t._v("\n            the actual GATT Configuration must be saved.\n            "),e("br"),e("br"),e("strong",[t._v(" Do you want to save it now?")])])],1),e("q-card-actions",{attrs:{align:"right"}},[e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{"data-cy":"service-save-pop-up-save",label:"Save",color:"primary"},on:{click:t.saveConfigOpenEditContributionDialog}}),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{"data-cy":"service-save-pop-up-cancel",flat:"",label:"Cancel",color:"primary"}})],1)],1)],1),e("q-dialog",{model:{value:t.editConfirmDialogOn,callback:function(e){t.editConfirmDialogOn=e},expression:"editConfirmDialogOn"}},[e("q-card",[e("q-card-section",[e("div",{staticClass:"row q-mx-sm"},[e("q-icon",{staticClass:"q-mr-md",attrs:{name:"mdi-alert-circle ",color:"negative",size:"md"}}),e("div",{staticClass:"q-mt-xs col"},[e("strong",[t._v("\n                Are you sure you want to edit the contributed items?\n              ")]),e("br"),e("br"),t._v("\n              If you do so, these items will become part of Your GATT\n              Configuration and removed from contribution file(s)."),e("br"),e("br"),t._v("\n              Note: To undo this operation: "),e("br"),e("ul",[e("li",[t._v("remove these item(s) from Your GATT Configuration")]),e("li",[t._v("delete the contribution file(s) from btconf directory")]),e("li",[t._v("force generate the project")])])])],1)]),e("q-card-actions",{attrs:{align:"right"}},[e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{"data-cy":"service-edit-pop-up-edit",outline:"",label:"Edit",color:"negative"},on:{click:function(e){return t.makeContributionEditable(t.service)}}}),e("q-btn",{directives:[{name:"close-popup",rawName:"v-close-popup"}],attrs:{"data-cy":"service-edit-pop-up-cancel",flat:"",label:"Cancel",color:"primary"}})],1)],1)],1)],1)])},Ni=[],Ri=i("a883"),ji={components:{SubGattItem:se,VInput:ot["VInput"]},props:{service:{type:Object,required:!0}},data(){return{GATT_TYPES:g["a"],SERVICE_META:_,ICONS:y,BUTTON_PROPS:v,INPUT_PROPS:{...b,disable:!!this.service.sourceFile},serviceIdTmp:null,saveConfirmDialogOn:!1,editConfirmDialogOn:!1}},computed:{...Object(p["mapGetters"])("GattConfiguratorApp",["capabilityDeclarationNames","serviceIds","gatt"]),...Object(p["mapGetters"])("MainView",["dirty"]),...ui("service",Object.keys(Object(mt["d"])())),filteredServiceIds(){return this.serviceIds.filter((t=>this.service.id!=t))},disableAdvertiseToggle(){return!!this.service.sourceFile||"secondary"===this.declarationType},idValMsgs(){return li(this.service.validationMessages.id)},uuidValidationMessages(){return li(this.service.validationMessages.uuid)}},watch:{saveConfirmDialogOn:function(){this.setDialogOn(this.saveConfirmDialogOn)},editConfirmDialogOn:function(){this.setDialogOn(this.editConfirmDialogOn)}},methods:{...Object(p["mapActions"])("Manual",["setManualTarget","setManualVisible"]),...Object(p["mapActions"])("GattConfiguratorApp",["setValue","maintainServiceIncludes","maintainCapabilities","makeContributionEditable"]),...Object(p["mapActions"])("MainView",["setDialogOn"]),maintainServiceAdvertise(t){"secondary"===t&&(this.advertise=!1)},editContributedItem(){!1===this.dirty?this.editConfirmDialogOn=!0:this.saveConfirmDialogOn=!0},saveConfigOpenEditContributionDialog(){Ri["a"].onSaveEvent(),this.editConfirmDialogOn=!0},showManualAtService(){this.setManualTarget("services"),this.setManualVisible(!0)}}},Fi=ji,Ui=i("ddd8"),Li=i("4983"),Bi=Object(q["a"])(Fi,Gi,Ni,!1,null,null,null),Qi=Bi.exports;M()(Bi,"components",{QCard:Ut["a"],QIcon:k["a"],QBtn:Gt["a"],QInput:Di["a"],QCheckbox:mi["a"],QSelect:Ui["a"],QToggle:xi["a"],QScrollArea:Li["a"],QDialog:Ft["a"],QCardSection:Lt["a"],QCardActions:Bt["a"]}),M()(Bi,"directives",{ClosePopup:Qt["a"]});var $i={namespaced:!0,components:{Service:Qi},computed:{...Object(p["mapGetters"])("GattConfiguratorApp",["mergedGatt","selectedItem"])},watch:{selectedItem:function(){this.renderCompleteEvent()}},created(){this.renderCompleteEvent()},methods:{renderCompleteEvent(){this.$nextTick((()=>{z(this.selectedItem,g["a"].service)}))}}},zi=$i,Yi=Object(q["a"])(zi,Mi,Vi,!1,null,null,null),Wi=Yi.exports,Hi=function(){var t=this,e=t._self._c;return e("div",{staticClass:"column q-gutter-md"},t._l(t.selectedItem.parent.characteristics,(function(i){return e("Characteristic",{key:i.vueId,staticClass:"card",class:{"card-selected":i.vueId==t.selectedItem.vueId},attrs:{id:i.vueId,characteristic:i}})})),1)},Ki=[],Xi=function(){var t=this,e=t._self._c;return e("q-card",{attrs:{"data-cy":"card-"+t.GATT_TYPES.characteristic}},[e("div",{staticClass:"row items-center q-gutter-sm q-pt-md"},[e("div",{staticClass:"col-auto"},[e("div",{staticClass:"card-title-block-bar"})]),e("q-icon",{attrs:{name:t.ICONS[t.GATT_TYPES.characteristic].name,color:t.ICONS[t.GATT_TYPES.characteristic].color,size:"lg"}}),e("div",{staticClass:"card-title-div"},[e("div",{staticClass:"card-title"},[t._v("\n        "+t._s(t.name)+"\n      ")]),e("em",{staticClass:"text-capitalize card-subtitle"},[t._v("\n        "+t._s(t.GATT_TYPES.characteristic)+"\n      ")])]),e("div",{staticClass:"col row justify-end items-center q-px-md"},[e("q-btn",{attrs:{icon:"mdi-help",color:"blue",round:"",dense:"",unelevated:"",size:"sm","data-cy":"card-help"},on:{click:t.showManualAtCharacteristic}})],1)],1),e("div",{staticClass:"row q-pa-md q-col-gutter-md"},[e("div",{staticClass:"col-grow",staticStyle:{width:"500px"}},[e("div",{staticClass:"row q-col-gutter-md"},[e("q-input",t._b({staticClass:"col-6",attrs:{label:t.CHARACTERISTIC_META.name.label,"data-cy":"input-name"},model:{value:t.name,callback:function(e){t.name=e},expression:"name"}},"q-input",t.INPUT_PROPS,!1)),e("VInput",t._b({staticClass:"col-6",attrs:{label:t.CHARACTERISTIC_META.uuid.label,"data-cy":"input-uuid","validation-messages":t.uuidValidationMessages},model:{value:t.uuid,callback:function(e){t.uuid=e},expression:"uuid"}},"VInput",t.INPUT_PROPS,!1)),e("VInput",t._b({staticClass:"col-6",attrs:{label:t.CHARACTERISTIC_META.id.label,readonly:void 0==t.id,"data-cy":"input-id","validation-messages":t.idValidationMessages},scopedSlots:t._u([{key:"prepend",fn:function(){return[e("q-checkbox",{attrs:{dense:"",value:void 0!=t.id,"data-cy":"input-id-enable"},on:{input:e=>t.id=e?"":void 0}})]},proxy:!0}]),model:{value:t.id,callback:function(e){t.id=e},expression:"id"}},"VInput",t.INPUT_PROPS,!1)),e("q-input",t._b({staticClass:"col-6",attrs:{label:t.CHARACTERISTIC_META.sigType.label,"data-cy":"input-sig-type"},model:{value:t.sigType,callback:function(e){t.sigType=e},expression:"sigType"}},"q-input",t.INPUT_PROPS,!1)),e("div",{staticClass:"col-12"},[e("AttributeValue",{attrs:{"gatt-item":t.characteristic}})],1),e("q-select",t._b({staticClass:"col-6",attrs:{label:t.CHARACTERISTIC_META.capabilities.label,multiple:"",options:t.characteristicCapabilityDeclarationOptions,"use-chips":"","data-cy":"input-capabilities"},model:{value:t.capabilities,callback:function(e){t.capabilities=e},expression:"capabilities"}},"q-select",t.INPUT_PROPS,!1)),e("VInput",t._b({staticClass:"col-6",attrs:{label:t.CHARACTERISTIC_META.description.label,readonly:void 0==t.description,"data-cy":"input-description","validation-messages":t.descriptionValidationMessages},scopedSlots:t._u([{key:"prepend",fn:function(){return[e("q-checkbox",{attrs:{dense:"",value:void 0!=t.description,"data-cy":"input-description-enable"},on:{input:e=>t.description=e?"":void 0}})]},proxy:!0}]),model:{value:t.description,callback:function(e){t.description=e},expression:"description"}},"VInput",t.INPUT_PROPS,!1)),e("div",{staticClass:"col"},[e("PropertySettings",{attrs:{"gatt-item":t.characteristic,disable:!!t.characteristic.sourceFile}})],1)],1)]),e("div",{staticClass:"col-grow",staticStyle:{width:"300px"}},[e("q-card",{staticClass:"fit",staticStyle:{"min-height":"100px"},attrs:{bordered:"",flat:""}},[e("q-scroll-area",{staticClass:"fit"},[e("q-input",t._b({staticClass:"q-px-sm",attrs:{autogrow:"",outlined:!1,borderless:"",label:t.CHARACTERISTIC_META.info.label,"data-cy":"input-info"},model:{value:t.info,callback:function(e){t.info=e},expression:"info"}},"q-input",t.INPUT_PROPS,!1))],1)],1)],1),e("div",{staticClass:"col-12"},[e("div",{staticClass:"row q-col-gutter-sm"},t._l(t.descriptors,(function(t){return e("div",{key:t.vueId,staticClass:"col-grow"},[e("SubGattItem",{attrs:{value:t}})],1)})),0)])])])},Ji=[],Zi=function(){var t=this,e=t._self._c;return e("q-card",{attrs:{bordered:"",flat:""}},[e("div",{staticClass:"row q-col-gutter-sm q-pa-sm"},[e("div",{staticClass:"col-6 text-bold text-primary"},[t._v("\n      Value settings\n    ")]),e("div",{staticClass:"col-6 row items-center"},[e("div",{staticClass:"col-auto"},[e("q-btn-toggle",{staticClass:"btn-toggle",attrs:{size:"sm",dense:"",disable:!!t.gattItem.sourceFile,unelevated:"","toggle-color":"primary",color:"transparent","text-color":"primary","toggle-text-color":"toggle-color",options:t.VALUE_META.type.items.map((t=>({label:t,value:t}))),"data-cy":"input-value-type","data-cy-value":t.type},model:{value:t.type,callback:function(e){t.type=e},expression:"type"}}),e("q-tooltip",[t._v("Value type: "+t._s(t.type))])],1)]),"user"!=t.type?[e("div",{staticClass:"col-6 row items-center"},[e("q-toggle",t._b({attrs:{label:t.VALUE_META.constant.label,"data-cy":"input-constant"},model:{value:t.constant,callback:function(e){t.constant=e},expression:"constant"}},"q-toggle",t.INPUT_PROPS,!1))],1),e("div",{staticClass:"col-6 row items-center"},[e("q-toggle",t._b({attrs:{label:t.VALUE_META.variableLength.label,"data-cy":"input-variable-length"},model:{value:t.variableLength,callback:function(e){t.variableLength=e},expression:"variableLength"}},"q-toggle",t.INPUT_PROPS,!1))],1),e("VInput",t._b({staticClass:"col-6",attrs:{label:t.VALUE_META.initialValue.label,"validation-messages":t.initValueValMsgs,"data-cy":"input-init-value",prefix:t.initialValuePrefix},model:{value:t.initialValue,callback:function(e){t.initialValue=e},expression:"initialValue"}},"VInput",t.INPUT_PROPS,!1)),e("VInput",t._b({staticClass:"col-6",attrs:{readonly:!t.length_enable,label:t.variableLength?t.VALUE_META.max_length.label:t.VALUE_META.length.label,suffix:t.variableLength?t.VALUE_META.max_length.unit:t.VALUE_META.length.unit,type:"number","validation-messages":t.valueLengthValMsgs,"data-cy":"input-length"},scopedSlots:t._u([{key:"prepend",fn:function(){return[e("q-checkbox",{attrs:{dense:"","data-cy":"input-length-enable"},model:{value:t.length_enable,callback:function(e){t.length_enable=e},expression:"length_enable"}},[e("q-tooltip",[t._v("Overwrite auto calculated length.")])],1)]},proxy:!0}],null,!1,4045484017),model:{value:t.calculated_length,callback:function(e){t.calculated_length=e},expression:"calculated_length"}},"VInput",t.INPUT_PROPS,!1))]:t._e()],2)])},ta=[],ea=i("da4a");function ia(){const t={};for(const e in new ea["Value"]({initialValue:""}))t[e]={get(){return this.gattItem.value[e]},set(t){const i=this.gattItem.value;this.setValue({object:i,key:e,value:t})}};return t}var aa={components:{VInput:ot["VInput"]},props:{gattItem:{type:Object,required:!0}},computed:{...ia(),length_enable:{get(){return null!==this.length},set(t){this.length=t?this.calculated_length:null}},calculated_length:{get(){return this.length_enable?this.length:this.type===ea["ValueType"].hex?this.initialValue.replace(/0x/i,"").length/2:this.utf8TextLength(this.initialValue)},set(t){this.length=t}},initValueValMsgs(){return li(this.gattItem.validationMessages.value_$_initialValue)},valueLengthValMsgs(){return li(this.gattItem.validationMessages.value_$_length)},initialValuePrefix(){return this.type===ea["ValueType"].hex?"0x":""}},created(){Object.assign(this,{VALUE_META:T,INPUT_PROPS:{...b,disable:!!this.gattItem.sourceFile},utf8TextLength:ce})},methods:{...Object(p["mapActions"])("GattConfiguratorApp",["setValue"])}},sa=aa,ra=(i("10eb"),i("6a67")),na=i("b498"),la=Object(q["a"])(sa,Zi,ta,!1,null,null,null),oa=la.exports;M()(la,"components",{QCard:Ut["a"],QBtnToggle:ra["a"],QTooltip:at["a"],QToggle:xi["a"],QCheckbox:mi["a"],QColor:na["a"]});var ca=function(){var t=this,e=t._self._c;return e("q-markup-table",{class:{"alert-error-border":"error"==t.propertiesValidationMessages.type,"alert-warning-border":"warning"==t.propertiesValidationMessages.type},attrs:{flat:"",bordered:"",dense:""}},[e("thead",[e("tr",[e("th",{attrs:{scope:"col"}},[t._v("Properties")]),e("th",{attrs:{scope:"col"}},[t._v("Authenticated")]),e("th",{attrs:{scope:"col"}},[t._v("Bonded")]),e("th",{attrs:{scope:"col"}},[t._v("Encrypted")]),e("th",{attrs:{scope:"col"}},[e("ValidationMarker",t._b({attrs:{placeholder:""}},"ValidationMarker",t.propertiesValidationMessages,!1))],1)])]),e("tbody",t._l(t.gattItem.permissions,(function(i,a){return e("PermissionRow",{key:a,attrs:{permission:i,"gatt-item":t.gattItem,disable:t.disable,"data-cy-row":a}})})),1)])},da=[],ua=function(){var t=this,e=t._self._c;return e("tr",[e("td",[e("div",{staticClass:"column q-gutter-sm"},t._l(t.permission.properties,(function(i,a){return e("q-toggle",t._b({key:a,attrs:{label:t.names[a],"data-cy":`input-enable-${a}`,value:i},on:{input:function(e){return t.setPropertyValue(e,a)}}},"q-toggle",t.INPUT_PROPS,!1))})),1)]),e("td",{staticClass:"text-center"},[e("q-checkbox",t._b({attrs:{"data-cy":"input-authenticated"},model:{value:t.authenticated,callback:function(e){t.authenticated=e},expression:"authenticated"}},"q-checkbox",t.INPUT_PROPS,!1))],1),e("td",{staticClass:"text-center"},[e("q-checkbox",t._b({attrs:{"data-cy":"input-bonded"},model:{value:t.bonded,callback:function(e){t.bonded=e},expression:"bonded"}},"q-checkbox",t.INPUT_PROPS,!1))],1),e("td",{staticClass:"text-center"},[e("q-checkbox",t._b({attrs:{"data-cy":"input-encrypted"},model:{value:t.encrypted,callback:function(e){t.encrypted=e},expression:"encrypted"}},"q-checkbox",t.INPUT_PROPS,!1))],1),e("td",["notify"in t.permission.properties?e("ValidationMarker",t._b({attrs:{placeholder:""}},"ValidationMarker",t.notifyValMsgs,!1)):t._e()],1)])},pa=[];const ha={read:"Read",write:"Write",reliableWrite:"Reliable write",writeNoResponse:"Write without response",notify:"Notify",indicate:"Indicate"};var ma={components:{ValidationMarker:ot["ValidationMarker"]},props:{permission:{type:Object,required:!0},disable:Boolean,gattItem:{type:Object,required:!0}},data(){return{INPUT_PROPS:{...b,disable:this.disable}}},computed:{...ui("permission",Object.keys(new ea["Permissions"])),names:()=>ha,notifyValMsgs(){return li(this.gattItem.validationMessages.notify)}},watch:{properties:{deep:!0,handler(){oe(this.permission.properties)||(this.authenticated=!1,this.bonded=!1,this.encrypted=!1)}},authenticated(t){t&&(this.enable=!0)},bonded(t){t&&(this.enable=!0)},encrypted(t){t&&(this.enable=!0)}},methods:{...Object(p["mapActions"])("GattConfiguratorApp",["setValue"]),setPropertyValue(t,e){this.setValue({key:e,value:t,object:this.permission.properties})}}},ga=ma,ba=Object(q["a"])(ga,ua,pa,!1,null,null,null),fa=ba.exports;M()(ba,"components",{QToggle:xi["a"],QCheckbox:mi["a"]});var va={components:{PermissionRow:fa,ValidationMarker:ot["ValidationMarker"]},props:{gattItem:{type:Object,required:!0},disable:Boolean},computed:{propertiesValidationMessages(){return li(this.gattItem.validationMessages.properties)}}},ya=va,Ia=Object(q["a"])(ya,ca,da,!1,null,null,null),_a=Ia.exports;M()(Ia,"components",{QMarkupTable:yi["a"]});var Ta={components:{SubGattItem:se,AttributeValue:oa,PropertySettings:_a,VInput:ot["VInput"]},props:{characteristic:{type:Object,required:!0}},data(){return{GATT_TYPES:g["a"],CHARACTERISTIC_META:C,ICONS:y,INPUT_PROPS:{...b,disable:!!this.characteristic.sourceFile}}},computed:{...ui("characteristic",Object.keys(Object(mt["a"])())),...Object(p["mapGetters"])("GattConfiguratorApp",["capabilityDeclarationNames"]),characteristicCapabilityDeclarationOptions(){let t;return t=this.characteristic.parent.capabilities.length>0?this.characteristic.parent.capabilities:this.capabilityDeclarationNames,t},uuidValidationMessages(){return li(this.characteristic.validationMessages.uuid)},descriptionValidationMessages(){return li(this.characteristic.validationMessages.description)},idValidationMessages(){return li(this.characteristic.validationMessages.id)}},methods:{...Object(p["mapActions"])("GattConfiguratorApp",["setValue"]),...Object(p["mapActions"])("Manual",["setManualTarget","setManualVisible"]),showManualAtCharacteristic(){this.setManualTarget("characteristics"),this.setManualVisible(!0)}}},Ca=Ta,wa=Object(q["a"])(Ca,Xi,Ji,!1,null,null,null),Sa=wa.exports;M()(wa,"components",{QCard:Ut["a"],QIcon:k["a"],QBtn:Gt["a"],QInput:Di["a"],QCheckbox:mi["a"],QSelect:Ui["a"],QScrollArea:Li["a"]});var xa={namespaced:!0,components:{Characteristic:Sa},computed:{...Object(p["mapGetters"])("GattConfiguratorApp",["selectedItem"])},watch:{selectedItem:function(){this.renderCompleteEvent()}},created(){this.renderCompleteEvent()},methods:{renderCompleteEvent(){this.$nextTick((()=>{z(this.selectedItem,g["a"].characteristic)}))}}},Aa=xa,Oa=Object(q["a"])(Aa,Hi,Ki,!1,null,null,null),qa=Oa.exports,ka=function(){var t=this,e=t._self._c;return e("div",{staticClass:"column q-gutter-md"},t._l(t.selectedItem.parent.descriptors,(function(i){return e("Descriptor",{key:i.vueId,staticClass:"card",class:{"card-selected":i.vueId==t.selectedItem.vueId},attrs:{id:i.vueId,descriptor:i}})})),1)},Da=[],Ea=function(){var t=this,e=t._self._c;return e("q-card",{attrs:{"data-cy":"card-"+t.GATT_TYPES.descriptor}},[e("div",{staticClass:"row items-center q-gutter-sm q-pt-md"},[e("div",{staticClass:"col-auto"},[e("div",{staticClass:"card-title-block-bar"})]),e("q-icon",{attrs:{name:t.ICONS[t.GATT_TYPES.descriptor].name,color:t.ICONS[t.GATT_TYPES.descriptor].color,size:"lg"}}),e("div",{staticClass:"card-title-div"},[e("div",{staticClass:"card-title"},[t._v("\n        "+t._s(t.name)+"\n      ")]),e("em",{staticClass:"text-capitalize card-subtitle"},[t._v("\n        "+t._s(t.GATT_TYPES.descriptor)+"\n      ")])]),e("div",{staticClass:"col row justify-end items-center q-px-md"},[e("q-btn",{attrs:{icon:"mdi-help",color:"blue",round:"",dense:"",unelevated:"",size:"sm","data-cy":"card-help"},on:{click:t.showManualAtDescriptor}})],1)],1),e("div",{staticClass:"row q-pa-md q-col-gutter-md"},[e("div",{staticClass:"col-grow",staticStyle:{width:"500px"}},[e("div",{staticClass:"row q-col-gutter-md"},[e("q-input",t._b({staticClass:"col-6",attrs:{label:t.DESCRIPTOR_META.name.label,"data-cy":"input-name"},model:{value:t.name,callback:function(e){t.name=e},expression:"name"}},"q-input",t.INPUT_PROPS,!1)),e("VInput",t._b({staticClass:"col-6",attrs:{label:t.DESCRIPTOR_META.uuid.label,"data-cy":"input-uuid","validation-messages":t.uuidValidationMessages},model:{value:t.uuid,callback:function(e){t.uuid=e},expression:"uuid"}},"VInput",t.INPUT_PROPS,!1)),e("VInput",t._b({staticClass:"col-6",attrs:{label:t.DESCRIPTOR_META.id.label,readonly:void 0==t.id,"data-cy":"input-id","validation-messages":t.idValidationMessages},scopedSlots:t._u([{key:"prepend",fn:function(){return[e("q-checkbox",{attrs:{dense:"",value:void 0!=t.id,"data-cy":"input-id-enable"},on:{input:e=>t.id=e?"":void 0}})]},proxy:!0}]),model:{value:t.id,callback:function(e){t.id=e},expression:"id"}},"VInput",t.INPUT_PROPS,!1)),e("q-input",t._b({staticClass:"col-6",attrs:{label:t.DESCRIPTOR_META.sigType.label,"data-cy":"input-sig-type"},model:{value:t.sigType,callback:function(e){t.sigType=e},expression:"sigType"}},"q-input",t.INPUT_PROPS,!1)),e("div",{staticClass:"col-12"},[e("AttributeValue",{attrs:{"gatt-item":t.descriptor}})],1),e("div",{staticClass:"col-auto"},[e("PropertySettings",{attrs:{"gatt-item":t.descriptor,disable:!!t.descriptor.sourceFile}})],1)],1)]),e("div",{staticClass:"col-grow",staticStyle:{width:"250px"}},[e("q-card",{staticClass:"fit",staticStyle:{"min-height":"100px"},attrs:{bordered:"",flat:""}},[e("q-scroll-area",{staticClass:"fit"},[e("q-input",t._b({staticClass:"q-px-sm",attrs:{autogrow:"",outlined:!1,borderless:"",label:t.DESCRIPTOR_META.info.label,"data-cy":"input-info"},model:{value:t.info,callback:function(e){t.info=e},expression:"info"}},"q-input",t.INPUT_PROPS,!1))],1)],1)],1)])])},Pa=[],Ma={components:{VInput:ot["VInput"],AttributeValue:oa,PropertySettings:_a},props:{descriptor:{type:Object,required:!0}},data(){return{GATT_TYPES:g["a"],DESCRIPTOR_META:w,ICONS:y,INPUT_PROPS:{...b,disable:!!this.descriptor.sourceFile}}},computed:{...ui("descriptor",Object.keys(Object(mt["b"])())),...Object(p["mapGetters"])("GattConfiguratorApp",["gatt"]),uuidValidationMessages(){return li(this.descriptor.validationMessages.uuid)},idValidationMessages(){return li(this.descriptor.validationMessages.id)}},methods:{...Object(p["mapActions"])("GattConfiguratorApp",["setValue"]),...Object(p["mapActions"])("Manual",["setManualTarget","setManualVisible"]),showManualAtDescriptor(){this.setManualTarget("descriptors"),this.setManualVisible(!0)}}},Va=Ma,Ga=Object(q["a"])(Va,Ea,Pa,!1,null,null,null),Na=Ga.exports;M()(Ga,"components",{QCard:Ut["a"],QIcon:k["a"],QBtn:Gt["a"],QInput:Di["a"],QCheckbox:mi["a"],QScrollArea:Li["a"]});var Ra={namespaced:!0,components:{Descriptor:Na},computed:{...Object(p["mapGetters"])("GattConfiguratorApp",["selectedItem"])},watch:{selectedItem:function(){this.renderCompleteEvent()}},created(){this.renderCompleteEvent()},methods:{renderCompleteEvent(){this.$nextTick((()=>{z(this.selectedItem,g["a"].descriptor)}))}}},ja=Ra,Fa=Object(q["a"])(ja,ka,Da,!1,null,null,null),Ua=Fa.exports,La=function(){var t=this,e=t._self._c;return e("div",{staticClass:"col row",attrs:{"data-cy":"sig-layout"}},[e("div",{staticClass:"col-auto"},[e("q-card",{staticClass:"full-height column"},[e("q-btn-toggle",{staticClass:"gatt-types",attrs:{ripple:!1,"no-caps":"",options:t.tabOptions,"toggle-text-color":"toggle-color"},model:{value:t.tab,callback:function(e){t.tab=e},expression:"tab"}}),e("q-separator",{staticStyle:{height:"1px"}}),e("div",{staticClass:"col-auto"},[e("q-input",{ref:"filter",staticClass:"q-px-sm",attrs:{borderless:"",dense:"",placeholder:"Filter",debounce:"500"},scopedSlots:t._u([{key:"prepend",fn:function(){return[e("q-icon",{attrs:{name:"mdi-filter-variant"}})]},proxy:!0},{key:"after",fn:function(){return[e("q-btn",t._b({attrs:{icon:"mdi-filter-menu-outline"}},"q-btn",t.ICON_BUTTON_PROPS,!1),[e("q-menu",[e("q-option-group",{staticClass:"q-pa-sm q-gutter-xs",attrs:{options:t.sourceOptions,color:"primary",dense:"",type:"checkbox"},model:{value:t.sourceFilter,callback:function(e){t.sourceFilter=e},expression:"sourceFilter"}})],1),e("q-tooltip",{attrs:{anchor:"center left",self:"center right"}},[t._v("\n                Source filter\n              ")])],1)]},proxy:!0}]),model:{value:t.filterText,callback:function(e){t.filterText=e},expression:"filterText"}})],1),e("q-separator",{staticStyle:{height:"1px"}}),e("div",{staticClass:"col"},[e("q-scroll-area",{staticClass:"fit"},t._l(t.GATT_TYPES,(function(i){return e("DragTree",{directives:[{name:"show",rawName:"v-show",value:i==t.tab,expression:"type == tab"}],key:i,attrs:{value:t.sigTree[i],"node-key":"vueId","read-only":"",groups:t.groups[i],"drag-options":{sort:!1,clone:t.clone},selected:t.selected},scopedSlots:t._u([{key:"label",fn:function({node:i}){return[e("div",{staticClass:"row sig-item q-pr-md"},[e("GattTreeItem",{staticClass:"cursor-pointer col",attrs:{value:i},on:{click:e=>t.selected=e,mousedown:e=>t.preFetchDraggedItem(e)}}),e("q-btn",{staticClass:"sig-add",attrs:{dense:"",flat:"",icon:"mdi-plus-thick",color:"primary",size:"sm"},on:{click:function(e){return t.addSigItem(i)}}})],1)]}}],null,!0)})})),1)],1)],1)],1),e("div",{staticClass:"q-ml-md col"},[e("q-card",{staticClass:"column full-height"},[null!=t.selected?[e("div",{staticClass:"row items-center q-py-md q-gutter-x-sm sig-info-title-block"},[e("div",{staticClass:"col-auto"},[e("div",{staticClass:"sig-info-title-block-bar"})]),e("q-icon",{attrs:{name:t.ICONS[t.selected.gattType].name,color:t.ICONS[t.selected.gattType].color,size:"lg"}}),e("div",{staticClass:"card-title-div"},[e("div",{staticClass:"card-title"},[t._v("\n              "+t._s(t.selected.name)+"\n            ")]),e("em",{staticClass:"text-capitalize card-subtitle"},[t._v("\n              "+t._s(t.selected.gattType)+"\n            ")])]),e("div",{staticClass:"col"}),t.selectedSigItem.uuid?e("div",{staticClass:"q-ml-lg",attrs:{"data-cy":"sig-layout-uuid"}},[e("strong",[t._v("UUID: ")]),e("span",{staticClass:"q-mr-sm",staticStyle:{"overflow-wrap":"break-word","word-wrap":"break-word"}},[t._v("\n              "+t._s(32===t.selectedSigItem.uuid.length?t.toUuidFormat(t.selectedSigItem.uuid):t.selectedSigItem.uuid)+"\n            ")])]):t._e()],1),e("div",[e("q-separator")],1),e("div",{staticClass:"col"},[e("q-scroll-area",{staticClass:"fit"},[e("div",{staticClass:"q-pa-sm"},[e("SigInfo",{attrs:{tab:t.tab,types:t.getTypes(t.selected),selected:t.selected}})],1)])],1)]:t._e()],2)],1)])},Ba=[];let Qa;function $a(t,e){let i,a,s;if(3===e.length){if(t===ea["GattTypes"].service){var r;i=ea["GattTypes"].descriptor,a=ea["SigMetadata"].descriptors.get(e[2]);const t=ea["SigMetadata"].services.get(e[0]),l=null===t||void 0===t||null===(r=t.characteristics)||void 0===r?void 0:r.find((t=>t.type===e[1]));var n;if(l)s=null===l||void 0===l||null===(n=l.descriptors)||void 0===n?void 0:n.find((t=>{var e;return t.type===(null===(e=a)||void 0===e?void 0:e.type)}))}}else if(2===e.length){if(t===ea["GattTypes"].profile){var l;i=ea["GattTypes"].service,a=ea["SigMetadata"].services.get(e[1]);let t=ea["SigMetadata"].profiles.get(e[0]);s=null===t||void 0===t||null===(l=t.services)||void 0===l?void 0:l.find((t=>{var e;return t.type===(null===(e=a)||void 0===e?void 0:e.type)}))}else if(t===ea["GattTypes"].service){var o;i=ea["GattTypes"].characteristic,a=ea["SigMetadata"].characteristics.get(e[1]);const t=ea["SigMetadata"].services.get(e[0]);s=null===t||void 0===t||null===(o=t.characteristics)||void 0===o?void 0:o.find((t=>{var e;return t.type==(null===(e=a)||void 0===e?void 0:e.type)}))}}else i=ea["GattTypes"][t],a=ea["SigMetadata"][Qa[t]].get(e[0]);return a||(i=void 0),{gattType:i,sigItem:a,sigInformation:s}}function za(t,e){const i=$a(t,e);let a;if(i.sigItem)switch(i.gattType){case ea["GattTypes"].profile:a={gattType:ea["GattTypes"].profile,gattItem:Object(ea["sigToGatt"])(i.sigItem)};break;case ea["GattTypes"].service:a={gattType:ea["GattTypes"].service,gattItem:Object(ea["sigToService"])(i.sigItem,i.sigInformation)};break;case ea["GattTypes"].characteristic:a={gattType:ea["GattTypes"].characteristic,gattItem:Object(ea["sigToCharacteristic"])(i.sigItem,i.sigInformation)};break;case ea["GattTypes"].descriptor:a={gattType:ea["GattTypes"].descriptor,gattItem:Object(ea["sigToDescriptor"])(i.sigItem,i.sigInformation)};break;default:break}return a}(function(t){t["profile"]="profiles",t["service"]="services",t["characteristic"]="characteristics",t["descriptor"]="descriptors"})(Qa||(Qa={}));var Ya=function(){var t=this,e=t._self._c;return t.item?e("div",{attrs:{"data-cy":"description"}},[e("div",{staticClass:"q-px-sm q-gutter-sm"},[t._m(0),e("div",{staticClass:"row"},[t.itemInfo&&t.selectedItemType==t.GATT_TYPES.service?e("div",[e("SigDescriptionTableServiceInformation",{attrs:{"sig-item":t.item,"sig-service-information":t.itemInfo}})],1):t.itemInfo&&t.selectedItemType==t.GATT_TYPES.characteristic?e("div",[e("SigDescriptionTableCharacteristicInformation",{attrs:{"sig-item":t.item,"sig-characteristic-information":t.itemInfo}})],1):t.itemInfo&&t.selectedItemType==t.GATT_TYPES.descriptor?e("div",[e("SigDescriptionTableDescriptorInformation",{attrs:{"sig-item":t.item,"sig-descriptor-information":t.itemInfo}})],1):e("div",[e("SigDescriptionTable",{attrs:{"sig-item":t.item}})],1)]),t.selectedItemType===t.GATT_TYPES.profile?e("div",[t.item.services&&t.item.services.length?e("div",{staticClass:"text-grey card-title"},[e("span",{staticClass:"sig-chapter-title",attrs:{"data-cy":"profile-services"}},[t._v("\n          Services\n        ")])]):t._e(),t.item.services&&t.item.services.length?e("div",t._l(t.item.services,(function(i,a){return e("div",{key:a,staticClass:"row"},[e("div",{staticClass:"q-my-sm",attrs:{flat:""}},[e("div",{staticClass:"row items-center"},[e("q-icon",{attrs:{name:t.ICONS.service.name,color:t.ICONS.service.color,size:"md"}}),e("strong",{staticClass:"q-ml-xs"},[t._v(t._s(t.getSigItem(i.type).name))])],1),e("div",[e("SigDescriptionTableServiceInformation",{attrs:{"sig-item":t.getSigItem(i.type),"sig-service-information":i}})],1)])])})),0):t._e()]):t._e(),t.selectedItemType===t.GATT_TYPES.service?e("div",[t.item.characteristics&&t.item.characteristics.length?e("div",{staticClass:"text-grey card-title"},[e("span",{staticClass:"sig-chapter-title",attrs:{"data-cy":"service-characteristics"}},[t._v("\n          Characteristics and Descriptors\n        ")])]):t._e(),t.item.characteristics&&t.item.characteristics.length?e("div",t._l(t.item.characteristics,(function(i,a){return e("div",{key:a},[e("div",{staticClass:"q-my-sm"},[e("div",{staticClass:"row items-center"},[e("q-icon",{attrs:{name:t.ICONS.characteristic.name,color:t.ICONS.characteristic.color,size:"md"}}),e("strong",{staticClass:"q-ml-xs"},[t._v(t._s(i.name))])],1),e("div",[e("SigDescriptionTableCharacteristicInformation",{attrs:{"sig-item":t.getSigItem(i.type),"sig-characteristic-information":i}}),i.descriptors?e("div",{staticClass:"q-ml-lg"},t._l(i.descriptors,(function(i,a){return e("div",{key:a},[e("div",{staticClass:"q-my-sm"},[e("div",{staticClass:"row items-center"},[e("q-icon",{attrs:{name:t.ICONS.descriptor.name,color:t.ICONS.descriptor.color,size:"md"}}),e("strong",{staticClass:"q-ml-xs"},[t._v(t._s(i.name))])],1),e("div",[e("SigDescriptionTableDescriptorInformation",{attrs:{"sig-item":t.getSigItem(i.type),"sig-descriptor-information":i}}),e("div")],1)])])})),0):t._e()],1)])])})),0):t._e()]):t.selectedItemType===t.GATT_TYPES.characteristic&&t.itemInfo?e("div",[t.itemInfo.descriptors&&t.itemInfo.descriptors.length?e("div",{staticClass:"text-grey card-title"},[e("span",{staticClass:"sig-chapter-title",attrs:{"data-cy":"characteristic-descriptors"}},[t._v("\n          Descriptors\n        ")])]):t._e(),t.itemInfo.descriptors&&t.itemInfo.descriptors.length?e("div",t._l(t.itemInfo.descriptors,(function(i,a){return e("div",{key:a},[e("div",{staticClass:"q-my-sm"},[e("div",{staticClass:"row items-center"},[e("q-icon",{attrs:{name:t.ICONS.descriptor.name,color:t.ICONS.descriptor.color,size:"md"}}),e("strong",{staticClass:"q-ml-xs"},[t._v(t._s(i.name))])],1),e("div",[e("SigDescriptionTableDescriptorInformation",{attrs:{"sig-item":t.getSigItem(i.type),"sig-descriptor-information":i}})],1)])])})),0):t._e()]):t.itemInfo||t.selectedItemType!==t.GATT_TYPES.characteristic&&t.selectedItemType!==t.GATT_TYPES.descriptor?t._e():e("div",[t.item.fields&&t.item.fields.length?e("div",{staticClass:"text-grey card-title"},[e("span",{staticClass:"sig-chapter-title",attrs:{"data-cy":"fields"}},[t._v("\n          Fields\n        ")])]):t._e(),t.item.fields&&t.item.fields.length?e("div",{staticClass:"q-my-xs"},t._l(t.item.fields,(function(t,i){return e("div",{key:i,staticClass:"row"},[e("SigField",{staticClass:"q-my-xs",attrs:{field:t}})],1)})),0):t._e()])])]):t._e()},Wa=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"text-grey card-title"},[e("span",{staticClass:"sig-chapter-title"},[t._v("Details")])])}],Ha=function(){var t=this,e=t._self._c;return e("q-markup-table",{attrs:{flat:"",bordered:"",dense:"","wrap-cells":""}},[e("tbody",[t.sigItem.uuid?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("UUID")]),e("td",[t._v("\n        "+t._s(32===t.sigItem.uuid.length?t.toUuidFormat(t.sigItem.uuid):t.sigItem.uuid)+"\n      ")])]):t._e(),e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("\n        Type\n      ")]),e("td",[e("div",{staticStyle:{"overflow-wrap":"anywhere"}},[t._v("\n          "+t._s(t.sigItem.type)+"\n        ")])])]),e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Source")]),e("td",[t._v("\n        "+t._s(t.source)+"\n      ")])]),t.sigItem.abstract&&t.sigItem.abstract.length?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Abstract")]),e("td",t._l(t.sigItem.abstract,(function(i,a){return e("div",{key:a,staticStyle:{"white-space":"pre-wrap"}},[t._v(t._s(i.trim()))])})),0)]):t._e(),t.sigItem.summary&&t.sigItem.summary.length?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Summary")]),e("td",t._l(t.sigItem.summary,(function(i,a){return e("div",{key:a,staticStyle:{"white-space":"pre-wrap"}},[t._v(t._s(i.trim()))])})),0)]):t._e(),t.sigItem.examples&&t.sigItem.examples.length?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Examples")]),e("td",t._l(t.sigItem.examples,(function(i,a){return e("div",{key:a,staticStyle:{"white-space":"pre-wrap"}},[t._v(t._s(i.trim()))])})),0)]):t._e(),t.sigItem.note?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Note")]),e("td",{staticStyle:{"white-space":"pre-wrap"}},[t._v(t._s(t.sigItem.note.trim()))])]):t._e(),t._t("default")],2)])},Ka=[],Xa={namespaced:!0,props:{sigItem:{type:Object,default:null}},data(){return{SIG_META:S}},computed:{source(){var t,e,i,a;return null!==(t=S.source_labels[null===(e=this.sigItem)||void 0===e?void 0:e.source])&&void 0!==t?t:null===(i=this.sigItem)||void 0===i||null===(a=i.source)||void 0===a?void 0:a.toUpperCase()}},methods:{toUuidFormat:de}},Ja=Xa,Za=Object(q["a"])(Ja,Ha,Ka,!1,null,null,null),ts=Za.exports;M()(Za,"components",{QMarkupTable:yi["a"]});var es=function(){var t=this,e=t._self._c;return e("SigDescriptionTable",t._b({},"SigDescriptionTable",t.$attrs,!1),[e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Declaration")]),e("td",[t._v("\n      "+t._s(t.sigServiceInformation.declaration)+"\n    ")])]),e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Requirement")]),e("td",[t._v("\n      "+t._s(t.sigServiceInformation.requirement)+"\n    ")])]),t.sigServiceInformation.includes&&t.sigServiceInformation.includes.length?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Includes")]),e("td",t._l(t.sigServiceInformation.includes,(function(i,a){return e("div",{key:a,staticStyle:{"overflow-wrap":"anywhere"}},[t._v(t._s(i.trim()))])})),0)]):t._e(),t.sigServiceInformation.characteristics&&t.sigServiceInformation.characteristics.length?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Characteristics")]),e("td",t._l(t.sigServiceInformation.characteristics,(function(i,a){return e("div",{key:a,staticStyle:{"overflow-wrap":"anywhere","white-space":"pre-wrap"}},[t._v(t._s(i.type+" - "+i.requirement))])})),0)]):t._e()])},is=[],as={namespaced:!0,components:{SigDescriptionTable:ts},props:{sigServiceInformation:{type:Object,default:null}}},ss=as,rs=Object(q["a"])(ss,es,is,!1,null,null,null),ns=rs.exports,ls=function(){var t=this,e=t._self._c;return e("SigDescriptionTable",t._b({},"SigDescriptionTable",t.$attrs,!1),[e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Requirement")]),e("td",[t._v("\n      "+t._s(t.sigCharacteristicInformation.requirement)+"\n    ")])]),e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Properties")]),e("td",[t._l(t.properties,(function(i,a,s){return e("div",{key:s,staticStyle:{"overflow-wrap":"anywhere","white-space":"pre-wrap"}},[t._v(t._s(a+" - "+i))])})),t.sigCharacteristicInformation.properties.informationText?e("div",{staticStyle:{"overflow-wrap":"anywhere","white-space":"pre-wrap"}},[t._v(" "+t._s(t.sigCharacteristicInformation.properties.informationText))]):t._e()],2)])])},os=[],cs={namespaced:!0,components:{SigDescriptionTable:ts},props:{sigCharacteristicInformation:{type:Object,default:null}},data(){return{}},computed:{properties(){const t=["SignedWrite","WritableAuxiliaries","Broadcast","ExtendedProperties"],e={};for(const i in this.sigCharacteristicInformation.properties.properties)t.includes(i)||(e[i]=this.sigCharacteristicInformation.properties.properties[i]);return e}}},ds=cs,us=Object(q["a"])(ds,ls,os,!1,null,null,null),ps=us.exports,hs=function(){var t=this,e=t._self._c;return e("SigDescriptionTable",t._b({},"SigDescriptionTable",t.$attrs,!1),[e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Requirement")]),e("td",{staticStyle:{"overflow-wrap":"anywhere"}},[t._v("\n      "+t._s(t.sigDescriptorInformation.requirement)+"\n    ")])]),e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Properties")]),e("td",t._l(t.sigDescriptorInformation.properties.properties,(function(i,a,s){return e("div",{key:s,staticStyle:{"overflow-wrap":"anywhere","white-space":"pre-wrap"}},[t._v(t._s(a+" - "+i))])})),0)])])},ms=[],gs={namespaced:!0,components:{SigDescriptionTable:ts},props:{sigDescriptorInformation:{type:Object,default:null}}},bs=gs,fs=Object(q["a"])(bs,hs,ms,!1,null,null,null),vs=fs.exports,ys=function(){var t=this,e=t._self._c;return e("q-markup-table",{attrs:{flat:"",bordered:"",dense:"","wrap-cells":""}},[e("tbody",[e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Name")]),e("td",[t._v(t._s(t.field.name))])]),e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Requirement")]),e("td",[e("div",{staticStyle:{"overflow-wrap":"anywhere"}},[t._v("\n          "+t._s(t.field.requirement)+"\n        ")])])]),t.field.reference?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Reference")]),e("td",[t._v("\n        "+t._s(t.field.reference)+"\n      ")])]):t._e(),t.field.format?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Format")]),e("td",[t._v("\n        "+t._s(t.field.format)+"\n      ")])]):t._e(),t.field.informativeText?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Info")]),e("td",[t._v("\n        "+t._s(t.field.informativeText)+"\n      ")])]):t._e(),t.field.unit?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Unit")]),e("td",[t._v("\n        "+t._s(t.field.unit)+"\n      ")])]):t._e(),t.field.decimalExponent?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("\n        Decimal Exponent\n      ")]),e("td",[t._v("\n        "+t._s(t.field.decimalExponent)+"\n      ")])]):t._e(),t.bitfield?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Bitfield")]),e("td",[e("div",{staticStyle:{"white-space":"pre-wrap"}},[t._v(t._s(t.bitfield))])])]):t._e(),t.enumerations?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Enumerations")]),e("td",[e("div",{staticStyle:{"white-space":"pre-wrap"}},[t._v(t._s(t.enumerations))])])]):t._e(),t.field.minimum?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Minimum")]),e("td",[t._v("\n        "+t._s(t.field.minimum)+"\n      ")])]):t._e(),t.field.maximum?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Maximum")]),e("td",[t._v("\n        "+t._s(t.field.maximum)+"\n      ")])]):t._e(),t.field.repeated?e("tr",[e("td",{staticClass:"text-left text-bold"},[t._v("Repeated")]),e("td",[t._v("\n        "+t._s(t.field.repeated)+"\n      ")])]):t._e()])])},Is=[],_s={namespaced:!0,props:{field:{type:Object,default:null}},data(){return{}},computed:{enumerations(){return this.generateEnumerationsStr(this.field.enumerations)},bitfield(){let t="";if(this.field.bitfield){if(this.field.bitfield.bits&&this.field.bitfield.bits.length)for(const e of this.field.bitfield.bits)t+=`Index: ${e.index}, Size: ${e.size}\n`+this.generateEnumerationsStr(e.enumerations)+"\n";if(this.field.bitfield.reserved&&this.field.bitfield.reserved.length)for(const e of this.field.bitfield.reserved)t+=`Reserved Bitfield index: ${e.index}, size: ${e.size}\n`}return t}},methods:{generateEnumerationsStr(t){let e="";if(t){if(t.enumerations&&t.enumerations.length)for(const i of t.enumerations)e+=`Key: ${i.key}, Value: ${i.value}`+(i.requires?` Requires: ${i.requires}`:"")+(i.description?` Description: ${i.description}`:"")+"\n";if(t.reserved&&t.reserved.length)for(const i of t.reserved)e+=`Reserved start: ${i.start}, Reserved end: ${i.end}\n`;t.informativeText&&(e+=t.informativeText)}return e}}},Ts=_s,Cs=Object(q["a"])(Ts,ys,Is,!1,null,null,null),ws=Cs.exports;M()(Cs,"components",{QMarkupTable:yi["a"]});var Ss={namespaced:!0,components:{SigDescriptionTable:ts,SigDescriptionTableServiceInformation:ns,SigDescriptionTableCharacteristicInformation:ps,SigDescriptionTableDescriptorInformation:vs,SigField:ws},props:{tab:{type:String,default:null},types:{type:Array,default:()=>[]},selected:{type:Object,default:null}},data(){return{GATT_TYPES:g["a"],ICONS:y,sigItemContainer:null}},computed:{selectedItemType(){var t;return null===(t=this.sigItemContainer)||void 0===t?void 0:t.gattType},item(){var t;return null===(t=this.sigItemContainer)||void 0===t?void 0:t.sigItem},itemInfo(){var t;return null===(t=this.sigItemContainer)||void 0===t?void 0:t.sigInformation}},watch:{selected(){this.sigItemContainer=$a(this.tab,this.types)}},created(){this.sigItemContainer=$a(this.tab,this.types)},methods:{getSigItem(t){var e,i,a;return null!==(e=null!==(i=null!==(a=ea["SigMetadata"].profiles.get(t))&&void 0!==a?a:ea["SigMetadata"].services.get(t))&&void 0!==i?i:ea["SigMetadata"].characteristics.get(t))&&void 0!==e?e:ea["SigMetadata"].descriptors.get(t)}}},xs=Ss,As=Object(q["a"])(xs,Ya,Wa,!1,null,null,null),Os=As.exports;M()(As,"components",{QIcon:k["a"]});var qs=i("7937");const{capitalize:ks}=qs["b"];function Ds(t,e){return t.reduce(((t,i)=>{if(i.children){let a=Ds(i.children,e);if(a.length)return t.push({...i,children:a,gattType:i.gattType}),t}return i.name.toLowerCase().includes(e)&&t.push(i),t}),[])}function Es(t,e,i){let a=t.filter((t=>i.includes(t.source)));return e&&(a=Ds(a,e.toLowerCase())),a}var Ps={namespaced:!0,components:{DragTree:J,GattTreeItem:rt,SigInfo:Os},data(){return{GATT_TYPES:g["a"],ICON_BUTTON_PROPS:f,ICONS:y,splitterModel:50,tab:g["a"].profile,filterText:"",selected:null,info:"",sourceFilter:null,groups:{[g["a"].profile]:[{name:g["a"].service,pull:"clone",put:!1},{name:g["a"].service,pull:"clone",put:!1}],[g["a"].service]:[{name:g["a"].service,pull:"clone",put:!1},{name:g["a"].characteristic,pull:"clone",put:!1},{name:g["a"].descriptor,pull:"clone",put:!1}],[g["a"].characteristic]:[{name:g["a"].characteristic,pull:"clone",put:!1}],[g["a"].descriptor]:[{name:g["a"].descriptor,pull:"clone",put:!1}]}}},computed:{...Object(p["mapGetters"])("Sig",["sigTreeData","sources"]),...Object(p["mapGetters"])("GattConfiguratorApp",["selectedItem"]),sourceOptions(){return this.sources.map((t=>({value:t,label:S.source_labels[t]||t})))},tabOptions(){return Object.keys(g["a"]).map((t=>({value:t,label:ks(t)+"s"})))},profiles(){return Es(this.sigTreeData.profiles,this.filterText,this.sourceFilter)},services(){return Es(this.sigTreeData.services,this.filterText,this.sourceFilter)},characteristics(){return Es(this.sigTreeData.characteristics,this.filterText,this.sourceFilter)},descriptors(){return Es(this.sigTreeData.descriptors,this.filterText,this.sourceFilter)},sigTree(){return{[g["a"].profile]:this.profiles,[g["a"].service]:this.services,[g["a"].characteristic]:this.characteristics,[g["a"].descriptor]:this.descriptors}},selectedSigItem(){var t,e,i;return null!==(t=null!==(e=null!==(i=ea["SigMetadata"].profiles.get(this.selected.type))&&void 0!==i?i:ea["SigMetadata"].services.get(this.selected.type))&&void 0!==e?e:ea["SigMetadata"].characteristics.get(this.selected.type))&&void 0!==t?t:ea["SigMetadata"].descriptors.get(this.selected.type)}},created(){this.sourceFilter=this.sources,this.selected=this.sigTree[this.tab][0]},methods:{...Object(p["mapActions"])("GattConfiguratorApp",["addGattItem"]),...Object(p["mapActions"])("Sig",["setDraggedItemPromise"]),toUuidFormat:de,getTypes(t){let e=t,i=[];while(e)i.unshift(e.type),e=e.parent;return i},addSigItem(t){const e=this.getTypes(t);if(0==e.length)return;const i=za(this.tab,e);if(ht["default"].debug("SIG Gatt item:"),ht["default"].debug(i,!0),null==this.selectedItem.sourceFile||null!=this.selectedItem.sourceFile&&(i.gattType==g["a"].profile||i.gattType==g["a"].service)){var a,s=0;i.gattType==g["a"].descriptor?this.selectedItem.characteristics?(a=Object(Ct["d"])(this.selectedItem.characteristics[0].name),s=Object(Ct["a"])(i.gattItem.name,this.selectedItem.characteristics[0].descriptors)):this.selectedItem.descriptors?(a=Object(Ct["d"])(this.selectedItem.name),s=Object(Ct["a"])(i.gattItem.name,this.selectedItem.descriptors)):(a=Object(Ct["d"])(this.selectedItem.parent.name),s=Object(Ct["a"])(i.gattItem.name,this.selectedItem.parent.descriptors)):i.gattType==g["a"].characteristic&&(s=this.selectedItem.characteristics?Object(Ct["a"])(i.gattItem.name,this.selectedItem.characteristics):this.selectedItem.descriptors?Object(Ct["a"])(i.gattItem.name,this.selectedItem.parent.characteristics):Object(Ct["a"])(i.gattItem.name,this.selectedItem.parent.parent.characteristics)),a&&(i.gattItem.id=a+"_"+i.gattItem.id),i.gattItem.id=i.gattItem.id+"_"+s,this.addGattItem(i);const t=vt(ft(i));null!=(null===t||void 0===t?void 0:t.title)&&this.$q.notify(gt["Notifications"].qNotificationHtmlObj(gt["Notifications"].notificationHtmlMsg(t)))}},clone(t){return{gattType:this.tab,types:this.getTypes(t)}},preFetchDraggedItem(t){const e=this.getTypes(t);0!=e.length&&this.setDraggedItemPromise(new Promise((t=>{const i=za(this.tab,e);t(i)})))}}},Ms=Ps,Vs=(i("7510"),i("9f0a")),Gs=i("adad"),Ns=i("823b"),Rs=Object(q["a"])(Ms,La,Ba,!1,null,null,null),js=Rs.exports;M()(Rs,"components",{QCard:Ut["a"],QBtnToggle:ra["a"],QSeparator:xt["a"],QInput:Di["a"],QIcon:k["a"],QBtn:Gt["a"],QMenu:Nt["a"],QOptionGroup:Vs["a"],QTooltip:at["a"],QScrollArea:Li["a"],QTabPanels:Gs["a"],QTabPanel:Ns["a"]});var Fs=i("eca4"),Us=function(){var t=this,e=t._self._c;return e("div",{attrs:{id:`validator_${t.gattItem.gattType}_${t.gattItem.vueId}`}},t._l(t.gattItem.services,(function(t){return e("ServiceValidators",{key:t.vueId,attrs:{"gatt-item":t}})})),1)},Ls=[],Bs=function(){var t=this,e=t._self._c;return e("div",{attrs:{id:`validator_${t.gattItem.gattType}_${t.gattItem.vueId}`}},t._l(t.gattItem.characteristics,(function(t){return e("CharacteristicValidators",{key:t.vueId,attrs:{"gatt-item":t}})})),1)},Qs=[],$s=function(){var t=this,e=t._self._c;return e("div",{attrs:{id:`validator_${t.gattItem.gattType}_${t.gattItem.vueId}`}},t._l(t.gattItem.descriptors,(function(t){return e("DescriptorValidators",{key:t.vueId,attrs:{"gatt-item":t}})})),1)},zs=[],Ys=function(){var t=this,e=t._self._c;return e("div",{attrs:{id:`validator_${t.gattItem.gattType}_${t.gattItem.vueId}`}})},Ws=[],Hs={props:{gattItem:{type:Object,required:!0}},computed:{...oi("descriptor")},mounted(){for(let t in this.$options.computed)this.$watch(t,(e=>{ni(this.gattItem,t,e)}),{immediate:!0})}},Ks=Hs,Xs=Object(q["a"])(Ks,Ys,Ws,!1,null,null,null),Js=Xs.exports,Zs={components:{DescriptorValidators:Js},props:{gattItem:{type:Object,required:!0}},computed:{...oi("characteristic")},mounted(){for(let t in this.$options.computed)this.$watch(t,(e=>{ni(this.gattItem,t,e)}),{immediate:!0})}},tr=Zs,er=Object(q["a"])(tr,$s,zs,!1,null,null,null),ir=er.exports,ar={components:{CharacteristicValidators:ir},props:{gattItem:{type:Object,required:!0}},computed:{...oi("service")},mounted(){for(let t in this.$options.computed)this.$watch(t,(e=>{ni(this.gattItem,t,e)}),{immediate:!0})}},sr=ar,rr=Object(q["a"])(sr,Bs,Qs,!1,null,null,null),nr=rr.exports,lr={components:{ServiceValidators:nr},props:{gattItem:{type:Object,required:!0}},computed:{...oi("profile")},mounted(){for(let t in this.$options.computed)this.$watch(t,(e=>{ni(this.gattItem,t,e)}),{immediate:!0})}},or=lr,cr=Object(q["a"])(or,Us,Ls,!1,null,null,null),dr=cr.exports,ur=(i("a1ac"),function(){var t=this,e=t._self._c;t._self._setupProxy;return e("WaspHideableDialog",{ref:"manual-dialog",attrs:{"container-data-cy":"manual-dialog",visible:t.manualVisible,"card-style":"min-width: 90vw;","hide-bottom-bar":""},on:{hide:t.hide,show:t.onShow},scopedSlots:t._u([{key:"title",fn:function(){return[e("span",{staticClass:"text-h6"},[t._v("Manual")])]},proxy:!0},{key:"content",fn:function(){return[e("div",{staticClass:"row no-wrap",staticStyle:{margin:"-16px"}},[e("div",{staticClass:"col-auto",staticStyle:{"max-width":"300px"}},[e("div",{staticClass:"scroll q-ma-md",staticStyle:{"max-height":"90vh"}},t._l(t.toc,(function(i){return e("ManualToc",{key:i.number,attrs:{toc:i},on:{onId:t.goTo}})})),1)]),e("q-separator",{attrs:{vertical:""}}),e("div",{staticClass:"col"},[e("div",{staticClass:"q-pa-md scroll manual",staticStyle:{"max-height":"85vh"},domProps:{innerHTML:t._s(t.html)}})])],1)]},proxy:!0}])})}),pr=[],hr=i("e932"),mr=i("10a3"),gr=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{style:`margin-left: ${t.left}px;`},[e("div",{staticClass:"title-toc text-primary cursor-pointer row no-wrap q-mb-xs",on:{click:function(e){return t.onClick(t.toc.number)}}},[e("span",{staticClass:"q-pr-xs"},[t._v(" "+t._s(t.toc.number))]),e("span",{staticStyle:{"white-space":"normal"}},[t._v(t._s(t.toc.name))])]),t._l(t.toc.children,(function(i){return e("div",{key:i.number},[e("ManualToc",{attrs:{toc:i,level:t.level+1},on:{onId:t.onClick}})],1)}))],2)},br=[],fr=Object(a["defineComponent"])({name:"ManualToc",props:{toc:{type:Object,required:!0},level:{type:Number,default:1}},data(){return{height:100}},computed:{left(){return 10*(this.level-1)}},methods:{onClick(t){this.$emit("onId",t)}}}),vr=fr,yr=(i("b3aa"),Object(q["a"])(vr,gr,br,!1,null,null,null)),Ir=yr.exports,_r=i("0831");const{getScrollTarget:Tr,setScrollPosition:Cr}=_r["a"];var wr=Object(a["defineComponent"])({name:"Manual",components:{WaspHideableDialog:mr["WaspHideableDialog"],ManualToc:Ir},data:()=>({html:hr["html"],toc:hr["toc"]}),computed:{...Object(p["mapGetters"])("Manual",["manualTarget","manualVisible"])},watch:{manualVisible:function(t){t&&this.show()}},updated:function(){this.$nextTick((function(){null!=this.manualTarget&&this.navTo(this.manualTarget,!1)}))},methods:{...Object(p["mapActions"])("Manual",["setManualTarget","setManualVisible"]),show(){this.setManualVisible(!0)},hide(){this.setManualVisible(!1)},onShow(){this.setManualTarget(void 0)},goTo(t){const e=/\./gm;this.navTo(`markdowntitle-${t.replace(e,"-")}`)},navTo(t,e=!0){const i=document.getElementById(t);if(null===i)return;const a=Tr(i),s=i.offsetTop-10,r=500;Cr(a,s,e?r:void 0)}}}),Sr=wr,xr=(i("8eba"),Object(q["a"])(Sr,ur,pr,!1,null,null,null)),Ar=xr.exports;M()(xr,"components",{QSeparator:xt["a"]});const Or=!1;let qr;Or&&(qr=i("0836").openConfig);var kr={components:{ActivePageTitle:G,GattTree:Ot,ButtonBar:zt,ProfileLayout:Pi,ServiceLayout:Wi,CharacteristicLayout:qa,DescriptorLayout:Ua,SigLayout:js,Manual:Ar,ProfileValidators:dr,UpgradeRuleDialog:Fs["UpgradeRuleDialog"]},data:()=>({GATT_TYPES:g["a"],BUTTON_PROPS:v,mini:!1,standalone:Or,platformHandler:Ri["a"],openConfig:qr}),computed:{...Object(p["mapGetters"])("MainView",["dirty","sigLayout","dialogOn"]),...Object(p["mapGetters"])("GattConfiguratorApp",["gatt","mergedGatt","selectedItem"]),layout(){return this.selectedItem.gattType}},methods:{...Object(p["mapActions"])("MainView",["setSigLayout","setData"]),...Object(p["mapActions"])("GattConfiguratorApp",["setSelectedItem"]),onScroll(t){if(!this.sigLayout&&!this.dialogOn){let e=W(t,this.selectedItem,this.mergedGatt);null!=e&&this.setSelectedItem(e)}},showManual(){this.$refs["manual-dialog"].show()},async generate(){await Ri["a"].generate("")}}},Dr=kr,Er=i("4d5a"),Pr=i("e359"),Mr=i("9404"),Vr=i("09e3"),Gr=i("9989"),Nr=Object(q["a"])(Dr,d,u,!1,null,null,null),Rr=Nr.exports;M()(Nr,"components",{QLayout:Er["a"],QHeader:Pr["a"],QSeparator:xt["a"],QBtn:Gt["a"],QDrawer:Mr["a"],QScrollArea:Li["a"],QPageContainer:Vr["a"],QPage:Gr["a"]});i("5993");var jr={components:{AppWindow:Rr}},Fr=jr,Ur=Object(q["a"])(Fr,o,c,!1,null,null,null),Lr=Ur.exports,Br=i("a18c"),Qr=i.n(Br),$r=async function(){const t="function"===typeof le["a"]?await Object(le["a"])({Vue:a["default"]}):le["a"],e="function"===typeof Qr.a?await Qr()({Vue:a["default"],store:t}):Qr.a;t.$router=e;const i={router:e,store:t,render:t=>t(Lr),el:"#q-app"};return{app:i,store:t,router:e}},zr=i("bc3a"),Yr=i.n(zr);a["default"].prototype.$axios=Yr.a;var Wr=({app:t})=>{window.Cypress&&(window.app=t,ht["default"].info("DEV or Cypress mode detected, app is available"))},Hr=i("4bde"),Kr=i("ff52"),Xr=i("b6cb");document.addEventListener("keydown",(async t=>{(ue()?t.metaKey:t.ctrlKey)&&("s"===t.key?(t.preventDefault(),Ri["a"].onSaveEvent()):"o"===t.key&&(t.preventDefault(),await Object(Pt["openConfig"])()))}));const Jr=i("853f").default;var Zr=Object(Hr["boot"])((async({store:t})=>{try{let e=await Ri["a"].loadConfiguration();const i=Xr["a"].load(e);Jr.info({gatt_config:e},!0),t.dispatch("MainView/setData",i,{root:!0})}catch(e){Jr.error("Configuration loading failed"),Jr.error(e,!0)}Ri["a"].onThemeChanged=t=>{Jr.info("Theme change to "+t),"dark"==t&&Kr["a"].set(!0),"light"==t&&Kr["a"].set(!1)},Ri["a"].onSaveEvent=async()=>{Jr.info("Save event");const e=ea["GattXmlTranslator"].gattToXml(new ea["Gatt"](t.getters["GattConfiguratorApp/gatt"]));try{await Ri["a"].saveConfiguration(e),t.dispatch("MainView/setDirty",!1,{root:!0}),Jr.info("Configuration has been saved")}catch(Xe){Jr.info("Save did not happen")}},Ri["a"].onContributionsEvent=()=>{Jr.info("Contributions have been changed."),Ri["a"].loadContributions().then((e=>{Jr.info(e,!0),t.dispatch("MainView/setContributions",e,{root:!0})}))},Ri["a"].onGenerationResult(),Ri["a"].onContributionsEvent();try{await Ri["a"].init()}catch(Xe){Jr.error(Xe,!0)}t.dispatch("init")}));const tn="";async function en(){const{app:t,store:e,router:i}=await $r();let s=!1;const r=t=>{s=!0;const e=Object(t)===t?i.resolve(t).route.fullPath:t;window.location.href=e},n=window.location.href.replace(window.location.origin,""),l=[void 0,void 0,Wr,Zr];for(let c=0;!1===s&&c<l.length;c++)if("function"===typeof l[c])try{await l[c]({app:t,router:i,store:e,Vue:a["default"],ssrContext:null,redirect:r,urlPath:n,publicPath:tn})}catch(o){return o&&o.url?void(window.location.href=o.url):void console.error("[Quasar] boot error:",o)}!0!==s&&new a["default"](t)}en()},"305b":function(t,e,i){},"31cd":function(t,e,i){},"3b2f":function(t,e,i){"use strict";i("b29f")},4360:function(t,e,i){"use strict";var a=i("2b0e"),s=i("2f62"),r=i("eca4");const n={target:void 0,visible:!1},l={manualTarget(t){return t.target},manualVisible(t){return t.visible}},o={setManualTarget({commit:t},e){t("SET_MANUAL_TARGET",e)},setManualVisible({commit:t},e){t("SET_MANUAL_VISIBLE",e)}},c={SET_MANUAL_TARGET(t,e){t.target=e},SET_MANUAL_VISIBLE(t,e){t.visible=e}};var d={namespaced:!0,state:n,getters:l,actions:o,mutations:c},u=i("a3a1"),p=i("853f"),h=i("1732"),m=i("da4a"),g=i("a883");const b="temp-instance-id-",f={dirty:!1,sigLayout:!1,dialogOn:!1},v={dirty(t){return t.dirty},sigLayout(t){return t.sigLayout},dialogOn(t){return t.dialogOn}},y={init:{root:!0,handler({dispatch:t}){t("setSigTreeData")}},setDirty({commit:t,state:e},i){e.dirty!=i&&(g["a"].setDirty(i),t("SET_DIRTY",i))},setSigLayout({commit:t},e){t("SET_SIG_LAYOUT",e)},setDialogOn({commit:t},e){t("SET_DIALOG_ON",e)},setData({commit:t,dispatch:e},i){null!=i.upgradeResults&&i.upgradeResults.length>0&&(e("setDirty",!0),t("UpgradeRule/SET_UPGRADE_DIALOG_ON",!0,{root:!0})),t("GattConfiguratorApp/SET_GATT",i,{root:!0}),t("GattConfiguratorApp/SET_SELECTED_ITEM",i,{root:!0})},setContributions({dispatch:t},e){if(t("GattConfiguratorApp/clearContributions",void 0,{root:!0}),Array.isArray(e))for(const i of e)if(i.filename&&i.content){const e=m["GattXmlTranslator"].xmlToGatt(i.content);let a=e.services;if(a&&Array.isArray(a))for(let t of a)t.instanceId||(t.instanceId=b+Object(h["a"])());const s=i.filename,r=new u["g"]({...e,sourceFile:s});t("GattConfiguratorApp/addContribution",{sourceFile:s,gatt:r},{root:!0})}},setSigTreeData({dispatch:t}){p["default"].time("sig");let e={};e.profiles=Array.from(m["SigMetadata"].profiles).map((([,t])=>({name:t.name,type:t.type,source:t.source,services:t.services.map((t=>{var e;return{name:null===(e=m["SigMetadata"].services.get(t.type))||void 0===e?void 0:e.name,type:t.type}}))}))).sort(((t,e)=>t.name.localeCompare(e.name))),e.services=Array.from(m["SigMetadata"].services).map((([,t])=>({name:t.name,type:t.type,source:t.source,characteristics:t.characteristics.map((t=>{var e,i;return{name:null===(e=m["SigMetadata"].characteristics.get(t.type))||void 0===e?void 0:e.name,type:t.type,descriptors:null===(i=t.descriptors)||void 0===i?void 0:i.map((t=>{var e;return{name:null===(e=m["SigMetadata"].descriptors.get(t.type))||void 0===e?void 0:e.name,type:t.type}}))}}))}))).sort(((t,e)=>t.name.localeCompare(e.name))),e.characteristics=Array.from(m["SigMetadata"].characteristics).map((([,t])=>({name:t.name,type:t.type,source:t.source}))).sort(((t,e)=>t.name.localeCompare(e.name))),e.descriptors=Array.from(m["SigMetadata"].descriptors).map((([,t])=>({name:t.name,type:t.type,source:t.source}))).sort(((t,e)=>t.name.localeCompare(e.name))),t("Sig/setSigTreeData",e,{root:!0}),p["default"].timeEnd("sig")}},I={SET_DIRTY(t,e){t.dirty=e},SET_SIG_LAYOUT(t,e){t.sigLayout=e},SET_DIALOG_ON(t,e){t.dialogOn=e}};var _={namespaced:!0,state:f,getters:v,actions:y,mutations:I},T=(i("14d9"),i("19e1"));class C{constructor(){this._idSet=new Set,this._duplicatedIdSet=new Set}visit(t){t.hasOwnProperty("id")&&void 0!=t.id&&(this._idSet.has(t.id)?this._duplicatedIdSet.add(t.id):this._idSet.add(t.id))}getDuplicatedIds(){return this._duplicatedIdSet}}var w=i("7d5e");function S(t,e){if(!t.children||!e.children)return;let i=t.children,a=e.children;for(const s of i){if(s.instanceId){const t=a.findIndex((t=>t.instanceId==s.instanceId));if(-1!=t){S(s,a[t]);continue}}s.parent=e,a.push(s)}}const x={gatt:null,contributions:{},selectedItem:null},A={gatt(t){return t.gatt},selectedItem(t){return t.selectedItem},itemByVueId:(t,e)=>i=>{if(t.gatt.vueId==i)return t.gatt;for(let t of e.mergedGatt.services){if(t.vueId==i)return t;for(let e of t.characteristics){if(e.vueId==i)return e;for(let t of e.descriptors)if(t.vueId==i)return t}}},serviceContributions(t){p["default"].debug("contributions"),p["default"].debug(t.contributions,!0);let e=new u["g"](Object(u["c"])());for(let i of Object.keys(t.contributions).sort())S(new u["g"](t.contributions[i]),e);return e.services.forEach((e=>e.parent=t.gatt)),e.services},capabilityContributions(t){let e=Object.keys(t.contributions).sort().map((e=>t.contributions[e].capabilityDeclarations)).flat();for(let i=e.length-1;i>=0;i--)e.findIndex((t=>t.name==e[i].name))!=i&&e.splice(i,1);return e},mergedGatt(t,e){const i=[...t.gatt.services,...e.serviceContributions],a=[...t.gatt.capabilityDeclarations,...e.capabilityContributions];return Object.assign(new u["g"](Object(u["c"])()),{...t.gatt,services:i,children:i,capabilityDeclarations:a})},serviceIds(t,e){return[...t.gatt.services,...e.serviceContributions].map((t=>t.id)).filter((t=>!!t))},duplicatedIds(t,e){let i=new C;return e.mergedGatt.accept(i),i.getDuplicatedIds()},capabilityDeclarationNames(t,e){return[...t.gatt.capabilityDeclarations,...e.capabilityContributions].map((t=>t.name))}},O={setSelectedItem({commit:t},e){t("SET_SELECTED_ITEM",e)},setGatt({commit:t},e){t("SET_GATT",e)},setValue({commit:t,dispatch:e},i){t("SET_VALUE",i),e("MainView/setDirty",!0,{root:!0})},setValueNoDirty({commit:t},e){t("SET_VALUE",e)},setChildren({commit:t,dispatch:e},i){t("SET_CHILDREN",i),e("MainView/setDirty",!0,{root:!0})},removeSelectedItem({commit:t,dispatch:e}){t("REMOVE_SELECTED_ITEM"),e("maintainServiceIncludes"),e("MainView/setDirty",!0,{root:!0})},duplicateSelectedItem({getters:t,dispatch:e}){e("duplicateItem",t.selectedItem)},duplicateItem({commit:t,dispatch:e},i){t("DUPLICATE_ITEM",i),e("MainView/setDirty",!0,{root:!0})},moveUpSelectedItem({commit:t,dispatch:e}){t("MOVE_UP_SELECTED_ITEM"),e("MainView/setDirty",!0,{root:!0})},moveDownSelectedItem({commit:t,dispatch:e}){t("MOVE_DOWN_SELECTED_ITEM"),e("MainView/setDirty",!0,{root:!0})},addGattItem({commit:t,dispatch:e},i){t("ADD_GATT_ITEM",i),e("MainView/setDirty",!0,{root:!0})},addContribution({commit:t},{sourceFile:e,gatt:i}){t("ADD_CONTRIBUTION",{sourceFile:e,gatt:i})},clearContributions({commit:t}){t("CLEAR_CONTRIBUTIONS")},addArrayItem({commit:t,dispatch:e},i){t("ADD_ARRAY_ITEM",i),e("MainView/setDirty",!0,{root:!0})},removeArrayItem({commit:t,dispatch:e},i){t("REMOVE_ARRAY_ITEM",i),e("MainView/setDirty",!0,{root:!0})},maintainCapabilities({commit:t,getters:e},i){t("MAINTAIN_CAPABILITIES",{capabilityDeclarationNames:e.capabilityDeclarationNames,...i})},maintainServiceIncludes({commit:t,getters:e},i){t("MAINTAIN_SERVICE_INCLUDES",{serviceIds:e.serviceIds,...i})},makeContributionEditable({state:t,dispatch:e},i){if(i.gattType!==T["a"].service)return;function a(t,e){return"sourceFile"===t||"parent"===t?void 0:e}e("duplicateItem",i);let s=JSON.parse(JSON.stringify(t.contributions,a));s=Object.entries(s).map((([t,e])=>({filename:t,content:e})));let r=s.filter((t=>{var e,a;return(null===(e=t.content)||void 0===e||null===(a=e.services)||void 0===a?void 0:a.filter((t=>!!i.instanceId&&t.instanceId===i.instanceId)).length)>0})),n=r.map((t=>(t.content.services&&(t.content.services=t.content.services.filter((t=>!i.instanceId||t.instanceId!=i.instanceId))),t)));for(let c of n){var l;let t=null===(l=c.content)||void 0===l?void 0:l.services;for(let e of t)"string"===typeof e.instanceId&&e.instance_ids.startsWith(b)&&delete e.instanceId}const o=[];for(const c of n)o.push({filename:c.filename,content:m["GattXmlTranslator"].gattToXml(new m["Gatt"](c.content))});g["a"].saveContributions(o),g["a"].onSaveEvent()}},q={SET_GATT(t,e){t.gatt=e},SET_SELECTED_ITEM(t,e){t.selectedItem=e},SET_VALUE(t,{object:e,key:i,value:a}){e[i]=a},SET_CHILDREN(t,{object:e,children:i}){e.children=i},REMOVE_SELECTED_ITEM(t){if(!t.selectedItem["parent"])return;let e=t.selectedItem.parent,i=e.children,a=i.indexOf(t.selectedItem),s=i.length-1;t.selectedItem=0==s?e:a<s?i[a+1]:i[a-1],i.splice(a,1)},DUPLICATE_ITEM(t,e){let i=e.parent,a=i.children;function s(t,e){return"sourceFile"===t||"parent"===t||"instanceId"==t?void 0:e}let r=JSON.parse(JSON.stringify(e,s));switch(e.gattType){case T["a"].service:a.push(new u["h"](r,i));break;case T["a"].characteristic:a.push(new u["e"](r,i));break;case T["a"].descriptor:a.push(new u["f"](r,i));break;default:break}},MOVE_UP_SELECTED_ITEM(t){let e=t.selectedItem,i=e.parent.children,a=i.indexOf(e);a>0&&(i.splice(a,1),i.splice(a-1,0,e))},MOVE_DOWN_SELECTED_ITEM(t){let e=t.selectedItem,i=e.parent.children,a=i.indexOf(e);a+1<i.length&&(i.splice(a,1),i.splice(a+1,0,e))},ADD_GATT_ITEM(t,{gattType:e,gattItem:i}){if(null==e)return void p["default"].error("Gatt type is null");if(null==i)return void p["default"].error("SIG Gatt item is null");Object(w["b"])(e,i);let a=t.gatt,s=t.selectedItem;switch(e){case T["a"].profile:for(let t of i.services)a.services.push(new u["h"](t,a));break;case T["a"].service:a.services.push(new u["h"](i,a));break;case T["a"].characteristic:if(null!=s.sourceFile)break;switch(s.gattType){case T["a"].profile:0==a.services.length&&a.services.push(new u["h"](Object(u["d"])(),a)),a.services[0].characteristics.push(new u["e"](i,a.services[0]));break;case T["a"].service:s.characteristics.push(new u["e"](i,s));break;case T["a"].characteristic:s.parent.characteristics.push(new u["e"](i,s.parent));break;case T["a"].descriptor:s.parent.parent.characteristics.push(new u["e"](i,s.parent.parent));break}break;case T["a"].descriptor:if(null!=s.sourceFile)break;switch(s.gattType){case T["a"].profile:0==a.services.length&&a.services.push(new u["h"](Object(u["d"])(),a)),0==a.services[0].characteristics.length&&a.services[0].characteristics.push(new u["e"](Object(u["a"])(),a.services[0])),a.services[0].characteristics[0].descriptors.push(new u["f"](i,a.services[0].characteristics[0]));break;case T["a"].service:0==s.characteristics.length&&s.characteristics.push(new u["e"](Object(u["a"])(),s)),s.characteristics[0].descriptors.push(new u["f"](i,s.characteristics[0]));break;case T["a"].characteristic:s.descriptors.push(new u["f"](i,s));break;case T["a"].descriptor:s.parent.descriptors.push(new u["f"](i,s.parent));break}break}},ADD_CONTRIBUTION(t,{sourceFile:e,gatt:i}){i.services.forEach((e=>{e.parent=t.gatt})),a["default"].set(t.contributions,e,i)},CLEAR_CONTRIBUTIONS(t){t.contributions={}},ADD_ARRAY_ITEM(t,{array_object:e,item:i}){e.push(i)},REMOVE_ARRAY_ITEM(t,{array_object:e,index:i}){e.splice(i,1)},MAINTAIN_CAPABILITIES(t,{capabilityDeclarationNames:e,oldVal:i=null,newVal:a=null}){t.gatt.accept({visit(t){if(t.capabilities){if(i&&a){const e=t.capabilities.indexOf(i);-1!==e&&(t.capabilities[e]=a)}t.gattType==T["a"].characteristic&&t.parent.capabilities.length>0?t.capabilities=t.capabilities.filter((e=>t.parent.capabilities.includes(e))):t.capabilities=t.capabilities.filter((t=>e.includes(t)))}}})},MAINTAIN_SERVICE_INCLUDES(t,{serviceIds:e,oldVal:i=null,newVal:a=null}){for(const s of t.gatt.services){if(i&&a){const t=s.includes.indexOf(i);-1!==t&&(s.includes[t]=a)}s.includes=s.includes.filter((t=>e.includes(t)))}}};var k={namespaced:!0,state:x,getters:A,actions:O,mutations:q};function D({name:t,type:e,source:i=null,services:a=[]}){this.name=t,this.type=e,this.source=i,this.services=a.map((t=>new E(t,this))),this.vueId=Object(h["a"])(),this.children=this.services,this.services.forEach((t=>t["vueId"]=Object(h["a"])()))}function E({name:t,type:e,source:i=null,characteristics:a=[]},s=null){this.name=t,this.type=e,this.source=i,this.characteristics=a.map((t=>new P(t,this))),this.vueId=Object(h["a"])(),this.parent=s,this.children=this.characteristics}function P({name:t,type:e,source:i=null,descriptors:a=[]},s=null){this.name=t,this.type=e,this.source=i,this.descriptors=a.map((t=>new M(t,this))),this.vueId=Object(h["a"])(),this.parent=s,this.children=this.descriptors}function M({name:t,type:e,source:i=null},a=null){this.name=t,this.type=e,this.source=i,this.vueId=Object(h["a"])(),this.parent=a}D.prototype.gattType=T["a"].profile,E.prototype.gattType=T["a"].service,P.prototype.gattType=T["a"].characteristic,M.prototype.gattType=T["a"].descriptor;const V={sigTreeData:null,draggedItemPromise:null},G={sigTreeData(t){return t.sigTreeData},draggedItemPromise(t){return t.draggedItemPromise},sources(t){let e=new Set;for(const i of Object.values(t.sigTreeData))for(const t of i)e.add(t.source);return Array.from(e)}},N={setSigTreeData({commit:t},e){let i={profiles:e.profiles.map((t=>new D(t))),services:e.services.map((t=>(Object(w["c"])(t),new E(t)))),characteristics:e.characteristics.map((t=>new P(t))),descriptors:e.descriptors.map((t=>new M(t)))};p["default"].info("setSigTreeData");const a="setSigTreeData:\n        SIG profiles: "+i.profiles.length.toString()+"\n        SIG services: "+i.services.length.toString()+"\n        SIG characteristics: "+i.characteristics.length.toString()+"\n        SIG descriptors: "+i.descriptors.length.toString();p["default"].info(a),t("SET_SIG_TREE_DATA",i)},setDraggedItemPromise({commit:t},e){t("SET_DRAGGED_ITEM_PROMISE",e)}},R={SET_SIG_TREE_DATA(t,e){t.sigTreeData=e},SET_DRAGGED_ITEM_PROMISE(t,e){t.draggedItemPromise=e}};var j={namespaced:!0,state:V,getters:G,actions:N,mutations:R},F=i("f174"),U=i.n(F);a["default"].use(s["default"]),a["default"].use(U.a);const L=new s["default"].Store({modules:{Manual:d,UpgradeRule:r["UpgradeRule"],MainView:_,GattConfiguratorApp:k,Sig:j},actions:{init:()=>{}}});e["a"]=L},"640b":function(t,e,i){},7510:function(t,e,i){"use strict";i("27d0")},"78f4":function(t,e,i){},"7d5e":function(t,e,i){"use strict";i.d(e,"b",(function(){return s})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){return n})),i.d(e,"d",(function(){return l}));var a=i("19e1");function s(t,e){let i;if(t===a["a"].profile?i=e.services:t===a["a"].service?i=[e]:t===a["a"].characteristic&&(i=[{characteristics:[e]}]),i)for(let a of i){var s;null===(s=a.characteristics)||void 0===s||s.forEach((t=>{var e;let i=null===(e=t.descriptors)||void 0===e?void 0:e.findIndex((t=>"2902"===t.uuid));var a;-1!=i&&(null===(a=t.descriptors)||void 0===a||a.splice(i,1))}))}}function r(t){var e;null===(e=t.characteristics)||void 0===e||e.forEach((t=>{var e;let i=null===(e=t.descriptors)||void 0===e?void 0:e.findIndex((t=>"org.bluetooth.descriptor.gatt.client_characteristic_configuration"===t.type));var a;-1!=i&&(null===(a=t.descriptors)||void 0===a||a.splice(i,1))}))}function n(t,e){var i=0;for(const a of e)t===a.name&&i++;return i}function l(t){if("string"!==typeof t||0===t.length)return;let e=t.toLocaleLowerCase().replace(/[^_0-9a-z]/gi,"_");return 1===e.charAt(0).length&&e.charAt(0)>="0"&&e.charAt(0)<="9"&&(e="_"+e),e}},"8eba":function(t,e,i){"use strict";i("e69a")},a18c:function(t,e){},a3a1:function(t,e,i){"use strict";i.d(e,"g",(function(){return o})),i.d(e,"h",(function(){return c})),i.d(e,"e",(function(){return d})),i.d(e,"f",(function(){return u})),i.d(e,"c",(function(){return p})),i.d(e,"d",(function(){return h})),i.d(e,"a",(function(){return m})),i.d(e,"b",(function(){return g}));var a=i("9523"),s=i.n(a),r=i("1732"),n=i("da4a");class l extends n["CapabilityDeclaration"]{constructor(t){var e;super(t),this.sourceFile=null!==(e=t.sourceFile)&&void 0!==e?e:null}}class o extends n["Gatt"]{constructor(t){var e,i,a,n,o,d;(super(t),s()(this,"upgradeResults",null),s()(this,"sourceFile",null),s()(this,"undraggable",!0),s()(this,"validationMessages",{}),s()(this,"vueId",Object(r["a"])()),t)&&(this.upgradeResults=null!==(e=t.upgradeResults)&&void 0!==e?e:null,this.sourceFile=null!==(i=t.sourceFile)&&void 0!==i?i:null,this.capabilityDeclarations=null!==(a=null===(n=t.capabilityDeclarations)||void 0===n?void 0:n.map((t=>new l({...t,sourceFile:this.sourceFile}))))&&void 0!==a?a:[],this.services=null!==(o=null===(d=t.services)||void 0===d?void 0:d.map((t=>new c(t,this))))&&void 0!==o?o:[])}}class c extends n["Service"]{constructor(t,e){super(t,e),s()(this,"validationMessages",{}),s()(this,"vueId",Object(r["a"])()),this.sourceFile=t.sourceFile?t.sourceFile:e?e.sourceFile:null,this.characteristics=t.characteristics.map((t=>new d(t,this))),this.undraggable=null!=this.sourceFile}}class d extends n["Characteristic"]{constructor(t,e){super(t,e),s()(this,"validationMessages",{}),s()(this,"vueId",Object(r["a"])()),this.sourceFile=t.sourceFile?t.sourceFile:e?e.sourceFile:null,this.descriptors=t.descriptors.map((t=>new u(t,this))),this.undraggable=null!=this.sourceFile}}class u extends n["Descriptor"]{constructor(t,e){super(t,e),s()(this,"validationMessages",{}),s()(this,"vueId",Object(r["a"])()),this.sourceFile=t.sourceFile?t.sourceFile:e?e.sourceFile:null,this.undraggable=null!=this.sourceFile}}function p(){return new n["Gatt"]({gattCaching:!0,genericAttributeService:!0,name:"Custom Gatt Config",capabilityDeclarations:[],services:[]})}function h(){return new n["Service"]({name:"Custom Service",uuid:Object(r["a"])(),sigType:"",info:"",id:void 0,capabilities:[],advertise:!1,declarationType:n["ServiceDeclarationType"].primary,includes:[],characteristics:[]})}function m(){return new n["Characteristic"]({name:"Custom Characteristic",uuid:Object(r["a"])(),sigType:"",info:"",id:void 0,capabilities:[],value:new n["Value"]({initialValue:"00"}),permissions:{},descriptors:[]})}function g(){return new n["Descriptor"]({name:"Custom Descriptor",uuid:Object(r["a"])(),sigType:"",info:"",id:void 0,value:new n["Value"]({initialValue:"00"}),discoverable:!0,permissions:{}})}},a883:function(t,e,i){"use strict";var a;a=i("d48b").default,e["a"]=a},b29f:function(t,e,i){},b3aa:function(t,e,i){"use strict";i("78f4")},b6cb:function(t,e,i){"use strict";i.d(e,"a",(function(){return r}));var a=i("da4a"),s=i("a3a1");class r{static load(t){let e=new s["g"](a["GattXmlTranslator"].xmlToGatt(t));return e}}},d48b:function(t,e,i){"use strict";i.r(e);var a=i("9523"),s=i.n(a),r=i("c4d0"),n=i.n(r),l=i("bc3a"),o=i.n(l);const c="/rest/gattConfigurator/",d=new URL(window.location.href),u=d.searchParams.get("id"),p="true"===d.searchParams.get("v6"),h="ws://"+window.location.host+"/ws/gattConfigurator/"+u;class m{constructor(){s()(this,"webSocketRouter",new n.a(h)),s()(this,"onThemeChanged",(t=>{})),s()(this,"onSaveEvent",(()=>{})),s()(this,"onGenerationResult",(()=>{})),s()(this,"onContributionsEvent",(()=>{})),s()(this,"themeChanged",(t=>this.onThemeChanged(t))),s()(this,"saveEvent",(()=>this.onSaveEvent())),s()(this,"contributionsEvent",(()=>this.onContributionsEvent())),this.webSocketRouter.addListener("theme",this.themeChanged),this.webSocketRouter.addListener("save",this.saveEvent),this.webSocketRouter.addListener("contributions",this.contributionsEvent),p&&window.addEventListener("message",(t=>{var e,i,a;switch(null===t||void 0===t||null===(e=t.data)||void 0===e?void 0:e.eventId){case"theme":this.themeChanged(null===(i=t.data.eventData)||void 0===i?void 0:i.theme);break;case"save":null!==(a=t.data.eventData)&&void 0!==a&&a.shouldSave&&this.saveEvent();break}}))}async init(){var t,e;null!=u&&(await this.webSocketRouter.connect(),p&&(null===(t=window.parent)||void 0===t||t.postMessage({eventId:"mounted",id:null===(e=frameElement)||void 0===e?void 0:e.id,eventData:{hasMounted:!0}},"*")))}loadConfiguration(){const t=c+"data?id="+u;return o.a.get(t).then((t=>t.data))}saveConfiguration(t){const e=c+"save/";o.a.post(e,t,{headers:{editorId:u}}).then((()=>this.setDirty(!1))).catch((t=>console.log("saveConfiguration returned ERROR: ",t.response.data)))}loadContributions(){const t=c+"contributions?id="+u;return o.a.get(t).then((t=>t.data))}saveContributions(t){const e=c+"saveContributions/";o.a.post(e,t,{headers:{editorId:u}}).catch((t=>console.log("saveContributions returned ERROR: ",t.response.data)))}setDirty(t){document.title=(t?"*":"")+"GATT Configurator";const e=c+"dirty/";var i,a;(o.a.post(e,JSON.stringify(t),{headers:{editorId:u}}).catch((t=>console.log("setDirty returned ERROR: ",t.response.data))),p)&&(null===(i=window.parent)||void 0===i||i.postMessage({eventId:"dirty",id:null===(a=frameElement)||void 0===a?void 0:a.id,eventData:{isDirty:t}},"*"))}get v6(){return p}generate(t){return Promise.resolve()}}const g=new m;e["default"]=g},d538:function(t,e,i){"use strict";i("305b")},e69a:function(t,e,i){},e932:function(t,e){t.exports={toc:[{name:"Understanding Profiles, Services, Characteristics and the Attribute Protocol",number:"1",children:[{name:"GATT-Based Bluetooth Profiles and Services",number:"1.1",children:[]},{name:"Services",number:"1.2",children:[]},{name:"Characteristics",number:"1.3",children:[]},{name:"The Attribute Protocol",number:"1.4",children:[]}]},{name:"Contents of the GATT Database",number:"2",children:[{name:"General Limitations",number:"2.1",children:[]},{name:"GATT database",number:"2.2",children:[]},{name:"Capability declarations",number:"2.3",children:[{name:"Capabilities",number:"2.3.1",children:[]}]},{name:"Services",number:"2.4",children:[]},{name:"Characteristics",number:"2.5",children:[{name:"Permission settings",number:"2.5.1",children:[]},{name:"Descriptors",number:"2.5.2",children:[]}]}]}],html:'\x3c!-- Author: Balázs Leidecker--\x3e\n\n\n                <h1 class="markdownheader" id="markdowntitle-">                 \n                  UG118: Blue Gecko Bluetooth®ProfileToolkit Developer&#39;s Guide\n                </h1><p>Bluetooth GATT services and characteristics are the basis of the Bluetooth data exchange. They are used to describe the structure, access type, and security properties of the data exposed by a device, such as a heart rate monitor. Bluetooth services and characteristics have a well defined and structured format, and they can be easily described using XML markup language. The Profile Toolkit is an XML-based markup language for describing the Bluetooth services and characteristics, also known as the GATT database, in both easy human-readable and machine-readable formats. This guide walks you through the XML syntax used in the Profile Toolkit and instructs you how to easily describe your own Bluetooth services and characteristics,configure the access and security properties, and how to include the GATT database as a part of the firmware. This guide also contains practical examples showing the use of both standardized Blue-tooth and vendor-specific proprietary services. These examples provide a good starting point for your own development work</p>\n<p>Key points:</p>\n<ul>\n<li>Understanding Bluetooth GATT profiles, services, characteristics, attribute protocol</li>\n<li>Building the GATT database with the Profile Toolkit</li>\n</ul>\n<p><img src="md/images/1.png" alt=""></p>\n\n                <h2 class="markdownheader" id="markdowntitle-1">                 \n                 1 Understanding Profiles, Services, Characteristics and the Attribute Protocol\n                </h2><p>This section gives a basic explanation of Bluetooth profiles, services and characteristics, and also explains how the Attribute protocol is used in the data exchange between the GATT server and client. Links to further information regarding these subjects are also provided.</p>\n\n                <h3 class="markdownheader" id="markdowntitle-1-1">                 \n                 1.1 GATT-Based Bluetooth Profiles and Services\n                </h3><p>A Bluetooth profile specifies the structure in which data is exchanged. The profile defines elements, such as services and characteristics used in a profile, but it may also contain definitions for security and connection establishment parameters. Typically a profile consists of one or more services which are needed to accomplish a high-level use case, such as heart rate or cadence monitoring. Standardized profiles allow device and software vendors to build interoperable devices and applications. Bluetooth SIG standardized profiles are available at: <a target="_blank" rel="noreferrer noopener nofollow" href="https://www.bluetooth.com/specifications/specs/">https://www.bluetooth.com/specifications/specs/</a>.</p>\n\n                <h3 class="markdownheader" id="markdowntitle-1-2">                 \n                 1.2 Services\n                </h3><p>A service is a collection of data composed of one or more characteristics used to accomplish a specific function of a device, such as battery monitoring or temperature data, rather than a complete use case. Bluetooth SIG standardized service specifications are available at: <a target="_blank" rel="noreferrer noopener nofollow" href="https://www.bluetooth.com/specifications/specs/">https://www.bluetooth.com/specifications/specs/</a>.</p>\n\n                <h3 class="markdownheader" id="markdowntitle-1-3">                 \n                 1.3 Characteristics\n                </h3><p>A characteristic is a value used in a service, either to expose and/or exchange data and/or to control information. Characteristics have a well defined, known format. They also contain information about how the value can be accessed, what security requirements must be fulfilled, and, optionally, how the characteristic value is displayed or interpreted. Characteristics may also contain descriptors that describe the value or permit configuration of characteristic data indications or notifications.</p>\n\n                <h3 class="markdownheader" id="markdowntitle-1-4">                 \n                 1.4 The Attribute Protocol\n                </h3><p>The Attribute protocol enables data exchange between the GATT server and the GATT client. The protocol also provides a set of operations, namely how to query, write, indicate or notify the data and/or control information between the two GATT parties.</p>\n<p><img src="md/images/AP.png" alt=""></p>\n<p><img src="md/images/attr_ops.png" alt=""></p>\n\n                <h2 class="markdownheader" id="markdowntitle-2">                 \n                 2 Contents of the GATT Database\n                </h2><p>This section of the document describes the XML syntax used in the Blue Gecko Bluetooth Profile Toolkit and walks you through the different options you can use when building Bluetooth services and characteristics. A few practical GATT database examples are also shown.</p>\n\n                <h3 class="markdownheader" id="markdowntitle-2-1">                 \n                 2.1 General Limitations\n                </h3><p>The table below shows the limitations of the GATT database supported by the Blue Gecko devices:</p>\n<table>\n<thead>\n<tr>\n<th>Item</th>\n<th align="center">Limitation</th>\n<th align="left">Notes</th>\n</tr>\n</thead>\n<tbody><tr>\n<td>Maximum number of characteristics</td>\n<td align="center">Not limited; practically restricted by the overall number of attributes in the database</td>\n<td align="left">All characteristics which do NOT have the property <strong>const=&quot;true&quot;</strong> are included in this count.</td>\n</tr>\n<tr>\n<td>Maximum length of a <strong>type=&quot;user&quot;</strong> characteristic</td>\n<td align="center">255 bytes</td>\n<td align="left">These characteristics are handled by the application, which means that the amount of RAM available for the application will limit this. Note: GATT procedures <strong>Write Long Characteristic Values, Reliable Writes</strong> and <strong>Read Multiple Characteristic Values</strong> are not supported for these characteristics</td>\n</tr>\n<tr>\n<td>Maximum length of a <strong>type=&quot;utf-8/hex&quot;</strong> characteristic</td>\n<td align="center">255 bytes</td>\n<td align="left">If <strong>const=&quot;true&quot;</strong> then the amount of free flash on the device defines this limit.<br> If <strong>const=&quot;false&quot;</strong> then RAM will be allocated for the characteristic for storing its value. The amount of free flash available on the device used defines this.</td>\n</tr>\n<tr>\n<td>Maximum number of attributes in a single GATT database</td>\n<td align="center">255</td>\n<td align="left">A single characteristic typically uses 3-5 attributes.</td>\n</tr>\n<tr>\n<td>Maximum number of notifiable characteristics</td>\n<td align="center">64</td>\n<td align="left"></td>\n</tr>\n<tr>\n<td>Maximum number of capabilities</td>\n<td align="center">16</td>\n<td align="left">The logic state of the capabilities will determine the visibility of each service/characteristic.</td>\n</tr>\n</tbody></table>\n\n                <h3 class="markdownheader" id="markdowntitle-2-2">                 \n                 2.2 GATT database<a id="profile"></a>\n                </h3><p>The GATT database along with the services and characteristics must be described inside the XML attribute <strong>&lt; gatt &gt;</strong></p>\n<table>\n<thead>\n<tr>\n<th align="center">Parameter</th>\n<th align="left">Description</th>\n</tr>\n</thead>\n<tbody><tr>\n<td align="center">name</td>\n<td align="left">Free text, not used by the database compiler<br><strong>Value</strong>: Any UTF-8 string <br><strong>Default</strong>: Custom BLE GATT</td>\n</tr>\n<tr>\n<td align="center">Generic Attribute Service</td>\n<td align="left">If enabled, Generic Attribute service and its service_changed characteristic will be added in the beginning of the database. The Bluetooth stack takes care of database structure change detection and will send service_changed notifications to clients when a change is detected. In addition, this will enable the GATT-caching feature introduced in Bluetooth 5.1. <br><strong>Values</strong>: <br><strong>enabled</strong>: Generic Attribute service is automatically added to the GATT database, and GATT caching is enabled.<br><strong>disabled</strong>: Generic Attribute service is not automatically added to the GATT database, and GATT caching is disabled.<br><strong>Default</strong>: enabled</td>\n</tr>\n<tr>\n<td align="center">GATT Caching</td>\n<td align="left">The GATT caching feature can be enabled/disabled</td>\n</tr>\n</tbody></table>\n\n                <h3 class="markdownheader" id="markdowntitle-2-3">                 \n                 2.3 Capability declarations<a id="capability_declarations"></a>\n                </h3><p>The GATT database services and characteristics can be made visible/invisible by using <strong>capabilities</strong>. All capabilities in a GATT database must be first declared in the &quot;Capability declarations&quot; table. </p>\n<p>With the + button, you can add capabilities to the GATT configuration. The Name you set here for the capability will also be the identifier name for that capability in the generated database C header. Thus, it must be valid in C.\nThe capabilites can be enabled/disabled with the checkbox nex to its name.</p>\n\n                <h4 class="markdownheader" id="markdowntitle-2-3-1">                 \n                 2.3.1 Capabilities\n                </h4><p>Each capability must be declared individually for services and characteristics</p>\n<p>In summary each service/characteristic can declare a number of capabilities, and the state of the capabilities (enable/disable) determines the visibility of those services/characteristics as a bit-wise OR operation, e.g. the service/characteristic is visible when at least one of its capabilities is enabled and it’s not visible when all of its the capabilities are disabled.</p>\n<p><strong>Note</strong>: Changing the capabilities state should not be done during a connection as that can cause misbehavior. The safest way is to change the capabilities when no devices are connected.</p>\n<p><strong>Inheritance of Capabilities</strong>\nServices and characteristics can declare the capabilities they want to use. If no capabilities are declared, then the following inheritance rules apply:</p>\n<ol>\n<li>A service that does not declare any capabilities will have all the capabilities from the GATT configration.</li>\n<li>A characteristic that does not declare any capabilities will have all the capabilities from the service that it belongs to. If the service\ndeclares a subset of the capabilities inthe GATT configuration, then only that subset will be inherited by the characteristic.</li>\n<li>All attributes of a characteristic inherit the characteristic&#39;s capabilities.</li>\n</ol>\n<p><strong>Visibility</strong></p>\n<p>Capabilities can be added to services and characteristics to make it visible/invisible to a GATT client, according with the following logic:</p>\n<ol>\n<li>A service and all its characteristics are <strong>visible</strong>, when at least one of its capabilities is <strong>enabled</strong>.</li>\n<li>A service and all its characteristics are <strong>invisible</strong>, when all of its capabilities are <strong>disabled</strong>.</li>\n<li>A characteristic and all its attributes are <strong>visible</strong>, when at least one of its capabilities is <strong>enabled</strong>.</li>\n<li>A characteristic and all its attributes are <strong>invisible</strong>, when all of its capabilities are <strong>disabled</strong>.</li>\n</ol>\n<p>The needed capabilites can be selected from the dropdown list. This list contains all the capabilites declared in the GATT configurator section.</p>\n\n                <h3 class="markdownheader" id="markdowntitle-2-4">                 \n                 2.4 Services<a id="services"></a>\n                </h3><p>The table below describes the parameters that can be used for defining a GATT service.</p>\n<table>\n<thead>\n<tr>\n<th>Parameter</th>\n<th align="left">Description</th>\n</tr>\n</thead>\n<tbody><tr>\n<td>Name</td>\n<td align="left">The name of the service</td>\n</tr>\n<tr>\n<td>UUID</td>\n<td align="left">Universally Unique Identifier. The UUID uniquely identifies a service. 16-bit values are used for the services defined by the Bluetooth SIG and 128-bit UUIDs can be used for vendor specific implementations.<br> <strong>Range:</strong><br> <strong>0x0000 - 0xFFFF</strong>: Reserved for Bluetooth SIG standardized services<br> <strong>0x00000000-0000-0000-0000-000000000000 - 0xFFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF</strong>: Reserved for vendor specific services</td>\n</tr>\n<tr>\n<td>Info</td>\n<td align="left">This field can be used to provide information about the service for the user of the configurator, it won&#39;t be used in the code.</td>\n</tr>\n<tr>\n<td>ID</td>\n<td align="left">The ID is used to identify a service within the service database and can be used as a reference from other services (include statement). Typically this does not need to be used. Value can only be porvided here, if the checkbox is checked. <br> <strong>Value</strong>: Any UTF-8 string</td>\n</tr>\n<tr>\n<td>SIG type</td>\n<td align="left">SIG type of the Service, if applicable</td>\n</tr>\n<tr>\n<td>Declaration type</td>\n<td align="left">The type field defines whether the service is a primary or a secondary service. Typically this does not need to be used<br> <strong>Values</strong>:<br> <strong>primary</strong>: a primary service<br> <strong>secondary</strong>: a secondary service<br> <strong>Default</strong>: primary</td>\n</tr>\n<tr>\n<td>Advertise service</td>\n<td align="left">This slider defines if the service UUID is included in the advertisement data. <br>The advertisement data can contain up to 13 16-bit UUIDs or one (1) 128-bit UUID <br> <strong>Values</strong>: <br><strong>enabled</strong>: UUID included in advertisement data<br> <strong>disabled</strong>: UUID not included in advertisement data <br> <strong>Default</strong>: disabled <br> <strong>Note</strong>: You can override the advertisement data with the GAP API, in which case this is not valid.</td>\n</tr>\n<tr>\n<td>Service includes</td>\n<td align="left">Service dependecies can be selected from the available services defined in the same GATT configuration. Multiple values can be selected from the dropdown list.</td>\n</tr>\n<tr>\n<td>Service capabilites</td>\n<td align="left">Capabilites can be declared for the service. Multiple values can be selected from the dropdown list.</td>\n</tr>\n</tbody></table>\n<p>Note: You can generate your own 128-bit UUIDs at: <a target="_blank" rel="noreferrer noopener nofollow" href="http://www.itu.int/en/ITU-T/asn1/Pages/UUID/uuids.aspx">http://www.itu.int/en/ITU-T/asn1/Pages/UUID/uuids.aspx</a></p>\n\n                <h3 class="markdownheader" id="markdowntitle-2-5">                 \n                 2.5 Characteristics<a id="characteristics"></a>\n                </h3><p>The table below describes the parameters that can be used for defining a characteristic.</p>\n<table>\n<thead>\n<tr>\n<th align="center">Parameter</th>\n<th align="left">Description</th>\n</tr>\n</thead>\n<tbody><tr>\n<td align="center">Name</td>\n<td align="left">Name of the characteristic, not used in the code</td>\n</tr>\n<tr>\n<td align="center">UUID</td>\n<td align="left">Universally Unique Identifier. The UUID uniquely identifies a characteristic. 16-bit values are used for the services defined by the Bluetooth SIG and 128-bit UUIDs can be used for vendor specific implementations. <br><strong>Range</strong>:<br><strong>0x0000 - 0xFFFF</strong>: Reserved for Bluetooth SIG standardized services<br><strong>0x00000000-0000-0000-0000-000000000000 - 0xFFFFFFFF-FFFF-FFFF-FFFF-FFFFFFFFFFFF</strong>: Reserved for vendor specific services</td>\n</tr>\n<tr>\n<td align="center">Info</td>\n<td align="left">This field can be used to provide information about the characteristic for the user of the configurator, it won&#39;t be used in the code.</td>\n</tr>\n<tr>\n<td align="center">ID</td>\n<td align="left">The ID is used to identify a characteristic. The ID is used within a C application to read and write characteristic values or to detect if notifications or indications are enabled or disabled for a specific characteristic. <br> When the project is built the generated GATT C header file contains a macro with the characteristic &#39;ID&#39; and corresponding handle value. It must be valid in C. Only used when the checkbox is ticked<br><strong>Value</strong>: Any UTF-8 string which is valid in C</td>\n</tr>\n<tr>\n<td align="center">SIG type</td>\n<td align="left">SIG type of the characteristic, if applicable</td>\n</tr>\n<tr>\n<td align="center">type</td>\n<td align="left">Defines the data type.<br><strong>Values</strong>:<br><strong>hex</strong>: Value type is hex<br><strong>utf-8</strong>: Value is a string<br><strong>user</strong>: When the characteristic type is marked as &quot;user&quot;, the application is responsible for initializing the characteristic value and also providing it, for example, when read operation occurs. The Bluetooth stack does not initialize the value or automatically provide the value when it is being read. When this is set, the Bluetooth stack generates <strong>gatt_server_user_read_request</strong> or <strong>gatt_server_user_write_request</strong>, which must be handled by the application. <br><strong>Default</strong>: utf-8</td>\n</tr>\n<tr>\n<td align="center">Constant</td>\n<td align="left">A characteristic that has been defined as constant is stored in flash instead of RAM and hence cannot be written to. The benefit of constant values is a smaller RAM footprint in the application. <br>Default: false</td>\n</tr>\n<tr>\n<td align="center">Variable length</td>\n<td align="left">If set to true, then the length of the value has variable length</td>\n</tr>\n<tr>\n<td align="center">Value length</td>\n<td align="left">Defines the length of the characteristic, or the maximum length if Variable length is true. If both length and value are defined, then the following rules apply: 1. If variable_length is false and length is bigger than the value&#39;s length, then the value will be padded with 0&#39;s at the end to match the attribute&#39;s length. 2. If length is smaller than the value&#39;s length, then the value will be clipped to match length, regardless of whether variable_length is true or false. Range:<br>If type is &#39;hex&#39; or &#39;utf-8&#39;: 1-255 <br>If type is &#39;user&#39;: 1-511 <br>Default: 1</td>\n</tr>\n<tr>\n<td align="center">Initial value</td>\n<td align="left">Initial value of the characteristic</td>\n</tr>\n<tr>\n<td align="center">Characteristic capabilites</td>\n<td align="left">Capabilites can be declared for the characteristic. Multiple values can be selected from the dropdown list.</td>\n</tr>\n</tbody></table>\n\n                <h4 class="markdownheader" id="markdowntitle-2-5-1">                 \n                 2.5.1 Permission settings<a id="characteristic_permissions"></a>\n                </h4><p>The characteristics access properties are defined by the following table:</p>\n<table>\n<thead>\n<tr>\n<th align="center">Permission Setting</th>\n<th align="left">Description</th>\n</tr>\n</thead>\n<tbody><tr>\n<td align="center">Read</td>\n<td align="left">Characteristic can be read by a remote device.<br><strong>Values</strong>:<br><strong>true</strong>: Characteristic can be read<br><strong>false</strong>: Characteristic cannot be read<br><strong>Default</strong>: false</td>\n</tr>\n<tr>\n<td align="center">Write</td>\n<td align="left">Characteristic can be written by a remote device<br><strong>Values</strong>:<br><strong>true</strong>: Characteristic can be written<br><strong>false</strong>: Characteristic cannot be written<br><strong>Default</strong>: false</td>\n</tr>\n<tr>\n<td align="center">Reliable Write</td>\n<td align="left">Allows using reliable write procedure to modify attribute, this is just a hint to GATT client. The Bluetooth stack always allows using reliable writes to be used to modify attributes.<br><strong>Values</strong>:<br><strong>true</strong>: Reliable write enabled<br><strong>false</strong>: Reliable write disabled<br><strong>Default</strong>: false</td>\n</tr>\n<tr>\n<td align="center">Write Without Response</td>\n<td align="left">Characteristic can be written by a remote device. Write without response is not acknowledged over the Attribute Protocol.<br><strong>Values</strong>:<br><strong>true</strong>: Characteristic can be written<br><strong>false</strong>: Characteristic cannot be written<br><strong>Default</strong>: false</td>\n</tr>\n<tr>\n<td align="center">Notify</td>\n<td align="left">Characteristic has the notify property and characteristic value changes are notified over the Attribute Protocol. Notifications are not acknowledged over the Attribute Protocol<br><strong>Values</strong>:<br><strong>true</strong>: Characteristic has notify property<br><strong>false</strong>: Characteristic does not have notify property<br><strong>Default</strong>: false</td>\n</tr>\n<tr>\n<td align="center">Indicate</td>\n<td align="left">Characteristic has the indicate property and characteristic value changes are indicated over the Attribute Protocol. Indications are acknowledged over the Attribute Protocol<br><strong>Values</strong>:<br><strong>true</strong>: Characteristic has indicate property<br><strong>false</strong>: Characteristic does not have indicate property<br><strong>Default</strong>: false</td>\n</tr>\n</tbody></table>\n<p>For all of the above mentioned accesses a securtiy level can also be defined:</p>\n<table>\n<thead>\n<tr>\n<th align="center">Permission Setting</th>\n<th align="left">Description</th>\n</tr>\n</thead>\n<tbody><tr>\n<td align="center">Authenticated</td>\n<td align="left">Accessing the characteristic value requires an authentication. In order to access the characteristic with this property, the remote device has to be bonded using MITM protection and the connection must be also encrypted.</td>\n</tr>\n<tr>\n<td align="center">Bonded</td>\n<td align="left">Accessing the characteristic value requires an encrypted link. Devices must also be bonded at least with Just Works pairing</td>\n</tr>\n<tr>\n<td align="center">Encrypted</td>\n<td align="left">Accessing the characteristic value requires an encrypted link. With iOS 9.1 and newer devices must also be bonded at least with Just Works pairing.</td>\n</tr>\n</tbody></table>\n\n                <h4 class="markdownheader" id="markdowntitle-2-5-2">                 \n                 2.5.2 Descriptors<a id="descriptors"></a>\n                </h4><p>Characteristic descriptors can also be defined in the GATT configurator. For the available settings and fields, see the <a href="#characteristics">Characteristics</a> chapter, as the descriptor settings are a subset of the characteritic ones.</p>\n'}}});