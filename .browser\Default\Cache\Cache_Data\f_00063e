{"html": "\n   <article>\n    <div class=\"header\">\n     <div class=\"headertitle\">\n      <h1 class=\"title\">\n       IADC - Incremental ADC\n      </h1>\n     </div>\n    </div>\n    <div class=\"contents\">\n     <a id=\"details\" name=\"details\">\n     </a>\n     <h2 class=\"groupheader\">\n      Description\n     </h2>\n     <p>\n      Incremental Analog to Digital Converter (IADC) Peripheral API.\n     </p>\n     <p>\n      This module contains functions to control the IADC peripheral of Silicon Labs 32-bit MCUs and SoCs. The IADC is used to convert analog signals into a digital representation.\n     </p>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"nested-classes\">\n          </a>\n          Data Structures\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-init-t\" target=\"_blank\">\n          IADC_Init_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC init structure, common for single conversion and scan sequence.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-config-t\" target=\"_blank\">\n          IADC_Config_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC config structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-all-configs-t\" target=\"_blank\">\n          IADC_AllConfigs_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Structure for all IADC configs.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-init-scan-t\" target=\"_blank\">\n          IADC_InitScan_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC scan init structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-init-single-t\" target=\"_blank\">\n          IADC_InitSingle_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC single init structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-single-input-t\" target=\"_blank\">\n          IADC_SingleInput_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC single input selection structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-scan-table-entry-t\" target=\"_blank\">\n          IADC_ScanTableEntry_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC scan table entry structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-scan-table-t\" target=\"_blank\">\n          IADC_ScanTable_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Structure for IADC scan table.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-result-t\" target=\"_blank\">\n          IADC_Result_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Structure holding IADC result, including data and ID.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"func-members\">\n          </a>\n          Functions\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gaa95a0c387dbf82c83aaa216209c8dfde\">\n          IADC_init\n         </a>\n         (IADC_TypeDef *iadc, const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-init-t\" target=\"_blank\">\n          IADC_Init_t\n         </a>\n         *init, const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-all-configs-t\" target=\"_blank\">\n          IADC_AllConfigs_t\n         </a>\n         *allConfigs)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Initialize IADC.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga8da3d2e45b0a5ae7622dcf1b30c68c2c\">\n          IADC_reset\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Reset IADC to same state as after a HW reset.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga89338e759472e4372c4b35876733bea3\">\n          IADC_initScan\n         </a>\n         (IADC_TypeDef *iadc, const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-init-scan-t\" target=\"_blank\">\n          IADC_InitScan_t\n         </a>\n         *init, const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-scan-table-t\" target=\"_blank\">\n          IADC_ScanTable_t\n         </a>\n         *scanTable)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Initialize IADC scan sequence.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga0054dbc14b8abd889c5a2e6fc8f490ab\">\n          IADC_updateScanEntry\n         </a>\n         (IADC_TypeDef *iadc, uint8_t id,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-scan-table-entry-t\" target=\"_blank\">\n          IADC_ScanTableEntry_t\n         </a>\n         *entry)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Add/update entry in scan table.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga283cbd96f53a6e36810f326a070bb6f6\">\n          IADC_setScanMask\n         </a>\n         (IADC_TypeDef *iadc, uint32_t mask)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Set mask of IADC scan table entries to include in scan.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga6daf8d5af3a2ab98195e2ed34018b337\">\n          IADC_initSingle\n         </a>\n         (IADC_TypeDef *iadc, const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-init-single-t\" target=\"_blank\">\n          IADC_InitSingle_t\n         </a>\n         *init, const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-single-input-t\" target=\"_blank\">\n          IADC_SingleInput_t\n         </a>\n         *input)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Initialize single IADC conversion.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga25ad70bb7cb7efb107cedf481e37bfca\">\n          IADC_updateSingleInput\n         </a>\n         (IADC_TypeDef *iadc, const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-single-input-t\" target=\"_blank\">\n          IADC_SingleInput_t\n         </a>\n         *input)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Update IADC single input selection.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint8_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga95ab149c3aece1c95a506dc736b8e818\">\n          IADC_calcSrcClkPrescale\n         </a>\n         (IADC_TypeDef *iadc, uint32_t srcClkFreq, uint32_t cmuClkFreq)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Calculate prescaler for CLK_SRC_ADC high speed clock.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gaf85f5c99a14ccafb7729cdcd78fe9d8f\">\n          IADC_calcAdcClkPrescale\n         </a>\n         (IADC_TypeDef *iadc, uint32_t adcClkFreq, uint32_t cmuClkFreq,\n         <a class=\"el\" href=\"#gac0395e857a03877c0a335c552a774752\">\n          IADC_CfgAdcMode_t\n         </a>\n         adcMode, uint8_t srcClkPrescaler)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Calculate prescaler for ADC_CLK clock.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint8_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga82909089ae9dfaa395eadb5430cd4223\">\n          IADC_calcTimebase\n         </a>\n         (IADC_TypeDef *iadc, uint32_t cmuClkFreq)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Calculate timebase value in order to get a timebase providing at least 1us.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-result-t\" target=\"_blank\">\n          IADC_Result_t\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gacdacbca6728d94d53d28f68710766f79\">\n          IADC_readSingleResult\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Read most recent single conversion result.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-result-t\" target=\"_blank\">\n          IADC_Result_t\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga961372b54bb66f3b13afb764aa9f5271\">\n          IADC_pullSingleFifoResult\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Pull result from single data FIFO.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-result-t\" target=\"_blank\">\n          IADC_Result_t\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gabe6a447ba1cb484ec66528a60e2ebc36\">\n          IADC_readScanResult\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Read most recent scan conversion result.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-result-t\" target=\"_blank\">\n          IADC_Result_t\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga52402a2c653644c11350c967514fc23a\">\n          IADC_pullScanFifoResult\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Pull result from scan data FIFO.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga37afb392a921d006e4a3a2cdab8fbfc7\">\n          IADC_getReferenceVoltage\n         </a>\n         (\n         <a class=\"el\" href=\"#gac1803c998a59a350d10147b81f80d156\">\n          IADC_CfgReference_t\n         </a>\n         reference)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get reference voltage selection.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga9f2f6460cc3e18ca1c52a70c537cbf03\">\n          IADC_pullSingleFifoData\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Pull data from single data FIFO.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga5375e06272c149b6caad0b3ea232e1be\">\n          IADC_readSingleData\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Read most recent single conversion data.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga93a8f8c91e1eb97693a87d4f1b21b35c\">\n          IADC_pullScanFifoData\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Pull data from scan data FIFO.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga1fdead030312cf9355e9074c20c52cb3\">\n          IADC_readScanData\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Read most recent scan conversion data.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga1a3188f2c082a114eaf6c61b8ae404b1\">\n          IADC_clearInt\n         </a>\n         (IADC_TypeDef *iadc, uint32_t flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Clear one or more pending IADC interrupts.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga33ba2f77c03dc8593bdaf450af2d45dc\">\n          IADC_disableInt\n         </a>\n         (IADC_TypeDef *iadc, uint32_t flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Disable one or more IADC interrupts.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga37a0290c58088655951695e9f6fd15ba\">\n          IADC_enableInt\n         </a>\n         (IADC_TypeDef *iadc, uint32_t flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Enable one or more IADC interrupts.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga3064d6dc58382114173c37c08aa648f0\">\n          IADC_getInt\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get pending IADC interrupt flags.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gad3ededd9bfa76e4a48d19843b2265dda\">\n          IADC_getEnabledInt\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get enabled and pending IADC interrupt flags.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gab767a70e50481fc13bfeaa7fa5ee912c\">\n          IADC_setInt\n         </a>\n         (IADC_TypeDef *iadc, uint32_t flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Set one or more pending IADC interrupts from SW.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gad7b8fab9d9a61b220cad1c298b052889\">\n          IADC_command\n         </a>\n         (IADC_TypeDef *iadc,\n         <a class=\"el\" href=\"#ga81517741e2dd7744d21850a22c400c0d\">\n          IADC_Cmd_t\n         </a>\n         cmd)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Start/stop scan sequence, single conversion and/or timer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga08aca161b3763f8e4c55a291417e9f98\">\n          IADC_getScanMask\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get the scan mask currently used in the IADC.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gad5e0e3835c579efe6ff44210148d2203\">\n          IADC_getStatus\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get status bits of IADC.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint8_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga4e09cacd7d993f70d0ceb0eacf615105\">\n          IADC_getSingleFifoCnt\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get the number of elements in the IADC single FIFO.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint8_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga9fcc5a064a2988d2c5fc76d704f6087c\">\n          IADC_getScanFifoCnt\n         </a>\n         (IADC_TypeDef *iadc)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get the number of elements in the IADC scan FIFO.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"#gabda64eab4e23b86883428db9060dbf74\">\n          IADC_NegInput_t\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga3bd726aca10a7e3a3d2e3dda9c277cb3\">\n          IADC_portPinToNegInput\n         </a>\n         (\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/group-gpio#gafe496583dfd425fc03c413d49459efe6\" target=\"_blank\">\n          GPIO_Port_TypeDef\n         </a>\n         port, uint8_t pin)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Convert the GPIO port/pin to IADC negative input selection.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"#gab3230940cb6abe6a598d926f6503442f\">\n          IADC_PosInput_t\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga67d47e7cda4569ab573e9fe3b4ac2019\">\n          IADC_portPinToPosInput\n         </a>\n         (\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/group-gpio#gafe496583dfd425fc03c413d49459efe6\" target=\"_blank\">\n          GPIO_Port_TypeDef\n         </a>\n         port, uint8_t pin)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Convert the GPIO port/pin to IADC positive input selection.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"define-members\">\n          </a>\n          Macros\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga2920221ff8a2bc5a7adcff4ee61f8f6d\">\n          IADC_INIT_DEFAULT\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Default config for IADC init structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gaf17aa0ddca20b272d09a575a8db5f04f\">\n          IADC_CONFIG_DEFAULT\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Default IADC config structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga93035a36d674c90bbb04c8c9d7292360\">\n          IADC_ALLCONFIGS_DEFAULT\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Default IADC sructure for all configs.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga25a8a2f6b4f6bec5ffed996fdc3d3385\">\n          IADC_INITSCAN_DEFAULT\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Default config for IADC scan init structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gac4f5e953de53d33f4b7e24677bd64913\">\n          IADC_INITSINGLE_DEFAULT\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Default config for IADC single init structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gab5de6ca3a01129040bf1bbf1482b09ce\">\n          IADC_SINGLEINPUT_DEFAULT\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Default config for IADC single input structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gae802efb3a2c2cf1c562034cea3aa383f\">\n          IADC_SCANTABLEENTRY_DEFAULT\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Default config for IADC scan table entry structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga6d31c865e6325bf3d445bb78ccd5aab5\">\n          IADC_SCANTABLE_DEFAULT\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Default IADC structure for scan table.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"enum-members\">\n          </a>\n          Enumerations\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga4487a975a2fac5be3613a29fb9f4a9a5\">\n          IADC_Warmup_t\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#gga4487a975a2fac5be3613a29fb9f4a9a5af220158c548e6e7c6c8459dcddf58610\">\n          iadcWarmupNormal\n         </a>\n         = _IADC_CTRL_WARMUPMODE_NORMAL,\n         <br>\n         <a class=\"el\" href=\"#gga4487a975a2fac5be3613a29fb9f4a9a5a5e3b9f87d46aae6afbe3723cd5b63e6d\">\n          iadcWarmupKeepInStandby\n         </a>\n         = _IADC_CTRL_WARMUPMODE_KEEPINSTANDBY,\n         <br>\n         <a class=\"el\" href=\"#gga4487a975a2fac5be3613a29fb9f4a9a5ad5bff89607aed8085e38eae230b74fc1\">\n          iadcWarmupKeepWarm\n         </a>\n         = _IADC_CTRL_WARMUPMODE_KEEPWARM\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Warm-up mode.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gac3000f2dd003cc23c74b7e8054e4550c\">\n          IADC_Alignment_t\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#ggac3000f2dd003cc23c74b7e8054e4550ca2f8fb7b73abde4bdfff3d88fb54fe71a\">\n          iadcAlignRight12\n         </a>\n         = _IADC_SCANFIFOCFG_ALIGNMENT_RIGHT12,\n         <br>\n         <a class=\"el\" href=\"#ggac3000f2dd003cc23c74b7e8054e4550cab902aecd539012fd4babed7d711dd931\">\n          iadcAlignLeft12\n         </a>\n         = _IADC_SCANFIFOCFG_ALIGNMENT_LEFT12\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC result alignment.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gabda64eab4e23b86883428db9060dbf74\">\n          IADC_NegInput_t\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a80d2ce7610ec1ca853649ce2dbe4a94e\">\n          iadcNegInputGnd\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a1e88b6db1413a7bfd3bc947d96afe22d\">\n          iadcNegInputGndaux\n         </a>\n         = (_IADC_SCAN_PORTNEG_GND &lt;&lt; (_IADC_SCAN_PORTNEG_SHIFT - _IADC_SCAN_PINNEG_SHIFT)),\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74ab9e2d4ca675b41e2348eb8b49c62965d\">\n          iadcNegInputPortAPin0\n         </a>\n         = (_IADC_SCAN_PORTNEG_PORTA &lt;&lt; (_IADC_SCAN_PORTNEG_SHIFT - _IADC_SCAN_PINNEG_SHIFT)),\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a064d7ec41b93142945a5fdb00a187204\">\n          iadcNegInputPortAPin1\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74ab0d1dd6b558362637d3fbdda5a9a76e1\">\n          iadcNegInputPortAPin2\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a066b1519392cf81180e58b24c36f0a9a\">\n          iadcNegInputPortAPin3\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a94eb323ae48f650f591e7df249913fe3\">\n          iadcNegInputPortAPin4\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a1e3beb5c49ec484031d37e66dce8e32a\">\n          iadcNegInputPortAPin5\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74aa5b63e925197004929ebb108c2326090\">\n          iadcNegInputPortAPin6\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a99b1167d57583ed0a724eb20521e1e88\">\n          iadcNegInputPortAPin7\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a6814d915f14cbf1d939e6935db758512\">\n          iadcNegInputPortAPin8\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74ab9cea3e4fb21dfbf00608addf4ecdfbc\">\n          iadcNegInputPortAPin9\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a710e22ba34c938e6221320c00627232b\">\n          iadcNegInputPortAPin10\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74adf20dba7019cffad432fca49c8597979\">\n          iadcNegInputPortAPin11\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74ab5c7a02cdfc115639d0847e1befa0032\">\n          iadcNegInputPortAPin12\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a17398aeb74d5f29e4107965328bc9fae\">\n          iadcNegInputPortAPin13\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74accb63423740771ff3937a6c2dd50ab3d\">\n          iadcNegInputPortAPin14\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a7144f8a54332246b72697fdbee3f53d8\">\n          iadcNegInputPortAPin15\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a31d71c2c76078e276f124c81e13bff2f\">\n          iadcNegInputPortBPin0\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74af32719bf4ac0002f8f3bf29b16f11e62\">\n          iadcNegInputPortBPin1\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74aecccaf09c7e2e58f997f95dca4bb69bc\">\n          iadcNegInputPortBPin2\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a7c0e9084953a33649662ec1d5f036004\">\n          iadcNegInputPortBPin3\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a981d1e67dac39f707e4f1d99cec01376\">\n          iadcNegInputPortBPin4\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a64e27700d85a40adc0b9ad0375045fa8\">\n          iadcNegInputPortBPin5\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a74db42ce0af9d911b1b2c32228189a7d\">\n          iadcNegInputPortBPin6\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a6891cb1dfe348e39c39c3a8e507ac68d\">\n          iadcNegInputPortBPin7\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74aafc1a1716def4b69cb979110ccafccfa\">\n          iadcNegInputPortBPin8\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74aef59f829afe7d963505701d0aa8f3eec\">\n          iadcNegInputPortBPin9\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a7c5dda04994a6f7ec69d7261158f57dd\">\n          iadcNegInputPortBPin10\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74ad104965a8d9a70f2d381d081701e94a2\">\n          iadcNegInputPortBPin11\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a236ab871cf52bba1e565343df7248278\">\n          iadcNegInputPortBPin12\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a6ddadf662552698617f9e4052167e1dd\">\n          iadcNegInputPortBPin13\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74ada91761086cac6a48d136f413891d6fa\">\n          iadcNegInputPortBPin14\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a170199913d7740b385859bcd6fb2a6ab\">\n          iadcNegInputPortBPin15\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74aae0623199d45ae33c3d01f84aebeab88\">\n          iadcNegInputPortCPin0\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a87fc2c52b902641465b5aa12f73c3265\">\n          iadcNegInputPortCPin1\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74ababe798a879df614e983acd84bca8af9\">\n          iadcNegInputPortCPin2\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74abea1931e25e7facea9ad7593eb83a721\">\n          iadcNegInputPortCPin3\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a8adcd837a122b0b5ba4515846e3d7a19\">\n          iadcNegInputPortCPin4\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74ad22856188e1c9123bf08a2200496b445\">\n          iadcNegInputPortCPin5\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a8ca62d801997a1bd6083fe0d43dffd4b\">\n          iadcNegInputPortCPin6\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a3deeb7e894897393d50fd496bf83e469\">\n          iadcNegInputPortCPin7\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a80dc46d17176a99283484bbffe883d23\">\n          iadcNegInputPortCPin8\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74af376ce12067b32a073b5b249a41ea096\">\n          iadcNegInputPortCPin9\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74afea4499725af5ac765cfedc94e426ef9\">\n          iadcNegInputPortCPin10\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74af8deceabfa5ec09aced1e78c457f0cce\">\n          iadcNegInputPortCPin11\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a79a067950ba1a86a8e45281cc5d49027\">\n          iadcNegInputPortCPin12\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a6b31cc823debce0b10def41585b9cb04\">\n          iadcNegInputPortCPin13\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74ad2fa573257b6e8cb77268ca639afbe4b\">\n          iadcNegInputPortCPin14\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74af4a0c671b39341a79dfc28220b910d53\">\n          iadcNegInputPortCPin15\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74aa241ee8899b71c8948c3465901798abb\">\n          iadcNegInputPortDPin0\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74af83c9c69e4a8b66dbc12dda59e8045e0\">\n          iadcNegInputPortDPin1\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a44741e05f858cf8fa7dc2b3199450ea7\">\n          iadcNegInputPortDPin2\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74af25c0c6a2999fb9a91f591433a6286b0\">\n          iadcNegInputPortDPin3\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a08679f2b45e800c5cb6eccc543cd2f91\">\n          iadcNegInputPortDPin4\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74ad98a53b7933265beae313b05ab870faf\">\n          iadcNegInputPortDPin5\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a2020eb6da728ab7c7bfd44378b5ccef4\">\n          iadcNegInputPortDPin6\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74acd704df824c0247dd11b2d64eb42f70e\">\n          iadcNegInputPortDPin7\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a85a3cf825f20e65724fd25e5000f6fbf\">\n          iadcNegInputPortDPin8\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74af5cde2b1071c11101431f54eae391034\">\n          iadcNegInputPortDPin9\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74afaa4aa03f097a702228d52a001165335\">\n          iadcNegInputPortDPin10\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a8cb2816e5ee42bbc33faf8d6120893bf\">\n          iadcNegInputPortDPin11\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a5aa7ed5aff37a30d0bcba4e5e339656a\">\n          iadcNegInputPortDPin12\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a228056bbf94a3dd66718feea366afa26\">\n          iadcNegInputPortDPin13\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74a74ef0a313c1844031991782a51ea056e\">\n          iadcNegInputPortDPin14\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggabda64eab4e23b86883428db9060dbf74ab4ebd9f75f678cba3fe756ccea564676\">\n          iadcNegInputPortDPin15\n         </a>\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC negative input selection.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gab3230940cb6abe6a598d926f6503442f\">\n          IADC_PosInput_t\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa8df9b1a274ac0a31719504e198394112\">\n          iadcPosInputGnd\n         </a>\n         = (_IADC_SCAN_PORTPOS_GND &lt;&lt; (_IADC_SCAN_PORTPOS_SHIFT - _IADC_SCAN_PINPOS_SHIFT)),\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442facd03f526f9e6b3c19fe3d30c6a630e1b\">\n          iadcPosInputAvdd\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442faf58baf1b96d92192340b27779509cfcb\">\n          iadcPosInputVddio\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa6819c6d64b87cf1e7141e532dd0c7d8a\">\n          iadcPosInputVss\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa1567e80dd6cd032150bf5bdcf99fa83f\">\n          iadcPosInputVssaux\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442faa122cdd60853f7de1256e0aecbda8c3a\">\n          iadcPosInputDvdd\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fad61cd12eb4e1b6e4129e935ad9232953\">\n          iadcPosInputDecouple\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442faaa63ebe63fc173efd6937f6b3d6544f1\">\n          iadcPosInputPortAPin0\n         </a>\n         = (_IADC_SCAN_PORTPOS_PORTA &lt;&lt; (_IADC_SCAN_PORTPOS_SHIFT - _IADC_SCAN_PINPOS_SHIFT)),\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442faafc0d855bc7186070fedfed9da88a81e\">\n          iadcPosInputPortAPin1\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa735c3c4b0e556de823f68251df0831a1\">\n          iadcPosInputPortAPin2\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fae38e95da9c522f4760aa8ca30675e041\">\n          iadcPosInputPortAPin3\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fab02fdceb177243c5558734eeb502cf4a\">\n          iadcPosInputPortAPin4\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa2e0caa2508b88378ce11efe4753d0175\">\n          iadcPosInputPortAPin5\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa38148148fdd991474742b5226aa66e15\">\n          iadcPosInputPortAPin6\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa037f0c2e3d60708aefda307870fcdda5\">\n          iadcPosInputPortAPin7\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa27d5e7a63629811801409cfdfb8eb597\">\n          iadcPosInputPortAPin8\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa5fd1893d84e4a972b38d5367557e4933\">\n          iadcPosInputPortAPin9\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fac151519574fc0a329117d60b730ea6d5\">\n          iadcPosInputPortAPin10\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa0f32185f05572751417d96d0c92aa2ab\">\n          iadcPosInputPortAPin11\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fac9812a7c60d8023f691ac951f5b94ea0\">\n          iadcPosInputPortAPin12\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa3be87f718d61e743d1dbfde022a7bfed\">\n          iadcPosInputPortAPin13\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fab579d0abc8d37e6b269490cc66d5318f\">\n          iadcPosInputPortAPin14\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa359588c2c1ad83cae24d6865259a5604\">\n          iadcPosInputPortAPin15\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fab1d3f423ebeaf07e738837627cdfe73e\">\n          iadcPosInputPortBPin0\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa14049e6b376e8fa4a7f752f288346621\">\n          iadcPosInputPortBPin1\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa9672e96f5d30f431eaf7f6ccc2ead424\">\n          iadcPosInputPortBPin2\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa2b9b42011e33b2dc4628a2d536d7d5f0\">\n          iadcPosInputPortBPin3\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442faa2e410c7ae55a17af3de6536407139f2\">\n          iadcPosInputPortBPin4\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa4bd2b64c5dcb3781fb77519154920061\">\n          iadcPosInputPortBPin5\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa055fdf7f5441910a74d9ea0718d50e73\">\n          iadcPosInputPortBPin6\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442faebb4cf6a710bd8bcbea6082bae2a2ef7\">\n          iadcPosInputPortBPin7\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fad76b16bbb4565575235ec6895e9cf69f\">\n          iadcPosInputPortBPin8\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fabb866c6b48d9db421e6e187376e2b96a\">\n          iadcPosInputPortBPin9\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fab5779cc07f565daa42b4520cea6382ea\">\n          iadcPosInputPortBPin10\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa42d0a8dda4ab69e75b06b5e12d526e51\">\n          iadcPosInputPortBPin11\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa23403bbdf2a2ca884154f26503ce8cad\">\n          iadcPosInputPortBPin12\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa9c420caf5c54e73ab1e6a955ddf3165e\">\n          iadcPosInputPortBPin13\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442faebd63ba94260ef85a7202bf442b8858b\">\n          iadcPosInputPortBPin14\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa8e99f0eaeb59af2e8c5ef55bdfb0030f\">\n          iadcPosInputPortBPin15\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa1877d2e4fdaed5ddf2ee38766a206d06\">\n          iadcPosInputPortCPin0\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fad48d8461ab80d9b048b99156307c072b\">\n          iadcPosInputPortCPin1\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa7a76880c5d3fa5cfeb7bfc38a3984000\">\n          iadcPosInputPortCPin2\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442faea17aee3bc08c7c097d51e566a743761\">\n          iadcPosInputPortCPin3\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fad2c80bd8a41512b90bfa172142c0f8b6\">\n          iadcPosInputPortCPin4\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa7dd54445082e41a9c343896fe21511bc\">\n          iadcPosInputPortCPin5\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fae0a86a9143ee0fd039ba1b521a697e8b\">\n          iadcPosInputPortCPin6\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa28a613931cec42143e01efaae3c3ba6c\">\n          iadcPosInputPortCPin7\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa244a31528ea50eeaa8ac0e4c2f965fbc\">\n          iadcPosInputPortCPin8\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442facde95492af138fac0555ee271a5790a6\">\n          iadcPosInputPortCPin9\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa98e658f6eccfa0e63550e95507ee2928\">\n          iadcPosInputPortCPin10\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442faf7887ce477b22407e28f9e09b294c5da\">\n          iadcPosInputPortCPin11\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa718948d7861d3ad8e46d20826788336c\">\n          iadcPosInputPortCPin12\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fab37f054716362e3cc2290d08c6c8bf57\">\n          iadcPosInputPortCPin13\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442faca81d02eef2f00594a4c0e7e8d29a49d\">\n          iadcPosInputPortCPin14\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa799dd49078e949587ce1167fb50efd7a\">\n          iadcPosInputPortCPin15\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa1460b2c47920622210eeb80f49daaec0\">\n          iadcPosInputPortDPin0\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa8bab2aa76a1bbe9ec7260af823229a96\">\n          iadcPosInputPortDPin1\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442faca1bb225c4ac224284b74209cbe453b4\">\n          iadcPosInputPortDPin2\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442faa06b0e6564f9a03ca548fae1e7f5d027\">\n          iadcPosInputPortDPin3\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa8d393553ba02bd16ed413bfca1f03153\">\n          iadcPosInputPortDPin4\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa99ec4b8dd5bd8d5a99e56dbd9689464e\">\n          iadcPosInputPortDPin5\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa0ea280095a61cf37f7205188f5f927f1\">\n          iadcPosInputPortDPin6\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fafc975193cc62ff1b9bcecc12e8d47d4d\">\n          iadcPosInputPortDPin7\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fad8298666bae2f38c8a9093737e7ac20f\">\n          iadcPosInputPortDPin8\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fab9dfe7010da5f37c44432ce5b03440e1\">\n          iadcPosInputPortDPin9\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa424d2851a2ee85d44a90af427f3ce555\">\n          iadcPosInputPortDPin10\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa268c7f161c6042ad665f7a772ede517f\">\n          iadcPosInputPortDPin11\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fac573b570fd86e8dded990c275a43cfe3\">\n          iadcPosInputPortDPin12\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa7d24b8bea5fd4ff4e4d6a253bd2e975b\">\n          iadcPosInputPortDPin13\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa15de0b8fd79a1ee34c3c0b8e36df5568\">\n          iadcPosInputPortDPin14\n         </a>\n         ,\n         <br>\n         <a class=\"el\" href=\"#ggab3230940cb6abe6a598d926f6503442fa2b71bc2d42f7279aa5f908c725cbdfc1\">\n          iadcPosInputPortDPin15\n         </a>\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC positive port selection.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga81517741e2dd7744d21850a22c400c0d\">\n          IADC_Cmd_t\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#gga81517741e2dd7744d21850a22c400c0daefd7dba8fac43ca855fbd2387dff9aa3\">\n          iadcCmdStartSingle\n         </a>\n         = IADC_CMD_SINGLESTART,\n         <br>\n         <a class=\"el\" href=\"#gga81517741e2dd7744d21850a22c400c0da9bc52aecc8a8c6d6eecb987aa47d31df\">\n          iadcCmdStopSingle\n         </a>\n         = IADC_CMD_SINGLESTOP,\n         <br>\n         <a class=\"el\" href=\"#gga81517741e2dd7744d21850a22c400c0dad1684dca867a37b754fef283f05c84a3\">\n          iadcCmdStartScan\n         </a>\n         = IADC_CMD_SCANSTART,\n         <br>\n         <a class=\"el\" href=\"#gga81517741e2dd7744d21850a22c400c0dadad05c1fbc09dc4719d4679726d0eeb3\">\n          iadcCmdStopScan\n         </a>\n         = IADC_CMD_SCANSTOP,\n         <br>\n         <a class=\"el\" href=\"#gga81517741e2dd7744d21850a22c400c0da6a76fdebce42c191bb96c9e165e58757\">\n          iadcCmdEnableTimer\n         </a>\n         = IADC_CMD_TIMEREN,\n         <br>\n         <a class=\"el\" href=\"#gga81517741e2dd7744d21850a22c400c0da88ec237337b12b2390ae2559d4ff9123\">\n          iadcCmdDisableTimer\n         </a>\n         = IADC_CMD_TIMERDIS\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC Commands.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gac0395e857a03877c0a335c552a774752\">\n          IADC_CfgAdcMode_t\n         </a>\n         {\n         <a class=\"el\" href=\"#ggac0395e857a03877c0a335c552a774752a53554a3d8d222b29f0f47cae8d115002\">\n          iadcCfgModeNormal\n         </a>\n         = _IADC_CFG_ADCMODE_NORMAL\n }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC Configuration.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga248af70c70659ca5fd7f56fca059f492\">\n          IADC_CfgOsrHighSpeed_t\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#gga248af70c70659ca5fd7f56fca059f492a6a8feae9a77e6961975bb5e4f09c1001\">\n          iadcCfgOsrHighSpeed2x\n         </a>\n         = _IADC_CFG_OSRHS_HISPD2,\n         <br>\n         <a class=\"el\" href=\"#gga248af70c70659ca5fd7f56fca059f492a74c032650280ee05edc74673232bff31\">\n          iadcCfgOsrHighSpeed4x\n         </a>\n         = _IADC_CFG_OSRHS_HISPD4,\n         <br>\n         <a class=\"el\" href=\"#gga248af70c70659ca5fd7f56fca059f492ad9bb317662fa788ddaf1e8ea506d86a9\">\n          iadcCfgOsrHighSpeed8x\n         </a>\n         = _IADC_CFG_OSRHS_HISPD8,\n         <br>\n         <a class=\"el\" href=\"#gga248af70c70659ca5fd7f56fca059f492a6119181df9ca5acdfe3cbb19dc177365\">\n          iadcCfgOsrHighSpeed16x\n         </a>\n         = _IADC_CFG_OSRHS_HISPD16,\n         <br>\n         <a class=\"el\" href=\"#gga248af70c70659ca5fd7f56fca059f492aac20fe5eb158a5f3059d4b73c6b69b9c\">\n          iadcCfgOsrHighSpeed32x\n         </a>\n         = _IADC_CFG_OSRHS_HISPD32,\n         <br>\n         <a class=\"el\" href=\"#gga248af70c70659ca5fd7f56fca059f492ac84cd47a5cfed0c64fef174f27c94dd2\">\n          iadcCfgOsrHighSpeed64x\n         </a>\n         = _IADC_CFG_OSRHS_HISPD64\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC Over sampling rate for high speed.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga63a0cd10e95207bc0aaaab88c5e01097\">\n          IADC_CfgAnalogGain_t\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#gga63a0cd10e95207bc0aaaab88c5e01097a7eff9c3b02fe2a181446552b4f26cb21\">\n          iadcCfgAnalogGain0P5x\n         </a>\n         = _IADC_CFG_ANALOGGAIN_ANAGAIN0P5,\n         <br>\n         <a class=\"el\" href=\"#gga63a0cd10e95207bc0aaaab88c5e01097ac70e3dadf103cff7f2500225e9e41e06\">\n          iadcCfgAnalogGain1x\n         </a>\n         = _IADC_CFG_ANALOGGAIN_ANAGAIN1,\n         <br>\n         <a class=\"el\" href=\"#gga63a0cd10e95207bc0aaaab88c5e01097addb52fc7356f6a901f44ec46533e426c\">\n          iadcCfgAnalogGain2x\n         </a>\n         = _IADC_CFG_ANALOGGAIN_ANAGAIN2,\n         <br>\n         <a class=\"el\" href=\"#gga63a0cd10e95207bc0aaaab88c5e01097ab9375e7cc15d005da6dc3ed11f6733db\">\n          iadcCfgAnalogGain3x\n         </a>\n         = _IADC_CFG_ANALOGGAIN_ANAGAIN3,\n         <br>\n         <a class=\"el\" href=\"#gga63a0cd10e95207bc0aaaab88c5e01097ad7dfc13e7c84e7dde8903efdb2804238\">\n          iadcCfgAnalogGain4x\n         </a>\n         = _IADC_CFG_ANALOGGAIN_ANAGAIN4\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC Analog Gain.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gac1803c998a59a350d10147b81f80d156\">\n          IADC_CfgReference_t\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#ggac1803c998a59a350d10147b81f80d156a0bbe253f4c3859eeaedf507243567f38\">\n          iadcCfgReferenceInt1V2\n         </a>\n         = _IADC_CFG_REFSEL_VBGR,\n         <br>\n         <a class=\"el\" href=\"#ggac1803c998a59a350d10147b81f80d156ac9adffd22270b80d2591fa02ab5c6347\">\n          iadcCfgReferenceExt1V25\n         </a>\n         = _IADC_CFG_REFSEL_VREF,\n         <br>\n         <a class=\"el\" href=\"#ggac1803c998a59a350d10147b81f80d156a35531c5cfcfd9a8829ec7aa0d55b39aa\">\n          iadcCfgReferenceVddx\n         </a>\n         = _IADC_CFG_REFSEL_VDDX,\n         <br>\n         <a class=\"el\" href=\"#ggac1803c998a59a350d10147b81f80d156af79b03fad3646bca315e3c25c2ec1a97\">\n          iadcCfgReferenceVddX0P8Buf\n         </a>\n         = _IADC_CFG_REFSEL_VDDX0P8BUF\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC Reference.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gadaad7387cbc0b5a22c631d54fa01d70c\">\n          IADC_CfgTwosComp_t\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#ggadaad7387cbc0b5a22c631d54fa01d70ca8c33776083aeb32162940f6589fd21e3\">\n          iadcCfgTwosCompAuto\n         </a>\n         = _IADC_CFG_TWOSCOMPL_AUTO,\n         <br>\n         <a class=\"el\" href=\"#ggadaad7387cbc0b5a22c631d54fa01d70cae9253452b2fc798c0389c21a350de2ae\">\n          iadcCfgTwosCompUnipolar\n         </a>\n         = _IADC_CFG_TWOSCOMPL_FORCEUNIPOLAR,\n         <br>\n         <a class=\"el\" href=\"#ggadaad7387cbc0b5a22c631d54fa01d70ca8e6f483d73d277d9bdbf702443ee2ebb\">\n          iadcCfgTwosCompBipolar\n         </a>\n         = _IADC_CFG_TWOSCOMPL_FORCEBIPOLAR\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC Two's complement results.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gab364568b748681b9a4a35f409bc437ec\">\n          IADC_TriggerSel_t\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#ggab364568b748681b9a4a35f409bc437eca3d6b9e5fd6d6b3c394430de7acc40bfb\">\n          iadcTriggerSelImmediate\n         </a>\n         = _IADC_TRIGGER_SCANTRIGSEL_IMMEDIATE,\n         <br>\n         <a class=\"el\" href=\"#ggab364568b748681b9a4a35f409bc437ecae3e95441deaed701de9ce834fa3bdd6b\">\n          iadcTriggerSelTimer\n         </a>\n         = _IADC_TRIGGER_SCANTRIGSEL_TIMER,\n         <br>\n         <a class=\"el\" href=\"#ggab364568b748681b9a4a35f409bc437eca8c526a236a3964374b05b5ed99929cba\">\n          iadcTriggerSelPrs0SameClk\n         </a>\n         = _IADC_TRIGGER_SCANTRIGSEL_PRSCLKGRP,\n         <br>\n         <a class=\"el\" href=\"#ggab364568b748681b9a4a35f409bc437ecaa2d04006a204c1aca31f9dfad9c935a4\">\n          iadcTriggerSelPrs0PosEdge\n         </a>\n         = _IADC_TRIGGER_SCANTRIGSEL_PRSPOS,\n         <br>\n         <a class=\"el\" href=\"#ggab364568b748681b9a4a35f409bc437ecab22e9480531b1d8d9adf801e77833ba1\">\n          iadcTriggerSelPrs0NegEdge\n         </a>\n         = _IADC_TRIGGER_SCANTRIGSEL_PRSNEG\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC trigger action.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga9d9fa77f1c78fbf4f88331806d383a17\">\n          IADC_TriggerAction_t\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#gga9d9fa77f1c78fbf4f88331806d383a17a976a78c46767f1bc979907b9217ef4aa\">\n          iadcTriggerActionOnce\n         </a>\n         = _IADC_TRIGGER_SCANTRIGACTION_ONCE,\n         <br>\n         <a class=\"el\" href=\"#gga9d9fa77f1c78fbf4f88331806d383a17ade1a00f0bbe43c2e98c45545491c62eb\">\n          iadcTriggerActionContinuous\n         </a>\n         = _IADC_TRIGGER_SCANTRIGACTION_CONTINUOUS\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC trigger action.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga0e8b39e509a9e8c6804b3a280b647e7d\">\n          IADC_FifoCfgDvl_t\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#gga0e8b39e509a9e8c6804b3a280b647e7daa5a3930417a3c70d4c94498d98770e8a\">\n          iadcFifoCfgDvl1\n         </a>\n         = _IADC_SCANFIFOCFG_DVL_VALID1,\n         <br>\n         <a class=\"el\" href=\"#gga0e8b39e509a9e8c6804b3a280b647e7dab1c85cff9a1d94c95b9d95f6f5f19a05\">\n          iadcFifoCfgDvl2\n         </a>\n         = _IADC_SCANFIFOCFG_DVL_VALID2,\n         <br>\n         <a class=\"el\" href=\"#gga0e8b39e509a9e8c6804b3a280b647e7daca98a3d46552ebfc4a95358534bdae8f\">\n          iadcFifoCfgDvl3\n         </a>\n         = _IADC_SCANFIFOCFG_DVL_VALID3,\n         <br>\n         <a class=\"el\" href=\"#gga0e8b39e509a9e8c6804b3a280b647e7da6c8cf1513d8f3230cf79774b736e01c6\">\n          iadcFifoCfgDvl4\n         </a>\n         = _IADC_SCANFIFOCFG_DVL_VALID4\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         IADC data valid level before requesting DMA transfer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <h2 class=\"groupheader\">\n      Function Documentation\n     </h2>\n     <a id=\"gaa95a0c387dbf82c83aaa216209c8dfde\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaa95a0c387dbf82c83aaa216209c8dfde\">\n        ◆\n       </a>\n      </span>\n      IADC_init()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void IADC_init\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-init-t\" target=\"_blank\">\n            IADC_Init_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            init,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-all-configs-t\" target=\"_blank\">\n            IADC_AllConfigs_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            allConfigs\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Initialize IADC.\n       </p>\n       <p>\n        Initializes common parts for both single conversion and scan sequence. In addition, single and/or scan control configuration must be done, please refer to\n        <a class=\"el\" href=\"#ga6daf8d5af3a2ab98195e2ed34018b337\">\n         IADC_initSingle()\n        </a>\n        and\n        <a class=\"el\" href=\"#ga89338e759472e4372c4b35876733bea3\">\n         IADC_initScan()\n        </a>\n        respectively.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         This function will stop any ongoing conversions.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              init\n             </code>\n            </td>\n            <td>\n             Pointer to IADC initialization structure.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              allConfigs\n             </code>\n            </td>\n            <td>\n             Pointer to structure holding all configs.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga8da3d2e45b0a5ae7622dcf1b30c68c2c\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga8da3d2e45b0a5ae7622dcf1b30c68c2c\">\n        ◆\n       </a>\n      </span>\n      IADC_reset()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void IADC_reset\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Reset IADC to same state as after a HW reset.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga89338e759472e4372c4b35876733bea3\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga89338e759472e4372c4b35876733bea3\">\n        ◆\n       </a>\n      </span>\n      IADC_initScan()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void IADC_initScan\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-init-scan-t\" target=\"_blank\">\n            IADC_InitScan_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            init,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-scan-table-t\" target=\"_blank\">\n            IADC_ScanTable_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            scanTable\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Initialize IADC scan sequence.\n       </p>\n       <p>\n        This function will configure scan mode and set up entries in the scan table. The scan table mask can be updated by calling IADC_updateScanMask.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         This function will stop any ongoing conversions.\n        </dd>\n        <dd>\n         If an even numbered pin is selected for the positive input, the negative input must use an odd numbered pin and vice versa.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              init\n             </code>\n            </td>\n            <td>\n             Pointer to IADC initialization structure.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              scanTable\n             </code>\n            </td>\n            <td>\n             Pointer to IADC scan table structure.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga0054dbc14b8abd889c5a2e6fc8f490ab\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga0054dbc14b8abd889c5a2e6fc8f490ab\">\n        ◆\n       </a>\n      </span>\n      IADC_updateScanEntry()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void IADC_updateScanEntry\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint8_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            id,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-scan-table-entry-t\" target=\"_blank\">\n            IADC_ScanTableEntry_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            entry\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Add/update entry in scan table.\n       </p>\n       <p>\n        This function will update or add an entry in the scan table with a specific ID.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         This function will stop any ongoing conversions.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              id\n             </code>\n            </td>\n            <td>\n             ID of scan table entry to add.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              entry\n             </code>\n            </td>\n            <td>\n             Pointer to scan table entry structure.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga283cbd96f53a6e36810f326a070bb6f6\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga283cbd96f53a6e36810f326a070bb6f6\">\n        ◆\n       </a>\n      </span>\n      IADC_setScanMask()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void IADC_setScanMask\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            mask\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Set mask of IADC scan table entries to include in scan.\n       </p>\n       <p>\n        Set mask of scan table entries to include in next scan. This function can be called while scan conversions are ongoing, but the new scan mask will take effect once the ongoing scan is completed.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              mask\n             </code>\n            </td>\n            <td>\n             Mask of scan table entries to include in scan.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga6daf8d5af3a2ab98195e2ed34018b337\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga6daf8d5af3a2ab98195e2ed34018b337\">\n        ◆\n       </a>\n      </span>\n      IADC_initSingle()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void IADC_initSingle\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-init-single-t\" target=\"_blank\">\n            IADC_InitSingle_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            init,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-single-input-t\" target=\"_blank\">\n            IADC_SingleInput_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            input\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Initialize single IADC conversion.\n       </p>\n       <p>\n        This function will initialize the single conversion and configure the single input selection.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         This function will stop any ongoing conversions.\n        </dd>\n        <dd>\n         If an even numbered pin is selected for the positive input, the negative input must use an odd numbered pin and vice versa.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              init\n             </code>\n            </td>\n            <td>\n             Pointer to IADC single initialization structure.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              input\n             </code>\n            </td>\n            <td>\n             Pointer to IADC single input selection initialization structure.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga25ad70bb7cb7efb107cedf481e37bfca\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga25ad70bb7cb7efb107cedf481e37bfca\">\n        ◆\n       </a>\n      </span>\n      IADC_updateSingleInput()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void IADC_updateSingleInput\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-single-input-t\" target=\"_blank\">\n            IADC_SingleInput_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            input\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Update IADC single input selection.\n       </p>\n       <p>\n        This function updates the single input selection. The function can be called while single and/or scan conversions are ongoing and the new input configuration will take place on the next single conversion.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         If an even numbered pin is selected for the positive input, the negative input must use an odd numbered pin and vice versa.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              input\n             </code>\n            </td>\n            <td>\n             Pointer to single input selection structure.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga95ab149c3aece1c95a506dc736b8e818\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga95ab149c3aece1c95a506dc736b8e818\">\n        ◆\n       </a>\n      </span>\n      IADC_calcSrcClkPrescale()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           uint8_t IADC_calcSrcClkPrescale\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            srcClkFreq,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            cmuClkFreq\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Calculate prescaler for CLK_SRC_ADC high speed clock.\n       </p>\n       <p>\n        The IADC high speed clock is given by: CLK_SRC_ADC / (srcClkPrescaler + 1).\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              srcClkFreq\n             </code>\n            </td>\n            <td>\n             CLK_SRC_ADC frequency wanted. The frequency will automatically be adjusted to be within valid range according to reference manual.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              cmuClkFreq\n             </code>\n            </td>\n            <td>\n             Frequency in Hz of reference CLK_CMU_ADC. Set to 0 to use currently defined CMU clock setting for the IADC.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Divider value to use for IADC in order to achieve a high speed clock value &lt;=\n         <code>\n          srcClkFreq\n         </code>\n         .\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaf85f5c99a14ccafb7729cdcd78fe9d8f\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf85f5c99a14ccafb7729cdcd78fe9d8f\">\n        ◆\n       </a>\n      </span>\n      IADC_calcAdcClkPrescale()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           uint32_t IADC_calcAdcClkPrescale\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            adcClkFreq,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            cmuClkFreq,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"#gac0395e857a03877c0a335c552a774752\">\n            IADC_CfgAdcMode_t\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            adcMode,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint8_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            srcClkPrescaler\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Calculate prescaler for ADC_CLK clock.\n       </p>\n       <p>\n        The ADC_CLK is given by: CLK_SRC_ADC / (adcClkprescale + 1).\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              adcClkFreq\n             </code>\n            </td>\n            <td>\n             ADC_CLK frequency wanted. The frequency will automatically be adjusted to be within valid range according to reference manual.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              cmuClkFreq\n             </code>\n            </td>\n            <td>\n             Frequency in Hz of CLK_CMU_ADC Set to 0 to use currently defined IADC clock setting (in CMU).\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              adcMode\n             </code>\n            </td>\n            <td>\n             Mode for IADC config.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              srcClkPrescaler\n             </code>\n            </td>\n            <td>\n             Precaler setting for ADC_CLK\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Divider value to use for IADC in order to achieve a ADC_CLK frequency &lt;=\n         <code>\n          adcClkFreq\n         </code>\n         .\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga82909089ae9dfaa395eadb5430cd4223\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga82909089ae9dfaa395eadb5430cd4223\">\n        ◆\n       </a>\n      </span>\n      IADC_calcTimebase()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           uint8_t IADC_calcTimebase\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            cmuClkFreq\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Calculate timebase value in order to get a timebase providing at least 1us.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              cmuClkFreq\n             </code>\n            </td>\n            <td>\n             Frequency in Hz of reference CLK_CMU_ADC clock. Set to 0 to use currently defined CMU clock setting for the IADC.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Timebase value to use for IADC in order to achieve at least 1 us.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gacdacbca6728d94d53d28f68710766f79\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gacdacbca6728d94d53d28f68710766f79\">\n        ◆\n       </a>\n      </span>\n      IADC_readSingleResult()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-result-t\" target=\"_blank\">\n            IADC_Result_t\n           </a>\n           IADC_readSingleResult\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Read most recent single conversion result.\n       </p>\n       <p>\n        The result struct includes both the data and the ID (0x20) if showId was set when initializing single mode. Calling this function will not affect the state of the single data FIFO.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Check data valid flag before calling this function.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Single conversion result struct holding data and id.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga961372b54bb66f3b13afb764aa9f5271\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga961372b54bb66f3b13afb764aa9f5271\">\n        ◆\n       </a>\n      </span>\n      IADC_pullSingleFifoResult()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-result-t\" target=\"_blank\">\n            IADC_Result_t\n           </a>\n           IADC_pullSingleFifoResult\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Pull result from single data FIFO.\n       </p>\n       <p>\n        The result struct includes both the data and the ID (0x20) if showId was set when initializing single mode.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Check data valid flag before calling this function.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Single conversion result struct holding data and id.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gabe6a447ba1cb484ec66528a60e2ebc36\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gabe6a447ba1cb484ec66528a60e2ebc36\">\n        ◆\n       </a>\n      </span>\n      IADC_readScanResult()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-result-t\" target=\"_blank\">\n            IADC_Result_t\n           </a>\n           IADC_readScanResult\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Read most recent scan conversion result.\n       </p>\n       <p>\n        The result struct includes both the data and the ID (0x20) if showId was set when initializing scan entry. Calling this function will not affect the state of the scan data FIFO.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Check data valid flag before calling this function.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Scan conversion result struct holding data and id.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga52402a2c653644c11350c967514fc23a\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga52402a2c653644c11350c967514fc23a\">\n        ◆\n       </a>\n      </span>\n      IADC_pullScanFifoResult()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i-a-d-c-result-t\" target=\"_blank\">\n            IADC_Result_t\n           </a>\n           IADC_pullScanFifoResult\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           IADC_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            iadc\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Pull result from scan data FIFO.\n       </p>\n       <p>\n        The result struct includes both the data and the ID (0x20) if showId was set when initializing scan entry.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Check data valid flag before calling this function.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Scan conversion result struct holding data and id.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga37afb392a921d006e4a3a2cdab8fbfc7\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga37afb392a921d006e4a3a2cdab8fbfc7\">\n        ◆\n       </a>\n      </span>\n      IADC_getReferenceVoltage()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           uint32_t IADC_getReferenceVoltage\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"#gac1803c998a59a350d10147b81f80d156\">\n            IADC_CfgReference_t\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            reference\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get reference voltage selection.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              reference\n             </code>\n            </td>\n            <td>\n             IADC Reference selection.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         IADC reference voltage in millivolts.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga9f2f6460cc3e18ca1c52a70c537cbf03\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga9f2f6460cc3e18ca1c52a70c537cbf03\">\n        ◆\n       </a>\n      </span>\n      IADC_pullSingleFifoData()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint32_t IADC_pullSingleFifoData\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Pull data from single data FIFO.\n       </p>\n       <p>\n        If showId was set when initializing single mode, the results will contain the ID (0x20).\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Check data valid flag before calling this function.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Single conversion data.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga5375e06272c149b6caad0b3ea232e1be\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga5375e06272c149b6caad0b3ea232e1be\">\n        ◆\n       </a>\n      </span>\n      IADC_readSingleData()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint32_t IADC_readSingleData\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Read most recent single conversion data.\n       </p>\n       <p>\n        If showId was set when initializing single mode, the data will contain the ID (0x20). Calling this function will not affect the state of the single data FIFO.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Check data valid flag before calling this function.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Single conversion data.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga93a8f8c91e1eb97693a87d4f1b21b35c\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga93a8f8c91e1eb97693a87d4f1b21b35c\">\n        ◆\n       </a>\n      </span>\n      IADC_pullScanFifoData()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint32_t IADC_pullScanFifoData\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Pull data from scan data FIFO.\n       </p>\n       <p>\n        If showId was set for the scan entry initialization, the data will contain the ID of the scan entry.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Check data valid flag before calling this function.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Scan conversion data.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga1fdead030312cf9355e9074c20c52cb3\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga1fdead030312cf9355e9074c20c52cb3\">\n        ◆\n       </a>\n      </span>\n      IADC_readScanData()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint32_t IADC_readScanData\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Read most recent scan conversion data.\n       </p>\n       <p>\n        If showId was set for the scan entry initialization, the data will contain the ID of the scan entry. Calling this function will not affect the state of the scan data FIFO.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Check data valid flag before calling this function.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Scan conversion data.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga1a3188f2c082a114eaf6c61b8ae404b1\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga1a3188f2c082a114eaf6c61b8ae404b1\">\n        ◆\n       </a>\n      </span>\n      IADC_clearInt()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void IADC_clearInt\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Clear one or more pending IADC interrupts.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              flags\n             </code>\n            </td>\n            <td>\n             Pending IADC interrupt source to clear. Use a bitwise logic OR combination of valid interrupt flags for the IADC module (IADC_IF_nnn).\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga33ba2f77c03dc8593bdaf450af2d45dc\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga33ba2f77c03dc8593bdaf450af2d45dc\">\n        ◆\n       </a>\n      </span>\n      IADC_disableInt()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void IADC_disableInt\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Disable one or more IADC interrupts.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              flags\n             </code>\n            </td>\n            <td>\n             IADC interrupt sources to disable. Use a bitwise logic OR combination of valid interrupt flags for the IADC module (IADC_IF_nnn).\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga37a0290c58088655951695e9f6fd15ba\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga37a0290c58088655951695e9f6fd15ba\">\n        ◆\n       </a>\n      </span>\n      IADC_enableInt()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void IADC_enableInt\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Enable one or more IADC interrupts.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Depending on the use, a pending interrupt may already be set prior to enabling the interrupt. Consider using IADC_intClear() prior to enabling if such a pending interrupt should be ignored.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              flags\n             </code>\n            </td>\n            <td>\n             IADC interrupt sources to enable. Use a bitwise logic OR combination of valid interrupt flags for the IADC module (IADC_IF_nnn).\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga3064d6dc58382114173c37c08aa648f0\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga3064d6dc58382114173c37c08aa648f0\">\n        ◆\n       </a>\n      </span>\n      IADC_getInt()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint32_t IADC_getInt\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get pending IADC interrupt flags.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         The event bits are not cleared by the use of this function.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         IADC interrupt sources pending. A bitwise logic OR combination of valid interrupt flags for the IADC module (IADC_IF_nnn).\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gad3ededd9bfa76e4a48d19843b2265dda\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gad3ededd9bfa76e4a48d19843b2265dda\">\n        ◆\n       </a>\n      </span>\n      IADC_getEnabledInt()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint32_t IADC_getEnabledInt\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get enabled and pending IADC interrupt flags.\n       </p>\n       <p>\n        Useful for handling more interrupt sources in the same interrupt handler.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Interrupt flags are not cleared by the use of this function.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Pending and enabled IADC interrupt sources. The return value is the bitwise AND combination of\n         <ul>\n          <li>\n           the OR combination of enabled interrupt sources in IADCx_IEN_nnn register (IADCx_IEN_nnn) and\n          </li>\n          <li>\n           the OR combination of valid interrupt flags of the IADC module (IADCx_IF_nnn).\n          </li>\n         </ul>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gab767a70e50481fc13bfeaa7fa5ee912c\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gab767a70e50481fc13bfeaa7fa5ee912c\">\n        ◆\n       </a>\n      </span>\n      IADC_setInt()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void IADC_setInt\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Set one or more pending IADC interrupts from SW.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              flags\n             </code>\n            </td>\n            <td>\n             IADC interrupt sources to set to pending. Use a bitwise logic OR combination of valid interrupt flags for the IADC module (IADC_IF_nnn).\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gad7b8fab9d9a61b220cad1c298b052889\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gad7b8fab9d9a61b220cad1c298b052889\">\n        ◆\n       </a>\n      </span>\n      IADC_command()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void IADC_command\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               <a class=\"el\" href=\"#ga81517741e2dd7744d21850a22c400c0d\">\n                IADC_Cmd_t\n               </a>\n              </td>\n              <td class=\"paramname\">\n               <code>\n                cmd\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Start/stop scan sequence, single conversion and/or timer.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              cmd\n             </code>\n            </td>\n            <td>\n             Command to be performed.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga08aca161b3763f8e4c55a291417e9f98\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga08aca161b3763f8e4c55a291417e9f98\">\n        ◆\n       </a>\n      </span>\n      IADC_getScanMask()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint32_t IADC_getScanMask\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get the scan mask currently used in the IADC.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Mask of scan table entries currently included in scan.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gad5e0e3835c579efe6ff44210148d2203\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gad5e0e3835c579efe6ff44210148d2203\">\n        ◆\n       </a>\n      </span>\n      IADC_getStatus()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint32_t IADC_getStatus\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get status bits of IADC.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         IADC status bits\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga4e09cacd7d993f70d0ceb0eacf615105\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga4e09cacd7d993f70d0ceb0eacf615105\">\n        ◆\n       </a>\n      </span>\n      IADC_getSingleFifoCnt()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint8_t IADC_getSingleFifoCnt\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get the number of elements in the IADC single FIFO.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Number of elements in single FIFO\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga9fcc5a064a2988d2c5fc76d704f6087c\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga9fcc5a064a2988d2c5fc76d704f6087c\">\n        ◆\n       </a>\n      </span>\n      IADC_getScanFifoCnt()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint8_t IADC_getScanFifoCnt\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               IADC_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                iadc\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get the number of elements in the IADC scan FIFO.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              iadc\n             </code>\n            </td>\n            <td>\n             Pointer to IADC peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Number of elements in scan FIFO\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga3bd726aca10a7e3a3d2e3dda9c277cb3\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga3bd726aca10a7e3a3d2e3dda9c277cb3\">\n        ◆\n       </a>\n      </span>\n      IADC_portPinToNegInput()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               <a class=\"el\" href=\"#gabda64eab4e23b86883428db9060dbf74\">\n                IADC_NegInput_t\n               </a>\n               IADC_portPinToNegInput\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/group-gpio#gafe496583dfd425fc03c413d49459efe6\" target=\"_blank\">\n                GPIO_Port_TypeDef\n               </a>\n              </td>\n              <td class=\"paramname\">\n               <code>\n                port,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint8_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                pin\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Convert the GPIO port/pin to IADC negative input selection.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              port\n             </code>\n            </td>\n            <td>\n             GPIO port\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              pin\n             </code>\n            </td>\n            <td>\n             GPIO in\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         IADC negative input selection\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga67d47e7cda4569ab573e9fe3b4ac2019\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga67d47e7cda4569ab573e9fe3b4ac2019\">\n        ◆\n       </a>\n      </span>\n      IADC_portPinToPosInput()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               <a class=\"el\" href=\"#gab3230940cb6abe6a598d926f6503442f\">\n                IADC_PosInput_t\n               </a>\n               IADC_portPinToPosInput\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/group-gpio#gafe496583dfd425fc03c413d49459efe6\" target=\"_blank\">\n                GPIO_Port_TypeDef\n               </a>\n              </td>\n              <td class=\"paramname\">\n               <code>\n                port,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint8_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                pin\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Convert the GPIO port/pin to IADC positive input selection.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              port\n             </code>\n            </td>\n            <td>\n             GPIO port\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              pin\n             </code>\n            </td>\n            <td>\n             GPIO in\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         IADC positive input selection\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <h2 class=\"groupheader\">\n      Macro Definition Documentation\n     </h2>\n     <a id=\"ga2920221ff8a2bc5a7adcff4ee61f8f6d\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga2920221ff8a2bc5a7adcff4ee61f8f6d\">\n        ◆\n       </a>\n      </span>\n      IADC_INIT_DEFAULT\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define IADC_INIT_DEFAULT\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <b>\n        Value:\n       </b>\n       <div class=\"fragment\">\n        <div class=\"line\">\n         {                                                                         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* IADC clock not disabled on PRS0*/\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* IADC clock not disabled on PRS1 */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* Do not halt during debug */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         iadcWarmupNormal,\n         <span class=\"comment\">\n          /* IADC shutdown after each conversion. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         0,\n         <span class=\"comment\">\n          /* Calculate timebase. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         0,\n         <span class=\"comment\">\n          /* Max IADC clock rate. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         _IADC_TIMER_TIMER_DEFAULT,\n         <span class=\"comment\">\n          /* Use HW default value. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         _IADC_CMPTHR_ADGT_DEFAULT,\n         <span class=\"comment\">\n          /* Use HW default value. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         _IADC_CMPTHR_ADLT_DEFAULT,\n         <span class=\"comment\">\n          /* Use HW default value. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         }\n        </div>\n       </div>\n       <p>\n        Default config for IADC init structure.\n       </p>\n      </div>\n     </div>\n     <a id=\"gaf17aa0ddca20b272d09a575a8db5f04f\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf17aa0ddca20b272d09a575a8db5f04f\">\n        ◆\n       </a>\n      </span>\n      IADC_CONFIG_DEFAULT\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define IADC_CONFIG_DEFAULT\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <b>\n        Value:\n       </b>\n       <div class=\"fragment\">\n        <div class=\"line\">\n         {                                                                       \\\n        </div>\n        <div class=\"line\">\n         iadcCfgModeNormal,\n         <span class=\"comment\">\n          /* Normal mode for IADC. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         iadcCfgOsrHighSpeed2x,\n         <span class=\"comment\">\n          /* 2x high speed over sampling. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         iadcCfgAnalogGain1x,\n         <span class=\"comment\">\n          /* 1x analog gain. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         iadcCfgReferenceInt1V2,\n         <span class=\"comment\">\n          /* Internal 1.2V band gap reference. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         iadcCfgTwosCompAuto,\n         <span class=\"comment\">\n          /* Automatic Two's Complement. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         0,\n         <span class=\"comment\">\n          /* Max IADC analog clock rate. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         1210\n         <span class=\"comment\">\n          /* Vref expressed in millivolts. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         }\n        </div>\n       </div>\n       <p>\n        Default IADC config structure.\n       </p>\n      </div>\n     </div>\n     <a id=\"ga93035a36d674c90bbb04c8c9d7292360\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga93035a36d674c90bbb04c8c9d7292360\">\n        ◆\n       </a>\n      </span>\n      IADC_ALLCONFIGS_DEFAULT\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define IADC_ALLCONFIGS_DEFAULT\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <b>\n        Value:\n       </b>\n       <div class=\"fragment\">\n        <div class=\"line\">\n         {                             \\\n        </div>\n        <div class=\"line\">\n         {                           \\\n        </div>\n        <div class=\"line\">\n         IADC_CONFIG_DEFAULT,      \\\n        </div>\n        <div class=\"line\">\n         IADC_CONFIG_DEFAULT       \\\n        </div>\n        <div class=\"line\">\n         }                           \\\n        </div>\n        <div class=\"line\">\n         }\n        </div>\n       </div>\n       <p>\n        Default IADC sructure for all configs.\n       </p>\n      </div>\n     </div>\n     <a id=\"ga25a8a2f6b4f6bec5ffed996fdc3d3385\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga25a8a2f6b4f6bec5ffed996fdc3d3385\">\n        ◆\n       </a>\n      </span>\n      IADC_INITSCAN_DEFAULT\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define IADC_INITSCAN_DEFAULT\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <b>\n        Value:\n       </b>\n       <div class=\"fragment\">\n        <div class=\"line\">\n         {                                                                          \\\n        </div>\n        <div class=\"line\">\n         iadcAlignRight12,\n         <span class=\"comment\">\n          /* Results 12-bit right aligned */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* Do not show ID in result */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         iadcFifoCfgDvl4,\n         <span class=\"comment\">\n          /* Use HW default value. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* Do not wake up DMA on scan FIFO DVL */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         iadcTriggerSelImmediate,\n         <span class=\"comment\">\n          /* Start scan immediately on trigger */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         iadcTriggerActionOnce,\n         <span class=\"comment\">\n          /* Convert once on scan trigger */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false\n         <span class=\"comment\">\n          /* Do not start scan queue */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         }\n        </div>\n       </div>\n       <p>\n        Default config for IADC scan init structure.\n       </p>\n      </div>\n     </div>\n     <a id=\"gac4f5e953de53d33f4b7e24677bd64913\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gac4f5e953de53d33f4b7e24677bd64913\">\n        ◆\n       </a>\n      </span>\n      IADC_INITSINGLE_DEFAULT\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define IADC_INITSINGLE_DEFAULT\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <b>\n        Value:\n       </b>\n       <div class=\"fragment\">\n        <div class=\"line\">\n         {                                                                              \\\n        </div>\n        <div class=\"line\">\n         iadcAlignRight12,\n         <span class=\"comment\">\n          /* Results 12-bit right aligned */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* Do not show ID in result */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         iadcFifoCfgDvl4,\n         <span class=\"comment\">\n          /* Use HW default value. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* Do not wake up DMA on single FIFO DVL */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         iadcTriggerSelImmediate,\n         <span class=\"comment\">\n          /* Start single immediately on trigger */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         iadcTriggerActionOnce,\n         <span class=\"comment\">\n          /* Convert once on single trigger */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* No tailgating */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false\n         <span class=\"comment\">\n          /* Do not start single queue */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         }\n        </div>\n       </div>\n       <p>\n        Default config for IADC single init structure.\n       </p>\n      </div>\n     </div>\n     <a id=\"gab5de6ca3a01129040bf1bbf1482b09ce\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gab5de6ca3a01129040bf1bbf1482b09ce\">\n        ◆\n       </a>\n      </span>\n      IADC_SINGLEINPUT_DEFAULT\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define IADC_SINGLEINPUT_DEFAULT\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <b>\n        Value:\n       </b>\n       <div class=\"fragment\">\n        <div class=\"line\">\n         {                                                           \\\n        </div>\n        <div class=\"line\">\n         iadcNegInputGnd,\n         <span class=\"comment\">\n          /* Negative input GND */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         iadcPosInputGnd,\n         <span class=\"comment\">\n          /* Positive input GND */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         0,\n         <span class=\"comment\">\n          /* Config 0 */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false\n         <span class=\"comment\">\n          /* Do not compare results */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         }\n        </div>\n       </div>\n       <p>\n        Default config for IADC single input structure.\n       </p>\n      </div>\n     </div>\n     <a id=\"gae802efb3a2c2cf1c562034cea3aa383f\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gae802efb3a2c2cf1c562034cea3aa383f\">\n        ◆\n       </a>\n      </span>\n      IADC_SCANTABLEENTRY_DEFAULT\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define IADC_SCANTABLEENTRY_DEFAULT\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <b>\n        Value:\n       </b>\n       <div class=\"fragment\">\n        <div class=\"line\">\n         {                                              \\\n        </div>\n        <div class=\"line\">\n         iadcNegInputGnd,\n         <span class=\"comment\">\n          /* Negative input GND */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         iadcPosInputGnd,\n         <span class=\"comment\">\n          /* Positive input GND */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         0,\n         <span class=\"comment\">\n          /* Config 0 */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* Do not compare results */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false\n         <span class=\"comment\">\n          /* Do not include in scan */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         }\n        </div>\n       </div>\n       <p>\n        Default config for IADC scan table entry structure.\n       </p>\n      </div>\n     </div>\n     <a id=\"ga6d31c865e6325bf3d445bb78ccd5aab5\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga6d31c865e6325bf3d445bb78ccd5aab5\">\n        ◆\n       </a>\n      </span>\n      IADC_SCANTABLE_DEFAULT\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define IADC_SCANTABLE_DEFAULT\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <b>\n        Value:\n       </b>\n       <div class=\"fragment\">\n        <div class=\"line\">\n         {                                \\\n        </div>\n        <div class=\"line\">\n         {                              \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT, \\\n        </div>\n        <div class=\"line\">\n         IADC_SCANTABLEENTRY_DEFAULT  \\\n        </div>\n        <div class=\"line\">\n         }                              \\\n        </div>\n        <div class=\"line\">\n         }\n        </div>\n       </div>\n       <p>\n        Default IADC structure for scan table.\n       </p>\n      </div>\n     </div>\n     <h2 class=\"groupheader\">\n      Enumeration Type Documentation\n     </h2>\n     <a id=\"ga4487a975a2fac5be3613a29fb9f4a9a5\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga4487a975a2fac5be3613a29fb9f4a9a5\">\n        ◆\n       </a>\n      </span>\n      IADC_Warmup_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#ga4487a975a2fac5be3613a29fb9f4a9a5\">\n            IADC_Warmup_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Warm-up mode.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga4487a975a2fac5be3613a29fb9f4a9a5af220158c548e6e7c6c8459dcddf58610\">\n           </a>\n           iadcWarmupNormal\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            IADC shutdown after each conversion.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga4487a975a2fac5be3613a29fb9f4a9a5a5e3b9f87d46aae6afbe3723cd5b63e6d\">\n           </a>\n           iadcWarmupKeepInStandby\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            ADC is kept in standby mode between conversion.\n           </p>\n           <p>\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga4487a975a2fac5be3613a29fb9f4a9a5ad5bff89607aed8085e38eae230b74fc1\">\n           </a>\n           iadcWarmupKeepWarm\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            ADC and reference selected for scan mode kept warmup, allowing continuous conversion.\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"gac3000f2dd003cc23c74b7e8054e4550c\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gac3000f2dd003cc23c74b7e8054e4550c\">\n        ◆\n       </a>\n      </span>\n      IADC_Alignment_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#gac3000f2dd003cc23c74b7e8054e4550c\">\n            IADC_Alignment_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        IADC result alignment.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggac3000f2dd003cc23c74b7e8054e4550ca2f8fb7b73abde4bdfff3d88fb54fe71a\">\n           </a>\n           iadcAlignRight12\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            IADC results 12-bit right aligned\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggac3000f2dd003cc23c74b7e8054e4550cab902aecd539012fd4babed7d711dd931\">\n           </a>\n           iadcAlignLeft12\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            IADC results 12-bit left aligned\n            <br>\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"gabda64eab4e23b86883428db9060dbf74\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gabda64eab4e23b86883428db9060dbf74\">\n        ◆\n       </a>\n      </span>\n      IADC_NegInput_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#gabda64eab4e23b86883428db9060dbf74\">\n            IADC_NegInput_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        IADC negative input selection.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a80d2ce7610ec1ca853649ce2dbe4a94e\">\n           </a>\n           iadcNegInputGnd\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Ground\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a1e88b6db1413a7bfd3bc947d96afe22d\">\n           </a>\n           iadcNegInputGndaux\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Ground using even mux.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74ab9e2d4ca675b41e2348eb8b49c62965d\">\n           </a>\n           iadcNegInputPortAPin0\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 0.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a064d7ec41b93142945a5fdb00a187204\">\n           </a>\n           iadcNegInputPortAPin1\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 1.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74ab0d1dd6b558362637d3fbdda5a9a76e1\">\n           </a>\n           iadcNegInputPortAPin2\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 2.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a066b1519392cf81180e58b24c36f0a9a\">\n           </a>\n           iadcNegInputPortAPin3\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 3.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a94eb323ae48f650f591e7df249913fe3\">\n           </a>\n           iadcNegInputPortAPin4\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 4.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a1e3beb5c49ec484031d37e66dce8e32a\">\n           </a>\n           iadcNegInputPortAPin5\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 5.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74aa5b63e925197004929ebb108c2326090\">\n           </a>\n           iadcNegInputPortAPin6\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 6.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a99b1167d57583ed0a724eb20521e1e88\">\n           </a>\n           iadcNegInputPortAPin7\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 7.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a6814d915f14cbf1d939e6935db758512\">\n           </a>\n           iadcNegInputPortAPin8\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 8.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74ab9cea3e4fb21dfbf00608addf4ecdfbc\">\n           </a>\n           iadcNegInputPortAPin9\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 9.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a710e22ba34c938e6221320c00627232b\">\n           </a>\n           iadcNegInputPortAPin10\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 10.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74adf20dba7019cffad432fca49c8597979\">\n           </a>\n           iadcNegInputPortAPin11\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 11.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74ab5c7a02cdfc115639d0847e1befa0032\">\n           </a>\n           iadcNegInputPortAPin12\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 12.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a17398aeb74d5f29e4107965328bc9fae\">\n           </a>\n           iadcNegInputPortAPin13\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 13.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74accb63423740771ff3937a6c2dd50ab3d\">\n           </a>\n           iadcNegInputPortAPin14\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 14.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a7144f8a54332246b72697fdbee3f53d8\">\n           </a>\n           iadcNegInputPortAPin15\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 15.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a31d71c2c76078e276f124c81e13bff2f\">\n           </a>\n           iadcNegInputPortBPin0\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 0.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74af32719bf4ac0002f8f3bf29b16f11e62\">\n           </a>\n           iadcNegInputPortBPin1\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 1.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74aecccaf09c7e2e58f997f95dca4bb69bc\">\n           </a>\n           iadcNegInputPortBPin2\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 2.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a7c0e9084953a33649662ec1d5f036004\">\n           </a>\n           iadcNegInputPortBPin3\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 3.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a981d1e67dac39f707e4f1d99cec01376\">\n           </a>\n           iadcNegInputPortBPin4\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 4.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a64e27700d85a40adc0b9ad0375045fa8\">\n           </a>\n           iadcNegInputPortBPin5\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 5.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a74db42ce0af9d911b1b2c32228189a7d\">\n           </a>\n           iadcNegInputPortBPin6\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 6.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a6891cb1dfe348e39c39c3a8e507ac68d\">\n           </a>\n           iadcNegInputPortBPin7\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 7.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74aafc1a1716def4b69cb979110ccafccfa\">\n           </a>\n           iadcNegInputPortBPin8\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 8.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74aef59f829afe7d963505701d0aa8f3eec\">\n           </a>\n           iadcNegInputPortBPin9\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 9.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a7c5dda04994a6f7ec69d7261158f57dd\">\n           </a>\n           iadcNegInputPortBPin10\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 10.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74ad104965a8d9a70f2d381d081701e94a2\">\n           </a>\n           iadcNegInputPortBPin11\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 11.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a236ab871cf52bba1e565343df7248278\">\n           </a>\n           iadcNegInputPortBPin12\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 12.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a6ddadf662552698617f9e4052167e1dd\">\n           </a>\n           iadcNegInputPortBPin13\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 13.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74ada91761086cac6a48d136f413891d6fa\">\n           </a>\n           iadcNegInputPortBPin14\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 14.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a170199913d7740b385859bcd6fb2a6ab\">\n           </a>\n           iadcNegInputPortBPin15\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 15.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74aae0623199d45ae33c3d01f84aebeab88\">\n           </a>\n           iadcNegInputPortCPin0\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 0.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a87fc2c52b902641465b5aa12f73c3265\">\n           </a>\n           iadcNegInputPortCPin1\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 1.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74ababe798a879df614e983acd84bca8af9\">\n           </a>\n           iadcNegInputPortCPin2\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 2.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74abea1931e25e7facea9ad7593eb83a721\">\n           </a>\n           iadcNegInputPortCPin3\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 3.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a8adcd837a122b0b5ba4515846e3d7a19\">\n           </a>\n           iadcNegInputPortCPin4\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 4.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74ad22856188e1c9123bf08a2200496b445\">\n           </a>\n           iadcNegInputPortCPin5\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 5.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a8ca62d801997a1bd6083fe0d43dffd4b\">\n           </a>\n           iadcNegInputPortCPin6\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 6.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a3deeb7e894897393d50fd496bf83e469\">\n           </a>\n           iadcNegInputPortCPin7\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 7.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a80dc46d17176a99283484bbffe883d23\">\n           </a>\n           iadcNegInputPortCPin8\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 8.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74af376ce12067b32a073b5b249a41ea096\">\n           </a>\n           iadcNegInputPortCPin9\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 9.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74afea4499725af5ac765cfedc94e426ef9\">\n           </a>\n           iadcNegInputPortCPin10\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 10.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74af8deceabfa5ec09aced1e78c457f0cce\">\n           </a>\n           iadcNegInputPortCPin11\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 11.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a79a067950ba1a86a8e45281cc5d49027\">\n           </a>\n           iadcNegInputPortCPin12\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 12.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a6b31cc823debce0b10def41585b9cb04\">\n           </a>\n           iadcNegInputPortCPin13\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 13.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74ad2fa573257b6e8cb77268ca639afbe4b\">\n           </a>\n           iadcNegInputPortCPin14\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 14.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74af4a0c671b39341a79dfc28220b910d53\">\n           </a>\n           iadcNegInputPortCPin15\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 15.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74aa241ee8899b71c8948c3465901798abb\">\n           </a>\n           iadcNegInputPortDPin0\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 0.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74af83c9c69e4a8b66dbc12dda59e8045e0\">\n           </a>\n           iadcNegInputPortDPin1\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 1.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a44741e05f858cf8fa7dc2b3199450ea7\">\n           </a>\n           iadcNegInputPortDPin2\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 2.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74af25c0c6a2999fb9a91f591433a6286b0\">\n           </a>\n           iadcNegInputPortDPin3\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 3.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a08679f2b45e800c5cb6eccc543cd2f91\">\n           </a>\n           iadcNegInputPortDPin4\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 4.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74ad98a53b7933265beae313b05ab870faf\">\n           </a>\n           iadcNegInputPortDPin5\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 5.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a2020eb6da728ab7c7bfd44378b5ccef4\">\n           </a>\n           iadcNegInputPortDPin6\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 6.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74acd704df824c0247dd11b2d64eb42f70e\">\n           </a>\n           iadcNegInputPortDPin7\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 7.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a85a3cf825f20e65724fd25e5000f6fbf\">\n           </a>\n           iadcNegInputPortDPin8\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 8.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74af5cde2b1071c11101431f54eae391034\">\n           </a>\n           iadcNegInputPortDPin9\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 9.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74afaa4aa03f097a702228d52a001165335\">\n           </a>\n           iadcNegInputPortDPin10\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 10.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a8cb2816e5ee42bbc33faf8d6120893bf\">\n           </a>\n           iadcNegInputPortDPin11\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 11.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a5aa7ed5aff37a30d0bcba4e5e339656a\">\n           </a>\n           iadcNegInputPortDPin12\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 12.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a228056bbf94a3dd66718feea366afa26\">\n           </a>\n           iadcNegInputPortDPin13\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 13.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74a74ef0a313c1844031991782a51ea056e\">\n           </a>\n           iadcNegInputPortDPin14\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 14.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabda64eab4e23b86883428db9060dbf74ab4ebd9f75f678cba3fe756ccea564676\">\n           </a>\n           iadcNegInputPortDPin15\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 15.\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"gab3230940cb6abe6a598d926f6503442f\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gab3230940cb6abe6a598d926f6503442f\">\n        ◆\n       </a>\n      </span>\n      IADC_PosInput_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#gab3230940cb6abe6a598d926f6503442f\">\n            IADC_PosInput_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        IADC positive port selection.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa8df9b1a274ac0a31719504e198394112\">\n           </a>\n           iadcPosInputGnd\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Ground\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442facd03f526f9e6b3c19fe3d30c6a630e1b\">\n           </a>\n           iadcPosInputAvdd\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Avdd / 4\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442faf58baf1b96d92192340b27779509cfcb\">\n           </a>\n           iadcPosInputVddio\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Vddio / 4\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa6819c6d64b87cf1e7141e532dd0c7d8a\">\n           </a>\n           iadcPosInputVss\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Vss\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa1567e80dd6cd032150bf5bdcf99fa83f\">\n           </a>\n           iadcPosInputVssaux\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Vss\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442faa122cdd60853f7de1256e0aecbda8c3a\">\n           </a>\n           iadcPosInputDvdd\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Dvdd / 4\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fad61cd12eb4e1b6e4129e935ad9232953\">\n           </a>\n           iadcPosInputDecouple\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Decouple\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442faaa63ebe63fc173efd6937f6b3d6544f1\">\n           </a>\n           iadcPosInputPortAPin0\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 0.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442faafc0d855bc7186070fedfed9da88a81e\">\n           </a>\n           iadcPosInputPortAPin1\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 1.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa735c3c4b0e556de823f68251df0831a1\">\n           </a>\n           iadcPosInputPortAPin2\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 2.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fae38e95da9c522f4760aa8ca30675e041\">\n           </a>\n           iadcPosInputPortAPin3\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 3.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fab02fdceb177243c5558734eeb502cf4a\">\n           </a>\n           iadcPosInputPortAPin4\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 4.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa2e0caa2508b88378ce11efe4753d0175\">\n           </a>\n           iadcPosInputPortAPin5\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 5.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa38148148fdd991474742b5226aa66e15\">\n           </a>\n           iadcPosInputPortAPin6\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 6.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa037f0c2e3d60708aefda307870fcdda5\">\n           </a>\n           iadcPosInputPortAPin7\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 7.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa27d5e7a63629811801409cfdfb8eb597\">\n           </a>\n           iadcPosInputPortAPin8\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 8.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa5fd1893d84e4a972b38d5367557e4933\">\n           </a>\n           iadcPosInputPortAPin9\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 9.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fac151519574fc0a329117d60b730ea6d5\">\n           </a>\n           iadcPosInputPortAPin10\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 10.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa0f32185f05572751417d96d0c92aa2ab\">\n           </a>\n           iadcPosInputPortAPin11\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 11.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fac9812a7c60d8023f691ac951f5b94ea0\">\n           </a>\n           iadcPosInputPortAPin12\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 12.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa3be87f718d61e743d1dbfde022a7bfed\">\n           </a>\n           iadcPosInputPortAPin13\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 13.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fab579d0abc8d37e6b269490cc66d5318f\">\n           </a>\n           iadcPosInputPortAPin14\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 14.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa359588c2c1ad83cae24d6865259a5604\">\n           </a>\n           iadcPosInputPortAPin15\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port A pin 15.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fab1d3f423ebeaf07e738837627cdfe73e\">\n           </a>\n           iadcPosInputPortBPin0\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 0.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa14049e6b376e8fa4a7f752f288346621\">\n           </a>\n           iadcPosInputPortBPin1\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 1.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa9672e96f5d30f431eaf7f6ccc2ead424\">\n           </a>\n           iadcPosInputPortBPin2\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 2.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa2b9b42011e33b2dc4628a2d536d7d5f0\">\n           </a>\n           iadcPosInputPortBPin3\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 3.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442faa2e410c7ae55a17af3de6536407139f2\">\n           </a>\n           iadcPosInputPortBPin4\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 4.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa4bd2b64c5dcb3781fb77519154920061\">\n           </a>\n           iadcPosInputPortBPin5\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 5.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa055fdf7f5441910a74d9ea0718d50e73\">\n           </a>\n           iadcPosInputPortBPin6\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 6.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442faebb4cf6a710bd8bcbea6082bae2a2ef7\">\n           </a>\n           iadcPosInputPortBPin7\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 7.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fad76b16bbb4565575235ec6895e9cf69f\">\n           </a>\n           iadcPosInputPortBPin8\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 8.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fabb866c6b48d9db421e6e187376e2b96a\">\n           </a>\n           iadcPosInputPortBPin9\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 9.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fab5779cc07f565daa42b4520cea6382ea\">\n           </a>\n           iadcPosInputPortBPin10\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 10.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa42d0a8dda4ab69e75b06b5e12d526e51\">\n           </a>\n           iadcPosInputPortBPin11\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 11.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa23403bbdf2a2ca884154f26503ce8cad\">\n           </a>\n           iadcPosInputPortBPin12\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 12.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa9c420caf5c54e73ab1e6a955ddf3165e\">\n           </a>\n           iadcPosInputPortBPin13\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 13.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442faebd63ba94260ef85a7202bf442b8858b\">\n           </a>\n           iadcPosInputPortBPin14\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 14.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa8e99f0eaeb59af2e8c5ef55bdfb0030f\">\n           </a>\n           iadcPosInputPortBPin15\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port B pin 15.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa1877d2e4fdaed5ddf2ee38766a206d06\">\n           </a>\n           iadcPosInputPortCPin0\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 0.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fad48d8461ab80d9b048b99156307c072b\">\n           </a>\n           iadcPosInputPortCPin1\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 1.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa7a76880c5d3fa5cfeb7bfc38a3984000\">\n           </a>\n           iadcPosInputPortCPin2\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 2.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442faea17aee3bc08c7c097d51e566a743761\">\n           </a>\n           iadcPosInputPortCPin3\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 3.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fad2c80bd8a41512b90bfa172142c0f8b6\">\n           </a>\n           iadcPosInputPortCPin4\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 4.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa7dd54445082e41a9c343896fe21511bc\">\n           </a>\n           iadcPosInputPortCPin5\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 5.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fae0a86a9143ee0fd039ba1b521a697e8b\">\n           </a>\n           iadcPosInputPortCPin6\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 6.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa28a613931cec42143e01efaae3c3ba6c\">\n           </a>\n           iadcPosInputPortCPin7\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 7.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa244a31528ea50eeaa8ac0e4c2f965fbc\">\n           </a>\n           iadcPosInputPortCPin8\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 8.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442facde95492af138fac0555ee271a5790a6\">\n           </a>\n           iadcPosInputPortCPin9\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 9.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa98e658f6eccfa0e63550e95507ee2928\">\n           </a>\n           iadcPosInputPortCPin10\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 10.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442faf7887ce477b22407e28f9e09b294c5da\">\n           </a>\n           iadcPosInputPortCPin11\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 11.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa718948d7861d3ad8e46d20826788336c\">\n           </a>\n           iadcPosInputPortCPin12\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 12.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fab37f054716362e3cc2290d08c6c8bf57\">\n           </a>\n           iadcPosInputPortCPin13\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 13.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442faca81d02eef2f00594a4c0e7e8d29a49d\">\n           </a>\n           iadcPosInputPortCPin14\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 14.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa799dd49078e949587ce1167fb50efd7a\">\n           </a>\n           iadcPosInputPortCPin15\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port C pin 15.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa1460b2c47920622210eeb80f49daaec0\">\n           </a>\n           iadcPosInputPortDPin0\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 0.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa8bab2aa76a1bbe9ec7260af823229a96\">\n           </a>\n           iadcPosInputPortDPin1\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 1.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442faca1bb225c4ac224284b74209cbe453b4\">\n           </a>\n           iadcPosInputPortDPin2\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 2.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442faa06b0e6564f9a03ca548fae1e7f5d027\">\n           </a>\n           iadcPosInputPortDPin3\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 3.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa8d393553ba02bd16ed413bfca1f03153\">\n           </a>\n           iadcPosInputPortDPin4\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 4.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa99ec4b8dd5bd8d5a99e56dbd9689464e\">\n           </a>\n           iadcPosInputPortDPin5\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 5.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa0ea280095a61cf37f7205188f5f927f1\">\n           </a>\n           iadcPosInputPortDPin6\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 6.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fafc975193cc62ff1b9bcecc12e8d47d4d\">\n           </a>\n           iadcPosInputPortDPin7\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 7.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fad8298666bae2f38c8a9093737e7ac20f\">\n           </a>\n           iadcPosInputPortDPin8\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 8.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fab9dfe7010da5f37c44432ce5b03440e1\">\n           </a>\n           iadcPosInputPortDPin9\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 9.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa424d2851a2ee85d44a90af427f3ce555\">\n           </a>\n           iadcPosInputPortDPin10\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 10.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa268c7f161c6042ad665f7a772ede517f\">\n           </a>\n           iadcPosInputPortDPin11\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 11.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fac573b570fd86e8dded990c275a43cfe3\">\n           </a>\n           iadcPosInputPortDPin12\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 12.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa7d24b8bea5fd4ff4e4d6a253bd2e975b\">\n           </a>\n           iadcPosInputPortDPin13\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 13.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa15de0b8fd79a1ee34c3c0b8e36df5568\">\n           </a>\n           iadcPosInputPortDPin14\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 14.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab3230940cb6abe6a598d926f6503442fa2b71bc2d42f7279aa5f908c725cbdfc1\">\n           </a>\n           iadcPosInputPortDPin15\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            GPIO port D pin 15.\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"ga81517741e2dd7744d21850a22c400c0d\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga81517741e2dd7744d21850a22c400c0d\">\n        ◆\n       </a>\n      </span>\n      IADC_Cmd_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#ga81517741e2dd7744d21850a22c400c0d\">\n            IADC_Cmd_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        IADC Commands.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga81517741e2dd7744d21850a22c400c0daefd7dba8fac43ca855fbd2387dff9aa3\">\n           </a>\n           iadcCmdStartSingle\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Start single queue\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga81517741e2dd7744d21850a22c400c0da9bc52aecc8a8c6d6eecb987aa47d31df\">\n           </a>\n           iadcCmdStopSingle\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Stop single queue\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga81517741e2dd7744d21850a22c400c0dad1684dca867a37b754fef283f05c84a3\">\n           </a>\n           iadcCmdStartScan\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Start scan queue\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga81517741e2dd7744d21850a22c400c0dadad05c1fbc09dc4719d4679726d0eeb3\">\n           </a>\n           iadcCmdStopScan\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Stop scan queue\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga81517741e2dd7744d21850a22c400c0da6a76fdebce42c191bb96c9e165e58757\">\n           </a>\n           iadcCmdEnableTimer\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Enable Timer\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga81517741e2dd7744d21850a22c400c0da88ec237337b12b2390ae2559d4ff9123\">\n           </a>\n           iadcCmdDisableTimer\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Disable Timer\n            <br>\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"gac0395e857a03877c0a335c552a774752\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gac0395e857a03877c0a335c552a774752\">\n        ◆\n       </a>\n      </span>\n      IADC_CfgAdcMode_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#gac0395e857a03877c0a335c552a774752\">\n            IADC_CfgAdcMode_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        IADC Configuration.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggac0395e857a03877c0a335c552a774752a53554a3d8d222b29f0f47cae8d115002\">\n           </a>\n           iadcCfgModeNormal\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Normal mode\n            <br>\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"ga248af70c70659ca5fd7f56fca059f492\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga248af70c70659ca5fd7f56fca059f492\">\n        ◆\n       </a>\n      </span>\n      IADC_CfgOsrHighSpeed_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#ga248af70c70659ca5fd7f56fca059f492\">\n            IADC_CfgOsrHighSpeed_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        IADC Over sampling rate for high speed.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga248af70c70659ca5fd7f56fca059f492a6a8feae9a77e6961975bb5e4f09c1001\">\n           </a>\n           iadcCfgOsrHighSpeed2x\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            High speed oversampling of 2x.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga248af70c70659ca5fd7f56fca059f492a74c032650280ee05edc74673232bff31\">\n           </a>\n           iadcCfgOsrHighSpeed4x\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            High speed oversampling of 4x.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga248af70c70659ca5fd7f56fca059f492ad9bb317662fa788ddaf1e8ea506d86a9\">\n           </a>\n           iadcCfgOsrHighSpeed8x\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            High speed oversampling of 8x.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga248af70c70659ca5fd7f56fca059f492a6119181df9ca5acdfe3cbb19dc177365\">\n           </a>\n           iadcCfgOsrHighSpeed16x\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            High speed oversampling of 16x.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga248af70c70659ca5fd7f56fca059f492aac20fe5eb158a5f3059d4b73c6b69b9c\">\n           </a>\n           iadcCfgOsrHighSpeed32x\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            High speed oversampling of 32x.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga248af70c70659ca5fd7f56fca059f492ac84cd47a5cfed0c64fef174f27c94dd2\">\n           </a>\n           iadcCfgOsrHighSpeed64x\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            High speed oversampling of 64x.\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"ga63a0cd10e95207bc0aaaab88c5e01097\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga63a0cd10e95207bc0aaaab88c5e01097\">\n        ◆\n       </a>\n      </span>\n      IADC_CfgAnalogGain_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#ga63a0cd10e95207bc0aaaab88c5e01097\">\n            IADC_CfgAnalogGain_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        IADC Analog Gain.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga63a0cd10e95207bc0aaaab88c5e01097a7eff9c3b02fe2a181446552b4f26cb21\">\n           </a>\n           iadcCfgAnalogGain0P5x\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Analog gain of 0.5x.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga63a0cd10e95207bc0aaaab88c5e01097ac70e3dadf103cff7f2500225e9e41e06\">\n           </a>\n           iadcCfgAnalogGain1x\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Analog gain of 1x.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga63a0cd10e95207bc0aaaab88c5e01097addb52fc7356f6a901f44ec46533e426c\">\n           </a>\n           iadcCfgAnalogGain2x\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Analog gain of 2x.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga63a0cd10e95207bc0aaaab88c5e01097ab9375e7cc15d005da6dc3ed11f6733db\">\n           </a>\n           iadcCfgAnalogGain3x\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Analog gain of 3x.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga63a0cd10e95207bc0aaaab88c5e01097ad7dfc13e7c84e7dde8903efdb2804238\">\n           </a>\n           iadcCfgAnalogGain4x\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Analog gain of 4x.\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"gac1803c998a59a350d10147b81f80d156\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gac1803c998a59a350d10147b81f80d156\">\n        ◆\n       </a>\n      </span>\n      IADC_CfgReference_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#gac1803c998a59a350d10147b81f80d156\">\n            IADC_CfgReference_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        IADC Reference.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggac1803c998a59a350d10147b81f80d156a0bbe253f4c3859eeaedf507243567f38\">\n           </a>\n           iadcCfgReferenceInt1V2\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Internal 1.2V Band Gap Reference (buffered) to ground.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggac1803c998a59a350d10147b81f80d156ac9adffd22270b80d2591fa02ab5c6347\">\n           </a>\n           iadcCfgReferenceExt1V25\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            External reference (unbuffered) VREFP to VREFN.\n           </p>\n           <p>\n            Up to 1.25V.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggac1803c998a59a350d10147b81f80d156a35531c5cfcfd9a8829ec7aa0d55b39aa\">\n           </a>\n           iadcCfgReferenceVddx\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            VDDX (unbuffered) to ground.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggac1803c998a59a350d10147b81f80d156af79b03fad3646bca315e3c25c2ec1a97\">\n           </a>\n           iadcCfgReferenceVddX0P8Buf\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            0.8 * VDDX (buffered) to ground.\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"gadaad7387cbc0b5a22c631d54fa01d70c\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gadaad7387cbc0b5a22c631d54fa01d70c\">\n        ◆\n       </a>\n      </span>\n      IADC_CfgTwosComp_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#gadaad7387cbc0b5a22c631d54fa01d70c\">\n            IADC_CfgTwosComp_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        IADC Two's complement results.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggadaad7387cbc0b5a22c631d54fa01d70ca8c33776083aeb32162940f6589fd21e3\">\n           </a>\n           iadcCfgTwosCompAuto\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Automatic.\n           </p>\n           <p>\n            Single ended =&gt; Unipolar, Differential =&gt; Bipolar\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggadaad7387cbc0b5a22c631d54fa01d70cae9253452b2fc798c0389c21a350de2ae\">\n           </a>\n           iadcCfgTwosCompUnipolar\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            All results in unipolar format.\n           </p>\n           <p>\n            Negative diff input gives 0 as result.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggadaad7387cbc0b5a22c631d54fa01d70ca8e6f483d73d277d9bdbf702443ee2ebb\">\n           </a>\n           iadcCfgTwosCompBipolar\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            All results in bipolar (2's complement) format.\n           </p>\n           <p>\n            Half range for SE.\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"gab364568b748681b9a4a35f409bc437ec\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gab364568b748681b9a4a35f409bc437ec\">\n        ◆\n       </a>\n      </span>\n      IADC_TriggerSel_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#gab364568b748681b9a4a35f409bc437ec\">\n            IADC_TriggerSel_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        IADC trigger action.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab364568b748681b9a4a35f409bc437eca3d6b9e5fd6d6b3c394430de7acc40bfb\">\n           </a>\n           iadcTriggerSelImmediate\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Start single/scan queue immediately.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab364568b748681b9a4a35f409bc437ecae3e95441deaed701de9ce834fa3bdd6b\">\n           </a>\n           iadcTriggerSelTimer\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Timer starts single/scan queue.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab364568b748681b9a4a35f409bc437eca8c526a236a3964374b05b5ed99929cba\">\n           </a>\n           iadcTriggerSelPrs0SameClk\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            PRS0 from timer in same clock group starts single/scan queue\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab364568b748681b9a4a35f409bc437ecaa2d04006a204c1aca31f9dfad9c935a4\">\n           </a>\n           iadcTriggerSelPrs0PosEdge\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            PRS0 positive edge starts single/scan queue\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggab364568b748681b9a4a35f409bc437ecab22e9480531b1d8d9adf801e77833ba1\">\n           </a>\n           iadcTriggerSelPrs0NegEdge\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            PRS0 negative edge starts single/scan queue\n            <br>\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"ga9d9fa77f1c78fbf4f88331806d383a17\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga9d9fa77f1c78fbf4f88331806d383a17\">\n        ◆\n       </a>\n      </span>\n      IADC_TriggerAction_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#ga9d9fa77f1c78fbf4f88331806d383a17\">\n            IADC_TriggerAction_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        IADC trigger action.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga9d9fa77f1c78fbf4f88331806d383a17a976a78c46767f1bc979907b9217ef4aa\">\n           </a>\n           iadcTriggerActionOnce\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Convert single/scan queue once per trigger\n            <br>\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga9d9fa77f1c78fbf4f88331806d383a17ade1a00f0bbe43c2e98c45545491c62eb\">\n           </a>\n           iadcTriggerActionContinuous\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Convert single/scan queue continuously\n            <br>\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"ga0e8b39e509a9e8c6804b3a280b647e7d\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga0e8b39e509a9e8c6804b3a280b647e7d\">\n        ◆\n       </a>\n      </span>\n      IADC_FifoCfgDvl_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#ga0e8b39e509a9e8c6804b3a280b647e7d\">\n            IADC_FifoCfgDvl_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        IADC data valid level before requesting DMA transfer.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga0e8b39e509a9e8c6804b3a280b647e7daa5a3930417a3c70d4c94498d98770e8a\">\n           </a>\n           iadcFifoCfgDvl1\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Data valid level is 1 before requesting DMA transfer.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga0e8b39e509a9e8c6804b3a280b647e7dab1c85cff9a1d94c95b9d95f6f5f19a05\">\n           </a>\n           iadcFifoCfgDvl2\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Data valid level is 2 before requesting DMA transfer.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga0e8b39e509a9e8c6804b3a280b647e7daca98a3d46552ebfc4a95358534bdae8f\">\n           </a>\n           iadcFifoCfgDvl3\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Data valid level is 3 before requesting DMA transfer.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga0e8b39e509a9e8c6804b3a280b647e7da6c8cf1513d8f3230cf79774b736e01c6\">\n           </a>\n           iadcFifoCfgDvl4\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Data valid level is 4 before requesting DMA transfer.\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n    </div>\n   </article>\n  ", "url": "http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/group-iadc", "status": "success"}