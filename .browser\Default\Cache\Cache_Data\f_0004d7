{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>I2C - Inter-Integrated Circuit<span id=\"i2-c-inter-integrated-circuit\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-inter-integrated-circuit\">#</a></span></h1><p style=\"color:inherit\">Inter-integrated Circuit (I2C) Peripheral API. </p><p style=\"color:inherit\">This module contains functions to control the I2C peripheral of Silicon Labs 32-bit MCUs and SoCs. The I2C interface allows communication on I2C buses with the lowest energy consumption possible. </p><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2-c-init-type-def\" target=\"_blank\" rel=\"\">I2C_Init_TypeDef</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2-c-transfer-seq-type-def\" target=\"_blank\" rel=\"\">I2C_TransferSeq_TypeDef</a></p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-clock-hlr-type-def\">I2C_ClockHLR_TypeDef</a> {</div><div class=\"enum\">i2cClockHLRStandard = _I2C_CTRL_CLHR_STANDARD</div><div class=\"enum\">i2cClockHLRAsymetric = _I2C_CTRL_CLHR_ASYMMETRIC</div><div class=\"enum\">i2cClockHLRFast = _I2C_CTRL_CLHR_FAST</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Clock low to high ratio settings. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-transfer-return-type-def\">I2C_TransferReturn_TypeDef</a> {</div><div class=\"enum\">i2cTransferInProgress = 1</div><div class=\"enum\">i2cTransferDone = 0</div><div class=\"enum\">i2cTransferNack = -1</div><div class=\"enum\">i2cTransferBusErr = -2</div><div class=\"enum\">i2cTransferArbLost = -3</div><div class=\"enum\">i2cTransferUsageFault = -4</div><div class=\"enum\">i2cTransferSwFault = -5</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Return codes for single Controller mode transfer function. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint32_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-bus-freq-get\">I2C_BusFreqGet</a>(I2C_TypeDef *i2c)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get the current configured I2C bus frequency. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-bus-freq-set\">I2C_BusFreqSet</a>(I2C_TypeDef *i2c, uint32_t freqRef, uint32_t freqScl, I2C_ClockHLR_TypeDef i2cMode)</div><div class=\"classdescription\"><p style=\"color:inherit\">Set the I2C bus frequency. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-enable\">I2C_Enable</a>(I2C_TypeDef *i2c, bool enable)</div><div class=\"classdescription\"><p style=\"color:inherit\">Enable/disable I2C. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-init\">I2C_Init</a>(I2C_TypeDef *i2c, const I2C_Init_TypeDef *init)</div><div class=\"classdescription\"><p style=\"color:inherit\">Initialize I2C. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-int-clear\">I2C_IntClear</a>(I2C_TypeDef *i2c, uint32_t flags)</div><div class=\"classdescription\"><p style=\"color:inherit\">Clear one or more pending I2C interrupts. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-int-disable\">I2C_IntDisable</a>(I2C_TypeDef *i2c, uint32_t flags)</div><div class=\"classdescription\"><p style=\"color:inherit\">Disable one or more I2C interrupts. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-int-enable\">I2C_IntEnable</a>(I2C_TypeDef *i2c, uint32_t flags)</div><div class=\"classdescription\"><p style=\"color:inherit\">Enable one or more I2C interrupts. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint32_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-int-get\">I2C_IntGet</a>(I2C_TypeDef *i2c)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get pending I2C interrupt flags. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint32_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-int-get-enabled\">I2C_IntGetEnabled</a>(I2C_TypeDef *i2c)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get enabled and pending I2C interrupt flags. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-int-set\">I2C_IntSet</a>(I2C_TypeDef *i2c, uint32_t flags)</div><div class=\"classdescription\"><p style=\"color:inherit\">Set one or more pending I2C interrupts from SW. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-reset\">I2C_Reset</a>(I2C_TypeDef *i2c)</div><div class=\"classdescription\"><p style=\"color:inherit\">Reset I2C to the same state that it was in after a hardware reset. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-slave-address-get\">I2C_SlaveAddressGet</a>(I2C_TypeDef *i2c)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get Target address used for I2C peripheral (when operating in Target mode). </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-slave-address-set\">I2C_SlaveAddressSet</a>(I2C_TypeDef *i2c, uint8_t addr)</div><div class=\"classdescription\"><p style=\"color:inherit\">Set Target address to use for I2C peripheral (when operating in Target mode). </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-slave-address-mask-get\">I2C_SlaveAddressMaskGet</a>(I2C_TypeDef *i2c)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get Target address mask used for I2C peripheral (when operating in Target mode). </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-slave-address-mask-set\">I2C_SlaveAddressMaskSet</a>(I2C_TypeDef *i2c, uint8_t mask)</div><div class=\"classdescription\"><p style=\"color:inherit\">Set Target address mask used for I2C peripheral (when operating in Target mode). </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2c#i2-c-transfer-return-type-def\" target=\"_blank\" rel=\"\">I2C_TransferReturn_TypeDef</a></div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-transfer\">I2C_Transfer</a>(I2C_TypeDef *i2c)</div><div class=\"classdescription\"><p style=\"color:inherit\">Continue an initiated I2C transfer (single master mode only). </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2c#i2-c-transfer-return-type-def\" target=\"_blank\" rel=\"\">I2C_TransferReturn_TypeDef</a></div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-transfer-init\">I2C_TransferInit</a>(I2C_TypeDef *i2c, I2C_TransferSeq_TypeDef *seq)</div><div class=\"classdescription\"><p style=\"color:inherit\">Prepare and start an I2C transfer (single master mode only). </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-freq-standard-max\">I2C_FREQ_STANDARD_MAX</a> 100000</div><div class=\"classdescription\"><p style=\"color:inherit\">Standard mode max frequency assuming using 4:4 ratio for Nlow:Nhigh. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-freq-fast-max\">I2C_FREQ_FAST_MAX</a> 392157</div><div class=\"classdescription\"><p style=\"color:inherit\">Fast mode max frequency assuming using 6:3 ratio for Nlow:Nhigh. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-freq-fastplus-max\">I2C_FREQ_FASTPLUS_MAX</a> 987167</div><div class=\"classdescription\"><p style=\"color:inherit\">Fast mode+ max frequency assuming using 11:6 ratio for Nlow:Nhigh. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-flag-write\">I2C_FLAG_WRITE</a> 0x0001</div><div class=\"classdescription\"><p style=\"color:inherit\">Indicate plain write sequence: S+ADDR(W)+DATA0+P. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-flag-read\">I2C_FLAG_READ</a> 0x0002</div><div class=\"classdescription\"><p style=\"color:inherit\">Indicate plain read sequence: S+ADDR(R)+DATA0+P. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-flag-write-read\">I2C_FLAG_WRITE_READ</a> 0x0004</div><div class=\"classdescription\"><p style=\"color:inherit\">Indicate combined write/read sequence: S+ADDR(W)+DATA0+Sr+ADDR(R)+DATA1+P. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-flag-write-write\">I2C_FLAG_WRITE_WRITE</a> 0x0008</div><div class=\"classdescription\"><p style=\"color:inherit\">Indicate write sequence using two buffers: S+ADDR(W)+DATA0+DATA1+P. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-flag-10-bit-addr\">I2C_FLAG_10BIT_ADDR</a> 0x0010</div><div class=\"classdescription\"><p style=\"color:inherit\">Use 10 bit address. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#i2-c-init-default\">I2C_INIT_DEFAULT</a> undefined</div><div class=\"classdescription\"><p style=\"color:inherit\">Suggested default configuration for I2C initialization structure. </p></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>I2C_ClockHLR_TypeDef<span id=\"i2-c-clock-hlr-type-def\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-clock-hlr-type-def\">#</a></span></h3><blockquote>I2C_ClockHLR_TypeDef</blockquote><p style=\"color:inherit\">Clock low to high ratio settings. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">i2cClockHLRStandard</td><td class=\"fielddescription\"><p style=\"color:inherit\">Ratio is 4:4. </p></td></tr><tr><td class=\"fieldname\">i2cClockHLRAsymetric</td><td class=\"fielddescription\"><p style=\"color:inherit\">Ratio is 6:3. </p></td></tr><tr><td class=\"fieldname\">i2cClockHLRFast</td><td class=\"fielddescription\"><p style=\"color:inherit\">Ratio is 11:3. </p></td></tr></tbody></table><br><div>Definition at line <code>162</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_TransferReturn_TypeDef<span id=\"i2-c-transfer-return-type-def\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-transfer-return-type-def\">#</a></span></h3><blockquote>I2C_TransferReturn_TypeDef</blockquote><p style=\"color:inherit\">Return codes for single Controller mode transfer function. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">i2cTransferInProgress</td><td class=\"fielddescription\"><p style=\"color:inherit\">Transfer in progress. </p></td></tr><tr><td class=\"fieldname\">i2cTransferDone</td><td class=\"fielddescription\"><p style=\"color:inherit\">Transfer completed successfully. </p></td></tr><tr><td class=\"fieldname\">i2cTransferNack</td><td class=\"fielddescription\"><p style=\"color:inherit\">NACK received during transfer. </p></td></tr><tr><td class=\"fieldname\">i2cTransferBusErr</td><td class=\"fielddescription\"><p style=\"color:inherit\">Bus error during transfer (misplaced START/STOP). </p></td></tr><tr><td class=\"fieldname\">i2cTransferArbLost</td><td class=\"fielddescription\"><p style=\"color:inherit\">Arbitration lost during transfer. </p></td></tr><tr><td class=\"fieldname\">i2cTransferUsageFault</td><td class=\"fielddescription\"><p style=\"color:inherit\">Usage fault. </p></td></tr><tr><td class=\"fieldname\">i2cTransferSwFault</td><td class=\"fielddescription\"><p style=\"color:inherit\">SW fault. </p></td></tr></tbody></table><br><div>Definition at line <code>169</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>I2C_BusFreqGet<span id=\"i2-c-bus-freq-get\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-bus-freq-get\">#</a></span></h3><blockquote>uint32_t I2C_BusFreqGet (I2C_TypeDef * i2c)</blockquote><p style=\"color:inherit\">Get the current configured I2C bus frequency. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">A pointer to the I2C peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This frequency is only relevant when acting as master.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The actual frequency is a real number, this function returns a rounded down (truncated) integer value.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The current I2C frequency in Hz. </p></li></ul><br><div>Definition at line <code>203</code> of file <code>platform/emlib/src/em_i2c.c</code></div><br></div><div><h3>I2C_BusFreqSet<span id=\"i2-c-bus-freq-set\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-bus-freq-set\">#</a></span></h3><blockquote>void I2C_BusFreqSet (I2C_TypeDef * i2c, uint32_t freqRef, uint32_t freqScl, <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2c#i2-c-clock-hlr-type-def\" target=\"_blank\" rel=\"\">I2C_ClockHLR_TypeDef</a> i2cMode)</blockquote><p style=\"color:inherit\">Set the I2C bus frequency. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">A pointer to the I2C peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">freqRef</td><td><p style=\"color:inherit\">An I2C reference clock frequency in Hz that will be used. If set to 0, HFPERCLK / HFPERCCLK clock is used. Setting it to a higher than actual configured value has the consequence of reducing the real I2C frequency.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">freqScl</td><td><p style=\"color:inherit\">A bus frequency to set (bus speed may be lower due to integer prescaling). Safe (according to the I2C specification) maximum frequencies for standard fast and fast+ modes are available using I2C_FREQ_ defines. (Using I2C_FREQ_ defines requires corresponding setting of <code>type</code>.) The slowest slave device on a bus must always be considered.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">i2cMode</td><td><p style=\"color:inherit\">A clock low-to-high ratio type to use. If not using i2cClockHLRStandard, make sure all devices on the bus support the specified mode. Using a non-standard ratio is useful to achieve a higher bus clock in fast and fast+ modes. </p></td></tr></tbody></table></div><p style=\"color:inherit\">The bus frequency is only relevant when acting as master. The bus frequency should not be set higher than the maximum frequency accepted by the slowest device on the bus.</p><p style=\"color:inherit\">Notice that, due to asymmetric requirements on low and high I2C clock cycles in the I2C specification, the maximum frequency allowed to comply with the specification may be somewhat lower than expected.</p><p style=\"color:inherit\">See the reference manual, details on I2C clock generation, for maximum allowed theoretical frequencies for different modes.</p><br><div>Definition at line <code>268</code> of file <code>platform/emlib/src/em_i2c.c</code></div><br></div><div><h3>I2C_Enable<span id=\"i2-c-enable\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-enable\">#</a></span></h3><blockquote>void I2C_Enable (I2C_TypeDef * i2c, bool enable)</blockquote><p style=\"color:inherit\">Enable/disable I2C. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">A pointer to the I2C peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">enable</td><td><p style=\"color:inherit\">True to enable counting, false to disable. </p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">After enabling the I2C (from being disabled), the I2C is in BUSY state.</p></li></ul><br><div>Definition at line <code>403</code> of file <code>platform/emlib/src/em_i2c.c</code></div><br></div><div><h3>I2C_Init<span id=\"i2-c-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-init\">#</a></span></h3><blockquote>void I2C_Init (I2C_TypeDef * i2c, const <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2-c-init-type-def\" target=\"_blank\" rel=\"\">I2C_Init_TypeDef</a> * init)</blockquote><p style=\"color:inherit\">Initialize I2C. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">A pointer to the I2C peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">init</td><td><p style=\"color:inherit\">A pointer to the I2C initialization structure. </p></td></tr></tbody></table></div><br><div>Definition at line <code>424</code> of file <code>platform/emlib/src/em_i2c.c</code></div><br></div><div><h3>I2C_IntClear<span id=\"i2-c-int-clear\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-int-clear\">#</a></span></h3><blockquote>void I2C_IntClear (I2C_TypeDef * i2c, uint32_t flags)</blockquote><p style=\"color:inherit\">Clear one or more pending I2C interrupts. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">Pointer to I2C peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">Pending I2C interrupt source to clear. Use a bitwise logic OR combination of valid interrupt flags for the I2C module (I2C_IF_nnn). </p></td></tr></tbody></table></div><br><div>Definition at line <code>294</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_IntDisable<span id=\"i2-c-int-disable\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-int-disable\">#</a></span></h3><blockquote>void I2C_IntDisable (I2C_TypeDef * i2c, uint32_t flags)</blockquote><p style=\"color:inherit\">Disable one or more I2C interrupts. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">Pointer to I2C peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">I2C interrupt sources to disable. Use a bitwise logic OR combination of valid interrupt flags for the I2C module (I2C_IF_nnn). </p></td></tr></tbody></table></div><br><div>Definition at line <code>314</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_IntEnable<span id=\"i2-c-int-enable\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-int-enable\">#</a></span></h3><blockquote>void I2C_IntEnable (I2C_TypeDef * i2c, uint32_t flags)</blockquote><p style=\"color:inherit\">Enable one or more I2C interrupts. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">Pointer to I2C peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">I2C interrupt sources to enable. Use a bitwise logic OR combination of valid interrupt flags for the I2C module (I2C_IF_nnn). </p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Depending on the use, a pending interrupt may already be set prior to enabling the interrupt. To ignore a pending interrupt, consider using <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2c#i2-c-int-clear\" target=\"_blank\" rel=\"\">I2C_IntClear()</a> prior to enabling the interrupt.</p></li></ul><br><div>Definition at line <code>339</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_IntGet<span id=\"i2-c-int-get\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-int-get\">#</a></span></h3><blockquote>uint32_t I2C_IntGet (I2C_TypeDef * i2c)</blockquote><p style=\"color:inherit\">Get pending I2C interrupt flags. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">Pointer to I2C peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Event bits are not cleared by the use of this function.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">I2C interrupt sources pending. A bitwise logic OR combination of valid interrupt flags for the I2C module (I2C_IF_nnn). </p></li></ul><br><div>Definition at line <code>362</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_IntGetEnabled<span id=\"i2-c-int-get-enabled\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-int-get-enabled\">#</a></span></h3><blockquote>uint32_t I2C_IntGetEnabled (I2C_TypeDef * i2c)</blockquote><p style=\"color:inherit\">Get enabled and pending I2C interrupt flags. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">Pointer to I2C peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Useful for handling more interrupt sources in the same interrupt handler.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Interrupt flags are not cleared by the use of this function.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Pending and enabled I2C interrupt sources Return value is the bitwise AND of</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">the enabled interrupt sources in I2Cn_IEN and</p></li><li><p style=\"color:inherit\">the pending interrupt flags I2Cn_IF </p></li></ul></li></ul><br><div>Definition at line <code>384</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_IntSet<span id=\"i2-c-int-set\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-int-set\">#</a></span></h3><blockquote>void I2C_IntSet (I2C_TypeDef * i2c, uint32_t flags)</blockquote><p style=\"color:inherit\">Set one or more pending I2C interrupts from SW. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">Pointer to I2C peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">I2C interrupt sources to set to pending. Use a bitwise logic OR combination of valid interrupt flags for the I2C module (I2C_IF_nnn). </p></td></tr></tbody></table></div><br><div>Definition at line <code>403</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_Reset<span id=\"i2-c-reset\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-reset\">#</a></span></h3><blockquote>void I2C_Reset (I2C_TypeDef * i2c)</blockquote><p style=\"color:inherit\">Reset I2C to the same state that it was in after a hardware reset. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">A pointer to the I2C peripheral register block. </p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The ROUTE register is NOT reset by this function to allow for centralized setup of this feature.</p></li></ul><br><div>Definition at line <code>450</code> of file <code>platform/emlib/src/em_i2c.c</code></div><br></div><div><h3>I2C_SlaveAddressGet<span id=\"i2-c-slave-address-get\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-slave-address-get\">#</a></span></h3><blockquote>uint8_t I2C_SlaveAddressGet (I2C_TypeDef * i2c)</blockquote><p style=\"color:inherit\">Get Target address used for I2C peripheral (when operating in Target mode). </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">Pointer to I2C peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">For 10-bit addressing mode, the address is split in two bytes, and only the first byte setting is fetched, effectively only controlling the 2 most significant bits of the 10-bit address. Full handling of 10-bit addressing in Target mode requires additional SW handling.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">I2C Target address in use. The 7 most significant bits define the actual address, the least significant bit is reserved and always returned as 0. </p></li></ul><br><div>Definition at line <code>431</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_SlaveAddressSet<span id=\"i2-c-slave-address-set\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-slave-address-set\">#</a></span></h3><blockquote>void I2C_SlaveAddressSet (I2C_TypeDef * i2c, uint8_t addr)</blockquote><p style=\"color:inherit\">Set Target address to use for I2C peripheral (when operating in Target mode). </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">Pointer to I2C peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">addr</td><td><p style=\"color:inherit\">I2C Target address to use. The 7 most significant bits define the actual address, the least significant bit is reserved and always set to 0. </p></td></tr></tbody></table></div><p style=\"color:inherit\">For 10- bit addressing mode, the address is split in two bytes, and only the first byte is set, effectively only controlling the 2 most significant bits of the 10-bit address. Full handling of 10-bit addressing in Target mode requires additional SW handling.</p><br><div>Definition at line <code>453</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_SlaveAddressMaskGet<span id=\"i2-c-slave-address-mask-get\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-slave-address-mask-get\">#</a></span></h3><blockquote>uint8_t I2C_SlaveAddressMaskGet (I2C_TypeDef * i2c)</blockquote><p style=\"color:inherit\">Get Target address mask used for I2C peripheral (when operating in Target mode). </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">Pointer to I2C peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">The address mask defines how the comparator works. A bit position with value 0 means that the corresponding Target address bit is ignored during comparison (don't care). A bit position with value 1 means that the corresponding Target address bit must match.</p><p style=\"color:inherit\">For 10-bit addressing mode, the address is split in two bytes, and only the mask for the first address byte is fetched, effectively only controlling the 2 most significant bits of the 10-bit address.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">I2C Target address mask in use. The 7 most significant bits define the actual address mask, the least significant bit is reserved and always returned as 0. </p></li></ul><br><div>Definition at line <code>481</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_SlaveAddressMaskSet<span id=\"i2-c-slave-address-mask-set\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-slave-address-mask-set\">#</a></span></h3><blockquote>void I2C_SlaveAddressMaskSet (I2C_TypeDef * i2c, uint8_t mask)</blockquote><p style=\"color:inherit\">Set Target address mask used for I2C peripheral (when operating in Target mode). </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">Pointer to I2C peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">mask</td><td><p style=\"color:inherit\">I2C Target address mask to use. The 7 most significant bits define the actual address mask, the least significant bit is reserved and should be 0. </p></td></tr></tbody></table></div><p style=\"color:inherit\">The address mask defines how the comparator works. A bit position with value 0 means that the corresponding Target address bit is ignored during comparison (don't care). A bit position with value 1 means that the corresponding Target address bit must match.</p><p style=\"color:inherit\">For 10-bit addressing mode, the address is split in two bytes, and only the mask for the first address byte is set, effectively only controlling the 2 most significant bits of the 10-bit address.</p><br><div>Definition at line <code>509</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_Transfer<span id=\"i2-c-transfer\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-transfer\">#</a></span></h3><blockquote>I2C_TransferReturn_TypeDef I2C_Transfer (I2C_TypeDef * i2c)</blockquote><p style=\"color:inherit\">Continue an initiated I2C transfer (single master mode only). </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">A pointer to the I2C peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function is used repeatedly after a <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2c#i2-c-transfer-init\" target=\"_blank\" rel=\"\">I2C_TransferInit()</a> to complete a transfer. It may be used in polled mode as the below example shows: </p><pre class=\"language-clike\"><code class=\"language-clike\">I2C_TransferReturn_TypeDef ret<span class=\"token punctuation\">;</span>\n\n<span class=\"token comment\">// Do a polled transfer</span>\nret <span class=\"token operator\">=</span> <span class=\"token function\">I2C_TransferInit</span><span class=\"token punctuation\">(</span>I2C0<span class=\"token punctuation\">,</span> seq<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token keyword\">while</span> <span class=\"token punctuation\">(</span>ret <span class=\"token operator\">==</span> i2cTransferInProgress<span class=\"token punctuation\">)</span>\n<span class=\"token punctuation\">{</span>\n  ret <span class=\"token operator\">=</span> <span class=\"token function\">I2C_Transfer</span><span class=\"token punctuation\">(</span>I2C0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token punctuation\">}</span>\n</code></pre><p style=\"color:inherit\"> It may also be used in interrupt driven mode, where this function is invoked from the interrupt handler. Notice that, if used in interrupt mode, NVIC interrupts must be configured and enabled for the I2C bus used. I2C peripheral specific interrupts are managed by this software.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Only single master mode is supported.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Returns status for an ongoing transfer. </p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2c#i2c-transfer-in-progress\" target=\"_blank\" rel=\"\">i2cTransferInProgress</a> - indicates that transfer not finished. </p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2c#i2c-transfer-done\" target=\"_blank\" rel=\"\">i2cTransferDone</a> - transfer completed successfully. </p></li><li><p style=\"color:inherit\">otherwise some sort of error has occurred. </p></li></ul></li></ul><br><div>Definition at line <code>505</code> of file <code>platform/emlib/src/em_i2c.c</code></div><br></div><div><h3>I2C_TransferInit<span id=\"i2-c-transfer-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-transfer-init\">#</a></span></h3><blockquote>I2C_TransferReturn_TypeDef I2C_TransferInit (I2C_TypeDef * i2c, <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2-c-transfer-seq-type-def\" target=\"_blank\" rel=\"\">I2C_TransferSeq_TypeDef</a> * seq)</blockquote><p style=\"color:inherit\">Prepare and start an I2C transfer (single master mode only). </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">i2c</td><td><p style=\"color:inherit\">A pointer to the I2C peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">seq</td><td><p style=\"color:inherit\">A pointer to the sequence structure defining the I2C transfer to take place. The referenced structure must exist until the transfer has fully completed.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function must be invoked to start an I2C transfer sequence. To complete the transfer, <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2c#i2-c-transfer\" target=\"_blank\" rel=\"\">I2C_Transfer()</a> must be used either in polled mode or by adding a small driver wrapper using interrupts.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Only single master mode is supported.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Returns the status for an ongoing transfer: </p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2c#i2c-transfer-in-progress\" target=\"_blank\" rel=\"\">i2cTransferInProgress</a> - indicates that the transfer is not finished. </p></li><li><p style=\"color:inherit\">Otherwise, an error has occurred. </p></li></ul></li></ul><br><div>Definition at line <code>868</code> of file <code>platform/emlib/src/em_i2c.c</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>I2C_FREQ_STANDARD_MAX<span id=\"i2-c-freq-standard-max\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-freq-standard-max\">#</a></span></h3><blockquote>#define I2C_FREQ_STANDARD_MAX</blockquote><b>Value:</b><pre class=\"macroshort\">100000</pre><p style=\"color:inherit\">Standard mode max frequency assuming using 4:4 ratio for Nlow:Nhigh. </p><p style=\"color:inherit\">From I2C specification: Min Tlow = 4.7us, min Thigh = 4.0us, max Trise=1.0us, max Tfall=0.3us. Since ratio is 4:4, have to use worst case value of Tlow or Thigh as base.</p><p style=\"color:inherit\">1/(Tlow + Thigh + 1us + 0.3us) = 1/(4.7 + 4.7 + 1.3)us = 93458Hz <strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Due to chip characteristics, max value is somewhat reduced. </p></li></ul><br><div>Definition at line <code>79</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_FREQ_FAST_MAX<span id=\"i2-c-freq-fast-max\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-freq-fast-max\">#</a></span></h3><blockquote>#define I2C_FREQ_FAST_MAX</blockquote><b>Value:</b><pre class=\"macroshort\">392157</pre><p style=\"color:inherit\">Fast mode max frequency assuming using 6:3 ratio for Nlow:Nhigh. </p><p style=\"color:inherit\">From I2C specification: Min Tlow = 1.3us, min Thigh = 0.6us, max Trise=0.3us, max Tfall=0.3us. Since ratio is 6:3, have to use worst case value of Tlow or 2xThigh as base.</p><p style=\"color:inherit\">1/(Tlow + Thigh + 0.3us + 0.3us) = 1/(1.3 + 0.65 + 0.6)us = 392157Hz </p><br><div>Definition at line <code>94</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_FREQ_FASTPLUS_MAX<span id=\"i2-c-freq-fastplus-max\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-freq-fastplus-max\">#</a></span></h3><blockquote>#define I2C_FREQ_FASTPLUS_MAX</blockquote><b>Value:</b><pre class=\"macroshort\">987167</pre><p style=\"color:inherit\">Fast mode+ max frequency assuming using 11:6 ratio for Nlow:Nhigh. </p><p style=\"color:inherit\">From I2C specification: Min Tlow = 0.5us, min Thigh = 0.26us, max Trise=0.12us, max Tfall=0.12us. Since ratio is 11:6, have to use worst case value of Tlow or (11/6)xThigh as base.</p><p style=\"color:inherit\">1/(Tlow + Thigh + 0.12us + 0.12us) = 1/(0.5 + 0.273 + 0.24)us = 987167Hz </p><br><div>Definition at line <code>106</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_FLAG_WRITE<span id=\"i2-c-flag-write\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-flag-write\">#</a></span></h3><blockquote>#define I2C_FLAG_WRITE</blockquote><b>Value:</b><pre class=\"macroshort\">0x0001</pre><p style=\"color:inherit\">Indicate plain write sequence: S+ADDR(W)+DATA0+P. </p><ul style=\"list-style:\"><li><p style=\"color:inherit\">S - Start </p></li><li><p style=\"color:inherit\">ADDR(W) - address with W/R bit cleared </p></li><li><p style=\"color:inherit\">DATA0 - Data taken from buffer with index 0 </p></li><li><p style=\"color:inherit\">P - Stop </p></li></ul><br><div>Definition at line <code>117</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_FLAG_READ<span id=\"i2-c-flag-read\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-flag-read\">#</a></span></h3><blockquote>#define I2C_FLAG_READ</blockquote><b>Value:</b><pre class=\"macroshort\">0x0002</pre><p style=\"color:inherit\">Indicate plain read sequence: S+ADDR(R)+DATA0+P. </p><ul style=\"list-style:\"><li><p style=\"color:inherit\">S - Start </p></li><li><p style=\"color:inherit\">ADDR(R) - Address with W/R bit set </p></li><li><p style=\"color:inherit\">DATA0 - Data read into buffer with index 0 </p></li><li><p style=\"color:inherit\">P - Stop </p></li></ul><br><div>Definition at line <code>128</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_FLAG_WRITE_READ<span id=\"i2-c-flag-write-read\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-flag-write-read\">#</a></span></h3><blockquote>#define I2C_FLAG_WRITE_READ</blockquote><b>Value:</b><pre class=\"macroshort\">0x0004</pre><p style=\"color:inherit\">Indicate combined write/read sequence: S+ADDR(W)+DATA0+Sr+ADDR(R)+DATA1+P. </p><ul style=\"list-style:\"><li><p style=\"color:inherit\">S - Start </p></li><li><p style=\"color:inherit\">Sr - Repeated start </p></li><li><p style=\"color:inherit\">ADDR(W) - Address with W/R bit cleared </p></li><li><p style=\"color:inherit\">ADDR(R) - Address with W/R bit set </p></li><li><p style=\"color:inherit\">DATAn - Data written from/read into buffer with index n </p></li><li><p style=\"color:inherit\">P - Stop </p></li></ul><br><div>Definition at line <code>141</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_FLAG_WRITE_WRITE<span id=\"i2-c-flag-write-write\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-flag-write-write\">#</a></span></h3><blockquote>#define I2C_FLAG_WRITE_WRITE</blockquote><b>Value:</b><pre class=\"macroshort\">0x0008</pre><p style=\"color:inherit\">Indicate write sequence using two buffers: S+ADDR(W)+DATA0+DATA1+P. </p><ul style=\"list-style:\"><li><p style=\"color:inherit\">S - Start </p></li><li><p style=\"color:inherit\">ADDR(W) - Address with W/R bit cleared </p></li><li><p style=\"color:inherit\">DATAn - Data written from buffer with index n </p></li><li><p style=\"color:inherit\">P - Stop </p></li></ul><br><div>Definition at line <code>152</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_FLAG_10BIT_ADDR<span id=\"i2-c-flag-10-bit-addr\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-flag-10-bit-addr\">#</a></span></h3><blockquote>#define I2C_FLAG_10BIT_ADDR</blockquote><b>Value:</b><pre class=\"macroshort\">0x0010</pre><p style=\"color:inherit\">Use 10 bit address. </p><br><div>Definition at line <code>155</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div><div><h3>I2C_INIT_DEFAULT<span id=\"i2-c-init-default\" class=\"self-anchor\"><a class=\"perm\" href=\"#i2-c-init-default\">#</a></span></h3><blockquote>#define I2C_INIT_DEFAULT</blockquote><b>Value:</b><div class=\"fragment\"><div class=\"macro\">  {                                                                        \\</div><div class=\"macro\">    true,                  /* Enable when initialization done. */          \\</div><div class=\"macro\">    true,                  /* Set to Controller mode. */                   \\</div><div class=\"macro\">    0,                     /* Use currently configured reference clock. */ \\</div><div class=\"macro\">    I2C_FREQ_STANDARD_MAX, /* Set to standard rate assuring being */       \\</div><div class=\"macro\">    /*                        within I2C specification. */                 \\</div><div class=\"macro\">    i2cClockHLRStandard    /* Set to use 4:4 low/high duty cycle. */       \\</div><div class=\"macro\">  }</div></div><p style=\"color:inherit\">Suggested default configuration for I2C initialization structure. </p><br><div>Definition at line <code>214</code> of file <code>platform/emlib/inc/em_i2c.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/i2c", "status": "success"}