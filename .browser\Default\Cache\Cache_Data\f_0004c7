{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Simple Button Driver<span id=\"simple-button-driver\" class=\"self-anchor\"><a class=\"perm\" href=\"#simple-button-driver\">#</a></span></h1><p style=\"color:inherit\">Simple Button Driver module provides APIs to initalize and read simple buttons. Subsequent sections provide more insight into button driver configuration and usage.</p><p style=\"color:inherit\"><br></p><h2>Introduction<span id=\"introduction\" class=\"self-anchor\"><a class=\"perm\" href=\"#introduction\">#</a></span></h2><p style=\"color:inherit\">The Simple Button driver is a module of the button driver that provides the functionality to initialize and read simple buttons.</p><p style=\"color:inherit\"><br></p><h2>Simple Button Configuration<span id=\"simple-button-configuration\" class=\"self-anchor\"><a class=\"perm\" href=\"#simple-button-configuration\">#</a></span></h2><p style=\"color:inherit\">Simple buttons use the sl_button_t struct and their <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-simple-button-context-t\" target=\"_blank\" rel=\"\">sl_simple_button_context_t</a> struct. These are automatically generated into the following files, as well as instance specific headers with macro definitions in them. The samples below are for a single instance called \"inst0\".</p><pre class=\"language-clike\"><code class=\"language-clike\"><span class=\"token comment\">// sl_simple_button_instances.c</span>\n\n#include <span class=\"token string\">\"sl_simple_button.h\"</span>\n#include <span class=\"token string\">\"sl_simple_button_inst0_config.h\"</span>\n\nsl_simple_button_context_t simple_inst0_context <span class=\"token operator\">=</span> <span class=\"token punctuation\">{</span>\n  <span class=\"token punctuation\">.</span>state <span class=\"token operator\">=</span> <span class=\"token number\">0</span><span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>history <span class=\"token operator\">=</span> <span class=\"token number\">0</span><span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>port <span class=\"token operator\">=</span> SL_SIMPLE_BUTTON_INST0_PORT<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>pin <span class=\"token operator\">=</span> SL_SIMPLE_BUTTON_INST0_PIN<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>mode <span class=\"token operator\">=</span> SL_SIMPLE_BUTTON_INST0_MODE<span class=\"token punctuation\">,</span>\n<span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\n\nconst sl_button_t sl_button_inst0 <span class=\"token operator\">=</span> <span class=\"token punctuation\">{</span>\n  <span class=\"token punctuation\">.</span>context <span class=\"token operator\">=</span> <span class=\"token operator\">&amp;</span>simple_inst0_context<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>init <span class=\"token operator\">=</span> sl_simple_button_init<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>get_state <span class=\"token operator\">=</span> sl_simple_button_get_state<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>poll <span class=\"token operator\">=</span> sl_simple_button_poll_step<span class=\"token punctuation\">,</span>\n<span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\n\nconst sl_button_t <span class=\"token operator\">*</span>sl_simple_button_array<span class=\"token punctuation\">[</span><span class=\"token punctuation\">]</span> <span class=\"token operator\">=</span> <span class=\"token punctuation\">{</span><span class=\"token operator\">&amp;</span>sl_button_inst0<span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\nconst uint8_t simple_button_count <span class=\"token operator\">=</span> <span class=\"token number\">1</span><span class=\"token punctuation\">;</span>\n\nvoid <span class=\"token function\">sl_simple_button_init_instances</span><span class=\"token punctuation\">(</span>void<span class=\"token punctuation\">)</span>\n<span class=\"token punctuation\">{</span>\n  <span class=\"token function\">sl_button_init</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>sl_button_inst0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token punctuation\">}</span>\n\nvoid <span class=\"token function\">sl_simple_button_poll_instances</span><span class=\"token punctuation\">(</span>void<span class=\"token punctuation\">)</span>\n<span class=\"token punctuation\">{</span>\n  <span class=\"token function\">sl_button_poll_step</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>sl_button_inst0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token punctuation\">}</span>\n</code></pre><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The sl_simple_button_instances.c file is shown with only one instance, but if more were in use they would all appear in this .c file.</p></li></ul><pre class=\"language-clike\"><code class=\"language-clike\"><span class=\"token comment\">// sl_simple_button_instances.h</span>\n\n#ifndef SL_SIMPLE_BUTTON_INSTANCES_H\n#define SL_SIMPLE_BUTTON_INSTANCES_H\n\n#include <span class=\"token string\">\"sl_simple_button.h\"</span>\n\nextern const sl_button_t sl_button_inst0<span class=\"token punctuation\">;</span>\n\nvoid <span class=\"token function\">sl_simple_button_init_instances</span><span class=\"token punctuation\">(</span>void<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\nvoid <span class=\"token function\">sl_simple_button_poll_instances</span><span class=\"token punctuation\">(</span>void<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n\n#endif <span class=\"token comment\">// SL_SIMPLE_BUTTON_INSTANCES_H</span>\n</code></pre><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The sl_simple_button_instances.h file is shown with only one instance, but if more were in use they would all appear in this .h file.</p></li></ul><p style=\"color:inherit\"><br></p><h2>Simple Button Usage<span id=\"simple-button-usage\" class=\"self-anchor\"><a class=\"perm\" href=\"#simple-button-usage\">#</a></span></h2><p style=\"color:inherit\">The simple button driver has no differences in its usage from the common button driver. See <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/group-button#group-button-1buttondrv-usage\" target=\"_blank\" rel=\"\">Usage</a>. </p><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-simple-button-context-t\" target=\"_blank\" rel=\"\">sl_simple_button_context_t</a></p><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-init\">sl_simple_button_init</a>(const sl_button_t *handle)</div><div class=\"classdescription\"><p style=\"color:inherit\">Initialize the simple button driver. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/button#sl-button-state-t\" target=\"_blank\" rel=\"\">sl_button_state_t</a></div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-get-state\">sl_simple_button_get_state</a>(const sl_button_t *handle)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get the current state of the simple button. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-poll-step\">sl_simple_button_poll_step</a>(const sl_button_t *handle)</div><div class=\"classdescription\"><p style=\"color:inherit\">Poll the simple button. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-enable\">sl_simple_button_enable</a>(const sl_button_t *handle)</div><div class=\"classdescription\"><p style=\"color:inherit\">Enable the simple button. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-disable\">sl_simple_button_disable</a>(const sl_button_t *handle)</div><div class=\"classdescription\"><p style=\"color:inherit\">Disable the simple button. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-mode-poll\">SL_SIMPLE_BUTTON_MODE_POLL</a> 0U</div><div class=\"classdescription\"><p style=\"color:inherit\">BUTTON input capture using polling. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-mode-poll-and-debounce\">SL_SIMPLE_BUTTON_MODE_POLL_AND_DEBOUNCE</a> 1U</div><div class=\"classdescription\"><p style=\"color:inherit\">BUTTON input capture using polling and debouncing. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-mode-interrupt\">SL_SIMPLE_BUTTON_MODE_INTERRUPT</a> 2U</div><div class=\"classdescription\"><p style=\"color:inherit\">BUTTON input capture using interrupt. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-disabled\">SL_SIMPLE_BUTTON_DISABLED</a> 2U</div><div class=\"classdescription\"><p style=\"color:inherit\">BUTTON state is disabled. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-pressed\">SL_SIMPLE_BUTTON_PRESSED</a> 1U</div><div class=\"classdescription\"><p style=\"color:inherit\">BUTTON state is pressed. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-released\">SL_SIMPLE_BUTTON_RELEASED</a> 0U</div><div class=\"classdescription\"><p style=\"color:inherit\">BUTTON state is released. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-get-state-1\">SL_SIMPLE_BUTTON_GET_STATE</a> (context)</div><div class=\"classdescription\"><p style=\"color:inherit\">BUTTON member function to get state. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-get-port\">SL_SIMPLE_BUTTON_GET_PORT</a> (context)</div><div class=\"classdescription\"><p style=\"color:inherit\">BUTTON member function to get port. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-get-pin\">SL_SIMPLE_BUTTON_GET_PIN</a> (context)</div><div class=\"classdescription\"><p style=\"color:inherit\">BUTTON member function to get pin. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-button-get-mode\">SL_SIMPLE_BUTTON_GET_MODE</a> (context)</div><div class=\"classdescription\"><p style=\"color:inherit\">BUTTON member function to get mode. </p></div></div></div></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_simple_button_init<span id=\"sl-simple-button-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-init\">#</a></span></h3><blockquote>sl_status_t sl_simple_button_init (const sl_button_t * handle)</blockquote><p style=\"color:inherit\">Initialize the simple button driver. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">handle</td><td><p style=\"color:inherit\">Pointer to button handle:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_button_t</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status Code:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK </p></li></ul></li></ul><br><div>Definition at line <code>97</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div><div><h3>sl_simple_button_get_state<span id=\"sl-simple-button-get-state\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-get-state\">#</a></span></h3><blockquote>sl_button_state_t sl_simple_button_get_state (const sl_button_t * handle)</blockquote><p style=\"color:inherit\">Get the current state of the simple button. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">handle</td><td><p style=\"color:inherit\">Pointer to button handle:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_button_t</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Button State: Current state of the button</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_SIMPLE_BUTTON_PRESSED</p></li><li><p style=\"color:inherit\">SL_SIMPLE_BUTTON_RELEASED </p></li></ul></li></ul><br><div>Definition at line <code>109</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div><div><h3>sl_simple_button_poll_step<span id=\"sl-simple-button-poll-step\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-poll-step\">#</a></span></h3><blockquote>void sl_simple_button_poll_step (const sl_button_t * handle)</blockquote><p style=\"color:inherit\">Poll the simple button. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">handle</td><td><p style=\"color:inherit\">Pointer to button handle:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_button_t </p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">(button mode - poll / poll and debonuce)</p><br><div>Definition at line <code>117</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div><div><h3>sl_simple_button_enable<span id=\"sl-simple-button-enable\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-enable\">#</a></span></h3><blockquote>void sl_simple_button_enable (const sl_button_t * handle)</blockquote><p style=\"color:inherit\">Enable the simple button. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">handle</td><td><p style=\"color:inherit\">Pointer to button handle:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_button_t </p></li></ul></td></tr></tbody></table></div><br><div>Definition at line <code>125</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div><div><h3>sl_simple_button_disable<span id=\"sl-simple-button-disable\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-disable\">#</a></span></h3><blockquote>void sl_simple_button_disable (const sl_button_t * handle)</blockquote><p style=\"color:inherit\">Disable the simple button. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">handle</td><td><p style=\"color:inherit\">Pointer to button handle:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_button_t </p></li></ul></td></tr></tbody></table></div><br><div>Definition at line <code>133</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>SL_SIMPLE_BUTTON_MODE_POLL<span id=\"sl-simple-button-mode-poll\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-mode-poll\">#</a></span></h3><blockquote>#define SL_SIMPLE_BUTTON_MODE_POLL</blockquote><b>Value:</b><pre class=\"macroshort\">0U</pre><p style=\"color:inherit\">BUTTON input capture using polling. </p><br><div>Definition at line <code>58</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div><div><h3>SL_SIMPLE_BUTTON_MODE_POLL_AND_DEBOUNCE<span id=\"sl-simple-button-mode-poll-and-debounce\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-mode-poll-and-debounce\">#</a></span></h3><blockquote>#define SL_SIMPLE_BUTTON_MODE_POLL_AND_DEBOUNCE</blockquote><b>Value:</b><pre class=\"macroshort\">1U</pre><p style=\"color:inherit\">BUTTON input capture using polling and debouncing. </p><br><div>Definition at line <code>59</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div><div><h3>SL_SIMPLE_BUTTON_MODE_INTERRUPT<span id=\"sl-simple-button-mode-interrupt\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-mode-interrupt\">#</a></span></h3><blockquote>#define SL_SIMPLE_BUTTON_MODE_INTERRUPT</blockquote><b>Value:</b><pre class=\"macroshort\">2U</pre><p style=\"color:inherit\">BUTTON input capture using interrupt. </p><br><div>Definition at line <code>60</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div><div><h3>SL_SIMPLE_BUTTON_DISABLED<span id=\"sl-simple-button-disabled\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-disabled\">#</a></span></h3><blockquote>#define SL_SIMPLE_BUTTON_DISABLED</blockquote><b>Value:</b><pre class=\"macroshort\">2U</pre><p style=\"color:inherit\">BUTTON state is disabled. </p><br><div>Definition at line <code>62</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div><div><h3>SL_SIMPLE_BUTTON_PRESSED<span id=\"sl-simple-button-pressed\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-pressed\">#</a></span></h3><blockquote>#define SL_SIMPLE_BUTTON_PRESSED</blockquote><b>Value:</b><pre class=\"macroshort\">1U</pre><p style=\"color:inherit\">BUTTON state is pressed. </p><br><div>Definition at line <code>63</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div><div><h3>SL_SIMPLE_BUTTON_RELEASED<span id=\"sl-simple-button-released\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-released\">#</a></span></h3><blockquote>#define SL_SIMPLE_BUTTON_RELEASED</blockquote><b>Value:</b><pre class=\"macroshort\">0U</pre><p style=\"color:inherit\">BUTTON state is released. </p><br><div>Definition at line <code>64</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div><div><h3>SL_SIMPLE_BUTTON_GET_STATE<span id=\"sl-simple-button-get-state-1\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-get-state-1\">#</a></span></h3><blockquote>#define SL_SIMPLE_BUTTON_GET_STATE</blockquote><b>Value:</b><pre class=\"macroshort\">(context)</pre><p style=\"color:inherit\">BUTTON member function to get state. </p><br><div>Definition at line <code>66</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div><div><h3>SL_SIMPLE_BUTTON_GET_PORT<span id=\"sl-simple-button-get-port\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-get-port\">#</a></span></h3><blockquote>#define SL_SIMPLE_BUTTON_GET_PORT</blockquote><b>Value:</b><pre class=\"macroshort\">(context)</pre><p style=\"color:inherit\">BUTTON member function to get port. </p><br><div>Definition at line <code>67</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div><div><h3>SL_SIMPLE_BUTTON_GET_PIN<span id=\"sl-simple-button-get-pin\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-get-pin\">#</a></span></h3><blockquote>#define SL_SIMPLE_BUTTON_GET_PIN</blockquote><b>Value:</b><pre class=\"macroshort\">(context)</pre><p style=\"color:inherit\">BUTTON member function to get pin. </p><br><div>Definition at line <code>68</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div><div><h3>SL_SIMPLE_BUTTON_GET_MODE<span id=\"sl-simple-button-get-mode\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-button-get-mode\">#</a></span></h3><blockquote>#define SL_SIMPLE_BUTTON_GET_MODE</blockquote><b>Value:</b><pre class=\"macroshort\">(context)</pre><p style=\"color:inherit\">BUTTON member function to get mode. </p><br><div>Definition at line <code>69</code> of file <code>platform/driver/button/inc/sl_simple_button.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/simple-button", "status": "success"}