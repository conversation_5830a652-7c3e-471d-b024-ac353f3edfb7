(()=>{var e={1934:(e,t,n)=>{"use strict";n(8964);var a=n(1957),o=n(1947),r=n(499),s=n(9835);function i(e,t,n,a,o,r){const i=(0,s.up)("router-view");return(0,s.wg)(),(0,s.j4)(i)}n(9665);var c=n(8700),l=n(3100);const d=(0,s.aZ)({name:"App",data(){return{StartPage:"Start"}},created(){this.$store.dispatch("notifications/open"),this.$store.dispatch("wizardProducts/open");var e=document.documentElement;this.setThemeMode(e),new MutationObserver((t=>{t.forEach((t=>{"attributes"===t.type&&"data-theme"===t.attributeName&&this.setThemeMode(e)}))})).observe(e,{attributes:!0,attributeFilter:["data-theme"],subtree:!1});let t=window.location.href.split("?");if(2===t.length){let e=t[1].split("=");2===e.length&&("ManagePackagesAvailable"===e[0]?"false"===e[1]?this.$store.commit("installOptions/setManagePackagesAvailable","0"):this.$store.commit("installOptions/setManagePackagesAvailable","1"):"ResolveLicenses"===e[0]?this.StartPage="/ReviewLicensesOnly":"DisplayLicenses"===e[0]?(console.log("ReviewLicensesOnly => DisplayLicenses"),this.$store.commit("user/SET_SHOW_ACCEPTED_LICENSES",!0),this.StartPage="/ReviewLicensesOnly"):"openPackageManager"===e[0]&&(this.StartPage="/PackageManager"))}},methods:{setThemeMode(e){const t=e.getAttribute("data-theme");"com.silabs.ss.platform.theme.dark"===t?this.$q.dark.set(!0):this.$q.dark.set(!1)}},mounted(){this.$router.push({path:this.StartPage})},computed:{...(0,l.rn)({locale:e=>e.user.locale})},watch:{locale:function(e){this.$i18n.locale=e},$route:function(e,t){c.Z.postPageChange(e.path)}},beforeUnmount(){this.$store.dispatch("notifications/close"),this.$store.dispatch("wizardProducts/close")}});var u=n(1639),p=n(7661),g=n(2857),m=n(9984),f=n.n(m);const S=(0,u.Z)(d,[["render",i]]),h=S;f()(d,"components",{QTab:p.Z,QIcon:g.Z});var E=n(5578),A=n(3340),b=n(8339);const T=[{path:"/",component:()=>Promise.all([n.e(736),n.e(64),n.e(844)]).then(n.bind(n,4844)),children:[{path:"",component:()=>Promise.all([n.e(736),n.e(64),n.e(370)]).then(n.bind(n,7374))},{path:"/Start",component:()=>Promise.all([n.e(736),n.e(64),n.e(370)]).then(n.bind(n,7374))},{path:"/SelectByDevice",component:()=>Promise.all([n.e(736),n.e(64),n.e(838)]).then(n.bind(n,557))},{path:"/SelectByTechnology",component:()=>Promise.all([n.e(736),n.e(64),n.e(738)]).then(n.bind(n,6850))},{path:"/CheckingPackageStatus",component:()=>Promise.all([n.e(736),n.e(64),n.e(796)]).then(n.bind(n,9032))},{path:"/PackageManager",component:()=>Promise.all([n.e(736),n.e(459)]).then(n.bind(n,2459)),children:[{path:"",component:()=>Promise.all([n.e(736),n.e(64),n.e(829)]).then(n.bind(n,2115))},{path:"sdks",component:()=>Promise.all([n.e(736),n.e(64),n.e(396)]).then(n.bind(n,9881))},{path:"earlyAccess",component:()=>Promise.all([n.e(736),n.e(64),n.e(660)]).then(n.bind(n,8861))},{path:"tools",component:()=>Promise.all([n.e(736),n.e(64),n.e(496)]).then(n.bind(n,5645))},{path:"toolChains",component:()=>Promise.all([n.e(736),n.e(64),n.e(625)]).then(n.bind(n,1623))},{path:"assets",component:()=>Promise.all([n.e(736),n.e(64),n.e(994)]).then(n.bind(n,5098))}]},{path:"/PackageWizard",component:()=>Promise.all([n.e(736),n.e(64),n.e(636)]).then(n.bind(n,609))},{path:"/SelectPackagesAdvanced",component:()=>Promise.all([n.e(736),n.e(64),n.e(180)]).then(n.bind(n,2781))},{path:"/ReviewLicenses",component:()=>Promise.all([n.e(736),n.e(64),n.e(490)]).then(n.bind(n,7429))},{path:"/ReviewLicensesIncomplete",component:()=>Promise.all([n.e(736),n.e(64),n.e(442)]).then(n.bind(n,6002))},{path:"/ReviewLicensesOnly",component:()=>Promise.all([n.e(736),n.e(64),n.e(21)]).then(n.bind(n,1446))},{path:"/GetAccess",component:()=>Promise.all([n.e(736),n.e(64),n.e(966)]).then(n.bind(n,5149))},{path:"/Installing",component:()=>Promise.all([n.e(736),n.e(64),n.e(778)]).then(n.bind(n,9778))},{path:"/InstallationFailed",component:()=>Promise.all([n.e(736),n.e(606)]).then(n.bind(n,7606))},{path:"/InstallationCancelled",component:()=>Promise.all([n.e(736),n.e(449)]).then(n.bind(n,4449))},{path:"/InstallationComplete",component:()=>Promise.all([n.e(736),n.e(861)]).then(n.bind(n,3861))}]},{path:"/:catchAll(.*)*",component:()=>n.e(613).then(n.bind(n,2613))}],I=T,v=(0,A.BC)((function(){const e=b.r5,t=(0,b.p7)({scrollBehavior:()=>({left:0,top:0}),routes:I,history:e("")});return t}));async function P(e,t){const a=e(h);a.use(o.Z,t);const s="function"===typeof E["default"]?await(0,E["default"])({}):E["default"],{storeKey:i}=await Promise.resolve().then(n.bind(n,5578)),c=(0,r.Xl)("function"===typeof v?await v({store:s}):v);return s.$router=c,{app:a,store:s,storeKey:i,router:c}}var y=n(7396),R=n(4328),k=n(1530);const C={config:{},iconSet:y.Z,plugins:{Notify:R.Z,Dialog:k.Z}},O="";async function L({app:e,router:t,store:n,storeKey:a},o){let r=!1;const s=e=>{try{return t.resolve(e).href}catch(n){}return Object(e)===e?null:e},i=e=>{if(r=!0,"string"===typeof e&&/^https?:\/\//.test(e))return void(window.location.href=e);const t=s(e);null!==t&&(window.location.href=t,window.location.reload())},c=window.location.href.replace(window.location.origin,"");for(let d=0;!1===r&&d<o.length;d++)try{await o[d]({app:e,router:t,store:n,ssrContext:null,redirect:i,urlPath:c,publicPath:O})}catch(l){return l&&l.url?void i(l.url):void console.error("[Quasar] boot error:",l)}!0!==r&&(e.use(t),e.use(n,a),e.mount("#q-app"))}P(a.ri,C).then((e=>{const[t,a]=void 0!==Promise.allSettled?["allSettled",e=>e.map((e=>{if("rejected"!==e.status)return e.value.default;console.error("[Quasar] boot error:",e.reason)}))]:["all",e=>e.map((e=>e.default))];return Promise[t]([Promise.resolve().then(n.bind(n,1569)),Promise.resolve().then(n.bind(n,6288)),Promise.resolve().then(n.bind(n,265)),Promise.resolve().then(n.bind(n,5178))]).then((t=>{const n=a(t).filter((e=>"function"===typeof e));L(e,n)}))}))},265:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});const a=async({store:e})=>{e.commit("installOptions/setManagePackagesAvailable","0")}},5178:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a});const a=async({store:e})=>{e.dispatch("user/getUser")}},1569:(e,t,n)=>{"use strict";n.r(t),n.d(t,{api:()=>s,default:()=>i});var a=n(3340),o=n(9981),r=n.n(o);const s=r().create({baseURL:"https://api.example.com"}),i=(0,a.xr)((({app:e})=>{e.config.globalProperties.$axios=r(),e.config.globalProperties.$api=s}))},6288:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>i});var a=n(3340),o=n(7712);const r={PackageInstallationManager:"Package Installation Manager",InstallationManager:"Installation Manager",LogOut:"Log out",LogIn:"Log In",login:"Log In",ChangeVersionLabel:"Change Version",ChangeVersionTitle:"Change version for ",ClearCredentials:"Clear Credentials",Cancel:"Cancel",Category:"Category",Version:"Version",Platform:"Platform",ProductUpdates:"Product Updates",Assets:"Assets",SDKs:"SDKs",EarlyAccess:"Early Access",Extensions:"Extensions:",ImpactedExtensions:"Impacted Extensions:",Tools:"Tools",Toolchains:"Toolchains",Install:"Install",Next:"Next",Back:"Back",SelectAll:"Select All",SelectFrom:"Select from",Loading:"Loading...",Search:"Search",Auto:"Auto",Advanced:"Advanced",ReadMore:"Read More >",Close:"Close",Done:"Done",ResourceDescription:"Learn some tips and tricks to help you ramp up using Simplicity Studio",Installations:"Installations",DemoOnlyMode:"Run demos on connected devices",InstallByDevice:"Install by connecting device(s)",InstallByTechnology:"Install by technology type (wireless, Xpress, MCU, sensors)",InstallNew:"Add...",InstallNewTitle:"Add new ",New:"NEW",ManageInstalledPackages:"Manage installed packages",SelectProducts:"Select Products",SelectProductsDesc:"Connect your board(s) and choose one or more to support within Simplicity Studio",SelectProductsConnected:"Connected boards",SelectProductsAll:"All products",CheckingForUpdatesStatus:"Checking For Required Updates",CheckingForUpdatesStatusDesc:"Checking for updates to install",SelectTechnologyType:"Select Technology Type",SelectTechnologyTypeDesc:"Select technology type to use with your products",SelectDevelopmentPackages:"Select Development Packages",SelectDevelopmentPackagesDesc:"Select individual development packages to install",DevelopmentPackages:"Development Packages",DownloadSize:"Download Size",PackagesInstallationOptions:"Package Installation Options",PackagesInstallationOptionsDesc:"Manage selected packages",PackagesInstallationAutoDesc:"Select this option to let Simplicity Studio install all the recommended development packages based on the previously-selected product.",PackagesInstallationAdvancedDesc:"Select this option to customize installed development packages based on your requirements.",ReviewLicenses:"Review Licenses",ReviewLicensesDesc:"Review and accept the licenses below to install the selected packages",ShowLicenses:"Accepted Licenses",ShowLicensesDesc:"Review the accepted licenses",Email:"Email",Password:"Password",RememberMe:"Remember me",ForgotPassword:"Forgot Password",SignIn:"Sign In",CreateAccount:"Create Account",SkipLogin:"Skip Login",Filter:"Filter",Kits:"Kits",Boards:"Boards",Parts:"Parts",ViewAllSelected:"View all selected",ViewSelectedOnly:"View Selected Only",TechnologyType:"Technology Type:  ",All:"All",NowInstalling:"Now installing...",NowCloningSDK:"Now Cloning SDK...",NowUnInstalling:"Now un-installing...",AlreadyInstalled:"Already Installed",TipsAndTricks:"Tips and Tricks",TipsAndTricksDesc:"Learn some tips and tricks to help you ramp up on using Simplicity Studio",Resources:"Resources",InstallationComplete:"Installation Complete",InstallationFailed:"Installation Failed",InstallationCancelled:"Installation Cancelled",Uninstall:"Uninstall",UninstallationComplete:"Uninstallation Complete",UninstallationFailed:"Uninstallation Failed",UninstallationCancelled:"Uninstallation Cancelled",Restart:"Restart",RequestAccess:"Request Access",RequestAccessPageTitle:"Access to Technology Packages",RequestAccessPageDesc:"Some technologies may require login or kit registration",RequestAccessActionOptional:"Request access to additional packages below (optional)",RequestAccessDevelopmentPackages:"Development Packages",RequestAccessGranted:"access granted",RequestAccessFor:" package needs access",RequestAccessForPlural:" packages need access",CheckForUpdates:"Check for Updates",Update:"Update",UpdateAll:"Update All",LegacySdks:"Legacy SDKs",LicenseAccept:"Accept this agreement",LicenseAcceptAll:"Accept all agreements",LicenseIncompleteTitle:"Installation Warning",LicenseIncompleteDesc:"The following software component(s) will not be installed because the required license agreements were not accepted.",LicenseIncompleteDirections:"Click 'Next' to continue installation without installing the software components below.  Click 'Back' to change your license agreement selections.",LicenseSoftwareBound:"Software Bound by this Agreement",Location:"Location",Recommended:"Recommended",OtherOptions:"Other Options",Required:"Required",DevicesFound:" Devices Found",DeviceFound:" Device Found",NoDevicesFound:"No Devices Found",NoDevicesActionDescription:"No devices were detected.  Please connect and device and refresh, or ",NoTechnologyGroupsFound:"Unable to access the Simplicity Studio update server. Please check your internet connectivity.",Refresh:"Refresh",ReleaseNotes:"Release Notes",ReleaseNotesFor:"Release Notes for ",NoSDKsCategories:"No SDKs Categories",NoVersionsAvailable:"No Versions Available",NoGeckoPlatformAvailable:"No Gecko Platform available",FilterByConnectedProduct:"Filter by connected product",NoAssetsAvailable:"No Assets Available",UpdateAllBeforeInstallWizardDesc:"'Update All' packages in the 'Product Updates' tab and restart before using the 'Install Wizard'",UpdateAllBeforeInstallAssetsDesc:"'Update All' packages in the 'Product Updates' tab and restart to install assets'",LastScanDesc:"Using cached update data, Server Last Checked : ",NoEarlyAccessPackages:"No Early Access Packages",NoProductUpdates:"No Product Updates",Installed:"Installed",Available:"Available",UninstallPackage:"Uninstall Package",UninstallExtension:"Uninstall Extension",InstallExtension:"Install Extension",ConfirmUninstallStart:"Are you sure you want to uninstall ",ConfirmUninstallEnd:" product support?",ConfirmInstallStart:"Are you sure you want to install ",ConfirmInstallEnd:" product support?",UpdatePackage:"Update Package to Latest Version ",ConfirmUpdateStart:"Are you sure you want to update ",ConfirmUpdateEnd:" product support?",registerkit:"Register Kit",requestaccess:"Request Access"},s={en:r},i=(0,a.xr)((({app:e})=>{const t=(0,o.o)({locale:"en-US",globalInjection:!0,messages:s});e.use(t)}))},8700:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var a=n(9981),o=n.n(a);const r={"Content-Type":"application/json"},s=(e,t,n=null)=>{let a={method:"get",url:e,params:t,headers:r};return null!=n&&(a.data=n),o()(a)},i=(e,t,n)=>o()({method:"delete",url:e,params:n,data:t,headers:r}),c=(e,t,n)=>o()({method:"post",url:e,params:n,data:t,headers:r}),l={get:s,post:c,delete:i,getUser(){var e="/rest/installer?user";return s(e).then((e=>e.data))},userQuit(){var e="/rest/installer?quit";return s(e).then((e=>e.data))},showDemos(){const e="/rest/installer?showDemos";return s(e).then((e=>e.data))},userLogOut(){var e="/rest/installer?userLogOut";return s(e).then((e=>e.data))},userLogin(){var e="/rest/installer?userLogIn";return s(e).then((e=>e.data))},registerKit(){var e="/rest/installer?registerKit";return s(e).then((e=>e.data))},userClearCredentials(){var e="/rest/installer?userClearCredentials";return s(e).then((e=>e.data))},userRestart(){var e="/rest/installer?restart";return s(e).then((e=>e.data))},userExit(){var e="/rest/installer?exit";return s(e).then((e=>e.data))},getTechnologyTypes(){var e="/rest/installer/technologyTypes/";return s(e).then((e=>e.data))},wizardGetPackagesFor(e){var t="/rest/installer/wizardPackages?selected&selectBy=";return t+=e,s(t).then((e=>e.data))},wizardGetAccessForSelectedPackages(e){var t="/rest/installer/wizardPackages?getAccess=";return t+=e,s(t).then((e=>e.data))},wizardGetLicensesSelectedPackages(e){var t="/rest/installer/?getLicenses=";return t+=e,s(t).then((e=>e.data))},wizardStartInstall(e){var t="/rest/installer?install=";return t+=e,s(t).then((e=>e.data))},cancelInstall(){var e="/rest/installer",t={operation:"cancelInstall"};return c(e,t,null)},getDistractions(){var e="/rest/installer?distractions";return s(e).then((e=>e.data))},getLicensesToResolve(){var e="/rest/installer/?getLicensesToResolve";return s(e).then((e=>e.data))},getAcceptedLicenses(){var e="/rest/installer/?getAcceptedLicenses";return s(e).then((e=>e.data))},postPageChange(e){var t="/rest/installer",n={operation:"pageChange",to:e};return c(t,n,null)},postLicenseAcceptanceStatus(e,t){var n="/rest/installer",a={operation:"acceptLicense",licenseId:e,accepted:t};return c(n,a,null)},wizardGetPackagesForDevices(e){var t="/rest/installer/wizardPackages",n={operation:"selectedDevices",selectedDevices:e};return c(t,n,null)},getToolPackages(){var e="/rest/installer/toolPackages/";return s(e).then((e=>e.data))},getToolChainPackages(){var e="/rest/installer/toolChainPackages/";return s(e).then((e=>e.data))},getSdkPackages(e){var t="/rest/installer/sdkPackages/?sdkCategory=";return 0!==e&&(t+=e[0],t+="&version="+e[1],t+="&platform="+e[2]),s(t).then((e=>e.data))},getSdks(){var e="/rest/installer/sdks";return s(e).then((e=>e.data))},postInstallPackages(e){var t="/rest/installer/install/";return c(t,e,null)},postRequestAccess(e){var t="/rest/studio/ui/services/openurl/";return c(t,e,null)},postCheckUpdates(e){var t="/rest/installer/checkUpdates/";return c(t,e,null)},getUpdatePackages(){var e="/rest/installer/updatePackages/";return s(e).then((e=>e.data))},getEarlyAccessPackages(){var e="/rest/installer/earlyAccessPackages/";return s(e).then((e=>e.data))},getSdkPermissions(){var e="/rest/installer/sdkPermissions/";return s(e).then((e=>e.data))},getAssets(e){var t="/rest/installer/assets/?retrieveAllAssets=";return t+=e,s(t).then((e=>e.data))},postInstallAssets(e){var t="/rest/installer/assetInstall/";return c(t,e,null)},checkForPackageManagerUpdates(){var e="/rest/installer?checkForUpdatesPackageManager";return s(e).then((e=>e.data))},getLicensesForPackageManagerPackages(e){var t="/rest/installer/?getPackageLicenses=";return t+=e,s(t).then((e=>e.data))},getLicensesForMultiplePackages(e){var t="/rest/installer";return e.operation="getMultiplePackageLicenses",c(t,e).then((e=>e.data))},getImpactedExtensions(e){var t=`/rest/installer/sdks/impactedextensions?id=${e[0]}&version=${e[1]}`;return s(t).then((e=>e.data))},getRefreshState(){const e="/rest/installer/checkUpdates";return s(e).then((e=>e.data))}}},5578:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>Sa});var a={};n.r(a),n.d(a,{canCheckForPackageManagerUpdates:()=>F,getLastScanTime:()=>_,getLoggedIn:()=>B,showAcceptedLicenses:()=>M,userNavigationSource:()=>U});var o={};n.r(o),n.d(o,{SET_SHOW_ACCEPTED_LICENSES:()=>Y,SET_USER:()=>V,SET_WIZARD_PACKAGE_NAVIGATION:()=>H,setDefaultUser:()=>x});var r={};n.r(r),n.d(r,{ClearCredentials:()=>Z,LogIn:()=>q,LogOut:()=>z,RegisterKit:()=>W,checkForPackageManagerUpdates:()=>X,getUser:()=>j});var s={};n.r(s),n.d(s,{getInstallOptions:()=>Q,getSelectedInstallType:()=>ee,isFirstTimeInstall:()=>te});var i={};n.r(i),n.d(i,{setInstallType:()=>ae,setManagePackagesAvailable:()=>ne});var c={};n.r(c),n.d(c,{get:()=>ce,getSelectedTechTypeIds:()=>le,isLoaded:()=>ie});var l={};n.r(l),n.d(l,{SET_SELECTED:()=>me,SET_SELECTED_EXTENSIONS:()=>fe,setDefaults:()=>pe,setSelectedTechIds:()=>ge,setTypes:()=>ue});var d={};n.r(d),n.d(d,{fetchTypes:()=>Se,setSelected:()=>he,setSelectedExtensions:()=>Ee});var u={};n.r(u),n.d(u,{count:()=>Te,get:()=>Ie,getSelectedDevices:()=>ve});var p={};n.r(p),n.d(p,{SET_SOCKET:()=>Be,SOCKET_CLOSE:()=>we,SOCKET_ONCLOSE:()=>Ce,SOCKET_ONERROR:()=>Oe,SOCKET_ONMESSAGE:()=>Le,SOCKET_ONOPEN:()=>ke,SOCKET_RECONNECT:()=>Ne,SOCKET_RECONNECT_ERROR:()=>De,setConnectedProducts:()=>Pe,setDevicesSelected:()=>Re,setSelected:()=>ye});var g={};n.r(g),n.d(g,{close:()=>_e,open:()=>Fe});var m={};n.r(m),n.d(m,{count:()=>Ke,get:()=>qe,getOptionalPackages:()=>Ye,getPackagesByRequirement:()=>xe,getRecommendedPackages:()=>He,getRequiredPackages:()=>Ve});var f={};n.r(f),n.d(f,{clearSelected:()=>Xe,setDefaultPackages:()=>ze,setPackageLocationById:()=>je,setPackageSelected:()=>$e,setPackages:()=>Ze,setPackagesSelected:()=>Je});var S={};n.r(S),n.d(S,{fetchPackages:()=>Qe,fetchPackagesForDevices:()=>et});var h={};n.r(h),n.d(h,{getExpandedAssets:()=>Nt,getSelectedAssets:()=>Lt});var E={};n.r(E),n.d(E,{someMutation:()=>Yt});var A={};n.r(A),n.d(A,{checkForUpdatesFailed:()=>Xt,refreshing:()=>jt,text:()=>zt,value:()=>Zt});var b={};n.r(b),n.d(b,{SET_REFRESH_STATE:()=>$t,SET_SOCKET:()=>cn,SET_STATE_UPDATED:()=>ln,SOCKET_CLOSE:()=>sn,SOCKET_ONCLOSE:()=>Qt,SOCKET_ONERROR:()=>en,SOCKET_ONMESSAGE:()=>an,SOCKET_ONOPEN:()=>Jt,SOCKET_RECONNECT:()=>on,SOCKET_RECONNECT_ERROR:()=>rn,navigateToFromNotification:()=>tn});var T={};n.r(T),n.d(T,{close:()=>un,getRefreshState:()=>pn,open:()=>dn});var I={};n.r(I),n.d(I,{set:()=>hn,setDefaults:()=>En});var v={};n.r(v),n.d(v,{fetch:()=>An});var P={};n.r(P),n.d(P,{accessNeeded:()=>In,get:()=>vn});var y={};n.r(y),n.d(y,{onAccessRequestCompleted:()=>kn,onAccessRequestError:()=>Cn,setAccess:()=>Rn,setDefaults:()=>yn});var R={};n.r(R),n.d(R,{fetchAccessForPackages:()=>On,requestAccessForPackage:()=>Ln});var k={};n.r(k),n.d(k,{get:()=>wn});var C={};n.r(C),n.d(C,{acceptAll:()=>Gn,finalize:()=>xn,setDefaultLicenses:()=>Un,setLicenses:()=>Fn,toggleAcceptance:()=>Mn,updateAcceptanceState:()=>_n});var O={};n.r(O),n.d(O,{fetchAcceptedLicenses:()=>qn,fetchLicensesForMultiplePackages:()=>Yn,fetchLicensesForPackageManagerPackages:()=>Hn,fetchLicensesForPackages:()=>Vn,fetchLicensesToResolve:()=>Kn});var L={};n.r(L),n.d(L,{getErrorMessage:()=>ta,getIsWizardInstall:()=>ea,getPMSourcePath:()=>na,getPackageManagerCategories:()=>$n,getPackageManagerFeature:()=>Zn,getPackageManagerFeatures:()=>jn,getRestartRequired:()=>Qn,getSdkInstallConfig:()=>Xn,getWizardPackageQuery:()=>Jn});var N={};n.r(N),n.d(N,{SET_BROWSE_LOCATION:()=>pa,SET_FEATURES_AND_CATEGORIES:()=>oa,SET_FEATURE_AND_CATEGORIES:()=>aa,SET_INSTALLATION_COMPLETE:()=>ca,SET_INSTALLATION_ERROR:()=>ua,SET_INSTALLATION_RESTART:()=>ia,SET_INSTALL_OPERATION:()=>la,SET_PM_ROUTE_PATH:()=>da,SET_SELECTED_SDK_INSTALL_CONFIG:()=>ra,SET_VERIFY_LOCATION:()=>ga,SET_WIZARD_PACKAGE_QUERY:()=>sa});var D=n(3100);function w(){return{name:"Logged in user",loggedIn:"0",locale:"es",licenseScreenShowAccepted:!1,lastCheckForPackageManagerUpdates:new Date,lastScanTime:"",wizardPackageNavigation:"auto"}}function B(e){return e.loggedIn}function U(e){return e.wizardPackageNavigation}function F(e){const t=Date.now()-e.lastCheckForPackageManagerUpdates,n=1,a=24,o=60,r=60,s=1e3;return t>=n*a*o*r*s}function _(e){return e.lastScanTime}function M(e){return!0===e.licenseScreenShowAccepted}const G=JSON.parse('{"name":"Logged in User","loggedIn":"1","locale":"en","language":"en","licenseScreenShowAccepted":false,"lastCheckForPackageManagerUpdates":0,"wizardPackageNavigation":"auto","lastScanTime":""}');function x(e,t){V(e,G),e.loggedIn=t}function V(e,t){e.name=t.name,e.loggedIn=t.loggedIn,e.locale=t.language,e.lastScanTime=t.lastScanTime,e.lastCheckForPackageManagerUpdates=new Date(t.lastCheckForPackageManagerUpdates)}function H(e,t){e.wizardPackageNavigation=t}function Y(e,t){e.licenseScreenShowAccepted=t}var K=n(8700);function q({commit:e}){return K.Z.userLogin().then((t=>e("SET_USER",t))).catch((()=>e("setDefaultUser","1")))}function W(){return K.Z.registerKit()}function z({commit:e}){return K.Z.userLogOut().then((t=>e("SET_USER",t))).catch((()=>e("setDefaultUser","0")))}function Z({commit:e}){return K.Z.userClearCredentials().then((t=>e("SET_USER",t))).catch((()=>e("setDefaultUser","0")))}function j(e){return K.Z.getUser().then((t=>e.commit("SET_USER",t))).catch((()=>e.commit("setDefaultUser","0")))}function X(e){return console.log("function checkForPackageManagerUpdates"),K.Z.checkForPackageManagerUpdates().then((t=>e.commit("SET_USER",t))).catch((()=>e.commit("setDefaultUser","0")))}const $={namespaced:!0,state:w,getters:a,mutations:o,actions:r};function J(){return{InstallType:"InstallByDevice",firstTimeInstall:!1,InstallOptions:[]}}function Q(e){return e.InstallOptions}function ee(e){return e.InstallType}function te(e){return e.firstTimeInstall}n(9665);function ne(e,t){e.InstallOptions=[],e.firstTimeInstall="0"===t,"0"===t&&e.InstallOptions.push({id:1,icon:"mdiEye",val:"DemoOnlyMode",label:"DemoOnlyMode",image:"icons/icon-installation-devices.png",backgroundColor:"installByDeviceBackground"}),e.InstallOptions.push({id:2,icon:"mdiEye",val:"InstallByDevice",label:"InstallByDevice",image:"icons/icon-installation-devices.png",backgroundColor:"installByDeviceBackground"}),e.InstallOptions.push({id:3,icon:"timeline",val:"InstallByTechnology",label:"InstallByTechnology",image:"icons/icon-installation-technology.png",backgroundColor:"installByProductBackground"}),"1"===t&&e.InstallOptions.push({id:4,icon:"mdiEye",val:"ManageInstalledPackages",label:"ManageInstalledPackages",image:"icons/icon-installation-package.png",backgroundColor:"#e28027"})}function ae(e,t){e.InstallType=t}var oe=n(5020);const re={namespaced:!0,state:J,getters:s,mutations:i,actions:oe};function se(){return{SelectedType:"None",selectedTechs:"",types:[]}}function ie(e){return void 0!==e.types&&e.types.length>0}function ce(e){return e.types}function le(e){return e.selectedTechs}const de=JSON.parse('[{"imageUrl":"icons/xpress.png","description":"Everything necessary for Xpress products, including configuration tools, part information, data sheets and examples.","id":"com.silabs.ffd.family.product.feature.metadata.resource.feature.group","label":"Xpress Interface","category":"com.silabs.ffd.family.product.feature","installed":"complete","selected":false},{"imageUrl":"icons/8-bit.png","description":"Everything necessary for EFM8 / C8051 MCU products, including configuration tools, SDKs, part information, toolchains, debug information, app notes, data sheets and examples.","id":"com.silabs.8051.family.product.feature.metadata.resource.feature.group","label":"8-bit Microcontrollers","category":"com.silabs.8051.family.product.feature","installed":"partial","selected":false},{"imageUrl":"icons/zwave.png","description":"Z-Wave target","id":"com.silabs.zwave.target.family.product.feature.metadata.resource.feature.group","label":"Z-Wave","category":"com.silabs.zwave.target.family.product.feature","installed":"partial","selected":false},{"imageUrl":"icons/bluetooth.png","description":"BLE target","id":"com.silabs.ble.target.family.product.feature.metadata.resource.feature.group","label":"Bluetooth","category":"com.silabs.ble.target.family.product.feature","installed":"partial","selected":false},{"imageUrl":"icons/zigbee.png","description":"Zigbee target","id":"com.silabs.zigbee.target.family.product.feature.metadata.resource.feature.group","label":"Zigbee","category":"com.silabs.zigbee.target.family.product.feature","installed":"partial","selected":false},{"imageUrl":"icons/sensors.png","description":"Everything necessary for Intelligent Sensor products, including part information, data sheets, examples and sensor tools.","id":"com.silabs.sensors.family.product.feature.metadata.resource.feature.group","label":"Sensors","category":"com.silabs.sensors.family.product.feature","installed":"partial","selected":false},{"imageUrl":"icons/proprietary.png","description":"Proprietary target","id":"com.silabs.proprietary.target.family.product.feature.metadata.resource.feature.group","label":"Proprietary","category":"com.silabs.proprietary.target.family.product.feature","installed":"none","selected":false},{"imageUrl":"icons/32-bit.png","description":"Everything necessary for EFM32 32-bit MCU products, including configuration tools, profiling tools, energy calculator, SDKs, part information, toolchains, debug information, app notes, data sheets and examples.","id":"com.silabs.efm32.family.product.feature.metadata.resource.feature.group","label":"32-bit Microcontrollers","category":"com.silabs.efm32.family.product.feature","installed":"partial","selected":false}]');function ue(e,t){e.types=t,void 0!==e.types&&e.types.length>1&&e.types.sort(((e,t)=>e.label.localeCompare(t.label))),e.types.forEach((function(e){e.selected=!1,e.extensions&&(e.extensions.forEach((function(e){e.selected=!1})),e.extensions.sort(((e,t)=>e.label.localeCompare(t.label)))),e.extensionsInfo&&e.extensionsInfo.sort(((e,t)=>e.label.localeCompare(t.label))),e.imageUrl=""+e.imageUrl}))}function pe(e){ue(e,de)}function ge(e,t){e.selectedTechs=t}function me(e,t){if(void 0!==e.types){for(var n=0;n<e.types.length;n++)e.types[n].selected=!1;for(n=0;n<t.length;n++)for(var a=0;a<e.types.length;a++)if(e.types[a].id===t[n]){e.types[a].selected=!0;break}}}function fe(e,t){if(e.types&&(e.types.forEach((function(e){e.extensions&&e.extensions.forEach((function(e){e.selected=!1}))})),t&&t.length>0)){let n=t.slice();e.types.forEach((function(e){e.extensions&&e.extensions.forEach((function(e){let t=n.indexOf(e.id);-1!=t&&(e.selected=!0,n.splice(t,1)),n.length}))}))}}function Se({commit:e}){return K.Z.getTechnologyTypes().then((t=>e("setTypes",t))).catch((()=>e("setDefaults")))}function he({commit:e},t){e("SET_SELECTED",t)}function Ee({commit:e},t){e("SET_SELECTED_EXTENSIONS",t)}const Ae={namespaced:!0,state:se,getters:c,mutations:l,actions:d};function be(){return{socket:null,selectedDevices:[],connectedProducts:[{id:"SLWSTK6101A",labelFriendly:"Blue Gecko Module Wireless Starter Kit (SLWSTK6101A)",type:"Kit",selected:!1},{id:"SLSTK37011A",labelFriendly:"EFM32GG11 Giant Gecko Starter Kit (SLSTK37011A)",type:"Kit",selected:!1},{id:"BGM11S12F256GA",labelFriendly:"BGM11S12F256GA",type:"Part",selected:!1},{part:{docs:[],simpleName:"PartDescriptor",imageURL:"",name:"efm32gg11b820f2048gl192",description:"",id:"mcu.arm.efm32.gg11.efm32gg11b820f2048gl192",label:"EFM32GG11B820F2048GL192",type:"Target Part"},boards:[{rev:"B00",docs:[],simpleName:"BoardDescriptor",allowedParts:["mcu.arm.efm32.gg11.efm32gg11b820f2048gl192"],imageURL:"",name:"EFM32GG11 Giant Gecko Starter Kit board",description:"",id:"brd2204a:0.0.0.B00",label:"EFM32GG11 Giant Gecko Starter Kit board (BRD2204A Rev B00)",type:"Board",opn:"BRD2204"}],id:"[usbNotifier@1366:1015@009:005]",label:"J-Link Silicon Labs (440096982)",labelFriendly:"EFM32GG11 Giant Gecko STK (ID: 000440096982)",isEthernet:!1,selected:!0},{part:{},boards:[],id:"[usbNotifier@1366:1015@009:009]",label:"J-Link (000440096982)",labelFriendly:"J-Link (000440096982) (ID: 000440096982)",isEthernet:!1,selected:!1}]}}function Te(e){return e.connectedProducts.length}function Ie(e){return e.connectedProducts}function ve(e){return e.selectedDevices}function Pe(e,t){e.connectedProducts=t,e.connectedProducts.forEach((function(e){e.selected=!0}))}function ye(e,t){if(void 0!==e.connectedProducts){for(var n=0;n<e.connectedProducts.length;n++)e.connectedProducts[n].selected=!1;for(n=0;n<t.length;n++){var a=e.connectedProducts.find((e=>e.id===t[n]));void 0!==a&&(a.selected=!0)}}}function Re(e,t){e.selectedDevices=t}function ke(e,t){console.log("products::SOCKET_ONOPEN: "+t)}function Ce(e,t){console.log("products::SOCKET_ONCLOSE: "+t)}function Oe(e,t){console.error("products::SOCKET_ONERROR: "+t)}function Le(e,t){const n=JSON.parse(t.data);Pe(e,n)}function Ne(e,t){console.error("products::SOCKET_RECONNECT: "+t)}function De(e,t){console.error("products::SOCKET_RECONNECT_ERROR: "+t)}function we(e){e.socket.close()}function Be(e,t){e.socket=t}const Ue={newSocket(e,t){let n=!1;return new Promise((a=>{const o=new WebSocket("ws://"+window.location.host+"/ws"+t);o.onmessage=function(t){e.commit("SOCKET_ONMESSAGE",t)},o.onopen=function(t){e.commit("SOCKET_ONOPEN",t),n=!0,a(t)},o.onclose=a=>{e.commit("SOCKET_ONCLOSE",a),setTimeout((()=>{console.log("Socket connection lost, attempting reconnect"),n&&this.reconnect(o,e,t)}),5e3)},o.onerror=function(t){e.commit("SOCKET_ONCLOSE",t)},o.onreconnect=function(t){e.commit("SOCKET_RECONNECT",t)},o.onreconnecterror=function(){e.commit("SOCKET_RECONNECT_ERROR")},window.onbeforeunload=function(){o.onclose=function(){},o.close()},e.commit("SET_SOCKET",o)}))},reconnect(e,t,n){return new Promise(((a,o)=>{e.readyState!==WebSocket.CONNECTING&&e.readyState!==WebSocket.OPEN?this.newSocket(t,n).then((e=>{a(e)})).catch((e=>{o(e)})):a()}))}};function Fe(e){const t=Ue.newSocket(e,"/device/devices");return t}function _e(e){e.commit("SOCKET_CLOSE")}const Me={namespaced:!0,state:be,getters:u,mutations:p,actions:g};function Ge(){return{allPackages:[]}}function xe(e,t){for(var n=[],a=0;a<e.allPackages.length;a++)!0===e.allPackages[a].visible&&e.allPackages[a].required===t&&n.push(e.allPackages[a]);return n}function Ve(e){return xe(e,"required")}function He(e){return xe(e,"recommended")}function Ye(e){return xe(e,"optional")}function Ke(e){return e.allPackages.length}function qe(e){return e.allPackages}const We=JSON.parse('[{"id":"00000001","label":"Package name 1","desc":"Package name 1 description","installed":0,"needAccess":0,"hasLicense":0,"required":"recommended","visible":true,"imageUrl":"icons/8-bit.png","releaseNotes":"4.2.2\\n   Removing zgm130 from part compatibility\\n4.2.1\\n   Demoting package from recommended to optional\\n4.2.0\\n\\tGeneral bug fixes\\n4.0.2\\n\\tAdded signature for authentication\\n4.0.1\\n\\tExtend infrastructure for multiple IDEs\\n4.0.0\\n\\tInitial release"},{"id":"00000002","label":"Package name 2","desc":"Package name 2 description","installed":0,"needAccess":0,"hasLicense":0,"required":"optional","visible":true,"imageUrl":"icons/8-bit.png","releaseNotes":"4.2.2\\n   Removing zgm130 from part compatibility\\n4.2.1\\n   Demoting package from recommended to optional\\n4.2.0\\n\\tGeneral bug fixes\\n4.0.2\\n\\tAdded signature for authentication\\n4.0.1\\n\\tExtend infrastructure for multiple IDEs\\n4.0.0\\n\\tInitial release"},{"id":"00000003","label":"Package name 3","desc":"Package name 3 description","installed":0,"needAccess":0,"hasLicense":0,"required":"required","visible":true,"imageUrl":"icons/8-bit.png","releaseNotes":"4.2.2\\n   Removing zgm130 from part compatibility\\n4.2.1\\n   Demoting package from recommended to optional\\n4.2.0\\n\\tGeneral bug fixes\\n4.0.2\\n\\tAdded signature for authentication\\n4.0.1\\n\\tExtend infrastructure for multiple IDEs\\n4.0.0\\n\\tInitial release"},{"id":"00000004","label":"Has license package 1","desc":"Has license package 1 description","installed":0,"needAccess":0,"hasLicense":1,"required":"recommended","visible":true,"imageUrl":"icons/8-bit.png","releaseNotes":"4.2.2\\n   Removing zgm130 from part compatibility\\n4.2.1\\n   Demoting package from recommended to optional\\n4.2.0\\n\\tGeneral bug fixes\\n4.0.2\\n\\tAdded signature for authentication\\n4.0.1\\n\\tExtend infrastructure for multiple IDEs\\n4.0.0\\n\\tInitial release"},{"id":"00000005","label":"Needs access package 1","desc":"Needs access package 1 description","installed":0,"needAccess":1,"hasLicense":0,"required":"optional","visible":true,"imageUrl":"icons/8-bit.png","releaseNotes":"4.2.2\\n   Removing zgm130 from part compatibility\\n4.2.1\\n   Demoting package from recommended to optional\\n4.2.0\\n\\tGeneral bug fixes\\n4.0.2\\n\\tAdded signature for authentication\\n4.0.1\\n\\tExtend infrastructure for multiple IDEs\\n4.0.0\\n\\tInitial release"},{"id":"00000006","label":"Has license package 2","desc":"Has license package 2 description","installed":0,"needAccess":0,"hasLicense":1,"required":"required","visible":true,"imageUrl":"icons/8-bit.png","releaseNotes":"4.2.2\\n   Removing zgm130 from part compatibility\\n4.2.1\\n   Demoting package from recommended to optional\\n4.2.0\\n\\tGeneral bug fixes\\n4.0.2\\n\\tAdded signature for authentication\\n4.0.1\\n\\tExtend infrastructure for multiple IDEs\\n4.0.0\\n\\tInitial release"},{"id":"00000007","label":"Package name 7","desc":"Package name 7 description","installed":0,"needAccess":0,"hasLicense":0,"required":"recommended","visible":true,"imageUrl":"icons/8-bit.png","releaseNotes":"4.2.2\\n   Removing zgm130 from part compatibility\\n4.2.1\\n   Demoting package from recommended to optional\\n4.2.0\\n\\tGeneral bug fixes\\n4.0.2\\n\\tAdded signature for authentication\\n4.0.1\\n\\tExtend infrastructure for multiple IDEs\\n4.0.0\\n\\tInitial release"},{"id":"00000008","label":"Needs access package 2","desc":"Needs access package 2 description","installed":0,"needAccess":1,"hasLicense":0,"required":"optional","visible":true,"imageUrl":"icons/8-bit.png","releaseNotes":"4.2.2\\n   Removing zgm130 from part compatibility\\n4.2.1\\n   Demoting package from recommended to optional\\n4.2.0\\n\\tGeneral bug fixes\\n4.0.2\\n\\tAdded signature for authentication\\n4.0.1\\n\\tExtend infrastructure for multiple IDEs\\n4.0.0\\n\\tInitial release"},{"id":"00000009","label":"Needs access package 3","desc":"Needs access package 3 description","installed":0,"needAccess":1,"hasLicense":0,"required":"optional","visible":true,"imageUrl":"icons/8-bit.png"},{"id":"00000010","label":"Needs access package 4","desc":"Needs access package 4 description","installed":0,"needAccess":1,"hasLicense":0,"required":"optional","visible":true,"imageUrl":"icons/8-bit.png"},{"id":"00000011","label":"Needs access package 5","desc":"Needs access package 5 description","installed":0,"needAccess":1,"hasLicense":0,"required":"optional","visible":true,"imageUrl":"icons/8-bit.png"},{"id":"00000012","label":"Needs access package 6","desc":"Needs access package 6 description","installed":0,"needAccess":1,"hasLicense":0,"required":"optional","visible":true,"imageUrl":"icons/8-bit.png","releaseNotes":"4.2.2\\n   Removing zgm130 from part compatibility\\n4.2.1\\n   Demoting package from recommended to optional\\n4.2.0\\n\\tGeneral bug fixes\\n4.0.2\\n\\tAdded signature for authentication\\n4.0.1\\n\\tExtend infrastructure for multiple IDEs\\n4.0.0\\n\\tInitial release"},{"id":"00000013","label":"Needs access package 7","desc":"Needs access package 7 description","installed":0,"needAccess":1,"hasLicense":0,"required":"optional","visible":true,"imageUrl":"icons/8-bit.png","releaseNotes":"4.2.2\\n   Removing zgm130 from part compatibility\\n4.2.1\\n   Demoting package from recommended to optional\\n4.2.0\\n\\tGeneral bug fixes\\n4.0.2\\n\\tAdded signature for authentication\\n4.0.1\\n\\tExtend infrastructure for multiple IDEs\\n4.0.0\\n\\tInitial release"},{"id":"00000014","label":"Needs access package 8","desc":"Needs access package 8 description","installed":0,"needAccess":1,"hasLicense":0,"required":"optional","visible":true,"imageUrl":"icons/8-bit.png","releaseNotes":"4.2.2\\n   Removing zgm130 from part compatibility\\n4.2.1\\n   Demoting package from recommended to optional\\n4.2.0\\n\\tGeneral bug fixes\\n4.0.2\\n\\tAdded signature for authentication\\n4.0.1\\n\\tExtend infrastructure for multiple IDEs\\n4.0.0\\n\\tInitial release"},{"id":"00000015","label":"Needs access package 9","desc":"Needs access package 9 description","installed":0,"needAccess":1,"hasLicense":0,"required":"optional","visible":true,"imageUrl":"icons/8-bit.png"},{"id":"00000016","label":"Needs access package 10","desc":"Needs access package 10 description","installed":0,"needAccess":1,"hasLicense":0,"required":"optional","visible":true,"imageUrl":"icons/8-bit.png"},{"id":"00000017","label":"Needs access package 11","desc":"Needs access package 11 description","installed":0,"needAccess":1,"hasLicense":0,"required":"required","visible":true,"imageUrl":"icons/8-bit.png"},{"id":"00000018","label":"Needs access package 12","desc":"Needs access package 12 description","installed":0,"needAccess":1,"hasLicense":0,"required":"required","visible":true,"imageUrl":"icons/8-bit.png"},{"id":"000000019","label":"Package name 13","desc":"Package name 13 description","installed":0,"needAccess":0,"hasLicense":0,"required":"required","visible":false,"imageUrl":"icons/8-bit.png"}]');function ze(e){Ze(e,We)}function Ze(e,t){e.allPackages=t,e.allPackages.forEach((function(e){"optional"===e.required?e.selected=!1:e.selected=!0,e.extensions&&e.extensions.sort(((e,t)=>e.label.localeCompare(t.label))),e.extensionsInfo&&e.extensionsInfo.sort(((e,t)=>e.label.localeCompare(t.label)))}))}function je(e,t){const n=e.allPackages.find((e=>e.id===t.id));n&&(n.location=t.location)}function Xe(e){if(void 0!==e.allPackages)for(var t=0;t<e.allPackages.length;t++)e.allPackages[t].selected=!1}function $e(e,t){if(void 0!==e.allPackages)for(var n=0;n<e.allPackages.length;n++)if(e.allPackages[n].id===t.packageId){e.allPackages[n].selected=t.selected;break}}function Je(e,t){if(void 0!==e.allPackages){for(var n=0;n<t.packageSet.length;n++)for(var a=0;a<e.allPackages.length;a++)if(e.allPackages[a].id===t.packageSet[n].id){e.allPackages[a].selected=!1;break}for(var o=0;o<t.selected.length;o++)for(var r=0;r<e.allPackages.length;r++)if(e.allPackages[r].id===t.packageSet[o].id){e.allPackages[r].selected=!0;break}}}function Qe({commit:e},t){return K.Z.wizardGetPackagesFor(t).then((t=>e("setPackages",t))).catch((()=>e("setDefaultPackages")))}function et({commit:e},t){return K.Z.wizardGetPackagesForDevices(t).then((t=>e("setPackages",t.data))).catch((()=>e("setDefaultPackages")))}const tt={namespaced:!0,state:Ge,getters:m,mutations:f,actions:S},nt={toolPackages:{}},at=JSON.parse('{"categoryPackages":[{"category":"Tools","packages":[{"installState":"Install","releaseNotes":"4.0.2\\n\\tMinor UI fixes\\n4.0.1\\n\\tFollowing version change in sibling features\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.flashprogrammer.8051.feature.metadata.resource.feature.group/icon_display_flashProgrammer.png","installEnabled":true,"id":"com.silabs.ss.tool.flashprogrammer.8051.feature.metadata.resource.feature.group","label":"Flash Programmer for 8051 - 4.0.3","featurePackageId":"com.silabs.ss.tool.flashprogrammer.8051.feature","desc":"Simplicity Studio Flash Programmer for 8051"},{"installState":"Install","releaseNotes":"4.0.3\\n\\tImproved handling of missing adapter pack\\n4.0.2\\n\\trefresh device detection values when changing MCU mode\\n4.0.1\\n\\tImprove error handling from flashing process\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.support.hwtools.feature.metadata.resource.feature.group/icon_hwtools_support.png","installEnabled":true,"id":"com.silabs.ss.support.hwtools.feature.metadata.resource.feature.group","label":"Debug Adapter Support for J-Link OB - 4.0.3","featurePackageId":"com.silabs.ss.support.hwtools.feature","desc":"Configuration support for Exx32/EFM8 On-board J-Link debug adapters"},{"installState":"Install","releaseNotes":"4.0.3\\n\\tImprove behavior with unsupported parts\\n4.0.2\\n\\tAdd \'Devices\' view to Xpress Configurator perspective default\\n4.0.1\\n\\tFix auto-scroll issue\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.xpressconfig.feature.metadata.resource.feature.group/icon_display_FixedFunction.png","installEnabled":true,"id":"com.silabs.ss.tool.xpressconfig.feature.metadata.resource.feature.group","label":"Xpress Configurator - 4.0.3","featurePackageId":"com.silabs.ss.tool.xpressconfig.feature","desc":"Simplicity Studio Xpress Configurator"},{"installState":"Install","releaseNotes":"4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.swoterminal.feature.metadata.resource.feature.group/icon_display_SWOTerminal.png","installEnabled":true,"id":"com.silabs.ss.tool.swoterminal.feature.metadata.resource.feature.group","label":"SWO Terminal Tool - 4.0.0","featurePackageId":"com.silabs.ss.tool.swoterminal.feature","desc":"Simplicity Studio SWO Terminal"},{"installState":"Install","releaseNotes":"4.0.1\\n\\tEnable logging when starting the tool\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.battery_estimator.feature.metadata.resource.feature.group/icon_display_energyAwareBattery.png","installEnabled":true,"id":"com.silabs.ss.tool.battery_estimator.feature.metadata.resource.feature.group","label":"Simplicity Battery Estimator - 4.0.1","featurePackageId":"com.silabs.ss.tool.battery_estimator.feature","desc":"Simplicity Studio Battery Estimator"},{"installState":"Install","releaseNotes":"4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.example.feature.metadata.resource.feature.group/example_tool_icon.png","installEnabled":true,"id":"com.silabs.ss.tool.example.feature.metadata.resource.feature.group","label":"Tool Example - 4.0.0","featurePackageId":"com.silabs.ss.tool.example.feature","desc":"Simplicity Studio Tool Example"},{"installState":"Install","releaseNotes":"4.0.2\\n\\tupdates for jlink 6 compatibility\\n4.0.1\\n\\tUpdate for Commander version 0.18.1\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.flashprogrammer.exx32.feature.metadata.resource.feature.group/icon_display_flashProgrammer.png","installEnabled":true,"id":"com.silabs.ss.tool.flashprogrammer.exx32.feature.metadata.resource.feature.group","label":"Flash Programmer for Exx32 - 4.0.3","featurePackageId":"com.silabs.ss.tool.flashprogrammer.exx32.feature","desc":"Simplicity Studio Flash Programmer for Exx32"},{"installState":"Install","releaseNotes":"4.0.3\\n\\tVarious improvements and bug fixes to infrastructureq\\n4.0.2\\n\\tPass Commander path from Studio to BG Tool\\n4.0.1\\n\\tFixed bug where missing BGBuild toolchains could cause multiple toolchain refreshes.\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.bgtool.feature.metadata.resource.feature.group/bg_tool_40x40.png","installEnabled":true,"id":"com.silabs.ss.tool.bgtool.feature.metadata.resource.feature.group","label":"Simplicity Bluetooth Support - 4.0.3","featurePackageId":"com.silabs.ss.tool.bgtool.feature","desc":"Simplicity Studio Bluetooth Support."},{"installState":"Install","releaseNotes":"4.0.1\\n\\tExtend infrastructure for multiple IDEs\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.ide.keil.feature.metadata.resource.feature.group/keil_uvision_icon.png","installEnabled":true,"id":"com.silabs.ss.tool.ide.keil.feature.metadata.resource.feature.group","label":"Keil uVision Integration - 4.0.1","featurePackageId":"com.silabs.ss.tool.ide.keil.feature","desc":"Support of Keil uVision in Simplicity Studio"},{"installState":"Install","releaseNotes":"4.0.5\\n\\tImproved logging\\n4.0.4\\n\\tFix font size scaling seen in BGM121\\n4.0.3\\n\\tFix for invalid SWT widget access\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.hwconfig.feature.metadata.resource.feature.group/icon_display_configurator.png","installEnabled":true,"id":"com.silabs.ss.tool.hwconfig.feature.metadata.resource.feature.group","label":"Hardware Configurator - 4.0.5","featurePackageId":"com.silabs.ss.tool.hwconfig.feature","desc":"Simplicity Studio Hardware Configurator"},{"installState":"Install","releaseNotes":"4.0.5\\n\\tEnable logging\\n4.0.2\\n\\tImproved device arbitration between tool\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.energyprofiler.si8051.feature.metadata.resource.feature.group/icon_display_EnergyProfiler.png","installEnabled":true,"id":"com.silabs.ss.tool.energyprofiler.si8051.feature.metadata.resource.feature.group","label":"Simplicity Energy Profiler for EFM8 - 4.0.5","featurePackageId":"com.silabs.ss.tool.energyprofiler.si8051.feature","desc":"Simplicity Studio Energy Profiler for EFM8"},{"installState":"Install","releaseNotes":"4.0.1\\n\\tVarious bug fixes\\n\\tExtend functionality around multiple IDEs\\n\\tImprove behavior with multiple IAR installations\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.ide.iar.feature.metadata.resource.feature.group/iar_workbench_icon.png","installEnabled":true,"id":"com.silabs.ss.tool.ide.iar.feature.metadata.resource.feature.group","label":"IAR Embedded Workbench Integration - 4.0.1","featurePackageId":"com.silabs.ss.tool.ide.iar.feature","desc":"Support of IAR Embedded Workbench in Simplicity Studio"},{"installState":"Install","releaseNotes":"4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.support.c8051.feature.metadata.resource.feature.group/icon_c8051_support.png","installEnabled":true,"id":"com.silabs.ss.support.c8051.feature.metadata.resource.feature.group","label":"Debug Adapter Support for C8051 - 4.0.0","featurePackageId":"com.silabs.ss.support.c8051.feature","desc":"Configuration support for C8051 debug adapters"},{"installState":"Install","releaseNotes":"4.0.6\\n\\tFixed stepping over JZ and JNZ instructions\\n4.0.5\\n\\tCDT does not need to compile *.INC files.\\n4.0.4\\n\\tImprove 3-byte pointer handling in the debugger\\n4.0.3\\n\\tGeneral bug fixes\\n4.0.2\\n\\tEmploy Keil serial number for 8051 tools that provides support until March 2019\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.ide.c8051.feature.metadata.resource.feature.group/icon_display_SimplicityIDE.png","installEnabled":true,"id":"com.silabs.ss.tool.ide.c8051.feature.metadata.resource.feature.group","label":"8051 IDE - 4.0.6","featurePackageId":"com.silabs.ss.tool.ide.c8051.feature","desc":"Allows you to create, edit, build and debug 8051 applications"},{"installState":"Install","releaseNotes":"No content found","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.wireless.tools.feature.metadata.resource.feature.group/icon_display_radioEvaluation.png","installEnabled":true,"id":"com.silabs.wireless.tools.feature.metadata.resource.feature.group","label":"Wireless Tools - 4.16.0","featurePackageId":"com.silabs.wireless.tools.feature","desc":"Simplicity Studio App Builder, Network Analyzer, RF Eval and other wireless tools"},{"installState":"Install","releaseNotes":"4.0.6\\n\\tFix bug to display correct active/inactive thresholds values in plots\\n\\tAdd support for CPT212 and CPT213 devices\\n\\tAdd ability to profile device over serial port. Only works for C8051/EFM8 related devices.\\n4.0.5\\n\\tAdded vcp setuptask as dependency to capsense profiler\\n\\tFix issue with error handling when device disconnects\\n4.0.4\\n\\tImproved device arbitration between tool\\n4.0.3\\n\\tAdd 8051 F996 part compatibility\\n4.0.2\\n\\tFix missing selectable channels from the Control Panel for the Noise view\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.analysis.cs0.feature.metadata.resource.feature.group/capsense_profiler_40x40.png","installEnabled":true,"id":"com.silabs.ss.tool.analysis.cs0.feature.metadata.resource.feature.group","label":"Simplicity Capacitive Sense Profiler - 4.0.6","featurePackageId":"com.silabs.ss.tool.analysis.cs0.feature","desc":"Simplicity Capacitive Sense Profiler"},{"installState":"Install","releaseNotes":"4.0.4\\n\\tBuild infrastructure change\\n4.0.3\\n\\tProvide the required part name to JLink for debugging\\n4.0.2\\n\\tupdates for jlink 6 compatibility\\n4.0.1\\n\\tadd support for utilities that need jlink speed in kHz\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.support.jlink.feature.metadata.resource.feature.group/icon_jlink_support.png","installEnabled":true,"id":"com.silabs.ss.support.jlink.feature.metadata.resource.feature.group","label":"Debug Adapter Support for J-Link - 4.0.4","featurePackageId":"com.silabs.ss.support.jlink.feature","desc":"Configuration support for J-Link debug adapters"},{"installState":"Install","releaseNotes":"No content found","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.epp.mpc.feature.metadata.resource.feature.group/marketplace32.png","installEnabled":true,"id":"com.silabs.ss.epp.mpc.feature.metadata.resource.feature.group","label":"EPP Marketplace Client - 1.4.1.v20150916-1928","featurePackageId":"com.silabs.ss.epp.mpc.feature","desc":"This version of EPP Marketplace Client supports Eclipse 4.4.0 or later."},{"installState":"Install","releaseNotes":"4.0.8\\n\\tMore efficient error logging\\n4.0.7\\n\\tFix script for path manipulation of dependency file content on Mac and Linux\\n4.0.6\\n\\tMinor bug fixes\\n4.0.5\\n\\tMinor bug fixes\\n4.0.4\\n\\tFix bug with IAR toolchain path and improve error reporting\\n4.0.3\\n\\tMinor bug fix\\n4.0.2\\n\\tMinor bug fixes\\n4.0.1\\n\\tFix issues with script validity check and execute flag \\n4.0.0\\n\\tInitial version","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.toolchain.iar.arm.support.feature.metadata.resource.feature.group/toolchain_40x40.png","installEnabled":true,"id":"com.silabs.ss.toolchain.iar.arm.support.feature.metadata.resource.feature.group","label":"IAR ARM Toolchain Integration - 4.0.8","featurePackageId":"com.silabs.ss.toolchain.iar.arm.support.feature","desc":"This package allows you to use the IAR ARM toolchain from the Simplicity Studio IDE"},{"installState":"Install","releaseNotes":"4.0.5\\n\\tEnable logging\\n4.0.3\\n\\tImproved device arbitration between tool\\n4.0.1\\n\\tensure code correlation support is correctly re-evaluated on each run\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.energyprofiler.si32.feature.metadata.resource.feature.group/icon_display_EnergyProfiler.png","installEnabled":true,"id":"com.silabs.ss.tool.energyprofiler.si32.feature.metadata.resource.feature.group","label":"Simplicity Energy Profiler for Exx32 - 4.0.5","featurePackageId":"com.silabs.ss.tool.energyprofiler.si32.feature","desc":"Simplicity Studio Energy Profiler for Exx32"},{"installState":"Install","releaseNotes":"4.0.5\\n\\tAdd default \\"Run As\\" Launch Configuration\\n4.0.4\\n\\tGeneral bug fixes\\n4.0.3\\n\\tThe GCC compiler will now automatically create s37 files\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.ide.arm.feature.metadata.resource.feature.group/icon_display_SimplicityIDE.png","installEnabled":true,"id":"com.silabs.ss.tool.ide.arm.feature.metadata.resource.feature.group","label":"ARM IDE - 4.0.5","featurePackageId":"com.silabs.ss.tool.ide.arm.feature","desc":"Allows you to create, edit, build and debug ARM applications"},{"installState":"Install","releaseNotes":"No content found","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.wireless.integration.feature.metadata.resource.feature.group/icon_display_SimplicityIDE.png","installEnabled":true,"id":"com.silabs.wireless.integration.feature.metadata.resource.feature.group","label":"Wireless Tools IDE Integration - 4.16.0","featurePackageId":"com.silabs.wireless.integration.feature","desc":"Integration of wireless tools with Studio IDE"},{"installState":"Install","releaseNotes":"4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Projects/Studio_V5/studio_dev/eclipse.4.14.x_workspace/.metadata/.plugins/org.eclipse.pde.core/Simplicity%20Studio%20V5%20Everything/cache/com.silabs.ss.tool.config2.feature.metadata.resource.feature.group/legacy_configuration_40x40.png","installEnabled":true,"id":"com.silabs.ss.tool.config2.feature.metadata.resource.feature.group","label":"Simplicity Config 2 - 4.0.2","featurePackageId":"com.silabs.ss.tool.config2.feature","desc":"Simplicity Studio Config 2, a legacy configurator."}]}]}'),ot={SET_TOOL_PACKAGES(e,t){e.toolPackages=t},setDefaultPackages(e){console.log("Toolpackages setting defaults"),e.toolPackages=at}},rt={getToolPackages(e){return K.Z.getToolPackages().then((t=>e.commit("SET_TOOL_PACKAGES",t))).catch((()=>e.commit("setDefaultPackages")))}},st={namespaced:!0,mutations:ot,actions:rt,state:nt},it={toolChainPackages:{}},ct={SET_TOOLCHAIN_PACKAGES(e,t){e.toolChainPackages=t}},lt={getToolChainPackages(e){return K.Z.getToolChainPackages().then((t=>e.commit("SET_TOOLCHAIN_PACKAGES",t)))}},dt={namespaced:!0,mutations:ct,actions:lt,state:it},ut={sdkPackages:{},sdkCategories:[],preferredSdk:"All",preferredVersion:"Latest",preferredGeckoPlatform:"All",impactedExtensions:[]},pt={},gt={SET_SDK_PACKAGES(e,t){e.sdkPackages=t},SET_SDK_CATEGORIES(e,t){e.sdkCategories=t.sdkCategories},SET_PREFERRED_SDK(e,t){e.preferredSdk=t},SET_PREFERRED_GECKO_PLATFORM(e,t){e.preferredGeckoPlatform=t},SET_PREFERRED_SDK_VERSION(e,t){e.preferredVersion=t},setDefaultPackages(e){console.log("SDK setting defaults"),e.sdkCategories=pt.sdkCategories},SET_IMPACTED_EXTENSIONS(e,t){e.impactedExtensions=t.impactedextensions}},mt={getSdkPackages(e){let t=[e.state.preferredSdk,e.state.preferredVersion,e.state.preferredGeckoPlatform];return K.Z.getSdkPackages(t).then((t=>e.commit("SET_SDK_PACKAGES",t)))},postRequestAccess(e,t){return K.Z.postRequestAccess(t)},setPreferredSdk(e,t){e.commit("SET_PREFERRED_SDK",t)},setPreferredGeckoPlatform(e,t){e.commit("SET_PREFERRED_GECKO_PLATFORM",t)},setPreferredVersion(e,t){e.commit("SET_PREFERRED_SDK_VERSION",t)},getSdks(e){return K.Z.getSdks().then((t=>e.commit("SET_SDK_CATEGORIES",t))).catch((()=>e.commit("setDefaultPackages")))},getImpactedExtensions(e,t){return K.Z.getImpactedExtensions(t).then((t=>e.commit("SET_IMPACTED_EXTENSIONS",t)))}},ft={namespaced:!0,mutations:gt,actions:mt,state:ut},St={updatePackages:{}},ht={SET_UPDATE_PACKAGES(e,t){e.updatePackages=t}},Et={getUpdatePackages(e){return K.Z.getUpdatePackages().then((t=>{e.commit("SET_UPDATE_PACKAGES",t)}))}},At={namespaced:!0,mutations:ht,actions:Et,state:St},bt={earlyAccessPackages:{}},Tt=JSON.parse('{"categoryPackages":[{"installState":"Uninstall","isInstalled":true,"installEnabled":true,"imageUrl":"icons/icon_stack.png","category":"EFM32 32-bit Products Early Access - 4.0.42","packages":[{"installState":"Uninstall","isInstalled":true,"releaseNotes":"4.0.92\\n\\tAdd data for Thunderboard EFR32BG22\\n\\tPackage version for early_access in  SV4.1.13.1\\n4.0.91\\n\\tPackage version for early_access in  SV4.1.13.0\\n4.0.90\\n\\tAdd data for WFM200 board\\n\\tPackage version for rel in  SV4.1.13.0\\n4.0.89\\n\\tNo metadata changes\\n\\tPackage version for early_access in  SV4.1.11.8\\n4.0.88\\n\\tPackage version for early_access in  SV4.1.11.7\\n4.0.87\\n\\tInclude missing target interface in boards BRD4308A, BRD4308B and BRD4309A\\n\\tPackage version for rel in  SV4.1.11.7\\n4.0.86\\n\\tAdd data for BRD4179A/B radio boards\\n\\tInternal version increment for early access in SV4.1.11.6\\n4.0.85\\n\\tData update to relfect BGM210 and MG210 OPNs\\n\\tPackage version for rel in SV4.1.11.6\\n4.0.84\\n\\tAdd data for BRD4179A/B radio boards\\n4.0.83\\n\\tAdd board data for BRD4308A, BRD4308B and BRD4309A\\n4.0.82\\n\\tPropagate version change\\n4.0.81\\n\\tVarious changes in board names making them more consistent on how they appear on www.silabs.com\\n\\tAdd data for SLWSTK6101D kit and associated boards\\n\\tVarious asset references updated\\n4.0.80\\n\\tAdd board date BRD4182 in Early Access package\\n4.0.79\\n\\tMiscellaneous data updates\\n\\tARTIK module radio boards are no longer supported for creating new projects\\n4.0.78\\n\\tPropagate version change\\n4.0.77\\n\\tAdd board support for BRD4180A and BRD4181A, EFR32xG21 radio boards\\n\\tAdd board support for BRD4321A - WGM160P device\\n4.0.76\\n\\tPropagate version change\\n4.0.75\\n\\tAdd data for an infrastructure property to support device filtering\\n4.0.74\\n\\tPropagate version change\\n4.0.73\\n\\tAdd data for Silicon Labs WF200 Expansion Board and Kit\\n4.0.72\\n\\tPropagate version change\\n\\tCorrection to EFR32MG21 board information\\n4.0.71\\n\\tAdded board and kit data for Thunderboard EFM32GG12\\n4.0.70\\n\\tAdd EFR32MG21 2.4 GHz 10 dBm Radio Board (BRD41741A)\\n4.0.69\\n\\tPropagate version change\\n4.0.68\\n\\tUpdate metadata following improvements to assets sourcing\\n4.0.67\\n\\tPropagate version change\\n4.0.66\\n\\tAdd data for Silicon Labs LTE-M Expansion Board and Kit\\n4.0.65\\n\\tPropagate version change\\n4.0.64\\n\\tData updates for Gecko SDK Suite v2.4 release:\\n\\t- Better support for module devices\\n\\t- Support for BGX modules\\n4.0.63\\n\\tPropagate version change\\n4.0.62\\n\\tPropagate version change\\n4.0.61\\n\\tUpdate references to board BRD5200\\n4.0.60\\n\\tAdd data for BRD4171A\\n4.0.59\\n\\tAdd data for BRD4305A, BRD4305C, BRD4305D, and BRD4305E\\n\\tAdd data for BRD4172A, BRD4172B, BRD4173A, BRD4174A, BRD4174B, and BRD4175A\\n4.0.58\\n\\tPropagate version change\\n4.0.57\\n\\tPropagate version change\\n4.0.56\\n\\tAdd BRD4170A board data\\n\\tAdd board data for BRD4105A, BRD4169A, BRD4169B\\n4.0.55\\n\\tPropagate version change\\n4.0.54\\n\\tAdd support for EFM32TG11 boards\\n4.0.53\\n\\tPropagate version change\\n4.0.52\\n\\tAdd support for boards BRD4306A, BRD4306B, BRD4306C, and BRD4306D\\n4.0.51\\n\\tPropagate version change\\n4.0.50\\n\\tFix reference to Thundboard UB3 document\\n4.0.49\\n\\tAdd Isolated CAN Expansion Board data\\n\\tAdd Thunderboard UB3 data\\n4.0.48\\n\\tPropagate version change\\n4.0.47\\n\\tFix missing reference to kit firmware asset for Thunderboard Sense 2\\n4.0.46\\n\\tAdd controlled data for EFR32MG14 and EFR32BG14 board\\n4.0.45\\n\\tAdd data for Thunderboard Sense 2 board\\n\\tAdd data for EFR32FG14 boards and kits\\n4.0.44\\n\\tPropagate version change\\n4.0.43\\n\\tPropagate version change\\n4.0.42\\n\\tPropagate version change\\n4.0.41\\n\\tAdd EFM8BB3 USB Type-C Charger Ref Design kit and board data\\n4.0.40\\n\\tPropagate version change\\n4.0.39\\n\\tPropagate version change\\n4.0.38\\n\\tAdd BRD4306 variants A, B, C, and D radio boards (early access)\\n4.0.37\\n\\tAdd USB and RS232/RS485 isolation expansion cards definitions\\n4.0.36\\n\\tPropagate version change\\n4.0.35\\n\\tAdd suupport for EFM32GG11 kit ********** and board BRD2204A\\n4.0.34\\n\\tPropagate version change\\n4.0.33\\n\\tAdd support for Si72xx Hall Effect Sensor\\n4.0.32\\n\\tPropagate version change\\n4.0.31\\n\\tAdd doc references to CPT007B and CPT112S\\n\\tAdd support for CPT212 and CPT213\\n\\tCorrect BRD4168A name\\n4.0.30\\n\\tPropagate version change\\n4.0.29\\n\\tPropagate version change\\n4.0.28\\n\\tPropagate version change\\n4.0.27\\n\\tNew boards support\\n4.0.26\\n\\tAdded SLWSTK6020B, BRD4104A-A00, BRD4167A-A00 and BRD4168A-A00 to public\\n4.0.25\\n\\tCorrection to BRD4101 document references\\n4.0.24\\n\\tSupport for EFx32xG13 boards\\n4.0.23\\n\\tSupport for EFx32xG12 boards\\n4.0.22\\n\\tAddition of new boards\\n4.0.21\\n\\tInternal reorganization of data files\\n\\tAdd property to some wireless boards.\\n\\tRemove incorrect compatibility setting for BRD4001.\\n\\tRemove duplicate BRD4160A definition.\\n4.0.19\\n\\tLaunch of EFR32BG WLCSP package\\n4.0.17\\n\\tMoving IoT Solutions to separate location\\n4.0.15\\n\\tPropagate version change\\n4.0.14\\n\\tAdded BGM121\\n4.0.12\\n\\tLatest boards and kits\\n4.0.10\\n\\tAdded ARTIK to public boards and kits\\n4.0.9\\n\\tAdded MGM-111\\n\\tAdded Thunderboard Sense\\n4.0.8\\n\\tPropagate version change\\n4.0.5\\n\\tCorrection to boards and kits list\\n4.0.4\\n\\tArtik early access of boards and kits list\\n4.0.3\\n\\tEarly access of boards and kits list\\n4.0.2\\n\\tCorrection to boards and kits list\\n4.0.1\\n\\tFixes to asset references on various wireless parts\\n4.0.0\\n\\tInitial release","id":"com.silabs.metadata.hwtools.feature.metadata.resource.feature.group","label":"Hardware Tools Board Support - 4.0.92","desc":"Hardware Tools Board Metadata"},{"installState":"Install","releaseNotes":"4.0.64\\n\\tNo metadata changes\\n\\tPackage version for early_access in SV4.1.13.1\\n4.0.63\\n\\tPackage version for early_access in SV4.1.13.0\\n4.0.62\\n\\tAdding WFM200 modules data\\n4.0.61\\n\\tPackage version for early_access in SV4.1.11.9\\n4.0.60\\n\\tEnable viewing NVIC/SysTick/SystemControl registers in debugger on supported devices\\n\\tPackage version for rel in SV4.1.11.9\\n4.0.59\\n\\tPackage version for early_access in SV4.1.11.7\\n4.0.58\\n\\tUpdate EFM32GG12 parts data to access Cortex-M4 registers for debugger\\n\\tPackage version for rel in SV4.1.11.7\\n4.0.57\\n\\tPackage version for early_access in SV4.1.11.6\\n4.0.56\\n\\tPackage version for early_access in SV4.1.11.4\\n4.0.55\\n\\tPackage version for early_access in SV4.1.11.2\\n4.0.54\\n\\tPackage version for rel in SV4.1.11.0\\t\\n4.0.53\\n\\tPackage version for SV4.1.11.0\\n4.0.52\\n\\tInternal version increment\\n4.0.51\\n\\tAdd WGM160P part data\\n4.0.50\\n\\tInternal version increment\\n4.0.49\\n\\tAdd data for an infrastructure property to support device filtering\\n4.0.48\\n\\tInternal version increment\\n4.0.47\\n\\tAdd WF200 expansion board parts data\\n4.0.46\\n\\tInternal version increment\\n4.0.45\\n\\tAdd EFM32GG12 parts data\\n4.0.44\\n\\tInternal version increment\\n4.0.43\\n\\tAdd display of configuration registers for several EFM32\\n4.0.42\\n\\tInternal version increment\\n4.0.41\\n\\tUpdate metadata following improvements to assets sourcing\\n4.0.40\\n\\tUpdate metadata to reflect use of documentation sourcing infrastructure\\n4.0.39\\n\\tInternal version increment\\n4.0.38\\n\\tMetadata revisions for Gecko SDK Suite v2.4 release\\n4.0.37\\n\\tInternal version increment\\n4.0.36\\n\\tEnable viewing of configurable fault status register (CFSR) on supported devices\\n4.0.35\\n\\tInternal version increment\\n4.0.34\\n\\tInternal version increment\\n4.0.33\\n\\tImproving application notes associations\\n4.0.32\\n\\tInternal version increment\\n4.0.31\\n\\tInternal version increment\\n4.0.30\\n\\tInternal version increment\\n4.0.29\\n\\tAdded GA support for EFM32TG11 parts\\n4.0.28\\n\\tInternal version increment\\n4.0.27\\n\\tRefer to efm32lg_errata.pdf for EMF32LG parts\\n\\tMisc updates to application notes references\\n4.0.26\\n\\tInternal version increment\\n4.0.25\\n\\tInternal version increment\\n4.0.24\\n\\tInternal version increment\\n4.0.23\\n\\tCorrection in reference to AN0024\\n4.0.22\\n\\tInternal version increment\\n4.0.21\\n\\tInternal version increment\\n4.0.20\\n\\tInternal version increment\\n4.0.19\\n\\tInternal version increment\\n4.0.18\\n\\tInternal version increment\\n4.0.17\\n\\tInternal version increment\\n4.0.16\\n\\tAdd support for EMF32GG11 parts\\n4.0.15\\n\\tInternal version increment\\n4.0.14\\n\\tApplication Notes data changes\\n4.0.13\\n\\tEarly access for EFM32TG11\\n4.0.12\\n\\tCorrection to early access part data\\n4.0.11\\n\\tAdding early access parts\\n4.0.10\\n\\tNew part support\\n4.0.9\\n\\tInternal version increment\\n4.0.8\\n\\tUpdates to document references\\n4.0.7\\n\\tInternal version increment\\n4.0.6\\n\\tSupport for EFM32xG13\\n4.0.5\\n\\tSupport for EFM32xG12\\n4.0.4\\n\\tInternal reorganiation of files\\n4.0.3\\n\\tUpdate configurator paths for efm32jg1b, efm32jg12b, efm32pg1b, efm32pg12b parts\\n4.0.0\\n\\tInitial release","id":"com.silabs.metadata.efm32.feature.metadata.resource.feature.group","label":"EFM32 Part Support - 4.0.64","desc":"EFM32 Part Metadata"}]},{"installState":"Install","imageUrl":"icons/icon_stack.png","category":"Fixed Function Products Early Access - 4.0.32","installEnabled":true,"packages":[{"installState":"Install","releaseNotes":"4.0.92\\n\\tAdd data for Thunderboard EFR32BG22\\n\\tPackage version for early_access in  SV4.1.13.1\\n4.0.91\\n\\tPackage version for early_access in  SV4.1.13.0\\n4.0.90\\n\\tAdd data for WFM200 board\\n\\tPackage version for rel in  SV4.1.13.0\\n4.0.89\\n\\tNo metadata changes\\n\\tPackage version for early_access in  SV4.1.11.8\\n4.0.88\\n\\tPackage version for early_access in  SV4.1.11.7\\n4.0.87\\n\\tInclude missing target interface in boards BRD4308A, BRD4308B and BRD4309A\\n\\tPackage version for rel in  SV4.1.11.7\\n4.0.86\\n\\tAdd data for BRD4179A/B radio boards\\n\\tInternal version increment for early access in SV4.1.11.6\\n4.0.85\\n\\tData update to relfect BGM210 and MG210 OPNs\\n\\tPackage version for rel in SV4.1.11.6\\n4.0.84\\n\\tAdd data for BRD4179A/B radio boards\\n4.0.83\\n\\tAdd board data for BRD4308A, BRD4308B and BRD4309A\\n4.0.82\\n\\tPropagate version change\\n4.0.81\\n\\tVarious changes in board names making them more consistent on how they appear on www.silabs.com\\n\\tAdd data for SLWSTK6101D kit and associated boards\\n\\tVarious asset references updated\\n4.0.80\\n\\tAdd board date BRD4182 in Early Access package\\n4.0.79\\n\\tMiscellaneous data updates\\n\\tARTIK module radio boards are no longer supported for creating new projects\\n4.0.78\\n\\tPropagate version change\\n4.0.77\\n\\tAdd board support for BRD4180A and BRD4181A, EFR32xG21 radio boards\\n\\tAdd board support for BRD4321A - WGM160P device\\n4.0.76\\n\\tPropagate version change\\n4.0.75\\n\\tAdd data for an infrastructure property to support device filtering\\n4.0.74\\n\\tPropagate version change\\n4.0.73\\n\\tAdd data for Silicon Labs WF200 Expansion Board and Kit\\n4.0.72\\n\\tPropagate version change\\n\\tCorrection to EFR32MG21 board information\\n4.0.71\\n\\tAdded board and kit data for Thunderboard EFM32GG12\\n4.0.70\\n\\tAdd EFR32MG21 2.4 GHz 10 dBm Radio Board (BRD41741A)\\n4.0.69\\n\\tPropagate version change\\n4.0.68\\n\\tUpdate metadata following improvements to assets sourcing\\n4.0.67\\n\\tPropagate version change\\n4.0.66\\n\\tAdd data for Silicon Labs LTE-M Expansion Board and Kit\\n4.0.65\\n\\tPropagate version change\\n4.0.64\\n\\tData updates for Gecko SDK Suite v2.4 release:\\n\\t- Better support for module devices\\n\\t- Support for BGX modules\\n4.0.63\\n\\tPropagate version change\\n4.0.62\\n\\tPropagate version change\\n4.0.61\\n\\tUpdate references to board BRD5200\\n4.0.60\\n\\tAdd data for BRD4171A\\n4.0.59\\n\\tAdd data for BRD4305A, BRD4305C, BRD4305D, and BRD4305E\\n\\tAdd data for BRD4172A, BRD4172B, BRD4173A, BRD4174A, BRD4174B, and BRD4175A\\n4.0.58\\n\\tPropagate version change\\n4.0.57\\n\\tPropagate version change\\n4.0.56\\n\\tAdd BRD4170A board data\\n\\tAdd board data for BRD4105A, BRD4169A, BRD4169B\\n4.0.55\\n\\tPropagate version change\\n4.0.54\\n\\tAdd support for EFM32TG11 boards\\n4.0.53\\n\\tPropagate version change\\n4.0.52\\n\\tAdd support for boards BRD4306A, BRD4306B, BRD4306C, and BRD4306D\\n4.0.51\\n\\tPropagate version change\\n4.0.50\\n\\tFix reference to Thundboard UB3 document\\n4.0.49\\n\\tAdd Isolated CAN Expansion Board data\\n\\tAdd Thunderboard UB3 data\\n4.0.48\\n\\tPropagate version change\\n4.0.47\\n\\tFix missing reference to kit firmware asset for Thunderboard Sense 2\\n4.0.46\\n\\tAdd controlled data for EFR32MG14 and EFR32BG14 board\\n4.0.45\\n\\tAdd data for Thunderboard Sense 2 board\\n\\tAdd data for EFR32FG14 boards and kits\\n4.0.44\\n\\tPropagate version change\\n4.0.43\\n\\tPropagate version change\\n4.0.42\\n\\tPropagate version change\\n4.0.41\\n\\tAdd EFM8BB3 USB Type-C Charger Ref Design kit and board data\\n4.0.40\\n\\tPropagate version change\\n4.0.39\\n\\tPropagate version change\\n4.0.38\\n\\tAdd BRD4306 variants A, B, C, and D radio boards (early access)\\n4.0.37\\n\\tAdd USB and RS232/RS485 isolation expansion cards definitions\\n4.0.36\\n\\tPropagate version change\\n4.0.35\\n\\tAdd suupport for EFM32GG11 kit ********** and board BRD2204A\\n4.0.34\\n\\tPropagate version change\\n4.0.33\\n\\tAdd support for Si72xx Hall Effect Sensor\\n4.0.32\\n\\tPropagate version change\\n4.0.31\\n\\tAdd doc references to CPT007B and CPT112S\\n\\tAdd support for CPT212 and CPT213\\n\\tCorrect BRD4168A name\\n4.0.30\\n\\tPropagate version change\\n4.0.29\\n\\tPropagate version change\\n4.0.28\\n\\tPropagate version change\\n4.0.27\\n\\tNew boards support\\n4.0.26\\n\\tAdded SLWSTK6020B, BRD4104A-A00, BRD4167A-A00 and BRD4168A-A00 to public\\n4.0.25\\n\\tCorrection to BRD4101 document references\\n4.0.24\\n\\tSupport for EFx32xG13 boards\\n4.0.23\\n\\tSupport for EFx32xG12 boards\\n4.0.22\\n\\tAddition of new boards\\n4.0.21\\n\\tInternal reorganization of data files\\n\\tAdd property to some wireless boards.\\n\\tRemove incorrect compatibility setting for BRD4001.\\n\\tRemove duplicate BRD4160A definition.\\n4.0.19\\n\\tLaunch of EFR32BG WLCSP package\\n4.0.17\\n\\tMoving IoT Solutions to separate location\\n4.0.15\\n\\tPropagate version change\\n4.0.14\\n\\tAdded BGM121\\n4.0.12\\n\\tLatest boards and kits\\n4.0.10\\n\\tAdded ARTIK to public boards and kits\\n4.0.9\\n\\tAdded MGM-111\\n\\tAdded Thunderboard Sense\\n4.0.8\\n\\tPropagate version change\\n4.0.5\\n\\tCorrection to boards and kits list\\n4.0.4\\n\\tArtik early access of boards and kits list\\n4.0.3\\n\\tEarly access of boards and kits list\\n4.0.2\\n\\tCorrection to boards and kits list\\n4.0.1\\n\\tFixes to asset references on various wireless parts\\n4.0.0\\n\\tInitial release","id":"com.silabs.metadata.hwtools.feature.metadata.resource.feature.group","label":"Hardware Tools Board Support - 4.0.92","desc":"Hardware Tools Board Metadata"},{"installState":"Install","releaseNotes":"4.0.48\\n\\tNo metadata changes\\n\\tPackage version for early_access in SV4.1.13.1\\n4.0.47\\n\\tPackage version for early_access in SV4.1.11.9\\n4.0.46\\n\\tPackage version for early_access in SV4.1.13.0\\n4.0.45\\n\\tAdd docs missing from bgx/cpt/wgx parts\\n\\tPackage version for rel in SV4.1.11.9\\n4.0.44\\n\\tPackage version for early_access in SV4.1.11.7\\n4.0.43\\n\\tPackage version for rel in SV4.1.11.7\\n4.0.42\\n\\tPackage version for early access in SV4.1.11.6\\n4.0.41\\n\\tPackage version for early access in SV4.1.11.4\\n4.0.40\\n\\tAdded data about versions of part revisions\\n\\tPackage version for rel in SV4.1.11.4\\n4.0.39\\n\\tPropagate version change\\n4.0.38\\n\\tPropagate version change\\n4.0.37\\n\\tPropagate version change\\n4.0.36\\n\\tAdd data for CP2102N A02 firmware version\\n4.0.35\\n\\tPropagate version change\\n4.0.34\\n\\tAdd data for an infrastructure property to support device filtering\\n4.0.33\\n\\tPropagate version change\\n4.0.32\\n\\tPropagate version change\\n4.0.31\\n\\tPropagate version change\\n4.0.30\\n\\tUpdate references to HTML documentation\\n4.0.29\\n\\tUpdate metadata to reflect use of documentation sourcing infrastructure\\n4.0.28\\n\\tPropagate version change\\n4.0.27\\n\\tPropagate version change\\n4.0.26\\n\\tAdd data for BGX and WGX parts\\n4.0.25\\n\\tPropagate version change\\n4.0.24\\n\\tData packaging for Studio release\\n4.0.23\\n\\tPropagate version change\\n4.0.22\\n\\tEarly Access package metadata update\\n4.0.21\\n\\tPropagate version change\\n4.0.20\\n\\tPropagate version change\\n4.0.19\\n\\tPropagate version change\\n4.0.18\\n\\tPropagate version change\\n4.0.17\\n\\tPropagate version change\\n4.0.16\\n\\tPropagate version change\\n4.0.15\\n\\tPropagate version change\\n4.0.14\\n\\tPropagate version change\\n4.0.13\\n\\tPropagate version change\\n4.0.12\\n\\tAdd asset references for CP2615\\n4.0.11\\n\\tPropagate version change\\n4.0.10\\n\\tPropagate version change\\n4.0.9\\n\\tPropagate version change\\n4.0.8\\n\\tPropagate version change\\n4.0.7\\n\\tAdding CPT212 and CPT213\\n4.0.6\\n\\tPropagate version change\\n4.0.5\\n\\tVarious corrections to documentation references\\n4.0.4\\n\\tAdd schematic and BOM for CP2012N-MiniEK\\n4.0.3\\n\\tAdd metadata and resources for CP2012N-MiniEK\\n4.0.2\\n\\tAdd metadata for new revisions of CPT007 and CP112\\n4.0.1\\n\\tUpdate CP2102/9 with part number and dummy firmware\\n4.0.0\\n\\tInitial release","id":"com.silabs.metadata.ffd.feature.metadata.resource.feature.group","label":"Fixed Function Parts and Kits - 4.0.48","desc":"Fixed Function Part and Kit Metadata"}]},{"installState":"Install","installEnabled":true,"imageUrl":"icons/pm_temp/sdk/icon_stack.png","category":"Wireless Products Early Access - 4.0.58","packages":[{"installState":"Install","releaseNotes":"4.0.71\\n\\tNo metadata changes\\n\\tPackage version for early_access in SV4.1.13.1\\n4.0.70\\n\\tPackage version for early_access in SV4.1.13.0\\n4.0.69\\n\\tAdd a property for EFR32BG21 devices\\n\\tPackage version for rel in SV4.1.13.0\\n4.0.68\\n\\tEnable viewing NVIC/SysTick/SystemControl registers in debugger on supported early access devices\\n\\tPackage version for early_access in SV4.1.11.9\\n4.0.67\\n\\tEnable viewing NVIC/SysTick/SystemControl registers in debugger on supported devices\\n\\tPackage version for rel in SV4.1.11.9\\n4.0.66\\n\\tPackage version for early_access in SV4.1.11.7\\n4.0.65\\n\\tPackage version for rel in SV4.1.11.7\\n4.0.64\\n\\tPackage version for early access in SV4.1.11.6\\n4.0.63\\n\\tData update to reflect updates in BGM210 and MGM210 OPNs\\n4.0.62\\n\\tInternal version increment\\n4.0.61\\n\\tAdd data for BGM210L/P and MGM210L/P modules \\n4.0.60\\n\\tInternal version increment\\n4.0.59\\n\\tAdd part support for bgm21 and mgm21 pcb and lighting modules\\n4.0.58\\n\\tInternal version increment\\n4.0.57\\n\\tAdd support for EFR32MG21 and EFR32BG21 parts\\n4.0.56\\n\\tInternal version increment\\n4.0.55\\n\\tAdd data for an infrastructure property to support device filtering\\n4.0.54\\n\\tInternal version increment\\n4.0.53\\n\\tAdded addition series 2 SoC device for Early Access\\n4.0.52\\n\\tAdded data for module devices ARTIK-020 and ARTIK-030\\n4.0.51\\n\\tInternal version increment\\n4.0.50\\n\\tUpdate metadata to reflect use of documentation sourcing infrastructure\\n4.0.49\\n\\tInternal version increment\\n4.0.48\\n\\tInternal version increment\\n4.0.47\\n\\tData updates for Gecko SDK Suite v2.4 release:\\n\\t- Better support for module devices\\n4.0.46\\n\\tInternal version increment\\n4.0.45\\n\\tEnable viewing of configurable fault status register (CFSR) on supported devices\\n4.0.44\\n\\tInternal version increment\\n4.0.43\\n\\tInternal version increment\\n4.0.42\\n\\tAdded part definitions for\\n\\t\\t* EFR32MG12P232F512GM68\\n\\t\\t* EFR32MG12P132F512GM68\\n\\t\\t* EFR32BG12P433F1024GM68\\n\\t\\t* EFR32BG12P232F1024GM68\\n\\t\\t* EFR32BG12P232F512GM68\\n4.0.41\\n\\tInternal version increment\\n4.0.40\\n\\tCompletion of EFR32xG13 QFN32 5x5 pacakge support\\n4.0.39\\n\\tInternal version increment\\n4.0.38\\n\\tAdd support for EFM32MG12 and EFR32FG12 8x8 QFN68 parts\\n\\tAdd support for EFM32MG14 and EFR32FG14 parts\\n4.0.37\\n\\tInternal version increment\\n4.0.36\\n\\tAdd EFR32FG1V232 parts\\n4.0.35\\n\\tAdded FG12 and MG12 QFN68 parts to early access\\n4.0.34\\n\\tAdd support for new EFR32MG13 and EFR32BG13 modules\\n4.0.31-4.0.33\\n\\tInternal version increment\\n4.0.30\\n\\tClean up EFR32MG14 and EFR32BG14 controlled access\\n4.0.29\\n\\tAdd data for Thunderboard Sense 2 board\\n4.0.28\\n\\tInternal version increment\\n4.0.27\\n\\tInternal version increment\\n4.0.26\\n\\tInternal version increment\\n4.0.25\\n\\tInternal version increment\\n4.0.24\\n\\tInternal version increment\\n4.0.23\\n\\tAdding support for several EFR32FGV parts\\n4.0.22\\n\\tInternal version increment\\n4.0.21\\n\\tCorrection to pin hardware property value\\n4.0.20\\n\\tInternal version increment\\n4.0.19\\n\\tInternal version increment\\n4.0.18\\n\\tInclude EFR32xG13 documents\\n4.0.17\\n\\tNew early access parts\\n4.0.16\\n\\tNew part support\\n4.0.15\\n\\tInternal version increment\\n4.0.14\\n\\tCorrection to data references\\n\\tFix RAM size for EFR32xG12 parts\\n4.0.13\\n\\tInternal version increment\\n4.0.12\\n\\tSupport for EFR32xG13 parts\\n4.0.11\\n\\tSupport for EFR32xG12 parts\\n4.0.10\\n\\tInitial support for new parts\\n4.0.9\\n\\tInternal reorganization of data files\\n4.0.6\\n\\tCorrect EFR32BG CSP documentation reference.\\n4.0.5\\n\\tUpdate debug part names for some bg and fg parts\\n4.0.4\\n\\tUse EFR32MG1PxxxF256 as the debug part for EFR32MG2/EFR32MG12 parts because JLinkARM does not have the definitions yet. This allows establishing a debug connection with an EFR32MG2/EFR32MG12 part.\\n4.0.3\\n\\tEarly access boards and parts\\n4.0.2\\n\\tEarly access boards and parts\\n4.0.1\\n\\tNew boards and parts\\n4.0.0\\n\\tInitial release","id":"com.silabs.metadata.efr32.feature.metadata.resource.feature.group","label":"EFR32 Part Support - 4.0.71","desc":"EFR32 Part Metadata"},{"installState":"Install","releaseNotes":"4.0.1\\n\\tNo change to tool resources; version increment to synchronize early access packages\\n\\tPackage version for early_access in SV4.1.13.1\\n4.0.0\\n\\tInitial release","imageUrl":"file:/C:/Users/<USER>/4.12_workspace/.metadata/.plugins/org.eclipse.pde.core/SS%20Platform%20-%20Package-Installer-Enabled/cache/com.silabs.ss.tool.efp.feature.metadata.resource.feature.group/tool_efp_40x40.png","id":"com.silabs.ss.tool.efp.feature.metadata.resource.feature.group","label":"EFP Configuration Tool - 4.0.1","desc":"EFP Configuration Tool"},{"installState":"Install","releaseNotes":"4.0.92\\n\\tAdd data for Thunderboard EFR32BG22\\n\\tPackage version for early_access in  SV4.1.13.1\\n4.0.91\\n\\tPackage version for early_access in  SV4.1.13.0\\n4.0.90\\n\\tAdd data for WFM200 board\\n\\tPackage version for rel in  SV4.1.13.0\\n4.0.89\\n\\tNo metadata changes\\n\\tPackage version for early_access in  SV4.1.11.8\\n4.0.88\\n\\tPackage version for early_access in  SV4.1.11.7\\n4.0.87\\n\\tInclude missing target interface in boards BRD4308A, BRD4308B and BRD4309A\\n\\tPackage version for rel in  SV4.1.11.7\\n4.0.86\\n\\tAdd data for BRD4179A/B radio boards\\n\\tInternal version increment for early access in SV4.1.11.6\\n4.0.85\\n\\tData update to relfect BGM210 and MG210 OPNs\\n\\tPackage version for rel in SV4.1.11.6\\n4.0.84\\n\\tAdd data for BRD4179A/B radio boards\\n4.0.83\\n\\tAdd board data for BRD4308A, BRD4308B and BRD4309A\\n4.0.82\\n\\tPropagate version change\\n4.0.81\\n\\tVarious changes in board names making them more consistent on how they appear on www.silabs.com\\n\\tAdd data for SLWSTK6101D kit and associated boards\\n\\tVarious asset references updated\\n4.0.80\\n\\tAdd board date BRD4182 in Early Access package\\n4.0.79\\n\\tMiscellaneous data updates\\n\\tARTIK module radio boards are no longer supported for creating new projects\\n4.0.78\\n\\tPropagate version change\\n4.0.77\\n\\tAdd board support for BRD4180A and BRD4181A, EFR32xG21 radio boards\\n\\tAdd board support for BRD4321A - WGM160P device\\n4.0.76\\n\\tPropagate version change\\n4.0.75\\n\\tAdd data for an infrastructure property to support device filtering\\n4.0.74\\n\\tPropagate version change\\n4.0.73\\n\\tAdd data for Silicon Labs WF200 Expansion Board and Kit\\n4.0.72\\n\\tPropagate version change\\n\\tCorrection to EFR32MG21 board information\\n4.0.71\\n\\tAdded board and kit data for Thunderboard EFM32GG12\\n4.0.70\\n\\tAdd EFR32MG21 2.4 GHz 10 dBm Radio Board (BRD41741A)\\n4.0.69\\n\\tPropagate version change\\n4.0.68\\n\\tUpdate metadata following improvements to assets sourcing\\n4.0.67\\n\\tPropagate version change\\n4.0.66\\n\\tAdd data for Silicon Labs LTE-M Expansion Board and Kit\\n4.0.65\\n\\tPropagate version change\\n4.0.64\\n\\tData updates for Gecko SDK Suite v2.4 release:\\n\\t- Better support for module devices\\n\\t- Support for BGX modules\\n4.0.63\\n\\tPropagate version change\\n4.0.62\\n\\tPropagate version change\\n4.0.61\\n\\tUpdate references to board BRD5200\\n4.0.60\\n\\tAdd data for BRD4171A\\n4.0.59\\n\\tAdd data for BRD4305A, BRD4305C, BRD4305D, and BRD4305E\\n\\tAdd data for BRD4172A, BRD4172B, BRD4173A, BRD4174A, BRD4174B, and BRD4175A\\n4.0.58\\n\\tPropagate version change\\n4.0.57\\n\\tPropagate version change\\n4.0.56\\n\\tAdd BRD4170A board data\\n\\tAdd board data for BRD4105A, BRD4169A, BRD4169B\\n4.0.55\\n\\tPropagate version change\\n4.0.54\\n\\tAdd support for EFM32TG11 boards\\n4.0.53\\n\\tPropagate version change\\n4.0.52\\n\\tAdd support for boards BRD4306A, BRD4306B, BRD4306C, and BRD4306D\\n4.0.51\\n\\tPropagate version change\\n4.0.50\\n\\tFix reference to Thundboard UB3 document\\n4.0.49\\n\\tAdd Isolated CAN Expansion Board data\\n\\tAdd Thunderboard UB3 data\\n4.0.48\\n\\tPropagate version change\\n4.0.47\\n\\tFix missing reference to kit firmware asset for Thunderboard Sense 2\\n4.0.46\\n\\tAdd controlled data for EFR32MG14 and EFR32BG14 board\\n4.0.45\\n\\tAdd data for Thunderboard Sense 2 board\\n\\tAdd data for EFR32FG14 boards and kits\\n4.0.44\\n\\tPropagate version change\\n4.0.43\\n\\tPropagate version change\\n4.0.42\\n\\tPropagate version change\\n4.0.41\\n\\tAdd EFM8BB3 USB Type-C Charger Ref Design kit and board data\\n4.0.40\\n\\tPropagate version change\\n4.0.39\\n\\tPropagate version change\\n4.0.38\\n\\tAdd BRD4306 variants A, B, C, and D radio boards (early access)\\n4.0.37\\n\\tAdd USB and RS232/RS485 isolation expansion cards definitions\\n4.0.36\\n\\tPropagate version change\\n4.0.35\\n\\tAdd suupport for EFM32GG11 kit ********** and board BRD2204A\\n4.0.34\\n\\tPropagate version change\\n4.0.33\\n\\tAdd support for Si72xx Hall Effect Sensor\\n4.0.32\\n\\tPropagate version change\\n4.0.31\\n\\tAdd doc references to CPT007B and CPT112S\\n\\tAdd support for CPT212 and CPT213\\n\\tCorrect BRD4168A name\\n4.0.30\\n\\tPropagate version change\\n4.0.29\\n\\tPropagate version change\\n4.0.28\\n\\tPropagate version change\\n4.0.27\\n\\tNew boards support\\n4.0.26\\n\\tAdded SLWSTK6020B, BRD4104A-A00, BRD4167A-A00 and BRD4168A-A00 to public\\n4.0.25\\n\\tCorrection to BRD4101 document references\\n4.0.24\\n\\tSupport for EFx32xG13 boards\\n4.0.23\\n\\tSupport for EFx32xG12 boards\\n4.0.22\\n\\tAddition of new boards\\n4.0.21\\n\\tInternal reorganization of data files\\n\\tAdd property to some wireless boards.\\n\\tRemove incorrect compatibility setting for BRD4001.\\n\\tRemove duplicate BRD4160A definition.\\n4.0.19\\n\\tLaunch of EFR32BG WLCSP package\\n4.0.17\\n\\tMoving IoT Solutions to separate location\\n4.0.15\\n\\tPropagate version change\\n4.0.14\\n\\tAdded BGM121\\n4.0.12\\n\\tLatest boards and kits\\n4.0.10\\n\\tAdded ARTIK to public boards and kits\\n4.0.9\\n\\tAdded MGM-111\\n\\tAdded Thunderboard Sense\\n4.0.8\\n\\tPropagate version change\\n4.0.5\\n\\tCorrection to boards and kits list\\n4.0.4\\n\\tArtik early access of boards and kits list\\n4.0.3\\n\\tEarly access of boards and kits list\\n4.0.2\\n\\tCorrection to boards and kits list\\n4.0.1\\n\\tFixes to asset references on various wireless parts\\n4.0.0\\n\\tInitial release","id":"com.silabs.metadata.hwtools.feature.metadata.resource.feature.group","label":"Hardware Tools Board Support - 4.0.92","desc":"Hardware Tools Board Metadata"},{"installState":"Install","releaseNotes":"4.1.11\\n\\tPackage version for early_access in SV4.1.13.1\\n4.1.10\\n\\tRemove duplicate board definition for BRD8010 - STK Debug Adapter Board\\n\\tPackage version for rel in SV4.1.13.1\\n4.1.9\\n\\tNo metadata changes\\n\\tPackage version for early_access in SV4.1.13.0\\n4.1.8\\n\\tNo metadata changes\\n\\tPackage version for early_access in SV4.1.11.8\\n4.1.7\\n\\tPackage version for early_access in SV4.1.11.7\\n4.1.6\\n\\tPackage version for rel in SV4.1.11.7\\n4.1.5\\n\\tPackage version for early access in SV4.1.11.6\\n4.1.4\\n\\tData updates with respect to module OPN changes.\\n\\tPackage version for rel in SV4.1.11.6\\n4.1.3\\n\\tPackage version for early access in SV4.1.11.4\\n4.1.2\\n\\tAdd BRD4201B, EFR32ZG13P531F512GM32 and BRD4203A, EFR32ZG13P531F512GM48 to Early Access package\\n4.1.1\\n\\tAsset version and source location updates\\n\\tPackage version for early access in SV4.1.11.2\\n4.1.0\\n\\tUpdates for the product launch\\n4.0.4\\n   Updated SLWSTK6050A kit to reference BRD4202\\n4.0.3\\n\\tAdded BRD4202A module\\n4.0.2\\n\\tUpdate BRD4200A module reference\\n4.0.1\\n\\tUpdate board names and BRD4200A module reference\\n4.0.0\\n\\tInitial release","id":"com.silabs.metadata.zwave.feature.metadata.resource.feature.group","label":"Z-Wave Board Support - 4.1.11","desc":"Z-Wave Board Metadata"},{"installState":"Install","releaseNotes":"4.0.1\\n\\tNo metadata changes\\n\\tPackage version for early_access in SV4.1.13.1\\n4.0.0\\n\\tInitial release","id":"com.silabs.metadata.efp.feature.metadata.resource.feature.group","label":"Energy Friendly Parts - 4.0.1","desc":"Energy Friendly Parts metadata"}]},{"installState":"Install","installEnabled":true,"imageUrl":"icons/pm_temp/sdk/icon_stack.png","category":"EFM8 / C8051 8-bit Products Early Access - 4.0.37","packages":[{"installState":"Install","releaseNotes":"4.0.92\\n\\tAdd data for Thunderboard EFR32BG22\\n\\tPackage version for early_access in  SV4.1.13.1\\n4.0.91\\n\\tPackage version for early_access in  SV4.1.13.0\\n4.0.90\\n\\tAdd data for WFM200 board\\n\\tPackage version for rel in  SV4.1.13.0\\n4.0.89\\n\\tNo metadata changes\\n\\tPackage version for early_access in  SV4.1.11.8\\n4.0.88\\n\\tPackage version for early_access in  SV4.1.11.7\\n4.0.87\\n\\tInclude missing target interface in boards BRD4308A, BRD4308B and BRD4309A\\n\\tPackage version for rel in  SV4.1.11.7\\n4.0.86\\n\\tAdd data for BRD4179A/B radio boards\\n\\tInternal version increment for early access in SV4.1.11.6\\n4.0.85\\n\\tData update to relfect BGM210 and MG210 OPNs\\n\\tPackage version for rel in SV4.1.11.6\\n4.0.84\\n\\tAdd data for BRD4179A/B radio boards\\n4.0.83\\n\\tAdd board data for BRD4308A, BRD4308B and BRD4309A\\n4.0.82\\n\\tPropagate version change\\n4.0.81\\n\\tVarious changes in board names making them more consistent on how they appear on www.silabs.com\\n\\tAdd data for SLWSTK6101D kit and associated boards\\n\\tVarious asset references updated\\n4.0.80\\n\\tAdd board date BRD4182 in Early Access package\\n4.0.79\\n\\tMiscellaneous data updates\\n\\tARTIK module radio boards are no longer supported for creating new projects\\n4.0.78\\n\\tPropagate version change\\n4.0.77\\n\\tAdd board support for BRD4180A and BRD4181A, EFR32xG21 radio boards\\n\\tAdd board support for BRD4321A - WGM160P device\\n4.0.76\\n\\tPropagate version change\\n4.0.75\\n\\tAdd data for an infrastructure property to support device filtering\\n4.0.74\\n\\tPropagate version change\\n4.0.73\\n\\tAdd data for Silicon Labs WF200 Expansion Board and Kit\\n4.0.72\\n\\tPropagate version change\\n\\tCorrection to EFR32MG21 board information\\n4.0.71\\n\\tAdded board and kit data for Thunderboard EFM32GG12\\n4.0.70\\n\\tAdd EFR32MG21 2.4 GHz 10 dBm Radio Board (BRD41741A)\\n4.0.69\\n\\tPropagate version change\\n4.0.68\\n\\tUpdate metadata following improvements to assets sourcing\\n4.0.67\\n\\tPropagate version change\\n4.0.66\\n\\tAdd data for Silicon Labs LTE-M Expansion Board and Kit\\n4.0.65\\n\\tPropagate version change\\n4.0.64\\n\\tData updates for Gecko SDK Suite v2.4 release:\\n\\t- Better support for module devices\\n\\t- Support for BGX modules\\n4.0.63\\n\\tPropagate version change\\n4.0.62\\n\\tPropagate version change\\n4.0.61\\n\\tUpdate references to board BRD5200\\n4.0.60\\n\\tAdd data for BRD4171A\\n4.0.59\\n\\tAdd data for BRD4305A, BRD4305C, BRD4305D, and BRD4305E\\n\\tAdd data for BRD4172A, BRD4172B, BRD4173A, BRD4174A, BRD4174B, and BRD4175A\\n4.0.58\\n\\tPropagate version change\\n4.0.57\\n\\tPropagate version change\\n4.0.56\\n\\tAdd BRD4170A board data\\n\\tAdd board data for BRD4105A, BRD4169A, BRD4169B\\n4.0.55\\n\\tPropagate version change\\n4.0.54\\n\\tAdd support for EFM32TG11 boards\\n4.0.53\\n\\tPropagate version change\\n4.0.52\\n\\tAdd support for boards BRD4306A, BRD4306B, BRD4306C, and BRD4306D\\n4.0.51\\n\\tPropagate version change\\n4.0.50\\n\\tFix reference to Thundboard UB3 document\\n4.0.49\\n\\tAdd Isolated CAN Expansion Board data\\n\\tAdd Thunderboard UB3 data\\n4.0.48\\n\\tPropagate version change\\n4.0.47\\n\\tFix missing reference to kit firmware asset for Thunderboard Sense 2\\n4.0.46\\n\\tAdd controlled data for EFR32MG14 and EFR32BG14 board\\n4.0.45\\n\\tAdd data for Thunderboard Sense 2 board\\n\\tAdd data for EFR32FG14 boards and kits\\n4.0.44\\n\\tPropagate version change\\n4.0.43\\n\\tPropagate version change\\n4.0.42\\n\\tPropagate version change\\n4.0.41\\n\\tAdd EFM8BB3 USB Type-C Charger Ref Design kit and board data\\n4.0.40\\n\\tPropagate version change\\n4.0.39\\n\\tPropagate version change\\n4.0.38\\n\\tAdd BRD4306 variants A, B, C, and D radio boards (early access)\\n4.0.37\\n\\tAdd USB and RS232/RS485 isolation expansion cards definitions\\n4.0.36\\n\\tPropagate version change\\n4.0.35\\n\\tAdd suupport for EFM32GG11 kit ********** and board BRD2204A\\n4.0.34\\n\\tPropagate version change\\n4.0.33\\n\\tAdd support for Si72xx Hall Effect Sensor\\n4.0.32\\n\\tPropagate version change\\n4.0.31\\n\\tAdd doc references to CPT007B and CPT112S\\n\\tAdd support for CPT212 and CPT213\\n\\tCorrect BRD4168A name\\n4.0.30\\n\\tPropagate version change\\n4.0.29\\n\\tPropagate version change\\n4.0.28\\n\\tPropagate version change\\n4.0.27\\n\\tNew boards support\\n4.0.26\\n\\tAdded SLWSTK6020B, BRD4104A-A00, BRD4167A-A00 and BRD4168A-A00 to public\\n4.0.25\\n\\tCorrection to BRD4101 document references\\n4.0.24\\n\\tSupport for EFx32xG13 boards\\n4.0.23\\n\\tSupport for EFx32xG12 boards\\n4.0.22\\n\\tAddition of new boards\\n4.0.21\\n\\tInternal reorganization of data files\\n\\tAdd property to some wireless boards.\\n\\tRemove incorrect compatibility setting for BRD4001.\\n\\tRemove duplicate BRD4160A definition.\\n4.0.19\\n\\tLaunch of EFR32BG WLCSP package\\n4.0.17\\n\\tMoving IoT Solutions to separate location\\n4.0.15\\n\\tPropagate version change\\n4.0.14\\n\\tAdded BGM121\\n4.0.12\\n\\tLatest boards and kits\\n4.0.10\\n\\tAdded ARTIK to public boards and kits\\n4.0.9\\n\\tAdded MGM-111\\n\\tAdded Thunderboard Sense\\n4.0.8\\n\\tPropagate version change\\n4.0.5\\n\\tCorrection to boards and kits list\\n4.0.4\\n\\tArtik early access of boards and kits list\\n4.0.3\\n\\tEarly access of boards and kits list\\n4.0.2\\n\\tCorrection to boards and kits list\\n4.0.1\\n\\tFixes to asset references on various wireless parts\\n4.0.0\\n\\tInitial release","id":"com.silabs.metadata.hwtools.feature.metadata.resource.feature.group","label":"Hardware Tools Board Support - 4.0.92","desc":"Hardware Tools Board Metadata"},{"installState":"Install","releaseNotes":"4.0.34\\n\\tNo metadata changes\\n\\tPackage version for early access in SV4.1.13.1\\n4.0.33\\n\\tUpdate metadata with correct hardware/rev ID for Si3470A\\n\\tPackage version for early access in SV4.1.11.8\\n4.0.32\\n\\tInternal version increment for early access in SV4.1.11.7\\n4.0.31\\n\\tPackage version for rel in SV4.1.11.7\\n4.0.30\\n\\tInternal version increment for early access in SV4.1.11.6\\n4.0.29\\n\\tInternal version increment for early access in SV4.1.11.4\\n4.0.28\\n\\tInternal version increment for early access in SV4.1.11.2\\n4.0.27\\n\\tInternal version increment for early access in SV4.1.11.0\\n4.0.26\\n\\tPackage version for SV4.1.11.0\\n4.0.25\\n\\tInternal version increment\\n4.0.24\\n\\tInternal version increment\\n4.0.23\\n\\tAdd data for an infrastructure property to support device filtering\\n4.0.22\\n\\tInternal version increment\\n4.0.21\\n\\tInternal version increment\\n4.0.20\\n\\tAdded EFM8BB3 and EFM8LB1 rev C parts data\\n4.0.19\\n\\tInternal version increment\\n4.0.18\\n\\tUpdate metadata to reflect use of documentation sourcing infrastructure\\n4.0.17\\n\\tInternal version increment\\n4.0.16\\n\\tInternal version increment\\n4.0.15\\n\\tData packaging for Studio release\\n4.0.14\\n\\tInternal version increment\\n4.0.13\\n\\tDefinition of Si3470 in Early Access package\\n4.0.12\\n\\tInternal infrastructure change\\n4.0.11\\n\\tFix lockByteAddress for EFM8BB3 and EFM8LB1\\n4.0.10\\n\\tAdd support for EFM8LB1 S1 devices\\n4.0.9\\n\\tCorrection to RAM size metadata for EFM8BB31F16* devices\\n\\tAdding EFM8UB2 rev B parts\\n4.0.8\\n\\tAdded signature for authentication\\n4.0.6\\n\\tDocumentation reference update\\n4.0.5\\n\\tUpdates for SDK changes\\n4.0.1 - 4.0.3\\n\\tearly access for efm8ub3/4\\n4.0.0\\n\\tInitial release","id":"com.silabs.metadata.efm8.feature.metadata.resource.feature.group","label":"EFM8 Part Support - 4.0.34","desc":"EFM8 Part Metadata"}]}]}'),It={SET_EARLYACCESS_PACKAGES(e,t){e.earlyAccessPackages=t},SET_DEFAULTS(e){e.earlyAccessPackages=Tt}},vt={getEarlyAccessPackages({commit:e}){return K.Z.getEarlyAccessPackages().then((t=>e("SET_EARLYACCESS_PACKAGES",t)),e("SET_DEFAULTS"))}},Pt={namespaced:!0,mutations:It,actions:vt,state:bt},yt={sdkPermissions:{}},Rt={SET_SDK_PERMISSIONS(e,t){e.sdkPermissions=t}},kt={getSdkPermissions(e){return K.Z.getSdkPermissions().then((t=>e.commit("SET_SDK_PERMISSIONS",t)))}},Ct={namespaced:!0,mutations:Rt,actions:kt,state:yt},Ot={assets:{},selectedAssets:[],expandedAssets:[],filterByConnectedProduct:!0,loading:!0};function Lt(e){return e.selectedAssets}function Nt(e){return e.expandedAssets}n(6727);const Dt={SET_ASSETS(e,t){e.assets=t},SET_INSTALL_ASSETS(e,t){e.installAssets=t},SET_SELECTED_ASSETS(e,t){e.selectedAssets=t},SET_EXPANDED_ASSETS(e,t){e.expandedAssets=t},SET_SELECTED_FILTER(e,t){e.filterByConnectedProduct=t},SET_LOADING_STATE(e,t){e.loading=t},UPDATE_ASSET_SELECTION(e,t){Ut(e,t[0],t[1],t[2]),t[3]||Ft(e,t[0],t[1],t[2],!0),t[3]||Mt(e,t[0],t[1],t[2])}};function wt(e,t,n){let a=1===t;if(a&&!e.selectedAssets.includes(n.id))e.selectedAssets.push(n.id);else if(!a){let t=e.selectedAssets.indexOf(n.id);t>-1&&e.selectedAssets.splice(t,1)}}function Bt(e,t,n,a,o){if(t){let t=o?n.id:a.id;e.selectedAssets.includes(t)||(!o||o&&-1!==a.checked)&&(n.checkCount=++n.checkCount)}else n.checkCount>0&&(n.checkCount=--n.checkCount)}function Ut(e,t,n,a){t.checked=n;let o=1===n;wt(e,n,t),void 0!==t.children&&t.children.forEach((t=>{if(void 0!==t.parent){let n=a.getNodeByKey(t.parent);void 0!==n&&Bt(e,o,n,t,!1)}Ut(e,t,n,a)}))}function Ft(e,t,n,a,o){let r=1===n;if(void 0!==t.parent){let s=a.getNodeByKey(t.parent);if(void 0!==s){let i=s.checked;o&&Bt(e,r,s,t,!0),_t(s),-1===i&&-1===s.checked||-1===i&&0===s.checked?(console.log("checkCnt calc avoided for parentOf "+s.label),o=!1):o=!0,Ft(e,s,n,a,o)}}}function _t(e){let t,n=Gt(e);t=n?-1:e.checkCount!==e.children.length?0===e.checkCount?0:-1:1,e.checked=t}function Mt(e,t,n,a){if(void 0!==t.parent){let n=a.getNodeByKey(t.parent);void 0!==n&&(wt(e,n.checked,n),Mt(e,n,n.checked,a))}}function Gt(e){if(void 0!==e.children){let n=e.children;for(var t=0;t<n.length;t++)if(-1===n[t].checked)return!0}return!1}const xt={getAssets(e){let t=!e.state.filterByConnectedProduct,n=e.state.loading;if(n)return K.Z.getAssets(t).then((t=>e.commit("SET_ASSETS",t)))},installAssets(e){var t='{ "installAssets": "'+e.state.selectedAssets+'" }';return K.Z.postInstallAssets(t)},updateAll(){var e='{ "updateAll": "true" }';return K.Z.postInstallPackages(e)},setSelectedAssets(e,t){e.commit("SET_SELECTED_ASSETS",t)},setExpandedAssets(e,t){e.commit("SET_EXPANDED_ASSETS",t)},setFilterByConnectedProduct(e,t){e.commit("SET_SELECTED_FILTER",t)},setLoadingState(e,t){e.commit("SET_LOADING_STATE",t)},updateAssetState(e,t){e.commit("UPDATE_ASSET_SELECTION",t)}},Vt={namespaced:!0,state:Ot,getters:h,mutations:Dt,actions:xt};function Ht(){return{}}function Yt(){}const Kt={postCheckUpdates(e,t){let n={notifyCompletion:t};return K.Z.postCheckUpdates(n)},postCheckUpdatesArgs(e,t){return K.Z.postCheckUpdates(t)}},qt={namespaced:!0,mutations:E,actions:Kt,state:Ht};function Wt(){return{socket:null,stateRefreshing:1,stateUpdated:0,availableUpdateCnt:0,checkForUpdatesFailed:0,userStatusChanged:0,message:[{id:0,text:"Empty",value:-1,detail:{}},{id:0,text:"Empty",value:-1,detail:{}}]}}function zt(e,t){return t.message[e].text}function Zt(e,t){return t.message[e].value}function jt(e){return e.stateRefreshing}function Xt(e){return e.checkForUpdatesFailed}function $t(e,t){e.stateRefreshing=t.stateRefresh}function Jt(e,t){console.log("notifications::SOCKET_ONOPEN: "+t)}function Qt(e,t){console.log("notifications::SOCKET_ONCLOSE: "+t)}function en(e,t){console.error("notifications::SOCKET_ONERROR: "+t)}function tn(e,t){e.path!==t&&e.push({path:t})}function nn(e,t){"START"===t.installData.state?tn(e.$router,"/Installing"):"CANCELLED"===t.installData.state?tn(e.$router,"/InstallationCancelled"):"DONE"===t.installData.state&&(1===t.installData.restart?e.commit("install/SET_INSTALLATION_RESTART",!0):e.commit("install/SET_INSTALLATION_RESTART",!1),1===t.installData.result?e.commit("install/SET_INSTALLATION_COMPLETE",!0):(e.commit("install/SET_INSTALLATION_COMPLETE",!1),e.commit("install/SET_INSTALLATION_ERROR",t.installData.message)))}function an(e,t){const n=JSON.parse(t.data);let a=0;n.hasOwnProperty("monitorId")&&(a=n["monitorId"]),e.message[a].id=n.id,e.message[a].text=n.text,e.message[a].value=n.value,void 0!==n.installData?nn(this,n):"stateUpdated"===n.text?e.stateUpdated+=1:"stateRefresh"===n.text?e.stateRefreshing=n.value:"availableUpdateCnt"===n.text?e.availableUpdateCnt=n.value:"checkForUpdatesFailed"===n.text?(e.checkForUpdatesFailed=n.value,0===n.value&&(e.message[a].text="")):"UserStatusChange"===n.text&&0!==n.value&&(e.userStatusChanged=e.userStatusChanged>0?0:1)}function on(e,t){console.error("notifications::SOCKET_RECONNECT: "+t)}function rn(e,t){console.error("notifications::SOCKET_RECONNECT_ERROR: "+t)}function sn(e){e.socket.close()}function cn(e,t){e.socket=t}function ln(e){e.stateUpdated+=1}function dn(e){const t=Ue.newSocket(e,"/installer/notifications");return t}function un(e){e.commit("SOCKET_CLOSE")}function pn({commit:e}){return K.Z.getRefreshState().then((t=>e("SET_REFRESH_STATE",t)))}const gn={namespaced:!0,state:Wt,getters:A,mutations:b,actions:T};function mn(){return{distractions:[]}}var fn=n(3050);const Sn=JSON.parse('[{"id":"distraction1","title":"Bluetooth for Dummies","image":"C:/SiliconLabs/SimplicityStudio/AllMyImages/ProductGroup01.jpg","description":"Read the cool stuff that only dummies don\'t know about bluetooth."},{"id":"distraction2","title":"Silabs for Dummies","image":"C:/SiliconLabs/SimplicityStudio/AllMyImages/ProductGroup01.jpg","description":"Read the cool stuff that only dummies don\'t know about Silabs."},{"id":"distraction3","title":"IoT for Dummies","image":"C:/SiliconLabs/SimplicityStudio/AllMyImages/ProductGroup01.jpg","description":"Read the cool stuff that only dummies don\'t know about Iot."},{"id":"distraction4","title":"Video for Dummies","image":"C:/SiliconLabs/SimplicityStudio/AllMyImages/ProductGroup01.jpg","description":"Watch this cool video that only dummies don\'t know.","video":"https://www.youtube.com/embed/fKSryBr4Y_A"}]');function hn(e,t){e.distractions=t}function En(e){e.distractions=Sn}function An({commit:e}){return K.Z.getDistractions().then((t=>e("set",t)),e("setDefaults"))}const bn={namespaced:!0,state:mn,getters:fn,mutations:I,actions:v};function Tn(){return{access:[]}}function In(e){for(var t=[],n=0;n<e.access.length;n++)1===e.access[n].relevant&&1!==e.access[n].granted&&t.push(e.access[n]);return t}function vn(e){return e.access}const Pn=JSON.parse('[{"imageUrl":"icons/icon_stack.png","link":"","action":"none","permission":"Access Granted","category":"8051","granted":1,"relevant":1},{"imageUrl":"icons/icon_stack.png","link":"","action":"none","permission":"Access Granted","category":"32 bit MCU","granted":1,"relevant":1},{"imageUrl":"icons/icon_stack.png","link":"","action":"login","permission":"Log In","category":"Micrium OS","granted":0,"relevant":1},{"imageUrl":"icons/icon_stack.png","link":"http://www.silabs.com/homekit/","action":"requestaccess","permission":"Request Access","category":"Bluetooth Homekit","granted":0,"relevant":1},{"imageUrl":"icons/icon_stack.png","link":"","action":"login","permission":"Log In","category":"Bluetooth Mesh SDK","granted":0,"relevant":1},{"imageUrl":"icons/icon_stack.png","link":"http://www.silabs.com/products/development-tools/software/bluetooth-smart-software/ble-mesh","action":"requestaccess","permission":"Request Access","category":"Bluetooth Mesh ADK","granted":0,"relevant":1},{"imageUrl":"icons/icon_stack.png","link":"","action":"login","permission":"Log In","category":"Bluetooth","granted":0,"relevant":1},{"imageUrl":"icons/icon_stack.png","link":"","action":"registerkit","permission":"Register Kit","category":"EmberZNet (zigbee)","granted":0,"relevant":1},{"imageUrl":"icons/icon_stack.png","link":"","action":"login","permission":"Log In","category":"Flex","granted":0,"relevant":1},{"imageUrl":"icons/icon_stack.png","link":"","action":"login","permission":"Log In","category":"OpenThread","granted":0,"relevant":1},{"imageUrl":"icons/icon_stack.png","link":"","action":"login","permission":"Log In","category":"Z-Wave","granted":0,"relevant":1},{"imageUrl":"icons/icon_stack.png","link":"https://www.silabs.com/buysample/Pages/contact-sales.aspx","action":"requestaccess","permission":"Request Access","category":"Z-Wave Homekit Bridge","granted":0,"relevant":1},{"imageUrl":"icons/icon_stack.png","link":"","action":"none","permission":"Access Granted","category":"Gecko Platform","granted":1,"relevant":1}]');function yn(e,t){Rn(e,Pn)}function Rn(e,t){e.access=t,e.access.forEach((function(e){e.imageUrl=""+e.imageUrl}))}function kn(e,t){console.log("wizardPackageAccess::onAccessRequestCompleted:  "+t)}function Cn(e,t){console.log("wizardPackageAccess::onAccessRequestError:  "+t)}function On({commit:e},t){return K.Z.wizardGetAccessForSelectedPackages(t).then((t=>e("setAccess",t))).catch((t=>e("setDefaults",t)))}function Ln({commit:e},t){var n='{ "url": "'+t.link+'" }';return K.Z.postRequestAccess(n).then((t=>e("onAccessRequestCompleted",t))).catch((t=>e("onAccessRequestError",t)))}const Nn={namespaced:!0,state:Tn,getters:P,mutations:y,actions:R,methods:{getInstallStartTitle:function(){return"InstallByTechnology"===this.$store.state.installOptions.InstallType||"ManageInstalledPackages"===this.$store.state.installOptions.InstallType?this.$t("SelectTechnologyType"):this.$t("SelectProducts")}}};function Dn(){return{licenses:[],numberAccepted:0,allAccepted:!1}}function wn(e){return e.licenses}const Bn=JSON.parse('[{"title":"First License","id":1,"text":"FIRST \\nEND-USER LICENSE AGREEMENT\\nIMPORTANT:  READ CAREFULLY\\nBEFORE AGREEING TO TERMS\\n\\nTHIS PRODUCT CONTAINS CERTAIN COMPUTER PROGRAMS AND OTHER THIRD PARTY PROPRIETARY MATERIAL (\\"LICENSED PRODUCT\\"), THE USE OF WHICH IS SUBJECT TO THIS END-USER LICENSE AGREEMENT. INDICATING YOUR AGREEMENT CONSTITUTES YOUR AND (IF APPLICABLE) YOUR COMPANY\'S ASSENT TO AND ACCEPTANCE OF THIS END-USER LICENSE AGREEMENT (THE \\"LICENSE\\" OR \\"AGREEMENT\\").  IF YOU DO NOT AGREE WITH ALL OF THE TERMS, YOU MUST NOT USE THIS PRODUCT.  WRITTEN APPROVAL IS NOT A \\nPREREQUISITE TO THE VALIDITY OR ENFORCEABILITY OF THIS AGREEMENT, AND NO SOLICITATION OF SUCH WRITTEN APPROVAL BY OR ON BEHALF OF SILICON LABORATORIES, INC. (\\"SILICON LABS\\") SHALL BE CONSTRUED AS AN INFERENCE TO THE CONTRARY.  IF THESE TERMS ARE CONSIDERED AN OFFER BY SILICON LABS, ACCEPTANCE IS EXPRESSLY LIMITED TO THESE TERMS.\\nLICENSE AND WARRANTY:  The Licensed Product and the embedded Software which is made the subject of this License is either the property of SILICON LABS or a third party from whom SILICON LABS has the authorization to distribute to you subject to the terms of this Agreement.  This Licensed Product is protected by state, federal, and international copyright law. Although SILICON LABS continues to own the Licensed Product and the right to distribute the embedded third party Software, you will have certain rights to use the Licensed Product and the embedded Software after your acceptance of this License. Except as may be modified by a license addendum which accompanies this License, your rights and obligations with respect to the use of this Product and the embedded software are as follows:\\n\\n\\n1.  AS APPROPRIATE WITH RESPECT TO THE LICENSED PRODUCT, YOU MAY: Use, copy, distribute and make derivative works of the Software for any purpose, including commercial applications, subject to the following restrictions: (i) The origin of this software must not be misrepresented; (ii) you must not claim that you wrote the original software; (iii) altered source versions must be plainly marked as such, and must not be misrepresented as being the original software; and (iv) any notices contained in the Software may not be removed or altered, including notices in source code versions.\\n\\n2.  YOU MAY NOT: (A) Sublicense, assign, rent or lease any portion of the Licensed Product or the embedded Software; or (B) Remove any product identification, copyright or other notices that appear on the Licensed Product or embedded Software.\\n\\n3.  Limited Use:  Use of any of the Software is strictly limited to use in systems containing one or more SILICON LABS products when the Software is enabled to be functional.  Any unauthorized use is expressly prohibited and will constitute a breach of this Agreement.\\n\\n4.  Warranty:  SILICON LABS does not warrant that the Licensed Product or embedded Software will meet your requirements or that operation of the Licensed Product will be uninterrupted or that the embedded Software will be error-free.  You agree that the Licensed Product is provided \\"AS IS\\" and that SILICON LABS makes no warranty as to the Licensed Product or embedded Software.  SILICON LABS DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT, RELATED TO THE SOFTWARE, ITS USE OR ANY INABILITY TO USE IT, THE RESULTS OF ITS USE AND THIS AGREEMENT.\\n\\nYOU MAY HAVE OTHER RIGHTS, WHICH VARY FROM STATE TO STATE.\\n\\n5.  Disclaimer of Damages:  IN NO EVENT WILL SILICON LABS BE LIABLE TO YOU FOR ANY SPECIAL, CONSEQUENTIAL, INDIRECT, OR SIMILAR DAMAGES, INCLUDING ANY LOST PROFITS OR LOST DATA ARISING OUT OF THE USE OR INABILITY TO USE THE LICENSED PRODUCT EVEN IF SILICON LABS HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.\\n\\nSOME STATES DO NOT ALLOW THE LIMITATION OR EXCLUSION OF LIABILITY FOR INCIDENTAL OR CONSEQUENTIAL DAMAGES. SO THE ABOVE LIMITATION OR EXCLUSION MAY NOT APPLY TO YOU.\\n\\nIN NO CASE SHALL SILICON LABS\' LIABILITY EXCEED THE PURCHASE PRICE FOR THE LICENSED PRODUCT.  The disclaimers and limitations set forth above will apply regardless of whether you accept the Licensed Software.\\n\\n6.  Term and Termination:  The term of this Agreement and the License granted herein shall begin upon use of the Licensed Product and continue in perpetuity unless you breach any of the obligations set out under this Agreement.  Upon your breach of this Agreement by you, the license granted hereunder shall terminate immediately and you shall cease all use of the Licensed Products and return same as well as any copies of the Licensed Product and/or embedded Software to SILICON LABS immediately.  Termination of this License upon your breach is only one remedy available to SILICON LABS. In addition to termination of this Agreement upon your breach, SILICON LABS shall be entitled to seek any and all other available remedies, at law or at equity, arising from your breach.\\n\\n7.  Export: You shall comply with all applicable federal, provincial, state and local laws, regulations and ordinances including but not limited to applicable U.S. Export Administration Laws and Regulations.  You shall not export or re-export, or allow the export or re-export of the Licensed Product, any component of the Licensed Product, or any copy of the embedded Software in violation of any such restrictions, laws or regulations, or to Cuba, Libya, North Korea, Iran, Iraq, or Rwanda or to any Group D: 1 or E: 2 country (or any national of such country) specified in the then current Supplement No. 1 to Part 740, or, in violation of the embargo provisions in Part 746, of the U.S. Export Administration Regulations (or any successor regulations or supplement), except in compliance with and with all licenses and approvals required under applicable export laws and regulations, including without limitation, those of the U.S. Department of Commerce.\\n\\n8.  General: This Agreement will be governed by the laws of the State of Texas and any applicable federal laws or regulations. The waiver by either Party of any default or breach of this Agreement shall not constitute a waiver of any other or subsequent default or breach. This Agreement constitutes the complete and exclusive statement of the mutual understanding between you and SILICON LABS with respect to this subject matter herein. This Agreement may only be modified by a written addendum, which has been signed by both you and SILICON LABS. Should you have any questions concerning this Agreement, or if you desire to contact SILICON LABS for any reason, please write:\\n\\nSilicon Laboratories, Inc.\\n400 West Cesar Chavez\\nAustin, Texas 78701, U.S.A.","softwareCovered":["First License Software 1","First License Software 2","First License Software 3","First License Software 4"],"accepted":false,"assignedId":0},{"title":"Second License","id":2,"text":"<html><head><style> H1 { font-size: 1.5em;} </style></head><body lang=EN-US><div><h1><b>SIMPLICITY STUDIO TERMS OF USE AND PRIVACY STATEMENT</b></h1><p>Version 20170905</p><p>BY USING SIMPLICITY STUDIO OR BY CLICKING &ldquo;I ACCEPT THE TERMS OF USE&rdquo;, YOU ACCEPT AND AGREE TO BE BOUND AND ABIDE BY THESE TERMS OF USE AND PRIVACY STATEMENT. IF YOU DO NOT WANT TO AGREE TO THESE TERMS OF USE AND PRIVACY STATEMENT, YOU MUST EXIT SIMPLICITY STUDIO AND NOT ACCESS OR USE SIMPLICITY STUDIO.</p> <p>THESE TERMS OF USE COVER SOFTWARE, DOCUMENTATION AND TOOLS OFFERED BY SILICON LABORATORIES INC. (&ldquo;<b>SILICON LABS</b>&rdquo;) AND THIRD PARTIES FROM WHOM SILICON LABS HAS OBTAINED A LICENSE TO DISTRIBUTE (COLLECTIVELY &ldquo;<b>THE LICENSED PRODUCTS</b>&rdquo;). IT APPLIES TO MULTIPLE SOFTWARE TYPES. YOUR RIGHTS AND OBLIGATIONS FOR EACH TYPE OF SOFTWARE ARE STATED BELOW. IT APPLIES TO LICENSED PRODUCTS THAT YOU DONWLOAD NOW OR IN THE FUTURE, INCLUDING ALL UPDATES AND VERSIONS.</p> <p>YOUR USE OF THE LICENSED PRODUCT IS SUBJECT TO THE FOLLOWING TERMS AND CONDITIONS. CLICKING &ldquo;I AGREE&rdquo; OR &ldquo;ACCEPT&rdquo; BELOW INDICATES YOUR (AND IF APPLICABLE, YOUR EMPLOYER&rsquo;S) ACCEPTANCE OF THESE TERMS AND CONDITIONS.  IF YOU DO NOT AGREE WITH ALL OF THESE TERMS, DO NOT USE ALL OR ANY PART OF THE LICENSED PRODUCTS.  </p> <h3><b>Acceptance of the Terms of Use</b></h3> <p>These Terms of Use are between you and Silicon Laboratories Inc., whose principal office is located at 400 W. Cesar Chavez, Austin, TX 78701 and its affiliates (&quot;Silicon Labs&quot;, &quot;we&quot; or &quot;us&quot;). The following terms and conditions (&quot;Terms of Use&quot;), govern your access to and use of Simplicity Studio, including any content, functionality and services offered on or through Simplicity Studio.</p> <p>IF YOU ARE AN INDIVIDUAL USING SIMPLICITY STUDIO FOR THE BENEFIT YOUR EMPLOYER, A COMPANY OR ANOTHER LEGAL ENTITY, YOU REPRESENT THAT YOU HAVE THE AUTHORITY TO BIND SUCH ENTITY AND ITS AFFILIATES TO THESE TERMS OF USE, IN WHICH CASE THE TERMS &ldquo;YOU&rdquo; OR &ldquo;YOUR&rdquo; SHALL REFER TO SUCH ENTITY AND ITS AFFILIATES.  IF YOU DO NOT HAVE SUCH AUTHORITY, OR IF YOU DO NOT AGREE WITH THESE TERMS OF USE, YOU MUST NOT ACCEPT THESE TERMS OF USE AND MAY NOT USE SIMPLICITY STUDIO.  YOU REPRESENT THAT YOU ARE OVER THE AGE OF 17 AND WARRANT THAT YOU ARE OF LEGAL AGE TO FORM A BINDING CONTRACT WITH SILICON LABS.</p> <h3><b>Changes to the Terms of Use</b></h3> <p>We may revise and update these Terms of Use from time to time in our sole discretion. All changes are effective immediately when we post them, and apply to all access to and useof Simplicity Studio thereafter.  However, any changes to the dispute resolution provisions set forth in Governing Law and Jurisdiction will not apply to any disputes for which the parties have actual notice prior to the date the change is posted on Simplicity Studio. </p> <p>Your continued use of Simplicity Studio following the posting of revised Terms of Use means that you accept and agree to the changes. You are expected to check this page from time to time so you are aware of any changes, as they are binding on you. </p> <h3><b>SIMPLICITY TOOLS</b></h3> <p>The following terms and conditions apply to the following types of Licensed Products available within the Simplicity Studio development environment including, but not limited to, Energy Profiler, Network Analyzer, AppBuilder, Xpress Configurator and Hardware Configurator (&ldquo;<b>Simplicity Tools</b>&rdquo;):</p> <ul> <li> You may use Simplicity Tools to assist you in your development of Authorized Applications.</li> <li> You may make multiple copies of Simplicity Tools on computers within your possession or control.</li> <li> You may not sell, lease, sublicense, distribute or transfer Simplicity Tools to any third party except to an Authorized Subcontractor.</li> <li> You may not modify Simplicity Tools except as necessary for their intended use.</li> <li> Silicon Labs does not warrant that the Simplicity Tools will meet your requirements or that operation of the Simplicity Tools will be uninterrupted or that any software will be error-free.  You agree that the Simplicity Tools are provided &quot;AS IS&quot; and that Silicon Labs makes no warranties as to the Simplicity Tools. SILICON LABS DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT, RELATED TO THE SIMPLICITY TOOLS AND ANY USE, INABILITY TO USE, OR RESULTS FROM USING THE SIMPLICITY TOOLS. </li> <li>Silicon Labs is not obligated to provide any technical support to you.  To the extent that Silicon Labs provides any technical support to you, such support is provided &ldquo;as-is&rdquo; without any warranties.  Technical support is not a substitute for your own obligation to test your products.  Silicon Labs is not obligated to sell any Silicon Labs devices or modules to you.  Such sales, if any, shall be governed by Silicon Labs Terms and Conditions of Sale or by the terms and conditions applicable between you and the Silicon Labs distributor from whom you purchased the products.</li> </ul> <h3><b>SIMPLICITY STUDIO</b></h3> <p>The following terms apply to your use of Simplicity Studio:</p> <h3><b>Accessing Simplicity Studio and Account Security</b></h3> <p>We reserve the right to withdraw or amend Simplicity Studio, and any service or material we provide on Simplicity Studio, in our sole discretion without notice. We will not be liable if for any reason all or any part of Simplicity Studio is unavailable at any time or for any period. From time to time, we may restrict access to some parts of Simplicity Studio, to users, including registered users.</p> <p>To access Simplicity Studio or some of the resources it offers, you may be asked to provide certain registration details or other information. It is a condition of your use of Simplicity Studio that all the information you provide on Simplicity Studio is correct, current and complete. You agree that all information you provide to register with Simplicity Studio or otherwise, including but not limited to through the use of any interactive features on Simplicity Studio, is governed by our Privacy Statement (below), and you consent to all actions we take with respect to your information consistent with our Privacy Statement.</p> <p>If you choose, or are provided with, a user name, password or any other piece of information as part of our security procedures, you must treat such information as confidential, and you must not disclose it to any other person or entity. You also acknowledge that your account is personal to you and agree not to provide any other person with access to Simplicity Studio or portions of it using your user name, password or other security information. You agree to notify us immediately of any unauthorized access to or use of your user name or password or any other breach of security. You also agree to ensure that you exit from your account at the end of each session. </p> <p>We have the right to disable any user name, password or other identifier, whether chosen by you or provided by us, at any time in our sole discretion for any or no reason, including if, in our opinion, you have violated any provision of these Terms of Use.</p> <h3><b>Electronic Communications</b></h3> <p>Visiting Simplicity Studio or sending us an e-mail is an electronic communication. You consent to receive electronic communications from us through e-mail and website postings. You agree that such electronic communications satisfy any legal requirement that agreements, notices, disclosures, or other communications be in writing.</p> <h3><b>Intellectual Property Rights</b></h3> <p>Simplicity Studio and its entire contents, features and functionality (including but not limited to all information, software, text, displays, images, video and audio, and the design, selection and arrangement thereof), are owned by Silicon Labs, its licensors or other providers of such material and are protected by United States and international copyright, trademark, patent, trade secret and other intellectual property or proprietary rights laws.</p> <p>These Terms of Use permit you to use Simplicity Studio solely to design applications that utilize Silicon Labs&rsquo; software and Silicon Labs&rsquo; integrated circuits and modules systems.  No other commercial use of Simplicity Studio is permitted. You must not reproduce, distribute, modify, create derivative works of, publicly display, publicly perform, republish, download, store or transmit any of the material on Simplicity Studio, except as follows:</p> <ul> <li>Your computer may temporarily store copies of such materials in RAM or physical storage incidental to your accessing and viewing those materials.</li> <li>You may store files that are created by Simplicity Studio for the purpose of developing your applications.</li> <li>You may download multiple copies of Simplicity Studio for the purpose of developing your applications.</li> <li>If we provide applications for download, you may download multiple copies for commercial applications provided you agree to be bound by our end user license agreement for such applications.</li> </ul> <p>You must not:</p> <ul> <li>Modify copies of Simplicity Studio.</li> <li>Use any illustrations, photographs, video or audio sequences or any graphics separately from the accompanying text for your own personal use and without attribution to Silicon Labs.</li> <li>Delete or alter any copyright, trademark or other proprietary rights notices from copies of materials from Simplicity Studio</li> /ul> <p>Except as provided above, you must not access or use for any commercial purposes any part of Simplicity Studio or any services or materials available through Simplicity Studio.</p> </body>","softwareCovered":["Second License Software 1","Second License Software 2","Second License Software 3","Second License Software 4"],"accepted":false,"assignedId":1},{"title":"Third License","id":3,"text":"THIRD \\nEND-USER LICENSE AGREEMENT\\nIMPORTANT:  READ CAREFULLY\\nBEFORE AGREEING TO TERMS\\n\\nTHIS PRODUCT CONTAINS CERTAIN COMPUTER PROGRAMS AND OTHER THIRD PARTY PROPRIETARY MATERIAL (\\"LICENSED PRODUCT\\"), THE USE OF WHICH IS SUBJECT TO THIS END-USER LICENSE AGREEMENT. INDICATING YOUR AGREEMENT CONSTITUTES YOUR AND (IF APPLICABLE) YOUR COMPANY\'S ASSENT TO AND ACCEPTANCE OF THIS END-USER LICENSE AGREEMENT (THE \\"LICENSE\\" OR \\"AGREEMENT\\").  IF YOU DO NOT AGREE WITH ALL OF THE TERMS, YOU MUST NOT USE THIS PRODUCT.  WRITTEN APPROVAL IS NOT A \\nPREREQUISITE TO THE VALIDITY OR ENFORCEABILITY OF THIS AGREEMENT, AND NO SOLICITATION OF SUCH WRITTEN APPROVAL BY OR ON BEHALF OF SILICON LABORATORIES, INC. (\\"SILICON LABS\\") SHALL BE CONSTRUED AS AN INFERENCE TO THE CONTRARY.  IF THESE TERMS ARE CONSIDERED AN OFFER BY SILICON LABS, ACCEPTANCE IS EXPRESSLY LIMITED TO THESE TERMS.\\nLICENSE AND WARRANTY:  The Licensed Product and the embedded Software which is made the subject of this License is either the property of SILICON LABS or a third party from whom SILICON LABS has the authorization to distribute to you subject to the terms of this Agreement.  This Licensed Product is protected by state, federal, and international copyright law. Although SILICON LABS continues to own the Licensed Product and the right to distribute the embedded third party Software, you will have certain rights to use the Licensed Product and the embedded Software after your acceptance of this License. Except as may be modified by a license addendum which accompanies this License, your rights and obligations with respect to the use of this Product and the embedded software are as follows:\\n\\n\\n1.  AS APPROPRIATE WITH RESPECT TO THE LICENSED PRODUCT, YOU MAY: Use, copy, distribute and make derivative works of the Software for any purpose, including commercial applications, subject to the following restrictions: (i) The origin of this software must not be misrepresented; (ii) you must not claim that you wrote the original software; (iii) altered source versions must be plainly marked as such, and must not be misrepresented as being the original software; and (iv) any notices contained in the Software may not be removed or altered, including notices in source code versions.\\n\\n2.  YOU MAY NOT: (A) Sublicense, assign, rent or lease any portion of the Licensed Product or the embedded Software; or (B) Remove any product identification, copyright or other notices that appear on the Licensed Product or embedded Software.\\n\\n3.  Limited Use:  Use of any of the Software is strictly limited to use in systems containing one or more SILICON LABS products when the Software is enabled to be functional.  Any unauthorized use is expressly prohibited and will constitute a breach of this Agreement.\\n\\n4.  Warranty:  SILICON LABS does not warrant that the Licensed Product or embedded Software will meet your requirements or that operation of the Licensed Product will be uninterrupted or that the embedded Software will be error-free.  You agree that the Licensed Product is provided \\"AS IS\\" and that SILICON LABS makes no warranty as to the Licensed Product or embedded Software.  SILICON LABS DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT, RELATED TO THE SOFTWARE, ITS USE OR ANY INABILITY TO USE IT, THE RESULTS OF ITS USE AND THIS AGREEMENT.\\n\\nYOU MAY HAVE OTHER RIGHTS, WHICH VARY FROM STATE TO STATE.\\n\\n5.  Disclaimer of Damages:  IN NO EVENT WILL SILICON LABS BE LIABLE TO YOU FOR ANY SPECIAL, CONSEQUENTIAL, INDIRECT, OR SIMILAR DAMAGES, INCLUDING ANY LOST PROFITS OR LOST DATA ARISING OUT OF THE USE OR INABILITY TO USE THE LICENSED PRODUCT EVEN IF SILICON LABS HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.\\n\\nSOME STATES DO NOT ALLOW THE LIMITATION OR EXCLUSION OF LIABILITY FOR INCIDENTAL OR CONSEQUENTIAL DAMAGES. SO THE ABOVE LIMITATION OR EXCLUSION MAY NOT APPLY TO YOU.\\n\\nIN NO CASE SHALL SILICON LABS\' LIABILITY EXCEED THE PURCHASE PRICE FOR THE LICENSED PRODUCT.  The disclaimers and limitations set forth above will apply regardless of whether you accept the Licensed Software.\\n\\n6.  Term and Termination:  The term of this Agreement and the License granted herein shall begin upon use of the Licensed Product and continue in perpetuity unless you breach any of the obligations set out under this Agreement.  Upon your breach of this Agreement by you, the license granted hereunder shall terminate immediately and you shall cease all use of the Licensed Products and return same as well as any copies of the Licensed Product and/or embedded Software to SILICON LABS immediately.  Termination of this License upon your breach is only one remedy available to SILICON LABS. In addition to termination of this Agreement upon your breach, SILICON LABS shall be entitled to seek any and all other available remedies, at law or at equity, arising from your breach.\\n\\n7.  Export: You shall comply with all applicable federal, provincial, state and local laws, regulations and ordinances including but not limited to applicable U.S. Export Administration Laws and Regulations.  You shall not export or re-export, or allow the export or re-export of the Licensed Product, any component of the Licensed Product, or any copy of the embedded Software in violation of any such restrictions, laws or regulations, or to Cuba, Libya, North Korea, Iran, Iraq, or Rwanda or to any Group D: 1 or E: 2 country (or any national of such country) specified in the then current Supplement No. 1 to Part 740, or, in violation of the embargo provisions in Part 746, of the U.S. Export Administration Regulations (or any successor regulations or supplement), except in compliance with and with all licenses and approvals required under applicable export laws and regulations, including without limitation, those of the U.S. Department of Commerce.\\n\\n8.  General: This Agreement will be governed by the laws of the State of Texas and any applicable federal laws or regulations. The waiver by either Party of any default or breach of this Agreement shall not constitute a waiver of any other or subsequent default or breach. This Agreement constitutes the complete and exclusive statement of the mutual understanding between you and SILICON LABS with respect to this subject matter herein. This Agreement may only be modified by a written addendum, which has been signed by both you and SILICON LABS. Should you have any questions concerning this Agreement, or if you desire to contact SILICON LABS for any reason, please write:\\n\\nSilicon Laboratories, Inc.\\n400 West Cesar Chavez\\nAustin, Texas 78701, U.S.A.","softwareCovered":["Third License Software 1","Third License Software 2","Third License Software 3","Third License Software 4"],"accepted":false,"assignedId":2}]');function Un(e){Fn(e,Bn)}function Fn(e,t){e.licenses=t;for(var n=0;n<e.licenses.length;n++)e.licenses[n].accepted=!1,e.licenses[n].assignedId=n;_n(e)}function _n(e){for(var t=0,n=0;n<e.licenses.length;n++)!0===e.licenses[n].accepted&&(t+=1);e.numberAccepted=t,e.numberAccepted===e.licenses.length?e.allAccepted=!0:e.allAccepted=!1}function Mn(e,t){for(var n=0;n<e.licenses.length;n++)e.licenses[n].assignedId===t.assignedId&&(!1===e.licenses[n].accepted?e.licenses[n].accepted=!0:e.licenses[n].accepted=!1);_n(e)}function Gn(e,t){let n=!t.accepted;e.licenses.forEach((function(e){e.accepted=n})),_n(e)}function xn(e){e.licenses.forEach((function(e){!0===e.accepted&&K.Z.postLicenseAcceptanceStatus(e.id,!0).catch((function(){}))}))}function Vn({commit:e},t){return K.Z.wizardGetLicensesSelectedPackages(t).then((t=>e("setLicenses",t))).catch((()=>e("setDefaultLicenses")))}function Hn({commit:e},t){return K.Z.getLicensesForPackageManagerPackages(t).then((t=>e("setLicenses",t))).catch((()=>e("setDefaultLicenses")))}function Yn({commit:e},t){return K.Z.getLicensesForMultiplePackages(t).then((t=>e("setLicenses",t))).catch((()=>e("setDefaultLicenses")))}function Kn({commit:e}){return K.Z.getLicensesToResolve().then((t=>e("setLicenses",t))).catch((()=>e("setDefaultLicenses")))}function qn({commit:e}){return K.Z.getAcceptedLicenses().then((t=>e("setLicenses",t))).catch((()=>e("setDefaultLicenses")))}const Wn={namespaced:!0,state:Dn,getters:k,mutations:C,actions:O};function zn(){return{selectedFeatureId:"",selectedFeatureIds:[],selectedCategories:[],selectedSdkInstallConfig:null,wizardSelectedPackagesQuery:"",installOrigin:"wizard",installationComplete:!1,installationSuccessful:!1,installOperation:"Install",installErrorMessage:"",pmSourcePath:"",restartRequired:!1,browseLocation:"",verifyLocation:{location:"",isValid:!0,message:""}}}function Zn(e){return e.selectedFeatureId}function jn(e){return e.selectedFeatureIds}function Xn(e){return e.selectedSdkInstallConfig}function $n(e){return e.selectedCategories}function Jn(e){return e.wizardSelectedPackagesQuery}function Qn(e){return e.restartRequired}function ea(e){return"wizard"===e.installOrigin}function ta(e){return e.installErrorMessage}function na(e){return e.pmSourcePath}function aa(e,{featureId:t,categories:n}){e.selectedFeatureId=t,e.selectedFeatureIds=[],e.selectedCategories=n,e.selectedSdkInstallConfig=null,e.installOrigin="packageManager"}function oa(e,{featureIds:t,categories:n}){e.selectedFeatureId="",e.selectedFeatureIds=t,e.selectedCategories=n,e.selectedSdkInstallConfig=null,e.installOrigin="packageManager"}function ra(e,{id:t,version:n,location:a,extensions:o}){e.selectedFeatureId="",e.selectedFeatureIds=[],e.selectedCategories=[],e.selectedSdkInstallConfig={packages:[{id:t,version:n,extensions:o}]},a&&(e.selectedSdkInstallConfig.packages[0].location=a),e.installOrigin="packageManager"}function sa(e,t){e.wizardSelectedPackagesQuery=t,e.installOrigin="wizard"}function ia(e,t){e.restartRequired=t}function ca(e,t){e.installationComplete=!0,e.installationSuccessful=t}function la(e,t){e.installOperation=t,e.installationComplete=!1,e.installationSuccessful=!1}function da(e,t){e.pmSourcePath=t}function ua(e,t){e.installErrorMessage=t}function pa(e,t){e.browseLocation=t.browseLocation}function ga(e,t){e.verifyLocation=t}const ma={postInstallSDK(e,t){let n="/rest/installer/sdks/install";return new Promise(((e,a)=>K.Z.post(n,t).then((t=>{e(t)})).catch((e=>{a(e)}))))},postInstallExtension(e,t){e.commit("SET_INSTALL_OPERATION","Install");let n="/rest/installer/sdks/extensioninstall";return K.Z.post(n,t)},postBrowseLocation(e,t){let n="/rest/studio/ui/services/browse";return new Promise(((a,o)=>K.Z.post(n,t).then((t=>{e.commit("SET_BROWSE_LOCATION",t.data),a(t)})).catch((e=>{o(e)}))))},postVerifyInstallLocation(e,t){let n="/rest/sdk/verifyinstalllocation";return new Promise(((a,o)=>K.Z.post(n,t).then((t=>{e.commit("SET_VERIFY_LOCATION",t.data),a(t)})).catch((e=>{o(e)}))))},postSetInstallLocation(e,t){let n="/rest/installer/sdks/location";return new Promise(((a,o)=>K.Z.post(n,t).then((n=>{e.commit("wizardPackages/setPackageLocationById",t,{root:!0}),a(n)})).catch((e=>{o(e)}))))},postUnInstallPackages(e,t){const n=this.$router.currentRoute.value.path;e.commit("SET_PM_ROUTE_PATH",n),e.commit("SET_INSTALL_OPERATION","UnInstall");let a="/rest/installer/install/";return console.log(t),new Promise(((e,n)=>K.Z.post(a,t).then((t=>{e(t)})).catch((e=>{n(e)}))))},deleteSDK(e,t){const n=this.$router.currentRoute.value.path;e.commit("SET_PM_ROUTE_PATH",n),e.commit("SET_INSTALL_OPERATION","UnInstall");let a="/rest/installer/sdks/uninstall";return new Promise(((e,n)=>K.Z["delete"](a,t).then((t=>{e(t)})).catch((e=>{n(e)}))))},deleteExtension(e,t){const n=this.$router.currentRoute.value.path;e.commit("SET_PM_ROUTE_PATH",n),e.commit("SET_INSTALL_OPERATION","UnInstall");let a="/rest/installer/sdks/extensionuninstall";return K.Z["delete"](a,t)}},fa={namespaced:!0,state:zn,getters:L,mutations:N,actions:ma};function Sa(){const e=(0,D.MT)({modules:{user:$,installOptions:re,technologyTypes:Ae,wizardProducts:Me,wizardPackages:tt,toolPackages:st,toolChainPackages:dt,sdkPackages:ft,updatePackages:At,earlyAccessPackages:Pt,sdkPermissions:Ct,assets:Vt,checkUpdates:qt,notifications:gn,distractions:bn,wizardPackageAccess:Nn,licenses:Wn,install:fa},strict:!1});return e}},5020:()=>{},3050:()=>{}},t={};function n(a){var o=t[a];if(void 0!==o)return o.exports;var r=t[a]={exports:{}};return e[a](r,r.exports,n),r.exports}n.m=e,(()=>{var e=[];n.O=(t,a,o,r)=>{if(!a){var s=1/0;for(d=0;d<e.length;d++){for(var[a,o,r]=e[d],i=!0,c=0;c<a.length;c++)(!1&r||s>=r)&&Object.keys(n.O).every((e=>n.O[e](a[c])))?a.splice(c--,1):(i=!1,r<s&&(s=r));if(i){e.splice(d--,1);var l=o();void 0!==l&&(t=l)}}return t}r=r||0;for(var d=e.length;d>0&&e[d-1][2]>r;d--)e[d]=e[d-1];e[d]=[a,o,r]}})(),(()=>{n.n=e=>{var t=e&&e.__esModule?()=>e["default"]:()=>e;return n.d(t,{a:t}),t}})(),(()=>{n.d=(e,t)=>{for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}})(),(()=>{n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((t,a)=>(n.f[a](e,t),t)),[]))})(),(()=>{n.u=e=>"js/"+(64===e?"chunk-common":e)+"."+{21:"f2b208fc",64:"c11f4593",180:"d62041a3",370:"dd9a41c7",396:"7a50b141",442:"459d6881",449:"e5d85f59",459:"c8d9c2f8",490:"13f6760a",496:"a6c0b6b3",606:"1434620a",613:"7113658b",625:"4007530a",636:"9f2da40f",660:"0f1a0b5a",738:"384e23a2",778:"c21494a3",796:"9b8e2636",829:"78c7f45c",838:"f0a07f52",844:"bbef1ccf",861:"7de082f6",966:"26a88312",994:"b58a1607"}[e]+".js"})(),(()=>{n.miniCssF=e=>"css/"+({143:"app",736:"vendor"}[e]||e)+"."+{21:"0d6474bf",143:"37a5edb5",180:"948e1433",370:"72501e25",396:"4c4f64e2",442:"bdbe7e24",459:"6a4456e5",490:"aa78e709",496:"30fe1ed6",625:"30fe1ed6",636:"4323bfc0",660:"6ebcf6e1",736:"32404860",738:"3713aa31",778:"373e04ab",796:"65454318",829:"5549bace",838:"ab2b929b",844:"24b9da6c",966:"18acf37b",994:"6d2b14ec"}[e]+".css"})(),(()=>{n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{var e={},t="installer:";n.l=(a,o,r,s)=>{if(e[a])e[a].push(o);else{var i,c;if(void 0!==r)for(var l=document.getElementsByTagName("script"),d=0;d<l.length;d++){var u=l[d];if(u.getAttribute("src")==a||u.getAttribute("data-webpack")==t+r){i=u;break}}i||(c=!0,i=document.createElement("script"),i.charset="utf-8",i.timeout=120,n.nc&&i.setAttribute("nonce",n.nc),i.setAttribute("data-webpack",t+r),i.src=a),e[a]=[o];var p=(t,n)=>{i.onerror=i.onload=null,clearTimeout(g);var o=e[a];if(delete e[a],i.parentNode&&i.parentNode.removeChild(i),o&&o.forEach((e=>e(n))),t)return t(n)},g=setTimeout(p.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=p.bind(null,i.onerror),i.onload=p.bind(null,i.onload),c&&document.head.appendChild(i)}}})(),(()=>{n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{n.p=""})(),(()=>{var e=(e,t,n,a)=>{var o=document.createElement("link");o.rel="stylesheet",o.type="text/css";var r=r=>{if(o.onerror=o.onload=null,"load"===r.type)n();else{var s=r&&("load"===r.type?"missing":r.type),i=r&&r.target&&r.target.href||t,c=new Error("Loading CSS chunk "+e+" failed.\n("+i+")");c.code="CSS_CHUNK_LOAD_FAILED",c.type=s,c.request=i,o.parentNode.removeChild(o),a(c)}};return o.onerror=o.onload=r,o.href=t,document.head.appendChild(o),o},t=(e,t)=>{for(var n=document.getElementsByTagName("link"),a=0;a<n.length;a++){var o=n[a],r=o.getAttribute("data-href")||o.getAttribute("href");if("stylesheet"===o.rel&&(r===e||r===t))return o}var s=document.getElementsByTagName("style");for(a=0;a<s.length;a++){o=s[a],r=o.getAttribute("data-href");if(r===e||r===t)return o}},a=a=>new Promise(((o,r)=>{var s=n.miniCssF(a),i=n.p+s;if(t(s,i))return o();e(a,i,o,r)})),o={143:0};n.f.miniCss=(e,t)=>{var n={21:1,180:1,370:1,396:1,442:1,459:1,490:1,496:1,625:1,636:1,660:1,738:1,778:1,796:1,829:1,838:1,844:1,966:1,994:1};o[e]?t.push(o[e]):0!==o[e]&&n[e]&&t.push(o[e]=a(e).then((()=>{o[e]=0}),(t=>{throw delete o[e],t})))}})(),(()=>{var e={143:0};n.f.j=(t,a)=>{var o=n.o(e,t)?e[t]:void 0;if(0!==o)if(o)a.push(o[2]);else{var r=new Promise(((n,a)=>o=e[t]=[n,a]));a.push(o[2]=r);var s=n.p+n.u(t),i=new Error,c=a=>{if(n.o(e,t)&&(o=e[t],0!==o&&(e[t]=void 0),o)){var r=a&&("load"===a.type?"missing":a.type),s=a&&a.target&&a.target.src;i.message="Loading chunk "+t+" failed.\n("+r+": "+s+")",i.name="ChunkLoadError",i.type=r,i.request=s,o[1](i)}};n.l(s,c,"chunk-"+t,t)}},n.O.j=t=>0===e[t];var t=(t,a)=>{var o,r,[s,i,c]=a,l=0;if(s.some((t=>0!==e[t]))){for(o in i)n.o(i,o)&&(n.m[o]=i[o]);if(c)var d=c(n)}for(t&&t(a);l<s.length;l++)r=s[l],n.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return n.O(d)},a=globalThis["webpackChunkinstaller"]=globalThis["webpackChunkinstaller"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))})();var a=n.O(void 0,[736],(()=>n(1934)));a=n.O(a)})();