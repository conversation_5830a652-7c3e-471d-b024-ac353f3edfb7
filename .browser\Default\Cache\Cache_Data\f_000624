{"html": "\n   <article>\n    <div class=\"header\">\n     <div class=\"headertitle\">\n      <h1 class=\"title\">\n       WDOG - Watchdog\n      </h1>\n     </div>\n    </div>\n    <div class=\"contents\">\n     <a id=\"details\" name=\"details\">\n     </a>\n     <h2 class=\"groupheader\">\n      Description\n     </h2>\n     <p>\n      Watchdog (WDOG) Peripheral API.\n     </p>\n     <p>\n      This module contains functions to control the WDOG peripheral of Silicon Labs 32-bit MCUs and SoCs. The WDOG resets the system in case of a fault condition.\n     </p>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"nested-classes\">\n          </a>\n          Data Structures\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-w-d-o-g-init-type-def\" target=\"_blank\">\n          WDOG_Init_TypeDef\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Watchdog initialization structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"func-members\">\n          </a>\n          Functions\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gaccf944498d4c40e6d87afa84e06d297b\">\n          WDOGn_Enable\n         </a>\n         (WDOG_TypeDef *wdog, bool enable)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Enable/disable the watchdog timer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga21dd2a92e33eaf3edd48d626cb4ff72b\">\n          WDOGn_Feed\n         </a>\n         (WDOG_TypeDef *wdog)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Feed WDOG.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga3de9748141ca1e172ec40a77b8e86433\">\n          WDOGn_Init\n         </a>\n         (WDOG_TypeDef *wdog, const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-w-d-o-g-init-type-def\" target=\"_blank\">\n          WDOG_Init_TypeDef\n         </a>\n         *init)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Initialize WDOG (assuming the WDOG configuration has not been locked).\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga638259d740de220fb9b34a5b9654e182\">\n          WDOGn_Lock\n         </a>\n         (WDOG_TypeDef *wdog)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Lock the WDOG configuration.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga91988a3a4394fcdea7c04b92b51057f8\">\n          WDOGn_SyncWait\n         </a>\n         (WDOG_TypeDef *wdog)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Wait for the WDOG to complete all synchronization of register changes and commands.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga23bb57a8e1e30b3f72b18e24fbe13e00\">\n          WDOGn_Unlock\n         </a>\n         (WDOG_TypeDef *wdog)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Unlock the WDOG configuration.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga40f4760701c3dc2f95ad2b7f02361414\">\n          WDOGn_IntClear\n         </a>\n         (WDOG_TypeDef *wdog, uint32_t flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Clear one or more pending WDOG interrupts.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga9f5e51b0a9e8914c28930c800d53f45b\">\n          WDOGn_IntDisable\n         </a>\n         (WDOG_TypeDef *wdog, uint32_t flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Disable one or more WDOG interrupts.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gaf99df5cd76ee4ebbb0592a2c38a365f9\">\n          WDOGn_IntEnable\n         </a>\n         (WDOG_TypeDef *wdog, uint32_t flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Enable one or more WDOG interrupts.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga3d949d4e0011948915107c40c812c830\">\n          WDOGn_IntGet\n         </a>\n         (WDOG_TypeDef *wdog)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get pending WDOG interrupt flags.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga6c1dab4d0f9244aca88f089f24b34a9f\">\n          WDOGn_IntGetEnabled\n         </a>\n         (WDOG_TypeDef *wdog)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get enabled and pending WDOG interrupt flags.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gabe0167894e4dd49de8249447f9496d1b\">\n          WDOGn_IntSet\n         </a>\n         (WDOG_TypeDef *wdog, uint32_t flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Set one or more pending WDOG interrupts from SW.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         bool\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga66395f0414769f1bdd9a61b2a169597c\">\n          WDOGn_IsEnabled\n         </a>\n         (WDOG_TypeDef *wdog)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get enabled status of the Watchdog.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         bool\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga87e4ceb5b99420799b44b9d373975de5\">\n          WDOGn_IsLocked\n         </a>\n         (WDOG_TypeDef *wdog)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get locked status of the Watchdog.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         SL_DEPRECATED_API_SDK_4_1 void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga17619d7af7caeb77a64ed43915270ad8\">\n          WDOG_Enable\n         </a>\n         (bool enable)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Enable/disable the Watchdog timer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         SL_DEPRECATED_API_SDK_4_1 void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga3f7c5f400e73cf23516bd167d3709a5f\">\n          WDOG_Feed\n         </a>\n         (void)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Feed the Watchdog.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         SL_DEPRECATED_API_SDK_4_1 void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gaf3eb7923095b3921b16729ae515fc4db\">\n          WDOG_Init\n         </a>\n         (const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-w-d-o-g-init-type-def\" target=\"_blank\">\n          WDOG_Init_TypeDef\n         </a>\n         *init)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Initialize Watchdog (assuming the Watchdog configuration has not been locked).\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         SL_DEPRECATED_API_SDK_4_1 void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gabd8e755528db5ad869faba1a12575ea1\">\n          WDOG_Lock\n         </a>\n         (void)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Lock the Watchdog configuration.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         SL_DEPRECATED_API_SDK_4_1 bool\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gabb401be908c1265b273348c5309965c2\">\n          WDOG_IsEnabled\n         </a>\n         (void)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get enabled status of the Watchdog.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         SL_DEPRECATED_API_SDK_4_1 bool\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gab6dbbdc81c7d3f4e7ddff164b3222e04\">\n          WDOG_IsLocked\n         </a>\n         (void)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get locked status of the Watchdog.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"define-members\">\n          </a>\n          Macros\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga669c6e1fdf53ecbf2e3123e1541513d0\">\n          WDOG_SYNC_TIMEOUT\n         </a>\n         30000\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         In some scenarioes when the watchdog is disabled the synchronization register might be set and not be cleared until the watchdog is enabled again.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gaa8a21262e0b67fbe432eed3edfc246fd\">\n          DEFAULT_WDOG\n         </a>\n         WDOG0\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Default WDOG instance for deprecated functions.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga8169ae345fecb9d7abddeb495db92693\">\n          WDOG_INIT_DEFAULT\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Suggested default configuration for WDOG initialization structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"enum-members\">\n          </a>\n          Enumerations\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gafb32176c669596071269d5fe62734505\">\n          WDOG_PeriodSel_TypeDef\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505a9ed45af9c6fd5b2d8e3f205b448755d1\">\n          wdogPeriod_9\n         </a>\n         = 0x0,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505aa813473bdb90f90ccebcc4220ebaf99b\">\n          wdogPeriod_17\n         </a>\n         = 0x1,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505a13f8ceefd730aea4db809cb5b3a2cafa\">\n          wdogPeriod_33\n         </a>\n         = 0x2,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505ad807cead07d003a35620973c339db111\">\n          wdogPeriod_65\n         </a>\n         = 0x3,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505a31ea334179d9e70ca0f92ea1369c74b3\">\n          wdogPeriod_129\n         </a>\n         = 0x4,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505a272770bd44311a38300bf6d0b92ab50c\">\n          wdogPeriod_257\n         </a>\n         = 0x5,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505a86b24d8f3bf634c9cdbbcc283d3e01e2\">\n          wdogPeriod_513\n         </a>\n         = 0x6,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505a33a208eaf203ae5d837d296d1282249d\">\n          wdogPeriod_1k\n         </a>\n         = 0x7,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505a582f804642864a8c10ee669183fe42b9\">\n          wdogPeriod_2k\n         </a>\n         = 0x8,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505a421552f135d5094c6c84b0a741dc3f30\">\n          wdogPeriod_4k\n         </a>\n         = 0x9,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505a69df19e83f9db479572531d795cedebb\">\n          wdogPeriod_8k\n         </a>\n         = 0xA,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505a9a2fde8ebb666994e72244dab6cbd449\">\n          wdogPeriod_16k\n         </a>\n         = 0xB,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505a78630e83f49b6ab218acbfc86bdaf6c6\">\n          wdogPeriod_32k\n         </a>\n         = 0xC,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505af993d4fb21b4a97af5d406ffdf64ebbb\">\n          wdogPeriod_64k\n         </a>\n         = 0xD,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505ad1fde90472a0e74d6db24fe91149f5ea\">\n          wdogPeriod_128k\n         </a>\n         = 0xE,\n         <br>\n         <a class=\"el\" href=\"#ggafb32176c669596071269d5fe62734505a763da9dc72d140d86dac3be63fcc77a9\">\n          wdogPeriod_256k\n         </a>\n         = 0xF\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Watchdog clock selection.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gaf247334d25d8a47cbd5ececf5d942f6d\">\n          WDOG_WarnSel_TypeDef\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#ggaf247334d25d8a47cbd5ececf5d942f6daaf9206fde6b10b8c9ccd9e7975ab0c25\">\n          wdogWarnDisable\n         </a>\n         = 0,\n         <br>\n         <a class=\"el\" href=\"#ggaf247334d25d8a47cbd5ececf5d942f6dae0a5c84a61755ba833fc2c8980001e02\">\n          wdogWarnTime25pct\n         </a>\n         = 1,\n         <br>\n         <a class=\"el\" href=\"#ggaf247334d25d8a47cbd5ececf5d942f6da5d1171c0925ed0e4fd15fd1e4512b20e\">\n          wdogWarnTime50pct\n         </a>\n         = 2,\n         <br>\n         <a class=\"el\" href=\"#ggaf247334d25d8a47cbd5ececf5d942f6da2d792825a22d64b1b09749001adc59ae\">\n          wdogWarnTime75pct\n         </a>\n         = 3\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Select Watchdog warning timeout period as percentage of timeout.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gaf7f977a90d9fe36572ad083fc1478839\">\n          WDOG_WinSel_TypeDef\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#ggaf7f977a90d9fe36572ad083fc1478839aa978e92b93a8cf01049bc18cc4bc37d5\">\n          wdogIllegalWindowDisable\n         </a>\n         = 0,\n         <br>\n         <a class=\"el\" href=\"#ggaf7f977a90d9fe36572ad083fc1478839aa5957e19b1d8089034e42bd7b95e89af\">\n          wdogIllegalWindowTime12_5pct\n         </a>\n         = 1,\n         <br>\n         <a class=\"el\" href=\"#ggaf7f977a90d9fe36572ad083fc1478839adfebd3f8b5a6441a1ec8ee0cea766cc6\">\n          wdogIllegalWindowTime25_0pct\n         </a>\n         = 2,\n         <br>\n         <a class=\"el\" href=\"#ggaf7f977a90d9fe36572ad083fc1478839aa767ef19bcea8edc1c5b0c1b6b514cb4\">\n          wdogIllegalWindowTime37_5pct\n         </a>\n         = 3,\n         <br>\n         <a class=\"el\" href=\"#ggaf7f977a90d9fe36572ad083fc1478839ae2332f7609208b8facf4e5db38ecd752\">\n          wdogIllegalWindowTime50_0pct\n         </a>\n         = 4,\n         <br>\n         <a class=\"el\" href=\"#ggaf7f977a90d9fe36572ad083fc1478839a532db1c48294dc096364aef1ace8414d\">\n          wdogIllegalWindowTime62_5pct\n         </a>\n         = 5,\n         <br>\n         <a class=\"el\" href=\"#ggaf7f977a90d9fe36572ad083fc1478839aa31ff7398fae9aff59bbfbc061437ac5\">\n          wdogIllegalWindowTime75_0pct\n         </a>\n         = 6,\n         <br>\n         <a class=\"el\" href=\"#ggaf7f977a90d9fe36572ad083fc1478839a099d1dca3fb1cb3b57cb6e01490b84e4\">\n          wdogIllegalWindowTime87_5pct\n         </a>\n         = 7\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Select Watchdog illegal window limit.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <h2 class=\"groupheader\">\n      Function Documentation\n     </h2>\n     <a id=\"gaccf944498d4c40e6d87afa84e06d297b\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaccf944498d4c40e6d87afa84e06d297b\">\n        ◆\n       </a>\n      </span>\n      WDOGn_Enable()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void WDOGn_Enable\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           WDOG_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            wdog,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           bool\n          </td>\n          <td class=\"paramname\">\n           <code>\n            enable\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Enable/disable the watchdog timer.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         This function modifies the WDOG CTRL register which requires synchronization into the low-frequency domain. If this register is modified before a previous update to the same register has completed, this function will stall until the previous synchronization has completed.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             A pointer to the WDOG peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              enable\n             </code>\n            </td>\n            <td>\n             True to enable Watchdog, false to disable. Watchdog cannot be disabled if it's been locked.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga21dd2a92e33eaf3edd48d626cb4ff72b\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga21dd2a92e33eaf3edd48d626cb4ff72b\">\n        ◆\n       </a>\n      </span>\n      WDOGn_Feed()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void WDOGn_Feed\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           WDOG_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            wdog\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Feed WDOG.\n       </p>\n       <p>\n        When WDOG is activated, it must be fed (i.e., clearing the counter) before it reaches the defined timeout period. Otherwise, WDOG will generate a reset.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Note that WDOG is an asynchronous peripheral and when calling the\n         <a class=\"el\" href=\"#ga21dd2a92e33eaf3edd48d626cb4ff72b\" title=\"Feed WDOG.\">\n          WDOGn_Feed()\n         </a>\n         function the hardware starts the process of clearing the counter. This process takes some time before it completes depending on the selected oscillator (up to 4 peripheral clock cycles). When using the ULFRCO for instance as the oscillator the watchdog runs on a 1 kHz clock and a watchdog clear operation might take up to 4 ms.\n        </dd>\n       </dl>\n       <p>\n        If the device enters EM2 or EM3 while a command is in progress then that command will be aborted. An application can use\n        <a class=\"el\" href=\"#ga91988a3a4394fcdea7c04b92b51057f8\">\n         WDOGn_SyncWait()\n        </a>\n        to wait for a command to complete.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             A pointer to the WDOG peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga3de9748141ca1e172ec40a77b8e86433\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga3de9748141ca1e172ec40a77b8e86433\">\n        ◆\n       </a>\n      </span>\n      WDOGn_Init()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void WDOGn_Init\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           WDOG_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            wdog,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-w-d-o-g-init-type-def\" target=\"_blank\">\n            WDOG_Init_TypeDef\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            init\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Initialize WDOG (assuming the WDOG configuration has not been locked).\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         This function modifies the WDOG CTRL register which requires synchronization into the low-frequency domain. If this register is modified before a previous update to the same register has completed, this function will stall until the previous synchronization has completed.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             Pointer to the WDOG peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              init\n             </code>\n            </td>\n            <td>\n             The structure holding the WDOG configuration. A default setting\n             <a class=\"el\" href=\"#ga8169ae345fecb9d7abddeb495db92693\" title=\"Suggested default configuration for WDOG initialization structure.\">\n              WDOG_INIT_DEFAULT\n             </a>\n             is available for initialization.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga638259d740de220fb9b34a5b9654e182\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga638259d740de220fb9b34a5b9654e182\">\n        ◆\n       </a>\n      </span>\n      WDOGn_Lock()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void WDOGn_Lock\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           WDOG_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            wdog\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Lock the WDOG configuration.\n       </p>\n       <p>\n        This prevents errors from overwriting the WDOG configuration, possibly disabling it. Only a reset can unlock the WDOG configuration once locked.\n       </p>\n       <p>\n        If the LFRCO or LFXO clocks are used to clock WDOG, consider using the option of inhibiting those clocks to be disabled. See the\n        <a class=\"el\" href=\"#ga17619d7af7caeb77a64ed43915270ad8\" title=\"Enable/disable the Watchdog timer.\">\n         WDOG_Enable()\n        </a>\n        initialization structure.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         This function modifies the WDOG CTRL register which requires synchronization into the low-frequency domain. If this register is modified before a previous update to the same register has completed, this function will stall until the previous synchronization has completed.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             A pointer to WDOG peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga91988a3a4394fcdea7c04b92b51057f8\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga91988a3a4394fcdea7c04b92b51057f8\">\n        ◆\n       </a>\n      </span>\n      WDOGn_SyncWait()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void WDOGn_SyncWait\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           WDOG_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            wdog\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Wait for the WDOG to complete all synchronization of register changes and commands.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             A pointer to WDOG peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga23bb57a8e1e30b3f72b18e24fbe13e00\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga23bb57a8e1e30b3f72b18e24fbe13e00\">\n        ◆\n       </a>\n      </span>\n      WDOGn_Unlock()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void WDOGn_Unlock\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           WDOG_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            wdog\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Unlock the WDOG configuration.\n       </p>\n       <p>\n        Note that this function will have no effect on devices where a reset is the only way to unlock the watchdog.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             A pointer to WDOG peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga40f4760701c3dc2f95ad2b7f02361414\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga40f4760701c3dc2f95ad2b7f02361414\">\n        ◆\n       </a>\n      </span>\n      WDOGn_IntClear()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void WDOGn_IntClear\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               WDOG_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                wdog,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Clear one or more pending WDOG interrupts.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             Pointer to the WDOG peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              flags\n             </code>\n            </td>\n            <td>\n             WDOG interrupt sources to clear. Use a set of interrupt flags OR-ed together to clear multiple interrupt sources.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga9f5e51b0a9e8914c28930c800d53f45b\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga9f5e51b0a9e8914c28930c800d53f45b\">\n        ◆\n       </a>\n      </span>\n      WDOGn_IntDisable()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void WDOGn_IntDisable\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               WDOG_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                wdog,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Disable one or more WDOG interrupts.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             Pointer to the WDOG peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              flags\n             </code>\n            </td>\n            <td>\n             WDOG interrupt sources to disable. Use a set of interrupt flags OR-ed together to disable multiple interrupt.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaf99df5cd76ee4ebbb0592a2c38a365f9\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf99df5cd76ee4ebbb0592a2c38a365f9\">\n        ◆\n       </a>\n      </span>\n      WDOGn_IntEnable()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void WDOGn_IntEnable\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               WDOG_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                wdog,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Enable one or more WDOG interrupts.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Depending on the use, a pending interrupt may already be set prior to enabling the interrupt. To ignore a pending interrupt, consider using WDOG_IntClear() prior to enabling the interrupt.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             Pointer to the WDOG peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              flags\n             </code>\n            </td>\n            <td>\n             WDOG interrupt sources to enable. Use a set of interrupt flags OR-ed together to set multiple interrupt.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga3d949d4e0011948915107c40c812c830\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga3d949d4e0011948915107c40c812c830\">\n        ◆\n       </a>\n      </span>\n      WDOGn_IntGet()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint32_t WDOGn_IntGet\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               WDOG_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                wdog\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get pending WDOG interrupt flags.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         The event bits are not cleared by the use of this function.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             Pointer to the WDOG peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Pending WDOG interrupt sources. Returns a set of interrupt flags OR-ed together for the interrupt sources set.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga6c1dab4d0f9244aca88f089f24b34a9f\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga6c1dab4d0f9244aca88f089f24b34a9f\">\n        ◆\n       </a>\n      </span>\n      WDOGn_IntGetEnabled()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint32_t WDOGn_IntGetEnabled\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               WDOG_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                wdog\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get enabled and pending WDOG interrupt flags.\n       </p>\n       <p>\n        Useful for handling more interrupt sources in the same interrupt handler.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             Pointer to the WDOG peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Pending and enabled WDOG interrupt sources. Returns a set of interrupt flags OR-ed together for the interrupt sources set.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gabe0167894e4dd49de8249447f9496d1b\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gabe0167894e4dd49de8249447f9496d1b\">\n        ◆\n       </a>\n      </span>\n      WDOGn_IntSet()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void WDOGn_IntSet\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               WDOG_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                wdog,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Set one or more pending WDOG interrupts from SW.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             Pointer to the WDOG peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              flags\n             </code>\n            </td>\n            <td>\n             WDOG interrupt sources to set to pending. Use a set of interrupt flags (WDOG_IFS_nnn).\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga66395f0414769f1bdd9a61b2a169597c\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga66395f0414769f1bdd9a61b2a169597c\">\n        ◆\n       </a>\n      </span>\n      WDOGn_IsEnabled()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               bool WDOGn_IsEnabled\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               WDOG_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                wdog\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get enabled status of the Watchdog.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             Pointer to the WDOG peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         True if Watchdog is enabled.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga87e4ceb5b99420799b44b9d373975de5\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga87e4ceb5b99420799b44b9d373975de5\">\n        ◆\n       </a>\n      </span>\n      WDOGn_IsLocked()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               bool WDOGn_IsLocked\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               WDOG_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                wdog\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get locked status of the Watchdog.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              wdog\n             </code>\n            </td>\n            <td>\n             Pointer to the WDOG peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         True if Watchdog is locked.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga17619d7af7caeb77a64ed43915270ad8\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga17619d7af7caeb77a64ed43915270ad8\">\n        ◆\n       </a>\n      </span>\n      WDOG_Enable()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               SL_DEPRECATED_API_SDK_4_1 void WDOG_Enable\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               bool\n              </td>\n              <td class=\"paramname\">\n               <code>\n                enable\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Enable/disable the Watchdog timer.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              enable\n             </code>\n            </td>\n            <td>\n             Set to true to enable Watchdog, false to disable. Watchdog cannot be disabled if Watchdog has been locked.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga3f7c5f400e73cf23516bd167d3709a5f\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga3f7c5f400e73cf23516bd167d3709a5f\">\n        ◆\n       </a>\n      </span>\n      WDOG_Feed()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               SL_DEPRECATED_API_SDK_4_1 void WDOG_Feed\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               void\n              </td>\n              <td class=\"paramname\">\n               <code>\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Feed the Watchdog.\n       </p>\n      </div>\n     </div>\n     <a id=\"gaf3eb7923095b3921b16729ae515fc4db\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf3eb7923095b3921b16729ae515fc4db\">\n        ◆\n       </a>\n      </span>\n      WDOG_Init()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               SL_DEPRECATED_API_SDK_4_1 void WDOG_Init\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               const\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-w-d-o-g-init-type-def\" target=\"_blank\">\n                WDOG_Init_TypeDef\n               </a>\n               *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                init\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Initialize Watchdog (assuming the Watchdog configuration has not been locked).\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              init\n             </code>\n            </td>\n            <td>\n             Structure holding Watchdog configuration. A default setting\n             <a class=\"el\" href=\"#ga8169ae345fecb9d7abddeb495db92693\" title=\"Suggested default configuration for WDOG initialization structure.\">\n              WDOG_INIT_DEFAULT\n             </a>\n             is available for initialization.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gabd8e755528db5ad869faba1a12575ea1\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gabd8e755528db5ad869faba1a12575ea1\">\n        ◆\n       </a>\n      </span>\n      WDOG_Lock()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               SL_DEPRECATED_API_SDK_4_1 void WDOG_Lock\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               void\n              </td>\n              <td class=\"paramname\">\n               <code>\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Lock the Watchdog configuration.\n       </p>\n      </div>\n     </div>\n     <a id=\"gabb401be908c1265b273348c5309965c2\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gabb401be908c1265b273348c5309965c2\">\n        ◆\n       </a>\n      </span>\n      WDOG_IsEnabled()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               SL_DEPRECATED_API_SDK_4_1 bool WDOG_IsEnabled\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               void\n              </td>\n              <td class=\"paramname\">\n               <code>\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get enabled status of the Watchdog.\n       </p>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         True if Watchdog is enabled.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gab6dbbdc81c7d3f4e7ddff164b3222e04\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gab6dbbdc81c7d3f4e7ddff164b3222e04\">\n        ◆\n       </a>\n      </span>\n      WDOG_IsLocked()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               SL_DEPRECATED_API_SDK_4_1 bool WDOG_IsLocked\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               void\n              </td>\n              <td class=\"paramname\">\n               <code>\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get locked status of the Watchdog.\n       </p>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         True if Watchdog is locked.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <h2 class=\"groupheader\">\n      Macro Definition Documentation\n     </h2>\n     <a id=\"ga669c6e1fdf53ecbf2e3123e1541513d0\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga669c6e1fdf53ecbf2e3123e1541513d0\">\n        ◆\n       </a>\n      </span>\n      WDOG_SYNC_TIMEOUT\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define WDOG_SYNC_TIMEOUT&nbsp;&nbsp;&nbsp;30000\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        In some scenarioes when the watchdog is disabled the synchronization register might be set and not be cleared until the watchdog is enabled again.\n       </p>\n       <p>\n        This will happen when for instance some watchdog register is modified while the watchdog clock is disabled. In these scenarioes we need to make sure that the software does not wait forever.\n       </p>\n      </div>\n     </div>\n     <a id=\"gaa8a21262e0b67fbe432eed3edfc246fd\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaa8a21262e0b67fbe432eed3edfc246fd\">\n        ◆\n       </a>\n      </span>\n      DEFAULT_WDOG\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define DEFAULT_WDOG&nbsp;&nbsp;&nbsp;WDOG0\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Default WDOG instance for deprecated functions.\n       </p>\n      </div>\n     </div>\n     <a id=\"ga8169ae345fecb9d7abddeb495db92693\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga8169ae345fecb9d7abddeb495db92693\">\n        ◆\n       </a>\n      </span>\n      WDOG_INIT_DEFAULT\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define WDOG_INIT_DEFAULT\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <b>\n        Value:\n       </b>\n       <div class=\"fragment\">\n        <div class=\"line\">\n         {                                                                                           \\\n        </div>\n        <div class=\"line\">\n         true,\n         <span class=\"comment\">\n          /* Start Watchdog when initialization is done. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* WDOG is not counting during debug halt. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* The clear bit will clear the WDOG counter. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* WDOG is not counting when in EM2. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* WDOG is not counting when in EM3. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* EM4 can be entered. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* PRS Source 0 missing event will not trigger a WDOG reset. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* PRS Source 1 missing event will not trigger a WDOG reset. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false,\n         <span class=\"comment\">\n          /* Do not lock WDOG configuration. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         wdogPeriod_256k,\n         <span class=\"comment\">\n          /* Set longest possible timeout period. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         wdogWarnDisable,\n         <span class=\"comment\">\n          /* Disable warning interrupt. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         wdogIllegalWindowDisable,\n         <span class=\"comment\">\n          /* Disable illegal window interrupt. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         false\n         <span class=\"comment\">\n          /* Do not disable reset. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         }\n        </div>\n       </div>\n       <p>\n        Suggested default configuration for WDOG initialization structure.\n       </p>\n      </div>\n     </div>\n     <h2 class=\"groupheader\">\n      Enumeration Type Documentation\n     </h2>\n     <a id=\"gafb32176c669596071269d5fe62734505\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gafb32176c669596071269d5fe62734505\">\n        ◆\n       </a>\n      </span>\n      WDOG_PeriodSel_TypeDef\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#gafb32176c669596071269d5fe62734505\">\n            WDOG_PeriodSel_TypeDef\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Watchdog clock selection.\n       </p>\n       <p>\n        Watchdog period selection.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505a9ed45af9c6fd5b2d8e3f205b448755d1\">\n           </a>\n           wdogPeriod_9\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            9 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505aa813473bdb90f90ccebcc4220ebaf99b\">\n           </a>\n           wdogPeriod_17\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            17 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505a13f8ceefd730aea4db809cb5b3a2cafa\">\n           </a>\n           wdogPeriod_33\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            33 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505ad807cead07d003a35620973c339db111\">\n           </a>\n           wdogPeriod_65\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            65 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505a31ea334179d9e70ca0f92ea1369c74b3\">\n           </a>\n           wdogPeriod_129\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            129 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505a272770bd44311a38300bf6d0b92ab50c\">\n           </a>\n           wdogPeriod_257\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            257 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505a86b24d8f3bf634c9cdbbcc283d3e01e2\">\n           </a>\n           wdogPeriod_513\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            513 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505a33a208eaf203ae5d837d296d1282249d\">\n           </a>\n           wdogPeriod_1k\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            1025 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505a582f804642864a8c10ee669183fe42b9\">\n           </a>\n           wdogPeriod_2k\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            2049 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505a421552f135d5094c6c84b0a741dc3f30\">\n           </a>\n           wdogPeriod_4k\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            4097 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505a69df19e83f9db479572531d795cedebb\">\n           </a>\n           wdogPeriod_8k\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            8193 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505a9a2fde8ebb666994e72244dab6cbd449\">\n           </a>\n           wdogPeriod_16k\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            16385 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505a78630e83f49b6ab218acbfc86bdaf6c6\">\n           </a>\n           wdogPeriod_32k\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            32769 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505af993d4fb21b4a97af5d406ffdf64ebbb\">\n           </a>\n           wdogPeriod_64k\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            65537 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505ad1fde90472a0e74d6db24fe91149f5ea\">\n           </a>\n           wdogPeriod_128k\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            131073 clock periods\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggafb32176c669596071269d5fe62734505a763da9dc72d140d86dac3be63fcc77a9\">\n           </a>\n           wdogPeriod_256k\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            262145 clock periods\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"gaf247334d25d8a47cbd5ececf5d942f6d\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf247334d25d8a47cbd5ececf5d942f6d\">\n        ◆\n       </a>\n      </span>\n      WDOG_WarnSel_TypeDef\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#gaf247334d25d8a47cbd5ececf5d942f6d\">\n            WDOG_WarnSel_TypeDef\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Select Watchdog warning timeout period as percentage of timeout.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggaf247334d25d8a47cbd5ececf5d942f6daaf9206fde6b10b8c9ccd9e7975ab0c25\">\n           </a>\n           wdogWarnDisable\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Watchdog warning period is disabled.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggaf247334d25d8a47cbd5ececf5d942f6dae0a5c84a61755ba833fc2c8980001e02\">\n           </a>\n           wdogWarnTime25pct\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Watchdog warning period is 25% of the timeout.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggaf247334d25d8a47cbd5ececf5d942f6da5d1171c0925ed0e4fd15fd1e4512b20e\">\n           </a>\n           wdogWarnTime50pct\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Watchdog warning period is 50% of the timeout.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggaf247334d25d8a47cbd5ececf5d942f6da2d792825a22d64b1b09749001adc59ae\">\n           </a>\n           wdogWarnTime75pct\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Watchdog warning period is 75% of the timeout.\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"gaf7f977a90d9fe36572ad083fc1478839\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf7f977a90d9fe36572ad083fc1478839\">\n        ◆\n       </a>\n      </span>\n      WDOG_WinSel_TypeDef\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#gaf7f977a90d9fe36572ad083fc1478839\">\n            WDOG_WinSel_TypeDef\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Select Watchdog illegal window limit.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggaf7f977a90d9fe36572ad083fc1478839aa978e92b93a8cf01049bc18cc4bc37d5\">\n           </a>\n           wdogIllegalWindowDisable\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Watchdog illegal window disabled.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggaf7f977a90d9fe36572ad083fc1478839aa5957e19b1d8089034e42bd7b95e89af\">\n           </a>\n           wdogIllegalWindowTime12_5pct\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Window timeout is 12.5% of the timeout.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggaf7f977a90d9fe36572ad083fc1478839adfebd3f8b5a6441a1ec8ee0cea766cc6\">\n           </a>\n           wdogIllegalWindowTime25_0pct\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Window timeout is 25% of the timeout.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggaf7f977a90d9fe36572ad083fc1478839aa767ef19bcea8edc1c5b0c1b6b514cb4\">\n           </a>\n           wdogIllegalWindowTime37_5pct\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Window timeout is 37.5% of the timeout.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggaf7f977a90d9fe36572ad083fc1478839ae2332f7609208b8facf4e5db38ecd752\">\n           </a>\n           wdogIllegalWindowTime50_0pct\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Window timeout is 50% of the timeout.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggaf7f977a90d9fe36572ad083fc1478839a532db1c48294dc096364aef1ace8414d\">\n           </a>\n           wdogIllegalWindowTime62_5pct\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Window timeout is 62.5% of the timeout.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggaf7f977a90d9fe36572ad083fc1478839aa31ff7398fae9aff59bbfbc061437ac5\">\n           </a>\n           wdogIllegalWindowTime75_0pct\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Window timeout is 75% of the timeout.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggaf7f977a90d9fe36572ad083fc1478839a099d1dca3fb1cb3b57cb6e01490b84e4\">\n           </a>\n           wdogIllegalWindowTime87_5pct\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Window timeout is 87.5% of the timeout.\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n    </div>\n   </article>\n  ", "url": "http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/group-wdog", "status": "success"}