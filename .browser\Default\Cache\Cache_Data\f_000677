{"availableCount": 107, "searchTerms": [], "userState": "19559cee-c684-447b-85a3-31cb40cdd070", "resources": [{"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": [], "description": "Project to run AWS Tests on Silicon Labs boards.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.amazon_aws_tests.example/amazon_aws_tests/amazon_aws_tests.slcp", "text": "Amazon AWS - Platform Tests", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Project to run AWS Tests on Silicon Labs boards."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": [], "description": "This application demonstrates how to use the FreeRTOS Bluetooth Low Energy middleware APIs to create a simple GATT server.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.amazon_aws_soc_gatt_server.example/amazon_aws_demos/amazon_aws_soc_gatt_server.slcp", "text": "Amazon AWS - SoC Bluetooth GATT Server", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This application demonstrates how to use the FreeRTOS Bluetooth Low Energy middleware APIs to create a simple GATT server."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": [], "description": "Project to run AWS Tests including BLE tests on Silicon Labs boards.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.amazon_aws_soc_bt_tests.example/amazon_aws_tests/amazon_aws_soc_bt_tests.slcp", "text": "Amazon AWS - SoC Bluetooth Tests", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Project to run AWS Tests including BLE tests on Silicon Labs boards."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": [], "description": "This application demonstrates how to use the MQTT over Bluetooth Low Energy service.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.amazon_aws_soc_mqtt_over_ble.example/amazon_aws_demos/amazon_aws_soc_mqtt_over_ble.slcp", "text": "Amazon AWS - SoC MQTT over Bluetooth Low Energy", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This application demonstrates how to use the MQTT over Bluetooth Low Energy service."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_apple_notification_center_service/README.md"], "description": "This example demonstrates how to receive Apple Notification Center Service (ANCS) Notifications, such as phone calls, calendar events, and so on and print them out to the VCOM.", "id": "template.uc.compatibleSDK.bt_ancs.bluetooth_apple_notification_center_service/SimplicityStudio/bt_ancs.slcp", "text": "Bluetooth - Apple Notification Center Service", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example demonstrates how to receive Apple Notification Center Service (ANCS) Notifications, such as phone calls, calendar events, and so on and print them out to the VCOM."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_bthome_v2_switch/README.md"], "description": "This project aims to implement a BTHome v2 compatible switch. The device is in sleep mode, and it wakes up once the button 0 on the board is pressed. The application supports press, double press, triple press, and long press events.", "id": "template.uc.compatibleSDK.bt_bthome_v2_switch.bluetooth_bthome_v2_switch/SimplicityStudio/bt_bthome_v2_switch.slcp", "text": "Bluetooth - BTHome v2 - Switch", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement a BTHome v2 compatible switch. The device is in sleep mode, and it wakes up once the button 0 on the board is pressed. The application supports press, double press, triple press, and long press events."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_continuous_glucose_monitoring/README.md"], "description": "This project aims to implement an example of Continuous Glucose Monitoring using the Thunderboard EFR32BG22.", "id": "template.uc.compatibleSDK.bt_continuous_glucose_monitoring.bluetooth_continuous_glucose_monitoring/SimplicityStudio/bt_continuous_glucose_monitoring.slcp", "text": "Bluetooth - Continuous Glucose Monitoring", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement an example of Continuous Glucose Monitoring using the Thunderboard EFR32BG22."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_controlling_led_from_smartphone/README.md"], "description": "This example shows how to develop a simple software to control the Wireless Development Kit's LEDs with a mobile phone application.", "id": "template.uc.compatibleSDK.bt_controlling_led_from_smartphone.bluetooth_controlling_led_from_smartphone/SimplicityStudio/bt_controlling_led_from_smartphone.slcp", "text": "Bluetooth - Controlling LED from Smartphone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example shows how to develop a simple software to control the Wireless Development Kit's LEDs with a mobile phone application."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_hid_keyboard/README.md"], "description": "This is an example for Bluetooth LE HID device development, which can connect wirelessly to HID hosts including Windows, Mac, Android and iOS systems.", "id": "template.uc.compatibleSDK.bt_hid_keyboard.bluetooth_hid_keyboard/SimplicityStudio/bt_hid_keyboard.slcp", "text": "Bluetooth - HID Keyboard", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is an example for Bluetooth LE HID device development, which can connect wirelessly to HID hosts including Windows, Mac, Android and iOS systems."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_log_system/README.md"], "description": "This example introduces a simple implementation of logging on EFR32 based devices.", "id": "template.uc.compatibleSDK.bt_log_system_rtt.bluetooth_log_system/SimplicityStudio/bt_log_system_rtt.slcp", "text": "Bluetooth - Log System via RTT", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example introduces a simple implementation of logging on EFR32 based devices."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_log_system/README.md"], "description": "This example introduces a simple implementation of logging on EFR32 based devices.", "id": "template.uc.compatibleSDK.bt_log_system_vcom.bluetooth_log_system/SimplicityStudio/bt_log_system_vcom.slcp", "text": "Bluetooth - Log System via VCOM (UART)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example introduces a simple implementation of logging on EFR32 based devices."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_man_in_the_middle/README.md"], "description": "This project shows the implementation of Man In The Middle (MITM) with BLE.", "id": "template.uc.compatibleSDK.bt_man_in_the_middle_device.bluetooth_man_in_the_middle/bluetooth_man_in_the_middle_device/SimplicityStudio/bt_man_in_the_middle_device.slcp", "text": "Bluetooth - Man-In-The-Middle", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project shows the implementation of Man In The Middle (MITM) with BLE."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_multicentral_multiperipheral_dual_topology/README.md"], "description": "This code example demonstrates how to handle multiple (simultaneous) connections, when the device has to act as central on some of the connections, and peripheral on the rest of the connections.", "id": "template.uc.compatibleSDK.bt_multicentral_multiperipheral_dual_topology.bluetooth_multicentral_multiperipheral_dual_topology/SimplicityStudio/bluetooth_multicentral_multiperipheral_dual_topology.slcp", "text": "Bluetooth - Multi-Central Multi-Peripheral Dual Topology", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This code example demonstrates how to handle multiple (simultaneous) connections, when the device has to act as central on some of the connections, and peripheral on the rest of the connections."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/ncp/readme.md"], "description": "Network Co-Processor (NCP) target application. Runs the Bluetooth stack dynamically and provides access to it via Bluetooth API (BGAPI) using UART connection. NCP mode makes it possible to run your application on a host controller or PC.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_ncp.example/bt_ncp/bt_ncp.slcp", "text": "Bluetooth - NCP", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Network Co-Processor (NCP) target application. Runs the Bluetooth stack dynamically and provides access to it via Bluetooth API (BGAPI) using UART connection. NCP mode makes it possible to run your application on a host controller or PC.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/ncp/readme.md"], "description": "Network Co-Processor (NCP) target application. Runs the Bluetooth stack dynamically and provides access to it via Bluetooth API (BGAPI) using UART connection. NCP mode makes it possible to run your application on a host controller or PC.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_ncp.example/bt_ncp/bt_ncp_xg1.slcp", "text": "Bluetooth - NCP", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Network Co-Processor (NCP) target application. Runs the Bluetooth stack dynamically and provides access to it via Bluetooth API (BGAPI) using UART connection. NCP mode makes it possible to run your application on a host controller or PC.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/ncp_host/readme.md"], "description": "Reference implementation of an NCP (Network Co-Processor) host, which typically runs on a central MCU without radio. It can connect to an NCP target running the NCP Example via UART to access the Bluetooth stack on the target and to control it using BGAPI.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_ncp_host.example/bt_ncp_host/bt_ncp_host.slcp", "text": "Bluetooth - NCP Host", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Reference implementation of an NCP (Network Co-Processor) host, which typically runs on a central MCU without radio. It can connect to an NCP target running the NCP Example via UART to access the Bluetooth stack on the target and to control it using BGAPI.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_ota_firmware_update_in_user_application/README.md"], "description": "This project aims to implement firmware upgrade method used in SoC-mode Bluetooth applications. A Gecko Bootloader (GBL) image containing the new firmware is sent to target device via a Bluetooth connection.", "id": "template.uc.compatibleSDK.bt_ota_firmware_update_in_user_application.bluetooth_ota_firmware_update_in_user_application/SimplicityStudio/bt_ota_firmware_update_in_user_application.slcp", "text": "Bluetooth - OTA Firmware Update in User Application", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement firmware upgrade method used in SoC-mode Bluetooth applications. A Gecko Bootloader (GBL) image containing the new firmware is sent to target device via a Bluetooth connection."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/rcp/readme.md"], "description": "Radio Co-Processor (RCP) target application. Runs the Bluetooth Controller (i.e. the Link Layer only) and provides access to it using the standard HCI (Host-Controller Interface) over a UART connection.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_rcp.example/bt_rcp/bt_rcp.slcp", "text": "Bluetooth - RCP", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Radio Co-Processor (RCP) target application. Runs the Bluetooth Controller (i.e. the Link Layer only) and provides access to it using the standard HCI (Host-Controller Interface) over a UART connection.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/rcp_cpc/readme.md"], "description": "Radio Co-Processor (RCP) target application. Runs the Bluetooth Controller (i.e. the Link Layer only) and provides access to it using the standard HCI (Host-Controller Interface) over CPC (Co-Processor Communication) protocol through UART connection.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_rcp_cpc.example/bt_rcp/bt_rcp_cpc.slcp", "text": "Bluetooth - RCP CPC", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Radio Co-Processor (RCP) target application. Runs the Bluetooth Controller (i.e. the Link Layer only) and provides access to it using the standard HCI (Host-Controller Interface) over CPC (Co-Processor Communication) protocol through UART connection.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_rssi_positioning/README.md"], "description": "A gateway device application intended to showcase a room finder or asset locator service using the BLE stack on Silicon Laboratories development kits.", "id": "template.uc.compatibleSDK.bt_indoor_positioning_gateway.bluetooth_rssi_positioning/bt_indoor_positioning_gateway/SimplicityStudio/bt_indoor_positioning_gateway.slcp", "text": "Bluetooth - RSSI Positioning - Gateway", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "A gateway device application intended to showcase a room finder or asset locator service using the BLE stack on Silicon Laboratories development kits."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_secure_attestation/README.md"], "description": "This project aims to implement a secure attestation over Bluetooth Low Energy for client device. The client acts as a central device for the accompanying server.", "id": "template.uc.compatibleSDK.bt_secure_attestation_client.bluetooth_secure_attestation/bt_secure_attestation_client/SimplicityStudio/bt_secure_attestation_client.slcp", "text": "Bluetooth - Secure Attestation - Client", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement a secure attestation over Bluetooth Low Energy for client device. The client acts as a central device for the accompanying server."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_secure_attestation/README.md"], "description": "This project aims to implement a secure attestation over Bluetooth Low Energy for server device. The server acts in the BLE peripheral role and allows a central/client to connect to it.", "id": "template.uc.compatibleSDK.bt_secure_attestation_server.bluetooth_secure_attestation/bt_secure_attestation_server/SimplicityStudio/bt_secure_attestation_server.slcp", "text": "Bluetooth - Secure Attestation - Server", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement a secure attestation over Bluetooth Low Energy for server device. The server acts in the BLE peripheral role and allows a central/client to connect to it."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_secure_spp_over_ble/README.md"], "description": "This project aims to implement a secure Bluetooth connection between two EFR32 devices and how to implement secure serial communication between them.", "id": "template.uc.compatibleSDK.bt_secure_spp_over_ble.bluetooth_secure_spp_over_ble/SimplicityStudio/bt_secure_spp_over_ble.slcp", "text": "Bluetooth - Secure SPP over BLE", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement a secure Bluetooth connection between two EFR32 devices and how to implement secure serial communication between them."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_serial_port_profile/README.md"], "description": "This example provides a simple template for SPP-like communication (also know as wire replacement), where Bluetooth serves as a transport channel for serial communication between server and client.", "id": "template.uc.compatibleSDK.bt_serial_port_profile_client.bluetooth_serial_port_profile/bt_spp_client/SimplicityStudio/bt_serial_port_profile_client.slcp", "text": "Bluetooth - Serial Port Profile (SPP) - Client", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example provides a simple template for SPP-like communication (also know as wire replacement), where Bluetooth serves as a transport channel for serial communication between server and client."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_serial_port_profile/README.md"], "description": "This example provides a simple template for SPP-like communication (also know as wire replacement), where Bluetooth serves as a transport channel for serial communication between server and client.", "id": "template.uc.compatibleSDK.bt_serial_port_profile_server.bluetooth_serial_port_profile/bt_spp_server/SimplicityStudio/bt_serial_port_profile_server.slcp", "text": "Bluetooth - Serial Port Profile (SPP) - Server", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example provides a simple template for SPP-like communication (also know as wire replacement), where Bluetooth serves as a transport channel for serial communication between server and client."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_blinky/readme.md"], "description": "The classic blinky example using Bluetooth communication. Demonstrates a simple two-way data exchange over GATT. This can be tested with the EFR Connect mobile app.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_blinky.example/bt_soc_blinky/bt_soc_blinky.slcp", "text": "Bluetooth - SoC Blinky", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The classic blinky example using Bluetooth communication. Demonstrates a simple two-way data exchange over GATT. This can be tested with the EFR Connect mobile app.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_blinky/readme.md"], "description": "The classic blinky example using Bluetooth communication. Demonstrates a simple two-way data exchange over GATT. This can be tested with the EFR Connect mobile app.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_blinky_shared.example/bt_soc_blinky/bt_soc_blinky_shared.slcp", "text": "Bluetooth - SoC Blinky", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The classic blinky example using Bluetooth communication. Demonstrates a simple two-way data exchange over GATT. This can be tested with the EFR Connect mobile app.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_dtm/readme.md"], "description": "This example implements the direct test mode (DTM) application for radio testing. DTM commands can be called via UART.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_dtm.example/bt_soc_dtm/bt_soc_dtm.slcp", "text": "Bluetooth - SoC DTM", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example implements the direct test mode (DTM) application for radio testing. DTM commands can be called via UART.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_empty/readme.md"], "description": "A minimal project structure, that serves as a starting point for custom Bluetooth applications. The application starts advertising after boot and restarts advertising after a connection is closed.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_empty.example/bt_soc_empty/bt_soc_empty.slcp", "text": "Bluetooth - SoC Empty", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "A minimal project structure, that serves as a starting point for custom Bluetooth applications. The application starts advertising after boot and restarts advertising after a connection is closed.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_iop_test/readme.md"], "description": "This is a test procedure containing several test cases for Bluetooth Low Energy communication. This demo is meant to be used with the EFR Connect mobile app, through the \"Interoperability Test\" tile on the Develop view of the app.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_iop_test_display.example/bt_soc_iop_test/bt_soc_iop_test_display.slcp", "text": "Bluetooth - SoC Interoperability Test", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a test procedure containing several test cases for Bluetooth Low Energy communication. This demo is meant to be used with the EFR Connect mobile app, through the \"Interoperability Test\" tile on the Develop view of the app.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_iop_test/readme.md"], "description": "This is a test procedure containing several test cases for Bluetooth Low Energy communication. This demo is meant to be used with the EFR Connect mobile app, through the \"Interoperability Test\" tile on the Develop view of the app.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_iop_test_log.example/bt_soc_iop_test/bt_soc_iop_test_log.slcp", "text": "Bluetooth - SoC Interoperability Test", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a test procedure containing several test cases for Bluetooth Low Energy communication. This demo is meant to be used with the EFR Connect mobile app, through the \"Interoperability Test\" tile on the Develop view of the app.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_thermometer/readme.md"], "description": "Implements a GATT Server with the Health Thermometer Profile, which enables a Client device to connect and get temperature data. Temperature is read from the Si7021 digital relative humidity and temperature sensor of the WSTK or of the Thunderboard.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_thermometer.example/bt_soc_thermometer/bt_soc_thermometer.slcp", "text": "Bluetooth - SoC Thermometer", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Implements a GATT Server with the Health Thermometer Profile, which enables a Client device to connect and get temperature data. Temperature is read from the Si7021 digital relative humidity and temperature sensor of the WSTK or of the Thunderboard.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_thermometer/readme.md"], "description": "Implements a GATT Server with the Health Thermometer Profile, which enables a Client device to connect and get temperature data. Temperature is read from the mock relative humidity and temperature sensor.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_thermometer_mock.example/bt_soc_thermometer/bt_soc_thermometer_mock.slcp", "text": "Bluetooth - SoC Thermometer (Mock)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Implements a GATT Server with the Health Thermometer Profile, which enables a Client device to connect and get temperature data. Temperature is read from the mock relative humidity and temperature sensor.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_thermometer_rtos/readme.md"], "description": "Demonstrates the integration of FreeRTOS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer (Mock) sample app.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_thermometer_freertos_mock.example/bt_soc_thermometer/bt_soc_thermometer_freertos_mock.slcp", "text": "Bluetooth - SoC Thermometer (Mock) FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates the integration of FreeRTOS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer (Mock) sample app.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_thermometer_rtos/readme.md"], "description": "Demonstrates the integration of Micrium OS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer (Mock) sample app.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_thermometer_micriumos_mock.example/bt_soc_thermometer/bt_soc_thermometer_micriumos_mock.slcp", "text": "Bluetooth - SoC Thermometer (Mock) Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates the integration of Micrium OS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer (Mock) sample app.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_man_in_the_middle/README.md"], "description": "This example project shows an example for thermometer server authenticator's role in MITM scenario.", "id": "template.uc.compatibleSDK.bt_thermometer_authenticated_server.bluetooth_man_in_the_middle/bluetooth_thermometer_authenticated_server/SimplicityStudio/bt_thermometer_authenticated_server.slcp", "text": "Bluetooth - SoC Thermometer Authenticated Server", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for thermometer server authenticator's role in MITM scenario."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_thermometer_client/readme.md"], "description": "Implements a GATT Client that discovers and connects with up to 4 BLE devices advertising themselves as Thermometer Servers. It displays the discovery process and the temperature values received via UART.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_thermometer_client.example/bt_soc_thermometer_client/bt_soc_thermometer_client.slcp", "text": "Bluetooth - SoC Thermometer Client", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Implements a GATT Client that discovers and connects with up to 4 BLE devices advertising themselves as Thermometer Servers. It displays the discovery process and the temperature values received via UART.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_thermometer_rtos/readme.md"], "description": "Demonstrates the integration of FreeRTOS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer sample app.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_thermometer_freertos.example/bt_soc_thermometer/bt_soc_thermometer_freertos.slcp", "text": "Bluetooth - SoC Thermometer FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates the integration of FreeRTOS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer sample app.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_thermometer_rtos/readme.md"], "description": "Demonstrates the integration of Micrium OS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer sample app.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_thermometer_micriumos.example/bt_soc_thermometer/bt_soc_thermometer_micriumos.slcp", "text": "Bluetooth - SoC Thermometer Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates the integration of Micrium OS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer sample app.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_throughput/readme.md"], "description": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using EFR Connect mobile app, through the Throughput demo tile.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_throughput_display.example/bt_soc_throughput/bt_soc_throughput_display.slcp", "text": "Bluetooth - SoC Throughput", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using EFR Connect mobile app, through the Throughput demo tile.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_throughput/readme.md"], "description": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using EFR Connect mobile app, through the Throughput demo tile.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_throughput_log.example/bt_soc_throughput/bt_soc_throughput_log.slcp", "text": "Bluetooth - SoC Throughput", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using EFR Connect mobile app, through the Throughput demo tile.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_throughput/readme.md"], "description": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using EFR Connect mobile app, through the Throughput demo tile.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_throughput_log_single.example/bt_soc_throughput/bt_soc_throughput_log_single.slcp", "text": "Bluetooth - SoC Throughput", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using EFR Connect mobile app, through the Throughput demo tile.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_ibeacon/readme.md"], "description": "Sends non-connectable advertisements in iBeacon format. The iBeacon Service gives Bluetooth accessories a simple and convenient way to send iBeacons to smartphones. This example can be tested together with the EFR Connect mobile app.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_soc_ibeacon.example/bt_soc_ibeacon/bt_soc_ibeacon.slcp", "text": "Bluetooth - SoC iBeacon", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Sends non-connectable advertisements in iBeacon format. The iBeacon Service gives Bluetooth accessories a simple and convenient way to send iBeacons to smartphones. This example can be tested together with the EFR Connect mobile app.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_thermometer_with_efr32_internal_temperature_sensor/README.md"], "description": "This example is an adaptation of the standard 'SOC - Thermometer' example. However, instead of accessing the Si7021 Temperature and Relative Humidity sensor through I2C, this example uses the EFR32's own internal temperature sensor. This sensor is measured during the production test.", "id": "template.uc.compatibleSDK.bt_thermometer_with_efr32_internal_temp_sensor.bluetooth_thermometer_with_efr32_internal_temperature_sensor/SimplicityStudio/bt_thermometer_with_efr32_internal_temp_sensor.slcp", "text": "Bluetooth - Thermometer with EFR32 Internal Temperature Sensor", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example is an adaptation of the standard 'SOC - Thermometer' example. However, instead of accessing the Si7021 Temperature and Relative Humidity sensor through I2C, this example uses the EFR32's own internal temperature sensor. This sensor is measured during the production test."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_wireless_dtm/README.md"], "description": "This project aims to implement the Wireless Direct Test Mode (DTM) to test transmission/reception capabilites of the Bluetooth-based design.", "id": "template.uc.compatibleSDK.bt_wireless_dtm.bluetooth_wireless_dtm/SimplicityStudio/bt_wireless_dtm.slcp", "text": "Bluetooth - Wireless Direct Test Mode (DTM)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement the Wireless Direct Test Mode (DTM) to test transmission/reception capabilites of the Bluetooth-based design."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/btmesh_ncp_empty/readme.md"], "description": "An NCP Target C application that makes it possible for the NCP Host Controller to access the Bluetooth Mesh stack via UART. It provides access to the host layer via BGAPI and not to the link layer via HCI.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.btmesh_ncp_empty.example/btmesh_ncp_empty/btmesh_ncp_empty.slcp", "text": "Bluetooth Mesh - NCP Empty", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An NCP Target C application that makes it possible for the NCP Host Controller to access the Bluetooth Mesh stack via UART. It provides access to the host layer via BGAPI and not to the link layer via HCI.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/btmesh_soc_empty/readme.md"], "description": "Demonstrates the bare minimum needed for a Bluetooth Mesh C application. The application starts Unprovisioned Device Beaconing after booting, and then waits to be provisioned.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.btmesh_soc_empty.example/btmesh_soc_empty/btmesh_soc_empty.slcp", "text": "Bluetooth Mesh - SoC Empty", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates the bare minimum needed for a Bluetooth Mesh C application. The application starts Unprovisioned Device Beaconing after booting, and then waits to be provisioned.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_empty_std_dmp/readme.md"], "description": "A minimal project structure used as a starting point for custom Bluetooth + Standard DMP (Dynamic Multiprotocol) applications. It runs on top of FreeRTOS and multiprotocol RAIL utilizing IEEE 802.15.4 standard protocol.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_rail_dmp_soc_empty_std_freertos.example/bt_rail_dmp_soc_empty_std/bt_rail_dmp_soc_empty_std_freertos.slcp", "text": "Bluetooth RAIL DMP - SoC Empty Standard FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "A minimal project structure used as a starting point for custom Bluetooth + Standard DMP (Dynamic Multiprotocol) applications. It runs on top of FreeRTOS and multiprotocol RAIL utilizing IEEE 802.15.4 standard protocol.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_empty_std_dmp/readme.md"], "description": "A minimal project structure used as a starting point for custom Bluetooth + Standard DMP (Dynamic Multiprotocol) applications. It runs on top of Micrium OS and multiprotocol RAIL utilizing IEEE 802.15.4 standard protocol.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_rail_dmp_soc_empty_std_micriumos.example/bt_rail_dmp_soc_empty_std/bt_rail_dmp_soc_empty_std_micriumos.slcp", "text": "Bluetooth RAIL DMP - SoC Empty Standard Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "A minimal project structure used as a starting point for custom Bluetooth + Standard DMP (Dynamic Multiprotocol) applications. It runs on top of Micrium OS and multiprotocol RAIL utilizing IEEE 802.15.4 standard protocol.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_light_std_dmp/readme.md"], "description": "This is a Dynamic Multiprotocol reference application demonstrating a light bulb that can be switched both via Bluetooth and via a standard protocol. Can be tested with the EFR Connect mobile app and Flex (RAIL) Switch Standards sample app.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_rail_dmp_soc_light_std_freertos.example/bt_rail_dmp_soc_light_std/bt_rail_dmp_soc_light_std_freertos.slcp", "text": "Bluetooth RAIL DMP - SoC Light Standard FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a Dynamic Multiprotocol reference application demonstrating a light bulb that can be switched both via Bluetooth and via a standard protocol. Can be tested with the EFR Connect mobile app and Flex (RAIL) Switch Standards sample app.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/bluetooth/documentation/example/soc_light_std_dmp/readme.md"], "description": "This is a Dynamic Multiprotocol reference application demonstrating a light bulb that can be switched both via Bluetooth and via a standard protocol. Can be tested with the EFR Connect mobile app and Flex (RAIL) Switch Standards sample app.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bt_rail_dmp_soc_light_std_micriumos.example/bt_rail_dmp_soc_light_std/bt_rail_dmp_soc_light_std_micriumos.slcp", "text": "Bluetooth RAIL DMP - SoC Light Standard Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a Dynamic Multiprotocol reference application demonstrating a light bulb that can be switched both via Bluetooth and via a standard protocol. Can be tested with the EFR Connect mobile app and Flex (RAIL) Switch Standards sample app.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-uart-bgapi/readme.md"], "description": "Standalone Bootloader using the BGAPI protocol for UART DFU. This is the recommended UART bootloader for the BLE protocol stack.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-uart-bgapi.sample-apps/bootloader-uart-bgapi/bootloader-uart-bgapi.slcp", "text": "Bootloader - NCP BGAPI UART DFU", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Standalone Bootloader using the BGAPI protocol for UART DFU. This is the recommended UART bootloader for the BLE protocol stack.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-uart-bgapi/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-uart-bgapi-nonsecure.sample-apps/bootloader-uart-bgapi/bootloader-uart-bgapi-nonsecure.slcp", "text": "Bootloader - NCP BGAPI UART DFU Non-Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-uart-bgapi/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-uart-bgapi-secure.sample-apps/bootloader-uart-bgapi/bootloader-uart-bgapi-secure.slcp", "text": "Bootloader - NCP BGAPI UART DFU Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-spi-ezsp/readme.md"], "description": "Standalone Bootloader using the EZSP protocol over SPI. This is the recommended SPI bootloader for the EmberZNet and Connect protocol stacks.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-spi-ezsp.sample-apps/bootloader-spi-ezsp/bootloader-spi-ezsp.slcp", "text": "Bootloader - NCP EZSP SPI", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Standalone Bootloader using the EZSP protocol over SPI. This is the recommended SPI bootloader for the EmberZNet and Connect protocol stacks.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-spi-ezsp/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-spi-ezsp-nonsecure.sample-apps/bootloader-spi-ezsp/bootloader-spi-ezsp-nonsecure.slcp", "text": "Bootloader - NCP EZSP SPI Non-Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-spi-ezsp/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-spi-ezsp-secure.sample-apps/bootloader-spi-ezsp/bootloader-spi-ezsp-secure.slcp", "text": "Bootloader - NCP EZSP SPI Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-uart-xmodem/readme.md"], "description": "Standalone Bootloader using XMODEM-CRC over UART. The bootloader shows a menu, where an XMODEM transfer can be started by sending ASCII '1', or the application can be started by sending ASCII '2'. This is the recommended UART bootloader for the EmberZNet and Connect protocol stacks.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-uart-xmodem.sample-apps/bootloader-uart-xmodem/bootloader-uart-xmodem.slcp", "text": "Bootloader - NCP UART XMODEM", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Standalone Bootloader using XMODEM-CRC over UART. The bootloader shows a menu, where an XMODEM transfer can be started by sending ASCII '1', or the application can be started by sending ASCII '2'. This is the recommended UART bootloader for the EmberZNet and Connect protocol stacks.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-uart-xmodem/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-uart-xmodem-nonsecure.sample-apps/bootloader-uart-xmodem/bootloader-uart-xmodem-nonsecure.slcp", "text": "Bootloader - NCP UART XMODEM Non-Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-uart-xmodem/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-uart-xmodem-secure.sample-apps/bootloader-uart-xmodem/bootloader-uart-xmodem-secure.slcp", "text": "Bootloader - NCP UART XMODEM Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-apploader/readme.md"], "description": "Standalone Bootloader using the Bluetooth AppLoader OTA DFU. This implements in-place application updates using Bluetooth connection.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-apploader.sample-apps/bootloader-apploader/bootloader-apploader.slcp", "text": "Bootloader - SoC Bluetooth AppLoader OTA DFU", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Standalone Bootloader using the Bluetooth AppLoader OTA DFU. This implements in-place application updates using Bluetooth connection.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-apploader/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-apploader-nonsecure.sample-apps/bootloader-apploader/bootloader-apploader-nonsecure.slcp", "text": "Bootloader - SoC Bluetooth AppLoader OTA DFU Non-Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-apploader/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-apploader-secure.sample-apps/bootloader-apploader/bootloader-apploader-secure.slcp", "text": "Bootloader - SoC Bluetooth AppLoader OTA DFU Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-internal-ble/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x21800 (or 0x8021800 for device with 0x8000000 flash base), and have a size of 86 kB. This can be configured on the \"Software Components->Platform->Bootloader->Storage->Bootloader Storage Slot Setup\".", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-internal-ble.sample-apps/bootloader-storage-internal-ble/bootloader-storage-internal-ble.slcp", "text": "Bootloader - SoC Bluetooth in-place OTA DFU", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x21800 (or 0x8021800 for device with 0x8000000 flash base), and have a size of 86 kB. This can be configured on the \"Software Components->Platform->Bootloader->Storage->Bootloader Storage Slot Setup\".\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-internal/readme.md"], "description": "Application Bootloader for all EFR32 and EFM32 devices with 1MB or larger flash, using the internal flash memory to store upgrade images received by the application over the air in an application specific way. This sample configuration supports storing multiple firmware update images simultaneously by configuring multiple storage slots. The storage layout is designed for 1024 kB EFR32xG12 devices, and the layout of the storage should be modified before being run on other devices.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-internal.sample-apps/bootloader-storage-internal/bootloader-storage-internal.slcp", "text": "Bootloader - SoC Internal Storage (multiple images on 1MB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Application Bootloader for all EFR32 and EFM32 devices with 1MB or larger flash, using the internal flash memory to store upgrade images received by the application over the air in an application specific way. This sample configuration supports storing multiple firmware update images simultaneously by configuring multiple storage slots. The storage layout is designed for 1024 kB EFR32xG12 devices, and the layout of the storage should be modified before being run on other devices.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-internal-single-1536k/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0xc0000 (or 0x80c0000 for device with 0x8000000 flash base), and have a size of 728 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-internal-single-1536k.sample-apps/bootloader-storage-internal-single-1536k/bootloader-storage-internal-single-1536k.slcp", "text": "Bootloader - SoC Internal Storage (single image on 1536kB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0xc0000 (or 0x80c0000 for device with 0x8000000 flash base), and have a size of 728 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-internal-single-1920k/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x80E8000, and have a size of 944 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-internal-single-1920k.sample-apps/bootloader-storage-internal-single-1920k/bootloader-storage-internal-single-1920k.slcp", "text": "Bootloader - SoC Internal Storage (single image on 1920kB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x80E8000, and have a size of 944 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-internal-single/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x84000 (or 0x8084000 for device with 0x8000000 flash base), and have a size of 448 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-internal-single.sample-apps/bootloader-storage-internal-single/bootloader-storage-internal-single.slcp", "text": "Bootloader - SoC Internal Storage (single image on 1MB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x84000 (or 0x8084000 for device with 0x8000000 flash base), and have a size of 448 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-internal-single-2048k/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x100000 (or 0x8100000 for device with 0x8000000 flash base), and have a size of 988 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-internal-single-2048k.sample-apps/bootloader-storage-internal-single-2048k/bootloader-storage-internal-single-2048k.slcp", "text": "Bootloader - SoC Internal Storage (single image on 2MB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x100000 (or 0x8100000 for device with 0x8000000 flash base), and have a size of 988 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-internal-single-352k/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x28000 (or 0x8028000 for device with 0x8000000 flash base), and have a size of 120 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-internal-single-352k.sample-apps/bootloader-storage-internal-single-352k/bootloader-storage-internal-single-352k.slcp", "text": "Bootloader - SoC Internal Storage (single image on 352kB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x28000 (or 0x8028000 for device with 0x8000000 flash base), and have a size of 120 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-internal-single-512k/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x44000 (or 0x8044000 for device with 0x8000000 flash base), and have a size of 192 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-internal-single-512k.sample-apps/bootloader-storage-internal-single-512k/bootloader-storage-internal-single-512k.slcp", "text": "Bootloader - SoC Internal Storage (single image on 512kB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x44000 (or 0x8044000 for device with 0x8000000 flash base), and have a size of 192 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-internal-single-768k/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x5C000 (or 0x805C000 for device with 0x8000000 flash base), and have a size of 368 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-internal-single-768k.sample-apps/bootloader-storage-internal-single-768k/bootloader-storage-internal-single-768k.slcp", "text": "Bootloader - SoC Internal Storage (single image on 768kB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x5C000 (or 0x805C000 for device with 0x8000000 flash base), and have a size of 368 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-internal-single-lzma/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x84000 (or 0x8084000 for device with 0x8000000 flash base), and have a size of 448 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-internal-single-lzma.sample-apps/bootloader-storage-internal-single-lzma/bootloader-storage-internal-single-lzma.slcp", "text": "Bootloader - SoC Internal Storage (single image with LZMA compression, 1MB flash)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x84000 (or 0x8084000 for device with 0x8000000 flash base), and have a size of 448 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-spiflash/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use an external SPI flash to store firmware update images. The storage configuration is set up to store up to two firmware update images at a time, in two storage slots. The storage slots are configured to start at address 0x2000 and 0x41000 of the SPI flash respectively, and have a size of 252 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-spiflash.sample-apps/bootloader-storage-spiflash/bootloader-storage-spiflash.slcp", "text": "Bootloader - SoC SPI Flash Storage (multiple images)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use an external SPI flash to store firmware update images. The storage configuration is set up to store up to two firmware update images at a time, in two storage slots. The storage slots are configured to start at address 0x2000 and 0x41000 of the SPI flash respectively, and have a size of 252 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-spiflash-single-1024k/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use an external SPI flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x0 of the SPI flash, and have a size of 1024 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-spiflash-single-1024k.sample-apps/bootloader-storage-spiflash-single-1024k/bootloader-storage-spiflash-single-1024k.slcp", "text": "Bootloader - SoC SPI Flash Storage (single image with slot size of 1024k)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use an external SPI flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x0 of the SPI flash, and have a size of 1024 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-spiflash-single/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use an external SPI flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x0 of the SPI flash, and have a size of 512 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-spiflash-single.sample-apps/bootloader-storage-spiflash-single/bootloader-storage-spiflash-single.slcp", "text": "Bootloader - SoC SPI Flash Storage (single image)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use an external SPI flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x0 of the SPI flash, and have a size of 512 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/platform/bootloader/sample-apps/bootloader-storage-spiflash-sfdp-single/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use an external SPI flash to store firmware update images. The SPI flash type is detected automatically by querying the SFDP parameter table present inside the flash memory. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x0 of the SPI flash, and have a size of 512 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.bootloader-storage-spiflash-sfdp-single.sample-apps/bootloader-storage-spiflash-sfdp-single/bootloader-storage-spiflash-sfdp-single.slcp", "text": "Bootloader - SoC SPI Flash Storage using SFDP (single image)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use an external SPI flash to store firmware update images. The SPI flash type is detected automatically by querying the SFDP parameter table present inside the flash memory. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x0 of the SPI flash, and have a size of 512 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/flex/documentation/example/connect/connect_soc_direct_mode_device/readme.md"], "description": "This sample app allows direct commissioning of nodes and exchange data between them via CLI commands.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.connect_soc_direct_mode_device.example/connect/connect_soc_direct_mode_device/connect_soc_direct_mode_device.slcp", "text": "Connect - SoC Direct Mode Device", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample app allows direct commissioning of nodes and exchange data between them via CLI commands."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/flex/documentation/example/connect/connect_soc_ecdh_key_exchange/readme.md"], "description": "This sample application illustrates how we could share the network key between multiple devices in a secure way (using Elliptic-curve <PERSON><PERSON><PERSON><PERSON> (ECDH) key agreement protocol).", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.connect_soc_ecdh_key_exchange.example/connect/connect_soc_ecdh_key_exchange/connect_soc_ecdh_key_exchange.slcp", "text": "Connect - SoC ECDH Key Exchange", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample application illustrates how we could share the network key between multiple devices in a secure way (using Elliptic-curve <PERSON><PERSON><PERSON><PERSON> (ECDH) key agreement protocol)."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/flex/documentation/example/connect/connect_soc_empty/readme.md"], "description": "The Connect Empty project is a barebone Connect app that can be a basis of streamlined proprietary solutions.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.connect_soc_empty.example/connect/connect_soc_empty/connect_soc_empty.slcp", "text": "Connect - SoC Empty", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Connect Empty project is a barebone Connect app that can be a basis of streamlined proprietary solutions."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/flex/documentation/example/connect/connect_soc_mac_mode_device/readme.md"], "description": "A 802.15.4 sample app that provides CLI commands to form a network or join an existing network, send data to another node based on short or long addresses.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.connect_soc_mac_mode_device.example/connect/connect_soc_mac_mode_device/connect_soc_mac_mode_device.slcp", "text": "Connect - SoC MAC Mode Device", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "A 802.15.4 sample app that provides CLI commands to form a network or join an existing network, send data to another node based on short or long addresses."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/flex/documentation/example/connect/connect_sink_sensor/readme.md"], "description": "Demonstrates how to properly setup a star network topology in which communication occurs in both directions between the Sink and the Sensor(s) nodes. The PB0 pushbutton can be used to enable or disable sleep, combined with changing the VCOM option 'Enable reception when sleeping'.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.connect_soc_sensor.example/connect/sensor_sink/connect_soc_sensor/connect_soc_sensor.slcp", "text": "Connect - SoC Sensor", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates how to properly setup a star network topology in which communication occurs in both directions between the Sink and the Sensor(s) nodes. The PB0 pushbutton can be used to enable or disable sleep, combined with changing the VCOM option 'Enable reception when sleeping'."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/flex/documentation/example/connect/connect_sink_sensor/readme.md"], "description": "The Sink example is the counterpart of the Sensor example. It receives reports of Sensor nodes joining to its network.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.connect_soc_sink.example/connect/sensor_sink/connect_soc_sink/connect_soc_sink.slcp", "text": "Connect - SoC Sink", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Sink example is the counterpart of the Sensor example. It receives reports of Sensor nodes joining to its network."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/flex/documentation/example/connect/connect_bt_dmp_soc_empty/readme.md"], "description": "The Connect Empty DMP example is an RTOS-based project that provides a skeleton for Connect but not functions, beside a BLE Task with a basic CLI interface.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.connect_bt_dmp_soc_empty.example/connect/connect_bt_dmp_soc_empty/connect_bt_dmp_soc_empty.slcp", "text": "Connect Bluetooth DMP - SoC Empty", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Connect Empty DMP example is an RTOS-based project that provides a skeleton for Connect but not functions, beside a BLE Task with a basic CLI interface."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": [], "description": "This example project shows an empty configuration that can be used as a starting point to add components and functionality.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.cExeProject", "text": "Empty C Project", "priority": 1, "category": "SOFTWARE", "toolTipText": "This example project shows an empty configuration that can be used as a starting point to add components and functionality."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": [], "description": "This example project shows an empty configuration that can be used as a starting point to add components and functionality.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.cppExeProject", "text": "Empty C++ Project", "priority": 1, "category": "SOFTWARE", "toolTipText": "This example project shows an empty configuration that can be used as a starting point to add components and functionality."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/blink_baremetal/readme.md"], "description": "This example project shows how to blink an LED in a bare-metal configuration.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.blink_baremetal.example/blink_baremetal/blink_baremetal.slcp", "text": "Platform - Blink Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to blink an LED in a bare-metal configuration.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/blink_kernel_freertos/readme.md"], "description": "This example project shows how to blink an LED using a FreeRTOS kernel task. The blink task can be created using either dynamic or static memory allocation for the task stack and tcb.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.blink_kernel_freertos.example/blink_kernel_freertos/blink_kernel_freertos.slcp", "text": "Platform - Blink Kernel FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to blink an LED using a FreeRTOS kernel task. The blink task can be created using either dynamic or static memory allocation for the task stack and tcb.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/blink_kernel_micriumos/readme.md"], "description": "This example project shows how to blink an LED using a Micrium OS kernel task.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.blink_kernel_micriumos.example/blink_kernel_micriumos/blink_kernel_micriumos.slcp", "text": "Platform - Blink Kernel Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to blink an LED using a Micrium OS kernel task.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/blink_pwm_baremetal/readme.md"], "description": "This example project uses the PWM driver that uses a TIMER to gradually adjust the intensity of an LED up and down.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.blink_pwm_baremetal.example/blink_pwm_baremetal/blink_pwm_baremetal.slcp", "text": "Platform - Blink PWM", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the PWM driver that uses a TIMER to gradually adjust the intensity of an LED up and down.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/cpc_secondary_spi_security_device_recovery/readme.md"], "description": "This simple example project implements the function sl_cpc_security_on_unbind_request(), allowing the secondary device to be unbound from the host.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.cpc_secondary_spi_security_device_recovery.example/cpc_secondary_spi_security_device_recovery/cpc_secondary_spi_security_device_recovery.slcp", "text": "Platform - CPC Secondary Device Recovery - SPI", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This simple example project implements the function sl_cpc_security_on_unbind_request(), allowing the secondary device to be unbound from the host.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/dmadrv_baremetal/readme.md"], "description": "This example project shows how to use DMADRV driver to transfer data between memory and a USART peripheral in a bare-metal configuration.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.dmadrv_baremetal.example/dmadrv_baremetal/dmadrv_baremetal.slcp", "text": "Platform - DMADRV Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to use DMADRV driver to transfer data between memory and a USART peripheral in a bare-metal configuration.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/iostream_usart_baremetal/readme.md"], "description": "This example project uses the I/O Stream service running in a bare-metal configuration  to demonstrate the use of UART communication over the virtual COM port (VCOM). The  application will echo back any characters it receives over the serial connection. The  VCOM serial port can be used either over USB or by connecting to port 4902 if the kit  is connected via Ethernet.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.iostream_usart_baremetal.example/iostream_usart_baremetal/iostream_usart_baremetal.slcp", "text": "Platform - I/O Stream USART Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the I/O Stream service running in a bare-metal configuration  to demonstrate the use of UART communication over the virtual COM port (VCOM). The  application will echo back any characters it receives over the serial connection. The  VCOM serial port can be used either over USB or by connecting to port 4902 if the kit  is connected via Ethernet. \n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/iostream_usart_kernel_micriumos/readme.md"], "description": "This example project uses the I/O Stream service running in a Micrium OS kernel task to demonstrate the use of UART communication over the virtual COM port (VCOM). The application will echo back any characters it receives over the serial connection. The VCOM serial port can be used either over USB or by connecting to port 4902 if the kit is connected via Ethernet.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.iostream_usart_kernel_micriumos.example/iostream_usart_kernel_micriumos/iostream_usart_kernel_micriumos.slcp", "text": "Platform - I/O Stream USART on Micrium OS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the I/O Stream service running in a Micrium OS kernel task to demonstrate the use of UART communication over the virtual COM port (VCOM). The application will echo back any characters it receives over the serial connection. The VCOM serial port can be used either over USB or by connecting to port 4902 if the kit is connected via Ethernet. \n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/joystick_baremetal/readme.md"], "description": "This example project uses the Joystick Driver running in a bare metal configuration to demonstrate the use of Joystick driver by printing joystick position after every finite interval over the virtual COM port (VCOM). The VCOM serial port can be used either over  USB or by connecting to port 4902 if the kit is connected via Internet. Important Note: This application only works when Wireless Pro Kit (BRD4002A) is used as  mainboard to mount and connect the compatible Radio Board.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.joystick_baremetal.example/joystick_baremetal/joystick_baremetal.slcp", "text": "Platform - Joystick Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the Joystick Driver running in a bare metal configuration to demonstrate the use of Joystick driver by printing joystick position after every finite interval over the virtual COM port (VCOM). The VCOM serial port can be used either over  USB or by connecting to port 4902 if the kit is connected via Internet.\nImportant Note: This application only works when Wireless Pro Kit (BRD4002A) is used as  mainboard to mount and connect the compatible Radio Board.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/joystick_kernel_freertos/readme.md"], "description": "This example project uses the Joystick driver running in a FreeRTOS kernel  task to demonstrate the use of Joystick driver by printing joystick position  after every finite interval over the virtual COM port (VCOM). The VCOM serial  port can be used either over USB or by connecting to port 4902 if the kit is  connected via Internet.  Important Note: This application only works when Wireless Pro Kit (BRD4002A) is used as  mainboard to mount and connect the compatible Radio Board.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.joystick_kernel_freertos.example/joystick_kernel_freertos/joystick_kernel_freertos.slcp", "text": "Platform - Joystick on FreeRTOS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the Joystick driver running in a FreeRTOS kernel  task to demonstrate the use of Joystick driver by printing joystick position  after every finite interval over the virtual COM port (VCOM). The VCOM serial  port can be used either over USB or by connecting to port 4902 if the kit is  connected via Internet. \nImportant Note: This application only works when Wireless Pro Kit (BRD4002A) is used as  mainboard to mount and connect the compatible Radio Board.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/joystick_kernel_micriumos/readme.md"], "description": "This example project uses the Joystick driver running in a Micrium OS kernel  task to demonstrate the use of Joystick driver by printing joystick position  after every finite interval over the virtual COM port (VCOM). The VCOM serial  port can be used either over USB or by connecting to port 4902 if the kit is  connected via Internet.  Important Note: This application only works when Wireless Pro Kit (BRD4002A) is used as  mainboard to mount and connect the compatible Radio Board.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.joystick_kernel_micriumos.example/joystick_kernel_micriumos/joystick_kernel_micriumos.slcp", "text": "Platform - Joystick on Micrium OS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the Joystick driver running in a Micrium OS kernel  task to demonstrate the use of Joystick driver by printing joystick position  after every finite interval over the virtual COM port (VCOM). The VCOM serial  port can be used either over USB or by connecting to port 4902 if the kit is  connected via Internet. \nImportant Note: This application only works when Wireless Pro Kit (BRD4002A) is used as  mainboard to mount and connect the compatible Radio Board.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/power_manager_baremetal/readme.md"], "description": "This example project demonstrates use of the Power Manager module in a bare metal application.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.power_manager_baremetal.example/power_manager_baremetal/power_manager_baremetal.slcp", "text": "Platform - Power Manager Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates use of the Power Manager module in a bare metal application.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/power_manager_kernel_micriumos/readme.md"], "description": "This example project demonstrates use of the Power Manager module in a Micrium OS kernel task.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.power_manager_kernel_micriumos.example/power_manager_kernel_micriumos/power_manager_kernel_micriumos.slcp", "text": "Platform - Power Manager Kern<PERSON> with Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates use of the Power Manager module in a Micrium OS kernel task.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/simple_button_baremetal/readme.md"], "description": "This example project shows how to toggle LEDs using buttons in a bare-metal environment.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.simple_button_baremetal.example/simple_button_baremetal/simple_button_baremetal.slcp", "text": "Platform - Simple Button Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to toggle LEDs using buttons in a bare-metal environment.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/common/example/simple_button_kernel_micriumos/readme.md"], "description": "This example project shows how to toggle LEDs using buttons in a Micrium OS kernel task.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.simple_button_kernel_micriumos.example/simple_button_kernel_micriumos/simple_button_kernel_micriumos.slcp", "text": "Platform - Simple <PERSON> on Micrium OS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to toggle LEDs using buttons in a Micrium OS kernel task.\n"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": [], "description": "The purpose of the application is to demonstrate  a simple wireless communication between two or more boards. In combination with the Switch sample application it creates a basic light functionality, where the light can be toggled in the Light node. After power up, the node is in ADVERTISE state. It means broadcast messages are sent periodically. After pushing PB1 button, the node is connected, and ready for toggling the light. This is called the READY state. If the Switch module has done the same procedure, light can be toggled from all the boards with pushing BP0 button", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.rail_soc_light_std.example/rail/rail_soc_light_std/rail_soc_light_std.slcp", "text": "RAIL - SoC Light Standards", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The purpose of the application is to demonstrate  a simple wireless communication between two or more boards. In combination with the Switch sample application it creates a basic light functionality, where the light can be toggled in the Light node. After power up, the node is in ADVERTISE state. It means broadcast messages are sent periodically. After pushing PB1 button, the node is connected, and ready for toggling the light. This is called the READY state. If the Switch module has done the same procedure, light can be toggled from all the boards with pushing BP0 button"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/flex/documentation/example/rail/rail_soc_railtest/readme.md"], "description": "The RAILtest application (RAILtest) provides a simple tool for testing the radio and the functionality of the RAIL library via CLI. For more advanced usage, developers must write software against the RAIL library and create a custom radio configuration.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.rail_soc_railtest.example/rail/rail_soc_railtest/rail_soc_railtest.slcp", "text": "RAIL - SoC RAILtest", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The RAILtest application (RAILtest) provides a simple tool for testing the radio and the functionality of the RAIL library via CLI. For more advanced usage, developers must write software against the RAIL library and create a custom radio configuration."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/flex/documentation/example/rail/rail_soc_range_test/readme.md"], "description": "This is the Range Test Sample Application that demonstrates over the air range of the Silicon Labs boards. 5 predefined PHYs can be used for this: BLE: 125kbps, BLE: 500kbps, BLE: 1Mbps, BLE: 2Mbps, IEEE80215.4: 250kbps. This sample app can act as a Transmitter and a Receiver. The role can be selected in the LCD menu. Flashing this app into two separate boards makes it possible to test the features and specification of the radio. The sample also provides an example how the RAIL API can be used. A menu is displayed in the LCD, which allows the user to see the most important information about the settings and also change some of them. The left button navigates in the menu and the right button selects or changes options. The bottom line always shows what the buttons do in the particular context. In Tx Mode, the user can send packets. Packet length defined by the PHY and the number of packets to transmit (from 500 up to continuous) can be set. Output power can be set in the LCD menu, in 0.5dBm steps (power setpoint), between -15..+20dBm. Actual minimum and maximum power may vary in different frequencies as well as the power that is actually set by RAIL. The LCD menu informs the user about the setpoint and the actual power. In the LCD menu, the Power item displays the setpoint first, then actual value. In Rx Mode, the radio listens on the given predefined PHY and inspects the packets received. Packet Error Rate, Bit Error Rate and RSSI of the packets is displayed to inform about the quality of the transmission. Radio related events can be logged on UART on demand. CLI can be used to set and get configuration of the app, and to start and stop it. To get started with CLI please send 'help' with a terminal. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, an USB power bank can be used if portability is needed.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.rail_soc_range_test_std.example/rail/rail_soc_range_test_std/rail_soc_range_test_std.slcp", "text": "RAIL - SoC Range Test BLE and IEEE802.15.4", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is the Range Test Sample Application that demonstrates over the air range of the Silicon Labs boards. 5 predefined PHYs can be used for this: BLE: 125kbps, BLE: 500kbps, BLE: 1Mbps, BLE: 2Mbps, IEEE80215.4: 250kbps. This sample app can act as a Transmitter and a Receiver. The role can be selected in the LCD menu. Flashing this app into two separate boards makes it possible to test the features and specification of the radio. The sample also provides an example how the RAIL API can be used. A menu is displayed in the LCD, which allows the user to see the most important information about the settings and also change some of them. The left button navigates in the menu and the right button selects or changes options. The bottom line always shows what the buttons do in the particular context. In Tx Mode, the user can send packets. Packet length defined by the PHY and the number of packets to transmit (from 500 up to continuous) can be set. Output power can be set in the LCD menu, in 0.5dBm steps (power setpoint), between -15..+20dBm. Actual minimum and maximum power may vary in different frequencies as well as the power that is actually set by RAIL. The LCD menu informs the user about the setpoint and the actual power. In the LCD menu, the Power item displays the setpoint first, then actual value. In Rx Mode, the radio listens on the given predefined PHY and inspects the packets received. Packet Error Rate, Bit Error Rate and RSSI of the packets is displayed to inform about the quality of the transmission. Radio related events can be logged on UART on demand. CLI can be used to set and get configuration of the app, and to start and stop it. To get started with CLI please send 'help' with a terminal. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, an USB power bank can be used if portability is needed."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/flex/documentation/example/rail/rail_soc_simple_trx_std/readme.md"], "description": "This application demonstrates the simplest exchange of transmit and receive operation between two nodes using IEEE Std. 802.15.4 and IEEE Std. 802.15.4g and BLE. Both nodes can send and receive IEEE Std. 802.15.4 and IEEE Std. 802.15.4g or BLE advertising frames. With IEEE Std. 802.15.4 the frame can be sent and received with CSMA/CA, auto-ACK, address filtering and setable Frame Control Frame (available settings broadcast or unicast). With IEEE Std. 802.15.4g the frame can be sent and received with auto-ACK, address filtering and setable Frame Control Frame (available settings broadcast or unicast). On the WSTK, any button press (PB0/PB1) will send a message. LED0 will toggle on message send and LED1 will toggle on message receive. CLI can also be used for sending and showing received messages. To send enter 'send'. By default the received packets will be printed out in terminal. To change standard to BLE, the Flex - RAIL BLE support software component shall be installed under Flex/RAIL/Utilility. With one click this baremetal sample app can be run on an OS, currently MicriumOS and FreeRTOS is supported. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, an USB power bank can be used if portability is needed.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.rail_soc_simple_trx_std.example/rail/rail_soc_simple_trx_std/rail_soc_simple_trx_std.slcp", "text": "RAIL - SoC Simple TRX Standards", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This application demonstrates the simplest exchange of transmit and receive operation between two nodes using IEEE Std. 802.15.4 and IEEE Std. 802.15.4g and BLE. Both nodes can send and receive IEEE Std. 802.15.4 and IEEE Std. 802.15.4g or BLE advertising frames. With IEEE Std. 802.15.4 the frame can be sent and received with CSMA/CA, auto-ACK, address filtering and setable Frame Control Frame (available settings broadcast or unicast). With IEEE Std. 802.15.4g the frame can be sent and received with auto-ACK, address filtering and setable Frame Control Frame (available settings broadcast or unicast). On the WSTK, any button press (PB0/PB1) will send a message. LED0 will toggle on message send and LED1 will toggle on message receive. CLI can also be used for sending and showing received messages. To send enter 'send'. By default the received packets will be printed out in terminal. To change standard to BLE, the Flex - RAIL BLE support software component shall be installed under Flex/RAIL/Utilility. With one click this baremetal sample app can be run on an OS, currently MicriumOS and FreeRTOS is supported. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, an USB power bank can be used if portability is needed."}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": [], "description": "The purpose of the application is to demonstrate a simple wireless communication between two or more boards. In combination with the Light sample application it creates a basic switch functionality, where the light can be toggled in the Light node. After power up, the node is in SCAN state. It means the broadcast messages of the light modules can be captured. After pushing PB1 button, the closest Light module will be connected. This is called the LINK state. If the Light module has done the same procedure, light can be toggled from all the boards with pushing BP0 button", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.rail_soc_switch_std.example/rail/rail_soc_switch_std/rail_soc_switch_std.slcp", "text": "RAIL - SoC Switch Standards", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The purpose of the application is to demonstrate a simple wireless communication between two or more boards. In combination with the Light sample application it creates a basic switch functionality, where the light can be toggled in the Light node. After power up, the node is in SCAN state. It means the broadcast messages of the light modules can be captured. After pushing PB1 button, the closest Light module will be connected. This is called the LINK state. If the Light module has done the same procedure, light can be toggled from all the boards with pushing BP0 button"}, {"imageURL": "bundleentry://460.fwk656015354/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.12/app/flex/documentation/example/rail/rail_soc_range_test/readme.md"], "description": "Range Test BLE and IEEE802.15.4 with Bluetooth connectivity. It runs on top of Micrium OS RTOS and multiprotocol RAIL. This application demonstrates over the air range of the Silicon Labs boards. 5 predefined PHYs can be used for this: BLE: 125kbps, BLE: 500kbps, BLE: 1Mbps, BLE: 2Mbps, IEEE80215.4: 250kbps. This sample app can act as a Transmitter and a Receiver. The role can be selected in the LCD menu. Flashing this app into two separate boards makes it possible to test the features and specification of the radio. The sample also provides an example how the RAIL API can be used. A menu is displayed in the LCD, which allows the user to see the most important information about the settings and also change some of them. The left button navigates in the menu and the right button selects or changes options. The bottom line always shows what the buttons do in the particular context. In Tx Mode, the user can send packets. Packet length defined by the PHY and the number of packets to transmit (from 500 up to continuous) can be set. Output power can be set in the LCD menu, in 0.5dBm steps (power setpoint), between -15..+20dBm. Actual minimum and maximum power may vary in different frequencies as well as the power that is actually set by RAIL. The LCD menu informs the user about the setpoint and the actual power. In the LCD menu, the Power item displays the setpoint first, then actual value. In Rx Mode, the radio listens on the given predefined PHY and inspects the packets received. Packet Error Rate, Bit Error Rate and RSSI of the packets is displayed to inform about the quality of the transmission. Radio related events can be logged on UART on demand. CLI can be used to set and get configuration of the app, and to start and stop it. To get started with CLI please send 'help' with a terminal. Wireless Gecko mobile app can also be used to control this application over Bluetooth. Currently MicriumOS and FreeRTOS is supported by this sample app. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, an USB power bank can be used if portability is needed.", "id": "template.uc.com.silabs.sdk.stack.super:4.1.2._-********.rail_bt_dmp_soc_range_test_std.example/rail/rail_bt_dmp_soc_range_test_std/rail_bt_dmp_soc_range_test_std.slcp", "text": "RAIL Bluetooth DMP - SoC Range Test BLE and IEEE802.15.4", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Range Test BLE and IEEE802.15.4 with Bluetooth connectivity. It runs on top of Micrium OS RTOS and multiprotocol RAIL. This application demonstrates over the air range of the Silicon Labs boards. 5 predefined PHYs can be used for this: BLE: 125kbps, BLE: 500kbps, BLE: 1Mbps, BLE: 2Mbps, IEEE80215.4: 250kbps. This sample app can act as a Transmitter and a Receiver. The role can be selected in the LCD menu. Flashing this app into two separate boards makes it possible to test the features and specification of the radio. The sample also provides an example how the RAIL API can be used. A menu is displayed in the LCD, which allows the user to see the most important information about the settings and also change some of them. The left button navigates in the menu and the right button selects or changes options. The bottom line always shows what the buttons do in the particular context. In Tx Mode, the user can send packets. Packet length defined by the PHY and the number of packets to transmit (from 500 up to continuous) can be set. Output power can be set in the LCD menu, in 0.5dBm steps (power setpoint), between -15..+20dBm. Actual minimum and maximum power may vary in different frequencies as well as the power that is actually set by RAIL. The LCD menu informs the user about the setpoint and the actual power. In the LCD menu, the Power item displays the setpoint first, then actual value. In Rx Mode, the radio listens on the given predefined PHY and inspects the packets received. Packet Error Rate, Bit Error Rate and RSSI of the packets is displayed to inform about the quality of the transmission. Radio related events can be logged on UART on demand. CLI can be used to set and get configuration of the app, and to start and stop it. To get started with CLI please send 'help' with a terminal. Wireless Gecko mobile app can also be used to control this application over Bluetooth. Currently MicriumOS and FreeRTOS is supported by this sample app. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, an USB power bank can be used if portability is needed."}], "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [{"futureCount": 50, "anySelected": false, "id": 0, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": false}, {"futureCount": 2, "anySelected": false, "id": 1, "filters": [], "title": "Bluetooth Mesh", "parentId": 0, "selected": false}, {"futureCount": 11, "anySelected": false, "id": 2, "filters": [], "title": "Connect", "parentId": 0, "selected": false}, {"futureCount": 6, "anySelected": false, "id": 3, "filters": [], "title": "RAIL", "parentId": 0, "selected": false}], "title": "Wireless Technology", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [{"futureCount": 1, "anySelected": false, "id": 0, "filters": [], "title": "Host", "parentId": 1, "selected": false}, {"futureCount": 12, "anySelected": false, "id": 1, "filters": [], "title": "NCP", "parentId": 1, "selected": false}, {"futureCount": 2, "anySelected": false, "id": 2, "filters": [], "title": "RCP", "parentId": 1, "selected": false}, {"futureCount": 88, "anySelected": false, "id": 3, "filters": [], "title": "SoC", "parentId": 1, "selected": false}], "title": "Device Type", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 2, "filters": [{"futureCount": 4, "anySelected": false, "id": 0, "filters": [], "title": "Amazon", "parentId": 2, "selected": false}], "title": "Ecosystem", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 3, "filters": [{"futureCount": 14, "anySelected": false, "id": 0, "filters": [], "title": "32-bit MCU", "parentId": 3, "selected": false}, {"futureCount": 26, "anySelected": false, "id": 1, "filters": [], "title": "Bootloader", "parentId": 3, "selected": false}, {"futureCount": 1, "anySelected": false, "id": 2, "filters": [], "title": "Operating Systems", "parentId": 3, "selected": false}], "title": "MCU", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 4, "filters": [{"futureCount": 6, "anySelected": false, "id": 0, "filters": [], "title": "Multiprotocol", "parentId": 4, "selected": false}], "title": "Capability", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 5, "filters": [{"futureCount": 62, "anySelected": false, "id": 0, "filters": [], "title": "Advanced", "parentId": 5, "selected": false}, {"futureCount": 41, "anySelected": false, "id": 1, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": false}], "title": "Project Difficulty", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 6, "filters": [{"futureCount": 4, "anySelected": false, "id": 0, "filters": [], "title": "Alpha", "parentId": 6, "selected": false}, {"futureCount": 8, "anySelected": false, "id": 1, "filters": [], "title": "Beta", "parentId": 6, "selected": false}, {"futureCount": 19, "anySelected": false, "id": 2, "filters": [], "title": "Experimental", "parentId": 6, "selected": false}, {"futureCount": 2, "anySelected": false, "id": 3, "filters": [], "title": "None Specified", "parentId": 6, "selected": false}, {"futureCount": 74, "anySelected": false, "id": 4, "filters": [], "title": "Production", "parentId": 6, "selected": false}], "title": "Quality", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 7, "filters": [{"futureCount": 88, "anySelected": false, "id": 0, "filters": [], "title": "Gecko SDK Suite v4.1.2", "parentId": 7, "selected": false}, {"futureCount": 19, "anySelected": false, "id": 1, "filters": [], "title": "bluetooth_applications", "parentId": 7, "selected": false}], "title": "Provider", "parentId": -1, "selected": false}], "totalCount": 107}