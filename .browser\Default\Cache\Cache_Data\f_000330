"use strict";(globalThis["webpackChunkinstaller"]=globalThis["webpackChunkinstaller"]||[]).push([[396],{9881:(e,t,n)=>{n.r(t),n.d(t,{default:()=>Ut});var s=n(9835),l=n(6970);const i=e=>((0,s.dD)("data-v-48afe300"),e=e(),(0,s.Cn)(),e),o={class:"page"},a=i((()=>(0,s._)("br",null,null,-1))),d={class:"extensions-container"},r={class:"extensions-title"},c={class:"row"},u={key:0},p={class:"impacted-extensions-container"},g={key:0,class:"extensions-title"},m={key:1,class:"extensions-title"},h={key:0},w={key:0},x={key:1},k={key:1},f={class:"release-notes"},b={class:"release-notes"},v={key:1},D=i((()=>(0,s._)("div",{class:"text-h6"},[(0,s._)("b",null,"Extension Removal Warning")],-1))),I=i((()=>(0,s._)("br",null,null,-1))),_=i((()=>(0,s._)("br",null,null,-1))),E=i((()=>(0,s._)("br",null,null,-1))),y=i((()=>(0,s._)("br",null,null,-1)));function C(e,t,n,i,C,S){const q=(0,s.up)("UpdateMessage"),V=(0,s.up)("q-badge"),K=(0,s.up)("q-btn"),U=(0,s.up)("q-item-section"),W=(0,s.up)("q-item"),Z=(0,s.up)("installation-row"),$=(0,s.up)("ExtensionsContainer"),P=(0,s.up)("ExtensionsInfoContainer"),L=(0,s.up)("ExtensionVersionSelector"),B=(0,s.up)("confirmation-dialog"),z=(0,s.up)("q-dialog"),Q=(0,s.up)("expandable"),T=(0,s.up)("Tile"),A=(0,s.up)("SdkContainer"),j=(0,s.up)("expandable-tile"),N=(0,s.up)("location-selector"),R=(0,s.up)("q-separator"),O=(0,s.up)("version-selector"),H=(0,s.up)("q-space"),M=(0,s.up)("q-checkbox"),F=(0,s.up)("q-card"),Y=(0,s.up)("q-card-section"),G=(0,s.up)("q-scroll-area"),X=(0,s.up)("PackageManagerLoadingAnimation"),J=(0,s.up)("q-card-actions"),ee=(0,s.Q2)("-if"),te=(0,s.Q2)("close-popup");return(0,s.wg)(),(0,s.iD)(s.HY,null,[(0,s._)("div",o,[(0,s.Wm)(q,{msg:C.msg},null,8,["msg"]),!1===S.showBusy?((0,s.wg)(),(0,s.j4)(G,{key:0,id:"scrollArea",style:(0,l.j5)(S.getControlHeight),"onHook:mounted":S.onResize},{default:(0,s.w5)((()=>[((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(S.filteredSdkCategories(e.sdkCategories,!1),(n=>((0,s.wg)(),(0,s.j4)(T,{key:n.id,icon:n.imageUrl,title:n.label,description:n.desc},(0,s.Nv)({"primary-action":(0,s.w5)((()=>[(0,s.wy)((0,s.Wm)(K,{"no-caps":"",flat:"",label:e.$t("InstallNew"),class:(0,l.C_)(e.updatePackages.productUpdates?"button-disable":"button-secondary"),disable:!!e.updatePackages.productUpdates,onClick:e=>S.openInstallationDialog(n)},null,8,["label","class","disable","onClick"]),[[ee,n.installable.length>0]])])),default:(0,s.w5)((()=>[!n.legacy&&n.installed.length>0?((0,s.wg)(),(0,s.j4)(Q,{key:0,label:e.$t("Installations"),defaultExpanded:!0},{default:(0,s.w5)((()=>[((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(S.sortedInstalledSdkCategories(n.installed),(i=>((0,s.wg)(),(0,s.iD)("div",{key:i.id},[(0,s.Wm)(Z,{prefix:e.$t("Location")+":",leftLabel:i.location,rightLabel:i.version,showBadge:!S.isLatestSdk(i,n.latest)&&!S.isP2Repository(n),disableDotsButton:e.updatePackages.productUpdates},{"right-action":(0,s.w5)((()=>[])),default:(0,s.w5)((()=>[(0,s._)("div",null,[S.isP2Repository(n)?(0,s.wy)(((0,s.wg)(),(0,s.j4)(W,{key:0,clickable:"",onClick:e=>S.openReleaseNoteDialog(i)},{default:(0,s.w5)((()=>[(0,s.Wm)(U,null,{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(e.$t("ReleaseNotes")),1)])),_:1})])),_:2},1032,["onClick"])),[[te]]):(0,s.kq)("",!0),S.isLatestSdk(i,n.latest)||S.isP2Repository(n)?(0,s.kq)("",!0):(0,s.wy)(((0,s.wg)(),(0,s.j4)(W,{key:1,clickable:"",onClick:e=>S.openVersionDialog(i,n,!0)},{default:(0,s.w5)((()=>[S.isLatestSdk(i,n.latest)?((0,s.wg)(),(0,s.j4)(U,{key:0},{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(e.$t("Update")),1)])),_:1})):((0,s.wg)(),(0,s.j4)(U,{key:1,style:{color:"var(--q-negative)"}},{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(e.$t("Update")),1)])),_:1}))])),_:2},1032,["onClick"])),[[te]]),S.isP2Repository(n)?(0,s.kq)("",!0):(0,s.wy)(((0,s.wg)(),(0,s.j4)(W,{key:2,clickable:"",onClick:e=>S.openVersionDialog(i,n)},{default:(0,s.w5)((()=>[(0,s.Wm)(U,null,{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(e.$t("ChangeVersionLabel")),1)])),_:1})])),_:2},1032,["onClick"])),[[te]]),(0,s.wy)(((0,s.wg)(),(0,s.j4)(W,{clickable:"",onClick:e=>S.confirmUnInstall(i,n.id)},{default:(0,s.w5)((()=>[(0,s.Wm)(U,null,{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(e.$t("Uninstall")),1)])),_:1})])),_:2},1032,["onClick"])),[[te]])])])),_:2},1032,["prefix","leftLabel","rightLabel","showBadge","disableDotsButton"]),S.showInstallableExtensions(i)?((0,s.wg)(),(0,s.j4)($,{key:0,extensionsList:S.getInstallableExtensions(i),currentSDK:i,showTopTxt:!1,disableButtons:e.updatePackages.productUpdates,onOpenExtensionInstallDialog:S.confirmExtInstallDialog,onOpenExtensionVersionDialog:S.openExtVersionDialog,onConfirmExtensionUninstall:S.confirmExtUnInstall},null,8,["extensionsList","currentSDK","disableButtons","onOpenExtensionInstallDialog","onOpenExtensionVersionDialog","onConfirmExtensionUninstall"])):(0,s.kq)("",!0),i.extensionsInfo?((0,s.wg)(),(0,s.j4)(P,{key:1,extensionsList:i.extensionsInfo,id:"ExtensionsInfoInstalled"},null,8,["extensionsList"])):(0,s.kq)("",!0),C.extensionVersionDialog?((0,s.wg)(),(0,s.j4)(z,{key:2,modelValue:C.extensionVersionDialog,"onUpdate:modelValue":t[1]||(t[1]=e=>C.extensionVersionDialog=e),persistent:""},{default:(0,s.w5)((()=>[(0,s.Wm)(B,{class:"install-dialog",onConfirmClicked:t[0]||(t[0]=e=>S.changeExtensionVersion(C.sdkForExtensionChangeVersion.id,C.selectedInstalledExtension.id,C.selectedExtensionVersion)),title:e.$t("ChangeVersionTitle")+C.selectedInstalledExtension.label,confirmDisabled:C.selectedInstalledExtension.installedVersion===C.selectedExtensionVersion},{default:(0,s.w5)((()=>[(0,s.Wm)(L,{onSelected:S.setSelectedExtensionVersion,options:C.selectedInstalledExtension.installableVersions,selected:C.selectedExtensionVersion,installedVersion:C.selectedInstalledExtension.installedVersion},null,8,["onSelected","options","selected","installedVersion"])])),_:1},8,["title","confirmDisabled"])])),_:1},8,["modelValue"])):(0,s.kq)("",!0)])))),128))])),_:2},1032,["label"])):(0,s.kq)("",!0)])),_:2},[n.showNew?{name:"title",fn:(0,s.w5)((()=>[(0,s.Wm)(V,{color:"negative",align:"top"},{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(e.$t("New")),1)])),_:1})])),key:"0"}:void 0]),1032,["icon","title","description"])))),128)),(0,s.Wm)(j,{label:e.$t("LegacySdks"),defaultExpanded:!1},{default:(0,s.w5)((()=>[((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(S.filteredSdkCategories(e.sdkCategories,!0),(t=>((0,s.wg)(),(0,s.iD)("div",{key:t.id},[a,(0,s.Wm)(A,{sdkCategory:t,expanded:!1,disableButtons:e.updatePackages.productUpdates},null,8,["sdkCategory","disableButtons"])])))),128))])),_:1},8,["label"]),(0,s.Wm)(z,{modelValue:C.installationDialog,"onUpdate:modelValue":t[4]||(t[4]=e=>C.installationDialog=e)},{default:(0,s.w5)((()=>[C.selectedSDK?((0,s.wg)(),(0,s.j4)(B,{key:0,class:"install-dialog",onConfirmClicked:t[3]||(t[3]=t=>S.installNewSDK(C.selectedSDK.id,C.selectedSDK.version,e.verifyLocation.location)),title:e.$t("InstallNewTitle")+C.selectedSDK.label,confirmDisabled:!e.verifyLocation.isValid},{default:(0,s.w5)((()=>["git"===C.selectedSDKCategory.repositoryType?((0,s.wg)(),(0,s.j4)(N,{key:0,defaultLocation:C.selectedSDK.preferredLocation},null,8,["defaultLocation"])):(0,s.kq)("",!0),(0,s.Wm)(R),(0,s.Wm)(O,{onSelected:S.setSelectedSDKAndExtensions,options:C.selectedSDKCategory.installable,selected:C.selectedSDK,installedSdks:C.selectedSDKCategory.installed},null,8,["onSelected","options","selected","installedSdks"]),(0,s.Wm)(H),(0,s._)("div",d,[(0,s._)("p",r,[(0,s._)("strong",null,(0,l.zw)(e.$t("Extensions")),1)]),((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(C.selectedSDK.extensions,(e=>((0,s.wg)(),(0,s.j4)(F,{key:e.id,class:"q-my-md q-lg extension-card",bordered:"",width:"auto"},{default:(0,s.w5)((()=>[(0,s._)("div",c,[(0,s.Wm)(M,{color:"primary",modelValue:C.selectedExtensionsToInstall,"onUpdate:modelValue":t[2]||(t[2]=e=>C.selectedExtensionsToInstall=e),id:e.id,val:e.id,label:S.getNewInstallableExtensionLabel(e)},null,8,["modelValue","id","val","label"]),(0,s.Wm)(H),(0,s.kq)("",!0)])])),_:2},1024)))),128)),C.selectedSDK.extensionsInfo?((0,s.wg)(),(0,s.iD)("div",u,[(0,s.Wm)(P,{extensionsList:C.selectedSDK.extensionsInfo,addCard:!0},null,8,["extensionsList"])])):(0,s.kq)("",!0)])])),_:1},8,["title","confirmDisabled"])):(0,s.kq)("",!0)])),_:1},8,["modelValue"]),C.versionDialog?((0,s.wg)(),(0,s.j4)(z,{key:0,modelValue:C.versionDialog,"onUpdate:modelValue":t[8]||(t[8]=e=>C.versionDialog=e),persistent:""},{default:(0,s.w5)((()=>[(0,s.Wm)(B,{class:"install-dialog",onConfirmClicked:t[7]||(t[7]=e=>S.changeSDKVersion(C.installedSDK.id,C.selectedSDK.version)),title:e.$t("ChangeVersionTitle")+C.installedSDK.label,confirmDisabled:C.installedSDK.version===C.selectedSDK.version,showUserConfirmation:S.getShowUserConfirmation},{default:(0,s.w5)((()=>[(0,s.Wm)(O,{onSelected:S.setSelectedSDKAndImpactedExtensions,isDirty:S.setSelectedSDKAndImpactedExtensions.dirty&&C.installedSDK.version!==C.selectedSDK.version,options:C.selectedSDKCategory.installable,selected:C.selectedSDK,installedSdks:C.selectedSDKCategory.installed},null,8,["onSelected","isDirty","options","selected","installedSdks"]),(0,s._)("div",p,[(0,s._)("div",null,[S.selectedSdkIsInstalled()?(0,s.kq)("",!0):((0,s.wg)(),(0,s.iD)("p",g,[(0,s._)("strong",null,(0,l.zw)(e.$t("ImpactedExtensions")),1)])),S.selectedSdkIsInstalled()?((0,s.wg)(),(0,s.iD)("p",m,[(0,s._)("strong",null,(0,l.zw)(e.$t("Extensions")),1)])):(0,s.kq)("",!0)]),S.selectedSdkIsInstalled()?(0,s.kq)("",!0):((0,s.wg)(),(0,s.iD)("div",h,[((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(S.getInstallableImpactedExtensions,(e=>((0,s.wg)(),(0,s.j4)(F,{key:e.id,class:"q-my-md q-lg extension-card",bordered:"",width:"auto"},{default:(0,s.w5)((()=>[(0,s.Wm)(M,{color:"primary",modelValue:C.selectedExtensionsToInstall,"onUpdate:modelValue":[t[5]||(t[5]=e=>C.selectedExtensionsToInstall=e),t[6]||(t[6]=e=>{S.onImpactedExtensionSelected(e)})],id:e.id,val:e.id,label:e.impact,disable:C.disableImpactedExtensions},null,8,["modelValue","id","val","label","disable"]),(0,s.Wm)(H)])),_:2},1024)))),128)),((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(S.getInstalledImpactedExtensions,(e=>((0,s.wg)(),(0,s.j4)(F,{key:e.id,class:"q-my-md q-lg extension-card",bordered:"",width:"auto"},{default:(0,s.w5)((()=>[e.toBeDeleted?((0,s.wg)(),(0,s.iD)("div",w,[(0,s.Wm)(Y,{horizontal:"",style:{"align-items":"center"}},{default:(0,s.w5)((()=>[(0,s.Wm)(K,{icon:C.mdiAlertOutline,flat:"",round:"",dense:"",color:"warning",onClick:t=>S.openExtensionRemovePopup(e),size:"18px"},null,8,["icon","onClick"]),(0,s._)("div",null,(0,l.zw)(e.impact),1)])),_:2},1024)])):((0,s.wg)(),(0,s.iD)("div",x,(0,l.zw)(e.impact),1))])),_:2},1024)))),128))])),S.selectedSdkIsInstalled()?((0,s.wg)(),(0,s.iD)("div",k,[((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(S.getInstalledExtensionsForInstalledSdk,(e=>((0,s.wg)(),(0,s.j4)(F,{key:e.id,class:"q-my-md q-lg extension-card",bordered:"",width:"auto"},{default:(0,s.w5)((()=>[(0,s._)("div",null,(0,l.zw)(e.label)+" - "+(0,l.zw)(e.installedVersion),1)])),_:2},1024)))),128))])):(0,s.kq)("",!0)])])),_:1},8,["title","confirmDisabled","showUserConfirmation"])])),_:1},8,["modelValue"])):(0,s.kq)("",!0),C.releaseNoteDialog?((0,s.wg)(),(0,s.j4)(z,{key:1,modelValue:C.releaseNoteDialog,"onUpdate:modelValue":t[9]||(t[9]=e=>C.releaseNoteDialog=e)},{default:(0,s.w5)((()=>[(0,s.Wm)(B,{title:e.$t("ReleaseNotesFor")+C.installedSDK.label,confirmLabel:"Ok",cancelButton:!1},{default:(0,s.w5)((()=>[(0,s.Wm)(Y,null,{default:(0,s.w5)((()=>[(0,s._)("div",f,(0,l.zw)(C.installedSDK.releaseNote),1)])),_:1})])),_:1},8,["title"])])),_:1},8,["modelValue"])):(0,s.kq)("",!0),C.extensionReleaseNoteDialog?((0,s.wg)(),(0,s.j4)(z,{key:2,modelValue:C.extensionReleaseNoteDialog,"onUpdate:modelValue":t[10]||(t[10]=e=>C.extensionReleaseNoteDialog=e)},{default:(0,s.w5)((()=>[(0,s.Wm)(B,{title:e.$t("ReleaseNotesFor")+this.selectedExtension.label,confirmLabel:"Ok",cancelButton:!1},{default:(0,s.w5)((()=>[(0,s.Wm)(Y,null,{default:(0,s.w5)((()=>[(0,s._)("div",b,(0,l.zw)(this.selectedExtension.desc),1)])),_:1})])),_:1},8,["title"])])),_:1},8,["modelValue"])):(0,s.kq)("",!0)])),_:1},8,["style","onHook:mounted"])):(0,s.kq)("",!0),!0===S.showBusy?((0,s.wg)(),(0,s.iD)("div",v,[(0,s.Wm)(X)])):(0,s.kq)("",!0)]),(0,s.Wm)(z,{modelValue:C.extensionRemovePopup,"onUpdate:modelValue":t[11]||(t[11]=e=>C.extensionRemovePopup=e)},{default:(0,s.w5)((()=>[(0,s.Wm)(F,null,{default:(0,s.w5)((()=>[(0,s.Wm)(Y,null,{default:(0,s.w5)((()=>[D])),_:1}),(0,s.Wm)(Y,{class:"q-pt-none"},{default:(0,s.w5)((()=>[(0,s.Uk)(" You currently have version "+(0,l.zw)(C.extensionInfo.extensionVersion)+" of the "+(0,l.zw)(C.extensionInfo.extensionName)+" extension installed with the SDK "+(0,l.zw)(C.extensionInfo.extensionInfoSDKVersion)+". Changing the SDK version to "+(0,l.zw)(C.extensionInfo.extensionInfoNewSDKVersion)+" will cause the "+(0,l.zw)(C.extensionInfo.extensionName)+" extension to be removed. ",1),I,_,(0,s.Uk)(" If you have created projects that require "+(0,l.zw)(C.extensionInfo.extensionName)+", this version change will remove the extension, and break those projects. If you need this extension, install another copy of the SDK "+(0,l.zw)(C.extensionInfo.extensionInfoNewSDKVersion)+" side by side. ",1),E,y,(0,s.Uk)(" If you proceed with the version change, please acknowledge that you understand the consequences by checking the box and finish the upgrade. ")])),_:1}),(0,s.Wm)(J,{align:"right"},{default:(0,s.w5)((()=>[(0,s.wy)((0,s.Wm)(K,{flat:"",label:"OK",color:"primary"},null,512),[[te]])])),_:1})])),_:1})])),_:1},8,["modelValue"])],64)}n(9665);var S=n(3100),q=n(4853),V=n(4480),K=n(1957);const U=e=>((0,s.dD)("data-v-4400ea66"),e=e(),(0,s.Cn)(),e),W={class:"row items-center col"},Z={class:"label-style label-pos"},$={class:"right col"},P={class:"row items-center justify-end"},L={key:0},B={class:"pkg-col"},z=U((()=>(0,s._)("br",null,null,-1)));function Q(e,t,n,i,o,a){const d=(0,s.up)("q-badge"),r=(0,s.up)("q-btn"),c=(0,s.up)("q-icon"),u=(0,s.up)("Package"),p=(0,s.up)("q-slide-transition");return(0,s.wg)(),(0,s.iD)("div",null,[(0,s._)("div",null,[(0,s._)("div",{class:(0,l.C_)(["card",{expandedCard:o.headerExpanded&&(n.sdkCategory.installable.length>0||n.sdkCategory.installed.length>0)}])},[(0,s._)("div",{class:"row items-center sdkGrantedHeaderTile",onClick:t[1]||(t[1]=e=>o.headerExpanded=!o.headerExpanded)},[(0,s._)("div",W,[(0,s._)("div",Z,(0,l.zw)(n.sdkCategory.label),1),a.updateableCnt(n.sdkCategory.installed)>0?((0,s.wg)(),(0,s.j4)(d,{key:0,class:"self-start",color:"negative",align:"top"},{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(a.updateableCnt(n.sdkCategory.installed)),1)])),_:1})):(0,s.kq)("",!0),(0,s._)("div",$,[(0,s._)("div",P,[(0,s.Wm)(r,{class:(0,l.C_)(n.disableButtons?"mass-install buttonDisable":"mass-install"),disable:!!n.disableButtons,ref:"installButton",onClickCapture:t[0]||(t[0]=(0,K.iM)((e=>a.installOrUninstallAll(n.sdkCategory)),["stop"])),id:n.sdkCategory.id,"no-caps":"",label:a.installButtonLabel(n.sdkCategory)},null,8,["class","disable","id","label"]),(0,s.Wm)(c,{class:(0,l.C_)(["iconChevronClass",{invisible:n.sdkCategory.installable.length<=0&&n.sdkCategory.installed.length<=0}]),name:o.headerExpanded?e.mdiChevronDown:e.mdiChevronUp},null,8,["class","name"])])])])]),n.sdkCategory.installable.length>0||n.sdkCategory.installed.length>0?((0,s.wg)(),(0,s.iD)("div",L,[(0,s.Wm)(p,null,{default:(0,s.w5)((()=>[(0,s.wy)((0,s._)("div",null,[(0,s._)("div",B,[((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(a.allPackages(),(e=>((0,s.wg)(),(0,s.iD)("div",{key:e.id},[(0,s.Wm)(u,{studioPackage:e,categories:o.categories},null,8,["studioPackage","categories"]),z])))),128))])],512),[[K.F8,o.headerExpanded]])])),_:1})])):(0,s.kq)("",!0)],2)])])}var T=n(2670),A=n(6664),j=n(6412);const N={name:"SdkContainer",mixins:[j.Z],props:["sdkCategory","expanded","disableButtons"],data(){return{categories:["sdk","stack"],headerExpanded:this.expanded}},created(){this.mdiChevronDown=T.CW,this.mdiChevronUp=T.Waq},methods:{installOrUninstallAll(e){"Install All"===this.$refs.installButton.label?this.doInstallAll(e):this.doUninstallAll(e)},doInstallAll(e){let t=[];e.installable.forEach((e=>{t.push(e.id)})),this.installMultiplePackages(t,this.categories)},doUninstallAll(e){let t={packages:[]};e.installed.forEach((e=>{t.packages.push({id:e.id,version:e.version,location:""})})),this.$store.dispatch("install/deleteSDK",t)},installButtonLabel(e){return 0!=e.installable.length?"Install All":"Uninstall All"},sdkPackageToClasicPackage(e,t){return{...e,hasIncludedPkgIds:!1,isInstalled:t,installEnabled:!0,installVisible:this.installVisible(e),installState:this.calcInstallState(e,t),releaseNotes:e.releaseNote,featurePackageId:e.id}},calcInstallState(e,t){return t?"updateable"in e&&e.updateable?"Update":"Uninstall":"Install"},installVisible(e){return!("dependency"in e)||!e.dependency},allPackages(){let e=this.sdkCategory.installed.map((e=>this.sdkPackageToClasicPackage(e,!0))).concat(this.sdkCategory.installable.map((e=>this.sdkPackageToClasicPackage(e,!1))));return e},updateableCnt(e){let t=0;return e&&e.length>0&&e.forEach((e=>{e.updateable&&t++})),t}},components:{Package:A.Z}};var R=n(1639),O=n(990),H=n(8879),M=n(2857),F=n(9003),Y=n(9984),G=n.n(Y);const X=(0,R.Z)(N,[["render",Q],["__scopeId","data-v-4400ea66"]]),J=X;G()(N,"components",{QBadge:O.Z,QBtn:H.Z,QIcon:M.Z,QSlideTransition:F.Z});var ee=n(5358);const te={class:"expandable-container"},ne={class:"row"};function se(e,t,n,i,o,a){const d=(0,s.up)("q-icon"),r=(0,s.up)("q-slide-transition");return(0,s.wg)(),(0,s.iD)("div",te,[(0,s._)("div",ne,[(0,s._)("div",{class:"exapandable-header",onClick:t[0]||(t[0]=e=>o.isExpanded=!o.isExpanded)},[(0,s.Wm)(d,{name:o.isExpanded?e.mdiMenuDown:e.mdiMenuRight},null,8,["name"]),(0,s._)("span",null,[(0,s._)("strong",null,(0,l.zw)(n.label),1)])])]),(0,s.Wm)(r,null,{default:(0,s.w5)((()=>[(0,s.wy)((0,s._)("div",null,[(0,s.WI)(e.$slots,"default",{},void 0,!0)],512),[[K.F8,o.isExpanded]])])),_:3})])}const le={name:"Expandable",props:["label","defaultExpanded"],components:{},data(){return{isExpanded:!!this.defaultExpanded}},created(){this.mdiMenuRight=T.ozb,this.mdiMenuDown=T.iW9}},ie=(0,R.Z)(le,[["render",se],["__scopeId","data-v-50d3e247"]]),oe=ie;G()(le,"components",{QIcon:M.Z,QSlideTransition:F.Z});const ae=e=>((0,s.dD)("data-v-83974524"),e=e(),(0,s.Cn)(),e),de={class:"expandable-tile"},re={class:"row"},ce={class:"label-style"},ue={class:"right"},pe={class:"row items-center justify-end"},ge=ae((()=>(0,s._)("br",null,null,-1)));function me(e,t,n,i,o,a){const d=(0,s.up)("q-icon"),r=(0,s.up)("q-slide-transition"),c=(0,s.up)("Tile");return(0,s.wg)(),(0,s.j4)(c,null,{default:(0,s.w5)((()=>[(0,s._)("div",de,[(0,s._)("div",re,[(0,s._)("div",{class:"expandable-header",onClick:t[0]||(t[0]=e=>o.isExpanded=!o.isExpanded)},[(0,s._)("div",ce,(0,l.zw)(n.label),1),(0,s._)("div",ue,[(0,s._)("div",pe,[(0,s.Wm)(d,{class:"iconChevronClass",name:o.isExpanded?e.mdiChevronDown:e.mdiChevronUp},null,8,["name"])])]),ge])]),(0,s.Wm)(r,null,{default:(0,s.w5)((()=>[(0,s.wy)((0,s._)("div",null,[(0,s.WI)(e.$slots,"default",{},void 0,!0)],512),[[K.F8,o.isExpanded]])])),_:3})])])),_:3})}const he={props:["label","defaultExpanded"],components:{Tile:V.Z},data(){return{isExpanded:!!this.defaultExpanded}},created(){this.mdiChevronDown=T.CW,this.mdiChevronUp=T.Waq}},we=(0,R.Z)(he,[["render",me],["__scopeId","data-v-83974524"]]),xe=we;G()(he,"components",{QIcon:M.Z,QSlideTransition:F.Z});const ke={class:"row_container"};function fe(e,t,n,i,o,a){const d=(0,s.up)("q-badge"),r=(0,s.up)("q-list"),c=(0,s.up)("q-menu"),u=(0,s.up)("q-btn");return(0,s.wg)(),(0,s.iD)("div",ke,[(0,s._)("div",null,[(0,s.Uk)((0,l.zw)(n.prefix)+" ",1),(0,s._)("span",null,(0,l.zw)(n.leftLabel),1)]),(0,s._)("div",null,[(0,s._)("span",null,(0,l.zw)(n.rightLabel),1),(0,s.WI)(e.$slots,"right-action",{},void 0,!0),e.$slots.default?((0,s.wg)(),(0,s.j4)(u,{key:0,icon:o.dotsIcon,class:(0,l.C_)(n.disableDotsButton?"dotsButton button-disable":"dotsButton"),flat:"",padding:"sm",disable:!!n.disableDotsButton},{default:(0,s.w5)((()=>[n.showBadge?((0,s.wg)(),(0,s.j4)(d,{key:0,color:"negative",rounded:"",floating:""})):(0,s.kq)("",!0),(0,s.Wm)(c,null,{default:(0,s.w5)((()=>[(0,s.Wm)(r,{style:{"min-width":"100px"}},{default:(0,s.w5)((()=>[(0,s.WI)(e.$slots,"default",{},void 0,!0)])),_:3})])),_:3})])),_:3},8,["icon","class","disable"])):(0,s.kq)("",!0)])])}const be={props:["prefix","leftLabel","rightLabel","showBadge","disableDotsButton"],data(){return{dotsIcon:T.XMI}}};var ve=n(6362),De=n(3246);const Ie=(0,R.Z)(be,[["render",fe],["__scopeId","data-v-8a4fddd2"]]),_e=Ie;G()(be,"components",{QBtn:H.Z,QBadge:O.Z,QMenu:ve.Z,QList:De.Z});var Ee=n(3571);const ye=e=>((0,s.dD)("data-v-79468cda"),e=e(),(0,s.Cn)(),e),Ce={key:0,class:"q-ml-sm info"},Se=ye((()=>(0,s._)("strong",null,"Warning! Local changes to your installation will be discarded.",-1))),qe=ye((()=>(0,s._)("div",{class:"q-ml-sm"},[(0,s._)("strong",null,"Versions:")],-1))),Ve=ye((()=>(0,s._)("div",{class:"q-ml-sm"},[(0,s._)("strong",null,"Release Notes:")],-1))),Ke={class:"format"};function Ue(e,t,n,i,o,a){const d=(0,s.up)("q-icon"),r=(0,s.up)("q-select"),c=(0,s.up)("q-card-section"),u=(0,s.up)("q-scroll-area");return(0,s.wg)(),(0,s.iD)("div",null,[(0,s.Wm)(c,null,{default:(0,s.w5)((()=>[n.isDirty?((0,s.wg)(),(0,s.iD)("div",Ce,[(0,s.Wm)(d,{name:o.infoIcon},null,8,["name"]),Se])):(0,s.kq)("",!0),qe,(0,s.Wm)(r,{id:"select-version",outlined:"",dense:"","options-dense":"",modelValue:o.model,"onUpdate:modelValue":[t[0]||(t[0]=e=>o.model=e),a.selectVersion],options:n.options,"option-value":e=>e.id,"option-label":e=>a.getVersionAndInstalledText(e)},null,8,["modelValue","options","option-value","option-label","onUpdate:modelValue"])])),_:1}),(0,s.Wm)(c,null,{default:(0,s.w5)((()=>[Ve,a.showReleaseNotes(n.selected)?((0,s.wg)(),(0,s.j4)(u,{key:0,class:"releaseNotesArea"},{default:(0,s.w5)((()=>[(0,s._)("div",Ke,(0,l.zw)(n.selected.releaseNote),1)])),_:1})):(0,s.kq)("",!0)])),_:1})])}const We={props:["options","selected","isDirty","installedSdks"],data(){return{model:this.selected,closeIcon:T.r5M,infoIcon:T._gM}},methods:{selectVersion(e){this.$emit("selected",e)},showReleaseNotes(e){return!(!e.releaseNote||""===e.releaseNote)},getVersionAndInstalledText(e){let t=this.installedSdks.find((t=>t.version===e.version));return void 0==t?e.version:e.version+" (installed)"}}};var Ze=n(3190),$e=n(1362),Pe=n(6663);const Le=(0,R.Z)(We,[["render",Ue],["__scopeId","data-v-79468cda"]]),Be=Le;G()(We,"components",{QCardSection:Ze.Z,QIcon:M.Z,QSelect:$e.Z,QScrollArea:Pe.Z});const ze=e=>((0,s.dD)("data-v-e9030516"),e=e(),(0,s.Cn)(),e),Qe={key:0,class:"q-ml-sm info"},Te=ze((()=>(0,s._)("strong",null,"Warning! Local changes to your installation will be discarded.",-1))),Ae=ze((()=>(0,s._)("div",{class:"q-ml-sm"},[(0,s._)("strong",null,"Versions:")],-1)));function je(e,t,n,l,i,o){const a=(0,s.up)("q-icon"),d=(0,s.up)("q-select"),r=(0,s.up)("q-card-section");return(0,s.wg)(),(0,s.iD)("div",null,[(0,s.Wm)(r,null,{default:(0,s.w5)((()=>[n.isDirty?((0,s.wg)(),(0,s.iD)("div",Qe,[(0,s.Wm)(a,{name:i.infoIcon},null,8,["name"]),Te])):(0,s.kq)("",!0),Ae,(0,s.Wm)(d,{id:"select-version",outlined:"",dense:"","options-dense":"",modelValue:i.model,"onUpdate:modelValue":[t[0]||(t[0]=e=>i.model=e),o.selectVersion],options:n.options,"option-value":e=>e,"option-label":e=>o.getVersionAndInstalledText(e)},null,8,["modelValue","options","option-value","option-label","onUpdate:modelValue"])])),_:1}),(0,s.kq)("",!0)])}const Ne={props:["options","selected","isDirty","installedVersion"],data(){return{model:this.selected,closeIcon:T.r5M,infoIcon:T._gM}},methods:{selectVersion(e){console.log("Inside VersionSelector#selectVersion getting ready to emit @selected"),this.$emit("selected",e)},getVersionAndInstalledText(e){return e!==this.installedVersion?e:e+" (installed)"}}},Re=(0,R.Z)(Ne,[["render",je],["__scopeId","data-v-e9030516"]]),Oe=Re;G()(Ne,"components",{QCardSection:Ze.Z,QIcon:M.Z,QSelect:$e.Z});var He=n(1257),Me=n(2031);const Fe={class:"extensions-container"},Ye={class:"extensions-title"},Ge={key:0,class:"q-ml-sm installed-color"},Xe={key:1,class:"q-ml-sm available-color"},Je={class:"row"},et={class:"q-ml-sm extension-label"};function tt(e,t,n,i,o,a){const d=(0,s.up)("q-space"),r=(0,s.up)("q-btn"),c=(0,s.up)("q-item-section"),u=(0,s.up)("q-item"),p=(0,s.up)("ActionDots"),g=(0,s.up)("q-card"),m=(0,s.Q2)("close-popup");return(0,s.wg)(),(0,s.iD)("div",Fe,[(0,s._)("p",Ye,[(0,s._)("strong",null,(0,l.zw)(e.$t("Extensions")),1)]),((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(n.extensionsList,(t=>((0,s.wg)(),(0,s.iD)("div",{key:t.id},[((0,s.wg)(),(0,s.j4)(g,{key:t.id,class:"q-my-md q-lg extension-card",bordered:"",width:"auto"},{default:(0,s.w5)((()=>[n.showTopTxt&&a.extensionIsInstalled(t)?((0,s.wg)(),(0,s.iD)("div",Ge,[(0,s._)("strong",null,(0,l.zw)(e.$t("Installed"))+":",1)])):(0,s.kq)("",!0),n.showTopTxt&&!a.extensionIsInstalled(t)?((0,s.wg)(),(0,s.iD)("div",Xe,[(0,s._)("strong",null,(0,l.zw)(e.$t("Available"))+":",1)])):(0,s.kq)("",!0),(0,s._)("div",Je,[(0,s._)("div",et,[(0,s._)("strong",null,(0,l.zw)(a.getExtensionLabel(t)),1)]),(0,s.Wm)(d),a.extensionIsInstalled(t)?(0,s.kq)("",!0):((0,s.wg)(),(0,s.j4)(r,{key:0,class:(0,l.C_)(n.disableButtons?"button-disable":"button-secondary button-pos"),disable:!!n.disableButtons,"no-caps":"",flat:"",label:e.$t("Install"),onClick:e=>a.openExtensionInstallDialog(t)},null,8,["class","disable","label","onClick"])),a.extensionIsInstalled(t)?((0,s.wg)(),(0,s.j4)(p,{key:1,class:"button-pos",showBadge:a.isUpdateAvailable(t),disableDotsButton:n.disableButtons},{"right-action":(0,s.w5)((()=>[])),default:(0,s.w5)((()=>[(0,s._)("div",null,[a.isUpdateAvailable(t)?(0,s.wy)(((0,s.wg)(),(0,s.j4)(u,{key:0,clickable:"",onClick:e=>a.openExtensionVersionDialog(t,!0)},{default:(0,s.w5)((()=>[(0,s.Wm)(c,{style:{color:"var(--q-negative)"}},{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(e.$t("Update")),1)])),_:1})])),_:2},1032,["onClick"])),[[m]]):(0,s.kq)("",!0),a.showChangeVersion(t)?(0,s.wy)(((0,s.wg)(),(0,s.j4)(u,{key:1,clickable:"",onClick:e=>a.openExtensionVersionDialog(t)},{default:(0,s.w5)((()=>[(0,s.Wm)(c,null,{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(e.$t("ChangeVersionLabel")),1)])),_:1})])),_:2},1032,["onClick"])),[[m]]):(0,s.kq)("",!0),(0,s.wy)(((0,s.wg)(),(0,s.j4)(u,{clickable:"",onClick:e=>a.confirmExtensionUnInstall(t)},{default:(0,s.w5)((()=>[(0,s.Wm)(c,null,{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(e.$t("Uninstall")),1)])),_:1})])),_:2},1032,["onClick"])),[[m]])])])),_:2},1032,["showBadge","disableDotsButton"])):(0,s.kq)("",!0)])])),_:2},1024))])))),128))])}const nt={class:"row_container"};function st(e,t,n,i,o,a){const d=(0,s.up)("q-badge"),r=(0,s.up)("q-list"),c=(0,s.up)("q-menu"),u=(0,s.up)("q-btn");return(0,s.wg)(),(0,s.iD)("div",nt,[(0,s.WI)(e.$slots,"right-action",{},void 0,!0),e.$slots.default?((0,s.wg)(),(0,s.j4)(u,{key:0,icon:o.dotsIcon,class:(0,l.C_)(n.disableDotsButton?"dotsButton button-disable":"dotsButton"),flat:"",padding:"sm",disable:!!n.disableDotsButton},{default:(0,s.w5)((()=>[n.showBadge?((0,s.wg)(),(0,s.j4)(d,{key:0,color:"negative",rounded:"",floating:""})):(0,s.kq)("",!0),(0,s.Wm)(c,null,{default:(0,s.w5)((()=>[(0,s.Wm)(r,{style:{"min-width":"100px"}},{default:(0,s.w5)((()=>[(0,s.WI)(e.$slots,"default",{},void 0,!0)])),_:3})])),_:3})])),_:3},8,["icon","class","disable"])):(0,s.kq)("",!0)])}const lt={props:["showBadge","disableDotsButton"],data(){return{dotsIcon:T.XMI}}},it=(0,R.Z)(lt,[["render",st],["__scopeId","data-v-6d36f286"]]),ot=it;G()(lt,"components",{QBtn:H.Z,QBadge:O.Z,QMenu:ve.Z,QList:De.Z});const at={props:["extensionsList","currentSDK","showTopTxt","disableButtons"],components:{ActionDots:ot},data(){return{}},methods:{extensionIsInstalled(e){return""!=e.installedVersion},showChangeVersion(e){return e.installableVersions.length>1&&this.extensionIsInstalled(e)},getExtensionLabel(e){let t=e.label;return!this.extensionIsInstalled(e)&&e.installableVersions.length>=1?t+=" - "+e.installableVersions[0]:t+=" - "+e.installedVersion,t},openExtensionInstallDialog(e){this.$emit("openExtensionInstallDialog",e,this.currentSDK)},openExtensionVersionDialog(e,t=!1){this.$emit("openExtensionVersionDialog",e,this.currentSDK,t)},confirmExtensionUnInstall(e){this.$emit("confirmExtensionUninstall",e,this.currentSDK)},isUpdateAvailable(e){return!0===e.updateAvailable}}};var dt=n(4458),rt=n(136),ct=n(490),ut=n(1233),pt=n(2146);const gt=(0,R.Z)(at,[["render",tt],["__scopeId","data-v-ee1e1986"]]),mt=gt;G()(at,"components",{QCard:dt.Z,QSpace:rt.Z,QBtn:H.Z,QItem:ct.Z,QItemSection:ut.Z}),G()(at,"directives",{ClosePopup:pt.Z});const ht=e=>((0,s.dD)("data-v-7680e111"),e=e(),(0,s.Cn)(),e),wt={class:"extensions-container"},xt={class:"row"},kt={class:"q-ml-sm extension-label"},ft=ht((()=>(0,s._)("div",{class:"text-h6"},[(0,s._)("b",null,"Extension Warning")],-1))),bt=ht((()=>(0,s._)("br",null,null,-1))),vt=ht((()=>(0,s._)("br",null,null,-1)));function Dt(e,t,n,i,o,a){const d=(0,s.up)("q-btn"),r=(0,s.up)("q-space"),c=(0,s.up)("q-card"),u=(0,s.up)("q-card-section"),p=(0,s.up)("q-card-actions"),g=(0,s.up)("q-dialog"),m=(0,s.Q2)("close-popup");return(0,s.wg)(),(0,s.iD)("div",wt,[((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(n.extensionsList,(e=>((0,s.wg)(),(0,s.iD)("div",{key:e.id},[((0,s.wg)(),(0,s.j4)(c,{key:e.id,class:(0,l.C_)(n.addCard?"q-my-md q-lg extension-add-card":"q-my-md q-lg extension-card"),bordered:"",width:"auto"},{default:(0,s.w5)((()=>[(0,s._)("div",xt,[(0,s.Wm)(d,{icon:o.mdiAlertOutline,flat:"",round:"",dense:"",color:"warning",onClick:t=>a.openExtensionInfoPopup(e),size:"17px"},null,8,["icon","onClick"]),(0,s._)("div",kt,[(0,s._)("strong",null,(0,l.zw)(a.getExtensionLabel(e)),1)]),(0,s.Wm)(r)])])),_:2},1032,["class"]))])))),128)),(0,s.Wm)(g,{modelValue:o.extensionInfoPopup,"onUpdate:modelValue":t[0]||(t[0]=e=>o.extensionInfoPopup=e)},{default:(0,s.w5)((()=>[(0,s.Wm)(c,null,{default:(0,s.w5)((()=>[(0,s.Wm)(u,null,{default:(0,s.w5)((()=>[ft])),_:1}),(0,s.Wm)(u,{class:"q-pt-none"},{default:(0,s.w5)((()=>[(0,s.Uk)(" The selected SDK version ("+(0,l.zw)(o.extensionInfo.sdkVersion)+") does not have the compatible extension version for all extensions. ",1),bt,vt,(0,s.Uk)(" If you are interested in using this, currently incompatible, extension, please select another version (e.g. "+(0,l.zw)(o.extensionInfo.recommendedSdkVersion)+") that is compatible with the extensions. ",1)])),_:1}),(0,s.Wm)(p,{align:"right"},{default:(0,s.w5)((()=>[(0,s.wy)((0,s.Wm)(d,{flat:"",label:"OK",color:"primary"},null,512),[[m]])])),_:1})])),_:1})])),_:1},8,["modelValue"])])}const It={props:["extensionsList","addCard"],components:{ActionDots:ot},data(){return{mdiAlertOutline:T.O8k,extensionInfoPopup:!1,extensionInfo:null}},methods:{getExtensionLabel(e){return e.version?e.label+" - "+e.version:e.label},openExtensionInfoPopup(e){this.extensionInfo=e,this.extensionInfoPopup=!0}}};var _t=n(7743),Et=n(1821);const yt=(0,R.Z)(It,[["render",Dt],["__scopeId","data-v-7680e111"]]),Ct=yt;G()(It,"components",{QCard:dt.Z,QBtn:H.Z,QSpace:rt.Z,QDialog:_t.Z,QCardSection:Ze.Z,QCardActions:Et.Z}),G()(It,"directives",{ClosePopup:pt.Z});const St={name:"PageSdk",components:{UpdateMessage:q.Z,VersionSelector:Be,Tile:V.Z,SdkContainer:J,Expandable:oe,ExpandableTile:xe,InstallationRow:_e,ConfirmationDialog:He.Z,PackageManagerLoadingAnimation:Ee.Z,LocationSelector:Me.Z,ExtensionsContainer:mt,ExtensionVersionSelector:Oe,ExtensionsInfoContainer:Ct},data(){return{loading:!0,refreshing:!1,installationDialog:!1,versionDialog:!1,msg:"SDKs",scrollHeight:"100%",currentCategory:null,installedSDK:null,selectedSDK:null,selectedSDKCategory:null,download:T.OGU,mdiAlertOutline:T.O8k,releaseNoteDialog:!1,extensionReleaseNoteDialog:!1,extensionVersionDialog:!1,selectedExtensionVersion:null,sdkForExtensionChangeVersion:null,selectedInstalledExtension:null,selectedExtension:null,selectedExtensionsToInstall:[],disableImpactedExtensions:!0,extensionRemovePopup:!1,extensionInfo:null}},mixins:[j.Z],created(){window.addEventListener("resize",this.onResize),this.getPackages()},watch:{stateRefreshing:function(){0!==this.$store.getters["notifications/refreshing"]?this.refreshing=!0:this.refreshing=!1},stateUpdated:function(){this.getPackages()},loading:{handler(e){this.loading||this.onResize()},flush:"post"},refreshing:{handler(e){this.refreshing||this.onResize()},flush:"post"}},computed:{...(0,S.rn)({sdkCategories:e=>e.sdkPackages.sdkCategories,stateUpdated:e=>e.notifications.stateUpdated,stateRefreshing:e=>e.notifications.stateRefreshing,verifyLocation:e=>e.install.verifyLocation,impactedExtensions:e=>e.sdkPackages.impactedExtensions,updatePackages:e=>e.updatePackages.updatePackages}),getControlHeight(){return"height: "+this.scrollHeight},showBusy(){return this.refreshing||this.loading},getInstallableImpactedExtensions(){let e=[];for(const t of this.impactedExtensions)t.install&&e.push(t);return e.slice().sort(((e,t)=>e.impact>t.impact?1:-1))},getShowUserConfirmation(){for(const e of this.impactedExtensions)if(e.toBeDeleted)return!0;return!1},getInstalledImpactedExtensions(){let e=[];for(const t of this.impactedExtensions)t.install||e.push(t);return e.slice().sort(((e,t)=>e.impact>t.impact?1:-1))},getInstalledExtensionsForInstalledSdk(){if(!this.installedSDK||!this.installedSDK.extensions)return[];let e=[];for(const t of this.installedSDK.extensions)""!=t.installedVersion&&e.push(t);return e.slice().sort(((e,t)=>e.label>t.label?1:-1))}},unmounted(){window.removeEventListener("resize",this.onResize)},mounted(){this.onResize()},methods:{filteredSdkCategories(e,t){return e?e.filter((e=>e.legacy==t)):[]},sortedInstalledSdkCategories(e){return e.slice().sort(((e,t)=>t.version>e.version?1:-1))},isLatestSdk(e,t){return e.version===t.version},isP2Repository(e){return"p2"==e.repositoryType},selectedSdkIsInstalled(){return this.installedSDK.version==this.selectedSDK.version},installNewSDK(e,t,n){this.postInstallSDK(e,t,n,this.selectedExtensionToInstall)},changeSDKVersion(e,t){this.postInstallSDK(e,t,null,this.selectedExtensionsToInstall)},postInstallSDK(e,t,n){this.installSdkPackage(e,t,n,this.selectedExtensionsToInstall)},openInstallationDialog(e){this.selectedSDK=e.installable[0],this.selectedSDKCategory=e,this.installationDialog=!0,this.selectAllExtensionsForSDK(this.selectedSDK)},async openVersionDialog(e,t,n=!1){this.selectedSDK=n?t.latest:e,this.installedSDK=e,this.selectedSDKCategory=t,this.versionDialog=!0,this.getImpactedExtensions(e.id,this.selectedSDK.version)},openReleaseNoteDialog(e){this.releaseNoteDialog=!0,this.installedSDK=e},openExtensionReleaseNoteDialog(e){this.extensionReleaseNoteDialog=!0,this.selectedExtension=e},getPackages(){this.loading=!0;var e=this.$store.dispatch("sdkPackages/getSdks");e.then(this.turnLoadingOff,this.turnLoadingOff)},setSelectedSDKAndExtensions(e){this.selectedSDK=e,null!=this.selectedSDK.extensions&&this.selectAllExtensionsForSDK(this.selectedSDK)},async setSelectedSDKAndImpactedExtensions(e){this.selectedSDK=e,this.getImpactedExtensions(this.installedSDK.id,this.selectedSDK.version)},selectAllExtensionsForSDK(e){this.selectedExtensionsToInstall=[],e.extensions&&e.extensions.forEach((e=>{this.selectedExtensionsToInstall.push(e.id)}))},selectAllInstallableImpactedExtensions(){this.selectedExtensionsToInstall=[],this.getInstallableImpactedExtensions.forEach((e=>{this.selectedExtensionsToInstall.push(e.id)}))},async getImpactedExtensions(e,t){this.disableImpactedExtensions=!0;let n=[e,t];await this.$store.dispatch("sdkPackages/getImpactedExtensions",n),this.selectAllInstallableImpactedExtensions(),this.disableImpactedExtensions=!1},turnLoadingOff(){this.loading=!1},getInstallableExtensions(e){let t=[];if(!e.extensions)return t;for(const n of e.extensions)n.installableVersions.length>0&&t.push(n);return t.slice().sort(((e,t)=>e.label>t.label?1:-1))},showInstallableExtensions(e){return this.getInstallableExtensions(e).length>0},onImpactedExtensionSelected(e){this.selectedExtensionsToInstall=e},getNewInstallableExtensionLabel(e){return e.version?e.label+" - "+e.version:e.label},confirmUpdate(e,t){var n=this.$t("ConfirmUpdateStart")+e.label+this.$t("ConfirmUpdateEnd");this.$q.dialog({title:this.$t("UpdatePackage")+t.version,message:n,cancel:!0,persistent:!0}).onOk((()=>{this.changeSDKVersion(e.id,t.version)}))},confirmUnInstall(e){var t=this.$t("ConfirmUninstallStart")+e.label+this.$t("ConfirmUninstallEnd");this.$q.dialog({title:this.$t("UninstallPackage"),message:t,cancel:!0,persistent:!0}).onOk((()=>{let t={packages:[{id:e.id}]};this.$store.dispatch("install/deleteSDK",t)}))},confirmExtInstallDialog(e,t){let n=e.installableVersions[0];var s=this.$t("ConfirmInstallStart")+e.label+" - "+n+this.$t("ConfirmInstallEnd");this.$q.dialog({title:this.$t("InstallExtension"),message:s,cancel:!0,persistent:!0}).onOk((()=>{this.installExtension(t.id,e.id,n)}))},openExtVersionDialog(e,t,n=!1){this.selectedInstalledExtension=e,this.selectedExtensionVersion=n?e.latestVersion:e.installedVersion,this.sdkForExtensionChangeVersion=t,this.extensionVersionDialog=!0},setSelectedExtensionVersion(e){this.selectedExtensionVersion=e},changeExtensionVersion(e,t,n){this.installExtension(e,t,n)},confirmExtUnInstall(e,t){var n=this.$t("ConfirmUninstallStart")+e.label+" - "+e.installedVersion+this.$t("ConfirmUninstallEnd");this.$q.dialog({title:this.$t("UninstallExtension"),message:n,cancel:!0,persistent:!0}).onOk((()=>{this.unInstallExtension(t.id,e.id)}))},onResize(){this.scrollHeight=ee.Z.calculateHeight("scrollArea","navigationToolbar")},install(e,t){this.installPackages(e,t)},openExtensionRemovePopup(e){this.extensionInfo=e,this.extensionRemovePopup=!0}}};var qt=n(926),Vt=n(1221);const Kt=(0,R.Z)(St,[["render",C],["__scopeId","data-v-48afe300"]]),Ut=Kt;G()(St,"components",{QScrollArea:Pe.Z,QBadge:O.Z,QBtn:H.Z,QItem:ct.Z,QItemSection:ut.Z,QDialog:_t.Z,QSeparator:qt.Z,QSpace:rt.Z,QCard:dt.Z,QCheckbox:Vt.Z,QCardSection:Ze.Z,QCardActions:Et.Z}),G()(St,"directives",{ClosePopup:pt.Z})}}]);