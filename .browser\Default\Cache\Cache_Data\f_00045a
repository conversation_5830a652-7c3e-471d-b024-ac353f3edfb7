{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Simple LED Driver<span id=\"simple-led-driver\" class=\"self-anchor\"><a class=\"perm\" href=\"#simple-led-driver\">#</a></span></h1><p style=\"color:inherit\">Simple LED Driver can be used to execute basic LED functionalities such as on, off, toggle, or retrive the on/off status on Silicon Labs devices. </p><p style=\"color:inherit\">Subsequent sections provide more insight into this module.</p><p style=\"color:inherit\"><br></p><h2>Introduction<span id=\"introduction\" class=\"self-anchor\"><a class=\"perm\" href=\"#introduction\">#</a></span></h2><p style=\"color:inherit\">The Simple LED driver is a module of the LED driver that provides the functionality to control simple on/off LEDs.</p><p style=\"color:inherit\"><br></p><h2>Simple LED Configuration<span id=\"simple-led-configuration\" class=\"self-anchor\"><a class=\"perm\" href=\"#simple-led-configuration\">#</a></span></h2><p style=\"color:inherit\">Simple LEDs use the <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-led-t\" target=\"_blank\" rel=\"\">sl_led_t</a> struct and their <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-simple-led-context-t\" target=\"_blank\" rel=\"\">sl_simple_led_context_t</a> struct. These are automatically generated into the following files, as well as instance specific headers with macro definitions in them. The samples below are for a single instance called \"inst0\".</p><pre class=\"language-clike\"><code class=\"language-clike\"><span class=\"token comment\">// sl_simple_led_instances.c</span>\n\n#include <span class=\"token string\">\"sl_simple_led.h\"</span>\n#include <span class=\"token string\">\"em_gpio.h\"</span>\n#include <span class=\"token string\">\"sl_simple_led_inst0_config.h\"</span>\n\nsl_simple_led_context_t simple_inst0_context <span class=\"token operator\">=</span> <span class=\"token punctuation\">{</span>\n  <span class=\"token punctuation\">.</span>port <span class=\"token operator\">=</span> SL_SIMPLE_LED_INST0_PORT<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>pin <span class=\"token operator\">=</span> SL_SIMPLE_LED_INST0_PIN<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>polarity <span class=\"token operator\">=</span> SL_SIMPLE_LED_INST0_POLARITY<span class=\"token punctuation\">,</span>\n<span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\n\nconst sl_led_t sl_led_inst0 <span class=\"token operator\">=</span> <span class=\"token punctuation\">{</span>\n  <span class=\"token punctuation\">.</span>context <span class=\"token operator\">=</span> <span class=\"token operator\">&amp;</span>simple_inst0_context<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>init <span class=\"token operator\">=</span> sl_simple_led_init<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>turn_on <span class=\"token operator\">=</span> sl_simple_led_turn_on<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>turn_off <span class=\"token operator\">=</span> sl_simple_led_turn_off<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>toggle <span class=\"token operator\">=</span> sl_simple_led_toggle<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>get_state <span class=\"token operator\">=</span> sl_simple_led_get_state<span class=\"token punctuation\">,</span>\n<span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\n\nvoid <span class=\"token function\">sl_simple_led_init_instances</span><span class=\"token punctuation\">(</span>void<span class=\"token punctuation\">)</span>\n<span class=\"token punctuation\">{</span>\n  <span class=\"token function\">sl_led_init</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>sl_led_inst0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token punctuation\">}</span>\n</code></pre><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The sl_simple_led_instances.c file is shown with only one instance, but if more were in use they would all appear in this .c file.</p></li></ul><pre class=\"language-clike\"><code class=\"language-clike\"><span class=\"token comment\">// sl_simple_led_instances.h</span>\n\n#ifndef SL_SIMPLE_LED_INSTANCES_H\n#define SL_SIMPLE_LED_INSTANCES_H\n\n#include <span class=\"token string\">\"sl_simple_led.h\"</span>\n\nextern const sl_led_t sl_led_inst0<span class=\"token punctuation\">;</span>\n\nvoid <span class=\"token function\">sl_simple_led_init_instances</span><span class=\"token punctuation\">(</span>void<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n\n#endif <span class=\"token comment\">// SL_SIMPLE_LED_INIT_H</span>\n</code></pre><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The sl_simple_led_instances.h file is shown with only one instance, but if more were in use they would all appear in this .h file.</p></li></ul><p style=\"color:inherit\"><br></p><h2>Simple LED Usage<span id=\"simple-led-usage\" class=\"self-anchor\"><a class=\"perm\" href=\"#simple-led-usage\">#</a></span></h2><p style=\"color:inherit\">The simple LED driver is for LEDs with basic on off functionality, and there are no additional functions beyond those in the common driver. The LEDs can be turned on and off, toggled, and their on/off state can be retrieved. The following code shows how to control these LEDs. An LED should always be initialized before calling any other functions with it.</p><pre class=\"language-clike\"><code class=\"language-clike\"><span class=\"token comment\">// initialize simple LED</span>\n<span class=\"token function\">sl_simple_led_init</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>simple_led_inst0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n\n<span class=\"token comment\">// turn on simple LED, turn off simple LED, and toggle the simple LED</span>\n<span class=\"token function\">sl_simple_led_turn_on</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>simple_led_inst0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token function\">sl_simple_led_turn_off</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>simple_led_inst0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token function\">sl_simple_led_toggle</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>simple_led_inst0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n\n<span class=\"token comment\">// get the state of the simple LED</span>\nsl_led_state_t state <span class=\"token operator\">=</span> <span class=\"token function\">sl_simple_led_get_state</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>simple_led_instance0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n</code></pre><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-simple-led-context-t\" target=\"_blank\" rel=\"\">sl_simple_led_context_t</a></p><div class=\"decl-class-section\"><h2>Typedefs<span id=\"typedef-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#typedef-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">typedef uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-led-polarity-t\">sl_led_polarity_t</a></div><div class=\"classdescription\"><p style=\"color:inherit\">LED GPIO polarities (active high/low) </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-led-init\">sl_simple_led_init</a>(void *led_handle)</div><div class=\"classdescription\"><p style=\"color:inherit\">Initialize the simple LED driver. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-led-turn-on\">sl_simple_led_turn_on</a>(void *led_handle)</div><div class=\"classdescription\"><p style=\"color:inherit\">Turn on a simple LED. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-led-turn-off\">sl_simple_led_turn_off</a>(void *led_handle)</div><div class=\"classdescription\"><p style=\"color:inherit\">Turn off a simple LED. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-led-toggle\">sl_simple_led_toggle</a>(void *led_handle)</div><div class=\"classdescription\"><p style=\"color:inherit\">Toggle a simple LED. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/led#sl-led-state-t\" target=\"_blank\" rel=\"\">sl_led_state_t</a></div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-led-get-state\">sl_simple_led_get_state</a>(void *led_handle)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get the current state of the simple LED. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-led-polarity-active-low\">SL_SIMPLE_LED_POLARITY_ACTIVE_LOW</a> 0U</div><div class=\"classdescription\"><p style=\"color:inherit\">LED Active polarity Low. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-led-polarity-active-high\">SL_SIMPLE_LED_POLARITY_ACTIVE_HIGH</a> 1U</div><div class=\"classdescription\"><p style=\"color:inherit\">LED Active polarity High. </p></div></div></div></div></div><div class=\"def-class-section\"><h2>Typedef Documentation<span id=\"typedef-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#typedef-definition\">#</a></span></h2><div><h3>sl_led_polarity_t<span id=\"sl-led-polarity-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-led-polarity-t\">#</a></span></h3><blockquote>typedef uint8_t sl_led_polarity_t </blockquote><p style=\"color:inherit\">LED GPIO polarities (active high/low) </p><br><div>Definition at line <code>66</code> of file <code>platform/driver/leddrv/inc/sl_simple_led.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_simple_led_init<span id=\"sl-simple-led-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-led-init\">#</a></span></h3><blockquote>sl_status_t sl_simple_led_init (void * led_handle)</blockquote><p style=\"color:inherit\">Initialize the simple LED driver. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">led_handle</td><td><p style=\"color:inherit\">Pointer to simple-led specific data:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-simple-led-context-t\" target=\"_blank\" rel=\"\">sl_simple_led_context_t</a></p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status Code:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK </p></li></ul></li></ul><br><div>Definition at line <code>88</code> of file <code>platform/driver/leddrv/inc/sl_simple_led.h</code></div><br></div><div><h3>sl_simple_led_turn_on<span id=\"sl-simple-led-turn-on\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-led-turn-on\">#</a></span></h3><blockquote>void sl_simple_led_turn_on (void * led_handle)</blockquote><p style=\"color:inherit\">Turn on a simple LED. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">led_handle</td><td><p style=\"color:inherit\">Pointer to simple-led specific data:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-simple-led-context-t\" target=\"_blank\" rel=\"\">sl_simple_led_context_t</a></p></li></ul></td></tr></tbody></table></div><br><div>Definition at line <code>97</code> of file <code>platform/driver/leddrv/inc/sl_simple_led.h</code></div><br></div><div><h3>sl_simple_led_turn_off<span id=\"sl-simple-led-turn-off\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-led-turn-off\">#</a></span></h3><blockquote>void sl_simple_led_turn_off (void * led_handle)</blockquote><p style=\"color:inherit\">Turn off a simple LED. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">led_handle</td><td><p style=\"color:inherit\">Pointer to simple-led specific data:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-simple-led-context-t\" target=\"_blank\" rel=\"\">sl_simple_led_context_t</a></p></li></ul></td></tr></tbody></table></div><br><div>Definition at line <code>106</code> of file <code>platform/driver/leddrv/inc/sl_simple_led.h</code></div><br></div><div><h3>sl_simple_led_toggle<span id=\"sl-simple-led-toggle\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-led-toggle\">#</a></span></h3><blockquote>void sl_simple_led_toggle (void * led_handle)</blockquote><p style=\"color:inherit\">Toggle a simple LED. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">led_handle</td><td><p style=\"color:inherit\">Pointer to simple-led specific data:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-simple-led-context-t\" target=\"_blank\" rel=\"\">sl_simple_led_context_t</a></p></li></ul></td></tr></tbody></table></div><br><div>Definition at line <code>115</code> of file <code>platform/driver/leddrv/inc/sl_simple_led.h</code></div><br></div><div><h3>sl_simple_led_get_state<span id=\"sl-simple-led-get-state\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-led-get-state\">#</a></span></h3><blockquote>sl_led_state_t sl_simple_led_get_state (void * led_handle)</blockquote><p style=\"color:inherit\">Get the current state of the simple LED. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">led_handle</td><td><p style=\"color:inherit\">Pointer to simple-led specific data:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-simple-led-context-t\" target=\"_blank\" rel=\"\">sl_simple_led_context_t</a></p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_led_state_t Current state of simple LED. 1 for on, 0 for off </p></li></ul><br><div>Definition at line <code>125</code> of file <code>platform/driver/leddrv/inc/sl_simple_led.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>SL_SIMPLE_LED_POLARITY_ACTIVE_LOW<span id=\"sl-simple-led-polarity-active-low\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-led-polarity-active-low\">#</a></span></h3><blockquote>#define SL_SIMPLE_LED_POLARITY_ACTIVE_LOW</blockquote><b>Value:</b><pre class=\"macroshort\">0U</pre><p style=\"color:inherit\">LED Active polarity Low. </p><br><div>Definition at line <code>59</code> of file <code>platform/driver/leddrv/inc/sl_simple_led.h</code></div><br></div><div><h3>SL_SIMPLE_LED_POLARITY_ACTIVE_HIGH<span id=\"sl-simple-led-polarity-active-high\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-led-polarity-active-high\">#</a></span></h3><blockquote>#define SL_SIMPLE_LED_POLARITY_ACTIVE_HIGH</blockquote><b>Value:</b><pre class=\"macroshort\">1U</pre><p style=\"color:inherit\">LED Active polarity High. </p><br><div>Definition at line <code>60</code> of file <code>platform/driver/leddrv/inc/sl_simple_led.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/simple-led", "status": "success"}