{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Address Resolving List<span id=\"address-resolving-list\" class=\"self-anchor\"><a class=\"perm\" href=\"#address-resolving-list\">#</a></span></h1><p style=\"color:inherit\">Address Resolving List. </p><p style=\"color:inherit\">Provides support for adding and removing devices from the Resolving List in controller-based privacy.</p><p style=\"color:inherit\">Adding a peer device to the Resolving List allows the Bluetooth controller to resolve the address when the peer device is using privacy and is transmitting with a Resolvable Private Address (RPA). When the controller has resolved an address in a received Bluetooth packet such as an advertisement, the corresponding event will report the peer device's identity address even if a Resolvable Private Address was used over the air.</p><p style=\"color:inherit\">When the application has added a peer device to the Resolving List, the application may use the peer device's identity address in commands such as <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-open\" target=\"_blank\" rel=\"\">sl_bt_connection_open</a> or <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync-scanner#sl-bt-sync-scanner-open\" target=\"_blank\" rel=\"\">sl_bt_sync_scanner_open</a> even if the peer device is using privacy and is using a Resolvable Private Address over the air. </p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-resolving-list-privacy-mode-t\">sl_bt_resolving_list_privacy_mode_t</a> {</div><div class=\"enum\">sl_bt_resolving_list_privacy_mode_network = 0x0</div><div class=\"enum\">sl_bt_resolving_list_privacy_mode_device = 0x1</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Specifies the Privacy Mode used for a peer device in the Resolving List. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-resolving-list-add-device-by-bonding\">sl_bt_resolving_list_add_device_by_bonding</a>(uint32_t bonding, uint8_t privacy_mode)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-resolving-list-add-device-by-address\">sl_bt_resolving_list_add_device_by_address</a>(bd_addr address, uint8_t address_type, aes_key_128 key, uint8_t privacy_mode)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-resolving-list-remove-device-by-bonding\">sl_bt_resolving_list_remove_device_by_bonding</a>(uint32_t bonding)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-resolving-list-remove-device-by-address\">sl_bt_resolving_list_remove_device_by_address</a>(bd_addr address, uint8_t address_type)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-resolving-list-remove-all-devices\">sl_bt_resolving_list_remove_all_devices</a>()</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-resolving-list-add-device-by-bonding-id\">sl_bt_cmd_resolving_list_add_device_by_bonding_id</a> 0x005d0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-resolving-list-add-device-by-address-id\">sl_bt_cmd_resolving_list_add_device_by_address_id</a> 0x015d0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-resolving-list-remove-device-by-bonding-id\">sl_bt_cmd_resolving_list_remove_device_by_bonding_id</a> 0x025d0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-resolving-list-remove-device-by-address-id\">sl_bt_cmd_resolving_list_remove_device_by_address_id</a> 0x035d0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-resolving-list-remove-all-devices-id\">sl_bt_cmd_resolving_list_remove_all_devices_id</a> 0x045d0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-resolving-list-add-device-by-bonding-id\">sl_bt_rsp_resolving_list_add_device_by_bonding_id</a> 0x005d0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-resolving-list-add-device-by-address-id\">sl_bt_rsp_resolving_list_add_device_by_address_id</a> 0x015d0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-resolving-list-remove-device-by-bonding-id\">sl_bt_rsp_resolving_list_remove_device_by_bonding_id</a> 0x025d0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-resolving-list-remove-device-by-address-id\">sl_bt_rsp_resolving_list_remove_device_by_address_id</a> 0x035d0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-resolving-list-remove-all-devices-id\">sl_bt_rsp_resolving_list_remove_all_devices_id</a> 0x045d0020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>sl_bt_resolving_list_privacy_mode_t<span id=\"sl-bt-resolving-list-privacy-mode-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-resolving-list-privacy-mode-t\">#</a></span></h3><blockquote>sl_bt_resolving_list_privacy_mode_t</blockquote><p style=\"color:inherit\">Specifies the Privacy Mode used for a peer device in the Resolving List. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_resolving_list_privacy_mode_network</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Use Network Privacy Mode for the peer device </p></td></tr><tr><td class=\"fieldname\">sl_bt_resolving_list_privacy_mode_device</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Use Device Privacy Mode for the peer device </p></td></tr></tbody></table><br><div>Definition at line <code>11926</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_resolving_list_add_device_by_bonding<span id=\"sl-bt-resolving-list-add-device-by-bonding\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-resolving-list-add-device-by-bonding\">#</a></span></h3><blockquote>sl_status_t sl_bt_resolving_list_add_device_by_bonding (uint32_t bonding, uint8_t privacy_mode)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">bonding</td><td><p style=\"color:inherit\">The bonding handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">privacy_mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-resolving-list#sl-bt-resolving-list-privacy-mode-t\" target=\"_blank\" rel=\"\">sl_bt_resolving_list_privacy_mode_t</a>. The Privacy Mode to use for the peer device. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_resolving_list_privacy_mode_network (0x0):</strong> Use Network Privacy Mode for the peer device</p></li><li><p style=\"color:inherit\"><strong>sl_bt_resolving_list_privacy_mode_device (0x1):</strong> Use Device Privacy Mode for the peer device</p></li></ul><p style=\"color:inherit\">Default: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-resolving-list#sl-bt-resolving-list-privacy-mode-network\" target=\"_blank\" rel=\"\">sl_bt_resolving_list_privacy_mode_network</a> (Use Network Privacy Mode for the peer device) </p></td></tr></tbody></table></div><p style=\"color:inherit\">Add a device to the Resolving List based on its bonding handle.</p><p style=\"color:inherit\">This command is not available if the application uses the external bonding database provided by the component bluetooth_feature_external_bonding_database. In that configuration the application can use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-resolving-list#sl-bt-resolving-list-add-device-by-address\" target=\"_blank\" rel=\"\">sl_bt_resolving_list_add_device_by_address</a> and provide the peer's identity address and its Identity Resolving Key (IRK).</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11962</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_resolving_list_add_device_by_address<span id=\"sl-bt-resolving-list-add-device-by-address\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-resolving-list-add-device-by-address\">#</a></span></h3><blockquote>sl_status_t sl_bt_resolving_list_add_device_by_address (<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> address, uint8_t address_type, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/aes-key-128\" target=\"_blank\" rel=\"\">aes_key_128</a> key, uint8_t privacy_mode)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">address</td><td><p style=\"color:inherit\">Bluetooth address of the peer device </p></td></tr><tr><td>[in]</td><td class=\"paramname\">address_type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>. The peer device address type. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address (0x0):</strong> Public device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">key</td><td><p style=\"color:inherit\">Identity Resolving Key (IRK) of the peer device in little endian format. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">privacy_mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-resolving-list#sl-bt-resolving-list-privacy-mode-t\" target=\"_blank\" rel=\"\">sl_bt_resolving_list_privacy_mode_t</a>. The Privacy Mode to use for the peer device. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_resolving_list_privacy_mode_network (0x0):</strong> Use Network Privacy Mode for the peer device</p></li><li><p style=\"color:inherit\"><strong>sl_bt_resolving_list_privacy_mode_device (0x1):</strong> Use Device Privacy Mode for the peer device</p></li></ul><p style=\"color:inherit\">Default: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-resolving-list#sl-bt-resolving-list-privacy-mode-network\" target=\"_blank\" rel=\"\">sl_bt_resolving_list_privacy_mode_network</a> (Use Network Privacy Mode for the peer device) </p></td></tr></tbody></table></div><p style=\"color:inherit\">Add a device to the Resolving List based on its identity address and its Identity Resolving Key (IRK).</p><p style=\"color:inherit\">This command is typically only needed when the application uses the external bonding database provided by the component bluetooth_feature_external_bonding_database. When the application uses the built-in bonding database, the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-resolving-list#sl-bt-resolving-list-add-device-by-bonding\" target=\"_blank\" rel=\"\">sl_bt_resolving_list_add_device_by_bonding</a> is more convenient.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11998</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_resolving_list_remove_device_by_bonding<span id=\"sl-bt-resolving-list-remove-device-by-bonding\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-resolving-list-remove-device-by-bonding\">#</a></span></h3><blockquote>sl_status_t sl_bt_resolving_list_remove_device_by_bonding (uint32_t bonding)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">bonding</td><td><p style=\"color:inherit\">The bonding handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Remove a device from the Resolving List based on its bonding handle.</p><p style=\"color:inherit\">This command is not available if the application uses the external bonding database provided by the component bluetooth_feature_external_bonding_database. In that configuration the application can use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-resolving-list#sl-bt-resolving-list-remove-device-by-address\" target=\"_blank\" rel=\"\">sl_bt_resolving_list_remove_device_by_address</a> and provide the peer's identity address.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>12019</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_resolving_list_remove_device_by_address<span id=\"sl-bt-resolving-list-remove-device-by-address\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-resolving-list-remove-device-by-address\">#</a></span></h3><blockquote>sl_status_t sl_bt_resolving_list_remove_device_by_address (<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> address, uint8_t address_type)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">address</td><td><p style=\"color:inherit\">Bluetooth address of the peer device </p></td></tr><tr><td>[in]</td><td class=\"paramname\">address_type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>. The peer device address type. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address (0x0):</strong> Public device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Remove a device from the Resolving List based on its identity address.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>12034</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_resolving_list_remove_all_devices<span id=\"sl-bt-resolving-list-remove-all-devices\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-resolving-list-remove-all-devices\">#</a></span></h3><blockquote>sl_status_t sl_bt_resolving_list_remove_all_devices ()</blockquote><p style=\"color:inherit\">Remove all devices from the Resolving List.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>12045</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_resolving_list_add_device_by_bonding_id<span id=\"sl-bt-cmd-resolving-list-add-device-by-bonding-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-resolving-list-add-device-by-bonding-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_resolving_list_add_device_by_bonding_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x005d0020</pre><br><div>Definition at line <code>11911</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_resolving_list_add_device_by_address_id<span id=\"sl-bt-cmd-resolving-list-add-device-by-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-resolving-list-add-device-by-address-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_resolving_list_add_device_by_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x015d0020</pre><br><div>Definition at line <code>11912</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_resolving_list_remove_device_by_bonding_id<span id=\"sl-bt-cmd-resolving-list-remove-device-by-bonding-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-resolving-list-remove-device-by-bonding-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_resolving_list_remove_device_by_bonding_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x025d0020</pre><br><div>Definition at line <code>11913</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_resolving_list_remove_device_by_address_id<span id=\"sl-bt-cmd-resolving-list-remove-device-by-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-resolving-list-remove-device-by-address-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_resolving_list_remove_device_by_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x035d0020</pre><br><div>Definition at line <code>11914</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_resolving_list_remove_all_devices_id<span id=\"sl-bt-cmd-resolving-list-remove-all-devices-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-resolving-list-remove-all-devices-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_resolving_list_remove_all_devices_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x045d0020</pre><br><div>Definition at line <code>11915</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_resolving_list_add_device_by_bonding_id<span id=\"sl-bt-rsp-resolving-list-add-device-by-bonding-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-resolving-list-add-device-by-bonding-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_resolving_list_add_device_by_bonding_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x005d0020</pre><br><div>Definition at line <code>11916</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_resolving_list_add_device_by_address_id<span id=\"sl-bt-rsp-resolving-list-add-device-by-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-resolving-list-add-device-by-address-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_resolving_list_add_device_by_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x015d0020</pre><br><div>Definition at line <code>11917</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_resolving_list_remove_device_by_bonding_id<span id=\"sl-bt-rsp-resolving-list-remove-device-by-bonding-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-resolving-list-remove-device-by-bonding-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_resolving_list_remove_device_by_bonding_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x025d0020</pre><br><div>Definition at line <code>11918</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_resolving_list_remove_device_by_address_id<span id=\"sl-bt-rsp-resolving-list-remove-device-by-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-resolving-list-remove-device-by-address-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_resolving_list_remove_device_by_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x035d0020</pre><br><div>Definition at line <code>11919</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_resolving_list_remove_all_devices_id<span id=\"sl-bt-rsp-resolving-list-remove-all-devices-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-resolving-list-remove-all-devices-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_resolving_list_remove_all_devices_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x045d0020</pre><br><div>Definition at line <code>11920</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-resolving-list", "status": "success"}