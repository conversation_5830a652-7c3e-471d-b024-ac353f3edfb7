"use strict";(self["webpackChunkmemory_editor"]=self["webpackChunkmemory_editor"]||[]).push([[985],{3985:(e,a,t)=>{t.r(a),t.d(a,{default:()=>ke});t(702);var n=t(9835),r=t(499),l=t(6970);const s={props:{devices:{type:Array,default:()=>[]}},emits:["change"],setup(e,{emit:a}){const t=e,l=(0,r.iH)(t.devices.length>0?t.devices[0]:null);a("change",0);const s=function(){const e=t.devices.findIndex((e=>e.opn===l.value.opn));a("change",e)};return(e,a)=>{const r=(0,n.up)("q-select");return(0,n.wg)(),(0,n.j4)(r,{modelValue:l.value,"onUpdate:modelValue":[a[0]||(a[0]=e=>l.value=e),s],"option-value":"opn","option-label":"name",options:t.devices,label:"OPN",borderless:"",dark:"",dense:""},null,8,["modelValue","options"])}}};var o=t(2880),i=t(9984),u=t.n(i);const c=s,m=c;u()(s,"components",{QSelect:o.Z});const p="0x",d=function(e){const a=parseInt(e);return p+a.toString(16)},g=function(e){const a=parseInt(e);return a%1024===0?a/1024+" kB":d(e)},v={class:"text-h6 row justify-center"},f={key:0,class:"text-overline row justify-center"},h={props:{regions:{type:Array,default:()=>[]}},setup(e){const a=(0,r.iH)({min:null,max:null});return(t,s)=>{const o=(0,n.up)("q-range");return(0,n.wg)(!0),(0,n.iD)(n.HY,null,(0,n.Ko)(e.regions,((t,i)=>((0,n.wg)(),(0,n.iD)("div",{class:"right-side topper",style:(0,l.j5)("flex-basis: calc((100% - 250px)/"+e.regions.length+");"),key:t.name},[(0,n._)("div",null,[(0,n._)("div",v,(0,l.zw)(t.name),1),t.page_size?((0,n.wg)(),(0,n.iD)("div",f," Page Size: "+(0,l.zw)((0,r.SU)(g)(t.page_size)),1)):(0,n.kq)("",!0)]),(0,n.Wm)(o,{modelValue:a.value,"onUpdate:modelValue":s[0]||(s[0]=e=>a.value=e),min:parseInt(t.start),max:parseInt(t.start)+parseInt(t.size),readonly:"","selection-color":"transparent","track-color":"transparent","switch-marker-labels-side":"","marker-labels":"","switch-label-side":"",dense:""},{"marker-label-group":(0,n.w5)((({markerList:a})=>[(0,n._)("div",{class:(0,l.C_)(a[0].classes),style:(0,l.j5)(a[0].style)},(0,l.zw)((0,r.SU)(d)(a[0].value)),7),(0,n._)("div",{class:(0,l.C_)(a[a.length-1].classes),style:(0,l.j5)([e.regions.length>1&&e.regions.length-1!=i?"bottom: 20px;":"",{left:"100%"}])},(0,l.zw)((0,r.SU)(d)(a[a.length-1].value)),7)])),_:2},1032,["modelValue","min","max"])],4)))),128)}}};var y=t(1639),w=t(9194);const S=(0,y.Z)(h,[["__scopeId","data-v-a7bc7d04"]]),_=S;u()(h,"components",{QRange:w.Z});var k=t(9448);const b={class:"hex-wrapper"},z={class:"numbers"},x={props:{min:{type:Number,default:0},max:{type:Number,default:1},input:{type:Number,default:0},label:{type:String,default:""},pageSize:{type:Number,default:1},alignment:{type:Array,default:()=>[]},alignmentBuff:{type:Number,default:0}},emits:["change","hasError"],setup(e,{emit:a}){const t=e,l=(0,r.iH)(t.input.toString(16));function s(){return parseInt(l.value,16)}const o=(0,r.iH)(null);(0,n.YP)((()=>t.input),(e=>{e.toString(16)!==l.value&&(l.value=e.toString(16))}));const i=/^[0-9a-fA-F]+$/,u=(0,n.Fl)((()=>{if(!i.test(l.value))return"Input must be Hexadecimal";const e=s();return e<t.min?`Cannot be less than ${d(t.min)}`:e>t.max?`Cannot be greater than ${d(t.max)}`:t.pageSize>1&&!t.alignment.includes(t.alignmentBuff+e)?"Must align with Page Size":""}));function c(e){l.value=e.toString(16),u.value?l.value=t.input.toString(16):a("change",s())}function m(){u.value?l.value=t.input.toString(16):a("change",s())}async function p(e){const a=e.indexOf("k");a<1||(e=e.slice(0,a),e=1024*parseInt(e),l.value=e.toString(16),await(0,n.Y3)(),(0,n.Y3)((()=>{o.value.getNativeElement().setSelectionRange(l.value.length,l.value.length)})))}return a("hasError",""!==u.value),(0,n.YP)((()=>u.value),(e=>{a("hasError",""!==e)})),(a,i)=>{const d=(0,n.up)("q-input"),g=(0,n.up)("q-btn");return(0,n.wg)(),(0,n.iD)("div",b,[(0,n.Wm)(d,{filled:"",modelValue:l.value,"onUpdate:modelValue":[i[0]||(i[0]=e=>l.value=e),p],label:e.label,prefix:"0x",mask:"xxxxxxxxxxxxxx","error-message":(0,r.SU)(u),error:!!(0,r.SU)(u),onBlur:m,class:"hex-input",dense:"",ref_key:"inputref",ref:o},null,8,["modelValue","label","error-message","error"]),(0,n._)("div",z,[(0,n.Wm)(g,{onClick:i[1]||(i[1]=e=>c(s()+t.pageSize)),disabled:s()+t.pageSize>t.max,flat:"",round:"",color:"primary",icon:(0,r.SU)(k.Waq),title:"Increase value"},null,8,["disabled","icon"]),(0,n.Wm)(g,{onClick:i[2]||(i[2]=e=>c(s()-t.pageSize)),disabled:s()-t.pageSize<t.min,flat:"",round:"",color:"primary",icon:(0,r.SU)(k.CW),title:"Decrease value"},null,8,["disabled","icon"])])])}}};var I=t(6611),U=t(9379);const j=(0,y.Z)(x,[["__scopeId","data-v-563202f1"]]),W=j;u()(x,"components",{QInput:I.Z,QBtn:U.Z});const q={props:{name:{type:String,default:""},min:{type:Number,default:0},max:{type:Number,default:1},start:{type:Number,default:0},end:{type:Number,default:1},pageSize:{type:Number,default:1}},emits:["change"],setup(e,{emit:a}){const t=e,s=(0,r.iH)({min:t.start,max:t.end});function o(){a("change",s.value)}(0,n.YP)((()=>t.start),(e=>{e!==s.value.min&&(s.value.min=e)})),(0,n.YP)((()=>t.end),(e=>{e!==s.value.min&&(s.value.max=e)}));const i=function(e){const a=e[s.value.min],t=e[s.value.max];if(!a||!t)return 0;const n=(t.index+a.index)/2;return parseInt(n)};return(a,u)=>{const c=(0,n.up)("q-range");return(0,n.wg)(),(0,n.j4)(c,{modelValue:s.value,"onUpdate:modelValue":[u[0]||(u[0]=e=>s.value=e),o],color:"primary",min:t.min,max:t.max,"track-size":"100px","thumb-size":"30px","track-color":"transparent","inner-track-color":"transparent","selection-color":"grey-4","selection-img":"icons/memory.svg","label-always":"","marker-labels":"","marker-labels-class":"marker-labels","left-label-value":(0,r.SU)(g)(s.value.min),"right-label-value":(0,r.SU)(g)(s.value.max),step:e.pageSize,snap:"","drag-range":"","switch-label-side":"","switch-marker-labels-side":""},{"marker-label-group":(0,n.w5)((({markerMap:e,markerList:t})=>[(0,n._)("div",{class:(0,l.C_)([t[i(e)].classes,"bar-name"]),style:(0,l.j5)(t[i(e)].style)},[(0,n.WI)(a.$slots,"default")],6)])),_:3},8,["modelValue","min","max","left-label-value","right-label-value","step"])}}},Z=(0,y.Z)(q,[["__scopeId","data-v-11d1c9ac"]]),C=Z;u()(q,"components",{QRange:w.Z});const H={class:"left-side"},D={class:"text-overline row left-header"},Q={class:"row input-container"},N={props:{name:{type:String,default:""},start:{type:Number,default:0},size:{type:Number,default:1},end:{type:Number,default:1},regions:{type:Array,default:()=>[]}},emits:["change","hasError"],setup(e,{emit:a}){const t=e,s=function(e,t){const n=t-e;a("change",{start:e,size:n})},o=(0,r.iH)(!1),i=(0,r.iH)(!1),u=(0,r.iH)(!1),c=(0,n.Fl)((()=>Boolean(o.value&i.value&u.value)));(0,n.YP)((()=>c.value),(e=>{a("hasError",e)}));const m=(0,n.Fl)((()=>{let e=t.regions;for(let a=0;a<e.length;a++){let n=parseInt(e[a].start),r=parseInt(e[a].size),l=n+r;if(t.start>=n&&t.end<=l)return a}return 0}));function p(e){s(parseInt(t.regions[e].start),parseInt(t.regions[e].start)+parseInt(t.regions[e].size))}const d=(0,n.Fl)((()=>parseInt(t.regions[m.value].start))),g=(0,n.Fl)((()=>parseInt(t.regions[m.value].start)+parseInt(t.regions[m.value].size))),v=(0,n.Fl)((()=>{const e=t.regions[m.value].page_size;return e?parseInt(e):1})),f=(0,n.Fl)((()=>{const e=[];for(let a=d.value;a<=g.value;a+=v.value)e.push(a);return e}));function h(e){let a=t.start+e,n=g.value-e;a<=g.value?s(t.start,a):n>=d.value?s(n,g.value):s(d.value,g.value)}return(a,t)=>{const c=(0,n.up)("q-btn");return(0,n.wg)(),(0,n.iD)("div",null,[(0,n._)("div",H,[(0,n._)("div",D,(0,l.zw)(e.name),1),(0,n._)("div",Q,[(0,n.Wm)(W,{min:(0,r.SU)(d),max:e.end-(0,r.SU)(v),input:e.start,label:"Start",onChange:t[0]||(t[0]=a=>s(a,e.end)),onHasError:t[1]||(t[1]=e=>o.value=e),pageSize:(0,r.SU)(v),alignment:(0,r.SU)(f)},null,8,["min","max","input","pageSize","alignment"]),(0,n.Wm)(W,{min:(0,r.SU)(v),max:(0,r.SU)(g)-(0,r.SU)(d),label:"Size",input:e.size,onChange:h,onHasError:t[2]||(t[2]=e=>i.value=e),pageSize:(0,r.SU)(v),alignmentBuff:(0,r.SU)(d),alignment:(0,r.SU)(f)},null,8,["min","max","input","pageSize","alignmentBuff","alignment"]),(0,n.Wm)(W,{min:e.start+(0,r.SU)(v),max:(0,r.SU)(g),label:"End",input:e.end,onChange:t[3]||(t[3]=a=>s(e.start,a)),onHasError:t[4]||(t[4]=e=>u.value=e),pageSize:(0,r.SU)(v),alignment:(0,r.SU)(f)},null,8,["min","max","input","pageSize","alignment"])])]),((0,n.wg)(!0),(0,n.iD)(n.HY,null,(0,n.Ko)(e.regions,(a=>((0,n.wg)(),(0,n.iD)("div",{class:"right-side right-bar",key:a.name,style:(0,l.j5)("flex-basis: calc((100% - 250px)/"+e.regions.length+");")},[(0,r.SU)(g)<=parseInt(a.start)+parseInt(a.size)&&(0,r.SU)(d)>=parseInt(a.start)?((0,n.wg)(),(0,n.j4)(C,{key:0,name:e.name,min:(0,r.SU)(d),max:(0,r.SU)(g),start:e.start,end:e.end,pageSize:(0,r.SU)(v),onChange:t[7]||(t[7]=e=>s(e.min,e.max))},{default:(0,n.w5)((()=>[(0,n.Wm)(c,{dense:"",flat:"",round:"",color:"primary",icon:(0,r.SU)(k.gAv),style:(0,l.j5)((0,r.SU)(m)>0?"":"visibility:hidden;"),onClick:t[5]||(t[5]=e=>p((0,r.SU)(m)-1)),id:"moveRangeLeft"},null,8,["icon","style"]),(0,n.Uk)(" "+(0,l.zw)(e.name)+" ",1),(0,n.Wm)(c,{dense:"",flat:"",round:"",color:"primary",icon:(0,r.SU)(k.zrb),style:(0,l.j5)((0,r.SU)(m)<e.regions.length-1?"":"visibility:hidden;"),onClick:t[6]||(t[6]=e=>p((0,r.SU)(m)+1)),id:"moveRangeRight"},null,8,["icon","style"])])),_:1},8,["name","min","max","start","end","pageSize"])):(0,n.kq)("",!0)],4)))),128))])}}},E=(0,y.Z)(N,[["__scopeId","data-v-7cb4f876"]]),V=E;u()(N,"components",{QBtn:U.Z});var P=t(7363),F=t(9981),Y=t.n(F);const M={"Content-Type":"application/json",Accept:"application/json"},T={get(e,a){return Y()({method:"get",url:e,params:a,headers:M})},put(e,a,t){return Y()({method:"put",url:e,params:t,data:a,headers:M})}};var B=t(9357),R=t(4328);const A=(0,P.Q_)("memory",{state:()=>({shouldSave:!1,workspace:{id:"",name:"",devices:[]},workspaceId:""}),getters:{},actions:{setWorkspaceId(e){this.workspaceId=e},getWorkspace(){const e=`/rest/memeditor/${this.workspaceId}`;return new Promise(((a,t)=>T.get(e).then((e=>{this.shouldSave=!1,this.workspace=e.data,a(e)})).catch((e=>{t(e)}))))},putWorkspace(e){const a=`/rest/memeditor/${this.workspaceId}/projects`;return new Promise(((t,n)=>T.put(a,e).then((e=>{this.shouldSave=!1,this.workspace=e.data,t(e)})).catch((e=>{n(e)}))))},putDirty(e){const a=`/rest/memeditor/${this.workspaceId}/dirty`;return T.put(a,e)},memSocketOnError(e){window.console.log(e)},memSocketOnMessage(e){const a=JSON.parse(e.data);a.clientId===this.workspaceId&&"save"===a.action?this.shouldSave=!0:a.clientId===this.workspaceId&&"contextChanged"===a.action&&R.Z.create({message:"The underlying project context has changed. A reload is recommended, but will discard any changes made. Do you want to reload the editor?",type:"warning",timeout:0,actions:[{label:"Reload",handler:()=>{location.reload()}},{label:"Dissmiss",color:"white",handler:()=>{}}]})},createSaveStatusSocket(){const e=new B.sh(`ws://${window.location.host}/ws/memeditor/server/notifications/${this.workspaceId}`,this,"memSocket");return e.init(),e}}});var $=t(5019),O=t(9302),K=t(8910);const J=(0,n.Uk)(" Memory Editor "),L={key:0,class:"container"},G={class:"top"},X={key:0,class:"q-pa-md right"},ee={class:"memory-panel"},ae={class:"memory-row"},te={class:"left-side"},ne={class:"memory-panel"},re={class:"memory-row"},le={class:"left-side"},se={key:0,class:"text-body2"},oe={setup(e){const a="Memory information missing for devices in the workspace. Make sure you have selected a compatible SDK.",t="Projects in the workspace had missing memory information and defaults values were set to the full memory range. If the software example requires a bootloader or other special memory configuration do not use these defaults, instead get the correct default values from the linker control file in the 'autogen' folder.  Default values will not be persisted until saved.",s="There are projects with overlapping memory boundaries.",o=(0,K.tv)(),i=(0,K.yj)(),u=(0,O.Z)(),c=A(),p=(0,r.iH)("ram");let d=(0,r.iH)(null);const g=(0,r.iH)(null),v=(0,r.iH)(""),f=(0,r.iH)("");function h(e){v.value="",f.value="",e.devices.every((e=>{const n=e.memory.volatile,r=e.memory.nonvolatile;return n&&0!==n.length&&r&&0!==r.length?(e.projects.forEach((a=>{0===parseInt(a.flash_size)&&(f.value=t,a.flash_start=e.memory.nonvolatile[0].start,a.flash_size=e.memory.nonvolatile[0].size),0===parseInt(a.ram_size)&&(f.value=t,a.ram_start=e.memory.volatile[0].start,a.ram_size=e.memory.volatile[0].size)})),!0):(v.value=a,!1)}))}function y(){d.value=(0,$.Z)(!0,{},c.workspace),h(d.value),g.value=(0,$.Z)(!0,{},d.value)}let w=null;(0,n.bv)((async()=>{u.loading.show({delay:400}),await o.isReady(),c.setWorkspaceId(i.query.workspace),w=c.createSaveStatusSocket(c),await c.getWorkspace().then((()=>{y()})).catch((()=>{u.notify({type:"negative",message:"Something went wrong getting workspace. Try reopening editor."})})),u.loading.hide()})),(0,n.Jd)((()=>{w&&w.close()}));const S=(0,r.iH)(null);let b=null,z=0;const x=function(e){z=e,b=d.value.devices[e],S.value=g.value.devices[e]},I=function(e,a){e.ram_start=a.start.toString(),e.ram_size=a.size.toString()};function U(){b.projects.forEach(((e,a)=>{S.value.projects[a].ram_start=e.ram_start,S.value.projects[a].ram_size=e.ram_size}))}const j=(0,n.Fl)((()=>{for(let e=0;e<S.value.projects.length;e++){if(b.projects[e].ram_start!==S.value.projects[e].ram_start)return!0;if(b.projects[e].ram_size!==S.value.projects[e].ram_size)return!0}return!1})),W=function(e,a){e.flash_start=a.start.toString(),e.flash_size=a.size.toString()};function q(){b.projects.forEach(((e,a)=>{S.value.projects[a].flash_start=e.flash_start,S.value.projects[a].flash_size=e.flash_size}))}const Z=(0,n.Fl)((()=>{for(let e=0;e<S.value.projects.length;e++){if(b.projects[e].flash_start!==S.value.projects[e].flash_start)return!0;if(b.projects[e].flash_size!==S.value.projects[e].flash_size)return!0}return!1})),C=(0,n.Fl)((()=>{if(v.value||null===g.value)return!1;if(JSON.stringify(c.workspace)===JSON.stringify(g.value))return!1;for(const e of g.value.devices)for(const a of e.projects)if(a.flash_error||a.ram_error)return!1;return!0}));async function H(){u.loading.show({delay:400}),await c.putWorkspace(g.value).then((()=>{y(),x(z),u.notify({type:"positive",message:"Workspace successfully saved!",actions:[{icon:k.r5M,color:"white"}]})})).catch((()=>{u.notify({type:"negative",message:"Something went wrong saving workspace!",actions:[{icon:k.r5M,color:"white"}]})})),u.loading.hide()}(0,n.YP)((()=>C.value),(async e=>{c.putDirty({isDirty:e})})),(0,n.YP)((()=>c.shouldSave),(async e=>{e&&H()}));const D=(0,n.Fl)((()=>{let e=S.value.projects;for(let a=0;a<e.length;a++){const t=parseInt(e[a].flash_start),n=t+parseInt(e[a].flash_size);for(let r=a+1;r<e.length;r++){const a=parseInt(e[r].flash_start),l=a+parseInt(e[r].flash_size);if(t>=a&&t<l||a>=t&&a<n)return s}}return""}));return(e,a)=>{const t=(0,n.up)("q-toolbar-title"),s=(0,n.up)("q-separator"),o=(0,n.up)("q-btn"),i=(0,n.up)("q-toolbar"),u=(0,n.up)("q-header"),c=(0,n.up)("q-icon"),d=(0,n.up)("q-banner"),h=(0,n.up)("q-tab"),y=(0,n.up)("q-tabs"),w=(0,n.up)("q-tab-panel"),b=(0,n.up)("q-tab-panels"),z=(0,n.up)("q-page"),Q=(0,n.up)("q-card"),N=(0,n.up)("q-page-container");return(0,n.wg)(),(0,n.iD)(n.HY,null,[(0,n.Wm)(u,null,{default:(0,n.w5)((()=>[(0,n.Wm)(i,{class:"shadow-1"},{default:(0,n.w5)((()=>[(0,n.Wm)(t,null,{default:(0,n.w5)((()=>[J])),_:1}),g.value?((0,n.wg)(),(0,n.j4)(m,{key:0,devices:g.value.devices,onChange:x,style:{"margin-right":"20px"}},null,8,["devices"])):(0,n.kq)("",!0),(0,n.Wm)(s,{dark:"",vertical:""}),(0,n.Wm)(o,{flat:"",title:"Save Workspace",stretch:"",style:{"font-size":"16px"},icon:(0,r.SU)(k.Tls),disabled:!(0,r.SU)(C),onClick:H},null,8,["icon","disabled"])])),_:1})])),_:1}),(0,n.Wm)(N,null,{default:(0,n.w5)((()=>[(0,n.Wm)(Q,null,{default:(0,n.w5)((()=>[(0,n.Wm)(z,null,{default:(0,n.w5)((()=>[g.value?((0,n.wg)(),(0,n.iD)("div",L,[(0,n._)("div",G,[v.value?((0,n.wg)(),(0,n.j4)(d,{key:0,class:"bg-negative text-white text-justify"},{default:(0,n.w5)((()=>[(0,n.Wm)(c,{name:(0,r.SU)(k._gM),size:"22px"},null,8,["name"]),(0,n.Uk)(" "+(0,l.zw)(v.value),1)])),_:1})):f.value?((0,n.wg)(),(0,n.j4)(d,{key:1,class:"bg-warning text-white text-subtitle2"},{default:(0,n.w5)((()=>[(0,n.Wm)(c,{name:(0,r.SU)(k._gM),size:"22px"},null,8,["name"]),(0,n.Uk)(" "+(0,l.zw)(f.value),1)])),_:1})):(0,n.kq)("",!0)]),v.value?(0,n.kq)("",!0):((0,n.wg)(),(0,n.iD)("div",X,[(0,n.Wm)(y,{modelValue:p.value,"onUpdate:modelValue":a[0]||(a[0]=e=>p.value=e),dense:"",class:"text-grey","active-color":"primary","indicator-color":"primary",align:"justify","narrow-indicator":""},{default:(0,n.w5)((()=>[(0,n.Wm)(h,{name:"ram",label:"Ram"}),(0,n.Wm)(h,{name:"flash",label:"Flash"})])),_:1},8,["modelValue"]),(0,n.Wm)(s),(0,n.Wm)(b,{modelValue:p.value,"onUpdate:modelValue":a[1]||(a[1]=e=>p.value=e),animated:"","keep-alive":""},{default:(0,n.w5)((()=>[((0,n.wg)(),(0,n.j4)(w,{name:"ram",key:S.value.opn+"ram"},{default:(0,n.w5)((()=>[(0,n._)("div",ee,[(0,n._)("div",ae,[(0,n._)("div",te,[(0,n._)("div",null,[(0,n.Wm)(o,{dense:"",flat:"",color:"negative",title:"Clear RAM",style:{"font-size":"16px"},icon:(0,r.SU)(k.DNZ),onClick:U,disabled:!(0,r.SU)(j)},null,8,["icon","disabled"])])]),S.value?((0,n.wg)(),(0,n.j4)(_,{key:0,regions:S.value.memory.volatile},null,8,["regions"])):(0,n.kq)("",!0)]),((0,n.wg)(!0),(0,n.iD)(n.HY,null,(0,n.Ko)(S.value.projects,(e=>((0,n.wg)(),(0,n.j4)(V,{key:e.id,regions:S.value.memory.volatile,name:e.name,start:parseInt(e.ram_start),size:parseInt(e.ram_size),end:parseInt(e.ram_start)+parseInt(e.ram_size),onChange:a=>I(e,a),onHasError:a=>e.ram_error=a,class:"memory-row"},null,8,["regions","name","start","size","end","onChange","onHasError"])))),128))])])),_:1})),((0,n.wg)(),(0,n.j4)(w,{name:"flash",key:S.value.opn+"flash"},{default:(0,n.w5)((()=>[(0,n._)("div",ne,[(0,n._)("div",re,[(0,n._)("div",le,[(0,n._)("div",null,[(0,n.Wm)(o,{dense:"",flat:"",color:"negative",title:"Clear Flash",style:{"font-size":"16px"},icon:(0,r.SU)(k.DNZ),onClick:q,disabled:!(0,r.SU)(Z)},null,8,["icon","disabled"])]),(0,r.SU)(D)?((0,n.wg)(),(0,n.iD)("div",se,[(0,n.Wm)(c,{name:(0,r.SU)(k._gM),color:"info",size:"20px"},null,8,["name"]),(0,n.Uk)(" "+(0,l.zw)((0,r.SU)(D)),1)])):(0,n.kq)("",!0)]),S.value?((0,n.wg)(),(0,n.j4)(_,{key:0,regions:S.value.memory.nonvolatile},null,8,["regions"])):(0,n.kq)("",!0)]),((0,n.wg)(!0),(0,n.iD)(n.HY,null,(0,n.Ko)(S.value.projects,(e=>((0,n.wg)(),(0,n.j4)(V,{key:e.id,regions:S.value.memory.nonvolatile,name:e.name,start:parseInt(e.flash_start),size:parseInt(e.flash_size),end:parseInt(e.flash_start)+parseInt(e.flash_size),onChange:a=>W(e,a),onHasError:a=>e.flash_error=a,class:"memory-row"},null,8,["regions","name","start","size","end","onChange","onHasError"])))),128))])])),_:1}))])),_:1},8,["modelValue"])]))])):(0,n.kq)("",!0)])),_:1})])),_:1})])),_:1})],64)}}};var ie=t(6602),ue=t(1663),ce=t(1973),me=t(926),pe=t(2133),de=t(4458),ge=t(9885),ve=t(7128),fe=t(2857),he=t(7817),ye=t(900),we=t(9800),Se=t(4106);const _e=(0,y.Z)(oe,[["__scopeId","data-v-47e0aa5e"]]),ke=_e;u()(oe,"components",{QHeader:ie.Z,QToolbar:ue.Z,QToolbarTitle:ce.Z,QSeparator:me.Z,QBtn:U.Z,QPageContainer:pe.Z,QCard:de.Z,QPage:ge.Z,QBanner:ve.Z,QIcon:fe.Z,QTabs:he.Z,QTab:ye.Z,QTabPanels:we.Z,QTabPanel:Se.Z})}}]);