{"availableCount": 233, "searchTerms": [], "userState": "f7cdf3ed-fd6f-40cc-87d5-f0c00c48f0bd", "resources": [{"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "This application demonstrates how to use the FreeRTOS Bluetooth Low Energy middleware APIs to create a simple GATT server.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.amazon_aws_soc_gatt_server.example/amazon_aws_demos/amazon_aws_soc_gatt_server.slcp", "text": "Amazon AWS - SoC Bluetooth GATT Server", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This application demonstrates how to use the FreeRTOS Bluetooth Low Energy middleware APIs to create a simple GATT server."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "Project to run AWS Tests including BLE tests on Silicon Labs boards.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.amazon_aws_soc_bt_tests.example/amazon_aws_tests/amazon_aws_soc_bt_tests.slcp", "text": "Amazon AWS - SoC Bluetooth Tests", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Project to run AWS Tests including BLE tests on Silicon Labs boards."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "This application demonstrates how to use the MQTT over Bluetooth Low Energy service.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.amazon_aws_soc_mqtt_over_ble.example/amazon_aws_demos/amazon_aws_soc_mqtt_over_ble.slcp", "text": "Amazon AWS - SoC MQTT over Bluetooth Low Energy", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This application demonstrates how to use the MQTT over Bluetooth Low Energy service."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/amazon/example/amazon_aws_tests/readme.md"], "description": "Project to run AWS Tests on Silicon Labs boards.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.amazon_aws_tests.example/amazon_aws_tests/amazon_aws_tests.slcp", "text": "Amazon AWS - SoC Platform Tests", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Project to run AWS Tests on Silicon Labs boards."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "configure the device with necessary parameters to start transmitting or receiving BLE PER packets.", "id": "projectTemplate.compatibleSDK.examples/featured/ble_per/projects/ble_per-brd4180a-mg21.slsproj", "text": "BLE PER - Transmit and Receive Performance and Regulatory Testing", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "configure the device with necessary parameters to start transmitting or receiving BLE PER packets."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "configure the device with necessary parameters to start transmitting or receiving BT PER packets.", "id": "projectTemplate.compatibleSDK.examples/featured/bt_per/projects/bt_per-brd4180a-mg21.slsproj", "text": "BT PER - Transmit and Receive Performance and Regulatory Testing", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "configure the device with necessary parameters to start transmitting or receiving BT PER packets."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_apple_notification_center_service/README.md"], "description": "This example demonstrates how to receive Apple Notification Center Service (ANCS) Notifications, such as phone calls, calendar events, and so on and print them out to the VCOM.", "id": "template.uc.compatibleSDK.bt_ancs.bluetooth_apple_notification_center_service/SimplicityStudio/bt_ancs.slcp", "text": "Bluetooth - Apple Notification Center Service", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example demonstrates how to receive Apple Notification Center Service (ANCS) Notifications, such as phone calls, calendar events, and so on and print them out to the VCOM."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_bthome_v2_switch/README.md"], "description": "This project aims to implement a BTHome v2 compatible switch. The device is in sleep mode, and it wakes up once the button 0 on the board is pressed. The application supports press, double press, triple press, and long press events.", "id": "template.uc.compatibleSDK.bt_bthome_v2_switch.bluetooth_bthome_v2_switch/SimplicityStudio/bt_bthome_v2_switch.slcp", "text": "Bluetooth - BTHome v2 - Switch", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement a BTHome v2 compatible switch. The device is in sleep mode, and it wakes up once the button 0 on the board is pressed. The application supports press, double press, triple press, and long press events."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_continuous_glucose_monitoring/README.md"], "description": "This project aims to implement an example of Continuous Glucose Monitoring using the Thunderboard EFR32BG22.", "id": "template.uc.compatibleSDK.bt_continuous_glucose_monitoring.bluetooth_continuous_glucose_monitoring/SimplicityStudio/bt_continuous_glucose_monitoring.slcp", "text": "Bluetooth - Continuous Glucose Monitoring", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement an example of Continuous Glucose Monitoring using the Thunderboard EFR32BG22."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_controlling_led_from_smartphone/README.md"], "description": "This example shows how to develop a simple software to control the Wireless Development Kit's LEDs with a mobile phone application.", "id": "template.uc.compatibleSDK.bt_controlling_led_from_smartphone.bluetooth_controlling_led_from_smartphone/SimplicityStudio/bt_controlling_led_from_smartphone.slcp", "text": "Bluetooth - Controlling LED from Smartphone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example shows how to develop a simple software to control the Wireless Development Kit's LEDs with a mobile phone application."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_hid_keyboard/README.md"], "description": "This is an example for Bluetooth LE HID device development, which can connect wirelessly to HID hosts including Windows, Mac, Android and iOS systems.", "id": "template.uc.compatibleSDK.bt_hid_keyboard.bluetooth_hid_keyboard/SimplicityStudio/bt_hid_keyboard.slcp", "text": "Bluetooth - HID Keyboard", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is an example for Bluetooth LE HID device development, which can connect wirelessly to HID hosts including Windows, Mac, Android and iOS systems."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_log_system/README.md"], "description": "This example introduces a simple implementation of logging on EFR32 based devices.", "id": "template.uc.compatibleSDK.bt_log_system_rtt.bluetooth_log_system/SimplicityStudio/bt_log_system_rtt.slcp", "text": "Bluetooth - Log System via RTT", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example introduces a simple implementation of logging on EFR32 based devices."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_log_system/README.md"], "description": "This example introduces a simple implementation of logging on EFR32 based devices.", "id": "template.uc.compatibleSDK.bt_log_system_vcom.bluetooth_log_system/SimplicityStudio/bt_log_system_vcom.slcp", "text": "Bluetooth - Log System via VCOM (UART)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example introduces a simple implementation of logging on EFR32 based devices."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_midi_over_ble/README.md"], "description": "This project aims to implement a method for encoding and decoding Musical Instrument Digital Interface (MIDI) data for transmission over Bluetooth Low Energy (BLE) connections.", "id": "template.uc.compatibleSDK.bt_midi_over_ble.bluetooth_midi_over_ble/SimplicityStudio/bt_midi_over_ble.slcp", "text": "Bluetooth - MIDI over BLE", "priority": 9999999, "category": "SOFTWARE", "toolTipText": " This project aims to implement a method for encoding and decoding Musical Instrument Digital Interface (MIDI) data for transmission over Bluetooth Low Energy (BLE) connections."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_man_in_the_middle/README.md"], "description": "This project shows the implementation of Man In The Middle (MITM) with BLE.", "id": "template.uc.compatibleSDK.bt_man_in_the_middle_device.bluetooth_man_in_the_middle/bluetooth_man_in_the_middle_device/SimplicityStudio/bt_man_in_the_middle_device.slcp", "text": "Bluetooth - Man-In-The-Middle", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project shows the implementation of Man In The Middle (MITM) with BLE."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_multicentral_multiperipheral_dual_topology/README.md"], "description": "This code example demonstrates how to handle multiple (simultaneous) connections, when the device has to act as central on some of the connections, and peripheral on the rest of the connections.", "id": "template.uc.compatibleSDK.bt_multicentral_multiperipheral_dual_topology.bluetooth_multicentral_multiperipheral_dual_topology/SimplicityStudio/bluetooth_multicentral_multiperipheral_dual_topology.slcp", "text": "Bluetooth - Multi-Central Multi-Peripheral Dual Topology", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This code example demonstrates how to handle multiple (simultaneous) connections, when the device has to act as central on some of the connections, and peripheral on the rest of the connections."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_ncp/readme.md"], "description": "Network Co-Processor (NCP) target application. Runs the Bluetooth stack dynamically and provides access to it via Bluetooth API (BGAPI) using UART connection. NCP mode makes it possible to run your application on a host controller or PC.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_ncp.example/bt_ncp/bt_ncp.slcp", "text": "Bluetooth - NCP", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Network Co-Processor (NCP) target application. Runs the Bluetooth stack dynamically and provides access to it via Bluetooth API (BGAPI) using UART connection. NCP mode makes it possible to run your application on a host controller or PC.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_ncp/readme.md"], "description": "Network Co-Processor (NCP) target application with additional features to support the Electronic Shelf Label Profile ESL Access Point role. Note: Some BLE features unused by the ESL Access Point are removed compared to the NCP target application.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_ncp_esl_ap.example/bt_ncp/bt_ncp_esl_ap.slcp", "text": "Bluetooth - NCP ESL Access Point", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Network Co-Processor (NCP) target application with additional features to support the Electronic Shelf Label Profile ESL Access Point role. Note: Some BLE features unused by the ESL Access Point are removed compared to the NCP target application.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_ncp_host/readme.md"], "description": "Reference implementation of an NCP (Network Co-Processor) host, which typically runs on a central MCU without radio. It can connect to an NCP target running the NCP Example via UART to access the Bluetooth stack on the target and to control it using BGAPI.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_ncp_host.example/bt_ncp_host/bt_ncp_host.slcp", "text": "Bluetooth - NCP Host", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Reference implementation of an NCP (Network Co-Processor) host, which typically runs on a central MCU without radio. It can connect to an NCP target running the NCP Example via UART to access the Bluetooth stack on the target and to control it using BGAPI.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_ota_firmware_update_in_user_application/README.md"], "description": "This project aims to implement firmware upgrade method used in SoC-mode Bluetooth applications. A Gecko Bootloader (GBL) image containing the new firmware is sent to target device via a Bluetooth connection.", "id": "template.uc.compatibleSDK.bt_ota_firmware_update_in_user_application.bluetooth_ota_firmware_update_in_user_application/SimplicityStudio/bt_ota_firmware_update_in_user_application.slcp", "text": "Bluetooth - OTA Firmware Update in User Application", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement firmware upgrade method used in SoC-mode Bluetooth applications. A Gecko Bootloader (GBL) image containing the new firmware is sent to target device via a Bluetooth connection."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_optimized_energy_consuming_sensor/README.md"], "description": "This project aims to implement an optimized energy consuming temperature sensor.", "id": "template.uc.compatibleSDK.bt_optimized_energy_consuming_sensor.bluetooth_optimized_energy_consuming_sensor/bluetooth_optimized_energy_consuming_sensor/SimplicityStudio/bt_optimized_energy_consuming_sensor.slcp", "text": "Bluetooth - Optimized Energy Consuming Sensor", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement an optimized energy consuming temperature sensor."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_optimized_energy_consuming_switch/README.md"], "description": "This project aims to implement an optimized energy consuming switch.", "id": "template.uc.compatibleSDK.bt_optimized_energy_switch.bluetooth_optimized_energy_consuming_switch/SimplicityStudio/bt_optimized_energy_switch.slcp", "text": "Bluetooth - Optimized Energy Consuming Switch", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement an optimized energy consuming switch."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_pawr_thermometer/README.md"], "description": "This project aims to implement a PAwR based Thermometer example. Broadcaster device get temperature data and LED status without establish any connection then displays the temperature values in onboard LCD.", "id": "template.uc.compatibleSDK.bt_pawr_thermometer_broadcaster.bluetooth_pawr_thermometer/bluetooth_pawr_thermometer_broadcaster/SimplicityStudio/bt_pawr_thermometer_broadcaster.slcp", "text": "Bluetooth - PAwR Thermometer - Broadcaster", "priority": 9999999, "category": "SOFTWARE", "toolTipText": " This project aims to implement a PAwR based Thermometer example. Broadcaster device get temperature data and LED status without establish any connection then displays the temperature values in onboard LCD."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_pawr_thermometer/README.md"], "description": "This project aims to implement a PAwR based Thermometer example. Temperature is read from internal temperature sensor then broadcast organized data in small packets in subevents.", "id": "template.uc.compatibleSDK.bt_pawr_thermometer_observer.bluetooth_pawr_thermometer/bluetooth_pawr_thermometer_observer/SimplicityStudio/bt_pawr_thermometer_observer.slcp", "text": "Bluetooth - PAwR Thermometer - Observer", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement a PAwR based Thermometer example. Temperature is read from internal temperature sensor then broadcast organized data in small packets in subevents."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_rcp/readme.md"], "description": "Radio Co-Processor (RCP) target application. Runs the Bluetooth Controller (i.e. the Link Layer only) and provides access to it using the standard HCI (Host-Controller Interface) over a UART connection.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_rcp.example/bt_rcp/bt_rcp.slcp", "text": "Bluetooth - RCP", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Radio Co-Processor (RCP) target application. Runs the Bluetooth Controller (i.e. the Link Layer only) and provides access to it using the standard HCI (Host-Controller Interface) over a UART connection.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_rcp/readme_rcp_cpc.md"], "description": "Radio Co-Processor (RCP) target application. Runs the Bluetooth Controller (i.e. the Link Layer only) and provides access to it using the standard HCI (Host-Controller Interface) over CPC (Co-Processor Communication) protocol through UART connection.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_rcp_cpc.example/bt_rcp/bt_rcp_cpc.slcp", "text": "Bluetooth - RCP CPC", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Radio Co-Processor (RCP) target application. Runs the Bluetooth Controller (i.e. the Link Layer only) and provides access to it using the standard HCI (Host-Controller Interface) over CPC (Co-Processor Communication) protocol through UART connection.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_rssi_positioning/README.md"], "description": "A gateway device application intended to showcase a room finder or asset locator service using the BLE stack on Silicon Laboratories development kits.", "id": "template.uc.compatibleSDK.bt_indoor_positioning_gateway.bluetooth_rssi_positioning/bt_indoor_positioning_gateway/SimplicityStudio/bt_indoor_positioning_gateway.slcp", "text": "Bluetooth - RSSI Positioning - Gateway", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "A gateway device application intended to showcase a room finder or asset locator service using the BLE stack on Silicon Laboratories development kits."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_secure_attestation/README.md"], "description": "This project aims to implement a secure attestation over Bluetooth Low Energy for server device. The server acts in the BLE peripheral role and allows a central/client to connect to it.", "id": "template.uc.compatibleSDK.bt_secure_attestation_server.bluetooth_secure_attestation/bt_secure_attestation_server/SimplicityStudio/bt_secure_attestation_server.slcp", "text": "Bluetooth - Secure Attestation - Server", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement a secure attestation over Bluetooth Low Energy for server device. The server acts in the BLE peripheral role and allows a central/client to connect to it."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_secure_spp_over_ble/README.md"], "description": "This project aims to implement a secure Bluetooth connection between two EFR32 devices and how to implement secure serial communication between them.", "id": "template.uc.compatibleSDK.bt_secure_spp_over_ble.bluetooth_secure_spp_over_ble/SimplicityStudio/bt_secure_spp_over_ble.slcp", "text": "Bluetooth - Secure SPP over BLE", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement a secure Bluetooth connection between two EFR32 devices and how to implement secure serial communication between them."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_serial_port_profile/README.md"], "description": "This example provides a simple template for SPP-like communication (also know as wire replacement), where Bluetooth serves as a transport channel for serial communication between server and client.", "id": "template.uc.compatibleSDK.bt_serial_port_profile_client.bluetooth_serial_port_profile/bt_spp_client/SimplicityStudio/bt_serial_port_profile_client.slcp", "text": "Bluetooth - Serial Port Profile (SPP) - Client", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example provides a simple template for SPP-like communication (also know as wire replacement), where Bluetooth serves as a transport channel for serial communication between server and client."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_serial_port_profile/README.md"], "description": "This example provides a simple template for SPP-like communication (also know as wire replacement), where Bluetooth serves as a transport channel for serial communication between server and client.", "id": "template.uc.compatibleSDK.bt_serial_port_profile_server.bluetooth_serial_port_profile/bt_spp_server/SimplicityStudio/bt_serial_port_profile_server.slcp", "text": "Bluetooth - Serial Port Profile (SPP) - Server", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example provides a simple template for SPP-like communication (also know as wire replacement), where Bluetooth serves as a transport channel for serial communication between server and client."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_smart_band/README.md"], "description": "This project demonstrates a Bluetooth smart band application using Silicon Labs development kits. It enables an Android smartphone to connect, send and receive text notifications and sensor data via BLE.", "id": "template.uc.compatibleSDK.bt_smart_band.bluetooth_smart_band/SimplicityStudio/bt_smart_band.slcp", "text": "Bluetooth - Smart Band Application", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "  This project demonstrates a Bluetooth smart band application using Silicon Labs development kits. It enables an Android smartphone to connect, send and receive text notifications and sensor data via BLE."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_blinky/readme.md"], "description": "The classic blinky example using Bluetooth communication. Demonstrates a simple two-way data exchange over GATT. This can be tested with the EFR Connect mobile app.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_soc_blinky.example/bt_soc_blinky/bt_soc_blinky.slcp", "text": "Bluetooth - SoC Blinky", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The classic blinky example using Bluetooth communication. Demonstrates a simple two-way data exchange over GATT. This can be tested with the EFR Connect mobile app.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_dtm/readme.md"], "description": "This example implements the direct test mode (DTM) application for radio testing. DTM commands can be called via UART.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_soc_dtm.example/bt_soc_dtm/bt_soc_dtm.slcp", "text": "Bluetooth - SoC DTM", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example implements the direct test mode (DTM) application for radio testing. DTM commands can be called via UART.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_esl_tag/readme.md"], "description": "This example is an evaluation showcase for a Bluetooth Electronic Shelf Label (ESL) Tag application. This demo includes image and display capabilities of an ESL Tag, utilizing the memory LCD display on the WSTK board while images are stored in RAM, only.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_soc_esl_tag.example/bt_soc_esl_tag/bt_soc_esl_tag.slcp", "text": "Bluetooth - SoC ESL Tag", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example is an evaluation showcase for a Bluetooth Electronic Shelf Label (ESL) Tag application. This demo includes image and display capabilities of an ESL Tag, utilizing the memory LCD display on the WSTK board while images are stored in RAM, only.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_empty/readme.md"], "description": "A minimal project structure, that serves as a starting point for custom Bluetooth applications. The application starts advertising after boot and restarts advertising after a connection is closed.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_soc_empty.example/bt_soc_empty/bt_soc_empty.slcp", "text": "Bluetooth - SoC Empty", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "A minimal project structure, that serves as a starting point for custom Bluetooth applications. The application starts advertising after boot and restarts advertising after a connection is closed.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_iop_test/readme.md"], "description": "This is a test procedure containing several test cases for Bluetooth Low Energy communication. This demo is meant to be used with the EFR Connect mobile app, through the \"Interoperability Test\" tile on the Develop view of the app.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_soc_iop_test_display.example/bt_soc_iop_test/bt_soc_iop_test_display.slcp", "text": "Bluetooth - SoC Interoperability Test", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a test procedure containing several test cases for Bluetooth Low Energy communication. This demo is meant to be used with the EFR Connect mobile app, through the \"Interoperability Test\" tile on the Develop view of the app.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_thermometer/readme.md"], "description": "Implements a GATT Server with the Health Thermometer Profile, which enables a Client device to connect and get temperature data. Temperature is read from the mock relative humidity and temperature sensor.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_soc_thermometer_mock.example/bt_soc_thermometer/bt_soc_thermometer_mock.slcp", "text": "Bluetooth - SoC Thermometer (Mock)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Implements a GATT Server with the Health Thermometer Profile, which enables a Client device to connect and get temperature data. Temperature is read from the mock relative humidity and temperature sensor.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_thermometer/readme_rtos.md"], "description": "Demonstrates the integration of FreeRTOS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer (Mock) sample app.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_soc_thermometer_freertos_mock.example/bt_soc_thermometer/bt_soc_thermometer_freertos_mock.slcp", "text": "Bluetooth - SoC Thermometer (Mock) FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates the integration of FreeRTOS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer (Mock) sample app.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_thermometer/readme_rtos.md"], "description": "Demonstrates the integration of Micrium OS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer (Mock) sample app.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_soc_thermometer_micriumos_mock.example/bt_soc_thermometer/bt_soc_thermometer_micriumos_mock.slcp", "text": "Bluetooth - SoC Thermometer (Mock) Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates the integration of Micrium OS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer (Mock) sample app.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_man_in_the_middle/README.md"], "description": "This example project shows an example for thermometer server authenticator's role in MITM scenario.", "id": "template.uc.compatibleSDK.bt_thermometer_authenticated_server.bluetooth_man_in_the_middle/bluetooth_thermometer_authenticated_server/SimplicityStudio/bt_thermometer_authenticated_server.slcp", "text": "Bluetooth - SoC Thermometer Authenticated Server", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for thermometer server authenticator's role in MITM scenario."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_thermometer_client/readme.md"], "description": "Implements a GATT Client that discovers and connects with up to 4 BLE devices advertising themselves as Thermometer Servers. It displays the discovery process and the temperature values received via UART.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_soc_thermometer_client.example/bt_soc_thermometer_client/bt_soc_thermometer_client.slcp", "text": "Bluetooth - SoC Thermometer Client", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Implements a GATT Client that discovers and connects with up to 4 BLE devices advertising themselves as Thermometer Servers. It displays the discovery process and the temperature values received via UART.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_throughput/readme.md"], "description": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using EFR Connect mobile app, through the Throughput demo tile.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_soc_throughput_display.example/bt_soc_throughput/bt_soc_throughput_display.slcp", "text": "Bluetooth - SoC Throughput (with display)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using EFR Connect mobile app, through the Throughput demo tile.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_ibeacon/readme.md"], "description": "Sends non-connectable advertisements in iBeacon format. The iBeacon Service gives Bluetooth accessories a simple and convenient way to send iBeacons to smartphones. This example can be tested together with the EFR Connect mobile app.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_soc_ibeacon.example/bt_soc_ibeacon/bt_soc_ibeacon.slcp", "text": "Bluetooth - SoC iBeacon", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Sends non-connectable advertisements in iBeacon format. The iBeacon Service gives Bluetooth accessories a simple and convenient way to send iBeacons to smartphones. This example can be tested together with the EFR Connect mobile app.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_thermometer_with_efr32_internal_temperature_sensor/README.md"], "description": "This example is an adaptation of the standard 'SOC - Thermometer' example. However, instead of accessing the Si7021 Temperature and Relative Humidity sensor through I2C, this example uses the EFR32's own internal temperature sensor. This sensor is measured during the production test.", "id": "template.uc.compatibleSDK.bt_thermometer_with_efr32_internal_temp_sensor.bluetooth_thermometer_with_efr32_internal_temperature_sensor/SimplicityStudio/bt_thermometer_with_efr32_internal_temp_sensor.slcp", "text": "Bluetooth - Thermometer with EFR32 Internal Temperature Sensor", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example is an adaptation of the standard 'SOC - Thermometer' example. However, instead of accessing the Si7021 Temperature and Relative Humidity sensor through I2C, this example uses the EFR32's own internal temperature sensor. This sensor is measured during the production test."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/developer/repos/bluetooth_applications/bluetooth_wireless_dtm/README.md"], "description": "This project aims to implement the Wireless Direct Test Mode (DTM) to test transmission/reception capabilites of the Bluetooth-based design.", "id": "template.uc.compatibleSDK.bt_wireless_dtm.bluetooth_wireless_dtm/SimplicityStudio/bt_wireless_dtm.slcp", "text": "Bluetooth - Wireless Direct Test Mode (DTM)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project aims to implement the Wireless Direct Test Mode (DTM) to test transmission/reception capabilites of the Bluetooth-based design."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_ncp_empty/readme.md"], "description": "An NCP Target C application that makes it possible for the NCP Host Controller to access the Bluetooth Mesh stack via UART. It provides access to the host layer via BGAPI and not to the link layer via HCI. Contains models from BT mesh specification 1.1.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_ncp_empty_v1_1.example/btmesh_ncp_empty/btmesh_ncp_empty_v1_1.slcp", "text": "Bluetooth Mesh - NCP Empty v1.1", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An NCP Target C application that makes it possible for the NCP Host Controller to access the Bluetooth Mesh stack via UART. It provides access to the host layer via BGAPI and not to the link layer via HCI. Contains models from BT mesh specification 1.1.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_sensor_ambient_light/readme.md"], "description": "An out-of-the-box Software Demo where the device acts as an ambient light sensor in a Networked Lighting Control (NLC) system. The device simulates ambient light measurements and sends these to the network. Properly configured NLC Basic Lightness Controllers then can act on the received data.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_nlc_sensor_ambient_light.example/btmesh_soc_nlc_sensor_ambient_light/btmesh_soc_nlc_sensor_ambient_light_mock_display.slcp", "text": "Bluetooth Mesh - NLC Ambient Light Sensor (Mock)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as an ambient light sensor in a Networked Lighting Control (NLC) system. The device simulates ambient light measurements and sends these to the network. Properly configured NLC Basic Lightness Controllers then can act on the received data.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_basic_lightness_controller/readme.md"], "description": "An out-of-the-box software demo where the device acts as a Basic Lightness controller in a Networked Lighting Control (NLC) system. The device listens to messages from other NLC devices, namely Occupancy Sensor, Ambient Light Sensor, Dimming Control and Basic Scene Selector nodes. This project uses the LEDs and display on the WSTK.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_nlc_basic_lightness_controller.example/btmesh_soc_nlc_basic_lightness_controller/btmesh_soc_nlc_basic_lightness_controller_display.slcp", "text": "Bluetooth Mesh - NLC Basic Lightness Controller", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box software demo where the device acts as a Basic Lightness controller in a Networked Lighting Control (NLC) system. The device listens to messages from other NLC devices, namely Occupancy Sensor, Ambient Light Sensor, Dimming Control and Basic Scene Selector nodes.\nThis project uses the LEDs and display on the WSTK.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_basic_scene_selector/readme.md"], "description": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_nlc_basic_scene_selector.example/btmesh_soc_nlc_basic_scene_selector/btmesh_soc_nlc_basic_scene_selector_display.slcp", "text": "Bluetooth Mesh - NLC Basic Scene Selector", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_basic_scene_selector/readme_low_power.md"], "description": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_nlc_basic_scene_selector_low_power.example/btmesh_soc_nlc_basic_scene_selector/btmesh_soc_nlc_basic_scene_selector_low_power.slcp", "text": "Bluetooth Mesh - NLC Basic Scene Selector Low Power", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_dimming_control/readme.md"], "description": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. <PERSON><PERSON> But<PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_nlc_dimming_control.example/btmesh_soc_nlc_dimming_control/btmesh_soc_nlc_dimming_control_display.slcp", "text": "Bluetooth Mesh - NLC Dimming Control", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. <PERSON><PERSON> But<PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_dimming_control/readme_low_power.md"], "description": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_nlc_dimming_control_low_power.example/btmesh_soc_nlc_dimming_control/btmesh_soc_nlc_dimming_control_low_power.slcp", "text": "Bluetooth Mesh - NLC Dimming Control Low Power", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_sensor_occupancy/readme.md"], "description": "An out-of-the-box Software Demo where the device acts as an Occupancy Sensor in a Networked Lighting Control (NLC) system. <PERSON><PERSON> Button presses imitate people count changes which can control a properly configured NLC Basic Lightness Controller.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_nlc_sensor_occupancy.example/btmesh_soc_nlc_sensor_occupancy/btmesh_soc_nlc_sensor_occupancy_mock_display.slcp", "text": "Bluetooth Mesh - NLC Occupancy Sensor", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as an Occupancy Sensor in a Networked Lighting Control (NLC) system. <PERSON><PERSON> Button presses imitate people count changes which can control a properly configured NLC Basic Lightness Controller.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_dfu_distributor/readme.md"], "description": "Demonstrates the Firmware Distributor role based on the BT Mesh Model specification. Distributor is responsible for delivering new firmware images to the Target nodes and monitoring the progress of the firmware update.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_dfu_distributor.example/btmesh_soc_dfu_distributor/btmesh_soc_dfu_distributor_display.slcp", "text": "Bluetooth Mesh - SoC DFU Distributor", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates the Firmware Distributor role based on the BT Mesh Model specification. Distributor is responsible for delivering new firmware images to the Target nodes and monitoring the progress of the firmware update.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_empty/readme.md"], "description": "Demonstrates the bare minimum needed for a Bluetooth Mesh C application. The application starts Unprovisioned Device Beaconing after booting, and then waits to be provisioned.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_empty.example/btmesh_soc_empty/btmesh_soc_empty.slcp", "text": "Bluetooth Mesh - SoC Empty", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates the bare minimum needed for a Bluetooth Mesh C application. The application starts Unprovisioned Device Beaconing after booting, and then waits to be provisioned.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_light_ctl/readme.md"], "description": "An out-of-the-box software demo where the LEDs of the WSTK can be switched on and off, and their lighting intensity, color temperature, and delta UV can be set.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_light_ctl.example/btmesh_soc_light_ctl/btmesh_soc_light_ctl_display.slcp", "text": "Bluetooth Mesh - SoC Light CTL", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box software demo where the LEDs of the WSTK can be switched on and off, and their lighting intensity, color temperature, and delta UV can be set.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_light_hsl/readme.md"], "description": "An out-of-the-box software demo where the LEDs of the mainboard can be switched on and off, and their lighting intensity, hue, and saturation can be set. The example also tries to establish friendship as a Friend node.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_light_hsl.example/btmesh_soc_light_hsl/btmesh_soc_light_hsl_display.slcp", "text": "Bluetooth Mesh - SoC Light HSL", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box software demo where the LEDs of the mainboard can be switched on and off, and their lighting intensity, hue, and saturation can be set. The example also tries to establish friendship as a Friend node.\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": [], "description": "An out-of-the-box software demo which can provision and configure nearby switch devices. The LEDs of the mainboard can be switched on and off and their lighting intensity can be set either by push buttons or by the provisioned switches.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_Bluetooth Mesh - SoC Provisioner Light Lightness_asset:..com.silabs.sdk.stack.super_4.4.4.app.btmesh.demos.btmesh_soc_provisioner_light_lightness.btmesh_soc_provisioner_light_lightness-brd4180a.s37", "text": "Bluetooth Mesh - SoC Provisioner Light Lightness", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box software demo which can provision and configure nearby switch devices.\nThe LEDs of the mainboard can be switched on and off and their lighting intensity can be set either by push buttons or by the provisioned switches.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_sensor_client/readme.md"], "description": "This example demonstrates the Bluetooth Mesh Sensor Client Model. It collects and displays sensor measurement data from remote device(s) (e.g., btmesh_soc_sensor_server).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_sensor_client.example/btmesh_soc_sensor_client/btmesh_soc_sensor_client_display.slcp", "text": "Bluetooth Mesh - SoC Sensor Client", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example demonstrates the Bluetooth Mesh Sensor Client Model. It collects and displays sensor measurement data from remote device(s) (e.g., btmesh_soc_sensor_server).\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_sensor_server/readme.md"], "description": "This example demonstrates the Bluetooth Mesh Sensor Server Model and Sensor Setup Server Model. If available, it measures CPU temperature and uses that data as temperature reading, otherwise it sends mocked temperature data to a remote device (e.g., btmesh_soc_sensor_client).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_sensor_thermometer.example/btmesh_soc_sensor_thermometer/btmesh_soc_sensor_thermometer_mock_display.slcp", "text": "Bluetooth Mesh - SoC Sensor Thermometer", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example demonstrates the Bluetooth Mesh Sensor Server Model and Sensor Setup Server Model. If available, it measures CPU temperature and uses that data as temperature reading, otherwise it sends mocked temperature data to a remote device (e.g., btmesh_soc_sensor_client).\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_switch/readme.md"], "description": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device. Note - this example is not compatible with the Dimming Control NLC Profile.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_switch_ctl.example/btmesh_soc_switch_ctl/btmesh_soc_switch_ctl_display.slcp", "text": "Bluetooth Mesh - SoC Switch CTL", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device.\nNote - this example is not compatible with the Dimming Control NLC Profile.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_switch/readme_low_power.md"], "description": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. It is optimized for low current consumption with disabled CLI, logging, and LCD. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device. Note - this example is not compatible with the Dimming Control NLC Profile.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.btmesh_soc_switch_ctl_low_power.example/btmesh_soc_switch_ctl/btmesh_soc_switch_ctl_low_power.slcp", "text": "Bluetooth Mesh - SoC Switch CTL Low Power", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. It is optimized for low current consumption with disabled CLI, logging, and LCD. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device.\nNote - this example is not compatible with the Dimming Control NLC Profile.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_rail_dmp_soc_empty_std/readme.md"], "description": "A minimal project structure used as a starting point for custom Bluetooth + Standard DMP (Dynamic Multiprotocol) applications. It runs on top of FreeRTOS and multiprotocol RAIL utilizing IEEE 802.15.4 standard protocol.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_rail_dmp_soc_empty_std_freertos.example/bt_rail_dmp_soc_empty_std/bt_rail_dmp_soc_empty_std_freertos.slcp", "text": "Bluetooth RAIL DMP - SoC Empty Standard FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "A minimal project structure used as a starting point for custom Bluetooth + Standard DMP (Dynamic Multiprotocol) applications. It runs on top of FreeRTOS and multiprotocol RAIL utilizing IEEE 802.15.4 standard protocol.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_rail_dmp_soc_empty_std/readme.md"], "description": "A minimal project structure used as a starting point for custom Bluetooth + Standard DMP (Dynamic Multiprotocol) applications. It runs on top of Micrium OS and multiprotocol RAIL utilizing IEEE 802.15.4 standard protocol.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_rail_dmp_soc_empty_std_micriumos.example/bt_rail_dmp_soc_empty_std/bt_rail_dmp_soc_empty_std_micriumos.slcp", "text": "Bluetooth RAIL DMP - SoC Empty Standard Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "A minimal project structure used as a starting point for custom Bluetooth + Standard DMP (Dynamic Multiprotocol) applications. It runs on top of Micrium OS and multiprotocol RAIL utilizing IEEE 802.15.4 standard protocol.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_rail_dmp_soc_light_std/readme.md"], "description": "This is a Dynamic Multiprotocol reference application demonstrating a light bulb that can be switched both via Bluetooth and via a standard protocol. Can be tested with the EFR Connect mobile app and Flex (RAIL) Switch Standards sample app.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_rail_dmp_soc_light_std_freertos.example/bt_rail_dmp_soc_light_std/bt_rail_dmp_soc_light_std_freertos.slcp", "text": "Bluetooth RAIL DMP - SoC Light Standard FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a Dynamic Multiprotocol reference application demonstrating a light bulb that can be switched both via Bluetooth and via a standard protocol. Can be tested with the EFR Connect mobile app and Flex (RAIL) Switch Standards sample app.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_rail_dmp_soc_light_std/readme.md"], "description": "This is a Dynamic Multiprotocol reference application demonstrating a light bulb that can be switched both via Bluetooth and via a standard protocol. Can be tested with the EFR Connect mobile app and Flex (RAIL) Switch Standards sample app.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_rail_dmp_soc_light_std_micriumos.example/bt_rail_dmp_soc_light_std/bt_rail_dmp_soc_light_std_micriumos.slcp", "text": "Bluetooth RAIL DMP - SoC Light Standard Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a Dynamic Multiprotocol reference application demonstrating a light bulb that can be switched both via Bluetooth and via a standard protocol. Can be tested with the EFR Connect mobile app and Flex (RAIL) Switch Standards sample app.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-uart-bgapi/readme.md"], "description": "Standalone Bootloader using the BGAPI protocol for UART DFU. This is the recommended UART bootloader for the BLE protocol stack.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bootloader-uart-bgapi.sample-apps/Series-2/bootloader-uart-bgapi/bootloader-uart-bgapi.slcp", "text": "Bootloader - NCP BGAPI UART DFU", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Standalone Bootloader using the BGAPI protocol for UART DFU. This is the recommended UART bootloader for the BLE protocol stack.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-spi-ezsp/readme.md"], "description": "Standalone Bootloader using the EZSP protocol over SPI. This is the recommended SPI bootloader for the EmberZNet and Connect protocol stacks.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bootloader-spi-ezsp.sample-apps/Series-2/bootloader-spi-ezsp/bootloader-spi-ezsp.slcp", "text": "Bootloader - NCP EZSP SPI", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Standalone Bootloader using the EZSP protocol over SPI. This is the recommended SPI bootloader for the EmberZNet and Connect protocol stacks.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-uart-xmodem/readme.md"], "description": "Standalone Bootloader using XMODEM-CRC over UART. The bootloader shows a menu, where an XMODEM transfer can be started by sending ASCII '1', or the application can be started by sending ASCII '2'. This is the recommended UART bootloader for the EmberZNet and Connect protocol stacks.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bootloader-uart-xmodem.sample-apps/Series-2/bootloader-uart-xmodem/bootloader-uart-xmodem.slcp", "text": "Bootloader - NCP UART XMODEM", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Standalone Bootloader using XMODEM-CRC over UART. The bootloader shows a menu, where an XMODEM transfer can be started by sending ASCII '1', or the application can be started by sending ASCII '2'. This is the recommended UART bootloader for the EmberZNet and Connect protocol stacks.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-apploader/readme.md"], "description": "Standalone Bootloader using the Bluetooth AppLoader OTA DFU. This implements in-place application updates using Bluetooth connection.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bootloader-apploader.sample-apps/Series-2/bootloader-apploader/bootloader-apploader.slcp", "text": "Bootloader - SoC Bluetooth AppLoader OTA DFU", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Standalone Bootloader using the Bluetooth AppLoader OTA DFU. This implements in-place application updates using Bluetooth connection.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-storage-internal-single/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x84000 (or 0x8084000 for device with 0x8000000 flash base), and have a size of 448 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bootloader-storage-internal-single.sample-apps/Series-2/bootloader-storage-internal-single/bootloader-storage-internal-single.slcp", "text": "Bootloader - SoC Internal Storage (single image on 1MB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x84000 (or 0x8084000 for device with 0x8000000 flash base), and have a size of 448 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-storage-internal-single-352k/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x28000 (or 0x8028000 for device with 0x8000000 flash base), and have a size of 120 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bootloader-storage-internal-single-352k.sample-apps/Series-2/bootloader-storage-internal-single-352k/bootloader-storage-internal-single-352k.slcp", "text": "Bootloader - SoC Internal Storage (single image on 352kB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x28000 (or 0x8028000 for device with 0x8000000 flash base), and have a size of 120 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-storage-internal-single-512k/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x44000 (or 0x8044000 for device with 0x8000000 flash base), and have a size of 192 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bootloader-storage-internal-single-512k.sample-apps/Series-2/bootloader-storage-internal-single-512k/bootloader-storage-internal-single-512k.slcp", "text": "Bootloader - SoC Internal Storage (single image on 512kB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x44000 (or 0x8044000 for device with 0x8000000 flash base), and have a size of 192 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-storage-internal-single-768k/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x5C000 (or 0x805C000 for device with 0x8000000 flash base), and have a size of 368 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bootloader-storage-internal-single-768k.sample-apps/Series-2/bootloader-storage-internal-single-768k/bootloader-storage-internal-single-768k.slcp", "text": "Bootloader - SoC Internal Storage (single image on 768kB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x5C000 (or 0x805C000 for device with 0x8000000 flash base), and have a size of 368 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-storage-internal-single-lzma/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x84000 (or 0x8084000 for device with 0x8000000 flash base), and have a size of 448 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bootloader-storage-internal-single-lzma.sample-apps/Series-2/bootloader-storage-internal-single-lzma/bootloader-storage-internal-single-lzma.slcp", "text": "Bootloader - SoC Internal Storage (single image with LZMA compression, 1MB flash)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x84000 (or 0x8084000 for device with 0x8000000 flash base), and have a size of 448 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/flex/documentation/example/connect/connect_ncp_app/readme.md"], "description": "Connect: NCP Application for the SoC", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.connect_ncp.../../protocol/flex/app/ncp-app/connect_ncp.slcp", "text": "Connect - NCP", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Connect: NCP Application for the SoC"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/flex/documentation/example/connect/connect_soc_direct_mode_device/readme.md"], "description": "This sample app allows direct commissioning of nodes and exchange data between them via CLI commands.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.connect_soc_direct_mode_device.example/connect/connect_soc_direct_mode_device/connect_soc_direct_mode_device.slcp", "text": "Connect - SoC Direct Mode Device", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample app allows direct commissioning of nodes and exchange data between them via CLI commands."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/flex/documentation/example/connect/connect_soc_ecdh_key_exchange/readme.md"], "description": "This sample application illustrates how to share the network key between multiple devices in a secure way (using Elliptic-curve <PERSON><PERSON><PERSON><PERSON> (ECDH) key agreement protocol).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.connect_soc_ecdh_key_exchange.example/connect/connect_soc_ecdh_key_exchange/connect_soc_ecdh_key_exchange.slcp", "text": "Connect - SoC ECDH Key Exchange", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample application illustrates how to share the network key between multiple devices in a secure way (using Elliptic-curve <PERSON><PERSON><PERSON><PERSON> (ECDH) key agreement protocol)."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/flex/documentation/example/connect/connect_soc_empty/readme.md"], "description": "The Connect Empty project is a barebone Connect app that can be a basis of streamlined proprietary solutions.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.connect_soc_empty.example/connect/connect_soc_empty/connect_soc_empty.slcp", "text": "Connect - SoC Empty", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Connect Empty project is a barebone Connect app that can be a basis of streamlined proprietary solutions."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/flex/documentation/example/connect/connect_soc_mac_mode_device/readme.md"], "description": "A 802.15.4 sample app that provides CLI commands to form a network or join an existing network, send data to another node based on short or long addresses.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.connect_soc_mac_mode_device.example/connect/connect_soc_mac_mode_device/connect_soc_mac_mode_device.slcp", "text": "Connect - SoC MAC Mode Device", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "A 802.15.4 sample app that provides CLI commands to form a network or join an existing network, send data to another node based on short or long addresses."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/flex/documentation/example/connect/connect_sink_sensor/readme.md"], "description": "The Sink example is the counterpart of the Sensor example. It receives reports of Sensor nodes joining to its network.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.connect_soc_sink.example/connect/sensor_sink/connect_soc_sink/connect_soc_sink.slcp", "text": "Connect - SoC Sink", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Sink example is the counterpart of the Sensor example. It receives reports of Sensor nodes joining to its network."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/flex/documentation/example/connect/connect_bt_dmp_soc_empty/readme.md"], "description": "The Connect Empty DMP example is an RTOS-based project that provides a skeleton for Connect but not functions, beside a BLE Task with a basic CLI interface.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.connect_bt_dmp_soc_empty.example/connect/connect_bt_dmp_soc_empty/connect_bt_dmp_soc_empty.slcp", "text": "Connect Bluetooth DMP - SoC Empty", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Connect Empty DMP example is an RTOS-based project that provides a skeleton for Connect but not functions, beside a BLE Task with a basic CLI interface."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "This example project shows an empty configuration that can be used as a starting point to add components and functionality.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.cExeProject", "text": "Empty C Project", "priority": 1, "category": "SOFTWARE", "toolTipText": "This example project shows an empty configuration that can be used as a starting point to add components and functionality."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "This example project shows an empty configuration that can be used as a starting point to add components and functionality.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.cppExeProject", "text": "Empty C++ Project", "priority": 1, "category": "SOFTWARE", "toolTipText": "This example project shows an empty configuration that can be used as a starting point to add components and functionality."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/ml_blink/readme.md"], "description": "This application demonstrates a model trained to replicate a sine function.  The model is continuously fed with values ranging from 0 to 2pi, and the  output of the model is used to control the intensity of an LED.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ml_blink.example/ml_blink/ml_blink.slcp", "text": "Machine Learning - Blink", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This application demonstrates a model trained to replicate a sine function.  The model is continuously fed with values ranging from 0 to 2pi, and the  output of the model is used to control the intensity of an LED. \n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/ml_model_profiler/readme.md"], "description": "This application profiles a ML model. The ML model is loaded as a byte array which is generated from a Tensorflow tflite model file. Profiling is performed by running one inference with the model.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ml_model_profiler.example/ml_model_profiler/ml_model_profiler.slcp", "text": "Machine Learning - Model Profiler", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This application profiles a ML model. The ML model is loaded as a byte array which is generated from a Tensorflow tflite model file. Profiling is performed by running one inference with the model.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ncp/README-MP-RCP.md"], "description": "This multiprotocol radio co-processor (RCP) application supports running OpenThread and Zigbee stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a SPI link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.rcp-spi-802154.sample-apps/ot-ncp/rcp-spi-802154.slcp", "text": "Multiprotocol (OpenThread+Zigbee) - RCP (SPI)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This multiprotocol radio co-processor (RCP) application supports running OpenThread and Zigbee stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a SPI link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ncp/README-MP-RCP.md"], "description": "This multiprotocol radio co-processor (RCP) application supports running OpenThread and Zigbee stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a SPI link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_Multiprotocol (OpenThread+Zigbee) - RCP (SPI)_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.openthread.demos.rcp-spi-802154.rcp-spi-802154-brd4180a.s37", "text": "Multiprotocol (OpenThread+Zigbee) - RCP (SPI)", "priority": 0, "category": "DEMOS", "toolTipText": "This multiprotocol radio co-processor (RCP) application supports running OpenThread and Zigbee stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a SPI link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ncp/README-MP-RCP.md"], "description": "This multiprotocol radio co-processor (RCP) application supports running OpenThread and Zigbee stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a UART link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.rcp-uart-802154.sample-apps/ot-ncp/rcp-uart-802154.slcp", "text": "Multiprotocol (OpenThread+Zigbee) - RCP (UART)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This multiprotocol radio co-processor (RCP) application supports running OpenThread and Zigbee stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a UART link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ncp/README-MP-RCP.md"], "description": "This multiprotocol radio co-processor (RCP) application supports running OpenThread and Zigbee stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a UART link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_Multiprotocol (OpenThread+Zigbee) - RCP (UART)_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.openthread.demos.rcp-uart-802154.rcp-uart-802154-brd4180a.s37", "text": "Multiprotocol (OpenThread+Zigbee) - RCP (UART)", "priority": 0, "category": "DEMOS", "toolTipText": "This multiprotocol radio co-processor (RCP) application supports running OpenThread and Zigbee stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a UART link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ncp/README-MP-DMP-RCP.md"], "description": "This multiprotocol radio co-processor (RCP) application supports running OpenThread, Zigbee, and Bluetooth stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel, and dynamic multiprotocol (DMP) to run the Bluetooth Link Layer simultaneously. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a SPI link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.rcp-spi-802154-blehci.sample-apps/ot-ncp/rcp-spi-802154-blehci.slcp", "text": "Multiprotocol (OpenThread+Zigbee+BLE) - RCP (SPI)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This multiprotocol radio co-processor (RCP) application supports running OpenThread, Zigbee, and Bluetooth stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel, and dynamic multiprotocol (DMP) to run the Bluetooth Link Layer simultaneously. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a SPI link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ncp/README-MP-DMP-RCP.md"], "description": "This multiprotocol radio co-processor (RCP) application supports running OpenThread, Zigbee, and Bluetooth stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel, and dynamic multiprotocol (DMP) to run the Bluetooth Link Layer simultaneously. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a SPI link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_Multiprotocol (OpenThread+Zigbee+BLE) - RCP (SPI)_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.openthread.demos.rcp-spi-802154-blehci.rcp-spi-802154-blehci-brd4180a.s37", "text": "Multiprotocol (OpenThread+Zigbee+BLE) - RCP (SPI)", "priority": 0, "category": "DEMOS", "toolTipText": "This multiprotocol radio co-processor (RCP) application supports running OpenThread, Zigbee, and Bluetooth stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel, and dynamic multiprotocol (DMP) to run the Bluetooth Link Layer simultaneously. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a SPI link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ncp/README-MP-DMP-RCP.md"], "description": "This multiprotocol radio co-processor (RCP) application supports running OpenThread, Zigbee, and Bluetooth stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel, and dynamic multiprotocol (DMP) to run the Bluetooth Link Layer simultaneously. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a UART link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.rcp-uart-802154-blehci.sample-apps/ot-ncp/rcp-uart-802154-blehci.slcp", "text": "Multiprotocol (OpenThread+Zigbee+BLE) - RCP (UART)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This multiprotocol radio co-processor (RCP) application supports running OpenThread, Zigbee, and Bluetooth stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel, and dynamic multiprotocol (DMP) to run the Bluetooth Link Layer simultaneously. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a UART link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ncp/README-MP-DMP-RCP.md"], "description": "This multiprotocol radio co-processor (RCP) application supports running OpenThread, Zigbee, and Bluetooth stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel, and dynamic multiprotocol (DMP) to run the Bluetooth Link Layer simultaneously. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a UART link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_Multiprotocol (OpenThread+Zigbee+BLE) - RCP (UART)_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.openthread.demos.rcp-uart-802154-blehci.rcp-uart-802154-blehci-brd4180a.s37", "text": "Multiprotocol (OpenThread+Zigbee+BLE) - RCP (UART)", "priority": 0, "category": "DEMOS", "toolTipText": "This multiprotocol radio co-processor (RCP) application supports running OpenThread, Zigbee, and Bluetooth stacks simultaneously on a host processor. It uses concurrent multiprotocol (CMP) / multi-PAN functionality to run the 802.15.4 networks simultaneously on the same channel, and dynamic multiprotocol (DMP) to run the Bluetooth Link Layer simultaneously. The host stacks and the RCP communicate using the Co-Processor Communication protocol (CPC), which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a UART link. Refer to *AN1333: Running Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-processor* for more information on running the multiprotocol RCP with different host applications.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ncp/README-OT-NCP.md"], "description": "This is a simple OpenThread Full Thread Device NCP application. This is equivalent to the ot-ncp-ftd application in the OpenThread GitHub repo.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ot-ncp-ftd.sample-apps/ot-ncp/ot-ncp-ftd.slcp", "text": "OpenThread - NCP (FTD)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a simple OpenThread Full Thread Device NCP application. This is equivalent to the ot-ncp-ftd application in the OpenThread GitHub repo.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ncp/README-OT-NCP.md"], "description": "This is a simple OpenThread Minimal Thread Device NCP application. This is equivalent to the ot-ncp-mtd application in the OpenThread GitHub repo.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ot-ncp-mtd.sample-apps/ot-ncp/ot-ncp-mtd.slcp", "text": "OpenThread - NCP (MTD)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a simple OpenThread Minimal Thread Device NCP application. This is equivalent to the ot-ncp-mtd application in the OpenThread GitHub repo.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ncp/README-OT-RCP.md"], "description": "This is a simple OpenThread RCP application. This is equivalent to the ot-rcp application in the OpenThread GitHub repo.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ot-rcp.sample-apps/ot-ncp/ot-rcp.slcp", "text": "OpenThread - RCP", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a simple OpenThread RCP application. This is equivalent to the ot-rcp application in the OpenThread GitHub repo.\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ncp/README-OT-RCP.md"], "description": "This is a simple OpenThread RCP application. This is equivalent to the ot-rcp application in the OpenThread GitHub repo.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_OpenThread - RCP_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.openthread.demos.ot-rcp.ot-rcp-brd4180a.s37", "text": "OpenThread - RCP", "priority": 0, "category": "DEMOS", "toolTipText": "This is a simple OpenThread RCP application. This is equivalent to the ot-rcp application in the OpenThread GitHub repo.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-cli/README.md"], "description": "This is a very simple CLI application to test the OpenThread stack on a Full Thread Device. This is equivalent to the ot-cli-ftd application in the OpenThread GitHub repo.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ot-cli-ftd.sample-apps/ot-cli/ot-cli-ftd.slcp", "text": "OpenThread - SoC CLI (FTD)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a very simple CLI application to test the OpenThread stack on a Full Thread Device. This is equivalent to the ot-cli-ftd application in the OpenThread GitHub repo.\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-cli/README.md"], "description": "This is a very simple CLI application to test the OpenThread stack on a Full Thread Device. This is equivalent to the ot-cli-ftd application in the OpenThread GitHub repo.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_OpenThread - SoC CLI (FTD)_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.openthread.demos.ot-cli-ftd.ot-cli-ftd-brd4180a.s37", "text": "OpenThread - SoC CLI (FTD)", "priority": 0, "category": "DEMOS", "toolTipText": "This is a very simple CLI application to test the OpenThread stack on a Full Thread Device. This is equivalent to the ot-cli-ftd application in the OpenThread GitHub repo.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-cli/README.md"], "description": "This is a very simple CLI application to test the OpenThread stack on a Minimal Thread Device. This is equivalent to the ot-cli-mtd application in the OpenThread GitHub repo.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ot-cli-mtd.sample-apps/ot-cli/ot-cli-mtd.slcp", "text": "OpenThread - SoC CLI (MTD)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a very simple CLI application to test the OpenThread stack on a Minimal Thread Device. This is equivalent to the ot-cli-mtd application in the OpenThread GitHub repo.\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-cli/README.md"], "description": "This is a very simple CLI application to test the OpenThread stack on a Minimal Thread Device. This is equivalent to the ot-cli-mtd application in the OpenThread GitHub repo.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_OpenThread - SoC CLI (MTD)_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.openthread.demos.ot-cli-mtd.ot-cli-mtd-brd4180a.s37", "text": "OpenThread - SoC CLI (MTD)", "priority": 0, "category": "DEMOS", "toolTipText": "This is a very simple CLI application to test the OpenThread stack on a Minimal Thread Device. This is equivalent to the ot-cli-mtd application in the OpenThread GitHub repo.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/sleepy-demo/README.md"], "description": "This is a sample application to start and form a Thread network on an FTD for the sleepy-demo.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.sleepy-demo-ftd.sample-apps/sleepy-demo/sleepy-demo-ftd.slcp", "text": "OpenThread - SoC Sleepy Demo (FTD)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a sample application to start and form a Thread network on an FTD for the sleepy-demo.\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/sleepy-demo/README.md"], "description": "This is a sample application to start and form a Thread network on an FTD for the sleepy-demo.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_OpenThread - SoC Sleepy Demo (FTD)_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.openthread.demos.sleepy-demo-ftd.sleepy-demo-ftd-brd4180a.s37", "text": "OpenThread - SoC Sleepy Demo (FTD)", "priority": 0, "category": "DEMOS", "toolTipText": "This is a sample application to start and form a Thread network on an FTD for the sleepy-demo.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/sleepy-demo/README.md"], "description": "This is a sample application to demonstrate Sleepy End Device (SED) behaviour with polling, using the EFR32's low power EM2 mode.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.sleepy-demo-mtd.sample-apps/sleepy-demo/sleepy-demo-mtd.slcp", "text": "OpenThread - SoC Sleepy Demo (MTD)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a sample application to demonstrate Sleepy End Device (SED) behaviour with polling, using the EFR32's low power EM2 mode.\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/sleepy-demo/README.md"], "description": "This is a sample application to demonstrate Sleepy End Device (SED) behaviour with polling, using the EFR32's low power EM2 mode.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_OpenThread - SoC Sleepy Demo (MTD)_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.openthread.demos.sleepy-demo-mtd.sleepy-demo-mtd-brd4180a.s37", "text": "OpenThread - SoC Sleepy Demo (MTD)", "priority": 0, "category": "DEMOS", "toolTipText": "This is a sample application to demonstrate Sleepy End Device (SED) behaviour with polling, using the EFR32's low power EM2 mode.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/sleepy-demo/README.md"], "description": "This is a sample application to demonstrate Synchronous Sleepy End Device (SSED) behaviour with CSL, using the EFR32's low power EM2 mode.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.sleepy-demo-ssed.sample-apps/sleepy-demo/sleepy-demo-ssed.slcp", "text": "OpenThread - SoC Sleepy Demo (SSED)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a sample application to demonstrate Synchronous Sleepy End Device (SSED) behaviour with CSL, using the EFR32's low power EM2 mode.\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/sleepy-demo/README.md"], "description": "This is a sample application to demonstrate Synchronous Sleepy End Device (SSED) behaviour with CSL, using the EFR32's low power EM2 mode.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_OpenThread - SoC Sleepy Demo (SSED)_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.openthread.demos.sleepy-demo-ssed.sleepy-demo-ssed-brd4180a.s37", "text": "OpenThread - SoC Sleepy Demo (SSED)", "priority": 0, "category": "DEMOS", "toolTipText": "This is a sample application to demonstrate Synchronous Sleepy End Device (SSED) behaviour with CSL, using the EFR32's low power EM2 mode.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ble-dmp/README.md"], "description": "This is a simple application to test DMP (Dynamic MultiProtocol) with OpenThread and Bluetooth running on FreeRTOS. It is identical to the ot-ble-dmp application with the exception that support for buttons has been removed. This application is meant to be used for boards which don't have buttons.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ot-ble-dmp-no-buttons.sample-apps/ot-ble-dmp/ot-ble-dmp-no-buttons.slcp", "text": "OpenThread BLE DMP (No buttons) - SoC FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a simple application to test DMP (Dynamic MultiProtocol) with OpenThread and Bluetooth running on FreeRTOS.\nIt is identical to the ot-ble-dmp application with the exception that support for buttons has been removed. This application is meant to be used for boards which don't have buttons.\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ble-dmp/README.md"], "description": "This is a simple application to test DMP (Dynamic MultiProtocol) with OpenThread and Bluetooth running on FreeRTOS. It is identical to the ot-ble-dmp application with the exception that support for buttons has been removed. This application is meant to be used for boards which don't have buttons.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_OpenThread BLE DMP (No buttons) - SoC FreeRTOS_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.openthread.demos.ot-ble-dmp-no-buttons.ot-ble-dmp-no-buttons-brd4180a.s37", "text": "OpenThread BLE DMP (No buttons) - SoC FreeRTOS", "priority": 0, "category": "DEMOS", "toolTipText": "This is a simple application to test DMP (Dynamic MultiProtocol) with OpenThread and Bluetooth running on FreeRTOS.\nIt is identical to the ot-ble-dmp application with the exception that support for buttons has been removed. This application is meant to be used for boards which don't have buttons.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ble-dmp/README.md"], "description": "This is a simple application to test DMP (Dynamic MultiProtocol) with OpenThread and Bluetooth running on FreeRTOS.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ot-ble-dmp.sample-apps/ot-ble-dmp/ot-ble-dmp.slcp", "text": "OpenThread BLE DMP - SoC FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a simple application to test DMP (Dynamic MultiProtocol) with OpenThread and Bluetooth running on FreeRTOS.\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-ble-dmp/README.md"], "description": "This is a simple application to test DMP (Dynamic MultiProtocol) with OpenThread and Bluetooth running on FreeRTOS.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_OpenThread BLE DMP - SoC FreeRTOS_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.openthread.demos.ot-ble-dmp.ot-ble-dmp-brd4180a.s37", "text": "OpenThread BLE DMP - SoC FreeRTOS", "priority": 0, "category": "DEMOS", "toolTipText": "This is a simple application to test DMP (Dynamic MultiProtocol) with OpenThread and Bluetooth running on FreeRTOS.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "The device is initialized for the board it is running on, and the qualified LFRCO is output to a pin where it can be observed.  The calibration mechanism is setup, and a calibration run is started.", "id": "projectTemplate.compatibleSDK.series2/cmu/lfrco_cal_interrupt/SimplicityStudio/BRD4180A_EFR32MG21_lfrco_cal_interrupt.slsproj", "text": "Platform - BRD4180A EFR32MG21 LFRCO CAL Interrupt", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The device is initialized for the board it is running on, and the qualified LFRCO is output to a pin where it can be observed.  The calibration mechanism is setup, and a calibration run is started. "}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "The device is initialized for the board it is running on, and the qualified LFRCO is output to a pin where it can be observed.  The BURTC is setup to generate an interrupt every 16 seconds, and the device enters EM1 (so that the clock output remains active).", "id": "projectTemplate.compatibleSDK.series2/cmu/lfrco_cal_polled/SimplicityStudio/BRD4180A_EFR32MG21_lfrco_cal_polled.slsproj", "text": "Platform - BRD4180A EFR32MG21 LFRCO CAL Polled", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The device is initialized for the board it is running on, and the qualified LFRCO is output to a pin where it can be observed.  The BURTC is setup to generate an interrupt every 16 seconds, and the device enters EM1 (so that the clock output remains active). "}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "This project demonstrates interrupt-drive operation of the USART in asynchronous mode.  USART0 is configured for asynchronous operation at 115200 baud with 8 data bits, no parity, and one stop bit (115200N81). The main loop waits until 80 characters or a carriage ret...", "id": "projectTemplate.compatibleSDK.series2/usart/async_interrupt/SimplicityStudio/BRD4180A_EFR32MG21_usart_async_interrupt.slsproj", "text": "Platform - BRD4180A EFR32MG21 USART Async Interrupt", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project demonstrates interrupt-drive operation of the USART in asynchronous mode.  USART0 is configured for asynchronous operation at 115200 baud with 8 data bits, no parity, and one stop bit (115200N81). The main loop waits until 80 characters or a carriage ret..."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "This project demonstrates polled operation of the USART in asynchronous mode.  USART0 is configured for asynchronous operation at 115200 baud with 8 data bits, no parity, and one stop bit (115200N81).  The main loop waits until 80 characters or a carriage return are ...", "id": "projectTemplate.compatibleSDK.series2/usart/async_polled/SimplicityStudio/BRD4180A_EFR32MG21_usart_async_polled.slsproj", "text": "Platform - BRD4180A EFR32MG21 USART Async Polled", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project demonstrates polled operation of the USART in asynchronous mode.  USART0 is configured for asynchronous operation at 115200 baud with 8 data bits, no parity, and one stop bit (115200N81).  The main loop waits until 80 characters or a carriage return are ..."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "This project demonstrates DMA-driven operation of the USART in synchronous main mode.  USART0 is configured for SPI-compatible operation at 1 Mbps.  Compatibility with the Synchronous Peripheral Interface standard implies a word size of 8 data bits transmitted and re...", "id": "projectTemplate.compatibleSDK.series2/usart/spi_main_dma/SimplicityStudio/BRD4180A_EFR32MG21_usart_spi_main_dma.slsproj", "text": "Platform - BRD4180A EFR32MG21 USART SPI Main DMA", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project demonstrates DMA-driven operation of the USART in synchronous main mode.  USART0 is configured for SPI-compatible operation at 1 Mbps.  Compatibility with the Synchronous Peripheral Interface standard implies a word size of 8 data bits transmitted and re..."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "This project demonstrates interrupt-driven operation of the USART in synchronous main mode.  USART0 is configured for SPI-compatible operation at 1 Mbps.  Compatibility with the Synchronous Peripheral Interface standard implies a word size of 8 data bits transmitted ...", "id": "projectTemplate.compatibleSDK.series2/usart/spi_main_interrupt/SimplicityStudio/BRD4180A_EFR32MG21_usart_spi_main_interrupt.slsproj", "text": "Platform - BRD4180A EFR32MG21 USART SPI Main Interrupt", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project demonstrates interrupt-driven operation of the USART in synchronous main mode.  USART0 is configured for SPI-compatible operation at 1 Mbps.  Compatibility with the Synchronous Peripheral Interface standard implies a word size of 8 data bits transmitted ..."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "This project demonstrates polled operation of the USART in synchronous main mode.  USART0 is configured for SPI-compatible operation at 1 Mbps.  Compatibility with the Synchronous Peripheral Interface standard implies a word size of 8 data bits transmitted and receiv...", "id": "projectTemplate.compatibleSDK.series2/usart/spi_main_polled/SimplicityStudio/BRD4180A_EFR32MG21_usart_spi_main_polled.slsproj", "text": "Platform - BRD4180A EFR32MG21 USART SPI Main Polled", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project demonstrates polled operation of the USART in synchronous main mode.  USART0 is configured for SPI-compatible operation at 1 Mbps.  Compatibility with the Synchronous Peripheral Interface standard implies a word size of 8 data bits transmitted and receiv..."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "This project demonstrates DMA-driven operation of the USART in synchronous secondary mode.  USART0 is configured for SPI-compatible operation at 1 Mbps.  Compatibility with the Synchronous Peripheral Interface standard implies a word size of 8 data bits transmitted a...", "id": "projectTemplate.compatibleSDK.series2/usart/spi_secondary_dma/SimplicityStudio/BRD4180A_EFR32MG21_usart_spi_secondary_dma.slsproj", "text": "Platform - BRD4180A EFR32MG21 USART SPI Secondary DMA", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project demonstrates DMA-driven operation of the USART in synchronous secondary mode.  USART0 is configured for SPI-compatible operation at 1 Mbps.  Compatibility with the Synchronous Peripheral Interface standard implies a word size of 8 data bits transmitted a..."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "This project demonstrates interrupt-driven operation of the USART in synchronous secondary mode.  USART0 is configured for SPI-compatible operation at 1 Mbps.  Compatibility with the Synchronous Peripheral Interface standard implies a word size of 8 data bits transmi...", "id": "projectTemplate.compatibleSDK.series2/usart/spi_secondary_interrupt/SimplicityStudio/BRD4180A_EFR32MG21_usart_spi_secondary_interrupt.slsproj", "text": "Platform - BRD4180A EFR32MG21 USART SPI Secondary Interrupt", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project demonstrates interrupt-driven operation of the USART in synchronous secondary mode.  USART0 is configured for SPI-compatible operation at 1 Mbps.  Compatibility with the Synchronous Peripheral Interface standard implies a word size of 8 data bits transmi..."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "This project demonstrates polled operation of the USART in synchronous secondary mode.  USART0 is configured for SPI-compatible operation at 1 Mbps. Compatibility with the Synchronous Peripheral Interface standard implies a word size of 8 data bits transmitted and re...", "id": "projectTemplate.compatibleSDK.series2/usart/spi_secondary_polled/SimplicityStudio/BRD4180A_EFR32MG21_usart_spi_secondary_polled.slsproj", "text": "Platform - BRD4180A EFR32MG21 USART SPI Secondary Polled", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project demonstrates polled operation of the USART in synchronous secondary mode.  USART0 is configured for SPI-compatible operation at 1 Mbps. Compatibility with the Synchronous Peripheral Interface standard implies a word size of 8 data bits transmitted and re..."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/blink_baremetal/readme.md"], "description": "This example project shows how to blink an LED in a bare-metal configuration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.blink_baremetal.example/blink_baremetal/blink_baremetal.slcp", "text": "Platform - Blink Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to blink an LED in a bare-metal configuration.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/blink_kernel_freertos/readme.md"], "description": "This example project shows how to blink an LED using a FreeRTOS kernel task. The blink task can be created using either dynamic or static memory allocation for the task stack and tcb.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.blink_kernel_freertos.example/blink_kernel_freertos/blink_kernel_freertos.slcp", "text": "Platform - Blink Kernel FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to blink an LED using a FreeRTOS kernel task. The blink task can be created using either dynamic or static memory allocation for the task stack and tcb.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/blink_kernel_micriumos/readme.md"], "description": "This example project shows how to blink an LED using a Micrium OS kernel task.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.blink_kernel_micriumos.example/blink_kernel_micriumos/blink_kernel_micriumos.slcp", "text": "Platform - Blink Kernel Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to blink an LED using a Micrium OS kernel task.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/blink_pwm_baremetal/readme.md"], "description": "This example project uses the PWM driver that uses a TIMER to gradually adjust the intensity of an LED up and down.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.blink_pwm_baremetal.example/blink_pwm_baremetal/blink_pwm_baremetal.slcp", "text": "Platform - Blink PWM", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the PWM driver that uses a TIMER to gradually adjust the intensity of an LED up and down.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/cli_baremetal/readme.md"], "description": "This example project demonstrates how to use the CLI driver in a bare-metal configuration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.cli_baremetal.example/cli_baremetal/cli_baremetal.slcp", "text": "Platform - CLI Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates how to use the CLI driver in a bare-metal configuration.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/cli_kernel_freertos/readme.md"], "description": "This example project demonstrates how to use the CLI driver using a FreeRTOS Kernel.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.cli_kernel_freertos.example/cli_kernel_freertos/cli_kernel_freertos.slcp", "text": "Platform - CLI Kernel FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates how to use the CLI driver using a FreeRTOS Kernel.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/cli_kernel_micriumos/readme.md"], "description": "This example project demonstrates how to use the CLI driver using a Micrium OS Kernel.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.cli_kernel_micriumos.example/cli_kernel_micriumos/cli_kernel_micriumos.slcp", "text": "Platform - CLI Kernel Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates how to use the CLI driver using a Micrium OS Kernel.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/cpc_secondary_spi_security_device_recovery/readme.md"], "description": "This simple example project implements the function sl_cpc_security_on_unbind_request(), allowing the secondary device to be unbound from the host.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.cpc_secondary_spi_security_device_recovery.example/cpc_secondary_spi_security_device_recovery/cpc_secondary_spi_security_device_recovery.slcp", "text": "Platform - CPC Secondary Device Recovery - SPI", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This simple example project implements the function sl_cpc_security_on_unbind_request(), allowing the secondary device to be unbound from the host.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/cpc_secondary_vcom_security_device_recovery/readme.md"], "description": "This simple example project implements the function sl_cpc_security_on_unbind_request(), allowing the secondary device to be unbound from the host.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.cpc_secondary_vcom_security_device_recovery.example/cpc_secondary_vcom_security_device_recovery/cpc_secondary_vcom_security_device_recovery.slcp", "text": "Platform - CPC Secondary Device Recovery - VCOM", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This simple example project implements the function sl_cpc_security_on_unbind_request(), allowing the secondary device to be unbound from the host.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/cpc_secondary_vcom_micriumos/readme.md"], "description": "This simple example project shows how to open user endpoints in a Micrium OS task with security disabled. By connecting a host running a CPCd instance to the secondary's VCOM port, the user can send data to the user endpoints, and verify that it is echoed back.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.cpc_secondary_vcom_micriumos.example/cpc_secondary_vcom_micriumos/cpc_secondary_vcom_micriumos.slcp", "text": "Platform - CPC Secondary with Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This simple example project shows how to open user endpoints in a Micrium OS task with security disabled. By connecting a host running a CPCd instance to the secondary's VCOM port, the user can send data to the user endpoints, and verify that it is echoed back.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/cpc_secondary_vcom_security_micriumos/readme.md"], "description": "This simple example project shows how to open user endpoints in a Micrium OS task with security enabled. By connecting a host running a CPCd instance to the secondary's VCOM port, the user can send data to the user endpoints, and verify that it is echoed back.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.cpc_secondary_vcom_security_micriumos.example/cpc_secondary_vcom_security_micriumos/cpc_secondary_vcom_security_micriumos.slcp", "text": "Platform - CPC Secondary with Micrium OS and Security Enabled", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This simple example project shows how to open user endpoints in a Micrium OS task with security enabled. By connecting a host running a CPCd instance to the secondary's VCOM port, the user can send data to the user endpoints, and verify that it is echoed back.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/dmadrv_baremetal/readme.md"], "description": "This example project shows how to use DMADRV driver to transfer data between memory and a USART peripheral in a bare-metal configuration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.dmadrv_baremetal.example/dmadrv_baremetal/dmadrv_baremetal.slcp", "text": "Platform - DMADRV Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to use DMADRV driver to transfer data between memory and a USART peripheral in a bare-metal configuration.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/emode_baremetal/readme.md"], "description": "Demo for energy mode current consumption testing.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.emode_baremetal.example/emode_baremetal/emode_baremetal.slcp", "text": "Platform - Emode Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demo for energy mode current consumption testing.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/iostream_usart_baremetal/readme.md"], "description": "This example project uses the I/O Stream service running in a bare-metal configuration  to demonstrate the use of UART communication over the virtual COM port (VCOM). The  application will echo back any characters it receives over the serial connection. The  VCOM serial port can be used either over USB or by connecting to port 4902 if the kit  is connected via Ethernet.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.iostream_usart_baremetal.example/iostream_usart_baremetal/iostream_usart_baremetal.slcp", "text": "Platform - I/O Stream USART Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the I/O Stream service running in a bare-metal configuration  to demonstrate the use of UART communication over the virtual COM port (VCOM). The  application will echo back any characters it receives over the serial connection. The  VCOM serial port can be used either over USB or by connecting to port 4902 if the kit  is connected via Ethernet. \n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/iostream_usart_kernel_micriumos/readme.md"], "description": "This example project uses the I/O Stream service running in a Micrium OS kernel task to demonstrate the use of UART communication over the virtual COM port (VCOM). The application will echo back any characters it receives over the serial connection. The VCOM serial port can be used either over USB or by connecting to port 4902 if the kit is connected via Ethernet.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.iostream_usart_kernel_micriumos.example/iostream_usart_kernel_micriumos/iostream_usart_kernel_micriumos.slcp", "text": "Platform - I/O Stream USART on Micrium OS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the I/O Stream service running in a Micrium OS kernel task to demonstrate the use of UART communication over the virtual COM port (VCOM). The application will echo back any characters it receives over the serial connection. The VCOM serial port can be used either over USB or by connecting to port 4902 if the kit is connected via Ethernet. \n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/joystick_baremetal/readme.md"], "description": "This example project uses the Joystick Driver running in a bare metal configuration to demonstrate the use of Joystick driver by printing joystick position after every finite interval over the virtual COM port (VCOM). The VCOM serial port can be used either over  USB or by connecting to port 4902 if the kit is connected via Internet. Important Note: This application only works when Wireless Pro Kit (BRD4002A) is used as  mainboard to mount and connect the compatible Radio Board.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.joystick_baremetal.example/joystick_baremetal/joystick_baremetal.slcp", "text": "Platform - Joystick Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the Joystick Driver running in a bare metal configuration to demonstrate the use of Joystick driver by printing joystick position after every finite interval over the virtual COM port (VCOM). The VCOM serial port can be used either over  USB or by connecting to port 4902 if the kit is connected via Internet.\nImportant Note: This application only works when Wireless Pro Kit (BRD4002A) is used as  mainboard to mount and connect the compatible Radio Board.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/joystick_kernel_freertos/readme.md"], "description": "This example project uses the Joystick driver running in a FreeRTOS kernel  task to demonstrate the use of Joystick driver by printing joystick position  after every finite interval over the virtual COM port (VCOM). The VCOM serial  port can be used either over USB or by connecting to port 4902 if the kit is  connected via Internet.  Important Note: This application only works when Wireless Pro Kit (BRD4002A) is used as  mainboard to mount and connect the compatible Radio Board.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.joystick_kernel_freertos.example/joystick_kernel_freertos/joystick_kernel_freertos.slcp", "text": "Platform - Joystick on FreeRTOS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the Joystick driver running in a FreeRTOS kernel  task to demonstrate the use of Joystick driver by printing joystick position  after every finite interval over the virtual COM port (VCOM). The VCOM serial  port can be used either over USB or by connecting to port 4902 if the kit is  connected via Internet. \nImportant Note: This application only works when Wireless Pro Kit (BRD4002A) is used as  mainboard to mount and connect the compatible Radio Board.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/joystick_kernel_micriumos/readme.md"], "description": "This example project uses the Joystick driver running in a Micrium OS kernel  task to demonstrate the use of Joystick driver by printing joystick position  after every finite interval over the virtual COM port (VCOM). The VCOM serial  port can be used either over USB or by connecting to port 4902 if the kit is  connected via Internet.  Important Note: This application only works when Wireless Pro Kit (BRD4002A) is used as  mainboard to mount and connect the compatible Radio Board.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.joystick_kernel_micriumos.example/joystick_kernel_micriumos/joystick_kernel_micriumos.slcp", "text": "Platform - Joystick on Micrium OS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the Joystick driver running in a Micrium OS kernel  task to demonstrate the use of Joystick driver by printing joystick position  after every finite interval over the virtual COM port (VCOM). The VCOM serial  port can be used either over USB or by connecting to port 4902 if the kit is  connected via Internet. \nImportant Note: This application only works when Wireless Pro Kit (BRD4002A) is used as  mainboard to mount and connect the compatible Radio Board.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/memlcd_baremetal/readme.md"], "description": "This example project demonstrates use of the Memory Liquid Crystal Display (LCD)  module in a baremetal application, using Silicon Labs Graphics Library (glib).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.memlcd_baremetal.example/memlcd_baremetal/memlcd_baremetal.slcp", "text": "Platform - MEMLCD Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates use of the Memory Liquid Crystal Display (LCD)  module in a baremetal application, using Silicon Labs Graphics Library (glib).\n"}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/memlcd_baremetal/readme.md"], "description": "This example project demonstrates use of the Memory Liquid Crystal Display (LCD) module in a baremetal application, using Silicon Labs Graphics Library (glib).", "id": "com.silabs.sdk.stack.super:4.4.4._-********_Platform - MEMLCD Bare-metal_asset:..com.silabs.sdk.stack.super_4.4.4.app.common.demos.memlcd_baremetal.memlcd_baremetal-brd4180a.s37", "text": "Platform - MEMLCD Bare-metal", "priority": 0, "category": "DEMOS", "toolTipText": "This example project demonstrates use of the Memory Liquid Crystal Display (LCD)  module in a baremetal application, using Silicon Labs Graphics Library (glib).\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/memlcd_kernel_micriumos/readme.md"], "description": "This example project demonstrates use of the Memory Liquid Crystal Display (LCD)  module in a Micrium OS kernel task, using Silicon Labs Graphics Library (glib).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.memlcd_kernel_micriumos.example/memlcd_kernel_micriumos/memlcd_kernel_micriumos.slcp", "text": "Platform - MEMLCD Kernel on Micrium OS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates use of the Memory Liquid Crystal Display (LCD)  module in a Micrium OS kernel task, using Silicon Labs Graphics Library (glib).\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/mpu_simple_baremetal/readme.md"], "description": "This example project demonstrates the use of the Simple MPU module. Its purpose is to block the execution of code from RAM in order to prevent code injection attacks. In this example,  some fake malicious executable code is copied to RAM and executed with the MPU disabled and  re-enabled to demonstrate its effectiveness.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.mpu_simple_baremetal.example/mpu_simple_baremetal/mpu_simple_baremetal.slcp", "text": "Platform - MPU Simple", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the use of the Simple MPU module. Its purpose is to block the execution of code from RAM in order to prevent code injection attacks. In this example,  some fake malicious executable code is copied to RAM and executed with the MPU disabled and  re-enabled to demonstrate its effectiveness. \n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/nvm3_baremetal/readme.md"], "description": "This example project demonstrates use of the NVM3 interface. Using the command line interface, the user can write, read and delete NVM3 data objects through the serial connection. The number of writes and deletes are tracked in counter objects.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.nvm3_baremetal.example/nvm3_baremetal/nvm3_baremetal.slcp", "text": "Platform - NVM3 Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates use of the NVM3 interface. Using the command line interface, the user can write, read and delete NVM3 data objects through the serial connection. The number of writes and deletes are tracked in counter objects.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/power_manager_baremetal/readme.md"], "description": "This example project demonstrates use of the Power Manager module in a bare metal application.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.power_manager_baremetal.example/power_manager_baremetal/power_manager_baremetal.slcp", "text": "Platform - Power Manager Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates use of the Power Manager module in a bare metal application.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/power_manager_kernel_micriumos/readme.md"], "description": "This example project demonstrates use of the Power Manager module in a Micrium OS kernel task.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.power_manager_kernel_micriumos.example/power_manager_kernel_micriumos/power_manager_kernel_micriumos.slcp", "text": "Platform - Power Manager Kern<PERSON> with Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates use of the Power Manager module in a Micrium OS kernel task.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/spidrv_master_baremetal/readme.md"], "description": "This example project demonstrates how to use the master mode of the Serial Peripheral Interface driver in a bare-metal configuration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.spidrv_master_baremetal.example/spidrv_master_baremetal/spidrv_master_baremetal.slcp", "text": "Platform - SPIDRV Master Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates how to use the master mode of the Serial Peripheral Interface driver in a bare-metal configuration.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/spidrv_master_kernel_micriumos/readme.md"], "description": "This example project demonstrates how to use the master mode of the Serial Peripheral Interface driver in a Micrium OS kernel task.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.spidrv_master_kernel_micriumos.example/spidrv_master_kernel_micriumos/spidrv_master_kernel_micriumos.slcp", "text": "Platform - SPIDRV Master on Micrium OS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates how to use the master mode of the Serial Peripheral Interface driver in a Micrium OS kernel task.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/spidrv_slave_baremetal/readme.md"], "description": "This example project demonstrates how to use the slave mode of the Serial Peripheral Interface driver in a bare-metal configuration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.spidrv_slave_baremetal.example/spidrv_slave_baremetal/spidrv_slave_baremetal.slcp", "text": "Platform - SPIDRV Slave Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates how to use the slave mode of the Serial Peripheral Interface driver in a bare-metal configuration.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/spidrv_slave_kernel_micriumos/readme.md"], "description": "This example project demonstrates how to use the slave mode of the Serial Peripheral Interface driver in a Micrium OS kernel task.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.spidrv_slave_kernel_micriumos.example/spidrv_slave_kernel_micriumos/spidrv_slave_kernel_micriumos.slcp", "text": "Platform - SPIDRV Slave on Micrium OS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates how to use the slave mode of the Serial Peripheral Interface driver in a Micrium OS kernel task.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/simple_button_baremetal/readme.md"], "description": "This example project shows how to toggle LEDs using buttons in a bare-metal environment.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.simple_button_baremetal.example/simple_button_baremetal/simple_button_baremetal.slcp", "text": "Platform - Simple Button Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to toggle LEDs using buttons in a bare-metal environment.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/simple_button_kernel_micriumos/readme.md"], "description": "This example project shows how to toggle LEDs using buttons in a Micrium OS kernel task.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.simple_button_kernel_micriumos.example/simple_button_kernel_micriumos/simple_button_kernel_micriumos.slcp", "text": "Platform - Simple <PERSON> on Micrium OS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to toggle LEDs using buttons in a Micrium OS kernel task.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/sleeptimer_baremetal/readme.md"], "description": "This example project demonstrates periodic and one-shot timers using the low-frequency real-time clock peripheral. Timers can be stopped and restarted using the buttons. Timer status is reported on the virtual COM serial port.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.sleeptimer_baremetal.example/sleeptimer_baremetal/sleeptimer_baremetal.slcp", "text": "Platform - Sleeptimer Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates periodic and one-shot timers using the low-frequency real-time clock peripheral. Timers can be stopped and restarted using the buttons. Timer status is reported on the virtual COM serial port.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/sleeptimer_wallclock_baremetal/readme.md"], "description": "This example project demonstrates the wallclock interface of the sleeptimer service. The user can get and set the date and time in different formats through the VCOM serial port.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.sleeptimer_wallclock_baremetal.example/sleeptimer_wallclock_baremetal/sleeptimer_wallclock_baremetal.slcp", "text": "Platform - <PERSON><PERSON><PERSON>", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the wallclock interface of the sleeptimer service. The user can get and set the date and time in different formats through the VCOM serial port.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/tempdrv_kernel_micriumos/readme.md"], "description": "This example project demonstrates how to use the internal temperature sensor in a Micrium OS kernel task.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.tempdrv_kernel_micriumos.example/tempdrv_kernel_micriumos/tempdrv_kernel_micriumos.slcp", "text": "Platform - TEMPDRV on Micrium OS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates how to use the internal temperature sensor in a Micrium OS kernel task.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_aead/readme.md"], "description": "This example uses the PSA Crypto API to perform Authenticated Encryption with Associated Data (AEAD) operations on the supported device.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.psa_crypto_aead.example/psa_crypto_aead/psa_crypto_aead.slcp", "text": "Platform Security - SoC PSA Crypto AEAD", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example uses the PSA Crypto API to perform Authenticated Encryption with Associated Data (AEAD) operations on the supported device.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_asymmetric_key/readme.md"], "description": "This example uses the PSA Crypto API to perform asymmetric key operations on the supported device.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.psa_crypto_asymmetric_key.example/psa_crypto_asymmetric_key/psa_crypto_asymmetric_key.slcp", "text": "Platform Security - SoC PSA Crypto Asymmetric Key", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example uses the PSA Crypto API to perform asymmetric key operations on the supported device.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_cipher/readme.md"], "description": "This example project demonstrates the unauthenticated cipher API for generic and built-in AES-128 keys.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.psa_crypto_cipher.example/psa_crypto_cipher/psa_crypto_cipher.slcp", "text": "Platform Security - SoC PSA Crypto Cipher", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the unauthenticated cipher API for generic and built-in AES-128 keys.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_dsa/readme.md"], "description": "This example project demonstrates the ECDSA and EdDSA digital signature API for generic and built-in ECC keys.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.psa_crypto_dsa.example/psa_crypto_dsa/psa_crypto_dsa.slcp", "text": "Platform Security - SoC PSA Crypto DSA", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the ECDSA and EdDSA digital signature API for generic and built-in ECC keys.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_ecdh/readme.md"], "description": "This example project demonstrates the ECDH key agreement API.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.psa_crypto_ecdh.example/psa_crypto_ecdh/psa_crypto_ecdh.slcp", "text": "Platform Security - SoC PSA Crypto ECDH", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the ECDH key agreement API.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_hash/readme.md"], "description": "This example project demonstrates the hash API.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.psa_crypto_hash.example/psa_crypto_hash/psa_crypto_hash.slcp", "text": "Platform Security - SoC PSA Crypto Hash", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the hash API.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_kdf/readme.md"], "description": "This example project demonstrates the Key Derivation Function (KDF) API.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.psa_crypto_kdf.example/psa_crypto_kdf/psa_crypto_kdf.slcp", "text": "Platform Security - SoC PSA Crypto KDF", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the Key Derivation Function (KDF) API.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_mac/readme.md"], "description": "This example project demonstrates the Message Authentication Code (MAC) API.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.psa_crypto_mac.example/psa_crypto_mac/psa_crypto_mac.slcp", "text": "Platform Security - SoC PSA Crypto MAC", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the Message Authentication Code (MAC) API.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_symmetric_key/readme.md"], "description": "This example project demonstrates the symmetric key API.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.psa_crypto_symmetric_key.example/psa_crypto_symmetric_key/psa_crypto_symmetric_key.slcp", "text": "Platform Security - SoC PSA Crypto Symmetric Key", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the symmetric key API.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_x509/readme.md"], "description": "This example project uses opaque ECDSA keys to implement the X.509 standard for certificates in Mbed TLS.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.psa_crypto_x509.example/psa_crypto_x509/psa_crypto_x509.slcp", "text": "Platform Security - SoC PSA Crypto X.509", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses opaque ECDSA keys to implement the X.509 standard for certificates in Mbed TLS.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_asymmetric_key_handling/readme.md"], "description": "This example project demonstrates the asymmetric key handling API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.se_manager_asymmetric_key_handling.example/se_manager_asymmetric_key_handling/se_manager_asymmetric_key_handling.slcp", "text": "Platform Security - SoC SE Manager Asymmetric Key Handling", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the asymmetric key handling API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_block_cipher/readme.md"], "description": "This example project demonstrates the block cipher API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.se_manager_block_cipher.example/se_manager_block_cipher/se_manager_block_cipher.slcp", "text": "Platform Security - SoC SE Manager Block Cipher", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the block cipher API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_signature/readme.md"], "description": "This example project demonstrates the digital signature (ECDSA and EdDSA) API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.se_manager_signature.example/se_manager_signature/se_manager_signature.slcp", "text": "Platform Security - SoC SE Manager Digital Signature (ECDSA and EdDSA)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the digital signature (ECDSA and EdDSA) API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_hash/readme.md"], "description": "This example project demonstrates the Hash API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.se_manager_hash.example/se_manager_hash/se_manager_hash.slcp", "text": "Platform Security - SoC SE Manager <PERSON><PERSON>", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the Hash API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_host_firmware_upgrade/readme.md"], "description": "This example project demonstrates the host firmware upgrade and debug lock API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.se_manager_host_firmware_upgrade.example/se_manager_host_firmware_upgrade/se_manager_host_firmware_upgrade.slcp", "text": "Platform Security - SoC SE Manager Host Firmware Upgrade and Debug Lock", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the host firmware upgrade and debug lock API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_ecdh/readme.md"], "description": "This example project demonstrates the key agreement (ECDH) API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.se_manager_ecdh.example/se_manager_ecdh/se_manager_ecdh.slcp", "text": "Platform Security - SoC SE Manager Key Agreement (ECDH)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the key agreement (ECDH) API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_ecjpake/readme.md"], "description": "This example project demonstrates the key agreement (ECJPAKE) API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.se_manager_ecjpake.example/se_manager_ecjpake/se_manager_ecjpake.slcp", "text": "Platform Security - SoC SE Manager Key Agreement (ECJPAKE)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the key agreement (ECJPAKE) API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_key_provisioning/readme.md"], "description": "This example project demonstrates the key provisioning API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.se_manager_key_provisioning.example/se_manager_key_provisioning/se_manager_key_provisioning.slcp", "text": "Platform Security - SoC SE Manager Key Provisioning", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the key provisioning API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_se_firmware_upgrade/readme.md"], "description": "This example project demonstrates the SE firmware upgrade API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.se_manager_se_firmware_upgrade.example/se_manager_se_firmware_upgrade/se_manager_se_firmware_upgrade.slcp", "text": "Platform Security - SoC SE Manager SE Firmware Upgrade", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the SE firmware upgrade API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_secure_debug/readme.md"], "description": "This example project demonstrates the secure debug API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.se_manager_secure_debug.example/se_manager_secure_debug/se_manager_secure_debug.slcp", "text": "Platform Security - SoC SE Manager Secure Debug", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the secure debug API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_stream_cipher/readme.md"], "description": "This example project demonstrates the stream cipher API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.se_manager_stream_cipher.example/se_manager_stream_cipher/se_manager_stream_cipher.slcp", "text": "Platform Security - SoC SE Manager Stream Cipher", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the stream cipher API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_symmetric_key_handling/readme.md"], "description": "This example project demonstrates the symmetric key handling API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.se_manager_symmetric_key_handling.example/se_manager_symmetric_key_handling/se_manager_symmetric_key_handling.slcp", "text": "Platform Security - SoC SE Manager Symmetric Key Handling", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the symmetric key handling API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_user_data/readme.md"], "description": "This example project demonstrates the user data API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.se_manager_user_data.example/se_manager_user_data/se_manager_user_data.slcp", "text": "Platform Security - SoC SE Manager User Data", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the user data API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/mbedtls_aescrypt/readme.md"], "description": "This example uses hardware accelerators to accelerate the AES encryption and SHA hash functions of mbedTLS.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.mbedtls_aescrypt.example/mbedtls_aescrypt/mbedtls_aescrypt.slcp", "text": "Platform Security - SoC mbedTLS AES", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example uses hardware accelerators to accelerate the AES encryption and SHA hash functions of mbedTLS.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/mbedtls_ecdh/readme.md"], "description": "This example uses hardware accelerators of the supported devices to perform ECDH key generation with mbedTLS.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.mbedtls_ecdh.example/mbedtls_ecdh/mbedtls_ecdh.slcp", "text": "Platform Security - SoC mbedTLS ECDH", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example uses hardware accelerators of the supported devices to perform ECDH key generation with mbedTLS.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/mbedtls_ecdsa/readme.md"], "description": "This example uses hardware accelerators on the supported device to perform ECDSA digital signature with mbedTLS.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.mbedtls_ecdsa.example/mbedtls_ecdsa/mbedtls_ecdsa.slcp", "text": "Platform Security - SoC mbedTLS ECDSA", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example uses hardware accelerators on the supported device to perform ECDSA digital signature with mbedTLS.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/flex/documentation/example/rail/rail_soc_railtest/readme.md"], "description": "The RAILtest application provides a simple tool for testing the radio and the functionality of the RAIL library via CLI. For more advanced usage, developers must write software against the RAIL library and create a custom radio configuration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.rail_soc_railtest.example/rail/rail_soc_railtest/rail_soc_railtest.slcp", "text": "RAIL - SoC RAILtest", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The RAILtest application provides a simple tool for testing the radio and the functionality of the RAIL library via CLI. For more advanced usage, developers must write software against the RAIL library and create a custom radio configuration."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/flex/documentation/example/rail/rail_soc_range_test/readme.md"], "description": "This is the Range Test Sample Application that demonstrates over the air range of the Silicon Labs boards. 5 predefined PHYs can be used for this: BLE: 125kbps, BLE: 500kbps, BLE: 1Mbps, BLE: 2Mbps, IEEE80215.4: 250kbps. This sample app can act as a Transmitter and a Receiver. The role can be selected in the LCD menu. Flashing this app into two separate boards makes it possible to test the features and specification of the radio. The sample also provides an example how the RAIL API can be used. A menu is displayed in the LCD, which allows the user to see the most important information about the settings and also change some of them. The left button navigates in the menu and the right button selects or changes options. The bottom line always shows what the buttons do in the particular context. In Tx Mode, the user can send packets. Packet length defined by the PHY and the number of packets to transmit (from 500 up to continuous) can be set. Output power can be set in the LCD menu, in 0.5dBm steps (power setpoint), between -15..+20dBm. Actual minimum and maximum power may vary in different frequencies as well as the power that is actually set by RAIL. The LCD menu informs the user about the setpoint and the actual power. In the LCD menu, the Power item displays the setpoint first, then actual value. In Rx Mode, the radio listens on the given predefined PHY and inspects the packets received. Packet Error Rate, Bit Error Rate and RSSI of the packets is displayed to inform about the quality of the transmission. Radio related events can be logged on UART on demand. CLI can be used to set and get configuration of the app, and to start and stop it. To get started with CLI send 'help' with a terminal. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, a USB power bank can be used if portability is needed.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.rail_soc_range_test_std.example/rail/rail_soc_range_test_std/rail_soc_range_test_std.slcp", "text": "RAIL - SoC Range Test BLE and IEEE802.15.4", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is the Range Test Sample Application that demonstrates over the air range of the Silicon Labs boards. 5 predefined PHYs can be used for this: BLE: 125kbps, BLE: 500kbps, BLE: 1Mbps, BLE: 2Mbps, IEEE80215.4: 250kbps. This sample app can act as a Transmitter and a Receiver. The role can be selected in the LCD menu. Flashing this app into two separate boards makes it possible to test the features and specification of the radio. The sample also provides an example how the RAIL API can be used. A menu is displayed in the LCD, which allows the user to see the most important information about the settings and also change some of them. The left button navigates in the menu and the right button selects or changes options. The bottom line always shows what the buttons do in the particular context. In Tx Mode, the user can send packets. Packet length defined by the PHY and the number of packets to transmit (from 500 up to continuous) can be set. Output power can be set in the LCD menu, in 0.5dBm steps (power setpoint), between -15..+20dBm. Actual minimum and maximum power may vary in different frequencies as well as the power that is actually set by RAIL. The LCD menu informs the user about the setpoint and the actual power. In the LCD menu, the Power item displays the setpoint first, then actual value. In Rx Mode, the radio listens on the given predefined PHY and inspects the packets received. Packet Error Rate, Bit Error Rate and RSSI of the packets is displayed to inform about the quality of the transmission. Radio related events can be logged on UART on demand. CLI can be used to set and get configuration of the app, and to start and stop it. To get started with CLI send 'help' with a terminal. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, a USB power bank can be used if portability is needed."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/flex/documentation/example/rail/rail_soc_simple_trx_std/readme.md"], "description": "This application demonstrates the simplest exchange of transmit and receive operation between two nodes using IEEE Std. 802.15.4 and IEEE Std. 802.15.4g and BLE. Both nodes can send and receive IEEE Std. 802.15.4 and IEEE Std. 802.15.4g or BLE advertising frames. With IEEE Std. 802.15.4 the frame can be sent and received with CSMA/CA, auto-ACK, address filtering and setable Frame Control Frame (available settings: broadcast or unicast). With IEEE Std. 802.15.4g the frame can be sent and received with auto-ACK, address filtering and setable Frame Control Frame (available settings broadcast or unicast). On the WSTK, any button press (PB0/PB1) will send a message. LED1 will toggle on message send and LED0 will toggle on message receive. CLI can also be used for sending and showing received messages. To send enter 'send'. By default the received packets will be printed out in terminal. To change standard to BLE, the Flex - RAIL BLE support software component shall be installed under Flex/RAIL/Utilility. With one click this baremetal sample app can be run on an OS, currently MicriumOS and FreeRTOS is supported. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, a USB power bank can be used if portability is needed.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.rail_soc_simple_trx_std.example/rail/rail_soc_simple_trx_std/rail_soc_simple_trx_std.slcp", "text": "RAIL - SoC Simple TRX Standards", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This application demonstrates the simplest exchange of transmit and receive operation between two nodes using IEEE Std. 802.15.4 and IEEE Std. 802.15.4g and BLE. Both nodes can send and receive IEEE Std. 802.15.4 and IEEE Std. 802.15.4g or BLE advertising frames. With IEEE Std. 802.15.4 the frame can be sent and received with CSMA/CA, auto-ACK, address filtering and setable Frame Control Frame (available settings: broadcast or unicast). With IEEE Std. 802.15.4g the frame can be sent and received with auto-ACK, address filtering and setable Frame Control Frame (available settings broadcast or unicast). On the WSTK, any button press (PB0/PB1) will send a message. LED1 will toggle on message send and LED0 will toggle on message receive. CLI can also be used for sending and showing received messages. To send enter 'send'. By default the received packets will be printed out in terminal. To change standard to BLE, the Flex - RAIL BLE support software component shall be installed under Flex/RAIL/Utilility. With one click this baremetal sample app can be run on an OS, currently MicriumOS and FreeRTOS is supported. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, a USB power bank can be used if portability is needed."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/flex/documentation/example/rail/rail_soc_range_test/readme.md"], "description": "Range Test BLE and IEEE802.15.4 with Bluetooth connectivity. It runs on top of Micrium OS RTOS and multiprotocol RAIL. This application demonstrates over the air range of the Silicon Labs boards. 5 predefined PHYs can be used for this: BLE: 125kbps, BLE: 500kbps, BLE: 1Mbps, BLE: 2Mbps, IEEE80215.4: 250kbps. This sample app can act as a Transmitter and a Receiver. The role can be selected in the LCD menu. Flashing this app into two separate boards makes it possible to test the features and specification of the radio. The sample also provides an example how the RAIL API can be used. A menu is displayed in the LCD, which allows the user to see the most important information about the settings and also change some of them. The left button navigates in the menu and the right button selects or changes options. The bottom line always shows what the buttons do in the particular context. In Tx Mode, the user can send packets. Packet length defined by the PHY and the number of packets to transmit (from 500 up to continuous) can be set. Output power can be set in the LCD menu, in 0.5dBm steps (power setpoint), between -15..+20dBm. Actual minimum and maximum power may vary in different frequencies as well as the power that is actually set by RAIL. The LCD menu informs the user about the setpoint and the actual power. In the LCD menu, the Power item displays the setpoint first, then actual value. In Rx Mode, the radio listens on the given predefined PHY and inspects the packets received. Packet Error Rate, Bit Error Rate and RSSI of the packets is displayed to inform about the quality of the transmission. Radio related events can be logged on UART on demand. CLI can be used to set and get configuration of the app, and to start and stop it. To get started with CLI send 'help' with a terminal. Wireless Gecko mobile app can also be used to control this application over Bluetooth. Currently MicriumOS and FreeRTOS is supported by this sample app. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, a USB power bank can be used if portability is needed.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.rail_bt_dmp_soc_range_test_std.example/rail/rail_bt_dmp_soc_range_test_std/rail_bt_dmp_soc_range_test_std.slcp", "text": "RAIL Bluetooth DMP - SoC Range Test BLE and IEEE802.15.4", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Range Test BLE and IEEE802.15.4 with Bluetooth connectivity. It runs on top of Micrium OS RTOS and multiprotocol RAIL. This application demonstrates over the air range of the Silicon Labs boards. 5 predefined PHYs can be used for this: BLE: 125kbps, BLE: 500kbps, BLE: 1Mbps, BLE: 2Mbps, IEEE80215.4: 250kbps. This sample app can act as a Transmitter and a Receiver. The role can be selected in the LCD menu. Flashing this app into two separate boards makes it possible to test the features and specification of the radio. The sample also provides an example how the RAIL API can be used. A menu is displayed in the LCD, which allows the user to see the most important information about the settings and also change some of them. The left button navigates in the menu and the right button selects or changes options. The bottom line always shows what the buttons do in the particular context. In Tx Mode, the user can send packets. Packet length defined by the PHY and the number of packets to transmit (from 500 up to continuous) can be set. Output power can be set in the LCD menu, in 0.5dBm steps (power setpoint), between -15..+20dBm. Actual minimum and maximum power may vary in different frequencies as well as the power that is actually set by RAIL. The LCD menu informs the user about the setpoint and the actual power. In the LCD menu, the Power item displays the setpoint first, then actual value. In Rx Mode, the radio listens on the given predefined PHY and inspects the packets received. Packet Error Rate, Bit Error Rate and RSSI of the packets is displayed to inform about the quality of the transmission. Radio related events can be logged on UART on demand. CLI can be used to set and get configuration of the app, and to start and stop it. To get started with CLI send 'help' with a terminal. Wireless Gecko mobile app can also be used to control this application over Bluetooth. Currently MicriumOS and FreeRTOS is supported by this sample app. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, a USB power bank can be used if portability is needed."}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/flex/documentation/example/rail/rail_soc_range_test/readme.md"], "description": "Range Test BLE and IEEE802.15.4 with Bluetooth connectivity. It runs on top of Micrium OS RTOS and multiprotocol RAIL. This application demonstrates over the air range of the Silicon Labs boards. 5 predefined PHYs can be used for this: BLE: 125kbps, BLE: 500kbps, BLE: 1Mbps, BLE: 2Mbps, IEEE80215.4: 250kbps. This sample app can act as a Transmitter and a Receiver. The role can be selected in the LCD menu. Flashing this app into two separate boards makes it possible to test the features and specification of the radio. The sample also provides an example how the RAIL API can be used. A menu is displayed in the LCD, which allows the user to see the most important information about the settings and also change some of them. The left button navigates in the menu and the right button selects or changes options. The bottom line always shows what the buttons do in the particular context. In Tx Mode, the user can send packets. Packet length defined by the PHY and the number of packets to transmit (from 500 up to continuous) can be set. Output power can be set in the LCD menu, in 0.5dBm steps (power setpoint), between -15..+20dBm. Actual minimum and maximum power may vary in different frequencies as well as the power that is actually set by RAIL. The LCD menu informs the user about the setpoint and the actual power. In the LCD menu, the Power item displays the setpoint first, then actual value. In Rx Mode, the radio listens on the given predefined PHY and inspects the packets received. Packet Error Rate, Bit Error Rate and RSSI of the packets is displayed to inform about the quality of the transmission. Radio related events can be logged on UART on demand. CLI can be used to set and get configuration of the app, and to start and stop it. To get started with CLI send 'help' with a terminal. Wireless Gecko mobile app can also be used to control this application over Bluetooth. Currently MicriumOS and FreeRTOS is supported by this sample app. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, a USB power bank can be used if portability is needed.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_RAIL Bluetooth DMP - SoC Range Test BLE and IEEE802.15.4_asset:..com.silabs.sdk.stack.super_4.4.4.app.flex.demos.rail_bt_dmp_soc_range_test_std.rail_bt_dmp_soc_range_test_std-brd4180a.s37", "text": "RAIL Bluetooth DMP - SoC Range Test BLE and IEEE802.15.4", "priority": 0, "category": "DEMOS", "toolTipText": "Range Test BLE and IEEE802.15.4 with Bluetooth connectivity. It runs on top of Micrium OS RTOS and multiprotocol RAIL. This application demonstrates over the air range of the Silicon Labs boards. 5 predefined PHYs can be used for this: BLE: 125kbps, BLE: 500kbps, BLE: 1Mbps, BLE: 2Mbps, IEEE80215.4: 250kbps. This sample app can act as a Transmitter and a Receiver. The role can be selected in the LCD menu. Flashing this app into two separate boards makes it possible to test the features and specification of the radio. The sample also provides an example how the RAIL API can be used. A menu is displayed in the LCD, which allows the user to see the most important information about the settings and also change some of them. The left button navigates in the menu and the right button selects or changes options. The bottom line always shows what the buttons do in the particular context. In Tx Mode, the user can send packets. Packet length defined by the PHY and the number of packets to transmit (from 500 up to continuous) can be set. Output power can be set in the LCD menu, in 0.5dBm steps (power setpoint), between -15..+20dBm. Actual minimum and maximum power may vary in different frequencies as well as the power that is actually set by RAIL. The LCD menu informs the user about the setpoint and the actual power. In the LCD menu, the Power item displays the setpoint first, then actual value. In Rx Mode, the radio listens on the given predefined PHY and inspects the packets received. Packet Error Rate, Bit Error Rate and RSSI of the packets is displayed to inform about the quality of the transmission. Radio related events can be logged on UART on demand. CLI can be used to set and get configuration of the app, and to start and stop it. To get started with CLI send 'help' with a terminal. Wireless Gecko mobile app can also be used to control this application over Bluetooth. Currently MicriumOS and FreeRTOS is supported by this sample app. NOTE: Due to the higher current consumption of the continuous radio usage (especially in Rx Mode), it is not recommended to power the boards from a coin cell. Instead, a USB power bank can be used if portability is needed."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/bthome_v2/README.md"], "description": "This example project showcases how to integrate the BTHome v2 library and send BLE advertisement packets using the BTHome v2 APIs.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bthome_v2.app/example/bthome_v2/bthome_v2.slcp", "text": "Third Party Hardware Drivers - BTHome v2", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project showcases how to integrate the BTHome v2 library and send BLE advertisement packets using the BTHome v2 APIs."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/bthome_v2_server/README.md"], "description": "This example project showcases how to integrate the BTHome v2 - Server library. The BTHome v2 library provides APIs to scan and read BTHome v2 devices.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.bthome_v2_server.app/example/bthome_v2_server/bthome_v2_server.slcp", "text": "Third Party Hardware Drivers - BTHome v2 - Server", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project showcases how to integrate the BTHome v2 - Server library. The BTHome v2 library provides APIs to scan and read BTHome v2 devices."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/silabs_ir_generate/README.md"], "description": "This example project shows an example for implementation of IR generator driver.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.silabs_ir_generate.app/example/silabs_ir_generate/silabs_ir_generate.slcp", "text": "Third Party Hardware Drivers - IR Generator (Silabs)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for implementation of IR generator driver."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "Configures the device in a transmit mode that is used for transmit performance and regulatory certification testing (FCC, ETSI, CE, etc.)", "id": "projectTemplate.compatibleSDK.examples/featured/wlan_rf_test/projects/wlan_rf_test-brd4180a-mg21.slsproj", "text": "Transmit Performance and Regulatory Testing", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Configures the device in a transmit mode that is used for transmit performance and regulatory certification testing (FCC, ETSI, CE, etc.)"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "Creates an AWS IoT 'thing' that connects to the AWS IoT device shadow service using the MQTT protocol", "id": "projectTemplate.compatibleSDK.examples/featured/aws_device_shadow/projects/aws_device_shadow-brd4180a-mg21.slsproj", "text": "Wi-Fi - AWS IoT Device Shadow", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Creates an AWS IoT 'thing' that connects to the AWS IoT device shadow service using the MQTT protocol"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "Wirelessly updates the RS911x firmware from a remote TCP server", "id": "projectTemplate.compatibleSDK.examples/featured/firmware_update/projects/firmware_update-brd4180a-mg21.slsproj", "text": "Wi-Fi - Firmware Update via TCP Server", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Wirelessly updates the RS911x firmware from a remote TCP server"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "Connects to a Wi-Fi Access Point in deep-sleep mode and intermittently wakes to send UDP packets", "id": "projectTemplate.compatibleSDK.examples/featured/powersave_standby_associated/projects/powersave_standby_associated-brd4180a-mg21.slsproj", "text": "Wi-Fi - Powersave Standby Associated", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Connects to a Wi-Fi Access Point in deep-sleep mode and intermittently wakes to send UDP packets"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "Measures Wi-Fi transmit/receive throughput performance using a remote iPerf client/server", "id": "projectTemplate.compatibleSDK.examples/featured/wlan_throughput/projects/wlan_throughput-brd4180a-mg21.slsproj", "text": "Wi-Fi - Throughput Test", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Measures Wi-Fi transmit/receive throughput performance using a remote iPerf client/server"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/ncp/sample-app/ncp-dmp/readme-zigbee_ncp-ble_ncp-spi.html"], "description": "This dynamic multiprotocol (DMP) application runs the Zigbee NCP simultaneously with the Bluetooth NCP. Communication with Zigbee and Bluetooth host applications is enabled using the Co-Processor Communication Protocol, which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a SPI link. The Zigbee NCP part of this application can be built as configured, or can optionally be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.zigbee_ncp-ble_ncp-spi.app/ncp/sample-app/ncp-dmp/zigbee_ncp-ble_ncp-spi.slcp", "text": "Zigbee - NCP + Bluetooth - NCP zigbee_ncp-ble_ncp-spi", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This dynamic multiprotocol (DMP) application runs the Zigbee NCP simultaneously with the Bluetooth NCP. Communication with Zigbee and Bluetooth host applications is enabled using the Co-Processor Communication Protocol, which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a SPI link. The Zigbee NCP part of this application can be built as configured, or can optionally be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/ncp/sample-app/ncp-dmp/readme-zigbee_ncp-ble_ncp-uart.html"], "description": "This dynamic multiprotocol (DMP) application runs the Zigbee NCP simultaneously with the Bluetooth NCP. Communication with Zigbee and Bluetooth host applications is enabled using the Co-Processor Communication Protocol, which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a UART link. The Zigbee NCP part of this application can be built as configured, or can optionally be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.zigbee_ncp-ble_ncp-uart.app/ncp/sample-app/ncp-dmp/zigbee_ncp-ble_ncp-uart.slcp", "text": "Zigbee - NCP + Bluetooth - NCP zigbee_ncp-ble_ncp-uart", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This dynamic multiprotocol (DMP) application runs the Zigbee NCP simultaneously with the Bluetooth NCP. Communication with Zigbee and Bluetooth host applications is enabled using the Co-Processor Communication Protocol, which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a UART link. The Zigbee NCP part of this application can be built as configured, or can optionally be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/ncp/sample-app/ncp-cmp/readme-zigbee_ncp-ot_rcp-spi.html"], "description": "This concurrent multiprotocol (CMP) application runs the Zigbee NCP simultaneously with the OpenThread RCP. Communication with Zigbee and OpenThread host applications is enabled using the Co-Processor Communication Protocol, which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a SPI link. The Zigbee NCP part of this application can be built as configured, or can optionally be augmented with ustomized extensions for initialization, main loop processing, event definition/handling, and messaging with the host.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.zigbee_ncp-ot_rcp-spi.app/ncp/sample-app/ncp-cmp/zigbee_ncp-ot_rcp-spi.slcp", "text": "Zigbee - NCP + OpenThread - RCP zigbee_ncp-ot_rcp-spi", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This concurrent multiprotocol (CMP) application runs the Zigbee NCP simultaneously with the OpenThread RCP. Communication with Zigbee and OpenThread host applications is enabled using the Co-Processor Communication Protocol, which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a SPI link. The Zigbee NCP part of this application can be built as configured, or can optionally be augmented with ustomized extensions for initialization, main loop processing, event definition/handling, and messaging with the host."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/ncp/sample-app/ncp-cmp/readme-zigbee_ncp-ot_rcp-uart.html"], "description": "This concurrent multiprotocol (CMP) application runs the Zigbee NCP simultaneously with the OpenThread RCP. Communication with Zigbee and OpenThread host applications is enabled using the Co-Processor Communication Protocol, which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a UART link. The Zigbee NCP part of this application can be built as configured, or can optionally be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.zigbee_ncp-ot_rcp-uart.app/ncp/sample-app/ncp-cmp/zigbee_ncp-ot_rcp-uart.slcp", "text": "Zigbee - NCP + OpenThread - RCP zigbee_ncp-ot_rcp-uart", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This concurrent multiprotocol (CMP) application runs the Zigbee NCP simultaneously with the OpenThread RCP. Communication with Zigbee and OpenThread host applications is enabled using the Co-Processor Communication Protocol, which acts as a protocol multiplexer and serial transport layer. The host applications connect to the CPC daemon, which in turn connects to the EFR via a UART link. The Zigbee NCP part of this application can be built as configured, or can optionally be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/ncp/sample-app/mp-ncp-spi/readme.html"], "description": "The multi-PAN application provides support to form two personal area networks  on same channel on single radio. This multi-PAN network coprocessor (NCP)  application supports communication with a host application over a SPI interface.  This NCP application can be built as configured, or optionally can be augmented  with customized extensions for initialization, main loop processing, event  definition/handling, and messaging with the host.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.mp-ncp-spi.app/ncp/sample-app/mp-ncp-spi/mp-ncp-spi.slcp", "text": "Zigbee - NCP mp-ncp-spi", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The multi-PAN application provides support to form two personal area networks  on same channel on single radio. This multi-PAN network coprocessor (NCP)  application supports communication with a host application over a SPI interface.  This NCP application can be built as configured, or optionally can be augmented  with customized extensions for initialization, main loop processing, event  definition/handling, and messaging with the host."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/ncp/sample-app/mp-ncp-uart-hw/readme.html"], "description": "The multi-PAN application provides support to form two personal area networks  on same channel on single radio. This multi-PAN network coprocessor (NCP)  application supports communication with a host application over a UART  interface with hardware flow control. This NCP application can be built as  configured, or optionally can be augmented with customized extensions for  initialization, main loop processing, event definition/handling, and messaging  with the host.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.mp-ncp-uart-hw.app/ncp/sample-app/mp-ncp-uart-hw/mp-ncp-uart-hw.slcp", "text": "Zigbee - NCP mp-ncp-uart-hw", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The multi-PAN application provides support to form two personal area networks  on same channel on single radio. This multi-PAN network coprocessor (NCP)  application supports communication with a host application over a UART  interface with hardware flow control. This NCP application can be built as  configured, or optionally can be augmented with customized extensions for  initialization, main loop processing, event definition/handling, and messaging  with the host."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/ncp/sample-app/ncp-spi/readme.html"], "description": "This network coprocessor (NCP) application supports communication with a host application over a SPI interface.  This NCP application can be built as configured, or optionally can be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ncp-spi.app/ncp/sample-app/ncp-spi/ncp-spi.slcp", "text": "Zigbee - NCP ncp-spi", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This network coprocessor (NCP) application supports communication with a host application over a SPI interface.  This NCP application can be built as configured, or optionally can be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host."}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/ncp/sample-app/ncp-spi/readme.html"], "description": "This network coprocessor (NCP) application supports communication with a host application over a SPI interface. This NCP application can be built as configured, or optionally can be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_Zigbee - NCP ncp-spi_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.zigbee.demos.ncp-spi.ncp-spi-brd4180a.s37", "text": "Zigbee - NCP ncp-spi", "priority": 0, "category": "DEMOS", "toolTipText": "This network coprocessor (NCP) application supports communication with a host application over a SPI interface.  This NCP application can be built as configured, or optionally can be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/ncp/sample-app/ncp-uart-hw/readme.html"], "description": "This network coprocessor (NCP) application supports communication with a host application over a UART interface with hardware flow control. This NCP application can be built as configured, or optionally can be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host. To create a ECC-enabled application for the Smart Energy Profile, please download and extract the 'EmberZnet-Smart-Energy.zip' side-package over your installed SDK and add, depending on use-case, one of two new components ('CBKE 163k1'; 'CBKE 283k1') to your application.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ncp-uart-hw.app/ncp/sample-app/ncp-uart-hw/ncp-uart-hw.slcp", "text": "Zigbee - NCP ncp-uart-hw", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This network coprocessor (NCP) application supports communication with a host application over a UART interface with hardware flow control. This NCP application can be built as configured, or optionally can be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host. To create a ECC-enabled application for the Smart Energy Profile, please download and extract the 'EmberZnet-Smart-Energy.zip' side-package over your installed SDK and add, depending on use-case, one of two new components ('CBKE 163k1'; 'CBKE 283k1') to your application."}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/ncp/sample-app/ncp-uart-hw/readme.html"], "description": "This network coprocessor (NCP) application supports communication with a host application over a UART interface with hardware flow control. This NCP application can be built as configured, or optionally can be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host. To create a ECC-enabled application for the Smart Energy Profile, please download and extract the 'EmberZnet-Smart-Energy.zip' side-package over your installed SDK and add, depending on use-case, one of two new components ('CBKE 163k1'; 'CBKE 283k1') to your application.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_Zigbee - NCP ncp-uart-hw_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.zigbee.demos.ncp-uart-hw.ncp-uart-hw-brd4180a.s37", "text": "Zigbee - NCP ncp-uart-hw", "priority": 0, "category": "DEMOS", "toolTipText": "This network coprocessor (NCP) application supports communication with a host application over a UART interface with hardware flow control. This NCP application can be built as configured, or optionally can be augmented with customized extensions for initialization, main loop processing, event definition/handling, and messaging with the host. To create a ECC-enabled application for the Smart Energy Profile, please download and extract the 'EmberZnet-Smart-Energy.zip' side-package over your installed SDK and add, depending on use-case, one of two new components ('CBKE 163k1'; 'CBKE 283k1') to your application."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/ncp/sample-app/ncp-uart-hw-gp-multi-rail/readme.html"], "description": "This network co-processor (NCP) application uses the multiple RAIL demo component (multirail-demo), which uses the second RAIL handle to schedule a pre-configured outgoing Green Power device frame (GPDF) in response to an incoming bidirectional GPDF with its rx-after-tx bit set.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ncp-uart-hw-gp-multi-rail.app/ncp/sample-app/ncp-uart-hw-gp-multi-rail/ncp-uart-hw-gp-multi-rail.slcp", "text": "Zigbee - NCP ncp-uart-hw-gp-multi-rail", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This network co-processor (NCP) application uses the multiple RAIL demo component (multirail-demo), which uses the second RAIL handle to schedule a pre-configured outgoing Green Power device frame (GPDF) in response to an incoming bidirectional GPDF with its rx-after-tx bit set."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/ncp/sample-app/xncp-led/readme.html"], "description": "An extensible network co-processor (xNCP) application with an LED controlled by a HOST.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.xncp-led-ncp.app/ncp/sample-app/xncp-led/xncp-led-ncp.slcp", "text": "Zigbee - NCP xncp-led-ncp", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An extensible network co-processor (xNCP) application with an LED controlled by a HOST."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/framework/scenarios/multiprotocol/DynamicMultiprotocolLight/readme.html"], "description": "This is a sample application demonstrating a light application using dynamic multiprotocol (Zigbee + Bluetooth LE) and NVM3 for persistent storage. We recommend the use of Micrium Kernel for this sample application.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.DynamicMultiprotocolLight.app/framework/scenarios/multiprotocol/DynamicMultiprotocolLight/DynamicMultiprotocolLight.slcp", "text": "Zigbee - SoC DynamicMultiprotocolLight", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a sample application demonstrating a light application using dynamic multiprotocol (Zigbee + Bluetooth LE) and NVM3 for persistent storage. We recommend the use of Micrium Kernel for this sample application."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/framework/scenarios/multiprotocol/DynamicMultiprotocolLight/readme.html"], "description": "This is a sample application demonstrating a light application using dynamic multiprotocol (Zigbee + Bluetooth LE) and NVM3 for persistent storage. This application does not use LCD, LED or buttons. We recommend the use of Micrium Kernel for this sample application.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.DynamicMultiprotocolLightMinimal.app/framework/scenarios/multiprotocol/DynamicMultiprotocolLight/DynamicMultiprotocolLightMinimal.slcp", "text": "Zigbee - SoC DynamicMultiprotocolLightMinimal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a sample application demonstrating a light application using dynamic multiprotocol (Zigbee + Bluetooth LE) and NVM3 for persistent storage. This application does not use LCD, LED or buttons. We recommend the use of Micrium Kernel for this sample application."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/framework/scenarios/multiprotocol/DynamicMultiprotocolLightSed/readme.html"], "description": "This is a sample application demonstrating a sleepy light application using dynamic multiprotocol (Zigbee + Bluetooth LE) and NVM3 for persistent storage. We recommend the use of Micrium Kernel for this sample application.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.DynamicMultiprotocolLightSed.app/framework/scenarios/multiprotocol/DynamicMultiprotocolLightSed/DynamicMultiprotocolLightSed.slcp", "text": "Zigbee - SoC DynamicMultiprotocolLightSed", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a sample application demonstrating a sleepy light application using dynamic multiprotocol (Zigbee + Bluetooth LE) and NVM3 for persistent storage. We recommend the use of Micrium Kernel for this sample application."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/framework/scenarios/multiprotocol/DynamicMultiprotocolLightSed/readme.html"], "description": "This is a sample application demonstrating a sleepy light application using dynamic multiprotocol (Zigbee + Bluetooth LE) and NVM3 for persistent storage. This application does not use LCD, LEDs or buttons. We recommend the use of Micrium Kernel for this sample application.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.DynamicMultiprotocolLightSedMinimal.app/framework/scenarios/multiprotocol/DynamicMultiprotocolLightSed/DynamicMultiprotocolLightSedMinimal.slcp", "text": "Zigbee - SoC DynamicMultiprotocolLightSedMinimal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a sample application demonstrating a sleepy light application using dynamic multiprotocol (Zigbee + Bluetooth LE) and NVM3 for persistent storage. This application does not use LCD, LEDs or buttons. We recommend the use of Micrium Kernel for this sample application."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/gpd/sample-app/gpd-sensor/readme.html"], "description": "This is an Energy Service Interface application for a Zigbee Smart Energy 1.2b network. This is a Green Power Sensor Device that pairs with a GP Combo or Sink device and sends gpd reports periodically.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.gpd-sensor.app/gpd/sample-app/gpd-sensor/gpd-sensor.slcp", "text": "Zigbee - SoC GPD Sensor", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is an Energy Service Interface application for a Zigbee Smart Energy 1.2b network. This is a Green Power Sensor Device that pairs with a GP Combo or Sink device and sends gpd reports periodically."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/gpd/sample-app/gpd-switch/readme.html"], "description": "This is a Green Power On/Off Switch Device that pairs with a GP Combo or Sink Light and controls its operation.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.gpd-switch.app/gpd/sample-app/gpd-switch/gpd-switch.slcp", "text": "Zigbee - SoC GPD Switch", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a Green Power On/Off Switch Device that pairs with a GP Combo or Sink Light and controls its operation."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/framework/scenarios/z3/Z3Light/readme.html"], "description": "This is a Zigbee 3.0 light application using NVM3 as the persistent storage.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.Z3Light.app/framework/scenarios/z3/Z3Light/Z3Light.slcp", "text": "Zigbee - SoC Light", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a Zigbee 3.0 light application using NVM3 as the persistent storage."}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/framework/scenarios/z3/Z3Light/readme.html"], "description": "This is a Zigbee 3.0 light application using NVM3 as the persistent storage.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_Zigbee - SoC Light_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.zigbee.demos.Z3Light.Z3Light-brd4180a.s37", "text": "Zigbee - SoC Light", "priority": 0, "category": "DEMOS", "toolTipText": "This is a Zigbee 3.0 light application using NVM3 as the persistent storage."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/framework/scenarios/z3/Z3LightGPCombo/readme.html"], "description": "This is a Zigbee 3.0 light application with Green Power endpoint, Green Power Proxy and Sink functionality.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.Z3LightGPCombo.app/framework/scenarios/z3/Z3LightGPCombo/Z3LightGPCombo.slcp", "text": "Zigbee - SoC LightGPCombo", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a Zigbee 3.0 light application with Green Power endpoint, Green Power Proxy and Sink functionality."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/framework/scenarios/zha/StandardizedRfTesting/readme.html"], "description": "This is a pre-standardization implementation of Zigbee's RF testing standard. It utilizes the TIS (Total Isotropic Sensitivity)/ TRP (Total Radiated Power) testing interfaces and is optional for Zigbee certifications. This application adheres to the Zigbee RF Performance Test Spec v1.0.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.StandardizedRfTesting.app/framework/scenarios/zha/StandardizedRfTesting/StandardizedRfTesting.slcp", "text": "Zigbee - SoC StandardizedRfTesting", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a pre-standardization implementation of Zigbee's RF testing standard. It utilizes the TIS (Total Isotropic Sensitivity)/ TRP (Total Radiated Power) testing interfaces and is optional for Zigbee certifications. This application adheres to the Zigbee RF Performance Test Spec v1.0."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/framework/scenarios/z3/Z3Switch/readme.html"], "description": "This is a Zigbee 3.0 switch application using NVM3 as the persistent storage.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.Z3Switch.app/framework/scenarios/z3/Z3Switch/Z3Switch.slcp", "text": "Zigbee - SoC Switch", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a Zigbee 3.0 switch application using NVM3 as the persistent storage."}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/framework/scenarios/z3/Z3Switch/readme.html"], "description": "This is a Zigbee 3.0 switch application using NVM3 as the persistent storage.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_Zigbee - SoC Switch_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.zigbee.demos.Z3Switch.Z3Switch-brd4180a.s37", "text": "Zigbee - SoC Switch", "priority": 0, "category": "DEMOS", "toolTipText": "This is a Zigbee 3.0 switch application using NVM3 as the persistent storage."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/framework/scenarios/multiprotocol/ZigbeeDirectDeviceLight/readme.html"], "description": "This is a sample application demonstrating a Zigbee Direct Device (ZDD) as part of a light application using dynamic multiprotocol (Zigbee + Bluetooth LE) and NVM3 for persistent storage.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ZigbeeDirectDeviceLight.app/framework/scenarios/multiprotocol/ZigbeeDirectDeviceLight/ZigbeeDirectDeviceLight.slcp", "text": "Zigbee - SoC ZigbeeDirectDeviceLight", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a sample application demonstrating a Zigbee Direct Device (ZDD) as part of a light application using dynamic multiprotocol (Zigbee + Bluetooth LE) and NVM3 for persistent storage."}, {"imageURL": "bundleentry://697.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/framework/scenarios/multiprotocol/ZigbeeDirectDeviceLight/readme.html"], "description": "This is a sample application demonstrating a Zigbee Direct Device (ZDD) as part of a light application using dynamic multiprotocol (Zigbee + Bluetooth LE) and NVM3 for persistent storage.", "id": "com.silabs.sdk.stack.super:4.4.4._-********_Zigbee - SoC ZigbeeDirectDeviceLight_asset:..com.silabs.sdk.stack.super_4.4.4.protocol.zigbee.demos.ZigbeeDirectDeviceLight.ZigbeeDirectDeviceLight-brd4180a.s37", "text": "Zigbee - SoC ZigbeeDirectDeviceLight", "priority": 0, "category": "DEMOS", "toolTipText": "This is a sample application demonstrating a Zigbee Direct Device (ZDD) as part of a light application using dynamic multiprotocol (Zigbee + Bluetooth LE) and NVM3 for persistent storage."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/zigbee/app/framework/scenarios/z3/ZigbeeMinimal/readme.html"], "description": "This is a Zigbee minimal SoC network-layer application suitable as a  starting point for new application development.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-********.ZigbeeMinimal.app/framework/scenarios/z3/ZigbeeMinimal/ZigbeeMinimal.slcp", "text": "Zigbee - SoC ZigbeeMinimal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a Zigbee minimal SoC network-layer application suitable as a  starting point for new application development."}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/workspaces/bootloader-apploader/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-********.bootloader-apploader-workspace.sample-apps/workspaces/bootloader-apploader/bootloader-apploader.slcw", "text": "bootloader-apploader-workspace", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/workspaces/bootloader-spi-ezsp/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-********.bootloader-spi-ezsp-workspace.sample-apps/workspaces/bootloader-spi-ezsp/bootloader-spi-ezsp.slcw", "text": "bootloader-spi-ezsp-workspace", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/workspaces/bootloader-uart-bgapi/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-********.bootloader-uart-bgapi-workspace.sample-apps/workspaces/bootloader-uart-bgapi/bootloader-uart-bgapi.slcw", "text": "bootloader-uart-bgapi-workspace", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/workspaces/bootloader-uart-xmodem/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-********.bootloader-uart-xmodem-workspace.sample-apps/workspaces/bootloader-uart-xmodem/bootloader-uart-xmodem.slcw", "text": "bootloader-uart-xmodem-workspace", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "Demonstrates Certificate Based Authentication and Pairing over BLE. This example utilizes TrustZone for storing keys.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_soc_cbap_tz_workspace.example/bt_soc_cbap/bt_soc_cbap_tz.slcw", "text": "bt_soc_cbap_tz_workspace", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "Demonstrates Certificate Based Authentication and Pairing over BLE. This example utilizes TrustZone for storing keys.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": [], "description": "Certificate generating firmware workspace. Software is generating the device EC key pair, the signing request for the device certificate, and other related data. The generated data can be read out by the Central Authority. This example utilizes TrustZone for storing keys.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-********.bt_soc_csr_generator_tz_workspace.example/bt_soc_csr_generator/bt_soc_csr_generator_tz.slcw", "text": "bt_soc_csr_generator_tz_workspace", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "Certificate generating firmware workspace. Software is generating the device EC key pair, the signing request for the device certificate, and other related data. The generated data can be read out by the Central Authority. This example utilizes TrustZone for storing keys.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-tz-secure-app/README.md"], "description": "Workspace to build trustzone enabled ot-cli-ftd application and accompanying bootloader.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-********.ot-cli-ftd-tz-workspace.sample-apps/ot-cli/trustzone/ot-cli-ftd-tz.slcw", "text": "ot-cli-ftd-tz-workspace", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "Workspace to build trustzone enabled ot-cli-ftd application and accompanying bootloader.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-tz-secure-app/README.md"], "description": "Workspace to build trustzone enabled ot-cli-mtd application and accompanying bootloader.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-********.ot-cli-mtd-tz-workspace.sample-apps/ot-cli/trustzone/ot-cli-mtd-tz.slcw", "text": "ot-cli-mtd-tz-workspace", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "Workspace to build trustzone enabled ot-cli-mtd application and accompanying bootloader.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/tz_psa_attestation/readme.md"], "description": "This example workspace demonstrates TrustZone for PSA Attestation.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-********.tz_psa_attestation_ws.example/tz_psa_attestation/tz_psa_attestation_ws.slcw", "text": "tz_psa_attestation_ws", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "This example workspace demonstrates TrustZone for PSA Attestation.\n"}, {"imageURL": "bundleentry://460.fwk1486794042/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/tz_psa_crypto_ecdh/readme.md"], "description": "This example workspace demonstrates TrustZone for ECDH key agreement.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-********.tz_psa_crypto_ecdh_ws.example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_ws.slcw", "text": "tz_psa_crypto_ecdh_ws", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "This example workspace demonstrates TrustZone for ECDH key agreement.\n"}], "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [{"futureCount": 68, "anySelected": false, "id": 0, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": false}, {"futureCount": 17, "anySelected": false, "id": 1, "filters": [], "title": "Bluetooth Mesh", "parentId": 0, "selected": false}, {"futureCount": 7, "anySelected": false, "id": 2, "filters": [], "title": "Connect", "parentId": 0, "selected": false}, {"futureCount": 5, "anySelected": false, "id": 3, "filters": [], "title": "RAIL", "parentId": 0, "selected": false}, {"futureCount": 30, "anySelected": false, "id": 4, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 0, "selected": false}, {"futureCount": 35, "anySelected": false, "id": 5, "filters": [], "title": "Zigbee", "parentId": 0, "selected": false}], "title": "Wireless Technology", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [{"futureCount": 1, "anySelected": false, "id": 0, "filters": [], "title": "Host", "parentId": 1, "selected": false}, {"futureCount": 24, "anySelected": false, "id": 1, "filters": [], "title": "NCP", "parentId": 1, "selected": false}, {"futureCount": 14, "anySelected": false, "id": 2, "filters": [], "title": "RCP", "parentId": 1, "selected": false}, {"futureCount": 174, "anySelected": false, "id": 3, "filters": [], "title": "SoC", "parentId": 1, "selected": false}], "title": "Device Type", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 2, "filters": [{"futureCount": 4, "anySelected": false, "id": 0, "filters": [], "title": "Amazon", "parentId": 2, "selected": false}], "title": "Ecosystem", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 3, "filters": [{"futureCount": 64, "anySelected": false, "id": 0, "filters": [], "title": "32-bit MCU", "parentId": 3, "selected": false}, {"futureCount": 13, "anySelected": false, "id": 1, "filters": [], "title": "Bootloader", "parentId": 3, "selected": false}, {"futureCount": 1, "anySelected": false, "id": 2, "filters": [], "title": "Operating Systems", "parentId": 3, "selected": false}], "title": "MCU", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 4, "filters": [{"futureCount": 2, "anySelected": false, "id": 0, "filters": [], "title": "Machine Learning", "parentId": 4, "selected": false}, {"futureCount": 29, "anySelected": false, "id": 1, "filters": [], "title": "Multiprotocol", "parentId": 4, "selected": false}], "title": "Capability", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 5, "filters": [{"futureCount": 155, "anySelected": false, "id": 0, "filters": [], "title": "Advanced", "parentId": 5, "selected": false}, {"futureCount": 58, "anySelected": false, "id": 1, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": false}], "title": "Project Difficulty", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 6, "filters": [{"futureCount": 15, "anySelected": false, "id": 0, "filters": [], "title": "Evaluation", "parentId": 6, "selected": false}, {"futureCount": 34, "anySelected": false, "id": 1, "filters": [], "title": "Experimental", "parentId": 6, "selected": false}, {"futureCount": 1, "anySelected": false, "id": 2, "filters": [], "title": "Internal", "parentId": 6, "selected": false}, {"futureCount": 9, "anySelected": false, "id": 3, "filters": [], "title": "None Specified", "parentId": 6, "selected": false}, {"futureCount": 174, "anySelected": false, "id": 4, "filters": [], "title": "Production", "parentId": 6, "selected": false}], "title": "Quality", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 7, "filters": [{"futureCount": 192, "anySelected": false, "id": 0, "filters": [], "title": "Gecko SDK Suite v4.4.4", "parentId": 7, "selected": false}, {"futureCount": 10, "anySelected": false, "id": 1, "filters": [], "title": "Peripheral Examples", "parentId": 7, "selected": false}, {"futureCount": 7, "anySelected": false, "id": 2, "filters": [], "title": "WiSeConnect Wi-Fi/BT SDK", "parentId": 7, "selected": false}, {"futureCount": 24, "anySelected": false, "id": 3, "filters": [], "title": "bluetooth_applications", "parentId": 7, "selected": false}], "title": "Provider", "parentId": -1, "selected": false}], "totalCount": 233}