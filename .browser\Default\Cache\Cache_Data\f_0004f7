{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Security Manager<span id=\"security-manager\" class=\"self-anchor\"><a class=\"perm\" href=\"#security-manager\">#</a></span></h1><p style=\"color:inherit\">Security Manager. </p><p style=\"color:inherit\">The commands in this class manage Bluetooth security, including commands for starting and stopping encryption and commands for management of all bonding operations.</p><p style=\"color:inherit\">Use the following procedure to bond with a remote device:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm#sl-bt-sm-configure\" target=\"_blank\" rel=\"\">sl_bt_sm_configure</a> to configure security requirements and I/O capabilities of this device.</p></li><li><p style=\"color:inherit\">Use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm#sl-bt-sm-set-bondable-mode\" target=\"_blank\" rel=\"\">sl_bt_sm_set_bondable_mode</a> to set this device into bondable mode.</p></li><li><p style=\"color:inherit\">Use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-open\" target=\"_blank\" rel=\"\">sl_bt_connection_open</a> to open a connection to the remote device.</p></li><li><p style=\"color:inherit\">After the connection is open, use command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm#sl-bt-sm-increase-security\" target=\"_blank\" rel=\"\">sl_bt_sm_increase_security</a> to encrypt the connection. This will also start the bonding process.</p></li></ul><p style=\"color:inherit\">Use the following procedure to respond to the bonding initiated by a remote device:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm#sl-bt-sm-configure\" target=\"_blank\" rel=\"\">sl_bt_sm_configure</a> to configure security requirements and I/O capabilities of this device.</p></li><li><p style=\"color:inherit\">Use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm#sl-bt-sm-set-bondable-mode\" target=\"_blank\" rel=\"\">sl_bt_sm_set_bondable_mode</a> to set this device into bondable mode.</p></li><li><p style=\"color:inherit\">Use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_advertiser_start</a> to start connectable advertising.</p></li><li><p style=\"color:inherit\">Open a connection to this device from the remote device.</p></li><li><p style=\"color:inherit\">After the connection is open, start the bonding process on the remote device.</p></li></ul><p style=\"color:inherit\">If MITM is required, the application needs to display or ask the user to enter a passkey during the process. See events <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sm-passkey-display\" target=\"_blank\" rel=\"\">sl_bt_evt_sm_passkey_display</a> and <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sm-passkey-request\" target=\"_blank\" rel=\"\">sl_bt_evt_sm_passkey_request</a> for more information. </p><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm-configuration\" target=\"_blank\" rel=\"\">Security Manager configuration flags</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sm-passkey-display\" target=\"_blank\" rel=\"\">sl_bt_evt_sm_passkey_display</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sm-passkey-request\" target=\"_blank\" rel=\"\">sl_bt_evt_sm_passkey_request</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sm-confirm-passkey\" target=\"_blank\" rel=\"\">sl_bt_evt_sm_confirm_passkey</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sm-bonded\" target=\"_blank\" rel=\"\">sl_bt_evt_sm_bonded</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sm-bonding-failed\" target=\"_blank\" rel=\"\">sl_bt_evt_sm_bonding_failed</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sm-confirm-bonding\" target=\"_blank\" rel=\"\">sl_bt_evt_sm_confirm_bonding</a></p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-io-capability-t\">sl_bt_sm_io_capability_t</a> {</div><div class=\"enum\">sl_bt_sm_io_capability_displayonly = 0x0</div><div class=\"enum\">sl_bt_sm_io_capability_displayyesno = 0x1</div><div class=\"enum\">sl_bt_sm_io_capability_keyboardonly = 0x2</div><div class=\"enum\">sl_bt_sm_io_capability_noinputnooutput = 0x3</div><div class=\"enum\">sl_bt_sm_io_capability_keyboarddisplay = 0x4</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values define the security management related I/O capabilities supported by the device. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-configure\">sl_bt_sm_configure</a>(uint8_t flags, uint8_t io_capabilities)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-set-minimum-key-size\">sl_bt_sm_set_minimum_key_size</a>(uint8_t minimum_key_size)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-set-debug-mode\">sl_bt_sm_set_debug_mode</a>()</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-add-to-whitelist\">sl_bt_sm_add_to_whitelist</a>(bd_addr address, uint8_t address_type)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-store-bonding-configuration\">sl_bt_sm_store_bonding_configuration</a>(uint8_t max_bonding_count, uint8_t policy_flags)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-set-bondable-mode\">sl_bt_sm_set_bondable_mode</a>(uint8_t bondable)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-set-passkey\">sl_bt_sm_set_passkey</a>(int32_t passkey)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-increase-security\">sl_bt_sm_increase_security</a>(uint8_t connection)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-enter-passkey\">sl_bt_sm_enter_passkey</a>(uint8_t connection, int32_t passkey)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-passkey-confirm\">sl_bt_sm_passkey_confirm</a>(uint8_t connection, uint8_t confirm)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-bonding-confirm\">sl_bt_sm_bonding_confirm</a>(uint8_t connection, uint8_t confirm)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-delete-bonding\">sl_bt_sm_delete_bonding</a>(uint8_t bonding)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-delete-bondings\">sl_bt_sm_delete_bondings</a>()</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-get-bonding-handles\">sl_bt_sm_get_bonding_handles</a>(uint32_t reserved, uint32_t *num_bondings, size_t max_bondings_size, size_t *bondings_len, uint8_t *bondings)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-get-bonding-details\">sl_bt_sm_get_bonding_details</a>(uint32_t bonding, bd_addr *address, uint8_t *address_type, uint8_t *security_mode, uint8_t *key_size)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-find-bonding-by-address\">sl_bt_sm_find_bonding_by_address</a>(bd_addr address, uint32_t *bonding, uint8_t *security_mode, uint8_t *key_size)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-resolve-rpa\">sl_bt_sm_resolve_rpa</a>(bd_addr rpa, bd_addr *address, uint8_t *address_type, uint32_t *bonding)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-set-bonding-key\">sl_bt_sm_set_bonding_key</a>(uint32_t bonding, uint8_t key_type, aes_key_128 key)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-set-legacy-oob\">sl_bt_sm_set_legacy_oob</a>(uint8_t enable, aes_key_128 oob_data)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-set-oob\">sl_bt_sm_set_oob</a>(uint8_t enable, aes_key_128 *random, aes_key_128 *confirm)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-set-remote-oob\">sl_bt_sm_set_remote_oob</a>(uint8_t enable, aes_key_128 random, aes_key_128 confirm)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sm-set-bonding-data\">sl_bt_sm_set_bonding_data</a>(uint8_t connection, uint8_t type, size_t data_len, const uint8_t *data)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-configure-id\">sl_bt_cmd_sm_configure_id</a> 0x010f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-set-minimum-key-size-id\">sl_bt_cmd_sm_set_minimum_key_size_id</a> 0x140f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-set-debug-mode-id\">sl_bt_cmd_sm_set_debug_mode_id</a> 0x0f0f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-add-to-whitelist-id\">sl_bt_cmd_sm_add_to_whitelist_id</a> 0x130f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-store-bonding-configuration-id\">sl_bt_cmd_sm_store_bonding_configuration_id</a> 0x020f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-set-bondable-mode-id\">sl_bt_cmd_sm_set_bondable_mode_id</a> 0x000f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-set-passkey-id\">sl_bt_cmd_sm_set_passkey_id</a> 0x100f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-increase-security-id\">sl_bt_cmd_sm_increase_security_id</a> 0x040f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-enter-passkey-id\">sl_bt_cmd_sm_enter_passkey_id</a> 0x080f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-passkey-confirm-id\">sl_bt_cmd_sm_passkey_confirm_id</a> 0x090f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-bonding-confirm-id\">sl_bt_cmd_sm_bonding_confirm_id</a> 0x0e0f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-delete-bonding-id\">sl_bt_cmd_sm_delete_bonding_id</a> 0x060f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-delete-bondings-id\">sl_bt_cmd_sm_delete_bondings_id</a> 0x070f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-get-bonding-handles-id\">sl_bt_cmd_sm_get_bonding_handles_id</a> 0x150f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-get-bonding-details-id\">sl_bt_cmd_sm_get_bonding_details_id</a> 0x160f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-find-bonding-by-address-id\">sl_bt_cmd_sm_find_bonding_by_address_id</a> 0x170f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-resolve-rpa-id\">sl_bt_cmd_sm_resolve_rpa_id</a> 0x1d0f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-set-bonding-key-id\">sl_bt_cmd_sm_set_bonding_key_id</a> 0x180f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-set-legacy-oob-id\">sl_bt_cmd_sm_set_legacy_oob_id</a> 0x190f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-set-oob-id\">sl_bt_cmd_sm_set_oob_id</a> 0x1a0f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-set-remote-oob-id\">sl_bt_cmd_sm_set_remote_oob_id</a> 0x1b0f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sm-set-bonding-data-id\">sl_bt_cmd_sm_set_bonding_data_id</a> 0x1c0f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-configure-id\">sl_bt_rsp_sm_configure_id</a> 0x010f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-set-minimum-key-size-id\">sl_bt_rsp_sm_set_minimum_key_size_id</a> 0x140f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-set-debug-mode-id\">sl_bt_rsp_sm_set_debug_mode_id</a> 0x0f0f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-add-to-whitelist-id\">sl_bt_rsp_sm_add_to_whitelist_id</a> 0x130f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-store-bonding-configuration-id\">sl_bt_rsp_sm_store_bonding_configuration_id</a> 0x020f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-set-bondable-mode-id\">sl_bt_rsp_sm_set_bondable_mode_id</a> 0x000f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-set-passkey-id\">sl_bt_rsp_sm_set_passkey_id</a> 0x100f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-increase-security-id\">sl_bt_rsp_sm_increase_security_id</a> 0x040f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-enter-passkey-id\">sl_bt_rsp_sm_enter_passkey_id</a> 0x080f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-passkey-confirm-id\">sl_bt_rsp_sm_passkey_confirm_id</a> 0x090f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-bonding-confirm-id\">sl_bt_rsp_sm_bonding_confirm_id</a> 0x0e0f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-delete-bonding-id\">sl_bt_rsp_sm_delete_bonding_id</a> 0x060f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-delete-bondings-id\">sl_bt_rsp_sm_delete_bondings_id</a> 0x070f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-get-bonding-handles-id\">sl_bt_rsp_sm_get_bonding_handles_id</a> 0x150f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-get-bonding-details-id\">sl_bt_rsp_sm_get_bonding_details_id</a> 0x160f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-find-bonding-by-address-id\">sl_bt_rsp_sm_find_bonding_by_address_id</a> 0x170f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-resolve-rpa-id\">sl_bt_rsp_sm_resolve_rpa_id</a> 0x1d0f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-set-bonding-key-id\">sl_bt_rsp_sm_set_bonding_key_id</a> 0x180f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-set-legacy-oob-id\">sl_bt_rsp_sm_set_legacy_oob_id</a> 0x190f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-set-oob-id\">sl_bt_rsp_sm_set_oob_id</a> 0x1a0f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-set-remote-oob-id\">sl_bt_rsp_sm_set_remote_oob_id</a> 0x1b0f0020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sm-set-bonding-data-id\">sl_bt_rsp_sm_set_bonding_data_id</a> 0x1c0f0020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>sl_bt_sm_io_capability_t<span id=\"sl-bt-sm-io-capability-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-io-capability-t\">#</a></span></h3><blockquote>sl_bt_sm_io_capability_t</blockquote><p style=\"color:inherit\">These values define the security management related I/O capabilities supported by the device. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_sm_io_capability_displayonly</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Display Only </p></td></tr><tr><td class=\"fieldname\">sl_bt_sm_io_capability_displayyesno</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Display with Yes/No-buttons </p></td></tr><tr><td class=\"fieldname\">sl_bt_sm_io_capability_keyboardonly</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) Keyboard Only </p></td></tr><tr><td class=\"fieldname\">sl_bt_sm_io_capability_noinputnooutput</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x3) No Input and No Output </p></td></tr><tr><td class=\"fieldname\">sl_bt_sm_io_capability_keyboarddisplay</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x4) Display with Keyboard </p></td></tr></tbody></table><br><div>Definition at line <code>10742</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_sm_configure<span id=\"sl-bt-sm-configure\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-configure\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_configure (uint8_t flags, uint8_t io_capabilities)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">Security requirement flags. This value can be a bitmask of multiple flags from <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm-configuration\" target=\"_blank\" rel=\"\">Security Manager configuration flags</a></p><p style=\"color:inherit\">Bit 0:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Allow bonding without authentication</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Bonding requires authentication (Man-in-the-Middle protection)</p></li></ul><p style=\"color:inherit\">Bit 1:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Allow encryption without bonding</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Encryption requires bonding. Note that this setting will also enable bonding.</p></li></ul><p style=\"color:inherit\">Bit 2:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Allow bonding with legacy pairing</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Secure connections only</p></li></ul><p style=\"color:inherit\">Bit 3:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Bonding request does not need to be confirmed</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Bonding requests need to be confirmed. Received bonding requests are notified by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sm-confirm-bonding\" target=\"_blank\" rel=\"\">sl_bt_evt_sm_confirm_bonding</a></p></li></ul><p style=\"color:inherit\">Bit 4: This option is ignored when the application includes the bluetooth_feature_external_bonding_database feature.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Allow all connections</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Allow connections only from bonded devices</p></li></ul><p style=\"color:inherit\">Bit 5:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Prefer just works pairing when both options are possible based on the settings.</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Prefer authenticated pairing when both options are possible based on the settings.</p></li></ul><p style=\"color:inherit\">Bit 6:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Allow secure connections OOB pairing with OOB data from only one device.</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Require secure connections OOB data from both devices.</p></li></ul><p style=\"color:inherit\">Bit 7:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Allow debug keys from remote device.</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Reject pairing if remote device uses debug keys.</p></li></ul><p style=\"color:inherit\">Default value: 0x00 </p></td></tr><tr><td>[in]</td><td class=\"paramname\">io_capabilities</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm#sl-bt-sm-io-capability-t\" target=\"_blank\" rel=\"\">sl_bt_sm_io_capability_t</a>. I/O Capabilities. The default I/O Capability used by the stack is No Input and No Output. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_sm_io_capability_displayonly (0x0):</strong> Display Only</p></li><li><p style=\"color:inherit\"><strong>sl_bt_sm_io_capability_displayyesno (0x1):</strong> Display with Yes/No-buttons</p></li><li><p style=\"color:inherit\"><strong>sl_bt_sm_io_capability_keyboardonly (0x2):</strong> Keyboard Only</p></li><li><p style=\"color:inherit\"><strong>sl_bt_sm_io_capability_noinputnooutput (0x3):</strong> No Input and No Output</p></li><li><p style=\"color:inherit\"><strong>sl_bt_sm_io_capability_keyboarddisplay (0x4):</strong> Display with Keyboard</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Configure security requirements and I/O capabilities of the system.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11029</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_set_minimum_key_size<span id=\"sl-bt-sm-set-minimum-key-size\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-set-minimum-key-size\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_set_minimum_key_size (uint8_t minimum_key_size)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">minimum_key_size</td><td><p style=\"color:inherit\">Minimum allowed key size for bonding. Range: 7 to 16</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set the minimum allowed key size used for bonding. The default value is 16 bytes.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11042</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_set_debug_mode<span id=\"sl-bt-sm-set-debug-mode\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-set-debug-mode\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_set_debug_mode ()</blockquote><p style=\"color:inherit\">Set Security Manager in debug mode. In this mode, the secure connections bonding uses known debug keys, so that the encrypted packet can be opened by Bluetooth protocol analyzer. To disable the debug mode, restart the device.</p><p style=\"color:inherit\">Bondings made in debug mode are unsecure.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11056</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_add_to_whitelist<span id=\"sl-bt-sm-add-to-whitelist\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-add-to-whitelist\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_sm_add_to_whitelist (<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> address, uint8_t address_type)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">address</td><td><p style=\"color:inherit\">Address of the device added to accept list </p></td></tr><tr><td>[in]</td><td class=\"paramname\">address_type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>. Address type of the device added to accept list. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address (0x0):</strong> Public device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-accept-list#sl-bt-accept-list-add-device-by-bonding\" target=\"_blank\" rel=\"\">sl_bt_accept_list_add_device_by_bonding</a> and <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-accept-list#sl-bt-accept-list-add-device-by-address\" target=\"_blank\" rel=\"\">sl_bt_accept_list_add_device_by_address</a> provided by the bluetooth_feature_accept_list component.</p><p style=\"color:inherit\">Add device to accept list, which can be enabled with <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-enable-whitelisting\" target=\"_blank\" rel=\"\">sl_bt_gap_enable_whitelisting</a>.</p><p style=\"color:inherit\">When using external bonding database, the accept list size must be set before adding devices to the list using <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm#sl-bt-sm-store-bonding-configuration\" target=\"_blank\" rel=\"\">sl_bt_sm_store_bonding_configuration</a>.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11080</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_store_bonding_configuration<span id=\"sl-bt-sm-store-bonding-configuration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-store-bonding-configuration\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_store_bonding_configuration (uint8_t max_bonding_count, uint8_t policy_flags)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">max_bonding_count</td><td><p style=\"color:inherit\">Maximum allowed bonding count. Range: 1 to 32</p><p style=\"color:inherit\">Sets the accept list size with external bonding database. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">policy_flags</td><td><p style=\"color:inherit\">Bonding policy. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> If database is full, new bonding attempts will fail</p></li><li><p style=\"color:inherit\"><strong>1:</strong> New bonding will overwrite the oldest existing bonding</p></li><li><p style=\"color:inherit\"><strong>2:</strong> New bonding will overwrite the bonding that was used the longest time ago</p></li></ul><p style=\"color:inherit\">Default: 0</p><p style=\"color:inherit\">With external bonding database the parameter is ignored. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Set the maximum allowed bonding count and bonding policy. The maximum number of bondings that can be supported depends on how much user data is stored in the NVM and the NVM size. When bond policy value 1 or 2 is selected, the stack will automatically write the new bond, as per the policy, only if the maximum allowed bonding count has been reached. If the stack can't write a new bond for any other reason (e.g., NVM is full), an error will be thrown through the bonding_failed event indicating why the bonding was not written. The application has to manually release space from the NVM (e.g., by deleting one of the existing bonds or application data) so that a new bond can be saved. The default value is 13.</p><p style=\"color:inherit\">When using external bonding database with accept list filtering, this command must be called before adding devices to the accept list to define the list size. Calling this function empties the existing accept list.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11119</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_set_bondable_mode<span id=\"sl-bt-sm-set-bondable-mode\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-set-bondable-mode\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_set_bondable_mode (uint8_t bondable)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">bondable</td><td><p style=\"color:inherit\">Bondable mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> New bondings not accepted</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Bondings allowed</p></li></ul><p style=\"color:inherit\">Default value: 0 </p></td></tr></tbody></table></div><p style=\"color:inherit\">Set whether the device should accept new bondings. By default, the device does not accept new bondings.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11138</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_set_passkey<span id=\"sl-bt-sm-set-passkey\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-set-passkey\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_set_passkey (int32_t passkey)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">passkey</td><td><p style=\"color:inherit\">Passkey. Valid range: 0-999999. Set -1 to disable and start using random passkeys.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Enter a fixed passkey, which will be used in the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sm-passkey-display\" target=\"_blank\" rel=\"\">sl_bt_evt_sm_passkey_display</a> event.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11151</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_increase_security<span id=\"sl-bt-sm-increase-security\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-increase-security\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_increase_security (uint8_t connection)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Enhance the security of a connection to current security requirements. On an unencrypted connection, it will encrypt the connection and will also perform bonding if requested by both devices. On an encrypted connection, it will cause the connection to be re-encrypted.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-parameters\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_parameters</a> - Triggered after increasing security has been completed successfully and indicates the latest security mode of the connection.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sm-bonded\" target=\"_blank\" rel=\"\">sl_bt_evt_sm_bonded</a> - Triggered if pairing or bonding was performed in this operation and the result is successful.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sm-bonding-failed\" target=\"_blank\" rel=\"\">sl_bt_evt_sm_bonding_failed</a> - Triggered if pairing or bonding was performed in this operation and the result has failed. </p></li></ul><br><div>Definition at line <code>11174</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_enter_passkey<span id=\"sl-bt-sm-enter-passkey\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-enter-passkey\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_enter_passkey (uint8_t connection, int32_t passkey)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">passkey</td><td><p style=\"color:inherit\">Passkey. Valid range: 0-999999. Set -1 to cancel pairing.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Enter a passkey after receiving a passkey request event.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11186</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_passkey_confirm<span id=\"sl-bt-sm-passkey-confirm\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-passkey-confirm\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_passkey_confirm (uint8_t connection, uint8_t confirm)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">confirm</td><td><p style=\"color:inherit\">Acceptance. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Reject</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Accept confirm value</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Accept or reject the reported passkey confirm value.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11200</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_bonding_confirm<span id=\"sl-bt-sm-bonding-confirm\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-bonding-confirm\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_bonding_confirm (uint8_t connection, uint8_t confirm)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">confirm</td><td><p style=\"color:inherit\">Acceptance. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Reject</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Accept bonding request</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Accept or reject the bonding request.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11214</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_delete_bonding<span id=\"sl-bt-sm-delete-bonding\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-delete-bonding\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_delete_bonding (uint8_t bonding)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">bonding</td><td><p style=\"color:inherit\">Bonding handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Delete the specified bonding or accept list filtering. The connection will be closed if the remote device is connected currently.</p><p style=\"color:inherit\">This commands deletes the information from the persistent bonding database when the built-in bonding database (bluetooth_feature_builtin_bonding_database) is used.</p><p style=\"color:inherit\">This command is unavailable if the external bonding database (bluetooth_feature_external_bonding_database) is used.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11233</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_delete_bondings<span id=\"sl-bt-sm-delete-bondings\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-delete-bondings\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_delete_bondings ()</blockquote><p style=\"color:inherit\">Delete all bondings, accept list filtering and device local identity resolving key (IRK). All connections to affected devices are closed as well.</p><p style=\"color:inherit\">This command empties the persistent bonding database when the built-in bonding database (bluetooth_feature_builtin_bonding_database) is used.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11247</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_get_bonding_handles<span id=\"sl-bt-sm-get-bonding-handles\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-get-bonding-handles\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_get_bonding_handles (uint32_t reserved, uint32_t * num_bondings, size_t max_bondings_size, size_t * bondings_len, uint8_t * bondings)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">reserved</td><td><p style=\"color:inherit\">Use the value 0 on this reserved field. Do not use none-zero values because they are reserved for future use. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">num_bondings</td><td><p style=\"color:inherit\">Total number of bondings and accept list filtering devices stored in bonding database. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">max_bondings_size</td><td><p style=\"color:inherit\">Size of output buffer passed in <code>bondings</code></p></td></tr><tr><td>[out]</td><td class=\"paramname\">bondings_len</td><td><p style=\"color:inherit\">On return, set to the length of output data written to <code>bondings</code></p></td></tr><tr><td>[out]</td><td class=\"paramname\">bondings</td><td><p style=\"color:inherit\">4 byte bit field of used bonding handles in little endian format. Bit 0 of first byte is bonding handle 0, bit 0 of second byte is bonding handle 8 etc. If the bit is 1 that bonding handle exists in the bonding database.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Get number of entries and bitmask of their handles saved in the bonding database. The entry in the bonding database can be either bonding or accept list filtering device.</p><p style=\"color:inherit\">To get the bonding type and peer device address of a bonding, use the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm#sl-bt-sm-get-bonding-details\" target=\"_blank\" rel=\"\">sl_bt_sm_get_bonding_details</a> command. The bonding handle can be calculated from the handle bitmask returned by this command, or alternatively, repeat calling the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm#sl-bt-sm-get-bonding-details\" target=\"_blank\" rel=\"\">sl_bt_sm_get_bonding_details</a> command to get the detailed information of all bondings.</p><p style=\"color:inherit\">This command is unavailable if the external bonding database (bluetooth_feature_external_bonding_database) is used.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11279</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_get_bonding_details<span id=\"sl-bt-sm-get-bonding-details\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-get-bonding-details\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_get_bonding_details (uint32_t bonding, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> * address, uint8_t * address_type, uint8_t * security_mode, uint8_t * key_size)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">bonding</td><td><p style=\"color:inherit\">Bonding handle </p></td></tr><tr><td>[out]</td><td class=\"paramname\">address</td><td><p style=\"color:inherit\">Bluetooth address of the remote device </p></td></tr><tr><td>[out]</td><td class=\"paramname\">address_type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>. Address type. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address (0x0):</strong> Public device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">security_mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-security-t\" target=\"_blank\" rel=\"\">sl_bt_connection_security_t</a>. Connection security mode. Accept list filtering entry has security mode as no security. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_connection_mode1_level1 (0x0):</strong> No security</p></li><li><p style=\"color:inherit\"><strong>sl_bt_connection_mode1_level2 (0x1):</strong> Unauthenticated pairing with encryption</p></li><li><p style=\"color:inherit\"><strong>sl_bt_connection_mode1_level3 (0x2):</strong> Authenticated pairing with encryption</p></li><li><p style=\"color:inherit\"><strong>sl_bt_connection_mode1_level4 (0x3):</strong> Authenticated Secure Connections pairing with encryption using a 128-bit strength encryption key </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">key_size</td><td><p style=\"color:inherit\">Key length in bytes, 0 for accept list filtering entry</p></td></tr></tbody></table></div><p style=\"color:inherit\">Get the detailed information for a bonding entry. Data includes remote device address and address type as well as security mode for bonding and a used encryption key length.</p><p style=\"color:inherit\">To get the detailed information of all bondings, repeat calling this command starting from 0 as the bonding handle value until the maximum number of configured bondings are reached. Use 32 as the maximum number if the configured number is unknown.</p><p style=\"color:inherit\">This command is unavailable if the external bonding database (bluetooth_feature_external_bonding_database) is used.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11321</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_find_bonding_by_address<span id=\"sl-bt-sm-find-bonding-by-address\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-find-bonding-by-address\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_find_bonding_by_address (<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> address, uint32_t * bonding, uint8_t * security_mode, uint8_t * key_size)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">address</td><td><p style=\"color:inherit\">The Bluetooth device address </p></td></tr><tr><td>[out]</td><td class=\"paramname\">bonding</td><td><p style=\"color:inherit\">The bonding handle </p></td></tr><tr><td>[out]</td><td class=\"paramname\">security_mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection#sl-bt-connection-security-t\" target=\"_blank\" rel=\"\">sl_bt_connection_security_t</a>. Connection security mode. Accept list filtering entry has security mode as no security. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_connection_mode1_level1 (0x0):</strong> No security</p></li><li><p style=\"color:inherit\"><strong>sl_bt_connection_mode1_level2 (0x1):</strong> Unauthenticated pairing with encryption</p></li><li><p style=\"color:inherit\"><strong>sl_bt_connection_mode1_level3 (0x2):</strong> Authenticated pairing with encryption</p></li><li><p style=\"color:inherit\"><strong>sl_bt_connection_mode1_level4 (0x3):</strong> Authenticated Secure Connections pairing with encryption using a 128-bit strength encryption key </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">key_size</td><td><p style=\"color:inherit\">Key length in bytes, 0 for accept list filtering entry</p></td></tr></tbody></table></div><p style=\"color:inherit\">Find the bonding or accept list filtering entry by using a Bluetooth device address.</p><p style=\"color:inherit\">This command is unavailable if the external bonding database (bluetooth_feature_external_bonding_database) is used.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11353</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_resolve_rpa<span id=\"sl-bt-sm-resolve-rpa\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-resolve-rpa\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_resolve_rpa (<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> rpa, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> * address, uint8_t * address_type, uint32_t * bonding)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">rpa</td><td><p style=\"color:inherit\">Resolvable private address to be resolved </p></td></tr><tr><td>[out]</td><td class=\"paramname\">address</td><td><p style=\"color:inherit\">The identity adderss of the bonded device </p></td></tr><tr><td>[out]</td><td class=\"paramname\">address_type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>. Identity address type. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address (0x0):</strong> Public device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">bonding</td><td><p style=\"color:inherit\">The bonding handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Find the identity address of bonded device by using resolvable private address (RPA).</p><p style=\"color:inherit\">The error SL_STATUS_NOT_FOUND is returned if the RPA cannot be resolved.</p><p style=\"color:inherit\">This command is unavailable if the external bonding database (bluetooth_feature_external_bonding_database) is used.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11379</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_set_bonding_key<span id=\"sl-bt-sm-set-bonding-key\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-set-bonding-key\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_set_bonding_key (uint32_t bonding, uint8_t key_type, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/aes-key-128\" target=\"_blank\" rel=\"\">aes_key_128</a> key)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\">bonding</td><td></td></tr><tr><td>N/A</td><td class=\"paramname\">key_type</td><td></td></tr><tr><td>N/A</td><td class=\"paramname\">key</td><td></td></tr></tbody></table></div><br><div>Definition at line <code>11410</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_set_legacy_oob<span id=\"sl-bt-sm-set-legacy-oob\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-set-legacy-oob\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_set_legacy_oob (uint8_t enable, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/aes-key-128\" target=\"_blank\" rel=\"\">aes_key_128</a> oob_data)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">enable</td><td><p style=\"color:inherit\">Enable OOB with legacy pairing. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> disable</p></li><li><p style=\"color:inherit\"><strong>1:</strong> enable </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">oob_data</td><td><p style=\"color:inherit\">16-byte legacy pairing OOB data in little endian format.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set Out-Of-Band (OOB) encryption data for a legacy pairing of a device. OOB data may be, for example, a PIN code exchanged over an alternate path, such as NFC. The device will not allow any other bonding if OOB data is set. OOB data can't be set simultaneously with secure connections OOB data.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11429</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_set_oob<span id=\"sl-bt-sm-set-oob\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-set-oob\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_set_oob (uint8_t enable, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/aes-key-128\" target=\"_blank\" rel=\"\">aes_key_128</a> * random, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/aes-key-128\" target=\"_blank\" rel=\"\">aes_key_128</a> * confirm)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">enable</td><td><p style=\"color:inherit\">Enable OOB with secure connections pairing. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> disable</p></li><li><p style=\"color:inherit\"><strong>1:</strong> enable </p></li></ul></td></tr><tr><td>[out]</td><td class=\"paramname\">random</td><td><p style=\"color:inherit\">16-byte randomly-generated secure connections OOB data in little endian format. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">confirm</td><td><p style=\"color:inherit\">16-byte confirm value for the OOB random value in little endian format.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Enable the use of Out-Of-Band (OOB) encryption data for a device for secure connections pairing. Enabling will generate new OOB data and confirm values, which can be sent to the remote device. After enabling the secure connections OOB data, the remote devices OOB data can be set with <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm#sl-bt-sm-set-remote-oob\" target=\"_blank\" rel=\"\">sl_bt_sm_set_remote_oob</a>. Calling this function will erase any set remote device OOB data and confirm values. The device will not allow any other bonding if OOB data is set. The secure connections OOB data cannot be enabled simultaneously with legacy pairing OOB data.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11453</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_set_remote_oob<span id=\"sl-bt-sm-set-remote-oob\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-set-remote-oob\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_set_remote_oob (uint8_t enable, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/aes-key-128\" target=\"_blank\" rel=\"\">aes_key_128</a> random, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/aes-key-128\" target=\"_blank\" rel=\"\">aes_key_128</a> confirm)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">enable</td><td><p style=\"color:inherit\">Enable remote device OOB data with secure connections pairing. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> disable</p></li><li><p style=\"color:inherit\"><strong>1:</strong> enable </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">random</td><td><p style=\"color:inherit\">16-byte remote device secure connections OOB data in little endian format. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">confirm</td><td><p style=\"color:inherit\">16-byte remote device confirm value for the OOB random value in little endian format.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set Out-Of-Band (OOB) data and confirm values received from the remote device for secure connections pairing. OOB data must be enabled with <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm#sl-bt-sm-set-oob\" target=\"_blank\" rel=\"\">sl_bt_sm_set_oob</a> before setting the remote device OOB data.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>11475</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sm_set_bonding_data<span id=\"sl-bt-sm-set-bonding-data\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sm-set-bonding-data\">#</a></span></h3><blockquote>sl_status_t sl_bt_sm_set_bonding_data (uint8_t connection, uint8_t type, size_t data_len, const uint8_t * data)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\">connection</td><td></td></tr><tr><td>N/A</td><td class=\"paramname\">type</td><td></td></tr><tr><td>N/A</td><td class=\"paramname\">data_len</td><td></td></tr><tr><td>N/A</td><td class=\"paramname\">data</td><td></td></tr></tbody></table></div><br><div>Definition at line <code>11512</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_sm_configure_id<span id=\"sl-bt-cmd-sm-configure-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-configure-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_configure_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x010f0020</pre><br><div>Definition at line <code>10639</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_set_minimum_key_size_id<span id=\"sl-bt-cmd-sm-set-minimum-key-size-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-set-minimum-key-size-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_set_minimum_key_size_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x140f0020</pre><br><div>Definition at line <code>10640</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_set_debug_mode_id<span id=\"sl-bt-cmd-sm-set-debug-mode-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-set-debug-mode-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_set_debug_mode_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0f0f0020</pre><br><div>Definition at line <code>10641</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_add_to_whitelist_id<span id=\"sl-bt-cmd-sm-add-to-whitelist-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-add-to-whitelist-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_add_to_whitelist_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x130f0020</pre><br><div>Definition at line <code>10642</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_store_bonding_configuration_id<span id=\"sl-bt-cmd-sm-store-bonding-configuration-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-store-bonding-configuration-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_store_bonding_configuration_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x020f0020</pre><br><div>Definition at line <code>10643</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_set_bondable_mode_id<span id=\"sl-bt-cmd-sm-set-bondable-mode-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-set-bondable-mode-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_set_bondable_mode_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x000f0020</pre><br><div>Definition at line <code>10644</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_set_passkey_id<span id=\"sl-bt-cmd-sm-set-passkey-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-set-passkey-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_set_passkey_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x100f0020</pre><br><div>Definition at line <code>10645</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_increase_security_id<span id=\"sl-bt-cmd-sm-increase-security-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-increase-security-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_increase_security_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x040f0020</pre><br><div>Definition at line <code>10646</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_enter_passkey_id<span id=\"sl-bt-cmd-sm-enter-passkey-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-enter-passkey-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_enter_passkey_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x080f0020</pre><br><div>Definition at line <code>10647</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_passkey_confirm_id<span id=\"sl-bt-cmd-sm-passkey-confirm-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-passkey-confirm-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_passkey_confirm_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x090f0020</pre><br><div>Definition at line <code>10648</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_bonding_confirm_id<span id=\"sl-bt-cmd-sm-bonding-confirm-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-bonding-confirm-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_bonding_confirm_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0e0f0020</pre><br><div>Definition at line <code>10649</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_delete_bonding_id<span id=\"sl-bt-cmd-sm-delete-bonding-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-delete-bonding-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_delete_bonding_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x060f0020</pre><br><div>Definition at line <code>10650</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_delete_bondings_id<span id=\"sl-bt-cmd-sm-delete-bondings-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-delete-bondings-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_delete_bondings_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x070f0020</pre><br><div>Definition at line <code>10651</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_get_bonding_handles_id<span id=\"sl-bt-cmd-sm-get-bonding-handles-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-get-bonding-handles-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_get_bonding_handles_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x150f0020</pre><br><div>Definition at line <code>10652</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_get_bonding_details_id<span id=\"sl-bt-cmd-sm-get-bonding-details-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-get-bonding-details-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_get_bonding_details_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x160f0020</pre><br><div>Definition at line <code>10653</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_find_bonding_by_address_id<span id=\"sl-bt-cmd-sm-find-bonding-by-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-find-bonding-by-address-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_find_bonding_by_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x170f0020</pre><br><div>Definition at line <code>10654</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_resolve_rpa_id<span id=\"sl-bt-cmd-sm-resolve-rpa-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-resolve-rpa-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_resolve_rpa_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1d0f0020</pre><br><div>Definition at line <code>10655</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_set_bonding_key_id<span id=\"sl-bt-cmd-sm-set-bonding-key-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-set-bonding-key-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_set_bonding_key_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x180f0020</pre><br><div>Definition at line <code>10656</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_set_legacy_oob_id<span id=\"sl-bt-cmd-sm-set-legacy-oob-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-set-legacy-oob-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_set_legacy_oob_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x190f0020</pre><br><div>Definition at line <code>10657</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_set_oob_id<span id=\"sl-bt-cmd-sm-set-oob-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-set-oob-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_set_oob_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1a0f0020</pre><br><div>Definition at line <code>10658</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_set_remote_oob_id<span id=\"sl-bt-cmd-sm-set-remote-oob-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-set-remote-oob-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_set_remote_oob_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1b0f0020</pre><br><div>Definition at line <code>10659</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sm_set_bonding_data_id<span id=\"sl-bt-cmd-sm-set-bonding-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sm-set-bonding-data-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sm_set_bonding_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1c0f0020</pre><br><div>Definition at line <code>10660</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_configure_id<span id=\"sl-bt-rsp-sm-configure-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-configure-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_configure_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x010f0020</pre><br><div>Definition at line <code>10661</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_set_minimum_key_size_id<span id=\"sl-bt-rsp-sm-set-minimum-key-size-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-set-minimum-key-size-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_set_minimum_key_size_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x140f0020</pre><br><div>Definition at line <code>10662</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_set_debug_mode_id<span id=\"sl-bt-rsp-sm-set-debug-mode-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-set-debug-mode-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_set_debug_mode_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0f0f0020</pre><br><div>Definition at line <code>10663</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_add_to_whitelist_id<span id=\"sl-bt-rsp-sm-add-to-whitelist-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-add-to-whitelist-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_add_to_whitelist_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x130f0020</pre><br><div>Definition at line <code>10664</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_store_bonding_configuration_id<span id=\"sl-bt-rsp-sm-store-bonding-configuration-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-store-bonding-configuration-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_store_bonding_configuration_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x020f0020</pre><br><div>Definition at line <code>10665</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_set_bondable_mode_id<span id=\"sl-bt-rsp-sm-set-bondable-mode-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-set-bondable-mode-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_set_bondable_mode_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x000f0020</pre><br><div>Definition at line <code>10666</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_set_passkey_id<span id=\"sl-bt-rsp-sm-set-passkey-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-set-passkey-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_set_passkey_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x100f0020</pre><br><div>Definition at line <code>10667</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_increase_security_id<span id=\"sl-bt-rsp-sm-increase-security-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-increase-security-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_increase_security_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x040f0020</pre><br><div>Definition at line <code>10668</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_enter_passkey_id<span id=\"sl-bt-rsp-sm-enter-passkey-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-enter-passkey-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_enter_passkey_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x080f0020</pre><br><div>Definition at line <code>10669</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_passkey_confirm_id<span id=\"sl-bt-rsp-sm-passkey-confirm-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-passkey-confirm-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_passkey_confirm_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x090f0020</pre><br><div>Definition at line <code>10670</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_bonding_confirm_id<span id=\"sl-bt-rsp-sm-bonding-confirm-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-bonding-confirm-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_bonding_confirm_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0e0f0020</pre><br><div>Definition at line <code>10671</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_delete_bonding_id<span id=\"sl-bt-rsp-sm-delete-bonding-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-delete-bonding-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_delete_bonding_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x060f0020</pre><br><div>Definition at line <code>10672</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_delete_bondings_id<span id=\"sl-bt-rsp-sm-delete-bondings-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-delete-bondings-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_delete_bondings_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x070f0020</pre><br><div>Definition at line <code>10673</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_get_bonding_handles_id<span id=\"sl-bt-rsp-sm-get-bonding-handles-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-get-bonding-handles-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_get_bonding_handles_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x150f0020</pre><br><div>Definition at line <code>10674</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_get_bonding_details_id<span id=\"sl-bt-rsp-sm-get-bonding-details-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-get-bonding-details-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_get_bonding_details_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x160f0020</pre><br><div>Definition at line <code>10675</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_find_bonding_by_address_id<span id=\"sl-bt-rsp-sm-find-bonding-by-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-find-bonding-by-address-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_find_bonding_by_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x170f0020</pre><br><div>Definition at line <code>10676</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_resolve_rpa_id<span id=\"sl-bt-rsp-sm-resolve-rpa-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-resolve-rpa-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_resolve_rpa_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1d0f0020</pre><br><div>Definition at line <code>10677</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_set_bonding_key_id<span id=\"sl-bt-rsp-sm-set-bonding-key-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-set-bonding-key-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_set_bonding_key_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x180f0020</pre><br><div>Definition at line <code>10678</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_set_legacy_oob_id<span id=\"sl-bt-rsp-sm-set-legacy-oob-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-set-legacy-oob-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_set_legacy_oob_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x190f0020</pre><br><div>Definition at line <code>10679</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_set_oob_id<span id=\"sl-bt-rsp-sm-set-oob-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-set-oob-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_set_oob_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1a0f0020</pre><br><div>Definition at line <code>10680</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_set_remote_oob_id<span id=\"sl-bt-rsp-sm-set-remote-oob-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-set-remote-oob-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_set_remote_oob_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1b0f0020</pre><br><div>Definition at line <code>10681</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sm_set_bonding_data_id<span id=\"sl-bt-rsp-sm-set-bonding-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sm-set-bonding-data-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sm_set_bonding_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x1c0f0020</pre><br><div>Definition at line <code>10682</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm", "status": "success"}