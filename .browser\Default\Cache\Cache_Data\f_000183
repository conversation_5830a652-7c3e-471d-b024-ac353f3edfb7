"use strict";(globalThis["webpackChunkpintool"]=globalThis["webpackChunkpintool"]||[]).push([[852],{2852:(e,t,n)=>{n.r(t),n.d(t,{default:()=>st});var o=n(9835),a=n(499),i=n(1957),r=n(1446),l=(n(702),n(3725)),s=n(3746),c=n(1569),p=n(6016);const u=function(e,t){return!(!e||!t)&&e.toUpperCase()===t.toUpperCase()},d=function(e,t){try{let n=document.createElement("a",{id:"a"});n.href=e,n.download=t,n.click(),n.remove()}catch(n){console.error(n)}},g=function(e){return void 0===e||null===e||0===e.length};function f(e){const t=e.CUSTOMNAME&&e.CUSTOMNAME.length>0?e.CUSTOMNAME:void 0;let n="GPIO mode"!==e.FUNCTION?e.FUNCTION:void 0;"string"==typeof n&&0==n.length&&(n=void 0);const o="GPIO mode"==e.FUNCTION?e.FUNCTION:void 0;return n&&t?t+" | "+n:n||(t||(o?"GPIO":""))}const m={DisabledBackgroundColor:"white",UnusedBackgroundColor:"white",FuncAssignedBackgroundColor:"#70B8FF",WarningBackgroundColor:"#FFFF80",ErrorBackgroundColor:"#FF4040"};function h(e,t,n,o){return e?t?m.ErrorBackgroundColor:n?m.WarningBackgroundColor:o&&o.FUNCTION&&o.FUNCTION.length>0?m.FuncAssignedBackgroundColor:m.UnusedBackgroundColor:m.DisabledBackgroundColor}async function w(e,t){return e.then((e=>e)).catch((e=>(console.error("Pin data store",e),t?e:null)))}const b=(0,s.Q_)("pin",{state:()=>({projectUniqueId:"",pinTableHeader:l.Z.PIN_TABLE_HEADER,pinTable:[],pinSelectedRows:[],pinWarnings:[],pinErrors:[],pinKey:"NAME",pinTableLoading:!1,pinSort:{},functionTableHeader:l.Z.FUNCTION_TABLE_HEADER,functionTable:[],functionSelectedRows:[],functionWarnings:[],functionErrors:[],functionKey:"FUNCTION",functionTableLoading:!1,functionSort:{},peripheralTableHeader:l.Z.PERIPHERAL_TABLE_HEADER,peripheralTable:[],peripheralSelectedRows:[],peripheralWarnings:[],peripheralErrors:[],peripheralKey:"NAME",peripheralTableLoading:!1,peripheralSort:{},recentlyChangedValue:"",specificItem:{tab:"",rowData:""}}),actions:{updateSort(e,t){u(t,l.Z.PIN_TABLE)?Object.assign(this.pinSort,e):u(t,l.Z.FUNCTION_TABLE)?Object.assign(this.functionSort,e):u(t,l.Z.PERIPHERAL_TABLE)&&Object.assign(this.peripheralSort,e)},updateSelectionState(e,t){switch(e){case l.Z.PIN:this.pinSelectedRows.splice(0),this.pinSelectedRows.push(...t);break;case l.Z.FUNCTION:this.functionSelectedRows.splice(0),this.functionSelectedRows.push(...t);break;case l.Z.PERIPHERAL:this.peripheralSelectedRows.splice(0),this.peripheralSelectedRows.push(...t);break;default:break}},async updatePinTable(){this.pinTableLoading=!0;let e=await w(c.api.post(`/pintool/pin-info/${this.projectUniqueId}`,this.pinSort));this.pinTable.splice(0),e&&this.pinTable.push(...e),this.pinTableLoading=!1},async updateFunctionTable(){this.functionTableLoading=!0;let e=await w(c.api.post(`/pintool/function-info/${this.projectUniqueId}`,this.functionSort));this.functionTable.splice(0),e&&this.functionTable.push(...e),this.functionTableLoading=!1},async updatePeripheralTable(){this.peripheralTableLoading=!0;let e=await w(c.api.post(`/pintool/peripheral-info/${this.projectUniqueId}`,this.peripheralSort));this.peripheralTable.splice(0),e&&this.peripheralTable.push(...e),this.peripheralTableLoading=!1},async updateAllTables(){return Promise.all([this.updatePinTable(),this.updateFunctionTable(),this.updatePeripheralTable()])},async fetchPossibleSoftwareComponentsAndFunctionsForPin(e){const t=`/pintool/pin-edit/${this.projectUniqueId}?getPossibleSoftwareComponents=${e}&getPossibleFunctions=${e}`;return await w(c.api.get(t))},async fetchPossiblePinsAndSoftwareComponentsForFunction(e){const t=`/pintool/function-edit/${this.projectUniqueId}?getPossiblePins=${e}&getPossibleSoftwareComponents=${e}`;return await w(c.api.get(t))},async fetchPossibleSoftwareComponentsForPeripheral(e){const t=`/pintool/peripheral-edit/${this.projectUniqueId}?getPossibleSoftwareComponents=${e}`;return await w(c.api.get(t))},async fetchProblemMarkers(){const e=`/pintool/problem-marker/${this.projectUniqueId}?getProblemMarkers=ALL`,t=await w(c.api.get(e));this.loadProblemMarkers(t)},async submitEditedData(e,t){const n=`/pintool/${e}-edit/${this.projectUniqueId}`;return await w(c.api.post(n,t))},async selectionChanged(e,t=[],n=[],o=!0){const a=`/pintool/selection-${e}s/${this.projectUniqueId}`;e!==l.Z.PIN&&(t=t&&0!=t.length?t[0]:"",n=n&&0!=n.length?n[0]:"");const i={[`selected${e}`]:t,[`unselected${e}`]:n,clearAll:o};return await w(c.api.post(a,i))},async openComponentEditor(){const e=`/pintool/edit-component/${this.projectUniqueId}`;return await w(c.api.post(e))},async openComponentSelector(){const e=`/pintool/new-component/${this.projectUniqueId}`;return await w(c.api.post(e))},setProjectUniqueId(e){this.projectUniqueId=e},pinSocketOnError(e){window.console.log(e)},pinSocketOnMessage(e){const t=JSON.parse(e.data);if(u(t.project,this.projectUniqueId))switch(t.msgType){case"PinSelectionsChanged":const e=t.SelectedPins.map((e=>this.pinTable.find((t=>u(t.NAME,e)))));this.pinSelectedRows.splice(0),this.pinSelectedRows.push(...e);break;case"FunctionSelectionsChanged":const n=t.SelectedFunctions.map((e=>this.functionTable.find((t=>u(t.FUNCTION,e)))));this.functionSelectedRows.splice(0),this.functionSelectedRows.push(...n);break;case"PeripheralSelectionsChanged":const o=t.SelectedPeripherals.map((e=>this.peripheralTable.find((t=>u(t.NAME,e)))));this.peripheralSelectedRows.splice(0),this.peripheralSelectedRows.push(...o);break;case"CurrentProblemMarkers":this.loadProblemMarkers(t);break;case"ComponentRemoved":this.updateAllTables();break;case"ModelUpdated":switch(t.type){case"ABPeripheral":this.updatePeripheralTable().then((()=>this.recentlyChangedValue=t.name));break;case"ABPin":Promise.all([this.updateFunctionTable(),this.updatePinTable()]).then((()=>this.recentlyChangedValue=t.name));break;default:this.updateAllTables().then((()=>this.recentlyChangedValue=t.name));break}break;case"ShowSpecificItem":this.specificItem={tab:t.tab,rowData:t.rowData};break;default:break}},loadProblemMarkers(e){this.pinWarnings.splice(0),this.pinErrors.splice(0),this.functionWarnings.splice(0),this.functionErrors.splice(0),this.peripheralWarnings.splice(0),this.peripheralErrors.splice(0),Object.entries(e.PINMARKERS).forEach((e=>{const[t,n]=e;u(n,"SEVERITY_WARNING")&&this.pinWarnings.push(t),u(n,"SEVERITY_ERROR")&&this.pinErrors.push(t)})),Object.entries(e.FUNCTIONMARKERS).forEach((e=>{const[t,n]=e;u(n,"SEVERITY_WARNING")&&this.functionWarnings.push(t),u(n,"SEVERITY_ERROR")&&this.functionErrors.push(t)})),Object.entries(e.PERIPHERALMARKERS).forEach((e=>{const[t,n]=e;u(n,"SEVERITY_WARNING")&&this.peripheralWarnings.push(t),u(n,"SEVERITY_ERROR")&&this.peripheralErrors.push(t)}))},createSaveStatusSocket(){const e=new p.sh(`ws://${window.location.host}/ws/pintool/server/notifications/project/${this.projectUniqueId}`,this,"pinSocket");return e.init(),e}}});var P=n(8339),v=n(9448),N=(n(6727),n(6204),n(7924),n(6970));const S={class:"text-h6"},y={key:0},k={key:1},T={key:2},E={key:0},C={class:"text-body2"},I={key:1},U={class:"text-body2"},O={key:0,class:"text-body2"},W={key:1,class:"text-body2"},A={key:2,class:"text-body2"},_={key:3,class:"text-body2"},D={key:0},R={key:1},F={__name:"edit-dialog",props:{modelValue:{type:Object,default:()=>{}},open:{type:Boolean,default:!1},type:{type:String,default:""}},emits:["change","close"],setup(e,{emit:t}){const n=e,r=(0,a.iH)([]),c=(0,a.iH)([]),p=(0,a.iH)([]),d=b(),f=(0,o.Fl)({get(){return n.modelValue},set(e){t("change",e)}}),m=(0,o.Fl)({get(){return n.open&&$(),n.open},set(e){t("close",e)}}),{recentlyChangedValue:h}=(0,s.Jk)(d);(0,o.YP)(h,(e=>{e&&""!==e&&m.value&&B(),h.value=""}));const w=(0,o.Fl)((()=>n.type&&u(n.type,l.Z.PIN))),P=(0,o.Fl)((()=>n.type&&u(n.type,l.Z.FUNCTION))),v=(0,o.Fl)((()=>n.type&&u(n.type,l.Z.PERIPHERAL))),F=(0,o.Fl)((()=>{let e=!1,t=f.value.CUSTOMNAME,o=u(n.type,l.Z.FUNCTION)?f.value.PINNAME:f.value.NAME;return u(n.type,l.Z.PIN)||u(n.type,l.Z.FUNCTION)?d.pinTable.find((n=>{g(t)||!u(n.CUSTOMNAME,t)||u(n.NAME,o)||(e=!0)})):u(n.type,l.Z.PERIPHERAL)&&d.peripheralTable.find((n=>{g(t)||!u(n.CUSTOMNAME,t)||u(n.NAME,f.value.NAME)||(e=!0)})),e}));function M(){return w.value&&!f.value.FUNCTION}function j(){return P.value&&"Disabled"===f.value.PINNAME}function q(){return!p.value||p.value&&0===p.value.length}async function B(){let e;if(w.value){const t=f.value.NAME;e=d.pinTable.find((e=>u(t,e.NAME))),e&&(f.value.FUNCTION=e.FUNCTION)}else if(P.value){const t=f.value.FUNCTION;e=d.functionTable.find((e=>u(t,e.FUNCTION))),e&&(f.value.PINNAME=e.PINNAME)}else if(v.value){const t=f.value.NAME;e=d.peripheralTable.find((e=>u(t,e.NAME)))}e&&(f.value.CUSTOMNAME=e.CUSTOMNAME,f.value.SOFTWARECOMPONENT=e.SOFTWARECOMPONENT)}let x="";async function Z(){const e=await d.fetchPossibleSoftwareComponentsAndFunctionsForPin(f.value.NAME);e.PossibleFunctions.length>0&&(c.value=e.PossibleFunctions[0][f.value.NAME]),e.PossibleSoftwareComponents.length>0&&(p.value=e.PossibleSoftwareComponents[0][f.value.NAME]),x=f.value.SOFTWARECOMPONENT}async function H(){const e=await d.fetchPossiblePinsAndSoftwareComponentsForFunction(f.value.FUNCTION);e.PossiblePins&&e.PossiblePins.length>0&&(r.value=e.PossiblePins[0][f.value.FUNCTION]),e.PossibleSoftwareComponents&&e.PossibleSoftwareComponents.length>0&&(p.value=e.PossibleSoftwareComponents[0][f.value.FUNCTION]),x=f.value.SOFTWARECOMPONENT}async function L(){let e=await d.fetchPossibleSoftwareComponentsForPeripheral(f.value.NAME);e.PossibleSoftwareComponents&&e.PossibleSoftwareComponents.length>0&&(p.value=e.PossibleSoftwareComponents[0][f.value.NAME]),x=f.value.SOFTWARECOMPONENT}async function $(){w.value?Z():P.value?H():v.value&&L()}async function V(){let e=await d.fetchPossiblePinsAndSoftwareComponentsForFunction(f.value.FUNCTION);e.PossibleSoftwareComponents&&e.PossibleSoftwareComponents.length>0&&e.PossibleSoftwareComponents[0][f.value.FUNCTION].length>0?(p.value=e.PossibleSoftwareComponents[0][f.value.FUNCTION],f.value.SOFTWARECOMPONENT=p.value[0]):(p.value=[],f.value.SOFTWARECOMPONENT=null)}async function z(){f.value.SELECTED=!0,w.value&&!f.value.FUNCTION&&(f.value.FUNCTION=""),f.value.CUSTOMNAME||(f.value.CUSTOMNAME=""),f.value.SOFTWARECOMPONENT||(f.value.SOFTWARECOMPONENT=""),await d.submitEditedData(n.type,f.value).then((e=>{if(e.PINTOOLMODELISCHANGED&&($(),d.updateAllTables(),w.value)){const e=f.value.NAME;f.value=d.pinTable.find((t=>u(e,t.NAME)))}})).catch((e=>{console.error(e)}))}function Q(){f.value.FUNCTION=null,f.value.SOFTWARECOMPONENT=null}function G(){f.value.PINNAME="Disabled",f.value.CUSTOMNAME="",f.value.SOFTWARECOMPONENT=null}async function K(){await d.openComponentEditor()}async function Y(){await d.openComponentSelector()}function J(){return!x||u("",x)||!f.value.SOFTWARECOMPONENT||u("",f.value.SOFTWARECOMPONENT)}return(e,t)=>{const n=(0,o.up)("q-card-section"),l=(0,o.up)("q-select"),s=(0,o.up)("q-input"),u=(0,o.up)("q-tooltip"),d=(0,o.up)("q-btn"),g=(0,o.up)("q-card-actions"),h=(0,o.up)("q-card"),b=(0,o.up)("q-dialog"),B=(0,o.Q2)("close-popup");return(0,o.wg)(),(0,o.j4)(b,{modelValue:(0,a.SU)(m),"onUpdate:modelValue":t[8]||(t[8]=e=>(0,a.dq)(m)?m.value=e:null),onKeydown:t[9]||(t[9]=(0,i.D2)((()=>{m.value=!1,z()}),["enter"]))},{default:(0,o.w5)((()=>[(0,o.Wm)(h,{class:"dialog"},{default:(0,o.w5)((()=>[(0,o.Wm)(n,{class:"card-header q-mb-md"},{default:(0,o.w5)((()=>[(0,o._)("div",S,[(0,a.SU)(w)?((0,o.wg)(),(0,o.iD)("span",y,(0,N.zw)(e.$t("edit"))+" "+(0,N.zw)(e.$t("pin"))+": "+(0,N.zw)((0,a.SU)(f).NAME)+" (#"+(0,N.zw)((0,a.SU)(f).NUMBER)+")",1)):(0,o.kq)("",!0),(0,a.SU)(P)?((0,o.wg)(),(0,o.iD)("span",k,(0,N.zw)(e.$t("edit"))+" "+(0,N.zw)(e.$t("function"))+": "+(0,N.zw)((0,a.SU)(f).FUNCTION),1)):(0,o.kq)("",!0),(0,a.SU)(v)?((0,o.wg)(),(0,o.iD)("span",T,(0,N.zw)(e.$t("edit"))+" "+(0,N.zw)(e.$t("peripheral"))+": "+(0,N.zw)((0,a.SU)(f).NAME),1)):(0,o.kq)("",!0)])])),_:1}),(0,o.Wm)(n,{class:"q-pt-none"},{default:(0,o.w5)((()=>[(0,a.SU)(w)?((0,o.wg)(),(0,o.iD)("div",E,[(0,o._)("span",C,(0,N.zw)(e.$t("function"))+":",1),(0,o.Wm)(l,{for:"function-select",class:"q-mb-md",outlined:"",dense:"",clearable:"",modelValue:(0,a.SU)(f).FUNCTION,"onUpdate:modelValue":t[0]||(t[0]=e=>(0,a.SU)(f).FUNCTION=e),onClear:Q,onPopupHide:V,options:c.value},null,8,["modelValue","options"])])):(0,o.kq)("",!0),(0,a.SU)(P)?((0,o.wg)(),(0,o.iD)("div",I,[(0,o._)("span",U,(0,N.zw)(e.$t("pin"))+":",1),(0,o.Wm)(l,{for:"pin-select",class:"q-mb-md",outlined:"",dense:"",clearable:"",onClear:G,onPopupHide:V,modelValue:(0,a.SU)(f).PINNAME,"onUpdate:modelValue":t[1]||(t[1]=e=>(0,a.SU)(f).PINNAME=e),options:r.value},null,8,["modelValue","options"])])):(0,o.kq)("",!0),(0,o._)("div",null,[(0,a.SU)(v)?((0,o.wg)(),(0,o.iD)("span",O,(0,N.zw)(e.$t("custom"))+" "+(0,N.zw)(e.$t("peripheral"))+" "+(0,N.zw)(e.$t("name"))+":",1)):((0,o.wg)(),(0,o.iD)("span",W,(0,N.zw)(e.$t("custom"))+" "+(0,N.zw)(e.$t("pin"))+" "+(0,N.zw)(e.$t("name"))+":",1)),(0,o.Wm)(s,{for:"custom-name-input",class:"q-mb-md",outlined:"",dense:"",clearable:"",modelValue:(0,a.SU)(f).CUSTOMNAME,"onUpdate:modelValue":t[2]||(t[2]=e=>(0,a.SU)(f).CUSTOMNAME=e),"error-message":"Already in use.",error:(0,a.SU)(F),disable:j()},null,8,["modelValue","error","disable"])]),(0,a.SU)(v)?((0,o.wg)(),(0,o.iD)("span",A,(0,N.zw)(e.$t("selectSWComponent"))+" "+(0,N.zw)(e.$t("peripheral")),1)):((0,o.wg)(),(0,o.iD)("span",_,(0,N.zw)(e.$t("selectSWComponent"))+" "+(0,N.zw)(e.$t("function")),1)),(0,o._)("div",null,[(0,o.Wm)(l,{for:"component-select",outlined:"",dense:"",clearable:"",modelValue:(0,a.SU)(f).SOFTWARECOMPONENT,"onUpdate:modelValue":t[5]||(t[5]=e=>(0,a.SU)(f).SOFTWARECOMPONENT=e),options:p.value},{selected:(0,o.w5)((()=>[(0,a.SU)(f).SOFTWARECOMPONENT?((0,o.wg)(),(0,o.iD)(o.HY,{key:0},[(0,o.Uk)((0,N.zw)((0,a.SU)(f).SOFTWARECOMPONENT),1)],64)):((0,o.wg)(),(0,o.iD)(o.HY,{key:1},[M()?((0,o.wg)(),(0,o.iD)("div",D,[(0,o._)("span",null,(0,N.zw)(e.$t("selectFunctionFirst")),1),(0,o.Wm)(u,null,{default:(0,o.w5)((()=>[(0,o.Uk)((0,N.zw)(e.$t("selectFunctionFirstDesc")),1)])),_:1})])):q()?((0,o.wg)(),(0,o.iD)("div",R,[(0,o._)("span",null,(0,N.zw)(e.$t("noCompatibleSwComp")),1),(0,o.Wm)(u,null,{default:(0,o.w5)((()=>[(0,o.Uk)((0,N.zw)(e.$t("noCompatibleSwCompDesc")),1)])),_:1})])):(0,o.kq)("",!0)],64))])),after:(0,o.w5)((()=>[(0,o.Wm)(d,{disable:J(),onClick:t[3]||(t[3]=e=>K()),label:e.$t("edit"),primary:""},{default:(0,o.w5)((()=>[(0,o.Wm)(u,null,{default:(0,o.w5)((()=>[(0,o.Uk)((0,N.zw)(e.$t("editTooltip")),1)])),_:1})])),_:1},8,["disable","label"]),(0,o.Wm)(d,{onClick:t[4]||(t[4]=e=>Y()),label:e.$t("new")},{default:(0,o.w5)((()=>[(0,o.Wm)(u,null,{default:(0,o.w5)((()=>[(0,o.Uk)((0,N.zw)(e.$t("addTooltip")),1)])),_:1})])),_:1},8,["label"])])),_:1},8,["modelValue","options"])])])),_:1}),(0,o.Wm)(g,{align:"right",class:"text-primary"},{default:(0,o.w5)((()=>[(0,o.Wm)(d,{label:e.$t("apply"),onClick:t[6]||(t[6]=e=>z())},null,8,["label"]),(0,o.wy)((0,o.Wm)(d,{label:e.$t("applyAndClose"),onClick:t[7]||(t[7]=e=>z())},null,8,["label"]),[[B]]),(0,o.wy)((0,o.Wm)(d,{label:e.$t("cancel")},null,8,["label"]),[[B]])])),_:1})])),_:1})])),_:1},8,["modelValue"])}}};var M=n(1639),j=n(3706),q=n(4458),B=n(3190),x=n(2913),Z=n(6611),H=n(6858),L=n(4455),$=n(1821),V=n(2146),z=n(9984),Q=n.n(z);const G=(0,M.Z)(F,[["__scopeId","data-v-11b9ac4a"]]),K=G;Q()(F,"components",{QDialog:j.Z,QCard:q.Z,QCardSection:B.Z,QSelect:x.Z,QInput:Z.Z,QTooltip:H.Z,QBtn:L.Z,QCardActions:$.Z}),Q()(F,"directives",{ClosePopup:V.Z});const Y={__name:"pin-tool-table",props:{id:{type:String,default:""},header:{type:Array,default:()=>[]},type:{type:String,default:"pin"},data:{type:Array,default:()=>[]},selectedRows:{type:Array,default:()=>[]},rowErrors:{type:Array,default:()=>[]},rowWarnings:{type:Array,default:()=>[]},rowKey:{type:String,default:""},loading:{type:Boolean,default:!1},filter:{type:String,default:""},sort:{type:Object,default:()=>{}}},setup(e,{expose:t}){const n=e;t({scrollToRow:T});const i=(0,a.iH)({tableColumns:n.header,tableRows:n.data,sort:n.sort}),r=b(),s=(0,a.iH)(),c=(0,a.iH)(!1),p=((0,a.iH)({}),(0,o.Fl)((()=>u(n.sort["sort-order"],"DESCENDING")))),d=(0,o.Fl)({get(){return n.selectedRows},set(e){r.updateSelectionState(n.type,e)}});function g(e){var t,o;return null!==(t=n.sort)&&void 0!==t&&t.sort||n.rowKey!==e?(null===(o=n.sort)||void 0===o?void 0:o.sort)==e:(r.updateSort({"sort-order":"ASCENDING",sort:e},n.id),!0)}function f(){return u(l.Z.PIN,n.type)}function m(){return f()?"multiple":"single"}function h(e){return n.rowErrors.includes(e)?"bg-red-5 text-white":n.rowWarnings.includes(e)?"bg-yellow-8 text-white":void 0}function w(e,t,a){let i=[];f()||(i=d.value.map((e=>e[n.rowKey]))),(0,o.Y3)((async()=>{a&&(v(),d.value.push(t)),await r.selectionChanged(n.type,d.value.map((e=>e[n.rowKey])),i)}))}async function P(e){let t={};n.sort.sort&&n.sort.sort==e?t["sort-order"]=u(n.sort["sort-order"],"ASCENDING")?"DESCENDING":"ASCENDING":t["sort-order"]="ASCENDING",t.sort=e,r.updateSort(t,n.id),u(n.id,l.Z.PIN_TABLE)?await r.updatePinTable():u(n.id,l.Z.FUNCTION_TABLE)?await r.updateFunctionTable():u(n.id,l.Z.PERIPHERAL_TABLE)&&await r.updatePeripheralTable()}function v(){d.value.splice(0)}function S(e){c.value=e,e||(w(null,d.value[0]),v())}async function y(e,t){w(e,t,!0),s.value=structuredClone(t),S(!0)}const k=(0,a.iH)();function T(e){if(!e||n.type!=e.tab&&e.rowData)return;const t=e.rowData.toLowerCase(),o=k.value,a=o.rows;let i="";"pin"===e.tab?i="NAME":"function"===e.tab?i="FUNCTION":"peripheral"===e.tab&&(i="PERIPHERAL");for(let n=0;n<a.length;n++)if(a[n][i].toLowerCase()===t){E(o,n);break}}async function E(e,t){await(0,o.Y3)(),e.scrollTo(t,"center")}return(e,t)=>{const r=(0,o.up)("q-icon"),l=(0,o.up)("q-th"),u=(0,o.up)("q-checkbox"),f=(0,o.up)("q-td"),b=(0,o.up)("q-table");return(0,o.wg)(),(0,o.iD)(o.HY,null,[(0,o.Wm)(b,{ref_key:"myTable",ref:k,class:"pin-tool-table","virtual-scroll":"",separator:"cell",dense:"",rows:i.value.tableRows,columns:i.value.tableColumns,loading:n.loading,filter:n.filter,"rows-per-page-options":[0],"row-key":n.rowKey,selection:m(),selected:(0,a.SU)(d),"onUpdate:selected":t[0]||(t[0]=e=>(0,a.dq)(d)?d.value=e:null),onSelection:w,onRowClick:y},{"header-cell":(0,o.w5)((e=>[(0,o.Wm)(l,{id:e.col.name,props:e,onClick:t=>P(e.col.field),class:"cursor-pointer"},{default:(0,o.w5)((()=>{var t;return[(0,o.Uk)((0,N.zw)(e.col.label)+" ",1),g(e.col.field)?((0,o.wg)(),(0,o.j4)(r,{key:0,name:"arrow_upward",class:(0,N.C_)(["arrowIcon",(null===(t=n.sort)||void 0===t?void 0:t.sort)==e.col.field&&(0,a.SU)(p)?"arrowRotate":""])},null,8,["class"])):(0,o.kq)("",!0)]})),_:2},1032,["id","props","onClick"])])),"header-selection":(0,o.w5)((e=>[(0,o.Wm)(u,{modelValue:e.selected,"onUpdate:modelValue":t=>e.selected=t},null,8,["modelValue","onUpdate:modelValue"])])),"body-selection":(0,o.w5)((e=>[(0,o.Wm)(u,{"model-value":e.selected,"onUpdate:modelValue":(t,n)=>{Object.getOwnPropertyDescriptor(e,"selected").set(t,n)}},null,8,["model-value","onUpdate:modelValue"])])),"body-cell":(0,o.w5)((e=>[(0,o.Wm)(f,{props:e,class:(0,N.C_)(h(e.value))},{default:(0,o.w5)((()=>[(0,o.Uk)((0,N.zw)(e.value),1)])),_:2},1032,["props","class"])])),bottom:(0,o.w5)((()=>[(0,o._)("div",null,(0,N.zw)(i.value.tableRows.length)+" Items",1)])),_:1},8,["rows","columns","loading","filter","row-key","selection","selected"]),c.value?((0,o.wg)(),(0,o.j4)(K,{key:0,"model-value":s.value,open:c.value,type:n.type,onClose:t[1]||(t[1]=e=>S(!1))},null,8,["model-value","open","type"])):(0,o.kq)("",!0)],64)}}};var J=n(1746),X=n(1682),ee=n(2857),te=n(1006),ne=n(7220);const oe=(0,M.Z)(Y,[["__scopeId","data-v-17c678dc"]]),ae=oe;Q()(Y,"components",{QTable:J.Z,QTh:X.Z,QIcon:ee.Z,QCheckbox:te.Z,QTd:ne.Z});n(8367);var ie=n(9981),re=n.n(ie);const le={"Content-Type":"application/json",Accept:"application/json"},se={get(e,t){return re()({method:"get",url:e,params:t,headers:le})},put(e,t,n){return re()({method:"put",url:e,params:n,data:t,headers:le})},post(e,t,n){return re()({method:"post",url:e,data:t,params:n,headers:le})}},ce=(0,s.Q_)("diagram",{state:()=>({uniqProjectId:"",diagramData:{packageConfigData:{},pinsConfigData:[],packageType:"",configStage:{}},stageWidth:0,stageHeight:0,scaleFactor:1,initialScaleFactor:-1}),actions:{setProjectId(e){this.uniqProjectId=e},getDiagramData(){const e=`/rest/pintool/diagram-base-info/${this.uniqProjectId}`;return new Promise(((t,n)=>se.get(e).then((e=>{this.diagramData=e.data,t(e)})).catch((e=>{n(e)}))))},async getProjectName(){const e=`/rest/pintool/project-details/${this.uniqProjectId}?getProjectName`;let t=await se.get(e);return t.data.ProjectName},async ConfigReportServlet(e){let t=await this.getProjectName();const n=`/rest/pintool/config-report/${this.uniqProjectId}`;let o=await se.post(n,{"report-type":e,"file-name":t});d("data:text/html;utf-8, "+o.data,t+"_"+e)}}}),pe=(0,o.Uk)("Save Diagram As Picture"),ue=(0,o.Uk)("Module Configuration Report"),de=(0,o.Uk)("Pin Configuration Report"),ge={__name:"pop-up-menu",props:{stage:{default:null,type:Object}},setup(e){const t=e,n=ce();function a(){const e=t.stage.getStage().toDataURL({pixelRatio:1});d(e,"stage.png")}function i(){n.ConfigReportServlet("moduleconfigreport")}async function r(){n.ConfigReportServlet("pinconfigreport")}return(e,t)=>{const n=(0,o.up)("q-item-section"),l=(0,o.up)("q-item"),s=(0,o.up)("q-separator"),c=(0,o.up)("q-list"),p=(0,o.Q2)("close-popup");return(0,o.wg)(),(0,o.j4)(c,{dense:"",style:{"min-width":"200px"}},{default:(0,o.w5)((()=>[(0,o.wy)(((0,o.wg)(),(0,o.j4)(l,{clickable:""},{default:(0,o.w5)((()=>[(0,o.Wm)(n,{name:"saveDiagram",id:"save-diagram",onClick:a},{default:(0,o.w5)((()=>[pe])),_:1})])),_:1})),[[p]]),(0,o.Wm)(s),(0,o.wy)(((0,o.wg)(),(0,o.j4)(l,{clickable:""},{default:(0,o.w5)((()=>[(0,o.Wm)(n,{name:"saveModuleReport",id:"save-module-report",onClick:i},{default:(0,o.w5)((()=>[ue])),_:1})])),_:1})),[[p]]),(0,o.wy)(((0,o.wg)(),(0,o.j4)(l,{clickable:""},{default:(0,o.w5)((()=>[(0,o.Wm)(n,{name:"savePinReport",id:"save-pin-report",onClick:r},{default:(0,o.w5)((()=>[de])),_:1})])),_:1})),[[p]])])),_:1})}}};var fe=n(3246),me=n(490),he=n(1233),we=n(926);const be=ge,Pe=be;Q()(ge,"components",{QList:fe.Z,QItem:me.Z,QItemSection:he.Z,QSeparator:we.Z}),Q()(ge,"directives",{ClosePopup:V.Z});var ve=n(9481);const Ne={style:{height:"100%"}},Se={class:"buttonbar"},ye=(0,o.Uk)("Rotate diagram left"),ke=(0,o.Uk)("Rotate diagram right"),Te=(0,o.Uk)("Zoom out"),Ee=(0,o.Uk)("Reset Zoom"),Ce=(0,o.Uk)("Zoom in"),Ie=(0,o.Uk)(" >"),Ue={__name:"DiagramTemplate",props:["packageType","configStage"],setup(e){const t=e,n=(0,r.Z)(),l=ce(),c=b(),p=(0,a.iH)(null),u=(0,a.iH)(null),{scaleFactor:d,performZoomIn:g,performZoomOut:m,initialScaleFactor:h}=(0,s.Jk)(l),w={width:50,height:24,drawBorder:!0,fill:"#F5E050",visible:!1},P={text:"",fontFamily:"Calibri",fontSize:14,padding:5,visible:!1,fill:"black"};function v(e){const t=u.value.getNode().getPointerPosition(),n=u.value.getNode().scaleX(),o=Math.floor(t.x/n),a=Math.floor(t.y/n);let i=y(e.target),r=p.value.getNode();r.position({x:o+5,y:a+5});const l=r.findOne("#tiptext");l.text(i),l.width("auto");const s=l.width(),c=r.findOne(".tipRect");c.width(s),A(u.value.getNode(),r);let d=r.getChildren();d.forEach((e=>e.visible(!0)))}function S(e){let t=p.value.getNode(),n=t.getChildren();n.forEach((e=>e.visible(!1)))}function y(e){let t=e.findAncestor(".pinGroup"),n=t.getAttr("id"),o=n,a=c.pinTable.find((e=>e.NAME===n));if(a){const e=f(a);e.length>0&&(o+=`: ${e}`)}return o}const k=(0,a.iH)(null);(0,o.bv)((()=>{l.stageWidth=t.configStage.width,l.stageHeight=t.configStage.height}));let T=!1;function E(){if(T)return;const e=k.value.clientWidth,n=k.value.clientHeight;if("undefined"===typeof e||e<=0)return;if("undefined"===typeof n||n<=0)return;const o=e/t.configStage.width,a=n/t.configStage.height,i=o<a?o:a;"function"===typeof u.value.getNode&&(F(i),T=!0)}const C=[.25,.33,.4,.5,.57,.67,.8,1,1.25,1.5,1.75,2,2.5,3,4];function I(){O(-90)}function U(){O(90)}function O(e){const t=u.value.getNode(),n=t.width()*d.value,o=t.height()*d.value;t.x(n/2),t.y(o/2);const a=(t.rotation()+e)%360;t.rotation(a),W(t)}function W(e){const t=l.stageWidth*d.value,n=l.stageHeight*d.value;let o=e.rotation();switch(o){case 0:e.x(0),e.y(0),e.width(t),e.height(n);break;case 90:case-270:e.x(n),e.y(0),e.width(n),e.height(t);break;case 180:case-180:e.x(t),e.y(n),e.width(t),e.height(n);break;case 270:case-90:e.x(0),e.y(t),e.width(n),e.height(t);break}}function A(e,t){let n=t.x(),o=t.y(),a=l.stageWidth,i=l.stageHeight,r=e.rotation();switch(r){case 0:t.x(n),t.y(o),t.rotation(0);break;case 90:case-270:t.x(o),t.y(i-n),t.rotation(-90);break;case 180:case-180:t.x(a-n),t.y(i-o),t.rotation(180);break;case 270:case-90:t.x(a-o),t.y(n),t.rotation(90);break}}function _(){let e=h.value>0?h.value:1;M(e,(()=>e))}function D(){M(C[C.length-1],(()=>{let e=u.value.getNode().scaleX();return C.find((t=>t>e))}))}function R(){M(C[0],(()=>{let e=u.value.getNode().scaleX();return C.findLast((t=>t<e))}))}function F(e){e=e<C[0]?C[0]:C.findLast((t=>t<=e)),h.value<0&&(h.value=e),M(C[0],(()=>C.findLast((t=>t<=e))))}function M(e,t){const n=u.value.getNode();if(n.scaleX()==e)return;let o=t();l.scaleFactor=o}const j=(0,o.Fl)((()=>{if(null===u.value)return"100%";const e=l.scaleFactor;let t=Math.floor(100*e);return t+"%"}));function q(e){e.evt.ctrlKey&&(e.evt.wheelDelta>0?D():e.evt.wheelDelta<0&&R(),e.evt.cancelBubble=!0)}(0,o.YP)(d,(()=>{const e=l.scaleFactor,t=u.value.getNode();t.scale({x:e,y:e}),W(t)}));const{pinSelectedRows:B}=(0,s.Jk)(c);async function x(e){const t=[],n=[];await c.selectionChanged("pin",t,n)}function Z(e){const t=u.value.getNode();H(JSON.stringify(t.getChildren()[1]))}function H(e){n.localStorage.set("chip_state",JSON.parse(e))}return(t,n)=>{const r=(0,o.up)("q-btn"),l=(0,o.up)("q-tooltip"),s=(0,o.up)("q-menu"),c=(0,o.up)("q-resize-observer"),d=(0,o.up)("v-group"),g=(0,o.up)("v-layer"),f=(0,o.up)("v-rect"),m=(0,o.up)("v-text"),h=(0,o.up)("v-stage");return(0,o.wg)(),(0,o.iD)("div",Ne,[(0,o._)("div",Se,[(0,o.wy)((0,o.Wm)(r,{id:"generate-json-state",onClick:Z},null,512),[[i.F8,!1]]),(0,o.Wm)(r,{id:"rotate-left",onClick:n[0]||(n[0]=e=>I()),icon:(0,a.SU)(ve.ocz),flat:"",round:"",dense:"",color:"primary"},{default:(0,o.w5)((()=>[(0,o.Wm)(l,null,{default:(0,o.w5)((()=>[ye])),_:1})])),_:1},8,["icon"]),(0,o.Wm)(r,{id:"rotate-right",onClick:n[1]||(n[1]=e=>U()),icon:(0,a.SU)(ve.jeY),flat:"",round:"",dense:"",color:"primary"},{default:(0,o.w5)((()=>[(0,o.Wm)(l,null,{default:(0,o.w5)((()=>[ke])),_:1})])),_:1},8,["icon"]),(0,o.Wm)(r,{id:"zoom-out",onClick:n[2]||(n[2]=e=>R()),icon:(0,a.SU)(ve.ElP),flat:"",round:"",dense:"",color:"primary"},{default:(0,o.w5)((()=>[(0,o.Wm)(l,null,{default:(0,o.w5)((()=>[Te])),_:1})])),_:1},8,["icon"]),(0,o.Wm)(r,{id:"zoom-default",onClick:n[3]||(n[3]=e=>_()),flat:"",round:"",dense:"",color:"primary"},{default:(0,o.w5)((()=>[(0,o.Uk)((0,N.zw)((0,a.SU)(j)),1),(0,o.Wm)(l,null,{default:(0,o.w5)((()=>[Ee])),_:1})])),_:1}),(0,o.Wm)(r,{id:"zoom-in",onClick:n[4]||(n[4]=e=>D()),icon:(0,a.SU)(ve.GZ8),flat:"",round:"",dense:"",color:"primary"},{default:(0,o.w5)((()=>[(0,o.Wm)(l,null,{default:(0,o.w5)((()=>[Ce])),_:1})])),_:1},8,["icon"]),(0,o.Wm)(r,{id:"chip-diagram-menu",icon:(0,a.SU)(ve.SXi),flat:"",round:"",dense:"",color:"primary"},{default:(0,o.w5)((()=>[(0,o.Wm)(s,null,{default:(0,o.w5)((()=>[(0,o.Wm)(Pe,{stage:u.value},null,8,["stage"])])),_:1})])),_:1},8,["icon"])]),(0,o._)("div",{class:"stageStyle",ref_key:"stageDiv",ref:k},[(0,o.Wm)(c,{onResize:E}),(0,o.Wm)(h,{ref_key:"stageRef",ref:u,config:e.configStage,onWheel:q},{default:(0,o.w5)((()=>[(0,o.Wm)(g,{ref:"packageLayer",onClick:x},{default:(0,o.w5)((()=>[(0,o.Wm)(d,null,{default:(0,o.w5)((()=>[(0,o.WI)(t.$slots,"package")])),_:3})])),_:3},512),(0,o.Wm)(g,{ref:"pinLayer",onMouseover:v,onMouseleave:S},{default:(0,o.w5)((()=>[(0,o._)("template",null,[(0,o.WI)(t.$slots,"pins")])])),_:3},512),(0,o.Wm)(g,{ref:"mouselayer"},{default:(0,o.w5)((()=>[(0,o.Wm)(d,{ref_key:"tipgroup",ref:p,name:"tipGroup"},{default:(0,o.w5)((()=>[(0,o.Wm)(f,{name:"tipRect",config:w}),(0,o.Wm)(m,{id:"tiptext",config:P}),Ie])),_:1},512)])),_:1},512)])),_:3},8,["config"])],512),(0,o.Wm)(s,{"touch-position":"","context-menu":""},{default:(0,o.w5)((()=>[(0,o.Wm)(Pe,{stage:t.stage},null,8,["stage"])])),_:1})])}}};var Oe=n(6362),We=n(883);const Ae=(0,M.Z)(Ue,[["__scopeId","data-v-9bbc31ce"]]),_e=Ae;Q()(Ue,"components",{QBtn:L.Z,QTooltip:H.Z,QMenu:Oe.Z,QResizeObserver:We.Z});const De={__name:"BGA_PinGroup",props:{pinId:String,pinCircleProps:Object,numberLabelProps:Object,defaultNameProps:Object,customNameProps:Object,gpioDecoratorProps:Object,selectionIndicatorProps:Object},setup(e){const t=e,n=b(),{pinTable:i,pinWarnings:r,pinErrors:l}=(0,s.Jk)(n),c=(0,o.Fl)((()=>{const e=Object.assign({},t.pinCircleProps);let n=i.value.find((e=>e.NAME==t.pinId));return e.fill=h(t.pinCircleProps.pinEnabledProp,l.value.includes(t.pinId),r.value.includes(t.pinId),n),e})),{pinSelectedRows:p}=(0,s.Jk)(n),u=(0,o.Fl)((()=>{const e=Object.assign({},t.selectionIndicatorProps);if(e.visible=!1,p.value){const n=p.value.findIndex((e=>e.NAME==t.pinId))>-1;e.visible=n}return e})),d=(0,o.Fl)((()=>{const e=Object.assign({},t.customNameProps);if(i.value){const n=i.value.find((e=>e.NAME==t.pinId));n&&(e.text=f(n))}return e})),g=(0,o.Fl)((()=>{const e=Object.assign({},t.gpioDecoratorProps);let n=i.value.find((e=>e.NAME==t.pinId));return n&&"GPIO mode"==n.FUNCTION?e.text="G":e.text="",e}));return(t,n)=>{const i=(0,o.up)("v-circle"),r=(0,o.up)("v-text"),l=(0,o.up)("v-rect");return(0,o.wg)(),(0,o.iD)(o.HY,null,[(0,o.Wm)(i,{config:(0,a.SU)(c)},null,8,["config"]),(0,o.Wm)(r,{config:e.numberLabelProps},null,8,["config"]),(0,o.Wm)(r,{config:e.defaultNameProps},null,8,["config"]),(0,o.Wm)(r,{config:(0,a.SU)(d)},null,8,["config"]),(0,o.Wm)(r,{config:(0,a.SU)(g)},null,8,["config"]),(0,o.Wm)(l,{config:(0,a.SU)(u)},null,8,["config"])],64)}}},Re=De,Fe=Re,Me={__name:"QFN_PinGroup",props:{pinId:String,pinBorderProps:Object,pinNumberLabelProps:Object,customNameProps:Object,gpioDecoratorProps:Object,selectionIndicatorProps:Object},setup(e){const t=e,n=b(),{pinTable:i,pinWarnings:r,pinErrors:l}=(0,s.Jk)(n),c=(0,o.Fl)((()=>{const e={};e.sceneFunc=p,e.stroke=t.pinBorderProps.stroke,e.strokeWidth=t.pinBorderProps.strokeWidth;let n=i.value.find((e=>e.NAME==t.pinId));return e.fill=h(t.pinBorderProps.pinEnabledProp,l.value.includes(t.pinId),r.value.includes(t.pinId),n),e.id=t.pinBorderProps.id,e}));function p(e,n){e.beginPath(),e.moveTo(t.pinBorderProps.point1.x,t.pinBorderProps.point1.y),e.lineTo(t.pinBorderProps.point2.x,t.pinBorderProps.point2.y),e.arc(t.pinBorderProps.arcProps.arcX,t.pinBorderProps.arcProps.arcY,t.pinBorderProps.arcProps.arcRadius,t.pinBorderProps.arcProps.startAngle,t.pinBorderProps.arcProps.endAngle,t.pinBorderProps.arcProps.counterClockwise),e.lineTo(t.pinBorderProps.point4.x,t.pinBorderProps.point4.y),e.closePath(),e.fillStrokeShape(n)}const{pinSelectedRows:u}=(0,s.Jk)(n),d=(0,o.Fl)((()=>{const e=Object.assign({},t.selectionIndicatorProps);if(e.visible=!1,u.value){const n=u.value.findIndex((e=>e.NAME===t.pinId))>-1;e.visible=n}return e})),g=(0,o.Fl)((()=>{const e=Object.assign({},t.customNameProps);if(i.value){const n=i.value.find((e=>e.NAME==t.pinId));n&&(e.text=f(n))}return e})),m=(0,o.Fl)((()=>{const e=Object.assign({},t.gpioDecoratorProps);let n=i.value.find((e=>e.NAME==t.pinId));return n&&"GPIO mode"==n.FUNCTION?e.text="G":e.text="",e}));return(t,n)=>{const i=(0,o.up)("v-shape"),r=(0,o.up)("v-text"),l=(0,o.up)("v-rect");return(0,o.wg)(),(0,o.iD)(o.HY,null,[(0,o.Wm)(i,{config:(0,a.SU)(c)},null,8,["config"]),(0,o.Wm)(r,{config:e.pinNumberLabelProps},null,8,["config"]),(0,o.Wm)(r,{config:(0,a.SU)(g)},null,8,["config"]),(0,o.Wm)(r,{config:(0,a.SU)(m)},null,8,["config"]),(0,o.Wm)(l,{config:(0,a.SU)(d)},null,8,["config"])],64)}}},je=Me,qe=je,Be={__name:"RECTS_PinGroup",props:{pinId:String,pinBorderProps:Object,pinNumberProps:Object,defaultNameProps:Object,customNameProps:Object,gpioDecoratorProps:Object,selectionIndicatorProps:Object},setup(e){const t=e,n=b(),{pinTable:i,pinWarnings:r,pinErrors:l}=(0,s.Jk)(n),c=(0,o.Fl)((()=>{const e=Object.assign({},t.pinBorderProps);let n=i.value.find((e=>e.NAME==t.pinId));return e.fill=h(t.pinBorderProps.pinEnabledProp,l.value.includes(t.pinId),r.value.includes(t.pinId),n),e})),{pinSelectedRows:p}=(0,s.Jk)(n),u=(0,o.Fl)((()=>{const e=Object.assign({},t.selectionIndicatorProps);if(e.visible=!1,p.value){const n=p.value.findIndex((e=>e.NAME===t.pinId))>-1;e.visible=n}return e})),d=(0,o.Fl)((()=>{const e=Object.assign({},t.customNameProps);if(i.value){const n=i.value.find((e=>e.NAME==t.pinId));n&&(e.text=f(n))}return e})),g=(0,o.Fl)((()=>{const e=Object.assign({},t.gpioDecoratorProps);let n=i.value.find((e=>e.NAME==t.pinId));return n&&"GPIO mode"==n.FUNCTION?e.text="G":e.text="",e}));return(t,n)=>{const i=(0,o.up)("v-rect"),r=(0,o.up)("v-text");return(0,o.wg)(),(0,o.iD)(o.HY,null,[(0,o.Wm)(i,{config:(0,a.SU)(c)},null,8,["config"]),(0,o.Wm)(r,{config:e.pinNumberProps},null,8,["config"]),(0,o.Wm)(r,{config:e.defaultNameProps},null,8,["config"]),(0,o.Wm)(r,{config:(0,a.SU)(d)},null,8,["config"]),(0,o.Wm)(r,{config:(0,a.SU)(g)},null,8,["config"]),(0,o.Wm)(i,{config:(0,a.SU)(u)},null,8,["config"])],64)}}},xe=Be,Ze=xe,He={__name:"TQFP_PinGroup",props:{pinId:String,pinBorderProps:Object,pinNumberLabelProps:Object,customNameProps:Object,gpioDecoratorProps:Object,selectionIndicatorProps:Object},setup(e){const t=e,n=b(),{pinTable:i,pinWarnings:r,pinErrors:l}=(0,s.Jk)(n),c=(0,o.Fl)((()=>{const e=Object.assign({},t.pinBorderProps);let n=i.value.find((e=>e.NAME==t.pinId));return e.fill=h(t.pinBorderProps.pinEnabledProp,l.value.includes(t.pinId),r.value.includes(t.pinId),n),e})),{pinSelectedRows:p}=(0,s.Jk)(n),u=(0,o.Fl)((()=>{const e=Object.assign({},t.selectionIndicatorProps);if(e.visible=!1,p.value){const n=p.value.findIndex((e=>e.NAME===t.pinId))>-1;e.visible=n}return e})),d=(0,o.Fl)((()=>{const e=Object.assign({},t.customNameProps);if(i.value){const n=i.value.find((e=>e.NAME==t.pinId));n&&(e.text=f(n))}return e})),g=(0,o.Fl)((()=>{const e=Object.assign({},t.gpioDecoratorProps);let n=i.value.find((e=>e.NAME==t.pinId));return n&&"GPIO mode"==n.FUNCTION?e.text="G":e.text="",e}));return(t,n)=>{const i=(0,o.up)("v-rect"),r=(0,o.up)("v-text");return(0,o.wg)(),(0,o.iD)(o.HY,null,[(0,o.Wm)(i,{config:(0,a.SU)(c)},null,8,["config"]),(0,o.Wm)(r,{config:e.pinNumberLabelProps},null,8,["config"]),(0,o.Wm)(r,{config:(0,a.SU)(d)},null,8,["config"]),(0,o.Wm)(r,{config:(0,a.SU)(g)},null,8,["config"]),(0,o.Wm)(i,{config:(0,a.SU)(u)},null,8,["config"])],64)}}},Le=He,$e=Le,Ve={key:0},ze={key:1},Qe={key:2},Ge={key:3},Ke={key:4},Ye={__name:"ChipDiagram",props:["packageType","configStage","packageData","pinConfigData"],setup(e){const t=e,n=(0,a.iH)(null),i=(0,a.iH)(null),r=(0,a.iH)(null),l=(0,a.iH)(null);function c(e,n){e.beginPath(),e.moveTo(t.packageData.notchedInnerRect.start.x,t.packageData.notchedInnerRect.start.y),e.lineTo(t.packageData.notchedInnerRect.rightTop.x,t.packageData.notchedInnerRect.rightTop.y),e.lineTo(t.packageData.notchedInnerRect.rightBottom.x,t.packageData.notchedInnerRect.rightBottom.y),e.lineTo(t.packageData.notchedInnerRect.leftBottom.x,t.packageData.notchedInnerRect.leftBottom.y),e.lineTo(t.packageData.notchedInnerRect.leftTopCorner.x,t.packageData.notchedInnerRect.leftTopCorner.y),e.closePath(),e.fillStrokeShape(n)}const p=b(),{pinSelectedRows:u}=(0,s.Jk)(p);async function d(e){const t=e.currentTarget.attrs["id"],n=u.value.filter((e=>t==e.NAME)).length>0,o=[],a=[];if(n){a.push(t);const e=u.value.filter((e=>t!==e.NAME));e.forEach((e=>o.push(e.NAME)))}else e.evt.ctrlKey?(u.value.forEach((e=>o.push(e.NAME))),o.push(t)):(o.push(t),u.value.forEach((e=>a.push(e.NAME))));await p.selectionChanged("pin",o,a)}return(t,a)=>{const s=(0,o.up)("v-rect"),p=(0,o.up)("v-circle"),u=(0,o.up)("v-text"),g=(0,o.up)("v-group"),f=(0,o.up)("v-shape");return"BGA"===e.packageType?((0,o.wg)(),(0,o.iD)("div",Ve,[((0,o.wg)(),(0,o.j4)(_e,{key:e.packageType,configStage:e.configStage,ref_key:"bgaPackage",ref:n},{package:(0,o.w5)((()=>[(0,o.Wm)(s,{config:e.packageData.outerRect},null,8,["config"]),(0,o.Wm)(p,{config:e.packageData.firstPinConfig},null,8,["config"]),(0,o.Wm)(u,{config:e.packageData.description},null,8,["config"])])),pins:(0,o.w5)((()=>[((0,o.wg)(!0),(0,o.iD)(o.HY,null,(0,o.Ko)(e.pinConfigData,(e=>((0,o.wg)(),(0,o.j4)(g,{key:e.id,id:e.id,name:"pinGroup",ref_for:!0,ref:"pinGroup",onClick:d},{default:(0,o.w5)((()=>[(0,o.Wm)(Fe,{pinId:e.id,pinCircleProps:e.pinCircleProps,numberLabelProps:e.numberLabelProps,defaultNameProps:e.defaultNameProps,customNameProps:e.customNameProps,gpioDecoratorProps:e.gpioDecoratorProps,selectionIndicatorProps:e.selectionIndicatorProps},null,8,["pinId","pinCircleProps","numberLabelProps","defaultNameProps","customNameProps","gpioDecoratorProps","selectionIndicatorProps"])])),_:2},1032,["id"])))),128))])),_:1},8,["configStage"]))])):"QFN"===e.packageType?((0,o.wg)(),(0,o.iD)("div",ze,[((0,o.wg)(),(0,o.j4)(_e,{key:e.packageType,configStage:e.configStage,ref_key:"qfnPackage",ref:i},{package:(0,o.w5)((()=>[(0,o.Wm)(s,{config:e.packageData.packageBorder},null,8,["config"]),(0,o.Wm)(f,{config:{sceneFunc:c,fill:e.packageData.notchedInnerRect.fill,stroke:e.packageData.notchedInnerRect.stroke,strokeWidth:e.packageData.notchedInnerRect.strokeWidth}},null,8,["config"]),(0,o.Wm)(p,{config:e.packageData.firstPinIndicator},null,8,["config"]),(0,o.Wm)(u,{config:e.packageData.subtitleText},null,8,["config"]),(0,o.Wm)(u,{config:e.packageData.padDescription},null,8,["config"]),(0,o.Wm)(u,{config:e.packageData.description},null,8,["config"])])),pins:(0,o.w5)((()=>[((0,o.wg)(!0),(0,o.iD)(o.HY,null,(0,o.Ko)(e.pinConfigData,(e=>((0,o.wg)(),(0,o.j4)(g,{key:e.id,id:e.id,name:"pinGroup",ref_for:!0,ref:"pinGroup",onClick:d},{default:(0,o.w5)((()=>[(0,o.Wm)(qe,{pinId:e.id,pinBorderProps:e.pinBorderProps,pinNumberLabelProps:e.numberLabelProps,customNameProps:e.customNameProps,gpioDecoratorProps:e.gpioDecoratorProps,selectionIndicatorProps:e.selectionIndicator},null,8,["pinId","pinBorderProps","pinNumberLabelProps","customNameProps","gpioDecoratorProps","selectionIndicatorProps"])])),_:2},1032,["id"])))),128))])),_:1},8,["configStage"]))])):"RECTS"===e.packageType?((0,o.wg)(),(0,o.iD)("div",Qe,[((0,o.wg)(),(0,o.j4)(_e,{key:e.packageType,configStage:e.configStage,ref_key:"rectsPackage",ref:r},{package:(0,o.w5)((()=>[(0,o.Wm)(s,{config:e.packageData.packageBorder},null,8,["config"]),(0,o.Wm)(u,{config:e.packageData.description},null,8,["config"])])),pins:(0,o.w5)((()=>[((0,o.wg)(!0),(0,o.iD)(o.HY,null,(0,o.Ko)(e.pinConfigData,(e=>((0,o.wg)(),(0,o.j4)(g,{key:e.id,id:e.id,name:"pinGroup",ref_for:!0,ref:"pinGroup",onClick:d},{default:(0,o.w5)((()=>[(0,o.Wm)(Ze,{pinId:e.id,pinBorderProps:e.pinBorder,pinNumberProps:e.pinNumberText,defaultNameProps:e.defaultNameText,customNameProps:e.customNameProps,gpioDecoratorProps:e.gpioDecoratorProps,selectionIndicatorProps:e.selectionIndicator},null,8,["pinId","pinBorderProps","pinNumberProps","defaultNameProps","customNameProps","gpioDecoratorProps","selectionIndicatorProps"])])),_:2},1032,["id"])))),128))])),_:1},8,["configStage"]))])):"TQFP"===e.packageType?((0,o.wg)(),(0,o.iD)("div",Ge,[((0,o.wg)(),(0,o.j4)(_e,{key:e.packageType,configStage:e.configStage,ref_key:"tqfpPackage",ref:l},{package:(0,o.w5)((()=>[(0,o.Wm)(s,{config:e.packageData.packageBorder},null,8,["config"]),(0,o.Wm)(p,{config:e.packageData.firstPinIndicator},null,8,["config"]),(0,o.Wm)(u,{config:e.packageData.description},null,8,["config"])])),pins:(0,o.w5)((()=>[((0,o.wg)(!0),(0,o.iD)(o.HY,null,(0,o.Ko)(e.pinConfigData,(e=>((0,o.wg)(),(0,o.j4)(g,{key:e.id,id:e.id,name:"pinGroup",ref_for:!0,ref:"pinGroup",onClick:d},{default:(0,o.w5)((()=>[((0,o.wg)(),(0,o.j4)($e,{key:e.id,pinId:e.id,pinBorderProps:e.pinBorderProps,pinNumberLabelProps:e.pinNumberLabelProps,customNameProps:e.customNameProps,gpioDecoratorProps:e.gpioDecoratorProps,selectionIndicatorProps:e.selectionIndicatorProps},null,8,["pinId","pinBorderProps","pinNumberLabelProps","customNameProps","gpioDecoratorProps","selectionIndicatorProps"]))])),_:2},1032,["id"])))),128))])),_:1},8,["configStage"]))])):((0,o.wg)(),(0,o.iD)("div",Ke,"Unknown package type "+(0,N.zw)(e.packageType),1))}}},Je=Ye,Xe=Je,et={__name:"IndexPage",setup(e){let t=null;(0,o.Jd)((()=>{t&&t.close()}));const n=(0,a.iH)(!1),l=(0,P.tv)(),c=(0,P.yj)(),p=(0,r.Z)(),u=b(),d=(0,a.iH)(""),g=ce(),f=(0,a.iH)(50),m=(0,a.iH)(""),h=[{name:"pin",label:"Pins"},{name:"function",label:"Function"},{name:"peripherals",label:"Peripherals"}],{specificItem:w}=(0,s.Jk)(u),N=(0,a.iH)(),S=(0,a.iH)(),y=(0,a.iH)();function k(e){return"pin"===e?N.value:"function"===e?S.value:"peripherals"===e?y.value:null}function T(){const e=m.value,t=k(e);if(null==t)return;const n=w.value;n&&t.scrollToRow(n)}function E(){d.value=""}(0,o.YP)(w,(e=>{if(!e||!e.tab)return;m.value=e.tab;const t=k(m.value);t&&t.scrollToRow(e)}));let C=!1;function I(){p.dark.set(!p.dark.isActive)}return(0,o.bv)((async()=>{p.loading.show({delay:400}),await l.isReady(),C=!0;const e=c.query.project;u.setProjectUniqueId(e),g.setProjectId(e),t=u.createSaveStatusSocket(u),u.updateAllTables(),u.fetchProblemMarkers(),await g.getDiagramData().catch((e=>{p.notify({type:"negative",message:"Problem getting diagram.  Try reopening editor.\n"+e.message})})),n.value=!0,p.loading.hide(),m.value="pin"})),(e,t)=>{const r=(0,o.up)("q-btn"),l=(0,o.up)("q-tab"),s=(0,o.up)("q-icon"),c=(0,o.up)("q-input"),p=(0,o.up)("q-tabs"),w=(0,o.up)("q-separator"),b=(0,o.up)("q-tab-panel"),P=(0,o.up)("q-tab-panels"),k=(0,o.up)("q-card"),U=(0,o.up)("q-splitter"),O=(0,o.up)("q-page");return n.value?((0,o.wg)(),(0,o.j4)(O,{key:0,class:"items-stretch"},{default:(0,o.w5)((()=>[(0,o.Wm)(U,{modelValue:f.value,"onUpdate:modelValue":t[4]||(t[4]=e=>f.value=e)},{before:(0,o.w5)((()=>[(0,a.SU)(C)?(0,o.kq)("",!0):((0,o.wg)(),(0,o.j4)(r,{key:0,style:{position:"absolute"},onClick:t[0]||(t[0]=e=>I()),label:"SWITCH THEME",dense:"",color:"primary"})),((0,o.wg)(),(0,o.j4)(Xe,{id:"chipdiagram",class:"windowHeight",key:(0,a.SU)(g).uniqProjectId,packageType:(0,a.SU)(g).diagramData.packageType,configStage:(0,a.SU)(g).diagramData.configStage,packageData:(0,a.SU)(g).diagramData.packageConfigData,pinConfigData:(0,a.SU)(g).diagramData.pinsConfigData},null,8,["packageType","configStage","packageData","pinConfigData"]))])),after:(0,o.w5)((()=>[(0,o.Wm)(k,{class:"windowHeight"},{default:(0,o.w5)((()=>[(0,o.Wm)(p,{id:"pintool-tabs",class:"pintool-tabs",modelValue:m.value,"onUpdate:modelValue":t[2]||(t[2]=e=>m.value=e),dense:"","active-color":"primary","indicator-color":"primary",align:"justify","narrow-indicator":""},{default:(0,o.w5)((()=>[((0,o.wg)(),(0,o.iD)(o.HY,null,(0,o.Ko)(h,(e=>(0,o.Wm)(l,{class:"root-menu-text","no-caps":"",name:e.name,label:e.label,key:e.name},null,8,["name","label"]))),64)),(0,o.Wm)(c,{class:"search-input",outlined:"",dense:"",modelValue:d.value,"onUpdate:modelValue":t[1]||(t[1]=e=>d.value=e),label:"Search",clearable:"",onKeydown:(0,i.D2)(E,["esc"])},{prepend:(0,o.w5)((()=>[(0,o.Wm)(s,{name:(0,a.SU)(v.I0v)},null,8,["name"])])),_:1},8,["modelValue","onKeydown"])])),_:1},8,["modelValue"]),(0,o.Wm)(w),(0,o.Wm)(P,{modelValue:m.value,"onUpdate:modelValue":t[3]||(t[3]=e=>m.value=e),animated:"","keep-alive":"",onTransition:T},{default:(0,o.w5)((()=>[(0,o.Wm)(b,{name:"pin"},{default:(0,o.w5)((()=>[(0,o.Wm)(ae,{ref_key:"pin_table",ref:N,id:e.$C.PIN_TABLE,header:(0,a.SU)(u).pinTableHeader,data:(0,a.SU)(u).pinTable,loading:(0,a.SU)(u).pinTableLoading,selectedRows:(0,a.SU)(u).pinSelectedRows,rowKey:(0,a.SU)(u).pinKey,rowWarnings:(0,a.SU)(u).pinWarnings,rowErrors:(0,a.SU)(u).pinErrors,filter:d.value,sort:(0,a.SU)(u).pinSort,type:"pin"},null,8,["id","header","data","loading","selectedRows","rowKey","rowWarnings","rowErrors","filter","sort"])])),_:1}),(0,o.Wm)(b,{name:"function"},{default:(0,o.w5)((()=>[(0,o.Wm)(ae,{ref_key:"function_table",ref:S,id:e.$C.FUNCTION_TABLE,header:(0,a.SU)(u).functionTableHeader,data:(0,a.SU)(u).functionTable,loading:(0,a.SU)(u).functionTableLoading,selectedRows:(0,a.SU)(u).functionSelectedRows,rowKey:(0,a.SU)(u).functionKey,rowWarnings:(0,a.SU)(u).functionWarnings,rowErrors:(0,a.SU)(u).functionErrors,filter:d.value,sort:(0,a.SU)(u).functionSort,type:"function"},null,8,["id","header","data","loading","selectedRows","rowKey","rowWarnings","rowErrors","filter","sort"])])),_:1}),(0,o.Wm)(b,{name:"peripherals"},{default:(0,o.w5)((()=>[(0,o.Wm)(ae,{ref_key:"peripheral_table",ref:y,id:e.$C.PERIPHERAL_TABLE,header:(0,a.SU)(u).peripheralTableHeader,data:(0,a.SU)(u).peripheralTable,loading:(0,a.SU)(u).peripheralTableLoading,selectedRows:(0,a.SU)(u).peripheralSelectedRows,rowKey:(0,a.SU)(u).peripheralKey,rowWarnings:(0,a.SU)(u).peripheralWarnings,rowErrors:(0,a.SU)(u).peripheralErrors,filter:d.value,sort:(0,a.SU)(u).peripheralSort,type:"peripheral"},null,8,["id","header","data","loading","selectedRows","rowKey","rowWarnings","rowErrors","filter","sort"])])),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["modelValue"])])),_:1})):(0,o.kq)("",!0)}}};var tt=n(1736),nt=n(9885),ot=n(7817),at=n(900),it=n(9800),rt=n(4106);const lt=(0,M.Z)(et,[["__scopeId","data-v-3f3d6003"]]),st=lt;Q()(et,"components",{QSplitter:tt.Z,QPage:nt.Z,QBtn:L.Z,QCard:q.Z,QTabs:ot.Z,QTab:at.Z,QInput:Z.Z,QIcon:ee.Z,QSeparator:we.Z,QTabPanels:it.Z,QTabPanel:rt.Z})}}]);