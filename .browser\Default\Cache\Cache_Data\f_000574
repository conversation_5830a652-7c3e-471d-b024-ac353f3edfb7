(()=>{"use strict";var t={6485:(t,e,o)=>{o(8964),o(702);var r=o(1957),s=o(1947),n=o(499),a=o(9835);const c={id:"q-app"};function i(t,e,o,r,s,n){const i=(0,a.up)("router-view");return(0,a.wg)(),(0,a.iD)("div",c,[(0,a.Wm)(i)])}var l=o(3100),u=o(9357);const d={name:"App",data(){return{socket:null,eventSocket:null,deviceStatusSocket:null}},created(){u.KM.observe(),this.$store.dispatch("lcontext/createSocket").then((t=>{this.socket=t})),this.$store.dispatch("event/createSocket").then((t=>{this.eventSocket=t})),this.$store.dispatch("device/createDeviceStatusSocket").then((t=>{this.deviceStatusSocket=t}))},computed:{...(0,l.rn)({lcontext:t=>t.lcontext.lcontext})},watch:{lcontext:function(t){const e=this.$router.currentRoute.value.name;t.hasOwnProperty("select")&&"tab"===t.select&&t.hasOwnProperty("tab")?this.$router.push({name:t.tab}):"welcome"===t.page?"welcome"!=e&&this.$router.push({name:"welcome"}):"welcome"===e&&this.$router.push({name:"overview"})}},beforeUnmount(){this.socket&&this.socket.close(),this.eventSocket&&this.eventSocket.close(),this.deviceStatusSocket&&this.deviceStatusSocket.close()}};var p=o(1639);const h=(0,p.Z)(d,[["render",i]]),m=h;var S=o(2837),w=o(8910);const v=[{path:"/",component:()=>Promise.all([o.e(736),o.e(55)]).then(o.bind(o,7055)),children:[{path:"/",name:"welcome",component:()=>Promise.all([o.e(736),o.e(64),o.e(632)]).then(o.bind(o,9567))},{path:"/product",component:()=>Promise.all([o.e(736),o.e(376)]).then(o.bind(o,7549)),children:[{path:"",name:"overview",component:()=>Promise.all([o.e(736),o.e(731)]).then(o.bind(o,6731))},{path:"examples",name:"examples",component:()=>Promise.all([o.e(736),o.e(64),o.e(131)]).then(o.bind(o,4552))},{path:"documentation",name:"documentation",component:()=>Promise.all([o.e(736),o.e(64),o.e(488)]).then(o.bind(o,5804))},{path:"tools",name:"tools",component:()=>Promise.all([o.e(736),o.e(64),o.e(440)]).then(o.bind(o,3410))}]}]}];v.push({path:"/:pathMatch(.*)*",component:()=>Promise.all([o.e(736),o.e(907)]).then(o.bind(o,3907))});const g=v;function f(){const t=w.r5;return(0,w.p7)({scrollBehavior:()=>({left:0,top:0}),routes:g,history:t("")})}async function E(t,e){const r=t(m);r.use(s.Z,e);const a="function"===typeof S["default"]?await(0,S["default"])({}):S["default"],{storeKey:c}=await Promise.resolve().then(o.bind(o,2837)),i=(0,n.Xl)("function"===typeof f?await f({store:a}):f);return a.$router=i,{app:r,store:a,storeKey:c,router:i}}var b=o(5448),T=o(4328),O=o(6950),k=o(3703);const C={config:{},iconSet:b.Z,plugins:{Notify:T.Z,Loading:O.Z,LocalStorage:k.Z}},_="";async function y({app:t,router:e,store:o,storeKey:r},s){let n=!1;const a=t=>{try{return e.resolve(t).href}catch(o){}return Object(t)===t?null:t},c=t=>{if(n=!0,"string"===typeof t&&/^https?:\/\//.test(t))return void(window.location.href=t);const e=a(t);null!==e&&(window.location.href=e,window.location.reload())},i=window.location.href.replace(window.location.origin,"");for(let u=0;!1===n&&u<s.length;u++)try{await s[u]({app:t,router:e,store:o,ssrContext:null,redirect:c,urlPath:i,publicPath:_})}catch(l){return l&&l.url?void c(l.url):void console.error("[Quasar] boot error:",l)}!0!==n&&(t.use(e),t.use(o,r),t.mount("#q-app"))}E(r.ri,C).then((t=>Promise.all([Promise.resolve().then(o.bind(o,1569)),Promise.resolve().then(o.bind(o,5529))]).then((e=>{const o=e.map((t=>t.default)).filter((t=>"function"===typeof t));y(t,o)}))))},1569:(t,e,o)=>{o.r(e),o.d(e,{api:()=>a,default:()=>c});var r=o(3340),s=o(9981),n=o.n(s);const a=n().create({baseURL:"/"}),c=(0,r.xr)((({app:t})=>{t.config.globalProperties.$axios=n(),t.config.globalProperties.$api=a}))},2837:(t,e,o)=>{o.r(e),o.d(e,{default:()=>Y});var r=o(3100);const s={lcontext:{page:"welcome",hashCode:-1,hardwareContext:"",label:""}},n={CONTEXT_ONMESSAGE(t,e){t.lcontext=e}};var a=o(9357);const c={createSocket(t){const e=new a.sh(`ws://${window.location.host}/ws/launcher/context`,{contextSocketOnError(t){window.console.log(t)},contextSocketOnMessage(e){const o=JSON.parse(e.data);t.commit("CONTEXT_ONMESSAGE",o)}},"contextSocket");return e.init(),e}},i={},l={namespaced:!0,getters:i,mutations:n,actions:c,state:s},u={hwContext:{kit:{},boards:[],part:{}},hwTools:{debugOutLabel:"",contextHash:-1,isEthernet:null,deviceStatus:[]},devices:[],product:{kits:[],boards:[],part:[]},detectPartJobStatus:{isRunning:!1,resultIsOk:!0,message:""}},d={SET_HW_CONTEXT(t,e){t.hwContext=e},SET_DEVICE_STATUS(t,e){t.hwtools=e},SET_PHOTOS(t,e){if(e.hardwareContext===t.hwContext.hardwareContext){for(var o in t.hwContext.boards)t.hwContext.boards[o].imageURL=e.boards[o].imageURL;t.hwContext.part.imageURL=e.part.imageURL}},SET_DOCS(t,e){if(e.hardwareContext===t.hwContext.hardwareContext){for(var o in t.hwContext.docs=e.docs,t.hwContext.boards)t.hwContext.boards[o].docs=e.boards[o].docs;t.hwContext.part.docs=e.part.docs}},DEVICES_ONMESSAGE(t,e){t.devices=e},SET_DEVICES(t,e){t.devices=e},SET_PRODUCT(t,e){t.product=e},DEVICE_STATUS_ONMESSAGE(t,e){t.hwTools=e},DEVICE_STATUS_SET_DEVICES(t,e){t.devices=e},DEVICE_STATUS_SET_PRODUCT(t,e){t.product=e},DETECT_PART_JOB_STATUS_ONMESSAGE(t,e){t.detectPartJobStatus=e}};var p=o(9981),h=o.n(p);const m={"Content-Type":"application/json"},S={get(t,e){return h()({method:"get",url:t,params:e,headers:m})},post(t,e,o){return h()({method:"post",url:t,params:o,data:e,headers:m})}},w={createDevicesSocket(t){const e=new a.sh(`ws://${window.location.host}/ws/device/devices`,{devicesSocketOnError(t){window.console.log(t)},devicesSocketOnMessage(e){const o=JSON.parse(e.data);t.commit("DEVICES_ONMESSAGE",o)}},"devicesSocket");return e.init(),e},createDeviceStatusSocket(t){const e=new a.sh(`ws://${window.location.host}/ws/hwtools/deviceStatus`,{deviceStatusSocketOnError(t){window.console.log(t)},deviceStatusSocketOnMessage(e){const o=JSON.parse(e.data);t.commit("DEVICE_STATUS_ONMESSAGE",o)}},"deviceStatusSocket");return e.init(),e},createDetectPartJobStatusSocket(t){const e=new a.sh(`ws://${window.location.host}/ws/device/detectpartwebsocket`,{detectPartJobStatusSocketOnError(t){window.console.log(t)},detectPartJobStatusSocketOnMessage(e){const o=JSON.parse(e.data);t.commit("DETECT_PART_JOB_STATUS_ONMESSAGE",o)}},"detectPartJobStatusSocket");return e.init(),e},getHWContext(t,e){const o="/rest/launcher/hwcontext";return new Promise(((r,s)=>S.get(o,e).then((e=>{t.commit("SET_HW_CONTEXT",e.data),r(e)})).catch((t=>{s(t)}))))},getProduct(t){const e="/rest/installer/product";return new Promise(((o,r)=>S.get(e).then((e=>{t.commit("SET_PRODUCT",e.data),o(e)})).catch((t=>{r(t)}))))},postDeviceStatus(t,e){const o="/rest/hwtools/deviceStatus";S.post(o,e)},postPhotoInfo(t,e){const o="/rest/studio/hardware/photoinfo";return new Promise(((r,s)=>S.post(o,e).then((e=>{t.commit("SET_PHOTOS",e.data),r(e)})).catch((t=>{s(t)}))))},postSelectDevice(t,e){const o="/rest/device/selectdevice";return S.post(o,e)},postDeviceDocs(t,e){const o="/rest/launcher/docs";return new Promise(((r,s)=>S.post(o,e).then((e=>{t.commit("SET_DOCS",e.data),r(e)})).catch((t=>{s(t)}))))},postProductView(t,e){const o="/rest/launcher/productview";return S.post(o,e)},postProduct(){const t="/rest/installer/product";return S.post(t)},postDeviceConfigDialog(){const t="/rest/device/deviceconfigdialog";return S.post(t)},postDebugOutDetectPart(t,e){const o="/rest/device/debugoutdetectpart";return S.post(o,e)}},v={namespaced:!0,mutations:d,actions:w,state:u},g={resource:{userState:"",resources:[],filters:[],totalCount:0},datalist:[],markdown:{content:""},launchStatusMessage:{},launchMessageCnt:0},f={},E={SET_RESOURCE(t,e){t.resource=e},SET_DATALIST(t,e){t.datalist=e},SET_MARKDOWN(t,e){t.markdown=e},LAUNCHABLE_STATUS_ONMESSAGE(t,e){t.launchStatusMessage=e,t.launchMessageCnt++}},b={createLaunchableSocket(t){const e=new a.sh(`ws://${window.location.host}/ws/launcher/launchable`,{launchableSocketOnError(t){window.console.log(t)},launchableSocketOnMessage(e){const o=JSON.parse(e.data);t.commit("LAUNCHABLE_STATUS_ONMESSAGE",o)}},"launchableSocket");return e.init(),e},getResource(t,e){const o="/rest/launcher/resource";return new Promise(((r,s)=>S.get(o,e).then((e=>{t.commit("SET_RESOURCE",e.data),r(e)})).catch((t=>{s(t)}))))},postResource(t,e){const o="/rest/launcher/resource";return new Promise(((r,s)=>S.post(o,e).then((e=>{t.commit("SET_RESOURCE",e.data),r(e)})).catch((t=>{s(t)}))))},postTypedTerm(t,e){const o="/rest/launcher/resource";return new Promise(((r,s)=>S.post(o,e).then((e=>{t.commit("SET_DATALIST",e.data),r(e)})).catch((t=>{s(t)}))))},postLaunchable(t,e){const o="/rest/launcher/launchable";return S.post(o,e)},postReadFile(t,e){const o="/rest/studio/ui/services/readFile";return t.commit("SET_MARKDOWN",{content:""}),new Promise(((r,s)=>S.post(o,e).then((e=>{t.commit("SET_MARKDOWN",e.data),r(e)})).catch((t=>{s(t)}))))}},T={namespaced:!0,getters:f,mutations:E,actions:b,state:g},O={tools:[]},k={},C={SET_TOOLS(t,e){t.tools=e}},_={getTools(t){const e="/rest/launcher/compatibletools";return new Promise(((o,r)=>S.get(e).then((e=>{t.commit("SET_TOOLS",e.data),o(e)})).catch((t=>{r(t)}))))}},y={namespaced:!0,getters:k,mutations:C,actions:_,state:O},P={tiles:[{id:1,label:"Simplicity Studio User's Guide",icon:"document",description:"The official Simplicity Studio 5 User's Guide",action:"Open",url:"https://docs.silabs.com/simplicity-studio-5-users-guide/latest/"},{id:2,label:"Getting Started (Video Series)",icon:"video",description:"Getting started with Simplicity Studio video series",action:"Start",url:"https://www.silabs.com/support/training/ss-studio-100-getting-started"},{id:3,label:"Training and Tutorials",icon:"training",description:"Our collection of Simplicity Studio training and tutorial videos",action:"Start",url:"https://www.silabs.com/support/training.soft-development-environments_simplicity-studio"},{id:4,label:"Silicon Labs Community",icon:"community",description:"Where users come together to learn, get help, and grow their skills",action:"Join",url:"https://www.silabs.com/community/software/simplicity-studio"},{id:5,label:"Technical Support",icon:"support",description:"Get technical support directly from Silicon Labs",action:"Start",url:"https://www.silabs.com/support"},{id:6,label:"Tips and Tricks",icon:"tips",description:"Useful tips and tricks to help you optimize your tools setup",action:"Start",url:"https://docs.silabs.com/simplicity-studio-5-users-guide/latest/ss-5-users-guide-tips-and-tricks/"}]},A={},x={},N={postLaunchExternal(t,e){const o="/rest/studio/ui/services/openurl";return S.post(o,e)}},D={namespaced:!0,getters:A,mutations:x,actions:N,state:P},L={sdks:[]},M={SET_SDKS(t,e){t.sdks=e}},R={postSDKs(t,e){const o="/rest/sdk/sdks",r={hardwareContext:e},s={filter:"sdksForHWContext"};return new Promise(((e,n)=>S.post(o,r,s).then((o=>{t.commit("SET_SDKS",o.data),e(o)})).catch((t=>{n(t)}))))},postPreferredSDK(t,e){const o="/rest/sdk/preferred";return S.post(o,e)},postManageSDKs(){const t="/rest/sdk/sdks/manage";return S.post(t)}},U={},j={namespaced:!0,getters:U,mutations:M,actions:R,state:L},I={recentProjects:[]},G={},$={SET_RECENT(t,e){t.recentProjects=e}},J={getRecentProjects(t){const e="/rest/launcher/recentprojects";return new Promise(((o,r)=>S.get(e).then((e=>{t.commit("SET_RECENT",e.data),o(e)})).catch((t=>{r(t)}))))},postLaunchNewProject(){const t="/rest/launcher/newproject";return S.post(t)}},K={namespaced:!0,getters:G,mutations:$,actions:J,state:I},V={event:{eventId:""}},H={EVENT_ONMESSAGE(t,e){t.event=e}},W={createSocket(t){const e=new a.sh(`ws://${window.location.host}/ws/event`,{eventSocketOnError(t){window.console.log(t)},eventSocketOnMessage(e){const o=JSON.parse(e.data);t.commit("EVENT_ONMESSAGE",o)}},"eventSocket");return e.init(),e}},B={},F={namespaced:!0,getters:B,mutations:H,actions:W,state:V},Z={install:{firstTimeComplete:!1}},X={INSTALL_STATE(t,e){t.firstTimeComplete=e.firstTimeInstallComplete}},q={getInstallState(t){const e="/rest/launcher/systemstate?installState";return new Promise(((o,r)=>S.get(e).then((e=>{t.commit("INSTALL_STATE",e.data),o(e)})).catch((t=>{r(t)}))))},postLaunchInstaller(t,e){const o="/rest/launcher/systemstate";return new Promise(((t,r)=>S.post(o,e).then((e=>{t(e)})).catch((t=>{r(t)}))))}},z={},Q={namespaced:!0,getters:z,mutations:X,actions:q,state:Z};function Y(){return(0,r.MT)({modules:{lcontext:l,device:v,learn:D,sdk:j,resource:T,tool:y,project:K,event:F,install:Q},strict:!1})}}},e={};function o(r){var s=e[r];if(void 0!==s)return s.exports;var n=e[r]={exports:{}};return t[r](n,n.exports,o),n.exports}o.m=t,(()=>{var t=[];o.O=(e,r,s,n)=>{if(!r){var a=1/0;for(u=0;u<t.length;u++){for(var[r,s,n]=t[u],c=!0,i=0;i<r.length;i++)(!1&n||a>=n)&&Object.keys(o.O).every((t=>o.O[t](r[i])))?r.splice(i--,1):(c=!1,n<a&&(a=n));if(c){t.splice(u--,1);var l=s();void 0!==l&&(e=l)}}return e}n=n||0;for(var u=t.length;u>0&&t[u-1][2]>n;u--)t[u]=t[u-1];t[u]=[r,s,n]}})(),(()=>{o.n=t=>{var e=t&&t.__esModule?()=>t["default"]:()=>t;return o.d(e,{a:e}),e}})(),(()=>{o.d=(t,e)=>{for(var r in e)o.o(e,r)&&!o.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}})(),(()=>{o.f={},o.e=t=>Promise.all(Object.keys(o.f).reduce(((e,r)=>(o.f[r](t,e),e)),[]))})(),(()=>{o.u=t=>"js/"+(64===t?"chunk-common":t)+"."+{55:"d7b5810b",64:"749d1fd1",131:"424dc651",376:"12d9c58b",440:"b3e276db",488:"d1ae10a4",632:"03c768ac",731:"08f8166e",907:"2f3ecb88"}[t]+".js"})(),(()=>{o.miniCssF=t=>"css/"+({143:"app",736:"vendor"}[t]||t)+"."+{131:"512e9425",143:"1b863900",376:"46e53771",440:"80d8bde8",488:"569f4aad",632:"2395f74e",731:"28a00863",736:"134f2cc5"}[t]+".css"})(),(()=>{o.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"===typeof window)return window}}()})(),(()=>{o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e)})(),(()=>{var t={},e="launcher:";o.l=(r,s,n,a)=>{if(t[r])t[r].push(s);else{var c,i;if(void 0!==n)for(var l=document.getElementsByTagName("script"),u=0;u<l.length;u++){var d=l[u];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==e+n){c=d;break}}c||(i=!0,c=document.createElement("script"),c.charset="utf-8",c.timeout=120,o.nc&&c.setAttribute("nonce",o.nc),c.setAttribute("data-webpack",e+n),c.src=r),t[r]=[s];var p=(e,o)=>{c.onerror=c.onload=null,clearTimeout(h);var s=t[r];if(delete t[r],c.parentNode&&c.parentNode.removeChild(c),s&&s.forEach((t=>t(o))),e)return e(o)},h=setTimeout(p.bind(null,void 0,{type:"timeout",target:c}),12e4);c.onerror=p.bind(null,c.onerror),c.onload=p.bind(null,c.onload),i&&document.head.appendChild(c)}}})(),(()=>{o.r=t=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}})(),(()=>{o.p=""})(),(()=>{var t=(t,e,o,r)=>{var s=document.createElement("link");s.rel="stylesheet",s.type="text/css";var n=n=>{if(s.onerror=s.onload=null,"load"===n.type)o();else{var a=n&&("load"===n.type?"missing":n.type),c=n&&n.target&&n.target.href||e,i=new Error("Loading CSS chunk "+t+" failed.\n("+c+")");i.code="CSS_CHUNK_LOAD_FAILED",i.type=a,i.request=c,s.parentNode.removeChild(s),r(i)}};return s.onerror=s.onload=n,s.href=e,document.head.appendChild(s),s},e=(t,e)=>{for(var o=document.getElementsByTagName("link"),r=0;r<o.length;r++){var s=o[r],n=s.getAttribute("data-href")||s.getAttribute("href");if("stylesheet"===s.rel&&(n===t||n===e))return s}var a=document.getElementsByTagName("style");for(r=0;r<a.length;r++){s=a[r],n=s.getAttribute("data-href");if(n===t||n===e)return s}},r=r=>new Promise(((s,n)=>{var a=o.miniCssF(r),c=o.p+a;if(e(a,c))return s();t(r,c,s,n)})),s={143:0};o.f.miniCss=(t,e)=>{var o={131:1,376:1,440:1,488:1,632:1,731:1};s[t]?e.push(s[t]):0!==s[t]&&o[t]&&e.push(s[t]=r(t).then((()=>{s[t]=0}),(e=>{throw delete s[t],e})))}})(),(()=>{var t={143:0};o.f.j=(e,r)=>{var s=o.o(t,e)?t[e]:void 0;if(0!==s)if(s)r.push(s[2]);else{var n=new Promise(((o,r)=>s=t[e]=[o,r]));r.push(s[2]=n);var a=o.p+o.u(e),c=new Error,i=r=>{if(o.o(t,e)&&(s=t[e],0!==s&&(t[e]=void 0),s)){var n=r&&("load"===r.type?"missing":r.type),a=r&&r.target&&r.target.src;c.message="Loading chunk "+e+" failed.\n("+n+": "+a+")",c.name="ChunkLoadError",c.type=n,c.request=a,s[1](c)}};o.l(a,i,"chunk-"+e,e)}},o.O.j=e=>0===t[e];var e=(e,r)=>{var s,n,[a,c,i]=r,l=0;if(a.some((e=>0!==t[e]))){for(s in c)o.o(c,s)&&(o.m[s]=c[s]);if(i)var u=i(o)}for(e&&e(r);l<a.length;l++)n=a[l],o.o(t,n)&&t[n]&&t[n][0](),t[n]=0;return o.O(u)},r=globalThis["webpackChunklauncher"]=globalThis["webpackChunklauncher"]||[];r.forEach(e.bind(null,0)),r.push=e.bind(null,r.push.bind(r))})();var r=o.O(void 0,[736],(()=>o(6485)));r=o.O(r)})();