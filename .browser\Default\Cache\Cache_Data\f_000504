{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Scanner<span id=\"scanner\" class=\"self-anchor\"><a class=\"perm\" href=\"#scanner\">#</a></span></h1><p style=\"color:inherit\">Scanner. </p><p style=\"color:inherit\">This is the scanning feature that is brought in when the application includes a software component for the scanning functionality. The functionality differences are listed below for various component inclusion scenario:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The bluetooth_feature_scanner component is included but neither bluetooth_feature_legacy_scanner nor bluetooth_feature_extended_scanner is included:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The scanner can scan advertising devices that use legacy or extended advertising PDUs.</p></li><li><p style=\"color:inherit\">The <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-scan-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_scan_report</a> event is used to report the received advertisements.</p></li></ul></li><li><p style=\"color:inherit\">The bluetooth_feature_legacy_scanner component is included but the bluetooth_feature_extended_scanner is not:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The scanner can only scan advertising devices that use legacy advertising PDUs.</p></li><li><p style=\"color:inherit\">The <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-legacy-advertisement-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_legacy_advertisement_report</a> event is used to report the received advertisements.</p></li></ul></li><li><p style=\"color:inherit\">The bluetooth_feature_extended_scanner component is included:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The scanner can scan advertising devices that use legacy or extended advertising PDUs.</p></li><li><p style=\"color:inherit\">The <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-legacy-advertisement-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_legacy_advertisement_report</a> event is used to report the received advertisements that use legacy advertising PDUs, and the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-extended-advertisement-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_extended_advertisement_report</a> event is used to report the received advertisements that use extended advertising PDUs.</p></li></ul></li><li><p style=\"color:inherit\">Either the bluetooth_feature_legacy_scanner or bluetooth_feature_extended_scanner component is included:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-set-timing\" target=\"_blank\" rel=\"\">sl_bt_scanner_set_timing</a> and <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-set-mode\" target=\"_blank\" rel=\"\">sl_bt_scanner_set_mode</a> commands are not available to use. They are superseded by the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-set-parameters\" target=\"_blank\" rel=\"\">sl_bt_scanner_set_parameters</a> command.</p></li><li><p style=\"color:inherit\">Calling a superseded command receives SL_STATUS_NOT_SUPPORTED error code. </p></li></ul></li></ul><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner-event-flag\" target=\"_blank\" rel=\"\">Event Type Flags of Advertisement Reports</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-legacy-advertisement-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_legacy_advertisement_report</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-extended-advertisement-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_extended_advertisement_report</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-scan-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_scan_report</a></p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-scanner-discover-mode-t\">sl_bt_scanner_discover_mode_t</a> {</div><div class=\"enum\">sl_bt_scanner_discover_limited = 0x0</div><div class=\"enum\">sl_bt_scanner_discover_generic = 0x1</div><div class=\"enum\">sl_bt_scanner_discover_observation = 0x2</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values indicate which Bluetooth discovery mode to use when scanning for advertising devices. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-scanner-scan-mode-t\">sl_bt_scanner_scan_mode_t</a> {</div><div class=\"enum\">sl_bt_scanner_scan_mode_passive = 0x0</div><div class=\"enum\">sl_bt_scanner_scan_mode_active = 0x1</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">The scanning modes. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-scanner-scan-phy-t\">sl_bt_scanner_scan_phy_t</a> {</div><div class=\"enum\">sl_bt_scanner_scan_phy_1m = 0x1</div><div class=\"enum\">sl_bt_scanner_scan_phy_coded = 0x4</div><div class=\"enum\">sl_bt_scanner_scan_phy_1m_and_coded = 0x5</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">The enum defines the scanning PHYs. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-scanner-data-status-t\">sl_bt_scanner_data_status_t</a> {</div><div class=\"enum\">sl_bt_scanner_data_status_complete = 0x0</div><div class=\"enum\">sl_bt_scanner_data_status_incomplete_more = 0x1</div><div class=\"enum\">sl_bt_scanner_data_status_incomplete_nomore = 0x2</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Defines the data completeness status types of an advertisement reported by the scanner. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-scanner-filter-policy-t\">sl_bt_scanner_filter_policy_t</a> {</div><div class=\"enum\">sl_bt_scanner_filter_policy_basic_unfiltered = 0x0</div><div class=\"enum\">sl_bt_scanner_filter_policy_basic_filtered = 0x1</div><div class=\"enum\">sl_bt_scanner_filter_policy_extended_unfiltered = 0x2</div><div class=\"enum\">sl_bt_scanner_filter_policy_extended_filtered = 0x3</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">The scanning filter policy setting determines which advertisements and scan responses are delivered to the application when scanning. See the Bluetooth Core specification Volume 6, Part B, Section 4.3.3 \"Scanning filter\npolicy\" for a detailed description of this setting. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-scanner-set-parameters\">sl_bt_scanner_set_parameters</a>(uint8_t mode, uint16_t interval, uint16_t window)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-scanner-set-parameters-and-filter\">sl_bt_scanner_set_parameters_and_filter</a>(uint8_t mode, uint16_t interval, uint16_t window, uint32_t flags, uint8_t filter_policy)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-scanner-stop\">sl_bt_scanner_stop</a>()</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-scanner-set-timing\">sl_bt_scanner_set_timing</a>(uint8_t phys, uint16_t scan_interval, uint16_t scan_window)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-scanner-set-mode\">sl_bt_scanner_set_mode</a>(uint8_t phys, uint8_t scan_mode)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-scanner-start\">sl_bt_scanner_start</a>(uint8_t scanning_phy, uint8_t discover_mode)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-scanner-set-parameters-id\">sl_bt_cmd_scanner_set_parameters_id</a> 0x06050020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-scanner-set-parameters-and-filter-id\">sl_bt_cmd_scanner_set_parameters_and_filter_id</a> 0x07050020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-scanner-stop-id\">sl_bt_cmd_scanner_stop_id</a> 0x05050020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-scanner-set-timing-id\">sl_bt_cmd_scanner_set_timing_id</a> 0x01050020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-scanner-set-mode-id\">sl_bt_cmd_scanner_set_mode_id</a> 0x02050020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-scanner-start-id\">sl_bt_cmd_scanner_start_id</a> 0x03050020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-scanner-set-parameters-id\">sl_bt_rsp_scanner_set_parameters_id</a> 0x06050020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-scanner-set-parameters-and-filter-id\">sl_bt_rsp_scanner_set_parameters_and_filter_id</a> 0x07050020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-scanner-stop-id\">sl_bt_rsp_scanner_stop_id</a> 0x05050020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-scanner-set-timing-id\">sl_bt_rsp_scanner_set_timing_id</a> 0x01050020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-scanner-set-mode-id\">sl_bt_rsp_scanner_set_mode_id</a> 0x02050020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-scanner-start-id\">sl_bt_rsp_scanner_start_id</a> 0x03050020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>sl_bt_scanner_discover_mode_t<span id=\"sl-bt-scanner-discover-mode-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-scanner-discover-mode-t\">#</a></span></h3><blockquote>sl_bt_scanner_discover_mode_t</blockquote><p style=\"color:inherit\">These values indicate which Bluetooth discovery mode to use when scanning for advertising devices. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_scanner_discover_limited</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Discover only limited discoverable devices. </p></td></tr><tr><td class=\"fieldname\">sl_bt_scanner_discover_generic</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Discover limited and general discoverable devices. </p></td></tr><tr><td class=\"fieldname\">sl_bt_scanner_discover_observation</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) Discover non-discoverable, limited and general discoverable devices. </p></td></tr></tbody></table><br><div>Definition at line <code>3654</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_scanner_scan_mode_t<span id=\"sl-bt-scanner-scan-mode-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-scanner-scan-mode-t\">#</a></span></h3><blockquote>sl_bt_scanner_scan_mode_t</blockquote><p style=\"color:inherit\">The scanning modes. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_scanner_scan_mode_passive</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Passive scanning mode where the device only listens to advertising packets and does not transmit packets </p></td></tr><tr><td class=\"fieldname\">sl_bt_scanner_scan_mode_active</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Active scanning mode where the device sends out a scan request packet upon receiving a scannable advertising packet from a remote device and listens to the scan response packet from the remote device </p></td></tr></tbody></table><br><div>Definition at line <code>3668</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_scanner_scan_phy_t<span id=\"sl-bt-scanner-scan-phy-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-scanner-scan-phy-t\">#</a></span></h3><blockquote>sl_bt_scanner_scan_phy_t</blockquote><p style=\"color:inherit\">The enum defines the scanning PHYs. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_scanner_scan_phy_1m</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Initiate the scanning on the 1M PHY </p></td></tr><tr><td class=\"fieldname\">sl_bt_scanner_scan_phy_coded</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x4) Initiate the scanning on the Coded PHY </p></td></tr><tr><td class=\"fieldname\">sl_bt_scanner_scan_phy_1m_and_coded</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x5) Simultaneous scanning by initiating the scanning on the 1M and Coded PHY alternatively </p></td></tr></tbody></table><br><div>Definition at line <code>3686</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_scanner_data_status_t<span id=\"sl-bt-scanner-data-status-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-scanner-data-status-t\">#</a></span></h3><blockquote>sl_bt_scanner_data_status_t</blockquote><p style=\"color:inherit\">Defines the data completeness status types of an advertisement reported by the scanner. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_scanner_data_status_complete</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) All data of the advertisement has been reported. </p></td></tr><tr><td class=\"fieldname\">sl_bt_scanner_data_status_incomplete_more</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Data of the advertisement is incomplete in this event, and more data will come in new events. </p></td></tr><tr><td class=\"fieldname\">sl_bt_scanner_data_status_incomplete_nomore</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) Data of the advertisement is incomplete in this event, but no more data will come, i.e., the data of the advertisement is truncated. </p></td></tr></tbody></table><br><div>Definition at line <code>3701</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_scanner_filter_policy_t<span id=\"sl-bt-scanner-filter-policy-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-scanner-filter-policy-t\">#</a></span></h3><blockquote>sl_bt_scanner_filter_policy_t</blockquote><p style=\"color:inherit\">The scanning filter policy setting determines which advertisements and scan responses are delivered to the application when scanning. See the Bluetooth Core specification Volume 6, Part B, Section 4.3.3 \"Scanning filter\npolicy\" for a detailed description of this setting. </p><p style=\"color:inherit\">Note that some filter policies require the application to include additional Bluetooth feature components. Filter policies that use the Filter Accept List require that the application has included the bluetooth_feature_accept_list component. Filter policies that require the Bluetooth controller to resolve a Resolvable Private Address require that the application has included the bluetooth_feature_resolving_list component. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_scanner_filter_policy_basic_unfiltered</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Advertising and scan response PDUs are processed from all devices. For directed advertising, the target address must additionally match the identity address of the local device or be a Resolvable Private Address that is resolved to the local device by the Bluetooth controller. </p></td></tr><tr><td class=\"fieldname\">sl_bt_scanner_filter_policy_basic_filtered</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Advertising and scan response PDUs are processed only from devices that the application has added to the Filter Accept List. For directed advertising, the target address must additionally match the identity address of the local device or be a Resolvable Private Address that is resolved to the local device by the Bluetooth controller. </p></td></tr><tr><td class=\"fieldname\">sl_bt_scanner_filter_policy_extended_unfiltered</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) Advertising and scan response PDUs are processed from all devices. For directed advertising, the target address must additionally match the identity address of the local device or be any Resolvable Private Address. </p></td></tr><tr><td class=\"fieldname\">sl_bt_scanner_filter_policy_extended_filtered</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x3) Advertising and scan response PDUs are processed only from devices that the application has added to the Filter Accept List. For directed advertising, the target address must additionally match the identity address of the local device or be any Resolvable Private Address. </p></td></tr></tbody></table><br><div>Definition at line <code>3735</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_scanner_set_parameters<span id=\"sl-bt-scanner-set-parameters\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-scanner-set-parameters\">#</a></span></h3><blockquote>sl_status_t sl_bt_scanner_set_parameters (uint8_t mode, uint16_t interval, uint16_t window)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-scan-mode-t\" target=\"_blank\" rel=\"\">sl_bt_scanner_scan_mode_t</a>.</p><p style=\"color:inherit\">Passive or active scan. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_mode_passive (0x0):</strong> Passive scanning mode where the device only listens to advertising packets and does not transmit packets</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_mode_active (0x1):</strong> Active scanning mode where the device sends out a scan request packet upon receiving a scannable advertising packet from a remote device and listens to the scan response packet from the remote device</p></li></ul><p style=\"color:inherit\">Default value: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-scan-mode-passive\" target=\"_blank\" rel=\"\">sl_bt_scanner_scan_mode_passive</a>. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">interval</td><td><p style=\"color:inherit\">The time interval when the device starts its last scan until it begins the subsequent scan. In other words, how often to scan</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 0.625 ms</p></li><li><p style=\"color:inherit\">Range: 0x0004 to 0xFFFF</p></li><li><p style=\"color:inherit\">Time Range: 2.5 ms to 40.96 s</p></li></ul><p style=\"color:inherit\">Default value: 10 ms</p><p style=\"color:inherit\">A variable delay occurs when switching channels at the end of each scanning interval, which is included in the scanning interval time. During the switch time, advertising packets are not received by the device. The switch time variation is use case dependent. For example, if scanning while keeping active connections, the channel switch time might be longer than when scanning without any active connections. Increasing the scanning interval reduces the amount of time in which the device can't receive advertising packets because it switches channels less often.</p><p style=\"color:inherit\">After every scan interval, the scanner changes the frequency at which it operates. It cycles through all three advertising channels in a round robin fashion. According to the specification, all three channels must be used by a scanner. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">window</td><td><p style=\"color:inherit\">The scan window, i.e., the duration of the scan, which must be less than or equal to the <code>interval</code></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 0.625 ms</p></li><li><p style=\"color:inherit\">Range: 0x0004 to 0xFFFF</p></li><li><p style=\"color:inherit\">Time Range: 2.5 ms to 40.96 s</p></li></ul><p style=\"color:inherit\">Default value: 10 ms</p><p style=\"color:inherit\">Note that the packet reception is aborted if it's started just before the scan window ends. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Set scan parameters for subsequent scanning operations. If the device is currently scanning, new parameters will take effect when scanning is restarted.</p><p style=\"color:inherit\">This command sets the scanning filter policy to the default value <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-filter-policy-basic-unfiltered\" target=\"_blank\" rel=\"\">sl_bt_scanner_filter_policy_basic_unfiltered</a>. Use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-set-parameters-and-filter\" target=\"_blank\" rel=\"\">sl_bt_scanner_set_parameters_and_filter</a> to set a specific scanning filter policy.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>4319</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_scanner_set_parameters_and_filter<span id=\"sl-bt-scanner-set-parameters-and-filter\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-scanner-set-parameters-and-filter\">#</a></span></h3><blockquote>sl_status_t sl_bt_scanner_set_parameters_and_filter (uint8_t mode, uint16_t interval, uint16_t window, uint32_t flags, uint8_t filter_policy)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-scan-mode-t\" target=\"_blank\" rel=\"\">sl_bt_scanner_scan_mode_t</a>.</p><p style=\"color:inherit\">Passive or active scan. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_mode_passive (0x0):</strong> Passive scanning mode where the device only listens to advertising packets and does not transmit packets</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_mode_active (0x1):</strong> Active scanning mode where the device sends out a scan request packet upon receiving a scannable advertising packet from a remote device and listens to the scan response packet from the remote device</p></li></ul><p style=\"color:inherit\">Default value: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-scan-mode-passive\" target=\"_blank\" rel=\"\">sl_bt_scanner_scan_mode_passive</a>. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">interval</td><td><p style=\"color:inherit\">The time interval when the device starts its last scan until it begins the subsequent scan. In other words, how often to scan</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 0.625 ms</p></li><li><p style=\"color:inherit\">Range: 0x0004 to 0xFFFF</p></li><li><p style=\"color:inherit\">Time Range: 2.5 ms to 40.96 s</p></li></ul><p style=\"color:inherit\">Default value: 10 ms</p><p style=\"color:inherit\">A variable delay occurs when switching channels at the end of each scanning interval, which is included in the scanning interval time. During the switch time, advertising packets are not received by the device. The switch time variation is use case dependent. For example, if scanning while keeping active connections, the channel switch time might be longer than when scanning without any active connections. Increasing the scanning interval reduces the amount of time in which the device can't receive advertising packets because it switches channels less often.</p><p style=\"color:inherit\">After every scan interval, the scanner changes the frequency at which it operates. It cycles through all three advertising channels in a round robin fashion. According to the specification, all three channels must be used by a scanner. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">window</td><td><p style=\"color:inherit\">The scan window, i.e., the duration of the scan, which must be less than or equal to the <code>interval</code></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 0.625 ms</p></li><li><p style=\"color:inherit\">Range: 0x0004 to 0xFFFF</p></li><li><p style=\"color:inherit\">Time Range: 2.5 ms to 40.96 s</p></li></ul><p style=\"color:inherit\">Default value: 10 ms</p><p style=\"color:inherit\">Note that the packet reception is aborted if it's started just before the scan window ends. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">No flags are currently defined. Set this parameter to 0. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">filter_policy</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-filter-policy-t\" target=\"_blank\" rel=\"\">sl_bt_scanner_filter_policy_t</a>.</p><p style=\"color:inherit\">The scanning filter policy to use when scanning is started. The filter policy determines which advertisements and scan responses are delivered to the application. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_scanner_filter_policy_basic_unfiltered (0x0):</strong> Advertising and scan response PDUs are processed from all devices. For directed advertising, the target address must additionally match the identity address of the local device or be a Resolvable Private Address that is resolved to the local device by the Bluetooth controller.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_filter_policy_basic_filtered (0x1):</strong> Advertising and scan response PDUs are processed only from devices that the application has added to the Filter Accept List. For directed advertising, the target address must additionally match the identity address of the local device or be a Resolvable Private Address that is resolved to the local device by the Bluetooth controller.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_filter_policy_extended_unfiltered (0x2):</strong> Advertising and scan response PDUs are processed from all devices. For directed advertising, the target address must additionally match the identity address of the local device or be any Resolvable Private Address.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_filter_policy_extended_filtered (0x3):</strong> Advertising and scan response PDUs are processed only from devices that the application has added to the Filter Accept List. For directed advertising, the target address must additionally match the identity address of the local device or be any Resolvable Private Address.</p></li></ul><p style=\"color:inherit\">Default value: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-filter-policy-basic-unfiltered\" target=\"_blank\" rel=\"\">sl_bt_scanner_filter_policy_basic_unfiltered</a></p></td></tr></tbody></table></div><p style=\"color:inherit\">Set scan parameters and the scanning filter policy for subsequent scanning operations. If the device is currently scanning, new parameters will take effect when scanning is restarted.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>4413</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_scanner_stop<span id=\"sl-bt-scanner-stop\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-scanner-stop\">#</a></span></h3><blockquote>sl_status_t sl_bt_scanner_stop ()</blockquote><p style=\"color:inherit\">Stop scanning for advertising devices. For more information about the discovery, see the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-start\" target=\"_blank\" rel=\"\">sl_bt_scanner_start</a> command.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>4428</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_scanner_set_timing<span id=\"sl-bt-scanner-set-timing\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-scanner-set-timing\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_scanner_set_timing (uint8_t phys, uint16_t scan_interval, uint16_t scan_window)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">phys</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-scan-phy-t\" target=\"_blank\" rel=\"\">sl_bt_scanner_scan_phy_t</a>. The scanning PHY(s) the setting is set for. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_phy_1m (0x1):</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_phy_coded (0x4):</strong> Coded PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_phy_1m_and_coded (0x5):</strong> 1M and Coded PHYs </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">scan_interval</td><td><p style=\"color:inherit\">Scan interval is defined as the time interval when the device starts its last scan until it begins the subsequent scan. In other words, how often to scan</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 0.625 ms</p></li><li><p style=\"color:inherit\">Range: 0x0004 to 0xFFFF</p></li><li><p style=\"color:inherit\">Time Range: 2.5 ms to 40.96 s</p></li></ul><p style=\"color:inherit\">Default value: 10 ms</p><p style=\"color:inherit\">A variable delay occurs when switching channels at the end of each scanning interval, which is included in the scanning interval time. During the switch time, advertising packets are not received by the device. The switch time variation is use case-dependent. For example, if scanning while keeping active connections, the channel switch time might be longer than when scanning without any active connections. Increasing the scanning interval reduces the amount of time in which the device can't receive advertising packets because it switches channels less often.</p><p style=\"color:inherit\">After every scan interval, the scanner changes the frequency at which it operates. It cycles through all three advertising channels in a round robin fashion. According to the specification, all three channels must be used by a scanner. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">scan_window</td><td><p style=\"color:inherit\">Scan window defines the duration of the scan which must be less than or equal to the <code>scan_interval</code></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Time = Value x 0.625 ms</p></li><li><p style=\"color:inherit\">Range: 0x0004 to 0xFFFF</p></li><li><p style=\"color:inherit\">Time Range: 2.5 ms to 40.96 s</p></li></ul><p style=\"color:inherit\">Default value: 10 ms Note that the packet reception is aborted if it's started just before the scan window ends. </p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-set-parameters\" target=\"_blank\" rel=\"\">sl_bt_scanner_set_parameters</a> command.</p><p style=\"color:inherit\">Set the scanning timing parameters on the specified PHY(s). If the device is currently scanning, new parameters will take effect when scanning is restarted.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>4482</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_scanner_set_mode<span id=\"sl-bt-scanner-set-mode\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-scanner-set-mode\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_scanner_set_mode (uint8_t phys, uint8_t scan_mode)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">phys</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-scan-phy-t\" target=\"_blank\" rel=\"\">sl_bt_scanner_scan_phy_t</a>. The scanning PHY(s) the setting is set for. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_phy_1m (0x1):</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_phy_coded (0x4):</strong> Coded PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_phy_1m_and_coded (0x5):</strong> 1M and Coded PHYs </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">scan_mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-scan-mode-t\" target=\"_blank\" rel=\"\">sl_bt_scanner_scan_mode_t</a>.</p><p style=\"color:inherit\">The scan mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_mode_passive (0x0):</strong> Passive scanning mode where the device only listens to advertising packets and does not transmit packets</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_mode_active (0x1):</strong> Active scanning mode where the device sends out a scan request packet upon receiving a scannable advertising packet from a remote device and listens to the scan response packet from the remote device</p></li></ul><p style=\"color:inherit\">Default value: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-scan-mode-passive\" target=\"_blank\" rel=\"\">sl_bt_scanner_scan_mode_passive</a>. </p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-set-parameters\" target=\"_blank\" rel=\"\">sl_bt_scanner_set_parameters</a> command.</p><p style=\"color:inherit\">Set the scan mode on the specified PHY(s). If the device is currently scanning, new parameters will take effect when scanning is restarted.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>4517</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_scanner_start<span id=\"sl-bt-scanner-start\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-scanner-start\">#</a></span></h3><blockquote>sl_status_t sl_bt_scanner_start (uint8_t scanning_phy, uint8_t discover_mode)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">scanning_phy</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-scan-phy-t\" target=\"_blank\" rel=\"\">sl_bt_scanner_scan_phy_t</a>.</p><p style=\"color:inherit\">The scanning PHY(s).</p><p style=\"color:inherit\">In simultaneous scanning, the stack alternates the scanning on two PHYs by switching the PHY at every scan interval. When a timing parameter is set differently on 1M and Coded PHY, the stack chooses the most relaxed value for both PHYs during simultaneous scanning, i.e., the largest scan interval or the smallest scan window. If one PHY is set to passive scanning and the other to active scanning, passive scanning is chosen for simultaneous scanning. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_phy_1m (0x1):</strong> Initiate the scanning on the 1M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_phy_coded (0x4):</strong> Initiate the scanning on the Coded PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_scan_phy_1m_and_coded (0x5):</strong> Simultaneous scanning by initiating the scanning on the 1M and Coded PHY alternatively </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">discover_mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-discover-mode-t\" target=\"_blank\" rel=\"\">sl_bt_scanner_discover_mode_t</a>. Bluetooth discovery Mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_scanner_discover_limited (0x0):</strong> Discover only limited discoverable devices.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_discover_generic (0x1):</strong> Discover limited and general discoverable devices.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_scanner_discover_observation (0x2):</strong> Discover non-discoverable, limited and general discoverable devices.</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Start the GAP discovery procedure to scan for advertising devices that use legacy or extended advertising PDUs. To cancel an ongoing discovery procedure, use the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-stop\" target=\"_blank\" rel=\"\">sl_bt_scanner_stop</a> command.</p><p style=\"color:inherit\">The invalid parameter error will be returned if the value of scanning PHYs is invalid or the device does not support a PHY.</p><p style=\"color:inherit\">Received advertising packets are not filtered in any way, so multiple events will be received for every advertising device in range.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-scan-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_scan_report</a> - This event is triggered for reporting a received advertisement if the application includes the bluetooth_feature_scanner component but does not include any other scanner component.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-legacy-advertisement-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_legacy_advertisement_report</a> - This event is triggered for reporting a received advertisement that uses legacy advertising PDUs if the application includes the bluetooth_feature_legacy_scanner or bluetooth_feature_extended_scanner component.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-extended-advertisement-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_extended_advertisement_report</a> - This event is triggered for reporting a received advertisement that uses extended advertising PDUs if the application includes the bluetooth_feature_extended_scanner component. </p></li></ul><br><div>Definition at line <code>4577</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_scanner_set_parameters_id<span id=\"sl-bt-cmd-scanner-set-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-scanner-set-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_scanner_set_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x06050020</pre><br><div>Definition at line <code>3637</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_scanner_set_parameters_and_filter_id<span id=\"sl-bt-cmd-scanner-set-parameters-and-filter-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-scanner-set-parameters-and-filter-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_scanner_set_parameters_and_filter_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x07050020</pre><br><div>Definition at line <code>3638</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_scanner_stop_id<span id=\"sl-bt-cmd-scanner-stop-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-scanner-stop-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_scanner_stop_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x05050020</pre><br><div>Definition at line <code>3639</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_scanner_set_timing_id<span id=\"sl-bt-cmd-scanner-set-timing-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-scanner-set-timing-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_scanner_set_timing_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01050020</pre><br><div>Definition at line <code>3640</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_scanner_set_mode_id<span id=\"sl-bt-cmd-scanner-set-mode-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-scanner-set-mode-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_scanner_set_mode_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02050020</pre><br><div>Definition at line <code>3641</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_scanner_start_id<span id=\"sl-bt-cmd-scanner-start-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-scanner-start-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_scanner_start_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03050020</pre><br><div>Definition at line <code>3642</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_scanner_set_parameters_id<span id=\"sl-bt-rsp-scanner-set-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-scanner-set-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_scanner_set_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x06050020</pre><br><div>Definition at line <code>3643</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_scanner_set_parameters_and_filter_id<span id=\"sl-bt-rsp-scanner-set-parameters-and-filter-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-scanner-set-parameters-and-filter-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_scanner_set_parameters_and_filter_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x07050020</pre><br><div>Definition at line <code>3644</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_scanner_stop_id<span id=\"sl-bt-rsp-scanner-stop-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-scanner-stop-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_scanner_stop_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x05050020</pre><br><div>Definition at line <code>3645</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_scanner_set_timing_id<span id=\"sl-bt-rsp-scanner-set-timing-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-scanner-set-timing-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_scanner_set_timing_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01050020</pre><br><div>Definition at line <code>3646</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_scanner_set_mode_id<span id=\"sl-bt-rsp-scanner-set-mode-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-scanner-set-mode-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_scanner_set_mode_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02050020</pre><br><div>Definition at line <code>3647</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_scanner_start_id<span id=\"sl-bt-rsp-scanner-start-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-scanner-start-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_scanner_start_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03050020</pre><br><div>Definition at line <code>3648</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner", "status": "success"}