{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Extended Advertiser<span id=\"extended-advertiser\" class=\"self-anchor\"><a class=\"perm\" href=\"#extended-advertiser\">#</a></span></h1><p style=\"color:inherit\">Extended Advertiser. </p><p style=\"color:inherit\">The commands and events in this class are related to extended advertising functionalities in GAP peripheral and broadcaster roles. </p><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser-flags\" target=\"_blank\" rel=\"\">Extended Advertising Configuration Flags</a></p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-extended-advertiser-connection-mode-t\">sl_bt_extended_advertiser_connection_mode_t</a> {</div><div class=\"enum\">sl_bt_extended_advertiser_non_connectable = 0x0</div><div class=\"enum\">sl_bt_extended_advertiser_scannable = 0x3</div><div class=\"enum\">sl_bt_extended_advertiser_connectable = 0x4</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values define the available connection modes in extended advertising. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-extended-advertiser-set-phy\">sl_bt_extended_advertiser_set_phy</a>(uint8_t advertising_set, uint8_t primary_phy, uint8_t secondary_phy)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-extended-advertiser-set-data\">sl_bt_extended_advertiser_set_data</a>(uint8_t advertising_set, size_t data_len, const uint8_t *data)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-extended-advertiser-set-long-data\">sl_bt_extended_advertiser_set_long_data</a>(uint8_t advertising_set)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-extended-advertiser-generate-data\">sl_bt_extended_advertiser_generate_data</a>(uint8_t advertising_set, uint8_t discover)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-extended-advertiser-start\">sl_bt_extended_advertiser_start</a>(uint8_t advertising_set, uint8_t connect, uint32_t flags)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-extended-advertiser-start-directed\">sl_bt_extended_advertiser_start_directed</a>(uint8_t advertising_set, uint8_t connect, uint32_t flags, bd_addr peer_addr, uint8_t peer_addr_type)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-extended-advertiser-set-phy-id\">sl_bt_cmd_extended_advertiser_set_phy_id</a> 0x00570020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-extended-advertiser-set-data-id\">sl_bt_cmd_extended_advertiser_set_data_id</a> 0x01570020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-extended-advertiser-set-long-data-id\">sl_bt_cmd_extended_advertiser_set_long_data_id</a> 0x02570020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-extended-advertiser-generate-data-id\">sl_bt_cmd_extended_advertiser_generate_data_id</a> 0x03570020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-extended-advertiser-start-id\">sl_bt_cmd_extended_advertiser_start_id</a> 0x04570020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-extended-advertiser-start-directed-id\">sl_bt_cmd_extended_advertiser_start_directed_id</a> 0x05570020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-extended-advertiser-set-phy-id\">sl_bt_rsp_extended_advertiser_set_phy_id</a> 0x00570020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-extended-advertiser-set-data-id\">sl_bt_rsp_extended_advertiser_set_data_id</a> 0x01570020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-extended-advertiser-set-long-data-id\">sl_bt_rsp_extended_advertiser_set_long_data_id</a> 0x02570020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-extended-advertiser-generate-data-id\">sl_bt_rsp_extended_advertiser_generate_data_id</a> 0x03570020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-extended-advertiser-start-id\">sl_bt_rsp_extended_advertiser_start_id</a> 0x04570020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-extended-advertiser-start-directed-id\">sl_bt_rsp_extended_advertiser_start_directed_id</a> 0x05570020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>sl_bt_extended_advertiser_connection_mode_t<span id=\"sl-bt-extended-advertiser-connection-mode-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-extended-advertiser-connection-mode-t\">#</a></span></h3><blockquote>sl_bt_extended_advertiser_connection_mode_t</blockquote><p style=\"color:inherit\">These values define the available connection modes in extended advertising. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_extended_advertiser_non_connectable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Non-connectable and non-scannable extended advertising </p></td></tr><tr><td class=\"fieldname\">sl_bt_extended_advertiser_scannable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x3) Scannable extended advertising </p></td></tr><tr><td class=\"fieldname\">sl_bt_extended_advertiser_connectable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x4) Connectable extended advertising </p></td></tr></tbody></table><br><div>Definition at line <code>2644</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_extended_advertiser_set_phy<span id=\"sl-bt-extended-advertiser-set-phy\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-extended-advertiser-set-phy\">#</a></span></h3><blockquote>sl_status_t sl_bt_extended_advertiser_set_phy (uint8_t advertising_set, uint8_t primary_phy, uint8_t secondary_phy)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">primary_phy</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-phy-t\" target=\"_blank\" rel=\"\">sl_bt_gap_phy_t</a>. The PHY on which the advertising packets are transmitted on the primary advertising channel. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_1m (0x1):</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coded (0x4):</strong> Coded PHY, 125k (S=8)</p></li><li><p style=\"color:inherit\"><strong>Default</strong> : <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-phy-1m\" target=\"_blank\" rel=\"\">sl_bt_gap_phy_1m</a></p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">secondary_phy</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-phy-t\" target=\"_blank\" rel=\"\">sl_bt_gap_phy_t</a>. The PHY on which the advertising packets are transmitted on the secondary advertising channel. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_1m (0x1):</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_2m (0x2):</strong> 2M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coded (0x4):</strong> Coded PHY, 125k (S=8)</p></li><li><p style=\"color:inherit\"><strong>Default</strong> : <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-phy-1m\" target=\"_blank\" rel=\"\">sl_bt_gap_phy_1m</a></p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Set the primary and secondary advertising PHYs used for extended and periodic advertising on an advertising set. This setting will take effect next time extended or periodic advertising is enabled. When advertising on the LE Coded PHY, coding scheme S=8 is used. The SL_STATUS_INVALID_PARAMETER error is returned if a PHY value is invalid or the device does not support a given PHY.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2702</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_extended_advertiser_set_data<span id=\"sl-bt-extended-advertiser-set-data\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-extended-advertiser-set-data\">#</a></span></h3><blockquote>sl_status_t sl_bt_extended_advertiser_set_data (uint8_t advertising_set, size_t data_len, const uint8_t * data)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">data_len</td><td><p style=\"color:inherit\">Length of data in <code>data</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">data</td><td><p style=\"color:inherit\">Data to be set</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set user-defined data for extended advertising. This overwrites the existing advertising data packet and scan response packet on this advertising set regardless of whether the data was set for the legacy or extended advertising. Maximum 191 bytes of data can be set for connectable extended advertising. Maximum 253 bytes of data can be set for non-connectable extended advertising. For setting longer advertising data, use command <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-set-long-data\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_set_long_data</a>.</p><p style=\"color:inherit\">If advertising mode is currently enabled, the new advertising data will be used immediately. Advertising mode can be enabled using command <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_start</a>.</p><p style=\"color:inherit\">The invalid parameter error is returned if the data is too long to fit into a single advertisement.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2730</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_extended_advertiser_set_long_data<span id=\"sl-bt-extended-advertiser-set-long-data\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-extended-advertiser-set-long-data\">#</a></span></h3><blockquote>sl_status_t sl_bt_extended_advertiser_set_long_data (uint8_t advertising_set)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set long user-defined data for extended advertising. This overwrites the existing advertising data packet and scan response packet on this advertising set regardless of whether the data was set for the legacy or extended advertising.</p><p style=\"color:inherit\">Prior to calling this command, add data to the buffer with one or multiple calls to <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-system#sl-bt-system-data-buffer-write\" target=\"_blank\" rel=\"\">sl_bt_system_data_buffer_write</a>. When this command is called, the data in the system data buffer is extracted as the advertising data. The buffer will be emptied after this command regardless of the completion status.</p><p style=\"color:inherit\">Maximum 191 bytes of data can be set for connectable extended advertising. Maximum 1650 bytes of data can be set for non-connectable extended advertising. Advertising parameters may limit the amount of data that can be sent in a single advertisement. See <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-set-data\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_set_data</a> for more details.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2758</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_extended_advertiser_generate_data<span id=\"sl-bt-extended-advertiser-generate-data\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-extended-advertiser-generate-data\">#</a></span></h3><blockquote>sl_status_t sl_bt_extended_advertiser_generate_data (uint8_t advertising_set, uint8_t discover)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">discover</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-discovery-mode-t\" target=\"_blank\" rel=\"\">sl_bt_advertiser_discovery_mode_t</a>. The discovery mode for the Flags data field in the packet. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_non_discoverable (0x0):</strong> Not discoverable</p></li><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_limited_discoverable (0x1):</strong> Discoverable by both limited and general discovery procedures</p></li><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_general_discoverable (0x2):</strong> Discoverable by the general discovery procedure</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Ask the stack to generate the extended advertising data on an advertising set. Alternatively, user-defined advertising data can be set using the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-set-data\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_set_data</a> command.</p><p style=\"color:inherit\">This overwrites the existing advertising data packet and scan response packet on this advertising set regardless of whether the data was set for the legacy or extended advertising.</p><p style=\"color:inherit\">If advertising mode is currently enabled, the new advertising data will be used immediately. To enable advertising mode, use command <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_start</a>.</p><p style=\"color:inherit\">See <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-generate-data\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_generate_data</a> for the advertising data generation logic.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2789</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_extended_advertiser_start<span id=\"sl-bt-extended-advertiser-start\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-extended-advertiser-start\">#</a></span></h3><blockquote>sl_status_t sl_bt_extended_advertiser_start (uint8_t advertising_set, uint8_t connect, uint32_t flags)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">connect</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-connection-mode-t\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_connection_mode_t</a>. Connection mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_extended_advertiser_non_connectable (0x0):</strong> Non-connectable and non-scannable extended advertising</p></li><li><p style=\"color:inherit\"><strong>sl_bt_extended_advertiser_scannable (0x3):</strong> Scannable extended advertising</p></li><li><p style=\"color:inherit\"><strong>sl_bt_extended_advertiser_connectable (0x4):</strong> Connectable extended advertising </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">Additional extended advertising options. Value: 0 or bitmask of <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser-flags\" target=\"_blank\" rel=\"\">Extended Advertising Configuration Flags</a></p></td></tr></tbody></table></div><p style=\"color:inherit\">Start undirected extended advertising on an advertising set with the specified connection mode. Use <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-stop\" target=\"_blank\" rel=\"\">sl_bt_advertiser_stop</a> to stop the advertising.</p><p style=\"color:inherit\">Use the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-set-data\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_set_data</a> or <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-generate-data\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_generate_data</a> command to set the advertising data before calling this command. Advertising data is added into the scan response packet if the connection mode is scannable. Otherwise, data is in the advertising data packet.</p><p style=\"color:inherit\">The number of concurrent connectable advertisings is limited by the connection number configuration. See <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_start</a> for more details.</p><p style=\"color:inherit\">This command fails with the invalid parameter error if the advertising uses a non-resolvable random address but the connection mode is <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-connectable\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_connectable</a>.</p><p style=\"color:inherit\">This command returns the error SL_STATUS_BT_CONTROLLER_COMMAND_DISALLOWED when the connection mode is <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-scannable\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_scannable</a> but no scan response data is provided.</p><p style=\"color:inherit\">Event <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> will be received when a remote device opens a connection to the advertiser on this advertising set. As a result, the advertising stops.</p><p style=\"color:inherit\">Event <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-advertiser-timeout\" target=\"_blank\" rel=\"\">sl_bt_evt_advertiser_timeout</a> will be received when the number of advertising events set by <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-timing\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_timing</a> command is done and the advertising has stopped.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-advertiser-timeout\" target=\"_blank\" rel=\"\">sl_bt_evt_advertiser_timeout</a> - Triggered when the number of advertising events set by <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-timing\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_timing</a> command is done and advertising has stopped on an advertising set.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> - Triggered when a remote device opens a connection to the advertiser and the advertising has stopped. </p></li></ul><br><div>Definition at line <code>2846</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_extended_advertiser_start_directed<span id=\"sl-bt-extended-advertiser-start-directed\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-extended-advertiser-start-directed\">#</a></span></h3><blockquote>sl_status_t sl_bt_extended_advertiser_start_directed (uint8_t advertising_set, uint8_t connect, uint32_t flags, <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> peer_addr, uint8_t peer_addr_type)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">connect</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-connection-mode-t\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_connection_mode_t</a>. Connection mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_extended_advertiser_non_connectable (0x0):</strong> Non-connectable and non-scannable extended advertising</p></li><li><p style=\"color:inherit\"><strong>sl_bt_extended_advertiser_scannable (0x3):</strong> Scannable extended advertising</p></li><li><p style=\"color:inherit\"><strong>sl_bt_extended_advertiser_connectable (0x4):</strong> Connectable extended advertising </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">Additional extended advertising options. Value: 0 or bitmask of <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser-flags\" target=\"_blank\" rel=\"\">Extended Advertising Configuration Flags</a></p></td></tr><tr><td>[in]</td><td class=\"paramname\">peer_addr</td><td><p style=\"color:inherit\">Address of the peer target device the advertising is directed to </p></td></tr><tr><td>[in]</td><td class=\"paramname\">peer_addr_type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>.</p><p style=\"color:inherit\">Peer target device address type.</p><p style=\"color:inherit\">If the application does not include the bluetooth_feature_use_accurate_api_address_types component, <code>peer_addr_type</code> uses the following values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Public address</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Random address</p></li></ul><p style=\"color:inherit\">If the application includes the bluetooth_feature_use_accurate_api_address_types component, <code>peer_addr_type</code> uses enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a> values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address (0x0):</strong> Public device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_resolvable_address (0x2):</strong> Resolvable private random address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_nonresolvable_address (0x3):</strong> Non-resolvable private random address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address_resolved_from_rpa (0x4):</strong> Public identity address resolved from a resolvable private address (RPA)</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address_resolved_from_rpa (0x5):</strong> Static identity address resolved from a resolvable private address (RPA) </p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Start directed extended advertising on an advertising set with the specified peer target device and connection mode. Use <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-stop\" target=\"_blank\" rel=\"\">sl_bt_advertiser_stop</a> to stop the advertising.</p><p style=\"color:inherit\">The number of concurrent connectable advertisings is limited by the connection number configuration. See <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_start</a> for more details.</p><p style=\"color:inherit\">This command fails with the invalid parameter error if the advertising uses a non-resolvable random address but the connection mode is <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-connectable\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_connectable</a>.</p><p style=\"color:inherit\">Event <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> will be received when the target device opens a connection to the advertiser on this advertising set. As a result, the advertising stops.</p><p style=\"color:inherit\">Event <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-advertiser-timeout\" target=\"_blank\" rel=\"\">sl_bt_evt_advertiser_timeout</a> will be received when the advertising stops and no Bluetooth connection is opened to it.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-advertiser-timeout\" target=\"_blank\" rel=\"\">sl_bt_evt_advertiser_timeout</a> - Triggered when the number of advertising events set by <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-timing\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_timing</a> command is done and the advertising has stopped.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> - Triggered when a remote device opens a connection to the advertiser and the advertising has stopped. </p></li></ul><br><div>Definition at line <code>2920</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_extended_advertiser_set_phy_id<span id=\"sl-bt-cmd-extended-advertiser-set-phy-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-extended-advertiser-set-phy-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_extended_advertiser_set_phy_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00570020</pre><br><div>Definition at line <code>2627</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_extended_advertiser_set_data_id<span id=\"sl-bt-cmd-extended-advertiser-set-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-extended-advertiser-set-data-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_extended_advertiser_set_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01570020</pre><br><div>Definition at line <code>2628</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_extended_advertiser_set_long_data_id<span id=\"sl-bt-cmd-extended-advertiser-set-long-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-extended-advertiser-set-long-data-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_extended_advertiser_set_long_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02570020</pre><br><div>Definition at line <code>2629</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_extended_advertiser_generate_data_id<span id=\"sl-bt-cmd-extended-advertiser-generate-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-extended-advertiser-generate-data-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_extended_advertiser_generate_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03570020</pre><br><div>Definition at line <code>2630</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_extended_advertiser_start_id<span id=\"sl-bt-cmd-extended-advertiser-start-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-extended-advertiser-start-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_extended_advertiser_start_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x04570020</pre><br><div>Definition at line <code>2631</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_extended_advertiser_start_directed_id<span id=\"sl-bt-cmd-extended-advertiser-start-directed-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-extended-advertiser-start-directed-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_extended_advertiser_start_directed_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x05570020</pre><br><div>Definition at line <code>2632</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_extended_advertiser_set_phy_id<span id=\"sl-bt-rsp-extended-advertiser-set-phy-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-extended-advertiser-set-phy-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_extended_advertiser_set_phy_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00570020</pre><br><div>Definition at line <code>2633</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_extended_advertiser_set_data_id<span id=\"sl-bt-rsp-extended-advertiser-set-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-extended-advertiser-set-data-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_extended_advertiser_set_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01570020</pre><br><div>Definition at line <code>2634</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_extended_advertiser_set_long_data_id<span id=\"sl-bt-rsp-extended-advertiser-set-long-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-extended-advertiser-set-long-data-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_extended_advertiser_set_long_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02570020</pre><br><div>Definition at line <code>2635</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_extended_advertiser_generate_data_id<span id=\"sl-bt-rsp-extended-advertiser-generate-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-extended-advertiser-generate-data-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_extended_advertiser_generate_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03570020</pre><br><div>Definition at line <code>2636</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_extended_advertiser_start_id<span id=\"sl-bt-rsp-extended-advertiser-start-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-extended-advertiser-start-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_extended_advertiser_start_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x04570020</pre><br><div>Definition at line <code>2637</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_extended_advertiser_start_directed_id<span id=\"sl-bt-rsp-extended-advertiser-start-directed-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-extended-advertiser-start-directed-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_extended_advertiser_start_directed_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x05570020</pre><br><div>Definition at line <code>2638</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-extended-advertiser", "status": "success"}