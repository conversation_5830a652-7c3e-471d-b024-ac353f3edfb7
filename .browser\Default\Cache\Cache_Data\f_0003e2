{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>TEMPDRV  Temperature Driver<span id=\"tempdrv-temperature-driver\" class=\"self-anchor\"><a class=\"perm\" href=\"#tempdrv-temperature-driver\">#</a></span></h1><p style=\"color:inherit\">TEMPDRV Temperature Driver provides an interface and various functionalities to the EMU internal temperature sensor. </p><p style=\"color:inherit\">Subsequent sections provide more insight into TEMPDRV driver.</p><p style=\"color:inherit\"><br></p><h2>Introduction<span id=\"introduction\" class=\"self-anchor\"><a class=\"perm\" href=\"#introduction\">#</a></span></h2><p style=\"color:inherit\">TEMPDRV provides a user-friendly interface to the EMU internal temperature sensor, which is present on the EFR32 and some EFM32 devices. TEMPDRV supports application-specific callbacks at given temperature thresholds. EMU internal temperature sensor runs in energy modes EM0-EM4 and can wake up the core whenever temperature changes. Also, EMU temperature sensor runs continuously and measurements are taken every 250 ms.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">TEMPDRV uses the EMU peripheral and not the ADC peripheral. ADC contains another internal temperature sensor, which is not touched by the TEMPDRV.</p></li></ul><p style=\"color:inherit\">\nTEMPDRV provides an important errata fix for the EFR32 first generation devices when operating at high temperature environments (above 50°C). The \"EMU_E201 - High Temperature Operation\" errata is described in the EFR32 errata. To implement the errata fix in a user application, include the TEMPDRV and call <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-init\" target=\"_blank\" rel=\"\">TEMPDRV_Init()</a> at the start of the program. This will activate the errata fix code, which modifies registers based on changes in the EMU temperature. <br></p><h2>TEMPDRV Usage<span id=\"tempdrv-usage\" class=\"self-anchor\"><a class=\"perm\" href=\"#tempdrv-usage\">#</a></span></h2><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv-error-codes\" target=\"_blank\" rel=\"\">Error Codes</a></p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#tempdrv-limit-type-t\">TEMPDRV_LimitType_t</a> {</div><div class=\"enum\">TEMPDRV_LIMIT_LOW = 0</div><div class=\"enum\">TEMPDRV_LIMIT_HIGH = 1</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Temperature limit. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Typedefs<span id=\"typedef-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#typedef-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">typedef void(*</div><div class=\"column\"><div class=\"attributename\"><a href=\"#tempdrv-callback-t\">TEMPDRV_Callback_t</a>)(int8_t temp, TEMPDRV_LimitType_t limit)</div><div class=\"classdescription\"><p style=\"color:inherit\">TEMPDRV temperature limit callback function. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#emu-irq-handler\">EMU_IRQHandler</a>(void)</div><div class=\"classdescription\"><p style=\"color:inherit\">EMU Interrupt Handler. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#tempdrv-irq-handler\">TEMPDRV_IRQHandler</a>(void)</div><div class=\"classdescription\"><p style=\"color:inherit\">TEMPDRV IRQ Handler. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/ecode#ecode-t\" target=\"_blank\" rel=\"\">Ecode_t</a></div><div class=\"column\"><div class=\"attributename\"><a href=\"#tempdrv-init\">TEMPDRV_Init</a>(void)</div><div class=\"classdescription\"><p style=\"color:inherit\">Initialize the TEMP driver. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/ecode#ecode-t\" target=\"_blank\" rel=\"\">Ecode_t</a></div><div class=\"column\"><div class=\"attributename\"><a href=\"#tempdrv-de-init\">TEMPDRV_DeInit</a>(void)</div><div class=\"classdescription\"><p style=\"color:inherit\">De-initialize the TEMP driver. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/ecode#ecode-t\" target=\"_blank\" rel=\"\">Ecode_t</a></div><div class=\"column\"><div class=\"attributename\"><a href=\"#tempdrv-enable\">TEMPDRV_Enable</a>(bool enable)</div><div class=\"classdescription\"><p style=\"color:inherit\">Enable or disable the TEMP driver. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#tempdrv-get-active-callbacks\">TEMPDRV_GetActiveCallbacks</a>(TEMPDRV_LimitType_t limit)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get the number of active callbacks for a limit. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">int8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#tempdrv-get-temp\">TEMPDRV_GetTemp</a>(void)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get the current temperature. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/ecode#ecode-t\" target=\"_blank\" rel=\"\">Ecode_t</a></div><div class=\"column\"><div class=\"attributename\"><a href=\"#tempdrv-register-callback\">TEMPDRV_RegisterCallback</a>(int8_t temp, TEMPDRV_LimitType_t limit, TEMPDRV_Callback_t callback)</div><div class=\"classdescription\"><p style=\"color:inherit\">Register a callback in the TEMP driver. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/ecode#ecode-t\" target=\"_blank\" rel=\"\">Ecode_t</a></div><div class=\"column\"><div class=\"attributename\"><a href=\"#tempdrv-unregister-callback\">TEMPDRV_UnregisterCallback</a>(TEMPDRV_Callback_t callback)</div><div class=\"classdescription\"><p style=\"color:inherit\">Unregister a callback in the TEMP driver. </p></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>TEMPDRV_LimitType_t<span id=\"tempdrv-limit-type-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#tempdrv-limit-type-t\">#</a></span></h3><blockquote>TEMPDRV_LimitType_t</blockquote><p style=\"color:inherit\">Temperature limit. </p><p style=\"color:inherit\">This is used in the TEMPDRV to specify the direction of temperature change. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">TEMPDRV_LIMIT_LOW</td><td class=\"fielddescription\"><p style=\"color:inherit\">Temperature low. </p></td></tr><tr><td class=\"fieldname\">TEMPDRV_LIMIT_HIGH</td><td class=\"fielddescription\"><p style=\"color:inherit\">Temperature high. </p></td></tr></tbody></table><br><div>Definition at line <code>98</code> of file <code>platform/emdrv/tempdrv/inc/tempdrv.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Typedef Documentation<span id=\"typedef-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#typedef-definition\">#</a></span></h2><div><h3>TEMPDRV_Callback_t<span id=\"tempdrv-callback-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#tempdrv-callback-t\">#</a></span></h3><blockquote>typedef void(* TEMPDRV_Callback_t) (int8_t temp, TEMPDRV_LimitType_t limit) )(int8_t temp, TEMPDRV_LimitType_t limit)</blockquote><p style=\"color:inherit\">TEMPDRV temperature limit callback function. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">temp</td><td><p style=\"color:inherit\">The current temperature at the time when the EMU temperature triggers an interrupt. Note that this is not necessarily the same temperature as was specified when registering a callback.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">limit</td><td><p style=\"color:inherit\">The upper/lower limit reached </p></td></tr></tbody></table></div><p style=\"color:inherit\">Called from the interrupt context. The callback function is called when the current temperature is equal to or exceeds one of the temperature limits that have been registered with the driver.</p><br><div>Definition at line <code>120</code> of file <code>platform/emdrv/tempdrv/inc/tempdrv.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>EMU_IRQHandler<span id=\"emu-irq-handler\" class=\"self-anchor\"><a class=\"perm\" href=\"#emu-irq-handler\">#</a></span></h3><blockquote>void EMU_IRQHandler (void )</blockquote><p style=\"color:inherit\">EMU Interrupt Handler. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">The EMU_IRQHandler provided by TEMPDRV will call <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-irq-handler\" target=\"_blank\" rel=\"\">TEMPDRV_IRQHandler</a>. Configure EMU_CUSTOM_IRQ_HANDLER = true if the application wants to implement its own EMU_IRQHandler. This is typically needed if one of the non-temperature related EMU interrupt flags are in use. </p><br><div>Definition at line <code>106</code> of file <code>platform/emdrv/tempdrv/src/tempdrv.c</code></div><br></div><div><h3>TEMPDRV_IRQHandler<span id=\"tempdrv-irq-handler\" class=\"self-anchor\"><a class=\"perm\" href=\"#tempdrv-irq-handler\">#</a></span></h3><blockquote>void TEMPDRV_IRQHandler (void )</blockquote><p style=\"color:inherit\">TEMPDRV IRQ Handler. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">This IRQ Handler should be called from within the <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#emu-irq-handler\" target=\"_blank\" rel=\"\">EMU_IRQHandler</a> to enable TEMPDRV callbacks. This is included by default with EMU_CUSTOM_IRQ_HANDLER defined as false. </p><br><div>Definition at line <code>123</code> of file <code>platform/emdrv/tempdrv/src/tempdrv.c</code></div><br></div><div><h3>TEMPDRV_Init<span id=\"tempdrv-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#tempdrv-init\">#</a></span></h3><blockquote>Ecode_t TEMPDRV_Init (void )</blockquote><p style=\"color:inherit\">Initialize the TEMP driver. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">This will clear all the registered callbacks and enable the EMU IRQ in the NVIC. Calling this function will also enable the EMU_E201 errata fix for first generation Pearl, Jade and EFR32 devices.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv-error-codes#ecode-emdrv-tempdrv-ok\" target=\"_blank\" rel=\"\">ECODE_EMDRV_TEMPDRV_OK</a> on success. </p></li></ul><br><div>Definition at line <code>538</code> of file <code>platform/emdrv/tempdrv/src/tempdrv.c</code></div><br></div><div><h3>TEMPDRV_DeInit<span id=\"tempdrv-de-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#tempdrv-de-init\">#</a></span></h3><blockquote>Ecode_t TEMPDRV_DeInit (void )</blockquote><p style=\"color:inherit\">De-initialize the TEMP driver. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">This will clear all the registered callbacks and disable the EMU IRQ in the NVIC.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv-error-codes#ecode-emdrv-tempdrv-ok\" target=\"_blank\" rel=\"\">ECODE_EMDRV_TEMPDRV_OK</a> on success. </p></li></ul><br><div>Definition at line <code>570</code> of file <code>platform/emdrv/tempdrv/src/tempdrv.c</code></div><br></div><div><h3>TEMPDRV_Enable<span id=\"tempdrv-enable\" class=\"self-anchor\"><a class=\"perm\" href=\"#tempdrv-enable\">#</a></span></h3><blockquote>Ecode_t TEMPDRV_Enable (bool enable)</blockquote><p style=\"color:inherit\">Enable or disable the TEMP driver. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">enable</td><td><p style=\"color:inherit\">true to enable the TEMP driver, false to disable the TEMP driver.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv-error-codes#ecode-emdrv-tempdrv-ok\" target=\"_blank\" rel=\"\">ECODE_EMDRV_TEMPDRV_OK</a> on success. </p></li></ul><br><div>Definition at line <code>591</code> of file <code>platform/emdrv/tempdrv/src/tempdrv.c</code></div><br></div><div><h3>TEMPDRV_GetActiveCallbacks<span id=\"tempdrv-get-active-callbacks\" class=\"self-anchor\"><a class=\"perm\" href=\"#tempdrv-get-active-callbacks\">#</a></span></h3><blockquote>uint8_t TEMPDRV_GetActiveCallbacks (<a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-limit-type-t\" target=\"_blank\" rel=\"\">TEMPDRV_LimitType_t</a> limit)</blockquote><p style=\"color:inherit\">Get the number of active callbacks for a limit. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">limit</td><td><p style=\"color:inherit\">Limit type, refer to <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-limit-type-t\" target=\"_blank\" rel=\"\">TEMPDRV_LimitType_t</a>.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Number of active callbacks </p></li></ul><br><div>Definition at line <code>614</code> of file <code>platform/emdrv/tempdrv/src/tempdrv.c</code></div><br></div><div><h3>TEMPDRV_GetTemp<span id=\"tempdrv-get-temp\" class=\"self-anchor\"><a class=\"perm\" href=\"#tempdrv-get-temp\">#</a></span></h3><blockquote>int8_t TEMPDRV_GetTemp (void )</blockquote><p style=\"color:inherit\">Get the current temperature. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Current temperature in degrees Celsius. </p></li></ul><br><div>Definition at line <code>645</code> of file <code>platform/emdrv/tempdrv/src/tempdrv.c</code></div><br></div><div><h3>TEMPDRV_RegisterCallback<span id=\"tempdrv-register-callback\" class=\"self-anchor\"><a class=\"perm\" href=\"#tempdrv-register-callback\">#</a></span></h3><blockquote>Ecode_t TEMPDRV_RegisterCallback (int8_t temp, <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-limit-type-t\" target=\"_blank\" rel=\"\">TEMPDRV_LimitType_t</a> limit, <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-callback-t\" target=\"_blank\" rel=\"\">TEMPDRV_Callback_t</a> callback)</blockquote><p style=\"color:inherit\">Register a callback in the TEMP driver. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">temp</td><td><p style=\"color:inherit\">Temperature to trigger on given in number of °C.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">limit</td><td><p style=\"color:inherit\">Limit type, refer to <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-limit-type-t\" target=\"_blank\" rel=\"\">TEMPDRV_LimitType_t</a>. Using <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-limit-low\" target=\"_blank\" rel=\"\">TEMPDRV_LIMIT_LOW</a> will register a callback when the EMU temperature reaches <code>temp</code>°C or lower, and using <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-limit-high\" target=\"_blank\" rel=\"\">TEMPDRV_LIMIT_HIGH</a> will register a callback when the EMU temperature reaches <code>temp</code>°C or higher.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">callback</td><td><p style=\"color:inherit\">User defined function to call when temperature threshold is reached or passed.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function is used for registering an application callback when the temperature changes. Note that when calling this function an application must specify the direction of the temperature change, use <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-limit-low\" target=\"_blank\" rel=\"\">TEMPDRV_LIMIT_LOW</a> to receive a callback when the temperature drops below the specified temp and use <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-limit-high\" target=\"_blank\" rel=\"\">TEMPDRV_LIMIT_HIGH</a> to receive a callback when the temperature increases above the specified temp.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The user registered callback will be cleared once it's called. This means that the callback functions are not persistent, and have to be managed by the application. This feature can be used to implement a user controlled hysteresis. So for instance to register a callback at 50°C with a 5°C hysteresis you can first register a callback at 50°C or above using this function, and when the callback fires you can use this function again to register a callback when the temperature decreases to 45°C or below. Each time a callback fires you only need to call the <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-register-callback\" target=\"_blank\" rel=\"\">TEMPDRV_RegisterCallback()</a> function, there is no need to call <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-unregister-callback\" target=\"_blank\" rel=\"\">TEMPDRV_UnregisterCallback()</a>.</p></li></ul><p style=\"color:inherit\">\nIt's important to know the current temperature before calling this function. Attempting to register a callback that would fire immediately is not supported and will result in a return value of <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv-error-codes#ecode-emdrv-tempdrv-bad-limit\" target=\"_blank\" rel=\"\">ECODE_EMDRV_TEMPDRV_BAD_LIMIT</a>. Examples of an illegal scenario would be to register a callback for a temperature that is higher than the current temperature and with a limit set to <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-limit-low\" target=\"_blank\" rel=\"\">TEMPDRV_LIMIT_LOW</a>.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv-error-codes#ecode-emdrv-tempdrv-ok\" target=\"_blank\" rel=\"\">ECODE_EMDRV_TEMPDRV_OK</a> on success.</p></li></ul><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv-error-codes#ecode-emdrv-tempdrv-param-error\" target=\"_blank\" rel=\"\">ECODE_EMDRV_TEMPDRV_PARAM_ERROR</a> if the callback is NULL.</p></li></ul><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv-error-codes#ecode-emdrv-tempdrv-no-init\" target=\"_blank\" rel=\"\">ECODE_EMDRV_TEMPDRV_NO_INIT</a> if the user has forgot to call <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-init\" target=\"_blank\" rel=\"\">TEMPDRV_Init()</a> before attempting to register a callback.</p></li></ul><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv-error-codes#ecode-emdrv-tempdrv-bad-limit\" target=\"_blank\" rel=\"\">ECODE_EMDRV_TEMPDRV_BAD_LIMIT</a> is returned if <code>temp</code> is below the current temperature and <code>limit</code> is <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-limit-low\" target=\"_blank\" rel=\"\">TEMPDRV_LIMIT_LOW</a>. It is also returned if <code>temp</code> is above the current temperature and <code>limit</code> is <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-limit-high\" target=\"_blank\" rel=\"\">TEMPDRV_LIMIT_HIGH</a>.</p></li></ul><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv-error-codes#ecode-emdrv-tempdrv-dup-temp\" target=\"_blank\" rel=\"\">ECODE_EMDRV_TEMPDRV_DUP_TEMP</a> is returned if a duplicate callback is detected. A duplicate callback is if you attempt to register a new callback with the same <code>temp</code> and the same <code>limit</code> as some already registered callback. </p></li></ul></li></ul><br><div>Definition at line <code>726</code> of file <code>platform/emdrv/tempdrv/src/tempdrv.c</code></div><br></div><div><h3>TEMPDRV_UnregisterCallback<span id=\"tempdrv-unregister-callback\" class=\"self-anchor\"><a class=\"perm\" href=\"#tempdrv-unregister-callback\">#</a></span></h3><blockquote>Ecode_t TEMPDRV_UnregisterCallback (<a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv#tempdrv-callback-t\" target=\"_blank\" rel=\"\">TEMPDRV_Callback_t</a> callback)</blockquote><p style=\"color:inherit\">Unregister a callback in the TEMP driver. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">callback</td><td><p style=\"color:inherit\">Callback to unregister.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv-error-codes#ecode-emdrv-tempdrv-ok\" target=\"_blank\" rel=\"\">ECODE_EMDRV_TEMPDRV_OK</a> on success.</p></li></ul><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv-error-codes#ecode-emdrv-tempdrv-param-error\" target=\"_blank\" rel=\"\">ECODE_EMDRV_TEMPDRV_PARAM_ERROR</a> if the callback is NULL.</p></li></ul><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv-error-codes#ecode-emdrv-tempdrv-no-callback\" target=\"_blank\" rel=\"\">ECODE_EMDRV_TEMPDRV_NO_CALLBACK</a> if the callback was not found. </p></li></ul></li></ul><br><div>Definition at line <code>779</code> of file <code>platform/emdrv/tempdrv/src/tempdrv.c</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/tempdrv", "status": "success"}