{"availableCount": 16, "searchTerms": [], "userState": "ecc8b438-974d-44be-a49d-c84b20c5851c", "resources": [{"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/example/bt_ncp/readme.md"], "description": "Network Co-Processor (NCP) target application. Runs the Bluetooth stack dynamically and provides access to it via Bluetooth API (BGAPI) using UART connection. NCP mode makes it possible to run your application on a host controller or PC.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth - NCP_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.bluetooth.demos.bt_ncp.bt_ncp-brd4314a.s37", "text": "Bluetooth - NCP", "priority": 0, "category": "DEMOS", "toolTipText": "Network Co-Processor (NCP) target application. Runs the Bluetooth stack dynamically and provides access to it via Bluetooth API (BGAPI) using UART connection. NCP mode makes it possible to run your application on a host controller or PC.\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/example/bt_soc_blinky/readme.md"], "description": "The classic blinky example using Bluetooth communication. Demonstrates a simple two-way data exchange over GATT. This can be tested with the Simplicity Connect mobile app.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth - SoC Blinky_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.bluetooth.demos.bt_soc_blinky.bt_soc_blinky-brd4314a.s37", "text": "Bluetooth - SoC Blinky", "priority": 0, "category": "DEMOS", "toolTipText": "The classic blinky example using Bluetooth communication. Demonstrates a simple two-way data exchange over GATT. This can be tested with the  Simplicity Connect mobile app.\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/example/bt_soc_thermometer/readme.md"], "description": "Implements a GATT Server with the Health Thermometer Profile, which enables a Client device to connect and get temperature data. Temperature is read from the mock relative humidity and temperature sensor.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth - SoC Thermometer (Mock)_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.bluetooth.demos.bt_soc_thermometer_mock.bt_soc_thermometer_mock-brd4314a.s37", "text": "Bluetooth - SoC Thermometer (Mock)", "priority": 0, "category": "DEMOS", "toolTipText": "Implements a GATT Server with the Health Thermometer Profile, which enables a Client device to connect and get temperature data. Temperature is read from the mock relative humidity and temperature sensor.\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/example/bt_soc_throughput/readme.md"], "description": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using Simplicity Connect mobile app, through the Throughput demo tile.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth - SoC Throughput (single button)_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.bluetooth.demos.bt_soc_throughput_log_single.bt_soc_throughput_log_single-brd4314a.s37", "text": "Bluetooth - SoC Throughput (single button)", "priority": 0, "category": "DEMOS", "toolTipText": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using Simplicity Connect mobile app, through the Throughput demo tile.\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/example/bt_soc_ibeacon/readme.md"], "description": "Sends non-connectable advertisements in iBeacon format. The iBeacon Service gives Bluetooth accessories a simple and convenient way to send iBeacons to smartphones. This example can be tested together with the Simplicity Connect mobile app.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth - SoC iBeacon_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.bluetooth.demos.bt_soc_ibeacon.bt_soc_ibeacon-brd4314a.s37", "text": "Bluetooth - SoC iBeacon", "priority": 0, "category": "DEMOS", "toolTipText": "Sends non-connectable advertisements in iBeacon format. The iBeacon Service gives Bluetooth accessories a simple and convenient way to send iBeacons to smartphones. This example can be tested together with the Simplicity Connect mobile app.\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/documentation/example/btmesh_ncp_empty/readme.md"], "description": "Demonstrates the bare minimum needed for an NCP Target C application. This example is recommended for EFR32xG22, which has limited RAM and flash, and therefore some of the stack classes are disabled by default.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth Mesh - NCP Empty_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.btmesh.demos.btmesh_ncp_empty.btmesh_ncp_empty-brd4314a.s37", "text": "Bluetooth Mesh - NCP Empty", "priority": 0, "category": "DEMOS", "toolTipText": "Demonstrates the bare minimum needed for an NCP Target C application.  This example is recommended for EFR32xG22, which has limited RAM and flash, and therefore some of the stack classes are disabled by default.\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/documentation/example/btmesh_soc_nlc_sensor_ambient_light/readme_xg22.md"], "description": "An out-of-the-box Software Demo where the device acts as an ambient light sensor in a Networked Lighting Control (NLC) system. The device simulates ambient light measurements and sends these to the network. Properly configured NLC Basic Lightness Controllers then can act on the received data.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth Mesh - NLC Ambient Light Sensor (Mock)_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.btmesh.demos.btmesh_soc_nlc_sensor_ambient_light.btmesh_soc_nlc_sensor_ambient_light-brd4314a.s37", "text": "Bluetooth Mesh - NLC Ambient Light Sensor (Mock)", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as an ambient light sensor in a Networked Lighting Control (NLC) system. The device simulates ambient light measurements and sends these to the network. Properly configured NLC Basic Lightness Controllers then can act on the received data.\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/documentation/example/btmesh_soc_nlc_basic_scene_selector/readme_xg22.md"], "description": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth Mesh - NLC Basic Scene Selector_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.btmesh.demos.btmesh_soc_nlc_basic_scene_selector.btmesh_soc_nlc_basic_scene_selector-brd4314a.s37", "text": "Bluetooth Mesh - NLC Basic Scene Selector", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/documentation/example/btmesh_soc_nlc_basic_scene_selector/readme_low_power.md"], "description": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth Mesh - NLC Basic Scene Selector Low Power_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.btmesh.demos.btmesh_soc_nlc_basic_scene_selector_low_power.btmesh_soc_nlc_basic_scene_selector_low_power-brd4314a.s37", "text": "Bluetooth Mesh - NLC Basic Scene Selector Low Power", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/documentation/example/btmesh_soc_nlc_dimming_control/readme_xg22.md"], "description": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. <PERSON><PERSON> But<PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth Mesh - NLC Dimming Control_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.btmesh.demos.btmesh_soc_nlc_dimming_control.btmesh_soc_nlc_dimming_control-brd4314a.s37", "text": "Bluetooth Mesh - NLC Dimming Control", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. <PERSON><PERSON> But<PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/documentation/example/btmesh_soc_nlc_dimming_control/readme_low_power.md"], "description": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth Mesh - NLC Dimming Control Low Power_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.btmesh.demos.btmesh_soc_nlc_dimming_control_low_power.btmesh_soc_nlc_dimming_control_low_power-brd4314a.s37", "text": "Bluetooth Mesh - NLC Dimming Control Low Power", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/documentation/example/btmesh_soc_nlc_sensor_occupancy/readme.md"], "description": "An out-of-the-box Software Demo where the device acts as an Occupancy Sensor in a Networked Lighting Control (NLC) system. <PERSON><PERSON> Button presses imitate people count changes which can control a properly configured NLC Basic Lightness Controller.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth Mesh - NLC Occupancy Sensor_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.btmesh.demos.btmesh_soc_nlc_sensor_occupancy.btmesh_soc_nlc_sensor_occupancy-brd4314a.s37", "text": "Bluetooth Mesh - NLC Occupancy Sensor", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as an Occupancy Sensor in a Networked Lighting Control (NLC) system. <PERSON><PERSON> Button presses imitate people count changes which can control a properly configured NLC Basic Lightness Controller.\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/documentation/example/btmesh_soc_sensor_client/readme.md"], "description": "This example demonstrates the Bluetooth Mesh Sensor Client Model. It collects and displays sensor measurement data from remote device(s) (e.g., btmesh_soc_sensor_server).", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth Mesh - SoC Sensor Client_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.btmesh.demos.btmesh_soc_sensor_client.btmesh_soc_sensor_client-brd4314a.s37", "text": "Bluetooth Mesh - SoC Sensor Client", "priority": 0, "category": "DEMOS", "toolTipText": "This example demonstrates the Bluetooth Mesh Sensor Client Model. It collects and displays sensor measurement data from remote device(s) (e.g., btmesh_soc_sensor_server).\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/documentation/example/btmesh_soc_sensor_thermometer/readme.md"], "description": "This example demonstrates the Bluetooth Mesh Sensor Server Model and Sensor Setup Server Model. If available, it measures CPU temperature and uses that data as temperature reading, otherwise it sends mocked temperature data to a remote device (e.g., btmesh_soc_sensor_client).", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth Mesh - SoC Sensor Thermometer_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.btmesh.demos.btmesh_soc_sensor_thermometer.btmesh_soc_sensor_thermometer-brd4314a.s37", "text": "Bluetooth Mesh - SoC Sensor Thermometer", "priority": 0, "category": "DEMOS", "toolTipText": "This example demonstrates the Bluetooth Mesh Sensor Server Model and Sensor Setup Server Model. If available, it measures CPU temperature and uses that data as temperature reading, otherwise it sends mocked temperature data to a remote device (e.g., btmesh_soc_sensor_client).\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/documentation/example/btmesh_soc_switch/readme.md"], "description": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device. Note - this example is not compatible with the Dimming Control NLC Profile.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth Mesh - SoC Switch CTL_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.btmesh.demos.btmesh_soc_switch_ctl.btmesh_soc_switch_ctl-brd4314a.s37", "text": "Bluetooth Mesh - SoC Switch CTL", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device.\nNote - this example is not compatible with the Dimming Control NLC Profile.\n"}, {"imageURL": "bundleentry://52.fwk1486794042/icons/run.svg", "supportDocuments": ["file:/C:/SiliconLabs/SimplicityStudio/v5/offline/sdk/demo-sdk/documentation/example/btmesh_soc_switch/readme_low_power.md"], "description": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. It is optimized for low current consumption with disabled CLI, logging, and LCD. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device. Note - this example is not compatible with the Dimming Control NLC Profile.", "id": "com.silabs.sdk.demo_sdk:2024.12.1._-1423345314_Bluetooth Mesh - SoC Switch CTL Low Power_asset:..com.silabs.sdk.stack.sisdk_2024.12.1.app.btmesh.demos.btmesh_soc_switch_ctl_low_power.btmesh_soc_switch_ctl_low_power-brd4314a.s37", "text": "Bluetooth Mesh - SoC Switch CTL Low Power", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. It is optimized for low current consumption with disabled CLI, logging, and LCD. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device.\nNote - this example is not compatible with the Dimming Control NLC Profile.\n"}], "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [{"futureCount": 5, "anySelected": false, "id": 0, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": false}, {"futureCount": 11, "anySelected": false, "id": 1, "filters": [], "title": "Bluetooth Mesh", "parentId": 0, "selected": false}], "title": "Wireless Technology", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [{"futureCount": 2, "anySelected": false, "id": 0, "filters": [], "title": "NCP", "parentId": 1, "selected": false}, {"futureCount": 14, "anySelected": false, "id": 1, "filters": [], "title": "SoC", "parentId": 1, "selected": false}], "title": "Device Type", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 2, "filters": [{"futureCount": 11, "anySelected": false, "id": 0, "filters": [], "title": "Advanced", "parentId": 2, "selected": false}, {"futureCount": 5, "anySelected": false, "id": 1, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 2, "selected": false}], "title": "Project Difficulty", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 3, "filters": [{"futureCount": 16, "anySelected": false, "id": 0, "filters": [], "title": "Production", "parentId": 3, "selected": false}], "title": "Quality", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 4, "filters": [{"futureCount": 16, "anySelected": false, "id": 0, "filters": [], "title": "Simplicity Demos v2024.12.1", "parentId": 4, "selected": false}], "title": "Provider", "parentId": -1, "selected": false}], "totalCount": 16}