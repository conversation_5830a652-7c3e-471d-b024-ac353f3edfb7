{"requiredByTree": [{"children": [{"children": [{"keyWords": "bluetooth_feature_advertiser", "componentId": "bluetooth_feature_advertiser", "isConfigurable": true, "documentation": "", "description": "This Bluetooth advertising component, corresponding to the \"advertiser\" class in Bluetooth APIs, is the base of legacy, extended and periodic advertisings. Functionalities in this component include advertising set creation, advertising parameter and address settings etc. The application must choose which advertising types are needed based on its use cases. The <bluetooth_feature_legacy_advertiser> component provides the legacy advertising feature. The <bluetooth_feature_extended_advertiser> component provides the extended advertising feature. And the <bluetooth_feature_periodic_advertiser> component provides the periodic advertising feature. When the <bluetooth_feature_legacy_advertiser>, <bluetooth_feature_extended_advertiser>, or <bluetooth_feature_periodic_advertiser> component is included by the application, some commands of the \"advertiser\" class whose behaviors vary by specific advertising types are no longer supported. See the Bluetooth API reference for more details. When none of the three components is included, all commands of the \"advertiser\" class are functional for providing the backwards compatibility.\n", "instantiable": false, "label": "Advertising Base Feature", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Feature|studiocomproot-Bluetooth-Feature-bluetooth_feature_advertiser", "isEditable": true, "isEnabled": true, "name": "bluetooth_feature_advertiser", "isSelected": true, "id": "studiocomproot-Bluetooth-Feature-bluetooth_feature_advertiser"}, {"keyWords": "bluetooth_feature_connection", "componentId": "bluetooth_feature_connection", "isConfigurable": true, "documentation": "", "description": "Bluetooth connection feature\n", "instantiable": false, "label": "Connection", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Feature|studiocomproot-Bluetooth-Feature-bluetooth_feature_connection", "isEditable": true, "isEnabled": true, "name": "bluetooth_feature_connection", "isSelected": true, "id": "studiocomproot-Bluetooth-Feature-bluetooth_feature_connection"}, {"keyWords": "bluetooth_feature_legacy_advertiser", "componentId": "bluetooth_feature_legacy_advertiser", "isConfigurable": false, "documentation": "", "description": "This component, corresponding to the \"legacy_advertiser\" class in Bluetooth APIs, provides the legacy advertising feature. Specifically, this component enables advertisements that use legacy advertising PDUs. Common advertising functionalities, e.g., advertising set creation, and address settings etc., are provided by its base component <bluetooth_feature_advertiser>.\n", "instantiable": false, "label": "Legacy Advertising", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Feature|studiocomproot-Bluetooth-Feature-bluetooth_feature_legacy_advertiser", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_legacy_advertiser", "isSelected": true, "id": "studiocomproot-Bluetooth-Feature-bluetooth_feature_legacy_advertiser"}, {"keyWords": "bluetooth_feature_scanner", "componentId": "bluetooth_feature_scanner", "isConfigurable": false, "documentation": "", "description": "The Bluetooth scanning component, corresponding to the \"scanner\" class in Bluetooth APIs, is the base of legacy and extended scannings and it provides functionalities that are common in legacy and extended scannings.\nThe <bluetooth_feature_legacy_scanner> component enables scanning the advertisements using legacy advertising PDUs. The <bluetooth_feature_extended_scanner> component enables scanning the advertisements using legacy or extended advertising PDUs. When the <bluetooth_feature_legacy_scanner>, or <bluetooth_feature_extended_scanner> is included by the application, some superseded commands of the \"scanner\" class are not available to use. See the Bluetooth API reference for more details. When none of these two components is included, all commands of the \"scanner\" class are functional for providing the backwards compatibility.\n", "instantiable": false, "label": "Scanner Base Feature", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Feature|studiocomproot-Bluetooth-Feature-bluetooth_feature_scanner", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_scanner", "isSelected": true, "id": "studiocomproot-Bluetooth-Feature-bluetooth_feature_scanner"}, {"keyWords": "bluetooth_feature_legacy_scanner", "componentId": "bluetooth_feature_legacy_scanner", "isConfigurable": false, "documentation": "", "description": "This component brings in necessary functionalities for scanning the advertisements that use legacy advertising PDUs.\nInclude this component if the application does not need to scan advertisements that use extended advertising PDUs. Advertisements received by the scanner are reported in the BGAPI sl_bt_evt_scanner_legacy_advertisement_report event.\nIf this component is included and the <bluetooth_feature_extended_scanner> is not, the number of received advertisement reports are reduced if advertising devices that use extended advertising PDUs are in the radio range. Another benefit of including this component only is reduced application size by eliminating the stack functionalities for scanning advertisements that use extended advertising PDUs.\n", "instantiable": false, "label": "Scanner for legacy advertisements", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Feature|studiocomproot-Bluetooth-Feature-bluetooth_feature_legacy_scanner", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_legacy_scanner", "isSelected": true, "id": "studiocomproot-Bluetooth-Feature-bluetooth_feature_legacy_scanner"}], "isEnabled": true, "name": "Feature", "id": "studiocomproot-Bluetooth-Feature", "label": "Feature", "type": "category"}, {"children": [{"keyWords": "in_place_ota_dfu", "componentId": "in_place_ota_dfu", "isConfigurable": true, "documentation": "", "description": "Component that provides in-place over-the-air (OTA) device firmware  update (DFU) functionality. In this solution, the application flash area is used as a temporary storage for the update.\n", "instantiable": false, "label": "In-Place OTA DFU", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-OTA|studiocomproot-Bluetooth-OTA-in_place_ota_dfu", "isEditable": true, "isEnabled": true, "name": "in_place_ota_dfu", "isSelected": true, "id": "studiocomproot-Bluetooth-OTA-in_place_ota_dfu"}], "isEnabled": true, "name": "OTA", "id": "studiocomproot-Bluetooth-OTA", "label": "OTA", "type": "category"}, {"children": [{"keyWords": "bluetooth_feature_gatt", "componentId": "bluetooth_feature_gatt", "isConfigurable": false, "documentation": "", "description": "GATT Client feature\nEnables the ability to browse and manage attributes in a remote GATT server.\n", "instantiable": false, "label": "GATT Client", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Stack|studiocomproot-Bluetooth-Stack-bluetooth_feature_gatt", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_gatt", "isSelected": true, "id": "studiocomproot-Bluetooth-Stack-bluetooth_feature_gatt"}, {"keyWords": "bluetooth_feature_gatt_server", "componentId": "bluetooth_feature_gatt_server", "isConfigurable": false, "documentation": "", "description": "GATT Server feature\nEnables the ability to browse and manage attributes in a local GATT database. \n", "instantiable": false, "label": "GATT Server", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Stack|studiocomproot-Bluetooth-Stack-bluetooth_feature_gatt_server", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_gatt_server", "isSelected": true, "id": "studiocomproot-Bluetooth-Stack-bluetooth_feature_gatt_server"}, {"keyWords": "bluetooth_feature_sm", "componentId": "bluetooth_feature_sm", "isConfigurable": false, "documentation": "", "description": "Bluetooth security manager (SM) feature\n", "instantiable": false, "label": "Security Manager", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Stack|studiocomproot-Bluetooth-Stack-bluetooth_feature_sm", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_sm", "isSelected": true, "id": "studiocomproot-Bluetooth-Stack-bluetooth_feature_sm"}, {"keyWords": "bluetooth_feature_system", "componentId": "bluetooth_feature_system", "isConfigurable": false, "documentation": "", "description": "Local device configruation and software timers\n", "instantiable": false, "label": "System", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Stack|studiocomproot-Bluetooth-Stack-bluetooth_feature_system", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_system", "isSelected": true, "id": "studiocomproot-Bluetooth-Stack-bluetooth_feature_system"}], "isEnabled": true, "name": "<PERSON><PERSON>", "id": "studiocomproot-Bluetooth-Stack", "label": "<PERSON><PERSON>", "type": "category"}], "isEnabled": true, "name": "Bluetooth", "id": "studiocomproot-Bluetooth", "label": "Bluetooth", "type": "category"}], "component": {"keyWords": "bluetooth_stack", "componentId": "bluetooth_stack", "isConfigurable": true, "documentation": "", "description": "Bluetooth Low Energy stack and configurations\nIn addition to this core component, select features needed by the application.\n", "instantiable": false, "label": "Bluetooth Core", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "bluetooth_stack", "isEditable": true, "isEnabled": true, "name": "bluetooth_stack", "isSelected": true, "id": "bluetooth_stack"}, "requiresTree": [{"children": [{"children": [{"keyWords": "rail_util_pa", "componentId": "rail_util_pa", "isConfigurable": true, "documentation": "http://docs.silabs.com/rail/2.13/rail-util-pa", "description": "Utility to aid with RAIL RF Power Amplifier (PA) Support", "instantiable": false, "label": "RAIL Utility, PA", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Platform|studiocomproot-Platform-Radio|studiocomproot-Platform-Radio-rail_util_pa", "isEditable": true, "isEnabled": true, "name": "rail_util_pa", "isSelected": true, "id": "studiocomproot-Platform-Radio-rail_util_pa"}], "isEnabled": true, "name": "Radio", "id": "studiocomproot-Platform-Radio", "label": "Radio", "type": "category"}, {"children": [{"keyWords": "sl_assert", "componentId": "sl_assert", "isConfigurable": false, "documentation": "http://docs.silabs.com/gecko-platform/4.1/common/api/group-assert", "description": "Component that provides assert functions.", "instantiable": false, "label": "Assert Functions", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Platform|studiocomproot-Platform-Utilities|studiocomproot-Platform-Utilities-sl_assert", "isEditable": false, "isEnabled": true, "name": "sl_assert", "isSelected": true, "id": "studiocomproot-Platform-Utilities-sl_assert"}, {"keyWords": "component_catalog", "componentId": "component_catalog", "isConfigurable": false, "documentation": "", "description": "Component catalog that provides the list of APIs present in the project.", "instantiable": false, "label": "Component Catalog", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Platform|studiocomproot-Platform-Utilities|studiocomproot-Platform-Utilities-component_catalog", "isEditable": false, "isEnabled": true, "name": "component_catalog", "isSelected": true, "id": "studiocomproot-Platform-Utilities-component_catalog"}, {"keyWords": "silabs_core_sl_malloc", "componentId": "silabs_core_sl_malloc", "isConfigurable": false, "documentation": "", "description": "This component provides a thread safe wrapper on top of the standard c memory allocation functions.\nThe component can be used in an environment where Micrium OS or FreeRTOS is used in order to safely\nallocate and free memory in multiple tasks from a shared heap.\n\nThe wrapper functions are called sl_malloc(), sl_calloc, sl_realloc() and sl_free().", "instantiable": false, "label": "<PERSON><PERSON>per", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Platform|studiocomproot-Platform-Utilities|studiocomproot-Platform-Utilities-silabs_core_sl_malloc", "isEditable": false, "isEnabled": true, "name": "silabs_core_sl_malloc", "isSelected": true, "id": "studiocomproot-Platform-Utilities-silabs_core_sl_malloc"}], "isEnabled": true, "name": "Utilities", "id": "studiocomproot-Platform-Utilities", "label": "Utilities", "type": "category"}], "isEnabled": true, "name": "Platform", "id": "studiocomproot-Platform", "label": "Platform", "type": "category"}, {"children": [{"children": [{"keyWords": "device_init", "componentId": "device_init", "isConfigurable": false, "documentation": "http://docs.silabs.com/gecko-platform/4.1/service/api/group-device-init", "description": "Device initialization wrapper. Depends on specific device initialization components depending on the selected hardware.\n", "instantiable": false, "label": "Device Init", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-Device_Initialization|studiocomproot-Services-Device_Initialization-device_init", "isEditable": false, "isEnabled": true, "name": "device_init", "isSelected": true, "id": "studiocomproot-Services-Device_Initialization-device_init"}, {"keyWords": "device_init_hfxo", "componentId": "device_init_hfxo", "isConfigurable": true, "documentation": "http://docs.silabs.com/gecko-platform/4.1/service/api/group-device-init-hfxo", "description": "Device initialization for HFXO - High Frequency Crystal Oscillator\n", "instantiable": false, "label": "Device Init: HFXO", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-Device_Initialization|studiocomproot-Services-Device_Initialization-device_init_hfxo", "isEditable": true, "isEnabled": true, "name": "device_init_hfxo", "isSelected": true, "id": "studiocomproot-Services-Device_Initialization-device_init_hfxo"}], "isEnabled": true, "name": "Device Initialization", "id": "studiocomproot-Services-Device_Initialization", "label": "Device Initialization", "type": "category"}, {"children": [{"keyWords": "nvm3_lib", "componentId": "nvm3_lib", "isConfigurable": false, "documentation": "http://docs.silabs.com/gecko-platform/4.1/driver/api/group-nvm3", "description": "The NVM3 driver provides a means to write and read data objects (key/value pairs) stored in flash. Wear-leveling is applied to reduce erase and write cycles and maximize flash lifetime. The driver is resilient to power loss and reset events, ensuring that objects retrieved from the driver are always in a valid state. A single NVM3 instance can be shared among several wireless stacks and application code, making it well-suited for multiprotocol applications.\n\nThis component includes only the NVM3 driver core. To configure and create the default instance of this driver, the NVM3 Default Instance component should be included in the project.\n", "instantiable": false, "label": "NVM3 Core", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-NVM3|studiocomproot-Services-NVM3-nvm3_lib", "isEditable": false, "isEnabled": true, "name": "nvm3_lib", "isSelected": true, "id": "studiocomproot-Services-NVM3-nvm3_lib"}, {"keyWords": "nvm3_default", "componentId": "nvm3_default", "isConfigurable": true, "documentation": "http://docs.silabs.com/gecko-platform/4.1/driver/api/group-nvm3default", "description": "This component provides functions to initialize/deinitialize the default NVM3 instance. The instance can be accessed with the NVM3 API by using the nvm3_defaultHandle provided in nvm3_default.h as the nvm3_Handle_t pointer.\n\nIf the Services->Runtime->System Setup component is included in a project, the default instance will be initialized automatically, using the default instance configuration, during the sl_system_init() call in main.c.\n\nSelecting this component will also include the NVM3 Core component, which is the implementation of the NVM3 driver itself.\n", "instantiable": false, "label": "NVM3 Default Instance", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-NVM3|studiocomproot-Services-NVM3-nvm3_default", "isEditable": true, "isEnabled": true, "name": "nvm3_default", "isSelected": true, "id": "studiocomproot-Services-NVM3-nvm3_default"}], "isEnabled": true, "name": "NVM3", "id": "studiocomproot-Services-NVM3", "label": "NVM3", "type": "category"}, {"children": [{"keyWords": "sl_system", "componentId": "sl_system", "isConfigurable": false, "documentation": "http://docs.silabs.com/gecko-platform/4.1/service/api/group-system", "description": "The System component provides a set of events that can be used by\nother components to register handlers that should be run when the system\nis initialized and is running. Handlers can be registered for the following\nevents using the Event Handler API provided by the Event Handler component:\n\n* platform_init\n* driver_init\n* service_init\n* stack_init\n* internal_app_init\n* kernel_start\n* platform_process_action\n* service_process_action\n* stack_process_action\n* internal_app_process_action\n\nThese events are fired in the order listed above when `sl_system_init()` and\nsl_system_kernel_start() or app_process_action() is called.\n\nUsage example:\n\n```c\n\\#include sl_component_catalog.h\n\\#include sl_system_init.h\n\\#include sl_power_manager.h\n\\#include app.h\n\\#if defined(SL_CATALOG_POWER_MANAGER_PRESENT)\n\\#include sl_power_manager.h\n\\#endif\n\\#if defined(SL_CATALOG_KERNEL_PRESENT)\n\\#include sl_system_kernel.h\n\\#else // SL_CATALOG_KERNEL_PRESENT\n\\#include sl_system_process_action.h\n\\#endif // SL_CATALOG_KERNEL_PRESENT\n\nint main(void)\n{\n  // Initialize Silicon Labs device, system, service(s) and protocol stack(s).\n  // Note that if the kernel is present, processing task(s) will be created by\n  // this call.\n  sl_system_init();\n\n  // Initialize the application.\n  app_init();\n\n\\#if defined(SL_CATALOG_KERNEL_PRESENT)\n  // Start the kernel. Task(s) created in app_init() will start running.\n  sl_system_kernel_start();\n\\#else // SL_CATALOG_KERNEL_PRESENT\n  while (1) {\n    // Do not remove this call: Silicon Labs components process action routine\n    // must be called from the super loop.\n    sl_system_process_action();\n\n    // Application process.\n    app_process_action();\n\n\\#if defined(SL_CATALOG_POWER_MANAGER_PRESENT)\n    // Let the CPU go to sleep if the system allows it.\n    sl_power_manager_sleep();\n\\#endif\n  }\n\\#endif // SL_CATALOG_KERNEL_PRESENT\n}\n```\n", "instantiable": false, "label": "System Setup", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-System_Setup|studiocomproot-Services-System_Setup-sl_system", "isEditable": false, "isEnabled": true, "name": "sl_system", "isSelected": true, "id": "studiocomproot-Services-System_Setup-sl_system"}], "isEnabled": true, "name": "System Setup", "id": "studiocomproot-Services-System_Setup", "label": "System Setup", "type": "category"}, {"keyWords": "power_manager", "componentId": "power_manager", "isConfigurable": true, "documentation": "http://docs.silabs.com/gecko-platform/4.1/service/power_manager/overview", "description": "Offers a management for the power consumption. Shut down the high-frequency clock and put the system in sleep mode when possible.", "instantiable": false, "label": "Power Manager", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-power_manager", "isEditable": true, "isEnabled": true, "name": "power_manager", "isSelected": true, "id": "studiocomproot-Services-power_manager"}, {"keyWords": "sleeptimer", "componentId": "sleeptimer", "isConfigurable": true, "documentation": "http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer", "description": "Low-frequency timer utilities (timer, delays and time keeping).", "instantiable": false, "label": "Sleep Timer", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-sleeptimer", "isEditable": true, "isEnabled": true, "name": "sleeptimer", "isSelected": true, "id": "studiocomproot-Services-sleeptimer"}], "isEnabled": true, "name": "Services", "id": "studiocomproot-Services", "label": "Services", "type": "category"}]}