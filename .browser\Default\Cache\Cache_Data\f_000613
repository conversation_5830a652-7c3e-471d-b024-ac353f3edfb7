<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!--Custom BLE GATT-->
<gatt gatt_caching="true" generic_attribute_service="true" header="gatt_db.h" name="Custom BLE GATT" out="gatt_db.c" prefix="gattdb_">

  <!--Generic Access-->
  <service advertise="false" name="Generic Access" requirement="mandatory" sourceId="org.bluetooth.service.generic_access" type="primary" uuid="1800">
    <informativeText>Abstract: The generic_access service contains generic information about the device. All available Characteristics are readonly. </informativeText>

    <!--Device Name-->
    <characteristic const="false" id="device_name" name="Device Name" sourceId="org.bluetooth.characteristic.gap.device_name" uuid="2A00">
      <value length="6" type="utf-8" variable_length="false">MCMPDT</value>
      <properties>
        <read authenticated="false" bonded="false" encrypted="false"/>
        <write authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>

    <!--Appearance-->
    <characteristic const="true" name="Appearance" sourceId="org.bluetooth.characteristic.gap.appearance" uuid="2A01">
      <informativeText>Abstract: The external appearance of this device. The values are composed of a category (10-bits) and sub-categories (6-bits). </informativeText>
      <value length="2" type="hex" variable_length="false">0000</value>
      <properties>
        <read authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>
  </service>

  <!--Device Information-->
  <service advertise="false" id="device_information" name="Device Information" requirement="mandatory" sourceId="org.bluetooth.service.device_information" type="primary" uuid="180A">
    <informativeText>Abstract: The Device Information Service exposes manufacturer and/or vendor information about a device. Summary: This service exposes manufacturer information about a device. The Device Information Service is instantiated as a Primary Service. Only one instance of the Device Information Service is exposed on a device.</informativeText>

    <!--Manufacturer Name String-->
    <characteristic const="true" id="manufacturer_name_string" name="Manufacturer Name String" sourceId="org.bluetooth.characteristic.manufacturer_name_string" uuid="2A29">
      <informativeText>Abstract: The value of this characteristic is a UTF-8 string representing the name of the manufacturer of the device.</informativeText>
      <value length="12" type="utf-8" variable_length="false">Silicon Labs</value>
      <properties>
        <read authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>

    <!--Model Number String-->
    <characteristic const="false" id="model_number_string" name="Model Number String" sourceId="org.bluetooth.characteristic.model_number_string" uuid="2A24">
      <informativeText>Abstract: The value of this characteristic is a UTF-8 string representing the model number assigned by the device vendor.</informativeText>
      <value length="8" type="utf-8" variable_length="false">00000000</value>
      <properties>
        <read authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>

    <!--Hardware Revision String-->
    <characteristic const="false" id="hardware_revision_string" name="Hardware Revision String" sourceId="org.bluetooth.characteristic.hardware_revision_string" uuid="2A27">
      <informativeText>Summary: The value of this characteristic is a UTF-8 string representing the hardware revision for the hardware within the device.</informativeText>
      <value length="3" type="utf-8" variable_length="false">000</value>
      <properties>
        <read authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>

    <!--Firmware Revision String-->
    <characteristic const="false" id="firmware_revision_string" name="Firmware Revision String" sourceId="org.bluetooth.characteristic.firmware_revision_string" uuid="2A26">
      <informativeText>Summary: The value of this characteristic is a UTF-8 string representing the firmware revision for the firmware within the device.</informativeText>
      <value length="5" type="utf-8" variable_length="false">0.0.0</value>
      <properties>
        <read authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>

    <!--System ID-->
    <characteristic const="false" id="system_id" name="System ID" sourceId="org.bluetooth.characteristic.system_id" uuid="2A23">
      <informativeText>Abstract: The SYSTEM ID characteristic consists of a structure with two fields. The first field are the LSOs and the second field contains the MSOs. This is a 64-bit structure which consists of a 40-bit manufacturer-defined identifier concatenated with a 24 bit unique Organizationally Unique Identifier (OUI). The OUI is issued by the IEEE Registration Authority (http://standards.ieee.org/regauth/index.html) and is required to be used in accordance with IEEE Standard 802-2001.6 while the least significant 40 bits are manufacturer defined. If System ID generated based on a Bluetooth Device Address, it is required to be done as follows. System ID and the Bluetooth Device Address have a very similar structure: a Bluetooth Device Address is 48 bits in length and consists of a 24 bit Company Assigned Identifier (manufacturer defined identifier) concatenated with a 24 bit Company Identifier (OUI). In order to encapsulate a Bluetooth Device Address as System ID, the Company Identifier is concatenated with 0xFFFE followed by the Company Assigned Identifier of the Bluetooth Address. For more guidelines related to EUI-64, refer to http://standards.ieee.org/develop/regauth/tut/eui64.pdf. Examples: If the system ID is based of a Bluetooth Device Address with a Company Identifier (OUI) is 0x123456 and the Company Assigned Identifier is 0x9ABCDE, then the System Identifier is required to be 0x123456FFFE9ABCDE.</informativeText>
      <value length="8" type="hex" variable_length="false"/>
      <properties>
        <read authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>
  </service>

  <!--Health Thermometer-->
  <service advertise="false" id="health_thermometer_0" name="Health Thermometer" requirement="mandatory" sourceId="org.bluetooth.service.health_thermometer" type="primary" uuid="1809">
    <informativeText>Abstract: The Health Thermometer service exposes temperature and other data from a thermometer intended for healthcare and fitness applications.
Summary: The Health Thermometer service is instantiated as a Primary Service. There are one or more instantiations of the Health Thermometer service per device claiming conformance with this specification. </informativeText>

    <!--Temperature Measurement-->
    <characteristic const="false" id="temperature_measurement_0" name="Temperature Measurement" sourceId="org.bluetooth.characteristic.temperature_measurement" uuid="2A1C">
      <informativeText>Abstract: 
    The Temperature Measurement characteristic is a variable length structure containing a Flags field, a Temperature Measurement Value field and, based upon the contents of the Flags field, optionally a Time Stamp field and/or a Temperature Type field.            
		
Summary: 
    The flags is the first field sent followed by the Temperature Measurement Value.            
		
Examples: 
      If the value of bit 1 of the Flags field is 0 and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of two fields in this order; Flags and Temperature Measurement Value.
     
      If the value of bit 1 of the Flags field is 1 (Time Stamp) and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Time Stamp.
     
      If the value of bit 1 of the Flags field is 1 and bit 2 is 1 (Time Stamp and Temperature Type), the structure of the Temperature Measurement characteristic consists of four fields in this order: Flags, Temperature Measurement Value, Time Stamp and Temperature Type.
     
      If the value of bit 1 of the Flags field is 0 and bit 2 is 1 (Temperature Type), the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Temperature Type.
    </informativeText>
      <value length="17" type="hex" variable_length="false"/>
      <properties>
        <indicate authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>

    <!--Temperature Measurement-->
    <characteristic const="false" id="temperature_measurement_1" name="Temperature Measurement" sourceId="org.bluetooth.characteristic.temperature_measurement" uuid="2A1C">
      <informativeText>Abstract: 
    The Temperature Measurement characteristic is a variable length structure containing a Flags field, a Temperature Measurement Value field and, based upon the contents of the Flags field, optionally a Time Stamp field and/or a Temperature Type field.            
		
Summary: 
    The flags is the first field sent followed by the Temperature Measurement Value.            
		
Examples: 
      If the value of bit 1 of the Flags field is 0 and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of two fields in this order; Flags and Temperature Measurement Value.
     
      If the value of bit 1 of the Flags field is 1 (Time Stamp) and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Time Stamp.
     
      If the value of bit 1 of the Flags field is 1 and bit 2 is 1 (Time Stamp and Temperature Type), the structure of the Temperature Measurement characteristic consists of four fields in this order: Flags, Temperature Measurement Value, Time Stamp and Temperature Type.
     
      If the value of bit 1 of the Flags field is 0 and bit 2 is 1 (Temperature Type), the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Temperature Type.
    </informativeText>
      <value length="17" type="hex" variable_length="false"/>
      <properties>
        <indicate authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>

    <!--Temperature Measurement-->
    <characteristic const="false" id="temperature_measurement_2" name="Temperature Measurement" sourceId="org.bluetooth.characteristic.temperature_measurement" uuid="2A1C">
      <informativeText>Abstract: 
    The Temperature Measurement characteristic is a variable length structure containing a Flags field, a Temperature Measurement Value field and, based upon the contents of the Flags field, optionally a Time Stamp field and/or a Temperature Type field.            
		
Summary: 
    The flags is the first field sent followed by the Temperature Measurement Value.            
		
Examples: 
      If the value of bit 1 of the Flags field is 0 and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of two fields in this order; Flags and Temperature Measurement Value.
     
      If the value of bit 1 of the Flags field is 1 (Time Stamp) and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Time Stamp.
     
      If the value of bit 1 of the Flags field is 1 and bit 2 is 1 (Time Stamp and Temperature Type), the structure of the Temperature Measurement characteristic consists of four fields in this order: Flags, Temperature Measurement Value, Time Stamp and Temperature Type.
     
      If the value of bit 1 of the Flags field is 0 and bit 2 is 1 (Temperature Type), the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Temperature Type.
    </informativeText>
      <value length="17" type="hex" variable_length="false"/>
      <properties>
        <indicate authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>

    <!--Temperature Measurement-->
    <characteristic const="false" id="temperature_measurement_3" name="Temperature Measurement" sourceId="org.bluetooth.characteristic.temperature_measurement" uuid="2A1C">
      <informativeText>Abstract: 
    The Temperature Measurement characteristic is a variable length structure containing a Flags field, a Temperature Measurement Value field and, based upon the contents of the Flags field, optionally a Time Stamp field and/or a Temperature Type field.            
		
Summary: 
    The flags is the first field sent followed by the Temperature Measurement Value.            
		
Examples: 
      If the value of bit 1 of the Flags field is 0 and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of two fields in this order; Flags and Temperature Measurement Value.
     
      If the value of bit 1 of the Flags field is 1 (Time Stamp) and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Time Stamp.
     
      If the value of bit 1 of the Flags field is 1 and bit 2 is 1 (Time Stamp and Temperature Type), the structure of the Temperature Measurement characteristic consists of four fields in this order: Flags, Temperature Measurement Value, Time Stamp and Temperature Type.
     
      If the value of bit 1 of the Flags field is 0 and bit 2 is 1 (Temperature Type), the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Temperature Type.
    </informativeText>
      <value length="17" type="hex" variable_length="false"/>
      <properties>
        <indicate authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>

    <!--Temperature Measurement-->
    <characteristic const="false" id="temperature_measurement_4" name="Temperature Measurement" sourceId="org.bluetooth.characteristic.temperature_measurement" uuid="2A1C">
      <informativeText>Abstract: 
    The Temperature Measurement characteristic is a variable length structure containing a Flags field, a Temperature Measurement Value field and, based upon the contents of the Flags field, optionally a Time Stamp field and/or a Temperature Type field.            
		
Summary: 
    The flags is the first field sent followed by the Temperature Measurement Value.            
		
Examples: 
      If the value of bit 1 of the Flags field is 0 and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of two fields in this order; Flags and Temperature Measurement Value.
     
      If the value of bit 1 of the Flags field is 1 (Time Stamp) and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Time Stamp.
     
      If the value of bit 1 of the Flags field is 1 and bit 2 is 1 (Time Stamp and Temperature Type), the structure of the Temperature Measurement characteristic consists of four fields in this order: Flags, Temperature Measurement Value, Time Stamp and Temperature Type.
     
      If the value of bit 1 of the Flags field is 0 and bit 2 is 1 (Temperature Type), the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Temperature Type.
    </informativeText>
      <value length="17" type="hex" variable_length="false"/>
      <properties>
        <indicate authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>

    <!--Temperature Measurement-->
    <characteristic const="false" id="temperature_measurement_5" name="Temperature Measurement" sourceId="org.bluetooth.characteristic.temperature_measurement" uuid="2A1C">
      <informativeText>Abstract: 
    The Temperature Measurement characteristic is a variable length structure containing a Flags field, a Temperature Measurement Value field and, based upon the contents of the Flags field, optionally a Time Stamp field and/or a Temperature Type field.            
		
Summary: 
    The flags is the first field sent followed by the Temperature Measurement Value.            
		
Examples: 
      If the value of bit 1 of the Flags field is 0 and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of two fields in this order; Flags and Temperature Measurement Value.
     
      If the value of bit 1 of the Flags field is 1 (Time Stamp) and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Time Stamp.
     
      If the value of bit 1 of the Flags field is 1 and bit 2 is 1 (Time Stamp and Temperature Type), the structure of the Temperature Measurement characteristic consists of four fields in this order: Flags, Temperature Measurement Value, Time Stamp and Temperature Type.
     
      If the value of bit 1 of the Flags field is 0 and bit 2 is 1 (Temperature Type), the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Temperature Type.
    </informativeText>
      <value length="17" type="hex" variable_length="false"/>
      <properties>
        <indicate authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>

    <!--Temperature Measurement-->
    <characteristic const="false" id="temperature_measurement_6" name="Temperature Measurement" sourceId="org.bluetooth.characteristic.temperature_measurement" uuid="2A1C">
      <informativeText>Abstract: 
    The Temperature Measurement characteristic is a variable length structure containing a Flags field, a Temperature Measurement Value field and, based upon the contents of the Flags field, optionally a Time Stamp field and/or a Temperature Type field.            
		
Summary: 
    The flags is the first field sent followed by the Temperature Measurement Value.            
		
Examples: 
      If the value of bit 1 of the Flags field is 0 and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of two fields in this order; Flags and Temperature Measurement Value.
     
      If the value of bit 1 of the Flags field is 1 (Time Stamp) and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Time Stamp.
     
      If the value of bit 1 of the Flags field is 1 and bit 2 is 1 (Time Stamp and Temperature Type), the structure of the Temperature Measurement characteristic consists of four fields in this order: Flags, Temperature Measurement Value, Time Stamp and Temperature Type.
     
      If the value of bit 1 of the Flags field is 0 and bit 2 is 1 (Temperature Type), the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Temperature Type.
    </informativeText>
      <value length="17" type="hex" variable_length="false"/>
      <properties>
        <indicate authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>

    <!--Temperature Measurement-->
    <characteristic const="false" id="temperature_measurement_7" name="Temperature Measurement" sourceId="org.bluetooth.characteristic.temperature_measurement" uuid="2A1C">
      <informativeText>Abstract: 
    The Temperature Measurement characteristic is a variable length structure containing a Flags field, a Temperature Measurement Value field and, based upon the contents of the Flags field, optionally a Time Stamp field and/or a Temperature Type field.            
		
Summary: 
    The flags is the first field sent followed by the Temperature Measurement Value.            
		
Examples: 
      If the value of bit 1 of the Flags field is 0 and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of two fields in this order; Flags and Temperature Measurement Value.
     
      If the value of bit 1 of the Flags field is 1 (Time Stamp) and bit 2 is 0, the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Time Stamp.
     
      If the value of bit 1 of the Flags field is 1 and bit 2 is 1 (Time Stamp and Temperature Type), the structure of the Temperature Measurement characteristic consists of four fields in this order: Flags, Temperature Measurement Value, Time Stamp and Temperature Type.
     
      If the value of bit 1 of the Flags field is 0 and bit 2 is 1 (Temperature Type), the structure of the Temperature Measurement characteristic consists of three fields in this order: Flags, Temperature Measurement Value and Temperature Type.
    </informativeText>
      <value length="17" type="hex" variable_length="false"/>
      <properties>
        <indicate authenticated="false" bonded="false" encrypted="false"/>
      </properties>
    </characteristic>
  </service>
</gatt>
