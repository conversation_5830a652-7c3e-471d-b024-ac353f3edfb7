{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Advertiser<span id=\"advertiser\" class=\"self-anchor\"><a class=\"perm\" href=\"#advertiser\">#</a></span></h1><p style=\"color:inherit\">Advertiser. </p><p style=\"color:inherit\">This is the base class of legacy, extended, and periodic advertisings for common functionalities including advertising set management, TX power setting, advertising address, and so on.</p><p style=\"color:inherit\">On an advertising set, either the legacy or extended advertising can be enabled at a time but they cannot be enabled simultaneously on the same advertising set. For example, the following sequence shows how to start the legacy advertising on an advertising set. Starting the extended advertising is similar. The only difference is to use the extended_advertiser API class.</p><ol style=\"list-style:decimal\"><li><p style=\"color:inherit\">Create an advertise set with the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-create-set\" target=\"_blank\" rel=\"\">sl_bt_advertiser_create_set</a> command.</p></li><li><p style=\"color:inherit\">Configure and set advertising parameters for the advertising set as needed.</p></li><li><p style=\"color:inherit\">Set the advertising data with the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-set-data\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_set_data</a> or <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-generate-data\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_generate_data</a> command.</p></li><li><p style=\"color:inherit\">Start the legacy advertising with the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_start</a> command.</p></li></ol><p style=\"color:inherit\">Periodic advertising can be enabled independently on the advertising set regardless of the state of the legacy or extended advertising. However, to ensure that scanners can find the periodic advertising information and establish a synchronization, the extended advertising must be enabled simultaneously with the periodic advertising.</p><p style=\"color:inherit\">When the bluetooth_feature_legacy_advertiser, bluetooth_feature_extended_advertiser or bluetooth_feature_periodic_advertiser component is included by the application, commands that have been superseded by the new classes are no longer available for use in the advertiser class. Calling them will receive SL_STATUS_NOT_SUPPORTED error code. These commands are as follows: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-phy\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_phy</a>, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-configuration\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_configuration</a>, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-clear-configuration\" target=\"_blank\" rel=\"\">sl_bt_advertiser_clear_configuration</a>, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-data\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_data</a>, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-long-data\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_long_data</a>, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_advertiser_start</a>, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-start-periodic-advertising\" target=\"_blank\" rel=\"\">sl_bt_advertiser_start_periodic_advertising</a>, and <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-stop-periodic-advertising\" target=\"_blank\" rel=\"\">sl_bt_advertiser_stop_periodic_advertising</a>. See the command descriptions for the replacements. </p><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser-flags\" target=\"_blank\" rel=\"\">Generic Advertising Configuration Flags</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-advertiser-timeout\" target=\"_blank\" rel=\"\">sl_bt_evt_advertiser_timeout</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-advertiser-scan-request\" target=\"_blank\" rel=\"\">sl_bt_evt_advertiser_scan_request</a></p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-connection-mode-t\">sl_bt_advertiser_connection_mode_t</a> {</div><div class=\"enum\">sl_bt_advertiser_non_connectable = 0x0</div><div class=\"enum\">sl_bt_advertiser_connectable_scannable = 0x2</div><div class=\"enum\">sl_bt_advertiser_scannable_non_connectable = 0x3</div><div class=\"enum\">sl_bt_advertiser_connectable_non_scannable = 0x4</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values define the available connection modes, which indicate whether the device accepts connection requests or scan requests. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-discovery-mode-t\">sl_bt_advertiser_discovery_mode_t</a> {</div><div class=\"enum\">sl_bt_advertiser_non_discoverable = 0x0</div><div class=\"enum\">sl_bt_advertiser_limited_discoverable = 0x1</div><div class=\"enum\">sl_bt_advertiser_general_discoverable = 0x2</div><div class=\"enum\">sl_bt_advertiser_broadcast = 0x3</div><div class=\"enum\">sl_bt_advertiser_user_data = 0x4</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values define the available discovery modes, which dictate how the device is visible to other devices in the legacy and extended advertising. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-adv-address-type-t\">sl_bt_advertiser_adv_address_type_t</a> {</div><div class=\"enum\">sl_bt_advertiser_identity_address = 0x0</div><div class=\"enum\">sl_bt_advertiser_non_resolvable = 0x1</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Address type to use for the legacy and extended advertising. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-packet-type-t\">sl_bt_advertiser_packet_type_t</a> {</div><div class=\"enum\">sl_bt_advertiser_advertising_data_packet = 0x0</div><div class=\"enum\">sl_bt_advertiser_scan_response_packet = 0x1</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values define the packet types in legacy and extended advertising. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-create-set\">sl_bt_advertiser_create_set</a>(uint8_t *handle)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-configure\">sl_bt_advertiser_configure</a>(uint8_t advertising_set, uint32_t flags)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-set-timing\">sl_bt_advertiser_set_timing</a>(uint8_t advertising_set, uint32_t interval_min, uint32_t interval_max, uint16_t duration, uint8_t maxevents)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-set-channel-map\">sl_bt_advertiser_set_channel_map</a>(uint8_t advertising_set, uint8_t channel_map)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-set-tx-power\">sl_bt_advertiser_set_tx_power</a>(uint8_t advertising_set, int16_t power, int16_t *set_power)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-set-report-scan-request\">sl_bt_advertiser_set_report_scan_request</a>(uint8_t advertising_set, uint8_t report_scan_req)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-set-random-address\">sl_bt_advertiser_set_random_address</a>(uint8_t advertising_set, uint8_t addr_type, bd_addr address, bd_addr *address_out)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-clear-random-address\">sl_bt_advertiser_clear_random_address</a>(uint8_t advertising_set)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-stop\">sl_bt_advertiser_stop</a>(uint8_t advertising_set)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-delete-set\">sl_bt_advertiser_delete_set</a>(uint8_t advertising_set)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-set-phy\">sl_bt_advertiser_set_phy</a>(uint8_t advertising_set, uint8_t primary_phy, uint8_t secondary_phy)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-set-configuration\">sl_bt_advertiser_set_configuration</a>(uint8_t advertising_set, uint32_t configurations)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-clear-configuration\">sl_bt_advertiser_clear_configuration</a>(uint8_t advertising_set, uint32_t configurations)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-set-data\">sl_bt_advertiser_set_data</a>(uint8_t advertising_set, uint8_t packet_type, size_t adv_data_len, const uint8_t *adv_data)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-set-long-data\">sl_bt_advertiser_set_long_data</a>(uint8_t advertising_set, uint8_t packet_type)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-start\">sl_bt_advertiser_start</a>(uint8_t advertising_set, uint8_t discover, uint8_t connect)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-start-periodic-advertising\">sl_bt_advertiser_start_periodic_advertising</a>(uint8_t advertising_set, uint16_t interval_min, uint16_t interval_max, uint32_t flags)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">SL_BGAPI_DEPRECATED sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-advertiser-stop-periodic-advertising\">sl_bt_advertiser_stop_periodic_advertising</a>(uint8_t advertising_set)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-create-set-id\">sl_bt_cmd_advertiser_create_set_id</a> 0x01040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-configure-id\">sl_bt_cmd_advertiser_configure_id</a> 0x12040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-set-timing-id\">sl_bt_cmd_advertiser_set_timing_id</a> 0x03040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-set-channel-map-id\">sl_bt_cmd_advertiser_set_channel_map_id</a> 0x04040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-set-tx-power-id\">sl_bt_cmd_advertiser_set_tx_power_id</a> 0x0b040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-set-report-scan-request-id\">sl_bt_cmd_advertiser_set_report_scan_request_id</a> 0x05040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-set-random-address-id\">sl_bt_cmd_advertiser_set_random_address_id</a> 0x10040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-clear-random-address-id\">sl_bt_cmd_advertiser_clear_random_address_id</a> 0x11040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-stop-id\">sl_bt_cmd_advertiser_stop_id</a> 0x0a040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-delete-set-id\">sl_bt_cmd_advertiser_delete_set_id</a> 0x02040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-set-phy-id\">sl_bt_cmd_advertiser_set_phy_id</a> 0x06040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-set-configuration-id\">sl_bt_cmd_advertiser_set_configuration_id</a> 0x07040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-clear-configuration-id\">sl_bt_cmd_advertiser_clear_configuration_id</a> 0x08040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-set-data-id\">sl_bt_cmd_advertiser_set_data_id</a> 0x0f040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-set-long-data-id\">sl_bt_cmd_advertiser_set_long_data_id</a> 0x0e040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-start-id\">sl_bt_cmd_advertiser_start_id</a> 0x09040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-start-periodic-advertising-id\">sl_bt_cmd_advertiser_start_periodic_advertising_id</a> 0x0c040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-advertiser-stop-periodic-advertising-id\">sl_bt_cmd_advertiser_stop_periodic_advertising_id</a> 0x0d040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-create-set-id\">sl_bt_rsp_advertiser_create_set_id</a> 0x01040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-configure-id\">sl_bt_rsp_advertiser_configure_id</a> 0x12040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-set-timing-id\">sl_bt_rsp_advertiser_set_timing_id</a> 0x03040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-set-channel-map-id\">sl_bt_rsp_advertiser_set_channel_map_id</a> 0x04040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-set-tx-power-id\">sl_bt_rsp_advertiser_set_tx_power_id</a> 0x0b040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-set-report-scan-request-id\">sl_bt_rsp_advertiser_set_report_scan_request_id</a> 0x05040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-set-random-address-id\">sl_bt_rsp_advertiser_set_random_address_id</a> 0x10040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-clear-random-address-id\">sl_bt_rsp_advertiser_clear_random_address_id</a> 0x11040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-stop-id\">sl_bt_rsp_advertiser_stop_id</a> 0x0a040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-delete-set-id\">sl_bt_rsp_advertiser_delete_set_id</a> 0x02040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-set-phy-id\">sl_bt_rsp_advertiser_set_phy_id</a> 0x06040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-set-configuration-id\">sl_bt_rsp_advertiser_set_configuration_id</a> 0x07040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-clear-configuration-id\">sl_bt_rsp_advertiser_clear_configuration_id</a> 0x08040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-set-data-id\">sl_bt_rsp_advertiser_set_data_id</a> 0x0f040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-set-long-data-id\">sl_bt_rsp_advertiser_set_long_data_id</a> 0x0e040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-start-id\">sl_bt_rsp_advertiser_start_id</a> 0x09040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-start-periodic-advertising-id\">sl_bt_rsp_advertiser_start_periodic_advertising_id</a> 0x0c040020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-advertiser-stop-periodic-advertising-id\">sl_bt_rsp_advertiser_stop_periodic_advertising_id</a> 0x0d040020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>sl_bt_advertiser_connection_mode_t<span id=\"sl-bt-advertiser-connection-mode-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-connection-mode-t\">#</a></span></h3><blockquote>sl_bt_advertiser_connection_mode_t</blockquote><p style=\"color:inherit\">These values define the available connection modes, which indicate whether the device accepts connection requests or scan requests. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_advertiser_non_connectable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Non-connectable non-scannable </p></td></tr><tr><td class=\"fieldname\">sl_bt_advertiser_connectable_scannable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) Undirected connectable scannable. This mode can only be used in legacy advertising PDUs. </p></td></tr><tr><td class=\"fieldname\">sl_bt_advertiser_scannable_non_connectable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x3) Undirected scannable (Non-connectable but responds to scan requests) </p></td></tr><tr><td class=\"fieldname\">sl_bt_advertiser_connectable_non_scannable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x4) Undirected connectable non-scannable. This mode can only be used in extended advertising PDUs. </p></td></tr></tbody></table><br><div>Definition at line <code>1982</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_discovery_mode_t<span id=\"sl-bt-advertiser-discovery-mode-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-discovery-mode-t\">#</a></span></h3><blockquote>sl_bt_advertiser_discovery_mode_t</blockquote><p style=\"color:inherit\">These values define the available discovery modes, which dictate how the device is visible to other devices in the legacy and extended advertising. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_advertiser_non_discoverable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Not discoverable </p></td></tr><tr><td class=\"fieldname\">sl_bt_advertiser_limited_discoverable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Discoverable by both limited and general discovery procedures </p></td></tr><tr><td class=\"fieldname\">sl_bt_advertiser_general_discoverable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) Discoverable by the general discovery procedure </p></td></tr><tr><td class=\"fieldname\">sl_bt_advertiser_broadcast</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x3) Device is not discoverable in either limited or generic discovery procedure but may be discovered using the Observation procedure. </p></td></tr><tr><td class=\"fieldname\">sl_bt_advertiser_user_data</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x4) Send advertising and/or scan response data defined by the user. The discovery mode is defined by the user. </p></td></tr></tbody></table><br><div>Definition at line <code>2009</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_adv_address_type_t<span id=\"sl-bt-advertiser-adv-address-type-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-adv-address-type-t\">#</a></span></h3><blockquote>sl_bt_advertiser_adv_address_type_t</blockquote><p style=\"color:inherit\">Address type to use for the legacy and extended advertising. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_advertiser_identity_address</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Use public or static device address, or an identity address if privacy mode is enabled. </p></td></tr><tr><td class=\"fieldname\">sl_bt_advertiser_non_resolvable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Use non-resolvable address type; the advertising must be non-connectable. </p></td></tr></tbody></table><br><div>Definition at line <code>2033</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_packet_type_t<span id=\"sl-bt-advertiser-packet-type-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-packet-type-t\">#</a></span></h3><blockquote>sl_bt_advertiser_packet_type_t</blockquote><p style=\"color:inherit\">These values define the packet types in legacy and extended advertising. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_advertiser_advertising_data_packet</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Advertising data packet </p></td></tr><tr><td class=\"fieldname\">sl_bt_advertiser_scan_response_packet</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Scan response packet </p></td></tr></tbody></table><br><div>Definition at line <code>2048</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_advertiser_create_set<span id=\"sl-bt-advertiser-create-set\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-create-set\">#</a></span></h3><blockquote>sl_status_t sl_bt_advertiser_create_set (uint8_t * handle)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[out]</td><td class=\"paramname\">handle</td><td><p style=\"color:inherit\">Advertising set handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Create an advertising set that can be used for legacy, extended, or periodic advertising. The handle of the created advertising set is returned in response if the operation succeeds.</p><p style=\"color:inherit\">The maximum number of advertising sets for user advertisers is limited by the SL_BT_CONFIG_USER_ADVERTISERS configuration.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2213</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_configure<span id=\"sl-bt-advertiser-configure\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-configure\">#</a></span></h3><blockquote>sl_status_t sl_bt_advertiser_configure (uint8_t advertising_set, uint32_t flags)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">Configuration flags. Value: 0 or bitmask of <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser-flags\" target=\"_blank\" rel=\"\">Generic Advertising Configuration Flags</a></p><p style=\"color:inherit\">Default value: 0 </p></td></tr></tbody></table></div><p style=\"color:inherit\">Configure the legacy and extended advertising on an advertising set. The configuration will take effect next time the legacy or extended advertising is enabled.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2231</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_set_timing<span id=\"sl-bt-advertiser-set-timing\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-set-timing\">#</a></span></h3><blockquote>sl_status_t sl_bt_advertiser_set_timing (uint8_t advertising_set, uint32_t interval_min, uint32_t interval_max, uint16_t duration, uint8_t maxevents)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">interval_min</td><td><p style=\"color:inherit\">Minimum advertising interval. Value in units of 0.625 ms</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x20 to 0xFFFFFF</p></li><li><p style=\"color:inherit\">Time range: 20 ms to 10485.759375 s</p></li></ul><p style=\"color:inherit\">Default value: 100 ms </p></td></tr><tr><td>[in]</td><td class=\"paramname\">interval_max</td><td><p style=\"color:inherit\">Maximum advertising interval. Value in units of 0.625 ms</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x20 to 0xFFFFFF</p></li><li><p style=\"color:inherit\">Time range: 20 ms to 10485.759375 s</p></li><li><p style=\"color:inherit\">Note: interval_max should be bigger than interval_min</p></li></ul><p style=\"color:inherit\">Default value: 200 ms </p></td></tr><tr><td>[in]</td><td class=\"paramname\">duration</td><td><p style=\"color:inherit\">Advertising duration for this advertising set. Value 0 indicates no advertising duration limit and advertising continues until it is disabled. A non-zero value sets the duration in units of 10 ms. The duration begins at the start of the first advertising event of this advertising set.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x0001 to 0xFFFF</p></li><li><p style=\"color:inherit\">Time range: 10 ms to 655.35 s</p></li></ul><p style=\"color:inherit\">Default value: 0 </p></td></tr><tr><td>[in]</td><td class=\"paramname\">maxevents</td><td><p style=\"color:inherit\">If non-zero, indicates the maximum number of advertising events to send before the advertiser is stopped. Value 0 indicates no maximum number limit.</p><p style=\"color:inherit\">Default value: 0 </p></td></tr></tbody></table></div><p style=\"color:inherit\">Set the timing parameters for legacy or extended advertising on an advertising set. This setting will take effect next time the legacy or extended advertising is enabled.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2276</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_set_channel_map<span id=\"sl-bt-advertiser-set-channel-map\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-set-channel-map\">#</a></span></h3><blockquote>sl_status_t sl_bt_advertiser_set_channel_map (uint8_t advertising_set, uint8_t channel_map)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">channel_map</td><td><p style=\"color:inherit\">Advertising channel map which determines, which of the three channels will be used for advertising. This value is given as a bitmask. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>1:</strong> Advertise on CH37</p></li><li><p style=\"color:inherit\"><strong>2:</strong> Advertise on CH38</p></li><li><p style=\"color:inherit\"><strong>3:</strong> Advertise on CH37 and CH38</p></li><li><p style=\"color:inherit\"><strong>4:</strong> Advertise on CH39</p></li><li><p style=\"color:inherit\"><strong>5:</strong> Advertise on CH37 and CH39</p></li><li><p style=\"color:inherit\"><strong>6:</strong> Advertise on CH38 and CH39</p></li><li><p style=\"color:inherit\"><strong>7:</strong> Advertise on all channels</p></li></ul><p style=\"color:inherit\">Recommended value: 7</p><p style=\"color:inherit\">Default value: 7 </p></td></tr></tbody></table></div><p style=\"color:inherit\">Set the primary advertising channel map on an advertising set. This setting will take effect next time when the legacy or extended advertising is enabled.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2308</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_set_tx_power<span id=\"sl-bt-advertiser-set-tx-power\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-set-tx-power\">#</a></span></h3><blockquote>sl_status_t sl_bt_advertiser_set_tx_power (uint8_t advertising_set, int16_t power, int16_t * set_power)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">power</td><td><p style=\"color:inherit\">TX power in 0.1 dBm steps. For example, the value of 10 is 1 dBm and 55 is 5.5 dBm. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">set_power</td><td><p style=\"color:inherit\">The selected maximum advertising TX power</p></td></tr></tbody></table></div><p style=\"color:inherit\">Limit the maximum advertising TX power on an advertising set. If the value goes over the global value that was set using the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-set-tx-power\" target=\"_blank\" rel=\"\">sl_bt_system_set_tx_power</a> command, the global value will be the maximum limit. The maximum TX power of legacy advertising is further constrained to be less than +10 dBm. The extended advertising and periodic advertising TX power can be +10 dBm and over if Adaptive Frequency Hopping is enabled.</p><p style=\"color:inherit\">This setting will take effect next time the legacy, extended or periodic advertising is enabled.</p><p style=\"color:inherit\">By default, maximum advertising TX power is limited by the global value.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2333</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_set_report_scan_request<span id=\"sl-bt-advertiser-set-report-scan-request\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-set-report-scan-request\">#</a></span></h3><blockquote>sl_status_t sl_bt_advertiser_set_report_scan_request (uint8_t advertising_set, uint8_t report_scan_req)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">report_scan_req</td><td><p style=\"color:inherit\">If non-zero, enables scan request notification and scan requests will be reported as events.</p><p style=\"color:inherit\">Default value: 0 </p></td></tr></tbody></table></div><p style=\"color:inherit\">Enable or disable the scan request notification on an advertising set. This setting will take effect next time the legacy or extended advertising is enabled.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-advertiser-scan-request\" target=\"_blank\" rel=\"\">sl_bt_evt_advertiser_scan_request</a> - Triggered when a scan request is received during advertising if the scan request notification is enabled by this command. </p></li></ul><br><div>Definition at line <code>2359</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_set_random_address<span id=\"sl-bt-advertiser-set-random-address\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-set-random-address\">#</a></span></h3><blockquote>sl_status_t sl_bt_advertiser_set_random_address (uint8_t advertising_set, uint8_t addr_type, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> address, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> * address_out)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">addr_type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>. Address type. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_resolvable_address (0x2):</strong> Resolvable private random address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_nonresolvable_address (0x3):</strong> Non-resolvable private random address. This type can only be used for non-connectable advertising. </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">address</td><td><p style=\"color:inherit\">The random address to set. Ignore this field when setting a resolvable random address. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">address_out</td><td><p style=\"color:inherit\">The resolvable random address set for the advertiser. Ignore this field when setting other types of random addresses.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set the advertiser on an advertising set to use a random address. This overrides the default advertiser address, which is either the public device address programmed at production or the address written into persistent storage using <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-set-identity-address\" target=\"_blank\" rel=\"\">sl_bt_system_set_identity_address</a> command. This setting is stored in RAM only and does not change the identity address in persistent storage. In privacy mode, the stack does not change an advertiser address set by this command. To ensure that the stack can manage the address update periodically in privacy mode, the address setting should be removed with the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-clear-random-address\" target=\"_blank\" rel=\"\">sl_bt_advertiser_clear_random_address</a> command.</p><p style=\"color:inherit\">When setting a resolvable random address, the address parameter is ignored. The stack generates one and set it as the advertiser address. The generated address is returned in the response. To enhance the privacy, the application should schedule periodic address updates by calling this command periodically. Use different schedules for different advertising sets.</p><p style=\"color:inherit\">To use the default advertiser address, remove this setting using <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-clear-random-address\" target=\"_blank\" rel=\"\">sl_bt_advertiser_clear_random_address</a> command.</p><p style=\"color:inherit\">Wrong state error is returned if advertising has been enabled on the advertising set. Invalid parameter error is returned if the advertising set handle is invalid or the address does not conform to the Bluetooth specification.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2405</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_clear_random_address<span id=\"sl-bt-advertiser-clear-random-address\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-clear-random-address\">#</a></span></h3><blockquote>sl_status_t sl_bt_advertiser_clear_random_address (uint8_t advertising_set)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Clear the random address previously set for the advertiser address on an advertising set. To set a random address, use <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-random-address\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_random_address</a> command. The default advertiser address will be used after this operation.</p><p style=\"color:inherit\">The error SL_STATUS_INVALID_STATE is returned if advertising has been enabled on the advertising set. An invalid parameter error is returned if the advertising set handle is invalid.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2426</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_stop<span id=\"sl-bt-advertiser-stop\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-stop\">#</a></span></h3><blockquote>sl_status_t sl_bt_advertiser_stop (uint8_t advertising_set)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Stop the legacy or extended advertising on an advertising set. Counterpart with <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_start</a> or <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_start</a>.</p><p style=\"color:inherit\">This command does not affect the enable state of the periodic advertising on the advertising set, i.e., periodic advertising is not stopped.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2442</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_delete_set<span id=\"sl-bt-advertiser-delete-set\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-delete-set\">#</a></span></h3><blockquote>sl_status_t sl_bt_advertiser_delete_set (uint8_t advertising_set)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Delete an advertising set. Any enabled legacy, extended, or periodic advertising is stopped before the deletion.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2454</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_set_phy<span id=\"sl-bt-advertiser-set-phy\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-set-phy\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_advertiser_set_phy (uint8_t advertising_set, uint8_t primary_phy, uint8_t secondary_phy)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">primary_phy</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-phy-t\" target=\"_blank\" rel=\"\">sl_bt_gap_phy_t</a>. The PHY on which the advertising packets are transmitted on the primary advertising channel. If legacy advertising PDUs are used, 1M PHY must be used. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_1m (0x1):</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coded (0x4):</strong> Coded PHY, 125k (S=8)</p></li></ul><p style=\"color:inherit\">Default value: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-phy-1m\" target=\"_blank\" rel=\"\">sl_bt_gap_phy_1m</a></p></td></tr><tr><td>[in]</td><td class=\"paramname\">secondary_phy</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-phy-t\" target=\"_blank\" rel=\"\">sl_bt_gap_phy_t</a>. The PHY on which the advertising packets are transmitted on the secondary advertising channel. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_1m (0x1):</strong> 1M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_2m (0x2):</strong> 2M PHY</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_phy_coded (0x4):</strong> Coded PHY, 125k (S=8)</p></li></ul><p style=\"color:inherit\">Default value: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-phy-1m\" target=\"_blank\" rel=\"\">sl_bt_gap_phy_1m</a></p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-set-phy\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_set_phy</a>.</p><p style=\"color:inherit\">Set the primary and secondary advertising PHYs used for extended and periodic advertising on an advertising set. This setting will take effect next time extended or periodic advertising is enabled. When advertising on the LE Coded PHY, coding scheme S=8 is used. The SL_STATUS_INVALID_PARAMETER error is returned if a PHY value is invalid or the device does not support a given PHY.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2490</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_set_configuration<span id=\"sl-bt-advertiser-set-configuration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-set-configuration\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_advertiser_set_configuration (uint8_t advertising_set, uint32_t configurations)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">configurations</td><td><p style=\"color:inherit\">Advertising configuration flags to enable. This value can be a bitmask of multiple flags. Flags:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>1 (Bit 0):</strong> Use legacy advertising PDUs.</p></li><li><p style=\"color:inherit\"><strong>2 (Bit 1):</strong> Omit advertiser's address from all PDUs (anonymous advertising). This flag is effective only in extended advertising.</p></li><li><p style=\"color:inherit\"><strong>4 (Bit 2):</strong> Use a non-resolvable private address. When this configuration is enabled, the advertising must use non-connectable mode. The stack generates a non-resolvable private address for the advertising set and the stack will update the address periodically when the privacy mode is enabled. This configuration is ignored if the advertiser address has been set with the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-random-address\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_random_address</a> command.</p></li><li><p style=\"color:inherit\"><strong>8 (Bit 3):</strong> Include TX power in advertising packets. This flag is effective only in extended advertising.</p></li><li><p style=\"color:inherit\"><strong>16 (Bit 4):</strong> Use the device identity address when the privacy mode is enabled in the stack. This configuration is ignored if the configuration of using non-resolvable private address is enabled or the advertising address has been set with the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-random-address\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_random_address</a> command.</p></li></ul><p style=\"color:inherit\">Default value: 1 </p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-configure\" target=\"_blank\" rel=\"\">sl_bt_advertiser_configure</a> command.</p><p style=\"color:inherit\">Enable advertising configuration flags on an advertising set. The configuration change will take effect next time the legacy or extended advertising is enabled.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2530</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_clear_configuration<span id=\"sl-bt-advertiser-clear-configuration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-clear-configuration\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_advertiser_clear_configuration (uint8_t advertising_set, uint32_t configurations)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">configurations</td><td><p style=\"color:inherit\">Advertising configuration flags to disable. This value can be a bitmask of multiple flags. See <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-configuration\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_configuration</a> for possible flags.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-configure\" target=\"_blank\" rel=\"\">sl_bt_advertiser_configure</a> command.</p><p style=\"color:inherit\">Disable advertising configuration flags on an advertising set. The configuration change will take effect next time the legacy or extended advertising is enabled.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2549</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_set_data<span id=\"sl-bt-advertiser-set-data\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-set-data\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_advertiser_set_data (uint8_t advertising_set, uint8_t packet_type, size_t adv_data_len, const uint8_t * adv_data)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">packet_type</td><td><p style=\"color:inherit\">This value selects whether data is intended for advertising packets, scan response packets, or periodic advertising packets.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Advertising packets</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Scan response packets</p></li><li><p style=\"color:inherit\"><strong>8:</strong> Periodic advertising packets </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">adv_data_len</td><td><p style=\"color:inherit\">Length of data in <code>adv_data</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">adv_data</td><td><p style=\"color:inherit\">Data to be set</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-set-data\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_set_data</a> for legacy advertising PDUs, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-set-data\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_set_data</a> for extended advertising PDUs, and <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-periodic-advertiser#sl-bt-periodic-advertiser-set-data\" target=\"_blank\" rel=\"\">sl_bt_periodic_advertiser_set_data</a> for periodic advertising PDUs.</p><p style=\"color:inherit\">Set user-defined data in advertising packets, scan response packets, or periodic advertising packets. Maximum 31 bytes of data can be set for legacy advertising. Maximum 191 bytes of data can be set for connectable extended advertising. Maximum 253 bytes of data can be set for periodic and non-connectable extended advertising. For setting longer advertising data, use command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-long-data\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_long_data</a>.</p><p style=\"color:inherit\">If advertising mode is currently enabled, the new advertising data will be used immediately. Advertising mode can be enabled using command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_advertiser_start</a>. Periodic advertising mode can be enabled using command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-start-periodic-advertising\" target=\"_blank\" rel=\"\">sl_bt_advertiser_start_periodic_advertising</a>.</p><p style=\"color:inherit\">The invalid parameter error will be returned in the following situations:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Data length is more than 31 bytes but the advertiser can only advertise using legacy advertising PDUs.</p></li><li><p style=\"color:inherit\">Data is too long to fit into a single advertisement.</p></li><li><p style=\"color:inherit\">Set data of the advertising data packet when the scannable advertising is enabled using extended advertising PDUs.</p></li><li><p style=\"color:inherit\">Set data of the scan response data packet when the connectable advertising is enabled using extended advertising PDUs.</p></li></ul><p style=\"color:inherit\">Note that the user-defined data may be overwritten by the system when the advertising is later enabled in a discovery mode other than user_data.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2596</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_set_long_data<span id=\"sl-bt-advertiser-set-long-data\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-set-long-data\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_advertiser_set_long_data (uint8_t advertising_set, uint8_t packet_type)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">packet_type</td><td><p style=\"color:inherit\">This value selects whether data is intended for advertising packets, scan response packets, or periodic advertising packets. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Advertising packets</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Scan response packets</p></li><li><p style=\"color:inherit\"><strong>8:</strong> Periodic advertising packets</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-set-long-data\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_set_long_data</a> for extended advertising PDUs and <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-periodic-advertiser#sl-bt-periodic-advertiser-set-long-data\" target=\"_blank\" rel=\"\">sl_bt_periodic_advertiser_set_long_data</a> for periodic advertising PDUs.</p><p style=\"color:inherit\">Set advertising data for a specified packet type and advertising set. Data currently in the system data buffer will be extracted as the advertising data. The buffer will be emptied after this command regardless of the completion status.</p><p style=\"color:inherit\">Prior to calling this command, add data to the buffer with one or multiple calls to <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system#sl-bt-system-data-buffer-write\" target=\"_blank\" rel=\"\">sl_bt_system_data_buffer_write</a>.</p><p style=\"color:inherit\">Maximum 31 bytes of data can be set for legacy advertising. Maximum 191 bytes of data can be set for connectable extended advertising. Maximum 1650 bytes of data can be set for periodic and non-connectable extended advertising, but advertising parameters may limit the amount of data that can be sent in a single advertisement.</p><p style=\"color:inherit\">See <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-data\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_data</a> for more details on advertising data.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2634</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_start<span id=\"sl-bt-advertiser-start\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-start\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_advertiser_start (uint8_t advertising_set, uint8_t discover, uint8_t connect)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">discover</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-discovery-mode-t\" target=\"_blank\" rel=\"\">sl_bt_advertiser_discovery_mode_t</a>. Discovery mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_non_discoverable (0x0):</strong> Not discoverable</p></li><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_limited_discoverable (0x1):</strong> Discoverable by both limited and general discovery procedures</p></li><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_general_discoverable (0x2):</strong> Discoverable by the general discovery procedure</p></li><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_broadcast (0x3):</strong> Device is not discoverable in either limited or generic discovery procedure but may be discovered using the Observation procedure.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_user_data (0x4):</strong> Send advertising and/or scan response data defined by the user. The discovery mode is defined by the user. </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">connect</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-connection-mode-t\" target=\"_blank\" rel=\"\">sl_bt_advertiser_connection_mode_t</a>. Connection mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_non_connectable (0x0):</strong> Non-connectable non-scannable</p></li><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_connectable_scannable (0x2):</strong> Undirected connectable scannable. This mode can only be used in legacy advertising PDUs.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_scannable_non_connectable (0x3):</strong> Undirected scannable (Non-connectable but responds to scan requests)</p></li><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_connectable_non_scannable (0x4):</strong> Undirected connectable non-scannable. This mode can only be used in extended advertising PDUs.</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_start</a> and <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_start</a> commands.</p><p style=\"color:inherit\">Start the legacy or extended advertising on an advertising set with specified discovery and connection modes.</p><p style=\"color:inherit\">The number of concurrent connectable advertisings is limited by the number of connections reserved by the user application (the SL_BT_CONFIG_MAX_CONNECTIONS configuration) and the number reserved by other software components (the SL_BT_COMPONENT_CONNECTIONS configuration). This command fails with the connection limit exceeded error if it may cause the number of connections exceeding the configured value in future. For example, only one connectable advertising can be enabled if the device has (SL_BT_CONFIG_MAX_CONNECTIONS + SL_BT_COMPONENT_CONNECTIONS - 1) connections. This limitation does not apply to non-connectable advertising.</p><p style=\"color:inherit\">The default advertising configuration in the stack is set to using legacy advertising PDUs on 1M PHY. The stack will automatically select extended advertising PDUs if either of the following has occurred with the default configuration:</p><ol style=\"list-style:decimal\"><li><p style=\"color:inherit\">The connection mode is set to <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-connectable-non-scannable\" target=\"_blank\" rel=\"\">sl_bt_advertiser_connectable_non_scannable</a>.</p></li><li><p style=\"color:inherit\">The primary advertising PHY is set to Coded PHY by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-extended-advertiser#sl-bt-extended-advertiser-set-phy\" target=\"_blank\" rel=\"\">sl_bt_extended_advertiser_set_phy</a>.</p></li><li><p style=\"color:inherit\">The user advertising data length is more than 31 bytes.</p></li><li><p style=\"color:inherit\">Periodic advertising is enabled.</p></li></ol><p style=\"color:inherit\">This command fails with the invalid parameter error if one of the following cases occurs:</p><ol style=\"list-style:decimal\"><li><p style=\"color:inherit\">Non-resolvable random address is used but the connection mode is advertiser_connectable_scannable or advertiser_connectable_non_scannable.</p></li><li><p style=\"color:inherit\">advertiser_connectable_non_scannable is the connection mode but using legacy advertising PDUs has been explicitly enabled with command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-configuration\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_configuration</a>.</p></li><li><p style=\"color:inherit\">Coded PHY is the primary advertising PHY but using legacy advertising PDUs has been explicitly enabled with command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-configuration\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_configuration</a>.</p></li><li><p style=\"color:inherit\">advertiser_connectable_scannable is the connection mode but using extended advertising PDUs has been explicitly enabled or the primary advertising PHY is set to Coded PHY.</p></li></ol><p style=\"color:inherit\">If advertising is enabled in user_data mode, use <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-data\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_data</a> to set advertising and scan response data before issuing this command. When advertising is enabled in modes other than user_data, advertising and scan response data is generated by the stack using the following procedure:</p><ol style=\"list-style:decimal\"><li><p style=\"color:inherit\">Add a flags field to advertising data.</p></li><li><p style=\"color:inherit\">Add a TX power level field to advertising data if the TX power service exists in the local GATT database.</p></li><li><p style=\"color:inherit\">Add a peripheral connection interval range field to advertising data if the GAP peripheral preferred connection parameters characteristic exists in the local GATT database.</p></li><li><p style=\"color:inherit\">Add a list of 16-bit service UUIDs to advertising data if there are one or more 16-bit service UUIDs to advertise. The list is complete if all advertised 16-bit UUIDs are in advertising data. Otherwise, the list is incomplete.</p></li><li><p style=\"color:inherit\">Add a list of 128-bit service UUIDs to advertising data if there are one or more 128-bit service UUIDs to advertise and there is still free space for this field. The list is complete if all advertised 128-bit UUIDs are in advertising data. Otherwise, the list is incomplete. Note that an advertising data packet can contain at most one 128-bit service UUID.</p></li><li><p style=\"color:inherit\">Try to add the full local name to advertising data if the device is not in privacy mode. If the full local name does not fit into the remaining free space, the advertised name is a shortened version by cutting off the end if the free space has at least 6 bytes. Otherwise, the local name is added to scan response data.</p></li></ol><p style=\"color:inherit\">Event <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> will be received when a remote device opens a connection to the advertiser on this advertising set. As a result, the advertising stops.</p><p style=\"color:inherit\">Event <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-advertiser-timeout\" target=\"_blank\" rel=\"\">sl_bt_evt_advertiser_timeout</a> will be received when the number of advertising events set by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-timing\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_timing</a> command is done and the advertising has stopped.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-advertiser-timeout\" target=\"_blank\" rel=\"\">sl_bt_evt_advertiser_timeout</a> - Triggered when the number of advertising events set by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-timing\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_timing</a> command is done and advertising has stopped on an advertising set.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> - Triggered when a remote device opens a connection to the advertiser and the advertising has stopped. </p></li></ul><br><div>Definition at line <code>2752</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_start_periodic_advertising<span id=\"sl-bt-advertiser-start-periodic-advertising\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-start-periodic-advertising\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_advertiser_start_periodic_advertising (uint8_t advertising_set, uint16_t interval_min, uint16_t interval_max, uint32_t flags)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">interval_min</td><td><p style=\"color:inherit\">Minimum periodic advertising interval. Value in units of 1.25 ms</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x06 to 0xFFFF</p></li><li><p style=\"color:inherit\">Time range: 7.5 ms to 81.92 s</p></li></ul><p style=\"color:inherit\">Default value: 100 ms </p></td></tr><tr><td>[in]</td><td class=\"paramname\">interval_max</td><td><p style=\"color:inherit\">Maximum periodic advertising interval. Value in units of 1.25 ms</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x06 to 0xFFFF</p></li><li><p style=\"color:inherit\">Time range: 7.5 ms to 81.92 s</p></li><li><p style=\"color:inherit\">Note: interval_max should be bigger than interval_min</p></li></ul><p style=\"color:inherit\">Default value: 200 ms </p></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">Periodic advertising configurations. Bitmask of the following:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>Bit 0:</strong> Include TX power in advertising PDU</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-periodic-advertiser#sl-bt-periodic-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_periodic_advertiser_start</a> command.</p><p style=\"color:inherit\">Start periodic advertising on an advertising set. The stack enables the advertising set automatically if the set was not enabled and the set can advertise using extended advertising PDUs beside the syncInfo, which is needed for the periodic advertising.</p><p style=\"color:inherit\">The invalid parameter error is returned if the application has configured legacy advertising PDUs or anonymous advertising, or the advertising set is enabled using legacy advertising PDUs.</p><p style=\"color:inherit\">To stop periodic advertising, use <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-stop-periodic-advertising\" target=\"_blank\" rel=\"\">sl_bt_advertiser_stop_periodic_advertising</a> command with the handle received in response from this command.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2797</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_advertiser_stop_periodic_advertising<span id=\"sl-bt-advertiser-stop-periodic-advertising\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-advertiser-stop-periodic-advertising\">#</a></span></h3><blockquote>SL_BGAPI_DEPRECATED sl_status_t sl_bt_advertiser_stop_periodic_advertising (uint8_t advertising_set)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Deprecated</strong> and replaced by <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-periodic-advertiser#sl-bt-periodic-advertiser-stop\" target=\"_blank\" rel=\"\">sl_bt_periodic_advertiser_stop</a> command.</p><p style=\"color:inherit\">Stop periodic advertising on an advertising set. Counterpart with <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-start-periodic-advertising\" target=\"_blank\" rel=\"\">sl_bt_advertiser_start_periodic_advertising</a>.</p><p style=\"color:inherit\">This command does not affect the enable state of the advertising set, i.e., legacy or extended advertising is not stopped.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2818</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_advertiser_create_set_id<span id=\"sl-bt-cmd-advertiser-create-set-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-create-set-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_create_set_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01040020</pre><br><div>Definition at line <code>1941</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_configure_id<span id=\"sl-bt-cmd-advertiser-configure-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-configure-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_configure_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x12040020</pre><br><div>Definition at line <code>1942</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_set_timing_id<span id=\"sl-bt-cmd-advertiser-set-timing-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-set-timing-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_set_timing_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03040020</pre><br><div>Definition at line <code>1943</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_set_channel_map_id<span id=\"sl-bt-cmd-advertiser-set-channel-map-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-set-channel-map-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_set_channel_map_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x04040020</pre><br><div>Definition at line <code>1944</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_set_tx_power_id<span id=\"sl-bt-cmd-advertiser-set-tx-power-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-set-tx-power-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_set_tx_power_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0b040020</pre><br><div>Definition at line <code>1945</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_set_report_scan_request_id<span id=\"sl-bt-cmd-advertiser-set-report-scan-request-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-set-report-scan-request-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_set_report_scan_request_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x05040020</pre><br><div>Definition at line <code>1946</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_set_random_address_id<span id=\"sl-bt-cmd-advertiser-set-random-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-set-random-address-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_set_random_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x10040020</pre><br><div>Definition at line <code>1947</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_clear_random_address_id<span id=\"sl-bt-cmd-advertiser-clear-random-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-clear-random-address-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_clear_random_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x11040020</pre><br><div>Definition at line <code>1948</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_stop_id<span id=\"sl-bt-cmd-advertiser-stop-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-stop-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_stop_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0a040020</pre><br><div>Definition at line <code>1949</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_delete_set_id<span id=\"sl-bt-cmd-advertiser-delete-set-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-delete-set-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_delete_set_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02040020</pre><br><div>Definition at line <code>1950</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_set_phy_id<span id=\"sl-bt-cmd-advertiser-set-phy-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-set-phy-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_set_phy_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x06040020</pre><br><div>Definition at line <code>1951</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_set_configuration_id<span id=\"sl-bt-cmd-advertiser-set-configuration-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-set-configuration-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_set_configuration_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x07040020</pre><br><div>Definition at line <code>1952</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_clear_configuration_id<span id=\"sl-bt-cmd-advertiser-clear-configuration-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-clear-configuration-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_clear_configuration_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x08040020</pre><br><div>Definition at line <code>1953</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_set_data_id<span id=\"sl-bt-cmd-advertiser-set-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-set-data-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_set_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0f040020</pre><br><div>Definition at line <code>1954</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_set_long_data_id<span id=\"sl-bt-cmd-advertiser-set-long-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-set-long-data-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_set_long_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0e040020</pre><br><div>Definition at line <code>1955</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_start_id<span id=\"sl-bt-cmd-advertiser-start-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-start-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_start_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x09040020</pre><br><div>Definition at line <code>1956</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_start_periodic_advertising_id<span id=\"sl-bt-cmd-advertiser-start-periodic-advertising-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-start-periodic-advertising-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_start_periodic_advertising_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0c040020</pre><br><div>Definition at line <code>1957</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_advertiser_stop_periodic_advertising_id<span id=\"sl-bt-cmd-advertiser-stop-periodic-advertising-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-advertiser-stop-periodic-advertising-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_advertiser_stop_periodic_advertising_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0d040020</pre><br><div>Definition at line <code>1958</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_create_set_id<span id=\"sl-bt-rsp-advertiser-create-set-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-create-set-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_create_set_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01040020</pre><br><div>Definition at line <code>1959</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_configure_id<span id=\"sl-bt-rsp-advertiser-configure-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-configure-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_configure_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x12040020</pre><br><div>Definition at line <code>1960</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_set_timing_id<span id=\"sl-bt-rsp-advertiser-set-timing-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-set-timing-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_set_timing_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03040020</pre><br><div>Definition at line <code>1961</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_set_channel_map_id<span id=\"sl-bt-rsp-advertiser-set-channel-map-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-set-channel-map-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_set_channel_map_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x04040020</pre><br><div>Definition at line <code>1962</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_set_tx_power_id<span id=\"sl-bt-rsp-advertiser-set-tx-power-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-set-tx-power-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_set_tx_power_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0b040020</pre><br><div>Definition at line <code>1963</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_set_report_scan_request_id<span id=\"sl-bt-rsp-advertiser-set-report-scan-request-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-set-report-scan-request-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_set_report_scan_request_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x05040020</pre><br><div>Definition at line <code>1964</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_set_random_address_id<span id=\"sl-bt-rsp-advertiser-set-random-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-set-random-address-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_set_random_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x10040020</pre><br><div>Definition at line <code>1965</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_clear_random_address_id<span id=\"sl-bt-rsp-advertiser-clear-random-address-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-clear-random-address-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_clear_random_address_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x11040020</pre><br><div>Definition at line <code>1966</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_stop_id<span id=\"sl-bt-rsp-advertiser-stop-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-stop-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_stop_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0a040020</pre><br><div>Definition at line <code>1967</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_delete_set_id<span id=\"sl-bt-rsp-advertiser-delete-set-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-delete-set-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_delete_set_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02040020</pre><br><div>Definition at line <code>1968</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_set_phy_id<span id=\"sl-bt-rsp-advertiser-set-phy-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-set-phy-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_set_phy_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x06040020</pre><br><div>Definition at line <code>1969</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_set_configuration_id<span id=\"sl-bt-rsp-advertiser-set-configuration-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-set-configuration-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_set_configuration_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x07040020</pre><br><div>Definition at line <code>1970</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_clear_configuration_id<span id=\"sl-bt-rsp-advertiser-clear-configuration-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-clear-configuration-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_clear_configuration_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x08040020</pre><br><div>Definition at line <code>1971</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_set_data_id<span id=\"sl-bt-rsp-advertiser-set-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-set-data-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_set_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0f040020</pre><br><div>Definition at line <code>1972</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_set_long_data_id<span id=\"sl-bt-rsp-advertiser-set-long-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-set-long-data-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_set_long_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0e040020</pre><br><div>Definition at line <code>1973</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_start_id<span id=\"sl-bt-rsp-advertiser-start-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-start-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_start_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x09040020</pre><br><div>Definition at line <code>1974</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_start_periodic_advertising_id<span id=\"sl-bt-rsp-advertiser-start-periodic-advertising-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-start-periodic-advertising-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_start_periodic_advertising_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0c040020</pre><br><div>Definition at line <code>1975</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_advertiser_stop_periodic_advertising_id<span id=\"sl-bt-rsp-advertiser-stop-periodic-advertising-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-advertiser-stop-periodic-advertising-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_advertiser_stop_periodic_advertising_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0d040020</pre><br><div>Definition at line <code>1976</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser", "status": "success"}