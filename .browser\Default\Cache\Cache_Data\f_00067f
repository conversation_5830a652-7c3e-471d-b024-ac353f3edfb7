{"html": "\n   <article>\n    \n    <div class=\"header\">\n     <div class=\"headertitle\">\n      <h1 class=\"title\">\n       Simple LED Driver\n       <div class=\"ingroups\">\n        <a href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led\" target=\"_blank\">\n         LED Driver\n        </a>\n       </div>\n      </h1>\n     </div>\n    </div>\n    <div class=\"contents\">\n     <a id=\"details\" name=\"details\">\n     </a>\n     <h2 class=\"groupheader\">\n      Description\n     </h2>\n     <p>\n      Simple LED Driver can be used to execute basic LED functionalities such as on, off, toggle, or retrive the on/off status on Silicon Labs devices. Subsequent sections provide more insight into this module.\n     </p>\n     <ul>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#simple-led-intro\" target=\"_blank\">\n        Introduction\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#simple-led-config\" target=\"_blank\">\n        Simple LED Configuration\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#simple-led-usage\" target=\"_blank\">\n        Simple LED Usage\n       </a>\n      </li>\n     </ul>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"simple-led-intro\">\n      </a>\n      Introduction\n     </h1>\n     <p>\n      The Simple LED driver is a module of the LED driver that provides the functionality to control simple on/off LEDs.\n     </p>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"simple-led-config\">\n      </a>\n      Simple LED Configuration\n     </h1>\n     <p>\n      Simple LEDs use the\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n       sl_led_t\n      </a>\n      struct and their\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-simple-led-context-t\" target=\"_blank\">\n       sl_simple_led_context_t\n      </a>\n      struct. These are automatically generated into the following files, as well as instance specific headers with macro definitions in them. The samples below are for a single instance called \"inst0\".\n     </p>\n     <div class=\"fragment\">\n      <div class=\"line\">\n       <span class=\"comment\">\n        // sl_simple_led_instances.c\n       </span>\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #include \"sl_simple_led.h\"\n       </span>\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #include \"em_gpio.h\"\n       </span>\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #include \"sl_simple_led_inst0_config.h\"\n       </span>\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-simple-led-context-t\" target=\"_blank\">\n        sl_simple_led_context_t\n       </a>\n       simple_inst0_context = {\n      </div>\n      <div class=\"line\">\n       .\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-simple-led-context-t#a6e677319715a534dcd283a4ec11177e7\" target=\"_blank\">\n        port\n       </a>\n       = SL_SIMPLE_LED_INST0_PORT,\n      </div>\n      <div class=\"line\">\n       .pin = SL_SIMPLE_LED_INST0_PIN,\n      </div>\n      <div class=\"line\">\n       .polarity = SL_SIMPLE_LED_INST0_POLARITY,\n      </div>\n      <div class=\"line\">\n       };\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"keyword\">\n        const\n       </span>\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n        sl_led_t\n       </a>\n       sl_led_inst0 = {\n      </div>\n      <div class=\"line\">\n       .\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t#a46fadab226cebd48acd4e7467d64eb6c\" target=\"_blank\">\n        context\n       </a>\n       = &amp;simple_inst0_context,\n      </div>\n      <div class=\"line\">\n       .init =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#ga9a44e6053f00170c328f4f4155684026\" target=\"_blank\">\n        sl_simple_led_init\n       </a>\n       ,\n      </div>\n      <div class=\"line\">\n       .turn_on =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#gaf97bac3a8f70bcf26b0c60acb3488fb7\" target=\"_blank\">\n        sl_simple_led_turn_on\n       </a>\n       ,\n      </div>\n      <div class=\"line\">\n       .turn_off =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#gaffd8ee11c216c921060872bf3904a0d8\" target=\"_blank\">\n        sl_simple_led_turn_off\n       </a>\n       ,\n      </div>\n      <div class=\"line\">\n       .toggle =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#ga02a672ac3657d6846c3314f960de80b3\" target=\"_blank\">\n        sl_simple_led_toggle\n       </a>\n       ,\n      </div>\n      <div class=\"line\">\n       .get_state =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#gaa1bb99d1e965d05f56ed4f4734ab3a02\" target=\"_blank\">\n        sl_simple_led_get_state\n       </a>\n       ,\n      </div>\n      <div class=\"line\">\n       };\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"keywordtype\">\n        void\n       </span>\n       sl_simple_led_init_instances(\n       <span class=\"keywordtype\">\n        void\n       </span>\n       )\n      </div>\n      <div class=\"line\">\n       {\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaeec4fd0fb9f100d0f357dbb7974cb6ae\" target=\"_blank\">\n        sl_led_init\n       </a>\n       (&amp;sl_led_inst0);\n      </div>\n      <div class=\"line\">\n       }\n      </div>\n     </div>\n     <dl class=\"section note\">\n      <dt>\n       Note\n      </dt>\n      <dd>\n       The sl_simple_led_instances.c file is shown with only one instance, but if more were in use they would all appear in this .c file.\n      </dd>\n     </dl>\n     <div class=\"fragment\">\n      <div class=\"line\">\n       <span class=\"comment\">\n        // sl_simple_led_instances.h\n       </span>\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #ifndef SL_SIMPLE_LED_INSTANCES_H\n       </span>\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #define SL_SIMPLE_LED_INSTANCES_H\n       </span>\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #include \"sl_simple_led.h\"\n       </span>\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"keyword\">\n        extern\n       </span>\n       <span class=\"keyword\">\n        const\n       </span>\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n        sl_led_t\n       </a>\n       sl_led_inst0;\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"keywordtype\">\n        void\n       </span>\n       sl_simple_led_init_instances(\n       <span class=\"keywordtype\">\n        void\n       </span>\n       );\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #endif // SL_SIMPLE_LED_INIT_H\n       </span>\n      </div>\n     </div>\n     <dl class=\"section note\">\n      <dt>\n       Note\n      </dt>\n      <dd>\n       The sl_simple_led_instances.h file is shown with only one instance, but if more were in use they would all appear in this .h file.\n      </dd>\n     </dl>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"simple-led-usage\">\n      </a>\n      Simple LED Usage\n     </h1>\n     <p>\n      The simple LED driver is for LEDs with basic on off functionality, and there are no additional functions beyond those in the common driver. The LEDs can be turned on and off, toggled, and their on/off state can be retrieved. The following code shows how to control these LEDs. An LED should always be initialized before calling any other functions with it.\n     </p>\n     <div class=\"fragment\">\n      <div class=\"line\">\n       <span class=\"comment\">\n        // initialize simple LED\n       </span>\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaeec4fd0fb9f100d0f357dbb7974cb6ae\" target=\"_blank\">\n        sl_led_init\n       </a>\n       (&amp;simple_led_inst0);\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"comment\">\n        // turn on LED, turn off LED, and toggle\n       </span>\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#ga330255a181ca62dfaede4428b71ab9ba\" target=\"_blank\">\n        sl_led_turn_on\n       </a>\n       (&amp;simple_led_inst0);\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gae4396c5a5963f9fb87a072a98da0cc79\" target=\"_blank\">\n        sl_led_turn_off\n       </a>\n       (&amp;simple_led_inst0);\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#ga800d7603e870e27d02a76a1438f80ece\" target=\"_blank\">\n        sl_led_toggle\n       </a>\n       (&amp;simple_led_inst0);\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"comment\">\n        // get the state of the led\n       </span>\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaf7839e80cf60eefc89ef109b048bec32\" target=\"_blank\">\n        sl_led_state_t\n       </a>\n       state =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaeb2bbdaed9dbe3c3f777c75e4624b526\" target=\"_blank\">\n        sl_led_get_state\n       </a>\n       (&amp;simple_led_instance0);\n      </div>\n     </div>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"nested-classes\">\n          </a>\n          Data Structures\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-simple-led-context-t\" target=\"_blank\">\n          sl_simple_led_context_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         A Simple LED instance.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"func-members\">\n          </a>\n          Functions\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#ga9a44e6053f00170c328f4f4155684026\" target=\"_blank\">\n          sl_simple_led_init\n         </a>\n         (void *led_handle)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Initialize the simple LED driver.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#gaf97bac3a8f70bcf26b0c60acb3488fb7\" target=\"_blank\">\n          sl_simple_led_turn_on\n         </a>\n         (void *led_handle)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Turn on a simple LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#gaffd8ee11c216c921060872bf3904a0d8\" target=\"_blank\">\n          sl_simple_led_turn_off\n         </a>\n         (void *led_handle)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Turn off a simple LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#ga02a672ac3657d6846c3314f960de80b3\" target=\"_blank\">\n          sl_simple_led_toggle\n         </a>\n         (void *led_handle)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Toggle a simple LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaf7839e80cf60eefc89ef109b048bec32\" target=\"_blank\">\n          sl_led_state_t\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#gaa1bb99d1e965d05f56ed4f4734ab3a02\" target=\"_blank\">\n          sl_simple_led_get_state\n         </a>\n         (void *led_handle)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get the current state of the simple LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"define-members\">\n          </a>\n          Macros\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#ga38f64870c5547f6911d30b316edbc6dc\" target=\"_blank\">\n          SL_SIMPLE_LED_POLARITY_ACTIVE_LOW\n         </a>\n         0U\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         LED Active polarity Low.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#gac75659d1f675e1fc2dd89746d7ff6ffc\" target=\"_blank\">\n          SL_SIMPLE_LED_POLARITY_ACTIVE_HIGH\n         </a>\n         1U\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         LED Active polarity High.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"typedef-members\">\n          </a>\n          Typedefs\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         typedef uint8_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#ga6b7e9cedc34a1f8d8bc492f1352d90bc\" target=\"_blank\">\n          sl_led_polarity_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         LED GPIO polarities (active high/low)\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <h2 class=\"groupheader\">\n      Function Documentation\n     </h2>\n     <a id=\"ga9a44e6053f00170c328f4f4155684026\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga9a44e6053f00170c328f4f4155684026\">\n        ◆\n       </a>\n      </span>\n      sl_simple_led_init()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_simple_led_init\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            led_handle\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Initialize the simple LED driver.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to simple-led specific data:\n             <ul>\n              <li>\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-simple-led-context-t\" title=\"A Simple LED instance.\" target=\"_blank\">\n                sl_simple_led_context_t\n               </a>\n              </li>\n             </ul>\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Status Code:\n         <ul>\n          <li>\n           SL_STATUS_OK\n          </li>\n         </ul>\n        </dd>\n       </dl>\n       <p>\n        defined(_SILICON_LABS_32B_SERIES_2)\n       </p>\n      </div>\n     </div>\n     <a id=\"gaf97bac3a8f70bcf26b0c60acb3488fb7\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf97bac3a8f70bcf26b0c60acb3488fb7\">\n        ◆\n       </a>\n      </span>\n      sl_simple_led_turn_on()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_simple_led_turn_on\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            led_handle\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Turn on a simple LED.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to simple-led specific data:\n             <ul>\n              <li>\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-simple-led-context-t\" title=\"A Simple LED instance.\" target=\"_blank\">\n                sl_simple_led_context_t\n               </a>\n              </li>\n             </ul>\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaffd8ee11c216c921060872bf3904a0d8\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaffd8ee11c216c921060872bf3904a0d8\">\n        ◆\n       </a>\n      </span>\n      sl_simple_led_turn_off()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_simple_led_turn_off\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            led_handle\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Turn off a simple LED.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to simple-led specific data:\n             <ul>\n              <li>\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-simple-led-context-t\" title=\"A Simple LED instance.\" target=\"_blank\">\n                sl_simple_led_context_t\n               </a>\n              </li>\n             </ul>\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga02a672ac3657d6846c3314f960de80b3\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga02a672ac3657d6846c3314f960de80b3\">\n        ◆\n       </a>\n      </span>\n      sl_simple_led_toggle()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_simple_led_toggle\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            led_handle\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Toggle a simple LED.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to simple-led specific data:\n             <ul>\n              <li>\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-simple-led-context-t\" title=\"A Simple LED instance.\" target=\"_blank\">\n                sl_simple_led_context_t\n               </a>\n              </li>\n             </ul>\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaa1bb99d1e965d05f56ed4f4734ab3a02\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaa1bb99d1e965d05f56ed4f4734ab3a02\">\n        ◆\n       </a>\n      </span>\n      sl_simple_led_get_state()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaf7839e80cf60eefc89ef109b048bec32\" target=\"_blank\">\n            sl_led_state_t\n           </a>\n           sl_simple_led_get_state\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            led_handle\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get the current state of the simple LED.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to simple-led specific data:\n             <ul>\n              <li>\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-simple-led-context-t\" title=\"A Simple LED instance.\" target=\"_blank\">\n                sl_simple_led_context_t\n               </a>\n              </li>\n             </ul>\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         sl_led_state_t Current state of simple LED. 1 for on, 0 for off\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <h2 class=\"groupheader\">\n      Macro Definition Documentation\n     </h2>\n     <a id=\"ga38f64870c5547f6911d30b316edbc6dc\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga38f64870c5547f6911d30b316edbc6dc\">\n        ◆\n       </a>\n      </span>\n      SL_SIMPLE_LED_POLARITY_ACTIVE_LOW\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define SL_SIMPLE_LED_POLARITY_ACTIVE_LOW&nbsp;&nbsp;&nbsp;0U\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        LED Active polarity Low.\n       </p>\n      </div>\n     </div>\n     <a id=\"gac75659d1f675e1fc2dd89746d7ff6ffc\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gac75659d1f675e1fc2dd89746d7ff6ffc\">\n        ◆\n       </a>\n      </span>\n      SL_SIMPLE_LED_POLARITY_ACTIVE_HIGH\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define SL_SIMPLE_LED_POLARITY_ACTIVE_HIGH&nbsp;&nbsp;&nbsp;1U\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        LED Active polarity High.\n       </p>\n      </div>\n     </div>\n     <h2 class=\"groupheader\">\n      Typedef Documentation\n     </h2>\n     <a id=\"ga6b7e9cedc34a1f8d8bc492f1352d90bc\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga6b7e9cedc34a1f8d8bc492f1352d90bc\">\n        ◆\n       </a>\n      </span>\n      sl_led_polarity_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           typedef uint8_t\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#ga6b7e9cedc34a1f8d8bc492f1352d90bc\" target=\"_blank\">\n            sl_led_polarity_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        LED GPIO polarities (active high/low)\n       </p>\n      </div>\n     </div>\n    </div>\n   </article>\n  ", "url": "http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led", "status": "success"}