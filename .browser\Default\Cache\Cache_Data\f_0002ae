{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Periodic Advertising Sync Scanner<span id=\"periodic-advertising-sync-scanner\" class=\"self-anchor\"><a class=\"perm\" href=\"#periodic-advertising-sync-scanner\">#</a></span></h1><p style=\"color:inherit\">Periodic Advertising Sync Scanner. </p><p style=\"color:inherit\">Synchronize to periodic advertising trains by scanning for extended advertisements that provide the synchronization information. </p><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sync-scanner-set-sync-parameters\">sl_bt_sync_scanner_set_sync_parameters</a>(uint16_t skip, uint16_t timeout, uint8_t reporting_mode)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-sync-scanner-open\">sl_bt_sync_scanner_open</a>(bd_addr address, uint8_t address_type, uint8_t adv_sid, uint16_t *sync)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sync-scanner-set-sync-parameters-id\">sl_bt_cmd_sync_scanner_set_sync_parameters_id</a> 0x00500020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-sync-scanner-open-id\">sl_bt_cmd_sync_scanner_open_id</a> 0x01500020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sync-scanner-set-sync-parameters-id\">sl_bt_rsp_sync_scanner_set_sync_parameters_id</a> 0x00500020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-sync-scanner-open-id\">sl_bt_rsp_sync_scanner_open_id</a> 0x01500020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_sync_scanner_set_sync_parameters<span id=\"sl-bt-sync-scanner-set-sync-parameters\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sync-scanner-set-sync-parameters\">#</a></span></h3><blockquote>sl_status_t sl_bt_sync_scanner_set_sync_parameters (uint16_t skip, uint16_t timeout, uint8_t reporting_mode)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">skip</td><td><p style=\"color:inherit\">The maximum number of periodic advertising packets that can be skipped after a successful receive.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x0000 to 0x01F3</p></li><li><p style=\"color:inherit\">Default value: 0 </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">timeout</td><td><p style=\"color:inherit\">The maximum permitted time between successful receives. If this time is exceeded, synchronization is lost. Unit: 10 ms.</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 0x0A to 0x4000</p></li><li><p style=\"color:inherit\">Unit: 10 ms</p></li><li><p style=\"color:inherit\">Time range: 100 ms to 163.84 s</p></li><li><p style=\"color:inherit\">Default value: 1000 ms </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">reporting_mode</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync#sl-bt-sync-reporting-mode-t\" target=\"_blank\" rel=\"\">sl_bt_sync_reporting_mode_t</a>. Specifies the initial mode for reporting data received in the periodic advertising train after it has achieved synchronization. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_sync_report_none (0x0):</strong> Data received in periodic advertising trains is not reported to the application.</p></li><li><p style=\"color:inherit\"><strong>sl_bt_sync_report_all (0x1):</strong> Data received in periodic advertising trains is reported to the application.</p></li></ul><p style=\"color:inherit\">Default: <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync#sl-bt-sync-report-all\" target=\"_blank\" rel=\"\">sl_bt_sync_report_all</a> (Data received in periodic advertising trains is reported to the application) </p></td></tr></tbody></table></div><p style=\"color:inherit\">Configure synchronization parameters for synchronizing to periodic advertising trains. The specified parameters take effect immediately for all periodic advertising trains that have not already established synchronization.</p><p style=\"color:inherit\">The application should determine skip and timeout values based on the periodic advertising interval provided by the advertiser. Ensure that you use a long enough timeout to allow multiple receives. If <code>skip</code> and <code>timeout</code> are used, select appropriate values so that they allow a few receiving attempts. Periodic advertising intervals are reported in <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-scan-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_scan_report</a> or <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-extended-advertisement-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_extended_advertisement_report</a> event.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>5102</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_sync_scanner_open<span id=\"sl-bt-sync-scanner-open\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-sync-scanner-open\">#</a></span></h3><blockquote>sl_status_t sl_bt_sync_scanner_open (<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> address, uint8_t address_type, uint8_t adv_sid, uint16_t * sync)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">address</td><td><p style=\"color:inherit\">Address of the advertiser </p></td></tr><tr><td>[in]</td><td class=\"paramname\">address_type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>.</p><p style=\"color:inherit\">Advertiser address type.</p><p style=\"color:inherit\">If the application does not include the bluetooth_feature_use_accurate_api_address_types component, <code>address_type</code> uses the following values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Public address</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Random address</p></li></ul><p style=\"color:inherit\">If the application includes the bluetooth_feature_use_accurate_api_address_types component, <code>address_type</code> uses enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a> values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address (0x0):</strong> Public device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_resolvable_address (0x2):</strong> Resolvable private random address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_nonresolvable_address (0x3):</strong> Non-resolvable private random address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address_resolved_from_rpa (0x4):</strong> Public identity address resolved from a resolvable private address (RPA)</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address_resolved_from_rpa (0x5):</strong> Static identity address resolved from a resolvable private address (RPA) </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">adv_sid</td><td><p style=\"color:inherit\">Advertising set identifier </p></td></tr><tr><td>[out]</td><td class=\"paramname\">sync</td><td><p style=\"color:inherit\">A handle that will be assigned to the periodic advertising synchronization after the synchronization is established. This handle is valid only if the result code of this response is SL_STATUS_OK.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Start establishing synchronization with the specified periodic advertiser in parallel with other advertisers given in previous invocations of this command.</p><p style=\"color:inherit\">If the application has not already started scanning with the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-start\" target=\"_blank\" rel=\"\">sl_bt_scanner_start</a> command, the stack will internally enable scanning so that synchronizations can occur. The internal scanning uses the PHY that was most recently used with <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-start\" target=\"_blank\" rel=\"\">sl_bt_scanner_start</a> and the parameters that have been configured with <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-set-timing\" target=\"_blank\" rel=\"\">sl_bt_scanner_set_timing</a>. The internal scanning is automatically stopped when all requested synchronizations have occurred.</p><p style=\"color:inherit\">The scanning responses from the internal scanning are not passed to the application unless the application starts scanning with the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-start\" target=\"_blank\" rel=\"\">sl_bt_scanner_start</a> command. If the application starts scanning while synchronizations are being established, the scanning PHY and settings set by the application take effect immediately and scanning for synchronizations continues with the new settings. When the application has started scanning with the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-start\" target=\"_blank\" rel=\"\">sl_bt_scanner_start</a> command, scanning continues until the application stops scanning with the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner#sl-bt-scanner-stop\" target=\"_blank\" rel=\"\">sl_bt_scanner_stop</a> command.</p><p style=\"color:inherit\">Advertisers that have not already synced before the invocation of this command will be synced using the <code>skip</code> and <code>timeout</code> values configured in the most recent invocation of command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-scanner-scan-report\" target=\"_blank\" rel=\"\">sl_bt_evt_scanner_scan_report</a>.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-periodic-sync-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_periodic_sync_opened</a> - Triggered after synchronization is established to a periodic advertising train that does not have subevents or response slots.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-pawr-sync-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_pawr_sync_opened</a> - Triggered after synchronization is established to a Periodic Advertising with Responses (PAwR) train.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-periodic-sync-report\" target=\"_blank\" rel=\"\">sl_bt_evt_periodic_sync_report</a> - Triggered when data for periodic advertising train that does not have subevents or response slots is received and accepted by the reporting mode currently set to the train.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-pawr-sync-subevent-report\" target=\"_blank\" rel=\"\">sl_bt_evt_pawr_sync_subevent_report</a> - Triggered when subevent data for Periodic Advertising with Responses (PAwR) train is received and accepted by the reporting mode currently set to the train.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-sync-closed\" target=\"_blank\" rel=\"\">sl_bt_evt_sync_closed</a> - Triggered after periodic advertising synchronization was lost or explicitly closed, or a synchronization establishment procedure was canceled. </p></li></ul><br><div>Definition at line <code>5182</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_sync_scanner_set_sync_parameters_id<span id=\"sl-bt-cmd-sync-scanner-set-sync-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sync-scanner-set-sync-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sync_scanner_set_sync_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00500020</pre><br><div>Definition at line <code>5056</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_sync_scanner_open_id<span id=\"sl-bt-cmd-sync-scanner-open-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-sync-scanner-open-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_sync_scanner_open_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01500020</pre><br><div>Definition at line <code>5057</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sync_scanner_set_sync_parameters_id<span id=\"sl-bt-rsp-sync-scanner-set-sync-parameters-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sync-scanner-set-sync-parameters-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sync_scanner_set_sync_parameters_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00500020</pre><br><div>Definition at line <code>5058</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_sync_scanner_open_id<span id=\"sl-bt-rsp-sync-scanner-open-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-sync-scanner-open-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_sync_scanner_open_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01500020</pre><br><div>Definition at line <code>5059</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sync-scanner", "status": "success"}