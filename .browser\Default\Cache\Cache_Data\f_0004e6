{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>ESL Tag core<span id=\"esl-tag-core\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-core\">#</a></span></h1><p style=\"color:inherit\">Implements the ESL Tag role of the Bluetooth Electronic Shelf Label Profile. It contains the mandatory ESL Tag characteristics, only. Optional features has to be added to the project as separate components, if needed. This component implements all the core functionalities, while also declares the interface of the optional features (and provides the WEAK implementations for them). This is a No-Code component, any additional feature can be added as a component. </p><div class=\"decl-class-section\"><h2>Typedefs<span id=\"typedef-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#typedef-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">typedef uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-type-t\">esl_display_type_t</a></div><div class=\"classdescription\"><p style=\"color:inherit\">ESL Display Info Display Type. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">typedef struct esl_display_info_t *</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-info-p\">esl_display_info_p</a></div><div class=\"classdescription\"><p style=\"color:inherit\">ESL Display Info first class abstract data pointer type. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">typedef uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-t\">esl_error_t</a></div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">typedef uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-image-object-id-t\">esl_image_object_id_t</a>[6]</div><div class=\"classdescription\"><p style=\"color:inherit\">ESL Service Specification d09r18, Section *******: 48 bit Image Object ID. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">typedef uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-type-t\">esl_led_type_t</a></div><div class=\"classdescription\"><p style=\"color:inherit\">ESL LED type. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">typedef uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-gamut-control-t\">esl_led_gamut_control_t</a></div><div class=\"classdescription\"><p style=\"color:inherit\">ESL LED color gamut control type. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">typedef uint16_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-repeats-type-t\">esl_led_repeats_type_t</a></div><div class=\"classdescription\"><p style=\"color:inherit\">ESL LED repeats type. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">typedef uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#tlv-t\">tlv_t</a></div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">typedef uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#tlv-tag-t\">tlv_tag_t</a></div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">typedef uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#tlv-length-t\">tlv_length_t</a></div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-sensor-battery-init\">esl_sensor_battery_init</a>(void)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-sensor-battery-read\">esl_sensor_battery_read</a>(void)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint32_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-sensor-get-battery-voltage-mv\">esl_sensor_get_battery_voltage_mv</a>(void)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-sensor-core-check-battery-level\">esl_sensor_core_check_battery_level</a>(void)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void *</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-core-encrypt-message\">esl_core_encrypt_message</a>(void *msg, uint8_t *len)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void *</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-core-decrypt-message\">esl_core_decrypt_message</a>(void *msg, uint8_t *len)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-init\">esl_display_init</a>(void)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-refresh\">esl_display_refresh</a>(uint8_t display_index, uint8_t *image_index)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-update\">esl_display_update</a>(uint8_t display_index, uint8_t image_index)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-get-count\">esl_display_get_count</a>()</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">esl_error_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-core-get-last-error\">esl_core_get_last_error</a>(void)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-core-set-last-error\">esl_core_set_last_error</a>(esl_error_t error_code)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-image-init\">esl_image_init</a>(void)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-image-characteristic-update\">esl_image_characteristic_update</a>(void)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-image-get-data\">esl_image_get_data</a>(uint8_t image_index, uint16_t *offset, uint16_t buf_size, uint8_t *target_buf)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-image-get-count\">esl_image_get_count</a>(void)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-image-reset-storage\">esl_image_reset_storage</a>(void)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\"></div><div class=\"column\"><div class=\"attributename\"><a href=\"#typedef\">typedef</a>(struct { uint8_t data[5];uint8_t bit_off_period;uint8_t bit_on_period;}) esl_led_flashing_pattern_t</div><div class=\"classdescription\"><p style=\"color:inherit\">ESL LED flashing pattern ESL Service Spec. d09r18, Section *********.2.2. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\"></div><div class=\"column\"><div class=\"attributename\"><a href=\"#typedef-1\">typedef</a>(struct { uint8_t index;esl_led_gamut_control_t gamut;esl_led_flashing_pattern_t pattern;esl_led_repeats_type_t repeats;}) esl_led_control_t</div><div class=\"classdescription\"><p style=\"color:inherit\">ESL LED Control parameter, ESL Service Spec. d09r18, Section *********.1. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-init\">esl_led_init</a>(void)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-control\">esl_led_control</a>(esl_led_control_t *control_param)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-get-count\">esl_led_get_count</a>()</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">bool</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-is-srgb\">esl_led_is_srgb</a>(uint8_t led_index)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-core-respones-init\">esl_core_respones_init</a>()</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-core-build-response\">esl_core_build_response</a>(tlv_t tlv, const void *input_data)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-core-get-responses\">esl_core_get_responses</a>(uint8_t remaining_length, uint8_t *buf_p)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-sensor-init\">esl_sensor_init</a>(void)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-sensor-read\">esl_sensor_read</a>(uint8_t index)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-sensor-get-count\">esl_sensor_get_count</a>(void)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-create\">esl_display_create</a>(uint16_t width, uint16_t height, esl_display_type_t type, esl_display_info_p *info)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-add\">esl_display_add</a>(esl_display_info_p info, esl_va_method_p init_func, esl_va_method_p write_func)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-bt-on-event\">esl_display_bt_on_event</a>(sl_bt_msg_t *evt)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-set-image\">esl_display_set_image</a>(uint8_t display_index, uint8_t image_index)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-get-width\">esl_display_get_width</a>(uint8_t display_index, uint16_t *width)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-get-height\">esl_display_get_height</a>(uint8_t display_index, uint16_t *height)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-get-type\">esl_display_get_type</a>(uint8_t display_index, esl_display_type_t *type)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-add\">esl_led_add</a>(esl_led_type_t type, uint8_t red_value, uint8_t green_value, uint8_t blue_value)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-on\">esl_led_on</a>(uint8_t led_index, esl_led_gamut_control_t gamut)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-off\">esl_led_off</a>(uint8_t led_index)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-led-gamut-control-t\" target=\"_blank\" rel=\"\">esl_led_gamut_control_t</a></div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-create-color\">esl_led_create_color</a>(uint8_t red_value, uint8_t green_value, uint8_t blue_value, uint8_t brightness)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-bt-on-event\">esl_led_bt_on_event</a>(sl_bt_msg_t *evt)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tag-advertising-interval-min\">ESL_TAG_ADVERTISING_INTERVAL_MIN</a> 750</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tag-advertising-interval-max\">ESL_TAG_ADVERTISING_INTERVAL_MAX</a> 1500</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tag-max-sync-lost-count\">ESL_TAG_MAX_SYNC_LOST_COUNT</a> 3</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tag-vendor-opcodes-enabled\">ESL_TAG_VENDOR_OPCODES_ENABLED</a> 1</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tag-builtin-battery-measure-enable\">ESL_TAG_BUILTIN_BATTERY_MEASURE_ENABLE</a> 1</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tag-battery-level-full-millivolts\">ESL_TAG_BATTERY_LEVEL_FULL_MILLIVOLTS</a> 3200</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tag-battery-level-low-millivolts\">ESL_TAG_BATTERY_LEVEL_LOW_MILLIVOLTS</a> 2400</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tag-battery-measurement-interval-min\">ESL_TAG_BATTERY_MEASUREMENT_INTERVAL_MIN</a> 10</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tag-power-down-enable\">ESL_TAG_POWER_DOWN_ENABLE</a> 1</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tag-power-down-timeout-min\">ESL_TAG_POWER_DOWN_TIMEOUT_MIN</a> 60</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tag-battery-measurement-interval-ms\">ESL_TAG_BATTERY_MEASUREMENT_INTERVAL_MS</a>   (ESL_TAG_BATTERY_MEASUREMENT_INTERVAL_MIN * 60 * 1000)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tag-power-down-timeout-ms\">ESL_TAG_POWER_DOWN_TIMEOUT_MS</a>   (ESL_TAG_POWER_DOWN_TIMEOUT_MIN * 60 * 1000)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tag-battery-level-unknown\">ESL_TAG_BATTERY_LEVEL_UNKNOWN</a> 0</div><div class=\"classdescription\"><p style=\"color:inherit\">Definition for unknown battery voltage level (an implausible value) </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-type-black-white\">ESL_DISPLAY_TYPE_BLACK_WHITE</a> 0x01</div><div class=\"classdescription\"><p style=\"color:inherit\">ESL Display Type Assigned Numbers. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-type-3-gray-scale\">ESL_DISPLAY_TYPE_3_GRAY_SCALE</a> 0x02</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-type-4-gray-scale\">ESL_DISPLAY_TYPE_4_GRAY_SCALE</a> 0x03</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-type-8-gray-scale\">ESL_DISPLAY_TYPE_8_GRAY_SCALE</a> 0x04</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-type-16-gray-scale\">ESL_DISPLAY_TYPE_16_GRAY_SCALE</a> 0x05</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-type-red-black-white\">ESL_DISPLAY_TYPE_RED_BLACK_WHITE</a> 0x06</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-type-yellow-black-white\">ESL_DISPLAY_TYPE_YELLOW_BLACK_WHITE</a> 0x07</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-type-red-yellow-black-white\">ESL_DISPLAY_TYPE_RED_YELLOW_BLACK_WHITE</a> 0x08</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-type-7-color\">ESL_DISPLAY_TYPE_7_COLOR</a> 0x09</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-type-16-color\">ESL_DISPLAY_TYPE_16_COLOR</a> 0x0A</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-type-full-rgb\">ESL_DISPLAY_TYPE_FULL_RGB</a> 0x0B</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-unspecified\">ESL_ERROR_UNSPECIFIED</a> ((esl_error_t)0x01)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-invalid-opcode\">ESL_ERROR_INVALID_OPCODE</a> ((esl_error_t)0x02)</div><div class=\"classdescription\"><p style=\"color:inherit\">Invalid Opcode: The opcode was not recognized. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-invalid-state\">ESL_ERROR_INVALID_STATE</a> ((esl_error_t)0x03)</div><div class=\"classdescription\"><p style=\"color:inherit\">Invalid State: The request was not valid for the present ESL state. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-invalid-image-index\">ESL_ERROR_INVALID_IMAGE_INDEX</a> ((esl_error_t)0x04)</div><div class=\"classdescription\"><p style=\"color:inherit\">Invalid Image Index: The Image_Index value was out of range. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-image-not-available\">ESL_ERROR_IMAGE_NOT_AVAILABLE</a> ((esl_error_t)0x05)</div><div class=\"classdescription\"><p style=\"color:inherit\">Image Not Available: The requested image contained no image data. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-invalid-parameter\">ESL_ERROR_INVALID_PARAMETER</a> ((esl_error_t)0x06)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-capacity-limit\">ESL_ERROR_CAPACITY_LIMIT</a> ((esl_error_t)0x07)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-insufficient-battery\">ESL_ERROR_INSUFFICIENT_BATTERY</a> ((esl_error_t)0x08)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-insufficient-resources\">ESL_ERROR_INSUFFICIENT_RESOURCES</a> ((esl_error_t)0x09)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-retry\">ESL_ERROR_RETRY</a> ((esl_error_t)0x0A)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-queue-full\">ESL_ERROR_QUEUE_FULL</a> ((esl_error_t)0x0B)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-implausible-time\">ESL_ERROR_IMPLAUSIBLE_TIME</a> ((esl_error_t)0x0C)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-vendor-noerror\">ESL_ERROR_VENDOR_NOERROR</a> ((esl_error_t)0xFF)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-error-vendor-noreport\">ESL_ERROR_VENDOR_NOREPORT</a> ((esl_error_t)0xFE)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-core-clear-last-error\">esl_core_clear_last_error</a> ()</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-core-has-no-error\">esl_core_has_no_error</a> ()</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-image-object-base\">ESL_IMAGE_OBJECT_BASE</a> 0x100u</div><div class=\"classdescription\"><p style=\"color:inherit\">ESL Service Specification d09r18, Section *******: 48 bit Image Object ID. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-type-shift\">ESL_LED_TYPE_SHIFT</a> ((esl_led_type_t)6)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-brightness-shift\">ESL_LED_BRIGHTNESS_SHIFT</a> ((esl_led_gamut_control_t)6)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-red-gamut-shift\">ESL_LED_RED_GAMUT_SHIFT</a> ((esl_led_gamut_control_t)0)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-green-gamut-shift\">ESL_LED_GREEN_GAMUT_SHIFT</a> ((esl_led_gamut_control_t)2)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-blue-gamut-shift\">ESL_LED_BLUE_GAMUT_SHIFT</a> ((esl_led_gamut_control_t)4)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-repeats-type-mask\">ESL_LED_REPEATS_TYPE_MASK</a> ((esl_led_repeats_type_t)0x0001)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-repeats-type-count\">ESL_LED_REPEATS_TYPE_COUNT</a> ((esl_led_repeats_type_t)0x0000)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-repeats-type-time\">ESL_LED_REPEATS_TYPE_TIME</a> ((esl_led_repeats_type_t)0x0001)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-repeats-duration-shift\">ESL_LED_REPEATS_DURATION_SHIFT</a> ((esl_led_repeats_type_t)0x0001)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-type-srgb\">ESL_LED_TYPE_SRGB</a> ((esl_led_type_t)(0x00 &lt;&lt; ESL_LED_TYPE_SHIFT))</div><div class=\"classdescription\"><p style=\"color:inherit\">ESL LED type definition: sRGB. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-type-monochrome\">ESL_LED_TYPE_MONOCHROME</a> ((esl_led_type_t)(0x01 &lt;&lt; ESL_LED_TYPE_SHIFT))</div><div class=\"classdescription\"><p style=\"color:inherit\">ESL LED type definition: Monochrome. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-type-mask\">ESL_LED_TYPE_MASK</a> ((esl_led_type_t)0xC0)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-generic-2-bit-mask\">ESL_LED_GENERIC_2BIT_MASK</a> ((esl_led_type_t)0x03)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-level-0\">ESL_LED_LEVEL_0</a> (0x00)</div><div class=\"classdescription\"><p style=\"color:inherit\">ESL LED color gamut / brightness values. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-level-1\">ESL_LED_LEVEL_1</a> (0x01)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-level-2\">ESL_LED_LEVEL_2</a> (0x02)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-level-3\">ESL_LED_LEVEL_3</a> (0x03)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-level-step-percentage\">ESL_LED_LEVEL_STEP_PERCENTAGE</a> 25</div><div class=\"classdescription\"><p style=\"color:inherit\">ESL LED color gamut / brightness step percentage. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-get-brightness\">esl_led_get_brightness</a> (_gamut)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-get-red-value\">esl_led_get_red_value</a> (_gamut)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-get-green-value\">esl_led_get_green_value</a> (_gamut)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-led-get-blue-value\">esl_led_get_blue_value</a> (_gamut)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tlv-response-error\">ESL_TLV_RESPONSE_ERROR</a> ((tlv_t)0x00)</div><div class=\"classdescription\"><p style=\"color:inherit\">The command could not be processed successfully. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tlv-response-led-state\">ESL_TLV_RESPONSE_LED_STATE</a> ((tlv_t)0x01)</div><div class=\"classdescription\"><p style=\"color:inherit\">Acknowledgment of a request to control an LED. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tlv-response-basic-state\">ESL_TLV_RESPONSE_BASIC_STATE</a> ((tlv_t)0x10)</div><div class=\"classdescription\"><p style=\"color:inherit\">General acknowledgment containing ESL status data. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tlv-response-display-state\">ESL_TLV_RESPONSE_DISPLAY_STATE</a> ((tlv_t)0x11)</div><div class=\"classdescription\"><p style=\"color:inherit\">Acknowledgment of a request to display an image. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tlv-response-sensor-value\">ESL_TLV_RESPONSE_SENSOR_VALUE</a> ((tlv_t)0x0E)</div><div class=\"classdescription\"><p style=\"color:inherit\">Sensor report. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tlv-response-vendor-value\">ESL_TLV_RESPONSE_VENDOR_VALUE</a> ((tlv_t)0x0F)</div><div class=\"classdescription\"><p style=\"color:inherit\">Response data as specified by the vendor of the ESL. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tlv-tag-mask\">ESL_TLV_TAG_MASK</a> ((tlv_t)0x0F)</div><div class=\"classdescription\"><p style=\"color:inherit\">Mask for getting the Tag value from an ESL TLV. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-tlv-len-mask\">ESL_TLV_LEN_MASK</a> ((tlv_t)0xF0)</div><div class=\"classdescription\"><p style=\"color:inherit\">Mask for getting the Length value from an ESL TLV. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-core-get-tlv-tag\">esl_core_get_tlv_tag</a> (tlv_byte)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-core-get-tlv-len\">esl_core_get_tlv_len</a> (tlv_byte)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-core-set-tlv-tag\">esl_core_set_tlv_tag</a> (_tlv, _tag)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-core-set-tlv-len\">esl_core_set_tlv_len</a> (_tlv, _len)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-init-func-parameters-count\">ESL_DISPLAY_INIT_FUNC_PARAMETERS_COUNT</a> (1)</div><div class=\"classdescription\"><p style=\"color:inherit\">ESL Display extra parameters count for init_func esl_va_method function. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#esl-display-write-func-parameters-count\">ESL_DISPLAY_WRITE_FUNC_PARAMETERS_COUNT</a> (2)</div><div class=\"classdescription\"><p style=\"color:inherit\">ESL Display extra parameters count for write_func esl_va_method function. </p></div></div></div></div></div><div class=\"def-class-section\"><h2>Typedef Documentation<span id=\"typedef-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#typedef-definition\">#</a></span></h2><div><h3>esl_display_type_t<span id=\"esl-display-type-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-type-t\">#</a></span></h3><blockquote>typedef uint8_t esl_display_type_t </blockquote><p style=\"color:inherit\">ESL Display Info Display Type. </p><br><div>Definition at line <code>55</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>esl_display_info_p<span id=\"esl-display-info-p\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-info-p\">#</a></span></h3><blockquote>typedef struct esl_display_info_t* esl_display_info_p </blockquote><p style=\"color:inherit\">ESL Display Info first class abstract data pointer type. </p><br><div>Definition at line <code>58</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>esl_error_t<span id=\"esl-error-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-t\">#</a></span></h3><blockquote>typedef uint8_t esl_error_t </blockquote><br><div>Definition at line <code>41</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>esl_image_object_id_t<span id=\"esl-image-object-id-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-image-object-id-t\">#</a></span></h3><blockquote>typedef uint8_t esl_image_object_id_t[6] [6]</blockquote><p style=\"color:inherit\">ESL Service Specification d09r18, Section *******: 48 bit Image Object ID. </p><br><div>Definition at line <code>42</code> of file <code>common/esl_tag_core/inc/esl_tag_image_core.h</code></div><br></div><div><h3>esl_led_type_t<span id=\"esl-led-type-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-type-t\">#</a></span></h3><blockquote>typedef uint8_t esl_led_type_t </blockquote><p style=\"color:inherit\">ESL LED type. </p><br><div>Definition at line <code>43</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>esl_led_gamut_control_t<span id=\"esl-led-gamut-control-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-gamut-control-t\">#</a></span></h3><blockquote>typedef uint8_t esl_led_gamut_control_t </blockquote><p style=\"color:inherit\">ESL LED color gamut control type. </p><br><div>Definition at line <code>46</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>esl_led_repeats_type_t<span id=\"esl-led-repeats-type-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-repeats-type-t\">#</a></span></h3><blockquote>typedef uint16_t esl_led_repeats_type_t </blockquote><p style=\"color:inherit\">ESL LED repeats type. </p><br><div>Definition at line <code>49</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>tlv_t<span id=\"tlv-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#tlv-t\">#</a></span></h3><blockquote>typedef uint8_t tlv_t </blockquote><br><div>Definition at line <code>40</code> of file <code>common/esl_tag_core/inc/esl_tag_tlv.h</code></div><br></div><div><h3>tlv_tag_t<span id=\"tlv-tag-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#tlv-tag-t\">#</a></span></h3><blockquote>typedef uint8_t tlv_tag_t </blockquote><br><div>Definition at line <code>43</code> of file <code>common/esl_tag_core/inc/esl_tag_tlv.h</code></div><br></div><div><h3>tlv_length_t<span id=\"tlv-length-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#tlv-length-t\">#</a></span></h3><blockquote>typedef uint8_t tlv_length_t </blockquote><br><div>Definition at line <code>46</code> of file <code>common/esl_tag_core/inc/esl_tag_tlv.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>esl_sensor_battery_init<span id=\"esl-sensor-battery-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-sensor-battery-init\">#</a></span></h3><blockquote>sl_status_t esl_sensor_battery_init (void )</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><br><div>Definition at line <code>53</code> of file <code>common/esl_tag_core/inc/esl_tag_battery_internal.h</code></div><br></div><div><h3>esl_sensor_battery_read<span id=\"esl-sensor-battery-read\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-sensor-battery-read\">#</a></span></h3><blockquote>sl_status_t esl_sensor_battery_read (void )</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><br><div>Definition at line <code>59</code> of file <code>common/esl_tag_core/inc/esl_tag_battery_internal.h</code></div><br></div><div><h3>esl_sensor_get_battery_voltage_mv<span id=\"esl-sensor-get-battery-voltage-mv\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-sensor-get-battery-voltage-mv\">#</a></span></h3><blockquote>uint32_t esl_sensor_get_battery_voltage_mv (void )</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><br><div>Definition at line <code>67</code> of file <code>common/esl_tag_core/inc/esl_tag_battery_internal.h</code></div><br></div><div><h3>esl_sensor_core_check_battery_level<span id=\"esl-sensor-core-check-battery-level\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-sensor-core-check-battery-level\">#</a></span></h3><blockquote>void esl_sensor_core_check_battery_level (void )</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><br><div>Definition at line <code>74</code> of file <code>common/esl_tag_core/inc/esl_tag_battery_internal.h</code></div><br></div><div><h3>esl_core_encrypt_message<span id=\"esl-core-encrypt-message\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-core-encrypt-message\">#</a></span></h3><blockquote>void * esl_core_encrypt_message (void * msg, uint8_t * len)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">msg</td><td><p style=\"color:inherit\">Pointer to the message to be encrypted </p></td></tr><tr><td>N/A</td><td class=\"paramname\">len</td><td><p style=\"color:inherit\">len Pointer to a variable containing the length of the message to be encrypted [in bytes] </p></td></tr></tbody></table></div><p style=\"color:inherit\">Message encryption function <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">void* Pointer to the encrypted message on success, NULL otherwise. </p></li></ul><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Also changes the value of *len to the resulting length on successful operation. </p></li></ul><br><div>Definition at line <code>48</code> of file <code>common/esl_tag_core/inc/esl_tag_crypto.h</code></div><br></div><div><h3>esl_core_decrypt_message<span id=\"esl-core-decrypt-message\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-core-decrypt-message\">#</a></span></h3><blockquote>void * esl_core_decrypt_message (void * msg, uint8_t * len)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">msg</td><td><p style=\"color:inherit\">Pointer to the message to be decrypted </p></td></tr><tr><td>N/A</td><td class=\"paramname\">len</td><td><p style=\"color:inherit\">len Pointer to a variable containing the length of the message to be decrypted [in bytes] </p></td></tr></tbody></table></div><p style=\"color:inherit\">Message decryption function <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">void* Pointer to the decrypted message on success, NULL otherwise. </p></li></ul><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Also changes the value of *len to the resulting length on successful operation. </p></li></ul><br><div>Definition at line <code>59</code> of file <code>common/esl_tag_core/inc/esl_tag_crypto.h</code></div><br></div><div><h3>esl_display_init<span id=\"esl-display-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-init\">#</a></span></h3><blockquote>void esl_display_init (void )</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">ESL Tag display component init function. ESL Core component will call this during the initialization of application. This call is hidden and happens automatically. </p><br><div>Definition at line <code>65</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>esl_display_refresh<span id=\"esl-display-refresh\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-refresh\">#</a></span></h3><blockquote>sl_status_t esl_display_refresh (uint8_t display_index, uint8_t * image_index)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">display_index</td><td><p style=\"color:inherit\">Selects the display to show the image on. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">image_index</td><td><p style=\"color:inherit\">Returns last image set on the refreshed display. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Refresh the content of the ESL Tag Display. Re-displays the image which was previously set on the given screen. <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t </p></li></ul><br><div>Definition at line <code>74</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>esl_display_update<span id=\"esl-display-update\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-update\">#</a></span></h3><blockquote>sl_status_t esl_display_update (uint8_t display_index, uint8_t image_index)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">display_index</td><td><p style=\"color:inherit\">Selects the display to show the image on. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">image_index</td><td><p style=\"color:inherit\">Selects the image to be shown on the display. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Update image data on ESL Tag Display. <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t </p></li></ul><br><div>Definition at line <code>82</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>esl_display_get_count<span id=\"esl-display-get-count\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-get-count\">#</a></span></h3><blockquote>uint8_t esl_display_get_count ()</blockquote><p style=\"color:inherit\">ESL Display: display count getter <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Number of available displays </p></li></ul><br><div>Definition at line <code>88</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>esl_core_get_last_error<span id=\"esl-core-get-last-error\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-core-get-last-error\">#</a></span></h3><blockquote>esl_error_t esl_core_get_last_error (void )</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">Gets the last error code <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">esl_error_t_t </p></li></ul><br><div>Definition at line <code>112</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>esl_core_set_last_error<span id=\"esl-core-set-last-error\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-core-set-last-error\">#</a></span></h3><blockquote>void esl_core_set_last_error (esl_error_t error_code)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">error_code</td><td><p style=\"color:inherit\">error code </p></td></tr></tbody></table></div><p style=\"color:inherit\">Sets the last error code </p><br><div>Definition at line <code>118</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>esl_image_init<span id=\"esl-image-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-image-init\">#</a></span></h3><blockquote>void esl_image_init (void )</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">ESL Tag image init function. ESL Core component will call this during the initialization of application. This call is hidden and happens automatically. </p><br><div>Definition at line <code>51</code> of file <code>common/esl_tag_core/inc/esl_tag_image_core.h</code></div><br></div><div><h3>esl_image_characteristic_update<span id=\"esl-image-characteristic-update\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-image-characteristic-update\">#</a></span></h3><blockquote>void esl_image_characteristic_update (void )</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">ESL Tag image characteristic update. ESL Core component will call this automatically on the bluetooth stack boot event. The real implementation in the ESL Tag Image component will get the display info and write it to the ESL Image Information Characteristic value for the lifecycle of the tag. </p><br><div>Definition at line <code>59</code> of file <code>common/esl_tag_core/inc/esl_tag_image_core.h</code></div><br></div><div><h3>esl_image_get_data<span id=\"esl-image-get-data\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-image-get-data\">#</a></span></h3><blockquote>sl_status_t esl_image_get_data (uint8_t image_index, uint16_t * offset, uint16_t buf_size, uint8_t * target_buf)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">image_index</td><td><p style=\"color:inherit\">image_index Index of the image to get raw data chunk of </p></td></tr><tr><td>[out]</td><td class=\"paramname\">offset</td><td><p style=\"color:inherit\">*offset Size of data already read out </p></td></tr><tr><td>[in]</td><td class=\"paramname\">buf_size</td><td><p style=\"color:inherit\">buf_size Actual size of the target buffer </p></td></tr><tr><td>[out]</td><td class=\"paramname\">target_buf</td><td><p style=\"color:inherit\">*target_buf Buffer address to copy the image chunk into </p></td></tr></tbody></table></div><p style=\"color:inherit\">Getter for an ESL Tag image raw data chunk</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">To get full image data this function needs to be called repeatedly until the offset value increases - offset in caller has to be persistent during the process, and usually its value must be set to 0, initially </p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t </p></li></ul><br><div>Definition at line <code>74</code> of file <code>common/esl_tag_core/inc/esl_tag_image_core.h</code></div><br></div><div><h3>esl_image_get_count<span id=\"esl-image-get-count\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-image-get-count\">#</a></span></h3><blockquote>uint8_t esl_image_get_count (void )</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">ESL Tag maximum image count getter <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Number of available images </p></li></ul><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">: To be implemented with each custom image storage implementation! </p></li></ul><br><div>Definition at line <code>82</code> of file <code>common/esl_tag_core/inc/esl_tag_image_core.h</code></div><br></div><div><h3>esl_image_reset_storage<span id=\"esl-image-reset-storage\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-image-reset-storage\">#</a></span></h3><blockquote>void esl_image_reset_storage (void )</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">Reset image storage objects <strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">: To be implemented with each custom image storage implementation! </p></li></ul><br><div>Definition at line <code>88</code> of file <code>common/esl_tag_core/inc/esl_tag_image_core.h</code></div><br></div><div><h3>typedef<span id=\"typedef\" class=\"self-anchor\"><a class=\"perm\" href=\"#typedef\">#</a></span></h3><blockquote>typedef (struct { uint8_t data[5];uint8_t bit_off_period;uint8_t bit_on_period;} )</blockquote><p style=\"color:inherit\">ESL LED flashing pattern ESL Service Spec. d09r18, Section *********.2.2. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><br><div>Definition at line <code>52</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>typedef<span id=\"typedef-1\" class=\"self-anchor\"><a class=\"perm\" href=\"#typedef-1\">#</a></span></h3><blockquote>typedef (struct { uint8_t index;<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-led-gamut-control-t\" target=\"_blank\" rel=\"\">esl_led_gamut_control_t</a> gamut;esl_led_flashing_pattern_t pattern;<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-led-repeats-type-t\" target=\"_blank\" rel=\"\">esl_led_repeats_type_t</a> repeats;} )</blockquote><p style=\"color:inherit\">ESL LED Control parameter, ESL Service Spec. d09r18, Section *********.1. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><br><div>Definition at line <code>59</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>esl_led_init<span id=\"esl-led-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-init\">#</a></span></h3><blockquote>void esl_led_init (void )</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">ESL Tag LED init function. ESL Core component will call this during the initialization of application. This call is hidden and happens automatically. </p><br><div>Definition at line <code>147</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>esl_led_control<span id=\"esl-led-control\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-control\">#</a></span></h3><blockquote>sl_status_t esl_led_control (esl_led_control_t * control_param)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">control_param</td><td><p style=\"color:inherit\">ESL LED control parameters defined by the ESL standard - excluding the ESL ID. </p></td></tr></tbody></table></div><p style=\"color:inherit\">ESL Tag LED control function <strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">updates the active LED bit in ESL basic state register according to the actual status of all LEDs </p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t </p></li></ul><br><div>Definition at line <code>157</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>esl_led_get_count<span id=\"esl-led-get-count\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-get-count\">#</a></span></h3><blockquote>uint8_t esl_led_get_count ()</blockquote><p style=\"color:inherit\">Get ESL Tag LED count <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Number of available LEDs </p></li></ul><br><div>Definition at line <code>163</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>esl_led_is_srgb<span id=\"esl-led-is-srgb\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-is-srgb\">#</a></span></h3><blockquote>bool esl_led_is_srgb (uint8_t led_index)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\">led_index</td><td></td></tr></tbody></table></div><p style=\"color:inherit\">Get ESL Tag LED count <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Number of available LEDs </p></li></ul><br><div>Definition at line <code>169</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>esl_core_respones_init<span id=\"esl-core-respones-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-core-respones-init\">#</a></span></h3><blockquote>void esl_core_respones_init ()</blockquote><p style=\"color:inherit\">Init ESL Tag responses queue. ESL Core component will call this during the initialization of application. This call is hidden and happens automatically. </p><br><div>Definition at line <code>62</code> of file <code>common/esl_tag_core/inc/esl_tag_response.h</code></div><br></div><div><h3>esl_core_build_response<span id=\"esl-core-build-response\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-core-build-response\">#</a></span></h3><blockquote>sl_status_t esl_core_build_response (tlv_t tlv, const void * input_data)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">tlv</td><td><p style=\"color:inherit\">The TLV to create the response stream for </p></td></tr><tr><td>[in]</td><td class=\"paramname\">input_data</td><td><p style=\"color:inherit\">can be a pointer to the basic status, led status etc. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Create response byte stream for single TLV based on input data and error status (if any) then puts the response stream into a circular buffer. The resulting output length of a single TLV will be the length encoded in that particular TLV + 1 byte as per ESL specification. Multiple response streams will be then concatenated on consecutive calls up to the maximum length limit of 48 bytes for multiple response data. <strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">the actual length of the input data length MUST be in correspondence with the TLV passed as the first parameter!</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_tatus_t </p></li></ul><br><div>Definition at line <code>78</code> of file <code>common/esl_tag_core/inc/esl_tag_response.h</code></div><br></div><div><h3>esl_core_get_responses<span id=\"esl-core-get-responses\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-core-get-responses\">#</a></span></h3><blockquote>uint8_t esl_core_get_responses (uint8_t remaining_length, uint8_t * buf_p)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">remaining_length</td><td><p style=\"color:inherit\">size of data out buffer </p></td></tr><tr><td>[out]</td><td class=\"paramname\">buf_p</td><td><p style=\"color:inherit\">pointer to data buffer to copy the responses into</p></td></tr></tbody></table></div><p style=\"color:inherit\">Copy all possible responses from internal circular buffer to a temporary linear buffer. Circular buffer drops the successfully read out data, while it might keep remaining responses which doesn't fit into the target buffer. Consistency of any response will be kept, that is, it might copy less data than the allowed maximum length passed as the first parameter, if there are responses left still in the internal circular buffer which otherwise wont fit into the target buffer. <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">overall length of responses, might be 0 if there's no response </p></li></ul><br><div>Definition at line <code>94</code> of file <code>common/esl_tag_core/inc/esl_tag_response.h</code></div><br></div><div><h3>esl_sensor_init<span id=\"esl-sensor-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-sensor-init\">#</a></span></h3><blockquote>void esl_sensor_init (void )</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">ESL Tag sensor init function. ESL Core component will call this during the initialization of application. This call is hidden and happens automatically. </p><br><div>Definition at line <code>44</code> of file <code>common/esl_tag_core/inc/esl_tag_sensor_core.h</code></div><br></div><div><h3>esl_sensor_read<span id=\"esl-sensor-read\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-sensor-read\">#</a></span></h3><blockquote>sl_status_t esl_sensor_read (uint8_t index)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\">index</td><td></td></tr></tbody></table></div><p style=\"color:inherit\">Read interface (only) for ESL Sensor. <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t SL_NOT_SUPPORTED if the ESL Sensor component is missing. </p></li></ul><br><div>Definition at line <code>50</code> of file <code>common/esl_tag_core/inc/esl_tag_sensor_core.h</code></div><br></div><div><h3>esl_sensor_get_count<span id=\"esl-sensor-get-count\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-sensor-get-count\">#</a></span></h3><blockquote>uint8_t esl_sensor_get_count (void )</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">Get ESL Tag Sensor count <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Number of available sensors </p></li></ul><br><div>Definition at line <code>56</code> of file <code>common/esl_tag_core/inc/esl_tag_sensor_core.h</code></div><br></div><div><h3>esl_display_create<span id=\"esl-display-create\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-create\">#</a></span></h3><blockquote>sl_status_t esl_display_create (uint16_t width, uint16_t height, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-display-type-t\" target=\"_blank\" rel=\"\">esl_display_type_t</a> type, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-display-info-p\" target=\"_blank\" rel=\"\">esl_display_info_p</a> * info)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">width</td><td><p style=\"color:inherit\">Display horizontal pixel count. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">height</td><td><p style=\"color:inherit\">Display vertical pixel count. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">type</td><td><p style=\"color:inherit\">Display type defined in BT SIG Assigned Numbers. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">info</td><td><p style=\"color:inherit\">esl_display_info_p type pointer to the newly created display info data. This value needs to pass to <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-display-add\" target=\"_blank\" rel=\"\">esl_display_add</a> call, which has to follow the invocation of esl_display_create for any new display to be created, fully and properly. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Create an ESL Tag display. Any display which a Tag wants to implement needs to becreated properly in the first place.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t </p></li></ul><br><div>Definition at line <code>62</code> of file <code>common/esl_tag_display/inc/esl_tag_display.h</code></div><br></div><div><h3>esl_display_add<span id=\"esl-display-add\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-add\">#</a></span></h3><blockquote>sl_status_t esl_display_add (<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-display-info-p\" target=\"_blank\" rel=\"\">esl_display_info_p</a> info, esl_va_method_p init_func, esl_va_method_p write_func)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">info</td><td><p style=\"color:inherit\">esl_display_info_p type pointer, can be get by calling <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-display-create\" target=\"_blank\" rel=\"\">esl_display_create</a> previously. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">init_func</td><td><p style=\"color:inherit\">esl_va_method_p type pointer to the low-level init function of the display driver. Driver implementation, however, it's up to the user to implement it properly - the only rule is to follow the interface specification defined by the esl_va_method_p type. If the 'init' method is not necessary for the given display, then a NULL pointer can be passed. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">write_func</td><td><p style=\"color:inherit\">esl_va_method_p type pointer to the low-level write function of the display driver. Has to be implemented by the users, and it's mandatory (can't be NULL). </p></td></tr></tbody></table></div><p style=\"color:inherit\">Add an ESL Tag display to the list of available displays after creation. Any display on a Tag can be only used after adding it to the list.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t </p></li></ul><br><div>Definition at line <code>85</code> of file <code>common/esl_tag_display/inc/esl_tag_display.h</code></div><br></div><div><h3>esl_display_bt_on_event<span id=\"esl-display-bt-on-event\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-bt-on-event\">#</a></span></h3><blockquote>void esl_display_bt_on_event (sl_bt_msg_t * evt)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">evt</td><td><p style=\"color:inherit\">Event coming from the Bluetooth stack. </p></td></tr></tbody></table></div><p style=\"color:inherit\">ESL display's bluetooth stack event handler. This one runs by the user implementation (usually in app.c) in parallel.</p><br><div>Definition at line <code>95</code> of file <code>common/esl_tag_display/inc/esl_tag_display.h</code></div><br></div><div><h3>esl_display_set_image<span id=\"esl-display-set-image\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-set-image\">#</a></span></h3><blockquote>sl_status_t esl_display_set_image (uint8_t display_index, uint8_t image_index)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">display_index</td><td><p style=\"color:inherit\">Selects the display to show the image on. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">image_index</td><td><p style=\"color:inherit\">Selects the image to be shown on the display. </p></td></tr></tbody></table></div><p style=\"color:inherit\">ESL Display: chose an image to show. Invokes the users write_func passed to <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-display-add\" target=\"_blank\" rel=\"\">esl_display_add</a> as its 3rd parameter. <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t </p></li></ul><br><div>Definition at line <code>104</code> of file <code>common/esl_tag_display/inc/esl_tag_display.h</code></div><br></div><div><h3>esl_display_get_width<span id=\"esl-display-get-width\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-get-width\">#</a></span></h3><blockquote>sl_status_t esl_display_get_width (uint8_t display_index, uint16_t * width)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">display_index</td><td><p style=\"color:inherit\">Selects the display to get the width of. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">width</td><td><p style=\"color:inherit\">Horizontal pixel count of the selected display. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Display width getter. <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t </p></li></ul><br><div>Definition at line <code>113</code> of file <code>common/esl_tag_display/inc/esl_tag_display.h</code></div><br></div><div><h3>esl_display_get_height<span id=\"esl-display-get-height\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-get-height\">#</a></span></h3><blockquote>sl_status_t esl_display_get_height (uint8_t display_index, uint16_t * height)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">display_index</td><td><p style=\"color:inherit\">Selects the display to get the height of. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">height</td><td><p style=\"color:inherit\">Vertical pixel count of the selected display. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Display height getter <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t </p></li></ul><br><div>Definition at line <code>121</code> of file <code>common/esl_tag_display/inc/esl_tag_display.h</code></div><br></div><div><h3>esl_display_get_type<span id=\"esl-display-get-type\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-get-type\">#</a></span></h3><blockquote>sl_status_t esl_display_get_type (uint8_t display_index, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-display-type-t\" target=\"_blank\" rel=\"\">esl_display_type_t</a> * type)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">display_index</td><td><p style=\"color:inherit\">Selects the display to get the type of. </p></td></tr><tr><td>[out]</td><td class=\"paramname\">type</td><td><p style=\"color:inherit\">esl_display_type_t Display type defined in BT SIG. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Display type getter. <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t </p></li></ul><br><div>Definition at line <code>129</code> of file <code>common/esl_tag_display/inc/esl_tag_display.h</code></div><br></div><div><h3>esl_led_add<span id=\"esl-led-add\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-add\">#</a></span></h3><blockquote>sl_status_t esl_led_add (<a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-led-type-t\" target=\"_blank\" rel=\"\">esl_led_type_t</a> type, uint8_t red_value, uint8_t green_value, uint8_t blue_value)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">type</td><td><p style=\"color:inherit\">Possible ESL LED type defined by ESL standard </p></td></tr><tr><td>[in]</td><td class=\"paramname\">red_value</td><td><p style=\"color:inherit\">ESL LED (initial, in case of sRGB type) red color value. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">green_value</td><td><p style=\"color:inherit\">ESL LED (initial, if sRGB) green color value. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">blue_value</td><td><p style=\"color:inherit\">ESL LED (initial, if sRGB) blue color value. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Add an ESL Tag LED to the list of available LEDs after creation. Any LED on a Tag can be only used after adding it to the internal LED registry.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t </p></li></ul><br><div>Definition at line <code>53</code> of file <code>common/esl_tag_led/inc/esl_tag_led.h</code></div><br></div><div><h3>esl_led_on<span id=\"esl-led-on\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-on\">#</a></span></h3><blockquote>sl_status_t esl_led_on (uint8_t led_index, <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-led-gamut-control-t\" target=\"_blank\" rel=\"\">esl_led_gamut_control_t</a> gamut)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\">led_index</td><td></td></tr><tr><td>N/A</td><td class=\"paramname\">gamut</td><td></td></tr></tbody></table></div><p style=\"color:inherit\">ESL Tag LED on function. Simple turn on function with given brightness and color values, to be implemented by end users. It's the users responsibility to actually turn given LED on with the given brightness and color parameters (if applicable) and the function shall return SL_STATUS_OK in case of success. Return SL_STATUS_FAIL on any other case (e.g. index out of bound, detectable HW error etc. <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t </p></li></ul><br><div>Definition at line <code>69</code> of file <code>common/esl_tag_led/inc/esl_tag_led.h</code></div><br></div><div><h3>esl_led_off<span id=\"esl-led-off\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-off\">#</a></span></h3><blockquote>sl_status_t esl_led_off (uint8_t led_index)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\">led_index</td><td></td></tr></tbody></table></div><p style=\"color:inherit\">ESL Tag LED off function. Simple turn off function, similar to sl_esl_led_on method except it omits the gamut parameter entirely. To be implemented by end users. It's the users responsibility to actually turn given LED off and the function shall return SL_STATUS_OK in case of success. Return SL_STATUS_FAIL on any other case (e.g. index out of bound, detectable HW error etc. <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_status_t </p></li></ul><br><div>Definition at line <code>81</code> of file <code>common/esl_tag_led/inc/esl_tag_led.h</code></div><br></div><div><h3>esl_led_create_color<span id=\"esl-led-create-color\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-create-color\">#</a></span></h3><blockquote>esl_led_gamut_control_t esl_led_create_color (uint8_t red_value, uint8_t green_value, uint8_t blue_value, uint8_t brightness)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">red_value</td><td><p style=\"color:inherit\">ESL LED red color level </p></td></tr><tr><td>[in]</td><td class=\"paramname\">green_value</td><td><p style=\"color:inherit\">ESL LED red color level </p></td></tr><tr><td>[in]</td><td class=\"paramname\">blue_value</td><td><p style=\"color:inherit\">ESL LED red color level </p></td></tr><tr><td>[in]</td><td class=\"paramname\">brightness</td><td><p style=\"color:inherit\">ESL LED red color level </p></td></tr></tbody></table></div><p style=\"color:inherit\">Assembly ESL Tag LED gamut value from input parameters for <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-led-gamut-control-t\" target=\"_blank\" rel=\"\">esl_led_gamut_control_t</a> type <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">esl_led_gamut_control_t Resulting ESL LED gamut value </p></li></ul><br><div>Definition at line <code>92</code> of file <code>common/esl_tag_led/inc/esl_tag_led.h</code></div><br></div><div><h3>esl_led_bt_on_event<span id=\"esl-led-bt-on-event\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-bt-on-event\">#</a></span></h3><blockquote>void esl_led_bt_on_event (sl_bt_msg_t * evt)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">evt</td><td><p style=\"color:inherit\">Event coming from the Bluetooth stack. </p></td></tr></tbody></table></div><p style=\"color:inherit\">ESL LED's bluetooth stack event handler. This one runs by the user implementation (usually in app.c) in parallel.</p><br><div>Definition at line <code>103</code> of file <code>common/esl_tag_led/inc/esl_tag_led.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>ESL_TAG_ADVERTISING_INTERVAL_MIN<span id=\"esl-tag-advertising-interval-min\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-advertising-interval-min\">#</a></span></h3><blockquote>#define ESL_TAG_ADVERTISING_INTERVAL_MIN</blockquote><b>Value:</b><pre class=\"macroshort\">750</pre><br><div>Definition at line <code>46</code> of file <code>common/esl_tag_core/config/esl_tag_core_config.h</code></div><br></div><div><h3>ESL_TAG_ADVERTISING_INTERVAL_MAX<span id=\"esl-tag-advertising-interval-max\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-advertising-interval-max\">#</a></span></h3><blockquote>#define ESL_TAG_ADVERTISING_INTERVAL_MAX</blockquote><b>Value:</b><pre class=\"macroshort\">1500</pre><br><div>Definition at line <code>51</code> of file <code>common/esl_tag_core/config/esl_tag_core_config.h</code></div><br></div><div><h3>ESL_TAG_MAX_SYNC_LOST_COUNT<span id=\"esl-tag-max-sync-lost-count\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-max-sync-lost-count\">#</a></span></h3><blockquote>#define ESL_TAG_MAX_SYNC_LOST_COUNT</blockquote><b>Value:</b><pre class=\"macroshort\">3</pre><br><div>Definition at line <code>58</code> of file <code>common/esl_tag_core/config/esl_tag_core_config.h</code></div><br></div><div><h3>ESL_TAG_VENDOR_OPCODES_ENABLED<span id=\"esl-tag-vendor-opcodes-enabled\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-vendor-opcodes-enabled\">#</a></span></h3><blockquote>#define ESL_TAG_VENDOR_OPCODES_ENABLED</blockquote><b>Value:</b><pre class=\"macroshort\">1</pre><br><div>Definition at line <code>63</code> of file <code>common/esl_tag_core/config/esl_tag_core_config.h</code></div><br></div><div><h3>ESL_TAG_BUILTIN_BATTERY_MEASURE_ENABLE<span id=\"esl-tag-builtin-battery-measure-enable\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-builtin-battery-measure-enable\">#</a></span></h3><blockquote>#define ESL_TAG_BUILTIN_BATTERY_MEASURE_ENABLE</blockquote><b>Value:</b><pre class=\"macroshort\">1</pre><br><div>Definition at line <code>68</code> of file <code>common/esl_tag_core/config/esl_tag_core_config.h</code></div><br></div><div><h3>ESL_TAG_BATTERY_LEVEL_FULL_MILLIVOLTS<span id=\"esl-tag-battery-level-full-millivolts\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-battery-level-full-millivolts\">#</a></span></h3><blockquote>#define ESL_TAG_BATTERY_LEVEL_FULL_MILLIVOLTS</blockquote><b>Value:</b><pre class=\"macroshort\">3200</pre><br><div>Definition at line <code>73</code> of file <code>common/esl_tag_core/config/esl_tag_core_config.h</code></div><br></div><div><h3>ESL_TAG_BATTERY_LEVEL_LOW_MILLIVOLTS<span id=\"esl-tag-battery-level-low-millivolts\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-battery-level-low-millivolts\">#</a></span></h3><blockquote>#define ESL_TAG_BATTERY_LEVEL_LOW_MILLIVOLTS</blockquote><b>Value:</b><pre class=\"macroshort\">2400</pre><br><div>Definition at line <code>78</code> of file <code>common/esl_tag_core/config/esl_tag_core_config.h</code></div><br></div><div><h3>ESL_TAG_BATTERY_MEASUREMENT_INTERVAL_MIN<span id=\"esl-tag-battery-measurement-interval-min\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-battery-measurement-interval-min\">#</a></span></h3><blockquote>#define ESL_TAG_BATTERY_MEASUREMENT_INTERVAL_MIN</blockquote><b>Value:</b><pre class=\"macroshort\">10</pre><br><div>Definition at line <code>83</code> of file <code>common/esl_tag_core/config/esl_tag_core_config.h</code></div><br></div><div><h3>ESL_TAG_POWER_DOWN_ENABLE<span id=\"esl-tag-power-down-enable\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-power-down-enable\">#</a></span></h3><blockquote>#define ESL_TAG_POWER_DOWN_ENABLE</blockquote><b>Value:</b><pre class=\"macroshort\">1</pre><br><div>Definition at line <code>91</code> of file <code>common/esl_tag_core/config/esl_tag_core_config.h</code></div><br></div><div><h3>ESL_TAG_POWER_DOWN_TIMEOUT_MIN<span id=\"esl-tag-power-down-timeout-min\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-power-down-timeout-min\">#</a></span></h3><blockquote>#define ESL_TAG_POWER_DOWN_TIMEOUT_MIN</blockquote><b>Value:</b><pre class=\"macroshort\">60</pre><br><div>Definition at line <code>96</code> of file <code>common/esl_tag_core/config/esl_tag_core_config.h</code></div><br></div><div><h3>ESL_TAG_BATTERY_MEASUREMENT_INTERVAL_MS<span id=\"esl-tag-battery-measurement-interval-ms\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-battery-measurement-interval-ms\">#</a></span></h3><blockquote>#define ESL_TAG_BATTERY_MEASUREMENT_INTERVAL_MS</blockquote><b>Value:</b><pre class=\"macroshort\">  (ESL_TAG_BATTERY_MEASUREMENT_INTERVAL_MIN * 60 * 1000)</pre><br><div>Definition at line <code>105</code> of file <code>common/esl_tag_core/config/esl_tag_core_config.h</code></div><br></div><div><h3>ESL_TAG_POWER_DOWN_TIMEOUT_MS<span id=\"esl-tag-power-down-timeout-ms\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-power-down-timeout-ms\">#</a></span></h3><blockquote>#define ESL_TAG_POWER_DOWN_TIMEOUT_MS</blockquote><b>Value:</b><pre class=\"macroshort\">  (ESL_TAG_POWER_DOWN_TIMEOUT_MIN * 60 * 1000)</pre><br><div>Definition at line <code>109</code> of file <code>common/esl_tag_core/config/esl_tag_core_config.h</code></div><br></div><div><h3>ESL_TAG_BATTERY_LEVEL_UNKNOWN<span id=\"esl-tag-battery-level-unknown\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tag-battery-level-unknown\">#</a></span></h3><blockquote>#define ESL_TAG_BATTERY_LEVEL_UNKNOWN</blockquote><b>Value:</b><pre class=\"macroshort\">0</pre><p style=\"color:inherit\">Definition for unknown battery voltage level (an implausible value) </p><br><div>Definition at line <code>47</code> of file <code>common/esl_tag_core/inc/esl_tag_battery_internal.h</code></div><br></div><div><h3>ESL_DISPLAY_TYPE_BLACK_WHITE<span id=\"esl-display-type-black-white\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-type-black-white\">#</a></span></h3><blockquote>#define ESL_DISPLAY_TYPE_BLACK_WHITE</blockquote><b>Value:</b><pre class=\"macroshort\">0x01</pre><p style=\"color:inherit\">ESL Display Type Assigned Numbers. </p><br><div>Definition at line <code>42</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>ESL_DISPLAY_TYPE_3_GRAY_SCALE<span id=\"esl-display-type-3-gray-scale\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-type-3-gray-scale\">#</a></span></h3><blockquote>#define ESL_DISPLAY_TYPE_3_GRAY_SCALE</blockquote><b>Value:</b><pre class=\"macroshort\">0x02</pre><br><div>Definition at line <code>43</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>ESL_DISPLAY_TYPE_4_GRAY_SCALE<span id=\"esl-display-type-4-gray-scale\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-type-4-gray-scale\">#</a></span></h3><blockquote>#define ESL_DISPLAY_TYPE_4_GRAY_SCALE</blockquote><b>Value:</b><pre class=\"macroshort\">0x03</pre><br><div>Definition at line <code>44</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>ESL_DISPLAY_TYPE_8_GRAY_SCALE<span id=\"esl-display-type-8-gray-scale\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-type-8-gray-scale\">#</a></span></h3><blockquote>#define ESL_DISPLAY_TYPE_8_GRAY_SCALE</blockquote><b>Value:</b><pre class=\"macroshort\">0x04</pre><br><div>Definition at line <code>45</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>ESL_DISPLAY_TYPE_16_GRAY_SCALE<span id=\"esl-display-type-16-gray-scale\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-type-16-gray-scale\">#</a></span></h3><blockquote>#define ESL_DISPLAY_TYPE_16_GRAY_SCALE</blockquote><b>Value:</b><pre class=\"macroshort\">0x05</pre><br><div>Definition at line <code>46</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>ESL_DISPLAY_TYPE_RED_BLACK_WHITE<span id=\"esl-display-type-red-black-white\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-type-red-black-white\">#</a></span></h3><blockquote>#define ESL_DISPLAY_TYPE_RED_BLACK_WHITE</blockquote><b>Value:</b><pre class=\"macroshort\">0x06</pre><br><div>Definition at line <code>47</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>ESL_DISPLAY_TYPE_YELLOW_BLACK_WHITE<span id=\"esl-display-type-yellow-black-white\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-type-yellow-black-white\">#</a></span></h3><blockquote>#define ESL_DISPLAY_TYPE_YELLOW_BLACK_WHITE</blockquote><b>Value:</b><pre class=\"macroshort\">0x07</pre><br><div>Definition at line <code>48</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>ESL_DISPLAY_TYPE_RED_YELLOW_BLACK_WHITE<span id=\"esl-display-type-red-yellow-black-white\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-type-red-yellow-black-white\">#</a></span></h3><blockquote>#define ESL_DISPLAY_TYPE_RED_YELLOW_BLACK_WHITE</blockquote><b>Value:</b><pre class=\"macroshort\">0x08</pre><br><div>Definition at line <code>49</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>ESL_DISPLAY_TYPE_7_COLOR<span id=\"esl-display-type-7-color\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-type-7-color\">#</a></span></h3><blockquote>#define ESL_DISPLAY_TYPE_7_COLOR</blockquote><b>Value:</b><pre class=\"macroshort\">0x09</pre><br><div>Definition at line <code>50</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>ESL_DISPLAY_TYPE_16_COLOR<span id=\"esl-display-type-16-color\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-type-16-color\">#</a></span></h3><blockquote>#define ESL_DISPLAY_TYPE_16_COLOR</blockquote><b>Value:</b><pre class=\"macroshort\">0x0A</pre><br><div>Definition at line <code>51</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>ESL_DISPLAY_TYPE_FULL_RGB<span id=\"esl-display-type-full-rgb\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-type-full-rgb\">#</a></span></h3><blockquote>#define ESL_DISPLAY_TYPE_FULL_RGB</blockquote><b>Value:</b><pre class=\"macroshort\">0x0B</pre><br><div>Definition at line <code>52</code> of file <code>common/esl_tag_core/inc/esl_tag_display_core.h</code></div><br></div><div><h3>ESL_ERROR_UNSPECIFIED<span id=\"esl-error-unspecified\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-unspecified\">#</a></span></h3><blockquote>#define ESL_ERROR_UNSPECIFIED</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0x01)</pre><p style=\"color:inherit\">Unspecified Error: any error condition that is not covered by a specific error code below </p><br><div>Definition at line <code>45</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_ERROR_INVALID_OPCODE<span id=\"esl-error-invalid-opcode\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-invalid-opcode\">#</a></span></h3><blockquote>#define ESL_ERROR_INVALID_OPCODE</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0x02)</pre><p style=\"color:inherit\">Invalid Opcode: The opcode was not recognized. </p><br><div>Definition at line <code>48</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_ERROR_INVALID_STATE<span id=\"esl-error-invalid-state\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-invalid-state\">#</a></span></h3><blockquote>#define ESL_ERROR_INVALID_STATE</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0x03)</pre><p style=\"color:inherit\">Invalid State: The request was not valid for the present ESL state. </p><br><div>Definition at line <code>51</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_ERROR_INVALID_IMAGE_INDEX<span id=\"esl-error-invalid-image-index\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-invalid-image-index\">#</a></span></h3><blockquote>#define ESL_ERROR_INVALID_IMAGE_INDEX</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0x04)</pre><p style=\"color:inherit\">Invalid Image Index: The Image_Index value was out of range. </p><br><div>Definition at line <code>54</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_ERROR_IMAGE_NOT_AVAILABLE<span id=\"esl-error-image-not-available\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-image-not-available\">#</a></span></h3><blockquote>#define ESL_ERROR_IMAGE_NOT_AVAILABLE</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0x05)</pre><p style=\"color:inherit\">Image Not Available: The requested image contained no image data. </p><br><div>Definition at line <code>57</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_ERROR_INVALID_PARAMETER<span id=\"esl-error-invalid-parameter\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-invalid-parameter\">#</a></span></h3><blockquote>#define ESL_ERROR_INVALID_PARAMETER</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0x06)</pre><p style=\"color:inherit\">Invalid Parameter(s): The parameter value(s) or length did not match the opcode </p><br><div>Definition at line <code>61</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_ERROR_CAPACITY_LIMIT<span id=\"esl-error-capacity-limit\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-capacity-limit\">#</a></span></h3><blockquote>#define ESL_ERROR_CAPACITY_LIMIT</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0x07)</pre><p style=\"color:inherit\">Capacity Limit: The required response could not be sent as it would exceed the payload size limit </p><br><div>Definition at line <code>65</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_ERROR_INSUFFICIENT_BATTERY<span id=\"esl-error-insufficient-battery\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-insufficient-battery\">#</a></span></h3><blockquote>#define ESL_ERROR_INSUFFICIENT_BATTERY</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0x08)</pre><p style=\"color:inherit\">Insufficient Battery: The request could not be processed because of a lack of battery charge </p><br><div>Definition at line <code>69</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_ERROR_INSUFFICIENT_RESOURCES<span id=\"esl-error-insufficient-resources\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-insufficient-resources\">#</a></span></h3><blockquote>#define ESL_ERROR_INSUFFICIENT_RESOURCES</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0x09)</pre><p style=\"color:inherit\">Insufficient Resources: The request could not be processed because of a lack of resources. This may be a temporary condition. </p><br><div>Definition at line <code>73</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_ERROR_RETRY<span id=\"esl-error-retry\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-retry\">#</a></span></h3><blockquote>#define ESL_ERROR_RETRY</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0x0A)</pre><p style=\"color:inherit\">Retry: The ESL is temporarily not able to give a full response (e.g., because the required sensor hardware was asleep) and the AP is asked to try the same command again. </p><br><div>Definition at line <code>78</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_ERROR_QUEUE_FULL<span id=\"esl-error-queue-full\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-queue-full\">#</a></span></h3><blockquote>#define ESL_ERROR_QUEUE_FULL</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0x0B)</pre><p style=\"color:inherit\">Queue Full: The ESL is temporarily unable to add a further timed command to the queue of pending commands Â­the queue has reached its limit. </p><br><div>Definition at line <code>82</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_ERROR_IMPLAUSIBLE_TIME<span id=\"esl-error-implausible-time\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-implausible-time\">#</a></span></h3><blockquote>#define ESL_ERROR_IMPLAUSIBLE_TIME</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0x0C)</pre><p style=\"color:inherit\">Implausible time: The Absolute Time parameter value in the command would result in a delay longer than 48 days (that is, it's a possible overflow) </p><br><div>Definition at line <code>86</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_ERROR_VENDOR_NOERROR<span id=\"esl-error-vendor-noerror\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-vendor-noerror\">#</a></span></h3><blockquote>#define ESL_ERROR_VENDOR_NOERROR</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0xFF)</pre><p style=\"color:inherit\">No Error: 0xFF is reserved for 'no error' indication. For internal use only, and it is Silicon Labs specific. </p><br><div>Definition at line <code>90</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_ERROR_VENDOR_NOREPORT<span id=\"esl-error-vendor-noreport\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-error-vendor-noreport\">#</a></span></h3><blockquote>#define ESL_ERROR_VENDOR_NOREPORT</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_error_t)0xFE)</pre><p style=\"color:inherit\">No report: 0xFE is reserved for 'no error report needed for timed commands' indication. For internal use only, and it is Silicon Labs specific. </p><br><div>Definition at line <code>94</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>esl_core_clear_last_error<span id=\"esl-core-clear-last-error\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-core-clear-last-error\">#</a></span></h3><blockquote>#define esl_core_clear_last_error</blockquote><b>Value:</b><pre class=\"macroshort\">()</pre><p style=\"color:inherit\">Wrapper around <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-core-set-last-error\" target=\"_blank\" rel=\"\">esl_core_set_last_error()</a> to clear the last error </p><br><div>Definition at line <code>99</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>esl_core_has_no_error<span id=\"esl-core-has-no-error\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-core-has-no-error\">#</a></span></h3><blockquote>#define esl_core_has_no_error</blockquote><b>Value:</b><pre class=\"macroshort\">()</pre><p style=\"color:inherit\">Wrapper around <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core#esl-core-get-last-error\" target=\"_blank\" rel=\"\">esl_core_get_last_error()</a> to check if there's no error </p><br><div>Definition at line <code>105</code> of file <code>common/esl_tag_core/inc/esl_tag_errors.h</code></div><br></div><div><h3>ESL_IMAGE_OBJECT_BASE<span id=\"esl-image-object-base\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-image-object-base\">#</a></span></h3><blockquote>#define ESL_IMAGE_OBJECT_BASE</blockquote><b>Value:</b><pre class=\"macroshort\">0x100u</pre><p style=\"color:inherit\">ESL Service Specification d09r18, Section *******: 48 bit Image Object ID. </p><br><div>Definition at line <code>45</code> of file <code>common/esl_tag_core/inc/esl_tag_image_core.h</code></div><br></div><div><h3>ESL_LED_TYPE_SHIFT<span id=\"esl-led-type-shift\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-type-shift\">#</a></span></h3><blockquote>#define ESL_LED_TYPE_SHIFT</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_led_type_t)6)</pre><br><div>Definition at line <code>67</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_BRIGHTNESS_SHIFT<span id=\"esl-led-brightness-shift\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-brightness-shift\">#</a></span></h3><blockquote>#define ESL_LED_BRIGHTNESS_SHIFT</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_led_gamut_control_t)6)</pre><br><div>Definition at line <code>71</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_RED_GAMUT_SHIFT<span id=\"esl-led-red-gamut-shift\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-red-gamut-shift\">#</a></span></h3><blockquote>#define ESL_LED_RED_GAMUT_SHIFT</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_led_gamut_control_t)0)</pre><br><div>Definition at line <code>72</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_GREEN_GAMUT_SHIFT<span id=\"esl-led-green-gamut-shift\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-green-gamut-shift\">#</a></span></h3><blockquote>#define ESL_LED_GREEN_GAMUT_SHIFT</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_led_gamut_control_t)2)</pre><br><div>Definition at line <code>73</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_BLUE_GAMUT_SHIFT<span id=\"esl-led-blue-gamut-shift\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-blue-gamut-shift\">#</a></span></h3><blockquote>#define ESL_LED_BLUE_GAMUT_SHIFT</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_led_gamut_control_t)4)</pre><br><div>Definition at line <code>74</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_REPEATS_TYPE_MASK<span id=\"esl-led-repeats-type-mask\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-repeats-type-mask\">#</a></span></h3><blockquote>#define ESL_LED_REPEATS_TYPE_MASK</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_led_repeats_type_t)0x0001)</pre><br><div>Definition at line <code>77</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_REPEATS_TYPE_COUNT<span id=\"esl-led-repeats-type-count\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-repeats-type-count\">#</a></span></h3><blockquote>#define ESL_LED_REPEATS_TYPE_COUNT</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_led_repeats_type_t)0x0000)</pre><br><div>Definition at line <code>80</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_REPEATS_TYPE_TIME<span id=\"esl-led-repeats-type-time\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-repeats-type-time\">#</a></span></h3><blockquote>#define ESL_LED_REPEATS_TYPE_TIME</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_led_repeats_type_t)0x0001)</pre><br><div>Definition at line <code>83</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_REPEATS_DURATION_SHIFT<span id=\"esl-led-repeats-duration-shift\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-repeats-duration-shift\">#</a></span></h3><blockquote>#define ESL_LED_REPEATS_DURATION_SHIFT</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_led_repeats_type_t)0x0001)</pre><br><div>Definition at line <code>86</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_TYPE_SRGB<span id=\"esl-led-type-srgb\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-type-srgb\">#</a></span></h3><blockquote>#define ESL_LED_TYPE_SRGB</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_led_type_t)(0x00 &lt;&lt; ESL_LED_TYPE_SHIFT))</pre><p style=\"color:inherit\">ESL LED type definition: sRGB. </p><br><div>Definition at line <code>89</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_TYPE_MONOCHROME<span id=\"esl-led-type-monochrome\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-type-monochrome\">#</a></span></h3><blockquote>#define ESL_LED_TYPE_MONOCHROME</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_led_type_t)(0x01 &lt;&lt; ESL_LED_TYPE_SHIFT))</pre><p style=\"color:inherit\">ESL LED type definition: Monochrome. </p><br><div>Definition at line <code>92</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_TYPE_MASK<span id=\"esl-led-type-mask\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-type-mask\">#</a></span></h3><blockquote>#define ESL_LED_TYPE_MASK</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_led_type_t)0xC0)</pre><br><div>Definition at line <code>95</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_GENERIC_2BIT_MASK<span id=\"esl-led-generic-2-bit-mask\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-generic-2-bit-mask\">#</a></span></h3><blockquote>#define ESL_LED_GENERIC_2BIT_MASK</blockquote><b>Value:</b><pre class=\"macroshort\">((esl_led_type_t)0x03)</pre><br><div>Definition at line <code>96</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_LEVEL_0<span id=\"esl-led-level-0\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-level-0\">#</a></span></h3><blockquote>#define ESL_LED_LEVEL_0</blockquote><b>Value:</b><pre class=\"macroshort\">(0x00)</pre><p style=\"color:inherit\">ESL LED color gamut / brightness values. </p><br><div>Definition at line <code>99</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_LEVEL_1<span id=\"esl-led-level-1\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-level-1\">#</a></span></h3><blockquote>#define ESL_LED_LEVEL_1</blockquote><b>Value:</b><pre class=\"macroshort\">(0x01)</pre><br><div>Definition at line <code>100</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_LEVEL_2<span id=\"esl-led-level-2\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-level-2\">#</a></span></h3><blockquote>#define ESL_LED_LEVEL_2</blockquote><b>Value:</b><pre class=\"macroshort\">(0x02)</pre><br><div>Definition at line <code>101</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_LEVEL_3<span id=\"esl-led-level-3\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-level-3\">#</a></span></h3><blockquote>#define ESL_LED_LEVEL_3</blockquote><b>Value:</b><pre class=\"macroshort\">(0x03)</pre><br><div>Definition at line <code>102</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_LED_LEVEL_STEP_PERCENTAGE<span id=\"esl-led-level-step-percentage\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-level-step-percentage\">#</a></span></h3><blockquote>#define ESL_LED_LEVEL_STEP_PERCENTAGE</blockquote><b>Value:</b><pre class=\"macroshort\">25</pre><p style=\"color:inherit\">ESL LED color gamut / brightness step percentage. </p><br><div>Definition at line <code>105</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>esl_led_get_brightness<span id=\"esl-led-get-brightness\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-get-brightness\">#</a></span></h3><blockquote>#define esl_led_get_brightness</blockquote><b>Value:</b><div class=\"fragment\"><div class=\"macro\">  (esl_led_gamut_control_t) \\</div><div class=\"macro\">  ((_gamut &amp; (ESL_LED_GENERIC_2BIT_MASK &lt;&lt; ESL_LED_BRIGHTNESS_SHIFT)) \\</div><div class=\"macro\">   &gt;&gt; ESL_LED_BRIGHTNESS_SHIFT)</div></div><p style=\"color:inherit\">Function like macro getter for 2-bit brightness value from a gamut value <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">esl_led_gamut_control_t brightness </p></li></ul><br><div>Definition at line <code>112</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>esl_led_get_red_value<span id=\"esl-led-get-red-value\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-get-red-value\">#</a></span></h3><blockquote>#define esl_led_get_red_value</blockquote><b>Value:</b><div class=\"fragment\"><div class=\"macro\">  (esl_led_gamut_control_t) \\</div><div class=\"macro\">  ((_gamut &amp; (ESL_LED_GENERIC_2BIT_MASK &lt;&lt; ESL_LED_RED_GAMUT_SHIFT))  \\</div><div class=\"macro\">   &gt;&gt; ESL_LED_RED_GAMUT_SHIFT)</div></div><p style=\"color:inherit\">Function like macro getter for 2-bit red value from a gamut value <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">esl_led_gamut_control_t red value </p></li></ul><br><div>Definition at line <code>121</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>esl_led_get_green_value<span id=\"esl-led-get-green-value\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-get-green-value\">#</a></span></h3><blockquote>#define esl_led_get_green_value</blockquote><b>Value:</b><div class=\"fragment\"><div class=\"macro\">  (esl_led_gamut_control_t)  \\</div><div class=\"macro\">  ((_gamut &amp; (ESL_LED_GENERIC_2BIT_MASK &lt;&lt; ESL_LED_GREEN_GAMUT_SHIFT)) \\</div><div class=\"macro\">   &gt;&gt; ESL_LED_GREEN_GAMUT_SHIFT)</div></div><p style=\"color:inherit\">Function like macro getter for 2-bit green value from a gamut value <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">esl_led_gamut_control_t green value </p></li></ul><br><div>Definition at line <code>130</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>esl_led_get_blue_value<span id=\"esl-led-get-blue-value\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-led-get-blue-value\">#</a></span></h3><blockquote>#define esl_led_get_blue_value</blockquote><b>Value:</b><div class=\"fragment\"><div class=\"macro\">  (esl_led_gamut_control_t) \\</div><div class=\"macro\">  ((_gamut &amp; (ESL_LED_GENERIC_2BIT_MASK &lt;&lt; ESL_LED_BLUE_GAMUT_SHIFT)) \\</div><div class=\"macro\">   &gt;&gt; ESL_LED_BLUE_GAMUT_SHIFT)</div></div><p style=\"color:inherit\">Function like macro getter for 2-bit blue value from a gamut value <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">esl_led_gamut_control_t blue value </p></li></ul><br><div>Definition at line <code>139</code> of file <code>common/esl_tag_core/inc/esl_tag_led_core.h</code></div><br></div><div><h3>ESL_TLV_RESPONSE_ERROR<span id=\"esl-tlv-response-error\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tlv-response-error\">#</a></span></h3><blockquote>#define ESL_TLV_RESPONSE_ERROR</blockquote><b>Value:</b><pre class=\"macroshort\">((tlv_t)0x00)</pre><p style=\"color:inherit\">The command could not be processed successfully. </p><br><div>Definition at line <code>41</code> of file <code>common/esl_tag_core/inc/esl_tag_response.h</code></div><br></div><div><h3>ESL_TLV_RESPONSE_LED_STATE<span id=\"esl-tlv-response-led-state\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tlv-response-led-state\">#</a></span></h3><blockquote>#define ESL_TLV_RESPONSE_LED_STATE</blockquote><b>Value:</b><pre class=\"macroshort\">((tlv_t)0x01)</pre><p style=\"color:inherit\">Acknowledgment of a request to control an LED. </p><br><div>Definition at line <code>44</code> of file <code>common/esl_tag_core/inc/esl_tag_response.h</code></div><br></div><div><h3>ESL_TLV_RESPONSE_BASIC_STATE<span id=\"esl-tlv-response-basic-state\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tlv-response-basic-state\">#</a></span></h3><blockquote>#define ESL_TLV_RESPONSE_BASIC_STATE</blockquote><b>Value:</b><pre class=\"macroshort\">((tlv_t)0x10)</pre><p style=\"color:inherit\">General acknowledgment containing ESL status data. </p><br><div>Definition at line <code>47</code> of file <code>common/esl_tag_core/inc/esl_tag_response.h</code></div><br></div><div><h3>ESL_TLV_RESPONSE_DISPLAY_STATE<span id=\"esl-tlv-response-display-state\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tlv-response-display-state\">#</a></span></h3><blockquote>#define ESL_TLV_RESPONSE_DISPLAY_STATE</blockquote><b>Value:</b><pre class=\"macroshort\">((tlv_t)0x11)</pre><p style=\"color:inherit\">Acknowledgment of a request to display an image. </p><br><div>Definition at line <code>50</code> of file <code>common/esl_tag_core/inc/esl_tag_response.h</code></div><br></div><div><h3>ESL_TLV_RESPONSE_SENSOR_VALUE<span id=\"esl-tlv-response-sensor-value\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tlv-response-sensor-value\">#</a></span></h3><blockquote>#define ESL_TLV_RESPONSE_SENSOR_VALUE</blockquote><b>Value:</b><pre class=\"macroshort\">((tlv_t)0x0E)</pre><p style=\"color:inherit\">Sensor report. </p><br><div>Definition at line <code>53</code> of file <code>common/esl_tag_core/inc/esl_tag_response.h</code></div><br></div><div><h3>ESL_TLV_RESPONSE_VENDOR_VALUE<span id=\"esl-tlv-response-vendor-value\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tlv-response-vendor-value\">#</a></span></h3><blockquote>#define ESL_TLV_RESPONSE_VENDOR_VALUE</blockquote><b>Value:</b><pre class=\"macroshort\">((tlv_t)0x0F)</pre><p style=\"color:inherit\">Response data as specified by the vendor of the ESL. </p><br><div>Definition at line <code>56</code> of file <code>common/esl_tag_core/inc/esl_tag_response.h</code></div><br></div><div><h3>ESL_TLV_TAG_MASK<span id=\"esl-tlv-tag-mask\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tlv-tag-mask\">#</a></span></h3><blockquote>#define ESL_TLV_TAG_MASK</blockquote><b>Value:</b><pre class=\"macroshort\">((tlv_t)0x0F)</pre><p style=\"color:inherit\">Mask for getting the Tag value from an ESL TLV. </p><br><div>Definition at line <code>49</code> of file <code>common/esl_tag_core/inc/esl_tag_tlv.h</code></div><br></div><div><h3>ESL_TLV_LEN_MASK<span id=\"esl-tlv-len-mask\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-tlv-len-mask\">#</a></span></h3><blockquote>#define ESL_TLV_LEN_MASK</blockquote><b>Value:</b><pre class=\"macroshort\">((tlv_t)0xF0)</pre><p style=\"color:inherit\">Mask for getting the Length value from an ESL TLV. </p><br><div>Definition at line <code>52</code> of file <code>common/esl_tag_core/inc/esl_tag_tlv.h</code></div><br></div><div><h3>esl_core_get_tlv_tag<span id=\"esl-core-get-tlv-tag\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-core-get-tlv-tag\">#</a></span></h3><blockquote>#define esl_core_get_tlv_tag</blockquote><b>Value:</b><pre class=\"macroshort\">(tlv_byte)</pre><p style=\"color:inherit\">Function like macro getter for 'Tag' field from an ESL TLV <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">tlv_tag_t 'Tag' value of the TLV </p></li></ul><br><div>Definition at line <code>60</code> of file <code>common/esl_tag_core/inc/esl_tag_tlv.h</code></div><br></div><div><h3>esl_core_get_tlv_len<span id=\"esl-core-get-tlv-len\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-core-get-tlv-len\">#</a></span></h3><blockquote>#define esl_core_get_tlv_len</blockquote><b>Value:</b><div class=\"fragment\"><div class=\"macro\">                                       ((tlv_t) \\</div><div class=\"macro\">                                       ((tlv_byte &amp; ESL_TLV_LEN_MASK) &gt;&gt; 4) + 1)</div></div><p style=\"color:inherit\">Function like macro getter for real 'Length' value from an ESL TLV <strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">tlv_length_t 'Length' value of the TLV </p></li></ul><br><div>Definition at line <code>68</code> of file <code>common/esl_tag_core/inc/esl_tag_tlv.h</code></div><br></div><div><h3>esl_core_set_tlv_tag<span id=\"esl-core-set-tlv-tag\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-core-set-tlv-tag\">#</a></span></h3><blockquote>#define esl_core_set_tlv_tag</blockquote><b>Value:</b><div class=\"fragment\"><div class=\"macro\">    do { \\</div><div class=\"macro\">    _tlv &amp;= (tlv_t)(~ESL_TLV_TAG_MASK);    \\</div><div class=\"macro\">    _tlv |= (((tlv_t)(_tag))               \\</div><div class=\"macro\">             &amp; ESL_TLV_TAG_MASK);          \\</div><div class=\"macro\">  } while (0)</div></div><p style=\"color:inherit\">Function like macro for setting the 'Tag' value of a TLV </p><br><div>Definition at line <code>77</code> of file <code>common/esl_tag_core/inc/esl_tag_tlv.h</code></div><br></div><div><h3>esl_core_set_tlv_len<span id=\"esl-core-set-tlv-len\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-core-set-tlv-len\">#</a></span></h3><blockquote>#define esl_core_set_tlv_len</blockquote><b>Value:</b><div class=\"fragment\"><div class=\"macro\">    do {       \\</div><div class=\"macro\">    _tlv &amp;= (tlv_t)(~ESL_TLV_LEN_MASK);          \\</div><div class=\"macro\">    _tlv |= _tlv | (((tlv_t)(((_len) - 1) &lt;&lt; 4)) \\</div><div class=\"macro\">                    &amp; ESL_TLV_LEN_MASK);         \\</div><div class=\"macro\">  } while (0)</div></div><p style=\"color:inherit\">Function like macro for setting the 'Length' value of a TLV </p><br><div>Definition at line <code>89</code> of file <code>common/esl_tag_core/inc/esl_tag_tlv.h</code></div><br></div><div><h3>ESL_DISPLAY_INIT_FUNC_PARAMETERS_COUNT<span id=\"esl-display-init-func-parameters-count\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-init-func-parameters-count\">#</a></span></h3><blockquote>#define ESL_DISPLAY_INIT_FUNC_PARAMETERS_COUNT</blockquote><b>Value:</b><pre class=\"macroshort\">(1)</pre><p style=\"color:inherit\">ESL Display extra parameters count for init_func esl_va_method function. </p><br><div>Definition at line <code>43</code> of file <code>common/esl_tag_display/inc/esl_tag_display.h</code></div><br></div><div><h3>ESL_DISPLAY_WRITE_FUNC_PARAMETERS_COUNT<span id=\"esl-display-write-func-parameters-count\" class=\"self-anchor\"><a class=\"perm\" href=\"#esl-display-write-func-parameters-count\">#</a></span></h3><blockquote>#define ESL_DISPLAY_WRITE_FUNC_PARAMETERS_COUNT</blockquote><b>Value:</b><pre class=\"macroshort\">(2)</pre><p style=\"color:inherit\">ESL Display extra parameters count for write_func esl_va_method function. </p><br><div>Definition at line <code>46</code> of file <code>common/esl_tag_display/inc/esl_tag_display.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/esl-tag-core", "status": "success"}