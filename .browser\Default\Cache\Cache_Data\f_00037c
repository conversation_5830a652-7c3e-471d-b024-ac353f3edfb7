{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Clock Manager<span id=\"clock-manager\" class=\"self-anchor\"><a class=\"perm\" href=\"#clock-manager\">#</a></span></h1><h3>Overview<span id=\"overview\" class=\"self-anchor\"><a class=\"perm\" href=\"#overview\">#</a></span></h3><p style=\"color:inherit\">Clock Manager is a platform-level software module that manages the device's oscillators and clock tree. The Clock Manager module is split into two main parts: The Initialization part and the Runtime part. The runtime part has its component <strong>clock_manager_runtime</strong> and can be used independently from the initialization part. The <strong>clock_manager</strong> component includes both the initialization part and the runtime part and it should be the component added to your project slcp file.</p><h3>Initialization<span id=\"initialization\" class=\"self-anchor\"><a class=\"perm\" href=\"#initialization\">#</a></span></h3><p style=\"color:inherit\">The initialization part includes the configuration files <strong>sl_clock_manager_oscillator_config.h</strong> and <strong>sl_clock_manager_tree_config.h</strong>. As their name indicates, those C header files are used to configure the different device oscillators and the device clock tree. Those header files use the CMSIS Configuration Wizard Annotations and are specific to each device. The API function <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-init\" target=\"_blank\" rel=\"\">sl_clock_manager_init()</a> is used to initialize the Clock Manager module based on the configuration values specified in the two configuration files. This function must be called early during your initialization sequence. If the SL System component (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/system\" target=\"_blank\" rel=\"\">System Initialization and Action Processing</a>) is used by your application, the <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-init\" target=\"_blank\" rel=\"\">sl_clock_manager_init()</a> call will be added automatically to your initialization sequence.</p><h4>Oscillators Configuration<span id=\"oscillators-configuration\" class=\"self-anchor\"><a class=\"perm\" href=\"#oscillators-configuration\">#</a></span></h4><p style=\"color:inherit\">Oscillators' configurations are all grouped in the <strong>sl_clock_manager_oscillator_config.h</strong> file. Crystal-based oscillators, HFXO and LFXO, have an enable/disable configuration to indicate if the required crystal is present or not. In the absence of the required crystal, the configuration must be disabled and the associated oscillator will not be initialized.</p><p style=\"color:inherit\">The HFXO configuration also provides the configuration for the Crystal Sharing feature when supported by the device. This feature allows to use the dedicated HFCLKOUT pin to output a sinusoidal clock that can be used as the HFXO input for another EFR device. In the configuration, you need to specify if your device is the leader or the follower. The leader will be the one outputting the clock and the follower, the one receiving the clock signal. In the leader configuration, the GPIO pin is used to receive the request from the follower. You can refer to your device datasheet to know the available location for the HFXO BUFOUT_REQ pin. In the follower mode, the pin configuration can be used to send an HFXO request signal to the leader. The \"High Frequency Clock Ouput\" section of your device reference manual also provides more details about this feature.</p><p style=\"color:inherit\">The first HFRCO module, whose output clock is called HFRCODPLL, can be connected to the DPLL module to have a better precision clock. When the DPLL is enabled through the configuration define <strong>SL_CLOCK_MANAGER_HFRCO_DPLL_EN</strong>, the DPLL settings take precedence over the HFRCO band configuration.</p><h4>Clock Tree Configuration<span id=\"clock-tree-configuration\" class=\"self-anchor\"><a class=\"perm\" href=\"#clock-tree-configuration\">#</a></span></h4><p style=\"color:inherit\">The device clock tree configurations are all grouped in the <strong>sl_clock_manager_tree_config.h</strong> file. Refer to your device's reference manual for the clock tree diagram and see which peripherals are connected to which clock branches. In the configuration file, each clock branch can be independently configured. However, to facilitate the clock setup for users, two additional configurations were added: <strong>SL_CLOCK_MANAGER_DEFAULT_HF_CLOCK_SOURCE</strong> and <strong>SL_CLOCK_MANAGER_DEFAULT_LF_CLOCK_SOURCE</strong>. Those configurations allow the selection of the default high-frequency and low-frequency oscillators to be used inside the clock tree. Every clock branch that can benefit from those default selections will use them by default. On certain devices, the <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/power-manager\" target=\"_blank\" rel=\"\">Power Manager</a> module offers an Execution Modes feature with the <strong>SL_POWER_MANAGER_EXECUTION_MODES_FEATURE_EN</strong> configuration. When this feature is enabled, the <strong>SL_CLOCK_MANAGER_SYSCLK_SOURCE</strong> configuration could be overriden by the Execution Modes feature. Refer to the description of <strong>SL_CLOCK_MANAGER_SYSCLK_SOURCE</strong> in <strong>sl_clock_manager_tree_config.h</strong> file to know if this is the case.</p><p style=\"color:inherit\">Some peripherals have an internal clock divider. Those are not handled by the Clock Manager configuration. The peripheral driver will usually expose the divider configuration when such a divider is present inside the peripheral module.</p><p style=\"color:inherit\">The Clock tree configuration is available at compile-time only. The Clock Manager module does not offer API functions to manipulate the clock tree at runtime.</p><p style=\"color:inherit\">The more oscillators are used by different clock branches the more power consumption you will have. To limit your power consumption, you can try to limit the number of oscillators used. So for example, only use one High-frequency oscillator and one Low-frequency oscillator across the clock tree. However, if the application is radio-based, the HFXO oscillator is mandatory for the Radio clock branch and if the Radio clock branch is connected to the SYSCLK branch, this will limit you to use HFXO for SYSCLK as well. In this specific case, SYSCLK could also be configured to use HFRCO with DPLL, but the chosen frequency must be two times the HFXO frequency so that the Radio module can retrieve the HF crystal frequency with its divider. This will also come with an increase in power consumption since both HFXO and HFRCO oscillators will be used. Refer to your device reference manual to know if your Radio clock is connected to the SYSCLK clock branch or not.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The Clock Manager Initialization is incompatible with the <strong>device_init_clocks</strong> (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-init-clocks\" target=\"_blank\" rel=\"\">Clock Initialization</a>), <strong>device_init_hfxo</strong> (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-init-hfxo\" target=\"_blank\" rel=\"\">HFXO Initialization</a>), <strong>device_init_hfrco</strong> (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-init-hfrco\" target=\"_blank\" rel=\"\">HFRCO Initialization</a>) <strong>device_init_dpll</strong> (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-init-dpll\" target=\"_blank\" rel=\"\">DPLL Initialization</a>), <strong>device_init_rffpll</strong> (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-init-rffpll\" target=\"_blank\" rel=\"\">RFFPLL Initialization</a>), <strong>device_init_usbpll</strong> (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-init-usbpll\" target=\"_blank\" rel=\"\">USB PLL Initialization</a>), <strong>device_init_lfxo</strong> (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-init-lfxo\" target=\"_blank\" rel=\"\">LFXO Initialization</a>) and <strong>device_init_lfrco</strong> (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-init-lfrco\" target=\"_blank\" rel=\"\">LFRCO Initialization</a>) components. This does not mean that the <strong>device_init</strong> component (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-init\" target=\"_blank\" rel=\"\">Device Initialization</a>) is incompatible with the <strong>clock_manager</strong> component. The <strong>device_init</strong> component can pull other initialization modules like EMU and DCDC that are not related to clocks. Therefore, both <strong>device_init</strong> and <strong>clock_manager</strong> should be present in your project file. SLC will take care of pulling only the sub <strong>device_init_xxx</strong> components that are needed.</p></li></ul><p style=\"color:inherit\">\nThe runtime part, which is associated with the <strong>clock_manager_runtime</strong> component, has also an initialization function of its own, <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-runtime-init\" target=\"_blank\" rel=\"\">sl_clock_manager_runtime_init()</a>. This function must also be part of the initialization sequence. If the SL System component (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/system\" target=\"_blank\" rel=\"\">System Initialization and Action Processing</a>) is used by your application, the <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-runtime-init\" target=\"_blank\" rel=\"\">sl_clock_manager_runtime_init()</a> call will be added automatically to your initialization sequence.</p><h3>Functionalities<span id=\"functionalities\" class=\"self-anchor\"><a class=\"perm\" href=\"#functionalities\">#</a></span></h3><p style=\"color:inherit\">The Runtime part includes functionalities related to oscillators, clock tree and the CMU hardware module features. The main functionalities are:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Retrieving the frequency or precision of an oscillator or clock branch</p></li><li><p style=\"color:inherit\">Enabling/Disabling modules' bus clock</p></li><li><p style=\"color:inherit\">Retrieving or setting calibration values for oscillators</p></li><li><p style=\"color:inherit\">Exporting clocks to GPIO</p></li><li><p style=\"color:inherit\">Starting an RCO Calibration process based on a reference clock source</p></li></ul><h4>Retrieve the frequency or precision of an oscillator or clock branch<span id=\"retrieve-the-frequency-or-precision-of-an-oscillator-or-clock-branch\" class=\"self-anchor\"><a class=\"perm\" href=\"#retrieve-the-frequency-or-precision-of-an-oscillator-or-clock-branch\">#</a></span></h4><p style=\"color:inherit\">API functions <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-get-oscillator-frequency\" target=\"_blank\" rel=\"\">sl_clock_manager_get_oscillator_frequency()</a> and <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-get-oscillator-precision\" target=\"_blank\" rel=\"\">sl_clock_manager_get_oscillator_precision()</a> allow retrieving respectively the frequency and precision of a given oscillator. Similar functions exist for clock branches: <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-get-clock-branch-frequency\" target=\"_blank\" rel=\"\">sl_clock_manager_get_clock_branch_frequency()</a> and <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-get-clock-branch-precision\" target=\"_blank\" rel=\"\">sl_clock_manager_get_clock_branch_precision()</a>.</p><p style=\"color:inherit\">To retrieve the frequency or precision of a specific peripheral, you will first need to retrieve to which clock branch this peripheral is connected. To do so, the Device Manager and its <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-peripheral\" target=\"_blank\" rel=\"\">Device Abstraction Peripheral</a> can be used. The below code example shows how to retrieve the clock branch of the TIMER0 peripheral.</p><pre class=\"language-clike\"><code class=\"language-clike\">#include <span class=\"token string\">\"sl_clock_manager.h\"</span>\n#include <span class=\"token string\">\"sl_device_peripheral.h\"</span>\n\nsl_status_t status<span class=\"token punctuation\">;</span>\nuint32_t freq<span class=\"token punctuation\">;</span>\nsl_clock_branch_t clock_branch<span class=\"token punctuation\">;</span>\n\nclock_branch <span class=\"token operator\">=</span> <span class=\"token function\">sl_device_peripheral_get_clock_branch</span><span class=\"token punctuation\">(</span>SL_PERIPHERAL_TIMER0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\nstatus <span class=\"token operator\">=</span> <span class=\"token function\">sl_clock_manager_get_clock_branch_frequency</span><span class=\"token punctuation\">(</span>clock_branch<span class=\"token punctuation\">,</span> <span class=\"token operator\">&amp;</span>freq<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n</code></pre><h4>Enable/Disable modules' bus clock<span id=\"enable-disable-modules-bus-clock\" class=\"self-anchor\"><a class=\"perm\" href=\"#enable-disable-modules-bus-clock\">#</a></span></h4><p style=\"color:inherit\">Before accessing a peripheral's register interface, its bus clock must be enabled, or else a bus fault exception will be triggered. API functions <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-enable-bus-clock\" target=\"_blank\" rel=\"\">sl_clock_manager_enable_bus_clock()</a> and <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-disable-bus-clock\" target=\"_blank\" rel=\"\">sl_clock_manager_disable_bus_clock()</a> allow to perform such operations.</p><p style=\"color:inherit\">Note that the peripheral clock will automatically be enabled when a peripheral is enabled with the clock on-demand feature.</p><h4>Oscillator Calibration<span id=\"oscillator-calibration\" class=\"self-anchor\"><a class=\"perm\" href=\"#oscillator-calibration\">#</a></span></h4><p style=\"color:inherit\">The Clock Manager initialization, if present, will calibrate the different oscillators during the initialization sequence, but sometimes calibration values must be updated during runtime in certain conditions, for example, if the device temperature changes too much. This is considered an advanced functionality and users must be careful as to when to use this functionality.</p><p style=\"color:inherit\">API functions <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-set-rc-oscillator-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_set_rc_oscillator_calibration()</a> and <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-get-rc-oscillator-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_get_rc_oscillator_calibration()</a> allow to set or get the CAL register of HFRCO and LFRCO oscillators. Not all devices have an LFRCO module with a CAL register. Some LFRCO modules will have a high-precision configuration allowing to use the HFXO to auto-calibrate the LFRCO. Refer to your device reference manual to retrieve oscillator specifications.</p><p style=\"color:inherit\">API functions <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-set-hfxo-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_set_hfxo_calibration()</a> and <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-get-hfxo-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_get_hfxo_calibration()</a> allow to set or get the <strong>COREBIASANA</strong> inside the HFXO <strong>XTALCTRL</strong> register. The HFXO module has a Core Bias Optimization stage at the end of the oscillator startup sequence that allows to further optimize current consumption. This optimization will automatically set the <strong>COREBIASANA</strong> bitfield when finished. Upon reset, this optimization will run the first time HFXO is started and afterwards, the <strong>XTALCTRL-&gt;SKIPCOREBIASOPT</strong> bit will automatically be set so that next time HFXO is started during the application lifetime, the optimization stage will be skipped. This optimization stage takes a while to run, in the order of hundreds of milliseconds, therefore we don't want it to run each time HFXO is started. With the function <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-set-hfxo-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_set_hfxo_calibration()</a> it is possible to manually set the <strong>COREBIASANA</strong> bitfield and set the <strong>SKIPCOREBIASOPT</strong> bit. This function will usually be used in the context of an EM4 wake-up where to save on the initialization sequence time, we want to skip the Core Bias Optimization stage and manually set the value that would have previously been retrieved with <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-get-hfxo-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_get_hfxo_calibration()</a> and saved in an EM4 retained memory. In this context, <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-set-hfxo-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_set_hfxo_calibration()</a> will need to be called early in the initialization sequence, before the usual clock initialization function.</p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#slx-clock-manager-hfxo-set-ctune\" target=\"_blank\" rel=\"\">slx_clock_manager_hfxo_set_ctune()</a>, <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#slx-clock-manager-hfxo-get-ctune\" target=\"_blank\" rel=\"\">slx_clock_manager_hfxo_get_ctune()</a> and <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#slx-clock-manager-hfxo-calibrate-ctune\" target=\"_blank\" rel=\"\">slx_clock_manager_hfxo_calibrate_ctune()</a> functions allow to manipulate the HFXO tuning capacitances. Changing the CTUNE value while HFXO is running can result in significant clock glitches for one clock period. Therefore, those functions should be used with caution. The difference between the <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#slx-clock-manager-hfxo-set-ctune\" target=\"_blank\" rel=\"\">slx_clock_manager_hfxo_set_ctune()</a> and <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#slx-clock-manager-hfxo-calibrate-ctune\" target=\"_blank\" rel=\"\">slx_clock_manager_hfxo_calibrate_ctune()</a> functions is that the calibration one will also start and wait for the HFXO Core Bias Optimization stage to complete.</p><p style=\"color:inherit\">API functions <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-set-lfxo-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_set_lfxo_calibration()</a> and <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-get-lfxo-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_get_lfxo_calibration()</a> allow to set and get the LFXO CTUNE value.</p><h4>Export clocks to GPIO<span id=\"export-clocks-to-gpio\" class=\"self-anchor\"><a class=\"perm\" href=\"#export-clocks-to-gpio\">#</a></span></h4><p style=\"color:inherit\">The CMU module offers the functionality to export a given clock source to a GPIO pin. Refer to function <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-set-gpio-clock-output\" target=\"_blank\" rel=\"\">sl_clock_manager_set_gpio_clock_output()</a> for more details and the <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-export-clock-source-t\" target=\"_blank\" rel=\"\">sl_clock_manager_export_clock_source_t</a> enum for a list of acceptable clock sources. Note that there is a specific clock branch named EXPCLK that is usually connected to the SYSCLK and offers an additional divider.</p><h4>RCO Calibration<span id=\"rco-calibration\" class=\"self-anchor\"><a class=\"perm\" href=\"#rco-calibration\">#</a></span></h4><p style=\"color:inherit\">The CMU module also offers RCO Calibration hardware support. This can be used to calibrate at runtime HFRCO and LFRCO oscillators using a high-precision reference clock. Refer to your device reference manual for more details about this functionality. API function <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-configure-rco-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_configure_rco_calibration()</a> can be used to configure the calibration process. Then <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-start-rco-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_start_rco_calibration()</a> and <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-stop-rco-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_stop_rco_calibration()</a> can be called to start/stop the process. <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-wait-rco-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_wait_rco_calibration()</a> function can be called to actively wait for the process to finish. And finally, <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-get-rco-calibration-count\" target=\"_blank\" rel=\"\">sl_clock_manager_get_rco_calibration_count()</a> can be called to retrieve the calibration process result. </p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-export-clock-source-t\">sl_clock_manager_export_clock_source_t</a> {</div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_DISABLED </div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_FSRCO </div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_HFXO </div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_HFRCODPLL </div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_HFRCOEM23 </div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_HFEXPCLK </div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_LFXO </div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_PLFRCO </div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_LFRCO </div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_ULFRCO </div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_HCLK </div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Export clock source. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-export-clock-output-select-t\">sl_clock_manager_export_clock_output_select_t</a> {</div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_OUTPUT_SELECT_0 = 0</div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_OUTPUT_SELECT_1 </div><div class=\"enum\">SL_CLOCK_MANAGER_EXPORT_CLOCK_OUTPUT_SELECT_2 </div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Export clock output selection. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-clock-calibration-t\">sl_clock_manager_clock_calibration_t</a> {</div><div class=\"enum\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_HCLK </div><div class=\"enum\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_PRS </div><div class=\"enum\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_HFXO </div><div class=\"enum\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_LFXO </div><div class=\"enum\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_HFRCODPLL </div><div class=\"enum\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_HFRCOEM23 </div><div class=\"enum\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_FSRCO </div><div class=\"enum\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_LFRCO </div><div class=\"enum\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_ULFRCO </div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Clocks available for Calibration. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-runtime-init\">sl_clock_manager_runtime_init</a>(void)</div><div class=\"classdescription\"><p style=\"color:inherit\">Performs Clock Manager runtime initialization. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-get-oscillator-frequency\">sl_clock_manager_get_oscillator_frequency</a>(sl_oscillator_t oscillator, uint32_t *frequency)</div><div class=\"classdescription\"><p style=\"color:inherit\">Gets frequency of given oscillator. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-get-oscillator-precision\">sl_clock_manager_get_oscillator_precision</a>(sl_oscillator_t oscillator, uint16_t *precision)</div><div class=\"classdescription\"><p style=\"color:inherit\">Gets precision of given oscillator. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-get-clock-branch-frequency\">sl_clock_manager_get_clock_branch_frequency</a>(sl_clock_branch_t clock_branch, uint32_t *frequency)</div><div class=\"classdescription\"><p style=\"color:inherit\">Gets frequency of given clock branch. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-get-clock-branch-precision\">sl_clock_manager_get_clock_branch_precision</a>(sl_clock_branch_t clock_branch, uint16_t *precision)</div><div class=\"classdescription\"><p style=\"color:inherit\">Gets precision of given clock branch. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-enable-bus-clock\">sl_clock_manager_enable_bus_clock</a>(sl_bus_clock_t module_bus_clock)</div><div class=\"classdescription\"><p style=\"color:inherit\">Enables the given module's bus clock. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-disable-bus-clock\">sl_clock_manager_disable_bus_clock</a>(sl_bus_clock_t module_bus_clock)</div><div class=\"classdescription\"><p style=\"color:inherit\">Disables the given module's bus clock. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-set-gpio-clock-output\">sl_clock_manager_set_gpio_clock_output</a>(sl_clock_manager_export_clock_source_t export_clock_source, sl_clock_manager_export_clock_output_select_t output_select, uint16_t hfexp_divider, uint32_t port, uint32_t pin)</div><div class=\"classdescription\"><p style=\"color:inherit\">Configures one clock export output with specified clock source. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-set-rc-oscillator-calibration\">sl_clock_manager_set_rc_oscillator_calibration</a>(sl_oscillator_t oscillator, uint32_t val)</div><div class=\"classdescription\"><p style=\"color:inherit\">Sets the RC oscillator frequency tuning control. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-get-rc-oscillator-calibration\">sl_clock_manager_get_rc_oscillator_calibration</a>(sl_oscillator_t oscillator, uint32_t *val)</div><div class=\"classdescription\"><p style=\"color:inherit\">Gets the RC oscillator frequency tuning setting. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-set-hfxo-calibration\">sl_clock_manager_set_hfxo_calibration</a>(uint32_t val)</div><div class=\"classdescription\"><p style=\"color:inherit\">Sets the HFXO calibration value. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-get-hfxo-calibration\">sl_clock_manager_get_hfxo_calibration</a>(uint32_t *val)</div><div class=\"classdescription\"><p style=\"color:inherit\">Gets the HFXO calibration value. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#slx-clock-manager-hfxo-set-ctune\">slx_clock_manager_hfxo_set_ctune</a>(uint32_t ctune)</div><div class=\"classdescription\"><p style=\"color:inherit\">Sets the HFXO's CTUNE. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#slx-clock-manager-hfxo-get-ctune\">slx_clock_manager_hfxo_get_ctune</a>(uint32_t *ctune)</div><div class=\"classdescription\"><p style=\"color:inherit\">Gets the HFXO's CTUNE. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#slx-clock-manager-hfxo-calibrate-ctune\">slx_clock_manager_hfxo_calibrate_ctune</a>(uint32_t ctune)</div><div class=\"classdescription\"><p style=\"color:inherit\">Updates the tuning capacitances and calibrate the Core Bias Current. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-set-lfxo-calibration\">sl_clock_manager_set_lfxo_calibration</a>(uint32_t val)</div><div class=\"classdescription\"><p style=\"color:inherit\">Sets the LFXO frequency tuning control. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-get-lfxo-calibration\">sl_clock_manager_get_lfxo_calibration</a>(uint32_t *val)</div><div class=\"classdescription\"><p style=\"color:inherit\">Gets the LFXO frequency tuning setting. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-configure-rco-calibration\">sl_clock_manager_configure_rco_calibration</a>(uint32_t cycles, sl_clock_manager_clock_calibration_t down_counter_selection, sl_clock_manager_clock_calibration_t up_counter_selection, bool continuous_calibration)</div><div class=\"classdescription\"><p style=\"color:inherit\">Configures the RCO calibration. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-start-rco-calibration\">sl_clock_manager_start_rco_calibration</a>(void)</div><div class=\"classdescription\"><p style=\"color:inherit\">Starts the RCO calibration. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-stop-rco-calibration\">sl_clock_manager_stop_rco_calibration</a>(void)</div><div class=\"classdescription\"><p style=\"color:inherit\">Stops the RCO calibration. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-wait-rco-calibration\">sl_clock_manager_wait_rco_calibration</a>(void)</div><div class=\"classdescription\"><p style=\"color:inherit\">Waits for the RCO calibration to finish. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-get-rco-calibration-count\">sl_clock_manager_get_rco_calibration_count</a>(uint32_t *count)</div><div class=\"classdescription\"><p style=\"color:inherit\">Gets calibration count value, returns the value of the up counter. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-wait-usbpll\">sl_clock_manager_wait_usbpll</a>(void)</div><div class=\"classdescription\"><p style=\"color:inherit\">Waits for USBPLL clock to be ready. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-hfxo-notify-consecutive-failed-startups\">sl_clock_manager_hfxo_notify_consecutive_failed_startups</a>(void)</div><div class=\"classdescription\"><p style=\"color:inherit\">When this callback function is called, it means that HFXO failed twice in a row to start with normal configurations. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-clock-manager-init\">sl_clock_manager_init</a>(void)</div><div class=\"classdescription\"><p style=\"color:inherit\">Initializes Oscillators and Clock branches. </p></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>sl_clock_manager_export_clock_source_t<span id=\"sl-clock-manager-export-clock-source-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-export-clock-source-t\">#</a></span></h3><blockquote>sl_clock_manager_export_clock_source_t</blockquote><p style=\"color:inherit\">Export clock source. </p><p style=\"color:inherit\">This is to be used with the <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-set-gpio-clock-output\" target=\"_blank\" rel=\"\">sl_clock_manager_set_gpio_clock_output()</a> API function. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_DISABLED</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Source Disabled. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_FSRCO</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Source FSRCO. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_HFXO</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Source HFXO. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_HFRCODPLL</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Source HFRCODPLL. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_HFRCOEM23</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Source HFRCOEM23. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_HFEXPCLK</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Source HFEXPCLK. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_LFXO</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Source LFXO. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_PLFRCO</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Source PLFRCO. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_LFRCO</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Source LFRCO. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_ULFRCO</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Source ULFRCO. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_SOURCE_HCLK</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Source HCLK. </p></td></tr></tbody></table><br><div>Definition at line <code>267</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_export_clock_output_select_t<span id=\"sl-clock-manager-export-clock-output-select-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-export-clock-output-select-t\">#</a></span></h3><blockquote>sl_clock_manager_export_clock_output_select_t</blockquote><p style=\"color:inherit\">Export clock output selection. </p><p style=\"color:inherit\">This is to be used with the <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-set-gpio-clock-output\" target=\"_blank\" rel=\"\">sl_clock_manager_set_gpio_clock_output()</a> API function. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_OUTPUT_SELECT_0</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Output #0. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_OUTPUT_SELECT_1</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Output #1. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_EXPORT_CLOCK_OUTPUT_SELECT_2</td><td class=\"fielddescription\"><p style=\"color:inherit\">Export Clock Output #2. </p></td></tr></tbody></table><br><div>Definition at line <code>283</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_clock_calibration_t<span id=\"sl-clock-manager-clock-calibration-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-clock-calibration-t\">#</a></span></h3><blockquote>sl_clock_manager_clock_calibration_t</blockquote><p style=\"color:inherit\">Clocks available for Calibration. </p><p style=\"color:inherit\">This is to be used with the <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-configure-rco-calibration\" target=\"_blank\" rel=\"\">sl_clock_manager_configure_rco_calibration()</a> API function. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_HCLK</td><td class=\"fielddescription\"><p style=\"color:inherit\">Clock Calibration HCLK. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_PRS</td><td class=\"fielddescription\"><p style=\"color:inherit\">Clock Calibration PRS. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_HFXO</td><td class=\"fielddescription\"><p style=\"color:inherit\">Clock Calibration HFXO. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_LFXO</td><td class=\"fielddescription\"><p style=\"color:inherit\">Clock Calibration LFXO. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_HFRCODPLL</td><td class=\"fielddescription\"><p style=\"color:inherit\">Clock Calibration HFRCODPLL. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_HFRCOEM23</td><td class=\"fielddescription\"><p style=\"color:inherit\">Clock Calibration HFRCOEM23. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_FSRCO</td><td class=\"fielddescription\"><p style=\"color:inherit\">Clock Calibration FSRCO. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_LFRCO</td><td class=\"fielddescription\"><p style=\"color:inherit\">Clock Calibration LFRCO. </p></td></tr><tr><td class=\"fieldname\">SL_CLOCK_MANAGER_CLOCK_CALIBRATION_ULFRCO</td><td class=\"fielddescription\"><p style=\"color:inherit\">Clock Calibration ULFRCO. </p></td></tr></tbody></table><br><div>Definition at line <code>291</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_clock_manager_runtime_init<span id=\"sl-clock-manager-runtime-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-runtime-init\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_runtime_init (void )</blockquote><p style=\"color:inherit\">Performs Clock Manager runtime initialization. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>312</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_get_oscillator_frequency<span id=\"sl-clock-manager-get-oscillator-frequency\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-get-oscillator-frequency\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_get_oscillator_frequency (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-clock#sl-oscillator-t\" target=\"_blank\" rel=\"\">sl_oscillator_t</a> oscillator, uint32_t * frequency)</blockquote><p style=\"color:inherit\">Gets frequency of given oscillator. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">oscillator</td><td><p style=\"color:inherit\">Oscillator</p></td></tr><tr><td>[out]</td><td class=\"paramname\">frequency</td><td><p style=\"color:inherit\">Oscillator's frequency in Hertz</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>324</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_get_oscillator_precision<span id=\"sl-clock-manager-get-oscillator-precision\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-get-oscillator-precision\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_get_oscillator_precision (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-clock#sl-oscillator-t\" target=\"_blank\" rel=\"\">sl_oscillator_t</a> oscillator, uint16_t * precision)</blockquote><p style=\"color:inherit\">Gets precision of given oscillator. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">oscillator</td><td><p style=\"color:inherit\">Oscillator</p></td></tr><tr><td>[out]</td><td class=\"paramname\">precision</td><td><p style=\"color:inherit\">Oscillator's precision in PPM</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>337</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_get_clock_branch_frequency<span id=\"sl-clock-manager-get-clock-branch-frequency\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-get-clock-branch-frequency\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_get_clock_branch_frequency (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-clock#sl-clock-branch-t\" target=\"_blank\" rel=\"\">sl_clock_branch_t</a> clock_branch, uint32_t * frequency)</blockquote><p style=\"color:inherit\">Gets frequency of given clock branch. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">clock_branch</td><td><p style=\"color:inherit\">Clock Branch</p></td></tr><tr><td>[out]</td><td class=\"paramname\">frequency</td><td><p style=\"color:inherit\">Clock Branch's frequency in Hertz</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>350</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_get_clock_branch_precision<span id=\"sl-clock-manager-get-clock-branch-precision\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-get-clock-branch-precision\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_get_clock_branch_precision (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-clock#sl-clock-branch-t\" target=\"_blank\" rel=\"\">sl_clock_branch_t</a> clock_branch, uint16_t * precision)</blockquote><p style=\"color:inherit\">Gets precision of given clock branch. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">clock_branch</td><td><p style=\"color:inherit\">Clock Branch</p></td></tr><tr><td>[out]</td><td class=\"paramname\">precision</td><td><p style=\"color:inherit\">Clock Branch's precision in PPM</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>363</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_enable_bus_clock<span id=\"sl-clock-manager-enable-bus-clock\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-enable-bus-clock\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_enable_bus_clock (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-clock#sl-bus-clock-t\" target=\"_blank\" rel=\"\">sl_bus_clock_t</a> module_bus_clock)</blockquote><p style=\"color:inherit\">Enables the given module's bus clock. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">module_bus_clock</td><td><p style=\"color:inherit\">module's bus clock to enable.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">modules' bus clocks are defined in the <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-clock\" target=\"_blank\" rel=\"\">Device Manager Clock</a> in the Bus Clock Defines section. </p></li></ul><br><div>Definition at line <code>377</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_disable_bus_clock<span id=\"sl-clock-manager-disable-bus-clock\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-disable-bus-clock\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_disable_bus_clock (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-clock#sl-bus-clock-t\" target=\"_blank\" rel=\"\">sl_bus_clock_t</a> module_bus_clock)</blockquote><p style=\"color:inherit\">Disables the given module's bus clock. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">module_bus_clock</td><td><p style=\"color:inherit\">module's bus clock to disable.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">modules' bus clocks are defined in the <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-clock\" target=\"_blank\" rel=\"\">Device Manager Clock</a> in the Bus Clock Defines section. </p></li></ul><br><div>Definition at line <code>390</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_set_gpio_clock_output<span id=\"sl-clock-manager-set-gpio-clock-output\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-set-gpio-clock-output\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_set_gpio_clock_output (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-export-clock-source-t\" target=\"_blank\" rel=\"\">sl_clock_manager_export_clock_source_t</a> export_clock_source, <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-export-clock-output-select-t\" target=\"_blank\" rel=\"\">sl_clock_manager_export_clock_output_select_t</a> output_select, uint16_t hfexp_divider, uint32_t port, uint32_t pin)</blockquote><p style=\"color:inherit\">Configures one clock export output with specified clock source. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">export_clock_source</td><td><p style=\"color:inherit\">One of the exportable clock source.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">output_select</td><td><p style=\"color:inherit\">Selected export clock output channel.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">hfexp_divider</td><td><p style=\"color:inherit\">HFEXP clock divider (1 to 32). Note: This parameter only affects the EXPCLK branch frequency.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">port</td><td><p style=\"color:inherit\">GPIO port to output exported clock.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">pin</td><td><p style=\"color:inherit\">GPIO pin number to output exported clock.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>410</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_set_rc_oscillator_calibration<span id=\"sl-clock-manager-set-rc-oscillator-calibration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-set-rc-oscillator-calibration\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_set_rc_oscillator_calibration (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-clock#sl-oscillator-t\" target=\"_blank\" rel=\"\">sl_oscillator_t</a> oscillator, uint32_t val)</blockquote><p style=\"color:inherit\">Sets the RC oscillator frequency tuning control. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">oscillator</td><td><p style=\"color:inherit\">RC Oscillator to set tuning value for.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">val</td><td><p style=\"color:inherit\">The RC oscillator frequency tuning setting to use.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">RC Oscillator tuning is done during production, and the tuning value is loaded after a reset by the Clock Manager initialization code. Changing the tuning value from the calibrated value is for more advanced use. Certain oscillators also have build-in tuning optimization.</p></li><li><p style=\"color:inherit\">Supported RC oscillators include:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_OSCILLATOR_HFRCODPLL</p></li><li><p style=\"color:inherit\">SL_OSCILLATOR_HFRCOEM23</p></li><li><p style=\"color:inherit\">SL_OSCILLATOR_LFRCO </p></li></ul></li></ul><br><div>Definition at line <code>436</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_get_rc_oscillator_calibration<span id=\"sl-clock-manager-get-rc-oscillator-calibration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-get-rc-oscillator-calibration\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_get_rc_oscillator_calibration (<a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/device-clock#sl-oscillator-t\" target=\"_blank\" rel=\"\">sl_oscillator_t</a> oscillator, uint32_t * val)</blockquote><p style=\"color:inherit\">Gets the RC oscillator frequency tuning setting. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">oscillator</td><td><p style=\"color:inherit\">An RC oscillator to get tuning value for.</p></td></tr><tr><td>[out]</td><td class=\"paramname\">val</td><td><p style=\"color:inherit\">The RC oscillator frequency tuning setting in use.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Supported RC oscillators include:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_OSCILLATOR_HFRCODPLL</p></li><li><p style=\"color:inherit\">SL_OSCILLATOR_HFRCOEM23</p></li><li><p style=\"color:inherit\">SL_OSCILLATOR_LFRCO </p></li></ul></li></ul><br><div>Definition at line <code>454</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_set_hfxo_calibration<span id=\"sl-clock-manager-set-hfxo-calibration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-set-hfxo-calibration\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_set_hfxo_calibration (uint32_t val)</blockquote><p style=\"color:inherit\">Sets the HFXO calibration value. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">val</td><td><p style=\"color:inherit\">The HFXO calibration setting to use.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>466</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_get_hfxo_calibration<span id=\"sl-clock-manager-get-hfxo-calibration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-get-hfxo-calibration\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_get_hfxo_calibration (uint32_t * val)</blockquote><p style=\"color:inherit\">Gets the HFXO calibration value. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[out]</td><td class=\"paramname\">val</td><td><p style=\"color:inherit\">The current HFXO calibration value.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>476</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>slx_clock_manager_hfxo_set_ctune<span id=\"slx-clock-manager-hfxo-set-ctune\" class=\"self-anchor\"><a class=\"perm\" href=\"#slx-clock-manager-hfxo-set-ctune\">#</a></span></h3><blockquote>sl_status_t slx_clock_manager_hfxo_set_ctune (uint32_t ctune)</blockquote><p style=\"color:inherit\">Sets the HFXO's CTUNE. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">ctune</td><td><p style=\"color:inherit\">The HFXO's CTUNE value.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Sets the XI value to the given ctune value and sets the XO value based on that same value, but with an offset that is hardware dependent. Updating CTune while the crystal oscillator is running can result in significant clock glitches for one XO clock period. Should be used with caution. </p></li></ul><br><div>Definition at line <code>492</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>slx_clock_manager_hfxo_get_ctune<span id=\"slx-clock-manager-hfxo-get-ctune\" class=\"self-anchor\"><a class=\"perm\" href=\"#slx-clock-manager-hfxo-get-ctune\">#</a></span></h3><blockquote>sl_status_t slx_clock_manager_hfxo_get_ctune (uint32_t * ctune)</blockquote><p style=\"color:inherit\">Gets the HFXO's CTUNE. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[out]</td><td class=\"paramname\">ctune</td><td><p style=\"color:inherit\">The returned HFXO's CTUNE value.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">This function only returns the CTUNE XI value. The XO value follows the XI value with a fixed delta that is hardware dependent. </p></li></ul><br><div>Definition at line <code>506</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>slx_clock_manager_hfxo_calibrate_ctune<span id=\"slx-clock-manager-hfxo-calibrate-ctune\" class=\"self-anchor\"><a class=\"perm\" href=\"#slx-clock-manager-hfxo-calibrate-ctune\">#</a></span></h3><blockquote>sl_status_t slx_clock_manager_hfxo_calibrate_ctune (uint32_t ctune)</blockquote><p style=\"color:inherit\">Updates the tuning capacitances and calibrate the Core Bias Current. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">ctune</td><td><p style=\"color:inherit\">The HFXO's CTUNE value.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Calibrating the CTUNE is time consuming and will cause glitches on the HFXO's clock. Care and caution should be taken when using this API. </p></li></ul><br><div>Definition at line <code>519</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_set_lfxo_calibration<span id=\"sl-clock-manager-set-lfxo-calibration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-set-lfxo-calibration\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_set_lfxo_calibration (uint32_t val)</blockquote><p style=\"color:inherit\">Sets the LFXO frequency tuning control. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">val</td><td><p style=\"color:inherit\">The LFXO frequency tuning setting to use.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>529</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_get_lfxo_calibration<span id=\"sl-clock-manager-get-lfxo-calibration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-get-lfxo-calibration\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_get_lfxo_calibration (uint32_t * val)</blockquote><p style=\"color:inherit\">Gets the LFXO frequency tuning setting. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[out]</td><td class=\"paramname\">val</td><td><p style=\"color:inherit\">The LFXO frequency tuning setting to use.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>539</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_configure_rco_calibration<span id=\"sl-clock-manager-configure-rco-calibration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-configure-rco-calibration\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_configure_rco_calibration (uint32_t cycles, <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-clock-calibration-t\" target=\"_blank\" rel=\"\">sl_clock_manager_clock_calibration_t</a> down_counter_selection, <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-clock-calibration-t\" target=\"_blank\" rel=\"\">sl_clock_manager_clock_calibration_t</a> up_counter_selection, bool continuous_calibration)</blockquote><p style=\"color:inherit\">Configures the RCO calibration. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">cycles</td><td><p style=\"color:inherit\">Number of cycles to run calibration. Increasing this number increases precision, but the calibration will take more time.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">down_counter_selection</td><td><p style=\"color:inherit\">The clock which will be counted down cycles.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">up_counter_selection</td><td><p style=\"color:inherit\">The number of cycles generated by this clock will be counted and added up, the result can be given with <a href=\"http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager#sl-clock-manager-get-rco-calibration-count\" target=\"_blank\" rel=\"\">sl_clock_manager_get_rco_calibration_count()</a>.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">continuous_calibration</td><td><p style=\"color:inherit\">Flag when true configures continuous calibration.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">RCO calibration related functions are not thread-safe and should therefore not be called across multiple tasks. </p></li></ul><br><div>Definition at line <code>565</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_start_rco_calibration<span id=\"sl-clock-manager-start-rco-calibration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-start-rco-calibration\">#</a></span></h3><blockquote>void sl_clock_manager_start_rco_calibration (void )</blockquote><p style=\"color:inherit\">Starts the RCO calibration. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">RCO calibration related functions are not thread-safe and should therefore not be called across multiple tasks. </p></li></ul><br><div>Definition at line <code>576</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_stop_rco_calibration<span id=\"sl-clock-manager-stop-rco-calibration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-stop-rco-calibration\">#</a></span></h3><blockquote>void sl_clock_manager_stop_rco_calibration (void )</blockquote><p style=\"color:inherit\">Stops the RCO calibration. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">RCO calibration related functions are not thread-safe and should therefore not be called across multiple tasks. </p></li></ul><br><div>Definition at line <code>584</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_wait_rco_calibration<span id=\"sl-clock-manager-wait-rco-calibration\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-wait-rco-calibration\">#</a></span></h3><blockquote>void sl_clock_manager_wait_rco_calibration (void )</blockquote><p style=\"color:inherit\">Waits for the RCO calibration to finish. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">RCO calibration related functions are not thread-safe and should therefore not be called across multiple tasks. </p></li></ul><br><div>Definition at line <code>592</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_get_rco_calibration_count<span id=\"sl-clock-manager-get-rco-calibration-count\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-get-rco-calibration-count\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_get_rco_calibration_count (uint32_t * count)</blockquote><p style=\"color:inherit\">Gets calibration count value, returns the value of the up counter. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[out]</td><td class=\"paramname\">count</td><td><p style=\"color:inherit\">Calibration count value.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">RCO calibration related functions are not thread-safe and should therefore not be called across multiple tasks. </p></li></ul><br><div>Definition at line <code>605</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_wait_usbpll<span id=\"sl-clock-manager-wait-usbpll\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-wait-usbpll\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_wait_usbpll (void )</blockquote><p style=\"color:inherit\">Waits for USBPLL clock to be ready. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>613</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_hfxo_notify_consecutive_failed_startups<span id=\"sl-clock-manager-hfxo-notify-consecutive-failed-startups\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-hfxo-notify-consecutive-failed-startups\">#</a></span></h3><blockquote>void sl_clock_manager_hfxo_notify_consecutive_failed_startups (void )</blockquote><p style=\"color:inherit\">When this callback function is called, it means that HFXO failed twice in a row to start with normal configurations. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\">This may mean that there is a bad crystal. When getting this callback, HFXO is running but its properties (frequency, precision) are not guaranteed. This should be considered as an error situation.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">This callback will be called only when the SL_CLOCK_MANAGER_HFXO_SLEEPY_CRYSTAL_SUPPORT config is enabled </p></li></ul><br><div>Definition at line <code>625</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager.h</code></div><br></div><div><h3>sl_clock_manager_init<span id=\"sl-clock-manager-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-clock-manager-init\">#</a></span></h3><blockquote>sl_status_t sl_clock_manager_init (void )</blockquote><p style=\"color:inherit\">Initializes Oscillators and Clock branches. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\"></td><td></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status code. SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>54</code> of file <code>platform/service/clock_manager/inc/sl_clock_manager_init.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/gecko-platform/5.0.2/platform-service/clock-manager", "status": "success"}