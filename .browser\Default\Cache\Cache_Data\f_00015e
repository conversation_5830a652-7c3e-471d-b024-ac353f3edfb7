{"availableCount": 173, "searchTerms": [], "userState": "6339fe56-972e-4527-bd6b-057de5f70f04", "resources": [{"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_ncp/readme.md"], "description": "Network Co-Processor (NCP) target application. Runs the Bluetooth stack dynamically and provides access to it via Bluetooth API (BGAPI) using UART connection. NCP mode makes it possible to run your application on a host controller or PC.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth - NCP_asset:..com.silabs.sdk.stack.super_4.4.4.app.bluetooth.demos.bt_ncp.bt_ncp-brd4314a.s37", "text": "Bluetooth - NCP", "priority": 0, "category": "DEMOS", "toolTipText": "Network Co-Processor (NCP) target application. Runs the Bluetooth stack dynamically and provides access to it via Bluetooth API (BGAPI) using UART connection. NCP mode makes it possible to run your application on a host controller or PC.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_ncp/readme.md"], "description": "Network Co-Processor (NCP) target application. Runs the Bluetooth stack dynamically and provides access to it via Bluetooth API (BGAPI) using UART connection. NCP mode makes it possible to run your application on a host controller or PC.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_ncp.example/bt_ncp/bt_ncp.slcp", "text": "Bluetooth - NCP", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Network Co-Processor (NCP) target application. Runs the Bluetooth stack dynamically and provides access to it via Bluetooth API (BGAPI) using UART connection. NCP mode makes it possible to run your application on a host controller or PC.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_ncp/readme.md"], "description": "Network Co-Processor (NCP) target application with additional features to support the Electronic Shelf Label Profile ESL Access Point role. Note: Some BLE features unused by the ESL Access Point are removed compared to the NCP target application.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_ncp_esl_ap.example/bt_ncp/bt_ncp_esl_ap.slcp", "text": "Bluetooth - NCP ESL Access Point", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Network Co-Processor (NCP) target application with additional features to support the Electronic Shelf Label Profile ESL Access Point role. Note: Some BLE features unused by the ESL Access Point are removed compared to the NCP target application.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_rcp/readme.md"], "description": "Radio Co-Processor (RCP) target application. Runs the Bluetooth Controller (i.e. the Link Layer only) and provides access to it using the standard HCI (Host-Controller Interface) over a UART connection.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_rcp.example/bt_rcp/bt_rcp.slcp", "text": "Bluetooth - RCP", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Radio Co-Processor (RCP) target application. Runs the Bluetooth Controller (i.e. the Link Layer only) and provides access to it using the standard HCI (Host-Controller Interface) over a UART connection.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_rcp/readme_rcp_cpc.md"], "description": "Radio Co-Processor (RCP) target application. Runs the Bluetooth Controller (i.e. the Link Layer only) and provides access to it using the standard HCI (Host-Controller Interface) over CPC (Co-Processor Communication) protocol through UART connection.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_rcp_cpc.example/bt_rcp/bt_rcp_cpc.slcp", "text": "Bluetooth - RCP CPC", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Radio Co-Processor (RCP) target application. Runs the Bluetooth Controller (i.e. the Link Layer only) and provides access to it using the standard HCI (Host-Controller Interface) over CPC (Co-Processor Communication) protocol through UART connection.\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_blinky/readme.md"], "description": "The classic blinky example using Bluetooth communication. Demonstrates a simple two-way data exchange over GATT. This can be tested with the EFR Connect mobile app.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth - SoC Blinky_asset:..com.silabs.sdk.stack.super_4.4.4.app.bluetooth.demos.bt_soc_blinky.bt_soc_blinky-brd4314a.s37", "text": "Bluetooth - SoC Blinky", "priority": 0, "category": "DEMOS", "toolTipText": "The classic blinky example using Bluetooth communication. Demonstrates a simple two-way data exchange over GATT. This can be tested with the EFR Connect mobile app.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_blinky/readme.md"], "description": "The classic blinky example using Bluetooth communication. Demonstrates a simple two-way data exchange over GATT. This can be tested with the EFR Connect mobile app.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_soc_blinky.example/bt_soc_blinky/bt_soc_blinky.slcp", "text": "Bluetooth - SoC Blinky", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The classic blinky example using Bluetooth communication. Demonstrates a simple two-way data exchange over GATT. This can be tested with the EFR Connect mobile app.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_empty/readme.md"], "description": "A minimal project structure, that serves as a starting point for custom Bluetooth applications. The application starts advertising after boot and restarts advertising after a connection is closed.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_soc_empty.example/bt_soc_empty/bt_soc_empty.slcp", "text": "Bluetooth - SoC Empty", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "A minimal project structure, that serves as a starting point for custom Bluetooth applications. The application starts advertising after boot and restarts advertising after a connection is closed.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_iop_test/readme.md"], "description": "This is a test procedure containing several test cases for Bluetooth Low Energy communication. This demo is meant to be used with the EFR Connect mobile app, through the \"Interoperability Test\" tile on the Develop view of the app.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_soc_iop_test_log.example/bt_soc_iop_test/bt_soc_iop_test_log.slcp", "text": "Bluetooth - SoC Interoperability Test", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a test procedure containing several test cases for Bluetooth Low Energy communication. This demo is meant to be used with the EFR Connect mobile app, through the \"Interoperability Test\" tile on the Develop view of the app.\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_thermometer/readme.md"], "description": "Implements a GATT Server with the Health Thermometer Profile, which enables a Client device to connect and get temperature data. Temperature is read from the mock relative humidity and temperature sensor.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth - SoC Thermometer (Mock)_asset:..com.silabs.sdk.stack.super_4.4.4.app.bluetooth.demos.bt_soc_thermometer_mock.bt_soc_thermometer_mock-brd4314a.s37", "text": "Bluetooth - SoC Thermometer (Mock)", "priority": 0, "category": "DEMOS", "toolTipText": "Implements a GATT Server with the Health Thermometer Profile, which enables a Client device to connect and get temperature data. Temperature is read from the mock relative humidity and temperature sensor.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_thermometer/readme.md"], "description": "Implements a GATT Server with the Health Thermometer Profile, which enables a Client device to connect and get temperature data. Temperature is read from the mock relative humidity and temperature sensor.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_soc_thermometer_mock.example/bt_soc_thermometer/bt_soc_thermometer_mock.slcp", "text": "Bluetooth - SoC Thermometer (Mock)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Implements a GATT Server with the Health Thermometer Profile, which enables a Client device to connect and get temperature data. Temperature is read from the mock relative humidity and temperature sensor.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_thermometer/readme_rtos.md"], "description": "Demonstrates the integration of FreeRTOS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer (Mock) sample app.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_soc_thermometer_freertos_mock.example/bt_soc_thermometer/bt_soc_thermometer_freertos_mock.slcp", "text": "Bluetooth - SoC Thermometer (Mock) FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates the integration of FreeRTOS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer (Mock) sample app.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_thermometer/readme_rtos.md"], "description": "Demonstrates the integration of Micrium OS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer (Mock) sample app.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_soc_thermometer_micriumos_mock.example/bt_soc_thermometer/bt_soc_thermometer_micriumos_mock.slcp", "text": "Bluetooth - SoC Thermometer (Mock) Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates the integration of Micrium OS into Bluetooth applications. RTOS is added to the Bluetooth - SoC Thermometer (Mock) sample app.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_thermometer_client/readme.md"], "description": "Implements a GATT Client that discovers and connects with up to 4 BLE devices advertising themselves as Thermometer Servers. It displays the discovery process and the temperature values received via UART.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_soc_thermometer_client.example/bt_soc_thermometer_client/bt_soc_thermometer_client.slcp", "text": "Bluetooth - SoC Thermometer Client", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Implements a GATT Client that discovers and connects with up to 4 BLE devices advertising themselves as Thermometer Servers. It displays the discovery process and the temperature values received via UART.\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_throughput/readme.md"], "description": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using EFR Connect mobile app, through the Throughput demo tile.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth - SoC Throughput (single button)_asset:..com.silabs.sdk.stack.super_4.4.4.app.bluetooth.demos.bt_soc_throughput_log_single.bt_soc_throughput_log_single-brd4314a.s37", "text": "Bluetooth - SoC Throughput (single button)", "priority": 0, "category": "DEMOS", "toolTipText": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using EFR Connect mobile app, through the Throughput demo tile.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_throughput/readme.md"], "description": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using EFR Connect mobile app, through the Throughput demo tile.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_soc_throughput_log_single.example/bt_soc_throughput/bt_soc_throughput_log_single.slcp", "text": "Bluetooth - SoC Throughput (single button)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example tests the throughput capabilities of the device and can be used to measure throughput between 2 *EFR32* devices, as well as between a device and a smartphone using EFR Connect mobile app, through the Throughput demo tile.\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_ibeacon/readme.md"], "description": "Sends non-connectable advertisements in iBeacon format. The iBeacon Service gives Bluetooth accessories a simple and convenient way to send iBeacons to smartphones. This example can be tested together with the EFR Connect mobile app.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth - SoC iBeacon_asset:..com.silabs.sdk.stack.super_4.4.4.app.bluetooth.demos.bt_soc_ibeacon.bt_soc_ibeacon-brd4314a.s37", "text": "Bluetooth - SoC iBeacon", "priority": 0, "category": "DEMOS", "toolTipText": "Sends non-connectable advertisements in iBeacon format. The iBeacon Service gives Bluetooth accessories a simple and convenient way to send iBeacons to smartphones. This example can be tested together with the EFR Connect mobile app.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_ibeacon/readme.md"], "description": "Sends non-connectable advertisements in iBeacon format. The iBeacon Service gives Bluetooth accessories a simple and convenient way to send iBeacons to smartphones. This example can be tested together with the EFR Connect mobile app.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_soc_ibeacon.example/bt_soc_ibeacon/bt_soc_ibeacon.slcp", "text": "Bluetooth - SoC iBeacon", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Sends non-connectable advertisements in iBeacon format. The iBeacon Service gives Bluetooth accessories a simple and convenient way to send iBeacons to smartphones. This example can be tested together with the EFR Connect mobile app.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_aoa_soc_asset_tag/readme.md"], "description": "This sample app demonstrates a CTE (Constant Tone Extension) transmitter that can be used as an asset tag in a Direction Finding setup estimating Angle of Arrival (AoA).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_aoa_soc_asset_tag.example/bt_aoa_soc_asset_tag/bt_aoa_soc_asset_tag.slcp", "text": "Bluetooth AoA - SoC Asset Tag", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample app demonstrates a CTE (Constant Tone Extension) transmitter that can be used as an asset tag in a Direction Finding setup estimating Angle of Arrival (AoA).\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": [], "description": "Network Co-Processor (NCP) target application extended with CTE Receiver support. It enables Angle of Departure (AoD) calculation. Use this application with Direction Finding Studio tools.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_aod_ncp_receiver.example/bt_aod_ncp_receiver/bt_aod_ncp_receiver.slcp", "text": "Bluetooth AoD - NCP Receiver", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Network Co-Processor (NCP) target application extended with CTE Receiver support. It enables Angle of Departure (AoD) calculation. Use this application with Direction Finding Studio tools.\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_ncp_empty/readme.md"], "description": "Demonstrates the bare minimum needed for an NCP Target C application. This example is recommended for EFR32xG22, which has limited RAM and flash, and therefore some of the stack classes are disabled by default.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth Mesh - NCP Empty_asset:..com.silabs.sdk.stack.super_4.4.4.app.btmesh.demos.btmesh_ncp_empty.btmesh_ncp_empty-brd4314a.s37", "text": "Bluetooth Mesh - NCP Empty", "priority": 0, "category": "DEMOS", "toolTipText": "Demonstrates the bare minimum needed for an NCP Target C application.  This example is recommended for EFR32xG22, which has limited RAM and flash, and therefore some of the stack classes are disabled by default.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_ncp_empty/readme.md"], "description": "Demonstrates the bare minimum needed for an NCP Target C application.  This example is recommended for EFR32xG22, which has limited RAM and flash, and therefore some of the stack classes are disabled by default.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.btmesh_ncp_empty.example/btmesh_ncp_empty/btmesh_ncp_empty_xg22.slcp", "text": "Bluetooth Mesh - NCP Empty", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates the bare minimum needed for an NCP Target C application.  This example is recommended for EFR32xG22, which has limited RAM and flash, and therefore some of the stack classes are disabled by default.\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_sensor_ambient_light/readme_xg22.md"], "description": "An out-of-the-box Software Demo where the device acts as an ambient light sensor in a Networked Lighting Control (NLC) system. The device simulates ambient light measurements and sends these to the network. Properly configured NLC Basic Lightness Controllers then can act on the received data.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth Mesh - NLC Ambient Light Sensor (Mock)_asset:..com.silabs.sdk.stack.super_4.4.4.app.btmesh.demos.btmesh_soc_nlc_sensor_ambient_light.btmesh_soc_nlc_sensor_ambient_light-brd4314a.s37", "text": "Bluetooth Mesh - NLC Ambient Light Sensor (Mock)", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as an ambient light sensor in a Networked Lighting Control (NLC) system. The device simulates ambient light measurements and sends these to the network. Properly configured NLC Basic Lightness Controllers then can act on the received data.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_sensor_ambient_light/readme_xg22.md"], "description": "An out-of-the-box Software Demo where the device acts as an ambient light sensor in a Networked Lighting Control (NLC) system. The device simulates ambient light measurements and sends these to the network. Properly configured NLC Basic Lightness Controllers then can act on the received data.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.btmesh_soc_nlc_sensor_ambient_light.example/btmesh_soc_nlc_sensor_ambient_light/btmesh_soc_nlc_sensor_ambient_light_mock_log_xg22.slcp", "text": "Bluetooth Mesh - NLC Ambient Light Sensor (Mock)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as an ambient light sensor in a Networked Lighting Control (NLC) system. The device simulates ambient light measurements and sends these to the network. Properly configured NLC Basic Lightness Controllers then can act on the received data.\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_basic_scene_selector/readme_xg22.md"], "description": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth Mesh - NLC Basic Scene Selector_asset:..com.silabs.sdk.stack.super_4.4.4.app.btmesh.demos.btmesh_soc_nlc_basic_scene_selector.btmesh_soc_nlc_basic_scene_selector-brd4314a.s37", "text": "Bluetooth Mesh - NLC Basic Scene Selector", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_basic_scene_selector/readme_xg22.md"], "description": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.btmesh_soc_nlc_basic_scene_selector.example/btmesh_soc_nlc_basic_scene_selector/btmesh_soc_nlc_basic_scene_selector_log_xg22.slcp", "text": "Bluetooth Mesh - NLC Basic Scene Selector", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_basic_scene_selector/readme_low_power.md"], "description": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth Mesh - NLC Basic Scene Selector Low Power_asset:..com.silabs.sdk.stack.super_4.4.4.app.btmesh.demos.btmesh_soc_nlc_basic_scene_selector_low_power.btmesh_soc_nlc_basic_scene_selector_low_power-brd4314a.s37", "text": "Bluetooth Mesh - NLC Basic Scene Selector Low Power", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_basic_scene_selector/readme_low_power.md"], "description": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.btmesh_soc_nlc_basic_scene_selector_low_power.example/btmesh_soc_nlc_basic_scene_selector/btmesh_soc_nlc_basic_scene_selector_low_power.slcp", "text": "Bluetooth Mesh - NLC Basic Scene Selector Low Power", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Basic Scene Selector in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by scene recall requests.\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_dimming_control/readme_xg22.md"], "description": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. <PERSON><PERSON> But<PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth Mesh - NLC Dimming Control_asset:..com.silabs.sdk.stack.super_4.4.4.app.btmesh.demos.btmesh_soc_nlc_dimming_control.btmesh_soc_nlc_dimming_control-brd4314a.s37", "text": "Bluetooth Mesh - NLC Dimming Control", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. <PERSON><PERSON> But<PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_dimming_control/readme_xg22.md"], "description": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. <PERSON><PERSON> But<PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.btmesh_soc_nlc_dimming_control.example/btmesh_soc_nlc_dimming_control/btmesh_soc_nlc_dimming_control_log_xg22.slcp", "text": "Bluetooth Mesh - NLC Dimming Control", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. <PERSON><PERSON> But<PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_dimming_control/readme_low_power.md"], "description": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth Mesh - NLC Dimming Control Low Power_asset:..com.silabs.sdk.stack.super_4.4.4.app.btmesh.demos.btmesh_soc_nlc_dimming_control_low_power.btmesh_soc_nlc_dimming_control_low_power-brd4314a.s37", "text": "Bluetooth Mesh - NLC Dimming Control Low Power", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_dimming_control/readme_low_power.md"], "description": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.btmesh_soc_nlc_dimming_control_low_power.example/btmesh_soc_nlc_dimming_control/btmesh_soc_nlc_dimming_control_low_power.slcp", "text": "Bluetooth Mesh - NLC Dimming Control Low Power", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as a Dimming Control in a Networked Lighting Control (NLC) system. It is optimized for low current consumption with disabled CLI, logging, and LCD. <PERSON><PERSON><PERSON> presses control Basic Lightness Controllers in the network by Generic Level Delta or Generic On/Off messages.\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_sensor_occupancy/readme.md"], "description": "An out-of-the-box Software Demo where the device acts as an Occupancy Sensor in a Networked Lighting Control (NLC) system. <PERSON><PERSON> Button presses imitate people count changes which can control a properly configured NLC Basic Lightness Controller.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth Mesh - NLC Occupancy Sensor_asset:..com.silabs.sdk.stack.super_4.4.4.app.btmesh.demos.btmesh_soc_nlc_sensor_occupancy.btmesh_soc_nlc_sensor_occupancy-brd4314a.s37", "text": "Bluetooth Mesh - NLC Occupancy Sensor", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as an Occupancy Sensor in a Networked Lighting Control (NLC) system. <PERSON><PERSON> Button presses imitate people count changes which can control a properly configured NLC Basic Lightness Controller.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_nlc_sensor_occupancy/readme.md"], "description": "An out-of-the-box Software Demo where the device acts as an Occupancy Sensor in a Networked Lighting Control (NLC) system. <PERSON><PERSON> Button presses imitate people count changes which can control a properly configured NLC Basic Lightness Controller.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.btmesh_soc_nlc_sensor_occupancy.example/btmesh_soc_nlc_sensor_occupancy/btmesh_soc_nlc_sensor_occupancy_mock_log.slcp", "text": "Bluetooth Mesh - NLC Occupancy Sensor", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as an Occupancy Sensor in a Networked Lighting Control (NLC) system. <PERSON><PERSON> Button presses imitate people count changes which can control a properly configured NLC Basic Lightness Controller.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_empty/readme.md"], "description": "Demonstrates the bare minimum needed for a Bluetooth Mesh C application. The application starts Unprovisioned Device Beaconing after booting, and then waits to be provisioned.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.btmesh_soc_empty.example/btmesh_soc_empty/btmesh_soc_empty.slcp", "text": "Bluetooth Mesh - SoC Empty", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates the bare minimum needed for a Bluetooth Mesh C application. The application starts Unprovisioned Device Beaconing after booting, and then waits to be provisioned.\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_sensor_client/readme.md"], "description": "This example demonstrates the Bluetooth Mesh Sensor Client Model. It collects and displays sensor measurement data from remote device(s) (e.g., btmesh_soc_sensor_server).", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth Mesh - SoC Sensor Client_asset:..com.silabs.sdk.stack.super_4.4.4.app.btmesh.demos.btmesh_soc_sensor_client.btmesh_soc_sensor_client-brd4314a.s37", "text": "Bluetooth Mesh - SoC Sensor Client", "priority": 0, "category": "DEMOS", "toolTipText": "This example demonstrates the Bluetooth Mesh Sensor Client Model. It collects and displays sensor measurement data from remote device(s) (e.g., btmesh_soc_sensor_server).\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_sensor_client/readme.md"], "description": "This example demonstrates the Bluetooth Mesh Sensor Client Model. It collects and displays sensor measurement data from remote device(s) (e.g., btmesh_soc_sensor_server).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.btmesh_soc_sensor_client.example/btmesh_soc_sensor_client/btmesh_soc_sensor_client_log_xg22.slcp", "text": "Bluetooth Mesh - SoC Sensor Client", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example demonstrates the Bluetooth Mesh Sensor Client Model. It collects and displays sensor measurement data from remote device(s) (e.g., btmesh_soc_sensor_server).\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_sensor_server/readme.md"], "description": "This example demonstrates the Bluetooth Mesh Sensor Server Model and Sensor Setup Server Model. If available, it measures CPU temperature and uses that data as temperature reading, otherwise it sends mocked temperature data to a remote device (e.g., btmesh_soc_sensor_client).", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth Mesh - SoC Sensor Thermometer_asset:..com.silabs.sdk.stack.super_4.4.4.app.btmesh.demos.btmesh_soc_sensor_thermometer.btmesh_soc_sensor_thermometer-brd4314a.s37", "text": "Bluetooth Mesh - SoC Sensor Thermometer", "priority": 0, "category": "DEMOS", "toolTipText": "This example demonstrates the Bluetooth Mesh Sensor Server Model and Sensor Setup Server Model. If available, it measures CPU temperature and uses that data as temperature reading, otherwise it sends mocked temperature data to a remote device (e.g., btmesh_soc_sensor_client).\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_sensor_server/readme.md"], "description": "This example demonstrates the Bluetooth Mesh Sensor Server Model and Sensor Setup Server Model. If available, it measures CPU temperature and uses that data as temperature reading, otherwise it sends mocked temperature data to a remote device (e.g., btmesh_soc_sensor_client).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.btmesh_soc_sensor_thermometer.example/btmesh_soc_sensor_thermometer/btmesh_soc_sensor_thermometer_mock_log.slcp", "text": "Bluetooth Mesh - SoC Sensor Thermometer", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example demonstrates the Bluetooth Mesh Sensor Server Model and Sensor Setup Server Model. If available, it measures CPU temperature and uses that data as temperature reading, otherwise it sends mocked temperature data to a remote device (e.g., btmesh_soc_sensor_client).\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_switch/readme.md"], "description": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device. Note - this example is not compatible with the Dimming Control NLC Profile.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth Mesh - SoC Switch CTL_asset:..com.silabs.sdk.stack.super_4.4.4.app.btmesh.demos.btmesh_soc_switch_ctl.btmesh_soc_switch_ctl-brd4314a.s37", "text": "Bluetooth Mesh - SoC Switch CTL", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device.\nNote - this example is not compatible with the Dimming Control NLC Profile.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_switch/readme.md"], "description": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device. Note - this example is not compatible with the Dimming Control NLC Profile.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.btmesh_soc_switch_ctl.example/btmesh_soc_switch_ctl/btmesh_soc_switch_ctl_log_xg22.slcp", "text": "Bluetooth Mesh - SoC Switch CTL", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device.\nNote - this example is not compatible with the Dimming Control NLC Profile.\n"}, {"imageURL": "bundleentry://52.fwk332573486/icons/run.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_switch/readme_low_power.md"], "description": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. It is optimized for low current consumption with disabled CLI, logging, and LCD. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device. Note - this example is not compatible with the Dimming Control NLC Profile.", "id": "com.silabs.sdk.stack.super:4.4.4._-85640013_Bluetooth Mesh - SoC Switch CTL Low Power_asset:..com.silabs.sdk.stack.super_4.4.4.app.btmesh.demos.btmesh_soc_switch_ctl_low_power.btmesh_soc_switch_ctl_low_power-brd4314a.s37", "text": "Bluetooth Mesh - SoC Switch CTL Low Power", "priority": 0, "category": "DEMOS", "toolTipText": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. It is optimized for low current consumption with disabled CLI, logging, and LCD. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device.\nNote - this example is not compatible with the Dimming Control NLC Profile.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/btmesh/documentation/example/btmesh_soc_switch/readme_low_power.md"], "description": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. It is optimized for low current consumption with disabled CLI, logging, and LCD. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device. Note - this example is not compatible with the Dimming Control NLC Profile.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.btmesh_soc_switch_ctl_low_power.example/btmesh_soc_switch_ctl/btmesh_soc_switch_ctl_low_power.slcp", "text": "Bluetooth Mesh - SoC Switch CTL Low Power", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "An out-of-the-box Software Demo where the device acts as a switch using the Light CTL Client Model. It is optimized for low current consumption with disabled CLI, logging, and LCD. Push Button presses or CLI commands can control the lightness and color temperature of the LEDs on a remote device.\nNote - this example is not compatible with the Dimming Control NLC Profile.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-uart-bgapi/readme.md"], "description": "Standalone Bootloader using the BGAPI protocol for UART DFU. This is the recommended UART bootloader for the BLE protocol stack.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bootloader-uart-bgapi.sample-apps/Series-2/bootloader-uart-bgapi/bootloader-uart-bgapi.slcp", "text": "Bootloader - NCP BGAPI UART DFU", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Standalone Bootloader using the BGAPI protocol for UART DFU. This is the recommended UART bootloader for the BLE protocol stack.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-uart-xmodem/readme.md"], "description": "Standalone Bootloader using XMODEM-CRC over UART. The bootloader shows a menu, where an XMODEM transfer can be started by sending ASCII '1', or the application can be started by sending ASCII '2'. This is the recommended UART bootloader for the EmberZNet and Connect protocol stacks.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bootloader-uart-xmodem.sample-apps/Series-2/bootloader-uart-xmodem/bootloader-uart-xmodem.slcp", "text": "Bootloader - NCP UART XMODEM", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Standalone Bootloader using XMODEM-CRC over UART. The bootloader shows a menu, where an XMODEM transfer can be started by sending ASCII '1', or the application can be started by sending ASCII '2'. This is the recommended UART bootloader for the EmberZNet and Connect protocol stacks.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-apploader/readme.md"], "description": "Standalone Bootloader using the Bluetooth AppLoader OTA DFU. This implements in-place application updates using Bluetooth connection.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bootloader-apploader.sample-apps/Series-2/bootloader-apploader/bootloader-apploader.slcp", "text": "Bootloader - SoC Bluetooth AppLoader OTA DFU", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Standalone Bootloader using the Bluetooth AppLoader OTA DFU. This implements in-place application updates using Bluetooth connection.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-storage-internal-single-352k/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x28000 (or 0x8028000 for device with 0x8000000 flash base), and have a size of 120 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bootloader-storage-internal-single-352k.sample-apps/Series-2/bootloader-storage-internal-single-352k/bootloader-storage-internal-single-352k.slcp", "text": "Bootloader - SoC Internal Storage (single image on 352kB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x28000 (or 0x8028000 for device with 0x8000000 flash base), and have a size of 120 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/Series-2/bootloader-storage-internal-single-512k/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x44000 (or 0x8044000 for device with 0x8000000 flash base), and have a size of 192 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bootloader-storage-internal-single-512k.sample-apps/Series-2/bootloader-storage-internal-single-512k/bootloader-storage-internal-single-512k.slcp", "text": "Bootloader - SoC Internal Storage (single image on 512kB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x44000 (or 0x8044000 for device with 0x8000000 flash base), and have a size of 192 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": [], "description": "This example project shows an empty configuration that can be used as a starting point to add components and functionality.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.cExeProject", "text": "Empty C Project", "priority": 1, "category": "SOFTWARE", "toolTipText": "This example project shows an empty configuration that can be used as a starting point to add components and functionality."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": [], "description": "This example project shows an empty configuration that can be used as a starting point to add components and functionality.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.cppExeProject", "text": "Empty C++ Project", "priority": 1, "category": "SOFTWARE", "toolTipText": "This example project shows an empty configuration that can be used as a starting point to add components and functionality."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/ml_blink/readme.md"], "description": "This application demonstrates a model trained to replicate a sine function.  The model is continuously fed with values ranging from 0 to 2pi, and the  output of the model is used to control the intensity of an LED.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.ml_blink.example/ml_blink/ml_blink.slcp", "text": "Machine Learning - Blink", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This application demonstrates a model trained to replicate a sine function.  The model is continuously fed with values ranging from 0 to 2pi, and the  output of the model is used to control the intensity of an LED. \n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/blink_baremetal/readme.md"], "description": "This example project shows how to blink an LED in a bare-metal configuration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.blink_baremetal.example/blink_baremetal/blink_baremetal.slcp", "text": "Platform - Blink Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to blink an LED in a bare-metal configuration.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/blink_kernel_freertos/readme.md"], "description": "This example project shows how to blink an LED using a FreeRTOS kernel task. The blink task can be created using either dynamic or static memory allocation for the task stack and tcb.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.blink_kernel_freertos.example/blink_kernel_freertos/blink_kernel_freertos.slcp", "text": "Platform - Blink Kernel FreeRTOS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to blink an LED using a FreeRTOS kernel task. The blink task can be created using either dynamic or static memory allocation for the task stack and tcb.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/blink_kernel_micriumos/readme.md"], "description": "This example project shows how to blink an LED using a Micrium OS kernel task.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.blink_kernel_micriumos.example/blink_kernel_micriumos/blink_kernel_micriumos.slcp", "text": "Platform - Blink Kernel Micrium OS", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows how to blink an LED using a Micrium OS kernel task.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/blink_pwm_baremetal/readme.md"], "description": "This example project uses the PWM driver that uses a TIMER to gradually adjust the intensity of an LED up and down.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.blink_pwm_baremetal.example/blink_pwm_baremetal/blink_pwm_baremetal.slcp", "text": "Platform - Blink PWM", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the PWM driver that uses a TIMER to gradually adjust the intensity of an LED up and down.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/iostream_eusart_baremetal/readme.md"], "description": "This example project uses the I/O Stream service running in a bare-metal configuration  to demonstrate the use of EUSART communication over the virtual COM port (VCOM). The  application will echo back any characters it receives over the serial connection. The  VCOM serial port can be used either over USB or by connecting to port 4902 if the kit  is connected via Ethernet.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.iostream_eusart_baremetal.example/iostream_eusart_baremetal/iostream_eusart_baremetal.slcp", "text": "Platform - I/O Stream EUSART Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the I/O Stream service running in a bare-metal configuration  to demonstrate the use of EUSART communication over the virtual COM port (VCOM). The  application will echo back any characters it receives over the serial connection. The  VCOM serial port can be used either over USB or by connecting to port 4902 if the kit  is connected via Ethernet. \n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/iostream_eusart_kernel_micriumos/readme.md"], "description": "This example project uses the I/O Stream service running in a Micrium OS kernel task to demonstrate the use of EUSART communication over the virtual COM port (VCOM). The application will echo back any characters it receives over the serial connection. The VCOM serial port can be used either over USB or by connecting to port 4902 if the kit is connected via Ethernet.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.iostream_eusart_kernel_micriumos.example/iostream_eusart_kernel_micriumos/iostream_eusart_kernel_micriumos.slcp", "text": "Platform - I/O Stream EUSART on Micrium OS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the I/O Stream service running in a Micrium OS kernel task to demonstrate the use of EUSART communication over the virtual COM port (VCOM). The application will echo back any characters it receives over the serial connection. The VCOM serial port can be used either over USB or by connecting to port 4902 if the kit is connected via Ethernet. \n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/iostream_usart_baremetal/readme.md"], "description": "This example project uses the I/O Stream service running in a bare-metal configuration  to demonstrate the use of UART communication over the virtual COM port (VCOM). The  application will echo back any characters it receives over the serial connection. The  VCOM serial port can be used either over USB or by connecting to port 4902 if the kit  is connected via Ethernet.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.iostream_usart_baremetal.example/iostream_usart_baremetal/iostream_usart_baremetal.slcp", "text": "Platform - I/O Stream USART Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the I/O Stream service running in a bare-metal configuration  to demonstrate the use of UART communication over the virtual COM port (VCOM). The  application will echo back any characters it receives over the serial connection. The  VCOM serial port can be used either over USB or by connecting to port 4902 if the kit  is connected via Ethernet. \n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/iostream_usart_kernel_micriumos/readme.md"], "description": "This example project uses the I/O Stream service running in a Micrium OS kernel task to demonstrate the use of UART communication over the virtual COM port (VCOM). The application will echo back any characters it receives over the serial connection. The VCOM serial port can be used either over USB or by connecting to port 4902 if the kit is connected via Ethernet.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.iostream_usart_kernel_micriumos.example/iostream_usart_kernel_micriumos/iostream_usart_kernel_micriumos.slcp", "text": "Platform - I/O Stream USART on Micrium OS kernel", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses the I/O Stream service running in a Micrium OS kernel task to demonstrate the use of UART communication over the virtual COM port (VCOM). The application will echo back any characters it receives over the serial connection. The VCOM serial port can be used either over USB or by connecting to port 4902 if the kit is connected via Ethernet. \n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/mpu_simple_baremetal/readme.md"], "description": "This example project demonstrates the use of the Simple MPU module. Its purpose is to block the execution of code from RAM in order to prevent code injection attacks. In this example,  some fake malicious executable code is copied to RAM and executed with the MPU disabled and  re-enabled to demonstrate its effectiveness.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mpu_simple_baremetal.example/mpu_simple_baremetal/mpu_simple_baremetal.slcp", "text": "Platform - MPU Simple", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the use of the Simple MPU module. Its purpose is to block the execution of code from RAM in order to prevent code injection attacks. In this example,  some fake malicious executable code is copied to RAM and executed with the MPU disabled and  re-enabled to demonstrate its effectiveness. \n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/nvm3_baremetal/readme.md"], "description": "This example project demonstrates use of the NVM3 interface. Using the command line interface, the user can write, read and delete NVM3 data objects through the serial connection. The number of writes and deletes are tracked in counter objects.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.nvm3_baremetal.example/nvm3_baremetal/nvm3_baremetal.slcp", "text": "Platform - NVM3 Bare-metal", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates use of the NVM3 interface. Using the command line interface, the user can write, read and delete NVM3 data objects through the serial connection. The number of writes and deletes are tracked in counter objects.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/sleeptimer_wallclock_baremetal/readme.md"], "description": "This example project demonstrates the wallclock interface of the sleeptimer service. The user can get and set the date and time in different formats through the VCOM serial port.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sleeptimer_wallclock_baremetal.example/sleeptimer_wallclock_baremetal/sleeptimer_wallclock_baremetal.slcp", "text": "Platform - <PERSON><PERSON><PERSON>", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the wallclock interface of the sleeptimer service. The user can get and set the date and time in different formats through the VCOM serial port.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_aead/readme.md"], "description": "This example uses the PSA Crypto API to perform Authenticated Encryption with Associated Data (AEAD) operations on the supported device.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.psa_crypto_aead.example/psa_crypto_aead/psa_crypto_aead.slcp", "text": "Platform Security - SoC PSA Crypto AEAD", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example uses the PSA Crypto API to perform Authenticated Encryption with Associated Data (AEAD) operations on the supported device.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_asymmetric_key/readme.md"], "description": "This example uses the PSA Crypto API to perform asymmetric key operations on the supported device.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.psa_crypto_asymmetric_key.example/psa_crypto_asymmetric_key/psa_crypto_asymmetric_key.slcp", "text": "Platform Security - SoC PSA Crypto Asymmetric Key", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example uses the PSA Crypto API to perform asymmetric key operations on the supported device.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_cipher/readme.md"], "description": "This example project demonstrates the unauthenticated cipher API for generic and built-in AES-128 keys.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.psa_crypto_cipher.example/psa_crypto_cipher/psa_crypto_cipher.slcp", "text": "Platform Security - SoC PSA Crypto Cipher", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the unauthenticated cipher API for generic and built-in AES-128 keys.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_dsa/readme.md"], "description": "This example project demonstrates the ECDSA and EdDSA digital signature API for generic and built-in ECC keys.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.psa_crypto_dsa.example/psa_crypto_dsa/psa_crypto_dsa.slcp", "text": "Platform Security - SoC PSA Crypto DSA", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the ECDSA and EdDSA digital signature API for generic and built-in ECC keys.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_ecdh/readme.md"], "description": "This example project demonstrates the ECDH key agreement API.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.psa_crypto_ecdh.example/psa_crypto_ecdh/psa_crypto_ecdh.slcp", "text": "Platform Security - SoC PSA Crypto ECDH", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the ECDH key agreement API.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_hash/readme.md"], "description": "This example project demonstrates the hash API.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.psa_crypto_hash.example/psa_crypto_hash/psa_crypto_hash.slcp", "text": "Platform Security - SoC PSA Crypto Hash", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the hash API.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_kdf/readme.md"], "description": "This example project demonstrates the Key Derivation Function (KDF) API.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.psa_crypto_kdf.example/psa_crypto_kdf/psa_crypto_kdf.slcp", "text": "Platform Security - SoC PSA Crypto KDF", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the Key Derivation Function (KDF) API.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_mac/readme.md"], "description": "This example project demonstrates the Message Authentication Code (MAC) API.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.psa_crypto_mac.example/psa_crypto_mac/psa_crypto_mac.slcp", "text": "Platform Security - SoC PSA Crypto MAC", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the Message Authentication Code (MAC) API.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_symmetric_key/readme.md"], "description": "This example project demonstrates the symmetric key API.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.psa_crypto_symmetric_key.example/psa_crypto_symmetric_key/psa_crypto_symmetric_key.slcp", "text": "Platform Security - SoC PSA Crypto Symmetric Key", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the symmetric key API.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/psa_crypto_x509/readme.md"], "description": "This example project uses opaque ECDSA keys to implement the X.509 standard for certificates in Mbed TLS.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.psa_crypto_x509.example/psa_crypto_x509/psa_crypto_x509.slcp", "text": "Platform Security - SoC PSA Crypto X.509", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project uses opaque ECDSA keys to implement the X.509 standard for certificates in Mbed TLS.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_key_provisioning/readme.md"], "description": "This example project demonstrates the key provisioning API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.se_manager_key_provisioning.example/se_manager_key_provisioning/se_manager_key_provisioning.slcp", "text": "Platform Security - SoC SE Manager Key Provisioning", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the key provisioning API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/se_manager_se_firmware_upgrade/readme.md"], "description": "This example project demonstrates the SE firmware upgrade API of SE Manager.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.se_manager_se_firmware_upgrade.example/se_manager_se_firmware_upgrade/se_manager_se_firmware_upgrade.slcp", "text": "Platform Security - SoC SE Manager SE Firmware Upgrade", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the SE firmware upgrade API of SE Manager.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/mbedtls_aescrypt/readme.md"], "description": "This example uses hardware accelerators to accelerate the AES encryption and SHA hash functions of mbedTLS.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mbedtls_aescrypt.example/mbedtls_aescrypt/mbedtls_aescrypt.slcp", "text": "Platform Security - SoC mbedTLS AES", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example uses hardware accelerators to accelerate the AES encryption and SHA hash functions of mbedTLS.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/mbedtls_ecdh/readme.md"], "description": "This example uses hardware accelerators of the supported devices to perform ECDH key generation with mbedTLS.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mbedtls_ecdh.example/mbedtls_ecdh/mbedtls_ecdh.slcp", "text": "Platform Security - SoC mbedTLS ECDH", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example uses hardware accelerators of the supported devices to perform ECDH key generation with mbedTLS.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/mbedtls_ecdsa/readme.md"], "description": "This example uses hardware accelerators on the supported device to perform ECDSA digital signature with mbedTLS.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mbedtls_ecdsa.example/mbedtls_ecdsa/mbedtls_ecdsa.slcp", "text": "Platform Security - SoC mbedTLS ECDSA", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example uses hardware accelerators on the supported device to perform ECDSA digital signature with mbedTLS.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_fingerprint2_a172mrq/README.md"], "description": "This example project shows an example for Mikroe Fingerprint 2 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_fingerprint2_a172mrq.app/example/mikroe_fingerprint2_a172mrq/mikroe_fingerprint2_a172mrq.slcp", "text": "Third Party Hardware Drivers - A-172-MRQ - Fingerprint 2 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Fingerprint 2 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_stepper_a3967/README.md"], "description": "This example project shows an example for A3967 - Stepper <PERSON>lick (Mikroe).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_stepper_a3967.app/example/mikroe_stepper_a3967/mikroe_stepper_a3967.slcp", "text": "Third Party Hardware Drivers - A3967 - <PERSON><PERSON> (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for A3967 - Stepper <PERSON>lick (Mikroe)."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_stepper2_a4988/README.md"], "description": "This example project shows an example for A4988 - Stepper 2 Click (Mikroe).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_stepper2_a4988.app/example/mikroe_stepper2_a4988/mikroe_stepper2_a4988.slcp", "text": "Third Party Hardware Drivers - A4988 - Stepper 2 Click (Mik<PERSON><PERSON>)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for A4988 - Stepper 2 Click (Mikroe)."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_rf_meter_ad8318/README.md"], "description": "This example project shows an example for Mikroe RF Meter Click driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_rf_meter_ad8318.app/example/mikroe_rf_meter_ad8318/mikroe_rf_meter_ad8318.slcp", "text": "Third Party Hardware Drivers - AD8318 - <PERSON> <PERSON><PERSON> (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe RF Meter Click driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_human_presence_ak9753/README.md"], "description": "This example project shows an example for Sparkfun Human Presence Sensor driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_human_presence_ak9753.app/example/sparkfun_human_presence_ak9753/sparkfun_human_presence_ak9753.slcp", "text": "Third Party Hardware Drivers - AK9753 - Human Presence Sensor (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Sparkfun Human Presence Sensor driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_ir_array_amg88xx/README.md"], "description": "This example project shows an example for Sparkfun Grid-EYE Infrared Array Breakout board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_ir_array_amg88xx.app/example/sparkfun_ir_array_amg88xx/sparkfun_ir_array_amg88xx.slcp", "text": "Third Party Hardware Drivers - AMG88XX - Grid-EYE Infrared Array Breakout (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Sparkfun Grid-EYE Infrared Array Breakout board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_thunder_as3935/README.md"], "description": "This example project shows an example for Thunder Click (Mikroe).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_thunder_as3935.app/example/mikroe_thunder_as3935/mikroe_thunder_as3935.slcp", "text": "Third Party Hardware Drivers - AS3935 - <PERSON>lick (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Thunder Click (Mikroe)."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_spectroscopy_as7265x/README.md"], "description": "This example project shows an example for Sparkfun Triad Spectroscopy Sensor driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_spectroscopy_as7265x.app/example/sparkfun_spectroscopy_as7265x/sparkfun_spectroscopy_as7265x.slcp", "text": "Third Party Hardware Drivers - AS7265X - Triad Spectroscopy Sensor (SparkFun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Sparkfun Triad Spectroscopy Sensor driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_lte_iot2_bg96/README.md"], "description": "This example project shows an example for Mikroe LTE IoT 2 Click driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_lte_iot2_bg96.app/example/mikroe_lte_iot2_bg96/mikroe_lte_iot2_bg96.slcp", "text": "Third Party Hardware Drivers - BG96 - LTE IoT 2 (Mik<PERSON>e)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe LTE IoT 2 Click driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_accel5_bma400/README.md"], "description": "This example project shows an example for Mikroe Accel 5 Click driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_accel5_bma400_i2c.app/example/mikroe_accel5_bma400/mikroe_accel5_bma400_i2c.slcp", "text": "Third Party Hardware Drivers - BMA400 - Accel 5 Click (Mikroe) - I2C", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Accel 5 Click driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_accel5_bma400/README.md"], "description": "This example project shows an example for Mikroe Accel 5 Click driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_accel5_bma400_spi.app/example/mikroe_accel5_bma400/mikroe_accel5_bma400_spi.slcp", "text": "Third Party Hardware Drivers - BMA400 - Accel 5 Click (Mikroe) - SPI", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Accel 5 Click driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_environmental_bme280_ccs811/README.md"], "description": "This example project shows an example for Sparkfun BME280 - CCS811 Environmental Combo board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_environmental_bme280_ccs811.app/example/sparkfun_environmental_bme280_ccs811/sparkfun_environmental_bme280_ccs811.slcp", "text": "Third Party Hardware Drivers - BME280 & CCS811 - Environmental Sensor Combo Breakout (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": " This example project shows an example for Sparkfun BME280 - CCS811 Environmental Combo board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_environment3_bme688/README.md"], "description": "This example project shows an example for BME688 - Environment 3 Click (Mikroe) - I2C integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_environment3_bme688_i2c.app/example/mikroe_environment3_bme688/mikroe_environment3_bme688_i2c.slcp", "text": "Third Party Hardware Drivers - BME688 - Environment 3 Click (Mikroe) - I2C", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for BME688 - Environment 3 Click (Mikroe) - I2C integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_environment3_bme688/README.md"], "description": "This example project shows an example for BME688 - Environment 3 Click (Mikroe) - SPI integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_environment3_bme688_spi.app/example/mikroe_environment3_bme688/mikroe_environment3_bme688_spi.slcp", "text": "Third Party Hardware Drivers - BME688 - Environment 3 Click (Mikroe) - SPI", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for BME688 - Environment 3 Click (Mikroe) - SPI integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_6dof_imu_bmi270/README.md"], "description": "This example project shows an example for BMI270 - 6DOF IMU (SparkFun).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_6dof_imu_bmi270.app/example/sparkfun_6dof_imu_bmi270/sparkfun_6dof_imu_bmi270.slcp", "text": "Third Party Hardware Drivers - BMI270 - 6DOF IMU (SparkFun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for BMI270 - 6DOF IMU (SparkFun)."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/bthome_v2/README.md"], "description": "This example project showcases how to integrate the BTHome v2 library and send BLE advertisement packets using the BTHome v2 APIs.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bthome_v2.app/example/bthome_v2/bthome_v2.slcp", "text": "Third Party Hardware Drivers - BTHome v2", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project showcases how to integrate the BTHome v2 library and send BLE advertisement packets using the BTHome v2 APIs."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/bthome_v2_server/README.md"], "description": "This example project showcases how to integrate the BTHome v2 - Server library. The BTHome v2 library provides APIs to scan and read BTHome v2 devices.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bthome_v2_server.app/example/bthome_v2_server/bthome_v2_server.slcp", "text": "Third Party Hardware Drivers - BTHome v2 - Server", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project showcases how to integrate the BTHome v2 - Server library. The BTHome v2 library provides APIs to scan and read BTHome v2 devices."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_captouch2_cap1166/README.md"], "description": "This example project shows an example for Mikroe Cap Touch 2 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_captouch2_cap1166.app/example/mikroe_captouch2_cap1166/mikroe_captouch2_cap1166.slcp", "text": "Third Party Hardware Drivers - CAP1166 - Cap Touch 2 Click (Mik<PERSON>e)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Cap Touch 2 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_buzz2_cmt_8540s_smt/README.md"], "description": "This example project shows an example for Mikroe Buzz 2 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_buzz2_cmt_8540s_smt.app/example/mikroe_buzz2_cmt_8540s_smt/mikroe_buzz2_cmt_8540s_smt.slcp", "text": "Third Party Hardware Drivers - CMT-8540S-SMT - Buzz 2 Click (<PERSON><PERSON><PERSON><PERSON>)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Buzz 2 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_pressure3_dps310/README.md"], "description": "This example project shows an example for DPS310 - Pressure 3 Click board driver integration on I2C communication.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_pressure3_dps310_i2c.app/example/mikroe_pressure3_dps310/mikroe_pressure3_dps310_i2c.slcp", "text": "Third Party Hardware Drivers - DPS310 - Pressure 3 Click (Mikroe) - I2C", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for DPS310 - Pressure 3 Click board driver integration on I2C communication."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_pressure3_dps310/README.md"], "description": "This example project shows an example for DPS310 - Pressure 3 Click board driver integration on SPI communication.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_pressure3_dps310_spi.app/example/mikroe_pressure3_dps310/mikroe_pressure3_dps310_spi.slcp", "text": "Third Party Hardware Drivers - DPS310 - Pressure 3 Click (Mikroe) - SPI", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for DPS310 - Pressure 3 Click board driver integration on SPI communication."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_rtc10_ds3231m/README.md"], "description": "This example project shows an example for Mikroe Real Time Clock Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_rtc10_ds3231m.app/example/mikroe_rtc10_ds3231m/mikroe_rtc10_ds3231m.slcp", "text": "Third Party Hardware Drivers - DS3231M - RTC 10 (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Real Time Clock Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_uwb_dwm1000/README.md"], "description": "This project shows the implementation of the radio transciever for DWM1000 - UWB Click (Mikroe)", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_uwb_dwm1000.app/example/mikroe_uwb_dwm1000/mikroe_uwb_dwm1000.slcp", "text": "Third Party Hardware Drivers - DWM1000 - UW<PERSON> Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project shows the implementation of the radio transciever for DWM1000 - UWB Click (Mikroe)"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_uwb2_dwm3000/README.md"], "description": "This example project shows an example for DWM3000 - UWB 2 Click (Mikroe).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_uwb2_dwm3000.app/example/mikroe_uwb2_dwm3000/SimplicityStudio/mikroe_uwb2_dwm3000.slcp", "text": "Third Party Hardware Drivers - DWM3000 - UWB 2 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for DWM3000 - UWB 2 Click (Mikroe)."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_e_paper_154_inch/README.md"], "description": "This example project shows an example for Mikroe E-Paper display 1.54\" 200x200 dots driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_e_paper_154_inch.app/example/mikroe_e_paper_154_inch/mikroe_e_paper_154_inch.slcp", "text": "Third Party Hardware Drivers - E-Paper display 1.54\" 200x200 dots (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe E-Paper display 1.54\" 200x200 dots driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_barcode2_em3080w/README.md"], "description": "This example project shows an example for Mikroe Barcode 2 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_barcode2_em3080w.app/example/mikroe_barcode2_em3080w/mikroe_barcode2_em3080w.slcp", "text": "Third Party Hardware Drivers - EM3080-W - Barcode 2 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Barcode 2 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_emg/README.md"], "description": "This example project shows an example for EMG Click (Mikroe).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_emg.app/example/mikroe_emg/mikroe_emg.slcp", "text": "Third Party Hardware Drivers - <PERSON><PERSON> (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for EMG Click (Mikroe)."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_force3_fsr400/README.md"], "description": "This example project shows an example for FSR400 - Force 3 Click (Mikroe).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_force3_fsr400.app/example/mikroe_force3_fsr400/mikroe_force3_fsr400.slcp", "text": "Third Party Hardware Drivers - FSR400 - Force 3 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for FSR400 - Force 3 Click (Mikroe)."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/adafruit_tft_lcd_hxd8357d/README.md"], "description": "This example project shows an example for Adafruit HXD8357D TFT LCD with Touchscreen driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.adafruit_tft_lcd_hxd8357d.app/example/adafruit_tft_lcd_hxd8357d/adafruit_tft_lcd_hxd8357d.slcp", "text": "Third Party Hardware Drivers - HXD8357D - TFT LCD with Touchscreen (Adafruit) - SPI", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Adafruit HXD8357D TFT LCD with Touchscreen driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/adafruit_tft_lcd_hxd8357d/README.md"], "description": "This example project shows an example for Adafruit HXD8357D TFT LCD with Touchscreen driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.adafruit_tft_lcd_hxd8357d_dma.app/example/adafruit_tft_lcd_hxd8357d/adafruit_tft_lcd_hxd8357d_dma.slcp", "text": "Third Party Hardware Drivers - HXD8357D - TFT LCD with Touchscreen (Adafruit) - SPI with DMA", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Adafruit HXD8357D TFT LCD with Touchscreen driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_rfid_id12la/README.md"], "description": "This example project shows an example for Sparkfun ID-12LA RFID reader module driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_rfid_id12la.app/example/sparkfun_rfid_id12la/sparkfun_rfid_id12la.slcp", "text": "Third Party Hardware Drivers - ID-12LA - RFID Qwiic Reader (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Sparkfun ID-12LA RFID reader module driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/adafruit_tft_lcd_ili9341/README.md"], "description": "This example project shows an example for Adafruit TFT LCD with Touchscreen driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.adafruit_tft_lcd_ili9341.app/example/adafruit_tft_lcd_ili9341/adafruit_tft_lcd_ili9341.slcp", "text": "Third Party Hardware Drivers - ILI9341 - TFT LCD with Touchscreen (Adafruit) - SPI", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Adafruit TFT LCD with Touchscreen driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/adafruit_tft_lcd_ili9341/README.md"], "description": "This example project shows an example for Adafruit TFT LCD with Touchscreen driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.adafruit_tft_lcd_ili9341_dma.app/example/adafruit_tft_lcd_ili9341/adafruit_tft_lcd_ili9341_dma.slcp", "text": "Third Party Hardware Drivers - ILI9341 - TFT LCD with Touchscreen (Adafruit) - SPI with DMA", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Adafruit TFT LCD with Touchscreen driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/adafruit_rgb_led_is31fl3741/README.md"], "description": "This example project shows an example for Adafruit 13x9 PWM RGB LED Matrix driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.adafruit_rgb_led_is31fl3741.app/example/adafruit_rgb_led_is31fl3741/adafruit_rgb_led_is31fl3741.slcp", "text": "Third Party Hardware Drivers - IS31FL3741 - 13x9 PWM RGB LED Matrix (Adafruit)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Adafruit 13x9 PWM RGB LED Matrix driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_dcmotor24_l9958/README.md"], "description": "This example project shows an example for Mikroe DC Motor 24 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_dcmotor24_l9958.app/example/mikroe_dcmotor24_l9958/mikroe_dcmotor24_l9958.slcp", "text": "Third Party Hardware Drivers - L9958 - DC Motor 24 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe DC Motor 24 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_brushless16_lb11685av/README.md"], "description": "This example project shows an example for Mikroe Brushless 16 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_brushless16_lb11685av.app/example/mikroe_brushless16_lb11685av/mikroe_brushless16_lb11685av.slcp", "text": "Third Party Hardware Drivers - LB11685AV - Brushless 16 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Brushless 16 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_relay2_lca717/README.md"], "description": "This example project shows an example for Mikroe Relay 2 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_relay2_lca717.app/example/mikroe_relay2_lca717/mikroe_relay2_lca717.slcp", "text": "Third Party Hardware Drivers - LCA717 - Relay 2 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Relay 2 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_gps_lea6s/README.md"], "description": "This example project shows an example for LEA-6S - GPS Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_gps_lea6s.app/example/mikroe_gps_lea6s/mikroe_gps_lea6s.slcp", "text": "Third Party Hardware Drivers - LEA-6S - GPS Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for LEA-6S - GPS Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_gnss_max_m10s/README.md"], "description": "This example project shows an example for Sparkfun MAX-M10S - GNSS Receiver Breakout board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_gnss_max_m10s.app/example/sparkfun_gnss_max_m10s/sparkfun_gnss_max_m10s.slcp", "text": "Third Party Hardware Drivers - MAX-M10S - GNSS Receiver Breakout (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Sparkfun MAX-M10S - GNSS Receiver Breakout board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_hr_po_max30101_max32664/README.md"], "description": "This example project shows an example for Sparkfun Pulse Oximeter and Heart Rate Sensor board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_hr_po_max30101_max32664.app/example/sparkfun_hr_po_max30101_max32664/sparkfun_hr_po_max30101_max32664.slcp", "text": "Third Party Hardware Drivers - MAX30101 & MAX32664 - Pulse Oximeter and Heart Rate Sensor (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Sparkfun Pulse Oximeter and Heart Rate Sensor board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_heartrate4_max30101/README.md"], "description": "This example project shows an example for Mikroe Heart Rate 4 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_heartrate4_max30101.app/example/mikroe_heartrate4_max30101/mikroe_heartrate4_max30101.slcp", "text": "Third Party Hardware Drivers - MAX30101 - Heart Rate 4 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Heart Rate 4 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_utm7segr_max6969/README.md"], "description": "This example project shows an example for Mikroe UT-M 7-SEG R Click board.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_utm7segr_max6969.app/example/mikroe_utm7segr_max6969/mikroe_utm7segr_max6969.slcp", "text": "Third Party Hardware Drivers - MAX6969 - UT-M 7-SEG <PERSON> (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe UT-M 7-SEG R Click board."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_heartrate2_maxm86161/README.md"], "description": "This example project shows an example for Mikroe Heartrate 2 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_heartrate2_maxm86161.app/example/mikroe_heartrate2_maxm86161/mikroe_heartrate2_maxm86161.slcp", "text": "Third Party Hardware Drivers - MAXM86161 - Heartrate 2 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Heartrate 2 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_mic2/README.md"], "description": "This example project shows an example for Mikroe MIC 2 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_mic2.app/example/mikroe_mic2/mikroe_mic2.slcp", "text": "Third Party Hardware Drivers - MIC 2 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe MIC 2 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_uv_ml8511a/README.md"], "description": "This example project shows an example for Third Party Hardware Drivers - ML8511A - UV Click (Mikroe) integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_uv_ml8511a.app/example/mikroe_uv_ml8511a/mikroe_uv_ml8511a.slcp", "text": "Third Party Hardware Drivers - ML8511A - <PERSON>lick (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Third Party Hardware Drivers - ML8511A - UV Click (Mikroe) integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_irthermo3_mlx90632/README.md"], "description": "This example project shows an example for Mikroe IrThermo 3 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_irthermo3_mlx90632.app/example/mikroe_irthermo3_mlx90632/mikroe_irthermo3_mlx90632.slcp", "text": "Third Party Hardware Drivers - MLX90632 - IrThermo 3 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe IrThermo 3 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_ir_array_mlx90640/README.md"], "description": "This example project shows an example for Sparkfun IR Array MLX90640 board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_ir_array_mlx90640.app/example/sparkfun_ir_array_mlx90640/sparkfun_ir_array_mlx90640.slcp", "text": "Third Party Hardware Drivers - MLX90640 - IR Array Breakout (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Sparkfun IR Array MLX90640 board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_radar_mm5d91_00/README.md"], "description": "This example project shows an example for Mikroe Radar Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_radar_mm5d91_00.app/example/mikroe_radar_mm5d91_00/mikroe_radar_mm5d91_00.slcp", "text": "Third Party Hardware Drivers - MM5D91-00 - <PERSON> (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Radar Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_accelerometer_mma8452q/README.md"], "description": "This example project shows an example for Sparkfun Triple Axis Accelerometer Breakout board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_accelerometer_mma8452q.app/example/sparkfun_accelerometer_mma8452q/sparkfun_accelerometer_mma8452q.slcp", "text": "Third Party Hardware Drivers - MMA8452Q - Triple Axis Accelerometer Breakout (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Sparkfun Triple Axis Accelerometer Breakout board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_ozone2_mq131/README.md"], "description": "This example project shows an example for Ozone 2 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_ozone2_mq131.app/example/mikroe_ozone2_mq131/mikroe_ozone2_mq131.slcp", "text": "Third Party Hardware Drivers - MQ131 - Ozone 2 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Ozone 2 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_alcohol_mq3/README.md"], "description": "This example project shows an example for Mikroe Alcohol Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_alcohol_mq3.app/example/mikroe_alcohol_mq3/mikroe_alcohol_mq3.slcp", "text": "Third Party Hardware Drivers - MQ3 - Alco<PERSON> (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Alcohol Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_co_mq7/README.md"], "description": "This example project shows an example for Mikroe CO Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_co_mq7.app/example/mikroe_co_mq7/mikroe_co_mq7.slcp", "text": "Third Party Hardware Drivers - <PERSON>Q7 - <PERSON> (Mik<PERSON>e)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe CO Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_nfctag2_nt3h2111/README.md"], "description": "This example project shows an example for NT3H2111 - NFC Tag 2 Click board driver integration on I2C communication.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_nfctag2_nt3h2111.app/example/mikroe_nfctag2_nt3h2111/mikroe_nfctag2_nt3h2111.slcp", "text": "Third Party Hardware Drivers - NT3H2111 - NFC Tag 2 Click (Mikroe) - I2C", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for NT3H2111 - NFC Tag 2 Click board driver integration on I2C communication."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/adafruit_neotre<PERSON>_keypad_atsamd09/README.md"], "description": "This example project shows an example for Adafruit NeoTrellis 4x4 Keypad.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.adafruit_neotrellis_keypad_atsamd09.app/example/adafruit_neotrellis_keypad_atsamd09/adafruit_neotrellis_keypad_atsamd09.slcp", "text": "Third Party Hardware Drivers - NeoTrellis 4x4 Keypad (Adafruit)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Adafruit NeoTrellis 4x4 Keypad."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_servo_pca9685/README.md"], "description": "This example project shows an example for Mikroe Servo Click driver integration on I2C communication.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_servo_pca9685.app/example/mikroe_servo_pca9685/mikroe_servo_pca9685.slcp", "text": "Third Party Hardware Drivers - PCA9685 - <PERSON><PERSON> (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Servo Click driver integration on I2C communication."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_pir_pl_n823_01/README.md"], "description": "This example project shows an example for Mikroe PIR Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_pir_pl_n823_01.app/example/mikroe_pir_pl_n823_01/mikroe_pir_pl_n823_01.slcp", "text": "Third Party Hardware Drivers - PL-N823-01 - <PERSON><PERSON> (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe PIR Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_nfc2_pn7150/README.md"], "description": "This example project shows an example for PN7150 - NFC 2 Click board driver integration on I2C communication.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_nfc2_pn7150.app/example/mikroe_nfc2_pn7150/mikroe_nfc2_pn7150.slcp", "text": "Third Party Hardware Drivers - PN7150 - NFC 2 Click (Mikroe) - I2C", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for PN7150 - NFC 2 Click board driver integration on I2C communication."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_nfc2_pn7150_nci_t2t_read/README.md"], "description": "This example project demonstrates the interface of the NFC NCI service by using the NFC controller PN7150 to read a T2T Tag.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.pn7150_nci_t2t_read.app/example/mikroe_nfc2_pn7150_nci_t2t_read/pn7150_nci_t2t_read.slcp", "text": "Third Party Hardware Drivers - PN7150 - Read a T2T Tag with NCI", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the interface of the NFC NCI service by using the NFC controller PN7150 to read a T2T Tag."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_nfc2_pn7150_nci_t2t_write/README.md"], "description": "This example project demonstrates the interface of the NFC NCI service by using the NFC controller PN7150 to write to a T2T Tag.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.pn7150_nci_t2t_write.app/example/mikroe_nfc2_pn7150_nci_t2t_write/pn7150_nci_t2t_write.slcp", "text": "Third Party Hardware Drivers - PN7150 - Write to a T2T Tag with NCI", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project demonstrates the interface of the NFC NCI service by using the NFC controller PN7150 to write to a T2T Tag."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_dosimeter_type5/README.md"], "description": "This example project shows an example for Sparkfun Pocket Geiger Radiation Sensor - Type 5 board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_dosimeter_type5.app/example/sparkfun_dosimeter_type5/sparkfun_dosimeter_type5.slcp", "text": "Third Party Hardware Drivers - Pocket Geiger Radiation Sensor - Type 5 (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Sparkfun Pocket Geiger Radiation Sensor - Type 5 board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_qwiic_joystick/README.md"], "description": "This project shows the implementation of a Joystick module that is integrated on the SparkFun Qwiic Joystick board.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_qwiic_joystick.app/example/sparkfun_qwiic_joystick/sparkfun_qwiic_joystick.slcp", "text": "Third Party Hardware Drivers - <PERSON><PERSON><PERSON> (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project shows the implementation of a Joystick module that is integrated on the SparkFun Qwiic Joystick board."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_qwiic_keypad/README.md"], "description": "This project shows the implementation of a Keypad module that is integrated on the SparkFun Qwiic Keypad board.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_qwiic_keypad.app/example/sparkfun_qwiic_keypad/sparkfun_qwiic_keypad.slcp", "text": "Third Party Hardware Drivers - <PERSON><PERSON><PERSON>pad (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project shows the implementation of a Keypad module that is integrated on the SparkFun Qwiic Keypad board."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_soil_moisture/README.md"], "description": "This example project shows an example for Sparkfun Soil Moisture Sensor board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_soil_moisture.app/example/sparkfun_soil_moisture/sparkfun_soil_moisture.slcp", "text": "Third Party Hardware Drivers - <PERSON><PERSON><PERSON> Soil Moisture Sensor (Sparkfun) - I2C", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Sparkfun Soil Moisture Sensor board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_rng/README.md"], "description": "This example project shows an example for RNG Click (Mikroe).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_rng.app/example/mikroe_rng/mikroe_rng.slcp", "text": "Third Party Hardware Drivers - <PERSON><PERSON> (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for RNG Click (Mikroe)."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_hvac_bundle_scd41_sps30/README.md"], "description": "This project shows an example for SCD41 & SPS30 - HVAC Click Bundle (Mikroe).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_hvac_bundle_scd41_sps30.app/example/mikroe_hvac_bundle_scd41_sps30/mikroe_hvac_bundle_scd41_sps30.slcp", "text": "Third Party Hardware Drivers - SCD41 & SPS30 - HVAC Click Bundle (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project shows an example for SCD41 & SPS30 - HVAC Click Bundle (Mikroe)."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_hvac_scd41/README.md"], "description": "This example project shows an example for SCD41 - HVAC Click (Mikroe).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_hvac_scd41.app/example/mikroe_hvac_scd41/mikroe_hvac_scd41.slcp", "text": "Third Party Hardware Drivers - SCD41 - <PERSON><PERSON><PERSON> (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for SCD41 - HVAC Click (Mikroe)."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_air_quality_sgp40/README.md"], "description": "This example project shows an example for the Sparkfun SGP40 Air Quality Sensor board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_air_quality_sgp40.app/example/sparkfun_air_quality_sgp40/sparkfun_air_quality_sgp40.slcp", "text": "Third Party Hardware Drivers - SGP40 - Air Quality Sensor (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for the Sparkfun SGP40 Air Quality Sensor board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_environment2_sht40_sgp40/README.md"], "description": "This example project shows an example for Mikroe Environment 2 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_environment2_sht40_sgp40.app/example/mikroe_environment2_sht40_sgp40/mikroe_environment2_sht40_sgp40.slcp", "text": "Third Party Hardware Drivers - SHT40 & SGP40 - Environment 2 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Environment 2 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_temphum15_sht40/README.md"], "description": "This example project shows an example for Mikroe Temp&Hum 15 Click board integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_temphum15_sht40.app/example/mikroe_temphum15_sht40/mikroe_temphum15_sht40.slcp", "text": "Third Party Hardware Drivers - SHT40 - Temp&Hum 15 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Temp&Hum 15 Click board integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_temphum9_shtc3/README.md"], "description": "This example project shows an example for Mikroe Temp&Hum 9 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_temphum9_shtc3.app/example/mikroe_temphum9_shtc3/mikroe_temphum9_shtc3.slcp", "text": "Third Party Hardware Drivers - SHTC3 - Temp&Hum 9 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Temp&Hum 9 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_particulate_matter_sensor_sps30/README.md"], "description": "This project shows an example for Sparkfun Particulate Matter Sensor SPS30 board driver integration using I2C interface.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_particulate_matter_sensor_sps30_i2c.app/example/sparkfun_particulate_matter_sensor_sps30/sparkfun_particulate_matter_sensor_sps30_i2c.slcp", "text": "Third Party Hardware Drivers - SPS30 - Particulate Matter Sensor (Sparkfun) - I2C", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project shows an example for Sparkfun Particulate Matter Sensor SPS30 board driver integration using I2C interface."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_particulate_matter_sensor_sps30/README.md"], "description": "This project shows an example for Sparkfun Particulate Matter Sensor SPS30 board driver integration using UART interface.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_particulate_matter_sensor_sps30_uart.app/example/sparkfun_particulate_matter_sensor_sps30/sparkfun_particulate_matter_sensor_sps30_uart.slcp", "text": "Third Party Hardware Drivers - SPS30 - Particulate Matter Sensor (Sparkfun) - UART", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project shows an example for Sparkfun Particulate Matter Sensor SPS30 board driver integration using UART interface."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_micro_oled_ssd1306/README.md"], "description": "This example project shows an example for Micro OLED Breakout driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_micro_oled_ssd1306.app/example/sparkfun_micro_oled_ssd1306/sparkfun_micro_oled_ssd1306.slcp", "text": "Third Party Hardware Drivers - SSD1306 - Micro OLED Breakout Qwiic (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Micro OLED Breakout driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_oledw_ssd1306/README.md"], "description": "This example project shows an example for Mikroe OLEDW Click driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_oledw_ssd1306.app/example/mikroe_oledw_ssd1306/mikroe_oledw_ssd1306.slcp", "text": "Third Party Hardware Drivers - SSD1306 - <PERSON><PERSON><PERSON><PERSON> OLEDW Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe OLEDW Click driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_oledw_ssd1306_glib/README.md"], "description": "This example project shows an example for Mikroe OLEDW Click driver integration with GLIB.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_oledw_ssd1306_glib.app/example/mikroe_oledw_ssd1306_glib/mikroe_oledw_ssd1306_glib.slcp", "text": "Third Party Hardware Drivers - SSD1306 - <PERSON><PERSON><PERSON><PERSON> OLEDW Click (Mikroe) with GLIB", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe OLEDW Click driver integration with GLIB."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_oledc_ssd1351/README.md"], "description": "This example project shows an example for Mikroe OLEDC Click driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_oledc_ssd1351.app/example/mikroe_oledc_ssd1351/mikroe_oledc_ssd1351.slcp", "text": "Third Party Hardware Drivers - SSD1351 - OLED <PERSON>lick (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe OLEDC Click driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/adafruit_tft_lcd_st7789/README.md"], "description": "This example project shows an example for Adafruit TFT LCD using ST7789 driver with SPI.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.adafruit_tft_lcd_st7789.app/example/adafruit_tft_lcd_st7789/adafruit_tft_lcd_st7789.slcp", "text": "Third Party Hardware Drivers - ST7789 - TFT LCD Display (Adafruit) - SPI", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Adafruit TFT LCD using ST7789 driver with SPI."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/adafruit_tft_lcd_st7789/README.md"], "description": "This example project shows an example for Adafruit TFT LCD using ST7789 driver with SPI DMA.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.adafruit_tft_lcd_st7789_dma.app/example/adafruit_tft_lcd_st7789/adafruit_tft_lcd_st7789_dma.slcp", "text": "Third Party Hardware Drivers - ST7789 - TFT LCD Display (Adafruit) - SPI with DMA", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Adafruit TFT LCD using ST7789 driver with SPI DMA."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_obdii_stn1110/README.md"], "description": "This example project shows an example for Mikroe OBDII Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_obdii_stn1110.app/example/mikroe_obdii_stn1110/mikroe_obdii_stn1110.slcp", "text": "Third Party Hardware Drivers - STN1110 - OBDII Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe OBDII Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_pwm_driver_si8711cc/README.md"], "description": "This example project shows an example for Si8711CC - PWM Driver Click (Mikroe) integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_pwm_driver_si8711cc.app/example/mikroe_pwm_driver_si8711cc/mikroe_pwm_driver_si8711cc.slcp", "text": "Third Party Hardware Drivers - Si8711CC - PWM Driver Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Si8711CC - PWM Driver Click (Mikroe) integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_smoke2_adpd188bi/README.md"], "description": "This example project shows an example for Smoke 2 Click (Mikroe) using I2C Interface.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_smoke2_i2c.app/example/mikroe_smoke2_adpd188bi/mikroe_smoke2_adpd188bi_i2c.slcp", "text": "Third Party Hardware Drivers - Smoke 2 Click (Mikroe) - I2C", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Smoke 2 Click (Mikroe) using I2C Interface."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_smoke2_adpd188bi/README.md"], "description": "This example project shows an example for Smoke 2 Click (Mikroe) using SPI Interface.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_smoke2_spi.app/example/mikroe_smoke2_adpd188bi/mikroe_smoke2_adpd188bi_spi.slcp", "text": "Third Party Hardware Drivers - Smoke 2 Click (Mikroe) - SPI", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Smoke 2 Click (Mikroe) using SPI Interface."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_stretch/README.md"], "description": "This example project shows an example for Mikroe Stretch Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_stretch.app/example/mikroe_stretch/mikroe_stretch.slcp", "text": "Third Party Hardware Drivers - <PERSON><PERSON><PERSON> (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Stretch Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_dcmotor3_tb6549fg/README.md"], "description": "This example project shows an example for Mikroe DC Motor 3 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_dcmotor3_tb6549fg.app/example/mikroe_dcmotor3_tb6549fg/mikroe_dcmotor3_tb6549fg.slcp", "text": "Third Party Hardware Drivers - TB6549FG - DC Motor 3 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe DC Motor 3 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_dcmotor26_tb9053ftg/README.md"], "description": "This example project shows an example for Mikroe DC Motor 26 Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_dcmotor26_tb9053ftg.app/example/mikroe_dcmotor26_tb9053ftg/mikroe_dcmotor26_tb9053ftg.slcp", "text": "Third Party Hardware Drivers - TB9053FTG - DC Motor 26 Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe DC Motor 26 Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_turbidity_tsd10/README.md"], "description": "This example project shows an example for TSD-10 - Turbidity 15 Click (Mikroe) integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_turbidity_tsd10.app/example/mikroe_turbidity_tsd10/mikroe_turbidity_tsd10.slcp", "text": "Third Party Hardware Drivers - TSD-10 - <PERSON><PERSON><PERSON><PERSON> Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for TSD-10 - Turbidity 15 Click (Mikroe) integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_proximity_vcnl4040/README.md"], "description": "This example project shows an example for Sparkfun Proximity VCNL4040 board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_proximity_vcnl4040.app/example/sparkfun_proximity_vcnl4040/sparkfun_proximity_vcnl4040.slcp", "text": "Third Party Hardware Drivers - VCNL4040 - Proximity Sensor (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Sparkfun Proximity VCNL4040 board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/sparkfun_distance_vl53l1x/README.md"], "description": "This example project shows an example for Sparkfun VL53L1X Distance Board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.sparkfun_distance_vl53l1x.app/example/sparkfun_distance_vl53l1x/sparkfun_distance_vl53l1x.slcp", "text": "Third Party Hardware Drivers - VL53L1X - Distance Sensor (Sparkfun)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Sparkfun VL53L1X Distance Board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_eth_wiz_w5500/README.md"], "description": "This example project shows an example for Mikroe ETH Wiz click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_eth_wiz_w5500.app/example/mikroe_eth_wiz_w5500/mikroe_eth_wiz_w5500.slcp", "text": "Third Party Hardware Drivers - W5500 - <PERSON><PERSON> <PERSON><PERSON> (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe ETH Wiz click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_water_detect/README.md"], "description": "This example project shows an example for Mikroe Water Detect Click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_water_detect.app/example/mikroe_water_detect/mikroe_water_detect.slcp", "text": "Third Party Hardware Drivers - Water Detect Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe Water Detect Click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/extension/third_party_hw_drivers_extension/app/documentation/example/mikroe_microsd/README.md"], "description": "This example project shows an example for Mikroe microSD click board driver integration.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.mikroe_microsd.app/example/mikroe_microsd/mikroe_microsd.slcp", "text": "Third Party Hardware Drivers - microSD Click (Mikroe)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This example project shows an example for Mikroe microSD click board driver integration."}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/workspaces/bootloader-apploader/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bootloader-apploader-workspace.sample-apps/workspaces/bootloader-apploader/bootloader-apploader.slcw", "text": "bootloader-apploader-workspace", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/workspaces/bootloader-uart-bgapi/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bootloader-uart-bgapi-workspace.sample-apps/workspaces/bootloader-uart-bgapi/bootloader-uart-bgapi.slcw", "text": "bootloader-uart-bgapi-workspace", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/platform/bootloader/sample-apps/workspaces/bootloader-uart-xmodem/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bootloader-uart-xmodem-workspace.sample-apps/workspaces/bootloader-uart-xmodem/bootloader-uart-xmodem.slcw", "text": "bootloader-uart-xmodem-workspace", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This workspace contains the secure and non-secure part of the bootloader and builds them together.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/tz_psa_attestation/readme.md"], "description": "This example workspace demonstrates TrustZone for PSA Attestation.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.tz_psa_attestation_ws.example/tz_psa_attestation/tz_psa_attestation_ws.slcw", "text": "tz_psa_attestation_ws", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "This example workspace demonstrates TrustZone for PSA Attestation.\n"}, {"imageURL": "bundleentry://460.fwk332573486/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/tz_psa_crypto_ecdh/readme.md"], "description": "This example workspace demonstrates TrustZone for ECDH key agreement.", "id": "template.solution.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.tz_psa_crypto_ecdh_ws.example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_ws.slcw", "text": "tz_psa_crypto_ecdh_ws", "priority": 9999999, "category": "SOFTWARE_SOLUTION", "toolTipText": "This example workspace demonstrates TrustZone for ECDH key agreement.\n"}], "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [{"futureCount": 20, "anySelected": false, "id": 0, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": false}, {"futureCount": 23, "anySelected": false, "id": 1, "filters": [], "title": "Bluetooth Mesh", "parentId": 0, "selected": false}], "title": "Wireless Technology", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [{"futureCount": 10, "anySelected": false, "id": 0, "filters": [], "title": "NCP", "parentId": 1, "selected": false}, {"futureCount": 2, "anySelected": false, "id": 1, "filters": [], "title": "RCP", "parentId": 1, "selected": false}, {"futureCount": 68, "anySelected": false, "id": 2, "filters": [], "title": "SoC", "parentId": 1, "selected": false}], "title": "Device Type", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 2, "filters": [{"futureCount": 29, "anySelected": false, "id": 0, "filters": [], "title": "32-bit MCU", "parentId": 2, "selected": false}, {"futureCount": 8, "anySelected": false, "id": 1, "filters": [], "title": "Bootloader", "parentId": 2, "selected": false}], "title": "MCU", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 3, "filters": [{"futureCount": 1, "anySelected": false, "id": 0, "filters": [], "title": "Machine Learning", "parentId": 3, "selected": false}], "title": "Capability", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 4, "filters": [{"futureCount": 53, "anySelected": false, "id": 0, "filters": [], "title": "Advanced", "parentId": 4, "selected": false}, {"futureCount": 118, "anySelected": false, "id": 1, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 4, "selected": false}], "title": "Project Difficulty", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 5, "filters": [{"futureCount": 91, "anySelected": false, "id": 0, "filters": [], "title": "Evaluation", "parentId": 5, "selected": false}, {"futureCount": 2, "anySelected": false, "id": 1, "filters": [], "title": "None Specified", "parentId": 5, "selected": false}, {"futureCount": 80, "anySelected": false, "id": 2, "filters": [], "title": "Production", "parentId": 5, "selected": false}], "title": "Quality", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 6, "filters": [{"futureCount": 173, "anySelected": false, "id": 0, "filters": [], "title": "Gecko SDK Suite v4.4.4", "parentId": 6, "selected": false}], "title": "Provider", "parentId": -1, "selected": false}], "totalCount": 173}