[{"softwareCovered": ["Simplicity SDK - 32-bit and Wireless MCUs 2024.12.2"], "text": "<html>\n<head>\n<title>Master Software License Agreement</title>\n<style>\n   OL { counter-reset: item }\n   LI { display: block;\n        margin: 0.5em 0 1 0;\n      }\n   UL { margin: 0.5em 0 1 0;  }\n   LI:before { content: counters(item, \".\") \". \"; counter-increment: item }\n   H1 { font-size: 1.5em; }\n</style>\n</head>\n\n<body lang=EN-US>\n\n<div>\n\n<h1><b>MASTER SOFTWARE LICENSE AGREEMENT</b></h1>\n\n<p>Version 20250501</p>\n\n<p><b>THIS MASTER SOFTWARE LICENSE AGREEMENT (&ldquo;<u>AGREEMENT</u>&rdquo;) GOVERNS YOUR USE OF THE LICENSED MATERIALS. INSTALLING, COPYING OR OTHERWISE USING THE SOFTWARE, INDICATES YOUR ACCEPTANCE OF THE TERMS OF THIS\nAGREEMENT REGARDLESS OF WHETHER YOU CLICK THE &ldquo;ACCEPT&rdquo; BUTTON. IF YOU DO NOT AGREE WITH THESE TERMS AND CONDITIONS OR IF YOU ARE NOT AUTHORIZED TO\nACCEPT THE TERMS OF THIS LICENSE ON BEHALF OF YOUR EMPLOYER, DECLINE THE LICENSE TERMS AND DO NOT USE THE SOFTWARE OR DOCUMENTATION. THESE TERMS GOVERN YOUR CONTINUED USE OF THE LICENSED MATERIALS THAT YOU DOWNLOAD NOW OR IN\nTHE FUTURE, INCLUDING SUCH ADDITIONAL SOFTWARE MADE AVAILABLE TO YOU THROUGH THE LICENSED MATERIALS YOU HAVE SELECTED, AND ALL UPDATES AND VERSIONS OF SUCH SOFTWARE.\n</b></p>\n\n<ol style=\"margin-left: -10px;\">\n<li><b><u>Definitions.</u></b>\n<ul>\n<p>&ldquo;<u>Application</u>&rdquo;\nmeans a product developed by Licensee, or for Licensee by a third party, that\ncontains Licensed Programs.</p>\n\n<p>&ldquo;<u>Authorized Application</u>&rdquo; means an Application that contains, integrates, is packaged\nwith, or functions with a Silicon Labs Device in Licensee&rsquo;s Application. This\nincludes a Licensed Program embedded in a network co-processor or host\nprocessor that operates in conjunction with a Silicon Labs Device.</p>\n\n<p>&ldquo;<u>Authorized Subcontractor</u>&rdquo; means a third-party subcontractor that you engage to design\nor manufacture Authorized Applications and has executed an agreement that is\nconsistent with the terms of this Agreement, including its confidentiality\nprovisions. At all times, you shall remain responsible for the actions or\nnon-actions of your Authorized Subcontractors the same as if the action or\nnon-action was committed by you.</p>\n\n<p>&ldquo;<u>Beta Software</u>&rdquo; means Software, including Software included within an SDK, that is \nundergoing testing or further development, and has not reached the generally available (GA) stage of \ndevelopment.</p>\n\n<p>&ldquo;<u>Commercial License</u>&rdquo; means an executed, in effect, software license agreement between\nLicensee and Silicon Labs that governs Licensee&rsquo;s rights and obligations with\nrespect to Licensee&rsquo;s use of Micrium Software.</p>\n\n<p>&ldquo;<u>Commercial Purpose</u>&rdquo; means embedding Micrium Software in any Application that you sell\nor license to End Users or plan to do so. </p>\n\n<p>&ldquo;<u>Deprecated Software</u>&rdquo; refers to any software versions or components that have been \nofficially discontinued by Silicon Labs. Such software is no longer supported, maintained, or updated \nby Silicon Labs. </p>\n\n<p>&ldquo;<u>Derivative Works</u>&rdquo; means a work based upon the Source Code version of the Software,\nsuch as a revision, modification, translation, abridgment, condensation,\nexpansion or any other form in which such Software may be recast, transformed\nor adapted, and that, if prepared without authorization from Silicon Labs,\nwould constitute copyright infringement.</p>\n\n<p>&ldquo;<u>Documentation</u>&rdquo;\nmeans Silicon Labs technical documentation related to the Software, excluding\nadvertising or marketing materials.</p>\n\n<p>&ldquo;<u>Embedded Stacks</u>&rdquo; means Software (other than Micrium Software) that is stack\nlibraries, application layers, and example code.</p>\n\n<p>&ldquo;<u>End User</u>&rdquo; means a purchaser, sublicensee, recipient and/or user of an\nApplication obtained directly or indirectly from Licensee.</p>\n\n<p>&ldquo;<u>External Manufacturers</u>&rdquo; means a third-party manufacturer, including such\nmanufacturer&rsquo;s subcontractors and agents, which is authorized by Licensee to\ndesign and/or manufacture the Applications and to use, install, and test the Applications\nand the Licensed Programs.</p>\n\n<p>&ldquo;<u>Feedback</u>&rdquo;\nmeans Licensee-provided suggestions, comments, contributions, modifications, changes, or\nother feedback to Silicon Labs regarding the Software, Documentation, or related services\nprovided by Silicon Labs.\n\n<p>&ldquo;<u>Firmware</u>&rdquo;\nmeans executable or binary code that is embedded in the Silicon Labs Device in\nROM or flash memory and cannot be modified by Licensee.</p>\n\n<p>&ldquo;<u>Licensee</u>&rdquo;\nor &ldquo;<u>you</u>&rdquo; means the acquirer of the license rights granted by this\nAgreement. If you are an individual working solely on your own behalf, then you\nare the Licensee. If you are an employee working on behalf of your employer,\nthen your employer is the Licensee and you confirm that you are authorized to\naccept the terms of this Agreement on behalf of your Employer.</p>\n\n<p>&ldquo;<u>Licensed Programs</u>&rdquo; means Software in Object Code form that was either originally in\nObject Code form or was compiled from the Software or Derivative Works and is\ndesigned to operate in Authorized Applications.</p>\n\n<p>&ldquo;<u>Licensed Materials</u>&rdquo; means Software and the related Documentation including all updates\nand upgrades of the foregoing. Licensed Materials do not include Beta Software.</p>\n\n<p>&ldquo;<u>Micrium Software</u>&rdquo; means the Micrium real time kernel within the Micrium real time\noperating system (Micrium OS Kernel) as well as the following modules: Micrium OS Net, Micrium OS FS,\nMicrium USB Device, Micrium USB Host, and Micrium OS CAN.</p>\n\n<p>&ldquo;<u>Modified Open Source Software</u>&rdquo; means Silicon Labs' modifications to Open Source \n   Software that was created by a third party.</p>\n   \n<p>&ldquo;<u>Object Code</u>&rdquo; means computer programming code in binary form suitable for machine\nexecution by a processor without the intervening steps of interpretation or\ncompilation.</p>\n\n<p>&ldquo;<u>Open Source Software</u>&rdquo; means the Source Code version of software that may be\nfreely used, modified or distributed without payment of a license fee or\nroyalties subject to the terms of a publicly available software license,\nexcluding Modified Open Source Software, the use of which is subject to the terms \nof this Agreement.</p>\n\n<p>&ldquo;<u>Personal Information</u>&rdquo; means data concerning an individual user, including but not\nlimited to a user&rsquo;s activity on a Silicon Labs website, location, IP address,\nmobile device ID, name, or biometric data collected, stored or transmitted by a\nSilicon Lab Device or Software.</p>\n\n<p>&ldquo;<u>SDK</u>&rdquo;\nmeans software development kit (other than Micrium Software).</p>\n\n<p>&ldquo;<u>Silicon Labs</u>&rdquo; means Silicon Laboratories Inc., a Delaware corporation located at\n400 W. Cesar Chavez, Austin, TX 78701 if you are physically located within the\nUnited States. If you are physically located outside of the United States,\nSilicon Labs means Silicon Laboratories International Pte Ltd., a Singapore\ncorporation located at No. 18 Tai Seng Street, #05-01, 18 Tai Seng, Singapore\n539775.</p>\n\n<p>&ldquo;<u>Silicon Labs Devices</u>&rdquo; means Silicon Labs branded integrated circuit chips purchased\nfrom Silicon Labs or one of its authorized distributors.</p>\n\n<p>&ldquo;<u>Silicon Labs Open Source Code</u>&rdquo; means Software created by Silicon Labs and \nwhich is (a) delivered to Licensee in Source Code format, (b) is identified \nas open source code and (c) states that use of the software is subject\nto the terms of this Agreement.</p>\n\n<p>&ldquo;<u>Software</u>&rdquo;\nmeans the computer programing code that was downloaded or otherwise distributed\nto Licensee and which is the subject of this Agreement. Software may be in\nObject Code or Source Code form or may be embedded as Firmware in a Silicon\nLabs Device. Your rights are different depending on whether the Software is\ndelivered to you in Object Code, Source Code or Firmware. Software that is\nMicrium Software is subject to specific terms and conditions defined in Section\n2.</p>\n\n<p>&ldquo;<u>Source Code</u>&rdquo; means the computer programming code in an uncompiled form readable by\nhumans which cannot be executed by a processor unless it is compiled into\nbinary form.</p>\n\n<p>&ldquo;<u>Third Party Software</u>&rdquo; means any software that Silicon Labs has licensed from a\nthird party.</p>\n\n<p>&ldquo;<u>Unauthorized Use</u>&rdquo; means use or inclusion of the Licensed Materials in (a) \naeronautical, aerospace, military or nuclear applications; (b) FDA\nClass III devices for which FDA premarket approval is required; (c)\nimplantable devices; (d) life support or life endangering applications where\nfailure or inaccuracy might cause death or personal injury; and (e) automotive\nor transportation applications or environments unless the specific Silicon Labs\nDevice has been designated by Silicon Labs as compliant with ISO/TS 16949\nrequirements.</p>\n\n<p>&ldquo;<u>Warranty Period</u>&rdquo; means the period of time ending thirty \n   (30) days after the first delivery of the Software to the Licensee, whether \n   such delivery is in the form of a download or actual physical delivery.</p>\n   \n</ul></li>\n\n<li><b><u>Micrium Software.</u></b>The terms and conditions in this Section 2 \n   apply only to Micrium Software and take precedence over any other conflicting\n   terms of this Agreement.\n<ol>\n<li><u>Grant of Micrium Software License.</u>\n   Upon accepting this Agreement and downloading the Micrium Software, subject \n   to your compliance with the terms of this Agreement, Silicon Labs hereby \n   grants you a limited, non-exclusive, nontransferable license for the Term \n   (as described in Section 25), as follows:\n   <ol>\n      <li>You and your Authorized Subcontractors may modify the Source Code \n         version of Micrium Software for the sole purpose of adapting the \n         Micrium Software to your application.\n      </li>\n      <li>You may embed the Micrium Software exclusively in products developed \n         by you or by your Authorized Subcontractors on your behalf during the \n         Term that embed the Micrium Software into Silicon Labs \n         Devices (the <b>&ldquo;End Products&rdquo;</b>).\n      </li>\n      <li>You may distribute copies of the Object Code version of the Micrium \n         Software solely to the extent that such copies are embedded in End \n         Products. \n      </li>\n   </ol>\n</li>\n<li><u>Micrium Software License Restrictions.</u>\n   Your use of the Micrium Software is subject to the following restrictions:\n   <ol>\n      <li>You may allow Authorized Subcontractors to access and use the Micrium \n         Software solely to the extent necessary to adapt the Micrium Software \n         to your application. \n      </li>\n      <li>\n         Except as provided in this Agreement, neither you, your Authorized \n         Subcontractors, nor any of your downstream customers may sell, \n         transfer, sublicense, distribute, or disseminate in any way the Object \n         Code or executable code of the Micrium Software in any written or \n         electronic manner except as permitted under this Agreement.      \n      </li>\n      <li>\n         Neither you, your Authorized Subcontractors, nor any of your \n         downstream customers may distribute the Source Code version of \n         Micrium Software Source Code to any third party under any \n         circumstances. You shall ensure that each of your employees and \n         Authorized Subcontractors that work with Micrium Software are \n         aware of this restriction.      \n      </li>\n      <li>\n         Except as permitted in this Agreement, neither you, your Authorized \n         Subcontractors, nor any of your downstream customer may embed the \n         Micrium Software in any integrated circuit device that is not a \n         Silicon Labs Device.\n      </li>\n   </ol>\n   <li><u>End User Support.</u>\n      You agree to be fully responsible for all End User support services \n      and warranty costs for all End Products. \n   </li>\n</li>\n      \n<li><u>Commercial Purpose</u>.\n   You are permitted to use the Micrium Software for a Commercial Purpose only \n   if you embed the Micrium Software into a Silicon Labs Device. You may not \n   embed the Micrium Software in a non-Silicon Labs Device unless you and \n   Silicon Labs execute a separate Commercial License Agreement expressly \n   permitting such use.\n</li>\n\n<li><u>Ownership.</u> \n   Silicon Labs is and shall remain the sole and exclusive owner of the Micrium \n   Software and all Documentation, media, manuals, specifications, instructions \n   and printed materials, and any copies or portions thereof, furnished by \n   Silicon Labs for use with the Micrium Software.. You own any improvements \n   or modifications to the Micrium Software made by you or your Authorized \n   Subcontractor on your behalf to adapt Micrium Software for use in your \n   End Products.\n</li>\n\n<li><u>Maintenance and Support.</u>\n   Standard maintenance and technical support, such as bug fixes, correction \n   of failures, updates and maintenance patches is provided to you at no cost \n   for one year from date that you download the Micrium Software.  Silicon \n   Labs may use a variety of methods, including but not limited to telephone, \n   Internet and/or e-mail, to provide such maintenance and support.\n</li>\n</ol>\n</li>\n\n<li><b><u>8051 SDK.</u></b>\n   The terms and condition in this Section 3 apply only to the 8051 SDK and \n   take precedence over any other conflicting terms of this Agreement.\n   <ol>\n      <li><u>8051 SDK</u>.\n         The 8051 SDK  consists of the following items:\n         <ol>\n            <li>\n               Drivers,\n            </li>\n            <li>\n               Peripheral libraries (excluding CSLIB); and\n            </li>\n            <li>\n               Example application code.\n            </li>\n         </ol>\n      </li>\n      <li><u>Installation and Use Rights</u>.\n         <ol>\n            <li>\n               You may download and install the 8051 SDK on one or more \n               computers and make any number of copies.\n            </li>\n            <li>\n               You may internally evaluate the 8051 SDK.  If you elect \n               to use the 8051 SDK for any other purpose, including \n               modification and distribution, then the following \n               additional terms apply to you.\n            </li>\n            <li>\n               You may modify any files for your own use. \n            </li>\n            <li>\n               You may redistribute to your customers applications that you \n               develop using the 8051 SDK.  Your redistribution may be in any \n               form, including executable binary code, source code, physical \n               media and Internet downloads.   \n            </li>\n         </ol>\n      </li>\n      <li><u>Restrictions</u>.\n         <ol>\n            <li>\n               You may not use the 8051 SDK with any integrated circuit products \n               other than those designed and manufactured by Silicon Labs.\n            </li>\n            <li>\n               Except as provided above, you may not redistribute, sublicense, \n               assign, rent or lease any portion of the 8051 SDK to any third party.\n            </li>\n            <li>\n               You may not modify or distribute the 8051 SDK so that all \n               or any part of it becomes Open Source Software.\n            </li>\n            <li>\n               You may not obscure or remove any product identification, copyright \n               or other notices that appear on or in the 8051 SDK, including any \n               notices from third parties.\n            </li>\n            <li>\n               You may not redistribute any modified or unmodified version of \n               the 8051 SDK to any third party as a standalone product.\n            </li>\n         </ol>\n      </li>\n      <li><u>Ownership</u>.\n         Silicon Labs is and shall remain the owner of the 8051 SDK at all times.  \n         Applications that you develop using the 8051 SDK shall belong to you.\n      </li>\n   </ol>\n</li>\n\n<li><b><u>License Grant.</u></b>Silicon Labs hereby grants Licensee a limited, non-transferable, non-exclusive,\nperpetual license to use the Licensed Materials solely under the following\nterms and condition:\n<ol>\n<li><u>Object Code</u>.\nWith respect to Software (other than Micrium Software) that is delivered to Licensee\nby Silicon Labs in Object Code format, Licensee may:\n<ol>\n<li>\n(a) if the Software is an Embedded Stack, you may install one copy of the Software\nand its components all together on a single computer, and if the Software is\ncopied onto another computer, the original copy must be deleted or otherwise\nmade irreversibly inoperable; (b) if the Software is an SDK, you may make\nmultiple copies of the Software for your own internal use;</li>\n\n<li>\nstore one copy of the Software for archival (non-operational) purposes only, so long as access to\nsuch copy is restricted;</li>\n\n<li>\nuse the Licensed Materials to develop applications to be used to program Silicon Labs Devices;</li>\n\n<li>\nincorporate the Licensed Materials into Authorized Applications; </li>\n\n<li>\nfacilitate the integration of the Licensed Materials and Silicon Labs Devices into\nAuthorized Applications; and</li>\n\n<li>\ndistribute copies of the Licensed Materials to Licensee&rsquo;s end-user customers, to the\nextent such copies are in Object Code form only and are incorporated into\nAuthorized Applications.</li>\n</ol></li>\n\n<li>\n<u>Source Code</u>.\nWith respect to Software (other than Micrium Software and SIlicon Labs Open Source Software) \nthat is delivered to Licensee by Silicon Labs in Source Code format, Licensee may:\n<ol>\n<li>\nuse the sample application software in Source Code format to develop and compile\napplications for use in Authorized Applications; </li>\n\n<li>\ncopy, prepare Derivative Works of, compile and modify Source Code of the Silicon Labs\nSoftware, solely to enable Licensee to design, develop, modify, test, support\nand/or debug Derivative Works and/or Licensed Programs that are intended to\noperate in Authorized Applications;</li>\n\n<li>\nreproduce and distribute Derivative Works to Authorized Subcontractors under agreements\nconsistent with Licensee&rsquo;s rights and obligations under this Agreement solely (a)\nto modify for Licensee&rsquo;s use in developing and maintaining the Licensed\nPrograms; and (b) to enable Licensee to distribute Licensed Programs externally\nto End Users in accordance with Section 4.2.5 below;</li>\n\n<li>\nreproduce and distribute Licensed Programs internally and to Licensee&rsquo;s External\nManufacturers under agreements consistent with Licensee&rsquo;s rights and\nobligations under this Agreement, solely (a) for Licensee&rsquo;s use in developing\nand maintaining the Licensed Programs; and (b) to enable Licensee to distribute\nLicensed Programs externally to End Users in accordance with Section 4.2.5\nbelow; and </li>\n\n<li>\ndistribute Licensed Programs externally to Licensee&rsquo;s End Users, either directly or\nthrough Licensee&rsquo;s distribution channels and methods, but only for use with Authorized\nApplications and not on a standalone basis.</li>\n</ol></li>\n</ol></li>\n\n\n<li><b><u>License Restrictions.</u></b>The Licensed Materials shall only be used as permitted by this Agreement. Any\nuse of the Licensed Materials not specifically authorized by this Agreement is\nprohibited.\n<ol>\n<li>\nWithout limiting the foregoing restriction, and except as authorized by this Agreement,\nLicensee shall <u>not</u>:\n\n<ol>\n\n<li>\nassign, sublicense, or otherwise transfer the Licensed Materials to any third party;</li>\n\n<li>\nreverse compile, disassemble, alter, add to, delete from, or otherwise modify Software\ndelivered to Licensee in Object Code form or in libraries in the Licensed\nMaterials;</li>\n\n<li>\npublish the Licensed Materials in any manner that would cause it to become part of the\npublic domain or otherwise become subject to the terms of an Open Source Software\nlicense;</li>\n\n<li>\nuse the Licensed Materials except in conjunction with Silicon Labs Devices;</li>\n\n<li>\ndistribute the Source Code form of Software to any third party, in whole or in part; or</li>\n\n<li>\nremove any copyright, trademark, patent or other proprietary notices from the Licensed\nMaterials or any portion thereof.</li>\n\n</ol></li>\n<li>\nLicensee shall not use the Licensed Materials in any way to further the development or\nimprovement of any product that does or would compete with any Silicon Labs Device.</li>\n\n<li>\nIf the Software is provided to demonstrate the capability of Silicon Labs Devices,\nit shall be used only for this purpose. Incorporation of the demonstration\nversion of Silicon Labs Software into Applications is solely at Licensee&rsquo;s risk\nand liability.</li>\n\n<li>\nAny subsequent distribution or transfer of the Licensed Programs to End Users shall\nremain subject to the terms and conditions of this Agreement. Whether by\nexecution of an end-user license agreement or other commercially reasonable\nmeans, Licensee shall ensure that its End Users&rsquo; use of the Licensed Programs shall only be\npermitted if they are incorporated into Authorized Applications. Licensee\nshall prohibit any further sublicensing, distribution, sale, marketing,\nreproduction, modification, reverse engineering or decompiling of the Licensed\nPrograms.</li>\n\n<li>\nLicensor may include features in the Software to restrict use of the Software that does\nnot comply with the terms of this Agreement.</li>\n\n</ol></li>\n<li><b><u>Unauthorized Use.</u></b>\nThe Licensed Materials are not licensed, designed, intended, authorized, or\nwarranted for Unauthorized Use. Licensee shall be solely and exclusively\nresponsible for any Unauthorized Uses by Licensee, Licensee&rsquo;s Authorized Subcontractors,\nLicensee&rsquo;s End Users or other sublicensees, and any Unauthorized Use by such Authorized\nSubcontractors, End Users or sublicensees, with or without the knowledge of\nLicensee, shall be attributed to Licensee. Licensee agrees to defend and indemnify\nSilicon Labs for all third-party claims and for all damages, costs and fees,\nincluding Silicon Labs&rsquo; attorneys&rsquo; fees, arising from any such Unauthorized Use\nof the Licensed Materials. </li>\n\n<li><b><u>Open Source Software.</u></b>\n<ol>\n<li>\nIf the Software includes any Open Source Software, such Software and the relevant\nOpen Source Software license under which such Software is licensed are\ndisclosed at <a href=\"http://www.silabs.com\">www.silabs.com</a>. All use of such\nOpen Source Software by Licensee is subject to the terms of the relevant open\nsource software license and Licensee&rsquo;s use of such Software is expressly\nconditioned upon Licensee&rsquo;s compliance with the term of such license.</li>\n\n<li>\nIf the Software is Silicon Labs Open Source Code, then the following provisions\napply:\n<ol>\n<li>\nSilicon Labs hereby grants to Licensee a perpetual, worldwide, non-exclusive,\nno-charge, royalty-free, irrevocable copyright license to reproduce, prepare\nDerivative Works of, publicly display, publicly perform, sublicense, and\ndistribute Silicon Labs Open Source Code and such Derivative Works in Source\nCode or Object Code form.</li>\n\n<li>\nSilicon Labs hereby grants to Licensee a perpetual, worldwide, non-exclusive,\nno-charge, royalty-free, irrevocable (except as stated in this section) patent\nlicense to make, have made, use, offer to sell, sell, import, and otherwise\ntransfer the Silicon Labs Open Source Code, where such license applies only to\nthose patent claims licensable by Silicon Labs that are necessarily infringed\nby Licensee&rsquo;s use of the Silicon Labs Open Source Code or by combination with\nany other device or software.</li>\n\n<li>\nLicensee may add Licensee&rsquo;s own copyright statement to Licensee&rsquo;s modifications of\nSilicon Labs Open Source Software and may provide additional or different\nlicense terms and conditions for use, reproduction, or distribution of such\nmodifications, or for any such Derivative Works as a whole, provided Licensee&rsquo;s\nuse, reproduction, and distribution of the Silicon Labs Open Source Software\notherwise complies with the conditions stated in this License. </li>\n\n<li>\nLicensee may reproduce and distribute copies of the Silicon Labs Open Source Code or\nDerivative Works thereof in any medium, with or without modifications, and in\nSource Code or Object Code form, provided that Licensee meets the following\nconditions: (a) Licensee must give any other recipients of the Silicon Labs\nOpen Source Code or Derivative Works a copy of this License; and (b) Licensee\nmust cause any modified files to carry prominent notices stating that Licensee\nchanged the files; and (c) Licensee must retain, in the Source Code form of any\nDerivative Works that Licensee distributes, all copyright, patent, trademark,\nand attribution notices from the Source Code form of the Silicon Labs Open\nSource Code, excluding those notices that do not pertain to any part of the\nDerivative Works; and (d) If the Silicon Labs Open Source Code includes a\n&ldquo;NOTICE&rdquo; text file as part of its distribution, then any Derivative\nWorks that Licensee distributes must include a readable copy of the attribution\nnotices contained within such NOTICE file, excluding those notices that do not\npertain to any part of the Derivative Works, in at least one of the following\nplaces: within a NOTICE text file distributed as part of the Derivative Works;\nwithin the Source Code form or documentation, if provided along with the\nDerivative Works; or, within a display generated by the Derivative Works, if\nand wherever such third-party notices normally appear. The contents of the\nNOTICE file are for informational purposes only and do not modify the License.\nLicensee may add Licensee&rsquo;s own attribution notices within Derivative Works\nthat Licensee distributes, alongside or as an addendum to the NOTICE text from\nthe Silicon Labs Open Source Code, provided that such additional attribution\nnotices cannot be construed as modifying the License.</li>\n</ol></li>\n\n<li>\nWith respect to Software that is not Open Source Software, Licensee shall not:\n<ol>\n<li>\ncause the Software to become subject to any Open Source Software license, including\nbut limited to the general public license (GPL) or the lesser general public\nlicense (LGPL);</li>\n\n<li>\ncause the Software to be disclosed into the public domain or to any third party\nexcept for those third parties to whom License is authorized to distribute\nLicensed Programs under Sections 4.1.6 or 4.2.5; or</li>\n\n<li>\ncause any part of the Software to become a derivative of any Open Source Software. </li>\n</ol></li>\n\n<li>\nLicensee shall not enable or permit any of its End Users to breach the provisions of\nthis Section 7, and shall include similar restrictive provisions in its end\nuser license agreement with such End Users. If Licensee breaches this Section 7,\nLicensee shall indemnify and hold Silicon Labs harmless from all costs, claims,\nsettlements and judgments incurred by Silicon Labs, including attorneys&rsquo; fees,\nin the process of defending, challenging and/or settling any demand, claim or\norder that the Software is subject to an Open Source Software license or must\nbe disclosed into the public domain or to any third party.</li>\n\n</ol></li>\n\n<li><b><u>Modified Open Source Software.</u></b>\n   Notwithstanding the terms of the Open Source Software license under which \n   the Open Source Software is licensed, the following terms apply to \n   modifications to such Open Source Software that constitute Modified Open \n   Source Software, as defined in this Agreement.  The following terms \n   apply regardless of how the Modified Open Source Software was \n   delivered to you.\n   <ol>\n   \n   <li>\n   You may not use Modified Open Source Software except for use with \n   Licensed Programs that are intended to operate in Authorized \n   Applications</li>\n   \n   <li>\n   You may not obscure, modify or remove copyright notices, files or \n   statements concerning ownership by Silicon Labs or reference to the \n   terms of this Agreement.</li>\n   \n   <li>\n   Subject to Sections 8.1 and 8.2:\n   <ol>\n   \n   <li>\n   You may copy and compile the Source Code of the Modified Open Source Software.</li>\n   <li>\n   You may reproduce the Object Code and Source Code versions of Modified \n   Open Source Software and distribute the same through multiple levels \n   of distributions, including to your External Manufacturers.</li>\n   <li>\n   You may modify Modified Open Source Software and create Derivative \n   Works of Modified Open Source Software.</li>\n           \n   </ol></li>\n</ol></li>\n\n<li><b><u>Third Party Software.</u></b>\n<ol>\n\n<li>\nIf the Software contains any Third Party Software, all use of such Third Party\nSoftware shall be subject to the terms of the license from such third party to\nSilicon Labs or a separate end user license agreement, if available, which may\nbe set forth in the header files of the Third Party Software. You agree to\ncomply with all terms and conditions for use of Third Party Software.</li>\n\n<li>\nSilicon Labs has licensed the BLE Homekit from Apple. You may not download or use the\nBLE Homekit software unless you have executed a MFi License with Apple.</li>\n\n<li>\nSilicon Labs does not make any endorsements or representations concerning Third Party \nSoftware and disclaims all implied warranties concerning Third Party Software. Third \nParty Software is offered &ldquo;AS IS&rdquo;.</li>\n\n</ol></li>\n\n<li><b><u>Inspection Rights.</u></b>\nSilicon Labs shall have the right, upon reasonable advance notice, to inspect\nLicensee&rsquo;s records and facilities with respect to the manufacture of Applications\nand to receive sample units of Applications in order to verify that such\nmanufacturing is within the scope of this Agreement, that there are appropriate\nsecurity procedures to protect Silicon Labs&rsquo; Confidential Information, and that\nLicensee is in compliance with its other obligations under this Agreement.</li>\n\n<li><b><u>No Other Licenses</u></b>.\nThe licenses granted under this Agreement are specifically set forth herein,\nand no licenses are granted by Silicon Labs to Licensee by implication or\nestoppel, and no licenses shall be granted by the parties&rsquo; course of doing\ndoing business. If you are licensing any Licensed Materials that run on or with \nan ancillary system or software, including but not limited to the Microsoft \nFAT file system, it is your responsibility to contact that third party to \nsecure the proper licensing for that system or software. You agree to fully \nindemnify and hold Silicon Labs harmless for any liability incurred \n(including attorney&rsquo;s fees) by your violation of this section.</li>\n\n<li><b><u>Beta Software</u></b> \nBeta Software, whether Object Code or Source Code (a) can only be used for \ninternal development, demonstration or testing purposes; (b) cannot be \nincluded within Licensee&rsquo;s or End-Users&rsquo; products that are \nintended for general release or high-volume production; and (c) cannot be \nused to support Z-Wave certification of Silicon Labs Devices.  \n</li>\n\n<li><b><u>Upgrades, Updates, New Versions</u></b>. \nAlthough it has no obligation to do so, Silicon Labs\nmay introduce updates, upgrades or new versions of the Software from time to\ntime. Licensee is under no obligation to download or use the updates, upgrade\nor new version of Software; however, if Licensee elects to do so, the licenses\ngranted to Licensee pursuant to this Agreement shall be deemed to include such updates,\nupgrades and new versions. In the case of any bug fix, improvement,\nwork-around, or correction made to the Software by Licensee, Licensee agrees to\nprovide to Silicon Labs, at no cost, the source code and any documentation\nreasonably required for Silicon Labs to be able to incorporate such changes\ninto the Silicon Labs Software. </li>\n\n<li><b><u>Deprecated Software</u></b>.\nDeprecated Software is not available for download and may have been replaced with \na newer, updated software solution.  You are advised to transition to the latest \nversions of the software to ensure you benefit from current features, improvements, \nand security updates provided by Silicon Labs. Silicon Labs does not recommend \nDeprecated Software for new designs. Silicon Labs does not offer any warranties \nor support on Deprecated Software and disclaims all implied warranties.  If \nDeprecated Software was Third Party Software, you may no longer download such \nsoftware from Silicon Labs.</li>\n\n<li><b><u>Feedback</u></b>.\nLicensee may from time to time provide Feedback to Silicon Labs regarding the \nSoftware, Documentation, or related Services. Silicon Labs may freely use, copy, \ndisclose, sublicense, distribute, and exploit such Feedback in any manner without\nany obligation, payment, or restriction based on intellectual property rights\nor otherwise. Licensee hereby grants to Silicon Labs perpetual, irrevocable, \nworldwide, royalty-free license to use any Feedback provided by Licensee in any\nmanner and in any form at the sole discretion of Silicon Labs. Silicon Labs is\nnot obligated to use any Feedback and does not waive any of its rights as a \nconsequence of its use or integration of Feedback.</li>\n\n<li><b><u>Regulatory Compliance</u></b>.\nSilicon Labs does not warrant that Software or any Application will comply with\nthe regulatory requirements of the United States or any other country.\nLicensee is solely responsible for complying with such requirements and for\nobtaining necessary government certifications, if any.</li>\n\n<li><b><u>License Fee and Royalties</u></b>.\nLicensee is not obligated to pay any license fees or royalties to Silicon Labs \nso long as Licensee complies with the terms of the licenses set forth herein.</li>\n\n<li><b><u>No Resale Fees</u></b>.\nLicensee may not directly or indirectly charge any fee or otherwise require or\naccept any type of monetary compensation solely for redistributing the Licensed\nMaterials which is in excess of any amount paid to Silicon Labs for the same\nLicensed Materials. This does not apply to the sale of hardware products\nhaving the Licensed Materials in object code form embedded within.</li>\n\n<li><b><u>Proprietary Rights</u></b>.\nAll rights and title in and to the Licensed Materials, including without\nlimitation, patents, copyrights, moral rights, trademarks and trade secret\nrights, belong to Silicon Labs or its licensors. Except for the rights\nexpressly granted herein, no other rights are granted to Licensee with respect\nto the Licensed Materials.</li>\n\n<li><b><u>Confidential Information</u></b>.\nYou agree that the Licensed Materials contain confidential information,\nincluding trade secrets, know-how and other information, that comprise the\nexclusive property of Silicon Labs or its licensors. During the period this\nAgreement is in effect and at all times after its termination, you shall\nmaintain the confidentiality of this information and shall not sell, license,\nsublicense, publish, display, distribute, disclose or otherwise make available\nthis information to any third party nor use such information except as\nauthorized by this Agreement. </li>\n\n<li><b><u>Limited Warranty and Remedies</u></b>. \nSilicon Labs warrants that, during the Warranty Period,\nthe Software will function substantially in accordance with the Documentation\nwhen used with Silicon Labs Devices and that the media on which the Software is\nfurnished will be free from defects in material and workmanship, under normal\nuse and service, when correctly installed and maintained. Silicon Labs does not\nwarrant that the functions in the Licensed Materials will meet Licensee&rsquo;s\nspecific requirements or that the operation of the Software will be\nuninterrupted or error free. Silicon Labs does not warrant that the Software\ndoes not contain any viruses or bugs. If Licensee notifies Silicon Labs,\nduring the Warranty Period, of a failure of the Software to conform to the\nlimited warranty stated in this section, Silicon Labs&rsquo; sole obligation, and\nLicensee&rsquo;s sole remedy, will be, at Silicon Labs&rsquo; sole discretion: (i)\nreplacement of the Software, or part thereof, with a functionally equivalent\nsoftware product or part, or (ii) repair of the Software. Without limiting any\nterm or condition stated in this Agreement, this warranty does not apply to any\nnonconformance caused by (A) improper or inadequate maintenance or calibration,\nor (B) software or interfacing equipment, parts or supplies not supplied by Silicon\nLabs or its authorized distributor, (C) modifications to the Software or (D)\nUnauthorized Use of the Software.</li>\n\n<li><b><u>WARRANTY DISCLAIMER</u>.\nEXCEPT AS PROVIDED ABOVE IN SECTION 21, THE LICENSED MATERIALS ARE PROVIDED &ldquo;AS IS&rdquo;\nWITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING, WITHOUT\nLIMITATION, ANY IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR\nPURPOSE, OR NON-INFRINGEMENT AND THE ENTIRE RISK AS TO THE QUALITY AND\nPERFORMANCE OF THE LICENSED MATERIALS IS WITH LICENSEE.  SILICON LABS DOES NOT\nWARRANT THAT THE LICENSED MATERIALS ARE FREE FROM DEFECTS THAT COULD CAUSE\nVULNERABILITY TO CYBER-ATTACK, DATA BREACH OR PRIVACY VIOLATIONS. SILICON LABS\nDISCLAIMS ALL LIABILITY RELATED TO LICENSEE&rsquo;S DATA THAT MAY BE RECEIVED, STORED\nOR USED BY SILICON LABS DEVICES OR SOFTWARE OR INTERCEPTED BY THIRD PARTIES.\nSILICON LABS DISCLAIMS ALL LIABILITY FOR DAMAGES CAUSED BY THIRD PARTIES,\nINCLUDING MACILICOUS USE OF, OR INTEFERENCE WITH TRANSMISSION OF, LICENSEE&rsquo;S\nDATA.</b></li>\n\n<li><b><u>LIMITATION OF LIABILITY</u>.\nSILICON LABS&rsquo; SOLE OBLIGATION OR LIABILITY UNDER THIS AGREEMENT IS THE \nREPAIR OR REPLACEMENT OF THE LICENSED MATERIALS ACCORDING TO THE LIMITED \nWARRANTY ABOVE. SILICON LABS HAS NO LIABILITY FOR ANY DAMAGES SUFFERED BY YOU \nBY REASON OF ANY ACT OR OMISSION OF SILICON LABS OR ANY OF ITS EMPLOYEES, \nCONTRACTORS, DIRECTORS, AGENCIES OR AFFILIATES, OR ARISING OUT OF OR IN \nCONNECTION WITH YOUR USE OF THE LICENSED MATERIALS, SILICON LABS&rsquo; \nPERFORMANCE UNDER THIS AGREEMENT OR ANY USE OF THE DOCUMENTATION, MEDIA, \nMANUALS, SPECIFICATIONS, INSTRUCTIONS OR PRINTED MATERIALS ACCOMPANYING THE \nLICENSED MATERIALS. IN NO EVENT SHALL SILICON LABS OR ANY OF ITS AFFILIATES \nOR SUPPLIERS BE LIABLE FOR CONSEQUENTIAL, SPECIAL, INCIDENTAL OR SIMILAR \nDAMAGES, SUCH AS (BUT NOT LIMITED TO) LOSS OF BUSINESS REVENUES, PROFITS \nOR SAVINGS OR LOSS OF DATA RESULTING FROM THE USE OR INABILITY TO USE THE \nLICENSED MATERIALS, LOSS OF BUSINESS OPPORTUNITIES, OR COSTS ASSOCIATED \nWITH DOWNTIME, EVEN IF SILICON LABS HAS BEEN ADVISED OF THE POSSIBILITY OF \nSUCH DAMAGES, OR FOR ANY CLAIM BY ANY THIRD PARTY. THIS INCLUDES, BUT IS NOT \nLIMITED TO, DAMAGES ARISING FROM THE FAILURE OF THE SILICON LABS DEVICE TO \nTRANSMIT DATA ARISING FROM A FAILURE OF THE SOFTWARE TO PERFORM IN SUBSTANTIAL \nACCORDANCE WITH THE DOCUMENTATION. IN NO EVENT SHALL THE TOTAL CUMULATIVE \nLIABILITY OF SILICON LABS TO LICENSEE FOR ALL MATTERS RELATED TO THE LICENSED \nMATERIALS EXCEED THE AMOUNT PAID BY LICENSEE TO SILICON LABS FOR SUCH LICENSED \nMATERIALS OR ONE UNITED STATES DOLLAR ($1.00 USD). YOU ACKNOWLEDGE THAT THE \nAMOUNT PAID BY YOU FOR THE LICENSED MATERIALS REFLECTS THIS ALLOCATION OF RISK.\n</b></li>\n\n<li><b><u>Data Collection</u></b>.\nTo the extent that Silicon Labs Devices collect, store or transfer Personal\nInformation, Silicon Labs may use such Personal Information for its own\ninternal purposes, including marketing Silicon Labs Devices to the user.&nbsp; Silicon\nLabs will not sell Personal Information to third parties.&nbsp; Silicon Labs\nDevices will not transfer Personal Information to other devices in a network or\nto third parties except to the extent necessary to perform the intended\nfunction of the Silicon Labs Device.&nbsp; Silicon Labs will not be liable to Licensee\nor Licensee&rsquo;s customers for (a) any intended transfer of Personal Information\ndescribed in the Documentation for the Silicon Labs Device; (b) any unintended\ntransfer of Personal Information or loss of data caused by any third parties or\nthird party devices or software, including hacking, malware, eavesdropping,\nman-in-the-middle attacks or other intentional acts; or (c) unauthorized access\nto or misuse of Personal Information by third parties.</li>\n\n<li><b><u>Term and Termination</u></b>.\nThis Agreement will take effect on the date the Licensed Materials are acquired\nby or delivered to Licensee, and will remain in effect unless terminated as\nprovided below. If you breach any of your obligations under this Agreement,\nthis Agreement will immediately and automatically terminate. You may terminate\nthis Agreement at any time by destroying all copies of the Licensed Materials.\nUpon termination of this Agreement, you shall immediately discontinue the use\nof the Licensed Materials and shall return or provide certification to Silicon\nLabs of the destruction of all copies of the Licensed Materials. You many keep\none copy of the Licensed Materials for archival (non-operational) purposes\nonly, so long as access to such copies is restricted. If the Agreement is\nterminated by Silicon Labs, you may continue to distribute copies of the\nSoftware already installed in finished inventory, but you may not make any\nadditional copies or install the Software in additional products. All\nprovisions of this Agreement relating to disclaimers of warranties, limitation\nof liability, remedies or damages, and Silicon Labs&rsquo; proprietary rights, shall\nsurvive any termination of this Agreement for any reason.</li>\n\n<li><b><u>Termination of License</u></b>.\nIf you institute patent litigation against Silicon Labs or any of its\nAffiliates (including a cross-claim or counterclaim in a lawsuit) alleging that\nthe Licensed Programs directly or indirectly infringe a patent of Licensee,\nthen any patent licenses granted to you under this Agreement for that Licensed\nProgram shall terminate as of the date such litigation is filed.</li>\n\n<li><b><u>Export Restrictions</u></b>.\nYou may not export or re-export the Software or any Licensed Programs, or any\ncopy thereof, in violation of any applicable laws or regulations. </li>\n\n<li>\n<b><u>Amendments</u></b>. This Agreement may\nbe amended unilaterally by Silicon Labs at any time. The most recent version\nof this Agreement supersedes and replaces all prior versions. In the event of\nany conflicting terms, the terms of the most recent version of this Agreement\nshall control. </li>\n\n<li><b><u>Miscellaneous</u></b>. This Agreement sets\nforth the entire agreement and understanding between the parties and neither\nparty shall be bound by any conditions, definitions, warranties, understandings\nor representations with respect to the subject matter hereof other than as\nprovided herein or as duly set forth on or after the date hereof in writing and\nsigned by a proper and duly authorized representative of the party to be bound\nthereby. The failure of any party at any time to require performance of any\nprovision of this Agreement shall in not affect the right of such party to\nenforce the terms of this Agreement at a later time. No waiver by any party of\nany condition or of any breach of any term contained in this Agreement, in any\none or more instances, shall be construed as a further or continuing waiver of\nany such condition or of any breach of any such term or any other term set\nforth in this Agreement. If any provision of this Agreement is unenforceable\nfor any reason, the remaining terms of the Agreement shall not be deemed invalid,\ninoperative, or unenforceable and, if possible, the unenforceable provision\nshall be modified or interpreted in a manner to make it enforceable. </li>\n\n<li><b><u>Governing Law</u></b>.\nThis Agreement shall be governed by the laws of the State of Texas, United\nStates of America, without regard to that state&rsquo;s conflicts of laws rules. The\n1980 United Nations Convention on Contracts for the International Sale of Goods\nshall not apply. In any dispute arising out of this Agreement, the parties each\nconsent to the exclusive personal jurisdiction and venue in the State and\nFederal courts located within Travis County, Texas, United States of America. All\ndisputes concerning this Agreement shall be resolved by binding arbitration in\nTravis County, Texas before a single arbitrator. The arbitration shall be\nadministered by JAMS pursuant to JAMS&rsquo; Streamlined Arbitration Rules and\nProcedures. The arbitration award shall include an award of attorneys&rsquo; fees to\nthe prevailing party. Judgment on the award may be entered in any court having\njurisdiction. This clause shall not preclude parties from seeking provisional\nremedies in aid of arbitration from a court of appropriate jurisdiction.</li>\n\n<li><b><u>Injunctive Relief</u></b>.\nThe copying, disclosure, or use of the Software in a manner inconsistent with\nany provision of this Agreement or the improper use of the Silicon Labs\ntrademarks may cause irreparable injury to Silicon Labs for which Silicon Labs\nmay not have an adequate remedy at law. Silicon Labs may be entitled to equitable\nrelief in court, including but not limited to temporary restraining orders,\npreliminary injunctions and permanent injunctions. </li>\n\n<li><b><u>Silicon Labs Trademarks</u></b>.\nSilicon Labs and the Silicon Labs logo are trademarks of Silicon Laboratories\nInc. in the United States and other countries. No use of the Silicon Labs\ntrademarks by Licensee is implied or consented to by Silicon Labs by entering\ninto this Agreement.</li>\n\n<li><b><u>Commercial Computer Software</u></b>.\nIf Licensee is an agency of the U.S. Government, the following will apply: The\nSoftware has been developed entirely at private expense, is regularly used for\nnongovernmental purposes and has been licensed to the public. The Software is a\n&ldquo;commercial item&rdquo; as that term is defined in 48 C.F.R. 2.101 (Oct. 1995),\nconsisting of &ldquo;commercial computer software&rdquo; and &ldquo;commercial computer software\ndocumentation&rdquo; as those terms are used in 48 C.F.R. 12.212 (Sept. 1995) or as\n&ldquo;commercial computer software&rdquo; as that term is defined in 48 C.F.R.\n252.227-7014 (June 1995) or any equivalent agency regulation or contract\nclause, whichever is applicable. Consistent with 48 C.F.R. 12.212 and 48 C.F.R.\n227.7202-1 through 227.7202-4 (June 1995), all U.S. Government agencies acquire\nonly those rights to the Software as are expressly set forth herein. </li>\n</ol>\n</div>\n\n</body>\n\n</html>\n", "id": 1, "title": "Master Software License Agreement"}]