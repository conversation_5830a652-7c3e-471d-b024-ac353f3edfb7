"use strict";(globalThis["webpackChunkinstaller"]=globalThis["webpackChunkinstaller"]||[]).push([[64],{6412:(e,t,a)=>{a.d(t,{Z:()=>l});a(9665);var s=a(8700);const l={methods:{installPackages(e,t){console.log("installPackages"),this.$store.commit("install/SET_FEATURE_AND_CATEGORIES",{featureId:e.featurePackageId,categories:t}),this.setupInstallRootPath(),this.dispatchLicenseCheck(e.featurePackageId,t).then((()=>{this.installPackagesAfterLicenses()})).catch((e=>this.installPackagesProblem(e)))},installMultiplePackages(e,t){console.log("installMutliplePackages"),this.$store.commit("install/SET_FEATURES_AND_CATEGORIES",{featureIds:e,categories:t}),this.setupInstallRootPath();var a={featureIds:e,categories:t};this.$store.dispatch("licenses/fetchLicensesForMultiplePackages",a).then((()=>{this.installPackagesAfterLicenses()})).catch((e=>this.installPackagesProblem(e)))},installSdkPackage(e,t,a,s){console.log("installPackages");let l=[];s.forEach((e=>{l.push({id:e})})),this.$store.commit("install/SET_SELECTED_SDK_INSTALL_CONFIG",{id:e,version:t,location:a,extensions:l}),this.setupInstallRootPath(),e.startsWith("gitrepo.")&&(e=e.slice("gitrepo.".length));let n=e.lastIndexOf("-");n>=0&&(e=e.substring(0,n)),this.dispatchLicenseCheck(e,["sdk","stack"]).then((()=>{this.installPackagesAfterLicenses()})).catch((e=>this.installPackagesProblem(e)))},setupInstallRootPath(){const e=this.$route.path;this.$store.commit("install/SET_PM_ROUTE_PATH",e),this.$store.commit("install/SET_INSTALL_OPERATION","Install")},dispatchLicenseCheck(e,t){let a=e+"&categories=";for(const s of t)a+=s+",";return this.$store.dispatch("licenses/fetchLicensesForPackageManagerPackages",a)},installPackagesAfterLicenses(){if(console.log("installPackagesAfterLicenses"),!0!==this.$store.state.licenses.allAccepted)return console.log("licenses.allAccepted !== true push to /ReviewLicenses"),this.$router.push({path:"/ReviewLicenses"});this.continuePackageManagerInstall()},installPackagesProblem(e){void 0!==e.response&&504===e.response.status&&(this.$store.commit("licenses/setDefaults",e),this.$router.push({path:"/ReviewLicenses"}))},continuePackageManagerInstall(){let e=this.$store.getters["install/getSdkInstallConfig"];if(e)return this.$store.dispatch("install/postInstallSDK",e);let t=0!=this.$store.getters["install/getPackageManagerFeatures"].length;console.log("continuePackageManagerInstall");const a=this.$store.getters[t?"install/getPackageManagerFeatures":"install/getPackageManagerFeature"],l=this.$store.getters["install/getPackageManagerCategories"],n='{ "installPackages": "'+(t?a.join(","):a)+'", "categories": "'+l+'" }';return console.log("continuePackageManagerInstall installPackagesArgs "+n),s.Z.postInstallPackages(n).catch((()=>this.$router.push({path:"/Installing"})))},unInstallPackages(e,t){const a=this.$route.path;this.$store.commit("install/SET_PM_ROUTE_PATH",a),this.$store.commit("install/SET_INSTALL_OPERATION","UnInstall");const l='{ "unInstallPackages": "'+e.featurePackageId+'", "categories": "'+t+'" }';return console.log("unInstallPackages unInstallPackagesArgs "+l),s.Z.postInstallPackages(l).catch((()=>this.$router.push({path:"/Installing"})))},continueInstall(){const e=this.$store.getters["install/getIsWizardInstall"];!0===e?this.continueWizardInstall():this.continuePackageManagerInstall()},cancelInstall(){s.Z.cancelInstall()},getSelectedPackagesAsQuery(){let e="";const t=this.$store.getters["wizardPackages/get"];for(const a of t)a.selected&&(e.length>0&&(e+="&"),e+=a.id);return e},installFromWizard(){let e=this.getSelectedPackagesAsQuery();if(this.$store.state.technologyTypes.types){const t=this.$store.state.technologyTypes.types;t.forEach((function(t){t.extensions&&t.extensions.forEach((function(t){t.selected&&(e+="&"+t.id)}))}))}this.$store.commit("install/SET_WIZARD_PACKAGE_QUERY",e),this.$store.commit("install/SET_INSTALL_OPERATION","Install"),this.$store.dispatch("licenses/fetchLicensesForPackages",e).then((()=>{this.installFromWizardAfterLicenses()})).catch((e=>this.installFromWizardProblem(e)))},continueWizardInstall(){const e=this.$store.getters["install/getWizardPackageQuery"];return s.Z.wizardStartInstall(e).catch((e=>this.installFromWizardProblem(e)))},installFromWizardAfterLicenses(){if(!0!==this.$store.state.licenses.allAccepted)return this.$router.push({path:"/ReviewLicenses"});this.continueWizardInstall()},installFromWizardProblem(e){void 0!==e.response&&504===e.response.status&&this.$router.push({path:"/Installing"})},installExtension(e,t,a){const s={sdkId:e,extensionId:t,extensionVersion:a};this.$store.dispatch("install/postInstallExtension",s)},unInstallExtension(e,t){const a={extensionId:t,sdkId:e};this.$store.dispatch("install/deleteExtension",a)}}}},5358:(e,t,a)=>{a.d(t,{Z:()=>s});const s={calculateHeightValue(e,t){var a=200,s=document.getElementById(e),l=document.getElementById(t);if(null===s||null===l)console.log("calculateHeight:  Unable to find relevant elements: Upper: "+s+" Lower: "+l);else{var n=l.getBoundingClientRect(),i=s.getBoundingClientRect();a=n.top-i.top}return a},calculateHeight(e,t){return this.calculateHeightValue(e,t)+"px"}}},1794:(e,t,a)=>{a.d(t,{Z:()=>s});const s={methods:{getInstallStartTitle:function(){return"InstallByTechnology"===this.$store.state.installOptions.InstallType||"ManageInstalledPackages"===this.$store.state.installOptions.InstallType?this.$t("SelectTechnologyType"):this.$t("SelectProducts")}}}},1255:(e,t,a)=>{a.d(t,{Z:()=>s});const s={methods:{getArgsForPkgAccess:function(){var e=this.$store.getters["installOptions/getSelectedInstallType"],t="";if("InstallByDevice"===e){t="selectedDevice&accessParameters=";var a=this.$store.getters["wizardProducts/getSelectedDevices"];t+=JSON.stringify(a)}else{var s=this.$store.getters["technologyTypes/getSelectedTechTypeIds"];t="selectedTechType&accessParameters=",t+=s}return t},fetchPackages:function(){var e=this.$store.getters["installOptions/getSelectedInstallType"];if("InstallByDevice"===e){var t=this.$store.getters["wizardProducts/getSelectedDevices"];this.$store.dispatch("wizardPackages/fetchPackagesForDevices",t).catch((()=>console.log("exception while fetching packages for devices")))}else if("InstallByTechnology"===e){for(var a="tech",s=this.$store.getters["technologyTypes/get"],l=0;l<s.length;l++)!0===s[l].selected&&(a.length>0&&(a+="&"),a+=s[l].id);this.$store.dispatch("wizardPackages/fetchPackages",a).catch((()=>console.log("exception while fetching packages for install type")))}}}}},1615:(e,t,a)=>{a.d(t,{Z:()=>s});const s={computed:{getUserName(){return"1"===this.$store.state.user.loggedIn?this.$store.state.user.name:this.$t("LogIn")},getLoggedInDescription(){return"1"===this.$store.state.user.loggedIn?this.$t("LogOut"):this.$t("LogIn")},getUserLoggedIn(){return"1"===this.$store.state.user.loggedIn}},methods:{}}},5056:(e,t,a)=>{a.d(t,{Z:()=>P});var s=a(9835),l=a(6970);const n={class:"q-pa-md justify-between",id:"licenseContainer"},i=["innerHTML"],o={class:"col-auto",id:"licenseSoftwareContainer"},c={class:"col-1",style:{height:"20px"},id:"licenseCheckboxContainer"};function r(e,t,a,r,d,g){const u=(0,s.up)("q-scroll-area"),p=(0,s.up)("q-separator"),h=(0,s.up)("q-expansion-item"),m=(0,s.up)("q-checkbox");return(0,s.wg)(),(0,s.iD)("div",n,[(0,s.Wm)(u,{class:"col-8",id:"licenseText",style:(0,l.j5)(g.getTextHeight)},{default:(0,s.w5)((()=>[(0,s._)("span",{class:"adjusted",innerHTML:g.getHTMLcontent},null,8,i)])),_:1},8,["style"]),(0,s._)("div",o,[(0,s.Wm)(p),(0,s.Wm)(h,{label:e.$t("LicenseSoftwareBound"),"expand-separator":"","switch-toggle-side":""},{default:(0,s.w5)((()=>[((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(a.license.softwareCovered,((e,t)=>((0,s.wg)(),(0,s.iD)("div",{key:t,class:"q-pm-lg q-px-lg"},(0,l.zw)(e),1)))),128))])),_:1},8,["label"])]),(0,s.Wm)(p),(0,s._)("div",c,[!1===a.disableAcceptance?((0,s.wg)(),(0,s.j4)(m,{key:0,modelValue:d.acceptAll,"onUpdate:modelValue":[t[0]||(t[0]=e=>d.acceptAll=e),t[1]||(t[1]=e=>g.onAcceptAllInput(a.license))],label:e.$t("LicenseAcceptAll"),"text-color":"primary",id:"licenseAcceptAllCheckbox"},null,8,["modelValue","label"])):(0,s.kq)("",!0),!1===a.disableAcceptance?((0,s.wg)(),(0,s.j4)(m,{key:1,modelValue:d.acceptThis,"onUpdate:modelValue":[t[2]||(t[2]=e=>d.acceptThis=e),t[3]||(t[3]=e=>g.onAcceptInput(a.license))],label:e.$t("LicenseAccept"),"text-color":"primary",id:"licenseAcceptCheckbox"},null,8,["modelValue","label"])):(0,s.kq)("",!0)])])}const d={name:"LicenseDisplay",props:{license:Object,onAccept:Function,onAcceptAll:Function,disableAcceptance:Boolean,containerSize:Number},data(){return{acceptThis:!1,acceptAll:!1,textHeight:"200px"}},created(){this.acceptThis=this.license.accepted,this.acceptAll=this.$store.state.licenses.allAccepted},mounted(){this.onSizeChanged(this.containerSize)},computed:{getTextHeight(){return"height: "+this.textHeight},getHTMLcontent(){return this.license.text}},methods:{onAcceptAllInput(e){this.onAcceptAll(e),this.acceptThis=this.license.accepted},onAcceptInput(e){this.onAccept(e),this.acceptAll=this.$store.state.licenses.allAccepted},onSizeChanged(e){var t=document.getElementById("licenseContainer");if(null!==t){var a=document.getElementById("licenseText"),s=a.getBoundingClientRect(),l=t.getBoundingClientRect(),n=s.top-l.top,i=document.getElementById("licenseSoftwareContainer"),o=document.getElementById("licenseCheckboxContainer"),c=i.clientHeight+o.clientHeight+n;c+=50;var r=e-c;this.textHeight=r+"px"}else console.log("License::watch:  Unable to find elements")}},watch:{containerSize:function(e){this.onSizeChanged(e)}}};var g=a(1639),u=a(6663),p=a(926),h=a(651),m=a(1221),k=a(9984),v=a.n(k);const f=(0,g.Z)(d,[["render",r],["__scopeId","data-v-2f3ffd46"]]),P=f;v()(d,"components",{QScrollArea:u.Z,QSeparator:p.Z,QExpansionItem:h.Z,QCheckbox:m.Z})},8139:(e,t,a)=>{a.d(t,{Z:()=>h});var s=a(9835),l=a(6970);const n={class:"big q-px-lg"};function i(e,t,a,i,o,c){const r=(0,s.up)("q-spinner"),d=(0,s.up)("q-inner-loading");return(0,s.wg)(),(0,s.iD)("div",null,[(0,s.Wm)(d,{showing:""},{default:(0,s.w5)((()=>[(0,s.Wm)(r,{size:"50px",color:"primary"})])),_:1}),(0,s._)("span",n,(0,l.zw)(e.$t("Loading")),1)])}const o={name:"LoadingAnimation"};var c=a(1639),r=a(854),d=a(3940),g=a(9984),u=a.n(g);const p=(0,c.Z)(o,[["render",i]]),h=p;u()(o,"components",{QInnerLoading:r.Z,QSpinner:d.Z})},2031:(e,t,a)=>{a.d(t,{Z:()=>f});var s=a(9835),l=a(6970);const n={class:"location_label"},i={class:"fit row inline no-wrap justify-start items-start content-start"},o={class:"q-ml-sm"},c={key:0,class:"message_label"};function r(e,t,a,r,d,g){const u=(0,s.up)("q-btn"),p=(0,s.up)("q-card-section");return(0,s.wg)(),(0,s.j4)(p,{class:"section"},{default:(0,s.w5)((()=>[(0,s._)("div",n,[(0,s._)("div",null,[(0,s.Uk)(" Location: "),(0,s._)("strong",null,(0,l.zw)(d.selectedLocation),1)])]),(0,s._)("div",i,[(0,s.Wm)(u,{class:"button-secondary",onClick:g.postBrowseLocation,label:"Browse"},null,8,["onClick"]),(0,s._)("div",o,[(0,s.Wm)(u,{class:"button-secondary",onClick:g.checkDefault,label:"Default"},null,8,["onClick"])])]),e.verifyLocation.message?((0,s.wg)(),(0,s.iD)("div",c,[(0,s._)("strong",null,"Error: "+(0,l.zw)(e.verifyLocation.message),1)])):(0,s.kq)("",!0)])),_:1})}var d=a(3100);const g={props:["defaultLocation"],data(){return{defaultSelected:!0,selectedLocation:this.defaultLocation,message:"Select SDK install location"}},created(){this.postVerifyInstallLocation()},computed:{...(0,d.rn)({browseLocation:e=>e.install.browseLocation,verifyLocation:e=>e.install.verifyLocation})},watch:{browseLocation(e){e&&(this.defaultLocation!==e&&(this.defaultSelected=!1),this.selectedLocation=e)},selectedLocation(){this.postVerifyInstallLocation()}},methods:{postBrowseLocation(){let e={location:this.selectedLocation,message:this.message};this.$store.dispatch("install/postBrowseLocation",e)},postVerifyInstallLocation(){let e={location:this.selectedLocation};this.$store.dispatch("install/postVerifyInstallLocation",e)},checkDefault(e){e&&(this.selectedLocation=this.defaultLocation,this.$store.commit("install/SET_BROWSE_LOCATION",{browseLocation:""}))}}};var u=a(1639),p=a(3190),h=a(8879),m=a(9984),k=a.n(m);const v=(0,u.Z)(g,[["render",r],["__scopeId","data-v-39cecd4e"]]),f=v;k()(g,"components",{QCardSection:p.Z,QBtn:h.Z})},5836:(e,t,a)=>{a.d(t,{Z:()=>h});var s=a(9835);function l(e,t,a,l,n,i){const o=(0,s.up)("q-separator"),c=(0,s.up)("q-step"),r=(0,s.up)("q-stepper");return(0,s.wg)(),(0,s.iD)("div",null,[(0,s.Wm)(o),(0,s.Wm)(r,{"alternative-":"",flat:"",modelValue:a.onStep,class:"navigationProgressStyle"},{default:(0,s.w5)((()=>[(0,s.Wm)(c,{name:"1",done:a.onStep>1,icon:1===a.onStep?e.mdiRecordCircle:e.mdiCircleMedium,color:"primary",title:i.getInstallStartTitle()},null,8,["done","icon","title"]),(0,s.Wm)(c,{name:"2",color:2===a.onStep?"primary":"","done-color":"primary",title:e.$t("SelectDevelopmentPackages"),done:a.onStep>2,icon:2===a.onStep?e.mdiRecordCircle:e.mdiCircleMedium},null,8,["color","title","done","icon"]),(0,s.Wm)(c,{name:"3",color:3===a.onStep?"primary":"","done-color":"primary",title:e.$t("ReviewLicenses"),done:a.onStep>3,icon:3===a.onStep?e.mdiRecordCircle:e.mdiCircleMedium},null,8,["color","title","done","icon"])])),_:1},8,["modelValue"]),(0,s.Wm)(o)])}var n=a(2670);const i={name:"NavigationProgess",props:{onStep:Number},created(){this.mdiCircleMedium=n.Pa3,this.mdiRecordCircle=n.gzC},methods:{getInstallStartTitle:function(){return"InstallByTechnology"===this.$store.state.installOptions.InstallType||"ManageInstalledPackages"===this.$store.state.installOptions.InstallType?this.$t("SelectTechnologyType"):this.$t("SelectProducts")}}};var o=a(1639),c=a(926),r=a(8225),d=a(6017),g=a(9984),u=a.n(g);const p=(0,o.Z)(i,[["render",l],["__scopeId","data-v-7a7c7010"]]),h=p;u()(i,"components",{QSeparator:c.Z,QStepper:r.Z,QStep:d.Z})},2127:(e,t,a)=>{a.d(t,{Z:()=>f});var s=a(9835),l=a(6970);const n={class:"q-gutter-md"};function i(e,t,a,i,o,c){const r=(0,s.up)("q-item-label"),d=(0,s.up)("q-btn"),g=(0,s.up)("q-space"),u=(0,s.up)("q-toolbar"),p=(0,s.up)("q-footer");return(0,s.wg)(),(0,s.iD)("div",null,[(0,s.Wm)(p,{bordered:"",class:"navigationToolStyle",id:"navigationToolbar",ref:"navToolbar"},{default:(0,s.w5)((()=>[(0,s.Wm)(u,null,{default:(0,s.w5)((()=>[(0,s.Wm)(d,{onClick:c.onCancel,flat:"","no-caps":"","text-color":"primary"},{default:(0,s.w5)((()=>[(0,s.Wm)(r,null,{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(e.$t("Cancel")),1)])),_:1})])),_:1},8,["onClick"]),(0,s.Wm)(g),(0,s._)("div",n,[!0===a.backVisible?((0,s.wg)(),(0,s.j4)(d,{key:0,onClick:c.onBack,outline:"","no-caps":"","text-color":"primary"},{default:(0,s.w5)((()=>[(0,s.Wm)(r,null,{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(e.$t("Back")),1)])),_:1})])),_:1},8,["onClick"])):(0,s.kq)("",!0),a.nextVisible?((0,s.wg)(),(0,s.j4)(d,{key:1,onClick:a.onNext,unelevated:"","no-caps":"",color:!1===a.nextEnabled?"grey-4":"primary",disable:!1===a.nextEnabled},{default:(0,s.w5)((()=>[(0,s.Wm)(r,null,{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(e.$t("Next")),1)])),_:1})])),_:1},8,["onClick","color","disable"])):(0,s.kq)("",!0)])])),_:1})])),_:1},512)])}var o=a(8700);const c={name:"NavigationToolbar",props:{backVisible:Boolean,nextEnabled:Boolean,onNext:Function,nextVisible:{type:Boolean,default:!0},backStepCount:{type:Number,default:1}},computed:{isNextDisabled(){return!1===this.nextEnabled}},methods:{onCancel(){return o.Z.userQuit()},onBack(){return this.$router.go(-this.backStepCount)}}};var r=a(1639),d=a(1378),g=a(1663),u=a(8879),p=a(3115),h=a(136),m=a(9984),k=a.n(m);const v=(0,r.Z)(c,[["render",i],["__scopeId","data-v-6b4d0358"]]),f=v;k()(c,"components",{QFooter:d.Z,QToolbar:g.Z,QBtn:u.Z,QItemLabel:p.Z,QSpace:h.Z})},6664:(e,t,a)=>{a.d(t,{Z:()=>L});var s=a(9835),l=a(6970);const n={key:0},i={key:1},o={class:"tile"},c={class:"row"},r=["src"],d={class:"label"},g={key:0,class:"right tile_disabled"},u={key:1,class:"right"},p={key:0},h={key:1},m={class:"description"};function k(e,t,a,k,v,f){const P=(0,s.up)("WrapperPackageContainer"),w=(0,s.up)("q-badge"),b=(0,s.up)("q-btn"),I=(0,s.up)("ReleaseNotes");return(0,s.wg)(),(0,s.iD)("div",null,[a.studioPackage.hasIncludedPkgIds?((0,s.wg)(),(0,s.iD)("div",n,[(0,s.Wm)(P,{categoryPackage:a.studioPackage,categories:a.categories},null,8,["categoryPackage","categories"])])):((0,s.wg)(),(0,s.iD)("div",i,[(0,s._)("div",o,[(0,s._)("div",{class:(0,l.C_)(["row",{tile_negative:a.studioPackage.isInstalled&&!a.studioPackage.updateable}])},[(0,s._)("div",c,[(0,s._)("div",null,[(0,s._)("img",{src:a.studioPackage.imageUrl,alt:""},null,8,r)]),(0,s._)("div",d,(0,l.zw)(a.studioPackage.label),1),a.studioPackage.updateable?((0,s.wg)(),(0,s.j4)(w,{key:0,color:"red",align:"top",class:"self-start"},{default:(0,s.w5)((()=>[(0,s.Uk)(" 1 ")])),_:1})):(0,s.kq)("",!0)]),!f.showInstall(a.studioPackage)||!e.updatePackages.productUpdates&&a.studioPackage.installEnabled?f.showInstall(a.studioPackage)?((0,s.wg)(),(0,s.iD)("div",u,["Install"===a.studioPackage.installState||"Update"===a.studioPackage.installState?((0,s.wg)(),(0,s.iD)("div",p,[(0,s.Wm)(b,{id:a.studioPackage.label,onClick:t[0]||(t[0]=e=>f.install(a.studioPackage,a.categories)),"no-caps":"",label:a.studioPackage.installState,class:"button-secondary"},null,8,["id","label"])])):((0,s.wg)(),(0,s.iD)("div",h,[(0,s.Wm)(b,{id:a.studioPackage.label,onClick:t[1]||(t[1]=e=>f.confirmUnInstall(a.studioPackage,a.categories)),"no-caps":"",label:a.studioPackage.installState,class:"button-secondary"},null,8,["id","label"])]))])):(0,s.kq)("",!0):((0,s.wg)(),(0,s.iD)("div",g,[(0,s.Wm)(b,{disable:"","no-caps":"",label:a.studioPackage.installState},null,8,["label"])]))],2),(0,s._)("div",m,(0,l.zw)(a.studioPackage.desc),1),(0,s.Wm)(I,{releaseNotes:a.studioPackage.releaseNotes},null,8,["releaseNotes"])])]))])}var v=a(819),f=a(3100),P=a(9858),w=a(6412);const b={name:"Package",mixins:[w.Z],props:["studioPackage","categories"],components:{WrapperPackageContainer:P.Z,ReleaseNotes:v.Z},methods:{install(e,t){console.log("From Package.vue install method : "),console.log("studioPackage "+e),console.log("categories : "+t),this.installPackages(e,t)},showInstall(e){return!("installVisible"in e)||e.installVisible},confirmUnInstall(e,t){var a=this.$t("ConfirmUninstallStart")+e.label+this.$t("ConfirmUninstallEnd");this.$q.dialog({title:this.$t("UninstallPackage"),message:a,cancel:!0,persistent:!0}).onOk((()=>{this.unInstallPackages(e,t)})).onOk((()=>{})).onCancel((()=>{})).onDismiss((()=>{}))}},computed:{...(0,f.rn)({updatePackages:e=>e.updatePackages.updatePackages})}};var I=a(1639),y=a(990),_=a(8879),C=a(9984),S=a.n(C);const $=(0,I.Z)(b,[["render",k],["__scopeId","data-v-cddcac30"]]),L=$;S()(b,"components",{QBadge:y.Z,QBtn:_.Z})},3571:(e,t,a)=>{a.d(t,{Z:()=>u});var s=a(9835);function l(e,t,a,l,n,i){const o=(0,s.up)("q-spinner"),c=(0,s.up)("q-inner-loading");return(0,s.wg)(),(0,s.iD)("div",null,[(0,s.Wm)(c,{showing:""},{default:(0,s.w5)((()=>[(0,s.Wm)(o,{size:"150px",color:"primary"})])),_:1})])}const n={name:"PackageManagerLoadingAnimation"};var i=a(1639),o=a(854),c=a(3940),r=a(9984),d=a.n(r);const g=(0,i.Z)(n,[["render",l]]),u=g;d()(n,"components",{QInnerLoading:o.Z,QSpinner:c.Z})},819:(e,t,a)=>{a.d(t,{Z:()=>f});var s=a(9835),l=a(6970),n=a(1957);const i={key:0},o={class:"row"},c={class:"format"};function r(e,t,a,r,d,g){const u=(0,s.up)("q-icon"),p=(0,s.up)("q-slide-transition");return a.releaseNotes?((0,s.wg)(),(0,s.iD)("div",i,[(0,s._)("div",null,[(0,s._)("div",null,[(0,s._)("div",o,[(0,s._)("div",null,[(0,s.Wm)(u,{class:"iconClass pointer",onClick:t[0]||(t[0]=e=>d.releaseNotesExpanded=!d.releaseNotesExpanded),name:d.releaseNotesExpanded?e.mdiMenuDown:e.mdiMenuRight},null,8,["name"])]),(0,s._)("div",{class:"releaseLabel",onClick:t[1]||(t[1]=e=>d.releaseNotesExpanded=!d.releaseNotesExpanded)}," Release Notes ")])])]),(0,s.Wm)(p,null,{default:(0,s.w5)((()=>[(0,s.wy)((0,s._)("div",null,[(0,s._)("div",c,(0,l.zw)(a.releaseNotes),1)],512),[[n.F8,d.releaseNotesExpanded]])])),_:1})])):(0,s.kq)("",!0)}var d=a(2670);const g={name:"ReleaseNotes",props:["releaseNotes"],data(){return{releaseNotesExpanded:!1}},created(){this.mdiMenuRight=d.ozb,this.mdiMenuDown=d.iW9}};var u=a(1639),p=a(2857),h=a(9003),m=a(9984),k=a.n(m);const v=(0,u.Z)(g,[["render",r],["__scopeId","data-v-1a143bc2"]]),f=v;k()(g,"components",{QIcon:p.Z,QSlideTransition:h.Z})},8205:(e,t,a)=>{a.d(t,{Z:()=>u});var s=a(9835);const l=e=>((0,s.dD)("data-v-7c53f39d"),e=e(),(0,s.Cn)(),e),n={class:"col col-gray"},i=l((()=>(0,s._)("br",null,null,-1)));function o(e,t,a,l,o,c){const r=(0,s.up)("Package");return(0,s.wg)(),(0,s.iD)("div",n,[((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(a.studioPackages.categoryPackages,(e=>((0,s.wg)(),(0,s.iD)("div",{key:e.category},[((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(e.packages,(e=>((0,s.wg)(),(0,s.iD)("div",{key:e.id},[(0,s.Wm)(r,{studioPackage:e,categories:o.categories},null,8,["studioPackage","categories"]),i])))),128))])))),128))])}var c=a(6664);const r={name:"ToolContainer",props:["studioPackages"],data(){return{categories:["tool","toolchain"]}},components:{Package:c.Z}};var d=a(1639);const g=(0,d.Z)(r,[["render",o],["__scopeId","data-v-7c53f39d"]]),u=g},4853:(e,t,a)=>{a.d(t,{Z:()=>C});var s=a(9835),l=a(6970);const n=e=>((0,s.dD)("data-v-6a4fcbce"),e=e(),(0,s.Cn)(),e),i={key:0},o={class:"headerCol"},c={class:"sdkHeaderTile"},r={class:"row"},d={class:"colorClass"},g={class:"colorClass row"},u=n((()=>(0,s._)("div",{class:"verticalLine"},null,-1))),p={class:"label"},h=n((()=>(0,s._)("a",{href:"#/PackageManager/"},"Product Updates",-1))),m=n((()=>(0,s._)("br",null,null,-1)));function k(e,t,a,n,k,v){const f=(0,s.up)("q-icon");return(0,s.wg)(),(0,s.iD)("div",null,[e.updatePackages.productUpdates&&!a.isAsset||a.isAsset&&e.updatePackages.assetUpdates?((0,s.wg)(),(0,s.iD)("div",i,[(0,s._)("div",o,[(0,s._)("div",c,[(0,s._)("div",r,[(0,s._)("div",d,[(0,s._)("div",g,[e.mdiAlert?((0,s.wg)(),(0,s.j4)(f,{key:0,class:"iconLockClass",name:e.mdiAlert},null,8,["name"])):(0,s.kq)("",!0),u])]),(0,s._)("div",p,[(0,s.Uk)(" Please install Studio updates on the "),h,(0,s.Uk)(" tab before installing "+(0,l.zw)(a.msg)+". ",1)])])])]),m])):(0,s.kq)("",!0)])}var v=a(3100),f=a(2670);const P={name:"UpdateMessage",props:["msg","isAsset"],computed:{...(0,v.rn)({updatePackages:e=>e.updatePackages.updatePackages})},created(){this.mdiAlert=f.jZI}};var w=a(1639),b=a(2857),I=a(9984),y=a.n(I);const _=(0,w.Z)(P,[["render",k],["__scopeId","data-v-6a4fcbce"]]),C=_;y()(P,"components",{QIcon:b.Z})},9858:(e,t,a)=>{a.d(t,{Z:()=>B});var s=a(9835),l=a(6970);const n=e=>((0,s.dD)("data-v-40c1f558"),e=e(),(0,s.Cn)(),e),i={class:"card"},o=n((()=>(0,s._)("br",null,null,-1))),c={class:"tile"},r={class:"subTile"},d={class:"row"},g={class:"childIndent"},u={class:"description"},p={key:0,class:"right description"},h={class:"childIndent"},m=n((()=>(0,s._)("br",null,null,-1)));function k(e,t,a,n,k,v){const f=(0,s.up)("EarlyAccessHeader"),P=(0,s.up)("q-icon"),w=(0,s.up)("ReleaseNotes");return(0,s.wg)(),(0,s.iD)("div",i,[(0,s.Wm)(f,{studioPackage:a.categoryPackage,categories:a.categories},null,8,["studioPackage","categories"]),o,(0,s._)("div",c,[((0,s.wg)(!0),(0,s.iD)(s.HY,null,(0,s.Ko)(a.categoryPackage.packages,(t=>((0,s.wg)(),(0,s.iD)("div",{key:t.id},[(0,s._)("div",r,[(0,s._)("div",d,[(0,s._)("div",g,[e.mdiPackageVariant?((0,s.wg)(),(0,s.j4)(P,{key:0,class:"iconLockClass",name:e.mdiPackageVariant},null,8,["name"])):(0,s.kq)("",!0)]),(0,s._)("div",u,(0,l.zw)(t.label),1),t.isInstalled?((0,s.wg)(),(0,s.iD)("div",p," Installed ")):(0,s.kq)("",!0)]),(0,s._)("div",h,[(0,s.Wm)(w,{releaseNotes:t.releaseNotes},null,8,["releaseNotes"])])]),m])))),128))])])}var v=a(819);const f={class:"eaHeaderTile"},P={class:"row"},w=["src"],b={class:"label"},I={key:0,class:"right tile_disabled"},y={key:1,class:"right"},_={key:0},C={key:1};function S(e,t,a,n,i,o){const c=(0,s.up)("q-btn");return(0,s.wg)(),(0,s.iD)("div",f,[(0,s._)("div",{class:(0,l.C_)(["row",{tile_negative:a.studioPackage.isInstalled}])},[(0,s._)("div",P,[(0,s._)("img",{src:a.studioPackage.imageUrl,alt:" "},null,8,w),(0,s._)("div",b,(0,l.zw)(a.studioPackage.category),1)]),e.updatePackages.productUpdates||!a.studioPackage.installEnabled?((0,s.wg)(),(0,s.iD)("div",I,[(0,s.Wm)(c,{disable:"","no-caps":"",label:a.studioPackage.installState},null,8,["label"])])):((0,s.wg)(),(0,s.iD)("div",y,["Install"===a.studioPackage.installState||"Update"===a.studioPackage.installState?((0,s.wg)(),(0,s.iD)("div",_,[(0,s.Wm)(c,{id:a.studioPackage.labelText,onClick:t[0]||(t[0]=e=>o.install(a.studioPackage,a.categories)),"no-caps":"",class:"button-secondary",label:a.studioPackage.installState},null,8,["id","label"])])):((0,s.wg)(),(0,s.iD)("div",C,[(0,s.Wm)(c,{id:a.studioPackage.labelText,onClick:t[1]||(t[1]=e=>o.confirmUnInstall(a.studioPackage,a.categories)),"no-caps":"",class:"button-secondary",label:a.studioPackage.installState},null,8,["id","label"])]))]))],2)])}var $=a(2670),L=a(3100),T=a(6412);const Z={props:["studioPackage","categories"],mixins:[T.Z],name:"EarlyAccessHeader",created(){this.mdiLockOpen=$.CE0,this.mdiCheckBold=$.YKm,this.mdiMenuRight=$.ozb,this.mdiMenuDown=$.iW9},methods:{install(e,t){console.log("From EarlyAccessHeader.vue install method : "),console.log("studioPackage "+e),console.log("categories : "+t),this.installPackages(e,t)},confirmUnInstall(e,t){var a=this.$t("ConfirmUninstallStart")+e.label+this.$t("ConfirmUninstallEnd");this.$q.dialog({title:this.$t("UninstallPackage"),message:a,cancel:!0,persistent:!0}).onOk((()=>{this.unInstallPackages(e,t)}))}},computed:{...(0,L.rn)({updatePackages:e=>e.updatePackages.updatePackages})}};var A=a(1639),x=a(8879),W=a(9984),E=a.n(W);const D=(0,A.Z)(Z,[["render",S],["__scopeId","data-v-07ce3a76"]]),q=D;E()(Z,"components",{QBtn:x.Z});const U={name:"EarlyAccessContainer",props:["categoryPackage","categories"],components:{EarlyAccessHeader:q,ReleaseNotes:v.Z},data(){return{expanded:!0}},created(){this.mdiPackageVariant=$.B3I}};var z=a(2857);const N=(0,A.Z)(U,[["render",k],["__scopeId","data-v-40c1f558"]]),B=N;E()(U,"components",{QIcon:z.Z})},1257:(e,t,a)=>{a.d(t,{Z:()=>_});var s=a(9835),l=a(6970);const n=["title"],i={class:"card-content"},o={class:"action-content"};function c(e,t,a,c,r,d){const g=(0,s.up)("q-space"),u=(0,s.up)("q-tooltip"),p=(0,s.up)("q-btn"),h=(0,s.up)("q-bar"),m=(0,s.up)("q-separator"),k=(0,s.up)("q-checkbox"),v=(0,s.up)("q-card-actions"),f=(0,s.up)("q-card"),P=(0,s.Q2)("close-popup");return(0,s.wg)(),(0,s.j4)(f,null,{default:(0,s.w5)((()=>[(0,s.Wm)(h,null,{default:(0,s.w5)((()=>[(0,s._)("div",{class:"title-bar",title:a.title},(0,l.zw)(a.title),9,n),(0,s.Wm)(g),(0,s.wy)(((0,s.wg)(),(0,s.j4)(p,{dense:"",flat:"",icon:r.closeIcon},{default:(0,s.w5)((()=>[(0,s.Wm)(u,{"content-class":"bg-white text-primary"},{default:(0,s.w5)((()=>[(0,s.Uk)((0,l.zw)(e.$t("Close")),1)])),_:1})])),_:1},8,["icon"])),[[P]])])),_:1}),(0,s._)("div",i,[(0,s.WI)(e.$slots,"default",{},void 0,!0)]),(0,s._)("div",o,[(0,s.Wm)(m),(0,s.Wm)(v,{align:"right"},{default:(0,s.w5)((()=>[a.showUserConfirmation?((0,s.wg)(),(0,s.j4)(k,{key:0,id:"checkbox-id",color:"primary",modelValue:r.userConfirmation,"onUpdate:modelValue":t[0]||(t[0]=e=>r.userConfirmation=e),label:"I understand the consequences",style:{"padding-right":"10px","font-weight":"bold"}},null,8,["modelValue"])):(0,s.kq)("",!0),a.cancelButton?(0,s.wy)(((0,s.wg)(),(0,s.j4)(p,{key:1,flat:"",label:e.$t("Cancel"),color:"primary"},null,8,["label"])),[[P]]):(0,s.kq)("",!0),(0,s.wy)((0,s.Wm)(p,{id:"confirmBtn-id",label:a.confirmLabel,color:"primary",disabled:a.confirmDisabled||!r.userConfirmation&&a.showUserConfirmation,onClick:d.confirmClicked},null,8,["label","disabled","onClick"]),[[P]])])),_:1})])])),_:3})}var r=a(2670);const d={props:{title:{type:String},cancelButton:{type:Boolean,default:!0},confirmLabel:{type:String,default:"Finish"},confirmDisabled:{type:Boolean,default:!1},showUserConfirmation:{type:Boolean,default:!1}},data(){return{selectedVersion:this.currentVersion,closeIcon:r.r5M,userConfirmation:!1}},methods:{confirmClicked(){this.$emit("confirmClicked")}}};var g=a(1639),u=a(4458),p=a(4526),h=a(136),m=a(8879),k=a(6858),v=a(926),f=a(1821),P=a(1221),w=a(2146),b=a(9984),I=a.n(b);const y=(0,g.Z)(d,[["render",c],["__scopeId","data-v-1ac96e5a"]]),_=y;I()(d,"components",{QCard:u.Z,QBar:p.Z,QSpace:h.Z,QBtn:m.Z,QTooltip:k.Z,QSeparator:v.Z,QCardActions:f.Z,QCheckbox:P.Z}),I()(d,"directives",{ClosePopup:w.Z})},4480:(e,t,a)=>{a.d(t,{Z:()=>f});var s=a(9835),l=a(6970);const n={class:"tile"},i={class:"right-section"},o={class:"tile-row"},c=["src"],r={class:"title"},d={class:"tile-row body-row"},g={class:"primary-action"},u={class:"tile-row body-row"},p={class:"tile-row body-row"};function h(e,t,a,h,m,k){return(0,s.wg)(),(0,s.iD)("div",n,[(0,s.WI)(e.$slots,"left-section",{},void 0,!0),(0,s._)("div",i,[(0,s._)("div",o,[(0,s._)("img",{src:a.icon,alt:""},null,8,c),(0,s._)("div",r,[(0,s.Uk)((0,l.zw)(a.title)+" ",1),(0,s.WI)(e.$slots,"title",{},void 0,!0)])]),(0,s._)("div",d,[(0,s._)("span",null,(0,l.zw)(a.description),1),(0,s._)("div",g,[(0,s.WI)(e.$slots,"primary-action",{},void 0,!0)])]),(0,s._)("div",u,[(0,s.WI)(e.$slots,"default",{},void 0,!0)]),(0,s._)("div",p,[(0,s.WI)(e.$slots,"secondary-action",{},void 0,!0)])])])}const m={props:["icon","title","description"]};var k=a(1639);const v=(0,k.Z)(m,[["render",h],["__scopeId","data-v-4356baba"]]),f=v}}]);