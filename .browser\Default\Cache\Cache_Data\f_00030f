{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Legacy Advertiser<span id=\"legacy-advertiser\" class=\"self-anchor\"><a class=\"perm\" href=\"#legacy-advertiser\">#</a></span></h1><p style=\"color:inherit\">Legacy Advertiser. </p><p style=\"color:inherit\">The commands and events in this class are related to legacy advertising functionalities. </p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-legacy-advertiser-connection-mode-t\">sl_bt_legacy_advertiser_connection_mode_t</a> {</div><div class=\"enum\">sl_bt_legacy_advertiser_non_connectable = 0x0</div><div class=\"enum\">sl_bt_legacy_advertiser_connectable = 0x2</div><div class=\"enum\">sl_bt_legacy_advertiser_scannable = 0x3</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values define the available connection modes of undirected legacy advertising. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-legacy-advertiser-directed-connection-mode-t\">sl_bt_legacy_advertiser_directed_connection_mode_t</a> {</div><div class=\"enum\">sl_bt_legacy_advertiser_high_duty_directed_connectable = 0x1</div><div class=\"enum\">sl_bt_legacy_advertiser_low_duty_directed_connectable = 0x5</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values define the available connection modes of directed legacy advertising. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-legacy-advertiser-set-data\">sl_bt_legacy_advertiser_set_data</a>(uint8_t advertising_set, uint8_t type, size_t data_len, const uint8_t *data)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-legacy-advertiser-generate-data\">sl_bt_legacy_advertiser_generate_data</a>(uint8_t advertising_set, uint8_t discover)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-legacy-advertiser-start\">sl_bt_legacy_advertiser_start</a>(uint8_t advertising_set, uint8_t connect)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-legacy-advertiser-start-directed\">sl_bt_legacy_advertiser_start_directed</a>(uint8_t advertising_set, uint8_t connect, bd_addr peer_addr, uint8_t peer_addr_type)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-legacy-advertiser-set-data-id\">sl_bt_cmd_legacy_advertiser_set_data_id</a> 0x00560020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-legacy-advertiser-generate-data-id\">sl_bt_cmd_legacy_advertiser_generate_data_id</a> 0x01560020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-legacy-advertiser-start-id\">sl_bt_cmd_legacy_advertiser_start_id</a> 0x02560020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-legacy-advertiser-start-directed-id\">sl_bt_cmd_legacy_advertiser_start_directed_id</a> 0x03560020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-legacy-advertiser-set-data-id\">sl_bt_rsp_legacy_advertiser_set_data_id</a> 0x00560020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-legacy-advertiser-generate-data-id\">sl_bt_rsp_legacy_advertiser_generate_data_id</a> 0x01560020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-legacy-advertiser-start-id\">sl_bt_rsp_legacy_advertiser_start_id</a> 0x02560020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-legacy-advertiser-start-directed-id\">sl_bt_rsp_legacy_advertiser_start_directed_id</a> 0x03560020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>sl_bt_legacy_advertiser_connection_mode_t<span id=\"sl-bt-legacy-advertiser-connection-mode-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-legacy-advertiser-connection-mode-t\">#</a></span></h3><blockquote>sl_bt_legacy_advertiser_connection_mode_t</blockquote><p style=\"color:inherit\">These values define the available connection modes of undirected legacy advertising. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_legacy_advertiser_non_connectable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Undirected non-connectable and non-scannable legacy advertising </p></td></tr><tr><td class=\"fieldname\">sl_bt_legacy_advertiser_connectable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) Undirected connectable and scannable legacy advertising </p></td></tr><tr><td class=\"fieldname\">sl_bt_legacy_advertiser_scannable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x3) Undirected scannable and non-connectable legacy advertising </p></td></tr></tbody></table><br><div>Definition at line <code>2367</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_legacy_advertiser_directed_connection_mode_t<span id=\"sl-bt-legacy-advertiser-directed-connection-mode-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-legacy-advertiser-directed-connection-mode-t\">#</a></span></h3><blockquote>sl_bt_legacy_advertiser_directed_connection_mode_t</blockquote><p style=\"color:inherit\">These values define the available connection modes of directed legacy advertising. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_legacy_advertiser_high_duty_directed_connectable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) High duty cycle directed connectable legacy advertising </p></td></tr><tr><td class=\"fieldname\">sl_bt_legacy_advertiser_low_duty_directed_connectable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x5) Low duty cycle directed connectable legacy advertising </p></td></tr></tbody></table><br><div>Definition at line <code>2385</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_legacy_advertiser_set_data<span id=\"sl-bt-legacy-advertiser-set-data\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-legacy-advertiser-set-data\">#</a></span></h3><blockquote>sl_status_t sl_bt_legacy_advertiser_set_data (uint8_t advertising_set, uint8_t type, size_t data_len, const uint8_t * data)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-packet-type-t\" target=\"_blank\" rel=\"\">sl_bt_advertiser_packet_type_t</a>. The advertising packet type </p></td></tr><tr><td>[in]</td><td class=\"paramname\">data_len</td><td><p style=\"color:inherit\">Length of data in <code>data</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">data</td><td><p style=\"color:inherit\">Data to set</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set user-defined advertising data packet or scan response packet on an advertising set. This overwrites the existing advertising data packet and scan response packet on this advertising set regardless of whether the data was set for the legacy or extended advertising. Maximum 31 bytes of data can be set with this command.</p><p style=\"color:inherit\">If advertising mode is currently enabled, the new advertising data will be used immediately. Advertising mode can be enabled using command <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_start</a>.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2422</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_legacy_advertiser_generate_data<span id=\"sl-bt-legacy-advertiser-generate-data\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-legacy-advertiser-generate-data\">#</a></span></h3><blockquote>sl_status_t sl_bt_legacy_advertiser_generate_data (uint8_t advertising_set, uint8_t discover)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">discover</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-discovery-mode-t\" target=\"_blank\" rel=\"\">sl_bt_advertiser_discovery_mode_t</a>. The discovery mode for the Flags data field in the packet. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_non_discoverable (0x0):</strong> Not discoverable</p></li><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_limited_discoverable (0x1):</strong> Discoverable by both limited and general discovery procedures</p></li><li><p style=\"color:inherit\"><strong>sl_bt_advertiser_general_discoverable (0x2):</strong> Discoverable by the general discovery procedure</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Ask the stack to generate the advertising data packet and scan response packet on an advertising set. Alternatively, the user-defined advertising data can be set using the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-set-data\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_set_data</a> command.</p><p style=\"color:inherit\">This overwrites the existing advertising data packet and scan response packet on this advertising set regardless of whether the data was set for the legacy or extended advertising.</p><p style=\"color:inherit\">If advertising mode is currently enabled, the new advertising data will be used immediately. To enable advertising mode, use command <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_start</a>.</p><p style=\"color:inherit\">The stack generates the advertising data and scan response packet using the following logic.</p><ol style=\"list-style:decimal\"><li><p style=\"color:inherit\">Add a flags field to advertising data.</p></li><li><p style=\"color:inherit\">Add a TX power level field to advertising data if the TX power service exists in the local GATT database.</p></li><li><p style=\"color:inherit\">Add a peripheral connection interval range field to advertising data if the GAP peripheral preferred connection parameters characteristic exists in the local GATT database.</p></li><li><p style=\"color:inherit\">Add a list of 16-bit service UUIDs to advertising data if there are one or more 16-bit service UUIDs to advertise. The list is complete if all advertised 16-bit UUIDs are in advertising data. Otherwise, the list is incomplete.</p></li><li><p style=\"color:inherit\">Add a list of 128-bit service UUIDs to advertising data if there are one or more 128-bit service UUIDs to advertise and there is still free space for this field. The list is complete if all advertised 128-bit UUIDs are in advertising data. Otherwise, the list is incomplete. Note that an advertising data packet can contain at most one 128-bit service UUID.</p></li><li><p style=\"color:inherit\">Try to add the full local name to advertising data if the device is not in privacy mode. If the full local name does not fit into the remaining free space, the advertised name is a shortened version by cutting off the end if the free space has at least 6 bytes. Otherwise, the local name is added to scan response data.</p></li></ol><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>2476</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_legacy_advertiser_start<span id=\"sl-bt-legacy-advertiser-start\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-legacy-advertiser-start\">#</a></span></h3><blockquote>sl_status_t sl_bt_legacy_advertiser_start (uint8_t advertising_set, uint8_t connect)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">connect</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-connection-mode-t\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_connection_mode_t</a>. Connection mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_legacy_advertiser_non_connectable (0x0):</strong> Undirected non-connectable and non-scannable legacy advertising</p></li><li><p style=\"color:inherit\"><strong>sl_bt_legacy_advertiser_connectable (0x2):</strong> Undirected connectable and scannable legacy advertising</p></li><li><p style=\"color:inherit\"><strong>sl_bt_legacy_advertiser_scannable (0x3):</strong> Undirected scannable and non-connectable legacy advertising</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Start undirected legacy advertising on an advertising set with the specified connection mode. Use <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-stop\" target=\"_blank\" rel=\"\">sl_bt_advertiser_stop</a> to stop the advertising.</p><p style=\"color:inherit\">Use the <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-set-data\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_set_data</a> or <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-generate-data\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_generate_data</a> command to set the advertising data before calling this command. The advertising data is added into the advertising data packet and scan response packet if the connection mode is connectable and/or scannable. The data is only added into the advertising data packet when the connection mode is non-connectable and non-scannable.</p><p style=\"color:inherit\">The number of concurrent connectable advertisings is limited by the number of connections reserved by the user application (the SL_BT_CONFIG_MAX_CONNECTIONS configuration) and the number reserved by other software components (the SL_BT_COMPONENT_CONNECTIONS configuration). This command fails with the connection limit exceeded error if it may cause the number of connections exceeding the configured value in future. For example, only one connectable advertising can be enabled if the device has (SL_BT_CONFIG_MAX_CONNECTIONS + SL_BT_COMPONENT_CONNECTIONS - 1) connections. This limitation does not apply to non-connectable advertising.</p><p style=\"color:inherit\">This command fails with the invalid parameter error if non-resolvable random address is used but the connection mode is <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-connectable\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_connectable</a>.</p><p style=\"color:inherit\">Event <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> will be received when a remote device opens a connection to the advertiser on this advertising set. As a result, the advertising stops.</p><p style=\"color:inherit\">Event <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-advertiser-timeout\" target=\"_blank\" rel=\"\">sl_bt_evt_advertiser_timeout</a> will be received when the number of advertising events set by <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-timing\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_timing</a> command is done and the advertising has stopped.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-advertiser-timeout\" target=\"_blank\" rel=\"\">sl_bt_evt_advertiser_timeout</a> - Triggered when the number of advertising events set by <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-timing\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_timing</a> command is done and the advertising has stopped.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> - Triggered when a remote device opens a connection to the advertiser and the advertising has stopped. </p></li></ul><br><div>Definition at line <code>2533</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_legacy_advertiser_start_directed<span id=\"sl-bt-legacy-advertiser-start-directed\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-legacy-advertiser-start-directed\">#</a></span></h3><blockquote>sl_status_t sl_bt_legacy_advertiser_start_directed (uint8_t advertising_set, uint8_t connect, <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/bd-addr\" target=\"_blank\" rel=\"\">bd_addr</a> peer_addr, uint8_t peer_addr_type)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">advertising_set</td><td><p style=\"color:inherit\">Advertising set handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">connect</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-directed-connection-mode-t\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_directed_connection_mode_t</a>. Connection mode. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_legacy_advertiser_high_duty_directed_connectable (0x1):</strong> High duty cycle directed connectable legacy advertising</p></li><li><p style=\"color:inherit\"><strong>sl_bt_legacy_advertiser_low_duty_directed_connectable (0x5):</strong> Low duty cycle directed connectable legacy advertising </p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">peer_addr</td><td><p style=\"color:inherit\">Address of the peer target device the advertising is directed to </p></td></tr><tr><td>[in]</td><td class=\"paramname\">peer_addr_type</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a>.</p><p style=\"color:inherit\">Peer target device address type.</p><p style=\"color:inherit\">If the application does not include the bluetooth_feature_use_accurate_api_address_types component, <code>peer_addr_type</code> uses the following values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>0:</strong> Public address</p></li><li><p style=\"color:inherit\"><strong>1:</strong> Random address</p></li></ul><p style=\"color:inherit\">If the application includes the bluetooth_feature_use_accurate_api_address_types component, <code>peer_addr_type</code> uses enum <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-gap#sl-bt-gap-address-type-t\" target=\"_blank\" rel=\"\">sl_bt_gap_address_type_t</a> values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address (0x0):</strong> Public device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address (0x1):</strong> Static device address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_resolvable_address (0x2):</strong> Resolvable private random address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_random_nonresolvable_address (0x3):</strong> Non-resolvable private random address</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_public_address_resolved_from_rpa (0x4):</strong> Public identity address resolved from a resolvable private address (RPA)</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gap_static_address_resolved_from_rpa (0x5):</strong> Static identity address resolved from a resolvable private address (RPA) </p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Start directed legacy advertising on an advertising set with the specified peer target device and connection mode. Use <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-stop\" target=\"_blank\" rel=\"\">sl_bt_advertiser_stop</a> to stop the advertising.</p><p style=\"color:inherit\">Directed legacy advertising does not allow any advertising data. When the connection mode is <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-high-duty-directed-connectable\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_high_duty_directed_connectable</a>, the stack defaults the advertising duration to 0.64 s if the application has not set the parameter. The duration is reduced to 1.28 s if the application has set a larger duration value.</p><p style=\"color:inherit\">The number of concurrent connectable advertisings is limited by the connection number configuration. See <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser#sl-bt-legacy-advertiser-start\" target=\"_blank\" rel=\"\">sl_bt_legacy_advertiser_start</a> for more details.</p><p style=\"color:inherit\">This command fails with the invalid parameter error if non-resolvable random address is set as the advertising address.</p><p style=\"color:inherit\">Event <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> will be received when the target device opens a connection to the advertiser on this advertising set. As a result, the advertising stops.</p><p style=\"color:inherit\">Event <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-advertiser-timeout\" target=\"_blank\" rel=\"\">sl_bt_evt_advertiser_timeout</a> will be received when the advertising stops and no Bluetooth connection is opened to it.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-advertiser-timeout\" target=\"_blank\" rel=\"\">sl_bt_evt_advertiser_timeout</a> - Triggered when the number of advertising events set by <a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-advertiser#sl-bt-advertiser-set-timing\" target=\"_blank\" rel=\"\">sl_bt_advertiser_set_timing</a> command is done and the advertising has stopped.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-evt-connection-opened\" target=\"_blank\" rel=\"\">sl_bt_evt_connection_opened</a> - Triggered when a remote device opens a connection to the advertiser and the advertising has stopped. </p></li></ul><br><div>Definition at line <code>2609</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_legacy_advertiser_set_data_id<span id=\"sl-bt-cmd-legacy-advertiser-set-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-legacy-advertiser-set-data-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_legacy_advertiser_set_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00560020</pre><br><div>Definition at line <code>2354</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_legacy_advertiser_generate_data_id<span id=\"sl-bt-cmd-legacy-advertiser-generate-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-legacy-advertiser-generate-data-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_legacy_advertiser_generate_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01560020</pre><br><div>Definition at line <code>2355</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_legacy_advertiser_start_id<span id=\"sl-bt-cmd-legacy-advertiser-start-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-legacy-advertiser-start-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_legacy_advertiser_start_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02560020</pre><br><div>Definition at line <code>2356</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_legacy_advertiser_start_directed_id<span id=\"sl-bt-cmd-legacy-advertiser-start-directed-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-legacy-advertiser-start-directed-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_legacy_advertiser_start_directed_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03560020</pre><br><div>Definition at line <code>2357</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_legacy_advertiser_set_data_id<span id=\"sl-bt-rsp-legacy-advertiser-set-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-legacy-advertiser-set-data-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_legacy_advertiser_set_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00560020</pre><br><div>Definition at line <code>2358</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_legacy_advertiser_generate_data_id<span id=\"sl-bt-rsp-legacy-advertiser-generate-data-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-legacy-advertiser-generate-data-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_legacy_advertiser_generate_data_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01560020</pre><br><div>Definition at line <code>2359</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_legacy_advertiser_start_id<span id=\"sl-bt-rsp-legacy-advertiser-start-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-legacy-advertiser-start-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_legacy_advertiser_start_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02560020</pre><br><div>Definition at line <code>2360</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_legacy_advertiser_start_directed_id<span id=\"sl-bt-rsp-legacy-advertiser-start-directed-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-legacy-advertiser-start-directed-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_legacy_advertiser_start_directed_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03560020</pre><br><div>Definition at line <code>2361</code> of file <code>/mnt/raid/workspaces/ws.iS2YF8Lrv/overlay/gsdk/protocol/bluetooth/build/native/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/8.2.0/bluetooth-stack-api/sl-bt-legacy-advertiser", "status": "success"}