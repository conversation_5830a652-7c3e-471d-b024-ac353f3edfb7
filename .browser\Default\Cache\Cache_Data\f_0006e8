{"availableCount": 178, "searchTerms": [], "userState": "341d2306-133d-46cb-a8bd-04d97e4f6996", "resources": [{"supportDocuments": [], "description": "Describes the differences between using Bluetooth SDK v2.x in Simplicity Studio 4 and using  Bluetooth SDK v3.x in Simplicity Studio 5. Outlines the steps needed to migrate a v2.x project to v3.x.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1255-transitioning-from-bluetooth-sdk-v2-to-v3.pdf", "text": "AN1255: Transitioning from the v2.x to the v3.x Bluetooth SDK", "priority": 1, "category": "DOCUMENTATION", "toolTipText": "Describes the differences between using Bluetooth SDK v2.x in Simplicity Studio 4 and using  Bluetooth SDK v3.x in Simplicity Studio 5. Outlines the steps needed to migrate a v2.x project to v3.x."}, {"supportDocuments": [], "description": "Describes the differences between using Bluetooth mesh SDK v1.x in Simplicity Studio 4 and using  Bluetooth mesh SDK v2.x in Simplicity Studio 5. Outlines the steps needed to migrate a v1.x project to v2.x.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1298-transitioning-from-bluetooth-mesh-1x-to-2x.pdf", "text": "AN1298: Transitioning from the v1.x to the v2.x Bluetooth Mesh SDK", "priority": 1, "category": "DOCUMENTATION", "toolTipText": "Describes the differences between using Bluetooth mesh SDK v1.x in Simplicity Studio 4 and using  Bluetooth mesh SDK v2.x in Simplicity Studio 5. Outlines the steps needed to migrate a v1.x project to v2.x."}, {"supportDocuments": [], "description": "Discusses the latest changes to the The Real-Time Locating (RTL) library, including added/deleted/deprecated  APIs, and lists fixed and known issues.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/bt-rtl-library-release-notes.pdf", "text": "Bluetooth Real-Time Locating Library Release Notes", "priority": 1, "category": "DOCUMENTATION", "toolTipText": "Discusses the latest changes to the The Real-Time Locating (RTL) library, including added/deleted/deprecated  APIs, and lists fixed and known issues."}, {"supportDocuments": [], "description": "Lists compatibility requirements and sources for all software components in the development environment.  Discusses the latest changes to the SiliconLabs Flex SDK, including added/deleted/deprecated features/API.  Reviews fixed and known issues.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/flex-release-notes.pdf", "text": "Flex SDK Release Notes", "priority": 1, "category": "DOCUMENTATION", "toolTipText": "Lists compatibility requirements and sources for all software components in the development environment.  Discusses the latest changes to the SiliconLabs Flex SDK, including added/deleted/deprecated features/API.  Reviews fixed and known issues."}, {"supportDocuments": [], "description": "Contains a comprehensive reference for the APIs of Silicon Labs Gecko Bootloader, including the Gecko Bootloader Application Interface used to interface with the bootloader from a running application, as well as the APIs used to implement bootloader functionality.", "id": "https://docs.silabs.com/mcu-bootloader/latest/", "text": "Gecko Bootloader API Reference Guide", "priority": 1, "category": "DOCUMENTATION", "toolTipText": "Contains a comprehensive reference for the APIs of Silicon Labs Gecko Bootloader, including the Gecko Bootloader Application Interface used to interface with the bootloader from a running application, as well as the APIs used to implement bootloader functionality."}, {"supportDocuments": [], "description": "A detailed overview of the changes, additions, and fixes in the Gecko Platform components.  The Gecko Platform includes EMLIB, EMDRV, RAIL Library, NVM3, and the component-based infrastructure.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/gecko-platform-release-notes.pdf", "text": "Gecko Platform Release Notes", "priority": 1, "category": "DOCUMENTATION", "toolTipText": "A detailed overview of the changes, additions, and fixes in the Gecko Platform components.  The Gecko Platform includes EMLIB, EMDRV, RAIL Library, NVM3, and the component-based infrastructure."}, {"supportDocuments": [], "description": "API Reference for Micrium OS Kernel", "id": "https://docs.silabs.com/micrium/latest/micrium-kernel-api/", "text": "Kernel API Reference", "priority": 1, "category": "DOCUMENTATION", "toolTipText": "API Reference for Micrium OS Kernel"}, {"supportDocuments": [], "description": "Provides basic information on configuring, building, and installing applications using Silicon Labs Connect and RAIL, the two development paths in the Silicon Labs Proprietary Flex SDK v3.x.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/qsg168-proprietary-flex-sdk-v3x-quick-start-guide.pdf", "text": "QSG168: Proprietary Flex SDK v3.x Quick Start Guide", "priority": 1, "category": "DOCUMENTATION", "toolTipText": "Provides basic information on configuring, building, and installing applications using Silicon Labs Connect and RAIL, the two development paths in the Silicon Labs Proprietary Flex SDK v3.x."}, {"supportDocuments": [], "description": "Describes the software components provided by Silicon Labs to support Direction Finding (DF) and provides instructions on how to start developing your own application.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/qsg175-direction-finding-solution-quick-start-guide.pdf", "text": "QSG175: Silicon Labs' Direction Finding Solution Quick-Start Guide", "priority": 1, "category": "DOCUMENTATION", "toolTipText": "Describes the software components provided by Silicon Labs to support Direction Finding (DF) and provides instructions on how to start developing your own application."}, {"supportDocuments": [], "description": "Describes how to get started with Bluetooth mesh development using the Bluetooth  Mesh Software Development Kit (SDK) version 4.x and higher, and Simplicity Studio 5 with a compatible  wireless starter kit. Contains information about features specific to Bluetooth mesh  specification version 1.1.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/qsg183-bluetooth-mesh-sdk-4x-quick-start-guide.pdf", "text": "QSG183: Bluetooth Mesh SDK Quick-Start Guide for SDK v4.x", "priority": 1, "category": "DOCUMENTATION", "toolTipText": "Describes how to get started with Bluetooth mesh development using the Bluetooth  Mesh Software Development Kit (SDK) version 4.x and higher, and Simplicity Studio 5 with a compatible  wireless starter kit. Contains information about features specific to Bluetooth mesh  specification version 1.1."}, {"supportDocuments": [], "description": "Lists compatibility requirements and sources for all software components  in the development environment. Discusses the latest changes to the Silicon Labs Bluetooth mesh SDK and associated utilities, including added/deleted/deprecated  features/API, and lists fixed and known issues.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/bt-mesh-2x-software-release-notes.pdf", "text": "Silicon Labs Bluetooth Mesh Release Notes", "priority": 1, "category": "DOCUMENTATION", "toolTipText": "Lists compatibility requirements and sources for all software components  in the development environment. Discusses the latest changes to the Silicon Labs Bluetooth mesh SDK and associated utilities, including added/deleted/deprecated  features/API, and lists fixed and known issues."}, {"supportDocuments": [], "description": "Lists compatibility requirements and sources for all software components  in the development environment. Discusses the latest changes to the Silicon Labs Bluetooth SDK and associated utilities, including added/deleted/deprecated  features/API, and lists fixed and known issues.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/bluetooth-release-notes.pdf", "text": "Silicon Labs Bluetooth Release Notes", "priority": 1, "category": "DOCUMENTATION", "toolTipText": "Lists compatibility requirements and sources for all software components  in the development environment. Discusses the latest changes to the Silicon Labs Bluetooth SDK and associated utilities, including added/deleted/deprecated  features/API, and lists fixed and known issues."}, {"supportDocuments": [], "description": "API Reference for Micrium OS CPU", "id": "https://docs.silabs.com/micrium/latest/micrium-cpu-api/", "text": "CPU API Reference", "priority": 2, "category": "DOCUMENTATION", "toolTipText": "API Reference for Micrium OS CPU"}, {"supportDocuments": [], "description": "Description of general concepts that are used throughout Micrium OS products", "id": "https://docs.silabs.com/micrium/latest/micrium-general-concepts/", "text": "General Concepts", "priority": 2, "category": "DOCUMENTATION", "toolTipText": "Description of general concepts that are used throughout Micrium OS products"}, {"supportDocuments": [], "description": "API Reference for Micrium OS Common", "id": "https://docs.silabs.com/micrium/latest/micrium-common-api/", "text": "Common API Reference", "priority": 3, "category": "DOCUMENTATION", "toolTipText": "API Reference for Micrium OS Common"}, {"supportDocuments": [], "description": "API Reference for Micrium OS IO", "id": "https://docs.silabs.com/micrium/latest/micrium-io-api/", "text": "IO API Reference", "priority": 4, "category": "DOCUMENTATION", "toolTipText": "API Reference for Micrium OS IO"}, {"supportDocuments": [], "description": "Provides an overview and hyperlinks to all packaged documentation.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/slBtMesh-index.html", "text": "Documentation Index", "priority": 9, "category": "DOCUMENTATION", "toolTipText": "Provides an overview and hyperlinks to all packaged documentation."}, {"supportDocuments": [], "description": "Oscillator design considerations for Wireless SoC Series 2 devices plus guidelines for oscillator circuit component selection.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an0016.2-efr32-series-2-oscillator-design-considerations.pdf", "text": "AN0016.2: Oscillator Design Considerations", "priority": 10, "category": "APP_NOTES", "toolTipText": "Oscillator design considerations for Wireless SoC Series 2 devices plus guidelines for oscillator circuit component selection."}, {"supportDocuments": [], "description": "Description is unavailable", "id": "asset://aem/www.silabs.com/documents/public/quick-start-guides/qsg169-bluetooth-sdk-v3x-quick-start-guide.pdf", "text": "QSG169: Bluetooth® Quick-Start Guide for SDK v3.x and Higher", "priority": 10, "category": "DOCUMENTATION", "toolTipText": "Description is unavailable"}, {"supportDocuments": [], "description": "This application note details hardware design considerations for EFR32 Wireless Gecko Series 2 devices. For hardware design considerations for EFM32 and EZR32 Wireless MCU Series 0 and EFM32 and EFR32 Wireless Gecko Series 1 devices, refer to AN0002.0: EFM32 and EZR32 Wireless MCU Series 0 Hardware Design Considerations and AN0002.1: EFM32 and EFR32 Wireless MCU Series 1 Hardware Design Considerations, respectively.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an0002.2-efr32-efm32-series-2-hardware-design-considerations.pdf", "text": "AN0002.2: EFM32 and EFR32 Wireless Gecko Series 2 Hardware Design Considerations", "priority": 10, "category": "APP_NOTES", "toolTipText": "This application note details hardware design considerations for EFR32 Wireless Gecko Series 2 devices. For hardware design considerations for EFM32 and EZR32 Wireless MCU Series 0 and EFM32 and EFR32 Wireless Gecko Series 1 devices, refer to AN0002.0: EFM32 and EZR32 Wireless MCU Series 0 Hardware Design Considerations and AN0002.1: EFM32 and EFR32 Wireless MCU Series 1 Hardware Design Considerations, respectively."}, {"supportDocuments": [], "description": "The purpose of this application note is to help users design PCBs for the EFR32 Series 2 Wireless Gecko Portfolio using design practices that allow for good RF performance.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an928.2-efr32-series2-layout-design-guide.pdf", "text": "AN928.2: EFR32 Series 2 Layout Design Guide", "priority": 10, "category": "APP_NOTES", "toolTipText": "The purpose of this application note is to help users design PCBs for the EFR32 Series 2 Wireless Gecko Portfolio using design practices that allow for good RF performance."}, {"supportDocuments": [], "description": "Guide to the best RF matching techniques for EFR32 Series 2 Wireless Gecko Portfolio devices in the 2.4 GHz band. Details of matching network design procedures and additional test results are presented.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an930.2-efr32-series-2.pdf", "text": "AN930.2: EFR32 Series 2 2.4 GHz Matching Guide", "priority": 10, "category": "APP_NOTES", "toolTipText": "Guide to the best RF matching techniques for EFR32 Series 2 Wireless Gecko Portfolio devices in the 2.4 GHz band. Details of matching network design procedures and additional test results are presented."}, {"supportDocuments": [], "description": "Contains a comprehensive list of APIs used to interface to the Silicon Labs Bluetooth stack.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/API_BLUETOOTH_HTML/index.html", "text": "Silicon Labs Bluetooth API Reference Guide", "priority": 10, "category": "DOCUMENTATION", "toolTipText": "Contains a comprehensive list of APIs used to interface to the Silicon Labs Bluetooth stack."}, {"supportDocuments": [], "description": "Contains a comprehensive list of APIs used to interface to the Silicon Labs Bluetooth Mesh stack.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/API_BLUETOOTH_MESH_HTML/index.html", "text": "Silicon Labs Bluetooth Mesh API Reference Guide", "priority": 10, "category": "DOCUMENTATION", "toolTipText": "Contains a comprehensive list of APIs used to interface to the Silicon Labs Bluetooth Mesh stack."}, {"supportDocuments": [], "description": "Introduces some fundamental concepts of wireless networking. These concepts are referred to in other Fundamentals documents. If you are new to wireless networking, you should read this document first.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug103-01-fundamentals-wireless-network.pdf", "text": "UG103.01: Wireless Networking Fundamentals", "priority": 10, "category": "DOCUMENTATION", "toolTipText": "Introduces some fundamental concepts of wireless networking. These concepts are referred to in other Fundamentals documents. If you are new to wireless networking, you should read this document first."}, {"supportDocuments": [], "description": "Describes the features and functions of the Silicon Labs Connect stack, including its device types, network topologies, and its 'building block' development methodology using plugins.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug103-12-fundamentals-connect.pdf", "text": "UG103.12: Connect Fundamentals", "priority": 10, "category": "DOCUMENTATION", "toolTipText": "Describes the features and functions of the Silicon Labs Connect stack, including its device types, network topologies, and its 'building block' development methodology using plugins."}, {"supportDocuments": [], "description": "Describes the features and functions of Silicon Labs RAIL (Radio Abstraction Interface Layer). RAIL provides an intuitive, easily-customizable radio interface layer that is designed to support proprietary or standards-based wireless protocols.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug103-13-fundamentals-rail.pdf", "text": "UG103.13: RAIL Fundamentals", "priority": 10, "category": "DOCUMENTATION", "toolTipText": "Describes the features and functions of Silicon Labs RAIL (Radio Abstraction Interface Layer). RAIL provides an intuitive, easily-customizable radio interface layer that is designed to support proprietary or standards-based wireless protocols."}, {"supportDocuments": [], "description": "Offers an overview for those new to the Bluetooth low energy technology.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/ug103-14-fundamentals-ble.pdf", "text": "UG103.14: Bluetooth LE Fundamentals", "priority": 10, "category": "DOCUMENTATION", "toolTipText": "Offers an overview for those new to the Bluetooth low energy technology."}, {"supportDocuments": [], "description": "Describes methods to improve the coexistence of 2.4 GHz IEEE 802.11b/g/n Wi-Fi and other 2.4 GHz radios such as Bluetooth, Bluetooth Mesh, Bluetooth Low Energy, and IEEE 802.15.4-based radios such as Zigbee and OpenThread.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/ug103-17-wi-fi-coexistence-fundamentals.pdf", "text": "UG103.17: Wi-Fi Coexistence Fundamentals", "priority": 10, "category": "DOCUMENTATION", "toolTipText": "Describes methods to improve the coexistence of 2.4 GHz IEEE 802.11b/g/n Wi-Fi and other 2.4 GHz radios such as Bluetooth, Bluetooth Mesh, Bluetooth Low Energy, and IEEE 802.15.4-based radios such as Zigbee and OpenThread."}, {"supportDocuments": [], "description": "A reference for those developing C-based applications for the Silicon Labs EFR32 products using the Silicon Labs  Bluetooth mesh stack. A companion to UG434: Silicon Labs Bluetooth C Application Developers Guide for SDK v3.x  containing content specific to Bluetooth mesh application development. Covers Bluetooth mesh stack architecture,  application development flow, use and limitations of the MCU core and peripherals, stack configuration options,  and stack resource usage.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/ug295-bluetooth-mesh-dev-guide.pdf", "text": "UG295: Silicon Labs Bluetooth Mesh C Developer's Guide", "priority": 10, "category": "DOCUMENTATION", "toolTipText": "A reference for those developing C-based applications for the Silicon Labs EFR32 products using the Silicon Labs  Bluetooth mesh stack. A companion to UG434: Silicon Labs Bluetooth C Application Developers Guide for SDK v3.x  containing content specific to Bluetooth mesh application development. Covers Bluetooth mesh stack architecture,  application development flow, use and limitations of the MCU core and peripherals, stack configuration options,  and stack resource usage."}, {"supportDocuments": [], "description": "Describes how to configure the NCP target and how to program the NCP host when  using the Bluetooth Stack in Network Co-Processor mode", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1259-bt-ncp-mode-sdk-v3x.pdf", "text": "AN1259: Using the v3.x Silicon Labs Bluetooth Stack in Network Co-Processor Mode", "priority": 12, "category": "DOCUMENTATION", "toolTipText": "Describes how to configure the NCP target and how to program the NCP host when  using the Bluetooth Stack in Network Co-Processor mode"}, {"supportDocuments": [], "description": "Describes how to integrate a v3.x Silicon Labs Bluetooth application with an RTOS, and  demonstrate how a time- and event-driven application can be run in parallel with the Bluetooth stack.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1260-integrating-v3x-bluetooth-applications-with-rtos.pdf", "text": "AN1260: Integrating v3.x Silicon Labs Bluetooth Applications with Real-Time Operating Systems", "priority": 12, "category": "DOCUMENTATION", "toolTipText": "Describes how to integrate a v3.x Silicon Labs Bluetooth application with an RTOS, and  demonstrate how a time- and event-driven application can be run in parallel with the Bluetooth stack."}, {"supportDocuments": [], "description": "Gives a short overview of the standard Host Controller Interface (HCI) and how to use  it with a Silicon Labs Bluetooth LE controller.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1328-enabling-rcp-using-bt-hci.pdf", "text": "AN1328: Enabling a Radio Co-Processor using the Bluetooth Controller and HCI Functions", "priority": 12, "category": "DOCUMENTATION", "toolTipText": "Gives a short overview of the standard Host Controller Interface (HCI) and how to use  it with a Silicon Labs Bluetooth LE controller."}, {"supportDocuments": [], "description": "Summarizes Amazon FreeRTOS components and sample applications, and explains how to use the examples  to communicate with the Amazon Web Services (AWS) cloud with a smart phone app.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1362-amazon-freertos-architecture-examples.pdf", "text": "AN1362: Amazon FreeRTOS Architecture and Sample Applications", "priority": 12, "category": "DOCUMENTATION", "toolTipText": "Summarizes Amazon FreeRTOS components and sample applications, and explains how to use the examples  to communicate with the Amazon Web Services (AWS) cloud with a smart phone app."}, {"supportDocuments": [], "description": "Describes how to exploit the different features of Bluetooth technology to achieve the minimum possible energy consumption for a given use case.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1366-bluetooth-use-case-based-low-power-optimization.pdf", "text": "AN1366: Bluetooth LE Use Case-Based Low Power Optimization", "priority": 12, "category": "DOCUMENTATION", "toolTipText": "Describes how to exploit the different features of Bluetooth technology to achieve the minimum possible energy consumption for a given use case."}, {"supportDocuments": [], "description": "Reviews using this XML-based mark-up language to describe the Bluetooth GATT database,  configure access and security properties, and include the GATT database as part of the firmware.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/ug118-bluegecko-bt-profile-toolkit.pdf", "text": "UG118: Blue Gecko Bluetooth Profile Toolkit Developer's Guide", "priority": 12, "category": "DOCUMENTATION", "toolTipText": "Reviews using this XML-based mark-up language to describe the Bluetooth GATT database,  configure access and security properties, and include the GATT database as part of the firmware."}, {"supportDocuments": [], "description": "Describes how and when to use Simplicity Commander's Command-Line Interface.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/ug162-simplicity-commander-reference-guide.pdf", "text": "UG162: Simplicity Commander Reference Guide", "priority": 12, "category": "DOCUMENTATION", "toolTipText": "Describes how and when to use Simplicity Commander's Command-Line Interface."}, {"supportDocuments": [], "description": "Covers the Bluetooth stack v7.x architecture, application development flow, using the MCU core  and peripherals, stack configuration options, and stack resource usage.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/ug434-bluetooth-c-soc-dev-guide-sdk-v3x.pdf", "text": "UG434: Silicon Labs Bluetooth C Application Developer's Guide for SDK v7.x and Higher", "priority": 12, "category": "DOCUMENTATION", "toolTipText": "Covers the Bluetooth stack v7.x architecture, application development flow, using the MCU core  and peripherals, stack configuration options, and stack resource usage."}, {"supportDocuments": [], "description": "Provides the information needed to effectively use the Bluetooth GATT Configurator provided as a part of Simplicity Studio 5 with Bluetooth SDK 3.x and higher and Bluetooth Mesh SDK 2.x and higher.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/ug438-gatt-configurator-users-guide-sdk-v3x.pdf", "text": "UG438: GATT Configurator User's Guide for Bluetooth LE and Bluetooth Mesh", "priority": 12, "category": "DOCUMENTATION", "toolTipText": "Provides the information needed to effectively use the Bluetooth GATT Configurator provided as a part of Simplicity Studio 5 with Bluetooth SDK 3.x and higher and Bluetooth Mesh SDK 2.x and higher."}, {"supportDocuments": [], "description": "Describes the components, stack, and DCD (Device Composition Data) configuration options for the  Bluetooth Mesh v2.x SDK.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/ug472-bluetooth-mesh-v2x-node-configuration-users-guide.pdf", "text": "UG472: Bluetooth Mesh Stack and Bluetooth Mesh Configurator User's Guide for SDK v2.x", "priority": 12, "category": "DOCUMENTATION", "toolTipText": "Describes the components, stack, and DCD (Device Composition Data) configuration options for the  Bluetooth Mesh v2.x SDK."}, {"supportDocuments": [], "description": "Introduces Simplicity Studio 5 Bluetooth Mesh SDK components. Describes how to modify the Device Composition Data (DCD), including device information, elements, and models. Describes the stack configuration options to optimize RAM and persistent storage usage.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/ug572-bluetooth-mesh-v5x-node-configuration.pdf", "text": "UG572: Bluetooth Mesh Stack and Bluetooth Mesh Configurator User's Guide for SDK v5.0 and Higher", "priority": 12, "category": "DOCUMENTATION", "toolTipText": "Introduces Simplicity Studio 5 Bluetooth Mesh SDK components. Describes how to modify the Device Composition Data (DCD), including device information, elements, and models. Describes the stack configuration options to optimize RAM and persistent storage usage."}, {"supportDocuments": [], "description": "Describes the Wi-Fi impact on Bluetooth and methods to improve Bluetooth coexistence with Wi-Fi.  Explains design considerations to improve coexistence without direct interaction between Bluetooth and Wi-Fi radios.  These techniques are applicable to the EFR32MGx and EFR32BGx series. Discusses the Silicon Labs Packet Traffic Arbitration (PTA) support to coordinate 2.4GHz RF traffic for co-located Bluetooth and Wi-Fi radios.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1128-bluetooth-coexistence-with-wifi.pdf", "text": "AN1128: Bluetooth Coexistence with Wi-Fi", "priority": 14, "category": "DOCUMENTATION", "toolTipText": "Describes the Wi-Fi impact on Bluetooth and methods to improve Bluetooth coexistence with Wi-Fi.  Explains design considerations to improve coexistence without direct interaction between Bluetooth and Wi-Fi radios.  These techniques are applicable to the EFR32MGx and EFR32BGx series. Discusses the Silicon Labs Packet Traffic Arbitration (PTA) support to coordinate 2.4GHz RF traffic for co-located Bluetooth and Wi-Fi radios."}, {"supportDocuments": [], "description": "Discusses the basics of Bluetooth mesh required to understand the Bluetooth mesh lighting example, and walks through key aspects of the application source code.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1299-understanding-bluetooth-mesh-lighting-demo-sdk-2x.pdf", "text": "AN1299: Understanding the Silicon Labs Bluetooth Mesh SDK v2.x Lighting Demonstration", "priority": 14, "category": "DOCUMENTATION", "toolTipText": "Discusses the basics of Bluetooth mesh required to understand the Bluetooth mesh lighting example, and walks through key aspects of the application source code."}, {"supportDocuments": [], "description": "Discusses the basics of sensor models and describe the related sample applications in the SDK that create a wireless network of sensors and sensor clients using Bluetooth mesh technology.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1300-understanding-bluetooth-mesh-sensor-model-demo-sdk-2x.pdf", "text": "AN1300: Understanding the Silicon Labs Bluetooth Mesh Sensor Model in SDK v2.x", "priority": 14, "category": "DOCUMENTATION", "toolTipText": "Discusses the basics of sensor models and describe the related sample applications in the SDK that create a wireless network of sensors and sensor clients using Bluetooth mesh technology."}, {"supportDocuments": [], "description": "Provides background information on the sequence number and IV index in a Bluetooth mesh network and the IV Update  and IV Index Recovery procedures. It also discusses how to implement IV Update functionality in a Bluetooth mesh  application.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1318-bluetooth-mesh-iv-update.pdf", "text": "AN1318: IV Update in a Bluetooth Mesh Network", "priority": 14, "category": "DOCUMENTATION", "toolTipText": "Provides background information on the sequence number and IV index in a Bluetooth mesh network and the IV Update  and IV Index Recovery procedures. It also discusses how to implement IV Update functionality in a Bluetooth mesh  application."}, {"supportDocuments": [], "description": "Provides background information on the Bluetooth Mesh Device Firmware Update (DFU) feature, including the BLOB transfer, the DFU roles in a Bluetooth mesh network, the models required for these roles, and the firmware update process.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1319-bluetooth-mesh-device-firmware-update.pdf", "text": "AN1319: Bluetooth Mesh Device Firmware Update", "priority": 14, "category": "DOCUMENTATION", "toolTipText": "Provides background information on the Bluetooth Mesh Device Firmware Update (DFU) feature, including the BLOB transfer, the DFU roles in a Bluetooth mesh network, the models required for these roles, and the firmware update process."}, {"supportDocuments": [], "description": "Describes the Bluetooth Mesh Remote Provisioning feature and provides an example walkthrough. With the feature, a device can be provisioned without a direct radio connection between the provisioner and the unprovisioned node.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1368-bluetooth-mesh-remote-provisioning.pdf", "text": "AN1368: Bluetooth Mesh Remote Provisioning", "priority": 14, "category": "DOCUMENTATION", "toolTipText": "Describes the Bluetooth Mesh Remote Provisioning feature and provides an example walkthrough. With the feature, a device can be provisioned without a direct radio connection between the provisioner and the unprovisioned node."}, {"supportDocuments": [], "description": "Describes the bootloader configurations and the device firmware update (DFU) models in the SDK's example projects, and walks through a firmware update demonstration.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1370-bluetooth-mesh-device-firmware-update-example.pdf", "text": "AN1370: Bluetooth Mesh Device Firmware Update Example Walkthrough", "priority": 14, "category": "DOCUMENTATION", "toolTipText": "Describes the bootloader configurations and the device firmware update (DFU) models in the SDK's example projects, and walks through a firmware update demonstration."}, {"supportDocuments": [], "description": "The NCP Host Provisioner example demonstrates how to run a provisioner on a computer with a NCP node connected. The user can provision, configure, and reset other nodes through the NCP node.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1371-bluetooth-mesh-host-provisioner-example.pdf", "text": "AN1371: Bluetooth Mesh NCP Host Provisioner Example Walkthrough", "priority": 14, "category": "DOCUMENTATION", "toolTipText": "The NCP Host Provisioner example demonstrates how to run a provisioner on a computer with a NCP node connected. The user can provision, configure, and reset other nodes through the NCP node."}, {"supportDocuments": [], "description": "Describes how certificates are used to establish the authenticity of devices wishing to join a mesh network.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1386-bluetooth-mesh-certificate-based-provisioning.pdf", "text": "AN1386: Bluetooth Mesh Certificate-Based Provisioning", "priority": 14, "category": "DOCUMENTATION", "toolTipText": "Describes how certificates are used to establish the authenticity of devices wishing to join a mesh network."}, {"supportDocuments": [], "description": "Describes the Bluetooth Mesh Advertising Extensions feature. The non-standard Bluetooth  Mesh modification achieves better performance through utilizing the Bluetooth 5 Advertising Extensions feature,  which allows sending much larger advertisement packets.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1405-bt-mesh-advertising-extensions.pdf", "text": "AN1405: Bluetooth Mesh on Advertising Extensions", "priority": 14, "category": "DOCUMENTATION", "toolTipText": "Describes the Bluetooth Mesh Advertising Extensions feature. The non-standard Bluetooth  Mesh modification achieves better performance through utilizing the Bluetooth 5 Advertising Extensions feature,  which allows sending much larger advertisement packets."}, {"supportDocuments": [], "description": "Walks through a device firmware update demonstration using the DFU Python script. The script is an NCP host application that requires an NCP node connected.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1422-provisioning-firmware-update-with-dfu-python-script.pdf", "text": "AN1422: Provisioning and Firmware Update Using the DFU Python Script", "priority": 14, "category": "DOCUMENTATION", "toolTipText": "Walks through a device firmware update demonstration using the DFU Python script. The script is an NCP host application that requires an NCP node connected."}, {"supportDocuments": [], "description": "Describes the following Networked Lighting Control (NLC) profiles: ambient light sensor, basic scene selector, dimming control, basic lightness controller, and occupancy sensor.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1438-network-lighting-control.pdf", "text": "AN1438: Networked Lighting Control", "priority": 14, "category": "DOCUMENTATION", "toolTipText": "Describes the following Networked Lighting Control (NLC) profiles: ambient light sensor, basic scene selector, dimming control, basic lightness controller, and occupancy sensor."}, {"supportDocuments": [], "description": "Includes detailed information on using the Gecko Bootloader with Silicon Labs Bluetooth applications. It supplements the general Gecko Bootloader implementation information provided in UG489: Silicon Labs Gecko Bootloader  User's Guide.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1086-gecko-bootloader-bluetooth.pdf", "text": "AN1086: Using the Gecko Bootloader with Silicon Labs Bluetooth Applications", "priority": 15, "category": "DOCUMENTATION", "toolTipText": "Includes detailed information on using the Gecko Bootloader with Silicon Labs Bluetooth applications. It supplements the general Gecko Bootloader implementation information provided in UG489: Silicon Labs Gecko Bootloader  User's Guide."}, {"supportDocuments": [], "description": "Contains detailed information on configuring and using the Secure Boot with hardware Root of Trust and Secure Loader on  Series 2 devices, including how to provision the signing key. This is a companion document to UG489: Silicon Labs Gecko  Bootloader User's Guide.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1218-secure-boot-with-rtsl.pdf", "text": "AN1218: Series 2 Secure Boot with RTSL", "priority": 15, "category": "DOCUMENTATION", "toolTipText": "Contains detailed information on configuring and using the Secure Boot with hardware Root of Trust and Secure Loader on  Series 2 devices, including how to provision the signing key. This is a companion document to UG489: Silicon Labs Gecko  Bootloader User's Guide."}, {"supportDocuments": [], "description": "Gecko Bootloader v2.x, introduced in GSDK 4.0, contains a number of changes compared to Gecko Bootloader v1.x. This document describes the differences between the versions, including how to configure the new Gecko Bootloader in Simplicity Studio 5.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1326-gecko-bootloader-transitioning-guide.pdf", "text": "AN1326: Transitioning to the Updated Gecko Bootloader in GSDK 4.0 and Higher", "priority": 15, "category": "DOCUMENTATION", "toolTipText": "Gecko Bootloader v2.x, introduced in GSDK 4.0, contains a number of changes compared to Gecko Bootloader v1.x. This document describes the differences between the versions, including how to configure the new Gecko Bootloader in Simplicity Studio 5."}, {"supportDocuments": [], "description": "Gecko Bootloader v2.x, introduced in GSDK 4.0, contains a number of changes compared to Gecko Bootloader v1.x. This document describes the differences between the versions, including how to configure the new Gecko Bootloader in Simplicity Studio 5.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1326-gecko-bootloader-transitioning-guide.pdf", "text": "Transitioning to the Updated Gecko Bootloader in GSDK 4.0 and Higher", "priority": 15, "category": "DOCUMENTATION", "toolTipText": "Gecko Bootloader v2.x, introduced in GSDK 4.0, contains a number of changes compared to Gecko Bootloader v1.x. This document describes the differences between the versions, including how to configure the new Gecko Bootloader in Simplicity Studio 5."}, {"supportDocuments": [], "description": "Introduces bootloading for Silicon Labs networking devices. Discusses the Gecko Bootloader as well  as legacy Ember and Bluetooth bootloaders, and describes the file formats used by each.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/ug103-06-fundamentals-bootloading.pdf", "text": "UG103.06: Bootloader Fundamentals", "priority": 15, "category": "DOCUMENTATION", "toolTipText": "Introduces bootloading for Silicon Labs networking devices. Discusses the Gecko Bootloader as well  as legacy Ember and Bluetooth bootloaders, and describes the file formats used by each."}, {"supportDocuments": [], "description": "Describes the high-level implementation of the Silicon Labs Gecko Bootloader for EFR32 SoCs and NCPs,  and provides information on how to get started using the Gecko Bootloader with Silicon Labs wireless protocol stacks in GSDK 4.0 and higher.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/ug489-gecko-bootloader-user-guide-gsdk-4.pdf", "text": "UG489: Silicon Labs Gecko Bootloader User's Guide for GSDK 4.0 and Higher", "priority": 15, "category": "DOCUMENTATION", "toolTipText": "Describes the high-level implementation of the Silicon Labs Gecko Bootloader for EFR32 SoCs and NCPs,  and provides information on how to get started using the Gecko Bootloader with Silicon Labs wireless protocol stacks in GSDK 4.0 and higher."}, {"supportDocuments": [], "description": "The Silicon Labs MCU and Wireless Starter Kits and Simplicity Studio provide a powerful development and debug environment.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an958-mcu-stk-wstk-guide.pdf", "text": "AN958: Debugging and Programming Interfaces for Custom Designs", "priority": 20, "category": "APP_NOTES", "toolTipText": "The Silicon Labs MCU and Wireless Starter Kits and Simplicity Studio provide a powerful development and debug environment."}, {"supportDocuments": [], "description": "Explains how NVM3 can be used as non-volatile data storage in various protocol implementations.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1135-using-third-generation-nonvolatile-memory.pdf", "text": "AN1135: Using Third Generation Non-Volatile Memory (NVM3) Data Storage", "priority": 20, "category": "DOCUMENTATION", "toolTipText": "Explains how NVM3 can be used as non-volatile data storage in various protocol implementations."}, {"supportDocuments": [], "description": "Bluetooth Service API", "id": "https://docs.silabs.com/bluetooth/9.1.0/bluetooth-service-api", "text": "Bluetooth Service API", "priority": 20, "category": "DOCUMENTATION", "toolTipText": "Bluetooth Service API"}, {"supportDocuments": [], "description": "Bluetooth stack API", "id": "https://docs.silabs.com/bluetooth/9.1.0/bluetooth-stack-api", "text": "Bluetooth Stack API", "priority": 20, "category": "DOCUMENTATION", "toolTipText": "Bluetooth Stack API"}, {"supportDocuments": [], "description": "BGX13-1.x Reference", "id": "https://docs.silabs.com/gecko-os/1/bgx/latest", "text": "Gecko OS", "priority": 20, "category": "DOCUMENTATION", "toolTipText": "Gecko OS"}, {"supportDocuments": [], "description": "Introduces non-volatile data storage using flash and the three different storage implementations offered  for Silicon Labs microcontrollers and SoCs: Simulated EEPROM, PS Store, and NVM3.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/ug103-07-non-volatile-data-storage-fundamentals.pdf", "text": "UG103.07: Non-Volatile Data Storage Fundamentals", "priority": 20, "category": "DOCUMENTATION", "toolTipText": "Introduces non-volatile data storage using flash and the three different storage implementations offered  for Silicon Labs microcontrollers and SoCs: Simulated EEPROM, PS Store, and NVM3."}, {"supportDocuments": [], "description": "", "id": "https://docs.silabs.com/gecko-os/2/amw007-w00001/latest/", "text": "Wi-Fi Xpress", "priority": 20, "category": "DOCUMENTATION", "toolTipText": "Wi-Fi Xpress"}, {"supportDocuments": [], "description": "Silicon Labs Wi-SUN Stack API Reference", "id": "https://docs.silabs.com/wisun/2.5.0/wisun-stack-api", "text": "Wi-SUN Stack API", "priority": 20, "category": "DOCUMENTATION", "toolTipText": "Wi-SUN Stack API"}, {"supportDocuments": [], "description": "Z-Wave API Reference", "id": "https://docs.silabs.com/z-wave/7.23.2/zwave-api", "text": "Z-Wave API Reference", "priority": 20, "category": "DOCUMENTATION", "toolTipText": "Z-Wave API Reference"}, {"supportDocuments": [], "description": "Details methods for testing Bluetooth mesh network performance; results are intended to provide guidance  on design practices and principles as well as expected field performance results.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1137-bluetooth-mesh-network-performance.pdf", "text": "AN1137: Bluetooth Mesh Network Performance", "priority": 25, "category": "DOCUMENTATION", "toolTipText": "Details methods for testing Bluetooth mesh network performance; results are intended to provide guidance  on design practices and principles as well as expected field performance results."}, {"supportDocuments": [], "description": "Reviews the Zigbee, Thread, and Bluetooth mesh networks to evaluate their differences in performance and behavior.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1142-mesh-network-performance-comparison.pdf", "text": "AN1142: Mesh Network Performance Comparison", "priority": 25, "category": "DOCUMENTATION", "toolTipText": "Reviews the Zigbee, Thread, and Bluetooth mesh networks to evaluate their differences in performance and behavior."}, {"supportDocuments": [], "description": "Reviews performing radio frequency physical layer evaluation with EFR32BG SoCs and BGM modules using  the Direct Test Mode protocol in Bluetooth SDK v3.x.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1267-bt-rf-phy-evaluation-using-dtm-sdk-v3x.pdf", "text": "AN1267: Radio Frequency Physical Layer Evaluation in Bluetooth SDK v3.x", "priority": 25, "category": "DOCUMENTATION", "toolTipText": "Reviews performing radio frequency physical layer evaluation with EFR32BG SoCs and BGM modules using  the Direct Test Mode protocol in Bluetooth SDK v3.x."}, {"supportDocuments": [], "description": "Provides details on how to develop a dynamic multiprotocol application running Bluetooth and a  proprietary protocol on RAIL in GSDK v3.x.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1269-bluetooth-rail-dynamic-multiprotocol-gsdk-v3x.pdf", "text": "AN1269: Dynamic Multiprotocol Development with Bluetooth and Proprietary Protocols on RAIL in GSDK v3.x", "priority": 25, "category": "DOCUMENTATION", "toolTipText": "Provides details on how to develop a dynamic multiprotocol application running Bluetooth and a  proprietary protocol on RAIL in GSDK v3.x."}, {"supportDocuments": [], "description": "Includes the results of the interoperability testing of Silicon Labs' ICs and Bluetooth Mesh stack with Android and iOS smart phones.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1308-bt-mesh-interop-report.pdf", "text": "AN1308: Bluetooth Mesh Interoperability Testing Report", "priority": 25, "category": "DOCUMENTATION", "toolTipText": "Includes the results of the interoperability testing of Silicon Labs' ICs and Bluetooth Mesh stack with Android and iOS smart phones."}, {"supportDocuments": [], "description": "Describes Low Power Node (LPN) and Friend operation and the parameters related to power consumption. It also  describes how to measure the power consumption of EFR32BG devices acting as Bluetooth mesh LPNs using the setup  and procedures recommended in AN969: Measuring Power Consumption in Wireless Gecko Devices.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1315-bluetooth-mesh-power-consumption-measurements.pdf", "text": "AN1315: Bluetooth Mesh Device Power Consumption Measurements", "priority": 25, "category": "DOCUMENTATION", "toolTipText": "Describes Low Power Node (LPN) and Friend operation and the parameters related to power consumption. It also  describes how to measure the power consumption of EFR32BG devices acting as Bluetooth mesh LPNs using the setup  and procedures recommended in AN969: Measuring Power Consumption in Wireless Gecko Devices."}, {"supportDocuments": [], "description": "Describes in detail how the Bluetooth mesh toplogy can influence network operation. Provides tips on how to tune your network and its nodes to achieve best performance.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1316-bluetooth-mesh-network-optimization.pdf", "text": "AN1316: Bluetooth Mesh Parameter Tuning for Network Optimization", "priority": 25, "category": "DOCUMENTATION", "toolTipText": "Describes in detail how the Bluetooth mesh toplogy can influence network operation. Provides tips on how to tune your network and its nodes to achieve best performance."}, {"supportDocuments": [], "description": "Describes using Simplicity Studio 5's Network Analyzer to debug Bluetooth Mesh and Low Energy applications. It  can be read jointly with AN958: Debugging and Programming Interfaces for Customer Designs for more information on  using Packet Trace Interface with custom hardware.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1317-network-analyzer-with-bluetooth-mesh-le.pdf", "text": "AN1317: Using Network Analyzer with Bluetooth Low Energy and Mesh", "priority": 25, "category": "DOCUMENTATION", "toolTipText": "Describes using Simplicity Studio 5's Network Analyzer to debug Bluetooth Mesh and Low Energy applications. It  can be read jointly with AN958: Debugging and Programming Interfaces for Customer Designs for more information on  using Packet Trace Interface with custom hardware."}, {"supportDocuments": [], "description": "Describes how to run any combination of Zigbee EmberZNet, OpenThread, and Bluetooth networking stacks on a Linux host processor, interfacing with a single EFR32 Radio Co-processor (RCP) with multiprotocol and multi-PAN support, as well as how to run the Zigbee stack on the EFR32 as a network co-processor (NCP) alongside the OpenThread RCP.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1333-concurrent-protocols-with-802-15-4-rcp.pdf", "text": "AN1333: Running  Zigbee, OpenThread, and Bluetooth Concurrently on a Linux Host with a Multiprotocol Co-Processor", "priority": 25, "category": "DOCUMENTATION", "toolTipText": "Describes how to run any combination of Zigbee EmberZNet, OpenThread, and Bluetooth networking stacks on a Linux host processor, interfacing with a single EFR32 Radio Co-processor (RCP) with multiprotocol and multi-PAN support, as well as how to run the Zigbee stack on the EFR32 as a network co-processor (NCP) alongside the OpenThread RCP."}, {"supportDocuments": [], "description": "Describes how to run a combination of Zigbee, Bluetooth, and OpenThread networking stacks and the Zigbee application layer on a System-on-Chip (SoC).", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1418-concurrent-mp-soc.pdf", "text": "AN1418: Running Zigbee, OpenThread, and Bluetooth Concurrently on a System-on-Chip", "priority": 25, "category": "DOCUMENTATION", "toolTipText": "Describes how to run a combination of Zigbee, Bluetooth, and OpenThread networking stacks and the Zigbee application layer on a System-on-Chip (SoC)."}, {"supportDocuments": [], "description": "Describes the four multiprotocol modes, discusses considerations when selecting protocols for  multiprotocol implementations, and reviews the Radio Scheduler, a required component of a  dynamic multiprotocol solution.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/ug103-16-multiprotocol-fundamentals.pdf", "text": "UG103.16: Multiprotocol Fundamentals", "priority": 25, "category": "DOCUMENTATION", "toolTipText": "Describes the four multiprotocol modes, discusses considerations when selecting protocols for  multiprotocol implementations, and reviews the Radio Scheduler, a required component of a  dynamic multiprotocol solution."}, {"supportDocuments": [], "description": "Describes how to implement a dynamic multiprotocol solution.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/ug305-dynamic-multiprotocol-users-guide.pdf", "text": "UG305: Dynamic Multiprotocol User's Guide", "priority": 25, "category": "DOCUMENTATION", "toolTipText": "Describes how to implement a dynamic multiprotocol solution."}, {"supportDocuments": [], "description": "Description is unavailable", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an700-1-mfg-test-guidelines-efr32.pdf", "text": "AN700.1: Manufacturing Test Guidelines for the EFR32", "priority": 30, "category": "APP_NOTES", "toolTipText": "Description is unavailable"}, {"supportDocuments": [], "description": "Best practices for project collaboration with Simplicity Studio. Covers collaboration on Simplicity Studio projects through source control; importing received Studio projects, and exporting and sharing Studio projects.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1160-project-collaboration-with-simplicity-studio.pdf", "text": "AN1160: Project Collaboration with Simplicity Studio", "priority": 30, "category": "APP_NOTES", "toolTipText": "Best practices for project collaboration with Simplicity Studio. Covers collaboration on Simplicity Studio projects through source control; importing received Studio projects, and exporting and sharing Studio projects."}, {"supportDocuments": [], "description": "Designing with the popular Inverted-F 2.4 GHz PCB Antenna for 2.4 GHz wireless chipset designs. The Inverted-F antenna is one of the more common antennas for 2.4 GHz. Antenna dimensions in two substrate thicknesses are provided, and layout, tuning, and antenna performance are covered.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1088-designing-with-pcb-antenna.pdf", "text": "AN1088: Designing with an Inverted-F 2.4 GHz PCB Antenna", "priority": 30, "category": "APP_NOTES", "toolTipText": "Designing with the popular Inverted-F 2.4 GHz PCB Antenna for 2.4 GHz wireless chipset designs. The Inverted-F antenna is one of the more common antennas for 2.4 GHz. Antenna dimensions in two substrate thicknesses are provided, and layout, tuning, and antenna performance are covered."}, {"supportDocuments": [], "description": "Describes how to measure the power consumption of EFR32BG devices running the Bluetooth i-Beacon example.  For general instructions, see AN969: Measuring Power Consumption in Wireless Gecko Devices, available on silabs.com.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1246-efr32bg-bluetooth-power-consumption.pdf", "text": "AN1246: EFR32BG SoC Bluetooth Smart Device Power Consumption Measurements", "priority": 30, "category": "DOCUMENTATION", "toolTipText": "Describes how to measure the power consumption of EFR32BG devices running the Bluetooth i-Beacon example.  For general instructions, see AN969: Measuring Power Consumption in Wireless Gecko Devices, available on silabs.com."}, {"supportDocuments": [], "description": "Includes the results of the interoperability testing of Silicon Labs' ICs and Bluetooth Low Energy stack with Android and iOS smart phones.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1309-ble-interop-testing-report.pdf", "text": "AN1309: Bluetooth Low Energy Interoperability Testing Report", "priority": 30, "category": "DOCUMENTATION", "toolTipText": "Includes the results of the interoperability testing of Silicon Labs' ICs and Bluetooth Low Energy stack with Android and iOS smart phones."}, {"supportDocuments": [], "description": "Describes how to initialize a piece of custom hardware (a 'device') based on the EFR32MG and EFR32FG  families so that it interfaces correctly with a network stack. The same procedures can be used to  restore devices whose settings have been corrupted or erased.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/an961-custom-nodes-efr32.pdf", "text": "AN961: Bringing Up Custom Devices for the EFR32MG and EFR32FG Families", "priority": 30, "category": "DOCUMENTATION", "toolTipText": "Describes how to initialize a piece of custom hardware (a 'device') based on the EFR32MG and EFR32FG  families so that it interfaces correctly with a network stack. The same procedures can be used to  restore devices whose settings have been corrupted or erased."}, {"supportDocuments": [], "description": "Contains a comprehensive list of APIs used to interface to the Silicon Labs Connect stack.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/API_CONNECT_HTML/index.html", "text": "Silicon Labs Connect API Reference Guide", "priority": 30, "category": "DOCUMENTATION", "toolTipText": "Contains a comprehensive list of APIs used to interface to the Silicon Labs Connect stack."}, {"supportDocuments": [], "description": "Introduces the Connect User's Guide for the Flex SDK v3.x.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug435-01-about-connect-v3x-users-guide.pdf", "text": "UG435.01: About the Connect v3.x User's Guide", "priority": 31, "category": "DOCUMENTATION", "toolTipText": "Introduces the Connect User's Guide for the Flex SDK v3.x."}, {"supportDocuments": [], "description": "Introduces the IEEE 802.15.4 standard on which Connect v3.x is based.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug435-02-using-connect-v3x-with-ieee-802-15-4.pdf", "text": "UG435.02: Using Silicon Labs Connect v3.x with IEEE 802.15.4", "priority": 32, "category": "DOCUMENTATION", "toolTipText": "Introduces the IEEE 802.15.4 standard on which Connect v3.x is based."}, {"supportDocuments": [], "description": "Describes the architecture of the Silicon Labs Connect stack v3.x an how it implements IEEE 802.15.4.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug435-03-architecture-of-connect-v3x.pdf", "text": "UG435.03: Architecture of the Silicon Labs Connect Stack v3.x", "priority": 33, "category": "DOCUMENTATION", "toolTipText": "Describes the architecture of the Silicon Labs Connect stack v3.x an how it implements IEEE 802.15.4."}, {"supportDocuments": [], "description": "Describes how to use components, callbacks, and events on top of the Gecko Platform application framework to configure features and application behavior.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug435-04-customizing-applications-with-connect-v3x.pdf", "text": "UG435.04: Customizing Applications with Silicon Labs Connect v3.x", "priority": 34, "category": "DOCUMENTATION", "toolTipText": "Describes how to use components, callbacks, and events on top of the Gecko Platform application framework to configure features and application behavior."}, {"supportDocuments": [], "description": "Describes the process to implement a Connect-based application on top of one of the supported Real Time Operating Systems (RTOS).", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug435-05-using-micrium-with-connect-v3x.pdf", "text": "UG435.05: Using Real Time Operating Systems with Silicon Labs Connect v3.x", "priority": 35, "category": "DOCUMENTATION", "toolTipText": "Describes the process to implement a Connect-based application on top of one of the supported Real Time Operating Systems (RTOS)."}, {"supportDocuments": [], "description": "Explains standalone (serial) and application (OTA) bootloader options available for use within Connect v3.x -based applications", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug435-06-bootloading-and-ota-with-connect-v3x.pdf", "text": "UG435.06: Bootloading and OTA with Silicon Labs Connect v3.x", "priority": 36, "category": "DOCUMENTATION", "toolTipText": "Explains standalone (serial) and application (OTA) bootloader options available for use within Connect v3.x -based applications"}, {"supportDocuments": [], "description": "Describes the features available in Connect v3.x to reduce power consumption. Using those features is described in AN1252: Building Low Power Networks with the Silicon Labs Connect Stack v3.x.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug435-07-energy-savings-with-connect-v3x.pdf", "text": "UG435.07: Energy Saving with Silicon Labs Connect v3.x", "priority": 37, "category": "DOCUMENTATION", "toolTipText": "Describes the features available in Connect v3.x to reduce power consumption. Using those features is described in AN1252: Building Low Power Networks with the Silicon Labs Connect Stack v3.x."}, {"supportDocuments": [], "description": "Describes how to run the Silicon Labs Connect stack in Network Co-Processor (NCP) mode, where the NCP runs on the EFR32 while the Host application and the Co-processor Communication daemon (CPCd) run on the Host device.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug435-08-network-co-processor-applications-connect-v3.x.pdf", "text": "UG435.08: Network Co-Processor Applications with Silicon Labs Connect v3.x", "priority": 37, "category": "DOCUMENTATION", "toolTipText": "Describes how to run the Silicon Labs Connect stack in Network Co-Processor (NCP) mode, where the NCP runs on the EFR32 while the Host application and the Co-processor Communication daemon (CPCd) run on the Host device."}, {"supportDocuments": [], "description": "Includes detailed information on using the Silicon Labs Gecko Bootloader with Connect.  It supplements the general Gecko Bootloader implementation information provided in UG489: Silicon Labs Gecko Bootloader User's Guide.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/an1085-gecko-bootloader-connect.pdf", "text": "AN1085: Using the Gecko Bootloader with Silicon Labs Connect", "priority": 40, "category": "DOCUMENTATION", "toolTipText": "Includes detailed information on using the Silicon Labs Gecko Bootloader with Connect.  It supplements the general Gecko Bootloader implementation information provided in UG489: Silicon Labs Gecko Bootloader User's Guide."}, {"supportDocuments": [], "description": "Illustrates reducing power consumption in a Connect v3.x application using the sensor example.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/an1252-connect-v3x-low-power-networks.pdf", "text": "AN1252: Building Low Power Networks with the Silicon Labs Connect Stack v3.x", "priority": 40, "category": "DOCUMENTATION", "toolTipText": "Illustrates reducing power consumption in a Connect v3.x application using the sensor example."}, {"supportDocuments": [], "description": "Describes the sample applications provided to demonstrate the directing finding capabilities of Bluetooth 5.1. Angle of Arrival (AoA) estimation is demonstrated with the use of Silicon Labs' Real Time Locating (RTL) library.  These techniques are applicable to the EFR32MGx and EFR32BGx series.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1296-application-development-with-rtl-library.pdf", "text": "AN1296: Application Development with Silicon Labs' RTL Library", "priority": 40, "category": "DOCUMENTATION", "toolTipText": "Describes the sample applications provided to demonstrate the directing finding capabilities of Bluetooth 5.1. Angle of Arrival (AoA) estimation is demonstrated with the use of Silicon Labs' Real Time Locating (RTL) library.  These techniques are applicable to the EFR32MGx and EFR32BGx series."}, {"supportDocuments": [], "description": "Bluetooth 5.1 makes it possible to send Constant Tone Extensions (CTEs) in Bluetooth packets on which phase measurements can be done. This guide is for those implementing custom applications that take advantage of phase measurement and antenna switching capabilites.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1297-custom-direction-finding-solutions-silicon-labs-bluetooth.pdf", "text": "AN1297: Custom Direction-Finding Solutions using Silicon Labs' Bluetooth Stack", "priority": 40, "category": "DOCUMENTATION", "toolTipText": "Bluetooth 5.1 makes it possible to send Constant Tone Extensions (CTEs) in Bluetooth packets on which phase measurements can be done. This guide is for those implementing custom applications that take advantage of phase measurement and antenna switching capabilites."}, {"supportDocuments": [], "description": "Contains a comprehensive list of APIs used to interface to the Silicon Labs Bluetooth Real-Time Locating Library.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/API_BLUETOOTH_AOX_HTML/index.html", "text": "Silicon Labs Bluetooth RTL Library API Reference Guide", "priority": 40, "category": "DOCUMENTATION", "toolTipText": "Contains a comprehensive list of APIs used to interface to the Silicon Labs Bluetooth Real-Time Locating Library."}, {"supportDocuments": [], "description": "Explains the basics of Bluetooth Angle of Arrival (AoA) and Angle of Departure (AoD) direction finding technologies and provides the theory behind estimating angle of arrival.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/ug103-18-bluetooth-direction-finding-fundamentals.pdf", "text": "UG103.18: Bluetooth Direction Finding Fundamentals", "priority": 40, "category": "DOCUMENTATION", "toolTipText": "Explains the basics of Bluetooth Angle of Arrival (AoA) and Angle of Departure (AoD) direction finding technologies and provides the theory behind estimating angle of arrival."}, {"supportDocuments": [], "description": "The Bluetooth Direction Finding Tool Suite is meant to ease development with the Silicon Labs' RTL library. It provides multiple tools to configure the system, and also helps the development with analyzer tools that calculate many output parameters from the observed IQ samples.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/ug514-using-bluetooth-direction-finding-tool-suite.pdf", "text": "UG514: Using the Bluetooth Direction Finding Tool Suite", "priority": 40, "category": "DOCUMENTATION", "toolTipText": "The Bluetooth Direction Finding Tool Suite is meant to ease development with the Silicon Labs' RTL library. It provides multiple tools to configure the system, and also helps the development with analyzer tools that calculate many output parameters from the observed IQ samples."}, {"supportDocuments": [], "description": "Describes tokens and shows how to use them for non-volatile data storage in EmberZNet PRO and Silicon Labs Flex  applications.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/an1154-tokens-for-non-volatile-storage.pdf", "text": "AN1154: Using Tokens for Non-Volatile Data Storage", "priority": 42, "category": "DOCUMENTATION", "toolTipText": "Describes tokens and shows how to use them for non-volatile data storage in EmberZNet PRO and Silicon Labs Flex  applications."}, {"supportDocuments": [], "description": "Describes using the Flex SDK for Wireless M-Bus development on EFR32 Wireless Geckos.  Includes features and limitations as well as examples.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/an1119-rail-wmbus.pdf", "text": "AN1119: Using RAIL for Wireless M-Bus Applications with EFR32", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Describes using the Flex SDK for Wireless M-Bus development on EFR32 Wireless Geckos.  Includes features and limitations as well as examples."}, {"supportDocuments": [], "description": "Describes how to lock and unlock the debug access of EFR32 Gecko Series 2  devices. Many aspects of the debug access, including the secure debug unlock are described. The Debug  Challenge Interface (DCI) and Secure Engine (SE) Mailbox Interface for locking and unlocking debug  access are also included.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1190-efr32-secure-debug.pdf", "text": "AN1190:  Series 2 Secure Debug", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Describes how to lock and unlock the debug access of EFR32 Gecko Series 2  devices. Many aspects of the debug access, including the secure debug unlock are described. The Debug  Challenge Interface (DCI) and Secure Engine (SE) Mailbox Interface for locking and unlocking debug  access are also included."}, {"supportDocuments": [], "description": "Details on programming, provisioning, and configuring Series 2 devices in production environments.  Covers Secure Engine Subsystem of Series 2 devices, which runs easily upgradeable Secure Engine (SE) or Virtual Secure Engine (VSE) firmware.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1222-efr32xg2x-production-programming.pdf", "text": "AN1222:  Production Programming of Series 2 Devices", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Details on programming, provisioning, and configuring Series 2 devices in production environments.  Covers Secure Engine Subsystem of Series 2 devices, which runs easily upgradeable Secure Engine (SE) or Virtual Secure Engine (VSE) firmware."}, {"supportDocuments": [], "description": "Describes the distinguishing features of different EFR32 families that are most relevant to porting proprietary wireless applications between them. Provides insight that is also helpful when selecting an initial target platform for proprietary wireless solutions.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/an1244-migration-efr32-families.pdf", "text": "AN1244: EFR32 Migration Guide for Proprietary Applications", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Describes the distinguishing features of different EFR32 families that are most relevant to porting proprietary wireless applications between them. Provides insight that is also helpful when selecting an initial target platform for proprietary wireless solutions."}, {"supportDocuments": [], "description": "How to program, provision, and configure the anti-tamper module on EFR32 Series 2 devices with  Secure Vault.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1247-efr32-secure-vault-tamper.pdf", "text": "AN1247:  Anti-Tamper Protection Configuration and Use", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "How to program, provision, and configure the anti-tamper module on EFR32 Series 2 devices with  Secure Vault."}, {"supportDocuments": [], "description": "Describes the radio configurator <PERSON><PERSON> for RAIL framework applications in Simplicity Studio 5. With it, you can  create standard or custom radio configurations on which to run your RAIL-based applications. The role of each GUI item is explained.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/an1253-efr32-radio-configurator-guide-for-ssv5.pdf", "text": "AN1253: EFR32 Radio Configurator Guide for Simplicity Studio 5", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Describes the radio configurator <PERSON><PERSON> for RAIL framework applications in Simplicity Studio 5. With it, you can  create standard or custom radio configurations on which to run your RAIL-based applications. The role of each GUI item is explained."}, {"supportDocuments": [], "description": "How to authenticate an EFR32 Series 2 device with Secure Vault, using secure device certificates  and signatures.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1268-efr32-secure-identity.pdf", "text": "AN1268:  Authenticating Silicon Labs Devices using Device Certificates", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "How to authenticate an EFR32 Series 2 device with Secure Vault, using secure device certificates  and signatures."}, {"supportDocuments": [], "description": "How to securely \"wrap\" keys in EFR32 Series 2 devices with Secure Vault, so they can be stored in  non-volatile storage.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1271-efr32-secure-key-storage.pdf", "text": "AN1271:  Secure Key Storage", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "How to securely \"wrap\" keys in EFR32 Series 2 devices with Secure Vault, so they can be stored in  non-volatile storage."}, {"supportDocuments": [], "description": "Provides details on designing Bluetooth Low Energy applications with security and privacy in mind.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1302-bluetooth-application-security-design-considerations.pdf", "text": "AN1302:  Bluetooth Low Energy Application Security Design Considerations in SDK v3.x and Higher", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Provides details on designing Bluetooth Low Energy applications with security and privacy in mind."}, {"supportDocuments": [], "description": "Describes how to provision and configure Series 2 devices through the DCI and SWD.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1303-efr32-dci-swd-programming.pdf", "text": "AN1303:  Programming Series 2 Devices Using the Debug Challenge Interface (DCI) and Serial Wire Debug (SWD)", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Describes how to provision and configure Series 2 devices through the DCI and SWD."}, {"supportDocuments": [], "description": "Describes how to integrate crypto functionality into applications using PSA Crypto  compared to Mbed TLS.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/an1311-mbedtls-psa-crypto-porting-guide.pdf", "text": "AN1311:  Integrating Crypto Functionality Using PSA Crypto Compared to Mbed TLS", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Describes how to integrate crypto functionality into applications using PSA Crypto  compared to Mbed TLS."}, {"supportDocuments": [], "description": "Describes using the Flex SDK for 802.15.4 development on EFR32 wireless parts.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/an1365-15-4-over-rail.pdf", "text": "AN1365: Using RAIL for IEEE 802.15.4 Applications with EFR32", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Describes using the Flex SDK for 802.15.4 development on EFR32 wireless parts."}, {"supportDocuments": [], "description": "Covers the basics of ARMv8-M TrustZone, describes how TrustZone is implemented on Series 2 devices, and provides application examples.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1374-trustzone.pdf", "text": "AN1374:  Series 2 TrustZone", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Covers the basics of ARMv8-M TrustZone, describes how TrustZone is implemented on Series 2 devices, and provides application examples."}, {"supportDocuments": [], "description": "Describes the theoretical background of certificate-based authentication and pairing, and demonstrates the usage of the related sample applications that can be found in the Silicon Labs Bluetooth SDK.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/bluetooth/documentation/an1396-bluetooth-certificates.pdf", "text": "AN1396:  Certificate-Based Bluetooth Authentication and Pairing", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Describes the theoretical background of certificate-based authentication and pairing, and demonstrates the usage of the related sample applications that can be found in the Silicon Labs Bluetooth SDK."}, {"supportDocuments": [], "description": "Describes using RAILTest to evaluate radio functionality, as well as peripherals, deep sleep states, etc. With it you can fully evaluate the receiving and transmitting performance and test RF functionality of development kit hardware or custom hardware.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/an972-efr32-rf-eval-guide.pdf", "text": "AN972: EFR32 RF Evaluation Guide", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Describes using RAILTest to evaluate radio functionality, as well as peripherals, deep sleep states, etc. With it you can fully evaluate the receiving and transmitting performance and test RF functionality of development kit hardware or custom hardware."}, {"supportDocuments": [], "description": "Contains a comprehensive list of APIs used to interface to the Silicon Labs RAIL library.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/API_RAIL_HTML/index.html", "text": "Silicon Labs RAIL API Reference Guide", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Contains a comprehensive list of APIs used to interface to the Silicon Labs RAIL library."}, {"supportDocuments": [], "description": "Introduces the security concepts that must be considered when implementing an Internet of Things (IoT)  system. Using the ioXt Alliance's eight security principles as a structure, it clearly delineates  the solutions Silicon Labs provides to support endpoint security and what you must do outside of  the Silicon Labs framework.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/btmesh/documentation/ug103-05-fundamentals-security.pdf", "text": "UG103.05: IoT Endpoint Security Fundamentals", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Introduces the security concepts that must be considered when implementing an Internet of Things (IoT)  system. Using the ioXt Alliance's eight security principles as a structure, it clearly delineates  the solutions Silicon Labs provides to support endpoint security and what you must do outside of  the Silicon Labs framework."}, {"supportDocuments": [], "description": "Describes the functionality available in the RAILtest application.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug409-railtest-users-guide.pdf", "text": "UG409: RAILtest User's Guide", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Describes the functionality available in the RAILtest application."}, {"supportDocuments": [], "description": "Describes how to test long range performance on EFR32 Series 2 devices using Simplicity Studio 5 and Silicon Labs development hardware. Instructions for using example applications are included.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug460.2-efr32-series-2-long-range-configuration.pdf", "text": "UG460.2: EFR32 Series 2 Long Range Configuration Reference", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Describes how to test long range performance on EFR32 Series 2 devices using Simplicity Studio 5 and Silicon Labs development hardware. Instructions for using example applications are included."}, {"supportDocuments": [], "description": "Introduces the long-range radio profile, describes its development, and examines underlying  details that enable it to realize extended range. Instructions for using example applications are included.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug460-efr32-series-1-long-range-configuration.pdf", "text": "UG460: EFR32 Series 1 Long Range Configuration Reference", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Introduces the long-range radio profile, describes its development, and examines underlying  details that enable it to realize extended range. Instructions for using example applications are included."}, {"supportDocuments": [], "description": "Provides an easy way to evaluate the link budget of the Wireless Gecko EFR32 devices using Silicon Labs RAIL (RAIL) by performing a range test between two nodes using Range Test, a standalone test application. The range test demo implements Packet Error Rate (PER) measurement.", "id": "asset://com.silabs.sdk.stack.super_4.4.4/app/flex/documentation/ug471-flex-v3x-range-test-demo.pdf", "text": "UG471: Flex SDK v3.x Range Test Demo User's Guide", "priority": 50, "category": "DOCUMENTATION", "toolTipText": "Provides an easy way to evaluate the link budget of the Wireless Gecko EFR32 devices using Silicon Labs RAIL (RAIL) by performing a range test between two nodes using Range Test, a standalone test application. The range test demo implements Packet Error Rate (PER) measurement."}, {"supportDocuments": [], "description": "This application note will demonstrate how to use the EFR32 Series 2 EMU's Reset Management Unit to determine if a brownout reset has occurred and shows how to use the Analog Comparator to monitor the supply voltage.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an0018.2-efr32-efm32-series-2-supply-voltage-monitoring.pdf", "text": "AN0018.2: Supply Voltage Monitoring", "priority": 999, "category": "APP_NOTES", "toolTipText": "This application note will demonstrate how to use the EFR32 Series 2 EMU's Reset Management Unit to determine if a brownout reset has occurred and shows how to use the Analog Comparator to monitor the supply voltage."}, {"supportDocuments": [], "description": "Compares Power Manager with its GSDK 2.x predecessor Sleep Driver (deprecated)..", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1358-migrating-from-sleep-driver-to-power%2520manager.pdf", "text": "AN1358: Using Power Manager Instead of Sleep Driver when Migrating Projects from GSDK 2.x", "priority": 999, "category": "APP_NOTES", "toolTipText": "Compares Power Manager with its GSDK 2.x predecessor Sleep Driver (deprecated).."}, {"supportDocuments": [], "description": "Description is unavailable", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1245-efp01-configuration-tool-guide.pdf", "text": "AN1245: EFP01 Configuration Tool Guide", "priority": 999, "category": "APP_NOTES", "toolTipText": "Description is unavailable"}, {"supportDocuments": [], "description": "This application note introduces the concept of impedance matching between source and load in RF circuit applications with the aid of VSWR, reflection coefficient, and Smith chart concepts. Various types of impedance matching network architectures (2, 3, 4, or more element) are discussed in detail, and mathematical approaches to matching network design, supported by two solved numerical examples, are presented.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1275-imp-match-for-network-arch.pdf", "text": "AN1275: Impedance Matching Network Architectures", "priority": 999, "category": "APP_NOTES", "toolTipText": "This application note introduces the concept of impedance matching between source and load in RF circuit applications with the aid of VSWR, reflection coefficient, and Smith chart concepts. Various types of impedance matching network architectures (2, 3, 4, or more element) are discussed in detail, and mathematical approaches to matching network design, supported by two solved numerical examples, are presented."}, {"supportDocuments": [], "description": "Details on DALI Communication using the EFR32. Implementing Digital Addressable Lighting Interface (DALI) timing, packet formats, and Manchester encoding/decoding with minimum overhead on Wireless SoC Series 1 and Series 2 core. Supports DALI master and slave; Manchester encoding and decoding; option to use DMADRV, and contains software examples.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1220-efr32-dali.pdf", "text": "AN1220: DALI Communication Using the EFR32", "priority": 999, "category": "APP_NOTES", "toolTipText": "Details on DALI Communication using the EFR32. Implementing Digital Addressable Lighting Interface (DALI) timing, packet formats, and Manchester encoding/decoding with minimum overhead on Wireless SoC Series 1 and Series 2 core. Supports DALI master and slave; Manchester encoding and decoding; option to use DMADRV, and contains software examples."}, {"supportDocuments": [], "description": "This application note provides an overview and the theory of op-eration for the EFP01 Energy Friendly Power Management IC (PMIC).", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1187-efp01-design-considerations.pdf", "text": "AN1187: EFP01 Design Considerations", "priority": 999, "category": "APP_NOTES", "toolTipText": "This application note provides an overview and the theory of op-eration for the EFP01 Energy Friendly Power Management IC (PMIC)."}, {"supportDocuments": [], "description": "Describes using the Simplicity Studio 5 IDE and tools for application development with Bluetooth Mesh SDK v2.x.", "id": "asset://aem/www.silabs.com/documents/public/quick-start-guides/qsg176-bluetooth-mesh-sdk-v2x-quick-start-guide.pdf", "text": "QSG176: Bluetooth® Mesh Quick-Start Guide for SDK v2.x and v3.x", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Describes using the Simplicity Studio 5 IDE and tools for application development with Bluetooth Mesh SDK v2.x."}, {"supportDocuments": [], "description": "Description is unavailable", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1382-rf-performance-with-bluetooth-low-energy-simultaneous-scanning.pdf", "text": "AN1382: RF Performance with Bluetooth Low Energy Simultaneous Scanning", "priority": 999, "category": "APP_NOTES", "toolTipText": "Description is unavailable"}, {"supportDocuments": [], "description": "For small, embedded devices like Silicon Labs EFR32 living on the Tiny Edge, Machine Learning (ML) is a sophisticated way to detect and identify patterns. ML can be used as a feature to enhance embedded software applications for a number of use cases. This guide provides an introduction to ML in embedded applications, discusses ML model development, and explains the key challenges for using ML as a feature.", "id": "asset://aem/www.silabs.com/documents/public/user-guides/ug103-19-machine-learning-fundamentals.pdf", "text": "UG103.19: Machine Learning in Embedded Applications Fundamentals", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "For small, embedded devices like Silicon Labs EFR32 living on the Tiny Edge, Machine Learning (ML) is a sophisticated way to detect and identify patterns. ML can be used as a feature to enhance embedded software applications for a number of use cases. This guide provides an introduction to ML in embedded applications, discusses ML model development, and explains the key challenges for using ML as a feature."}, {"supportDocuments": [], "description": "This application note describes the configuration, calibration, and operation of the Coulomb counter on the EFP01 Energy Friendly Power Management IC (PMIC).", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1188-efp01-coulomb-counting.pdf", "text": "AN1188: EFP01 Coulomb Counting", "priority": 999, "category": "APP_NOTES", "toolTipText": "This application note describes the configuration, calibration, and operation of the Coulomb counter on the EFP01 Energy Friendly Power Management IC (PMIC)."}, {"supportDocuments": [], "description": "Description is unavailable", "id": "asset://aem/www.silabs.com/documents/public/training/mcu/um006-energy-modes.pdf", "text": "UM006: Lesson 6 - EFM32 Energy Modes", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Description is unavailable"}, {"supportDocuments": [], "description": "Examines growing consumer demand for smart lighting in the home automation market and the design challenges associated with wireless lighting connectivity.", "id": "asset://aem/www.silabs.com/documents/referenced/white-papers/smart-connected-lighting.pdf", "text": "Flipping the Switch on Smart Connected Lighting", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Examines growing consumer demand for smart lighting in the home automation market and the design challenges associated with wireless lighting connectivity."}, {"supportDocuments": [], "description": "How to use the BRD4181C Radio Board together with a Wireless Starter Kit Mainboard.", "id": "asset://aem/www.silabs.com/documents/public/user-guides/ug428-brd4181c-user-guide.pdf", "text": "UG428: EFR32xG21B 2.4 GHz 10 dBm Wireless Starter Kit User's Guide", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "How to use the BRD4181C Radio Board together with a Wireless Starter Kit Mainboard."}, {"supportDocuments": [], "description": "Bluetooth 5, Refined for the IoT", "id": "asset://aem/www.silabs.com/documents/referenced/white-papers/bluetooth-5-refined-for-the-IoT.pdf", "text": "Bluetooth 5, Refined for the IoT", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Bluetooth 5, Refined for the IoT"}, {"supportDocuments": [], "description": "Description is unavailable", "id": "asset://aem/www.silabs.com/documents/referenced/white-papers/battery-life-in-connected-wireless-iot-devices.pdf", "text": "Battery Life in Connected Wireless IoT Devices", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Description is unavailable"}, {"supportDocuments": [], "description": "Measurements of radio receive and transmit current values for EFR32 compared to data sheet values. Relevant register settings are explained. Focuses on 2.4 GHz 802.15.4 and BLE standards for receive and transmit currents.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1082-current-measurements.pdf", "text": "AN1082: EFR32 Receive and Transmit Current Measurements", "priority": 999, "category": "APP_NOTES", "toolTipText": "Measurements of radio receive and transmit current values for EFR32 compared to data sheet values. Relevant register settings are explained. Focuses on 2.4 GHz 802.15.4 and BLE standards for receive and transmit currents."}, {"supportDocuments": [], "description": "Describes security upgrades to the latest Silicon Labs wireless MCUs.", "id": "asset://aem/www.silabs.com/documents/public/white-papers/the-linley-group-microprocessor-report-silicon-labs-upgrades-wireless-mcus.pdf", "text": "The Linley Group Microprocessor Report Silicon Labs Upgrades Wireless MCUs", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Describes security upgrades to the latest Silicon Labs wireless MCUs."}, {"supportDocuments": [], "description": "Examines common methods and associated security tradeoffs of commissioning wireless devices onto networks.", "id": "asset://aem/www.silabs.com/documents/referenced/white-papers/security-tradeoffs-and-commissioning-methods-for-wireless-iot-protocols.pdf", "text": "Security Tradeoffs and Commissioning Methods for Wireless IoT Protocols", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Examines common methods and associated security tradeoffs of commissioning wireless devices onto networks."}, {"supportDocuments": [], "description": "Selecting the appropriate wireless mesh network technology for maximum efficiency in diverse IoT applications.", "id": "asset://aem/www.silabs.com/documents/referenced/white-papers/selecting-the-appropriate-wireless-mesh-network-technology.pdf", "text": "Selecting the Appropriate Wireless Mesh Network Technology", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Selecting the appropriate wireless mesh network technology for maximum efficiency in diverse IoT applications."}, {"supportDocuments": [], "description": "Describes optimization of embedded systems for low power consumption through balancing performance and power usage while preserving product function and reliability.", "id": "asset://aem/www.silabs.com/documents/public/white-papers/power-efficiency-in-embedded-systems.pdf", "text": "Balancing Performance and Power Efficiency in Embedded Systems", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Describes optimization of embedded systems for low power consumption through balancing performance and power usage while preserving product function and reliability."}, {"supportDocuments": [], "description": "Helps designers choose the best ARM Cortex core processor for their application.", "id": "asset://aem/www.silabs.com/documents/public/white-papers/Which-ARM-Cortex-Core-Is-Right-for-Your-Application.pdf", "text": "Which ARM Cortex Core Is Right for Your Application", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Helps designers choose the best ARM Cortex core processor for their application."}, {"supportDocuments": [], "description": "This porting guide is for users migrating an existing EFR32BG21 or EFR32MG21 design from revision B to revision C+. Migration entails both hardware and software considerations.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1377-xg21-revb-to-revc-migration.pdf", "text": "AN1377: EFR32xG21 Revision B to Revision C+ Compatibility and Migration Guide", "priority": 999, "category": "APP_NOTES", "toolTipText": "This porting guide is for users migrating an existing EFR32BG21 or EFR32MG21 design from revision B to revision C+. Migration entails both hardware and software considerations."}, {"supportDocuments": [], "description": "Description is unavailable", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1398-efr32-coulomb-counting.pdf", "text": "AN1398: EFR32 Coulomb Counting", "priority": 999, "category": "APP_NOTES", "toolTipText": "Description is unavailable"}, {"supportDocuments": [], "description": "This document includes the results of the interoperability testing of Silicon Labs’ ICs andBluetooth Low Energy stack with Android and iOS smart phones.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1400-ble-interop-report.pdf", "text": "AN1400: Bluetooth Low Energy Interoperability Testing Report", "priority": 999, "category": "APP_NOTES", "toolTipText": "This document includes the results of the interoperability testing of Silicon Labs’ ICs andBluetooth Low Energy stack with Android and iOS smart phones."}, {"supportDocuments": [], "description": "Highlights performance metrics of new Bluetooth Mesh 1.1 features and provides a performance overview of Silicon Labs’s Bluetooth Mesh software and hardware.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1424-bluetooth-mesh-11-network-performance.pdf", "text": "AN1424: Bluetooth® Mesh 1.1 Network Performance", "priority": 999, "category": "APP_NOTES", "toolTipText": "Highlights performance metrics of new Bluetooth Mesh 1.1 features and provides a performance overview of Silicon Labs’s Bluetooth Mesh software and hardware."}, {"supportDocuments": [], "description": "This application note describes an alternative way to implement a watchdog functionality without using the built-in Watchdog of the EFR32 Series 2 devices.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1426-low-power-lean-watchdog-solution.pdf", "text": "AN1426: Low-power Lean Watchdog Solution on EFR32 Series 2 Devices", "priority": 999, "category": "APP_NOTES", "toolTipText": "This application note describes an alternative way to implement a watchdog functionality without using the built-in Watchdog of the EFR32 Series 2 devices."}, {"supportDocuments": [], "description": "Description is unavailable", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1492-clock-manager-migration-guide.pdf", "text": "AN1492: Clock Manager Migration Guide", "priority": 999, "category": "APP_NOTES", "toolTipText": "Description is unavailable"}, {"supportDocuments": [], "description": "Bluetooth 5.4 introduces support for bi-directional, large-scale, one-to-many networking, driven by wireless standardization needs of the electronics shelf label (ESL) market. This low-power networking capability opens new opportunities in other markets as well, including shelf sensors, manufacturing and logistics, asset monitoring, and agriculture use cases. The new Periodic Advertisement with Responses (PAwR) and Encrypted Advertisement Data (EAD) features large scale and secure networking capabilities. Additionally, the new Bluetooth ESL Service and Profile helps to create interoperability, taking advantage of the benefits of the new Bluetooth 5.4 features. Join this session to learn more about the applications and how new Bluetooth 5.4 features are enabling these in the IoT market.", "id": "asset://aem/www.silabs.com/documents/public/presentations/bt-203-everything-you-need-to-know-about-bluetooth-5-4.pdf", "text": "Bluetooth 5.4: Everything You Need to Know", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Bluetooth 5.4 introduces support for bi-directional, large-scale, one-to-many networking, driven by wireless standardization needs of the electronics shelf label (ESL) market. This low-power networking capability opens new opportunities in other markets as well, including shelf sensors, manufacturing and logistics, asset monitoring, and agriculture use cases. The new Periodic Advertisement with Responses (PAwR) and Encrypted Advertisement Data (EAD) features large scale and secure networking capabilities. Additionally, the new Bluetooth ESL Service and Profile helps to create interoperability, taking advantage of the benefits of the new Bluetooth 5.4 features. Join this session to learn more about the applications and how new Bluetooth 5.4 features are enabling these in the IoT market."}, {"supportDocuments": [], "description": "For the past decade, tracking solutions have relied on the RSSI method for distance measurement which is prone to unfavorable environmental conditions and can result in inaccurate results. The latest advancements in Bluetooth HADM (High Accuracy Distance Measurement) technology enable users to accurately measure the distance between two Bluetooth devices. With centimeter-level location accuracy, this technology can disrupt the current trends in asset tracking, item finding, and proximity-aware systems. Silicon Labs’ EFR32MG24 device supports different measurement techniques in its system-on-chip (SoC) and Bluetooth stack with advanced ranging algorithms that improve accuracy and reduce complexity in application development. Join this session to elevate your business by discovering the recent additions to our HADM solution.", "id": "asset://aem/www.silabs.com/documents/public/presentations/bt-202-bluetooth-hadm-perfecting-location-centric-services.pdf", "text": "Bluetooth HADM: Perfecting Location Services", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "For the past decade, tracking solutions have relied on the RSSI method for distance measurement which is prone to unfavorable environmental conditions and can result in inaccurate results. The latest advancements in Bluetooth HADM (High Accuracy Distance Measurement) technology enable users to accurately measure the distance between two Bluetooth devices. With centimeter-level location accuracy, this technology can disrupt the current trends in asset tracking, item finding, and proximity-aware systems. Silicon Labs’ EFR32MG24 device supports different measurement techniques in its system-on-chip (SoC) and Bluetooth stack with advanced ranging algorithms that improve accuracy and reduce complexity in application development. Join this session to elevate your business by discovering the recent additions to our HADM solution."}, {"supportDocuments": [], "description": "Enterprise Access Points are the “Swiss Army Knives” of the IoT, providing the RF infrastructure and backbone for a wide spectrum of emerging IoT applications. Whether connecting thousands of electronic shelf labels in the retail space, managing street lights in the smart city, controlling door locks in the hospitality space, or tracking crash carts in a smart hospital, this device is both the brains of the network as well as the interface to the rest of the world. The EAP must provide the performance, the scalability, the reliability, and the security that is required by industrial and commercial applications – all while co-existing with the collocated high performance enterprise Wi-Fi solution. Join us to learn about the Wi-Fi enterprise access point and its design requirements to enable a robust and diverse offering of IoT applications.", "id": "asset://aem/www.silabs.com/documents/public/presentations/bt-204-design-iot-ready-enterprise-grade-wi-fi-access-points.pdf", "text": "Design IoT-Ready Enterprise Wi-Fi Access Points", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Enterprise Access Points are the “Swiss Army Knives” of the IoT, providing the RF infrastructure and backbone for a wide spectrum of emerging IoT applications. Whether connecting thousands of electronic shelf labels in the retail space, managing street lights in the smart city, controlling door locks in the hospitality space, or tracking crash carts in a smart hospital, this device is both the brains of the network as well as the interface to the rest of the world. The EAP must provide the performance, the scalability, the reliability, and the security that is required by industrial and commercial applications – all while co-existing with the collocated high performance enterprise Wi-Fi solution. Join us to learn about the Wi-Fi enterprise access point and its design requirements to enable a robust and diverse offering of IoT applications."}, {"supportDocuments": [], "description": "Learn about the design of ultra-low power, small form factor Bluetooth medical devices as we explore the Silicon Labs continuous glucose monitor (CGM) Reference Design. Utilizing the recently announced BG27, we will discuss how to optimize your wireless medical device design, reducing size and complexity, minimizing power consumption, and optimizing overall performance and functionality while keeping your product cost to a minimum. This session will cover hardware and software considerations from PCB layout, component selection, interfaces, and application development.", "id": "asset://aem/www.silabs.com/documents/public/presentations/bt-205-designing-medical-devices-with-the-bg27.pdf", "text": "Designing Bluetooth Medical Devices with BG27", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Learn about the design of ultra-low power, small form factor Bluetooth medical devices as we explore the Silicon Labs continuous glucose monitor (CGM) Reference Design. Utilizing the recently announced BG27, we will discuss how to optimize your wireless medical device design, reducing size and complexity, minimizing power consumption, and optimizing overall performance and functionality while keeping your product cost to a minimum. This session will cover hardware and software considerations from PCB layout, component selection, interfaces, and application development."}, {"supportDocuments": [], "description": "Bluetooth LE single mode shipments are set to see 3x growth between 2021-2025, driven by existing IoT markets like smart lighting and wearables as well as newer markets like portable medical, smart appliances, industrial automation, and audio. One of the key reasons is that Bluetooth LE has consistently evolved to incorporate new features that address the key considerations for IoT like low power, security, range, reliability, and now audio. Join this session to learn more about the applications and technology trends driving Bluetooth LE adoption in the IoT market.", "id": "asset://aem/www.silabs.com/documents/public/presentations/bt-101-emerging-bluetooth-le-use-cases-and-applications-eh.pdf", "text": "Emerging Bluetooth LE Use Cases and Applications", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Bluetooth LE single mode shipments are set to see 3x growth between 2021-2025, driven by existing IoT markets like smart lighting and wearables as well as newer markets like portable medical, smart appliances, industrial automation, and audio. One of the key reasons is that Bluetooth LE has consistently evolved to incorporate new features that address the key considerations for IoT like low power, security, range, reliability, and now audio. Join this session to learn more about the applications and technology trends driving Bluetooth LE adoption in the IoT market."}, {"supportDocuments": [], "description": "The upcoming Bluetooth mesh enhancements introduce multiple new features that simplify and speed up the deployment and maintenance of Bluetooth mesh networks. This session will provide an overview of six of the new key features and their benefits of the Bluetooth 1.1 specification: Remote provisioning (RPR), Device Firmware Update (DFU), Certificate Based Provisioning (CBP), Directed Forwarding (MDF), Subnet Bridging (SBR), and Private Beacons. Join us to learn more about the new Bluetooth mesh standard.", "id": "asset://aem/www.silabs.com/documents/public/presentations/bt-201-features-and-benefits-of-new-bluetooth-mesh-standard.pdf", "text": "New Bluetooth Mesh Standard: Features and Benefits", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "The upcoming Bluetooth mesh enhancements introduce multiple new features that simplify and speed up the deployment and maintenance of Bluetooth mesh networks. This session will provide an overview of six of the new key features and their benefits of the Bluetooth 1.1 specification: Remote provisioning (RPR), Device Firmware Update (DFU), Certificate Based Provisioning (CBP), Directed Forwarding (MDF), Subnet Bridging (SBR), and Private Beacons. Join us to learn more about the new Bluetooth mesh standard."}, {"supportDocuments": [], "description": "Description is unavailable", "id": "asset://aem/www.silabs.com/documents/public/presentations/tech-talks-the-latest-in-hadm-with-bluetooth-le.pdf", "text": "The Latest in HADM with Bluetooth LE", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "Description is unavailable"}, {"supportDocuments": [], "description": "This document describes how to use the BRD4195B Radio Board together with a Wireless Starter Kit Mainboard or a Wireless Pro Kit Mainboard. A Wireless Starter Kit with the BRD4195B Radio Board is an excellent starting point to get familiar with the EFR32 Wireless Gecko Wireless System-on-Chip.", "id": "asset://aem/www.silabs.com/documents/public/user-guides/ug556-brd4195b-user-guide.pdf", "text": "UG556: EFR32xG21B 2.4 GHz 10 dBm Radio Board User's Guide", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "This document describes how to use the BRD4195B Radio Board together with a Wireless Starter Kit Mainboard or a Wireless Pro Kit Mainboard. A Wireless Starter Kit with the BRD4195B Radio Board is an excellent starting point to get familiar with the EFR32 Wireless Gecko Wireless System-on-Chip."}, {"supportDocuments": [], "description": "This document describes how to use the BRD4196B Radio Board together with a Wireless Starter Kit Mainboard or a Wireless Pro Kit Mainboard. A Wireless Starter Kit with the BRD4196B Radio Board is an excellent starting point to get familiar with the EFR32 Wireless Gecko Wireless System-on-Chip.", "id": "asset://aem/www.silabs.com/documents/public/user-guides/ug557-brd4196b-user-guide.pdf", "text": "UG557: EFR32xG21B 2.4 GHz 20 dBm Radio Board User's Guide", "priority": 999, "category": "DOCUMENTATION", "toolTipText": "This document describes how to use the BRD4196B Radio Board together with a Wireless Starter Kit Mainboard or a Wireless Pro Kit Mainboard. A Wireless Starter Kit with the BRD4196B Radio Board is an excellent starting point to get familiar with the EFR32 Wireless Gecko Wireless System-on-Chip."}, {"supportDocuments": [], "description": "This application note demonstrates how to use the Watchdog module on EFR32 Series 2 devices. For watchdog information for EFM32 and EZR32 Wireless MCU Series 0 devices, refer to AN0015.0: EFM32 and EZR32 Wireless MCU Series 0 Watchdog. For watchdog information regarding EFM32 and EFR32 Series 1 devices, please refer to AN0015.1: EFM32 and EFR32 Series 1 Watchdog.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an0015.2-efr32-series-2-watchdog.pdf", "text": "AN0015.2: EFR32 Series 2 Watchdog", "priority": 9999999, "category": "APP_NOTES", "toolTipText": "This application note demonstrates how to use the Watchdog module on EFR32 Series 2 devices. For watchdog information for EFM32 and EZR32 Wireless MCU Series 0 devices, refer to AN0015.0: EFM32 and EZR32 Wireless MCU Series 0 Watchdog. For watchdog information regarding EFM32 and EFR32 Series 1 devices, please refer to AN0015.1: EFM32 and EFR32 Series 1 Watchdog."}, {"supportDocuments": [], "description": "This application note provides an overview of the CMU module for EFR32 Wireless Gecko Series 2 devices with explanations on how to choose clock sources, prescaling, and clock calibration.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an0004.2-efr32-series2-cmu.pdf", "text": "AN0004.2: EFR32 Series 2 Wireless MCU Clock Management Unit (CMU)", "priority": 9999999, "category": "APP_NOTES", "toolTipText": "This application note provides an overview of the CMU module for EFR32 Wireless Gecko Series 2 devices with explanations on how to choose clock sources, prescaling, and clock calibration."}, {"supportDocuments": [], "description": "Introduces bootloading for Silicon Labs networking devices. Discusses the Gecko Bootloader and describes the file formats used by each.", "id": "asset://aem/www.silabs.com/documents/public/user-guides/ug103-06-fundamentals-bootloading.pdf", "text": "UG103.6: Bootloader Fundamentals", "priority": 9999999, "category": "DOCUMENTATION", "toolTipText": "Introduces bootloading for Silicon Labs networking devices. Discusses the Gecko Bootloader and describes the file formats used by each."}, {"supportDocuments": [], "description": "Explains methods of measuring the power consumption of  Silicon Labs Wireless Gecko devices.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an969-measuring-power-consumption.pdf", "text": "AN969: Measuring Power Consumption on Wireless Gecko Devices", "priority": 9999999, "category": "APP_NOTES", "toolTipText": "Explains methods of measuring the power consumption of  Silicon Labs Wireless Gecko devices."}, {"supportDocuments": [], "description": "This porting guide is targeted at migrating an existing design from Series 1 to Wireless Gecko Series 2. Both hardware and software migration needs to be considered.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an0918.2-efm32-to-efr32xg2x-migration-guide.pdf", "text": "AN0918.2: Gecko Series 1 to Series 2 Compatibility and Migration Guide", "priority": 9999999, "category": "APP_NOTES", "toolTipText": "This porting guide is targeted at migrating an existing design from Series 1 to Wireless Gecko Series 2. Both hardware and software migration needs to be considered."}, {"supportDocuments": [], "description": "Discusses the major decisions that must be made about which wireless protocol you should use, as well as additional decisions to be made if you are designing a Zigbee solution.", "id": "asset://aem/www.silabs.com/documents/public/user-guides/ug103-03-fundamentals-design-choices.pdf", "text": "UG103.03: Software Design Fundamentals", "priority": 9999999, "category": "DOCUMENTATION", "toolTipText": "Discusses the major decisions that must be made about which wireless protocol you should use, as well as additional decisions to be made if you are designing a Zigbee solution."}, {"supportDocuments": [], "description": "Describes how to initialize and customize peripherals for 32-bit devices, both in the Simplicity IDE and using the standalone Hardware Configurator tool.. Does not apply to the Bluetooth SDK or the 32-bit MCU SDK.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1115-32-bit-device-peripheral-configuration-in-simplicity-studio.pdf", "text": "AN1115: Configuring Peripherals for 32-Bit Devices using Hardware Configurator", "priority": 9999999, "category": "APP_NOTES", "toolTipText": "Describes how to initialize and customize peripherals for 32-bit devices, both in the Simplicity IDE and using the standalone Hardware Configurator tool.. Does not apply to the Bluetooth SDK or the 32-bit MCU SDK."}, {"supportDocuments": [], "description": "EFR32BG21 Wireless Gecko Errata", "id": "asset://aem/www.silabs.com/documents/public/errata/efr32bg21-errata.pdf", "text": "EFR32BG21 Errata", "priority": 9999999, "category": "DOCUMENTATION", "toolTipText": "EFR32BG21 Wireless Gecko Errata"}, {"supportDocuments": [], "description": "Describes the high-level implementation of the Silicon Labs Gecko Bootloader for EFR32 SoCs and NCPs, and provides information on how to get started using the Gecko Bootloader with Silicon Labs wireless protocol stacks.", "id": "asset://aem/www.silabs.com/documents/public/user-guides/ug266-gecko-bootloader-user-guide.pdf", "text": "UG266: Silicon Labs Gecko Bootloader User’s Guide for GSDK 3.2 and Lower", "priority": 9999999, "category": "DOCUMENTATION", "toolTipText": "Describes the high-level implementation of the Silicon Labs Gecko Bootloader for EFR32 SoCs and NCPs, and provides information on how to get started using the Gecko Bootloader with Silicon Labs wireless protocol stacks."}, {"supportDocuments": [], "description": "This application note describes a standalone programmer to program the internal flash and user page of EFM32 Gecko, Series 0 and 1, EZR32 Series 0, and EFR32 Wireless Gecko Series 1 devices that use the Serial Wire Debug (SWD) interface. Details on how to use the SWD interface to program device can be found in AN0062: Programming Internal Flash over the Serial Wire Debug Interface. This document focuses on how to optimize the process when programming the devices. The objectives of a standalone programmer are low cost, easy to build, simple to use and no PC connection is required. For simplicity, EFM32 Wonder Gecko, Gecko, Giant Gecko, <PERSON>pard Gecko, Tiny Gecko, Zero Gecko, and Happy Gecko are a part of the EFM32 Gecko Series 0. EZR32 Wonder Gecko, <PERSON><PERSON> Gecko, and <PERSON> Gecko are a part of the EZR32 Wireless MCU Series 0. EFM32 Pearl Gecko and Jade Gecko (and future devices) are a part of the EFM32 Gecko Series 1. EFR32 Blue Gecko, Flex Gecko, and Mighty Gecko are a part of the EFR32 Wireless Gecko Series 1.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/AN1011-efm32-standalone-programmer.pdf", "text": "AN1011: Standalone Programmer via the SWD Interface", "priority": 9999999, "category": "APP_NOTES", "toolTipText": "This application note describes a standalone programmer to program the internal flash and user page of EFM32 Gecko, Series 0 and 1, EZR32 Series 0, and EFR32 Wireless Gecko Series 1 devices that use the Serial Wire Debug (SWD) interface. Details on how to use the SWD interface to program device can be found in AN0062: Programming Internal Flash over the Serial Wire Debug Interface. This document focuses on how to optimize the process when programming the devices. The objectives of a standalone programmer are low cost, easy to build, simple to use and no PC connection is required. For simplicity, EFM32 Wonder Gecko, Gecko, Giant Gecko, <PERSON>pard Gecko, Tiny Gecko, Zero Gecko, and Happy Gecko are a part of the EFM32 Gecko Series 0. EZR32 Wonder Gecko, <PERSON><PERSON> Gecko, and <PERSON> Gecko are a part of the EZR32 Wireless MCU Series 0. EFM32 Pearl Gecko and Jade Gecko (and future devices) are a part of the EFM32 Gecko Series 1. EFR32 Blue Gecko, Flex Gecko, and Mighty Gecko are a part of the EFR32 Wireless Gecko Series 1."}, {"supportDocuments": [], "description": "Reference material for EFR32xG21 Wireless Gecko devices.", "id": "asset://aem/www.silabs.com/documents/public/reference-manuals/efr32xg21-rm.pdf", "text": "EFR32xG21 Wireless Gecko Reference Manual", "priority": 9999999, "category": "DOCUMENTATION", "toolTipText": "Reference material for EFR32xG21 Wireless Gecko devices."}, {"supportDocuments": [], "description": "This application note describes the EFR32 Gecko Series 2 Incremental Analog to Digital Converter (IADC) operation and advanced features. In addition, this document explains how to use the IADC to convert an analog input voltage to a digital value and features a high-speed, low-power operation. Many aspects of the IADC, including inputs, references, and the different operating modes are described.", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an1189-efr32-iadc.pdf", "text": "AN1189: Incremental Analog to Digital Converter (IADC)", "priority": 9999999, "category": "APP_NOTES", "toolTipText": "This application note describes the EFR32 Gecko Series 2 Incremental Analog to Digital Converter (IADC) operation and advanced features. In addition, this document explains how to use the IADC to convert an analog input voltage to a digital value and features a high-speed, low-power operation. Many aspects of the IADC, including inputs, references, and the different operating modes are described."}, {"supportDocuments": [], "description": "Description is unavailable", "id": "asset://aem/www.silabs.com/documents/public/data-sheets/efr32bg21-datasheet.pdf", "text": "EFR32BG21 BLE Wireless SoC Family Data Sheet", "priority": 9999999, "category": "DOCUMENTATION", "toolTipText": "Description is unavailable"}, {"supportDocuments": [], "description": "This application note describes usage of the EFM32 general-purpose input/output (GPIO) subsystem. This document discusses configuration, read and writing pin values, peripheral function routing, external interrupt capability, and use of GPIO pins as producers for the Peripheral Reflex System (PRS). Example projects that illustrate these concepts can be run on the Starter Kit boards for many different EFM32 derivatives. This application note includes: This PDF document Source files (zip)  Example C source code Multiple IDE projects", "id": "asset://aem/www.silabs.com/documents/public/application-notes/an0012-efm32-gpio.pdf", "text": "AN0012: General Purpose Input Output", "priority": 9999999, "category": "APP_NOTES", "toolTipText": "This application note describes usage of the EFM32 general-purpose input/output (GPIO) subsystem. This document discusses configuration, read and writing pin values, peripheral function routing, external interrupt capability, and use of GPIO pins as producers for the Peripheral Reflex System (PRS). Example projects that illustrate these concepts can be run on the Starter Kit boards for many different EFM32 derivatives. This application note includes: This PDF document Source files (zip)  Example C source code Multiple IDE projects"}, {"supportDocuments": [], "description": "Description is unavailable", "id": "asset://aem/www.silabs.com/documents/referenced/white-papers/the-many-flavors-of-bluetooth-iot-connectivity.pdf", "text": "The Many Flavors of Bluetooth IoT Connectivity Whitepaper", "priority": 9999999, "category": "DOCUMENTATION", "toolTipText": "Description is unavailable"}, {"supportDocuments": [], "description": "Description is unavailable", "id": "asset://aem/www.silabs.com/documents/referenced/white-papers/bluetooth-smart-technologies-fundamentals.pdf", "text": "Designing for Bluetooth Low Energy Applications", "priority": 9999999, "category": "DOCUMENTATION", "toolTipText": "Description is unavailable"}, {"supportDocuments": [], "description": "Bluetooth Smart (BLE) Beacon Applications and Design", "id": "asset://aem/www.silabs.com/documents/referenced/white-papers/developing-beacons-with-bluetooth-low-energy-technology.pdf", "text": "Developing Beacons with Bluetooth low energy (BLE) Technology", "priority": 9999999, "category": "DOCUMENTATION", "toolTipText": "Bluetooth Smart (BLE) Beacon Applications and Design"}, {"supportDocuments": [], "description": "Description is unavailable", "id": "asset://aem/www.silabs.com/documents/public/presentations/Bluetooth-EPL-guide-for-2014.pdf", "text": "Bluetooth End Product Listing Guide", "priority": 9999999, "category": "DOCUMENTATION", "toolTipText": "Description is unavailable"}], "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [{"futureCount": 88, "anySelected": false, "id": 0, "filters": [], "title": "Application Notes", "parentId": 0, "selected": false}, {"futureCount": 1, "anySelected": false, "id": 1, "filters": [], "title": "Data Sheets", "parentId": 0, "selected": false}, {"futureCount": 1, "anySelected": false, "id": 2, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 0, "selected": false}, {"futureCount": 6, "anySelected": false, "id": 3, "filters": [], "title": "Quick Start Guides", "parentId": 0, "selected": false}, {"futureCount": 7, "anySelected": false, "id": 4, "filters": [], "title": "Reference Manuals", "parentId": 0, "selected": false}, {"futureCount": 5, "anySelected": false, "id": 5, "filters": [], "title": "Release Notes", "parentId": 0, "selected": false}, {"futureCount": 6, "anySelected": false, "id": 6, "filters": [], "title": "Software API Documentation", "parentId": 0, "selected": false}, {"futureCount": 38, "anySelected": false, "id": 7, "filters": [], "title": "User's Guides", "parentId": 0, "selected": false}, {"futureCount": 12, "anySelected": false, "id": 8, "filters": [], "title": "White Papers", "parentId": 0, "selected": false}], "title": "Resource Type", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [{"futureCount": 56, "anySelected": false, "id": 0, "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 2, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 3, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 4, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 5, "filters": [], "title": "silicon-labs-software:software-development-kits/bluetooth-sdk", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 6, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 7, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 8, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 9, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 10, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 11, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 12, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 13, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 14, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 15, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 16, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 17, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 18, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 19, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 20, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 21, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 22, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 23, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 24, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 25, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 26, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 27, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 28, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 29, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 30, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 31, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 32, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 33, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 34, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 35, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 36, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 37, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 38, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 39, "filters": [], "title": "silicon-labs-software:software-development-kits/bluetooth-sdk", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 40, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 41, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 42, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 43, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 44, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 45, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 46, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 47, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 48, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 49, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 50, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 51, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 52, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 53, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 54, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 55, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 56, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 57, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.ble:7.1.2.0_-2063853123", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 58, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 59, "filters": [], "title": "silicon-labs-software:software-development-kits/bluegiga-bluetooth-smart-sdk", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 60, "filters": [], "title": "silicon-labs-software:software-development-kits/bluetooth-sdk", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 61, "filters": [], "title": "silicon-labs-software:software-development-kits/bluetooth-sdk", "parentId": 0, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 62, "filters": [], "title": "silicon-labs-software:software-development-kits/bluetooth-sdk", "parentId": 0, "selected": true}], "title": "Bluetooth", "parentId": 1, "selected": false}, {"futureCount": 53, "anySelected": false, "id": 1, "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 2, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 3, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 4, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 5, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 6, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 7, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 8, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 9, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 10, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 11, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 12, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 13, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 14, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 15, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 16, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 17, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 18, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 19, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 20, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 21, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 22, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 23, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 24, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 25, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 26, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 27, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 28, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 29, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 30, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 31, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 32, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 33, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 34, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 35, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 36, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 37, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 38, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 39, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 40, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 41, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 42, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 43, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 44, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 45, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 46, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 47, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 48, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 49, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 50, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 51, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 52, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 53, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 54, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 55, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 56, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 57, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 58, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 59, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 60, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 61, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 62, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 63, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 64, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 65, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 66, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 67, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 68, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 69, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 70, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 71, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 72, "filters": [], "title": "Bluetooth Mesh", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 73, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.btMesh:6.1.2.0_1914391595", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 74, "filters": [], "title": "silicon-labs-software:software-development-kits/bluetooth-mesh-sdk", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 75, "filters": [], "title": "silicon-labs-software:software-development-kits/bluetooth-mesh-sdk", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 76, "filters": [], "title": "silicon-labs-software:software-development-kits/bluetooth-mesh-sdk", "parentId": 1, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 77, "filters": [], "title": "silicon-labs-software:software-development-kits/bluetooth-mesh-sdk", "parentId": 1, "selected": true}], "title": "Bluetooth Mesh", "parentId": 1, "selected": false}, {"futureCount": 1, "anySelected": false, "id": 2, "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.sdk.stack.bootloader:2.1.0._1650478304", "parentId": 2, "selected": true}], "title": "Bootloader", "parentId": 1, "selected": false}, {"futureCount": 5, "anySelected": false, "id": 3, "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.sdk.micrium:5.15.0._1259617192", "parentId": 3, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.sdk.micrium:5.15.0._1259617192", "parentId": 3, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 2, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.sdk.micrium:5.15.0._1259617192", "parentId": 3, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 3, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.sdk.micrium:5.15.0._1259617192", "parentId": 3, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 4, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.sdk.micrium:5.15.0._1259617192", "parentId": 3, "selected": true}], "title": "Operating Systems", "parentId": 1, "selected": false}, {"futureCount": 52, "anySelected": false, "id": 4, "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 2, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 3, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 4, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 5, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 6, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 7, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 8, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 9, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 10, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 11, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 12, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 13, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 14, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 15, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 16, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 17, "filters": [], "title": "silicon-labs-software:software-development-kits/connect-sdk", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 18, "filters": [], "title": "silicon-labs-software:software-development-kits/flex-sdk", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 19, "filters": [], "title": "silicon-labs-software:software-development-kits/rail-sdk", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 20, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 21, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 22, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 23, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 24, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 25, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 26, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 27, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 28, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 29, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 30, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 31, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 32, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 33, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 34, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 35, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 36, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 37, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 38, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 39, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 40, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 41, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 42, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 43, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 44, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 45, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 46, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 47, "filters": [], "title": "Proprietary", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 48, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 49, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 50, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 51, "filters": [], "title": "com.silabs.sdk.stack.super:4.4.4._-85640013.com.silabs.stack.flex:3.7.4.0_-1458823386", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 52, "filters": [], "title": "silicon-labs-software:software-development-kits/connect-sdk", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 53, "filters": [], "title": "silicon-labs-software:software-development-kits/flex-sdk", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 54, "filters": [], "title": "silicon-labs-software:software-development-kits/rail-sdk", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 55, "filters": [], "title": "silicon-labs-software:software-development-kits/connect-sdk", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 56, "filters": [], "title": "silicon-labs-software:software-development-kits/flex-sdk", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 57, "filters": [], "title": "silicon-labs-software:software-development-kits/rail-sdk", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 58, "filters": [], "title": "silicon-labs-software:software-development-kits/connect-sdk", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 59, "filters": [], "title": "silicon-labs-software:software-development-kits/flex-sdk", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 60, "filters": [], "title": "silicon-labs-software:software-development-kits/rail-sdk", "parentId": 4, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 61, "filters": [], "title": "silicon-labs-software:software-development-kits/connect-sdk", "parentId": 4, "selected": true}], "title": "Proprietary", "parentId": 1, "selected": false}, {"futureCount": 28, "anySelected": false, "id": 5, "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 2, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 3, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 4, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 5, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 6, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 7, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 8, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 9, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 10, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 11, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 12, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 13, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 14, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 15, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 16, "filters": [], "title": "silicon-labs-software:software-development-kits/thread-sdk", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 17, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 18, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 19, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 20, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 21, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 22, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 23, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 24, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 25, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 26, "filters": [], "title": "silicon-labs-software:software-development-kits/thread-sdk", "parentId": 5, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 27, "filters": [], "title": "silicon-labs-software:software-development-kits/thread-sdk", "parentId": 5, "selected": true}], "title": "<PERSON><PERSON><PERSON>", "parentId": 1, "selected": false}, {"futureCount": 11, "anySelected": false, "id": 6, "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [], "title": "Wi-SUN", "parentId": 6, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [], "title": "Wi-SUN", "parentId": 6, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 2, "filters": [], "title": "Wi-SUN", "parentId": 6, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 3, "filters": [], "title": "Wi-SUN", "parentId": 6, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 4, "filters": [], "title": "Wi-SUN", "parentId": 6, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 5, "filters": [], "title": "Wi-SUN", "parentId": 6, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 6, "filters": [], "title": "Wi-SUN", "parentId": 6, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 7, "filters": [], "title": "Wi-SUN", "parentId": 6, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 8, "filters": [], "title": "Wi-SUN", "parentId": 6, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 9, "filters": [], "title": "Wi-SUN", "parentId": 6, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 10, "filters": [], "title": "Wi-SUN", "parentId": 6, "selected": true}], "title": "Wi-SUN", "parentId": 1, "selected": false}, {"futureCount": 3, "anySelected": false, "id": 7, "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [], "title": "Z-Wave", "parentId": 7, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [], "title": "Z-Wave", "parentId": 7, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 2, "filters": [], "title": "silicon-labs-software:software-development-kits/z-wave-sdk", "parentId": 7, "selected": true}], "title": "Z-Wave", "parentId": 1, "selected": false}, {"futureCount": 31, "anySelected": false, "id": 8, "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 2, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 3, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 4, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 5, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 6, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 7, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 8, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 9, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 10, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 11, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 12, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 13, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 14, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 15, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 16, "filters": [], "title": "silicon-labs-software:software-development-kits/zigbee-sdk", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 17, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 18, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 19, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 20, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 21, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 22, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 23, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 24, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 25, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 26, "filters": [], "title": "Zigbee", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 27, "filters": [], "title": "silicon-labs-software:software-development-kits/zigbee-sdk", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 28, "filters": [], "title": "silicon-labs-software:software-development-kits/zigbee-sdk", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 29, "filters": [], "title": "silicon-labs-software:software-development-kits/zigbee-sdk", "parentId": 8, "selected": true}, {"futureCount": 0, "anySelected": false, "id": 30, "filters": [], "title": "silicon-labs-software:software-development-kits/zigbee-sdk", "parentId": 8, "selected": true}], "title": "Zigbee", "parentId": 1, "selected": false}], "title": "Technology Type", "parentId": -1, "selected": false}], "totalCount": 178}