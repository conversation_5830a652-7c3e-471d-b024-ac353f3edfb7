{"html": "\n   <article>\n    \n    <div class=\"header\">\n     <div class=\"headertitle\">\n      <h1 class=\"title\">\n       Simple RGB PWM LED Driver\n       <div class=\"ingroups\">\n        <a href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led\" target=\"_blank\">\n         LED Driver\n        </a>\n       </div>\n      </h1>\n     </div>\n    </div>\n    <div class=\"contents\">\n     <a id=\"details\" name=\"details\">\n     </a>\n     <h2 class=\"groupheader\">\n      Description\n     </h2>\n     <p>\n      Simple Red/Green/Blue PWM LED Driver.\n     </p>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"rgb-led-intro\">\n      </a>\n      Introduction\n     </h1>\n     <p>\n      The Simple RGB PWM LED Driver is a module for the LED driver that provides functionality for controlling Red/Green/Blue LEDs that are driven by PWM.\n     </p>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"rgb-led-config\">\n      </a>\n      RGB PWM LED Configuration\n     </h1>\n     <p>\n      RGB PWM LEDs use the\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n       sl_led_t\n      </a>\n      struct, and their own structs\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-simple-rgb-pwm-led-context-t\" target=\"_blank\">\n       sl_simple_rgb_pwm_led_context_t\n      </a>\n      and\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-rgb-pwm-t\" target=\"_blank\">\n       sl_led_rgb_pwm_t\n      </a>\n      . These are automatically generated into the following files, as well as instance specific headers with macro definitions in them. The samples below are for a single instance called \"inst0\".\n     </p>\n     <div class=\"fragment\">\n      <div class=\"line\">\n       <span class=\"comment\">\n        // sl_simple_rgb_pwm_led_instances.c\n       </span>\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #include \"em_gpio.h\"\n       </span>\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #include \"sl_simple_rgb_pwm_led.h\"\n       </span>\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #include \"sl_simple_rgb_pwm_led_inst0_config.h\"\n       </span>\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-pwm-t\" target=\"_blank\">\n        sl_led_pwm_t\n       </a>\n       red_inst0 = {\n      </div>\n      <div class=\"line\">\n       .\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-pwm-t#ac9499526a3b385f00232a5f70f7578cd\" target=\"_blank\">\n        port\n       </a>\n       = SIMPLE_RGB_PWM_LED_INST0_PORT,\n      </div>\n      <div class=\"line\">\n       .pin = SIMPLE_RGB_PWM_LED_INST0_PIN,\n      </div>\n      <div class=\"line\">\n       .polarity = SIMPLE_RGB_PWM_LED_INST0_POLARITY,\n      </div>\n      <div class=\"line\">\n       .channel = SIMPLE_RGB_PWM_LED_INST0_CHANNEL,\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #if defined(SL_SIMPLE_RGB_PWM_LED_INST0_RED_LOC)\n       </span>\n      </div>\n      <div class=\"line\">\n       .location = SIMPLE_RGB_PWM_LED_INST0_LOC,\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #endif\n       </span>\n      </div>\n      <div class=\"line\">\n       };\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-pwm-t\" target=\"_blank\">\n        sl_led_pwm_t\n       </a>\n       green_inst0 = {\n      </div>\n      <div class=\"line\">\n       .\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-pwm-t#ac9499526a3b385f00232a5f70f7578cd\" target=\"_blank\">\n        port\n       </a>\n       = SIMPLE_RGB_PWM_LED_INST0_PORT,\n      </div>\n      <div class=\"line\">\n       .pin = SIMPLE_RGB_PWM_LED_INST0_PIN,\n      </div>\n      <div class=\"line\">\n       .polarity = SIMPLE_RGB_PWM_LED_INST0_POLARITY,\n      </div>\n      <div class=\"line\">\n       .channel = SIMPLE_RGB_PWM_LED_INST0_CHANNEL,\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #if defined(SL_SIMPLE_RGB_PWM_LED_INST0_RED_LOC)\n       </span>\n      </div>\n      <div class=\"line\">\n       .location = SIMPLE_RGB_PWM_LED_INST0_LOC,\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #endif\n       </span>\n      </div>\n      <div class=\"line\">\n       };\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-pwm-t\" target=\"_blank\">\n        sl_led_pwm_t\n       </a>\n       blue_inst0 = {\n      </div>\n      <div class=\"line\">\n       .\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-pwm-t#ac9499526a3b385f00232a5f70f7578cd\" target=\"_blank\">\n        port\n       </a>\n       = SIMPLE_RGB_PWM_LED_INST0_PORT,\n      </div>\n      <div class=\"line\">\n       .pin = SIMPLE_RGB_PWM_LED_INST0_PIN,\n      </div>\n      <div class=\"line\">\n       .polarity = SIMPLE_RGB_PWM_LED_INST0_POLARITY,\n      </div>\n      <div class=\"line\">\n       .channel = SIMPLE_RGB_PWM_LED_INST0_CHANNEL,\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #if defined(SL_SIMPLE_RGB_PWM_LED_INST0_RED_LOC)\n       </span>\n      </div>\n      <div class=\"line\">\n       .location = SIMPLE_RGB_PWM_LED_INST0_LOC,\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #endif\n       </span>\n      </div>\n      <div class=\"line\">\n       };\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-simple-rgb-pwm-led-context-t\" target=\"_blank\">\n        sl_simple_rgb_pwm_led_context_t\n       </a>\n       simple_rgb_pwm_inst0_context = {\n      </div>\n      <div class=\"line\">\n       .\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-simple-rgb-pwm-led-context-t#accddf0a3b5b42ac669b90024d2cea8dc\" target=\"_blank\">\n        red\n       </a>\n       = &amp;red_inst0,\n      </div>\n      <div class=\"line\">\n       .green = &amp;green_inst0,\n      </div>\n      <div class=\"line\">\n       .blue = &amp;blue_inst0,\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       .\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-pwm-t#ab4e1fd012898a55a83c8c7b242009a9a\" target=\"_blank\">\n        timer\n       </a>\n       = SL_SIMPLE_RGB_PWM_LED_INST0_PERIPHERAL,\n      </div>\n      <div class=\"line\">\n       .frequency = SL_SIMPLE_RGB_PWM_LED_INST0_FREQUENCY,\n      </div>\n      <div class=\"line\">\n       .resolution = SL_SIMPLE_RGB_PWM_LED_INST0_RESOLUTION,\n      </div>\n      <div class=\"line\">\n       };\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"keyword\">\n        const\n       </span>\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-rgb-pwm-t\" target=\"_blank\">\n        sl_led_rgb_pwm_t\n       </a>\n       sl_inst0 = {\n      </div>\n      <div class=\"line\">\n       .\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-rgb-pwm-t#a6c7ecaf6352315e613de454b19f58fd4\" target=\"_blank\">\n        led_common\n       </a>\n       .\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t#a46fadab226cebd48acd4e7467d64eb6c\" target=\"_blank\">\n        context\n       </a>\n       = &amp;simple_rgb_pwm_inst0_context,\n      </div>\n      <div class=\"line\">\n       .led_common.init =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#gafe96206535a164cd900707d7c2330348\" target=\"_blank\">\n        sl_simple_rgb_pwm_led_init\n       </a>\n       ,\n      </div>\n      <div class=\"line\">\n       .led_common.turn_on =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga5e07d2fa9ee43e3525efe52457c9bc36\" target=\"_blank\">\n        sl_simple_rgb_pwm_led_turn_on\n       </a>\n       ,\n      </div>\n      <div class=\"line\">\n       .led_common.turn_off =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#gac645663b0a3e87b64dfb6b268e5856a2\" target=\"_blank\">\n        sl_simple_rgb_pwm_led_turn_off\n       </a>\n       ,\n      </div>\n      <div class=\"line\">\n       .led_common.toggle =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga225a85057ba2855008168206ef04bb99\" target=\"_blank\">\n        sl_simple_rgb_pwm_led_toggle\n       </a>\n       ,\n      </div>\n      <div class=\"line\">\n       .led_common.get_state =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga1d0c97d649ca4c16b084cf3fc3beefc5\" target=\"_blank\">\n        sl_simple_rgb_pwm_led_get_state\n       </a>\n       ,\n      </div>\n      <div class=\"line\">\n       .set_rgb_color =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga7b6514684775681bf9fca809d8b54bf3\" target=\"_blank\">\n        sl_simple_rgb_pwm_led_set_color\n       </a>\n       ,\n      </div>\n      <div class=\"line\">\n       .get_rgb_color =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#gad066fb8b341159c09a507e808cc62ba2\" target=\"_blank\">\n        sl_simple_rgb_pwm_led_get_color\n       </a>\n       ,\n      </div>\n      <div class=\"line\">\n       };\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"keywordtype\">\n        void\n       </span>\n       sl_simple_rgb_pwm_led_init_instances(\n       <span class=\"keywordtype\">\n        void\n       </span>\n       )\n      </div>\n      <div class=\"line\">\n       {\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaeec4fd0fb9f100d0f357dbb7974cb6ae\" target=\"_blank\">\n        sl_led_init\n       </a>\n       ((\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n        sl_led_t\n       </a>\n       *)&amp;sl_inst0);\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       }\n      </div>\n     </div>\n     <dl class=\"section note\">\n      <dt>\n       Note\n      </dt>\n      <dd>\n       The sl_simple_rgb_pwm_led_instances.c file is shown with only one instance, but if more were in use they would all appear in this .c file.\n      </dd>\n     </dl>\n     <div class=\"fragment\">\n      <div class=\"line\">\n       <span class=\"comment\">\n        // sl_simple_rgb_pwm_led_instances.h\n       </span>\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #ifndef SL_SIMPLE_RGB_PWM_LED_INSTANCES_H\n       </span>\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #define SL_SIMPLE_RGB_PWM_LED_INSTANCES_H\n       </span>\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #include \"sl_simple_rgb_pwm_led.h\"\n       </span>\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"keyword\">\n        extern\n       </span>\n       <span class=\"keyword\">\n        const\n       </span>\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-rgb-pwm-t\" target=\"_blank\">\n        sl_led_rgb_pwm_t\n       </a>\n       sl_inst0;\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"keywordtype\">\n        void\n       </span>\n       sl_simple_rgb_pwm_led_init_instances(\n       <span class=\"keywordtype\">\n        void\n       </span>\n       );\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #endif // SL_SIMPLE_RGB_PWM_LED_INIT_H\n       </span>\n      </div>\n     </div>\n     <dl class=\"section note\">\n      <dt>\n       Note\n      </dt>\n      <dd>\n       The sl_simple_rgb_pwm_led_instances.h file is shown with only one instance, but if more were in use they would all appear in this .h file.\n      </dd>\n     </dl>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"rgb-led-usage\">\n      </a>\n      RGB PWM LED Usage\n     </h1>\n     <p>\n      The RGB PWM Led driver provides functionality for controlling Red/Green/Blue/White LEDs that are driven by PWM. The LEDs can be turned on and off and toggled, and remember their color and brightness state when being turned back on. The color and brightness can be set using values of 0-65535 for red, green, blue, and white. Retrieving the state gives the on/off value, while retrieving the color gives the rgb values. The following code shows how to control these LEDs. An LED should always be initialized before calling any other functions with it.\n     </p>\n     <div class=\"fragment\">\n      <div class=\"line\">\n       <span class=\"comment\">\n        // initialize rgb LED\n       </span>\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaeec4fd0fb9f100d0f357dbb7974cb6ae\" target=\"_blank\">\n        sl_led_init\n       </a>\n       (&amp;rgb_led_inst0);\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"comment\">\n        // turn on LED, set color to purple, turn off, toggle (would maintain purple color)\n       </span>\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#ga330255a181ca62dfaede4428b71ab9ba\" target=\"_blank\">\n        sl_led_turn_on\n       </a>\n       (&amp;rgb_led_inst0);\n      </div>\n      <div class=\"line\">\n       uint16_t red = 65535;\n       <span class=\"comment\">\n        // max red\n       </span>\n      </div>\n      <div class=\"line\">\n       uint16_t green = 0;\n       <span class=\"comment\">\n        // no green\n       </span>\n      </div>\n      <div class=\"line\">\n       uint16_t blue = 65535;\n       <span class=\"comment\">\n        // max blue\n       </span>\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga12b5c133114b86ab7906a433a239e53a\" target=\"_blank\">\n        sl_led_set_rgb_color\n       </a>\n       (&amp;rgb_led_inst0, red, green, blue);\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gae4396c5a5963f9fb87a072a98da0cc79\" target=\"_blank\">\n        sl_led_turn_off\n       </a>\n       (&amp;rgb_led_inst0);\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#ga800d7603e870e27d02a76a1438f80ece\" target=\"_blank\">\n        sl_led_toggle\n       </a>\n       (&amp;rgb_led_inst0);\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"comment\">\n        // get the state of the led\n       </span>\n      </div>\n      <div class=\"line\">\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaf7839e80cf60eefc89ef109b048bec32\" target=\"_blank\">\n        sl_led_state_t\n       </a>\n       state =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaeb2bbdaed9dbe3c3f777c75e4624b526\" target=\"_blank\">\n        sl_led_get_state\n       </a>\n       (&amp;rgb_led_inst0);\n      </div>\n     </div>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"nested-classes\">\n          </a>\n          Data Structures\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-simple-rgb-pwm-led-context-t\" target=\"_blank\">\n          sl_simple_rgb_pwm_led_context_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         A Simple RGB LED context.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-rgb-pwm-t\" target=\"_blank\">\n          sl_led_rgb_pwm_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         A Simple RGB LED PWM instance.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"func-members\">\n          </a>\n          Functions\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#gafe96206535a164cd900707d7c2330348\" target=\"_blank\">\n          sl_simple_rgb_pwm_led_init\n         </a>\n         (void *rgb)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Initialize an RGB PWM LED driver.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga5e07d2fa9ee43e3525efe52457c9bc36\" target=\"_blank\">\n          sl_simple_rgb_pwm_led_turn_on\n         </a>\n         (void *rgb)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Turn on an RBG LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#gac645663b0a3e87b64dfb6b268e5856a2\" target=\"_blank\">\n          sl_simple_rgb_pwm_led_turn_off\n         </a>\n         (void *rgb)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Turn off an RGB LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga225a85057ba2855008168206ef04bb99\" target=\"_blank\">\n          sl_simple_rgb_pwm_led_toggle\n         </a>\n         (void *rgb)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Toggle an RGB LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaf7839e80cf60eefc89ef109b048bec32\" target=\"_blank\">\n          sl_led_state_t\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga1d0c97d649ca4c16b084cf3fc3beefc5\" target=\"_blank\">\n          sl_simple_rgb_pwm_led_get_state\n         </a>\n         (void *rgb)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get status of an RGB LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga7b6514684775681bf9fca809d8b54bf3\" target=\"_blank\">\n          sl_simple_rgb_pwm_led_set_color\n         </a>\n         (void *rgb, uint16_t red, uint16_t green, uint16_t blue)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Set color mixing and dimming level of an RGB LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#gad066fb8b341159c09a507e808cc62ba2\" target=\"_blank\">\n          sl_simple_rgb_pwm_led_get_color\n         </a>\n         (void *rgb, uint16_t *red, uint16_t *green, uint16_t *blue)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get current color mixing and dimming level of an RGB LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga12b5c133114b86ab7906a433a239e53a\" target=\"_blank\">\n          sl_led_set_rgb_color\n         </a>\n         (const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-rgb-pwm-t\" target=\"_blank\">\n          sl_led_rgb_pwm_t\n         </a>\n         *rgb, uint16_t red, uint16_t green, uint16_t blue)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         LED set RGB color.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga5cb63133eb5b7194a513aaf9aa332a04\" target=\"_blank\">\n          sl_led_get_rgb_color\n         </a>\n         (const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-rgb-pwm-t\" target=\"_blank\">\n          sl_led_rgb_pwm_t\n         </a>\n         *rgb, uint16_t *red, uint16_t *green, uint16_t *blue)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         LED get RGB setting.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"define-members\">\n          </a>\n          Macros\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#gab8289f58a81ac3a492743894da6073ca\" target=\"_blank\">\n          SL_SIMPLE_RGB_PWM_LED_POLARITY_ACTIVE_HIGH\n         </a>\n         0U\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         LED Active polarity High.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga18cfc442b84bfdcc0632100c07f5b8cc\" target=\"_blank\">\n          SL_SIMPLE_RGB_PWM_LED_POLARITY_ACTIVE_LOW\n         </a>\n         1U\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         LED Active polarity Low.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga16b492b2b4da7a6763821e9506fa88ff\" target=\"_blank\">\n          SL_SIMPLE_RGB_PWM_LED_COLOR_R\n         </a>\n         0U\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         LED Red.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga3448becbb783bf0d7fbc5974445a56c1\" target=\"_blank\">\n          SL_SIMPLE_RGB_PWM_LED_COLOR_G\n         </a>\n         1U\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         LED Green.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga6907d673c5eebdf30ea77fbe1e8fd96b\" target=\"_blank\">\n          SL_SIMPLE_RGB_PWM_LED_COLOR_B\n         </a>\n         2U\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         LED Blue.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga37a8480366f9863eceea270bf339a715\" target=\"_blank\">\n          SL_SIMPLE_RGB_PWM_LED_NUM_CC_REQUIRED\n         </a>\n         3U\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Number of Timer Capture Channels required (1 for each RGB color)\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <h2 class=\"groupheader\">\n      Function Documentation\n     </h2>\n     <a id=\"gafe96206535a164cd900707d7c2330348\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gafe96206535a164cd900707d7c2330348\">\n        ◆\n       </a>\n      </span>\n      sl_simple_rgb_pwm_led_init()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_simple_rgb_pwm_led_init\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            rgb\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Initialize an RGB PWM LED driver.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to rgb-pwm-led specific data.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Status Code:\n         <ul>\n          <li>\n           SL_STATUS_OK Success\n          </li>\n          <li>\n           SL_STATUS_FAIL Init error\n          </li>\n         </ul>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga5e07d2fa9ee43e3525efe52457c9bc36\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga5e07d2fa9ee43e3525efe52457c9bc36\">\n        ◆\n       </a>\n      </span>\n      sl_simple_rgb_pwm_led_turn_on()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_simple_rgb_pwm_led_turn_on\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            rgb\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Turn on an RBG LED.\n       </p>\n       <p>\n        Turns on at previously set color levels If no previous levels set, turns on at max level for all RGB LEDs\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to rgb_pwm-led specific data.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gac645663b0a3e87b64dfb6b268e5856a2\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gac645663b0a3e87b64dfb6b268e5856a2\">\n        ◆\n       </a>\n      </span>\n      sl_simple_rgb_pwm_led_turn_off()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_simple_rgb_pwm_led_turn_off\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            rgb\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Turn off an RGB LED.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to rgb-pwm-led specific data.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga225a85057ba2855008168206ef04bb99\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga225a85057ba2855008168206ef04bb99\">\n        ◆\n       </a>\n      </span>\n      sl_simple_rgb_pwm_led_toggle()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_simple_rgb_pwm_led_toggle\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            rgb\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Toggle an RGB LED.\n       </p>\n       <p>\n        The toggle \"ON\" behavior is as defined for\n        <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga5e07d2fa9ee43e3525efe52457c9bc36\" title=\"Turn on an RBG LED.\" target=\"_blank\">\n         sl_simple_rgb_pwm_led_turn_on()\n        </a>\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to rgb-pwm-led specific data.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga1d0c97d649ca4c16b084cf3fc3beefc5\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga1d0c97d649ca4c16b084cf3fc3beefc5\">\n        ◆\n       </a>\n      </span>\n      sl_simple_rgb_pwm_led_get_state()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaf7839e80cf60eefc89ef109b048bec32\" target=\"_blank\">\n            sl_led_state_t\n           </a>\n           sl_simple_rgb_pwm_led_get_state\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            rgb\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get status of an RGB LED.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to rgb-pwm-led specific data.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         sl_led_state_t Current state of RGB LED. 0 for Red, Green, Blue and White LEDs are all OFF 1 for Red, Green, Blue or White LED is ON\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga7b6514684775681bf9fca809d8b54bf3\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga7b6514684775681bf9fca809d8b54bf3\">\n        ◆\n       </a>\n      </span>\n      sl_simple_rgb_pwm_led_set_color()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_simple_rgb_pwm_led_set_color\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            rgb,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            red,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            green,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            blue\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Set color mixing and dimming level of an RGB LED.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to rgb-pwm-led specific data:\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              red\n             </code>\n            </td>\n            <td>\n             Red color level (PWM duty-cycle [0-65535])\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              green\n             </code>\n            </td>\n            <td>\n             Green color level (PWM duty-cycle [0-65535])\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              blue\n             </code>\n            </td>\n            <td>\n             Blue color level (PWM duty-cycle [0-65535])\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gad066fb8b341159c09a507e808cc62ba2\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gad066fb8b341159c09a507e808cc62ba2\">\n        ◆\n       </a>\n      </span>\n      sl_simple_rgb_pwm_led_get_color()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_simple_rgb_pwm_led_get_color\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            rgb,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            red,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            green,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            blue\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get current color mixing and dimming level of an RGB LED.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Will return the last stored levels regardless of the current ON/OFF state. Call\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led#ga1d0c97d649ca4c16b084cf3fc3beefc5\" title=\"Get status of an RGB LED.\" target=\"_blank\">\n          sl_simple_rgb_pwm_led_get_state()\n         </a>\n         to determine if the RGB LED is actually ON or OFF.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to rgb-pwm-led specific data:\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [out]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              red\n             </code>\n            </td>\n            <td>\n             Red color level (PWM duty-cycle [0-65535])\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [out]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              green\n             </code>\n            </td>\n            <td>\n             Green color level (PWM duty-cycle [0-65535])\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [out]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              blue\n             </code>\n            </td>\n            <td>\n             Blue color level (PWM duty-cycle [0-65535])\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga12b5c133114b86ab7906a433a239e53a\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga12b5c133114b86ab7906a433a239e53a\">\n        ◆\n       </a>\n      </span>\n      sl_led_set_rgb_color()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_led_set_rgb_color\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-rgb-pwm-t\" target=\"_blank\">\n            sl_led_rgb_pwm_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            rgb,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            red,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            green,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            blue\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        LED set RGB color.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              rgb\n             </code>\n            </td>\n            <td>\n             LED Instance handle\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              red\n             </code>\n            </td>\n            <td>\n             LED red intensity\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              green\n             </code>\n            </td>\n            <td>\n             LED green intensity\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              blue\n             </code>\n            </td>\n            <td>\n             LED blue intensity\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga5cb63133eb5b7194a513aaf9aa332a04\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga5cb63133eb5b7194a513aaf9aa332a04\">\n        ◆\n       </a>\n      </span>\n      sl_led_get_rgb_color()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_led_get_rgb_color\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-rgb-pwm-t\" target=\"_blank\">\n            sl_led_rgb_pwm_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            rgb,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            red,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            green,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            blue\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        LED get RGB setting.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              rgb\n             </code>\n            </td>\n            <td>\n             LED Instance handle\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              red\n             </code>\n            </td>\n            <td>\n             LED red intensity\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              green\n             </code>\n            </td>\n            <td>\n             LED green intensity\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              blue\n             </code>\n            </td>\n            <td>\n             LED blue intensity\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <h2 class=\"groupheader\">\n      Macro Definition Documentation\n     </h2>\n     <a id=\"gab8289f58a81ac3a492743894da6073ca\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gab8289f58a81ac3a492743894da6073ca\">\n        ◆\n       </a>\n      </span>\n      SL_SIMPLE_RGB_PWM_LED_POLARITY_ACTIVE_HIGH\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define SL_SIMPLE_RGB_PWM_LED_POLARITY_ACTIVE_HIGH&nbsp;&nbsp;&nbsp;0U\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        LED Active polarity High.\n       </p>\n      </div>\n     </div>\n     <a id=\"ga18cfc442b84bfdcc0632100c07f5b8cc\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga18cfc442b84bfdcc0632100c07f5b8cc\">\n        ◆\n       </a>\n      </span>\n      SL_SIMPLE_RGB_PWM_LED_POLARITY_ACTIVE_LOW\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define SL_SIMPLE_RGB_PWM_LED_POLARITY_ACTIVE_LOW&nbsp;&nbsp;&nbsp;1U\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        LED Active polarity Low.\n       </p>\n      </div>\n     </div>\n     <a id=\"ga16b492b2b4da7a6763821e9506fa88ff\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga16b492b2b4da7a6763821e9506fa88ff\">\n        ◆\n       </a>\n      </span>\n      SL_SIMPLE_RGB_PWM_LED_COLOR_R\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define SL_SIMPLE_RGB_PWM_LED_COLOR_R&nbsp;&nbsp;&nbsp;0U\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        LED Red.\n       </p>\n      </div>\n     </div>\n     <a id=\"ga3448becbb783bf0d7fbc5974445a56c1\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga3448becbb783bf0d7fbc5974445a56c1\">\n        ◆\n       </a>\n      </span>\n      SL_SIMPLE_RGB_PWM_LED_COLOR_G\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define SL_SIMPLE_RGB_PWM_LED_COLOR_G&nbsp;&nbsp;&nbsp;1U\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        LED Green.\n       </p>\n      </div>\n     </div>\n     <a id=\"ga6907d673c5eebdf30ea77fbe1e8fd96b\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga6907d673c5eebdf30ea77fbe1e8fd96b\">\n        ◆\n       </a>\n      </span>\n      SL_SIMPLE_RGB_PWM_LED_COLOR_B\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define SL_SIMPLE_RGB_PWM_LED_COLOR_B&nbsp;&nbsp;&nbsp;2U\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        LED Blue.\n       </p>\n      </div>\n     </div>\n     <a id=\"ga37a8480366f9863eceea270bf339a715\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga37a8480366f9863eceea270bf339a715\">\n        ◆\n       </a>\n      </span>\n      SL_SIMPLE_RGB_PWM_LED_NUM_CC_REQUIRED\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define SL_SIMPLE_RGB_PWM_LED_NUM_CC_REQUIRED&nbsp;&nbsp;&nbsp;3U\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Number of Timer Capture Channels required (1 for each RGB color)\n       </p>\n      </div>\n     </div>\n    </div>\n   </article>\n  ", "url": "http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led", "status": "success"}