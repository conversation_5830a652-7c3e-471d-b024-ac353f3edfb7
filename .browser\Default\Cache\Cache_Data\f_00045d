{"requiredByTree": [{"children": [{"children": [{"children": [{"keyWords": "in_place_ota_dfu", "componentId": "in_place_ota_dfu", "isConfigurable": true, "documentation": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/in-place-ota-dfu", "description": "Component that provides in-place over-the-air (OTA) device firmware update (DFU) functionality. In this solution, the application flash area is used as a temporary storage for the update.\nThis is a Low-Code component because additional security measures might be needed from user side for checking the correct security status. Our minimal solution can be seen in\nsl_bt_in_place_ota_dfu_security_status()\nfunction in sl_bt_in_place_ota_dfu.c file.\n", "instantiable": false, "label": "In-Place OTA DFU", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "Low-Code", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Application|studiocomproot-Bluetooth-Application-Firmware_Update|studiocomproot-Bluetooth-Application-Firmware_Update-in_place_ota_dfu", "isEditable": true, "isEnabled": true, "name": "in_place_ota_dfu", "isSelected": true, "id": "studiocomproot-Bluetooth-Application-Firmware_Update-in_place_ota_dfu"}], "isEnabled": true, "name": "Firmware Update", "id": "studiocomproot-Bluetooth-Application-Firmware_Update", "label": "Firmware Update", "type": "category"}, {"children": [{"keyWords": "gatt_service_device_information", "componentId": "gatt_service_device_information", "isConfigurable": false, "documentation": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-service-api/gatt-service-device-information", "description": "This component provides the default values for the characteristics under the Device Information service upon boot event.\n", "instantiable": false, "label": "Device Information GATT Service", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "No-Code", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Application|studiocomproot-Bluetooth-Application-GATT_Services|studiocomproot-Bluetooth-Application-GATT_Services-gatt_service_device_information", "isEditable": false, "isEnabled": true, "name": "gatt_service_device_information", "isSelected": true, "id": "studiocomproot-Bluetooth-Application-GATT_Services-gatt_service_device_information"}], "isEnabled": true, "name": "GATT Services", "id": "studiocomproot-Bluetooth-Application-GATT_Services", "label": "GATT Services", "type": "category"}], "isEnabled": true, "name": "Application", "id": "studiocomproot-Bluetooth-Application", "label": "Application", "type": "category"}, {"children": [{"children": [{"keyWords": "bluetooth_feature_gatt", "componentId": "bluetooth_feature_gatt", "isConfigurable": false, "documentation": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt", "description": "GATT Client feature\nEnables the ability to browse and manage attributes in a remote GATT server.\n", "instantiable": false, "label": "GATT Client", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Bluetooth_Host_(Stack)|studiocomproot-Bluetooth-Bluetooth_Host_(Stack)-Features_with_Commands_and_Events|studiocomproot-Bluetooth-Bluetooth_Host_(Stack)-Features_with_Commands_and_Events-bluetooth_feature_gatt", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_gatt", "isSelected": true, "id": "studiocomproot-Bluetooth-Bluetooth_Host_(Stack)-Features_with_Commands_and_Events-bluetooth_feature_gatt"}, {"keyWords": "bluetooth_feature_gatt_server", "componentId": "bluetooth_feature_gatt_server", "isConfigurable": false, "documentation": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt-server", "description": "GATT Server feature\nEnables the ability to browse and manage attributes in a local GATT database. \n", "instantiable": false, "label": "GATT Server", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Bluetooth_Host_(Stack)|studiocomproot-Bluetooth-Bluetooth_Host_(Stack)-Features_with_Commands_and_Events|studiocomproot-Bluetooth-Bluetooth_Host_(Stack)-Features_with_Commands_and_Events-bluetooth_feature_gatt_server", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_gatt_server", "isSelected": true, "id": "studiocomproot-Bluetooth-Bluetooth_Host_(Stack)-Features_with_Commands_and_Events-bluetooth_feature_gatt_server"}, {"keyWords": "bluetooth_feature_sm", "componentId": "bluetooth_feature_sm", "isConfigurable": false, "documentation": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-sm", "description": "Bluetooth security manager (SM) feature\n", "instantiable": false, "label": "Security Manager", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Bluetooth_Host_(Stack)|studiocomproot-Bluetooth-Bluetooth_Host_(Stack)-Features_with_Commands_and_Events|studiocomproot-Bluetooth-Bluetooth_Host_(Stack)-Features_with_Commands_and_Events-bluetooth_feature_sm", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_sm", "isSelected": true, "id": "studiocomproot-Bluetooth-Bluetooth_Host_(Stack)-Features_with_Commands_and_Events-bluetooth_feature_sm"}, {"keyWords": "bluetooth_feature_system", "componentId": "bluetooth_feature_system", "isConfigurable": false, "documentation": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-system", "description": "Local device configruation and software timers\n", "instantiable": false, "label": "System", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Bluetooth_Host_(Stack)|studiocomproot-Bluetooth-Bluetooth_Host_(Stack)-Features_with_Commands_and_Events|studiocomproot-Bluetooth-Bluetooth_Host_(Stack)-Features_with_Commands_and_Events-bluetooth_feature_system", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_system", "isSelected": true, "id": "studiocomproot-Bluetooth-Bluetooth_Host_(Stack)-Features_with_Commands_and_Events-bluetooth_feature_system"}], "isEnabled": true, "name": "Features with Commands and Events", "id": "studiocomproot-Bluetooth-Bluetooth_Host_(Stack)-Features_with_Commands_and_Events", "label": "Features with Commands and Events", "type": "category"}], "isEnabled": true, "name": "Bluetooth Host (Stack)", "id": "studiocomproot-Bluetooth-Bluetooth_Host_(Stack)", "label": "Bluetooth Host (Stack)", "type": "category"}, {"children": [{"children": [{"keyWords": "bluetooth_feature_advertiser", "componentId": "bluetooth_feature_advertiser", "isConfigurable": true, "documentation": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-advertiser", "description": "This Bluetooth advertising component, corresponding to the \"advertiser\" class in Bluetooth APIs, is the base of legacy, extended and periodic advertisings. Functionalities in this component include advertising set creation, advertising parameter and address settings etc. The application must choose which advertising types are needed based on its use cases. The <bluetooth_feature_legacy_advertiser> component provides the legacy advertising feature. The <bluetooth_feature_extended_advertiser> component provides the extended advertising feature. And the <bluetooth_feature_periodic_advertiser> component provides the periodic advertising feature. When the <bluetooth_feature_legacy_advertiser>, <bluetooth_feature_extended_advertiser>, or <bluetooth_feature_periodic_advertiser> component is included by the application, some commands of the \"advertiser\" class whose behaviors vary by specific advertising types are no longer supported. See the Bluetooth API reference for more details. When none of the three components is included, all commands of the \"advertiser\" class are functional for providing the backwards compatibility.\n", "instantiable": false, "label": "Advertising Base Feature", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events-bluetooth_feature_advertiser", "isEditable": true, "isEnabled": true, "name": "bluetooth_feature_advertiser", "isSelected": true, "id": "studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events-bluetooth_feature_advertiser"}, {"keyWords": "bluetooth_feature_connection", "componentId": "bluetooth_feature_connection", "isConfigurable": true, "documentation": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-connection", "description": "Bluetooth connection feature\n", "instantiable": false, "label": "Connection", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events-bluetooth_feature_connection", "isEditable": true, "isEnabled": true, "name": "bluetooth_feature_connection", "isSelected": true, "id": "studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events-bluetooth_feature_connection"}, {"keyWords": "bluetooth_feature_legacy_advertiser", "componentId": "bluetooth_feature_legacy_advertiser", "isConfigurable": false, "documentation": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-legacy-advertiser", "description": "This component, corresponding to the \"legacy_advertiser\" class in Bluetooth APIs, provides the legacy advertising feature. Specifically, this component enables advertisements that use legacy advertising PDUs. Common advertising functionalities, e.g., advertising set creation, and address settings etc., are provided by its base component <bluetooth_feature_advertiser>.\n", "instantiable": false, "label": "Legacy Advertising", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events-bluetooth_feature_legacy_advertiser", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_legacy_advertiser", "isSelected": true, "id": "studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events-bluetooth_feature_legacy_advertiser"}, {"keyWords": "bluetooth_feature_scanner", "componentId": "bluetooth_feature_scanner", "isConfigurable": false, "documentation": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner", "description": "The Bluetooth scanning component, corresponding to the \"scanner\" class in Bluetooth APIs, is the base of legacy and extended scannings and it provides functionalities that are common in legacy and extended scannings.\nThe <bluetooth_feature_legacy_scanner> component enables scanning the advertisements using legacy advertising PDUs. The <bluetooth_feature_extended_scanner> component enables scanning the advertisements using legacy or extended advertising PDUs. When the <bluetooth_feature_legacy_scanner>, or <bluetooth_feature_extended_scanner> is included by the application, some superseded commands of the \"scanner\" class are not available to use. See the Bluetooth API reference for more details. When none of these two components is included, all commands of the \"scanner\" class are functional for providing the backwards compatibility.\n", "instantiable": false, "label": "Scanner Base Feature", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events-bluetooth_feature_scanner", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_scanner", "isSelected": true, "id": "studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events-bluetooth_feature_scanner"}, {"keyWords": "bluetooth_feature_legacy_scanner", "componentId": "bluetooth_feature_legacy_scanner", "isConfigurable": false, "documentation": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-scanner", "description": "This component brings in necessary functionalities for scanning the advertisements that use legacy advertising PDUs.\nInclude this component if the application does not need to scan advertisements that use extended advertising PDUs. Advertisements received by the scanner are reported in the BGAPI sl_bt_evt_scanner_legacy_advertisement_report event.\nIf this component is included and the <bluetooth_feature_extended_scanner> is not, the number of received advertisement reports are reduced if advertising devices that use extended advertising PDUs are in the radio range. Another benefit of including this component only is reduced application size by eliminating the stack functionalities for scanning advertisements that use extended advertising PDUs.\n", "instantiable": false, "label": "Scanner for legacy advertisements", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Bluetooth|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events|studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events-bluetooth_feature_legacy_scanner", "isEditable": false, "isEnabled": true, "name": "bluetooth_feature_legacy_scanner", "isSelected": true, "id": "studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events-bluetooth_feature_legacy_scanner"}], "isEnabled": true, "name": "Features with Commands and Events", "id": "studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)-Features_with_Commands_and_Events", "label": "Features with Commands and Events", "type": "category"}], "isEnabled": true, "name": "Bluetooth LE Controller (Link Layer)", "id": "studiocomproot-Bluetooth-Bluetooth_LE_Controller_(Link_Layer)", "label": "Bluetooth LE Controller (Link Layer)", "type": "category"}], "isEnabled": true, "name": "Bluetooth", "id": "studiocomproot-Bluetooth", "label": "Bluetooth", "type": "category"}], "component": {"keyWords": "bluetooth_stack", "componentId": "bluetooth_stack", "isConfigurable": true, "documentation": "", "description": "Bluetooth Low Energy stack and configurations\nIn addition to this core component, select features needed by the application.\n", "instantiable": false, "label": "Bluetooth Core", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "bluetooth_stack", "isEditable": true, "isEnabled": true, "name": "bluetooth_stack", "isSelected": true, "id": "bluetooth_stack"}, "requiresTree": [{"children": [{"children": [{"keyWords": "emlib_system", "componentId": "emlib_system", "isConfigurable": false, "documentation": "http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/system", "description": "System peripheral API\n", "instantiable": false, "label": "SYSTEM", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Platform|studiocomproot-Platform-Peripheral|studiocomproot-Platform-Peripheral-emlib_system", "isEditable": false, "isEnabled": true, "name": "emlib_system", "isSelected": true, "id": "studiocomproot-Platform-Peripheral-emlib_system"}], "isEnabled": true, "name": "Per<PERSON>heral", "id": "studiocomproot-Platform-Peripheral", "label": "Per<PERSON>heral", "type": "category"}, {"children": [{"keyWords": "rail_util_built_in_phys", "componentId": "rail_util_built_in_phys", "isConfigurable": false, "documentation": "http://docs.silabs.com/rail/2.16.4/rail-api/rail-util-built-in-phys", "description": "Utility to include built-in PHYs to work with different HFXO frequencies. On EFR32XG24 parts, 38.4 MHz, 39 MHz, and 40 Mhz HFXO support is provided by this component.\n", "instantiable": false, "label": "RAIL Utility, Built-in PHYs Across HFXO Frequencies", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Platform|studiocomproot-Platform-Radio|studiocomproot-Platform-Radio-rail_util_built_in_phys", "isEditable": false, "isEnabled": true, "name": "rail_util_built_in_phys", "isSelected": true, "id": "studiocomproot-Platform-Radio-rail_util_built_in_phys"}, {"keyWords": "rail_util_pa", "componentId": "rail_util_pa", "isConfigurable": true, "documentation": "http://docs.silabs.com/rail/2.16.4/rail-api/rail-util-pa", "description": "Utility to aid with RAIL RF Power Amplifier (PA) Support", "instantiable": false, "label": "RAIL Utility, PA", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Platform|studiocomproot-Platform-Radio|studiocomproot-Platform-Radio-rail_util_pa", "isEditable": true, "isEnabled": true, "name": "rail_util_pa", "isSelected": true, "id": "studiocomproot-Platform-Radio-rail_util_pa"}], "isEnabled": true, "name": "Radio", "id": "studiocomproot-Platform-Radio", "label": "Radio", "type": "category"}, {"children": [{"keyWords": "sl_assert", "componentId": "sl_assert", "isConfigurable": false, "documentation": "http://docs.silabs.com/gecko-platform/4.4.4/platform-common/assert", "description": "Component that provides assert functions.", "instantiable": false, "label": "Assert Functions", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Platform|studiocomproot-Platform-Utilities|studiocomproot-Platform-Utilities-sl_assert", "isEditable": false, "isEnabled": true, "name": "sl_assert", "isSelected": true, "id": "studiocomproot-Platform-Utilities-sl_assert"}, {"keyWords": "component_catalog", "componentId": "component_catalog", "isConfigurable": false, "documentation": "", "description": "Component catalog that provides the list of APIs present in the project.", "instantiable": false, "label": "Component Catalog", "type": "component", "quality": "PRODUCTION", "isUserSelected": true, "tags": "", "path": "studiocomproot-Platform|studiocomproot-Platform-Utilities|studiocomproot-Platform-Utilities-component_catalog", "isEditable": false, "isEnabled": true, "name": "component_catalog", "isSelected": true, "id": "studiocomproot-Platform-Utilities-component_catalog"}, {"keyWords": "silabs_core_sl_malloc", "componentId": "silabs_core_sl_malloc", "isConfigurable": false, "documentation": "", "description": "This component provides a thread safe wrapper on top of the standard c memory allocation functions.\nThe component can be used in an environment where Micrium OS or FreeRTOS is used in order to safely\nallocate and free memory in multiple tasks from a shared heap.\n\nThe wrapper functions are called sl_malloc(), sl_calloc, sl_realloc() and sl_free().", "instantiable": false, "label": "<PERSON><PERSON>per", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Platform|studiocomproot-Platform-Utilities|studiocomproot-Platform-Utilities-silabs_core_sl_malloc", "isEditable": false, "isEnabled": true, "name": "silabs_core_sl_malloc", "isSelected": true, "id": "studiocomproot-Platform-Utilities-silabs_core_sl_malloc"}], "isEnabled": true, "name": "Utilities", "id": "studiocomproot-Platform-Utilities", "label": "Utilities", "type": "category"}], "isEnabled": true, "name": "Platform", "id": "studiocomproot-Platform", "label": "Platform", "type": "category"}, {"children": [{"children": [{"children": [{"keyWords": "device_init_hfxo", "componentId": "device_init_hfxo", "isConfigurable": true, "documentation": "http://docs.silabs.com/gecko-platform/4.4.4/platform-service/device-init-hfxo", "description": "Instantiate the component for High Frequency Crystal Oscillator (HFXO) initialization based on the provided configuration.\n", "instantiable": false, "label": "High Frequency Crystal Oscillator (HFXO)", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-Device_Initialization|studiocomproot-Services-Device_Initialization-Peripherals|studiocomproot-Services-Device_Initialization-Peripherals-device_init_hfxo", "isEditable": true, "isEnabled": true, "name": "device_init_hfxo", "isSelected": true, "id": "studiocomproot-Services-Device_Initialization-Peripherals-device_init_hfxo"}], "isEnabled": true, "name": "Peripher<PERSON>", "id": "studiocomproot-Services-Device_Initialization-Peripherals", "label": "Peripher<PERSON>", "type": "category"}, {"keyWords": "device_init", "componentId": "device_init", "isConfigurable": false, "documentation": "http://docs.silabs.com/gecko-platform/4.4.4/platform-service/device-init", "description": "This component automatically handles the installation of all required device components for proper MCU operation. The hardware peripherals that will be initialized will depend on the specific device's capabilities. Individual component configuration can be edited by selecting from the \"Peripherals\" sub-menu.\nIn the event that the user wants to implement its own initialization routine for any of the peripherals under the \"Peripherals\" sub-menu, it is recommended to uninstall this component, as well as the corresponding peripheral's initialization component.\n", "instantiable": false, "label": "Automatic Device Initialization", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-Device_Initialization|studiocomproot-Services-Device_Initialization-device_init", "isEditable": false, "isEnabled": true, "name": "device_init", "isSelected": true, "id": "studiocomproot-Services-Device_Initialization-device_init"}], "isEnabled": true, "name": "Device Initialization", "id": "studiocomproot-Services-Device_Initialization", "label": "Device Initialization", "type": "category"}, {"children": [{"keyWords": "nvm3_lib", "componentId": "nvm3_lib", "isConfigurable": false, "documentation": "http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/nvm3", "description": "The NVM3 driver provides a means to write and read data objects (key/value pairs) stored in flash. Wear-leveling is applied to reduce erase and write cycles and maximize flash lifetime. The driver is resilient to power loss and reset events, ensuring that objects retrieved from the driver are always in a valid state. A single NVM3 instance can be shared among several wireless stacks and application code, making it well-suited for multiprotocol applications.\n\nThis component includes only the NVM3 driver core. To configure and create the default instance of this driver, the NVM3 Default Instance component should be included in the project.\n", "instantiable": false, "label": "NVM3 Core", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-NVM3|studiocomproot-Services-NVM3-nvm3_lib", "isEditable": false, "isEnabled": true, "name": "nvm3_lib", "isSelected": true, "id": "studiocomproot-Services-NVM3-nvm3_lib"}, {"keyWords": "nvm3_default", "componentId": "nvm3_default", "isConfigurable": false, "documentation": "http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/nvm3default", "description": "This component provides functions to initialize/deinitialize the default NVM3 instance. The instance can be accessed with the NVM3 API by using the nvm3_defaultHandle provided in nvm3_default.h as the nvm3_Handle_t pointer.\n\nIf the Services->Runtime->System Setup component is included in a project, the default instance will be initialized automatically, using the default instance configuration, during the sl_system_init() call in main.c.\n\nSelecting this component will also include the NVM3 Core component, which is the implementation of the NVM3 driver itself.\n", "instantiable": false, "label": "NVM3 Default Instance", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-NVM3|studiocomproot-Services-NVM3-nvm3_default", "isEditable": false, "isEnabled": true, "name": "nvm3_default", "isSelected": true, "id": "studiocomproot-Services-NVM3-nvm3_default"}], "isEnabled": true, "name": "NVM3", "id": "studiocomproot-Services-NVM3", "label": "NVM3", "type": "category"}, {"children": [{"keyWords": "power_manager", "componentId": "power_manager", "isConfigurable": true, "documentation": "http://docs.silabs.com/gecko-platform/4.4.4/platform-service-power-manager-overview", "description": "Instantiate the Power Manager (PM) component for managing the power consumption of the MCU. The PM will shut down the high-frequency clock and put the system in lower Energy Modes (EM) when possible. The PM also allows the user to register certain callback that will be executed when going into/out of certain EM. This allows the user to carry out any bookkeeping following EM transition events.\n", "instantiable": false, "label": "Power Manager", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-Power_Manager|studiocomproot-Services-Power_Manager-power_manager", "isEditable": true, "isEnabled": true, "name": "power_manager", "isSelected": true, "id": "studiocomproot-Services-Power_Manager-power_manager"}], "isEnabled": true, "name": "Power Manager", "id": "studiocomproot-Services-Power_Manager", "label": "Power Manager", "type": "category"}, {"children": [{"keyWords": "sl_system", "componentId": "sl_system", "isConfigurable": false, "documentation": "http://docs.silabs.com/gecko-platform/4.4.4/platform-service/system", "description": "The System Setup component provides a set of events that can be used by\nother components to register handlers that should be run when the system\nis initialized and is running. For a detailed list of the handlers that can be\nregistered using the Event Handler API and a usage example, please refer to \nthe documentation.", "instantiable": false, "label": "System Setup", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-System_Setup|studiocomproot-Services-System_Setup-sl_system", "isEditable": false, "isEnabled": true, "name": "sl_system", "isSelected": true, "id": "studiocomproot-Services-System_Setup-sl_system"}], "isEnabled": true, "name": "System Setup", "id": "studiocomproot-Services-System_Setup", "label": "System Setup", "type": "category"}, {"children": [{"keyWords": "sleeptimer", "componentId": "sleeptimer", "isConfigurable": true, "documentation": "http://docs.silabs.com/gecko-platform/4.4.4/platform-service/sleeptimer", "description": "The Sleeptimer driver provides software timers, delays, timekeeping and date functionalities using a low-frequency real-time clock peripheral. Sleep Timer uses one Hardware Timer and creates multiple software timer instances. Sleep Timer can be used for creating timers which are tightly integrated with power management. The Power Manager requires precision timing to have all clocks ready on time, so that wakeup happens a little bit earlier to prepare the system to be ready at the right time.\n", "instantiable": false, "label": "Sleep Timer", "type": "component", "quality": "PRODUCTION", "isUserSelected": false, "tags": "", "path": "studiocomproot-Services|studiocomproot-Services-Timers|studiocomproot-Services-Timers-sleeptimer", "isEditable": true, "isEnabled": true, "name": "sleeptimer", "isSelected": true, "id": "studiocomproot-Services-Timers-sleeptimer"}], "isEnabled": true, "name": "Timers", "id": "studiocomproot-Services-Timers", "label": "Timers", "type": "category"}], "isEnabled": true, "name": "Services", "id": "studiocomproot-Services", "label": "Services", "type": "category"}]}