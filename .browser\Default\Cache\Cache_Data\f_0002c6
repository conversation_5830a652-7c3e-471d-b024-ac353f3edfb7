{"categoryPackages": [{"category": "Tools", "packages": [{"installState": "Install", "releaseNotes": "4.2.12\n\tAddress issue when installing by connected Si91x device\n4.2.11\n\tSV5.7 release notes\n4.2.10\n\tFix text box width issues on linux\n4.2.7\n\tBuild feature with Eclipse 4.23\n4.2.6\n\tAddressed issue where device was not being flashed\n4.2.5\n\tCode maintenance.\n4.2.4\n\tGeneral bug fixes\n4.2.3\n\tMinor feature update relating to diplay of icons\n4.2.2\n\tGeneral bug fixes\n4.2.1\n\tAddress an issue where a UI object was not handled properly\n4.2.0\n\tGeneral bug fixes\n4.0.7\n\tInternal version increment\n4.0.6\n\tMinor bug fixes\n4.0.5\n\tInternal version increment\n4.0.4\n\tPackage signing\n4.0.3\n\tInternal version increment\n4.0.2\n\tMinor UI fixes\n4.0.1\n\tFollowing version change in sibling features\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.flashprogrammer.8051.feature.metadata.resource.feature.group/icon_display_flashProgrammer.png", "installEnabled": true, "id": "com.silabs.ss.tool.flashprogrammer.8051.feature.metadata.resource.feature.group", "label": "Flash Programmer for 8051 - 4.2.12", "featurePackageId": "com.silabs.ss.tool.flashprogrammer.8051.feature", "desc": "Simplicity Studio Flash Programmer for 8051"}, {"installState": "Uninstall", "releaseNotes": "4.3.1\n\tFix validation error\n4.3.0\n\tAdd Mesh 1.1 support for vendor model corresponds and extends relations\n4.2.4\n\tMinor bug fixes and improvements.\n4.2.3\n\tSV5.8 release\n4.2.2\n\tSV5.7 release\n4.2.1\n\tBluetooth Mesh 1.1 Support\n4.2.0\n\tImprove Vendor model name and id validation\n4.1.0\n\tCompany and Location drop-downs now auto selects the first filtered value\n\tAdded Ctrl + Del shortcut for item deletion\n\tFixed issue with Company and Location drop-down where filter did not clear itself\n\tFixed issue with Element Move Up / Down buttons\n\tFixed issue in Vendor Model Name got cleared after deselecting the field\n4.0.1\n\tFix issues where you could not open the Bluetooth Mesh Configurator in softwarep project editor.\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool_btmesh_configurator.feature.metadata.resource.feature.group/btmesh-16x16.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool_btmesh_configurator.feature.metadata.resource.feature.group", "label": "Bluetooth Mesh Configurator - 4.3.1", "featurePackageId": "com.silabs.ss.tool_btmesh_configurator.feature", "desc": "Bluetooth Mesh Configurator"}, {"installState": "Install", "releaseNotes": "4.5.6\n\tAddress issue when installing by connected Si91x device\n4.5.5\n  SV5.9 include wifi feature dependencies\n4.5.4\n\tSV5.9 release notes\n4.5.3\n\tSV5.7 release notes\n4.5.2\n\tEnable support for si917.\n4.5.1\n\tMinor code improvements.\n4.5.0\n\tAdd beta support for min/max current plots\n4.4.0\n\tAdd new Wireless Pro Kit support.\n\tAdd support for Logic Signal processing on Wireless Pro Kits.\n\tVisualize optionally up to eight Logic Signals per board. (Four Internal,  Four external)\n\tAdded new trigger options for the Logical signals, (High, Low, Rising, Falling)\n\tCustom positioning of Logic charts.\n4.3.7\n\tHandle segments with out of sequence start/end times \n4.3.6\n\tUpdates in support of BRD4002A\n4.3.5\n    Help pages moved to docs.silabs.com\n4.3.4\n    Updates for profiling device that has no part defined, automatically use target part from project\n4.3.3\n\tGeneral bug fixes\n4.3.2\n\tImprove error message for unsupported devices\n4.3.1\n\tGeneral bug fixes\n4.3.0\n\tWhen profiling device that has no part defined, automatically use target part from project\n4.2.7\n\tUpdates to infrastructure APIs\n4.2.6\n\tUpdates to infrastructure APIs\n4.2.5\n\tVarious fixes in the core components\n4.2.4\n\tUpdate tool compatibility - exclude Wi-Fi parts\n4.2.3\n\tGeneral bug fixes\n4.2.2\n\tFix issue where launch configuration was not correctly using the complete device context\n4.2.1\n\tFollowing API changes in infrastructure components\n4.2.0\n\tGeneral bug fixes\n4.0.13\n\tCorrection to spelling errors in documentation\n4.0.12\n\tCorrection to spelling errors in documentation\n4.0.11\n\tFollow version increment of sibling packages\n4.0.10\n\tProperly cleanup profiler launch contexts\n4.0.9\n\tFix issue with debug context after energy profiler executes\n4.0.8\n\tEnable Multi-node energy profiling\n4.0.7\n\tInternal version increment\n4.0.6\n\tAdded signature for authentication\n4.0.5\n\tEnable logging\n4.0.2\n\tImproved device arbitration between tool\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.energyprofiler.si8051.feature.metadata.resource.feature.group/icon_display_EnergyProfiler.png", "installEnabled": true, "id": "com.silabs.ss.tool.energyprofiler.si8051.feature.metadata.resource.feature.group", "label": "Simplicity Energy Profiler for EFM8 - 4.5.6", "featurePackageId": "com.silabs.ss.tool.energyprofiler.si8051.feature", "desc": "Simplicity Studio Energy Profiler for EFM8"}, {"installState": "Uninstall", "releaseNotes": "1.1.7\n\tImprove stack change behaviour\n\tFix issue with multiple commander adapter pack\n1.1.6\n\tMinor bug fixes and improvements.\n1.1.5\n\tMinor bug fixes and improvements.\n1.1.4\n\tValidation for Stack Name input\n1.1.3\n    Application Page is restored\n    Fix for AWS credentials handling (recognize profile that isn't tagged to default name)\n    Other minor maintenance fixes\n1.1.2\n    Integration fix for SV5.8 Simplicity Commander Adapter Pack (handling of multiple executables)\n1.1.1\n\tApplication Settings Page: set firmware related parameters\n\tCommunication Page: see uplink & send downlink messages with advanced options\n\tUI re-design on Infrastructure and End Device pages\n\tStack Update feature\n\tCompare local Cloud Formation template with the existing from the Cloud\n\tNotify users about the Cloud Formation Stack creation/modification statuses\n\tStack's destination related end devices are handled at delete (with fallback feature as well)\n\tRollback status handling for existing Stack\n\tTagging functionality is added for end devices\n    Delete feature for end devices with de-registration\n    Custom device name handling before provisioning\n\tCustom destination name handling for provisioning\n\tHandling of multiple credential profiles\n\tTest Connection feature in Credentials menu\n\tLink to Manual Page\n1.0.6\n    MFG page offset handling at erasing is refactored (based on xg24 mapping issue)\n1.0.5\n    Enable device connection for chips on custom boards\n    Recognizing Sidewalk compatible devices based on template xml files from Extension folder\n1.0.4\n\tOcelot support is added\n\tMargay explorer KIT support is added\n1.0.3\n\tMargay Support is added\n\tKG100S Radio Board handling is improved\n\tMissing Configuration Tools link is fixed\n1.0.2\n\tImproved erasing before uploading MFG file\n\tSidewalk Assistant is now showed up in Configuration Tools tab\n1.0.1\n\tImprove UX\n1.0.0\n\tInitial release of Amazon Sidewalk Tool", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.sidewalk.feature.metadata.resource.feature.group/sidewalk_16x16.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool.sidewalk.feature.metadata.resource.feature.group", "label": "Amazon Sidewalk Tool - 1.1.7", "featurePackageId": "com.silabs.ss.tool.sidewalk.feature", "desc": "Amazon Sidewalk Tool"}, {"installState": "Uninstall", "releaseNotes": "No content found", "isInstalled": true, "installEnabled": true, "id": "com.silabs.wireless.cockpit.feature.metadata.resource.feature.group", "label": "Simplicity Studio console - 8.20.4", "featurePackageId": "com.silabs.wireless.cockpit.feature", "desc": "Console for terminal-like interaction with the adapters."}, {"installState": "Uninstall", "releaseNotes": "1.1.16\n\tMinor bug fixes and improvements.\n1.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.wireless.wifi_memory_calculator.feature.metadata.resource.feature.group/icon.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.wireless.wifi_memory_calculator.feature.metadata.resource.feature.group", "label": "Wi-Fi Memory Calculator - 1.1.16", "featurePackageId": "com.silabs.ss.wireless.wifi_memory_calculator.feature", "desc": "Wi-Fi Memory Calculator"}, {"installState": "Uninstall", "releaseNotes": "4.3.5\n\tSV5.7 release notes\n4.3.4\n\tBuild feature with Eclipse 4.23\n4.3.3\n\tCode maintenance.\n4.3.2\n\tMinor feature update relating to diplay of icons\n4.3.1\n\tUpdates to infrastructure APIs\n4.3.0\n\tUse CSP_PREFIX to identify Capsense Profiler data from EFR32 devices\n4.2.1\n\tRemoving zgm130 part compatibility\n4.2.0\n\tGeneral bug fixes\n4.0.10\n\tImprove part-compatibility infrastructure for installation\n4.0.9\n\tFix issue with \"Use Device\" flow\n4.0.8\n\tPackaging data updates\n4.0.7\n\tFix bugs in Device/Port selection dialog\n\tUpdate profiler to support EFR32 devices\n4.0.6\n\tFix bug to display correct active/inactive thresholds values in plots\n\tAdd support for CPT212 and CPT213 devices\n\tAdd ability to profile device over serial port. Only works for C8051/EFM8 related devices.\n4.0.5\n\tAdded vcp setuptask as dependency to capsense profiler\n\tFix issue with error handling when device disconnects\n4.0.4\n\tImproved device arbitration between tool\n4.0.3\n\tAdd 8051 F996 part compatibility\n4.0.2\n\tFix missing selectable channels from the Control Panel for the Noise view\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.analysis.cs0.feature.metadata.resource.feature.group/capsense_profiler_40x40.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool.analysis.cs0.feature.metadata.resource.feature.group", "label": "Simplicity Capacitive Sense Profiler - 4.3.5", "featurePackageId": "com.silabs.ss.tool.analysis.cs0.feature", "desc": "Simplicity Capacitive Sense Profiler"}, {"installState": "Uninstall", "releaseNotes": "4.2.5\n\tAddress issue when installing by connected Si91x device\n4.2.4\n\tbugfix for JLink Pro support\n4.2.3\n\tSV5.7 release notes\n4.2.2\n\tBuild feautre with Eclipse 4.23\n4.2.1\n\tCode maintenance.\n4.2.0\n\tRefactoring of APIs in infrastructure\n4.0.5\n\tAdd JlinkConfigUtils as a public utility\n4.0.4\n\tBuild infrastructure change\n4.0.3\n\tProvide the required part name to JLink for debugging\n4.0.2\n\tupdates for jlink 6 compatibility\n4.0.1\n\tadd support for utilities that need jlink speed in kHz\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.support.jlink.feature.metadata.resource.feature.group/icon_jlink_support.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.support.jlink.feature.metadata.resource.feature.group", "label": "Debug Adapter Support for J-Link - 4.2.5", "featurePackageId": "com.silabs.ss.support.jlink.feature", "desc": "Configuration support for J-Link debug adapters"}, {"installState": "Uninstall", "releaseNotes": "1.2.1\n  Minor bug fixes around handling of board firmware.\n1.2.0\n  Improve handling of board with no initial firmware\n1.1.0\n  Minor enhancements\n1.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.support.wifi.feature.metadata.resource.feature.group/icon_wifi_support.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.support.wifi.feature.metadata.resource.feature.group", "label": "WiFi Part Support - 1.2.1", "featurePackageId": "com.silabs.ss.support.wifi.feature", "desc": "Configuration support for WiFi parts"}, {"installState": "Uninstall", "releaseNotes": "No content found", "isInstalled": true, "installEnabled": true, "id": "com.silabs.wireless.tool_na.feature.metadata.resource.feature.group", "label": "Simplicity Studio Network Analyzer - 8.20.4", "featurePackageId": "com.silabs.wireless.tool_na.feature", "desc": "Simplicity Studio Network Analyzer with set of decoder for packet trace visualization"}, {"installState": "Uninstall", "releaseNotes": "5.56.1\r\n   Release PC Controller application version 5.56.1\r\n5.55.1\r\n   Release PC Controller application version 5.55.1\r\n5.54.103\r\n   Release PC Controller application version 5.54.103\r\n5.53.103\r\n   Release PC Controller application version 5.53.103\r\n5.52.301\r\n  \tRelease PC Controller application version 5.52.301\r\n5.51.301\r\n  \tRelease PC Controller application version 5.51.301\r\n5.51.102\r\n\tRelease PC Controller application version 4.51.102\r\n5.50.102\r\n\tRelease PC Controller application version 4.50.102\r\n5.50.0\r\n\tRelease PC Controller application version 4.50\r\n5.42.0\r\n\tRelease PC Controller application version 4.42\r\n5.30.0\r\n\tInitial release of PC Controller application", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.zwave.app.pccontroller.feature.metadata.resource.feature.group/zwave_icon_40x40.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.zwave.app.pccontroller.feature.metadata.resource.feature.group", "label": "Z-Wave PC Controller - 5.56.1", "featurePackageId": "com.silabs.ss.zwave.app.pccontroller.feature", "desc": "Application for example of Static/Bridge Controller Serial API functionality"}, {"installState": "Uninstall", "releaseNotes": "No content found", "isInstalled": true, "installEnabled": true, "id": "com.silabs.wireless.wasp_studio.feature.metadata.resource.feature.group", "label": "WASP Studio Components - 8.9.12", "featurePackageId": "com.silabs.wireless.wasp_studio.feature", "desc": "Wireless application software components for Simplicity Studio"}, {"installState": "Uninstall", "releaseNotes": "********\r\n   Release Zniffer version ********\r\n4.68.1.7\r\n   Release Zniffer version 4.68.1.7\r\n4.68.0.2\r\n   Release Zniffer version 4.68.0.2\r\n4.67.1005\r\n   Release Zniffer version 4.67.100.5\r\n4.66.106\r\n   Release Zniffer version 4.66.106\r\n4.66.104\r\n   Release Zniffer version 4.66.104\r\n4.66.0\r\n   Release Zniffer version 4.66\r\n4.65.102\r\n\tRelease Zniffer version 4.65.102 of application\r\n4.65.0\r\n\tRelease Zniffer version 4.65 of application\r\n4.64.0\r\n\tRelease Zniffer version 4.64 of application\r\n4.62.1\r\n\tFix bug: put XML configuration files in Xml sub-directory for application\r\n4.62.0\r\n\tRelease Zniffer version 4.62 of application\r\n4.60.0\r\n\tRelease Zniffer version 4.60 of application\r\n4.57.1\r\n\tFix bug: add missing XML configuration files to application\r\n4.57.0\r\n\tInitial release of Zniffer development tool", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.zwave.app.zniffer.feature.metadata.resource.feature.group/zwave_icon_40x40.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.zwave.app.zniffer.feature.metadata.resource.feature.group", "label": "Z-<PERSON> - 4.69.1", "featurePackageId": "com.silabs.ss.zwave.app.zniffer.feature", "desc": "Application to detect RF communication in a Z-Wave network"}, {"installState": "Uninstall", "releaseNotes": "4.0.3\n\tUpdates needed for new Jep and Python.\n4.0.2\n\tSystem wide update\t\n4.0.1\n\tImprove error reporting to callers\n\tImprovement to an internal API\t\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.support.jep.feature.metadata.resource.feature.group/icon_jep_support.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.support.jep.feature.metadata.resource.feature.group", "label": "JEP Support - 4.0.3", "featurePackageId": "com.silabs.ss.support.jep.feature", "desc": "Simplicity Studio Python3 Support using JEP"}, {"installState": "Uninstall", "releaseNotes": "5.0.3\n\tMinor bug fixes and improvements.\n1.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.wireless.tool_wifi_commander.feature.metadata.resource.feature.group/icon_40x40.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.wireless.tool_wifi_commander.feature.metadata.resource.feature.group", "label": "Wi-Fi Commander - 5.0.3", "featurePackageId": "com.silabs.ss.wireless.tool_wifi_commander.feature", "desc": "Graphical UI to control an SiWG917 running the Wi-Fi firmware"}, {"installState": "Uninstall", "releaseNotes": "1.0.5\n\tAddress issue when installing by connected Si91x device\n1.0.4\n\tMinor bug fixes and improvements.\n1.0.3\n\tNo changes\n1.0.2\n\tMinor improvements\n1.0.1\n\tFix calculated length parsing on characteristic and descriptor\n1.0.0\n\tInitial release of standalone GATT Configurator package\n\tArchitectural improvement\n\tNew standard Bluetooth item description view\n\tManual table of content\n\tMinor improvements", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool_gatt_configurator.feature.metadata.resource.feature.group/bluetooth_16x16.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool_gatt_configurator.feature.metadata.resource.feature.group", "label": "Bluetooth GATT Configurator - 1.0.5", "featurePackageId": "com.silabs.ss.tool_gatt_configurator.feature", "desc": "Bluetooth GATT Configurator"}, {"installState": "Uninstall", "releaseNotes": "8.20.4\n\tSV5.10.0 release changes\n8.20.1\n\tMinor bug fixes and improvements.\n8.20.0\n\tMinor bug fixes and improvements.\nRelease SV5.1.0.0\n  - AppBuilder: Added Zigbee RTOS Plugin Upgrade Rule\n  - AppBuilder: Upgrades for ZCL8 support\n  - Network Analyzer: Zigbee decoders updated to ZCL8 along with door lock NFR\n  - Network Analyzer: Added Z-Wave Decoders to Network Analyzer\n  - Network Analyzer: Auto-decoder detects Z-Wave traffic and decodes as Z-Wave\n  - Network Analyzer: Support for BLE Group Addressing\n\nRelease SV5.0.0.0\n\n  Differentiate the appbuilder changes such that they apply for EFRs only.\n  Multinetwork application creation support\n  Profiling of AppBuilder and Simplicity Studio Heap Space issues\n  Remove appbuilder restrictions on the creation of more than 2 networks for multinetwork\n  Upgrade rules for plugins name changes and modifications  Bug fix: Appbuilder defaults to uneditable values if absolute path to StackRoot is not found\n  Bug fix: Getting an error on upgrading isd file format on MacOS\n  Bug fix: ISC file does not save the settings in the Zigbee stack tab. Version *******\n  Bug fix: NCP needs upgrade rule for enabling multi-pan-stub-library when multi-pan-stub-library and multi-pan-library not found in ISC\n  Bug fix: Resolve generation issues with xstream\n  Bug fix: Studio console buffer not even large enough to display railtest help\n  Bug fix: Zigbee Makefile can't handle relative paths with spaces\n\n  Issues with detailed description:\n\n  Bug fix: IAR Project file does not have all of the include paths from a Simplicity Studio project\n           The issue appears to relate to long relative pathname references that exceed limits in certain Windows environments. Recommended workaround is to locate Studio workspace so as to reduce the length of relative pathname references between Studio workspace location (where customer application resides) and Studio installation location (where Gecko SDK resides).\n  Bug fix: An invalid value of EMBER_AF_PLUGIN_IAS_ZONE_SERVER_ZONE_TYPE is generated by AppBuilder\n           A fix was made in AppBuilder to generate a correct value for C macro definition EMBER_AF_PLUGIN_IAS_ZONE_SERVER_ZONE_TYPE, based on the setting of the Zone Type option in the EmberZNet IAS Zone Server plugin. Previously, the value for this macro definition was invalid, causing a compilation error and requiring a manual edit to correct.\n  Bug fix: EmberZNet Appbuilder projects do not 'merge' or 'carryover' changes to Bootloader Configuration setting\n           In an EmberZNet project, if [Edit Architecture] is used in AppBuilder to change the board, part or toolchain, it will revert any changes to the Bootloader Configuration on the [HAL] tab back to the Application default. \n           Workaround: change the Bootloader Configuration to the desired setting again.\n\n           Known Issue: In an EmberZNet project, if [Edit Architecture] is used in AppBuilder to change the board, part or toolchain, it will revert any changes to the Bootloader Configuration on the [HAL] tab back to the Application default. Workaround: change the Bootloader Configuration to the desired setting again.\n  Bug fix: Remove postbuild support for mac/linux in V5\n           Prebuild and postbuild processing is no longer supported for mac and linux users starting 20Q2 release of Studio V5 and 20Q2 release of SDK 3.0\n  Bug fix: IAR Project file does not have all of the include paths from a Simplicity Studio project\n           The issue appears to relate to long relative pathname references that exceed limits in certain Windows environments. Recommended workaround is to locate Studio workspace so as to reduce the length of relative pathname references between Studio workspace location (where customer application resides) and Studio installation location (where Gecko SDK resides).\n  Bug fix: An invalid value of EMBER_AF_PLUGIN_IAS_ZONE_SERVER_ZONE_TYPE is generated by the app builder\n           A fix was made in AppBuilder to generate a correct value for C macro definition EMBER_AF_PLUGIN_IAS_ZONE_SERVER_ZONE_TYPE, based on the setting of the Zone Type option in the EmberZNet IAS Zone Server plugin. Previously, the value for this macro definition was invalid, causing a compilation error and requiring a manual edit to correct.\n  Bug fix: \"Launch Console\" (silink.exe) cannot be started again after stopping a debug session\n           If a debug session is started using a WSTK and then the Device Console tool is launched, the console tool works correctly. However if the debug session is ended, then the Device Console cannot be made functional again without restarting Simplicity Studio.\n           This issue will be fixed in an upcoming patch release of Simplicity Studio V5.\n  Bug fix: DMP SED project doesn't generate when BLE and micrium OS SDKs were installed\n           The EmberZNet SDK's dynamic multi-protocol (DMP) sample applications rely on the Micrium OS and BLE SDKs also being installed. If those SDKs are not installed, the DMP sample applications are still presented as options. Attempting to create a DMP sample application may appear to complete, but subsequently will fail to generate because of unresolved dependencies on those SDKs. Subsequently installing those SDKs may not resolve the issues in that existing DMP sample application project. Be sure to install the Micrium OS and BLE SDKs before attempting to create an EmberZNet DMP sample application project.\n  Bug fix: DMP SED project doesn't create when BLE and micrium OS SDKs are missing\n           The EmberZNet SDK's dynamic multi-protocol (DMP) sample applications rely on the Micrium OS and BLE SDKs also being installed. If those SDKs are not installed, the DMP sample applications are still presented as options. Attempting to create a DMP sample application project can fail to complete, with no explicit indication about the missing SDKs. Subsequently installing those SDKs may not resolve the issues in that existing DMP sample application project, if it was created. Be sure to install the Micrium OS and BLE SDKs before attempting to create an EmberZNet DMP sample application project.\n\nEarlier releases\n\n  Hardware support\n    Added support for wireless mesh modules\n\n  IDE integration\n    IDE capabilities for wireless software integration, phase 4\n\n\n  IDE capabilities for wireless software integration, phase 3\n  Zigbee and Thread protocol in-depth support\n  IDE capabilities for wireless software integration, phase 2\n  Next Generation wireless protocol IDE support\n  IDE capabilities for wireless software integration, phase 1\n  Initial release of wireless support IDE integration", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.wireless.integration.feature.metadata.resource.feature.group/icon_display_SimplicityIDE.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.wireless.integration.feature.metadata.resource.feature.group", "label": "Wireless Tools IDE Integration - 8.20.4", "featurePackageId": "com.silabs.wireless.integration.feature", "desc": "Integration of wireless tools with Studio IDE"}, {"installState": "Uninstall", "releaseNotes": "4.0.16\n\tAddress issue when installing by connected Si91x device\n4.0.15\n\tFix version cache issue with custom boards\n4.0.14\n\tMinor bug fixes and improvements.\n4.0.13\n\tAdd support for SG28 parts\n4.0.12\n   Add support for SG23 parts\n4.0.11\n   SV5.7 release notes\n4.0.10\n   Fix tamper provisioning issues\n4.0.9\n   Update signing\n4.0.8\n\tCode maintenance\n4.0.7\n\tGeneral bug fixes\n4.0.6\n\tAdd support for new Series 2 features\n4.0.5\n\tEnable support for XG22 parts\n4.0.4\n\tEnable additional secure boot features\n4.0.3\n\tEnable secure boot features\n4.0.2\n\tUseability improvements\n4.0.1\n\tGeneral bug fixes\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.support.dci.feature.metadata.resource.feature.group/icon_dci_support.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.support.dci.feature.metadata.resource.feature.group", "label": "Secure Part DCI Support - 4.0.16", "featurePackageId": "com.silabs.ss.support.dci.feature", "desc": "Configuration support for secure parts with Debug Challenge Interface"}, {"installState": "Uninstall", "releaseNotes": "4.3.8\n\tSV5.10.0 release changes\n4.3.7\n  Enhance support for flashing devices in minimial Studio install.\n4.3.6\n  Minor bug fixes and improvements.\n4.3.5\n  Minor bug fixes and improvements.\n4.3.4\n\tMinor bug fixes and improvements.\n4.3.3\n\tChange message to user when no SE f/w files are available for the current part.\n4.3.2\n\tSV5.7 release notes\n4.3.1\n\tAdding support for deubg-out-only mode board.\n4.2.12\n\tFix issue with external debug adapter\n4.2.10\n   Fix for Locale issue with Turkish OS\n4.2.9\n\tCode maintenance.\n4.2.8\n    General bug fixes\n4.2.7\n\tGeneral bug fixes\n4.2.6\n\tEnable support for XG22 parts\n4.2.5\n\tInfrastructure changes\n4.2.4\n\tImprove warning message during bootloader update\n4.2.3\n\tCTune panel bug fixes\n4.2.2\n\tProvide error message when SE firmware version is unsupported\n4.2.1\n\tLaying down some groundwork for new features\n4.2.0\n\tGeneral bug fixes\n4.0.8\n\tFix issues with the changelog link.\n4.0.7\n\tImprovement to displaying the change-log link.\n\tMake adapter text on Launcher \"copy-able\".\n4.0.6\n\tFix issue where Download Firmware link does nothing for EMF32GG11 and EF32TG11 parts\n\tProvide error message when no firmware assets are available\n4.0.5\n\tUpdated Firmware handling\n4.0.3\n\tImproved handling of missing adapter pack\n4.0.2\n\trefresh device detection values when changing MCU mode\n4.0.1\n\tImprove error handling from flashing process\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.support.hwtools.feature.metadata.resource.feature.group/icon_hwtools_support.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.support.hwtools.feature.metadata.resource.feature.group", "label": "Debug Adapter Support for J-Link OB - 4.3.8", "featurePackageId": "com.silabs.ss.support.hwtools.feature", "desc": "Configuration support for Exx32/EFM8 On-board J-Link debug adapters"}, {"installState": "Install", "releaseNotes": "4.2.5\n\tSV5.7 release notes\n4.2.4\n\tBuild feature with Eclipse 4.23\n4.2.3\n\tCode maintenance\n4.2.2\n\tChange font in editor's Terminal section for improved readability\n4.2.1\n\tMinor feature update relating to diplay of icons\n4.2.0\n\tGeneral bug fixes\n4.0.7\n\tVarious improvements and features for new devices\n4.0.6\n\tAdd serial dependency for BGX port detection\n4.0.5\n\tFix UI colors on Windows\n4.0.4\n\tFix bug with prompt to download asset\n4.0.3\n\tImprove behavior with unsupported parts\n4.0.2\n\tAdd 'Devices' view to Xpress Configurator perspective default\n4.0.1\n\tFix auto-scroll issue\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.xpressconfig.feature.metadata.resource.feature.group/icon_display_FixedFunction.png", "installEnabled": true, "id": "com.silabs.ss.tool.xpressconfig.feature.metadata.resource.feature.group", "label": "Xpress Configurator - 4.2.5", "featurePackageId": "com.silabs.ss.tool.xpressconfig.feature", "desc": "Simplicity Studio Xpress Configurator"}, {"installState": "Uninstall", "releaseNotes": "4.2.4\n    Signing Windows EXE/DLL\n4.2.3\n\tSV5.7 release notes\n4.2.2\n\tBuild feature with Eclipse 4.23\n4.2.1\n\tMinor feature update relating to diplay of icons\n4.2.0\n\tUpdate dependencies and infrastructure refactoring\n4.0.3\n\tFix the tool's part compatibility such that it's only compatible with Gecko, Giant Gecko, <PERSON>pard Gecko, Tiny Gecko, Wonder Gecko and Zero Gecko parts\n4.0.2\n\tAdded signature for authentication\n4.0.1\n\tEnable logging when starting the tool\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.battery_estimator.feature.metadata.resource.feature.group/icon_display_energyAwareBattery.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool.battery_estimator.feature.metadata.resource.feature.group", "label": "Simplicity Battery Estimator - 4.2.4", "featurePackageId": "com.silabs.ss.tool.battery_estimator.feature", "desc": "Simplicity Studio Battery Estimator"}, {"installState": "Uninstall", "releaseNotes": "4.0.11\n\tSV5.9 - adding support for SiSDK\n4.0.10\n\tSV5.7 release notes\n4.0.9\n\tBuild feature with Eclipse 4.23\n4.0.8\n   Code maintenance.\n4.0.7\n   Cosmetic change to icon color\n4.0.6\n\tBug fixes\n4.0.5\n\tSupport for VOC tab\n\tOther Improvements\n4.0.3\n\tNo change to tool resources; version increment to synchronize early access packages\n\tPackage version for early_access in SV4.1.13.4\n4.0.2\n\tNo change to tool resources; version increment to synchronize early access packages\n\tPackage version for early_access in SV4.1.13.3\n4.0.1\n\tNo change to tool resources; version increment to synchronize early access packages\n\tPackage version for early_access in SV4.1.13.1\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.efp.feature.metadata.resource.feature.group/tool_efp_40x40.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool.efp.feature.metadata.resource.feature.group", "label": "EFP Configuration Tool - 4.0.11", "featurePackageId": "com.silabs.ss.tool.efp.feature", "desc": "EFP Configuration Tool"}, {"installState": "Uninstall", "releaseNotes": "4.2.12\n\tAddress issue when installing by connected Si91x device\n4.2.11\n\tSV5.7 release notes\n4.2.10\n\tFix text box width issues on linux\n4.2.9\n\tBuild feature with Eclipse 4.23\n4.2.8\n\tAddressed issue where device was not being flashed\n4.2.7\n\tCode maintenance.\n4.2.6\n\tGeneral bug fixes\n4.2.5\n\tMinor feature update relating to diplay of icons\n4.2.4\n\tImprove error handling when secure boot fails\n4.2.3\n\tFix issue where type of selected file, e.g. hex, bin, and such was not configuring the UI the way it is supposed to\n4.2.2\n\tGeneral bug fixes\n4.2.1\n\tAddress an issue where a UI object was not handled properly\n4.2.0\n\tGeneral bug fixes\n4.0.7\n\tEnable flashing of axf/s37 files from context menu on binary file in project\n4.0.6\n\tMinor bug fixes\n4.0.5\n\tInternal version increment\n4.0.4\n\tPackage signing\n4.0.3\n\tInternal version increment\n4.0.2\n\tupdates for jlink 6 compatibility\n4.0.1\n\tUpdate for Commander version 0.18.1\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.flashprogrammer.exx32.feature.metadata.resource.feature.group/icon_display_flashProgrammer.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool.flashprogrammer.exx32.feature.metadata.resource.feature.group", "label": "Flash Programmer for Exx32 - 4.2.12", "featurePackageId": "com.silabs.ss.tool.flashprogrammer.exx32.feature", "desc": "Simplicity Studio Flash Programmer for Exx32"}, {"installState": "Uninstall", "releaseNotes": "4.2.5\n\tSV5.7 release notes\n4.2.4\n\tBuild feature with Eclipse 4.23\n4.2.3\n\tMinor maintenance.\n4.2.2\n   Removing zgm130 from part compatibility\n4.2.1\n   Demoting package from recommended to optional\n4.2.0\n\tGeneral bug fixes\n4.0.2\n\tAdded signature for authentication\n4.0.1\n\tExtend infrastructure for multiple IDEs\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.ide.keil.feature.metadata.resource.feature.group/keil_uvision_icon.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool.ide.keil.feature.metadata.resource.feature.group", "label": "Keil uVision Integration - 4.2.5", "featurePackageId": "com.silabs.ss.tool.ide.keil.feature", "desc": "Support of Keil uVision in Simplicity Studio"}, {"installState": "Uninstall", "releaseNotes": "4.3.8\n\tSV5.10.0 release changes\n4.3.7\n\tMinor bug fixes and improvements.\n4.3.6\n\tFix bug where Pintool editor was not saving changes.\n4.3.5\n\tFix bug where the hardware configuration changes are lost when updating SDK on a project.\n4.3.4\n\tSV5.7 release notes\n4.3.3\n\tCode maintenance.\n4.3.2\n\tPintool editor conversion to web ui.\n4.3.1\n\tMinor maintenance.\n4.3.0\n\tBuild with and for Eclipse 4.23.\n4.2.9\n\tMinor maintenance.\n4.2.8\n\tFix issue with un-assigning GPIO_SWx function with EFM32PG22.\n\tWhen Pintool does not find relevant software component, then open the Component Library without the search string. \n\tCorrect UI refresh behavior.\n4.2.7\n   Fix for Locale issue with Turkish OS\n4.2.6\n\tCode maintenance.\n4.2.5\n\tNow showing HwConf editor only for 8051 parts\n4.2.4\n\tFix issue where changing part/device was not updating the .pintool file.\n\tAdd menu item to \"Add Components...\" for a Pin or Peripheral.\n4.2.3\n\tFix bug for BGX device locks in Xpress Configurator editor\n4.2.2\n\tGeneral bug fixes\n\tInfrastructure updates for future extensions\n4.2.1\n\tAddress issue with cut off text in module package presentation\n4.2.0\n\tGeneral bug fixes\n4.0.12\n\tFix issues with plugin versions\n4.0.10\n\tUnsupported modules are not displayed at all\n\tImprove presentation of hardware conflicts\n\tEnable zooming with mouse wheel in package view\n4.0.10\n\tImproved handling of upgrade messages\n4.0.9\n\tBetter handling of case of missing hardware configuration model\n\tAdd progress bar when generating\n4.0.8\n\tVarious changes to support hardware configuration in App Builder editor\n4.0.7\n\tVarious bug fixes and improvements\n4.0.6\n\tImprove Dark Theme with Hardware Configurator editor\n4.0.5\n\tImproved logging\n4.0.4\n\tFix font size scaling seen in BGM121\n4.0.3\n\tFix for invalid SWT widget access\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.hwconfig.feature.metadata.resource.feature.group/icon_display_configurator.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool.hwconfig.feature.metadata.resource.feature.group", "label": "Hardware Configurator - 4.3.8", "featurePackageId": "com.silabs.ss.tool.hwconfig.feature", "desc": "Simplicity Studio Hardware Configurator"}, {"installState": "Update", "releaseNotes": "1.1.18\n\tMinor bug fixes and improvements.\n1.1.17\n\tMinor bug fixes and improvements.\n1.1.16\n\tMinor bug fixes and improvements.\n1.1.15\n\tMinor bug fixes and improvements.\n1.1.14\n\tMinor bug fixes and improvements.\n1.1.13\n\tMinor bug fixes and improvements.\n1.1.12\n\tUpdate Radio Configurator generator to v2302.7.2\n1.1.11\n\tUpdate Radio Configurator generator to v2304.5.2\n1.1.10\n\tUpdate Radio Configurator generator to v2304.4.4\n1.1.9\n\tUpdate Radio Configurator generator to v2302.6.1\n1.1.8\n\tUpdate Radio Configurator generator to v2302.5.1\n1.1.7\n\tRX Direct Mode support for Series-2\n\tWi-SUN Fan 1.1 Concurrent OFDM option support\n\tUpdate Radio Configurator generator to v2302.4.4\n1.1.6\n\tFix encoding issue in backing data.\n1.1.5\n\tUpdate Radio Configurator generator to v2204.6.2\n1.1.4\n\tUpdate Radio Configurator generator to v2023.2.1\n1.1.3\n\tImprove layour handling\n\tFix issue with EFRXG22 restrictions\n\tUpdate Radio Configurator generator to v2023.1.1\n1.1.2\n\tA preselector was added to select the regulatory domain, operating class, and operating mode for Wi-Sun PHYs, which supports the selection of the right PHY.\n\tPHY override capability for channel settings was added to allow users to override the PHY settings derived from the protocol level.\n1.1.1\n\tAdd Symbol map selection to OOK modulation for xG23 and up\n\tAdd .radioconf import functionality \n\tFix Save issue from Wi-SUN Configurator apply action\n1.1.0\n\tAdd new Wi-SUN PHY selector support\n\tImprove dependency between symbol map and modulation\n\tAdd Backwards Compatibility for Radio Configurator\n\tUpdate Radio Configurator generator to v2022.5.2\n1.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.proprietary.multiphy_radio_configurator.feature.metadata.resource.feature.group/icon.png", "installEnabled": true, "id": "com.silabs.ss.proprietary.multiphy_radio_configurator.feature.metadata.resource.feature.group", "label": "Multiphy Radio Configurator - 1.1.18", "featurePackageId": "com.silabs.ss.proprietary.multiphy_radio_configurator.feature", "desc": "Multiphy Radio Configurator"}, {"installState": "Install", "releaseNotes": "1.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.priority.configurator.feature.metadata.resource.feature.group/icon.png", "installEnabled": true, "id": "com.silabs.ss.tool.priority.configurator.feature.metadata.resource.feature.group", "label": "Radio Priority Configurator - 1.0.0", "featurePackageId": "com.silabs.ss.tool.priority.configurator.feature", "desc": "Radio Priority Configurator"}, {"installState": "Uninstall", "releaseNotes": "4.2.5\n\tImprove GDB debugger integration\n4.2.4\n\tRebuild feature for SV5.7.0.0 release\n4.2.3\n\tBuild feature with Eclipse 4.23\n4.2.2\n   Fix for Locale issue with Turkish OS\n4.2.1\n   Cosmetic change to icon color\n4.2.0\n\tUpdates to dependencies and infrastructure changes.\n4.1.1\n\tFix to project template\n4.1.0\n\tSystemView fixes/improvements due to changes in SDK v2.2.0 (v5.4.0)\n4.0.0\n\tInitial release. Provides Segger SystemView and integration into IDE/Debugger.", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.segger.systemview.integration.feature.metadata.resource.feature.group/systemview.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.segger.systemview.integration.feature.metadata.resource.feature.group", "label": "Segger SystemView Integration - 4.2.5", "featurePackageId": "com.silabs.ss.segger.systemview.integration.feature", "desc": "Segger SystemView for profiling and analysis of an embedded system."}, {"installState": "Uninstall", "releaseNotes": "4.2.5\n\tGeneral bug fixes\n4.2.4\n\tSV5.7 release notes\n4.2.3\n\tBuild feature with Eclipse 4.23\n4.2.2\n\tImprovement for future extensions\n4.2.1\n\tUpdate to the package installation compatibility definition\n4.2.0\n\tUpdated dependencies and infrastructure refactor\n4.0.4\n\tPreferred IDE: users can specify their preferred IDE installation to use for a given IDE. IDE installation such as IAR are discovered by scanning registered toolchains and any Windows Registry.\n4.0.3\n\tImprove part-compatibility infrastructure for installation\n4.0.2\n\tAdded signature for authentication\n4.0.1\n\tVarious bug fixes\n\tExtend functionality around multiple IDEs\n\tImprove behavior with multiple IAR installations\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.ide.iar.feature.metadata.resource.feature.group/iar_workbench_icon.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool.ide.iar.feature.metadata.resource.feature.group", "label": "IAR Embedded Workbench Integration - 4.2.5", "featurePackageId": "com.silabs.ss.tool.ide.iar.feature", "desc": "Support of IAR Embedded Workbench in Simplicity Studio"}, {"installState": "Update", "releaseNotes": "5.1.2\n\tUpdate to BgApi 9.1.0\n5.1.1\n\tUpdate to BgApi 9.0.1\n5.1.0\n\tUpdate to BgApi 9.0.0\n\tMigrate reset to reboot command\n\tMinor improvements\n5.0.2\n\tUpdate to BgApi 8.2.0\n5.0.1\n\tUpdate to BgApi 8.1.0\n5.0.0\n\tUpdate to BgApi 8.0.0\n\tMinor improvements\n4.3.9\n\tUpdate to BgApi 7.1.0\n4.3.8\n\tUpdate to BgApi 7.0.1\n4.1.10\n\tAdding native builds for Mac ARM64 (macosx_aarch64) systems\n\tUpdate to BgApi 7.0.0\n\tRemove deprecated Advertiser and Scanner APIs usage from UI, Console still supported\n\tMinor improvements\n4.1.9\n\tUpdate to BgApi 6.2.0\n4.1.8\n\tReplace nwjs to electron\n\tUpdate to BgApi 6.1.0\n4.1.7\n\tUpdate to BgApi 6.0.0\n\tFix passkey copy\n\tFix Discovered Devices table\n\tSupport custom baud rate\n4.1.6\n\tFix keyboard only bonding dialog\n4.1.5\n\tBluetooth Mesh 1.1 Support\n\tUpdate to BgApi 5.1.1\n4.1.4\n\tUpdate to BgApi 5.1.0\n4.1.3\n\tUpdate to BgApi 5.0.0\n\tImprove RSSI filter slider\n\tNew API reference Search options\n\tOrder API reference entries\n\tAuto-pad bytes in Local GATT\n\tShow number of subscribed devices in Local GATT\n\tReject UI refresh on permission error in Remote GATT\n\tImprove Advertise Set Power Section\n\tAllow unloading custom API\n\tStore Scan table's pagination as persistent Setting\n\tNew: Models page, support for Generic OnOff and Level Get, Set and State\n\tNew: Key Refresh section, reworked DDB deletion\n\tNew: automatic retry option for config_client commands failed with timeout (enable in Settings)\n\tNew: ability to create App Keys and Networks with custom key, and group with custom address\n\tNew: App Keys, Subscriptions and Publish can be removed\n\tNew: Groups, App Keys, Networks and Nodes can be renamed\n\tNew: Mesh operations supported by the UI also work when typed manually in the console\n\tReworked local mesh data saving: Only labels (names) are saved for Networks, App Keys & Nodes. Groups are re-created when adding an already known network. Stored data clearable in Settings\n\tButtons, labels and pages renamed to align with mesh terminology\n\tImproved flow and UI for Initialize as Provisioner\n\tIntroduced app-wide colours for App Keys and Groups (orange and purple)\n\tApp Keys and Networks show their indices instead of their keys\n\tAdded ability to copy unformatted keys and UUIDs (on hover)\n\tNode Configuration no longer shows false values initially\n\tLong mesh operations have in-progress indication\n\tBind to All Models is now in the DCD Section\n\tImproved Provision page\n\tImproved Networks & Devices page\n\tImproved Node Configure page\n\tImproved individual model configuration\n\tImproved Mesh Settings page\n\tUnknown models In DCD now show up and are configurable\n\tMesh stability improvements and bugfixes\n4.1.2\n\tFixed issue with read and write request dialog.\n4.1.1\n\tUpdate to BgApi 4.2.0\n4.1.0\n\tSupport new BLE scan events on the Discovered Devices table.\n\tImprove Dark Theme UI\n4.0.8\n\tReshape smart console helper popup\n\tShow public Bluetooth address on provision device scan\n\tVisual and UX improvements\n\tSIG Models renamed to DCD (Device Composition Data)\n\tFix adding model subscriptions\n\tFix adding model publish addresses\n\tFix binding App Key to Vendor Models\n\tFix getting DCD via UI button and console\n4.0.7\n\tEnable unlisted enum value entry in Smart Console\n\tUpdate BG API: reset_to_dfu command, fix some bit mask values\n\tMesh Provision: Correct Device Key to Bluetooth Address in Discovered Devices table\n\tMesh Provision: Added default ordering by UUID to Discovered Devices table\n4.0.6\n\tAdd new security manager configuration flag\n\tFix CTE Length setting issue\n\tUpdate to latest BGAPI\n\tFix issue with Global Transmit Power Slider\n\tDeleting a Mesh Group now requires confirmation\n\tFixed issue in Device Tab name did not rename when the device was renamed\n\tChanged default Mesh LPN Timeout in Settings\n\tAdded Secure Network Beacon switch to Mesh device setting tab\n\tImplemented setting Unicast Address Range during Mesh Provisioning\n4.0.5\n   Fix issues with symbolic links on Mac OS and property values with spaces\n4.0.4\n   Updates for Studio SV5.3.1.0.\n4.0.3\n   Updated signing\n4.0.2\n   Updates for Gecko SDK 3.2.2.\n4.0.1\n   Maintenance\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.apack.ncp_commander.feature.metadata.resource.feature.group/bluetooth_brackets_40x40.png", "installEnabled": true, "id": "com.silabs.apack.ncp_commander.feature.metadata.resource.feature.group", "label": "Bluetooth NCP Commander Standalone for EFR32 - 5.1.2", "featurePackageId": "com.silabs.apack.ncp_commander.feature", "desc": "Standalone executable version of Bluetooth NCP Commander"}, {"installState": "Install", "releaseNotes": "4.3.6\n\tUpdate Keil Serial Product Number that extends license to Feb 2028 \n4.3.5\n\tSV5.8 release.\n4.3.4\n\tSV5.7 release notes\n4.3.3\n\tAdd more logging for troubleshooting purposes.\n4.3.2\n\tFix issue with spaces in SDK path\n4.3.1\n\tBuild feature with Eclipse 4.23\n4.2.9\n\tUpdate Keil Serial Product Number that extends license to Feb 2025\n4.2.8\n\tRemove the nature filters from Keil so it continues building even if a CPP file is added\n4.2.7\n\tAdd support for Multi-Project Solutions\n4.2.6\n\tCode maintenance.\n4.2.5\n\tCorrection to icon color\n\tInfrastructure changes for debuggers\n4.2.4\n\tMinor infrastrucutre chage that does not affect features in any way\n4.2.3\n\tInfrastrucutre updates for future extensions\n4.2.2\n\tUpdate to the master serial number for Keil 8051 tools\n4.2.1\n\tFix issue where launch configuration was not correctly using the complete device context\n4.2.0\n  General improvements and bug fixes\n4.0.11\n\tInfrastructure updates for extensibility\n4.0.10\n\tDon't add CODE or CONST for the extended linker\n4.0.9\n\tAdd better error logging for case when license request is failing\n4.0.8\n\tPackage signing\n4.0.7\n\tFix issue where debugger does not halt on next instruction when stepping through a bit check\n4.0.6\n\tFixed stepping over J<PERSON> and JNZ instructions\n4.0.5\n\tCDT does not need to compile *.INC files.\n4.0.4\n\tImprove 3-byte pointer handling in the debugger\n4.0.3\n\tGeneral bug fixes\n4.0.2\n\tEmploy Keil serial number for 8051 tools that provides support until March 2019\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.ide.c8051.feature.metadata.resource.feature.group/icon_display_SimplicityIDE.png", "installEnabled": true, "id": "com.silabs.ss.tool.ide.c8051.feature.metadata.resource.feature.group", "label": "8051 IDE - 4.3.6", "featurePackageId": "com.silabs.ss.tool.ide.c8051.feature", "desc": "Allows you to create, edit, build and debug 8051 applications"}, {"installState": "Uninstall", "releaseNotes": "1.1.15\n\tMinor bug fixes and improvements.\n1.1.14\n\tMinor bug fixes and improvements.\n1.1.13\n\tMinor bug fixes of CS Analyzer\n1.1.12\n\tMinor bug fixes and improvements.\n1.1.11\n\tFix minor issues\n1.1.10\n\tFix the ABR Analyzer antenna offset issue and improve the charts of the AoA Analyzer.\n1.1.9\n\tABR Analyzer has been published with new analytic charts.\n1.1.8\n\tHADM Analyzer was added\n1.1.7\n\tMinor bug fixes\n1.1.6\n\tImport-export functions were added to Positioning tools.\n\tMinor bugfixes and performance improvement.\n1.1.5\n\tFix import issue in Positioning Tool\n\tFix Antenna Mode selection for devices with boards configured Positioning Tool\n1.1.4\n\tFix Azimuth Angle issue in AoA Analyzer\n\tFix AoA Analyzer reconnection issue\n\tInfrastructure changes.\n\tFix detection issue in Positioning Tool\n1.1.3\n\tAoA Analyzer: Minor bug fixes and performance improvements.\n1.1.2\n\tMake Device connection process more intuitive on AoA Analyzer\n\tAdd Device ID information right after connection\n\tImprove dialog window layout and sizing\n1.1.1\n\tAdd missing export functionality to Positioning Tool\n\tAdd extra help text\n\tFix Typos\n\tFix Asset tag removal after disconnection\n1.1.0\n\tAdd BRD4191A board support.\n\tRedesign Direction Finding Tooling from being Project centered to Device centered.\n\tUpdate to latest RTL Library.\n\tAdd angle line visualization.\n\tFix issue in switching pattern selection for 1x4 configuration\n\tFix configuration import from .json file. \n\tCreate a Device Identification System.\n\tImprove Asset handling - add, remove, idle.\n1.0.5\n\tFix bug where AoA recording does not stop when stop button is pressed.\n\tFix bug where second and subsequent AoA recordings are not saved.\n1.0.4\n\tCorrected label in AoA Viewer infobox\n\tFix issue with delete button on histogram\n\tAddress issue where AoA Viewer infobox was too narrow\n1.0.2\n\tInfrastructure improvements.\n1.0.1\n\tMinor bug fixes.\n1.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.bluetooth.aox.feature.metadata.resource.feature.group/bluetooth_brackets_40x40.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.bluetooth.aox.feature.metadata.resource.feature.group", "label": "AoA Tools - 1.1.15", "featurePackageId": "com.silabs.ss.bluetooth.aox.feature", "desc": "Suite of tools supporting AoA development"}, {"installState": "Update", "releaseNotes": "5.1.2\n\tUpdate to BgApi 9.1.0\n5.1.1\n\tUpdate to BgApi 9.0.1\n5.1.0\n\tUpdate to BgApi 9.0.0\n\tMigrate reset to reboot command\n\tMinor improvements\n5.0.2\n\tUpdate to BgApi 8.2.0\n5.0.1\n\tUpdate to BgApi 8.1.0\n5.0.0\n\tUpdate to BgApi 8.0.0\n\tMinor improvements\n4.3.9\n\tUpdate to BgApi 7.1.0\n4.3.8\n\tUpdate to BgApi 7.0.1\n4.3.7\n\tUpdate to BgApi 7.0.0\n\tRemove deprecated Advertiser and Scanner APIs usage from UI, Console still supported\n\tMinor improvements\n4.3.6\n\tUpdate to BgApi 6.2.0\n4.3.5\n\tUpdate to BgApi 6.1.0\n4.3.4\n\tUpdate to BgApi 6.0.0\n\tFix passkey copy\n\tFix Discovered Devices table\n4.3.3\n\tFix keyboard only bonding dialog\n4.3.2\n\tBluetooth Mesh 1.1 Support\n\tUpdate to BgApi 5.1.1\n4.3.1\n\tUpdate to BgApi 5.1.0\n4.3.0\n\tUpdate to BgApi 5.0.0\n\tImprove RSSI filter slider\n\tNew API reference Search options\n\tOrder API reference entries\n\tAuto-pad bytes in Local GATT\n\tShow number of subscribed devices in Local GATT\n\tReject UI refresh on permission error in Remote GATT\n\tImprove Advertise Set Power Section\n\tAllow unloading custom API\n\tStore Scan table's pagination as persistent Setting\n\tNew: Models page, support for Generic OnOff and Level Get, Set and State\n\tNew: Key Refresh section, reworked DDB deletion\n\tNew: automatic retry option for config_client commands failed with timeout (enable in Settings)\n\tNew: ability to create App Keys and Networks with custom key, and group with custom address\n\tNew: App Keys, Subscriptions and Publish can be removed\n\tNew: Groups, App Keys, Networks and Nodes can be renamed\n\tNew: Mesh operations supported by the UI also work when typed manually in the console\n\tReworked local mesh data saving: Only labels (names) are saved for Networks, App Keys & Nodes. Groups are re-created when adding an already known network. Stored data clearable in Settings\n\tButtons, labels and pages renamed to align with mesh terminology\n\tImproved flow and UI for Initialize as Provisioner\n\tIntroduced app-wide colours for App Keys and Groups (orange and purple)\n\tApp Keys and Networks show their indices instead of their keys\n\tAdded ability to copy unformatted keys and UUIDs (on hover)\n\tNode Configuration no longer shows false values initially\n\tLong mesh operations have in-progress indication\n\tBind to All Models is now in the DCD Section\n\tImproved Provision page\n\tImproved Networks & Devices page\n\tImproved Node Configure page\n\tImproved individual model configuration\n\tImproved Mesh Settings page\n\tUnknown models In DCD now show up and are configurable\n\tMesh stability improvements and bugfixes\n4.2.2\n\tFixed issue with read and write request dialog.\n4.2.1\n\tUpdate to BgApi 4.2.0\n4.2.0\n\tSupport new BLE scan events on the Discovered Devices table.\n\tImprove Dark Theme UI\n4.1.2\n\tReshape smart console helper popup\n\tShow public Bluetooth address on provision device scan\n\tVisual and UX improvements\n\tSIG Models renamed to DCD (Device Composition Data)\n\tFix adding model subscriptions\n\tFix adding model publish addresses\n\tFix binding App Key to Vendor Models\n\tFix getting DCD via UI button and console\n4.1.1\n\tEnable unlisted enum value entry in Smart Console\n\tUpdate BG API: reset_to_dfu command, fix some bit mask values\n\tMesh Provision: Correct Device Key to Bluetooth Address in Discovered Devices table\n\tMesh Provision: Added default ordering by UUID to Discovered Devices table\n4.1.0\n\tAdd new security manager configuration flag\n\tFix CTE Length setting issue\n\tUpdate to latest BGAPI\n\tFix issue with Global Transmit Power Slider\n\tDeleting a Mesh Group now requires confirmation\n\tFixed issue in Device Tab name did not rename when the device was renamed\n\tChanged default Mesh LPN Timeout in Settings\n\tAdded Secure Network Beacon switch to Mesh device setting tab\n\tImplemented setting Unicast Address Range during Mesh Provisioning\n4.0.6\n\tFixed issues with symbolic links on Mac OS\n\tFixed issue with 20dBm for CW mode in regulatory test\n\tFixed issue with Transmit Power unit in RF Regulatory Test\n4.0.5\n\tFix standalone build issue on Mac.\n\tFix restricted enum options.\n\tSecure Network Beacon toggle is added to device tab.\n4.0.3\n\tUpdates for Gecko SDK 3.2.2 release.\n\tImprove usability while connecting to a node.\n\tFixed issue with transmit power slider.\n4.0.2\n\tMinor bug fixes.\n4.0.1\n\tInfrastructure enhancements and bug fixes.\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.bluetooth.tool_ncp_commander.feature.metadata.resource.feature.group/bluetooth_brackets_40x40.png", "installEnabled": true, "id": "com.silabs.ss.bluetooth.tool_ncp_commander.feature.metadata.resource.feature.group", "label": "Bluetooth NCP Commander - 5.1.2", "featurePackageId": "com.silabs.ss.bluetooth.tool_ncp_commander.feature", "desc": "Graphical UI to control an EFR32 running the NCP firmware"}, {"installState": "Update", "releaseNotes": "1.3.1\n\tDPP mode improvements\n1.3.0\n    New security method has been added\n        Dynamic Data Provisioning option for compatible devices\n        Local signing server creation\n    Minor UI fixes\n    Fixed invalid radio channel ranges\n    New fan 1.1 profiles\n1.2.5\n\tMinor bug fixes and improvements.\n1.2.4\n    Updates for SiSDK 2024.6.0\n1.2.3\n    Radio Configurator metadata update\n    Data loading error fix for GSDK patches (4.2.2-4.2.5)\n    Other minor maintenance fixes\n1.2.2\n    Issue with deviceProfile property setting is fixed\n    OFDM PHY-s are hidden for xG28 projects\n    FAN1.0 & FAN1.1 PHY metadata update for GSDK v4.4.0\n1.2.1\n    Mode Switch Checkbox UI Improvements\n    CA Private Key upload fix\n    Manual is updated with v5.7 features\n    Helper texts and links are updated\n1.2.0\n    Backwards compatibility for older SDKs\n    Application Page:\n        Broadcast Retransmissions Input\n        Device Type and Device Profile Selectors\n    Security Page:\n        Generate Device Private Key and Device Certificate\n    Radio Page:\n        Reference PHY table filtering is improved\n        Support for Mode Switch feature\n        Allowed Channels Input\n        Custom Protocols Table is realigned\n        Already selected PHYs are signed\n1.1.4\n    Fix issue with Regulatory Domain filtering\n\tFix issue with Channel Plan ID list decimal value\n\tFix issue with Application Tab MAC Allow/Deny List\n1.1.3\n    Fix issue with default PHY naming\n1.1.2\n    Fix datarate unit label\n1.1.1\n    Added JSON validation for Wi-SUN configuration file\n    Stabilization and bugfixes\n1.1.0\n\tAdded Wi-SUN FAN 1.1 PHY support\n\tDefault PHY selection feature for the application\n\tVisualization of Errors from the Radio Configuration and PHY duplications\n\tDelete All PHYs button and ordering feature for selected PHYs table\n1.0.1\n\tFix Brazilian PHY configurations channel number\n1.0.0\n\tInitial release of the The Wi-SUN Configurator.\n\tIt is an easy-to-use user interface to help you configure your Wi-SUN application. \n\tFor a detailed description see UG495: Silicon Labs Wi-SUN Developer's Guide.", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.proprietary.wisun_configurator.feature.metadata.resource.feature.group/icon.png", "installEnabled": true, "id": "com.silabs.ss.proprietary.wisun_configurator.feature.metadata.resource.feature.group", "label": "Wi-SUN Configurator - 1.3.1", "featurePackageId": "com.silabs.ss.proprietary.wisun_configurator.feature", "desc": "Wi-SUN Configurator"}, {"installState": "Uninstall", "releaseNotes": "1.1.0\n\t* Move GUI from Beta status to General Availability (GA) status.\n\t* Update GUI to be compatible with Matter Provision script version 2.2.\n\t* Add GUI fields for Discriminator, Spake2p Iterations, OTA Key, Params, Generator Firmware, and Production Firmware.\n\t* Add 'Read From Device' function to GUI to read provisioned data from device.\n\t* Write output to Provision Console in real-time as provisioning script executes.\n\t* Add ability to generate test Certificate Declaration and PAA/PAI/DAC test certificate chain files with user's Vendor ID/Product ID.\n\t* Fix minor bugs\n1.0.1\n\tMinor bug fixes and improvements.\n1.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.matter.configurator.feature.metadata.resource.feature.group/matter_icon.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool.matter.configurator.feature.metadata.resource.feature.group", "label": "Matter Provisioning - 1.1.0", "featurePackageId": "com.silabs.ss.tool.matter.configurator.feature", "desc": "Graphical tool used to provision certificates on a device for use in Matter application development"}, {"installState": "Install", "releaseNotes": "1.9.3\n\tUpdate mpc to 1.9.3 to support Eclipse 4.23\n1.8.1\n\tUpdate mpc to 1.8.1 to support Eclipse 4.14\n1.7.7\n\tUpdate mpc to 1.7.7 to support Eclipse 4.12\n1.4.1\n\tAdded signature for authentication", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.epp.mpc.feature.metadata.resource.feature.group/marketplace32.png", "installEnabled": true, "id": "com.silabs.ss.epp.mpc.feature.metadata.resource.feature.group", "label": "EPP Marketplace Client - 1.9.4.v20220121-1317", "featurePackageId": "com.silabs.ss.epp.mpc.feature", "desc": "This version of EPP Marketplace Client supports Eclipse 4.23 or later."}, {"installState": "Uninstall", "releaseNotes": "1.1.4\n   SV5.7 release notes\n1.1.3\n   Build feature with Eclipse 4.23\n1.1.2\n   Code maintenance.\n1.1.1\n   UI bug fixes\n1.1.0\n   Update to support the product launch\n1.0.1\n   Updated Device Configuration UI to include Device Specific Key\n1.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.zwave.support.feature.metadata.resource.feature.group/icon_40x40.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.zwave.support.feature.metadata.resource.feature.group", "label": "Simplicity Studio Z-Wave Support - 1.1.4", "featurePackageId": "com.silabs.ss.zwave.support.feature", "desc": "Provide Z-Wave tool support in Simplicity Studio."}, {"installState": "Uninstall", "releaseNotes": "1.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.pti.wireshark.integration.feature.metadata.resource.feature.group/pti_wireshark_integration.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.pti.wireshark.integration.feature.metadata.resource.feature.group", "label": "Packet Trace using Wireshark Integration - 1.0.0", "featurePackageId": "com.silabs.ss.pti.wireshark.integration.feature", "desc": "Resources to enable Packet Trace using Wireshark"}, {"installState": "Uninstall", "releaseNotes": "5.2.20\n\tSV5.10.0 release changes\n5.2.19\n\tSV5.9.3.1 release\n5.2.18\n\tSV5.9.3.0 release.\n5.2.17\n    Improvements for SV5.9.2.0 release\n5.2.16\n    Improvements for SV5.9.1.1 release\n5.2.15\n\tMinor bug fixes and improvements.\n5.2.14\n\tMinor bug fixes and improvements.\n5.2.13\n\tAdding analytics\n5.2.12\n\tGeneral bug fixes\n5.2.11\n\tNow depends on the GCC 12.2.2023.7 toolchain.\n5.2.10\n\tFix several issues in the project upgrade flow for extensions.\n\tFix issue when component configuration file fails to copy to the config directory dur to text encoding on Windows.\n5.2.9\n\tAdd feature to enable extension project upgrades when an extension is updated without a GSDK update.\n5.2.8\n\tcomponent selector performance improvements\n5.2.7\n\tFix renaming of slpb files when renaming an SLC Project\n\tUpdate the SLC Workspace content when the SLC Project is renamed\n\tUpdate the SLC Workspace content when the Simplicity IDE Solution is renamed\n5.2.6\n    Post-build editor bug fixes\n5.2.5\n    SV5.7 release notes\n5.2.4\n    Add Amazon Sidewalk Assistant quick link metadata.\n5.2.3\n    Minor bug fixes and improvements.\n5.2.2\n\tAdding support for workspaces feature.\n\tAdding support for post-build step.\n\tAdding support for project report generation.\n5.2.1\n    Improvements to Dark Theme UI\n5.2.0\n\tMinor bug fixes and improvements.\n5.0.8\n\tMinor bug fixes and improvements.\n5.0.7\n\tMinor bug fixes and improvements.\n5.0.5\n   Locale-awareness for Turkish language\n5.0.4\n\tCode maintenance.\n5.0.3\n   Improvements\n5.0.2\n   Updated bouncycastle dependency\n5.0.1\n\tFixed bug where spaces in project name results in empty overview page.\n\tFixed bug where installed components sometimes cannot be uninstalled.\n\tImprove Project Overview layout for different screen sizes.\n\tChange filter layout at small screen size.\t\n5.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.uc.feature.metadata.resource.feature.group/slc_tool_install.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool.uc.feature.metadata.resource.feature.group", "label": "SLC Project Configurator - 5.2.20", "featurePackageId": "com.silabs.ss.tool.uc.feature", "desc": "Project Configurator (SLC Tools)"}, {"installState": "Uninstall", "releaseNotes": "4.3.7\n\tSV5.10.0 release changes\n4.3.6\n\tSV5.9.1.1 release\n4.3.5\n\tMinor bug fixes and improvements.\n4.3.4\n\tRefactor the generic toolchain testing into base classes to reduce duplication.\n4.3.2\n\tAdded option for trustzone library creation.\n\t\n4.3.1\n\tFix issue when IAR projects override inputTypes that was causing build failures.\n\tEnsure that the absolute path variable is always injected when checking for the native linux install.\n4.3.0\n\tChanges for SV5.4.0.0 and move to Eclipse 4.23\n4.2.10\n\tCode maintenance. \n4.2.9\n\tFix bug where the default linker file could incorrectly override a requested linker file \n4.2.8\n\tAdd options for stack protection to IAR.\n4.2.7\n\tFix bug where generated makefiles were setup to build source using wine with native Linux IAR\n\tPackage version for v4.rel in SV4.1.13.6\n4.2.6\n\tAdd support for Linux IAR\n\tPackage version for v4.rel in SV4.1.13.1\n4.2.5\n\tClean up of IAR logging\n\tPackage version for v4.rel in SV4.1.13.0\n4.2.4\n\tAddress a \"null pointer exception\" that appeared when user installed and registered IAR version 8.40\n4.2.3\n\tAdd better exception handling in case of loading failures\n4.2.2\n\tUpdate to the package installation compatibility definition\n4.2.1\n\tInternal build infrastructure change\n4.2.0\n\tFix issue with building for IAR 8.x\n4.1.1\n\tCorrection to packaging for new install and update\n4.1.0\n\tAdding support for IAR 8.x\n4.0.14\n\tFix issue with handling escape characters for checksum configuration. The issue is raised in https://www.silabs.com/community/software/simplicity-studio/knowledge-base.entry.html/2016/12/12/enabling_checksumus-6elz\n4.0.13\n\tInfrastructure improvements for extensibility\n4.0.12\n\tImprove part-compatibility infrastructure for installation\n4.0.11\n\tShow correct IAR version when building with a different version\n4.0.10\n\tEnable clearing of IAR preferences when IAR is removed\n\tCreate default linker script\n\tEnable auto-generation of IAR linker script\n4.0.9\n\tPackage signing\n4.0.8\n\tMore efficient error logging\n4.0.7\n\tFix script for path manipulation of dependency file content on Mac and Linux\n4.0.6\n\tMinor bug fixes\n4.0.5\n\tMinor bug fixes\n4.0.4\n\tFix bug with IAR toolchain path and improve error reporting\n4.0.3\n\tMinor bug fix\n4.0.2\n\tMinor bug fixes\n4.0.1\n\tFix issues with script validity check and execute flag\n4.0.0\n\tInitial version", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.toolchain.iar.arm.support.feature.metadata.resource.feature.group/toolchain_40x40.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.toolchain.iar.arm.support.feature.metadata.resource.feature.group", "label": "IAR ARM Toolchain Integration - 4.3.7", "featurePackageId": "com.silabs.ss.toolchain.iar.arm.support.feature", "desc": "This package allows you to use the IAR ARM toolchain from the Simplicity Studio IDE"}, {"installState": "Uninstall", "releaseNotes": "2.0.16\r\n    Signing executables\r\n2.0.15\r\n\tBuild feature with Eclipse 4.23\r\n2.0.14\r\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.tool.app.canopen.feature.metadata.resource.feature.group/canopen.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.tool.app.canopen.feature.metadata.resource.feature.group", "label": "CANopen Node Configurator - 2.0.16", "featurePackageId": "com.silabs.tool.app.canopen.feature", "desc": "CANopen Stack Configuration Tool"}, {"installState": "Uninstall", "releaseNotes": "4.5.6\n\tAddress issue when installing by connected Si91x device\n4.5.5\n  SV5.9 include wifi feature dependencies\n4.5.4\n\tSV5.9 release notes\n4.5.3\n\tSV5.7 release notes\n4.5.2\n\tEnable support for si917.\n4.5.1\n\tMinor code improvements.\n4.5.0\n\tAdd beta support for min/max current plots\n4.4.0\n\tAdd new Wireless Pro Kit support.\n\tAdd support for Logic Signal processing on Wireless Pro Kits.\n\tVisualize optionally up to eight Logic Signals per board. (Four Internal,  Four external)\n\tAdded new trigger options for the Logical signals, (High, Low, Rising, Falling)\n\tCustom positioning of Logic charts.\n4.3.7\n\tHandle segments with out of sequence start/end times \n4.3.6\n\tUpdates in support of BRD4002A\n4.3.5\n    Help pages moved to docs.silabs.com\n4.3.4\n    Updates for profiling device that has no part defined, automatically use target part from project\n4.3.3\n\tGeneral bug fixes\n4.3.2\n\tImprove error message for unsupported devices\n4.3.1\n\tGeneral bug fixes \n4.3.0\n\tWhen profiling device that has no part defined, automatically use target part from project \n4.2.7\n\tUpdates to infrastructure APIs\n4.2.6\n\tUpdates to infrastructure APIs\n4.2.5\n\tVarious fixes in the core components\n4.2.4\n\tUpdate tool compatibility - exclude Wi-Fi parts\n4.2.3\n\tGeneral bug fixes\n4.2.2\n\tFix issue where launch configuration was not correctly using the complete device context\n4.2.1\n\tFollowing API changes in infrastructure components\n4.2.0\n\tGeneral bug fixes\n4.0.13\n\tCorrection to spelling errors in documentation\n4.0.12\n\tCorrection to spelling errors in documentation\n4.0.11\n\tFix issue with multinode search functionality\n4.0.10\n\tProperly cleanup profiler launch contexts\n4.0.9\n\tFix issue with debug context after energy profiler executes\n4.0.8\n\tEnable Multi-node energy profiling\n4.0.7\n\tInternal version increment\n4.0.6\n\tAdded signature for authentication\n4.0.5\n\tEnable logging\n4.0.3\n\tImproved device arbitration between tool\n4.0.1\n\tensure code correlation support is correctly re-evaluated on each run\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.energyprofiler.si32.feature.metadata.resource.feature.group/icon_display_EnergyProfiler.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool.energyprofiler.si32.feature.metadata.resource.feature.group", "label": "Simplicity Energy Profiler for Exx32 - 4.5.6", "featurePackageId": "com.silabs.ss.tool.energyprofiler.si32.feature", "desc": "Simplicity Studio Energy Profiler for Exx32"}, {"installState": "Uninstall", "releaseNotes": "4.2.4\n\tMinor bug fixes and improvements.\n4.2.3\n\tNo changes\n4.2.2\n\tNo changes\n4.2.1\n\tFix scrolling for selected item when drag-n-drop reordering\n4.2.0\n\tCorrect Characteristic Formats\n\tCorrect Characteristic format Casing\n4.1.1\n\tFix Max Lenght validation\n4.1.0\n\tAdd Compliance with HomeKit Accessory Protocol Specification Release R17, ADK 6.1\n\tInput validation over UUID, InstanceID, Constraints, Format, FW/HW version, Length values\n\tSanitize linked data after deletion \n\tImprove config file export\n4.0.4\n\tFixed issue when an extra, harmless field was created in configuration\n4.0.3\n\tImprove configuration file export\n4.0.2\n   Update for studio V5.3.0\n4.0.1\n\tMinor update to internal infrastructure component\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool_homekit_configurator.feature.metadata.resource.feature.group/ha_40.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool_homekit_configurator.feature.metadata.resource.feature.group", "label": "HomeKit Configurator - 4.2.4", "featurePackageId": "com.silabs.ss.tool_homekit_configurator.feature", "desc": "HomeKit Configurator"}, {"installState": "Uninstall", "releaseNotes": "4.3.12\n\tSV5.10.0 release changes\n4.3.11\n\tMinor bug fixes and improvements.\n4.3.10\n\tMinor bug fixes and improvements.\n4.3.9\n\tMinor bug fixes and improvements.\n4.3.8\n\tGDB Bugfix - incorrect RTOS plugin auto-selected during launch of debug session\n4.3.7\n\tAdd ability to override the dwarf output version in Studio IDE.\n4.3.6\n\tFix issue where GDB does not stop at main() for Si*917 devices on startup\n4.3.5\n\tChange default gcc debug level to improve gdb performance\n4.3.4\n\tFix issue where GDB does not stop at main() for Si*917 devices on startup\n4.3.3\n\tSV5.7 release notes\n4.3.2\n\tFix bug with trustzone library argument\n4.3.1\n\tAdd toolchain option for trustzone library creation.\n\tFix bug where GDB was not flashing device connected over IP.\n4.3.0\n\tUpdates for Eclipse 4.23.\n4.2.14\n\tUpdate Studio Debugger and GNU Debugger (GDB) to support Multi-Project Solutions\n\tGCC project dependency improvements\n\tGeneral bug fixes\n4.2.13\n\tFix issue with re-building projects with GCC 10.2 on Windows\n4.2.12\n\tFix null pointer error when selecting GNU GCC properties on a project.\n4.2.11\n\tFix locale issue with Turkish language OS\n\tFix \"Build Selected Files\" command\n4.2.10\n\tAdd support for GNU ARM 10.2_2020Q4 support\n\tCode maintenance.\n\tFix bug with GDB to support devices connected via IP address\n4.2.9\n\tUpdates to support GDB bug fixes and improvements\n4.2.8\n\tIntegrate GDB debugging support\n4.2.7\n\tAdded -A argument to size tool (arm-none-eabi-size) to change output format\n\tMake debug level None the default for ARM Release projects\n4.2.6\n\tUpdates to infrastructure APIs\n4.2.5\n\tInfrastructure updates for future extensions\n4.2.4\n\tInfrastructure updates for future extensions\n4.2.3\n\tUpdate tool compatibility - exclude Wi-Fi parts\n4.2.2\n\tFix issue where launch configuration was not correctly using the complete device context\n\tFix optimization settings when creating a Debug Configuration for GCC\n4.2.1\n\tFix some thread-safety issues\n\tFix issue where you could not select \"Print float\" and \"Scan float\" options for C++ GNU Linker configuration\n4.2.0\n\tGeneral improvements and bug fixes\n4.0.12\n\tInfrastructure and extensibility additions\n4.0.11\n\tFix issue where linker C Library option changes unintentionally when setting \"All Configurations\"\n4.0.10\n\tInfrastructure updates for extensibility\n4.0.9\n\tUpdate required GNU ARM Toolchain to version 7.2_2017q4 (7.2.1)\n4.0.8\n\tAdd startup debug script support\n4.0.7\n\tAdd assembler options for FPU settings\n\tAdd upgrade logic for assembler FPU settings\n\tAdd support for bootloaders during launch sequence\n\tGCC linker script generation improvements\n4.0.6\n\tSi32 Launch Sequence will add steps from external clients\n4.0.5\n\tAdd default \"Run As\" Launch Configuration\n4.0.4\n\tGeneral bug fixes\n4.0.3\n\tThe GCC compiler will now automatically create s37 files\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.ide.arm.feature.metadata.resource.feature.group/icon_display_SimplicityIDE.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool.ide.arm.feature.metadata.resource.feature.group", "label": "ARM IDE - 4.3.12", "featurePackageId": "com.silabs.ss.tool.ide.arm.feature", "desc": "Allows you to create, edit, build and debug ARM applications"}, {"installState": "Uninstall", "releaseNotes": "1.0.6\n\tAddress issue when installing by connected Si91x device\n1.0.5\n\tMinor bug fixes\n1.0.4\n\tMinor bug fixes and improvements.\n1.0.3\n\tFix various bugs in Simplicity Studio presentation.\n1.0.2\n\tUpdate of VSCode id for SV\n1.0.1\n\tAdd support for VSCode in the New Project Wizard\n1.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.ide.external.vscode.feature.metadata.resource.feature.group/vscode_icon.png", "isInstalled": true, "installEnabled": true, "id": "com.silabs.ss.tool.ide.external.vscode.feature.metadata.resource.feature.group", "label": "Visual Studio Code Integration - 1.0.6", "featurePackageId": "com.silabs.ss.tool.ide.external.vscode.feature", "desc": "Support for Visual Studio Code IDE in Simplicity Studio"}, {"installState": "Install", "releaseNotes": "4.2.3\n    Sign windows exes\n4.2.2\n\tBuild feature with Eclipse 4.23\n4.2.1\n\tMinor feature update relating to diplay of icons\n4.2.0\n\tGeneral bug fixes\n4.0.5\n\tKnown issue: 'F98x and 'F99x: Watchdog Timer is incorrectly disabled after the following sequence:\n\t\t* Open the PCA peripheral window,\n\t\t* Select Timer 0 overflow as the clock source,\n\t\t* Select the Module 2 tab.\n\t\tThe Watchdog Timer will have to be manually re-enabled if this is the desired outcome.\n4.0.4\n\tImprove part-compatibility infrastructure for installation\n4.0.3\n\tAdded signature for authentication\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.tool.config2.feature.metadata.resource.feature.group/legacy_configuration_40x40.png", "installEnabled": true, "id": "com.silabs.ss.tool.config2.feature.metadata.resource.feature.group", "label": "Simplicity Config 2 - 4.2.3", "featurePackageId": "com.silabs.ss.tool.config2.feature", "desc": "Simplicity Studio Config 2, a legacy configurator."}]}]}