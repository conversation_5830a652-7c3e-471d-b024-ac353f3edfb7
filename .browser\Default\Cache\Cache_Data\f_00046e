(globalThis["webpackChunknew_project_wizard"]=globalThis["webpackChunknew_project_wizard"]||[]).push([[736],{9984:e=>{e.exports=function(e,t,n){const o=void 0!==e.__vccOpts?e.__vccOpts:e,r=o[t];if(void 0===r)o[t]=n;else for(const l in n)void 0===r[l]&&(r[l]=n[l])}},499:(e,t,n)=>{"use strict";n.d(t,{B:()=>i,Bj:()=>l,Fl:()=>We,IU:()=>Ae,Jd:()=>E,PG:()=>Te,SU:()=>Be,Um:()=>xe,WL:()=>ze,X$:()=>F,X3:()=>Le,XI:()=>De,Xl:()=>Pe,dq:()=>Ie,iH:()=>$e,j:()=>T,lk:()=>C,nZ:()=>s,qj:()=>ke,qq:()=>w,yT:()=>Fe});var o=n(6970);let r;class l{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=r,!e&&r&&(this.index=(r.scopes||(r.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=r;try{return r=this,e()}finally{r=t}}else 0}on(){r=this}off(){r=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function i(e){return new l(e)}function a(e,t=r){t&&t.active&&t.effects.push(e)}function s(){return r}const u=e=>{const t=new Set(e);return t.w=0,t.n=0,t},c=e=>(e.w&m)>0,d=e=>(e.n&m)>0,f=({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=m},p=e=>{const{deps:t}=e;if(t.length){let n=0;for(let o=0;o<t.length;o++){const r=t[o];c(r)&&!d(r)?r.delete(e):t[n++]=r,r.w&=~m,r.n&=~m}t.length=n}},v=new WeakMap;let h=0,m=1;const g=30;let b;const y=Symbol(""),_=Symbol("");class w{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,a(this,n)}run(){if(!this.active)return this.fn();let e=b,t=k;while(e){if(e===this)return;e=e.parent}try{return this.parent=b,b=this,k=!0,m=1<<++h,h<=g?f(this):S(this),this.fn()}finally{h<=g&&p(this),m=1<<--h,b=this.parent,k=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){b===this?this.deferStop=!0:this.active&&(S(this),this.onStop&&this.onStop(),this.active=!1)}}function S(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let k=!0;const x=[];function E(){x.push(k),k=!1}function C(){const e=x.pop();k=void 0===e||e}function T(e,t,n){if(k&&b){let t=v.get(e);t||v.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=u());const r=void 0;O(o,r)}}function O(e,t){let n=!1;h<=g?d(e)||(e.n|=m,n=!c(e)):n=!e.has(b),n&&(e.add(b),b.deps.push(e))}function F(e,t,n,r,l,i){const a=v.get(e);if(!a)return;let s=[];if("clear"===t)s=[...a.values()];else if("length"===n&&(0,o.kJ)(e)){const e=Number(r);a.forEach(((t,n)=>{("length"===n||n>=e)&&s.push(t)}))}else switch(void 0!==n&&s.push(a.get(n)),t){case"add":(0,o.kJ)(e)?(0,o.S0)(n)&&s.push(a.get("length")):(s.push(a.get(y)),(0,o._N)(e)&&s.push(a.get(_)));break;case"delete":(0,o.kJ)(e)||(s.push(a.get(y)),(0,o._N)(e)&&s.push(a.get(_)));break;case"set":(0,o._N)(e)&&s.push(a.get(y));break}if(1===s.length)s[0]&&L(s[0]);else{const e=[];for(const t of s)t&&e.push(...t);L(u(e))}}function L(e,t){const n=(0,o.kJ)(e)?e:[...e];for(const o of n)o.computed&&A(o,t);for(const o of n)o.computed||A(o,t)}function A(e,t){(e!==b||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const P=(0,o.fY)("__proto__,__v_isRef,__isVue"),q=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(o.yk)),R=V(),N=V(!1,!0),M=V(!0),I=$();function $(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Ae(this);for(let t=0,r=this.length;t<r;t++)T(n,"get",t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Ae)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){E();const n=Ae(this)[t].apply(this,e);return C(),n}})),e}function D(e){const t=Ae(this);return T(t,"has",e),t.hasOwnProperty(e)}function V(e=!1,t=!1){return function(n,r,l){if("__v_isReactive"===r)return!e;if("__v_isReadonly"===r)return e;if("__v_isShallow"===r)return t;if("__v_raw"===r&&l===(e?t?_e:ye:t?be:ge).get(n))return n;const i=(0,o.kJ)(n);if(!e){if(i&&(0,o.RI)(I,r))return Reflect.get(I,r,l);if("hasOwnProperty"===r)return D}const a=Reflect.get(n,r,l);return((0,o.yk)(r)?q.has(r):P(r))?a:(e||T(n,"get",r),t?a:Ie(a)?i&&(0,o.S0)(r)?a:a.value:(0,o.Kn)(a)?e?Ee(a):ke(a):a)}}const j=H(),B=H(!0);function H(e=!1){return function(t,n,r,l){let i=t[n];if(Oe(i)&&Ie(i)&&!Ie(r))return!1;if(!e&&(Fe(r)||Oe(r)||(i=Ae(i),r=Ae(r)),!(0,o.kJ)(t)&&Ie(i)&&!Ie(r)))return i.value=r,!0;const a=(0,o.kJ)(t)&&(0,o.S0)(n)?Number(n)<t.length:(0,o.RI)(t,n),s=Reflect.set(t,n,r,l);return t===Ae(l)&&(a?(0,o.aU)(r,i)&&F(t,"set",n,r,i):F(t,"add",n,r)),s}}function z(e,t){const n=(0,o.RI)(e,t),r=e[t],l=Reflect.deleteProperty(e,t);return l&&n&&F(e,"delete",t,void 0,r),l}function U(e,t){const n=Reflect.has(e,t);return(0,o.yk)(t)&&q.has(t)||T(e,"has",t),n}function W(e){return T(e,"iterate",(0,o.kJ)(e)?"length":y),Reflect.ownKeys(e)}const Z={get:R,set:j,deleteProperty:z,has:U,ownKeys:W},K={get:M,set(e,t){return!0},deleteProperty(e,t){return!0}},Y=(0,o.l7)({},Z,{get:N,set:B}),J=e=>e,X=e=>Reflect.getPrototypeOf(e);function G(e,t,n=!1,o=!1){e=e["__v_raw"];const r=Ae(e),l=Ae(t);n||(t!==l&&T(r,"get",t),T(r,"get",l));const{has:i}=X(r),a=o?J:n?Re:qe;return i.call(r,t)?a(e.get(t)):i.call(r,l)?a(e.get(l)):void(e!==r&&e.get(t))}function Q(e,t=!1){const n=this["__v_raw"],o=Ae(n),r=Ae(e);return t||(e!==r&&T(o,"has",e),T(o,"has",r)),e===r?n.has(e):n.has(e)||n.has(r)}function ee(e,t=!1){return e=e["__v_raw"],!t&&T(Ae(e),"iterate",y),Reflect.get(e,"size",e)}function te(e){e=Ae(e);const t=Ae(this),n=X(t),o=n.has.call(t,e);return o||(t.add(e),F(t,"add",e,e)),this}function ne(e,t){t=Ae(t);const n=Ae(this),{has:r,get:l}=X(n);let i=r.call(n,e);i||(e=Ae(e),i=r.call(n,e));const a=l.call(n,e);return n.set(e,t),i?(0,o.aU)(t,a)&&F(n,"set",e,t,a):F(n,"add",e,t),this}function oe(e){const t=Ae(this),{has:n,get:o}=X(t);let r=n.call(t,e);r||(e=Ae(e),r=n.call(t,e));const l=o?o.call(t,e):void 0,i=t.delete(e);return r&&F(t,"delete",e,void 0,l),i}function re(){const e=Ae(this),t=0!==e.size,n=void 0,o=e.clear();return t&&F(e,"clear",void 0,void 0,n),o}function le(e,t){return function(n,o){const r=this,l=r["__v_raw"],i=Ae(l),a=t?J:e?Re:qe;return!e&&T(i,"iterate",y),l.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function ie(e,t,n){return function(...r){const l=this["__v_raw"],i=Ae(l),a=(0,o._N)(i),s="entries"===e||e===Symbol.iterator&&a,u="keys"===e&&a,c=l[e](...r),d=n?J:t?Re:qe;return!t&&T(i,"iterate",u?_:y),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:s?[d(e[0]),d(e[1])]:d(e),done:t}},[Symbol.iterator](){return this}}}}function ae(e){return function(...t){return"delete"!==e&&this}}function se(){const e={get(e){return G(this,e)},get size(){return ee(this)},has:Q,add:te,set:ne,delete:oe,clear:re,forEach:le(!1,!1)},t={get(e){return G(this,e,!1,!0)},get size(){return ee(this)},has:Q,add:te,set:ne,delete:oe,clear:re,forEach:le(!1,!0)},n={get(e){return G(this,e,!0)},get size(){return ee(this,!0)},has(e){return Q.call(this,e,!0)},add:ae("add"),set:ae("set"),delete:ae("delete"),clear:ae("clear"),forEach:le(!0,!1)},o={get(e){return G(this,e,!0,!0)},get size(){return ee(this,!0)},has(e){return Q.call(this,e,!0)},add:ae("add"),set:ae("set"),delete:ae("delete"),clear:ae("clear"),forEach:le(!0,!0)},r=["keys","values","entries",Symbol.iterator];return r.forEach((r=>{e[r]=ie(r,!1,!1),n[r]=ie(r,!0,!1),t[r]=ie(r,!1,!0),o[r]=ie(r,!0,!0)})),[e,n,t,o]}const[ue,ce,de,fe]=se();function pe(e,t){const n=t?e?fe:de:e?ce:ue;return(t,r,l)=>"__v_isReactive"===r?!e:"__v_isReadonly"===r?e:"__v_raw"===r?t:Reflect.get((0,o.RI)(n,r)&&r in t?n:t,r,l)}const ve={get:pe(!1,!1)},he={get:pe(!1,!0)},me={get:pe(!0,!1)};const ge=new WeakMap,be=new WeakMap,ye=new WeakMap,_e=new WeakMap;function we(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Se(e){return e["__v_skip"]||!Object.isExtensible(e)?0:we((0,o.W7)(e))}function ke(e){return Oe(e)?e:Ce(e,!1,Z,ve,ge)}function xe(e){return Ce(e,!1,Y,he,be)}function Ee(e){return Ce(e,!0,K,me,ye)}function Ce(e,t,n,r,l){if(!(0,o.Kn)(e))return e;if(e["__v_raw"]&&(!t||!e["__v_isReactive"]))return e;const i=l.get(e);if(i)return i;const a=Se(e);if(0===a)return e;const s=new Proxy(e,2===a?r:n);return l.set(e,s),s}function Te(e){return Oe(e)?Te(e["__v_raw"]):!(!e||!e["__v_isReactive"])}function Oe(e){return!(!e||!e["__v_isReadonly"])}function Fe(e){return!(!e||!e["__v_isShallow"])}function Le(e){return Te(e)||Oe(e)}function Ae(e){const t=e&&e["__v_raw"];return t?Ae(t):e}function Pe(e){return(0,o.Nj)(e,"__v_skip",!0),e}const qe=e=>(0,o.Kn)(e)?ke(e):e,Re=e=>(0,o.Kn)(e)?Ee(e):e;function Ne(e){k&&b&&(e=Ae(e),O(e.dep||(e.dep=u())))}function Me(e,t){e=Ae(e);const n=e.dep;n&&L(n)}function Ie(e){return!(!e||!0!==e.__v_isRef)}function $e(e){return Ve(e,!1)}function De(e){return Ve(e,!0)}function Ve(e,t){return Ie(e)?e:new je(e,t)}class je{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Ae(e),this._value=t?e:qe(e)}get value(){return Ne(this),this._value}set value(e){const t=this.__v_isShallow||Fe(e)||Oe(e);e=t?e:Ae(e),(0,o.aU)(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:qe(e),Me(this,e))}}function Be(e){return Ie(e)?e.value:e}const He={get:(e,t,n)=>Be(Reflect.get(e,t,n)),set:(e,t,n,o)=>{const r=e[t];return Ie(r)&&!Ie(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function ze(e){return Te(e)?e:new Proxy(e,He)}class Ue{constructor(e,t,n,o){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this["__v_isReadonly"]=!1,this._dirty=!0,this.effect=new w(e,(()=>{this._dirty||(this._dirty=!0,Me(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!o,this["__v_isReadonly"]=n}get value(){const e=Ae(this);return Ne(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function We(e,t,n=!1){let r,l;const i=(0,o.mf)(e);i?(r=e,l=o.dG):(r=e.get,l=e.set);const a=new Ue(r,l,i||!l,n);return a}},9835:(e,t,n)=>{"use strict";n.d(t,{$d:()=>i,Ah:()=>Le,Cn:()=>M,F4:()=>bn,FN:()=>An,Fl:()=>Jn,HY:()=>Jt,JJ:()=>ht,Jd:()=>Fe,Ko:()=>Be,Ob:()=>he,P$:()=>re,Q2:()=>De,Q6:()=>ce,U2:()=>ie,Uk:()=>_n,Us:()=>Mt,WI:()=>He,Wm:()=>mn,Xn:()=>Te,Y3:()=>b,Y8:()=>ee,YP:()=>Z,_:()=>hn,aZ:()=>de,bv:()=>Ce,dD:()=>N,dl:()=>ge,f3:()=>mt,h:()=>Xn,iD:()=>sn,ic:()=>Oe,j4:()=>un,kq:()=>wn,lR:()=>Kt,nJ:()=>ne,nK:()=>ue,se:()=>be,up:()=>Ie,w5:()=>I,wF:()=>Ee,wg:()=>nn,wy:()=>G,xv:()=>Xt});var o=n(499),r=n(6970);function l(e,t,n,o){let r;try{r=o?e(...o):e()}catch(l){a(l,t,n)}return r}function i(e,t,n,o){if((0,r.mf)(e)){const i=l(e,t,n,o);return i&&(0,r.tI)(i)&&i.catch((e=>{a(e,t,n)})),i}const s=[];for(let r=0;r<e.length;r++)s.push(i(e[r],t,n,o));return s}function a(e,t,n,o=!0){const r=t?t.vnode:null;if(t){let o=t.parent;const r=t.proxy,i=n;while(o){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const a=t.appContext.config.errorHandler;if(a)return void l(a,null,10,[e,r,i])}s(e,n,r,o)}function s(e,t,n,o=!0){console.error(e)}let u=!1,c=!1;const d=[];let f=0;const p=[];let v=null,h=0;const m=Promise.resolve();let g=null;function b(e){const t=g||m;return e?t.then(this?e.bind(this):e):t}function y(e){let t=f+1,n=d.length;while(t<n){const o=t+n>>>1,r=C(d[o]);r<e?t=o+1:n=o}return t}function _(e){d.length&&d.includes(e,u&&e.allowRecurse?f+1:f)||(null==e.id?d.push(e):d.splice(y(e.id),0,e),w())}function w(){u||c||(c=!0,g=m.then(O))}function S(e){const t=d.indexOf(e);t>f&&d.splice(t,1)}function k(e){(0,r.kJ)(e)?p.push(...e):v&&v.includes(e,e.allowRecurse?h+1:h)||p.push(e),w()}function x(e,t=(u?f+1:0)){for(0;t<d.length;t++){const e=d[t];e&&e.pre&&(d.splice(t,1),t--,e())}}function E(e){if(p.length){const e=[...new Set(p)];if(p.length=0,v)return void v.push(...e);for(v=e,v.sort(((e,t)=>C(e)-C(t))),h=0;h<v.length;h++)v[h]();v=null,h=0}}const C=e=>null==e.id?1/0:e.id,T=(e,t)=>{const n=C(e)-C(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function O(e){c=!1,u=!0,d.sort(T);r.dG;try{for(f=0;f<d.length;f++){const e=d[f];e&&!1!==e.active&&l(e,null,14)}}finally{f=0,d.length=0,E(e),u=!1,g=null,(d.length||p.length)&&O(e)}}function F(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||r.kT;let l=n;const a=t.startsWith("update:"),s=a&&t.slice(7);if(s&&s in o){const e=`${"modelValue"===s?"model":s}Modifiers`,{number:t,trim:i}=o[e]||r.kT;i&&(l=n.map((e=>(0,r.HD)(e)?e.trim():e))),t&&(l=n.map(r.h5))}let u;let c=o[u=(0,r.hR)(t)]||o[u=(0,r.hR)((0,r._A)(t))];!c&&a&&(c=o[u=(0,r.hR)((0,r.rs)(t))]),c&&i(c,e,6,l);const d=o[u+"Once"];if(d){if(e.emitted){if(e.emitted[u])return}else e.emitted={};e.emitted[u]=!0,i(d,e,6,l)}}function L(e,t,n=!1){const o=t.emitsCache,l=o.get(e);if(void 0!==l)return l;const i=e.emits;let a={},s=!1;if(!(0,r.mf)(e)){const o=e=>{const n=L(e,t,!0);n&&(s=!0,(0,r.l7)(a,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||s?((0,r.kJ)(i)?i.forEach((e=>a[e]=null)):(0,r.l7)(a,i),(0,r.Kn)(e)&&o.set(e,a),a):((0,r.Kn)(e)&&o.set(e,null),null)}function A(e,t){return!(!e||!(0,r.F7)(t))&&(t=t.slice(2).replace(/Once$/,""),(0,r.RI)(e,t[0].toLowerCase()+t.slice(1))||(0,r.RI)(e,(0,r.rs)(t))||(0,r.RI)(e,t))}let P=null,q=null;function R(e){const t=P;return P=e,q=e&&e.type.__scopeId||null,t}function N(e){q=e}function M(){q=null}function I(e,t=P,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&ln(-1);const r=R(t);let l;try{l=e(...n)}finally{R(r),o._d&&ln(1)}return l};return o._n=!0,o._c=!0,o._d=!0,o}function $(e){const{type:t,vnode:n,proxy:o,withProxy:l,props:i,propsOptions:[s],slots:u,attrs:c,emit:d,render:f,renderCache:p,data:v,setupState:h,ctx:m,inheritAttrs:g}=e;let b,y;const _=R(e);try{if(4&n.shapeFlag){const e=l||o;b=Sn(f.call(e,e,p,i,h,v,m)),y=c}else{const e=t;0,b=Sn(e.length>1?e(i,{attrs:c,slots:u,emit:d}):e(i,null)),y=t.props?c:D(c)}}catch(S){en.length=0,a(S,e,1),b=mn(Gt)}let w=b;if(y&&!1!==g){const e=Object.keys(y),{shapeFlag:t}=w;e.length&&7&t&&(s&&e.some(r.tR)&&(y=V(y,s)),w=yn(w,y))}return n.dirs&&(w=yn(w),w.dirs=w.dirs?w.dirs.concat(n.dirs):n.dirs),n.transition&&(w.transition=n.transition),b=w,R(_),b}const D=e=>{let t;for(const n in e)("class"===n||"style"===n||(0,r.F7)(n))&&((t||(t={}))[n]=e[n]);return t},V=(e,t)=>{const n={};for(const o in e)(0,r.tR)(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function j(e,t,n){const{props:o,children:r,component:l}=e,{props:i,children:a,patchFlag:s}=t,u=l.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&s>=0))return!(!r&&!a||a&&a.$stable)||o!==i&&(o?!i||B(o,i,u):!!i);if(1024&s)return!0;if(16&s)return o?B(o,i,u):!!i;if(8&s){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(i[n]!==o[n]&&!A(u,n))return!0}}return!1}function B(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const l=o[r];if(t[l]!==e[l]&&!A(n,l))return!0}return!1}function H({vnode:e,parent:t},n){while(t&&t.subTree===e)(e=t.vnode).el=n,t=t.parent}const z=e=>e.__isSuspense;function U(e,t){t&&t.pendingBranch?(0,r.kJ)(e)?t.effects.push(...e):t.effects.push(e):k(e)}const W={};function Z(e,t,n){return K(e,t,n)}function K(e,t,{immediate:n,deep:a,flush:s,onTrack:u,onTrigger:c}=r.kT){var d;const f=(0,o.nZ)()===(null==(d=Ln)?void 0:d.scope)?Ln:null;let p,v,h=!1,m=!1;if((0,o.dq)(e)?(p=()=>e.value,h=(0,o.yT)(e)):(0,o.PG)(e)?(p=()=>e,a=!0):(0,r.kJ)(e)?(m=!0,h=e.some((e=>(0,o.PG)(e)||(0,o.yT)(e))),p=()=>e.map((e=>(0,o.dq)(e)?e.value:(0,o.PG)(e)?X(e):(0,r.mf)(e)?l(e,f,2):void 0))):p=(0,r.mf)(e)?t?()=>l(e,f,2):()=>{if(!f||!f.isUnmounted)return v&&v(),i(e,f,3,[b])}:r.dG,t&&a){const e=p;p=()=>X(e())}let g,b=e=>{v=k.onStop=()=>{l(e,f,4)}};if(Vn){if(b=r.dG,t?n&&i(t,f,3,[p(),m?[]:void 0,b]):p(),"sync"!==s)return r.dG;{const e=Qn();g=e.__watcherHandles||(e.__watcherHandles=[])}}let y=m?new Array(e.length).fill(W):W;const w=()=>{if(k.active)if(t){const e=k.run();(a||h||(m?e.some(((e,t)=>(0,r.aU)(e,y[t]))):(0,r.aU)(e,y)))&&(v&&v(),i(t,f,3,[e,y===W?void 0:m&&y[0]===W?[]:y,b]),y=e)}else k.run()};let S;w.allowRecurse=!!t,"sync"===s?S=w:"post"===s?S=()=>Nt(w,f&&f.suspense):(w.pre=!0,f&&(w.id=f.uid),S=()=>_(w));const k=new o.qq(p,S);t?n?w():y=k.run():"post"===s?Nt(k.run.bind(k),f&&f.suspense):k.run();const x=()=>{k.stop(),f&&f.scope&&(0,r.Od)(f.scope.effects,k)};return g&&g.push(x),x}function Y(e,t,n){const o=this.proxy,l=(0,r.HD)(e)?e.includes(".")?J(o,e):()=>o[e]:e.bind(o,o);let i;(0,r.mf)(t)?i=t:(i=t.handler,n=t);const a=Ln;Nn(this);const s=K(l,i.bind(o),n);return a?Nn(a):Mn(),s}function J(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function X(e,t){if(!(0,r.Kn)(e)||e["__v_skip"])return e;if(t=t||new Set,t.has(e))return e;if(t.add(e),(0,o.dq)(e))X(e.value,t);else if((0,r.kJ)(e))for(let n=0;n<e.length;n++)X(e[n],t);else if((0,r.DM)(e)||(0,r._N)(e))e.forEach((e=>{X(e,t)}));else if((0,r.PO)(e))for(const n in e)X(e[n],t);return e}function G(e,t){const n=P;if(null===n)return e;const o=Zn(n)||n.proxy,l=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,n,a,s=r.kT]=t[i];e&&((0,r.mf)(e)&&(e={mounted:e,updated:e}),e.deep&&X(n),l.push({dir:e,instance:o,value:n,oldValue:void 0,arg:a,modifiers:s}))}return e}function Q(e,t,n,r){const l=e.dirs,a=t&&t.dirs;for(let s=0;s<l.length;s++){const u=l[s];a&&(u.oldValue=a[s].value);let c=u.dir[r];c&&((0,o.Jd)(),i(c,n,8,[e.el,u,e,t]),(0,o.lk)())}}function ee(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Ce((()=>{e.isMounted=!0})),Fe((()=>{e.isUnmounting=!0})),e}const te=[Function,Array],ne={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:te,onEnter:te,onAfterEnter:te,onEnterCancelled:te,onBeforeLeave:te,onLeave:te,onAfterLeave:te,onLeaveCancelled:te,onBeforeAppear:te,onAppear:te,onAfterAppear:te,onAppearCancelled:te},oe={name:"BaseTransition",props:ne,setup(e,{slots:t}){const n=An(),r=ee();let l;return()=>{const i=t.default&&ce(t.default(),!0);if(!i||!i.length)return;let a=i[0];if(i.length>1){let e=!1;for(const t of i)if(t.type!==Gt){0,a=t,e=!0;break}}const s=(0,o.IU)(e),{mode:u}=s;if(r.isLeaving)return ae(a);const c=se(a);if(!c)return ae(a);const d=ie(c,s,r,n);ue(c,d);const f=n.subTree,p=f&&se(f);let v=!1;const{getTransitionKey:h}=c.type;if(h){const e=h();void 0===l?l=e:e!==l&&(l=e,v=!0)}if(p&&p.type!==Gt&&(!dn(c,p)||v)){const e=ie(p,s,r,n);if(ue(p,e),"out-in"===u)return r.isLeaving=!0,e.afterLeave=()=>{r.isLeaving=!1,!1!==n.update.active&&n.update()},ae(a);"in-out"===u&&c.type!==Gt&&(e.delayLeave=(e,t,n)=>{const o=le(r,p);o[String(p.key)]=p,e._leaveCb=()=>{t(),e._leaveCb=void 0,delete d.delayedLeave},d.delayedLeave=n})}return a}}},re=oe;function le(e,t){const{leavingVNodes:n}=e;let o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function ie(e,t,n,o){const{appear:l,mode:a,persisted:s=!1,onBeforeEnter:u,onEnter:c,onAfterEnter:d,onEnterCancelled:f,onBeforeLeave:p,onLeave:v,onAfterLeave:h,onLeaveCancelled:m,onBeforeAppear:g,onAppear:b,onAfterAppear:y,onAppearCancelled:_}=t,w=String(e.key),S=le(n,e),k=(e,t)=>{e&&i(e,o,9,t)},x=(e,t)=>{const n=t[1];k(e,t),(0,r.kJ)(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},E={mode:a,persisted:s,beforeEnter(t){let o=u;if(!n.isMounted){if(!l)return;o=g||u}t._leaveCb&&t._leaveCb(!0);const r=S[w];r&&dn(e,r)&&r.el._leaveCb&&r.el._leaveCb(),k(o,[t])},enter(e){let t=c,o=d,r=f;if(!n.isMounted){if(!l)return;t=b||c,o=y||d,r=_||f}let i=!1;const a=e._enterCb=t=>{i||(i=!0,k(t?r:o,[e]),E.delayedLeave&&E.delayedLeave(),e._enterCb=void 0)};t?x(t,[e,a]):a()},leave(t,o){const r=String(e.key);if(t._enterCb&&t._enterCb(!0),n.isUnmounting)return o();k(p,[t]);let l=!1;const i=t._leaveCb=n=>{l||(l=!0,o(),k(n?m:h,[t]),t._leaveCb=void 0,S[r]===e&&delete S[r])};S[r]=e,v?x(v,[t,i]):i()},clone(e){return ie(e,t,n,o)}};return E}function ae(e){if(pe(e))return e=yn(e),e.children=null,e}function se(e){return pe(e)?e.children?e.children[0]:void 0:e}function ue(e,t){6&e.shapeFlag&&e.component?ue(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function ce(e,t=!1,n){let o=[],r=0;for(let l=0;l<e.length;l++){let i=e[l];const a=null==n?i.key:String(n)+String(null!=i.key?i.key:l);i.type===Jt?(128&i.patchFlag&&r++,o=o.concat(ce(i.children,t,a))):(t||i.type!==Gt)&&o.push(null!=a?yn(i,{key:a}):i)}if(r>1)for(let l=0;l<o.length;l++)o[l].patchFlag=-2;return o}function de(e,t){return(0,r.mf)(e)?(()=>(0,r.l7)({name:e.name},t,{setup:e}))():e}const fe=e=>!!e.type.__asyncLoader;const pe=e=>e.type.__isKeepAlive,ve={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=An(),o=n.ctx;if(!o.renderer)return()=>{const e=t.default&&t.default();return e&&1===e.length?e[0]:e};const l=new Map,i=new Set;let a=null;const s=n.suspense,{renderer:{p:u,m:c,um:d,o:{createElement:f}}}=o,p=f("div");function v(e){we(e),d(e,n,s,!0)}function h(e){l.forEach(((t,n)=>{const o=Kn(t.type);!o||e&&e(o)||m(n)}))}function m(e){const t=l.get(e);a&&dn(t,a)?a&&we(a):v(t),l.delete(e),i.delete(e)}o.activate=(e,t,n,o,l)=>{const i=e.component;c(e,t,n,0,s),u(i.vnode,e,t,n,i,s,o,e.slotScopeIds,l),Nt((()=>{i.isDeactivated=!1,i.a&&(0,r.ir)(i.a);const t=e.props&&e.props.onVnodeMounted;t&&Cn(t,i.parent,e)}),s)},o.deactivate=e=>{const t=e.component;c(e,p,null,1,s),Nt((()=>{t.da&&(0,r.ir)(t.da);const n=e.props&&e.props.onVnodeUnmounted;n&&Cn(n,t.parent,e),t.isDeactivated=!0}),s)},Z((()=>[e.include,e.exclude]),(([e,t])=>{e&&h((t=>me(e,t))),t&&h((e=>!me(t,e)))}),{flush:"post",deep:!0});let g=null;const b=()=>{null!=g&&l.set(g,Se(n.subTree))};return Ce(b),Oe(b),Fe((()=>{l.forEach((e=>{const{subTree:t,suspense:o}=n,r=Se(t);if(e.type!==r.type||e.key!==r.key)v(e);else{we(r);const e=r.component.da;e&&Nt(e,o)}}))})),()=>{if(g=null,!t.default)return null;const n=t.default(),o=n[0];if(n.length>1)return a=null,n;if(!cn(o)||!(4&o.shapeFlag)&&!(128&o.shapeFlag))return a=null,o;let r=Se(o);const s=r.type,u=Kn(fe(r)?r.type.__asyncResolved||{}:s),{include:c,exclude:d,max:f}=e;if(c&&(!u||!me(c,u))||d&&u&&me(d,u))return a=r,o;const p=null==r.key?s:r.key,v=l.get(p);return r.el&&(r=yn(r),128&o.shapeFlag&&(o.ssContent=r)),g=p,v?(r.el=v.el,r.component=v.component,r.transition&&ue(r,r.transition),r.shapeFlag|=512,i.delete(p),i.add(p)):(i.add(p),f&&i.size>parseInt(f,10)&&m(i.values().next().value)),r.shapeFlag|=256,a=r,z(o.type)?o:r}}},he=ve;function me(e,t){return(0,r.kJ)(e)?e.some((e=>me(e,t))):(0,r.HD)(e)?e.split(",").includes(t):!!(0,r.Kj)(e)&&e.test(t)}function ge(e,t){ye(e,"a",t)}function be(e,t){ye(e,"da",t)}function ye(e,t,n=Ln){const o=e.__wdc||(e.__wdc=()=>{let t=n;while(t){if(t.isDeactivated)return;t=t.parent}return e()});if(ke(t,o,n),n){let e=n.parent;while(e&&e.parent)pe(e.parent.vnode)&&_e(o,t,n,e),e=e.parent}}function _e(e,t,n,o){const l=ke(t,e,o,!0);Le((()=>{(0,r.Od)(o[t],l)}),n)}function we(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Se(e){return 128&e.shapeFlag?e.ssContent:e}function ke(e,t,n=Ln,r=!1){if(n){const l=n[e]||(n[e]=[]),a=t.__weh||(t.__weh=(...r)=>{if(n.isUnmounted)return;(0,o.Jd)(),Nn(n);const l=i(t,n,e,r);return Mn(),(0,o.lk)(),l});return r?l.unshift(a):l.push(a),a}}const xe=e=>(t,n=Ln)=>(!Vn||"sp"===e)&&ke(e,((...e)=>t(...e)),n),Ee=xe("bm"),Ce=xe("m"),Te=xe("bu"),Oe=xe("u"),Fe=xe("bum"),Le=xe("um"),Ae=xe("sp"),Pe=xe("rtg"),qe=xe("rtc");function Re(e,t=Ln){ke("ec",e,t)}const Ne="components",Me="directives";function Ie(e,t){return Ve(Ne,e,!0,t)||e}const $e=Symbol.for("v-ndc");function De(e){return Ve(Me,e)}function Ve(e,t,n=!0,o=!1){const l=P||Ln;if(l){const n=l.type;if(e===Ne){const e=Kn(n,!1);if(e&&(e===t||e===(0,r._A)(t)||e===(0,r.kC)((0,r._A)(t))))return n}const i=je(l[e]||n[e],t)||je(l.appContext[e],t);return!i&&o?n:i}}function je(e,t){return e&&(e[t]||e[(0,r._A)(t)]||e[(0,r.kC)((0,r._A)(t))])}function Be(e,t,n,o){let l;const i=n&&n[o];if((0,r.kJ)(e)||(0,r.HD)(e)){l=new Array(e.length);for(let n=0,o=e.length;n<o;n++)l[n]=t(e[n],n,void 0,i&&i[n])}else if("number"===typeof e){0,l=new Array(e);for(let n=0;n<e;n++)l[n]=t(n+1,n,void 0,i&&i[n])}else if((0,r.Kn)(e))if(e[Symbol.iterator])l=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);l=new Array(n.length);for(let o=0,r=n.length;o<r;o++){const r=n[o];l[o]=t(e[r],r,o,i&&i[o])}}else l=[];return n&&(n[o]=l),l}function He(e,t,n={},o,r){if(P.isCE||P.parent&&fe(P.parent)&&P.parent.isCE)return"default"!==t&&(n.name=t),mn("slot",n,o&&o());let l=e[t];l&&l._c&&(l._d=!1),nn();const i=l&&ze(l(n)),a=un(Jt,{key:n.key||i&&i.key||`_${t}`},i||(o?o():[]),i&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),l&&l._c&&(l._d=!0),a}function ze(e){return e.some((e=>!cn(e)||e.type!==Gt&&!(e.type===Jt&&!ze(e.children))))?e:null}const Ue=e=>e?In(e)?Zn(e)||e.proxy:Ue(e.parent):null,We=(0,r.l7)(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ue(e.parent),$root:e=>Ue(e.root),$emit:e=>e.emit,$options:e=>tt(e),$forceUpdate:e=>e.f||(e.f=()=>_(e.update)),$nextTick:e=>e.n||(e.n=b.bind(e.proxy)),$watch:e=>Y.bind(e)}),Ze=(e,t)=>e!==r.kT&&!e.__isScriptSetup&&(0,r.RI)(e,t),Ke={get({_:e},t){const{ctx:n,setupState:l,data:i,props:a,accessCache:s,type:u,appContext:c}=e;let d;if("$"!==t[0]){const o=s[t];if(void 0!==o)switch(o){case 1:return l[t];case 2:return i[t];case 4:return n[t];case 3:return a[t]}else{if(Ze(l,t))return s[t]=1,l[t];if(i!==r.kT&&(0,r.RI)(i,t))return s[t]=2,i[t];if((d=e.propsOptions[0])&&(0,r.RI)(d,t))return s[t]=3,a[t];if(n!==r.kT&&(0,r.RI)(n,t))return s[t]=4,n[t];Je&&(s[t]=0)}}const f=We[t];let p,v;return f?("$attrs"===t&&(0,o.j)(e,"get",t),f(e)):(p=u.__cssModules)&&(p=p[t])?p:n!==r.kT&&(0,r.RI)(n,t)?(s[t]=4,n[t]):(v=c.config.globalProperties,(0,r.RI)(v,t)?v[t]:void 0)},set({_:e},t,n){const{data:o,setupState:l,ctx:i}=e;return Ze(l,t)?(l[t]=n,!0):o!==r.kT&&(0,r.RI)(o,t)?(o[t]=n,!0):!(0,r.RI)(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(i[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:l,propsOptions:i}},a){let s;return!!n[a]||e!==r.kT&&(0,r.RI)(e,a)||Ze(t,a)||(s=i[0])&&(0,r.RI)(s,a)||(0,r.RI)(o,a)||(0,r.RI)(We,a)||(0,r.RI)(l.config.globalProperties,a)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:(0,r.RI)(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Ye(e){return(0,r.kJ)(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}let Je=!0;function Xe(e){const t=tt(e),n=e.proxy,l=e.ctx;Je=!1,t.beforeCreate&&Qe(t.beforeCreate,e,"bc");const{data:i,computed:a,methods:s,watch:u,provide:c,inject:d,created:f,beforeMount:p,mounted:v,beforeUpdate:h,updated:m,activated:g,deactivated:b,beforeDestroy:y,beforeUnmount:_,destroyed:w,unmounted:S,render:k,renderTracked:x,renderTriggered:E,errorCaptured:C,serverPrefetch:T,expose:O,inheritAttrs:F,components:L,directives:A,filters:P}=t,q=null;if(d&&Ge(d,l,q),s)for(const o in s){const e=s[o];(0,r.mf)(e)&&(l[o]=e.bind(n))}if(i){0;const t=i.call(n,n);0,(0,r.Kn)(t)&&(e.data=(0,o.qj)(t))}if(Je=!0,a)for(const o in a){const e=a[o],t=(0,r.mf)(e)?e.bind(n,n):(0,r.mf)(e.get)?e.get.bind(n,n):r.dG;0;const i=!(0,r.mf)(e)&&(0,r.mf)(e.set)?e.set.bind(n):r.dG,s=Jn({get:t,set:i});Object.defineProperty(l,o,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(u)for(const o in u)et(u[o],l,n,o);if(c){const e=(0,r.mf)(c)?c.call(n):c;Reflect.ownKeys(e).forEach((t=>{ht(t,e[t])}))}function R(e,t){(0,r.kJ)(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(f&&Qe(f,e,"c"),R(Ee,p),R(Ce,v),R(Te,h),R(Oe,m),R(ge,g),R(be,b),R(Re,C),R(qe,x),R(Pe,E),R(Fe,_),R(Le,S),R(Ae,T),(0,r.kJ)(O))if(O.length){const t=e.exposed||(e.exposed={});O.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});k&&e.render===r.dG&&(e.render=k),null!=F&&(e.inheritAttrs=F),L&&(e.components=L),A&&(e.directives=A)}function Ge(e,t,n=r.dG){(0,r.kJ)(e)&&(e=it(e));for(const l in e){const n=e[l];let i;i=(0,r.Kn)(n)?"default"in n?mt(n.from||l,n.default,!0):mt(n.from||l):mt(n),(0,o.dq)(i)?Object.defineProperty(t,l,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}):t[l]=i}}function Qe(e,t,n){i((0,r.kJ)(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function et(e,t,n,o){const l=o.includes(".")?J(n,o):()=>n[o];if((0,r.HD)(e)){const n=t[e];(0,r.mf)(n)&&Z(l,n)}else if((0,r.mf)(e))Z(l,e.bind(n));else if((0,r.Kn)(e))if((0,r.kJ)(e))e.forEach((e=>et(e,t,n,o)));else{const o=(0,r.mf)(e.handler)?e.handler.bind(n):t[e.handler];(0,r.mf)(o)&&Z(l,o,e)}else 0}function tt(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:l,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,s=i.get(t);let u;return s?u=s:l.length||n||o?(u={},l.length&&l.forEach((e=>nt(u,e,a,!0))),nt(u,t,a)):u=t,(0,r.Kn)(t)&&i.set(t,u),u}function nt(e,t,n,o=!1){const{mixins:r,extends:l}=t;l&&nt(e,l,n,!0),r&&r.forEach((t=>nt(e,t,n,!0)));for(const i in t)if(o&&"expose"===i);else{const o=ot[i]||n&&n[i];e[i]=o?o(e[i],t[i]):t[i]}return e}const ot={data:rt,props:ut,emits:ut,methods:st,computed:st,beforeCreate:at,created:at,beforeMount:at,mounted:at,beforeUpdate:at,updated:at,beforeDestroy:at,beforeUnmount:at,destroyed:at,unmounted:at,activated:at,deactivated:at,errorCaptured:at,serverPrefetch:at,components:st,directives:st,watch:ct,provide:rt,inject:lt};function rt(e,t){return t?e?function(){return(0,r.l7)((0,r.mf)(e)?e.call(this,this):e,(0,r.mf)(t)?t.call(this,this):t)}:t:e}function lt(e,t){return st(it(e),it(t))}function it(e){if((0,r.kJ)(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function at(e,t){return e?[...new Set([].concat(e,t))]:t}function st(e,t){return e?(0,r.l7)(Object.create(null),e,t):t}function ut(e,t){return e?(0,r.kJ)(e)&&(0,r.kJ)(t)?[...new Set([...e,...t])]:(0,r.l7)(Object.create(null),Ye(e),Ye(null!=t?t:{})):t}function ct(e,t){if(!e)return t;if(!t)return e;const n=(0,r.l7)(Object.create(null),e);for(const o in t)n[o]=at(e[o],t[o]);return n}function dt(){return{app:null,config:{isNativeTag:r.NO,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ft=0;function pt(e,t){return function(n,o=null){(0,r.mf)(n)||(n=(0,r.l7)({},n)),null==o||(0,r.Kn)(o)||(o=null);const l=dt();const i=new Set;let a=!1;const s=l.app={_uid:ft++,_component:n,_props:o,_container:null,_context:l,_instance:null,version:eo,get config(){return l.config},set config(e){0},use(e,...t){return i.has(e)||(e&&(0,r.mf)(e.install)?(i.add(e),e.install(s,...t)):(0,r.mf)(e)&&(i.add(e),e(s,...t))),s},mixin(e){return l.mixins.includes(e)||l.mixins.push(e),s},component(e,t){return t?(l.components[e]=t,s):l.components[e]},directive(e,t){return t?(l.directives[e]=t,s):l.directives[e]},mount(r,i,u){if(!a){0;const c=mn(n,o);return c.appContext=l,i&&t?t(c,r):e(c,r,u),a=!0,s._container=r,r.__vue_app__=s,Zn(c.component)||c.component.proxy}},unmount(){a&&(e(null,s._container),delete s._container.__vue_app__)},provide(e,t){return l.provides[e]=t,s},runWithContext(e){vt=s;try{return e()}finally{vt=null}}};return s}}let vt=null;function ht(e,t){if(Ln){let n=Ln.provides;const o=Ln.parent&&Ln.parent.provides;o===n&&(n=Ln.provides=Object.create(o)),n[e]=t}else 0}function mt(e,t,n=!1){const o=Ln||P;if(o||vt){const l=o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:vt._context.provides;if(l&&e in l)return l[e];if(arguments.length>1)return n&&(0,r.mf)(t)?t.call(o&&o.proxy):t}else 0}function gt(e,t,n,l=!1){const i={},a={};(0,r.Nj)(a,fn,1),e.propsDefaults=Object.create(null),yt(e,t,i,a);for(const o in e.propsOptions[0])o in i||(i[o]=void 0);n?e.props=l?i:(0,o.Um)(i):e.type.props?e.props=i:e.props=a,e.attrs=a}function bt(e,t,n,l){const{props:i,attrs:a,vnode:{patchFlag:s}}=e,u=(0,o.IU)(i),[c]=e.propsOptions;let d=!1;if(!(l||s>0)||16&s){let o;yt(e,t,i,a)&&(d=!0);for(const l in u)t&&((0,r.RI)(t,l)||(o=(0,r.rs)(l))!==l&&(0,r.RI)(t,o))||(c?!n||void 0===n[l]&&void 0===n[o]||(i[l]=_t(c,u,l,void 0,e,!0)):delete i[l]);if(a!==u)for(const e in a)t&&(0,r.RI)(t,e)||(delete a[e],d=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let l=n[o];if(A(e.emitsOptions,l))continue;const s=t[l];if(c)if((0,r.RI)(a,l))s!==a[l]&&(a[l]=s,d=!0);else{const t=(0,r._A)(l);i[t]=_t(c,u,t,s,e,!1)}else s!==a[l]&&(a[l]=s,d=!0)}}d&&(0,o.X$)(e,"set","$attrs")}function yt(e,t,n,l){const[i,a]=e.propsOptions;let s,u=!1;if(t)for(let o in t){if((0,r.Gg)(o))continue;const c=t[o];let d;i&&(0,r.RI)(i,d=(0,r._A)(o))?a&&a.includes(d)?(s||(s={}))[d]=c:n[d]=c:A(e.emitsOptions,o)||o in l&&c===l[o]||(l[o]=c,u=!0)}if(a){const t=(0,o.IU)(n),l=s||r.kT;for(let o=0;o<a.length;o++){const s=a[o];n[s]=_t(i,t,s,l[s],e,!(0,r.RI)(l,s))}}return u}function _t(e,t,n,o,l,i){const a=e[n];if(null!=a){const e=(0,r.RI)(a,"default");if(e&&void 0===o){const e=a.default;if(a.type!==Function&&!a.skipFactory&&(0,r.mf)(e)){const{propsDefaults:r}=l;n in r?o=r[n]:(Nn(l),o=r[n]=e.call(null,t),Mn())}else o=e}a[0]&&(i&&!e?o=!1:!a[1]||""!==o&&o!==(0,r.rs)(n)||(o=!0))}return o}function wt(e,t,n=!1){const o=t.propsCache,l=o.get(e);if(l)return l;const i=e.props,a={},s=[];let u=!1;if(!(0,r.mf)(e)){const o=e=>{u=!0;const[n,o]=wt(e,t,!0);(0,r.l7)(a,n),o&&s.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!i&&!u)return(0,r.Kn)(e)&&o.set(e,r.Z6),r.Z6;if((0,r.kJ)(i))for(let d=0;d<i.length;d++){0;const e=(0,r._A)(i[d]);St(e)&&(a[e]=r.kT)}else if(i){0;for(const e in i){const t=(0,r._A)(e);if(St(t)){const n=i[e],o=a[t]=(0,r.kJ)(n)||(0,r.mf)(n)?{type:n}:(0,r.l7)({},n);if(o){const e=Et(Boolean,o.type),n=Et(String,o.type);o[0]=e>-1,o[1]=n<0||e<n,(e>-1||(0,r.RI)(o,"default"))&&s.push(t)}}}}const c=[a,s];return(0,r.Kn)(e)&&o.set(e,c),c}function St(e){return"$"!==e[0]}function kt(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function xt(e,t){return kt(e)===kt(t)}function Et(e,t){return(0,r.kJ)(t)?t.findIndex((t=>xt(t,e))):(0,r.mf)(t)&&xt(t,e)?0:-1}const Ct=e=>"_"===e[0]||"$stable"===e,Tt=e=>(0,r.kJ)(e)?e.map(Sn):[Sn(e)],Ot=(e,t,n)=>{if(t._n)return t;const o=I(((...e)=>Tt(t(...e))),n);return o._c=!1,o},Ft=(e,t,n)=>{const o=e._ctx;for(const l in e){if(Ct(l))continue;const n=e[l];if((0,r.mf)(n))t[l]=Ot(l,n,o);else if(null!=n){0;const e=Tt(n);t[l]=()=>e}}},Lt=(e,t)=>{const n=Tt(t);e.slots.default=()=>n},At=(e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=(0,o.IU)(t),(0,r.Nj)(t,"_",n)):Ft(t,e.slots={})}else e.slots={},t&&Lt(e,t);(0,r.Nj)(e.slots,fn,1)},Pt=(e,t,n)=>{const{vnode:o,slots:l}=e;let i=!0,a=r.kT;if(32&o.shapeFlag){const e=t._;e?n&&1===e?i=!1:((0,r.l7)(l,t),n||1!==e||delete l._):(i=!t.$stable,Ft(t,l)),a=t}else t&&(Lt(e,t),a={default:1});if(i)for(const r in l)Ct(r)||r in a||delete l[r]};function qt(e,t,n,i,a=!1){if((0,r.kJ)(e))return void e.forEach(((e,o)=>qt(e,t&&((0,r.kJ)(t)?t[o]:t),n,i,a)));if(fe(i)&&!a)return;const s=4&i.shapeFlag?Zn(i.component)||i.component.proxy:i.el,u=a?null:s,{i:c,r:d}=e;const f=t&&t.r,p=c.refs===r.kT?c.refs={}:c.refs,v=c.setupState;if(null!=f&&f!==d&&((0,r.HD)(f)?(p[f]=null,(0,r.RI)(v,f)&&(v[f]=null)):(0,o.dq)(f)&&(f.value=null)),(0,r.mf)(d))l(d,c,12,[u,p]);else{const t=(0,r.HD)(d),l=(0,o.dq)(d);if(t||l){const o=()=>{if(e.f){const n=t?(0,r.RI)(v,d)?v[d]:p[d]:d.value;a?(0,r.kJ)(n)&&(0,r.Od)(n,s):(0,r.kJ)(n)?n.includes(s)||n.push(s):t?(p[d]=[s],(0,r.RI)(v,d)&&(v[d]=p[d])):(d.value=[s],e.k&&(p[e.k]=d.value))}else t?(p[d]=u,(0,r.RI)(v,d)&&(v[d]=u)):l&&(d.value=u,e.k&&(p[e.k]=u))};u?(o.id=-1,Nt(o,n)):o()}else 0}}function Rt(){}const Nt=U;function Mt(e){return It(e)}function It(e,t){Rt();const n=(0,r.E9)();n.__VUE__=!0;const{insert:l,remove:i,patchProp:a,createElement:s,createText:u,createComment:c,setText:d,setElementText:f,parentNode:p,nextSibling:v,setScopeId:h=r.dG,insertStaticContent:m}=e,g=(e,t,n,o=null,r=null,l=null,i=!1,a=null,s=!!t.dynamicChildren)=>{if(e===t)return;e&&!dn(e,t)&&(o=G(e),Z(e,r,l,!0),e=null),-2===t.patchFlag&&(s=!1,t.dynamicChildren=null);const{type:u,ref:c,shapeFlag:d}=t;switch(u){case Xt:b(e,t,n,o);break;case Gt:y(e,t,n,o);break;case Qt:null==e&&w(t,n,o,i);break;case Jt:R(e,t,n,o,r,l,i,a,s);break;default:1&d?T(e,t,n,o,r,l,i,a,s):6&d?N(e,t,n,o,r,l,i,a,s):(64&d||128&d)&&u.process(e,t,n,o,r,l,i,a,s,te)}null!=c&&r&&qt(c,e&&e.ref,l,t||e,!t)},b=(e,t,n,o)=>{if(null==e)l(t.el=u(t.children),n,o);else{const n=t.el=e.el;t.children!==e.children&&d(n,t.children)}},y=(e,t,n,o)=>{null==e?l(t.el=c(t.children||""),n,o):t.el=e.el},w=(e,t,n,o)=>{[e.el,e.anchor]=m(e.children,t,n,o,e.el,e.anchor)},k=({el:e,anchor:t},n,o)=>{let r;while(e&&e!==t)r=v(e),l(e,n,o),e=r;l(t,n,o)},C=({el:e,anchor:t})=>{let n;while(e&&e!==t)n=v(e),i(e),e=n;i(t)},T=(e,t,n,o,r,l,i,a,s)=>{i=i||"svg"===t.type,null==e?O(t,n,o,r,l,i,a,s):A(e,t,r,l,i,a,s)},O=(e,t,n,o,i,u,c,d)=>{let p,v;const{type:h,props:m,shapeFlag:g,transition:b,dirs:y}=e;if(p=e.el=s(e.type,u,m&&m.is,m),8&g?f(p,e.children):16&g&&L(e.children,p,null,o,i,u&&"foreignObject"!==h,c,d),y&&Q(e,null,o,"created"),F(p,e,e.scopeId,c,o),m){for(const t in m)"value"===t||(0,r.Gg)(t)||a(p,t,null,m[t],u,e.children,o,i,X);"value"in m&&a(p,"value",null,m.value),(v=m.onVnodeBeforeMount)&&Cn(v,o,e)}y&&Q(e,null,o,"beforeMount");const _=(!i||i&&!i.pendingBranch)&&b&&!b.persisted;_&&b.beforeEnter(p),l(p,t,n),((v=m&&m.onVnodeMounted)||_||y)&&Nt((()=>{v&&Cn(v,o,e),_&&b.enter(p),y&&Q(e,null,o,"mounted")}),i)},F=(e,t,n,o,r)=>{if(n&&h(e,n),o)for(let l=0;l<o.length;l++)h(e,o[l]);if(r){let n=r.subTree;if(t===n){const t=r.vnode;F(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},L=(e,t,n,o,r,l,i,a,s=0)=>{for(let u=s;u<e.length;u++){const s=e[u]=a?kn(e[u]):Sn(e[u]);g(null,s,t,n,o,r,l,i,a)}},A=(e,t,n,o,l,i,s)=>{const u=t.el=e.el;let{patchFlag:c,dynamicChildren:d,dirs:p}=t;c|=16&e.patchFlag;const v=e.props||r.kT,h=t.props||r.kT;let m;n&&$t(n,!1),(m=h.onVnodeBeforeUpdate)&&Cn(m,n,t,e),p&&Q(t,e,n,"beforeUpdate"),n&&$t(n,!0);const g=l&&"foreignObject"!==t.type;if(d?P(e.dynamicChildren,d,u,n,o,g,i):s||B(e,t,u,null,n,o,g,i,!1),c>0){if(16&c)q(u,t,v,h,n,o,l);else if(2&c&&v.class!==h.class&&a(u,"class",null,h.class,l),4&c&&a(u,"style",v.style,h.style,l),8&c){const r=t.dynamicProps;for(let t=0;t<r.length;t++){const i=r[t],s=v[i],c=h[i];c===s&&"value"!==i||a(u,i,s,c,l,e.children,n,o,X)}}1&c&&e.children!==t.children&&f(u,t.children)}else s||null!=d||q(u,t,v,h,n,o,l);((m=h.onVnodeUpdated)||p)&&Nt((()=>{m&&Cn(m,n,t,e),p&&Q(t,e,n,"updated")}),o)},P=(e,t,n,o,r,l,i)=>{for(let a=0;a<t.length;a++){const s=e[a],u=t[a],c=s.el&&(s.type===Jt||!dn(s,u)||70&s.shapeFlag)?p(s.el):n;g(s,u,c,null,o,r,l,i,!0)}},q=(e,t,n,o,l,i,s)=>{if(n!==o){if(n!==r.kT)for(const u in n)(0,r.Gg)(u)||u in o||a(e,u,n[u],null,s,t.children,l,i,X);for(const u in o){if((0,r.Gg)(u))continue;const c=o[u],d=n[u];c!==d&&"value"!==u&&a(e,u,d,c,s,t.children,l,i,X)}"value"in o&&a(e,"value",n.value,o.value)}},R=(e,t,n,o,r,i,a,s,c)=>{const d=t.el=e?e.el:u(""),f=t.anchor=e?e.anchor:u("");let{patchFlag:p,dynamicChildren:v,slotScopeIds:h}=t;h&&(s=s?s.concat(h):h),null==e?(l(d,n,o),l(f,n,o),L(t.children,n,f,r,i,a,s,c)):p>0&&64&p&&v&&e.dynamicChildren?(P(e.dynamicChildren,v,n,r,i,a,s),(null!=t.key||r&&t===r.subTree)&&Dt(e,t,!0)):B(e,t,n,f,r,i,a,s,c)},N=(e,t,n,o,r,l,i,a,s)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,i,s):M(t,n,o,r,l,i,s):I(e,t,s)},M=(e,t,n,o,r,l,i)=>{const a=e.component=Fn(e,o,r);if(pe(e)&&(a.ctx.renderer=te),jn(a),a.asyncDep){if(r&&r.registerDep(a,D),!e.el){const e=a.subTree=mn(Gt);y(null,e,t,n)}}else D(a,e,t,n,r,l,i)},I=(e,t,n)=>{const o=t.component=e.component;if(j(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void V(o,t,n);o.next=t,S(o.update),o.update()}else t.el=e.el,o.vnode=t},D=(e,t,n,l,i,a,s)=>{const u=()=>{if(e.isMounted){let t,{next:n,bu:o,u:l,parent:u,vnode:c}=e,d=n;0,$t(e,!1),n?(n.el=c.el,V(e,n,s)):n=c,o&&(0,r.ir)(o),(t=n.props&&n.props.onVnodeBeforeUpdate)&&Cn(t,u,n,c),$t(e,!0);const f=$(e);0;const v=e.subTree;e.subTree=f,g(v,f,p(v.el),G(v),e,i,a),n.el=f.el,null===d&&H(e,f.el),l&&Nt(l,i),(t=n.props&&n.props.onVnodeUpdated)&&Nt((()=>Cn(t,u,n,c)),i)}else{let o;const{el:s,props:u}=t,{bm:c,m:d,parent:f}=e,p=fe(t);if($t(e,!1),c&&(0,r.ir)(c),!p&&(o=u&&u.onVnodeBeforeMount)&&Cn(o,f,t),$t(e,!0),s&&oe){const n=()=>{e.subTree=$(e),oe(s,e.subTree,e,i,null)};p?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{0;const o=e.subTree=$(e);0,g(null,o,n,l,e,i,a),t.el=o.el}if(d&&Nt(d,i),!p&&(o=u&&u.onVnodeMounted)){const e=t;Nt((()=>Cn(o,f,e)),i)}(256&t.shapeFlag||f&&fe(f.vnode)&&256&f.vnode.shapeFlag)&&e.a&&Nt(e.a,i),e.isMounted=!0,t=n=l=null}},c=e.effect=new o.qq(u,(()=>_(d)),e.scope),d=e.update=()=>c.run();d.id=e.uid,$t(e,!0),d()},V=(e,t,n)=>{t.component=e;const r=e.vnode.props;e.vnode=t,e.next=null,bt(e,t.props,r,n),Pt(e,t.children,n),(0,o.Jd)(),x(),(0,o.lk)()},B=(e,t,n,o,r,l,i,a,s=!1)=>{const u=e&&e.children,c=e?e.shapeFlag:0,d=t.children,{patchFlag:p,shapeFlag:v}=t;if(p>0){if(128&p)return void U(u,d,n,o,r,l,i,a,s);if(256&p)return void z(u,d,n,o,r,l,i,a,s)}8&v?(16&c&&X(u,r,l),d!==u&&f(n,d)):16&c?16&v?U(u,d,n,o,r,l,i,a,s):X(u,r,l,!0):(8&c&&f(n,""),16&v&&L(d,n,o,r,l,i,a,s))},z=(e,t,n,o,l,i,a,s,u)=>{e=e||r.Z6,t=t||r.Z6;const c=e.length,d=t.length,f=Math.min(c,d);let p;for(p=0;p<f;p++){const o=t[p]=u?kn(t[p]):Sn(t[p]);g(e[p],o,n,null,l,i,a,s,u)}c>d?X(e,l,i,!0,!1,f):L(t,n,o,l,i,a,s,u,f)},U=(e,t,n,o,l,i,a,s,u)=>{let c=0;const d=t.length;let f=e.length-1,p=d-1;while(c<=f&&c<=p){const o=e[c],r=t[c]=u?kn(t[c]):Sn(t[c]);if(!dn(o,r))break;g(o,r,n,null,l,i,a,s,u),c++}while(c<=f&&c<=p){const o=e[f],r=t[p]=u?kn(t[p]):Sn(t[p]);if(!dn(o,r))break;g(o,r,n,null,l,i,a,s,u),f--,p--}if(c>f){if(c<=p){const e=p+1,r=e<d?t[e].el:o;while(c<=p)g(null,t[c]=u?kn(t[c]):Sn(t[c]),n,r,l,i,a,s,u),c++}}else if(c>p)while(c<=f)Z(e[c],l,i,!0),c++;else{const v=c,h=c,m=new Map;for(c=h;c<=p;c++){const e=t[c]=u?kn(t[c]):Sn(t[c]);null!=e.key&&m.set(e.key,c)}let b,y=0;const _=p-h+1;let w=!1,S=0;const k=new Array(_);for(c=0;c<_;c++)k[c]=0;for(c=v;c<=f;c++){const o=e[c];if(y>=_){Z(o,l,i,!0);continue}let r;if(null!=o.key)r=m.get(o.key);else for(b=h;b<=p;b++)if(0===k[b-h]&&dn(o,t[b])){r=b;break}void 0===r?Z(o,l,i,!0):(k[r-h]=c+1,r>=S?S=r:w=!0,g(o,t[r],n,null,l,i,a,s,u),y++)}const x=w?Vt(k):r.Z6;for(b=x.length-1,c=_-1;c>=0;c--){const e=h+c,r=t[e],f=e+1<d?t[e+1].el:o;0===k[c]?g(null,r,n,f,l,i,a,s,u):w&&(b<0||c!==x[b]?W(r,n,f,2):b--)}}},W=(e,t,n,o,r=null)=>{const{el:i,type:a,transition:s,children:u,shapeFlag:c}=e;if(6&c)return void W(e.component.subTree,t,n,o);if(128&c)return void e.suspense.move(t,n,o);if(64&c)return void a.move(e,t,n,te);if(a===Jt){l(i,t,n);for(let e=0;e<u.length;e++)W(u[e],t,n,o);return void l(e.anchor,t,n)}if(a===Qt)return void k(e,t,n);const d=2!==o&&1&c&&s;if(d)if(0===o)s.beforeEnter(i),l(i,t,n),Nt((()=>s.enter(i)),r);else{const{leave:e,delayLeave:o,afterLeave:r}=s,a=()=>l(i,t,n),u=()=>{e(i,(()=>{a(),r&&r()}))};o?o(i,a,u):u()}else l(i,t,n)},Z=(e,t,n,o=!1,r=!1)=>{const{type:l,props:i,ref:a,children:s,dynamicChildren:u,shapeFlag:c,patchFlag:d,dirs:f}=e;if(null!=a&&qt(a,null,n,e,!0),256&c)return void t.ctx.deactivate(e);const p=1&c&&f,v=!fe(e);let h;if(v&&(h=i&&i.onVnodeBeforeUnmount)&&Cn(h,t,e),6&c)J(e.component,n,o);else{if(128&c)return void e.suspense.unmount(n,o);p&&Q(e,null,t,"beforeUnmount"),64&c?e.type.remove(e,t,n,r,te,o):u&&(l!==Jt||d>0&&64&d)?X(u,t,n,!1,!0):(l===Jt&&384&d||!r&&16&c)&&X(s,t,n),o&&K(e)}(v&&(h=i&&i.onVnodeUnmounted)||p)&&Nt((()=>{h&&Cn(h,t,e),p&&Q(e,null,t,"unmounted")}),n)},K=e=>{const{type:t,el:n,anchor:o,transition:r}=e;if(t===Jt)return void Y(n,o);if(t===Qt)return void C(e);const l=()=>{i(n),r&&!r.persisted&&r.afterLeave&&r.afterLeave()};if(1&e.shapeFlag&&r&&!r.persisted){const{leave:t,delayLeave:o}=r,i=()=>t(n,l);o?o(e.el,l,i):i()}else l()},Y=(e,t)=>{let n;while(e!==t)n=v(e),i(e),e=n;i(t)},J=(e,t,n)=>{const{bum:o,scope:l,update:i,subTree:a,um:s}=e;o&&(0,r.ir)(o),l.stop(),i&&(i.active=!1,Z(a,e,t,n)),s&&Nt(s,t),Nt((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},X=(e,t,n,o=!1,r=!1,l=0)=>{for(let i=l;i<e.length;i++)Z(e[i],t,n,o,r)},G=e=>6&e.shapeFlag?G(e.component.subTree):128&e.shapeFlag?e.suspense.next():v(e.anchor||e.el),ee=(e,t,n)=>{null==e?t._vnode&&Z(t._vnode,null,null,!0):g(t._vnode||null,e,t,null,null,null,n),x(),E(),t._vnode=e},te={p:g,um:Z,m:W,r:K,mt:M,mc:L,pc:B,pbc:P,n:G,o:e};let ne,oe;return t&&([ne,oe]=t(te)),{render:ee,hydrate:ne,createApp:pt(ee,ne)}}function $t({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Dt(e,t,n=!1){const o=e.children,l=t.children;if((0,r.kJ)(o)&&(0,r.kJ)(l))for(let r=0;r<o.length;r++){const e=o[r];let t=l[r];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=l[r]=kn(l[r]),t.el=e.el),n||Dt(e,t)),t.type===Xt&&(t.el=e.el)}}function Vt(e){const t=e.slice(),n=[0];let o,r,l,i,a;const s=e.length;for(o=0;o<s;o++){const s=e[o];if(0!==s){if(r=n[n.length-1],e[r]<s){t[o]=r,n.push(o);continue}l=0,i=n.length-1;while(l<i)a=l+i>>1,e[n[a]]<s?l=a+1:i=a;s<e[n[l]]&&(l>0&&(t[o]=n[l-1]),n[l]=o)}}l=n.length,i=n[l-1];while(l-- >0)n[l]=i,i=t[i];return n}const jt=e=>e.__isTeleport,Bt=e=>e&&(e.disabled||""===e.disabled),Ht=e=>"undefined"!==typeof SVGElement&&e instanceof SVGElement,zt=(e,t)=>{const n=e&&e.to;if((0,r.HD)(n)){if(t){const e=t(n);return e}return null}return n},Ut={__isTeleport:!0,process(e,t,n,o,r,l,i,a,s,u){const{mc:c,pc:d,pbc:f,o:{insert:p,querySelector:v,createText:h,createComment:m}}=u,g=Bt(t.props);let{shapeFlag:b,children:y,dynamicChildren:_}=t;if(null==e){const e=t.el=h(""),u=t.anchor=h("");p(e,n,o),p(u,n,o);const d=t.target=zt(t.props,v),f=t.targetAnchor=h("");d&&(p(f,d),i=i||Ht(d));const m=(e,t)=>{16&b&&c(y,e,t,r,l,i,a,s)};g?m(n,u):d&&m(d,f)}else{t.el=e.el;const o=t.anchor=e.anchor,c=t.target=e.target,p=t.targetAnchor=e.targetAnchor,h=Bt(e.props),m=h?n:c,b=h?o:p;if(i=i||Ht(c),_?(f(e.dynamicChildren,_,m,r,l,i,a),Dt(e,t,!0)):s||d(e,t,m,b,r,l,i,a,!1),g)h||Wt(t,n,o,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const e=t.target=zt(t.props,v);e&&Wt(t,e,null,u,0)}else h&&Wt(t,c,p,u,1)}Yt(t)},remove(e,t,n,o,{um:r,o:{remove:l}},i){const{shapeFlag:a,children:s,anchor:u,targetAnchor:c,target:d,props:f}=e;if(d&&l(c),(i||!Bt(f))&&(l(u),16&a))for(let p=0;p<s.length;p++){const e=s[p];r(e,t,n,!0,!!e.dynamicChildren)}},move:Wt,hydrate:Zt};function Wt(e,t,n,{o:{insert:o},m:r},l=2){0===l&&o(e.targetAnchor,t,n);const{el:i,anchor:a,shapeFlag:s,children:u,props:c}=e,d=2===l;if(d&&o(i,t,n),(!d||Bt(c))&&16&s)for(let f=0;f<u.length;f++)r(u[f],t,n,2);d&&o(a,t,n)}function Zt(e,t,n,o,r,l,{o:{nextSibling:i,parentNode:a,querySelector:s}},u){const c=t.target=zt(t.props,s);if(c){const s=c._lpa||c.firstChild;if(16&t.shapeFlag)if(Bt(t.props))t.anchor=u(i(e),t,a(e),n,o,r,l),t.targetAnchor=s;else{t.anchor=i(e);let a=s;while(a)if(a=i(a),a&&8===a.nodeType&&"teleport anchor"===a.data){t.targetAnchor=a,c._lpa=t.targetAnchor&&i(t.targetAnchor);break}u(s,t,c,n,o,r,l)}Yt(t)}return t.anchor&&i(t.anchor)}const Kt=Ut;function Yt(e){const t=e.ctx;if(t&&t.ut){let n=e.children[0].el;while(n!==e.targetAnchor)1===n.nodeType&&n.setAttribute("data-v-owner",t.uid),n=n.nextSibling;t.ut()}}const Jt=Symbol.for("v-fgt"),Xt=Symbol.for("v-txt"),Gt=Symbol.for("v-cmt"),Qt=Symbol.for("v-stc"),en=[];let tn=null;function nn(e=!1){en.push(tn=e?null:[])}function on(){en.pop(),tn=en[en.length-1]||null}let rn=1;function ln(e){rn+=e}function an(e){return e.dynamicChildren=rn>0?tn||r.Z6:null,on(),rn>0&&tn&&tn.push(e),e}function sn(e,t,n,o,r,l){return an(hn(e,t,n,o,r,l,!0))}function un(e,t,n,o,r){return an(mn(e,t,n,o,r,!0))}function cn(e){return!!e&&!0===e.__v_isVNode}function dn(e,t){return e.type===t.type&&e.key===t.key}const fn="__vInternal",pn=({key:e})=>null!=e?e:null,vn=({ref:e,ref_key:t,ref_for:n})=>("number"===typeof e&&(e=""+e),null!=e?(0,r.HD)(e)||(0,o.dq)(e)||(0,r.mf)(e)?{i:P,r:e,k:t,f:!!n}:e:null);function hn(e,t=null,n=null,o=0,l=null,i=(e===Jt?0:1),a=!1,s=!1){const u={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&pn(t),ref:t&&vn(t),scopeId:q,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:l,dynamicChildren:null,appContext:null,ctx:P};return s?(xn(u,n),128&i&&e.normalize(u)):n&&(u.shapeFlag|=(0,r.HD)(n)?8:16),rn>0&&!a&&tn&&(u.patchFlag>0||6&i)&&32!==u.patchFlag&&tn.push(u),u}const mn=gn;function gn(e,t=null,n=null,l=0,i=null,a=!1){if(e&&e!==$e||(e=Gt),cn(e)){const o=yn(e,t,!0);return n&&xn(o,n),rn>0&&!a&&tn&&(6&o.shapeFlag?tn[tn.indexOf(e)]=o:tn.push(o)),o.patchFlag|=-2,o}if(Yn(e)&&(e=e.__vccOpts),t){t=bn(t);let{class:e,style:n}=t;e&&!(0,r.HD)(e)&&(t.class=(0,r.C_)(e)),(0,r.Kn)(n)&&((0,o.X3)(n)&&!(0,r.kJ)(n)&&(n=(0,r.l7)({},n)),t.style=(0,r.j5)(n))}const s=(0,r.HD)(e)?1:z(e)?128:jt(e)?64:(0,r.Kn)(e)?4:(0,r.mf)(e)?2:0;return hn(e,t,n,l,i,s,a,!0)}function bn(e){return e?(0,o.X3)(e)||fn in e?(0,r.l7)({},e):e:null}function yn(e,t,n=!1){const{props:o,ref:l,patchFlag:i,children:a}=e,s=t?En(o||{},t):o,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:s,key:s&&pn(s),ref:t&&t.ref?n&&l?(0,r.kJ)(l)?l.concat(vn(t)):[l,vn(t)]:vn(t):l,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Jt?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&yn(e.ssContent),ssFallback:e.ssFallback&&yn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return u}function _n(e=" ",t=0){return mn(Xt,null,e,t)}function wn(e="",t=!1){return t?(nn(),un(Gt,null,e)):mn(Gt,null,e)}function Sn(e){return null==e||"boolean"===typeof e?mn(Gt):(0,r.kJ)(e)?mn(Jt,null,e.slice()):"object"===typeof e?kn(e):mn(Xt,null,String(e))}function kn(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:yn(e)}function xn(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if((0,r.kJ)(t))n=16;else if("object"===typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),xn(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||fn in t?3===o&&P&&(1===P.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=P}}else(0,r.mf)(t)?(t={default:t,_ctx:P},n=32):(t=String(t),64&o?(n=16,t=[_n(t)]):n=8);e.children=t,e.shapeFlag|=n}function En(...e){const t={};for(let n=0;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=(0,r.C_)([t.class,o.class]));else if("style"===e)t.style=(0,r.j5)([t.style,o.style]);else if((0,r.F7)(e)){const n=t[e],l=o[e];!l||n===l||(0,r.kJ)(n)&&n.includes(l)||(t[e]=n?[].concat(n,l):l)}else""!==e&&(t[e]=o[e])}return t}function Cn(e,t,n,o=null){i(e,t,7,[n,o])}const Tn=dt();let On=0;function Fn(e,t,n){const l=e.type,i=(t?t.appContext:e.appContext)||Tn,a={uid:On++,vnode:e,type:l,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,scope:new o.Bj(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:wt(l,i),emitsOptions:L(l,i),emit:null,emitted:null,propsDefaults:r.kT,inheritAttrs:l.inheritAttrs,ctx:r.kT,data:r.kT,props:r.kT,attrs:r.kT,slots:r.kT,refs:r.kT,setupState:r.kT,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return a.ctx={_:a},a.root=t?t.root:a,a.emit=F.bind(null,a),e.ce&&e.ce(a),a}let Ln=null;const An=()=>Ln||P;let Pn,qn,Rn="__VUE_INSTANCE_SETTERS__";(qn=(0,r.E9)()[Rn])||(qn=(0,r.E9)()[Rn]=[]),qn.push((e=>Ln=e)),Pn=e=>{qn.length>1?qn.forEach((t=>t(e))):qn[0](e)};const Nn=e=>{Pn(e),e.scope.on()},Mn=()=>{Ln&&Ln.scope.off(),Pn(null)};function In(e){return 4&e.vnode.shapeFlag}let $n,Dn,Vn=!1;function jn(e,t=!1){Vn=t;const{props:n,children:o}=e.vnode,r=In(e);gt(e,n,r,t),At(e,o);const l=r?Bn(e,t):void 0;return Vn=!1,l}function Bn(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=(0,o.Xl)(new Proxy(e.ctx,Ke));const{setup:i}=n;if(i){const n=e.setupContext=i.length>1?Wn(e):null;Nn(e),(0,o.Jd)();const s=l(i,e,0,[e.props,n]);if((0,o.lk)(),Mn(),(0,r.tI)(s)){if(s.then(Mn,Mn),t)return s.then((n=>{Hn(e,n,t)})).catch((t=>{a(t,e,0)}));e.asyncDep=s}else Hn(e,s,t)}else zn(e,t)}function Hn(e,t,n){(0,r.mf)(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:(0,r.Kn)(t)&&(e.setupState=(0,o.WL)(t)),zn(e,n)}function zn(e,t,n){const l=e.type;if(!e.render){if(!t&&$n&&!l.render){const t=l.template||tt(e).template;if(t){0;const{isCustomElement:n,compilerOptions:o}=e.appContext.config,{delimiters:i,compilerOptions:a}=l,s=(0,r.l7)((0,r.l7)({isCustomElement:n,delimiters:i},o),a);l.render=$n(t,s)}}e.render=l.render||r.dG,Dn&&Dn(e)}Nn(e),(0,o.Jd)(),Xe(e),(0,o.lk)(),Mn()}function Un(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get(t,n){return(0,o.j)(e,"get","$attrs"),t[n]}}))}function Wn(e){const t=t=>{e.exposed=t||{}};return{get attrs(){return Un(e)},slots:e.slots,emit:e.emit,expose:t}}function Zn(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy((0,o.WL)((0,o.Xl)(e.exposed)),{get(t,n){return n in t?t[n]:n in We?We[n](e):void 0},has(e,t){return t in e||t in We}}))}function Kn(e,t=!0){return(0,r.mf)(e)?e.displayName||e.name:e.name||t&&e.__name}function Yn(e){return(0,r.mf)(e)&&"__vccOpts"in e}const Jn=(e,t)=>(0,o.Fl)(e,t,Vn);function Xn(e,t,n){const o=arguments.length;return 2===o?(0,r.Kn)(t)&&!(0,r.kJ)(t)?cn(t)?mn(e,null,[t]):mn(e,t):mn(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&cn(n)&&(n=[n]),mn(e,t,n))}const Gn=Symbol.for("v-scx"),Qn=()=>{{const e=mt(Gn);return e}};const eo="3.3.4"},1957:(e,t,n)=>{"use strict";n.d(t,{D2:()=>ae,W3:()=>te,ri:()=>de,uT:()=>N});var o=n(6970),r=n(9835),l=n(499);const i="http://www.w3.org/2000/svg",a="undefined"!==typeof document?document:null,s=a&&a.createElement("template"),u={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r=t?a.createElementNS(i,e):a.createElement(e,n?{is:n}:void 0);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>a.createTextNode(e),createComment:e=>a.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>a.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,l){const i=n?n.previousSibling:t.lastChild;if(r&&(r===l||r.nextSibling)){while(1)if(t.insertBefore(r.cloneNode(!0),n),r===l||!(r=r.nextSibling))break}else{s.innerHTML=o?`<svg>${e}</svg>`:e;const r=s.content;if(o){const e=r.firstChild;while(e.firstChild)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};function c(e,t,n){const o=e._vtc;o&&(t=(t?[t,...o]:[...o]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}function d(e,t,n){const r=e.style,l=(0,o.HD)(n);if(n&&!l){if(t&&!(0,o.HD)(t))for(const e in t)null==n[e]&&p(r,e,"");for(const e in n)p(r,e,n[e])}else{const o=r.display;l?t!==n&&(r.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(r.display=o)}}const f=/\s*!important$/;function p(e,t,n){if((0,o.kJ)(n))n.forEach((n=>p(e,t,n)));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const r=m(e,t);f.test(n)?e.setProperty((0,o.rs)(r),n.replace(f,""),"important"):e[r]=n}}const v=["Webkit","Moz","ms"],h={};function m(e,t){const n=h[t];if(n)return n;let r=(0,o._A)(t);if("filter"!==r&&r in e)return h[t]=r;r=(0,o.kC)(r);for(let o=0;o<v.length;o++){const n=v[o]+r;if(n in e)return h[t]=n}return t}const g="http://www.w3.org/1999/xlink";function b(e,t,n,r,l){if(r&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(g,t.slice(6,t.length)):e.setAttributeNS(g,t,n);else{const r=(0,o.Pq)(t);null==n||r&&!(0,o.yA)(n)?e.removeAttribute(t):e.setAttribute(t,r?"":n)}}function y(e,t,n,r,l,i,a){if("innerHTML"===t||"textContent"===t)return r&&a(r,l,i),void(e[t]=null==n?"":n);const s=e.tagName;if("value"===t&&"PROGRESS"!==s&&!s.includes("-")){e._value=n;const o="OPTION"===s?e.getAttribute("value"):e.value,r=null==n?"":n;return o!==r&&(e.value=r),void(null==n&&e.removeAttribute(t))}let u=!1;if(""===n||null==n){const r=typeof e[t];"boolean"===r?n=(0,o.yA)(n):null==n&&"string"===r?(n="",u=!0):"number"===r&&(n=0,u=!0)}try{e[t]=n}catch(c){0}u&&e.removeAttribute(t)}function _(e,t,n,o){e.addEventListener(t,n,o)}function w(e,t,n,o){e.removeEventListener(t,n,o)}function S(e,t,n,o,r=null){const l=e._vei||(e._vei={}),i=l[t];if(o&&i)i.value=o;else{const[n,a]=x(t);if(o){const i=l[t]=O(o,r);_(e,n,i,a)}else i&&(w(e,n,i,a),l[t]=void 0)}}const k=/(?:Once|Passive|Capture)$/;function x(e){let t;if(k.test(e)){let n;t={};while(n=e.match(k))e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}const n=":"===e[2]?e.slice(3):(0,o.rs)(e.slice(2));return[n,t]}let E=0;const C=Promise.resolve(),T=()=>E||(C.then((()=>E=0)),E=Date.now());function O(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();(0,r.$d)(F(e,n.value),t,5,[e])};return n.value=e,n.attached=T(),n}function F(e,t){if((0,o.kJ)(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e&&e(t)))}return t}const L=/^on[a-z]/,A=(e,t,n,r,l=!1,i,a,s,u)=>{"class"===t?c(e,r,l):"style"===t?d(e,n,r):(0,o.F7)(t)?(0,o.tR)(t)||S(e,t,n,r,a):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):P(e,t,r,l))?y(e,t,r,i,a,s,u):("true-value"===t?e._trueValue=r:"false-value"===t&&(e._falseValue=r),b(e,t,r,l))};function P(e,t,n,r){return r?"innerHTML"===t||"textContent"===t||!!(t in e&&L.test(t)&&(0,o.mf)(n)):"spellcheck"!==t&&"draggable"!==t&&"translate"!==t&&("form"!==t&&(("list"!==t||"INPUT"!==e.tagName)&&(("type"!==t||"TEXTAREA"!==e.tagName)&&((!L.test(t)||!(0,o.HD)(n))&&t in e))))}"undefined"!==typeof HTMLElement&&HTMLElement;const q="transition",R="animation",N=(e,{slots:t})=>(0,r.h)(r.P$,V(e),t);N.displayName="Transition";const M={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},I=N.props=(0,o.l7)({},r.nJ,M),$=(e,t=[])=>{(0,o.kJ)(e)?e.forEach((e=>e(...t))):e&&e(...t)},D=e=>!!e&&((0,o.kJ)(e)?e.some((e=>e.length>1)):e.length>1);function V(e){const t={};for(const o in e)o in M||(t[o]=e[o]);if(!1===e.css)return t;const{name:n="v",type:r,duration:l,enterFromClass:i=`${n}-enter-from`,enterActiveClass:a=`${n}-enter-active`,enterToClass:s=`${n}-enter-to`,appearFromClass:u=i,appearActiveClass:c=a,appearToClass:d=s,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:v=`${n}-leave-to`}=e,h=j(l),m=h&&h[0],g=h&&h[1],{onBeforeEnter:b,onEnter:y,onEnterCancelled:_,onLeave:w,onLeaveCancelled:S,onBeforeAppear:k=b,onAppear:x=y,onAppearCancelled:E=_}=t,C=(e,t,n)=>{z(e,t?d:s),z(e,t?c:a),n&&n()},T=(e,t)=>{e._isLeaving=!1,z(e,f),z(e,v),z(e,p),t&&t()},O=e=>(t,n)=>{const o=e?x:y,l=()=>C(t,e,n);$(o,[t,l]),U((()=>{z(t,e?u:i),H(t,e?d:s),D(o)||Z(t,r,m,l)}))};return(0,o.l7)(t,{onBeforeEnter(e){$(b,[e]),H(e,i),H(e,a)},onBeforeAppear(e){$(k,[e]),H(e,u),H(e,c)},onEnter:O(!1),onAppear:O(!0),onLeave(e,t){e._isLeaving=!0;const n=()=>T(e,t);H(e,f),X(),H(e,p),U((()=>{e._isLeaving&&(z(e,f),H(e,v),D(w)||Z(e,r,g,n))})),$(w,[e,n])},onEnterCancelled(e){C(e,!1),$(_,[e])},onAppearCancelled(e){C(e,!0),$(E,[e])},onLeaveCancelled(e){T(e),$(S,[e])}})}function j(e){if(null==e)return null;if((0,o.Kn)(e))return[B(e.enter),B(e.leave)];{const t=B(e);return[t,t]}}function B(e){const t=(0,o.He)(e);return t}function H(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e._vtc||(e._vtc=new Set)).add(t)}function z(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function U(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let W=0;function Z(e,t,n,o){const r=e._endId=++W,l=()=>{r===e._endId&&o()};if(n)return setTimeout(l,n);const{type:i,timeout:a,propCount:s}=K(e,t);if(!i)return o();const u=i+"end";let c=0;const d=()=>{e.removeEventListener(u,f),l()},f=t=>{t.target===e&&++c>=s&&d()};setTimeout((()=>{c<s&&d()}),a+1),e.addEventListener(u,f)}function K(e,t){const n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o(`${q}Delay`),l=o(`${q}Duration`),i=Y(r,l),a=o(`${R}Delay`),s=o(`${R}Duration`),u=Y(a,s);let c=null,d=0,f=0;t===q?i>0&&(c=q,d=i,f=l.length):t===R?u>0&&(c=R,d=u,f=s.length):(d=Math.max(i,u),c=d>0?i>u?q:R:null,f=c?c===q?l.length:s.length:0);const p=c===q&&/\b(transform|all)(,|$)/.test(o(`${q}Property`).toString());return{type:c,timeout:d,propCount:f,hasTransform:p}}function Y(e,t){while(e.length<t.length)e=e.concat(e);return Math.max(...t.map(((t,n)=>J(t)+J(e[n]))))}function J(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}function X(){return document.body.offsetHeight}const G=new WeakMap,Q=new WeakMap,ee={name:"TransitionGroup",props:(0,o.l7)({},I,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=(0,r.FN)(),o=(0,r.Y8)();let i,a;return(0,r.ic)((()=>{if(!i.length)return;const t=e.moveClass||`${e.name||"v"}-move`;if(!le(i[0].el,n.vnode.el,t))return;i.forEach(ne),i.forEach(oe);const o=i.filter(re);X(),o.forEach((e=>{const n=e.el,o=n.style;H(n,t),o.transform=o.webkitTransform=o.transitionDuration="";const r=n._moveCb=e=>{e&&e.target!==n||e&&!/transform$/.test(e.propertyName)||(n.removeEventListener("transitionend",r),n._moveCb=null,z(n,t))};n.addEventListener("transitionend",r)}))})),()=>{const s=(0,l.IU)(e),u=V(s);let c=s.tag||r.HY;i=a,a=t.default?(0,r.Q6)(t.default()):[];for(let e=0;e<a.length;e++){const t=a[e];null!=t.key&&(0,r.nK)(t,(0,r.U2)(t,u,o,n))}if(i)for(let e=0;e<i.length;e++){const t=i[e];(0,r.nK)(t,(0,r.U2)(t,u,o,n)),G.set(t,t.el.getBoundingClientRect())}return(0,r.Wm)(c,null,a)}}};ee.props;const te=ee;function ne(e){const t=e.el;t._moveCb&&t._moveCb(),t._enterCb&&t._enterCb()}function oe(e){Q.set(e,e.el.getBoundingClientRect())}function re(e){const t=G.get(e),n=Q.get(e),o=t.left-n.left,r=t.top-n.top;if(o||r){const t=e.el.style;return t.transform=t.webkitTransform=`translate(${o}px,${r}px)`,t.transitionDuration="0s",e}}function le(e,t,n){const o=e.cloneNode();e._vtc&&e._vtc.forEach((e=>{e.split(/\s+/).forEach((e=>e&&o.classList.remove(e)))})),n.split(/\s+/).forEach((e=>e&&o.classList.add(e))),o.style.display="none";const r=1===t.nodeType?t:t.parentNode;r.appendChild(o);const{hasTransform:l}=K(o);return r.removeChild(o),l}const ie={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},ae=(e,t)=>n=>{if(!("key"in n))return;const r=(0,o.rs)(n.key);return t.some((e=>e===r||ie[e]===r))?e(n):void 0};const se=(0,o.l7)({patchProp:A},u);let ue;function ce(){return ue||(ue=(0,r.Us)(se))}const de=(...e)=>{const t=ce().createApp(...e);const{mount:n}=t;return t.mount=e=>{const r=fe(e);if(!r)return;const l=t._component;(0,o.mf)(l)||l.render||l.template||(l.template=r.innerHTML),r.innerHTML="";const i=n(r,!1,r instanceof SVGElement);return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function fe(e){if((0,o.HD)(e)){const t=document.querySelector(e);return t}return e}},6970:(e,t,n)=>{"use strict";function o(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}n.d(t,{C_:()=>G,DM:()=>g,E9:()=>z,F7:()=>u,Gg:()=>L,HD:()=>w,He:()=>B,Kj:()=>y,Kn:()=>k,NO:()=>a,Nj:()=>V,Od:()=>f,PO:()=>O,Pq:()=>te,RI:()=>v,S0:()=>F,W7:()=>T,WV:()=>re,Z6:()=>l,_A:()=>q,_N:()=>m,aU:()=>$,dG:()=>i,e1:()=>W,fY:()=>o,h5:()=>j,hR:()=>I,hq:()=>le,ir:()=>D,j5:()=>Z,kC:()=>M,kJ:()=>h,kT:()=>r,l7:()=>d,mf:()=>_,rs:()=>N,tI:()=>x,tR:()=>c,vs:()=>Q,yA:()=>ne,yk:()=>S,zw:()=>ie});const r={},l=[],i=()=>{},a=()=>!1,s=/^on[^a-z]/,u=e=>s.test(e),c=e=>e.startsWith("onUpdate:"),d=Object.assign,f=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},p=Object.prototype.hasOwnProperty,v=(e,t)=>p.call(e,t),h=Array.isArray,m=e=>"[object Map]"===C(e),g=e=>"[object Set]"===C(e),b=e=>"[object Date]"===C(e),y=e=>"[object RegExp]"===C(e),_=e=>"function"===typeof e,w=e=>"string"===typeof e,S=e=>"symbol"===typeof e,k=e=>null!==e&&"object"===typeof e,x=e=>k(e)&&_(e.then)&&_(e.catch),E=Object.prototype.toString,C=e=>E.call(e),T=e=>C(e).slice(8,-1),O=e=>"[object Object]"===C(e),F=e=>w(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,L=o(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),A=e=>{const t=Object.create(null);return n=>{const o=t[n];return o||(t[n]=e(n))}},P=/-(\w)/g,q=A((e=>e.replace(P,((e,t)=>t?t.toUpperCase():"")))),R=/\B([A-Z])/g,N=A((e=>e.replace(R,"-$1").toLowerCase())),M=A((e=>e.charAt(0).toUpperCase()+e.slice(1))),I=A((e=>e?`on${M(e)}`:"")),$=(e,t)=>!Object.is(e,t),D=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},V=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},j=e=>{const t=parseFloat(e);return isNaN(t)?e:t},B=e=>{const t=w(e)?Number(e):NaN;return isNaN(t)?e:t};let H;const z=()=>H||(H="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:{});const U="Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt,console",W=o(U);function Z(e){if(h(e)){const t={};for(let n=0;n<e.length;n++){const o=e[n],r=w(o)?X(o):Z(o);if(r)for(const e in r)t[e]=r[e]}return t}return w(e)||k(e)?e:void 0}const K=/;(?![^(]*\))/g,Y=/:([^]+)/,J=/\/\*[^]*?\*\//g;function X(e){const t={};return e.replace(J,"").split(K).forEach((e=>{if(e){const n=e.split(Y);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function G(e){let t="";if(w(e))t=e;else if(h(e))for(let n=0;n<e.length;n++){const o=G(e[n]);o&&(t+=o+" ")}else if(k(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}function Q(e){if(!e)return null;let{class:t,style:n}=e;return t&&!w(t)&&(e.class=G(t)),n&&(e.style=Z(n)),e}const ee="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",te=o(ee);function ne(e){return!!e||""===e}function oe(e,t){if(e.length!==t.length)return!1;let n=!0;for(let o=0;n&&o<e.length;o++)n=re(e[o],t[o]);return n}function re(e,t){if(e===t)return!0;let n=b(e),o=b(t);if(n||o)return!(!n||!o)&&e.getTime()===t.getTime();if(n=S(e),o=S(t),n||o)return e===t;if(n=h(e),o=h(t),n||o)return!(!n||!o)&&oe(e,t);if(n=k(e),o=k(t),n||o){if(!n||!o)return!1;const r=Object.keys(e).length,l=Object.keys(t).length;if(r!==l)return!1;for(const n in e){const o=e.hasOwnProperty(n),r=t.hasOwnProperty(n);if(o&&!r||!o&&r||!re(e[n],t[n]))return!1}}return String(e)===String(t)}function le(e,t){return e.findIndex((e=>re(e,t)))}const ie=e=>w(e)?e:null==e?"":h(e)||k(e)&&(e.toString===E||!_(e.toString))?JSON.stringify(e,ae,2):String(e),ae=(e,t)=>t&&t.__v_isRef?ae(e,t.value):m(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n])=>(e[`${t} =>`]=n,e)),{})}:g(t)?{[`Set(${t.size})`]:[...t.values()]}:!k(t)||h(t)||O(t)?t:String(t)},9379:(e,t,n)=>{"use strict";n.d(t,{Z:()=>A});n(9665);var o=n(9835),r=n(499),l=n(1957),i=n(2857),a=n(3940),s=n(1722);const u={left:"start",center:"center",right:"end",between:"between",around:"around",evenly:"evenly",stretch:"stretch"},c=Object.keys(u),d={align:{type:String,validator:e=>c.includes(e)}};function f(e){return(0,o.Fl)((()=>{const t=void 0===e.align?!0===e.vertical?"stretch":"left":e.align;return`${!0===e.vertical?"items":"justify"}-${u[t]}`}))}var p=n(244),v=n(945);const h={none:0,xs:4,sm:8,md:16,lg:24,xl:32},m={xs:8,sm:10,md:14,lg:20,xl:24},g=["button","submit","reset"],b=/[^\s]\/[^\s]/,y=["flat","outline","push","unelevated"],_=(e,t)=>!0===e.flat?"flat":!0===e.outline?"outline":!0===e.push?"push":!0===e.unelevated?"unelevated":t,w={...p.LU,...v.$,type:{type:String,default:"button"},label:[Number,String],icon:String,iconRight:String,...y.reduce(((e,t)=>(e[t]=Boolean)&&e),{}),square:Boolean,round:Boolean,rounded:Boolean,glossy:Boolean,size:String,fab:Boolean,fabMini:Boolean,padding:String,color:String,textColor:String,noCaps:Boolean,noWrap:Boolean,dense:Boolean,tabindex:[Number,String],ripple:{type:[Boolean,Object],default:!0},align:{...d.align,default:"center"},stack:Boolean,stretch:Boolean,loading:{type:Boolean,default:null},disable:Boolean};function S(e){const t=(0,p.ZP)(e,m),n=f(e),{hasRouterLink:r,hasLink:l,linkTag:i,linkAttrs:a,navigateOnClick:s}=(0,v.Z)({fallbackTag:"button"}),u=(0,o.Fl)((()=>{const n=!1===e.fab&&!1===e.fabMini?t.value:{};return void 0!==e.padding?Object.assign({},n,{padding:e.padding.split(/\s+/).map((e=>e in h?h[e]+"px":e)).join(" "),minWidth:"0",minHeight:"0"}):n})),c=(0,o.Fl)((()=>!0===e.rounded||!0===e.fab||!0===e.fabMini)),d=(0,o.Fl)((()=>!0!==e.disable&&!0!==e.loading)),y=(0,o.Fl)((()=>!0===d.value?e.tabindex||0:-1)),w=(0,o.Fl)((()=>_(e,"standard"))),S=(0,o.Fl)((()=>{const t={tabindex:y.value};return!0===l.value?Object.assign(t,a.value):!0===g.includes(e.type)&&(t.type=e.type),"a"===i.value?(!0===e.disable?t["aria-disabled"]="true":void 0===t.href&&(t.role="button"),!0!==r.value&&!0===b.test(e.type)&&(t.type=e.type)):!0===e.disable&&(t.disabled="",t["aria-disabled"]="true"),!0===e.loading&&void 0!==e.percentage&&Object.assign(t,{role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":e.percentage}),t})),k=(0,o.Fl)((()=>{let t;void 0!==e.color?t=!0===e.flat||!0===e.outline?`text-${e.textColor||e.color}`:`bg-${e.color} text-${e.textColor||"white"}`:e.textColor&&(t=`text-${e.textColor}`);const n=!0===e.round?"round":"rectangle"+(!0===c.value?" q-btn--rounded":!0===e.square?" q-btn--square":"");return`q-btn--${w.value} q-btn--${n}`+(void 0!==t?" "+t:"")+(!0===d.value?" q-btn--actionable q-focusable q-hoverable":!0===e.disable?" disabled":"")+(!0===e.fab?" q-btn--fab":!0===e.fabMini?" q-btn--fab-mini":"")+(!0===e.noCaps?" q-btn--no-uppercase":"")+(!0===e.dense?" q-btn--dense":"")+(!0===e.stretch?" no-border-radius self-stretch":"")+(!0===e.glossy?" glossy":"")+(e.square?" q-btn--square":"")})),x=(0,o.Fl)((()=>n.value+(!0===e.stack?" column":" row")+(!0===e.noWrap?" no-wrap text-no-wrap":"")+(!0===e.loading?" q-btn__content--hidden":"")));return{classes:k,style:u,innerClasses:x,attributes:S,hasLink:l,linkTag:i,navigateOnClick:s,isActionable:d}}var k=n(5987),x=n(2026),E=n(1384),C=n(1705);const{passiveCapture:T}=E.listenOpts;let O=null,F=null,L=null;const A=(0,k.L)({name:"QBtn",props:{...w,percentage:Number,darkPercentage:Boolean,onTouchstart:[Function,Array]},emits:["click","keydown","mousedown","keyup"],setup(e,{slots:t,emit:n}){const{proxy:u}=(0,o.FN)(),{classes:c,style:d,innerClasses:f,attributes:p,hasLink:v,linkTag:h,navigateOnClick:m,isActionable:g}=S(e),b=(0,r.iH)(null),y=(0,r.iH)(null);let _,w=null,k=null;const A=(0,o.Fl)((()=>void 0!==e.label&&null!==e.label&&""!==e.label)),P=(0,o.Fl)((()=>!0!==e.disable&&!1!==e.ripple&&{keyCodes:!0===v.value?[13,32]:[13],...!0===e.ripple?{}:e.ripple})),q=(0,o.Fl)((()=>({center:e.round}))),R=(0,o.Fl)((()=>{const t=Math.max(0,Math.min(100,e.percentage));return t>0?{transition:"transform 0.6s",transform:`translateX(${t-100}%)`}:{}})),N=(0,o.Fl)((()=>{if(!0===e.loading)return{onMousedown:H,onTouchstart:H,onClick:H,onKeydown:H,onKeyup:H};if(!0===g.value){const t={onClick:I,onKeydown:$,onMousedown:V};if(!0===u.$q.platform.has.touch){const n=void 0!==e.onTouchstart?"":"Passive";t[`onTouchstart${n}`]=D}return t}return{onClick:E.NS}})),M=(0,o.Fl)((()=>({ref:b,class:"q-btn q-btn-item non-selectable no-outline "+c.value,style:d.value,...p.value,...N.value})));function I(t){if(null!==b.value){if(void 0!==t){if(!0===t.defaultPrevented)return;const n=document.activeElement;if("submit"===e.type&&n!==document.body&&!1===b.value.contains(n)&&!1===n.contains(b.value)){b.value.focus();const e=()=>{document.removeEventListener("keydown",E.NS,!0),document.removeEventListener("keyup",e,T),null!==b.value&&b.value.removeEventListener("blur",e,T)};document.addEventListener("keydown",E.NS,!0),document.addEventListener("keyup",e,T),b.value.addEventListener("blur",e,T)}}m(t)}}function $(e){null!==b.value&&(n("keydown",e),!0===(0,C.So)(e,[13,32])&&F!==b.value&&(null!==F&&B(),!0!==e.defaultPrevented&&(b.value.focus(),F=b.value,b.value.classList.add("q-btn--active"),document.addEventListener("keyup",j,!0),b.value.addEventListener("blur",j,T)),(0,E.NS)(e)))}function D(e){null!==b.value&&(n("touchstart",e),!0!==e.defaultPrevented&&(O!==b.value&&(null!==O&&B(),O=b.value,w=e.target,w.addEventListener("touchcancel",j,T),w.addEventListener("touchend",j,T)),_=!0,null!==k&&clearTimeout(k),k=setTimeout((()=>{k=null,_=!1}),200)))}function V(e){null!==b.value&&(e.qSkipRipple=!0===_,n("mousedown",e),!0!==e.defaultPrevented&&L!==b.value&&(null!==L&&B(),L=b.value,b.value.classList.add("q-btn--active"),document.addEventListener("mouseup",j,T)))}function j(e){if(null!==b.value&&(void 0===e||"blur"!==e.type||document.activeElement!==b.value)){if(void 0!==e&&"keyup"===e.type){if(F===b.value&&!0===(0,C.So)(e,[13,32])){const t=new MouseEvent("click",e);t.qKeyEvent=!0,!0===e.defaultPrevented&&(0,E.X$)(t),!0===e.cancelBubble&&(0,E.sT)(t),b.value.dispatchEvent(t),(0,E.NS)(e),e.qKeyEvent=!0}n("keyup",e)}B()}}function B(e){const t=y.value;!0===e||O!==b.value&&L!==b.value||null===t||t===document.activeElement||(t.setAttribute("tabindex",-1),t.focus()),O===b.value&&(null!==w&&(w.removeEventListener("touchcancel",j,T),w.removeEventListener("touchend",j,T)),O=w=null),L===b.value&&(document.removeEventListener("mouseup",j,T),L=null),F===b.value&&(document.removeEventListener("keyup",j,!0),null!==b.value&&b.value.removeEventListener("blur",j,T),F=null),null!==b.value&&b.value.classList.remove("q-btn--active")}function H(e){(0,E.NS)(e),e.qSkipRipple=!0}return(0,o.Jd)((()=>{B(!0)})),Object.assign(u,{click:I}),()=>{let n=[];void 0!==e.icon&&n.push((0,o.h)(i.Z,{name:e.icon,left:!1===e.stack&&!0===A.value,role:"img","aria-hidden":"true"})),!0===A.value&&n.push((0,o.h)("span",{class:"block"},[e.label])),n=(0,x.vs)(t.default,n),void 0!==e.iconRight&&!1===e.round&&n.push((0,o.h)(i.Z,{name:e.iconRight,right:!1===e.stack&&!0===A.value,role:"img","aria-hidden":"true"}));const r=[(0,o.h)("span",{class:"q-focus-helper",ref:y})];return!0===e.loading&&void 0!==e.percentage&&r.push((0,o.h)("span",{class:"q-btn__progress absolute-full overflow-hidden"+(!0===e.darkPercentage?" q-btn__progress--dark":"")},[(0,o.h)("span",{class:"q-btn__progress-indicator fit block",style:R.value})])),r.push((0,o.h)("span",{class:"q-btn__content text-center col items-center q-anchor--skip "+f.value},n)),null!==e.loading&&r.push((0,o.h)(l.uT,{name:"q-transition--fade"},(()=>!0===e.loading?[(0,o.h)("span",{key:"loading",class:"absolute-full flex flex-center"},void 0!==t.loading?t.loading():[(0,o.h)(a.Z)])]:null))),(0,o.wy)((0,o.h)(h.value,M.value,r),[[s.Z,P.value,void 0,q.value]])}}})},1221:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var o=n(9835),r=n(2857),l=n(5987),i=n(1926);const a=(0,o.h)("div",{key:"svg",class:"q-checkbox__bg absolute"},[(0,o.h)("svg",{class:"q-checkbox__svg fit absolute-full",viewBox:"0 0 24 24"},[(0,o.h)("path",{class:"q-checkbox__truthy",fill:"none",d:"M1.73,12.91 8.1,19.28 22.79,4.59"}),(0,o.h)("path",{class:"q-checkbox__indet",d:"M4,14H20V10H4"})])]),s=(0,l.L)({name:"QCheckbox",props:i.Fz,emits:i.ZB,setup(e){function t(t,n){const l=(0,o.Fl)((()=>(!0===t.value?e.checkedIcon:!0===n.value?e.indeterminateIcon:e.uncheckedIcon)||null));return()=>null!==l.value?[(0,o.h)("div",{key:"icon",class:"q-checkbox__icon-container absolute-full flex flex-center no-wrap"},[(0,o.h)(r.Z,{class:"q-checkbox__icon",name:l.value})])]:[a]}return(0,i.ZP)("checkbox",t)}})},1926:(e,t,n)=>{"use strict";n.d(t,{Fz:()=>f,ZB:()=>p,ZP:()=>v});n(9665);var o=n(9835),r=n(499),l=n(8234),i=n(244),a=n(5917),s=n(9256),u=n(9480),c=n(1384),d=n(2026);const f={...l.S,...i.LU,...s.Fz,modelValue:{required:!0,default:null},val:{},trueValue:{default:!0},falseValue:{default:!1},indeterminateValue:{default:null},checkedIcon:String,uncheckedIcon:String,indeterminateIcon:String,toggleOrder:{type:String,validator:e=>"tf"===e||"ft"===e},toggleIndeterminate:Boolean,label:String,leftLabel:Boolean,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},p=["update:modelValue"];function v(e,t){const{props:n,slots:f,emit:p,proxy:v}=(0,o.FN)(),{$q:h}=v,m=(0,l.Z)(n,h),g=(0,r.iH)(null),{refocusTargetEl:b,refocusTarget:y}=(0,a.Z)(n,g),_=(0,i.ZP)(n,u.Z),w=(0,o.Fl)((()=>void 0!==n.val&&Array.isArray(n.modelValue))),S=(0,o.Fl)((()=>{const e=(0,r.IU)(n.val);return!0===w.value?n.modelValue.findIndex((t=>(0,r.IU)(t)===e)):-1})),k=(0,o.Fl)((()=>!0===w.value?S.value>-1:(0,r.IU)(n.modelValue)===(0,r.IU)(n.trueValue))),x=(0,o.Fl)((()=>!0===w.value?-1===S.value:(0,r.IU)(n.modelValue)===(0,r.IU)(n.falseValue))),E=(0,o.Fl)((()=>!1===k.value&&!1===x.value)),C=(0,o.Fl)((()=>!0===n.disable?-1:n.tabindex||0)),T=(0,o.Fl)((()=>`q-${e} cursor-pointer no-outline row inline no-wrap items-center`+(!0===n.disable?" disabled":"")+(!0===m.value?` q-${e}--dark`:"")+(!0===n.dense?` q-${e}--dense`:"")+(!0===n.leftLabel?" reverse":""))),O=(0,o.Fl)((()=>{const t=!0===k.value?"truthy":!0===x.value?"falsy":"indet",o=void 0===n.color||!0!==n.keepColor&&("toggle"===e?!0!==k.value:!0===x.value)?"":` text-${n.color}`;return`q-${e}__inner relative-position non-selectable q-${e}__inner--${t}${o}`})),F=(0,o.Fl)((()=>{const e={type:"checkbox"};return void 0!==n.name&&Object.assign(e,{".checked":k.value,"^checked":!0===k.value?"checked":void 0,name:n.name,value:!0===w.value?n.val:n.trueValue}),e})),L=(0,s.eX)(F),A=(0,o.Fl)((()=>{const t={tabindex:C.value,role:"toggle"===e?"switch":"checkbox","aria-label":n.label,"aria-checked":!0===E.value?"mixed":!0===k.value?"true":"false"};return!0===n.disable&&(t["aria-disabled"]="true"),t}));function P(e){void 0!==e&&((0,c.NS)(e),y(e)),!0!==n.disable&&p("update:modelValue",q(),e)}function q(){if(!0===w.value){if(!0===k.value){const e=n.modelValue.slice();return e.splice(S.value,1),e}return n.modelValue.concat([n.val])}if(!0===k.value){if("ft"!==n.toggleOrder||!1===n.toggleIndeterminate)return n.falseValue}else{if(!0!==x.value)return"ft"!==n.toggleOrder?n.trueValue:n.falseValue;if("ft"===n.toggleOrder||!1===n.toggleIndeterminate)return n.trueValue}return n.indeterminateValue}function R(e){13!==e.keyCode&&32!==e.keyCode||(0,c.NS)(e)}function N(e){13!==e.keyCode&&32!==e.keyCode||P(e)}const M=t(k,E);return Object.assign(v,{toggle:P}),()=>{const t=M();!0!==n.disable&&L(t,"unshift",` q-${e}__native absolute q-ma-none q-pa-none`);const r=[(0,o.h)("div",{class:O.value,style:_.value,"aria-hidden":"true"},t)];null!==b.value&&r.push(b.value);const l=void 0!==n.label?(0,d.vs)(f.default,[n.label]):(0,d.KR)(f.default);return void 0!==l&&r.push((0,o.h)("div",{class:`q-${e}__label q-anchor--skip`},l)),(0,o.h)("div",{ref:g,class:T.value,...A.value,onClick:P,onKeydown:R,onKeyup:N},r)}}},7691:(e,t,n)=>{"use strict";n.d(t,{Z:()=>f});n(9665);var o=n(9835),r=n(2857),l=n(1722),i=n(8234),a=n(244),s=n(5987),u=n(1384),c=n(2026);const d={xs:8,sm:10,md:14,lg:20,xl:24},f=(0,s.L)({name:"QChip",props:{...i.S,...a.LU,dense:Boolean,icon:String,iconRight:String,iconRemove:String,iconSelected:String,label:[String,Number],color:String,textColor:String,modelValue:{type:Boolean,default:!0},selected:{type:Boolean,default:null},square:Boolean,outline:Boolean,clickable:Boolean,removable:Boolean,removeAriaLabel:String,tabindex:[String,Number],disable:Boolean,ripple:{type:[Boolean,Object],default:!0}},emits:["update:modelValue","update:selected","remove","click"],setup(e,{slots:t,emit:n}){const{proxy:{$q:s}}=(0,o.FN)(),f=(0,i.Z)(e,s),p=(0,a.ZP)(e,d),v=(0,o.Fl)((()=>!0===e.selected||void 0!==e.icon)),h=(0,o.Fl)((()=>!0===e.selected?e.iconSelected||s.iconSet.chip.selected:e.icon)),m=(0,o.Fl)((()=>e.iconRemove||s.iconSet.chip.remove)),g=(0,o.Fl)((()=>!1===e.disable&&(!0===e.clickable||null!==e.selected))),b=(0,o.Fl)((()=>{const t=!0===e.outline&&e.color||e.textColor;return"q-chip row inline no-wrap items-center"+(!1===e.outline&&void 0!==e.color?` bg-${e.color}`:"")+(t?` text-${t} q-chip--colored`:"")+(!0===e.disable?" disabled":"")+(!0===e.dense?" q-chip--dense":"")+(!0===e.outline?" q-chip--outline":"")+(!0===e.selected?" q-chip--selected":"")+(!0===g.value?" q-chip--clickable cursor-pointer non-selectable q-hoverable":"")+(!0===e.square?" q-chip--square":"")+(!0===f.value?" q-chip--dark q-dark":"")})),y=(0,o.Fl)((()=>{const t=!0===e.disable?{tabindex:-1,"aria-disabled":"true"}:{tabindex:e.tabindex||0},n={...t,role:"button","aria-hidden":"false","aria-label":e.removeAriaLabel||s.lang.label.remove};return{chip:t,remove:n}}));function _(e){13===e.keyCode&&w(e)}function w(t){e.disable||(n("update:selected",!e.selected),n("click",t))}function S(t){void 0!==t.keyCode&&13!==t.keyCode||((0,u.NS)(t),!1===e.disable&&(n("update:modelValue",!1),n("remove")))}function k(){const n=[];!0===g.value&&n.push((0,o.h)("div",{class:"q-focus-helper"})),!0===v.value&&n.push((0,o.h)(r.Z,{class:"q-chip__icon q-chip__icon--left",name:h.value}));const l=void 0!==e.label?[(0,o.h)("div",{class:"ellipsis"},[e.label])]:void 0;return n.push((0,o.h)("div",{class:"q-chip__content col row no-wrap items-center q-anchor--skip"},(0,c.pf)(t.default,l))),e.iconRight&&n.push((0,o.h)(r.Z,{class:"q-chip__icon q-chip__icon--right",name:e.iconRight})),!0===e.removable&&n.push((0,o.h)(r.Z,{class:"q-chip__icon q-chip__icon--remove cursor-pointer",name:m.value,...y.value.remove,onClick:S,onKeyup:S})),n}return()=>{if(!1===e.modelValue)return;const t={class:b.value,style:p.value};return!0===g.value&&Object.assign(t,y.value.chip,{onClick:w,onKeyup:_}),(0,c.Jl)("div",t,k(),"ripple",!1!==e.ripple&&!0!==e.disable,(()=>[[l.Z,e.ripple]]))}}})},2290:(e,t,n)=>{"use strict";n.d(t,{Z:()=>ne});n(9665),n(6890);var o=n(9835),r=n(499),l=n(2873),i=n(9256),a=n(8234),s=n(321),u=n(1384),c=n(4680),d=n(2026);const f="q-slider__marker-labels",p=e=>({value:e}),v=({marker:e})=>(0,o.h)("div",{key:e.value,style:e.style,class:e.classes},e.label),h=[34,37,40,33,39,38],m={...a.S,...i.Fz,min:{type:Number,default:0},max:{type:Number,default:100},innerMin:Number,innerMax:Number,step:{type:Number,default:1,validator:e=>e>=0},snap:Boolean,vertical:Boolean,reverse:Boolean,hideSelection:Boolean,color:String,markerLabelsClass:String,label:Boolean,labelColor:String,labelTextColor:String,labelAlways:Boolean,switchLabelSide:Boolean,markers:[Boolean,Number],markerLabels:[Boolean,Array,Object,Function],switchMarkerLabelsSide:Boolean,trackImg:String,trackColor:String,innerTrackImg:String,innerTrackColor:String,selectionColor:String,selectionImg:String,thumbSize:{type:String,default:"20px"},trackSize:{type:String,default:"4px"},disable:Boolean,readonly:Boolean,dense:Boolean,tabindex:[String,Number],thumbColor:String,thumbPath:{type:String,default:"M 4, 10 a 6,6 0 1,0 12,0 a 6,6 0 1,0 -12,0"}},g=["pan","update:modelValue","change"];function b({updateValue:e,updatePosition:t,getDragging:n,formAttrs:m}){const{props:g,emit:b,slots:y,proxy:{$q:_}}=(0,o.FN)(),w=(0,a.Z)(g,_),S=(0,i.eX)(m),k=(0,r.iH)(!1),x=(0,r.iH)(!1),E=(0,r.iH)(!1),C=(0,r.iH)(!1),T=(0,o.Fl)((()=>!0===g.vertical?"--v":"--h")),O=(0,o.Fl)((()=>"-"+(!0===g.switchLabelSide?"switched":"standard"))),F=(0,o.Fl)((()=>!0===g.vertical?!0===g.reverse:g.reverse!==(!0===_.lang.rtl))),L=(0,o.Fl)((()=>!0===isNaN(g.innerMin)||g.innerMin<g.min?g.min:g.innerMin)),A=(0,o.Fl)((()=>!0===isNaN(g.innerMax)||g.innerMax>g.max?g.max:g.innerMax)),P=(0,o.Fl)((()=>!0!==g.disable&&!0!==g.readonly&&L.value<A.value)),q=(0,o.Fl)((()=>(String(g.step).trim().split(".")[1]||"").length)),R=(0,o.Fl)((()=>0===g.step?1:g.step)),N=(0,o.Fl)((()=>!0===P.value?g.tabindex||0:-1)),M=(0,o.Fl)((()=>g.max-g.min)),I=(0,o.Fl)((()=>A.value-L.value)),$=(0,o.Fl)((()=>ie(L.value))),D=(0,o.Fl)((()=>ie(A.value))),V=(0,o.Fl)((()=>!0===g.vertical?!0===F.value?"bottom":"top":!0===F.value?"right":"left")),j=(0,o.Fl)((()=>!0===g.vertical?"height":"width")),B=(0,o.Fl)((()=>!0===g.vertical?"width":"height")),H=(0,o.Fl)((()=>!0===g.vertical?"vertical":"horizontal")),z=(0,o.Fl)((()=>{const e={role:"slider","aria-valuemin":L.value,"aria-valuemax":A.value,"aria-orientation":H.value,"data-step":g.step};return!0===g.disable?e["aria-disabled"]="true":!0===g.readonly&&(e["aria-readonly"]="true"),e})),U=(0,o.Fl)((()=>`q-slider q-slider${T.value} q-slider--${!0===k.value?"":"in"}active inline no-wrap `+(!0===g.vertical?"row":"column")+(!0===g.disable?" disabled":" q-slider--enabled"+(!0===P.value?" q-slider--editable":""))+("both"===E.value?" q-slider--focus":"")+(g.label||!0===g.labelAlways?" q-slider--label":"")+(!0===g.labelAlways?" q-slider--label-always":"")+(!0===w.value?" q-slider--dark":"")+(!0===g.dense?" q-slider--dense q-slider--dense"+T.value:"")));function W(e){const t="q-slider__"+e;return`${t} ${t}${T.value} ${t}${T.value}${O.value}`}function Z(e){const t="q-slider__"+e;return`${t} ${t}${T.value}`}const K=(0,o.Fl)((()=>{const e=g.selectionColor||g.color;return"q-slider__selection absolute"+(void 0!==e?` text-${e}`:"")})),Y=(0,o.Fl)((()=>Z("markers")+" absolute overflow-hidden")),J=(0,o.Fl)((()=>Z("track-container"))),X=(0,o.Fl)((()=>W("pin"))),G=(0,o.Fl)((()=>W("label"))),Q=(0,o.Fl)((()=>W("text-container"))),ee=(0,o.Fl)((()=>W("marker-labels-container")+(void 0!==g.markerLabelsClass?` ${g.markerLabelsClass}`:""))),te=(0,o.Fl)((()=>"q-slider__track relative-position no-outline"+(void 0!==g.trackColor?` bg-${g.trackColor}`:""))),ne=(0,o.Fl)((()=>{const e={[B.value]:g.trackSize};return void 0!==g.trackImg&&(e.backgroundImage=`url(${g.trackImg}) !important`),e})),oe=(0,o.Fl)((()=>"q-slider__inner absolute"+(void 0!==g.innerTrackColor?` bg-${g.innerTrackColor}`:""))),re=(0,o.Fl)((()=>{const e={[V.value]:100*$.value+"%",[j.value]:100*(D.value-$.value)+"%"};return void 0!==g.innerTrackImg&&(e.backgroundImage=`url(${g.innerTrackImg}) !important`),e}));function le(e){const{min:t,max:n,step:o}=g;let r=t+e*(n-t);if(o>0){const e=(r-t)%o;r+=(Math.abs(e)>=o/2?(e<0?-1:1)*o:0)-e}return q.value>0&&(r=parseFloat(r.toFixed(q.value))),(0,s.vX)(r,L.value,A.value)}function ie(e){return 0===M.value?0:(e-g.min)/M.value}function ae(e,t){const n=(0,u.FK)(e),o=!0===g.vertical?(0,s.vX)((n.top-t.top)/t.height,0,1):(0,s.vX)((n.left-t.left)/t.width,0,1);return(0,s.vX)(!0===F.value?1-o:o,$.value,D.value)}const se=(0,o.Fl)((()=>!0===(0,c.hj)(g.markers)?g.markers:R.value)),ue=(0,o.Fl)((()=>{const e=[],t=se.value,n=g.max;let o=g.min;do{e.push(o),o+=t}while(o<n);return e.push(n),e})),ce=(0,o.Fl)((()=>{const e=` ${f}${T.value}-`;return f+`${e}${!0===g.switchMarkerLabelsSide?"switched":"standard"}`+`${e}${!0===F.value?"rtl":"ltr"}`})),de=(0,o.Fl)((()=>!1===g.markerLabels?null:ve(g.markerLabels).map(((e,t)=>({index:t,value:e.value,label:e.label||e.value,classes:ce.value+(void 0!==e.classes?" "+e.classes:""),style:{...he(e.value),...e.style||{}}}))))),fe=(0,o.Fl)((()=>({markerList:de.value,markerMap:me.value,classes:ce.value,getStyle:he}))),pe=(0,o.Fl)((()=>{if(0!==I.value){const e=100*se.value/I.value;return{...re.value,backgroundSize:!0===g.vertical?`2px ${e}%`:`${e}% 2px`}}return null}));function ve(e){if(!1===e)return null;if(!0===e)return ue.value.map(p);if("function"===typeof e)return ue.value.map((t=>{const n=e(t);return!0===(0,c.Kn)(n)?{...n,value:t}:{value:t,label:n}}));const t=({value:e})=>e>=g.min&&e<=g.max;return!0===Array.isArray(e)?e.map((e=>!0===(0,c.Kn)(e)?e:{value:e})).filter(t):Object.keys(e).map((t=>{const n=e[t],o=Number(t);return!0===(0,c.Kn)(n)?{...n,value:o}:{value:o,label:n}})).filter(t)}function he(e){return{[V.value]:100*(e-g.min)/M.value+"%"}}const me=(0,o.Fl)((()=>{if(!1===g.markerLabels)return null;const e={};return de.value.forEach((t=>{e[t.value]=t})),e}));function ge(){if(void 0!==y["marker-label-group"])return y["marker-label-group"](fe.value);const e=y["marker-label"]||v;return de.value.map((t=>e({marker:t,...fe.value})))}const be=(0,o.Fl)((()=>[[l.Z,ye,void 0,{[H.value]:!0,prevent:!0,stop:!0,mouse:!0,mouseAllDir:!0}]]));function ye(o){!0===o.isFinal?(void 0!==C.value&&(t(o.evt),!0===o.touch&&e(!0),C.value=void 0,b("pan","end")),k.value=!1,E.value=!1):!0===o.isFirst?(C.value=n(o.evt),t(o.evt),e(),k.value=!0,b("pan","start")):(t(o.evt),e())}function _e(){E.value=!1}function we(o){t(o,n(o)),e(),x.value=!0,k.value=!0,document.addEventListener("mouseup",Se,!0)}function Se(){x.value=!1,k.value=!1,e(!0),_e(),document.removeEventListener("mouseup",Se,!0)}function ke(o){t(o,n(o)),e(!0)}function xe(t){h.includes(t.keyCode)&&e(!0)}function Ee(e){if(!0===g.vertical)return null;const t=_.lang.rtl!==g.reverse?1-e:e;return{transform:`translateX(calc(${2*t-1} * ${g.thumbSize} / 2 + ${50-100*t}%))`}}function Ce(e){const t=(0,o.Fl)((()=>!1!==x.value||E.value!==e.focusValue&&"both"!==E.value?"":" q-slider--focus")),n=(0,o.Fl)((()=>`q-slider__thumb q-slider__thumb${T.value} q-slider__thumb${T.value}-${!0===F.value?"rtl":"ltr"} absolute non-selectable`+t.value+(void 0!==e.thumbColor.value?` text-${e.thumbColor.value}`:""))),r=(0,o.Fl)((()=>({width:g.thumbSize,height:g.thumbSize,[V.value]:100*e.ratio.value+"%",zIndex:E.value===e.focusValue?2:void 0}))),l=(0,o.Fl)((()=>void 0!==e.labelColor.value?` text-${e.labelColor.value}`:"")),i=(0,o.Fl)((()=>Ee(e.ratio.value))),a=(0,o.Fl)((()=>"q-slider__text"+(void 0!==e.labelTextColor.value?` text-${e.labelTextColor.value}`:"")));return()=>{const t=[(0,o.h)("svg",{class:"q-slider__thumb-shape absolute-full",viewBox:"0 0 20 20","aria-hidden":"true"},[(0,o.h)("path",{d:g.thumbPath})]),(0,o.h)("div",{class:"q-slider__focus-ring fit"})];return!0!==g.label&&!0!==g.labelAlways||(t.push((0,o.h)("div",{class:X.value+" absolute fit no-pointer-events"+l.value},[(0,o.h)("div",{class:G.value,style:{minWidth:g.thumbSize}},[(0,o.h)("div",{class:Q.value,style:i.value},[(0,o.h)("span",{class:a.value},e.label.value)])])])),void 0!==g.name&&!0!==g.disable&&S(t,"push")),(0,o.h)("div",{class:n.value,style:r.value,...e.getNodeData()},t)}}function Te(e,t,n,r){const l=[];"transparent"!==g.innerTrackColor&&l.push((0,o.h)("div",{key:"inner",class:oe.value,style:re.value})),"transparent"!==g.selectionColor&&l.push((0,o.h)("div",{key:"selection",class:K.value,style:e.value})),!1!==g.markers&&l.push((0,o.h)("div",{key:"marker",class:Y.value,style:pe.value})),r(l);const i=[(0,d.Jl)("div",{key:"trackC",class:J.value,tabindex:t.value,...n.value},[(0,o.h)("div",{class:te.value,style:ne.value},l)],"slide",P.value,(()=>be.value))];if(!1!==g.markerLabels){const e=!0===g.switchMarkerLabelsSide?"unshift":"push";i[e]((0,o.h)("div",{key:"markerL",class:ee.value},ge()))}return i}return(0,o.Jd)((()=>{document.removeEventListener("mouseup",Se,!0)})),{state:{active:k,focus:E,preventFocus:x,dragging:C,editable:P,classes:U,tabindex:N,attributes:z,step:R,decimals:q,trackLen:M,innerMin:L,innerMinRatio:$,innerMax:A,innerMaxRatio:D,positionProp:V,sizeProp:j,isReversed:F},methods:{onActivate:we,onMobileClick:ke,onBlur:_e,onKeyup:xe,getContent:Te,getThumbRenderFn:Ce,convertRatioToModel:le,convertModelToRatio:ie,getDraggingRatio:ae}}}var y=n(5987);const _=()=>({}),w=(0,y.L)({name:"QSlider",props:{...m,modelValue:{required:!0,default:null,validator:e=>"number"===typeof e||null===e},labelValue:[String,Number]},emits:g,setup(e,{emit:t}){const{proxy:{$q:n}}=(0,o.FN)(),{state:l,methods:a}=b({updateValue:S,updatePosition:x,getDragging:k,formAttrs:(0,i.Vt)(e)}),c=(0,r.iH)(null),d=(0,r.iH)(0),f=(0,r.iH)(0);function p(){f.value=null===e.modelValue?l.innerMin.value:(0,s.vX)(e.modelValue,l.innerMin.value,l.innerMax.value)}(0,o.YP)((()=>`${e.modelValue}|${l.innerMin.value}|${l.innerMax.value}`),p),p();const v=(0,o.Fl)((()=>a.convertModelToRatio(f.value))),m=(0,o.Fl)((()=>!0===l.active.value?d.value:v.value)),g=(0,o.Fl)((()=>{const t={[l.positionProp.value]:100*l.innerMinRatio.value+"%",[l.sizeProp.value]:100*(m.value-l.innerMinRatio.value)+"%"};return void 0!==e.selectionImg&&(t.backgroundImage=`url(${e.selectionImg}) !important`),t})),y=a.getThumbRenderFn({focusValue:!0,getNodeData:_,ratio:m,label:(0,o.Fl)((()=>void 0!==e.labelValue?e.labelValue:f.value)),thumbColor:(0,o.Fl)((()=>e.thumbColor||e.color)),labelColor:(0,o.Fl)((()=>e.labelColor)),labelTextColor:(0,o.Fl)((()=>e.labelTextColor))}),w=(0,o.Fl)((()=>!0!==l.editable.value?{}:!0===n.platform.is.mobile?{onClick:a.onMobileClick}:{onMousedown:a.onActivate,onFocus:E,onBlur:a.onBlur,onKeydown:C,onKeyup:a.onKeyup}));function S(n){f.value!==e.modelValue&&t("update:modelValue",f.value),!0===n&&t("change",f.value)}function k(){return c.value.getBoundingClientRect()}function x(t,n=l.dragging.value){const o=a.getDraggingRatio(t,n);f.value=a.convertRatioToModel(o),d.value=!0!==e.snap||0===e.step?o:a.convertModelToRatio(f.value)}function E(){l.focus.value=!0}function C(t){if(!h.includes(t.keyCode))return;(0,u.NS)(t);const n=([34,33].includes(t.keyCode)?10:1)*l.step.value,o=([34,37,40].includes(t.keyCode)?-1:1)*(!0===l.isReversed.value?-1:1)*(!0===e.vertical?-1:1)*n;f.value=(0,s.vX)(parseFloat((f.value+o).toFixed(l.decimals.value)),l.innerMin.value,l.innerMax.value),S()}return()=>{const t=a.getContent(g,l.tabindex,w,(e=>{e.push(y())}));return(0,o.h)("div",{ref:c,class:l.classes.value+(null===e.modelValue?" q-slider--no-value":""),...l.attributes.value,"aria-valuenow":e.modelValue},t)}}});var S=n(2857),k=n(883),x=n(6916),E=n(2695),C=n(5439),T=n(8383);function O(e,t,n){const o=!0===n?["left","right"]:["top","bottom"];return`absolute-${!0===t?o[0]:o[1]}${e?` text-${e}`:""}`}const F=["left","center","right","justify"],L=(0,y.L)({name:"QTabs",props:{modelValue:[Number,String],align:{type:String,default:"center",validator:e=>F.includes(e)},breakpoint:{type:[String,Number],default:600},vertical:Boolean,shrink:Boolean,stretch:Boolean,activeClass:String,activeColor:String,activeBgColor:String,indicatorColor:String,leftIcon:String,rightIcon:String,outsideArrows:Boolean,mobileArrows:Boolean,switchIndicator:Boolean,narrowIndicator:Boolean,inlineLabel:Boolean,noCaps:Boolean,dense:Boolean,contentClass:String,"onUpdate:modelValue":[Function,Array]},setup(e,{slots:t,emit:n}){const{proxy:l}=(0,o.FN)(),{$q:i}=l,{registerTick:a}=(0,x.Z)(),{registerTick:s}=(0,x.Z)(),{registerTick:u}=(0,x.Z)(),{registerTimeout:c,removeTimeout:f}=(0,E.Z)(),{registerTimeout:p,removeTimeout:v}=(0,E.Z)(),h=(0,r.iH)(null),m=(0,r.iH)(null),g=(0,r.iH)(e.modelValue),b=(0,r.iH)(!1),y=(0,r.iH)(!0),_=(0,r.iH)(!1),w=(0,r.iH)(!1),F=[],L=(0,r.iH)(0),A=(0,r.iH)(!1);let P,q=null,R=null;const N=(0,o.Fl)((()=>({activeClass:e.activeClass,activeColor:e.activeColor,activeBgColor:e.activeBgColor,indicatorClass:O(e.indicatorColor,e.switchIndicator,e.vertical),narrowIndicator:e.narrowIndicator,inlineLabel:e.inlineLabel,noCaps:e.noCaps}))),M=(0,o.Fl)((()=>{const e=L.value,t=g.value;for(let n=0;n<e;n++)if(F[n].name.value===t)return!0;return!1})),I=(0,o.Fl)((()=>{const t=!0===b.value?"left":!0===w.value?"justify":e.align;return`q-tabs__content--align-${t}`})),$=(0,o.Fl)((()=>`q-tabs row no-wrap items-center q-tabs--${!0===b.value?"":"not-"}scrollable q-tabs--`+(!0===e.vertical?"vertical":"horizontal")+" q-tabs__arrows--"+(!0===e.outsideArrows?"outside":"inside")+` q-tabs--mobile-with${!0===e.mobileArrows?"":"out"}-arrows`+(!0===e.dense?" q-tabs--dense":"")+(!0===e.shrink?" col-shrink":"")+(!0===e.stretch?" self-stretch":""))),D=(0,o.Fl)((()=>"q-tabs__content scroll--mobile row no-wrap items-center self-stretch hide-scrollbar relative-position "+I.value+(void 0!==e.contentClass?` ${e.contentClass}`:""))),V=(0,o.Fl)((()=>!0===e.vertical?{container:"height",content:"offsetHeight",scroll:"scrollHeight"}:{container:"width",content:"offsetWidth",scroll:"scrollWidth"})),j=(0,o.Fl)((()=>!0!==e.vertical&&!0===i.lang.rtl)),B=(0,o.Fl)((()=>!1===T.e&&!0===j.value));function H({name:t,setCurrent:o,skipEmit:r}){g.value!==t&&(!0!==r&&void 0!==e["onUpdate:modelValue"]&&n("update:modelValue",t),!0!==o&&void 0!==e["onUpdate:modelValue"]||(W(g.value,t),g.value=t))}function z(){a((()=>{U({width:h.value.offsetWidth,height:h.value.offsetHeight})}))}function U(t){if(void 0===V.value||null===m.value)return;const n=t[V.value.container],o=Math.min(m.value[V.value.scroll],Array.prototype.reduce.call(m.value.children,((e,t)=>e+(t[V.value.content]||0)),0)),r=n>0&&o>n;b.value=r,!0===r&&s(K),w.value=n<parseInt(e.breakpoint,10)}function W(t,n){const o=void 0!==t&&null!==t&&""!==t?F.find((e=>e.name.value===t)):null,r=void 0!==n&&null!==n&&""!==n?F.find((e=>e.name.value===n)):null;if(o&&r){const t=o.tabIndicatorRef.value,n=r.tabIndicatorRef.value;null!==q&&(clearTimeout(q),q=null),t.style.transition="none",t.style.transform="none",n.style.transition="none",n.style.transform="none";const l=t.getBoundingClientRect(),i=n.getBoundingClientRect();n.style.transform=!0===e.vertical?`translate3d(0,${l.top-i.top}px,0) scale3d(1,${i.height?l.height/i.height:1},1)`:`translate3d(${l.left-i.left}px,0,0) scale3d(${i.width?l.width/i.width:1},1,1)`,u((()=>{q=setTimeout((()=>{q=null,n.style.transition="transform .25s cubic-bezier(.4, 0, .2, 1)",n.style.transform="none"}),70)}))}r&&!0===b.value&&Z(r.rootRef.value)}function Z(t){const{left:n,width:o,top:r,height:l}=m.value.getBoundingClientRect(),i=t.getBoundingClientRect();let a=!0===e.vertical?i.top-r:i.left-n;if(a<0)return m.value[!0===e.vertical?"scrollTop":"scrollLeft"]+=Math.floor(a),void K();a+=!0===e.vertical?i.height-l:i.width-o,a>0&&(m.value[!0===e.vertical?"scrollTop":"scrollLeft"]+=Math.ceil(a),K())}function K(){const t=m.value;if(null===t)return;const n=t.getBoundingClientRect(),o=!0===e.vertical?t.scrollTop:Math.abs(t.scrollLeft);!0===j.value?(y.value=Math.ceil(o+n.width)<t.scrollWidth-1,_.value=o>0):(y.value=o>0,_.value=!0===e.vertical?Math.ceil(o+n.height)<t.scrollHeight:Math.ceil(o+n.width)<t.scrollWidth)}function Y(e){null!==R&&clearInterval(R),R=setInterval((()=>{!0===te(e)&&G()}),5)}function J(){Y(!0===B.value?Number.MAX_SAFE_INTEGER:0)}function X(){Y(!0===B.value?0:Number.MAX_SAFE_INTEGER)}function G(){null!==R&&(clearInterval(R),R=null)}function Q(t,n){const o=Array.prototype.filter.call(m.value.children,(e=>e===n||e.matches&&!0===e.matches(".q-tab.q-focusable"))),r=o.length;if(0===r)return;if(36===t)return Z(o[0]),o[0].focus(),!0;if(35===t)return Z(o[r-1]),o[r-1].focus(),!0;const l=t===(!0===e.vertical?38:37),i=t===(!0===e.vertical?40:39),a=!0===l?-1:!0===i?1:void 0;if(void 0!==a){const e=!0===j.value?-1:1,t=o.indexOf(n)+a*e;return t>=0&&t<r&&(Z(o[t]),o[t].focus({preventScroll:!0})),!0}}(0,o.YP)(j,K),(0,o.YP)((()=>e.modelValue),(e=>{H({name:e,setCurrent:!0,skipEmit:!0})})),(0,o.YP)((()=>e.outsideArrows),z);const ee=(0,o.Fl)((()=>!0===B.value?{get:e=>Math.abs(e.scrollLeft),set:(e,t)=>{e.scrollLeft=-t}}:!0===e.vertical?{get:e=>e.scrollTop,set:(e,t)=>{e.scrollTop=t}}:{get:e=>e.scrollLeft,set:(e,t)=>{e.scrollLeft=t}}));function te(e){const t=m.value,{get:n,set:o}=ee.value;let r=!1,l=n(t);const i=e<l?-1:1;return l+=5*i,l<0?(r=!0,l=0):(-1===i&&l<=e||1===i&&l>=e)&&(r=!0,l=e),o(t,l),K(),r}function ne(e,t){for(const n in e)if(e[n]!==t[n])return!1;return!0}function oe(){let e=null,t={matchedLen:0,queryDiff:9999,hrefLen:0};const n=F.filter((e=>void 0!==e.routeData&&!0===e.routeData.hasRouterLink.value)),{hash:o,query:r}=l.$route,i=Object.keys(r).length;for(const l of n){const n=!0===l.routeData.exact.value;if(!0!==l.routeData[!0===n?"linkIsExactActive":"linkIsActive"].value)continue;const{hash:a,query:s,matched:u,href:c}=l.routeData.resolvedLink.value,d=Object.keys(s).length;if(!0===n){if(a!==o)continue;if(d!==i||!1===ne(r,s))continue;e=l.name.value;break}if(""!==a&&a!==o)continue;if(0!==d&&!1===ne(s,r))continue;const f={matchedLen:u.length,queryDiff:i-d,hrefLen:c.length-a.length};if(f.matchedLen>t.matchedLen)e=l.name.value,t=f;else if(f.matchedLen===t.matchedLen){if(f.queryDiff<t.queryDiff)e=l.name.value,t=f;else if(f.queryDiff!==t.queryDiff)continue;f.hrefLen>t.hrefLen&&(e=l.name.value,t=f)}}null===e&&!0===F.some((e=>void 0===e.routeData&&e.name.value===g.value))||H({name:e,setCurrent:!0})}function re(e){if(f(),!0!==A.value&&null!==h.value&&e.target&&"function"===typeof e.target.closest){const t=e.target.closest(".q-tab");t&&!0===h.value.contains(t)&&(A.value=!0,!0===b.value&&Z(t))}}function le(){c((()=>{A.value=!1}),30)}function ie(){!1===ce.avoidRouteWatcher?p(oe):v()}function ae(){if(void 0===P){const e=(0,o.YP)((()=>l.$route.fullPath),ie);P=()=>{e(),P=void 0}}}function se(e){F.push(e),L.value++,z(),void 0===e.routeData||void 0===l.$route?p((()=>{if(!0===b.value){const e=g.value,t=void 0!==e&&null!==e&&""!==e?F.find((t=>t.name.value===e)):null;t&&Z(t.rootRef.value)}})):(ae(),!0===e.routeData.hasRouterLink.value&&ie())}function ue(e){F.splice(F.indexOf(e),1),L.value--,z(),void 0!==P&&void 0!==e.routeData&&(!0===F.every((e=>void 0===e.routeData))&&P(),ie())}const ce={currentModel:g,tabProps:N,hasFocus:A,hasActiveTab:M,registerTab:se,unregisterTab:ue,verifyRouteModel:ie,updateModel:H,onKbdNavigate:Q,avoidRouteWatcher:!1};function de(){null!==q&&clearTimeout(q),G(),void 0!==P&&P()}let fe;return(0,o.JJ)(C.Nd,ce),(0,o.Jd)(de),(0,o.se)((()=>{fe=void 0!==P,de()})),(0,o.dl)((()=>{!0===fe&&ae(),z()})),()=>(0,o.h)("div",{ref:h,class:$.value,role:"tablist",onFocusin:re,onFocusout:le},[(0,o.h)(k.Z,{onResize:U}),(0,o.h)("div",{ref:m,class:D.value,onScroll:K},(0,d.KR)(t.default)),(0,o.h)(S.Z,{class:"q-tabs__arrow q-tabs__arrow--left absolute q-tab__icon"+(!0===y.value?"":" q-tabs__arrow--faded"),name:e.leftIcon||i.iconSet.tabs[!0===e.vertical?"up":"left"],onMousedownPassive:J,onTouchstartPassive:J,onMouseupPassive:G,onMouseleavePassive:G,onTouchendPassive:G}),(0,o.h)(S.Z,{class:"q-tabs__arrow q-tabs__arrow--right absolute q-tab__icon"+(!0===_.value?"":" q-tabs__arrow--faded"),name:e.rightIcon||i.iconSet.tabs[!0===e.vertical?"down":"right"],onMousedownPassive:X,onTouchstartPassive:X,onMouseupPassive:G,onMouseleavePassive:G,onTouchendPassive:G})])}});var A=n(1722),P=n(1705),q=n(796);let R=0;const N=["click","keydown"],M={icon:String,label:[Number,String],alert:[Boolean,String],alertIcon:String,name:{type:[Number,String],default:()=>"t_"+R++},noCaps:Boolean,tabindex:[String,Number],disable:Boolean,contentClass:String,ripple:{type:[Boolean,Object],default:!0}};function I(e,t,n,l){const i=(0,o.f3)(C.Nd,C.qO);if(i===C.qO)return console.error("QTab/QRouteTab component needs to be child of QTabs"),C.qO;const{proxy:a}=(0,o.FN)(),s=(0,r.iH)(null),f=(0,r.iH)(null),p=(0,r.iH)(null),v=(0,o.Fl)((()=>!0!==e.disable&&!1!==e.ripple&&Object.assign({keyCodes:[13,32],early:!0},!0===e.ripple?{}:e.ripple))),h=(0,o.Fl)((()=>i.currentModel.value===e.name)),m=(0,o.Fl)((()=>"q-tab relative-position self-stretch flex flex-center text-center"+(!0===h.value?" q-tab--active"+(i.tabProps.value.activeClass?" "+i.tabProps.value.activeClass:"")+(i.tabProps.value.activeColor?` text-${i.tabProps.value.activeColor}`:"")+(i.tabProps.value.activeBgColor?` bg-${i.tabProps.value.activeBgColor}`:""):" q-tab--inactive")+(e.icon&&e.label&&!1===i.tabProps.value.inlineLabel?" q-tab--full":"")+(!0===e.noCaps||!0===i.tabProps.value.noCaps?" q-tab--no-caps":"")+(!0===e.disable?" disabled":" q-focusable q-hoverable cursor-pointer")+(void 0!==l?l.linkClass.value:""))),g=(0,o.Fl)((()=>"q-tab__content self-stretch flex-center relative-position q-anchor--skip non-selectable "+(!0===i.tabProps.value.inlineLabel?"row no-wrap q-tab__content--inline":"column")+(void 0!==e.contentClass?` ${e.contentClass}`:""))),b=(0,o.Fl)((()=>!0===e.disable||!0===i.hasFocus.value||!1===h.value&&!0===i.hasActiveTab.value?-1:e.tabindex||0));function y(t,o){if(!0!==o&&null!==s.value&&s.value.focus(),!0!==e.disable){if(void 0===l)return i.updateModel({name:e.name}),void n("click",t);if(!0===l.hasRouterLink.value){const o=(n={})=>{let o;const r=void 0===n.to||!0===(0,c.xb)(n.to,e.to)?i.avoidRouteWatcher=(0,q.Z)():null;return l.navigateToRouterLink(t,{...n,returnRouterError:!0}).catch((e=>{o=e})).then((t=>{if(r===i.avoidRouteWatcher&&(i.avoidRouteWatcher=!1,void 0!==o||void 0!==t&&!0!==t.message.startsWith("Avoided redundant navigation")||i.updateModel({name:e.name})),!0===n.returnRouterError)return void 0!==o?Promise.reject(o):t}))};return n("click",t,o),void(!0!==t.defaultPrevented&&o())}n("click",t)}else void 0!==l&&!0===l.hasRouterLink.value&&(0,u.NS)(t)}function _(e){(0,P.So)(e,[13,32])?y(e,!0):!0!==(0,P.Wm)(e)&&e.keyCode>=35&&e.keyCode<=40&&!0!==e.altKey&&!0!==e.metaKey&&!0===i.onKbdNavigate(e.keyCode,a.$el)&&(0,u.NS)(e),n("keydown",e)}function w(){const n=i.tabProps.value.narrowIndicator,r=[],l=(0,o.h)("div",{ref:p,class:["q-tab__indicator",i.tabProps.value.indicatorClass]});void 0!==e.icon&&r.push((0,o.h)(S.Z,{class:"q-tab__icon",name:e.icon})),void 0!==e.label&&r.push((0,o.h)("div",{class:"q-tab__label"},e.label)),!1!==e.alert&&r.push(void 0!==e.alertIcon?(0,o.h)(S.Z,{class:"q-tab__alert-icon",color:!0!==e.alert?e.alert:void 0,name:e.alertIcon}):(0,o.h)("div",{class:"q-tab__alert"+(!0!==e.alert?` text-${e.alert}`:"")})),!0===n&&r.push(l);const a=[(0,o.h)("div",{class:"q-focus-helper",tabindex:-1,ref:s}),(0,o.h)("div",{class:g.value},(0,d.vs)(t.default,r))];return!1===n&&a.push(l),a}const k={name:(0,o.Fl)((()=>e.name)),rootRef:f,tabIndicatorRef:p,routeData:l};function x(t,n){const r={ref:f,class:m.value,tabindex:b.value,role:"tab","aria-selected":!0===h.value?"true":"false","aria-disabled":!0===e.disable?"true":void 0,onClick:y,onKeydown:_,...n};return(0,o.wy)((0,o.h)(t,r,w()),[[A.Z,v.value]])}return(0,o.Jd)((()=>{i.unregisterTab(k)})),(0,o.bv)((()=>{i.registerTab(k)})),{renderTab:x,$tabs:i}}const $=(0,y.L)({name:"QTab",props:M,emits:N,setup(e,{slots:t,emit:n}){const{renderTab:o}=I(e,t,n);return()=>o("div")}});var D=n(5475);const V=(0,y.L)({name:"QTabPanels",props:{...D.t6,...a.S},emits:D.K6,setup(e,{slots:t}){const n=(0,o.FN)(),r=(0,a.Z)(e,n.proxy.$q),{updatePanelsList:l,getPanelContent:i,panelDirectives:s}=(0,D.ZP)(),u=(0,o.Fl)((()=>"q-tab-panels q-panel-parent"+(!0===r.value?" q-tab-panels--dark q-dark":"")));return()=>(l(t),(0,d.Jl)("div",{class:u.value},i(),"pan",e.swipeable,(()=>s.value)))}}),j=(0,y.L)({name:"QTabPanel",props:D.vZ,setup(e,{slots:t}){return()=>(0,o.h)("div",{class:"q-tab-panel",role:"tabpanel"},(0,d.KR)(t.default))}});var B=n(3978),H=n(3628),z=n(8633);const U=/^rgb(a)?\((\d{1,3}),(\d{1,3}),(\d{1,3}),?([01]?\.?\d*?)?\)$/;function W({r:e,g:t,b:n,a:o}){const r=void 0!==o;if(e=Math.round(e),t=Math.round(t),n=Math.round(n),e>255||t>255||n>255||r&&o>100)throw new TypeError("Expected 3 numbers below 256 (and optionally one below 100)");return o=r?(256|Math.round(255*o/100)).toString(16).slice(1):"","#"+(n|t<<8|e<<16|1<<24).toString(16).slice(1)+o}function Z({r:e,g:t,b:n,a:o}){return`rgb${void 0!==o?"a":""}(${e},${t},${n}${void 0!==o?","+o/100:""})`}function K(e){if("string"!==typeof e)throw new TypeError("Expected a string");e=e.replace(/^#/,""),3===e.length?e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]:4===e.length&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]+e[3]+e[3]);const t=parseInt(e,16);return e.length>6?{r:t>>24&255,g:t>>16&255,b:t>>8&255,a:Math.round((255&t)/2.55)}:{r:t>>16,g:t>>8&255,b:255&t}}function Y({h:e,s:t,v:n,a:o}){let r,l,i;t/=100,n/=100,e/=360;const a=Math.floor(6*e),s=6*e-a,u=n*(1-t),c=n*(1-s*t),d=n*(1-(1-s)*t);switch(a%6){case 0:r=n,l=d,i=u;break;case 1:r=c,l=n,i=u;break;case 2:r=u,l=n,i=d;break;case 3:r=u,l=c,i=n;break;case 4:r=d,l=u,i=n;break;case 5:r=n,l=u,i=c;break}return{r:Math.round(255*r),g:Math.round(255*l),b:Math.round(255*i),a:o}}function J({r:e,g:t,b:n,a:o}){const r=Math.max(e,t,n),l=Math.min(e,t,n),i=r-l,a=0===r?0:i/r,s=r/255;let u;switch(r){case l:u=0;break;case e:u=t-n+i*(t<n?6:0),u/=6*i;break;case t:u=n-e+2*i,u/=6*i;break;case n:u=e-t+4*i,u/=6*i;break}return{h:Math.round(360*u),s:Math.round(100*a),v:Math.round(100*s),a:o}}function X(e){if("string"!==typeof e)throw new TypeError("Expected a string");const t=e.replace(/ /g,""),n=U.exec(t);if(null===n)return K(t);const o={r:Math.min(255,parseInt(n[2],10)),g:Math.min(255,parseInt(n[3],10)),b:Math.min(255,parseInt(n[4],10))};if(n[1]){const e=parseFloat(n[5]);o.a=100*Math.min(1,!0===isNaN(e)?1:e)}return o}function G(e){if("string"!==typeof e&&(!e||void 0===e.r))throw new TypeError("Expected a string or a {r, g, b} object as color");const t="string"===typeof e?X(e):e,n=t.r/255,o=t.g/255,r=t.b/255,l=n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4),i=o<=.03928?o/12.92:Math.pow((o+.055)/1.055,2.4),a=r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4);return.2126*l+.7152*i+.0722*a}const Q=["rgb(255,204,204)","rgb(255,230,204)","rgb(255,255,204)","rgb(204,255,204)","rgb(204,255,230)","rgb(204,255,255)","rgb(204,230,255)","rgb(204,204,255)","rgb(230,204,255)","rgb(255,204,255)","rgb(255,153,153)","rgb(255,204,153)","rgb(255,255,153)","rgb(153,255,153)","rgb(153,255,204)","rgb(153,255,255)","rgb(153,204,255)","rgb(153,153,255)","rgb(204,153,255)","rgb(255,153,255)","rgb(255,102,102)","rgb(255,179,102)","rgb(255,255,102)","rgb(102,255,102)","rgb(102,255,179)","rgb(102,255,255)","rgb(102,179,255)","rgb(102,102,255)","rgb(179,102,255)","rgb(255,102,255)","rgb(255,51,51)","rgb(255,153,51)","rgb(255,255,51)","rgb(51,255,51)","rgb(51,255,153)","rgb(51,255,255)","rgb(51,153,255)","rgb(51,51,255)","rgb(153,51,255)","rgb(255,51,255)","rgb(255,0,0)","rgb(255,128,0)","rgb(255,255,0)","rgb(0,255,0)","rgb(0,255,128)","rgb(0,255,255)","rgb(0,128,255)","rgb(0,0,255)","rgb(128,0,255)","rgb(255,0,255)","rgb(245,0,0)","rgb(245,123,0)","rgb(245,245,0)","rgb(0,245,0)","rgb(0,245,123)","rgb(0,245,245)","rgb(0,123,245)","rgb(0,0,245)","rgb(123,0,245)","rgb(245,0,245)","rgb(214,0,0)","rgb(214,108,0)","rgb(214,214,0)","rgb(0,214,0)","rgb(0,214,108)","rgb(0,214,214)","rgb(0,108,214)","rgb(0,0,214)","rgb(108,0,214)","rgb(214,0,214)","rgb(163,0,0)","rgb(163,82,0)","rgb(163,163,0)","rgb(0,163,0)","rgb(0,163,82)","rgb(0,163,163)","rgb(0,82,163)","rgb(0,0,163)","rgb(82,0,163)","rgb(163,0,163)","rgb(92,0,0)","rgb(92,46,0)","rgb(92,92,0)","rgb(0,92,0)","rgb(0,92,46)","rgb(0,92,92)","rgb(0,46,92)","rgb(0,0,92)","rgb(46,0,92)","rgb(92,0,92)","rgb(255,255,255)","rgb(205,205,205)","rgb(178,178,178)","rgb(153,153,153)","rgb(127,127,127)","rgb(102,102,102)","rgb(76,76,76)","rgb(51,51,51)","rgb(25,25,25)","rgb(0,0,0)"],ee="M5 5 h10 v10 h-10 v-10 z",te="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAAAH0lEQVQoU2NkYGAwZkAFZ5G5jPRRgOYEVDeB3EBjBQBOZwTVugIGyAAAAABJRU5ErkJggg==",ne=(0,y.L)({name:"QColor",props:{...a.S,...i.Fz,modelValue:String,defaultValue:String,defaultView:{type:String,default:"spectrum",validator:e=>["spectrum","tune","palette"].includes(e)},formatModel:{type:String,default:"auto",validator:e=>["auto","hex","rgb","hexa","rgba"].includes(e)},palette:Array,noHeader:Boolean,noHeaderTabs:Boolean,noFooter:Boolean,square:Boolean,flat:Boolean,bordered:Boolean,disable:Boolean,readonly:Boolean},emits:["update:modelValue","change"],setup(e,{emit:t}){const{proxy:n}=(0,o.FN)(),{$q:s}=n,c=(0,a.Z)(e,s),{getCache:f}=(0,B.Z)(),p=(0,r.iH)(null),v=(0,r.iH)(null),h=(0,o.Fl)((()=>"auto"===e.formatModel?null:e.formatModel.indexOf("hex")>-1)),m=(0,o.Fl)((()=>"auto"===e.formatModel?null:e.formatModel.indexOf("a")>-1)),g=(0,r.iH)("auto"===e.formatModel?void 0===e.modelValue||null===e.modelValue||""===e.modelValue||e.modelValue.startsWith("#")?"hex":"rgb":e.formatModel.startsWith("hex")?"hex":"rgb"),b=(0,r.iH)(e.defaultView),y=(0,r.iH)(D(e.modelValue||e.defaultValue)),_=(0,o.Fl)((()=>!0!==e.disable&&!0!==e.readonly)),k=(0,o.Fl)((()=>void 0===e.modelValue||null===e.modelValue||""===e.modelValue||e.modelValue.startsWith("#"))),x=(0,o.Fl)((()=>null!==h.value?h.value:k.value)),E=(0,o.Fl)((()=>({type:"hidden",name:e.name,value:y.value[!0===x.value?"hex":"rgb"]}))),C=(0,i.eX)(E),T=(0,o.Fl)((()=>null!==m.value?m.value:void 0!==y.value.a)),O=(0,o.Fl)((()=>({backgroundColor:y.value.rgb||"#000"}))),F=(0,o.Fl)((()=>{const e=void 0!==y.value.a&&y.value.a<65||G(y.value)>.4;return"q-color-picker__header-content q-color-picker__header-content--"+(e?"light":"dark")})),A=(0,o.Fl)((()=>({background:`hsl(${y.value.h},100%,50%)`}))),P=(0,o.Fl)((()=>({top:100-y.value.v+"%",[!0===s.lang.rtl?"right":"left"]:`${y.value.s}%`}))),q=(0,o.Fl)((()=>void 0!==e.palette&&0!==e.palette.length?e.palette:Q)),R=(0,o.Fl)((()=>"q-color-picker"+(!0===e.bordered?" q-color-picker--bordered":"")+(!0===e.square?" q-color-picker--square no-border-radius":"")+(!0===e.flat?" q-color-picker--flat no-shadow":"")+(!0===e.disable?" disabled":"")+(!0===c.value?" q-color-picker--dark q-dark":""))),N=(0,o.Fl)((()=>!0===e.disable?{"aria-disabled":"true"}:!0===e.readonly?{"aria-readonly":"true"}:{})),M=(0,o.Fl)((()=>[[l.Z,ie,void 0,{prevent:!0,stop:!0,mouse:!0}]]));function I(e,n){y.value.hex=W(e),y.value.rgb=Z(e),y.value.r=e.r,y.value.g=e.g,y.value.b=e.b,y.value.a=e.a;const o=y.value[!0===x.value?"hex":"rgb"];t("update:modelValue",o),!0===n&&t("change",o)}function D(t){const n=void 0!==m.value?m.value:"auto"===e.formatModel?null:e.formatModel.indexOf("a")>-1;if("string"!==typeof t||0===t.length||!0!==H.E.anyColor(t.replace(/ /g,"")))return{h:0,s:0,v:0,r:0,g:0,b:0,a:!0===n?100:void 0,hex:void 0,rgb:void 0};const o=X(t);return!0===n&&void 0===o.a&&(o.a=100),o.hex=W(o),o.rgb=Z(o),Object.assign(o,J(o))}function U(e,t,n){const o=p.value;if(null===o)return;const r=o.clientWidth,l=o.clientHeight,i=o.getBoundingClientRect();let a=Math.min(r,Math.max(0,e-i.left));!0===s.lang.rtl&&(a=r-a);const u=Math.min(l,Math.max(0,t-i.top)),c=Math.round(100*a/r),d=Math.round(100*Math.max(0,Math.min(1,-u/l+1))),f=Y({h:y.value.h,s:c,v:d,a:!0===T.value?y.value.a:void 0});y.value.s=c,y.value.v=d,I(f,n)}function ne(e,t){const n=Math.round(e),o=Y({h:n,s:y.value.s,v:y.value.v,a:!0===T.value?y.value.a:void 0});y.value.h=n,I(o,t)}function oe(e,t,r,l,i){if(void 0!==l&&(0,u.sT)(l),!/^[0-9]+$/.test(e))return void(!0===i&&n.$forceUpdate());const a=Math.floor(Number(e));if(a<0||a>r)return void(!0===i&&n.$forceUpdate());const s={r:"r"===t?a:y.value.r,g:"g"===t?a:y.value.g,b:"b"===t?a:y.value.b,a:!0===T.value?"a"===t?a:y.value.a:void 0};if("a"!==t){const e=J(s);y.value.h=e.h,y.value.s=e.s,y.value.v=e.v}if(I(s,i),void 0!==l&&!0!==i&&void 0!==l.target.selectionEnd){const e=l.target.selectionEnd;(0,o.Y3)((()=>{l.target.setSelectionRange(e,e)}))}}function re(e,t){let n;const r=e.target.value;if((0,u.sT)(e),"hex"===g.value){if(r.length!==(!0===T.value?9:7)||!/^#[0-9A-Fa-f]+$/.test(r))return!0;n=K(r)}else{let e;if(!r.endsWith(")"))return!0;if(!0!==T.value&&r.startsWith("rgb(")){if(e=r.substring(4,r.length-1).split(",").map((e=>parseInt(e,10))),3!==e.length||!/^rgb\([0-9]{1,3},[0-9]{1,3},[0-9]{1,3}\)$/.test(r))return!0}else{if(!0!==T.value||!r.startsWith("rgba("))return!0;{if(e=r.substring(5,r.length-1).split(","),4!==e.length||!/^rgba\([0-9]{1,3},[0-9]{1,3},[0-9]{1,3},(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/.test(r))return!0;for(let n=0;n<3;n++){const t=parseInt(e[n],10);if(t<0||t>255)return!0;e[n]=t}const t=parseFloat(e[3]);if(t<0||t>1)return!0;e[3]=t}}if(e[0]<0||e[0]>255||e[1]<0||e[1]>255||e[2]<0||e[2]>255||!0===T.value&&(e[3]<0||e[3]>1))return!0;n={r:e[0],g:e[1],b:e[2],a:!0===T.value?100*e[3]:void 0}}const l=J(n);if(y.value.h=l.h,y.value.s=l.s,y.value.v=l.v,I(n,t),!0!==t){const t=e.target.selectionEnd;(0,o.Y3)((()=>{e.target.setSelectionRange(t,t)}))}}function le(e){const t=D(e),n={r:t.r,g:t.g,b:t.b,a:t.a};void 0===n.a&&(n.a=y.value.a),y.value.h=t.h,y.value.s=t.s,y.value.v=t.v,I(n,!0)}function ie(e){e.isFinal?U(e.position.left,e.position.top,!0):ae(e)}(0,o.YP)((()=>e.modelValue),(t=>{const n=D(t||e.defaultValue);n.hex!==y.value.hex&&(y.value=n)})),(0,o.YP)((()=>e.defaultValue),(t=>{if(!e.modelValue&&t){const e=D(t);e.hex!==y.value.hex&&(y.value=e)}}));const ae=(0,z.Z)((e=>{U(e.position.left,e.position.top)}),20);function se(e){U(e.pageX-window.pageXOffset,e.pageY-window.pageYOffset,!0)}function ue(e){U(e.pageX-window.pageXOffset,e.pageY-window.pageYOffset)}function ce(e){null!==v.value&&(v.value.$el.style.opacity=e?1:0)}function de(){const t=[];return!0!==e.noHeaderTabs&&t.push((0,o.h)(L,{class:"q-color-picker__header-tabs",modelValue:g.value,dense:!0,align:"justify",...f("topVTab",{"onUpdate:modelValue":e=>{g.value=e}})},(()=>[(0,o.h)($,{label:"HEX"+(!0===T.value?"A":""),name:"hex",ripple:!1}),(0,o.h)($,{label:"RGB"+(!0===T.value?"A":""),name:"rgb",ripple:!1})]))),t.push((0,o.h)("div",{class:"q-color-picker__header-banner row flex-center no-wrap"},[(0,o.h)("input",{class:"fit",value:y.value[g.value],...!0!==_.value?{readonly:!0}:{},...f("topIn",{onInput:e=>{ce(!0===re(e))},onChange:u.sT,onBlur:e=>{!0===re(e,!0)&&n.$forceUpdate(),ce(!1)}})}),(0,o.h)(S.Z,{ref:v,class:"q-color-picker__error-icon absolute no-pointer-events",name:s.iconSet.type.negative})])),(0,o.h)("div",{class:"q-color-picker__header relative-position overflow-hidden"},[(0,o.h)("div",{class:"q-color-picker__header-bg absolute-full"}),(0,o.h)("div",{class:F.value,style:O.value},t)])}function fe(){return(0,o.h)(V,{modelValue:b.value,animated:!0},(()=>[(0,o.h)(j,{class:"q-color-picker__spectrum-tab overflow-hidden",name:"spectrum"},ve),(0,o.h)(j,{class:"q-pa-md q-color-picker__tune-tab",name:"tune"},he),(0,o.h)(j,{class:"q-color-picker__palette-tab",name:"palette"},me)]))}function pe(){return(0,o.h)("div",{class:"q-color-picker__footer relative-position overflow-hidden"},[(0,o.h)(L,{class:"absolute-full",modelValue:b.value,dense:!0,align:"justify",...f("ftIn",{"onUpdate:modelValue":e=>{b.value=e}})},(()=>[(0,o.h)($,{icon:s.iconSet.colorPicker.spectrum,name:"spectrum",ripple:!1}),(0,o.h)($,{icon:s.iconSet.colorPicker.tune,name:"tune",ripple:!1}),(0,o.h)($,{icon:s.iconSet.colorPicker.palette,name:"palette",ripple:!1})]))])}function ve(){const e={ref:p,class:"q-color-picker__spectrum non-selectable relative-position cursor-pointer"+(!0!==_.value?" readonly":""),style:A.value,...!0===_.value?{onClick:se,onMousedown:ue}:{}},t=[(0,o.h)("div",{style:{paddingBottom:"100%"}}),(0,o.h)("div",{class:"q-color-picker__spectrum-white absolute-full"}),(0,o.h)("div",{class:"q-color-picker__spectrum-black absolute-full"}),(0,o.h)("div",{class:"absolute",style:P.value},[void 0!==y.value.hex?(0,o.h)("div",{class:"q-color-picker__spectrum-circle"}):null])],n=[(0,o.h)(w,{class:"q-color-picker__hue non-selectable",modelValue:y.value.h,min:0,max:360,trackSize:"8px",innerTrackColor:"transparent",selectionColor:"transparent",readonly:!0!==_.value,thumbPath:ee,"onUpdate:modelValue":ne,...f("lazyhue",{onChange:e=>ne(e,!0)})})];return!0===T.value&&n.push((0,o.h)(w,{class:"q-color-picker__alpha non-selectable",modelValue:y.value.a,min:0,max:100,trackSize:"8px",trackColor:"white",innerTrackColor:"transparent",selectionColor:"transparent",trackImg:te,readonly:!0!==_.value,hideSelection:!0,thumbPath:ee,...f("alphaSlide",{"onUpdate:modelValue":e=>oe(e,"a",100),onChange:e=>oe(e,"a",100,void 0,!0)})})),[(0,d.Jl)("div",e,t,"spec",_.value,(()=>M.value)),(0,o.h)("div",{class:"q-color-picker__sliders"},n)]}function he(){return[(0,o.h)("div",{class:"row items-center no-wrap"},[(0,o.h)("div","R"),(0,o.h)(w,{modelValue:y.value.r,min:0,max:255,color:"red",dark:c.value,readonly:!0!==_.value,...f("rSlide",{"onUpdate:modelValue":e=>oe(e,"r",255),onChange:e=>oe(e,"r",255,void 0,!0)})}),(0,o.h)("input",{value:y.value.r,maxlength:3,readonly:!0!==_.value,onChange:u.sT,...f("rIn",{onInput:e=>oe(e.target.value,"r",255,e),onBlur:e=>oe(e.target.value,"r",255,e,!0)})})]),(0,o.h)("div",{class:"row items-center no-wrap"},[(0,o.h)("div","G"),(0,o.h)(w,{modelValue:y.value.g,min:0,max:255,color:"green",dark:c.value,readonly:!0!==_.value,...f("gSlide",{"onUpdate:modelValue":e=>oe(e,"g",255),onChange:e=>oe(e,"g",255,void 0,!0)})}),(0,o.h)("input",{value:y.value.g,maxlength:3,readonly:!0!==_.value,onChange:u.sT,...f("gIn",{onInput:e=>oe(e.target.value,"g",255,e),onBlur:e=>oe(e.target.value,"g",255,e,!0)})})]),(0,o.h)("div",{class:"row items-center no-wrap"},[(0,o.h)("div","B"),(0,o.h)(w,{modelValue:y.value.b,min:0,max:255,color:"blue",readonly:!0!==_.value,dark:c.value,...f("bSlide",{"onUpdate:modelValue":e=>oe(e,"b",255),onChange:e=>oe(e,"b",255,void 0,!0)})}),(0,o.h)("input",{value:y.value.b,maxlength:3,readonly:!0!==_.value,onChange:u.sT,...f("bIn",{onInput:e=>oe(e.target.value,"b",255,e),onBlur:e=>oe(e.target.value,"b",255,e,!0)})})]),!0===T.value?(0,o.h)("div",{class:"row items-center no-wrap"},[(0,o.h)("div","A"),(0,o.h)(w,{modelValue:y.value.a,color:"grey",readonly:!0!==_.value,dark:c.value,...f("aSlide",{"onUpdate:modelValue":e=>oe(e,"a",100),onChange:e=>oe(e,"a",100,void 0,!0)})}),(0,o.h)("input",{value:y.value.a,maxlength:3,readonly:!0!==_.value,onChange:u.sT,...f("aIn",{onInput:e=>oe(e.target.value,"a",100,e),onBlur:e=>oe(e.target.value,"a",100,e,!0)})})]):null]}function me(){const e=e=>(0,o.h)("div",{class:"q-color-picker__cube col-auto",style:{backgroundColor:e},...!0===_.value?f("palette#"+e,{onClick:()=>{le(e)}}):{}});return[(0,o.h)("div",{class:"row items-center q-color-picker__palette-rows"+(!0===_.value?" q-color-picker__palette-rows--editable":"")},q.value.map(e))]}return()=>{const t=[fe()];return void 0!==e.name&&!0!==e.disable&&C(t,"push"),!0!==e.noHeader&&t.unshift(de()),!0!==e.noFooter&&t.push(pe()),(0,o.h)("div",{class:R.value,...N.value},t)}}})},1378:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});n(9665);var o=n(9835),r=n(499),l=n(7506),i=n(883),a=n(5987),s=n(2026),u=n(5439);const c=(0,a.L)({name:"QFooter",props:{modelValue:{type:Boolean,default:!0},reveal:Boolean,bordered:Boolean,elevated:Boolean,heightHint:{type:[String,Number],default:50}},emits:["reveal","focusin"],setup(e,{slots:t,emit:n}){const{proxy:{$q:a}}=(0,o.FN)(),c=(0,o.f3)(u.YE,u.qO);if(c===u.qO)return console.error("QFooter needs to be child of QLayout"),u.qO;const d=(0,r.iH)(parseInt(e.heightHint,10)),f=(0,r.iH)(!0),p=(0,r.iH)(!0===l.uX.value||!0===c.isContainer.value?0:window.innerHeight),v=(0,o.Fl)((()=>!0===e.reveal||c.view.value.indexOf("F")>-1||a.platform.is.ios&&!0===c.isContainer.value)),h=(0,o.Fl)((()=>!0===c.isContainer.value?c.containerHeight.value:p.value)),m=(0,o.Fl)((()=>{if(!0!==e.modelValue)return 0;if(!0===v.value)return!0===f.value?d.value:0;const t=c.scroll.value.position+h.value+d.value-c.height.value;return t>0?t:0})),g=(0,o.Fl)((()=>!0!==e.modelValue||!0===v.value&&!0!==f.value)),b=(0,o.Fl)((()=>!0===e.modelValue&&!0===g.value&&!0===e.reveal)),y=(0,o.Fl)((()=>"q-footer q-layout__section--marginal "+(!0===v.value?"fixed":"absolute")+"-bottom"+(!0===e.bordered?" q-footer--bordered":"")+(!0===g.value?" q-footer--hidden":"")+(!0!==e.modelValue?" q-layout--prevent-focus"+(!0!==v.value?" hidden":""):""))),_=(0,o.Fl)((()=>{const e=c.rows.value.bottom,t={};return"l"===e[0]&&!0===c.left.space&&(t[!0===a.lang.rtl?"right":"left"]=`${c.left.size}px`),"r"===e[2]&&!0===c.right.space&&(t[!0===a.lang.rtl?"left":"right"]=`${c.right.size}px`),t}));function w(e,t){c.update("footer",e,t)}function S(e,t){e.value!==t&&(e.value=t)}function k({height:e}){S(d,e),w("size",e)}function x(){if(!0!==e.reveal)return;const{direction:t,position:n,inflectionPoint:o}=c.scroll.value;S(f,"up"===t||n-o<100||c.height.value-h.value-n-d.value<300)}function E(e){!0===b.value&&S(f,!0),n("focusin",e)}(0,o.YP)((()=>e.modelValue),(e=>{w("space",e),S(f,!0),c.animate()})),(0,o.YP)(m,(e=>{w("offset",e)})),(0,o.YP)((()=>e.reveal),(t=>{!1===t&&S(f,e.modelValue)})),(0,o.YP)(f,(e=>{c.animate(),n("reveal",e)})),(0,o.YP)([d,c.scroll,c.height],x),(0,o.YP)((()=>a.screen.height),(e=>{!0!==c.isContainer.value&&S(p,e)}));const C={};return c.instances.footer=C,!0===e.modelValue&&w("size",d.value),w("space",e.modelValue),w("offset",m.value),(0,o.Jd)((()=>{c.instances.footer===C&&(c.instances.footer=void 0,w("size",0),w("offset",0),w("space",!1))})),()=>{const n=(0,s.vs)(t.default,[(0,o.h)(i.Z,{debounce:0,onResize:k})]);return!0===e.elevated&&n.push((0,o.h)("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),(0,o.h)("footer",{class:y.value,style:_.value,onFocusin:E},n)}}})},8326:(e,t,n)=>{"use strict";n.d(t,{Z:()=>d});n(9665);var o=n(9835),r=n(499),l=n(5987),i=n(1384),a=n(7026),s=n(2026),u=n(5439),c=n(2046);const d=(0,l.L)({name:"QForm",props:{autofocus:Boolean,noErrorFocus:Boolean,noResetFocus:Boolean,greedy:Boolean,onSubmit:Function},emits:["reset","validationSuccess","validationError"],setup(e,{slots:t,emit:n}){const l=(0,o.FN)(),d=(0,r.iH)(null);let f=0;const p=[];function v(t){const o="boolean"===typeof t?t:!0!==e.noErrorFocus,r=++f,l=(e,t)=>{n("validation"+(!0===e?"Success":"Error"),t)},i=e=>{const t=e.validate();return"function"===typeof t.then?t.then((t=>({valid:t,comp:e})),(t=>({valid:!1,comp:e,err:t}))):Promise.resolve({valid:t,comp:e})},a=!0===e.greedy?Promise.all(p.map(i)).then((e=>e.filter((e=>!0!==e.valid)))):p.reduce(((e,t)=>e.then((()=>i(t).then((e=>{if(!1===e.valid)return Promise.reject(e)}))))),Promise.resolve()).catch((e=>[e]));return a.then((e=>{if(void 0===e||0===e.length)return r===f&&l(!0),!0;if(r===f){const{comp:t,err:n}=e[0];if(void 0!==n&&console.error(n),l(!1,t),!0===o){const t=e.find((({comp:e})=>"function"===typeof e.focus&&!1===(0,c.$D)(e.$)));void 0!==t&&t.comp.focus()}}return!1}))}function h(){f++,p.forEach((e=>{"function"===typeof e.resetValidation&&e.resetValidation()}))}function m(t){void 0!==t&&(0,i.NS)(t);const o=f+1;v().then((r=>{o===f&&!0===r&&(void 0!==e.onSubmit?n("submit",t):void 0!==t&&void 0!==t.target&&"function"===typeof t.target.submit&&t.target.submit())}))}function g(t){void 0!==t&&(0,i.NS)(t),n("reset"),(0,o.Y3)((()=>{h(),!0===e.autofocus&&!0!==e.noResetFocus&&b()}))}function b(){(0,a.jd)((()=>{if(null===d.value)return;const e=d.value.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||d.value.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||d.value.querySelector("[autofocus], [data-autofocus]")||Array.prototype.find.call(d.value.querySelectorAll("[tabindex]"),(e=>e.tabIndex>-1));null!==e&&void 0!==e&&e.focus({preventScroll:!0})}))}(0,o.JJ)(u.vh,{bindComponent(e){p.push(e)},unbindComponent(e){const t=p.indexOf(e);t>-1&&p.splice(t,1)}});let y=!1;return(0,o.se)((()=>{y=!0})),(0,o.dl)((()=>{!0===y&&!0===e.autofocus&&b()})),(0,o.bv)((()=>{!0===e.autofocus&&b()})),Object.assign(l.proxy,{validate:v,resetValidation:h,submit:m,reset:g,focus:b,getValidationComponents:()=>p}),()=>(0,o.h)("form",{class:"q-form",ref:d,onSubmit:m,onReset:g},(0,s.KR)(t.default))}})},6602:(e,t,n)=>{"use strict";n.d(t,{Z:()=>u});n(9665);var o=n(9835),r=n(499),l=n(883),i=n(5987),a=n(2026),s=n(5439);const u=(0,i.L)({name:"QHeader",props:{modelValue:{type:Boolean,default:!0},reveal:Boolean,revealOffset:{type:Number,default:250},bordered:Boolean,elevated:Boolean,heightHint:{type:[String,Number],default:50}},emits:["reveal","focusin"],setup(e,{slots:t,emit:n}){const{proxy:{$q:i}}=(0,o.FN)(),u=(0,o.f3)(s.YE,s.qO);if(u===s.qO)return console.error("QHeader needs to be child of QLayout"),s.qO;const c=(0,r.iH)(parseInt(e.heightHint,10)),d=(0,r.iH)(!0),f=(0,o.Fl)((()=>!0===e.reveal||u.view.value.indexOf("H")>-1||i.platform.is.ios&&!0===u.isContainer.value)),p=(0,o.Fl)((()=>{if(!0!==e.modelValue)return 0;if(!0===f.value)return!0===d.value?c.value:0;const t=c.value-u.scroll.value.position;return t>0?t:0})),v=(0,o.Fl)((()=>!0!==e.modelValue||!0===f.value&&!0!==d.value)),h=(0,o.Fl)((()=>!0===e.modelValue&&!0===v.value&&!0===e.reveal)),m=(0,o.Fl)((()=>"q-header q-layout__section--marginal "+(!0===f.value?"fixed":"absolute")+"-top"+(!0===e.bordered?" q-header--bordered":"")+(!0===v.value?" q-header--hidden":"")+(!0!==e.modelValue?" q-layout--prevent-focus":""))),g=(0,o.Fl)((()=>{const e=u.rows.value.top,t={};return"l"===e[0]&&!0===u.left.space&&(t[!0===i.lang.rtl?"right":"left"]=`${u.left.size}px`),"r"===e[2]&&!0===u.right.space&&(t[!0===i.lang.rtl?"left":"right"]=`${u.right.size}px`),t}));function b(e,t){u.update("header",e,t)}function y(e,t){e.value!==t&&(e.value=t)}function _({height:e}){y(c,e),b("size",e)}function w(e){!0===h.value&&y(d,!0),n("focusin",e)}(0,o.YP)((()=>e.modelValue),(e=>{b("space",e),y(d,!0),u.animate()})),(0,o.YP)(p,(e=>{b("offset",e)})),(0,o.YP)((()=>e.reveal),(t=>{!1===t&&y(d,e.modelValue)})),(0,o.YP)(d,(e=>{u.animate(),n("reveal",e)})),(0,o.YP)(u.scroll,(t=>{!0===e.reveal&&y(d,"up"===t.direction||t.position<=e.revealOffset||t.position-t.inflectionPoint<100)}));const S={};return u.instances.header=S,!0===e.modelValue&&b("size",c.value),b("space",e.modelValue),b("offset",p.value),(0,o.Jd)((()=>{u.instances.header===S&&(u.instances.header=void 0,b("size",0),b("offset",0),b("space",!1))})),()=>{const n=(0,a.Bl)(t.default,[]);return!0===e.elevated&&n.push((0,o.h)("div",{class:"q-layout__shadow absolute-full overflow-hidden no-pointer-events"})),n.push((0,o.h)(l.Z,{debounce:0,onResize:_})),(0,o.h)("header",{class:m.value,style:g.value,onFocusin:w},n)}}})},2857:(e,t,n)=>{"use strict";n.d(t,{Z:()=>w});var o=n(9835),r=n(244),l=n(5987),i=n(2026);const a="0 0 24 24",s=e=>e,u=e=>`ionicons ${e}`,c={"mdi-":e=>`mdi ${e}`,"icon-":s,"bt-":e=>`bt ${e}`,"eva-":e=>`eva ${e}`,"ion-md":u,"ion-ios":u,"ion-logo":u,"iconfont ":s,"ti-":e=>`themify-icon ${e}`,"bi-":e=>`bootstrap-icons ${e}`},d={o_:"-outlined",r_:"-round",s_:"-sharp"},f={sym_o_:"-outlined",sym_r_:"-rounded",sym_s_:"-sharp"},p=new RegExp("^("+Object.keys(c).join("|")+")"),v=new RegExp("^("+Object.keys(d).join("|")+")"),h=new RegExp("^("+Object.keys(f).join("|")+")"),m=/^[Mm]\s?[-+]?\.?\d/,g=/^img:/,b=/^svguse:/,y=/^ion-/,_=/^(fa-(sharp|solid|regular|light|brands|duotone|thin)|[lf]a[srlbdk]?) /,w=(0,l.L)({name:"QIcon",props:{...r.LU,tag:{type:String,default:"i"},name:String,color:String,left:Boolean,right:Boolean},setup(e,{slots:t}){const{proxy:{$q:n}}=(0,o.FN)(),l=(0,r.ZP)(e),s=(0,o.Fl)((()=>"q-icon"+(!0===e.left?" on-left":"")+(!0===e.right?" on-right":"")+(void 0!==e.color?` text-${e.color}`:""))),u=(0,o.Fl)((()=>{let t,r=e.name;if("none"===r||!r)return{none:!0};if(null!==n.iconMapFn){const e=n.iconMapFn(r);if(void 0!==e){if(void 0===e.icon)return{cls:e.cls,content:void 0!==e.content?e.content:" "};if(r=e.icon,"none"===r||!r)return{none:!0}}}if(!0===m.test(r)){const[e,t=a]=r.split("|");return{svg:!0,viewBox:t,nodes:e.split("&&").map((e=>{const[t,n,r]=e.split("@@");return(0,o.h)("path",{style:n,d:t,transform:r})}))}}if(!0===g.test(r))return{img:!0,src:r.substring(4)};if(!0===b.test(r)){const[e,t=a]=r.split("|");return{svguse:!0,src:e.substring(7),viewBox:t}}let l=" ";const i=r.match(p);if(null!==i)t=c[i[1]](r);else if(!0===_.test(r))t=r;else if(!0===y.test(r))t=`ionicons ion-${!0===n.platform.is.ios?"ios":"md"}${r.substring(3)}`;else if(!0===h.test(r)){t="notranslate material-symbols";const e=r.match(h);null!==e&&(r=r.substring(6),t+=f[e[1]]),l=r}else{t="notranslate material-icons";const e=r.match(v);null!==e&&(r=r.substring(2),t+=d[e[1]]),l=r}return{cls:t,content:l}}));return()=>{const n={class:s.value,style:l.value,"aria-hidden":"true",role:"presentation"};return!0===u.value.none?(0,o.h)(e.tag,n,(0,i.KR)(t.default)):!0===u.value.img?(0,o.h)("span",n,(0,i.vs)(t.default,[(0,o.h)("img",{src:u.value.src})])):!0===u.value.svg?(0,o.h)("span",n,(0,i.vs)(t.default,[(0,o.h)("svg",{viewBox:u.value.viewBox||"0 0 24 24"},u.value.nodes)])):!0===u.value.svguse?(0,o.h)("span",n,(0,i.vs)(t.default,[(0,o.h)("svg",{viewBox:u.value.viewBox},[(0,o.h)("use",{"xlink:href":u.value.src})])])):(void 0!==u.value.cls&&(n.class+=" "+u.value.cls),(0,o.h)(e.tag,n,(0,i.vs)(t.default,[u.value.content])))}}})},6611:(e,t,n)=>{"use strict";n.d(t,{Z:()=>S});var o=n(9835),r=n(499),l=n(1486),i=(n(9665),n(1705));const a={date:"####/##/##",datetime:"####/##/## ##:##",time:"##:##",fulltime:"##:##:##",phone:"(###) ### - ####",card:"#### #### #### ####"},s={"#":{pattern:"[\\d]",negate:"[^\\d]"},S:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]"},N:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]"},A:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleUpperCase()},a:{pattern:"[a-zA-Z]",negate:"[^a-zA-Z]",transform:e=>e.toLocaleLowerCase()},X:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleUpperCase()},x:{pattern:"[0-9a-zA-Z]",negate:"[^0-9a-zA-Z]",transform:e=>e.toLocaleLowerCase()}},u=Object.keys(s);u.forEach((e=>{s[e].regex=new RegExp(s[e].pattern)}));const c=new RegExp("\\\\([^.*+?^${}()|([\\]])|([.*+?^${}()|[\\]])|(["+u.join("")+"])|(.)","g"),d=/[.*+?^${}()|[\]\\]/g,f=String.fromCharCode(1),p={mask:String,reverseFillMask:Boolean,fillMask:[Boolean,String],unmaskedValue:Boolean};function v(e,t,n,l){let u,p,v,h,m,g;const b=(0,r.iH)(null),y=(0,r.iH)(w());function _(){return!0===e.autogrow||["textarea","text","search","url","tel","password"].includes(e.type)}function w(){if(k(),!0===b.value){const t=F(A(e.modelValue));return!1!==e.fillMask?P(t):t}return e.modelValue}function S(e){if(e<u.length)return u.slice(-e);let t="",n=u;const o=n.indexOf(f);if(o>-1){for(let o=e-n.length;o>0;o--)t+=f;n=n.slice(0,o)+t+n.slice(o)}return n}function k(){if(b.value=void 0!==e.mask&&0!==e.mask.length&&_(),!1===b.value)return h=void 0,u="",void(p="");const t=void 0===a[e.mask]?e.mask:a[e.mask],n="string"===typeof e.fillMask&&0!==e.fillMask.length?e.fillMask.slice(0,1):"_",o=n.replace(d,"\\$&"),r=[],l=[],i=[];let m=!0===e.reverseFillMask,g="",y="";t.replace(c,((e,t,n,o,a)=>{if(void 0!==o){const e=s[o];i.push(e),y=e.negate,!0===m&&(l.push("(?:"+y+"+)?("+e.pattern+"+)?(?:"+y+"+)?("+e.pattern+"+)?"),m=!1),l.push("(?:"+y+"+)?("+e.pattern+")?")}else if(void 0!==n)g="\\"+("\\"===n?"":n),i.push(n),r.push("([^"+g+"]+)?"+g+"?");else{const e=void 0!==t?t:a;g="\\"===e?"\\\\\\\\":e.replace(d,"\\\\$&"),i.push(e),r.push("([^"+g+"]+)?"+g+"?")}}));const w=new RegExp("^"+r.join("")+"("+(""===g?".":"[^"+g+"]")+"+)?"+(""===g?"":"["+g+"]*")+"$"),S=l.length-1,k=l.map(((t,n)=>0===n&&!0===e.reverseFillMask?new RegExp("^"+o+"*"+t):n===S?new RegExp("^"+t+"("+(""===y?".":y)+"+)?"+(!0===e.reverseFillMask?"$":o+"*")):new RegExp("^"+t)));v=i,h=t=>{const n=w.exec(!0===e.reverseFillMask?t:t.slice(0,i.length+1));null!==n&&(t=n.slice(1).join(""));const o=[],r=k.length;for(let e=0,l=t;e<r;e++){const t=k[e].exec(l);if(null===t)break;l=l.slice(t.shift().length),o.push(...t)}return 0!==o.length?o.join(""):t},u=i.map((e=>"string"===typeof e?e:f)).join(""),p=u.split(f).join(n)}function x(t,r,i){const a=l.value,s=a.selectionEnd,c=a.value.length-s,d=A(t);!0===r&&k();const v=F(d),h=!1!==e.fillMask?P(v):v,g=y.value!==h;a.value!==h&&(a.value=h),!0===g&&(y.value=h),document.activeElement===a&&(0,o.Y3)((()=>{if(h!==p)if("insertFromPaste"!==i||!0===e.reverseFillMask)if(["deleteContentBackward","deleteContentForward"].indexOf(i)>-1){const t=!0===e.reverseFillMask?0===s?h.length>v.length?1:0:Math.max(0,h.length-(h===p?0:Math.min(v.length,c)+1))+1:s;a.setSelectionRange(t,t,"forward")}else if(!0===e.reverseFillMask)if(!0===g){const e=Math.max(0,h.length-(h===p?0:Math.min(v.length,c+1)));1===e&&1===s?a.setSelectionRange(e,e,"forward"):C.rightReverse(a,e)}else{const e=h.length-c;a.setSelectionRange(e,e,"backward")}else if(!0===g){const e=Math.max(0,u.indexOf(f),Math.min(v.length,s)-1);C.right(a,e)}else{const e=s-1;C.right(a,e)}else{const e=a.selectionEnd;let t=s-1;for(let n=m;n<=t&&n<e;n++)u[n]!==f&&t++;C.right(a,t)}else{const t=!0===e.reverseFillMask?p.length:0;a.setSelectionRange(t,t,"forward")}}));const b=!0===e.unmaskedValue?A(h):h;String(e.modelValue)!==b&&n(b,!0)}function E(e,t,n){const o=F(A(e.value));t=Math.max(0,u.indexOf(f),Math.min(o.length,t)),m=t,e.setSelectionRange(t,n,"forward")}(0,o.YP)((()=>e.type+e.autogrow),k),(0,o.YP)((()=>e.mask),(n=>{if(void 0!==n)x(y.value,!0);else{const n=A(y.value);k(),e.modelValue!==n&&t("update:modelValue",n)}})),(0,o.YP)((()=>e.fillMask+e.reverseFillMask),(()=>{!0===b.value&&x(y.value,!0)})),(0,o.YP)((()=>e.unmaskedValue),(()=>{!0===b.value&&x(y.value)}));const C={left(e,t){const n=-1===u.slice(t-1).indexOf(f);let o=Math.max(0,t-1);for(;o>=0;o--)if(u[o]===f){t=o,!0===n&&t++;break}if(o<0&&void 0!==u[t]&&u[t]!==f)return C.right(e,0);t>=0&&e.setSelectionRange(t,t,"backward")},right(e,t){const n=e.value.length;let o=Math.min(n,t+1);for(;o<=n;o++){if(u[o]===f){t=o;break}u[o-1]===f&&(t=o)}if(o>n&&void 0!==u[t-1]&&u[t-1]!==f)return C.left(e,n);e.setSelectionRange(t,t,"forward")},leftReverse(e,t){const n=S(e.value.length);let o=Math.max(0,t-1);for(;o>=0;o--){if(n[o-1]===f){t=o;break}if(n[o]===f&&(t=o,0===o))break}if(o<0&&void 0!==n[t]&&n[t]!==f)return C.rightReverse(e,0);t>=0&&e.setSelectionRange(t,t,"backward")},rightReverse(e,t){const n=e.value.length,o=S(n),r=-1===o.slice(0,t+1).indexOf(f);let l=Math.min(n,t+1);for(;l<=n;l++)if(o[l-1]===f){t=l,t>0&&!0===r&&t--;break}if(l>n&&void 0!==o[t-1]&&o[t-1]!==f)return C.leftReverse(e,n);e.setSelectionRange(t,t,"forward")}};function T(e){t("click",e),g=void 0}function O(n){if(t("keydown",n),!0===(0,i.Wm)(n)||!0===n.altKey)return;const o=l.value,r=o.selectionStart,a=o.selectionEnd;if(n.shiftKey||(g=void 0),37===n.keyCode||39===n.keyCode){n.shiftKey&&void 0===g&&(g="forward"===o.selectionDirection?r:a);const t=C[(39===n.keyCode?"right":"left")+(!0===e.reverseFillMask?"Reverse":"")];if(n.preventDefault(),t(o,g===r?a:r),n.shiftKey){const e=o.selectionStart;o.setSelectionRange(Math.min(g,e),Math.max(g,e),"forward")}}else 8===n.keyCode&&!0!==e.reverseFillMask&&r===a?(C.left(o,r),o.setSelectionRange(o.selectionStart,a,"backward")):46===n.keyCode&&!0===e.reverseFillMask&&r===a&&(C.rightReverse(o,a),o.setSelectionRange(r,o.selectionEnd,"forward"))}function F(t){if(void 0===t||null===t||""===t)return"";if(!0===e.reverseFillMask)return L(t);const n=v;let o=0,r="";for(let e=0;e<n.length;e++){const l=t[o],i=n[e];if("string"===typeof i)r+=i,l===i&&o++;else{if(void 0===l||!i.regex.test(l))return r;r+=void 0!==i.transform?i.transform(l):l,o++}}return r}function L(e){const t=v,n=u.indexOf(f);let o=e.length-1,r="";for(let l=t.length-1;l>=0&&o>-1;l--){const i=t[l];let a=e[o];if("string"===typeof i)r=i+r,a===i&&o--;else{if(void 0===a||!i.regex.test(a))return r;do{r=(void 0!==i.transform?i.transform(a):a)+r,o--,a=e[o]}while(n===l&&void 0!==a&&i.regex.test(a))}}return r}function A(e){return"string"!==typeof e||void 0===h?"number"===typeof e?h(""+e):e:h(e)}function P(t){return p.length-t.length<=0?t:!0===e.reverseFillMask&&0!==t.length?p.slice(0,-t.length)+t:t+p.slice(t.length)}return{innerValue:y,hasMask:b,moveCursorForPaste:E,updateMaskValue:x,onMaskedKeydown:O,onMaskedClick:T}}var h=n(9256);function m(e,t){function n(){const t=e.modelValue;try{const e="DataTransfer"in window?new DataTransfer:"ClipboardEvent"in window?new ClipboardEvent("").clipboardData:void 0;return Object(t)===t&&("length"in t?Array.from(t):[t]).forEach((t=>{e.items.add(t)})),{files:e.files}}catch(n){return{files:void 0}}}return!0===t?(0,o.Fl)((()=>{if("file"===e.type)return n()})):(0,o.Fl)(n)}var g=n(2802),b=n(5987),y=n(1384),_=n(7026),w=n(3251);const S=(0,b.L)({name:"QInput",inheritAttrs:!1,props:{...l.Cl,...p,...h.Fz,modelValue:{required:!1},shadowText:String,type:{type:String,default:"text"},debounce:[String,Number],autogrow:Boolean,inputClass:[Array,String,Object],inputStyle:[Array,String,Object]},emits:[...l.HJ,"paste","change","keydown","click","animationend"],setup(e,{emit:t,attrs:n}){const{proxy:i}=(0,o.FN)(),{$q:a}=i,s={};let u,c,d,f=NaN,p=null;const b=(0,r.iH)(null),S=(0,h.Do)(e),{innerValue:k,hasMask:x,moveCursorForPaste:E,updateMaskValue:C,onMaskedKeydown:T,onMaskedClick:O}=v(e,t,B,b),F=m(e,!0),L=(0,o.Fl)((()=>(0,l.yV)(k.value))),A=(0,g.Z)(V),P=(0,l.tL)(),q=(0,o.Fl)((()=>"textarea"===e.type||!0===e.autogrow)),R=(0,o.Fl)((()=>!0===q.value||["text","search","url","tel","password"].includes(e.type))),N=(0,o.Fl)((()=>{const t={...P.splitAttrs.listeners.value,onInput:V,onPaste:D,onChange:z,onBlur:U,onFocus:y.sT};return t.onCompositionstart=t.onCompositionupdate=t.onCompositionend=A,!0===x.value&&(t.onKeydown=T,t.onClick=O),!0===e.autogrow&&(t.onAnimationend=j),t})),M=(0,o.Fl)((()=>{const t={tabindex:0,"data-autofocus":!0===e.autofocus||void 0,rows:"textarea"===e.type?6:void 0,"aria-label":e.label,name:S.value,...P.splitAttrs.attributes.value,id:P.targetUid.value,maxlength:e.maxlength,disabled:!0===e.disable,readonly:!0===e.readonly};return!1===q.value&&(t.type=e.type),!0===e.autogrow&&(t.rows=1),t}));function I(){(0,_.jd)((()=>{const e=document.activeElement;null===b.value||b.value===e||null!==e&&e.id===P.targetUid.value||b.value.focus({preventScroll:!0})}))}function $(){null!==b.value&&b.value.select()}function D(n){if(!0===x.value&&!0!==e.reverseFillMask){const e=n.target;E(e,e.selectionStart,e.selectionEnd)}t("paste",n)}function V(n){if(!n||!n.target)return;if("file"===e.type)return void t("update:modelValue",n.target.files);const r=n.target.value;if(!0!==n.target.qComposing){if(!0===x.value)C(r,!1,n.inputType);else if(B(r),!0===R.value&&n.target===document.activeElement){const{selectionStart:e,selectionEnd:t}=n.target;void 0!==e&&void 0!==t&&(0,o.Y3)((()=>{n.target===document.activeElement&&0===r.indexOf(n.target.value)&&n.target.setSelectionRange(e,t)}))}!0===e.autogrow&&H()}else s.value=r}function j(e){t("animationend",e),H()}function B(n,r){d=()=>{p=null,"number"!==e.type&&!0===s.hasOwnProperty("value")&&delete s.value,e.modelValue!==n&&f!==n&&(f=n,!0===r&&(c=!0),t("update:modelValue",n),(0,o.Y3)((()=>{f===n&&(f=NaN)}))),d=void 0},"number"===e.type&&(u=!0,s.value=n),void 0!==e.debounce?(null!==p&&clearTimeout(p),s.value=n,p=setTimeout(d,e.debounce)):d()}function H(){requestAnimationFrame((()=>{const e=b.value;if(null!==e){const t=e.parentNode.style,{scrollTop:n}=e,{overflowY:o,maxHeight:r}=!0===a.platform.is.firefox?{}:window.getComputedStyle(e),l=void 0!==o&&"scroll"!==o;!0===l&&(e.style.overflowY="hidden"),t.marginBottom=e.scrollHeight-1+"px",e.style.height="1px",e.style.height=e.scrollHeight+"px",!0===l&&(e.style.overflowY=parseInt(r,10)<e.scrollHeight?"auto":"hidden"),t.marginBottom="",e.scrollTop=n}}))}function z(e){A(e),null!==p&&(clearTimeout(p),p=null),void 0!==d&&d(),t("change",e.target.value)}function U(t){void 0!==t&&(0,y.sT)(t),null!==p&&(clearTimeout(p),p=null),void 0!==d&&d(),u=!1,c=!1,delete s.value,"file"!==e.type&&setTimeout((()=>{null!==b.value&&(b.value.value=void 0!==k.value?k.value:"")}))}function W(){return!0===s.hasOwnProperty("value")?s.value:void 0!==k.value?k.value:""}(0,o.YP)((()=>e.type),(()=>{b.value&&(b.value.value=e.modelValue)})),(0,o.YP)((()=>e.modelValue),(t=>{if(!0===x.value){if(!0===c&&(c=!1,String(t)===f))return;C(t)}else k.value!==t&&(k.value=t,"number"===e.type&&!0===s.hasOwnProperty("value")&&(!0===u?u=!1:delete s.value));!0===e.autogrow&&(0,o.Y3)(H)})),(0,o.YP)((()=>e.autogrow),(e=>{!0===e?(0,o.Y3)(H):null!==b.value&&n.rows>0&&(b.value.style.height="auto")})),(0,o.YP)((()=>e.dense),(()=>{!0===e.autogrow&&(0,o.Y3)(H)})),(0,o.Jd)((()=>{U()})),(0,o.bv)((()=>{!0===e.autogrow&&H()})),Object.assign(P,{innerValue:k,fieldClass:(0,o.Fl)((()=>"q-"+(!0===q.value?"textarea":"input")+(!0===e.autogrow?" q-textarea--autogrow":""))),hasShadow:(0,o.Fl)((()=>"file"!==e.type&&"string"===typeof e.shadowText&&0!==e.shadowText.length)),inputRef:b,emitValue:B,hasValue:L,floatingLabel:(0,o.Fl)((()=>!0===L.value&&("number"!==e.type||!1===isNaN(k.value))||(0,l.yV)(e.displayValue))),getControl:()=>(0,o.h)(!0===q.value?"textarea":"input",{ref:b,class:["q-field__native q-placeholder",e.inputClass],style:e.inputStyle,...M.value,...N.value,..."file"!==e.type?{value:W()}:F.value}),getShadowControl:()=>(0,o.h)("div",{class:"q-field__native q-field__shadow absolute-bottom no-pointer-events"+(!0===q.value?"":" text-no-wrap")},[(0,o.h)("span",{class:"invisible"},W()),(0,o.h)("span",e.shadowText)])});const Z=(0,l.ZP)(P);return Object.assign(i,{focus:I,select:$,getNativeElement:()=>b.value}),(0,w.g)(i,"nativeEl",(()=>b.value)),Z}})},490:(e,t,n)=>{"use strict";n.d(t,{Z:()=>d});n(6890);var o=n(9835),r=n(499),l=n(8234),i=n(945),a=n(5987),s=n(2026),u=n(1384),c=n(1705);const d=(0,a.L)({name:"QItem",props:{...l.S,...i.$,tag:{type:String,default:"div"},active:{type:Boolean,default:null},clickable:Boolean,dense:Boolean,insetLevel:Number,tabindex:[String,Number],focused:Boolean,manualFocus:Boolean},emits:["click","keyup"],setup(e,{slots:t,emit:n}){const{proxy:{$q:a}}=(0,o.FN)(),d=(0,l.Z)(e,a),{hasLink:f,linkAttrs:p,linkClass:v,linkTag:h,navigateOnClick:m}=(0,i.Z)(),g=(0,r.iH)(null),b=(0,r.iH)(null),y=(0,o.Fl)((()=>!0===e.clickable||!0===f.value||"label"===e.tag)),_=(0,o.Fl)((()=>!0!==e.disable&&!0===y.value)),w=(0,o.Fl)((()=>"q-item q-item-type row no-wrap"+(!0===e.dense?" q-item--dense":"")+(!0===d.value?" q-item--dark":"")+(!0===f.value&&null===e.active?v.value:!0===e.active?" q-item--active"+(void 0!==e.activeClass?` ${e.activeClass}`:""):"")+(!0===e.disable?" disabled":"")+(!0===_.value?" q-item--clickable q-link cursor-pointer "+(!0===e.manualFocus?"q-manual-focusable":"q-focusable q-hoverable")+(!0===e.focused?" q-manual-focusable--focused":""):""))),S=(0,o.Fl)((()=>{if(void 0===e.insetLevel)return null;const t=!0===a.lang.rtl?"Right":"Left";return{["padding"+t]:16+56*e.insetLevel+"px"}}));function k(e){!0===_.value&&(null!==b.value&&(!0!==e.qKeyEvent&&document.activeElement===g.value?b.value.focus():document.activeElement===b.value&&g.value.focus()),m(e))}function x(e){if(!0===_.value&&!0===(0,c.So)(e,13)){(0,u.NS)(e),e.qKeyEvent=!0;const t=new MouseEvent("click",e);t.qKeyEvent=!0,g.value.dispatchEvent(t)}n("keyup",e)}function E(){const e=(0,s.Bl)(t.default,[]);return!0===_.value&&e.unshift((0,o.h)("div",{class:"q-focus-helper",tabindex:-1,ref:b})),e}return()=>{const t={ref:g,class:w.value,style:S.value,role:"listitem",onClick:k,onKeyup:x};return!0===_.value?(t.tabindex=e.tabindex||"0",Object.assign(t,p.value)):!0===y.value&&(t["aria-disabled"]="true"),(0,o.h)(h.value,t,E())}}})},3115:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var o=n(9835),r=n(5987),l=n(2026);const i=(0,r.L)({name:"QItemLabel",props:{overline:Boolean,caption:Boolean,header:Boolean,lines:[Number,String]},setup(e,{slots:t}){const n=(0,o.Fl)((()=>parseInt(e.lines,10))),r=(0,o.Fl)((()=>"q-item__label"+(!0===e.overline?" q-item__label--overline text-overline":"")+(!0===e.caption?" q-item__label--caption text-caption":"")+(!0===e.header?" q-item__label--header":"")+(1===n.value?" ellipsis":""))),i=(0,o.Fl)((()=>void 0!==e.lines&&n.value>1?{overflow:"hidden",display:"-webkit-box","-webkit-box-orient":"vertical","-webkit-line-clamp":n.value}:null));return()=>(0,o.h)("div",{style:i.value,class:r.value},(0,l.KR)(t.default))}})},1233:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var o=n(9835),r=n(5987),l=n(2026);const i=(0,r.L)({name:"QItemSection",props:{avatar:Boolean,thumbnail:Boolean,side:Boolean,top:Boolean,noWrap:Boolean},setup(e,{slots:t}){const n=(0,o.Fl)((()=>"q-item__section column q-item__section--"+(!0===e.avatar||!0===e.side||!0===e.thumbnail?"side":"main")+(!0===e.top?" q-item__section--top justify-start":" justify-center")+(!0===e.avatar?" q-item__section--avatar":"")+(!0===e.thumbnail?" q-item__section--thumbnail":"")+(!0===e.noWrap?" q-item__section--nowrap":"")));return()=>(0,o.h)("div",{class:n.value},(0,l.KR)(t.default))}})},249:(e,t,n)=>{"use strict";n.d(t,{Z:()=>f});var o=n(9835),r=n(499),l=n(7506),i=n(1868),a=n(883),s=n(5987),u=n(3701),c=n(2026),d=n(5439);const f=(0,s.L)({name:"QLayout",props:{container:Boolean,view:{type:String,default:"hhh lpr fff",validator:e=>/^(h|l)h(h|r) lpr (f|l)f(f|r)$/.test(e.toLowerCase())},onScroll:Function,onScrollHeight:Function,onResize:Function},setup(e,{slots:t,emit:n}){const{proxy:{$q:s}}=(0,o.FN)(),f=(0,r.iH)(null),p=(0,r.iH)(s.screen.height),v=(0,r.iH)(!0===e.container?0:s.screen.width),h=(0,r.iH)({position:0,direction:"down",inflectionPoint:0}),m=(0,r.iH)(0),g=(0,r.iH)(!0===l.uX.value?0:(0,u.np)()),b=(0,o.Fl)((()=>"q-layout q-layout--"+(!0===e.container?"containerized":"standard"))),y=(0,o.Fl)((()=>!1===e.container?{minHeight:s.screen.height+"px"}:null)),_=(0,o.Fl)((()=>0!==g.value?{[!0===s.lang.rtl?"left":"right"]:`${g.value}px`}:null)),w=(0,o.Fl)((()=>0!==g.value?{[!0===s.lang.rtl?"right":"left"]:0,[!0===s.lang.rtl?"left":"right"]:`-${g.value}px`,width:`calc(100% + ${g.value}px)`}:null));function S(t){if(!0===e.container||!0!==document.qScrollPrevented){const o={position:t.position.top,direction:t.direction,directionChanged:t.directionChanged,inflectionPoint:t.inflectionPoint.top,delta:t.delta.top};h.value=o,void 0!==e.onScroll&&n("scroll",o)}}function k(t){const{height:o,width:r}=t;let l=!1;p.value!==o&&(l=!0,p.value=o,void 0!==e.onScrollHeight&&n("scrollHeight",o),E()),v.value!==r&&(l=!0,v.value=r),!0===l&&void 0!==e.onResize&&n("resize",t)}function x({height:e}){m.value!==e&&(m.value=e,E())}function E(){if(!0===e.container){const e=p.value>m.value?(0,u.np)():0;g.value!==e&&(g.value=e)}}let C=null;const T={instances:{},view:(0,o.Fl)((()=>e.view)),isContainer:(0,o.Fl)((()=>e.container)),rootRef:f,height:p,containerHeight:m,scrollbarWidth:g,totalWidth:(0,o.Fl)((()=>v.value+g.value)),rows:(0,o.Fl)((()=>{const t=e.view.toLowerCase().split(" ");return{top:t[0].split(""),middle:t[1].split(""),bottom:t[2].split("")}})),header:(0,r.qj)({size:0,offset:0,space:!1}),right:(0,r.qj)({size:300,offset:0,space:!1}),footer:(0,r.qj)({size:0,offset:0,space:!1}),left:(0,r.qj)({size:300,offset:0,space:!1}),scroll:h,animate(){null!==C?clearTimeout(C):document.body.classList.add("q-body--layout-animate"),C=setTimeout((()=>{C=null,document.body.classList.remove("q-body--layout-animate")}),155)},update(e,t,n){T[e][t]=n}};if((0,o.JJ)(d.YE,T),(0,u.np)()>0){let O=null;const F=document.body;function L(){O=null,F.classList.remove("hide-scrollbar")}function A(){if(null===O){if(F.scrollHeight>s.screen.height)return;F.classList.add("hide-scrollbar")}else clearTimeout(O);O=setTimeout(L,300)}function P(e){null!==O&&"remove"===e&&(clearTimeout(O),L()),window[`${e}EventListener`]("resize",A)}(0,o.YP)((()=>!0!==e.container?"add":"remove"),P),!0!==e.container&&P("add"),(0,o.Ah)((()=>{P("remove")}))}return()=>{const n=(0,c.vs)(t.default,[(0,o.h)(i.Z,{onScroll:S}),(0,o.h)(a.Z,{onResize:k})]),r=(0,o.h)("div",{class:b.value,style:y.value,ref:!0===e.container?void 0:f,tabindex:-1},n);return!0===e.container?(0,o.h)("div",{class:"q-layout-container overflow-hidden",ref:f},[(0,o.h)(a.Z,{onResize:x}),(0,o.h)("div",{class:"absolute-full",style:_.value},[(0,o.h)("div",{class:"scroll",style:w.value},[r])])]):r}}})},9885:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(9835),r=n(5987),l=n(2026),i=n(5439);const a=(0,r.L)({name:"QPage",props:{padding:Boolean,styleFn:Function},setup(e,{slots:t}){const{proxy:{$q:n}}=(0,o.FN)(),r=(0,o.f3)(i.YE,i.qO);if(r===i.qO)return console.error("QPage needs to be a deep child of QLayout"),i.qO;const a=(0,o.f3)(i.Mw,i.qO);if(a===i.qO)return console.error("QPage needs to be child of QPageContainer"),i.qO;const s=(0,o.Fl)((()=>{const t=(!0===r.header.space?r.header.size:0)+(!0===r.footer.space?r.footer.size:0);if("function"===typeof e.styleFn){const o=!0===r.isContainer.value?r.containerHeight.value:n.screen.height;return e.styleFn(t,o)}return{minHeight:!0===r.isContainer.value?r.containerHeight.value-t+"px":0===n.screen.height?0!==t?`calc(100vh - ${t}px)`:"100vh":n.screen.height-t+"px"}})),u=(0,o.Fl)((()=>"q-page"+(!0===e.padding?" q-layout-padding":"")));return()=>(0,o.h)("main",{class:u.value,style:s.value},(0,l.KR)(t.default))}})},2133:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(9835),r=n(5987),l=n(2026),i=n(5439);const a=(0,r.L)({name:"QPageContainer",setup(e,{slots:t}){const{proxy:{$q:n}}=(0,o.FN)(),r=(0,o.f3)(i.YE,i.qO);if(r===i.qO)return console.error("QPageContainer needs to be child of QLayout"),i.qO;(0,o.JJ)(i.Mw,!0);const a=(0,o.Fl)((()=>{const e={};return!0===r.header.space&&(e.paddingTop=`${r.header.size}px`),!0===r.right.space&&(e["padding"+(!0===n.lang.rtl?"Left":"Right")]=`${r.right.size}px`),!0===r.footer.space&&(e.paddingBottom=`${r.footer.size}px`),!0===r.left.space&&(e["padding"+(!0===n.lang.rtl?"Right":"Left")]=`${r.left.size}px`),e}));return()=>(0,o.h)("div",{class:"q-page-container",style:a.value},(0,l.KR)(t.default))}})},1480:(e,t,n)=>{"use strict";n.d(t,{Z:()=>h});n(9665);var o=n(9835),r=n(499),l=n(2857),i=n(8234),a=n(244),s=n(5917),u=n(9256),c=n(5987),d=n(9480),f=n(1384),p=n(2026);const v=(0,o.h)("svg",{key:"svg",class:"q-radio__bg absolute non-selectable",viewBox:"0 0 24 24"},[(0,o.h)("path",{d:"M12,22a10,10 0 0 1 -10,-10a10,10 0 0 1 10,-10a10,10 0 0 1 10,10a10,10 0 0 1 -10,10m0,-22a12,12 0 0 0 -12,12a12,12 0 0 0 12,12a12,12 0 0 0 12,-12a12,12 0 0 0 -12,-12"}),(0,o.h)("path",{class:"q-radio__check",d:"M12,6a6,6 0 0 0 -6,6a6,6 0 0 0 6,6a6,6 0 0 0 6,-6a6,6 0 0 0 -6,-6"})]),h=(0,c.L)({name:"QRadio",props:{...i.S,...a.LU,...u.Fz,modelValue:{required:!0},val:{required:!0},label:String,leftLabel:Boolean,checkedIcon:String,uncheckedIcon:String,color:String,keepColor:Boolean,dense:Boolean,disable:Boolean,tabindex:[String,Number]},emits:["update:modelValue"],setup(e,{slots:t,emit:n}){const{proxy:c}=(0,o.FN)(),h=(0,i.Z)(e,c.$q),m=(0,a.ZP)(e,d.Z),g=(0,r.iH)(null),{refocusTargetEl:b,refocusTarget:y}=(0,s.Z)(e,g),_=(0,o.Fl)((()=>(0,r.IU)(e.modelValue)===(0,r.IU)(e.val))),w=(0,o.Fl)((()=>"q-radio cursor-pointer no-outline row inline no-wrap items-center"+(!0===e.disable?" disabled":"")+(!0===h.value?" q-radio--dark":"")+(!0===e.dense?" q-radio--dense":"")+(!0===e.leftLabel?" reverse":""))),S=(0,o.Fl)((()=>{const t=void 0===e.color||!0!==e.keepColor&&!0!==_.value?"":` text-${e.color}`;return`q-radio__inner relative-position q-radio__inner--${!0===_.value?"truthy":"falsy"}${t}`})),k=(0,o.Fl)((()=>(!0===_.value?e.checkedIcon:e.uncheckedIcon)||null)),x=(0,o.Fl)((()=>!0===e.disable?-1:e.tabindex||0)),E=(0,o.Fl)((()=>{const t={type:"radio"};return void 0!==e.name&&Object.assign(t,{".checked":!0===_.value,"^checked":!0===_.value?"checked":void 0,name:e.name,value:e.val}),t})),C=(0,u.eX)(E);function T(t){void 0!==t&&((0,f.NS)(t),y(t)),!0!==e.disable&&!0!==_.value&&n("update:modelValue",e.val,t)}function O(e){13!==e.keyCode&&32!==e.keyCode||(0,f.NS)(e)}function F(e){13!==e.keyCode&&32!==e.keyCode||T(e)}return Object.assign(c,{set:T}),()=>{const n=null!==k.value?[(0,o.h)("div",{key:"icon",class:"q-radio__icon-container absolute-full flex flex-center no-wrap"},[(0,o.h)(l.Z,{class:"q-radio__icon",name:k.value})])]:[v];!0!==e.disable&&C(n,"unshift"," q-radio__native q-ma-none q-pa-none");const r=[(0,o.h)("div",{class:S.value,style:m.value,"aria-hidden":"true"},n)];null!==b.value&&r.push(b.value);const i=void 0!==e.label?(0,p.vs)(t.default,[e.label]):(0,p.KR)(t.default);return void 0!==i&&r.push((0,o.h)("div",{class:"q-radio__label q-anchor--skip"},i)),(0,o.h)("div",{ref:g,class:w.value,tabindex:x.value,role:"radio","aria-label":e.label,"aria-checked":!0===_.value?"true":"false","aria-disabled":!0===e.disable?"true":void 0,onClick:T,onKeydown:O,onKeyup:F},r)}}})},883:(e,t,n)=>{"use strict";n.d(t,{Z:()=>d});var o=n(9835),r=n(499),l=n(7506);function i(){const e=(0,r.iH)(!l.uX.value);return!1===e.value&&(0,o.bv)((()=>{e.value=!0})),e}var a=n(5987),s=n(1384);const u="undefined"!==typeof ResizeObserver,c=!0===u?{}:{style:"display:block;position:absolute;top:0;left:0;right:0;bottom:0;height:100%;width:100%;overflow:hidden;pointer-events:none;z-index:-1;",url:"about:blank"},d=(0,a.L)({name:"QResizeObserver",props:{debounce:{type:[String,Number],default:100}},emits:["resize"],setup(e,{emit:t}){let n,r=null,l={width:-1,height:-1};function a(t){!0===t||0===e.debounce||"0"===e.debounce?d():null===r&&(r=setTimeout(d,e.debounce))}function d(){if(null!==r&&(clearTimeout(r),r=null),n){const{offsetWidth:e,offsetHeight:o}=n;e===l.width&&o===l.height||(l={width:e,height:o},t("resize",l))}}const{proxy:f}=(0,o.FN)();if(!0===u){let p;const v=e=>{n=f.$el.parentNode,n?(p=new ResizeObserver(a),p.observe(n),d()):!0!==e&&(0,o.Y3)((()=>{v(!0)}))};return(0,o.bv)((()=>{v()})),(0,o.Jd)((()=>{null!==r&&clearTimeout(r),void 0!==p&&(void 0!==p.disconnect?p.disconnect():n&&p.unobserve(n))})),s.ZT}{const h=i();let m;function g(){null!==r&&(clearTimeout(r),r=null),void 0!==m&&(void 0!==m.removeEventListener&&m.removeEventListener("resize",a,s.listenOpts.passive),m=void 0)}function b(){g(),n&&n.contentDocument&&(m=n.contentDocument.defaultView,m.addEventListener("resize",a,s.listenOpts.passive),d())}return(0,o.bv)((()=>{(0,o.Y3)((()=>{n=f.$el,n&&b()}))})),(0,o.Jd)(g),f.trigger=a,()=>{if(!0===h.value)return(0,o.h)("object",{style:c.style,tabindex:-1,type:"text/html",data:c.url,"aria-hidden":"true",onLoad:b})}}}})},6663:(e,t,n)=>{"use strict";n.d(t,{Z:()=>b});var o=n(499),r=n(9835),l=n(8234),i=n(883),a=n(1868),s=n(2873),u=n(5987),c=n(321),d=n(3701),f=n(2026),p=n(899);const v=["vertical","horizontal"],h={vertical:{offset:"offsetY",scroll:"scrollTop",dir:"down",dist:"y"},horizontal:{offset:"offsetX",scroll:"scrollLeft",dir:"right",dist:"x"}},m={prevent:!0,mouse:!0,mouseAllDir:!0},g=e=>e>=250?50:Math.ceil(e/5),b=(0,u.L)({name:"QScrollArea",props:{...l.S,thumbStyle:Object,verticalThumbStyle:Object,horizontalThumbStyle:Object,barStyle:[Array,String,Object],verticalBarStyle:[Array,String,Object],horizontalBarStyle:[Array,String,Object],contentStyle:[Array,String,Object],contentActiveStyle:[Array,String,Object],delay:{type:[String,Number],default:1e3},visible:{type:Boolean,default:null},tabindex:[String,Number],onScroll:Function},setup(e,{slots:t,emit:n}){const u=(0,o.iH)(!1),b=(0,o.iH)(!1),y=(0,o.iH)(!1),_={vertical:(0,o.iH)(0),horizontal:(0,o.iH)(0)},w={vertical:{ref:(0,o.iH)(null),position:(0,o.iH)(0),size:(0,o.iH)(0)},horizontal:{ref:(0,o.iH)(null),position:(0,o.iH)(0),size:(0,o.iH)(0)}},{proxy:S}=(0,r.FN)(),k=(0,l.Z)(e,S.$q);let x,E=null;const C=(0,o.iH)(null),T=(0,r.Fl)((()=>"q-scrollarea"+(!0===k.value?" q-scrollarea--dark":"")));w.vertical.percentage=(0,r.Fl)((()=>{const e=w.vertical.size.value-_.vertical.value;if(e<=0)return 0;const t=(0,c.vX)(w.vertical.position.value/e,0,1);return Math.round(1e4*t)/1e4})),w.vertical.thumbHidden=(0,r.Fl)((()=>!0!==(null===e.visible?y.value:e.visible)&&!1===u.value&&!1===b.value||w.vertical.size.value<=_.vertical.value+1)),w.vertical.thumbStart=(0,r.Fl)((()=>w.vertical.percentage.value*(_.vertical.value-w.vertical.thumbSize.value))),w.vertical.thumbSize=(0,r.Fl)((()=>Math.round((0,c.vX)(_.vertical.value*_.vertical.value/w.vertical.size.value,g(_.vertical.value),_.vertical.value)))),w.vertical.style=(0,r.Fl)((()=>({...e.thumbStyle,...e.verticalThumbStyle,top:`${w.vertical.thumbStart.value}px`,height:`${w.vertical.thumbSize.value}px`}))),w.vertical.thumbClass=(0,r.Fl)((()=>"q-scrollarea__thumb q-scrollarea__thumb--v absolute-right"+(!0===w.vertical.thumbHidden.value?" q-scrollarea__thumb--invisible":""))),w.vertical.barClass=(0,r.Fl)((()=>"q-scrollarea__bar q-scrollarea__bar--v absolute-right"+(!0===w.vertical.thumbHidden.value?" q-scrollarea__bar--invisible":""))),w.horizontal.percentage=(0,r.Fl)((()=>{const e=w.horizontal.size.value-_.horizontal.value;if(e<=0)return 0;const t=(0,c.vX)(Math.abs(w.horizontal.position.value)/e,0,1);return Math.round(1e4*t)/1e4})),w.horizontal.thumbHidden=(0,r.Fl)((()=>!0!==(null===e.visible?y.value:e.visible)&&!1===u.value&&!1===b.value||w.horizontal.size.value<=_.horizontal.value+1)),w.horizontal.thumbStart=(0,r.Fl)((()=>w.horizontal.percentage.value*(_.horizontal.value-w.horizontal.thumbSize.value))),w.horizontal.thumbSize=(0,r.Fl)((()=>Math.round((0,c.vX)(_.horizontal.value*_.horizontal.value/w.horizontal.size.value,g(_.horizontal.value),_.horizontal.value)))),w.horizontal.style=(0,r.Fl)((()=>({...e.thumbStyle,...e.horizontalThumbStyle,[!0===S.$q.lang.rtl?"right":"left"]:`${w.horizontal.thumbStart.value}px`,width:`${w.horizontal.thumbSize.value}px`}))),w.horizontal.thumbClass=(0,r.Fl)((()=>"q-scrollarea__thumb q-scrollarea__thumb--h absolute-bottom"+(!0===w.horizontal.thumbHidden.value?" q-scrollarea__thumb--invisible":""))),w.horizontal.barClass=(0,r.Fl)((()=>"q-scrollarea__bar q-scrollarea__bar--h absolute-bottom"+(!0===w.horizontal.thumbHidden.value?" q-scrollarea__bar--invisible":"")));const O=(0,r.Fl)((()=>!0===w.vertical.thumbHidden.value&&!0===w.horizontal.thumbHidden.value?e.contentStyle:e.contentActiveStyle)),F=[[s.Z,e=>{I(e,"vertical")},void 0,{vertical:!0,...m}]],L=[[s.Z,e=>{I(e,"horizontal")},void 0,{horizontal:!0,...m}]];function A(){const e={};return v.forEach((t=>{const n=w[t];e[t+"Position"]=n.position.value,e[t+"Percentage"]=n.percentage.value,e[t+"Size"]=n.size.value,e[t+"ContainerSize"]=_[t].value})),e}const P=(0,p.Z)((()=>{const e=A();e.ref=S,n("scroll",e)}),0);function q(e,t,n){if(!1===v.includes(e))return void console.error("[QScrollArea]: wrong first param of setScrollPosition (vertical/horizontal)");const o="vertical"===e?d.f3:d.ik;o(C.value,t,n)}function R({height:e,width:t}){let n=!1;_.vertical.value!==e&&(_.vertical.value=e,n=!0),_.horizontal.value!==t&&(_.horizontal.value=t,n=!0),!0===n&&j()}function N({position:e}){let t=!1;w.vertical.position.value!==e.top&&(w.vertical.position.value=e.top,t=!0),w.horizontal.position.value!==e.left&&(w.horizontal.position.value=e.left,t=!0),!0===t&&j()}function M({height:e,width:t}){w.horizontal.size.value!==t&&(w.horizontal.size.value=t,j()),w.vertical.size.value!==e&&(w.vertical.size.value=e,j())}function I(e,t){const n=w[t];if(!0===e.isFirst){if(!0===n.thumbHidden.value)return;x=n.position.value,b.value=!0}else if(!0!==b.value)return;!0===e.isFinal&&(b.value=!1);const o=h[t],r=_[t].value,l=(n.size.value-r)/(r-n.thumbSize.value),i=e.distance[o.dist],a=x+(e.direction===o.dir?1:-1)*i*l;B(a,t)}function $(e,t){const n=w[t];if(!0!==n.thumbHidden.value){const o=e[h[t].offset];if(o<n.thumbStart.value||o>n.thumbStart.value+n.thumbSize.value){const e=o-n.thumbSize.value/2;B(e/_[t].value*n.size.value,t)}null!==n.ref.value&&n.ref.value.dispatchEvent(new MouseEvent(e.type,e))}}function D(e){$(e,"vertical")}function V(e){$(e,"horizontal")}function j(){u.value=!0,null!==E&&clearTimeout(E),E=setTimeout((()=>{E=null,u.value=!1}),e.delay),void 0!==e.onScroll&&P()}function B(e,t){C.value[h[t].scroll]=e}function H(){y.value=!0}function z(){y.value=!1}let U=null;return(0,r.YP)((()=>S.$q.lang.rtl),(e=>{null!==C.value&&(0,d.ik)(C.value,Math.abs(w.horizontal.position.value)*(!0===e?-1:1))})),(0,r.se)((()=>{U={top:w.vertical.position.value,left:w.horizontal.position.value}})),(0,r.dl)((()=>{if(null===U)return;const e=C.value;null!==e&&((0,d.ik)(e,U.left),(0,d.f3)(e,U.top))})),(0,r.Jd)(P.cancel),Object.assign(S,{getScrollTarget:()=>C.value,getScroll:A,getScrollPosition:()=>({top:w.vertical.position.value,left:w.horizontal.position.value}),getScrollPercentage:()=>({top:w.vertical.percentage.value,left:w.horizontal.percentage.value}),setScrollPosition:q,setScrollPercentage(e,t,n){q(e,t*(w[e].size.value-_[e].value)*("horizontal"===e&&!0===S.$q.lang.rtl?-1:1),n)}}),()=>(0,r.h)("div",{class:T.value,onMouseenter:H,onMouseleave:z},[(0,r.h)("div",{ref:C,class:"q-scrollarea__container scroll relative-position fit hide-scrollbar",tabindex:void 0!==e.tabindex?e.tabindex:void 0},[(0,r.h)("div",{class:"q-scrollarea__content absolute",style:O.value},(0,f.vs)(t.default,[(0,r.h)(i.Z,{debounce:0,onResize:M})])),(0,r.h)(a.Z,{axis:"both",onScroll:N})]),(0,r.h)(i.Z,{debounce:0,onResize:R}),(0,r.h)("div",{class:w.vertical.barClass.value,style:[e.barStyle,e.verticalBarStyle],"aria-hidden":"true",onMousedown:D}),(0,r.h)("div",{class:w.horizontal.barClass.value,style:[e.barStyle,e.horizontalBarStyle],"aria-hidden":"true",onMousedown:V}),(0,r.wy)((0,r.h)("div",{ref:w.vertical.ref,class:w.vertical.thumbClass.value,style:w.vertical.style.value,"aria-hidden":"true"}),F),(0,r.wy)((0,r.h)("div",{ref:w.horizontal.ref,class:w.horizontal.thumbClass.value,style:w.horizontal.style.value,"aria-hidden":"true"}),L)])}})},1868:(e,t,n)=>{"use strict";n.d(t,{Z:()=>u});var o=n(9835),r=n(5987),l=n(3701),i=n(1384);const{passive:a}=i.listenOpts,s=["both","horizontal","vertical"],u=(0,r.L)({name:"QScrollObserver",props:{axis:{type:String,validator:e=>s.includes(e),default:"vertical"},debounce:[String,Number],scrollTarget:{default:void 0}},emits:["scroll"],setup(e,{emit:t}){const n={position:{top:0,left:0},direction:"down",directionChanged:!1,delta:{top:0,left:0},inflectionPoint:{top:0,left:0}};let r,s,u=null;function c(){null!==u&&u();const o=Math.max(0,(0,l.u3)(r)),i=(0,l.OI)(r),a={top:o-n.position.top,left:i-n.position.left};if("vertical"===e.axis&&0===a.top||"horizontal"===e.axis&&0===a.left)return;const s=Math.abs(a.top)>=Math.abs(a.left)?a.top<0?"up":"down":a.left<0?"left":"right";n.position={top:o,left:i},n.directionChanged=n.direction!==s,n.delta=a,!0===n.directionChanged&&(n.direction=s,n.inflectionPoint=n.position),t("scroll",{...n})}function d(){r=(0,l.b0)(s,e.scrollTarget),r.addEventListener("scroll",p,a),p(!0)}function f(){void 0!==r&&(r.removeEventListener("scroll",p,a),r=void 0)}function p(t){if(!0===t||0===e.debounce||"0"===e.debounce)c();else if(null===u){const[t,n]=e.debounce?[setTimeout(c,e.debounce),clearTimeout]:[requestAnimationFrame(c),cancelAnimationFrame];u=()=>{n(t),u=null}}}(0,o.YP)((()=>e.scrollTarget),(()=>{f(),d()}));const{proxy:v}=(0,o.FN)();return(0,o.YP)((()=>v.$q.lang.rtl),c),(0,o.bv)((()=>{s=v.$el.parentNode,d()})),(0,o.Jd)((()=>{null!==u&&u(),f()})),Object.assign(v,{trigger:p,getPosition:()=>n}),i.ZT}})},9080:(e,t,n)=>{"use strict";n.d(t,{Z:()=>ye});n(9665);var o=n(9835),r=n(499),l=n(1486),i=n(5987);const a=(0,i.L)({name:"QField",inheritAttrs:!1,props:l.Cl,emits:l.HJ,setup(){return(0,l.ZP)((0,l.tL)())}});var s=n(2857),u=n(7691),c=n(490),d=n(1233),f=n(3115),p=n(1957),v=n(4397),h=n(4088),m=n(3842),g=n(8234),b=n(1518),y=n(431),_=n(6916),w=n(2695),S=n(2909),k=n(3701),x=n(1384),E=n(2026),C=n(7506),T=n(1705);const O=[];let F;function L(e){F=27===e.keyCode}function A(){!0===F&&(F=!1)}function P(e){!0===F&&(F=!1,!0===(0,T.So)(e,27)&&O[O.length-1](e))}function q(e){window[e]("keydown",L),window[e]("blur",A),window[e]("keyup",P),F=!1}function R(e){!0===C.client.is.desktop&&(O.push(e),1===O.length&&q("addEventListener"))}function N(e){const t=O.indexOf(e);t>-1&&(O.splice(t,1),0===O.length&&q("removeEventListener"))}const M=[];function I(e){M[M.length-1](e)}function $(e){!0===C.client.is.desktop&&(M.push(e),1===M.length&&document.body.addEventListener("focusin",I))}function D(e){const t=M.indexOf(e);t>-1&&(M.splice(t,1),0===M.length&&document.body.removeEventListener("focusin",I))}var V=n(223),j=n(9092),B=n(7026),H=n(9388);const z=(0,i.L)({name:"QMenu",inheritAttrs:!1,props:{...v.u,...m.vr,...g.S,...y.D,persistent:Boolean,autoClose:Boolean,separateClosePopup:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,fit:Boolean,cover:Boolean,square:Boolean,anchor:{type:String,validator:H.$},self:{type:String,validator:H.$},offset:{type:Array,validator:H.io},scrollTarget:{default:void 0},touchPosition:Boolean,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null}},emits:[...m.gH,"click","escapeKey"],setup(e,{slots:t,emit:n,attrs:l}){let i,a,s,u=null;const c=(0,o.FN)(),{proxy:d}=c,{$q:f}=d,C=(0,r.iH)(null),T=(0,r.iH)(!1),O=(0,o.Fl)((()=>!0!==e.persistent&&!0!==e.noRouteDismiss)),F=(0,g.Z)(e,f),{registerTick:L,removeTick:A}=(0,_.Z)(),{registerTimeout:P}=(0,w.Z)(),{transitionProps:q,transitionStyle:M}=(0,y.Z)(e),{localScrollTarget:I,changeScrollEvent:z,unconfigureScrollTarget:U}=(0,h.Z)(e,se),{anchorEl:W,canShow:Z}=(0,v.Z)({showing:T}),{hide:K}=(0,m.ZP)({showing:T,canShow:Z,handleShow:le,handleHide:ie,hideOnRouteChange:O,processOnMount:!0}),{showPortal:Y,hidePortal:J,renderPortal:X}=(0,b.Z)(c,C,pe,"menu"),G={anchorEl:W,innerRef:C,onClickOutside(t){if(!0!==e.persistent&&!0===T.value)return K(t),("touchstart"===t.type||t.target.classList.contains("q-dialog__backdrop"))&&(0,x.NS)(t),!0}},Q=(0,o.Fl)((()=>(0,H.li)(e.anchor||(!0===e.cover?"center middle":"bottom start"),f.lang.rtl))),ee=(0,o.Fl)((()=>!0===e.cover?Q.value:(0,H.li)(e.self||"top start",f.lang.rtl))),te=(0,o.Fl)((()=>(!0===e.square?" q-menu--square":"")+(!0===F.value?" q-menu--dark q-dark":""))),ne=(0,o.Fl)((()=>!0===e.autoClose?{onClick:ue}:{})),oe=(0,o.Fl)((()=>!0===T.value&&!0!==e.persistent));function re(){(0,B.jd)((()=>{let e=C.value;e&&!0!==e.contains(document.activeElement)&&(e=e.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||e.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||e.querySelector("[autofocus], [data-autofocus]")||e,e.focus({preventScroll:!0}))}))}function le(t){if(u=!1===e.noRefocus?document.activeElement:null,$(ce),Y(),se(),i=void 0,void 0!==t&&(e.touchPosition||e.contextMenu)){const e=(0,x.FK)(t);if(void 0!==e.left){const{top:t,left:n}=W.value.getBoundingClientRect();i={left:e.left-n,top:e.top-t}}}void 0===a&&(a=(0,o.YP)((()=>f.screen.width+"|"+f.screen.height+"|"+e.self+"|"+e.anchor+"|"+f.lang.rtl),fe)),!0!==e.noFocus&&document.activeElement.blur(),L((()=>{fe(),!0!==e.noFocus&&re()})),P((()=>{!0===f.platform.is.ios&&(s=e.autoClose,C.value.click()),fe(),Y(!0),n("show",t)}),e.transitionDuration)}function ie(t){A(),J(),ae(!0),null===u||void 0!==t&&!0===t.qClickOutside||(((t&&0===t.type.indexOf("key")?u.closest('[tabindex]:not([tabindex^="-"])'):void 0)||u).focus(),u=null),P((()=>{J(!0),n("hide",t)}),e.transitionDuration)}function ae(e){i=void 0,void 0!==a&&(a(),a=void 0),!0!==e&&!0!==T.value||(D(ce),U(),(0,j.D)(G),N(de)),!0!==e&&(u=null)}function se(){null===W.value&&void 0===e.scrollTarget||(I.value=(0,k.b0)(W.value,e.scrollTarget),z(I.value,fe))}function ue(e){!0!==s?((0,S.AH)(d,e),n("click",e)):s=!1}function ce(t){!0===oe.value&&!0!==e.noFocus&&!0!==(0,V.mY)(C.value,t.target)&&re()}function de(e){n("escapeKey"),K(e)}function fe(){(0,H.wq)({targetEl:C.value,offset:e.offset,anchorEl:W.value,anchorOrigin:Q.value,selfOrigin:ee.value,absoluteOffset:i,fit:e.fit,cover:e.cover,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}function pe(){return(0,o.h)(p.uT,q.value,(()=>!0===T.value?(0,o.h)("div",{role:"menu",...l,ref:C,tabindex:-1,class:["q-menu q-position-engine scroll"+te.value,l.class],style:[l.style,M.value],...ne.value},(0,E.KR)(t.default)):null))}return(0,o.YP)(oe,(e=>{!0===e?(R(de),(0,j.m)(G)):(N(de),(0,j.D)(G))})),(0,o.Jd)(ae),Object.assign(d,{focus:re,updatePosition:fe}),X}});var U=n(5310);function W(e,t,n){let r;function l(){void 0!==r&&(U.Z.remove(r),r=void 0)}return(0,o.Jd)((()=>{!0===e.value&&l()})),{removeFromHistory:l,addToHistory(){r={condition:()=>!0===n.value,handler:t},U.Z.add(r)}}}var Z=n(5984);function K(){let e;return{preventBodyScroll(t){t===e||void 0===e&&!0!==t||(e=t,(0,Z.Z)(t))}}}let Y=0;const J={standard:"fixed-full flex-center",top:"fixed-top justify-center",bottom:"fixed-bottom justify-center",right:"fixed-right items-center",left:"fixed-left items-center"},X={standard:["scale","scale"],top:["slide-down","slide-up"],bottom:["slide-up","slide-down"],right:["slide-left","slide-right"],left:["slide-right","slide-left"]},G=(0,i.L)({name:"QDialog",inheritAttrs:!1,props:{...m.vr,...y.D,transitionShow:String,transitionHide:String,persistent:Boolean,autoClose:Boolean,allowFocusOutside:Boolean,noEscDismiss:Boolean,noBackdropDismiss:Boolean,noRouteDismiss:Boolean,noRefocus:Boolean,noFocus:Boolean,noShake:Boolean,seamless:Boolean,maximized:Boolean,fullWidth:Boolean,fullHeight:Boolean,square:Boolean,position:{type:String,default:"standard",validator:e=>"standard"===e||["top","bottom","left","right"].includes(e)}},emits:[...m.gH,"shake","click","escapeKey"],setup(e,{slots:t,emit:n,attrs:l}){const i=(0,o.FN)(),a=(0,r.iH)(null),s=(0,r.iH)(!1),u=(0,r.iH)(!1);let c,d,f=null,v=null;const h=(0,o.Fl)((()=>!0!==e.persistent&&!0!==e.noRouteDismiss&&!0!==e.seamless)),{preventBodyScroll:g}=K(),{registerTimeout:S}=(0,w.Z)(),{registerTick:k,removeTick:x}=(0,_.Z)(),{transitionProps:C,transitionStyle:T}=(0,y.Z)(e,(()=>X[e.position][0]),(()=>X[e.position][1])),{showPortal:O,hidePortal:F,portalIsAccessible:L,renderPortal:A}=(0,b.Z)(i,a,ie,"dialog"),{hide:P}=(0,m.ZP)({showing:s,hideOnRouteChange:h,handleShow:U,handleHide:Z,processOnMount:!0}),{addToHistory:q,removeFromHistory:M}=W(s,P,h),I=(0,o.Fl)((()=>"q-dialog__inner flex no-pointer-events q-dialog__inner--"+(!0===e.maximized?"maximized":"minimized")+` q-dialog__inner--${e.position} ${J[e.position]}`+(!0===u.value?" q-dialog__inner--animating":"")+(!0===e.fullWidth?" q-dialog__inner--fullwidth":"")+(!0===e.fullHeight?" q-dialog__inner--fullheight":"")+(!0===e.square?" q-dialog__inner--square":""))),j=(0,o.Fl)((()=>!0===s.value&&!0!==e.seamless)),H=(0,o.Fl)((()=>!0===e.autoClose?{onClick:oe}:{})),z=(0,o.Fl)((()=>["q-dialog fullscreen no-pointer-events q-dialog--"+(!0===j.value?"modal":"seamless"),l.class]));function U(t){q(),v=!1===e.noRefocus&&null!==document.activeElement?document.activeElement:null,ne(e.maximized),O(),u.value=!0,!0!==e.noFocus?(null!==document.activeElement&&document.activeElement.blur(),k(G)):x(),S((()=>{if(!0===i.proxy.$q.platform.is.ios){if(!0!==e.seamless&&document.activeElement){const{top:e,bottom:t}=document.activeElement.getBoundingClientRect(),{innerHeight:n}=window,o=void 0!==window.visualViewport?window.visualViewport.height:n;e>0&&t>o/2&&(document.scrollingElement.scrollTop=Math.min(document.scrollingElement.scrollHeight-o,t>=n?1/0:Math.ceil(document.scrollingElement.scrollTop+t-o/2))),document.activeElement.scrollIntoView()}d=!0,a.value.click(),d=!1}O(!0),u.value=!1,n("show",t)}),e.transitionDuration)}function Z(t){x(),M(),te(!0),u.value=!0,F(),null!==v&&(((t&&0===t.type.indexOf("key")?v.closest('[tabindex]:not([tabindex^="-"])'):void 0)||v).focus(),v=null),S((()=>{F(!0),u.value=!1,n("hide",t)}),e.transitionDuration)}function G(e){(0,B.jd)((()=>{let t=a.value;null!==t&&!0!==t.contains(document.activeElement)&&(t=(""!==e?t.querySelector(e):null)||t.querySelector("[autofocus][tabindex], [data-autofocus][tabindex]")||t.querySelector("[autofocus] [tabindex], [data-autofocus] [tabindex]")||t.querySelector("[autofocus], [data-autofocus]")||t,t.focus({preventScroll:!0}))}))}function Q(e){e&&"function"===typeof e.focus?e.focus({preventScroll:!0}):G(),n("shake");const t=a.value;null!==t&&(t.classList.remove("q-animate--scale"),t.classList.add("q-animate--scale"),null!==f&&clearTimeout(f),f=setTimeout((()=>{f=null,null!==a.value&&(t.classList.remove("q-animate--scale"),G())}),170))}function ee(){!0!==e.seamless&&(!0===e.persistent||!0===e.noEscDismiss?!0!==e.maximized&&!0!==e.noShake&&Q():(n("escapeKey"),P()))}function te(t){null!==f&&(clearTimeout(f),f=null),!0!==t&&!0!==s.value||(ne(!1),!0!==e.seamless&&(g(!1),D(le),N(ee))),!0!==t&&(v=null)}function ne(e){!0===e?!0!==c&&(Y<1&&document.body.classList.add("q-body--dialog"),Y++,c=!0):!0===c&&(Y<2&&document.body.classList.remove("q-body--dialog"),Y--,c=!1)}function oe(e){!0!==d&&(P(e),n("click",e))}function re(t){!0!==e.persistent&&!0!==e.noBackdropDismiss?P(t):!0!==e.noShake&&Q()}function le(t){!0!==e.allowFocusOutside&&!0===L.value&&!0!==(0,V.mY)(a.value,t.target)&&G('[tabindex]:not([tabindex="-1"])')}function ie(){return(0,o.h)("div",{role:"dialog","aria-modal":!0===j.value?"true":"false",...l,class:z.value},[(0,o.h)(p.uT,{name:"q-transition--fade",appear:!0},(()=>!0===j.value?(0,o.h)("div",{class:"q-dialog__backdrop fixed-full",style:T.value,"aria-hidden":"true",tabindex:-1,onClick:re}):null)),(0,o.h)(p.uT,C.value,(()=>!0===s.value?(0,o.h)("div",{ref:a,class:I.value,style:T.value,tabindex:-1,...H.value},(0,E.KR)(t.default)):null))])}return(0,o.YP)((()=>e.maximized),(e=>{!0===s.value&&ne(e)})),(0,o.YP)(j,(e=>{g(e),!0===e?($(le),R(ee)):(D(le),N(ee))})),Object.assign(i.proxy,{focus:G,shake:Q,__updateRefocusTarget(e){v=e||null}}),(0,o.Jd)(te),A}});var Q=n(899),ee=n(8383);const te=1e3,ne=["start","center","end","start-force","center-force","end-force"],oe=Array.prototype.filter,re=void 0===window.getComputedStyle(document.body).overflowAnchor?x.ZT:function(e,t){null!==e&&(void 0!==e._qOverflowAnimationFrame&&cancelAnimationFrame(e._qOverflowAnimationFrame),e._qOverflowAnimationFrame=requestAnimationFrame((()=>{if(null===e)return;e._qOverflowAnimationFrame=void 0;const n=e.children||[];oe.call(n,(e=>e.dataset&&void 0!==e.dataset.qVsAnchor)).forEach((e=>{delete e.dataset.qVsAnchor}));const o=n[t];o&&o.dataset&&(o.dataset.qVsAnchor="")})))};function le(e,t){return e+t}function ie(e,t,n,o,r,l,i,a){const s=e===window?document.scrollingElement||document.documentElement:e,u=!0===r?"offsetWidth":"offsetHeight",c={scrollStart:0,scrollViewSize:-i-a,scrollMaxSize:0,offsetStart:-i,offsetEnd:-a};if(!0===r?(e===window?(c.scrollStart=window.pageXOffset||window.scrollX||document.body.scrollLeft||0,c.scrollViewSize+=document.documentElement.clientWidth):(c.scrollStart=s.scrollLeft,c.scrollViewSize+=s.clientWidth),c.scrollMaxSize=s.scrollWidth,!0===l&&(c.scrollStart=(!0===ee.e?c.scrollMaxSize-c.scrollViewSize:0)-c.scrollStart)):(e===window?(c.scrollStart=window.pageYOffset||window.scrollY||document.body.scrollTop||0,c.scrollViewSize+=document.documentElement.clientHeight):(c.scrollStart=s.scrollTop,c.scrollViewSize+=s.clientHeight),c.scrollMaxSize=s.scrollHeight),null!==n)for(let d=n.previousElementSibling;null!==d;d=d.previousElementSibling)!1===d.classList.contains("q-virtual-scroll--skip")&&(c.offsetStart+=d[u]);if(null!==o)for(let d=o.nextElementSibling;null!==d;d=d.nextElementSibling)!1===d.classList.contains("q-virtual-scroll--skip")&&(c.offsetEnd+=d[u]);if(t!==e){const n=s.getBoundingClientRect(),o=t.getBoundingClientRect();!0===r?(c.offsetStart+=o.left-n.left,c.offsetEnd-=o.width):(c.offsetStart+=o.top-n.top,c.offsetEnd-=o.height),e!==window&&(c.offsetStart+=c.scrollStart),c.offsetEnd+=c.scrollMaxSize-c.offsetStart}return c}function ae(e,t,n,o){"end"===t&&(t=(e===window?document.body:e)[!0===n?"scrollWidth":"scrollHeight"]),e===window?!0===n?(!0===o&&(t=(!0===ee.e?document.body.scrollWidth-document.documentElement.clientWidth:0)-t),window.scrollTo(t,window.pageYOffset||window.scrollY||document.body.scrollTop||0)):window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,t):!0===n?(!0===o&&(t=(!0===ee.e?e.scrollWidth-e.offsetWidth:0)-t),e.scrollLeft=t):e.scrollTop=t}function se(e,t,n,o){if(n>=o)return 0;const r=t.length,l=Math.floor(n/te),i=Math.floor((o-1)/te)+1;let a=e.slice(l,i).reduce(le,0);return n%te!==0&&(a-=t.slice(l*te,n).reduce(le,0)),o%te!==0&&o!==r&&(a-=t.slice(o,i*te).reduce(le,0)),a}const ue={virtualScrollSliceSize:{type:[Number,String],default:null},virtualScrollSliceRatioBefore:{type:[Number,String],default:1},virtualScrollSliceRatioAfter:{type:[Number,String],default:1},virtualScrollItemSize:{type:[Number,String],default:24},virtualScrollStickySizeStart:{type:[Number,String],default:0},virtualScrollStickySizeEnd:{type:[Number,String],default:0},tableColspan:[Number,String]},ce=(Object.keys(ue),{virtualScrollHorizontal:Boolean,onVirtualScroll:Function,...ue});function de({virtualScrollLength:e,getVirtualScrollTarget:t,getVirtualScrollEl:n,virtualScrollItemSizeComputed:l}){const i=(0,o.FN)(),{props:a,emit:s,proxy:u}=i,{$q:c}=u;let d,f,p,v,h=[];const m=(0,r.iH)(0),g=(0,r.iH)(0),b=(0,r.iH)({}),y=(0,r.iH)(null),_=(0,r.iH)(null),w=(0,r.iH)(null),S=(0,r.iH)({from:0,to:0}),k=(0,o.Fl)((()=>void 0!==a.tableColspan?a.tableColspan:100));void 0===l&&(l=(0,o.Fl)((()=>a.virtualScrollItemSize)));const x=(0,o.Fl)((()=>l.value+";"+a.virtualScrollHorizontal)),E=(0,o.Fl)((()=>x.value+";"+a.virtualScrollSliceRatioBefore+";"+a.virtualScrollSliceRatioAfter));function C(){q(f,!0)}function T(e){q(void 0===e?f:e)}function O(o,r){const l=t();if(void 0===l||null===l||8===l.nodeType)return;const i=ie(l,n(),y.value,_.value,a.virtualScrollHorizontal,c.lang.rtl,a.virtualScrollStickySizeStart,a.virtualScrollStickySizeEnd);p!==i.scrollViewSize&&R(i.scrollViewSize),L(l,i,Math.min(e.value-1,Math.max(0,parseInt(o,10)||0)),0,ne.indexOf(r)>-1?r:f>-1&&o>f?"end":"start")}function F(){const o=t();if(void 0===o||null===o||8===o.nodeType)return;const r=ie(o,n(),y.value,_.value,a.virtualScrollHorizontal,c.lang.rtl,a.virtualScrollStickySizeStart,a.virtualScrollStickySizeEnd),l=e.value-1,i=r.scrollMaxSize-r.offsetStart-r.offsetEnd-g.value;if(d===r.scrollStart)return;if(r.scrollMaxSize<=0)return void L(o,r,0,0);p!==r.scrollViewSize&&R(r.scrollViewSize),A(S.value.from);const s=Math.floor(r.scrollMaxSize-Math.max(r.scrollViewSize,r.offsetEnd)-Math.min(v[l],r.scrollViewSize/2));if(s>0&&Math.ceil(r.scrollStart)>=s)return void L(o,r,l,r.scrollMaxSize-r.offsetEnd-h.reduce(le,0));let u=0,f=r.scrollStart-r.offsetStart,b=f;if(f<=i&&f+r.scrollViewSize>=m.value)f-=m.value,u=S.value.from,b=f;else for(let e=0;f>=h[e]&&u<l;e++)f-=h[e],u+=te;while(f>0&&u<l)f-=v[u],f>-r.scrollViewSize?(u++,b=f):b=v[u]+f;L(o,r,u,b)}function L(t,n,o,r,l){const i="string"===typeof l&&l.indexOf("-force")>-1,s=!0===i?l.replace("-force",""):l,u=void 0!==s?s:"start";let f=Math.max(0,o-b.value[u]),p=f+b.value.total;p>e.value&&(p=e.value,f=Math.max(0,p-b.value.total)),d=n.scrollStart;const y=f!==S.value.from||p!==S.value.to;if(!1===y&&void 0===s)return void M(o);const{activeElement:_}=document,k=w.value;!0===y&&null!==k&&k!==_&&!0===k.contains(_)&&(k.addEventListener("focusout",P),setTimeout((()=>{null!==k&&k.removeEventListener("focusout",P)}))),re(k,o-f);const x=void 0!==s?v.slice(f,o).reduce(le,0):0;if(!0===y){const t=p>=S.value.from&&f<=S.value.to?S.value.to:p;S.value={from:f,to:t},m.value=se(h,v,0,f),g.value=se(h,v,p,e.value),requestAnimationFrame((()=>{S.value.to!==p&&d===n.scrollStart&&(S.value={from:S.value.from,to:p},g.value=se(h,v,p,e.value))}))}requestAnimationFrame((()=>{if(d!==n.scrollStart)return;!0===y&&A(f);const e=v.slice(f,o).reduce(le,0),l=e+n.offsetStart+m.value,u=l+v[o];let p=l+r;if(void 0!==s){const t=e-x,r=n.scrollStart+t;p=!0!==i&&r<l&&u<r+n.scrollViewSize?r:"end"===s?u-n.scrollViewSize:l-("start"===s?0:Math.round((n.scrollViewSize-v[o])/2))}d=p,ae(t,p,a.virtualScrollHorizontal,c.lang.rtl),M(o)}))}function A(e){const t=w.value;if(t){const n=oe.call(t.children,(e=>e.classList&&!1===e.classList.contains("q-virtual-scroll--skip"))),o=n.length,r=!0===a.virtualScrollHorizontal?e=>e.getBoundingClientRect().width:e=>e.offsetHeight;let l,i,s=e;for(let e=0;e<o;){l=r(n[e]),e++;while(e<o&&!0===n[e].classList.contains("q-virtual-scroll--with-prev"))l+=r(n[e]),e++;i=l-v[s],0!==i&&(v[s]+=i,h[Math.floor(s/te)]+=i),s++}}}function P(){null!==w.value&&void 0!==w.value&&w.value.focus()}function q(t,n){const r=1*l.value;!0!==n&&!1!==Array.isArray(v)||(v=[]);const i=v.length;v.length=e.value;for(let o=e.value-1;o>=i;o--)v[o]=r;const a=Math.floor((e.value-1)/te);h=[];for(let o=0;o<=a;o++){let t=0;const n=Math.min((o+1)*te,e.value);for(let e=o*te;e<n;e++)t+=v[e];h.push(t)}f=-1,d=void 0,m.value=se(h,v,0,S.value.from),g.value=se(h,v,S.value.to,e.value),t>=0?(A(S.value.from),(0,o.Y3)((()=>{O(t)}))):I()}function R(e){if(void 0===e&&"undefined"!==typeof window){const o=t();void 0!==o&&null!==o&&8!==o.nodeType&&(e=ie(o,n(),y.value,_.value,a.virtualScrollHorizontal,c.lang.rtl,a.virtualScrollStickySizeStart,a.virtualScrollStickySizeEnd).scrollViewSize)}p=e;const o=parseFloat(a.virtualScrollSliceRatioBefore)||0,r=parseFloat(a.virtualScrollSliceRatioAfter)||0,i=1+o+r,s=void 0===e||e<=0?1:Math.ceil(e/l.value),u=Math.max(1,s,Math.ceil((a.virtualScrollSliceSize>0?a.virtualScrollSliceSize:10)/i));b.value={total:Math.ceil(u*i),start:Math.ceil(u*o),center:Math.ceil(u*(.5+o)),end:Math.ceil(u*(1+o)),view:s}}function N(e,t){const n=!0===a.virtualScrollHorizontal?"width":"height",r={["--q-virtual-scroll-item-"+n]:l.value+"px"};return["tbody"===e?(0,o.h)(e,{class:"q-virtual-scroll__padding",key:"before",ref:y},[(0,o.h)("tr",[(0,o.h)("td",{style:{[n]:`${m.value}px`,...r},colspan:k.value})])]):(0,o.h)(e,{class:"q-virtual-scroll__padding",key:"before",ref:y,style:{[n]:`${m.value}px`,...r}}),(0,o.h)(e,{class:"q-virtual-scroll__content",key:"content",ref:w,tabindex:-1},t.flat()),"tbody"===e?(0,o.h)(e,{class:"q-virtual-scroll__padding",key:"after",ref:_},[(0,o.h)("tr",[(0,o.h)("td",{style:{[n]:`${g.value}px`,...r},colspan:k.value})])]):(0,o.h)(e,{class:"q-virtual-scroll__padding",key:"after",ref:_,style:{[n]:`${g.value}px`,...r}})]}function M(e){f!==e&&(void 0!==a.onVirtualScroll&&s("virtualScroll",{index:e,from:S.value.from,to:S.value.to-1,direction:e<f?"decrease":"increase",ref:u}),f=e)}(0,o.YP)(E,(()=>{R()})),(0,o.YP)(x,C),R();const I=(0,Q.Z)(F,!0===c.platform.is.ios?120:35);(0,o.wF)((()=>{R()}));let $=!1;return(0,o.se)((()=>{$=!0})),(0,o.dl)((()=>{if(!0!==$)return;const e=t();void 0!==d&&void 0!==e&&null!==e&&8!==e.nodeType?ae(e,d,a.virtualScrollHorizontal,c.lang.rtl):O(f)})),(0,o.Jd)((()=>{I.cancel()})),Object.assign(u,{scrollTo:O,reset:C,refresh:T}),{virtualScrollSliceRange:S,virtualScrollSliceSizeComputed:b,setVirtualScrollSize:R,onVirtualScrollEvt:I,localResetVirtualScroll:q,padVirtualScroll:N,scrollTo:O,reset:C,refresh:T}}var fe=n(9256),pe=n(2802),ve=n(4680),he=n(321);const me=e=>["add","add-unique","toggle"].includes(e),ge=".*+?^${}()|[]\\",be=Object.keys(l.Cl),ye=(0,i.L)({name:"QSelect",inheritAttrs:!1,props:{...ce,...fe.Fz,...l.Cl,modelValue:{required:!0},multiple:Boolean,displayValue:[String,Number],displayValueHtml:Boolean,dropdownIcon:String,options:{type:Array,default:()=>[]},optionValue:[Function,String],optionLabel:[Function,String],optionDisable:[Function,String],hideSelected:Boolean,hideDropdownIcon:Boolean,fillInput:Boolean,maxValues:[Number,String],optionsDense:Boolean,optionsDark:{type:Boolean,default:null},optionsSelectedClass:String,optionsHtml:Boolean,optionsCover:Boolean,menuShrink:Boolean,menuAnchor:String,menuSelf:String,menuOffset:Array,popupContentClass:String,popupContentStyle:[String,Array,Object],useInput:Boolean,useChips:Boolean,newValueMode:{type:String,validator:me},mapOptions:Boolean,emitValue:Boolean,inputDebounce:{type:[Number,String],default:500},inputClass:[Array,String,Object],inputStyle:[Array,String,Object],tabindex:{type:[String,Number],default:0},autocomplete:String,transitionShow:String,transitionHide:String,transitionDuration:[String,Number],behavior:{type:String,validator:e=>["default","menu","dialog"].includes(e),default:"default"},virtualScrollItemSize:{type:[Number,String],default:void 0},onNewValue:Function,onFilter:Function},emits:[...l.HJ,"add","remove","inputValue","newValue","keyup","keypress","keydown","filterAbort"],setup(e,{slots:t,emit:n}){const{proxy:i}=(0,o.FN)(),{$q:p}=i,v=(0,r.iH)(!1),h=(0,r.iH)(!1),m=(0,r.iH)(-1),g=(0,r.iH)(""),b=(0,r.iH)(!1),y=(0,r.iH)(!1);let _,w,S,k,C,O,F,L=null,A=null;const P=(0,r.iH)(null),q=(0,r.iH)(null),R=(0,r.iH)(null),N=(0,r.iH)(null),M=(0,r.iH)(null),I=(0,fe.Do)(e),$=(0,pe.Z)(Ye),D=(0,o.Fl)((()=>Array.isArray(e.options)?e.options.length:0)),V=(0,o.Fl)((()=>void 0===e.virtualScrollItemSize?!0===e.optionsDense?24:48:e.virtualScrollItemSize)),{virtualScrollSliceRange:j,virtualScrollSliceSizeComputed:B,localResetVirtualScroll:H,padVirtualScroll:U,onVirtualScrollEvt:W,scrollTo:Z,setVirtualScrollSize:K}=de({virtualScrollLength:D,getVirtualScrollTarget:Ue,getVirtualScrollEl:ze,virtualScrollItemSizeComputed:V}),Y=(0,l.tL)(),J=(0,o.Fl)((()=>{const t=!0===e.mapOptions&&!0!==e.multiple,n=void 0===e.modelValue||null===e.modelValue&&!0!==t?[]:!0===e.multiple&&Array.isArray(e.modelValue)?e.modelValue:[e.modelValue];if(!0===e.mapOptions&&!0===Array.isArray(e.options)){const o=!0===e.mapOptions&&void 0!==_?_:[],r=n.map((e=>Me(e,o)));return null===e.modelValue&&!0===t?r.filter((e=>null!==e)):r}return n})),X=(0,o.Fl)((()=>{const t={};return be.forEach((n=>{const o=e[n];void 0!==o&&(t[n]=o)})),t})),Q=(0,o.Fl)((()=>null===e.optionsDark?Y.isDark.value:e.optionsDark)),ee=(0,o.Fl)((()=>(0,l.yV)(J.value))),te=(0,o.Fl)((()=>{let t="q-field__input q-placeholder col";return!0===e.hideSelected||0===J.value.length?[t,e.inputClass]:(t+=" q-field__input--padding",void 0===e.inputClass?t:[t,e.inputClass])})),ne=(0,o.Fl)((()=>(!0===e.virtualScrollHorizontal?"q-virtual-scroll--horizontal":"")+(e.popupContentClass?" "+e.popupContentClass:""))),oe=(0,o.Fl)((()=>0===D.value)),re=(0,o.Fl)((()=>J.value.map((e=>Ee.value(e))).join(", "))),le=(0,o.Fl)((()=>void 0!==e.displayValue?e.displayValue:re.value)),ie=(0,o.Fl)((()=>!0===e.optionsHtml?()=>!0:e=>void 0!==e&&null!==e&&!0===e.html)),ae=(0,o.Fl)((()=>!0===e.displayValueHtml||void 0===e.displayValue&&(!0===e.optionsHtml||J.value.some(ie.value)))),se=(0,o.Fl)((()=>!0===Y.focused.value?e.tabindex:-1)),ue=(0,o.Fl)((()=>{const t={tabindex:e.tabindex,role:"combobox","aria-label":e.label,"aria-readonly":!0===e.readonly?"true":"false","aria-autocomplete":!0===e.useInput?"list":"none","aria-expanded":!0===v.value?"true":"false","aria-controls":`${Y.targetUid.value}_lb`};return m.value>=0&&(t["aria-activedescendant"]=`${Y.targetUid.value}_${m.value}`),t})),ce=(0,o.Fl)((()=>({id:`${Y.targetUid.value}_lb`,role:"listbox","aria-multiselectable":!0===e.multiple?"true":"false"}))),ye=(0,o.Fl)((()=>J.value.map(((e,t)=>({index:t,opt:e,html:ie.value(e),selected:!0,removeAtIndex:Ae,toggleOption:qe,tabindex:se.value}))))),_e=(0,o.Fl)((()=>{if(0===D.value)return[];const{from:t,to:n}=j.value;return e.options.slice(t,n).map(((n,o)=>{const r=!0===Ce.value(n),l=t+o,i={clickable:!0,active:!1,activeClass:ke.value,manualFocus:!0,focused:!1,disable:r,tabindex:-1,dense:e.optionsDense,dark:Q.value,role:"option",id:`${Y.targetUid.value}_${l}`,onClick:()=>{qe(n)}};return!0!==r&&(!0===$e(n)&&(i.active=!0),m.value===l&&(i.focused=!0),i["aria-selected"]=!0===i.active?"true":"false",!0===p.platform.is.desktop&&(i.onMousemove=()=>{!0===v.value&&Re(l)})),{index:l,opt:n,html:ie.value(n),label:Ee.value(n),selected:i.active,focused:i.focused,toggleOption:qe,setOptionIndex:Re,itemProps:i}}))})),we=(0,o.Fl)((()=>void 0!==e.dropdownIcon?e.dropdownIcon:p.iconSet.arrow.dropdown)),Se=(0,o.Fl)((()=>!1===e.optionsCover&&!0!==e.outlined&&!0!==e.standout&&!0!==e.borderless&&!0!==e.rounded)),ke=(0,o.Fl)((()=>void 0!==e.optionsSelectedClass?e.optionsSelectedClass:void 0!==e.color?`text-${e.color}`:"")),xe=(0,o.Fl)((()=>Ie(e.optionValue,"value"))),Ee=(0,o.Fl)((()=>Ie(e.optionLabel,"label"))),Ce=(0,o.Fl)((()=>Ie(e.optionDisable,"disable"))),Te=(0,o.Fl)((()=>J.value.map((e=>xe.value(e))))),Oe=(0,o.Fl)((()=>{const e={onInput:Ye,onChange:$,onKeydown:He,onKeyup:je,onKeypress:Be,onFocus:De,onClick(e){!0===w&&(0,x.sT)(e)}};return e.onCompositionstart=e.onCompositionupdate=e.onCompositionend=$,e}));function Fe(t){return!0===e.emitValue?xe.value(t):t}function Le(t){if(t>-1&&t<J.value.length)if(!0===e.multiple){const o=e.modelValue.slice();n("remove",{index:t,value:o.splice(t,1)[0]}),n("update:modelValue",o)}else n("update:modelValue",null)}function Ae(e){Le(e),Y.focus()}function Pe(t,o){const r=Fe(t);if(!0!==e.multiple)return!0===e.fillInput&&Xe(Ee.value(t),!0,!0),void n("update:modelValue",r);if(0===J.value.length)return n("add",{index:0,value:r}),void n("update:modelValue",!0===e.multiple?[r]:r);if(!0===o&&!0===$e(t))return;if(void 0!==e.maxValues&&e.modelValue.length>=e.maxValues)return;const l=e.modelValue.slice();n("add",{index:l.length,value:r}),l.push(r),n("update:modelValue",l)}function qe(t,o){if(!0!==Y.editable.value||void 0===t||!0===Ce.value(t))return;const r=xe.value(t);if(!0!==e.multiple)return!0!==o&&(Xe(!0===e.fillInput?Ee.value(t):"",!0,!0),ct()),null!==q.value&&q.value.focus(),void(0!==J.value.length&&!0===(0,ve.xb)(xe.value(J.value[0]),r)||n("update:modelValue",!0===e.emitValue?r:t));if((!0!==w||!0===b.value)&&Y.focus(),De(),0===J.value.length){const o=!0===e.emitValue?r:t;return n("add",{index:0,value:o}),void n("update:modelValue",!0===e.multiple?[o]:o)}const l=e.modelValue.slice(),i=Te.value.findIndex((e=>(0,ve.xb)(e,r)));if(i>-1)n("remove",{index:i,value:l.splice(i,1)[0]});else{if(void 0!==e.maxValues&&l.length>=e.maxValues)return;const o=!0===e.emitValue?r:t;n("add",{index:l.length,value:o}),l.push(o)}n("update:modelValue",l)}function Re(e){if(!0!==p.platform.is.desktop)return;const t=e>-1&&e<D.value?e:-1;m.value!==t&&(m.value=t)}function Ne(t=1,n){if(!0===v.value){let o=m.value;do{o=(0,he.Uz)(o+t,-1,D.value-1)}while(-1!==o&&o!==m.value&&!0===Ce.value(e.options[o]));m.value!==o&&(Re(o),Z(o),!0!==n&&!0===e.useInput&&!0===e.fillInput&&Je(o>=0?Ee.value(e.options[o]):k))}}function Me(t,n){const o=e=>(0,ve.xb)(xe.value(e),t);return e.options.find(o)||n.find(o)||t}function Ie(e,t){const n=void 0!==e?e:t;return"function"===typeof n?n:e=>null!==e&&"object"===typeof e&&n in e?e[n]:e}function $e(e){const t=xe.value(e);return void 0!==Te.value.find((e=>(0,ve.xb)(e,t)))}function De(t){!0===e.useInput&&null!==q.value&&(void 0===t||q.value===t.target&&t.target.value===re.value)&&q.value.select()}function Ve(e){!0===(0,T.So)(e,27)&&!0===v.value&&((0,x.sT)(e),ct(),dt()),n("keyup",e)}function je(t){const{value:n}=t.target;if(void 0===t.keyCode)if(t.target.value="",null!==L&&(clearTimeout(L),L=null),dt(),"string"===typeof n&&0!==n.length){const t=n.toLocaleLowerCase(),o=n=>{const o=e.options.find((e=>n.value(e).toLocaleLowerCase()===t));return void 0!==o&&(-1===J.value.indexOf(o)?qe(o):ct(),!0)},r=e=>{!0!==o(xe)&&!0!==o(Ee)&&!0!==e&&Ge(n,!0,(()=>r(!0)))};r()}else Y.clearValue(t);else Ve(t)}function Be(e){n("keypress",e)}function He(t){if(n("keydown",t),!0===(0,T.Wm)(t))return;const r=0!==g.value.length&&(void 0!==e.newValueMode||void 0!==e.onNewValue),l=!0!==t.shiftKey&&!0!==e.multiple&&(m.value>-1||!0===r);if(27===t.keyCode)return void(0,x.X$)(t);if(9===t.keyCode&&!1===l)return void st();if(void 0===t.target||t.target.id!==Y.targetUid.value||!0!==Y.editable.value)return;if(40===t.keyCode&&!0!==Y.innerLoading.value&&!1===v.value)return(0,x.NS)(t),void ut();if(8===t.keyCode&&!0!==e.hideSelected&&0===g.value.length)return void(!0===e.multiple&&!0===Array.isArray(e.modelValue)?Le(e.modelValue.length-1):!0!==e.multiple&&null!==e.modelValue&&n("update:modelValue",null));35!==t.keyCode&&36!==t.keyCode||"string"===typeof g.value&&0!==g.value.length||((0,x.NS)(t),m.value=-1,Ne(36===t.keyCode?1:-1,e.multiple)),33!==t.keyCode&&34!==t.keyCode||void 0===B.value||((0,x.NS)(t),m.value=Math.max(-1,Math.min(D.value,m.value+(33===t.keyCode?-1:1)*B.value.view)),Ne(33===t.keyCode?1:-1,e.multiple)),38!==t.keyCode&&40!==t.keyCode||((0,x.NS)(t),Ne(38===t.keyCode?-1:1,e.multiple));const i=D.value;if((void 0===O||F<Date.now())&&(O=""),i>0&&!0!==e.useInput&&void 0!==t.key&&1===t.key.length&&!1===t.altKey&&!1===t.ctrlKey&&!1===t.metaKey&&(32!==t.keyCode||0!==O.length)){!0!==v.value&&ut(t);const n=t.key.toLocaleLowerCase(),r=1===O.length&&O[0]===n;F=Date.now()+1500,!1===r&&((0,x.NS)(t),O+=n);const l=new RegExp("^"+O.split("").map((e=>ge.indexOf(e)>-1?"\\"+e:e)).join(".*"),"i");let a=m.value;if(!0===r||a<0||!0!==l.test(Ee.value(e.options[a])))do{a=(0,he.Uz)(a+1,-1,i-1)}while(a!==m.value&&(!0===Ce.value(e.options[a])||!0!==l.test(Ee.value(e.options[a]))));m.value!==a&&(0,o.Y3)((()=>{Re(a),Z(a),a>=0&&!0===e.useInput&&!0===e.fillInput&&Je(Ee.value(e.options[a]))}))}else if(13===t.keyCode||32===t.keyCode&&!0!==e.useInput&&""===O||9===t.keyCode&&!1!==l)if(9!==t.keyCode&&(0,x.NS)(t),m.value>-1&&m.value<i)qe(e.options[m.value]);else{if(!0===r){const t=(t,n)=>{if(n){if(!0!==me(n))return}else n=e.newValueMode;if(Xe("",!0!==e.multiple,!0),void 0===t||null===t)return;const o="toggle"===n?qe:Pe;o(t,"add-unique"===n),!0!==e.multiple&&(null!==q.value&&q.value.focus(),ct())};if(void 0!==e.onNewValue?n("newValue",g.value,t):t(g.value),!0!==e.multiple)return}!0===v.value?st():!0!==Y.innerLoading.value&&ut()}}function ze(){return!0===w?M.value:null!==R.value&&null!==R.value.contentEl?R.value.contentEl:void 0}function Ue(){return ze()}function We(){return!0===e.hideSelected?[]:void 0!==t["selected-item"]?ye.value.map((e=>t["selected-item"](e))).slice():void 0!==t.selected?[].concat(t.selected()):!0===e.useChips?ye.value.map(((t,n)=>(0,o.h)(u.Z,{key:"option-"+n,removable:!0===Y.editable.value&&!0!==Ce.value(t.opt),dense:!0,textColor:e.color,tabindex:se.value,onRemove(){t.removeAtIndex(n)}},(()=>(0,o.h)("span",{class:"ellipsis",[!0===t.html?"innerHTML":"textContent"]:Ee.value(t.opt)}))))):[(0,o.h)("span",{[!0===ae.value?"innerHTML":"textContent"]:le.value})]}function Ze(){if(!0===oe.value)return void 0!==t["no-option"]?t["no-option"]({inputValue:g.value}):void 0;const e=void 0!==t.option?t.option:e=>(0,o.h)(c.Z,{key:e.index,...e.itemProps},(()=>(0,o.h)(d.Z,(()=>(0,o.h)(f.Z,(()=>(0,o.h)("span",{[!0===e.html?"innerHTML":"textContent"]:e.label})))))));let n=U("div",_e.value.map(e));return void 0!==t["before-options"]&&(n=t["before-options"]().concat(n)),(0,E.vs)(t["after-options"],n)}function Ke(t,n){const r=!0===n?{...ue.value,...Y.splitAttrs.attributes.value}:void 0,l={ref:!0===n?q:void 0,key:"i_t",class:te.value,style:e.inputStyle,value:void 0!==g.value?g.value:"",type:"search",...r,id:!0===n?Y.targetUid.value:void 0,maxlength:e.maxlength,autocomplete:e.autocomplete,"data-autofocus":!0===t||!0===e.autofocus||void 0,disabled:!0===e.disable,readonly:!0===e.readonly,...Oe.value};return!0!==t&&!0===w&&(!0===Array.isArray(l.class)?l.class=[...l.class,"no-pointer-events"]:l.class+=" no-pointer-events"),(0,o.h)("input",l)}function Ye(t){null!==L&&(clearTimeout(L),L=null),t&&t.target&&!0===t.target.qComposing||(Je(t.target.value||""),S=!0,k=g.value,!0===Y.focused.value||!0===w&&!0!==b.value||Y.focus(),void 0!==e.onFilter&&(L=setTimeout((()=>{L=null,Ge(g.value)}),e.inputDebounce)))}function Je(e){g.value!==e&&(g.value=e,n("inputValue",e))}function Xe(t,n,o){S=!0!==o,!0===e.useInput&&(Je(t),!0!==n&&!0===o||(k=t),!0!==n&&Ge(t))}function Ge(t,r,l){if(void 0===e.onFilter||!0!==r&&!0!==Y.focused.value)return;!0===Y.innerLoading.value?n("filterAbort"):(Y.innerLoading.value=!0,y.value=!0),""!==t&&!0!==e.multiple&&0!==J.value.length&&!0!==S&&t===Ee.value(J.value[0])&&(t="");const a=setTimeout((()=>{!0===v.value&&(v.value=!1)}),10);null!==A&&clearTimeout(A),A=a,n("filter",t,((e,t)=>{!0!==r&&!0!==Y.focused.value||A!==a||(clearTimeout(A),"function"===typeof e&&e(),y.value=!1,(0,o.Y3)((()=>{Y.innerLoading.value=!1,!0===Y.editable.value&&(!0===r?!0===v.value&&ct():!0===v.value?ft(!0):v.value=!0),"function"===typeof t&&(0,o.Y3)((()=>{t(i)})),"function"===typeof l&&(0,o.Y3)((()=>{l(i)}))})))}),(()=>{!0===Y.focused.value&&A===a&&(clearTimeout(A),Y.innerLoading.value=!1,y.value=!1),!0===v.value&&(v.value=!1)}))}function Qe(){return(0,o.h)(z,{ref:R,class:ne.value,style:e.popupContentStyle,modelValue:v.value,fit:!0!==e.menuShrink,cover:!0===e.optionsCover&&!0!==oe.value&&!0!==e.useInput,anchor:e.menuAnchor,self:e.menuSelf,offset:e.menuOffset,dark:Q.value,noParentEvent:!0,noRefocus:!0,noFocus:!0,square:Se.value,transitionShow:e.transitionShow,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,separateClosePopup:!0,...ce.value,onScrollPassive:W,onBeforeShow:ht,onBeforeHide:et,onShow:tt},Ze)}function et(e){mt(e),st()}function tt(){K()}function nt(e){(0,x.sT)(e),null!==q.value&&q.value.focus(),b.value=!0,window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,0)}function ot(e){(0,x.sT)(e),(0,o.Y3)((()=>{b.value=!1}))}function rt(){const n=[(0,o.h)(a,{class:`col-auto ${Y.fieldClass.value}`,...X.value,for:Y.targetUid.value,dark:Q.value,square:!0,loading:y.value,itemAligned:!1,filled:!0,stackLabel:0!==g.value.length,...Y.splitAttrs.listeners.value,onFocus:nt,onBlur:ot},{...t,rawControl:()=>Y.getControl(!0),before:void 0,after:void 0})];return!0===v.value&&n.push((0,o.h)("div",{ref:M,class:ne.value+" scroll",style:e.popupContentStyle,...ce.value,onClick:x.X$,onScrollPassive:W},Ze())),(0,o.h)(G,{ref:N,modelValue:h.value,position:!0===e.useInput?"top":void 0,transitionShow:C,transitionHide:e.transitionHide,transitionDuration:e.transitionDuration,onBeforeShow:ht,onBeforeHide:lt,onHide:it,onShow:at},(()=>(0,o.h)("div",{class:"q-select__dialog"+(!0===Q.value?" q-select__dialog--dark q-dark":"")+(!0===b.value?" q-select__dialog--focused":"")},n)))}function lt(e){mt(e),null!==N.value&&N.value.__updateRefocusTarget(Y.rootRef.value.querySelector(".q-field__native > [tabindex]:last-child")),Y.focused.value=!1}function it(e){ct(),!1===Y.focused.value&&n("blur",e),dt()}function at(){const e=document.activeElement;null!==e&&e.id===Y.targetUid.value||null===q.value||q.value===e||q.value.focus(),K()}function st(){!0!==h.value&&(m.value=-1,!0===v.value&&(v.value=!1),!1===Y.focused.value&&(null!==A&&(clearTimeout(A),A=null),!0===Y.innerLoading.value&&(n("filterAbort"),Y.innerLoading.value=!1,y.value=!1)))}function ut(n){!0===Y.editable.value&&(!0===w?(Y.onControlFocusin(n),h.value=!0,(0,o.Y3)((()=>{Y.focus()}))):Y.focus(),void 0!==e.onFilter?Ge(g.value):!0===oe.value&&void 0===t["no-option"]||(v.value=!0))}function ct(){h.value=!1,st()}function dt(){!0===e.useInput&&Xe(!0!==e.multiple&&!0===e.fillInput&&0!==J.value.length&&Ee.value(J.value[0])||"",!0,!0)}function ft(t){let n=-1;if(!0===t){if(0!==J.value.length){const t=xe.value(J.value[0]);n=e.options.findIndex((e=>(0,ve.xb)(xe.value(e),t)))}H(n)}Re(n)}function pt(e,t){!0===v.value&&!1===Y.innerLoading.value&&(H(-1,!0),(0,o.Y3)((()=>{!0===v.value&&!1===Y.innerLoading.value&&(e>t?H():ft(!0))})))}function vt(){!1===h.value&&null!==R.value&&R.value.updatePosition()}function ht(e){void 0!==e&&(0,x.sT)(e),n("popupShow",e),Y.hasPopupOpen=!0,Y.onControlFocusin(e)}function mt(e){void 0!==e&&(0,x.sT)(e),n("popupHide",e),Y.hasPopupOpen=!1,Y.onControlFocusout(e)}function gt(){w=(!0===p.platform.is.mobile||"dialog"===e.behavior)&&("menu"!==e.behavior&&(!0!==e.useInput||(void 0!==t["no-option"]||void 0!==e.onFilter||!1===oe.value))),C=!0===p.platform.is.ios&&!0===w&&!0===e.useInput?"fade":e.transitionShow}return(0,o.YP)(J,(t=>{_=t,!0===e.useInput&&!0===e.fillInput&&!0!==e.multiple&&!0!==Y.innerLoading.value&&(!0!==h.value&&!0!==v.value||!0!==ee.value)&&(!0!==S&&dt(),!0!==h.value&&!0!==v.value||Ge(""))}),{immediate:!0}),(0,o.YP)((()=>e.fillInput),dt),(0,o.YP)(v,ft),(0,o.YP)(D,pt),(0,o.Xn)(gt),(0,o.ic)(vt),gt(),(0,o.Jd)((()=>{null!==L&&clearTimeout(L)})),Object.assign(i,{showPopup:ut,hidePopup:ct,removeAtIndex:Le,add:Pe,toggleOption:qe,getOptionIndex:()=>m.value,setOptionIndex:Re,moveOptionSelection:Ne,filter:Ge,updateMenuPosition:vt,updateInputValue:Xe,isOptionSelected:$e,getEmittingOptionValue:Fe,isOptionDisabled:(...e)=>!0===Ce.value.apply(null,e),getOptionValue:(...e)=>xe.value.apply(null,e),getOptionLabel:(...e)=>Ee.value.apply(null,e)}),Object.assign(Y,{innerValue:J,fieldClass:(0,o.Fl)((()=>`q-select q-field--auto-height q-select--with${!0!==e.useInput?"out":""}-input q-select--with${!0!==e.useChips?"out":""}-chips q-select--`+(!0===e.multiple?"multiple":"single"))),inputRef:P,targetRef:q,hasValue:ee,showPopup:ut,floatingLabel:(0,o.Fl)((()=>!0!==e.hideSelected&&!0===ee.value||"number"===typeof g.value||0!==g.value.length||(0,l.yV)(e.displayValue))),getControlChild:()=>{if(!1!==Y.editable.value&&(!0===h.value||!0!==oe.value||void 0!==t["no-option"]))return!0===w?rt():Qe();!0===Y.hasPopupOpen&&(Y.hasPopupOpen=!1)},controlEvents:{onFocusin(e){Y.onControlFocusin(e)},onFocusout(e){Y.onControlFocusout(e,(()=>{dt(),st()}))},onClick(e){if((0,x.X$)(e),!0!==w&&!0===v.value)return st(),void(null!==q.value&&q.value.focus());ut(e)}},getControl:t=>{const n=We(),r=!0===t||!0!==h.value||!0!==w;if(!0===e.useInput)n.push(Ke(t,r));else if(!0===Y.editable.value){const l=!0===r?ue.value:void 0;n.push((0,o.h)("input",{ref:!0===r?q:void 0,key:"d_t",class:"q-select__focus-target",id:!0===r?Y.targetUid.value:void 0,value:le.value,readonly:!0,"data-autofocus":!0===t||!0===e.autofocus||void 0,...l,onKeydown:He,onKeyup:Ve,onKeypress:Be})),!0===r&&"string"===typeof e.autocomplete&&0!==e.autocomplete.length&&n.push((0,o.h)("input",{class:"q-select__autocomplete-input",autocomplete:e.autocomplete,tabindex:-1,onKeyup:je}))}if(void 0!==I.value&&!0!==e.disable&&0!==Te.value.length){const t=Te.value.map((e=>(0,o.h)("option",{value:e,selected:!0})));n.push((0,o.h)("select",{class:"hidden",name:I.value,multiple:e.multiple},t))}const l=!0===e.useInput||!0!==r?void 0:Y.splitAttrs.attributes.value;return(0,o.h)("div",{class:"q-field__native row items-center",...l,...Y.splitAttrs.listeners.value},n)},getInnerAppend:()=>!0!==e.loading&&!0!==y.value&&!0!==e.hideDropdownIcon?[(0,o.h)(s.Z,{class:"q-select__dropdown-icon"+(!0===v.value?" rotate-180":""),name:we.value})]:null}),(0,l.ZP)(Y)}})},926:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var o=n(9835),r=n(8234),l=n(5987);const i={true:"inset",item:"item-inset","item-thumbnail":"item-thumbnail-inset"},a={xs:2,sm:4,md:8,lg:16,xl:24},s=(0,l.L)({name:"QSeparator",props:{...r.S,spaced:[Boolean,String],inset:[Boolean,String],vertical:Boolean,color:String,size:String},setup(e){const t=(0,o.FN)(),n=(0,r.Z)(e,t.proxy.$q),l=(0,o.Fl)((()=>!0===e.vertical?"vertical":"horizontal")),s=(0,o.Fl)((()=>` q-separator--${l.value}`)),u=(0,o.Fl)((()=>!1!==e.inset?`${s.value}-${i[e.inset]}`:"")),c=(0,o.Fl)((()=>`q-separator${s.value}${u.value}`+(void 0!==e.color?` bg-${e.color}`:"")+(!0===n.value?" q-separator--dark":""))),d=(0,o.Fl)((()=>{const t={};if(void 0!==e.size&&(t[!0===e.vertical?"width":"height"]=e.size),!1!==e.spaced){const n=!0===e.spaced?`${a.md}px`:e.spaced in a?`${a[e.spaced]}px`:e.spaced,o=!0===e.vertical?["Left","Right"]:["Top","Bottom"];t[`margin${o[0]}`]=t[`margin${o[1]}`]=n}return t}));return()=>(0,o.h)("hr",{class:c.value,style:d.value,"aria-orientation":l.value})}})},136:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var o=n(9835),r=n(5987);const l=(0,o.h)("div",{class:"q-space"}),i=(0,r.L)({name:"QSpace",setup(){return()=>l}})},3940:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var o=n(9835),r=n(244);const l={size:{type:[Number,String],default:"1em"},color:String};function i(e){return{cSize:(0,o.Fl)((()=>e.size in r.Ok?`${r.Ok[e.size]}px`:e.size)),classes:(0,o.Fl)((()=>"q-spinner"+(e.color?` text-${e.color}`:"")))}}var a=n(5987);const s=(0,a.L)({name:"QSpinner",props:{...l,thickness:{type:Number,default:5}},setup(e){const{cSize:t,classes:n}=i(e);return()=>(0,o.h)("svg",{class:n.value+" q-spinner-mat",width:t.value,height:t.value,viewBox:"25 25 50 50"},[(0,o.h)("circle",{class:"path",cx:"50",cy:"50",r:"20",fill:"none",stroke:"currentColor","stroke-width":e.thickness,"stroke-miterlimit":"10"})])}})},8335:(e,t,n)=>{"use strict";n.d(t,{Z:()=>h});var o=n(9835),r=n(499),l=n(1957),i=n(5987);const a=(0,i.L)({name:"QSlideTransition",props:{appear:Boolean,duration:{type:Number,default:300}},emits:["show","hide"],setup(e,{slots:t,emit:n}){let r,i,a,s,u=!1,c=null,d=null;function f(){r&&r(),r=null,u=!1,null!==c&&(clearTimeout(c),c=null),null!==d&&(clearTimeout(d),d=null),void 0!==i&&i.removeEventListener("transitionend",a),a=null}function p(t,n,o){void 0!==n&&(t.style.height=`${n}px`),t.style.transition=`height ${e.duration}ms cubic-bezier(.25, .8, .50, 1)`,u=!0,r=o}function v(e,t){e.style.overflowY=null,e.style.height=null,e.style.transition=null,f(),t!==s&&n(t)}function h(t,n){let o=0;i=t,!0===u?(f(),o=t.offsetHeight===t.scrollHeight?0:void 0):(s="hide",t.style.overflowY="hidden"),p(t,o,n),c=setTimeout((()=>{c=null,t.style.height=`${t.scrollHeight}px`,a=e=>{d=null,Object(e)===e&&e.target!==t||v(t,"show")},t.addEventListener("transitionend",a),d=setTimeout(a,1.1*e.duration)}),100)}function m(t,n){let o;i=t,!0===u?f():(s="show",t.style.overflowY="hidden",o=t.scrollHeight),p(t,o,n),c=setTimeout((()=>{c=null,t.style.height=0,a=e=>{d=null,Object(e)===e&&e.target!==t||v(t,"hide")},t.addEventListener("transitionend",a),d=setTimeout(a,1.1*e.duration)}),100)}return(0,o.Jd)((()=>{!0===u&&f()})),()=>(0,o.h)(l.uT,{css:!1,appear:e.appear,onEnter:h,onLeave:m},t.default)}});var s=n(7078),u=n(5475),c=n(3978),d=n(5439),f=n(2026);function p(e){return(0,o.h)("div",{class:"q-stepper__step-content"},[(0,o.h)("div",{class:"q-stepper__step-inner"},(0,f.KR)(e.default))])}const v={setup(e,{slots:t}){return()=>p(t)}},h=(0,i.L)({name:"QStep",props:{...u.vZ,icon:String,color:String,title:{type:String,required:!0},caption:String,prefix:[String,Number],doneIcon:String,doneColor:String,activeIcon:String,activeColor:String,errorIcon:String,errorColor:String,headerNav:{type:Boolean,default:!0},done:Boolean,error:Boolean,onScroll:[Function,Array]},setup(e,{slots:t,emit:n}){const{proxy:{$q:l}}=(0,o.FN)(),i=(0,o.f3)(d.Lu,d.qO);if(i===d.qO)return console.error("QStep needs to be a child of QStepper"),d.qO;const{getCacheWithFn:u}=(0,c.Z)(),f=(0,r.iH)(null),h=(0,o.Fl)((()=>i.value.modelValue===e.name)),m=(0,o.Fl)((()=>!0!==l.platform.is.ios&&!0===l.platform.is.chrome||!0!==h.value||!0!==i.value.vertical?{}:{onScroll(t){const{target:o}=t;o.scrollTop>0&&(o.scrollTop=0),void 0!==e.onScroll&&n("scroll",t)}})),g=(0,o.Fl)((()=>"string"===typeof e.name||"number"===typeof e.name?e.name:String(e.name)));function b(){const e=i.value.vertical;return!0===e&&!0===i.value.keepAlive?(0,o.h)(o.Ob,i.value.keepAliveProps.value,!0===h.value?[(0,o.h)(!0===i.value.needsUniqueKeepAliveWrapper.value?u(g.value,(()=>({...v,name:g.value}))):v,{key:g.value},t.default)]:void 0):!0!==e||!0===h.value?p(t):void 0}return()=>(0,o.h)("div",{ref:f,class:"q-stepper__step",role:"tabpanel",...m.value},!0===i.value.vertical?[(0,o.h)(s.Z,{stepper:i.value,step:e,goToPanel:i.value.goToPanel}),!0===i.value.animated?(0,o.h)(a,b):b()]:[b()])}})},8225:(e,t,n)=>{"use strict";n.d(t,{Z:()=>f});var o=n(9835),r=n(7078),l=n(8234),i=n(5475),a=n(5987),s=n(5439),u=n(2026);const c=/(-\w)/g;function d(e){const t={};for(const n in e){const o=n.replace(c,(e=>e[1].toUpperCase()));t[o]=e[n]}return t}const f=(0,a.L)({name:"QStepper",props:{...l.S,...i.t6,flat:Boolean,bordered:Boolean,alternativeLabels:Boolean,headerNav:Boolean,contracted:Boolean,headerClass:String,inactiveColor:String,inactiveIcon:String,doneIcon:String,doneColor:String,activeIcon:String,activeColor:String,errorIcon:String,errorColor:String},emits:i.K6,setup(e,{slots:t}){const n=(0,o.FN)(),a=(0,l.Z)(e,n.proxy.$q),{updatePanelsList:c,isValidPanelName:f,updatePanelIndex:p,getPanelContent:v,getPanels:h,panelDirectives:m,goToPanel:g,keepAliveProps:b,needsUniqueKeepAliveWrapper:y}=(0,i.ZP)();(0,o.JJ)(s.Lu,(0,o.Fl)((()=>({goToPanel:g,keepAliveProps:b,needsUniqueKeepAliveWrapper:y,...e}))));const _=(0,o.Fl)((()=>"q-stepper q-stepper--"+(!0===e.vertical?"vertical":"horizontal")+(!0===e.flat?" q-stepper--flat":"")+(!0===e.bordered?" q-stepper--bordered":"")+(!0===a.value?" q-stepper--dark q-dark":""))),w=(0,o.Fl)((()=>`q-stepper__header row items-stretch justify-between q-stepper__header--${!0===e.alternativeLabels?"alternative":"standard"}-labels`+(!1===e.flat||!0===e.bordered?" q-stepper__header--border":"")+(!0===e.contracted?" q-stepper__header--contracted":"")+(void 0!==e.headerClass?` ${e.headerClass}`:"")));function S(){const n=(0,u.KR)(t.message,[]);if(!0===e.vertical){f(e.modelValue)&&p();const r=(0,o.h)("div",{class:"q-stepper__content"},(0,u.KR)(t.default));return void 0===n?[r]:n.concat(r)}return[(0,o.h)("div",{class:w.value},h().map((t=>{const n=d(t.props);return(0,o.h)(r.Z,{key:n.name,stepper:e,step:n,goToPanel:g})}))),n,(0,u.Jl)("div",{class:"q-stepper__content q-panel-parent"},v(),"cont",e.swipeable,(()=>m.value))]}return()=>(c(t),(0,o.h)("div",{class:_.value},(0,u.vs)(t.navigation,S())))}})},7078:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});n(9665);var o=n(9835),r=n(499),l=n(2857),i=n(1722),a=n(5987);const s=(0,a.L)({name:"StepHeader",props:{stepper:{},step:{},goToPanel:Function},setup(e,{attrs:t}){const{proxy:{$q:n}}=(0,o.FN)(),a=(0,r.iH)(null),s=(0,o.Fl)((()=>e.stepper.modelValue===e.step.name)),u=(0,o.Fl)((()=>{const t=e.step.disable;return!0===t||""===t})),c=(0,o.Fl)((()=>{const t=e.step.error;return!0===t||""===t})),d=(0,o.Fl)((()=>{const t=e.step.done;return!1===u.value&&(!0===t||""===t)})),f=(0,o.Fl)((()=>{const t=e.step.headerNav,n=!0===t||""===t||void 0===t;return!1===u.value&&e.stepper.headerNav&&n})),p=(0,o.Fl)((()=>e.step.prefix&&(!1===s.value||"none"===e.stepper.activeIcon)&&(!1===c.value||"none"===e.stepper.errorIcon)&&(!1===d.value||"none"===e.stepper.doneIcon))),v=(0,o.Fl)((()=>{const t=e.step.icon||e.stepper.inactiveIcon;if(!0===s.value){const o=e.step.activeIcon||e.stepper.activeIcon;return"none"===o?t:o||n.iconSet.stepper.active}if(!0===c.value){const o=e.step.errorIcon||e.stepper.errorIcon;return"none"===o?t:o||n.iconSet.stepper.error}if(!1===u.value&&!0===d.value){const o=e.step.doneIcon||e.stepper.doneIcon;return"none"===o?t:o||n.iconSet.stepper.done}return t})),h=(0,o.Fl)((()=>{const t=!0===c.value?e.step.errorColor||e.stepper.errorColor:void 0;if(!0===s.value){const n=e.step.activeColor||e.stepper.activeColor||e.step.color;return void 0!==n?n:t}return void 0!==t?t:!1===u.value&&!0===d.value?e.step.doneColor||e.stepper.doneColor||e.step.color||e.stepper.inactiveColor:e.step.color||e.stepper.inactiveColor})),m=(0,o.Fl)((()=>"q-stepper__tab col-grow flex items-center no-wrap relative-position"+(void 0!==h.value?` text-${h.value}`:"")+(!0===c.value?" q-stepper__tab--error q-stepper__tab--error-with-"+(!0===p.value?"prefix":"icon"):"")+(!0===s.value?" q-stepper__tab--active":"")+(!0===d.value?" q-stepper__tab--done":"")+(!0===f.value?" q-stepper__tab--navigation q-focusable q-hoverable":"")+(!0===u.value?" q-stepper__tab--disabled":""))),g=(0,o.Fl)((()=>!0===e.stepper.headerNav&&f.value));function b(){null!==a.value&&a.value.focus(),!1===s.value&&e.goToPanel(e.step.name)}function y(t){13===t.keyCode&&!1===s.value&&e.goToPanel(e.step.name)}return()=>{const n={class:m.value};!0===f.value&&(n.onClick=b,n.onKeyup=y,Object.assign(n,!0===u.value?{tabindex:-1,"aria-disabled":"true"}:{tabindex:t.tabindex||0}));const r=[(0,o.h)("div",{class:"q-focus-helper",tabindex:-1,ref:a}),(0,o.h)("div",{class:"q-stepper__dot row flex-center q-stepper__line relative-position"},[(0,o.h)("span",{class:"row flex-center"},[!0===p.value?e.step.prefix:(0,o.h)(l.Z,{name:v.value})])])];if(void 0!==e.step.title&&null!==e.step.title){const t=[(0,o.h)("div",{class:"q-stepper__title"},e.step.title)];void 0!==e.step.caption&&null!==e.step.caption&&t.push((0,o.h)("div",{class:"q-stepper__caption"},e.step.caption)),r.push((0,o.h)("div",{class:"q-stepper__label q-stepper__line relative-position"},t))}return(0,o.wy)((0,o.h)("div",n,r),[[i.Z,g.value]])}}})},3175:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(9835),r=n(2857),l=n(1926),i=n(5987);const a=(0,i.L)({name:"QToggle",props:{...l.Fz,icon:String,iconColor:String},emits:l.ZB,setup(e){function t(t,n){const l=(0,o.Fl)((()=>(!0===t.value?e.checkedIcon:!0===n.value?e.indeterminateIcon:e.uncheckedIcon)||e.icon)),i=(0,o.Fl)((()=>!0===t.value?e.iconColor:null));return()=>[(0,o.h)("div",{class:"q-toggle__track"}),(0,o.h)("div",{class:"q-toggle__thumb absolute flex flex-center no-wrap"},void 0!==l.value?[(0,o.h)(r.Z,{name:l.value,color:i.value})]:void 0)]}return(0,l.ZP)("toggle",t)}})},1663:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var o=n(9835),r=n(5987),l=n(2026);const i=(0,r.L)({name:"QToolbar",props:{inset:Boolean},setup(e,{slots:t}){const n=(0,o.Fl)((()=>"q-toolbar row no-wrap items-center"+(!0===e.inset?" q-toolbar--inset":"")));return()=>(0,o.h)("div",{class:n.value,role:"toolbar"},(0,l.KR)(t.default))}})},6858:(e,t,n)=>{"use strict";n.d(t,{Z:()=>_});var o=n(9835),r=n(499),l=n(1957),i=n(4397),a=n(4088),s=n(3842),u=n(1518),c=n(431),d=n(6916),f=n(2695),p=n(5987),v=n(3701),h=n(1384),m=n(2589),g=n(2026),b=n(9092),y=n(9388);const _=(0,p.L)({name:"QTooltip",inheritAttrs:!1,props:{...i.u,...s.vr,...c.D,maxHeight:{type:String,default:null},maxWidth:{type:String,default:null},transitionShow:{default:"jump-down"},transitionHide:{default:"jump-up"},anchor:{type:String,default:"bottom middle",validator:y.$},self:{type:String,default:"top middle",validator:y.$},offset:{type:Array,default:()=>[14,14],validator:y.io},scrollTarget:{default:void 0},delay:{type:Number,default:0},hideDelay:{type:Number,default:0}},emits:[...s.gH],setup(e,{slots:t,emit:n,attrs:p}){let _,w;const S=(0,o.FN)(),{proxy:{$q:k}}=S,x=(0,r.iH)(null),E=(0,r.iH)(!1),C=(0,o.Fl)((()=>(0,y.li)(e.anchor,k.lang.rtl))),T=(0,o.Fl)((()=>(0,y.li)(e.self,k.lang.rtl))),O=(0,o.Fl)((()=>!0!==e.persistent)),{registerTick:F,removeTick:L}=(0,d.Z)(),{registerTimeout:A}=(0,f.Z)(),{transitionProps:P,transitionStyle:q}=(0,c.Z)(e),{localScrollTarget:R,changeScrollEvent:N,unconfigureScrollTarget:M}=(0,a.Z)(e,G),{anchorEl:I,canShow:$,anchorEvents:D}=(0,i.Z)({showing:E,configureAnchorEl:X}),{show:V,hide:j}=(0,s.ZP)({showing:E,canShow:$,handleShow:U,handleHide:W,hideOnRouteChange:O,processOnMount:!0});Object.assign(D,{delayShow:Y,delayHide:J});const{showPortal:B,hidePortal:H,renderPortal:z}=(0,u.Z)(S,x,ee,"tooltip");if(!0===k.platform.is.mobile){const t={anchorEl:I,innerRef:x,onClickOutside(e){return j(e),e.target.classList.contains("q-dialog__backdrop")&&(0,h.NS)(e),!0}},n=(0,o.Fl)((()=>null===e.modelValue&&!0!==e.persistent&&!0===E.value));(0,o.YP)(n,(e=>{const n=!0===e?b.m:b.D;n(t)})),(0,o.Jd)((()=>{(0,b.D)(t)}))}function U(t){B(),F((()=>{w=new MutationObserver((()=>K())),w.observe(x.value,{attributes:!1,childList:!0,characterData:!0,subtree:!0}),K(),G()})),void 0===_&&(_=(0,o.YP)((()=>k.screen.width+"|"+k.screen.height+"|"+e.self+"|"+e.anchor+"|"+k.lang.rtl),K)),A((()=>{B(!0),n("show",t)}),e.transitionDuration)}function W(t){L(),H(),Z(),A((()=>{H(!0),n("hide",t)}),e.transitionDuration)}function Z(){void 0!==w&&(w.disconnect(),w=void 0),void 0!==_&&(_(),_=void 0),M(),(0,h.ul)(D,"tooltipTemp")}function K(){(0,y.wq)({targetEl:x.value,offset:e.offset,anchorEl:I.value,anchorOrigin:C.value,selfOrigin:T.value,maxHeight:e.maxHeight,maxWidth:e.maxWidth})}function Y(t){if(!0===k.platform.is.mobile){(0,m.M)(),document.body.classList.add("non-selectable");const e=I.value,t=["touchmove","touchcancel","touchend","click"].map((t=>[e,t,"delayHide","passiveCapture"]));(0,h.M0)(D,"tooltipTemp",t)}A((()=>{V(t)}),e.delay)}function J(t){!0===k.platform.is.mobile&&((0,h.ul)(D,"tooltipTemp"),(0,m.M)(),setTimeout((()=>{document.body.classList.remove("non-selectable")}),10)),A((()=>{j(t)}),e.hideDelay)}function X(){if(!0===e.noParentEvent||null===I.value)return;const t=!0===k.platform.is.mobile?[[I.value,"touchstart","delayShow","passive"]]:[[I.value,"mouseenter","delayShow","passive"],[I.value,"mouseleave","delayHide","passive"]];(0,h.M0)(D,"anchor",t)}function G(){if(null!==I.value||void 0!==e.scrollTarget){R.value=(0,v.b0)(I.value,e.scrollTarget);const t=!0===e.noParentEvent?K:j;N(R.value,t)}}function Q(){return!0===E.value?(0,o.h)("div",{...p,ref:x,class:["q-tooltip q-tooltip--style q-position-engine no-pointer-events",p.class],style:[p.style,q.value],role:"tooltip"},(0,g.KR)(t.default)):null}function ee(){return(0,o.h)(l.uT,P.value,Q)}return(0,o.Jd)(Z),Object.assign(S.proxy,{updatePosition:K}),z}})},4397:(e,t,n)=>{"use strict";n.d(t,{Z:()=>u,u:()=>s});var o=n(9835),r=n(499),l=n(2589),i=n(1384),a=n(1705);const s={target:{default:!0},noParentEvent:Boolean,contextMenu:Boolean};function u({showing:e,avoidEmit:t,configureAnchorEl:n}){const{props:s,proxy:u,emit:c}=(0,o.FN)(),d=(0,r.iH)(null);let f=null;function p(e){return null!==d.value&&(void 0===e||void 0===e.touches||e.touches.length<=1)}const v={};function h(){(0,i.ul)(v,"anchor")}function m(e){d.value=e;while(d.value.classList.contains("q-anchor--skip"))d.value=d.value.parentNode;n()}function g(){if(!1===s.target||""===s.target||null===u.$el.parentNode)d.value=null;else if(!0===s.target)m(u.$el.parentNode);else{let t=s.target;if("string"===typeof s.target)try{t=document.querySelector(s.target)}catch(e){t=void 0}void 0!==t&&null!==t?(d.value=t.$el||t,n()):(d.value=null,console.error(`Anchor: target "${s.target}" not found`))}}return void 0===n&&(Object.assign(v,{hide(e){u.hide(e)},toggle(e){u.toggle(e),e.qAnchorHandled=!0},toggleKey(e){!0===(0,a.So)(e,13)&&v.toggle(e)},contextClick(e){u.hide(e),(0,i.X$)(e),(0,o.Y3)((()=>{u.show(e),e.qAnchorHandled=!0}))},prevent:i.X$,mobileTouch(e){if(v.mobileCleanup(e),!0!==p(e))return;u.hide(e),d.value.classList.add("non-selectable");const t=e.target;(0,i.M0)(v,"anchor",[[t,"touchmove","mobileCleanup","passive"],[t,"touchend","mobileCleanup","passive"],[t,"touchcancel","mobileCleanup","passive"],[d.value,"contextmenu","prevent","notPassive"]]),f=setTimeout((()=>{f=null,u.show(e),e.qAnchorHandled=!0}),300)},mobileCleanup(t){d.value.classList.remove("non-selectable"),null!==f&&(clearTimeout(f),f=null),!0===e.value&&void 0!==t&&(0,l.M)()}}),n=function(e=s.contextMenu){if(!0===s.noParentEvent||null===d.value)return;let t;t=!0===e?!0===u.$q.platform.is.mobile?[[d.value,"touchstart","mobileTouch","passive"]]:[[d.value,"mousedown","hide","passive"],[d.value,"contextmenu","contextClick","notPassive"]]:[[d.value,"click","toggle","passive"],[d.value,"keyup","toggleKey","passive"]],(0,i.M0)(v,"anchor",t)}),(0,o.YP)((()=>s.contextMenu),(e=>{null!==d.value&&(h(),n(e))})),(0,o.YP)((()=>s.target),(()=>{null!==d.value&&h(),g()})),(0,o.YP)((()=>s.noParentEvent),(e=>{null!==d.value&&(!0===e?h():n())})),(0,o.bv)((()=>{g(),!0!==t&&!0===s.modelValue&&null===d.value&&c("update:modelValue",!1)})),(0,o.Jd)((()=>{null!==f&&clearTimeout(f),h()})),{anchorEl:d,canShow:p,anchorEvents:v}}},3978:(e,t,n)=>{"use strict";function o(){const e=new Map;return{getCache:function(t,n){return void 0===e[t]?e[t]=n:e[t]},getCacheWithFn:function(t,n){return void 0===e[t]?e[t]=n():e[t]}}}n.d(t,{Z:()=>o})},8234:(e,t,n)=>{"use strict";n.d(t,{S:()=>r,Z:()=>l});var o=n(9835);const r={dark:{type:Boolean,default:null}};function l(e,t){return(0,o.Fl)((()=>null===e.dark?t.dark.isActive:e.dark))}},1486:(e,t,n)=>{"use strict";n.d(t,{ZP:()=>F,yV:()=>E,HJ:()=>T,Cl:()=>C,tL:()=>O});n(9665);var o=n(9835),r=n(499),l=n(1957),i=n(7506),a=n(2857),s=n(3940),u=n(8234),c=n(5439);function d({validate:e,resetValidation:t,requiresQForm:n}){const r=(0,o.f3)(c.vh,!1);if(!1!==r){const{props:n,proxy:l}=(0,o.FN)();Object.assign(l,{validate:e,resetValidation:t}),(0,o.YP)((()=>n.disable),(e=>{!0===e?("function"===typeof t&&t(),r.unbindComponent(l)):r.bindComponent(l)})),(0,o.bv)((()=>{!0!==n.disable&&r.bindComponent(l)})),(0,o.Jd)((()=>{!0!==n.disable&&r.unbindComponent(l)}))}else!0===n&&console.error("Parent QForm not found on useFormChild()!")}var f=n(3628),p=n(899),v=n(3251);const h=[!0,!1,"ondemand"],m={modelValue:{},error:{type:Boolean,default:null},errorMessage:String,noErrorIcon:Boolean,rules:Array,reactiveRules:Boolean,lazyRules:{type:[Boolean,String],validator:e=>h.includes(e)}};function g(e,t){const{props:n,proxy:l}=(0,o.FN)(),i=(0,r.iH)(!1),a=(0,r.iH)(null),s=(0,r.iH)(null);d({validate:_,resetValidation:y});let u,c=0;const h=(0,o.Fl)((()=>void 0!==n.rules&&null!==n.rules&&0!==n.rules.length)),m=(0,o.Fl)((()=>!0!==n.disable&&!0===h.value)),g=(0,o.Fl)((()=>!0===n.error||!0===i.value)),b=(0,o.Fl)((()=>"string"===typeof n.errorMessage&&0!==n.errorMessage.length?n.errorMessage:a.value));function y(){c++,t.value=!1,s.value=null,i.value=!1,a.value=null,S.cancel()}function _(e=n.modelValue){if(!0!==m.value)return!0;const o=++c,r=!0!==t.value?()=>{s.value=!0}:()=>{},l=(e,n)=>{!0===e&&r(),i.value=e,a.value=n||null,t.value=!1},u=[];for(let t=0;t<n.rules.length;t++){const o=n.rules[t];let r;if("function"===typeof o?r=o(e,f.E):"string"===typeof o&&void 0!==f.E[o]&&(r=f.E[o](e)),!1===r||"string"===typeof r)return l(!0,r),!1;!0!==r&&void 0!==r&&u.push(r)}return 0===u.length?(l(!1),!0):(t.value=!0,Promise.all(u).then((e=>{if(void 0===e||!1===Array.isArray(e)||0===e.length)return o===c&&l(!1),!0;const t=e.find((e=>!1===e||"string"===typeof e));return o===c&&l(void 0!==t,t),void 0===t}),(e=>(o===c&&(console.error(e),l(!0)),!1))))}function w(e){!0===m.value&&"ondemand"!==n.lazyRules&&(!0===s.value||!0!==n.lazyRules&&!0!==e)&&S()}(0,o.YP)((()=>n.modelValue),(()=>{w()})),(0,o.YP)((()=>n.reactiveRules),(e=>{!0===e?void 0===u&&(u=(0,o.YP)((()=>n.rules),(()=>{w(!0)}))):void 0!==u&&(u(),u=void 0)}),{immediate:!0}),(0,o.YP)(e,(e=>{!0===e?null===s.value&&(s.value=!1):!1===s.value&&(s.value=!0,!0===m.value&&"ondemand"!==n.lazyRules&&!1===t.value&&S())}));const S=(0,p.Z)(_,0);return(0,o.Jd)((()=>{void 0!==u&&u(),S.cancel()})),Object.assign(l,{resetValidation:y,validate:_}),(0,v.g)(l,"hasError",(()=>g.value)),{isDirtyModel:s,hasRules:h,hasError:g,errorMessage:b,validate:_,resetValidation:y}}const b=/^on[A-Z]/;function y(e,t){const n={listeners:(0,r.iH)({}),attributes:(0,r.iH)({})};function l(){const o={},r={};for(const t in e)"class"!==t&&"style"!==t&&!1===b.test(t)&&(o[t]=e[t]);for(const e in t.props)!0===b.test(e)&&(r[e]=t.props[e]);n.attributes.value=o,n.listeners.value=r}return(0,o.Xn)(l),l(),n}var _=n(2026),w=n(796),S=n(1384),k=n(7026);function x(e){return void 0===e?`f_${(0,w.Z)()}`:e}function E(e){return void 0!==e&&null!==e&&0!==(""+e).length}const C={...u.S,...m,label:String,stackLabel:Boolean,hint:String,hideHint:Boolean,prefix:String,suffix:String,labelColor:String,color:String,bgColor:String,filled:Boolean,outlined:Boolean,borderless:Boolean,standout:[Boolean,String],square:Boolean,loading:Boolean,labelSlot:Boolean,bottomSlots:Boolean,hideBottomSpace:Boolean,rounded:Boolean,dense:Boolean,itemAligned:Boolean,counter:Boolean,clearable:Boolean,clearIcon:String,disable:Boolean,readonly:Boolean,autofocus:Boolean,for:String,maxlength:[Number,String]},T=["update:modelValue","clear","focus","blur","popupShow","popupHide"];function O(){const{props:e,attrs:t,proxy:n,vnode:l}=(0,o.FN)(),i=(0,u.Z)(e,n.$q);return{isDark:i,editable:(0,o.Fl)((()=>!0!==e.disable&&!0!==e.readonly)),innerLoading:(0,r.iH)(!1),focused:(0,r.iH)(!1),hasPopupOpen:!1,splitAttrs:y(t,l),targetUid:(0,r.iH)(x(e.for)),rootRef:(0,r.iH)(null),targetRef:(0,r.iH)(null),controlRef:(0,r.iH)(null)}}function F(e){const{props:t,emit:n,slots:r,attrs:u,proxy:c}=(0,o.FN)(),{$q:d}=c;let f=null;void 0===e.hasValue&&(e.hasValue=(0,o.Fl)((()=>E(t.modelValue)))),void 0===e.emitValue&&(e.emitValue=e=>{n("update:modelValue",e)}),void 0===e.controlEvents&&(e.controlEvents={onFocusin:M,onFocusout:I}),Object.assign(e,{clearValue:$,onControlFocusin:M,onControlFocusout:I,focus:R}),void 0===e.computedCounter&&(e.computedCounter=(0,o.Fl)((()=>{if(!1!==t.counter){const e="string"===typeof t.modelValue||"number"===typeof t.modelValue?(""+t.modelValue).length:!0===Array.isArray(t.modelValue)?t.modelValue.length:0,n=void 0!==t.maxlength?t.maxlength:t.maxValues;return e+(void 0!==n?" / "+n:"")}})));const{isDirtyModel:p,hasRules:v,hasError:h,errorMessage:m,resetValidation:b}=g(e.focused,e.innerLoading),y=void 0!==e.floatingLabel?(0,o.Fl)((()=>!0===t.stackLabel||!0===e.focused.value||!0===e.floatingLabel.value)):(0,o.Fl)((()=>!0===t.stackLabel||!0===e.focused.value||!0===e.hasValue.value)),w=(0,o.Fl)((()=>!0===t.bottomSlots||void 0!==t.hint||!0===v.value||!0===t.counter||null!==t.error)),C=(0,o.Fl)((()=>!0===t.filled?"filled":!0===t.outlined?"outlined":!0===t.borderless?"borderless":t.standout?"standout":"standard")),T=(0,o.Fl)((()=>`q-field row no-wrap items-start q-field--${C.value}`+(void 0!==e.fieldClass?` ${e.fieldClass.value}`:"")+(!0===t.rounded?" q-field--rounded":"")+(!0===t.square?" q-field--square":"")+(!0===y.value?" q-field--float":"")+(!0===F.value?" q-field--labeled":"")+(!0===t.dense?" q-field--dense":"")+(!0===t.itemAligned?" q-field--item-aligned q-item-type":"")+(!0===e.isDark.value?" q-field--dark":"")+(void 0===e.getControl?" q-field--auto-height":"")+(!0===e.focused.value?" q-field--focused":"")+(!0===h.value?" q-field--error":"")+(!0===h.value||!0===e.focused.value?" q-field--highlighted":"")+(!0!==t.hideBottomSpace&&!0===w.value?" q-field--with-bottom":"")+(!0===t.disable?" q-field--disabled":!0===t.readonly?" q-field--readonly":""))),O=(0,o.Fl)((()=>"q-field__control relative-position row no-wrap"+(void 0!==t.bgColor?` bg-${t.bgColor}`:"")+(!0===h.value?" text-negative":"string"===typeof t.standout&&0!==t.standout.length&&!0===e.focused.value?` ${t.standout}`:void 0!==t.color?` text-${t.color}`:""))),F=(0,o.Fl)((()=>!0===t.labelSlot||void 0!==t.label)),L=(0,o.Fl)((()=>"q-field__label no-pointer-events absolute ellipsis"+(void 0!==t.labelColor&&!0!==h.value?` text-${t.labelColor}`:""))),A=(0,o.Fl)((()=>({id:e.targetUid.value,editable:e.editable.value,focused:e.focused.value,floatingLabel:y.value,modelValue:t.modelValue,emitValue:e.emitValue}))),P=(0,o.Fl)((()=>{const n={for:e.targetUid.value};return!0===t.disable?n["aria-disabled"]="true":!0===t.readonly&&(n["aria-readonly"]="true"),n}));function q(){const t=document.activeElement;let n=void 0!==e.targetRef&&e.targetRef.value;!n||null!==t&&t.id===e.targetUid.value||(!0===n.hasAttribute("tabindex")||(n=n.querySelector("[tabindex]")),n&&n!==t&&n.focus({preventScroll:!0}))}function R(){(0,k.jd)(q)}function N(){(0,k.fP)(q);const t=document.activeElement;null!==t&&e.rootRef.value.contains(t)&&t.blur()}function M(t){null!==f&&(clearTimeout(f),f=null),!0===e.editable.value&&!1===e.focused.value&&(e.focused.value=!0,n("focus",t))}function I(t,o){null!==f&&clearTimeout(f),f=setTimeout((()=>{f=null,(!0!==document.hasFocus()||!0!==e.hasPopupOpen&&void 0!==e.controlRef&&null!==e.controlRef.value&&!1===e.controlRef.value.contains(document.activeElement))&&(!0===e.focused.value&&(e.focused.value=!1,n("blur",t)),void 0!==o&&o())}))}function $(r){if((0,S.NS)(r),!0!==d.platform.is.mobile){const t=void 0!==e.targetRef&&e.targetRef.value||e.rootRef.value;t.focus()}else!0===e.rootRef.value.contains(document.activeElement)&&document.activeElement.blur();"file"===t.type&&(e.inputRef.value.value=null),n("update:modelValue",null),n("clear",t.modelValue),(0,o.Y3)((()=>{b(),!0!==d.platform.is.mobile&&(p.value=!1)}))}function D(){const n=[];return void 0!==r.prepend&&n.push((0,o.h)("div",{class:"q-field__prepend q-field__marginal row no-wrap items-center",key:"prepend",onClick:S.X$},r.prepend())),n.push((0,o.h)("div",{class:"q-field__control-container col relative-position row no-wrap q-anchor--skip"},V())),!0===h.value&&!1===t.noErrorIcon&&n.push(B("error",[(0,o.h)(a.Z,{name:d.iconSet.field.error,color:"negative"})])),!0===t.loading||!0===e.innerLoading.value?n.push(B("inner-loading-append",void 0!==r.loading?r.loading():[(0,o.h)(s.Z,{color:t.color})])):!0===t.clearable&&!0===e.hasValue.value&&!0===e.editable.value&&n.push(B("inner-clearable-append",[(0,o.h)(a.Z,{class:"q-field__focusable-action",tag:"button",name:t.clearIcon||d.iconSet.field.clear,tabindex:0,type:"button","aria-hidden":null,role:null,onClick:$})])),void 0!==r.append&&n.push((0,o.h)("div",{class:"q-field__append q-field__marginal row no-wrap items-center",key:"append",onClick:S.X$},r.append())),void 0!==e.getInnerAppend&&n.push(B("inner-append",e.getInnerAppend())),void 0!==e.getControlChild&&n.push(e.getControlChild()),n}function V(){const n=[];return void 0!==t.prefix&&null!==t.prefix&&n.push((0,o.h)("div",{class:"q-field__prefix no-pointer-events row items-center"},t.prefix)),void 0!==e.getShadowControl&&!0===e.hasShadow.value&&n.push(e.getShadowControl()),void 0!==e.getControl?n.push(e.getControl()):void 0!==r.rawControl?n.push(r.rawControl()):void 0!==r.control&&n.push((0,o.h)("div",{ref:e.targetRef,class:"q-field__native row",tabindex:-1,...e.splitAttrs.attributes.value,"data-autofocus":!0===t.autofocus||void 0},r.control(A.value))),!0===F.value&&n.push((0,o.h)("div",{class:L.value},(0,_.KR)(r.label,t.label))),void 0!==t.suffix&&null!==t.suffix&&n.push((0,o.h)("div",{class:"q-field__suffix no-pointer-events row items-center"},t.suffix)),n.concat((0,_.KR)(r.default))}function j(){let n,i;!0===h.value?null!==m.value?(n=[(0,o.h)("div",{role:"alert"},m.value)],i=`q--slot-error-${m.value}`):(n=(0,_.KR)(r.error),i="q--slot-error"):!0===t.hideHint&&!0!==e.focused.value||(void 0!==t.hint?(n=[(0,o.h)("div",t.hint)],i=`q--slot-hint-${t.hint}`):(n=(0,_.KR)(r.hint),i="q--slot-hint"));const a=!0===t.counter||void 0!==r.counter;if(!0===t.hideBottomSpace&&!1===a&&void 0===n)return;const s=(0,o.h)("div",{key:i,class:"q-field__messages col"},n);return(0,o.h)("div",{class:"q-field__bottom row items-start q-field__bottom--"+(!0!==t.hideBottomSpace?"animated":"stale"),onClick:S.X$},[!0===t.hideBottomSpace?s:(0,o.h)(l.uT,{name:"q-transition--field-message"},(()=>s)),!0===a?(0,o.h)("div",{class:"q-field__counter"},void 0!==r.counter?r.counter():e.computedCounter.value):null])}function B(e,t){return null===t?null:(0,o.h)("div",{key:e,class:"q-field__append q-field__marginal row no-wrap items-center q-anchor--skip"},t)}(0,o.YP)((()=>t.for),(t=>{e.targetUid.value=x(t)}));let H=!1;return(0,o.se)((()=>{H=!0})),(0,o.dl)((()=>{!0===H&&!0===t.autofocus&&c.focus()})),(0,o.bv)((()=>{!0===i.uX.value&&void 0===t.for&&(e.targetUid.value=x()),!0===t.autofocus&&c.focus()})),(0,o.Jd)((()=>{null!==f&&clearTimeout(f)})),Object.assign(c,{focus:R,blur:N}),function(){const n=void 0===e.getControl&&void 0===r.control?{...e.splitAttrs.attributes.value,"data-autofocus":!0===t.autofocus||void 0,...P.value}:P.value;return(0,o.h)("label",{ref:e.rootRef,class:[T.value,u.class],style:u.style,...n},[void 0!==r.before?(0,o.h)("div",{class:"q-field__before q-field__marginal row no-wrap items-center",onClick:S.X$},r.before()):null,(0,o.h)("div",{class:"q-field__inner relative-position col self-stretch"},[(0,o.h)("div",{ref:e.controlRef,class:O.value,tabindex:-1,...e.controlEvents},D()),!0===w.value?j():null]),void 0!==r.after?(0,o.h)("div",{class:"q-field__after q-field__marginal row no-wrap items-center",onClick:S.X$},r.after()):null])}}},9256:(e,t,n)=>{"use strict";n.d(t,{Do:()=>a,Fz:()=>r,Vt:()=>l,eX:()=>i});var o=n(9835);const r={name:String};function l(e){return(0,o.Fl)((()=>({type:"hidden",name:e.name,value:e.modelValue})))}function i(e={}){return(t,n,r)=>{t[n]((0,o.h)("input",{class:"hidden"+(r||""),...e.value}))}}function a(e){return(0,o.Fl)((()=>e.name||e.for))}},2802:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});var o=n(7506);const r=/[\u3000-\u303f\u3040-\u309f\u30a0-\u30ff\uff00-\uff9f\u4e00-\u9faf\u3400-\u4dbf]/,l=/[\u4e00-\u9fff\u3400-\u4dbf\u{20000}-\u{2a6df}\u{2a700}-\u{2b73f}\u{2b740}-\u{2b81f}\u{2b820}-\u{2ceaf}\uf900-\ufaff\u3300-\u33ff\ufe30-\ufe4f\uf900-\ufaff\u{2f800}-\u{2fa1f}]/u,i=/[\u3131-\u314e\u314f-\u3163\uac00-\ud7a3]/,a=/[a-z0-9_ -]$/i;function s(e){return function(t){if("compositionend"===t.type||"change"===t.type){if(!0!==t.target.qComposing)return;t.target.qComposing=!1,e(t)}else if("compositionupdate"===t.type&&!0!==t.target.qComposing&&"string"===typeof t.data){const e=!0===o.client.is.firefox?!1===a.test(t.data):!0===r.test(t.data)||!0===l.test(t.data)||!0===i.test(t.data);!0===e&&(t.target.qComposing=!0)}}}},3842:(e,t,n)=>{"use strict";n.d(t,{ZP:()=>a,gH:()=>i,vr:()=>l});var o=n(9835),r=n(2046);const l={modelValue:{type:Boolean,default:null},"onUpdate:modelValue":[Function,Array]},i=["beforeShow","show","beforeHide","hide"];function a({showing:e,canShow:t,hideOnRouteChange:n,handleShow:l,handleHide:i,processOnMount:a}){const s=(0,o.FN)(),{props:u,emit:c,proxy:d}=s;let f;function p(t){!0===e.value?m(t):v(t)}function v(e){if(!0===u.disable||void 0!==e&&!0===e.qAnchorHandled||void 0!==t&&!0!==t(e))return;const n=void 0!==u["onUpdate:modelValue"];!0===n&&(c("update:modelValue",!0),f=e,(0,o.Y3)((()=>{f===e&&(f=void 0)}))),null!==u.modelValue&&!1!==n||h(e)}function h(t){!0!==e.value&&(e.value=!0,c("beforeShow",t),void 0!==l?l(t):c("show",t))}function m(e){if(!0===u.disable)return;const t=void 0!==u["onUpdate:modelValue"];!0===t&&(c("update:modelValue",!1),f=e,(0,o.Y3)((()=>{f===e&&(f=void 0)}))),null!==u.modelValue&&!1!==t||g(e)}function g(t){!1!==e.value&&(e.value=!1,c("beforeHide",t),void 0!==i?i(t):c("hide",t))}function b(t){if(!0===u.disable&&!0===t)void 0!==u["onUpdate:modelValue"]&&c("update:modelValue",!1);else if(!0===t!==e.value){const e=!0===t?h:g;e(f)}}(0,o.YP)((()=>u.modelValue),b),void 0!==n&&!0===(0,r.Rb)(s)&&(0,o.YP)((()=>d.$route.fullPath),(()=>{!0===n.value&&!0===e.value&&m()})),!0===a&&(0,o.bv)((()=>{b(u.modelValue)}));const y={show:v,hide:m,toggle:p};return Object.assign(d,y),y}},5475:(e,t,n)=>{"use strict";n.d(t,{ZP:()=>_,vZ:()=>m,K6:()=>y,t6:()=>b});var o=n(9835),r=n(499),l=n(1957),i=n(7506),a=n(5987),s=n(9367),u=n(1384),c=n(2589);function d(e){const t=[.06,6,50];return"string"===typeof e&&e.length&&e.split(":").forEach(((e,n)=>{const o=parseFloat(e);o&&(t[n]=o)})),t}const f=(0,a.f)({name:"touch-swipe",beforeMount(e,{value:t,arg:n,modifiers:o}){if(!0!==o.mouse&&!0!==i.client.has.touch)return;const r=!0===o.mouseCapture?"Capture":"",l={handler:t,sensitivity:d(n),direction:(0,s.R)(o),noop:u.ZT,mouseStart(e){(0,s.n)(e,l)&&(0,u.du)(e)&&((0,u.M0)(l,"temp",[[document,"mousemove","move",`notPassive${r}`],[document,"mouseup","end","notPassiveCapture"]]),l.start(e,!0))},touchStart(e){if((0,s.n)(e,l)){const t=e.target;(0,u.M0)(l,"temp",[[t,"touchmove","move","notPassiveCapture"],[t,"touchcancel","end","notPassiveCapture"],[t,"touchend","end","notPassiveCapture"]]),l.start(e)}},start(t,n){!0===i.client.is.firefox&&(0,u.Jf)(e,!0);const o=(0,u.FK)(t);l.event={x:o.left,y:o.top,time:Date.now(),mouse:!0===n,dir:!1}},move(e){if(void 0===l.event)return;if(!1!==l.event.dir)return void(0,u.NS)(e);const t=Date.now()-l.event.time;if(0===t)return;const n=(0,u.FK)(e),o=n.left-l.event.x,r=Math.abs(o),i=n.top-l.event.y,a=Math.abs(i);if(!0!==l.event.mouse){if(r<l.sensitivity[1]&&a<l.sensitivity[1])return void l.end(e)}else{if(""!==window.getSelection().toString())return void l.end(e);if(r<l.sensitivity[2]&&a<l.sensitivity[2])return}const s=r/t,d=a/t;!0===l.direction.vertical&&r<a&&r<100&&d>l.sensitivity[0]&&(l.event.dir=i<0?"up":"down"),!0===l.direction.horizontal&&r>a&&a<100&&s>l.sensitivity[0]&&(l.event.dir=o<0?"left":"right"),!0===l.direction.up&&r<a&&i<0&&r<100&&d>l.sensitivity[0]&&(l.event.dir="up"),!0===l.direction.down&&r<a&&i>0&&r<100&&d>l.sensitivity[0]&&(l.event.dir="down"),!0===l.direction.left&&r>a&&o<0&&a<100&&s>l.sensitivity[0]&&(l.event.dir="left"),!0===l.direction.right&&r>a&&o>0&&a<100&&s>l.sensitivity[0]&&(l.event.dir="right"),!1!==l.event.dir?((0,u.NS)(e),!0===l.event.mouse&&(document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),(0,c.M)(),l.styleCleanup=e=>{l.styleCleanup=void 0,document.body.classList.remove("non-selectable");const t=()=>{document.body.classList.remove("no-pointer-events--children")};!0===e?setTimeout(t,50):t()}),l.handler({evt:e,touch:!0!==l.event.mouse,mouse:l.event.mouse,direction:l.event.dir,duration:t,distance:{x:r,y:a}})):l.end(e)},end(t){void 0!==l.event&&((0,u.ul)(l,"temp"),!0===i.client.is.firefox&&(0,u.Jf)(e,!1),void 0!==l.styleCleanup&&l.styleCleanup(!0),void 0!==t&&!1!==l.event.dir&&(0,u.NS)(t),l.event=void 0)}};if(e.__qtouchswipe=l,!0===o.mouse){const t=!0===o.mouseCapture||!0===o.mousecapture?"Capture":"";(0,u.M0)(l,"main",[[e,"mousedown","mouseStart",`passive${t}`]])}!0===i.client.has.touch&&(0,u.M0)(l,"main",[[e,"touchstart","touchStart","passive"+(!0===o.capture?"Capture":"")],[e,"touchmove","noop","notPassiveCapture"]])},updated(e,t){const n=e.__qtouchswipe;void 0!==n&&(t.oldValue!==t.value&&("function"!==typeof t.value&&n.end(),n.handler=t.value),n.direction=(0,s.R)(t.modifiers))},beforeUnmount(e){const t=e.__qtouchswipe;void 0!==t&&((0,u.ul)(t,"main"),(0,u.ul)(t,"temp"),!0===i.client.is.firefox&&(0,u.Jf)(e,!1),void 0!==t.styleCleanup&&t.styleCleanup(),delete e.__qtouchswipe)}});var p=n(3978),v=n(2026),h=n(2046);const m={name:{required:!0},disable:Boolean},g={setup(e,{slots:t}){return()=>(0,o.h)("div",{class:"q-panel scroll",role:"tabpanel"},(0,v.KR)(t.default))}},b={modelValue:{required:!0},animated:Boolean,infinite:Boolean,swipeable:Boolean,vertical:Boolean,transitionPrev:String,transitionNext:String,transitionDuration:{type:[String,Number],default:300},keepAlive:Boolean,keepAliveInclude:[String,Array,RegExp],keepAliveExclude:[String,Array,RegExp],keepAliveMax:Number},y=["update:modelValue","beforeTransition","transition"];function _(){const{props:e,emit:t,proxy:n}=(0,o.FN)(),{getCacheWithFn:i}=(0,p.Z)();let a,s;const u=(0,r.iH)(null),c=(0,r.iH)(null);function d(t){const o=!0===e.vertical?"up":"left";A((!0===n.$q.lang.rtl?-1:1)*(t.direction===o?1:-1))}const m=(0,o.Fl)((()=>[[f,d,void 0,{horizontal:!0!==e.vertical,vertical:e.vertical,mouse:!0}]])),b=(0,o.Fl)((()=>e.transitionPrev||"slide-"+(!0===e.vertical?"down":"right"))),y=(0,o.Fl)((()=>e.transitionNext||"slide-"+(!0===e.vertical?"up":"left"))),_=(0,o.Fl)((()=>`--q-transition-duration: ${e.transitionDuration}ms`)),w=(0,o.Fl)((()=>"string"===typeof e.modelValue||"number"===typeof e.modelValue?e.modelValue:String(e.modelValue))),S=(0,o.Fl)((()=>({include:e.keepAliveInclude,exclude:e.keepAliveExclude,max:e.keepAliveMax}))),k=(0,o.Fl)((()=>void 0!==e.keepAliveInclude||void 0!==e.keepAliveExclude));function x(){A(1)}function E(){A(-1)}function C(e){t("update:modelValue",e)}function T(e){return void 0!==e&&null!==e&&""!==e}function O(e){return a.findIndex((t=>t.props.name===e&&""!==t.props.disable&&!0!==t.props.disable))}function F(){return a.filter((e=>""!==e.props.disable&&!0!==e.props.disable))}function L(t){const n=0!==t&&!0===e.animated&&-1!==u.value?"q-transition--"+(-1===t?b.value:y.value):null;c.value!==n&&(c.value=n)}function A(n,o=u.value){let r=o+n;while(r>-1&&r<a.length){const e=a[r];if(void 0!==e&&""!==e.props.disable&&!0!==e.props.disable)return L(n),s=!0,t("update:modelValue",e.props.name),void setTimeout((()=>{s=!1}));r+=n}!0===e.infinite&&0!==a.length&&-1!==o&&o!==a.length&&A(n,-1===n?a.length:-1)}function P(){const t=O(e.modelValue);return u.value!==t&&(u.value=t),!0}function q(){const t=!0===T(e.modelValue)&&P()&&a[u.value];return!0===e.keepAlive?[(0,o.h)(o.Ob,S.value,[(0,o.h)(!0===k.value?i(w.value,(()=>({...g,name:w.value}))):g,{key:w.value,style:_.value},(()=>t))])]:[(0,o.h)("div",{class:"q-panel scroll",style:_.value,key:w.value,role:"tabpanel"},[t])]}function R(){if(0!==a.length)return!0===e.animated?[(0,o.h)(l.uT,{name:c.value},q)]:q()}function N(e){return a=(0,h.Pf)((0,v.KR)(e.default,[])).filter((e=>null!==e.props&&void 0===e.props.slot&&!0===T(e.props.name))),a.length}function M(){return a}return(0,o.YP)((()=>e.modelValue),((e,n)=>{const r=!0===T(e)?O(e):-1;!0!==s&&L(-1===r?0:r<O(n)?-1:1),u.value!==r&&(u.value=r,t("beforeTransition",e,n),(0,o.Y3)((()=>{t("transition",e,n)})))})),Object.assign(n,{next:x,previous:E,goTo:C}),{panelIndex:u,panelDirectives:m,updatePanelsList:N,updatePanelIndex:P,getPanelContent:R,getEnabledPanels:F,getPanels:M,isValidPanelName:T,keepAliveProps:S,needsUniqueKeepAliveWrapper:k,goToPanelByOffset:A,goToPanel:C,nextPanel:x,previousPanel:E}}},1518:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});n(9665);var o=n(499),r=n(9835),l=(n(1384),n(7026)),i=n(6669),a=n(2909),s=n(3251);function u(e){e=e.parent;while(void 0!==e&&null!==e){if("QGlobalDialog"===e.type.name)return!0;if("QDialog"===e.type.name||"QMenu"===e.type.name)return!1;e=e.parent}return!1}function c(e,t,n,c){const d=(0,o.iH)(!1),f=(0,o.iH)(!1);let p=null;const v={},h="dialog"===c&&u(e);function m(t){if(!0===t)return(0,l.xF)(v),void(f.value=!0);f.value=!1,!1===d.value&&(!1===h&&null===p&&(p=(0,i.q_)(!1,c)),d.value=!0,a.Q$.push(e.proxy),(0,l.YX)(v))}function g(t){if(f.value=!1,!0!==t)return;(0,l.xF)(v),d.value=!1;const n=a.Q$.indexOf(e.proxy);-1!==n&&a.Q$.splice(n,1),null!==p&&((0,i.pB)(p),p=null)}return(0,r.Ah)((()=>{g(!0)})),e.proxy.__qPortal=!0,(0,s.g)(e.proxy,"contentEl",(()=>t.value)),{showPortal:m,hidePortal:g,portalIsActive:d,portalIsAccessible:f,renderPortal:()=>!0===h?n():!0===d.value?[(0,r.h)(r.lR,{to:p},n())]:void 0}}},5917:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var o=n(499),r=n(9835);function l(e,t){const n=(0,o.iH)(null),l=(0,r.Fl)((()=>!0===e.disable?null:(0,r.h)("span",{ref:n,class:"no-outline",tabindex:-1})));function i(e){const o=t.value;void 0!==e&&0===e.type.indexOf("key")?null!==o&&document.activeElement!==o&&!0===o.contains(document.activeElement)&&o.focus():null!==n.value&&(void 0===e||null!==o&&!0===o.contains(e.target))&&n.value.focus()}return{refocusTargetEl:l,refocusTarget:i}}},945:(e,t,n)=>{"use strict";n.d(t,{$:()=>d,Z:()=>f});var o=n(9835),r=n(2046);function l(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}function i(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function a(e,t){for(const n in t){const o=t[n],r=e[n];if("string"===typeof o){if(o!==r)return!1}else if(!1===Array.isArray(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}function s(e,t){return!0===Array.isArray(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}function u(e,t){return!0===Array.isArray(e)?s(e,t):!0===Array.isArray(t)?s(t,e):e===t}function c(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!1===u(e[n],t[n]))return!1;return!0}const d={to:[String,Object],replace:Boolean,exact:Boolean,activeClass:{type:String,default:"q-router-link--active"},exactActiveClass:{type:String,default:"q-router-link--exact-active"},href:String,target:String,disable:Boolean};function f({fallbackTag:e,useDisableForRouterLinkProps:t=!0}={}){const n=(0,o.FN)(),{props:s,proxy:u,emit:d}=n,f=(0,r.Rb)(n),p=(0,o.Fl)((()=>!0!==s.disable&&void 0!==s.href)),v=!0===t?(0,o.Fl)((()=>!0===f&&!0!==s.disable&&!0!==p.value&&void 0!==s.to&&null!==s.to&&""!==s.to)):(0,o.Fl)((()=>!0===f&&!0!==p.value&&void 0!==s.to&&null!==s.to&&""!==s.to)),h=(0,o.Fl)((()=>!0===v.value?x(s.to):null)),m=(0,o.Fl)((()=>null!==h.value)),g=(0,o.Fl)((()=>!0===p.value||!0===m.value)),b=(0,o.Fl)((()=>"a"===s.type||!0===g.value?"a":s.tag||e||"div")),y=(0,o.Fl)((()=>!0===p.value?{href:s.href,target:s.target}:!0===m.value?{href:h.value.href,target:s.target}:{})),_=(0,o.Fl)((()=>{if(!1===m.value)return-1;const{matched:e}=h.value,{length:t}=e,n=e[t-1];if(void 0===n)return-1;const o=u.$route.matched;if(0===o.length)return-1;const r=o.findIndex(i.bind(null,n));if(r>-1)return r;const a=l(e[t-2]);return t>1&&l(n)===a&&o[o.length-1].path!==a?o.findIndex(i.bind(null,e[t-2])):r})),w=(0,o.Fl)((()=>!0===m.value&&-1!==_.value&&a(u.$route.params,h.value.params))),S=(0,o.Fl)((()=>!0===w.value&&_.value===u.$route.matched.length-1&&c(u.$route.params,h.value.params))),k=(0,o.Fl)((()=>!0===m.value?!0===S.value?` ${s.exactActiveClass} ${s.activeClass}`:!0===s.exact?"":!0===w.value?` ${s.activeClass}`:"":""));function x(e){try{return u.$router.resolve(e)}catch(t){}return null}function E(e,{returnRouterError:t,to:n=s.to,replace:o=s.replace}={}){if(!0===s.disable)return e.preventDefault(),Promise.resolve(!1);if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey||void 0!==e.button&&0!==e.button||"_blank"===s.target)return Promise.resolve(!1);e.preventDefault();const r=u.$router[!0===o?"replace":"push"](n);return!0===t?r:r.then((()=>{})).catch((()=>{}))}function C(e){if(!0===m.value){const t=t=>E(e,t);d("click",e,t),!0!==e.defaultPrevented&&t()}else d("click",e)}return{hasRouterLink:m,hasHrefLink:p,hasLink:g,linkTag:b,resolvedLink:h,linkIsActive:w,linkIsExactActive:S,linkClass:k,linkAttrs:y,getLink:x,navigateToRouterLink:E,navigateOnClick:C}}},4088:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var o=n(499),r=n(9835),l=n(1384);function i(e,t){const n=(0,o.iH)(null);let i;function a(e,t){const n=(void 0!==t?"add":"remove")+"EventListener",o=void 0!==t?t:i;e!==window&&e[n]("scroll",o,l.listenOpts.passive),window[n]("scroll",o,l.listenOpts.passive),i=t}function s(){null!==n.value&&(a(n.value),n.value=null)}const u=(0,r.YP)((()=>e.noParentEvent),(()=>{null!==n.value&&(s(),t())}));return(0,r.Jd)(u),{localScrollTarget:n,unconfigureScrollTarget:s,changeScrollEvent:a}}},244:(e,t,n)=>{"use strict";n.d(t,{LU:()=>l,Ok:()=>r,ZP:()=>i});var o=n(9835);const r={xs:18,sm:24,md:32,lg:38,xl:46},l={size:String};function i(e,t=r){return(0,o.Fl)((()=>void 0!==e.size?{fontSize:e.size in t?`${t[e.size]}px`:e.size}:null))}},6916:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var o=n(9835),r=n(2046);function l(){let e;const t=(0,o.FN)();function n(){e=void 0}return(0,o.se)(n),(0,o.Jd)(n),{removeTick:n,registerTick(n){e=n,(0,o.Y3)((()=>{e===n&&(!1===(0,r.$D)(t)&&e(),e=void 0)}))}}}},2695:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var o=n(9835),r=n(2046);function l(){let e=null;const t=(0,o.FN)();function n(){null!==e&&(clearTimeout(e),e=null)}return(0,o.se)(n),(0,o.Jd)(n),{removeTimeout:n,registerTimeout(o,l){n(e),!1===(0,r.$D)(t)&&(e=setTimeout(o,l))}}}},431:(e,t,n)=>{"use strict";n.d(t,{D:()=>r,Z:()=>l});var o=n(9835);const r={transitionShow:{type:String,default:"fade"},transitionHide:{type:String,default:"fade"},transitionDuration:{type:[String,Number],default:300}};function l(e,t=(()=>{}),n=(()=>{})){return{transitionProps:(0,o.Fl)((()=>{const o=`q-transition--${e.transitionShow||t()}`,r=`q-transition--${e.transitionHide||n()}`;return{appear:!0,enterFromClass:`${o}-enter-from`,enterActiveClass:`${o}-enter-active`,enterToClass:`${o}-enter-to`,leaveFromClass:`${r}-leave-from`,leaveActiveClass:`${r}-leave-active`,leaveToClass:`${r}-leave-to`}})),transitionStyle:(0,o.Fl)((()=>`--q-transition-duration: ${e.transitionDuration}ms`))}}},2146:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(5987),r=n(2909),l=n(1705);function i(e){if(!1===e)return 0;if(!0===e||void 0===e)return 1;const t=parseInt(e,10);return isNaN(t)?0:t}const a=(0,o.f)({name:"close-popup",beforeMount(e,{value:t}){const n={depth:i(t),handler(t){0!==n.depth&&setTimeout((()=>{const o=(0,r.je)(e);void 0!==o&&(0,r.S7)(o,t,n.depth)}))},handlerKey(e){!0===(0,l.So)(e,13)&&n.handler(e)}};e.__qclosepopup=n,e.addEventListener("click",n.handler),e.addEventListener("keyup",n.handlerKey)},updated(e,{value:t,oldValue:n}){t!==n&&(e.__qclosepopup.depth=i(t))},beforeUnmount(e){const t=e.__qclosepopup;e.removeEventListener("click",t.handler),e.removeEventListener("keyup",t.handlerKey),delete e.__qclosepopup}})},1722:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});n(9665);var o=n(5987),r=n(223),l=n(1384),i=n(1705),a=n(8633);function s(e,t,n,o){!0===n.modifiers.stop&&(0,l.sT)(e);const i=n.modifiers.color;let a=n.modifiers.center;a=!0===a||!0===o;const s=document.createElement("span"),u=document.createElement("span"),c=(0,l.FK)(e),{left:d,top:f,width:p,height:v}=t.getBoundingClientRect(),h=Math.sqrt(p*p+v*v),m=h/2,g=(p-h)/2+"px",b=a?g:c.left-d-m+"px",y=(v-h)/2+"px",_=a?y:c.top-f-m+"px";u.className="q-ripple__inner",(0,r.iv)(u,{height:`${h}px`,width:`${h}px`,transform:`translate3d(${b},${_},0) scale3d(.2,.2,1)`,opacity:0}),s.className="q-ripple"+(i?" text-"+i:""),s.setAttribute("dir","ltr"),s.appendChild(u),t.appendChild(s);const w=()=>{s.remove(),clearTimeout(S)};n.abort.push(w);let S=setTimeout((()=>{u.classList.add("q-ripple__inner--enter"),u.style.transform=`translate3d(${g},${y},0) scale3d(1,1,1)`,u.style.opacity=.2,S=setTimeout((()=>{u.classList.remove("q-ripple__inner--enter"),u.classList.add("q-ripple__inner--leave"),u.style.opacity=0,S=setTimeout((()=>{s.remove(),n.abort.splice(n.abort.indexOf(w),1)}),275)}),250)}),50)}function u(e,{modifiers:t,value:n,arg:o}){const r=Object.assign({},e.cfg.ripple,t,n);e.modifiers={early:!0===r.early,stop:!0===r.stop,center:!0===r.center,color:r.color||o,keyCodes:[].concat(r.keyCodes||13)}}const c=(0,o.f)({name:"ripple",beforeMount(e,t){const n=t.instance.$.appContext.config.globalProperties.$q.config||{};if(!1===n.ripple)return;const o={cfg:n,enabled:!1!==t.value,modifiers:{},abort:[],start(t){!0===o.enabled&&!0!==t.qSkipRipple&&t.type===(!0===o.modifiers.early?"pointerdown":"click")&&s(t,e,o,!0===t.qKeyEvent)},keystart:(0,a.Z)((t=>{!0===o.enabled&&!0!==t.qSkipRipple&&!0===(0,i.So)(t,o.modifiers.keyCodes)&&t.type==="key"+(!0===o.modifiers.early?"down":"up")&&s(t,e,o,!0)}),300)};u(o,t),e.__qripple=o,(0,l.M0)(o,"main",[[e,"pointerdown","start","passive"],[e,"click","start","passive"],[e,"keydown","keystart","passive"],[e,"keyup","keystart","passive"]])},updated(e,t){if(t.oldValue!==t.value){const n=e.__qripple;void 0!==n&&(n.enabled=!1!==t.value,!0===n.enabled&&Object(t.value)===t.value&&u(n,t))}},beforeUnmount(e){const t=e.__qripple;void 0!==t&&(t.abort.forEach((e=>{e()})),(0,l.ul)(t,"main"),delete e._qripple)}})},2873:(e,t,n)=>{"use strict";n.d(t,{Z:()=>c});var o=n(7506),r=n(5987),l=n(9367),i=n(1384),a=n(2589);function s(e,t,n){const o=(0,i.FK)(e);let r,l=o.left-t.event.x,a=o.top-t.event.y,s=Math.abs(l),u=Math.abs(a);const c=t.direction;!0===c.horizontal&&!0!==c.vertical?r=l<0?"left":"right":!0!==c.horizontal&&!0===c.vertical?r=a<0?"up":"down":!0===c.up&&a<0?(r="up",s>u&&(!0===c.left&&l<0?r="left":!0===c.right&&l>0&&(r="right"))):!0===c.down&&a>0?(r="down",s>u&&(!0===c.left&&l<0?r="left":!0===c.right&&l>0&&(r="right"))):!0===c.left&&l<0?(r="left",s<u&&(!0===c.up&&a<0?r="up":!0===c.down&&a>0&&(r="down"))):!0===c.right&&l>0&&(r="right",s<u&&(!0===c.up&&a<0?r="up":!0===c.down&&a>0&&(r="down")));let d=!1;if(void 0===r&&!1===n){if(!0===t.event.isFirst||void 0===t.event.lastDir)return{};r=t.event.lastDir,d=!0,"left"===r||"right"===r?(o.left-=l,s=0,l=0):(o.top-=a,u=0,a=0)}return{synthetic:d,payload:{evt:e,touch:!0!==t.event.mouse,mouse:!0===t.event.mouse,position:o,direction:r,isFirst:t.event.isFirst,isFinal:!0===n,duration:Date.now()-t.event.time,distance:{x:s,y:u},offset:{x:l,y:a},delta:{x:o.left-t.event.lastX,y:o.top-t.event.lastY}}}}let u=0;const c=(0,r.f)({name:"touch-pan",beforeMount(e,{value:t,modifiers:n}){if(!0!==n.mouse&&!0!==o.client.has.touch)return;function r(e,t){!0===n.mouse&&!0===t?(0,i.NS)(e):(!0===n.stop&&(0,i.sT)(e),!0===n.prevent&&(0,i.X$)(e))}const c={uid:"qvtp_"+u++,handler:t,modifiers:n,direction:(0,l.R)(n),noop:i.ZT,mouseStart(e){(0,l.n)(e,c)&&(0,i.du)(e)&&((0,i.M0)(c,"temp",[[document,"mousemove","move","notPassiveCapture"],[document,"mouseup","end","passiveCapture"]]),c.start(e,!0))},touchStart(e){if((0,l.n)(e,c)){const t=e.target;(0,i.M0)(c,"temp",[[t,"touchmove","move","notPassiveCapture"],[t,"touchcancel","end","passiveCapture"],[t,"touchend","end","passiveCapture"]]),c.start(e)}},start(t,r){if(!0===o.client.is.firefox&&(0,i.Jf)(e,!0),c.lastEvt=t,!0===r||!0===n.stop){if(!0!==c.direction.all&&(!0!==r||!0!==c.modifiers.mouseAllDir&&!0!==c.modifiers.mousealldir)){const e=t.type.indexOf("mouse")>-1?new MouseEvent(t.type,t):new TouchEvent(t.type,t);!0===t.defaultPrevented&&(0,i.X$)(e),!0===t.cancelBubble&&(0,i.sT)(e),Object.assign(e,{qKeyEvent:t.qKeyEvent,qClickOutside:t.qClickOutside,qAnchorHandled:t.qAnchorHandled,qClonedBy:void 0===t.qClonedBy?[c.uid]:t.qClonedBy.concat(c.uid)}),c.initialEvent={target:t.target,event:e}}(0,i.sT)(t)}const{left:l,top:a}=(0,i.FK)(t);c.event={x:l,y:a,time:Date.now(),mouse:!0===r,detected:!1,isFirst:!0,isFinal:!1,lastX:l,lastY:a}},move(e){if(void 0===c.event)return;const t=(0,i.FK)(e),o=t.left-c.event.x,l=t.top-c.event.y;if(0===o&&0===l)return;c.lastEvt=e;const u=!0===c.event.mouse,d=()=>{let t;r(e,u),!0!==n.preserveCursor&&!0!==n.preservecursor&&(t=document.documentElement.style.cursor||"",document.documentElement.style.cursor="grabbing"),!0===u&&document.body.classList.add("no-pointer-events--children"),document.body.classList.add("non-selectable"),(0,a.M)(),c.styleCleanup=e=>{if(c.styleCleanup=void 0,void 0!==t&&(document.documentElement.style.cursor=t),document.body.classList.remove("non-selectable"),!0===u){const t=()=>{document.body.classList.remove("no-pointer-events--children")};void 0!==e?setTimeout((()=>{t(),e()}),50):t()}else void 0!==e&&e()}};if(!0===c.event.detected){!0!==c.event.isFirst&&r(e,c.event.mouse);const{payload:t,synthetic:n}=s(e,c,!1);return void(void 0!==t&&(!1===c.handler(t)?c.end(e):(void 0===c.styleCleanup&&!0===c.event.isFirst&&d(),c.event.lastX=t.position.left,c.event.lastY=t.position.top,c.event.lastDir=!0===n?void 0:t.direction,c.event.isFirst=!1)))}if(!0===c.direction.all||!0===u&&(!0===c.modifiers.mouseAllDir||!0===c.modifiers.mousealldir))return d(),c.event.detected=!0,void c.move(e);const f=Math.abs(o),p=Math.abs(l);f!==p&&(!0===c.direction.horizontal&&f>p||!0===c.direction.vertical&&f<p||!0===c.direction.up&&f<p&&l<0||!0===c.direction.down&&f<p&&l>0||!0===c.direction.left&&f>p&&o<0||!0===c.direction.right&&f>p&&o>0?(c.event.detected=!0,c.move(e)):c.end(e,!0))},end(t,n){if(void 0!==c.event){if((0,i.ul)(c,"temp"),!0===o.client.is.firefox&&(0,i.Jf)(e,!1),!0===n)void 0!==c.styleCleanup&&c.styleCleanup(),!0!==c.event.detected&&void 0!==c.initialEvent&&c.initialEvent.target.dispatchEvent(c.initialEvent.event);else if(!0===c.event.detected){!0===c.event.isFirst&&c.handler(s(void 0===t?c.lastEvt:t,c).payload);const{payload:e}=s(void 0===t?c.lastEvt:t,c,!0),n=()=>{c.handler(e)};void 0!==c.styleCleanup?c.styleCleanup(n):n()}c.event=void 0,c.initialEvent=void 0,c.lastEvt=void 0}}};if(e.__qtouchpan=c,!0===n.mouse){const t=!0===n.mouseCapture||!0===n.mousecapture?"Capture":"";(0,i.M0)(c,"main",[[e,"mousedown","mouseStart",`passive${t}`]])}!0===o.client.has.touch&&(0,i.M0)(c,"main",[[e,"touchstart","touchStart","passive"+(!0===n.capture?"Capture":"")],[e,"touchmove","noop","notPassiveCapture"]])},updated(e,t){const n=e.__qtouchpan;void 0!==n&&(t.oldValue!==t.value&&("function"!==typeof value&&n.end(),n.handler=t.value),n.direction=(0,l.R)(t.modifiers))},beforeUnmount(e){const t=e.__qtouchpan;void 0!==t&&(void 0!==t.event&&t.end(),(0,i.ul)(t,"main"),(0,i.ul)(t,"temp"),!0===o.client.is.firefox&&(0,i.Jf)(e,!1),void 0!==t.styleCleanup&&t.styleCleanup(),delete e.__qtouchpan)}})},5310:(e,t,n)=>{"use strict";n.d(t,{Z:()=>u});n(9665);var o=n(7506),r=n(1384);const l=()=>!0;function i(e){return"string"===typeof e&&""!==e&&"/"!==e&&"#/"!==e}function a(e){return!0===e.startsWith("#")&&(e=e.substring(1)),!1===e.startsWith("/")&&(e="/"+e),!0===e.endsWith("/")&&(e=e.substring(0,e.length-1)),"#"+e}function s(e){if(!1===e.backButtonExit)return()=>!1;if("*"===e.backButtonExit)return l;const t=["#/"];return!0===Array.isArray(e.backButtonExit)&&t.push(...e.backButtonExit.filter(i).map(a)),()=>t.includes(window.location.hash)}const u={__history:[],add:r.ZT,remove:r.ZT,install({$q:e}){if(!0===this.__installed)return;const{cordova:t,capacitor:n}=o.client.is;if(!0!==t&&!0!==n)return;const r=e.config[!0===t?"cordova":"capacitor"];if(void 0!==r&&!1===r.backButton)return;if(!0===n&&(void 0===window.Capacitor||void 0===window.Capacitor.Plugins.App))return;this.add=e=>{void 0===e.condition&&(e.condition=l),this.__history.push(e)},this.remove=e=>{const t=this.__history.indexOf(e);t>=0&&this.__history.splice(t,1)};const i=s(Object.assign({backButtonExit:!0},r)),a=()=>{if(this.__history.length){const e=this.__history[this.__history.length-1];!0===e.condition()&&(this.__history.pop(),e.handler())}else!0===i()?navigator.app.exitApp():window.history.back()};!0===t?document.addEventListener("deviceready",(()=>{document.addEventListener("backbutton",a,!1)})):window.Capacitor.Plugins.App.addListener("backButton",a)}}},2289:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(4124),r=n(3251);const l={name:"material-icons",type:{positive:"check_circle",negative:"warning",info:"info",warning:"priority_high"},arrow:{up:"arrow_upward",right:"arrow_forward",down:"arrow_downward",left:"arrow_back",dropdown:"arrow_drop_down"},chevron:{left:"chevron_left",right:"chevron_right"},colorPicker:{spectrum:"gradient",tune:"tune",palette:"style"},pullToRefresh:{icon:"refresh"},carousel:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down",navigationIcon:"lens"},chip:{remove:"cancel",selected:"check"},datetime:{arrowLeft:"chevron_left",arrowRight:"chevron_right",now:"access_time",today:"today"},editor:{bold:"format_bold",italic:"format_italic",strikethrough:"strikethrough_s",underline:"format_underlined",unorderedList:"format_list_bulleted",orderedList:"format_list_numbered",subscript:"vertical_align_bottom",superscript:"vertical_align_top",hyperlink:"link",toggleFullscreen:"fullscreen",quote:"format_quote",left:"format_align_left",center:"format_align_center",right:"format_align_right",justify:"format_align_justify",print:"print",outdent:"format_indent_decrease",indent:"format_indent_increase",removeFormat:"format_clear",formatting:"text_format",fontSize:"format_size",align:"format_align_left",hr:"remove",undo:"undo",redo:"redo",heading:"format_size",code:"code",size:"format_size",font:"font_download",viewSource:"code"},expansionItem:{icon:"keyboard_arrow_down",denseIcon:"arrow_drop_down"},fab:{icon:"add",activeIcon:"close"},field:{clear:"cancel",error:"error"},pagination:{first:"first_page",prev:"keyboard_arrow_left",next:"keyboard_arrow_right",last:"last_page"},rating:{icon:"grade"},stepper:{done:"check",active:"edit",error:"warning"},tabs:{left:"chevron_left",right:"chevron_right",up:"keyboard_arrow_up",down:"keyboard_arrow_down"},table:{arrowUp:"arrow_upward",warning:"warning",firstPage:"first_page",prevPage:"chevron_left",nextPage:"chevron_right",lastPage:"last_page"},tree:{icon:"play_arrow"},uploader:{done:"done",clear:"clear",add:"add_box",upload:"cloud_upload",removeQueue:"clear_all",removeUploaded:"done_all"}},i=(0,o.Z)({iconMapFn:null,__icons:{}},{set(e,t){const n={...e,rtl:!0===e.rtl};n.set=i.set,Object.assign(i.__icons,n)},install({$q:e,iconSet:t,ssrContext:n}){void 0!==e.config.iconMapFn&&(this.iconMapFn=e.config.iconMapFn),e.iconSet=this.__icons,(0,r.g)(e,"iconMapFn",(()=>this.iconMapFn),(e=>{this.iconMapFn=e})),!0===this.__installed?void 0!==t&&this.set(t):this.set(t||l)}}),a=i},1583:(e,t,n)=>{"use strict";n.d(t,{$:()=>T,Z:()=>L});var o=n(1957),r=n(7506),l=(n(9665),n(4124)),i=n(1384),a=n(899);const s=["sm","md","lg","xl"],{passive:u}=i.listenOpts,c=(0,l.Z)({width:0,height:0,name:"xs",sizes:{sm:600,md:1024,lg:1440,xl:1920},lt:{sm:!0,md:!0,lg:!0,xl:!0},gt:{xs:!1,sm:!1,md:!1,lg:!1},xs:!0,sm:!1,md:!1,lg:!1,xl:!1},{setSizes:i.ZT,setDebounce:i.ZT,install({$q:e,onSSRHydrated:t}){if(e.screen=this,!0===this.__installed)return void(void 0!==e.config.screen&&(!1===e.config.screen.bodyClasses?document.body.classList.remove(`screen--${this.name}`):this.__update(!0)));const{visualViewport:n}=window,o=n||window,l=document.scrollingElement||document.documentElement,i=void 0===n||!0===r.client.is.mobile?()=>[Math.max(window.innerWidth,l.clientWidth),Math.max(window.innerHeight,l.clientHeight)]:()=>[n.width*n.scale+window.innerWidth-l.clientWidth,n.height*n.scale+window.innerHeight-l.clientHeight],c=void 0!==e.config.screen&&!0===e.config.screen.bodyClasses;this.__update=e=>{const[t,n]=i();if(n!==this.height&&(this.height=n),t!==this.width)this.width=t;else if(!0!==e)return;let o=this.sizes;this.gt.xs=t>=o.sm,this.gt.sm=t>=o.md,this.gt.md=t>=o.lg,this.gt.lg=t>=o.xl,this.lt.sm=t<o.sm,this.lt.md=t<o.md,this.lt.lg=t<o.lg,this.lt.xl=t<o.xl,this.xs=this.lt.sm,this.sm=!0===this.gt.xs&&!0===this.lt.md,this.md=!0===this.gt.sm&&!0===this.lt.lg,this.lg=!0===this.gt.md&&!0===this.lt.xl,this.xl=this.gt.lg,o=(!0===this.xs?"xs":!0===this.sm&&"sm")||!0===this.md&&"md"||!0===this.lg&&"lg"||"xl",o!==this.name&&(!0===c&&(document.body.classList.remove(`screen--${this.name}`),document.body.classList.add(`screen--${o}`)),this.name=o)};let d,f={},p=16;this.setSizes=e=>{s.forEach((t=>{void 0!==e[t]&&(f[t]=e[t])}))},this.setDebounce=e=>{p=e};const v=()=>{const e=getComputedStyle(document.body);e.getPropertyValue("--q-size-sm")&&s.forEach((t=>{this.sizes[t]=parseInt(e.getPropertyValue(`--q-size-${t}`),10)})),this.setSizes=e=>{s.forEach((t=>{e[t]&&(this.sizes[t]=e[t])})),this.__update(!0)},this.setDebounce=e=>{void 0!==d&&o.removeEventListener("resize",d,u),d=e>0?(0,a.Z)(this.__update,e):this.__update,o.addEventListener("resize",d,u)},this.setDebounce(p),0!==Object.keys(f).length?(this.setSizes(f),f=void 0):this.__update(),!0===c&&"xs"===this.name&&document.body.classList.add("screen--xs")};!0===r.uX.value?t.push(v):v()}}),d=(0,l.Z)({isActive:!1,mode:!1},{__media:void 0,set(e){d.mode=e,"auto"===e?(void 0===d.__media&&(d.__media=window.matchMedia("(prefers-color-scheme: dark)"),d.__updateMedia=()=>{d.set("auto")},d.__media.addListener(d.__updateMedia)),e=d.__media.matches):void 0!==d.__media&&(d.__media.removeListener(d.__updateMedia),d.__media=void 0),d.isActive=!0===e,document.body.classList.remove("body--"+(!0===e?"light":"dark")),document.body.classList.add("body--"+(!0===e?"dark":"light"))},toggle(){d.set(!1===d.isActive)},install({$q:e,onSSRHydrated:t,ssrContext:n}){const{dark:o}=e.config;if(e.dark=this,!0===this.__installed&&void 0===o)return;this.isActive=!0===o;const l=void 0!==o&&o;if(!0===r.uX.value){const e=e=>{this.__fromSSR=e},n=this.set;this.set=e,e(l),t.push((()=>{this.set=n,this.set(this.__fromSSR)}))}else this.set(l)}}),f=d;var p=n(5310),v=n(3558),h=n(7674),m=n(1705);function g(e){return!0===e.ios?"ios":!0===e.android?"android":void 0}function b({is:e,has:t,within:n},o){const r=[!0===e.desktop?"desktop":"mobile",(!1===t.touch?"no-":"")+"touch"];if(!0===e.mobile){const t=g(e);void 0!==t&&r.push("platform-"+t)}if(!0===e.nativeMobile){const t=e.nativeMobileWrapper;r.push(t),r.push("native-mobile"),!0!==e.ios||void 0!==o[t]&&!1===o[t].iosStatusBarPadding||r.push("q-ios-padding")}else!0===e.electron?r.push("electron"):!0===e.bex&&r.push("bex");return!0===n.iframe&&r.push("within-iframe"),r}function y(){const{is:e}=r.client,t=document.body.className,n=new Set(t.replace(/ {2}/g," ").split(" "));if(void 0!==r.aG)n.delete("desktop"),n.add("platform-ios"),n.add("mobile");else if(!0!==e.nativeMobile&&!0!==e.electron&&!0!==e.bex)if(!0===e.desktop)n.delete("mobile"),n.delete("platform-ios"),n.delete("platform-android"),n.add("desktop");else if(!0===e.mobile){n.delete("desktop"),n.add("mobile");const t=g(e);void 0!==t?(n.add(`platform-${t}`),n.delete("platform-"+("ios"===t?"android":"ios"))):(n.delete("platform-ios"),n.delete("platform-android"))}!0===r.client.has.touch&&(n.delete("no-touch"),n.add("touch")),!0===r.client.within.iframe&&n.add("within-iframe");const o=Array.from(n).join(" ");t!==o&&(document.body.className=o)}function _(e){for(const t in e)(0,h.Z)(t,e[t])}const w={install(e){if(!0!==this.__installed){if(!0===r.uX.value)y();else{const{$q:t}=e;void 0!==t.config.brand&&_(t.config.brand);const n=b(r.client,t.config);document.body.classList.add.apply(document.body.classList,n)}!0===r.client.is.ios&&document.body.addEventListener("touchstart",i.ZT),window.addEventListener("keydown",m.ZK,!0)}}};var S=n(2289),k=n(5439),x=n(7495),E=n(4680);const C=[r.ZP,w,f,c,p.Z,v.Z,S.Z];function T(e,t){const n=(0,o.ri)(e);n.config.globalProperties=t.config.globalProperties;const{reload:r,...l}=t._context;return Object.assign(n._context,l),n}function O(e,t){t.forEach((t=>{t.install(e),t.__installed=!0}))}function F(e,t,n){e.config.globalProperties.$q=n.$q,e.provide(k.Ng,n.$q),O(n,C),void 0!==t.components&&Object.values(t.components).forEach((t=>{!0===(0,E.Kn)(t)&&void 0!==t.name&&e.component(t.name,t)})),void 0!==t.directives&&Object.values(t.directives).forEach((t=>{!0===(0,E.Kn)(t)&&void 0!==t.name&&e.directive(t.name,t)})),void 0!==t.plugins&&O(n,Object.values(t.plugins).filter((e=>"function"===typeof e.install&&!1===C.includes(e)))),!0===r.uX.value&&(n.$q.onSSRHydrated=()=>{n.onSSRHydrated.forEach((e=>{e()})),n.$q.onSSRHydrated=()=>{}})}const L=function(e,t={}){const n={version:"2.12.5"};!1===x.Uf?(void 0!==t.config&&Object.assign(x.w6,t.config),n.config={...x.w6},(0,x.tP)()):n.config=t.config||{},F(e,t,{parentApp:e,$q:n,lang:t.lang,iconSet:t.iconSet,onSSRHydrated:[]})}},3558:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var o=n(4124);const r={isoName:"en-US",nativeName:"English (US)",label:{clear:"Clear",ok:"OK",cancel:"Cancel",close:"Close",set:"Set",select:"Select",reset:"Reset",remove:"Remove",update:"Update",create:"Create",search:"Search",filter:"Filter",refresh:"Refresh",expand:e=>e?`Expand "${e}"`:"Expand",collapse:e=>e?`Collapse "${e}"`:"Collapse"},date:{days:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),daysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),firstDayOfWeek:0,format24h:!1,pluralDay:"days"},table:{noData:"No data available",noResults:"No matching records found",loading:"Loading...",selectedRecords:e=>1===e?"1 record selected.":(0===e?"No":e)+" records selected.",recordsPerPage:"Records per page:",allRows:"All",pagination:(e,t,n)=>e+"-"+t+" of "+n,columns:"Columns"},editor:{url:"URL",bold:"Bold",italic:"Italic",strikethrough:"Strikethrough",underline:"Underline",unorderedList:"Unordered List",orderedList:"Ordered List",subscript:"Subscript",superscript:"Superscript",hyperlink:"Hyperlink",toggleFullscreen:"Toggle Fullscreen",quote:"Quote",left:"Left align",center:"Center align",right:"Right align",justify:"Justify align",print:"Print",outdent:"Decrease indentation",indent:"Increase indentation",removeFormat:"Remove formatting",formatting:"Formatting",fontSize:"Font Size",align:"Align",hr:"Insert Horizontal Rule",undo:"Undo",redo:"Redo",heading1:"Heading 1",heading2:"Heading 2",heading3:"Heading 3",heading4:"Heading 4",heading5:"Heading 5",heading6:"Heading 6",paragraph:"Paragraph",code:"Code",size1:"Very small",size2:"A bit small",size3:"Normal",size4:"Medium-large",size5:"Big",size6:"Very big",size7:"Maximum",defaultFont:"Default Font",viewSource:"View Source"},tree:{noNodes:"No nodes available",noResults:"No matching nodes found"}};function l(){const e=!0===Array.isArray(navigator.languages)&&0!==navigator.languages.length?navigator.languages[0]:navigator.language;if("string"===typeof e)return e.split(/[-_]/).map(((e,t)=>0===t?e.toLowerCase():t>1||e.length<4?e.toUpperCase():e[0].toUpperCase()+e.slice(1).toLowerCase())).join("-")}const i=(0,o.Z)({__langPack:{}},{getLocale:l,set(e=r,t){const n={...e,rtl:!0===e.rtl,getLocale:l};if(n.set=i.set,void 0===i.__langConfig||!0!==i.__langConfig.noHtmlAttrs){const e=document.documentElement;e.setAttribute("dir",!0===n.rtl?"rtl":"ltr"),e.setAttribute("lang",n.isoName)}Object.assign(i.__langPack,n),i.props=n,i.isoName=n.isoName,i.nativeName=n.nativeName},install({$q:e,lang:t,ssrContext:n}){e.lang=i.__langPack,i.__langConfig=e.config.lang,!0===this.__installed?void 0!==t&&this.set(t):this.set(t||r)}}),a=i},6950:(e,t,n)=>{"use strict";n.d(t,{Z:()=>w});n(9665);var o=n(9835),r=n(1957),l=n(3940),i=n(1583),a=n(4124),s=n(6669),u=n(5984),c=n(4680);let d,f,p=0,v=null,h={},m={};const g={group:"__default_quasar_group__",delay:0,message:!1,html:!1,spinnerSize:80,spinnerColor:"",messageColor:"",backgroundColor:"",boxClass:"",spinner:l.Z,customClass:""},b={...g};function y(e){if(e&&void 0!==e.group&&void 0!==m[e.group])return Object.assign(m[e.group],e);const t=!0===(0,c.Kn)(e)&&!0===e.ignoreDefaults?{...g,...e}:{...b,...e};return m[t.group]=t,t}const _=(0,a.Z)({isActive:!1},{show(e){h=y(e);const{group:t}=h;return _.isActive=!0,void 0!==d?(h.uid=p,f.$forceUpdate()):(h.uid=++p,null!==v&&clearTimeout(v),v=setTimeout((()=>{v=null;const e=(0,s.q_)("q-loading");d=(0,i.$)({name:"QLoading",setup(){function t(){!0!==_.isActive&&void 0!==d&&((0,u.Z)(!1),d.unmount(e),(0,s.pB)(e),d=void 0,f=void 0)}function n(){if(!0!==_.isActive)return null;const e=[(0,o.h)(h.spinner,{class:"q-loading__spinner",color:h.spinnerColor,size:h.spinnerSize})];return h.message&&e.push((0,o.h)("div",{class:"q-loading__message"+(h.messageColor?` text-${h.messageColor}`:""),[!0===h.html?"innerHTML":"textContent"]:h.message})),(0,o.h)("div",{class:"q-loading fullscreen flex flex-center z-max "+h.customClass.trim(),key:h.uid},[(0,o.h)("div",{class:"q-loading__backdrop"+(h.backgroundColor?` bg-${h.backgroundColor}`:"")}),(0,o.h)("div",{class:"q-loading__box column items-center "+h.boxClass},e)])}return(0,o.bv)((()=>{(0,u.Z)(!0)})),()=>(0,o.h)(r.uT,{name:"q-transition--fade",appear:!0,onAfterLeave:t},n)}},_.__parentApp),f=d.mount(e)}),h.delay)),e=>{void 0!==e&&Object(e)===e?_.show({...e,group:t}):_.hide(t)}},hide(e){if(!0===_.isActive){if(void 0===e)m={};else{if(void 0===m[e])return;{delete m[e];const t=Object.keys(m);if(0!==t.length){const e=t[t.length-1];return void _.show({group:e})}}}null!==v&&(clearTimeout(v),v=null),_.isActive=!1}},setDefaults(e){!0===(0,c.Kn)(e)&&Object.assign(b,e)},install({$q:e,parentApp:t}){e.loading=this,_.__parentApp=t,void 0!==e.config.loading&&this.setDefaults(e.config.loading)}}),w=_},4328:(e,t,n)=>{"use strict";n.d(t,{Z:()=>A});n(9665);var o=n(499),r=n(9835),l=n(1957),i=n(2857),a=n(244),s=n(5987),u=n(2026);const c=(0,s.L)({name:"QAvatar",props:{...a.LU,fontSize:String,color:String,textColor:String,icon:String,square:Boolean,rounded:Boolean},setup(e,{slots:t}){const n=(0,a.ZP)(e),o=(0,r.Fl)((()=>"q-avatar"+(e.color?` bg-${e.color}`:"")+(e.textColor?` text-${e.textColor} q-chip--colored`:"")+(!0===e.square?" q-avatar--square":!0===e.rounded?" rounded-borders":""))),l=(0,r.Fl)((()=>e.fontSize?{fontSize:e.fontSize}:null));return()=>{const a=void 0!==e.icon?[(0,r.h)(i.Z,{name:e.icon})]:void 0;return(0,r.h)("div",{class:o.value,style:n.value},[(0,r.h)("div",{class:"q-avatar__content row flex-center overflow-hidden",style:l.value},(0,u.pf)(t.default,a))])}}});var d=n(9379),f=n(3940),p=(n(1384),n(6669)),v=n(1583),h=n(4680);let m=0;const g={},b={},y={},_={},w=/^\s*$/,S=[],k=["top-left","top-right","bottom-left","bottom-right","top","bottom","left","right","center"],x=["top-left","top-right","bottom-left","bottom-right"],E={positive:{icon:e=>e.iconSet.type.positive,color:"positive"},negative:{icon:e=>e.iconSet.type.negative,color:"negative"},warning:{icon:e=>e.iconSet.type.warning,color:"warning",textColor:"dark"},info:{icon:e=>e.iconSet.type.info,color:"info"},ongoing:{group:!1,timeout:0,spinner:!0,color:"grey-8"}};function C(e,t,n){if(!e)return F("parameter required");let r;const l={textColor:"white"};if(!0!==e.ignoreDefaults&&Object.assign(l,g),!1===(0,h.Kn)(e)&&(l.type&&Object.assign(l,E[l.type]),e={message:e}),Object.assign(l,E[e.type||l.type],e),"function"===typeof l.icon&&(l.icon=l.icon(t)),l.spinner?(!0===l.spinner&&(l.spinner=f.Z),l.spinner=(0,o.Xl)(l.spinner)):l.spinner=!1,l.meta={hasMedia:Boolean(!1!==l.spinner||l.icon||l.avatar),hasText:O(l.message)||O(l.caption)},l.position){if(!1===k.includes(l.position))return F("wrong position",e)}else l.position="bottom";if(void 0===l.timeout)l.timeout=5e3;else{const t=parseInt(l.timeout,10);if(isNaN(t)||t<0)return F("wrong timeout",e);l.timeout=t}0===l.timeout?l.progress=!1:!0===l.progress&&(l.meta.progressClass="q-notification__progress"+(l.progressClass?` ${l.progressClass}`:""),l.meta.progressStyle={animationDuration:`${l.timeout+1e3}ms`});const i=(!0===Array.isArray(e.actions)?e.actions:[]).concat(!0!==e.ignoreDefaults&&!0===Array.isArray(g.actions)?g.actions:[]).concat(void 0!==E[e.type]&&!0===Array.isArray(E[e.type].actions)?E[e.type].actions:[]),{closeBtn:a}=l;if(a&&i.push({label:"string"===typeof a?a:t.lang.label.close}),l.actions=i.map((({handler:e,noDismiss:t,...n})=>({flat:!0,...n,onClick:"function"===typeof e?()=>{e(),!0!==t&&s()}:()=>{s()}}))),void 0===l.multiLine&&(l.multiLine=l.actions.length>1),Object.assign(l.meta,{class:"q-notification row items-stretch q-notification--"+(!0===l.multiLine?"multi-line":"standard")+(void 0!==l.color?` bg-${l.color}`:"")+(void 0!==l.textColor?` text-${l.textColor}`:"")+(void 0!==l.classes?` ${l.classes}`:""),wrapperClass:"q-notification__wrapper col relative-position border-radius-inherit "+(!0===l.multiLine?"column no-wrap justify-center":"row items-center"),contentClass:"q-notification__content row items-center"+(!0===l.multiLine?"":" col"),leftClass:!0===l.meta.hasText?"additional":"single",attrs:{role:"alert",...l.attrs}}),!1===l.group?(l.group=void 0,l.meta.group=void 0):(void 0!==l.group&&!0!==l.group||(l.group=[l.message,l.caption,l.multiline].concat(l.actions.map((e=>`${e.label}*${e.icon}`))).join("|")),l.meta.group=l.group+"|"+l.position),0===l.actions.length?l.actions=void 0:l.meta.actionsClass="q-notification__actions row items-center "+(!0===l.multiLine?"justify-end":"col-auto")+(!0===l.meta.hasMedia?" q-notification__actions--with-media":""),void 0!==n){n.notif.meta.timer&&(clearTimeout(n.notif.meta.timer),n.notif.meta.timer=void 0),l.meta.uid=n.notif.meta.uid;const e=y[l.position].value.indexOf(n.notif);y[l.position].value[e]=l}else{const t=b[l.meta.group];if(void 0===t){if(l.meta.uid=m++,l.meta.badge=1,-1!==["left","right","center"].indexOf(l.position))y[l.position].value.splice(Math.floor(y[l.position].value.length/2),0,l);else{const e=l.position.indexOf("top")>-1?"unshift":"push";y[l.position].value[e](l)}void 0!==l.group&&(b[l.meta.group]=l)}else{if(t.meta.timer&&(clearTimeout(t.meta.timer),t.meta.timer=void 0),void 0!==l.badgePosition){if(!1===x.includes(l.badgePosition))return F("wrong badgePosition",e)}else l.badgePosition="top-"+(l.position.indexOf("left")>-1?"right":"left");l.meta.uid=t.meta.uid,l.meta.badge=t.meta.badge+1,l.meta.badgeClass=`q-notification__badge q-notification__badge--${l.badgePosition}`+(void 0!==l.badgeColor?` bg-${l.badgeColor}`:"")+(void 0!==l.badgeTextColor?` text-${l.badgeTextColor}`:"")+(l.badgeClass?` ${l.badgeClass}`:"");const n=y[l.position].value.indexOf(t);y[l.position].value[n]=b[l.meta.group]=l}}const s=()=>{T(l),r=void 0};return l.timeout>0&&(l.meta.timer=setTimeout((()=>{l.meta.timer=void 0,s()}),l.timeout+1e3)),void 0!==l.group?t=>{void 0!==t?F("trying to update a grouped one which is forbidden",e):s()}:(r={dismiss:s,config:e,notif:l},void 0===n?e=>{if(void 0!==r)if(void 0===e)r.dismiss();else{const n=Object.assign({},r.config,e,{group:!1,position:l.position});C(n,t,r)}}:void Object.assign(n,r))}function T(e){e.meta.timer&&(clearTimeout(e.meta.timer),e.meta.timer=void 0);const t=y[e.position].value.indexOf(e);if(-1!==t){void 0!==e.group&&delete b[e.meta.group];const n=S[""+e.meta.uid];if(n){const{width:e,height:t}=getComputedStyle(n);n.style.left=`${n.offsetLeft}px`,n.style.width=e,n.style.height=t}y[e.position].value.splice(t,1),"function"===typeof e.onDismiss&&e.onDismiss()}}function O(e){return void 0!==e&&null!==e&&!0!==w.test(e)}function F(e,t){return console.error(`Notify: ${e}`,t),!1}function L(){return(0,s.L)({name:"QNotifications",devtools:{hide:!0},setup(){return()=>(0,r.h)("div",{class:"q-notifications"},k.map((e=>(0,r.h)(l.W3,{key:e,class:_[e],tag:"div",name:`q-notification--${e}`},(()=>y[e].value.map((e=>{const t=e.meta,n=[];if(!0===t.hasMedia&&(!1!==e.spinner?n.push((0,r.h)(e.spinner,{class:"q-notification__spinner q-notification__spinner--"+t.leftClass,color:e.spinnerColor,size:e.spinnerSize})):e.icon?n.push((0,r.h)(i.Z,{class:"q-notification__icon q-notification__icon--"+t.leftClass,name:e.icon,color:e.iconColor,size:e.iconSize,role:"img"})):e.avatar&&n.push((0,r.h)(c,{class:"q-notification__avatar q-notification__avatar--"+t.leftClass},(()=>(0,r.h)("img",{src:e.avatar,"aria-hidden":"true"}))))),!0===t.hasText){let t;const o={class:"q-notification__message col"};if(!0===e.html)o.innerHTML=e.caption?`<div>${e.message}</div><div class="q-notification__caption">${e.caption}</div>`:e.message;else{const n=[e.message];t=e.caption?[(0,r.h)("div",n),(0,r.h)("div",{class:"q-notification__caption"},[e.caption])]:n}n.push((0,r.h)("div",o,t))}const o=[(0,r.h)("div",{class:t.contentClass},n)];return!0===e.progress&&o.push((0,r.h)("div",{key:`${t.uid}|p|${t.badge}`,class:t.progressClass,style:t.progressStyle})),void 0!==e.actions&&o.push((0,r.h)("div",{class:t.actionsClass},e.actions.map((e=>(0,r.h)(d.Z,e))))),t.badge>1&&o.push((0,r.h)("div",{key:`${t.uid}|${t.badge}`,class:e.meta.badgeClass,style:e.badgeStyle},[t.badge])),(0,r.h)("div",{ref:e=>{S[""+t.uid]=e},key:t.uid,class:t.class,...t.attrs},[(0,r.h)("div",{class:t.wrapperClass},o)])})))))))}})}const A={setDefaults(e){!0===(0,h.Kn)(e)&&Object.assign(g,e)},registerType(e,t){!0===(0,h.Kn)(t)&&(E[e]=t)},install({$q:e,parentApp:t}){if(e.notify=this.create=t=>C(t,e),e.notify.setDefaults=this.setDefaults,e.notify.registerType=this.registerType,void 0!==e.config.notify&&this.setDefaults(e.config.notify),!0!==this.__installed){k.forEach((e=>{y[e]=(0,o.iH)([]);const t=!0===["left","center","right"].includes(e)?"center":e.indexOf("top")>-1?"top":"bottom",n=e.indexOf("left")>-1?"start":e.indexOf("right")>-1?"end":"center",r=["left","right"].includes(e)?`items-${"left"===e?"start":"end"} justify-center`:"center"===e?"flex-center":`items-${n}`;_[e]=`q-notifications__list q-notifications__list--${t} fixed column no-wrap ${r}`}));const e=(0,p.q_)("q-notify");(0,v.$)(L(),t).mount(e)}}}},7506:(e,t,n)=>{"use strict";n.d(t,{ZP:()=>g,aG:()=>i,client:()=>h,uX:()=>l});n(9665);var o=n(499),r=n(3251);const l=(0,o.iH)(!1);let i,a=!1;function s(e,t){const n=/(edg|edge|edga|edgios)\/([\w.]+)/.exec(e)||/(opr)[\/]([\w.]+)/.exec(e)||/(vivaldi)[\/]([\w.]+)/.exec(e)||/(chrome|crios)[\/]([\w.]+)/.exec(e)||/(version)(applewebkit)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+).*(version)[\/]([\w.]+).*(safari)[\/]([\w.]+)/.exec(e)||/(firefox|fxios)[\/]([\w.]+)/.exec(e)||/(webkit)[\/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[\/]([\w.]+)/.exec(e)||[];return{browser:n[5]||n[3]||n[1]||"",version:n[2]||n[4]||"0",versionNumber:n[4]||n[2]||"0",platform:t[0]||""}}function u(e){return/(ipad)/.exec(e)||/(ipod)/.exec(e)||/(windows phone)/.exec(e)||/(iphone)/.exec(e)||/(kindle)/.exec(e)||/(silk)/.exec(e)||/(android)/.exec(e)||/(win)/.exec(e)||/(mac)/.exec(e)||/(linux)/.exec(e)||/(cros)/.exec(e)||/(playbook)/.exec(e)||/(bb)/.exec(e)||/(blackberry)/.exec(e)||[]}const c="ontouchstart"in window||window.navigator.maxTouchPoints>0;function d(e){i={is:{...e}},delete e.mac,delete e.desktop;const t=Math.min(window.innerHeight,window.innerWidth)>414?"ipad":"iphone";Object.assign(e,{mobile:!0,ios:!0,platform:t,[t]:!0})}function f(e){const t=e.toLowerCase(),n=u(t),o=s(t,n),r={};o.browser&&(r[o.browser]=!0,r.version=o.version,r.versionNumber=parseInt(o.versionNumber,10)),o.platform&&(r[o.platform]=!0);const l=r.android||r.ios||r.bb||r.blackberry||r.ipad||r.iphone||r.ipod||r.kindle||r.playbook||r.silk||r["windows phone"];return!0===l||t.indexOf("mobile")>-1?(r.mobile=!0,r.edga||r.edgios?(r.edge=!0,o.browser="edge"):r.crios?(r.chrome=!0,o.browser="chrome"):r.fxios&&(r.firefox=!0,o.browser="firefox")):r.desktop=!0,(r.ipod||r.ipad||r.iphone)&&(r.ios=!0),r["windows phone"]&&(r.winphone=!0,delete r["windows phone"]),(r.chrome||r.opr||r.safari||r.vivaldi||!0===r.mobile&&!0!==r.ios&&!0!==l)&&(r.webkit=!0),r.edg&&(o.browser="edgechromium",r.edgeChromium=!0),(r.safari&&r.blackberry||r.bb)&&(o.browser="blackberry",r.blackberry=!0),r.safari&&r.playbook&&(o.browser="playbook",r.playbook=!0),r.opr&&(o.browser="opera",r.opera=!0),r.safari&&r.android&&(o.browser="android",r.android=!0),r.safari&&r.kindle&&(o.browser="kindle",r.kindle=!0),r.safari&&r.silk&&(o.browser="silk",r.silk=!0),r.vivaldi&&(o.browser="vivaldi",r.vivaldi=!0),r.name=o.browser,r.platform=o.platform,t.indexOf("electron")>-1?r.electron=!0:document.location.href.indexOf("-extension://")>-1?r.bex=!0:(void 0!==window.Capacitor?(r.capacitor=!0,r.nativeMobile=!0,r.nativeMobileWrapper="capacitor"):void 0===window._cordovaNative&&void 0===window.cordova||(r.cordova=!0,r.nativeMobile=!0,r.nativeMobileWrapper="cordova"),!0===c&&!0===r.mac&&(!0===r.desktop&&!0===r.safari||!0===r.nativeMobile&&!0!==r.android&&!0!==r.ios&&!0!==r.ipad)&&d(r)),r}const p=navigator.userAgent||navigator.vendor||window.opera,v={has:{touch:!1,webStorage:!1},within:{iframe:!1}},h={userAgent:p,is:f(p),has:{touch:c},within:{iframe:window.self!==window.top}},m={install(e){const{$q:t}=e;!0===l.value?(e.onSSRHydrated.push((()=>{Object.assign(t.platform,h),l.value=!1,i=void 0})),t.platform=(0,o.qj)(this)):t.platform=this}};{let e;(0,r.g)(h.has,"webStorage",(()=>{if(void 0!==e)return e;try{if(window.localStorage)return e=!0,!0}catch(t){}return e=!1,!1})),a=!0===h.is.ios&&-1===window.navigator.vendor.toLowerCase().indexOf("apple"),!0===l.value?Object.assign(m,h,i,v):Object.assign(m,h)}const g=m},899:(e,t,n)=>{"use strict";function o(e,t=250,n){let o=null;function r(){const r=arguments,l=()=>{o=null,!0!==n&&e.apply(this,r)};null!==o?clearTimeout(o):!0===n&&e.apply(this,r),o=setTimeout(l,t)}return r.cancel=()=>{null!==o&&clearTimeout(o)},r}n.d(t,{Z:()=>o})},223:(e,t,n)=>{"use strict";n.d(t,{iv:()=>r,mY:()=>i,sb:()=>l});var o=n(499);function r(e,t){const n=e.style;for(const o in t)n[o]=t[o]}function l(e){if(void 0===e||null===e)return;if("string"===typeof e)try{return document.querySelector(e)||void 0}catch(n){return}const t=(0,o.SU)(e);return t?t.$el||t:void 0}function i(e,t){if(void 0===e||null===e||!0===e.contains(t))return!0;for(let n=e.nextElementSibling;null!==n;n=n.nextElementSibling)if(n.contains(t))return!0;return!1}},1384:(e,t,n)=>{"use strict";n.d(t,{AZ:()=>a,FK:()=>i,Jf:()=>d,M0:()=>f,NS:()=>c,X$:()=>u,ZT:()=>r,du:()=>l,listenOpts:()=>o,sT:()=>s,ul:()=>p});n(9665);const o={hasPassive:!1,passiveCapture:!0,notPassiveCapture:!0};try{const e=Object.defineProperty({},"passive",{get(){Object.assign(o,{hasPassive:!0,passive:{passive:!0},notPassive:{passive:!1},passiveCapture:{passive:!0,capture:!0},notPassiveCapture:{passive:!1,capture:!0}})}});window.addEventListener("qtest",null,e),window.removeEventListener("qtest",null,e)}catch(v){}function r(){}function l(e){return 0===e.button}function i(e){return e.touches&&e.touches[0]?e=e.touches[0]:e.changedTouches&&e.changedTouches[0]?e=e.changedTouches[0]:e.targetTouches&&e.targetTouches[0]&&(e=e.targetTouches[0]),{top:e.clientY,left:e.clientX}}function a(e){if(e.path)return e.path;if(e.composedPath)return e.composedPath();const t=[];let n=e.target;while(n){if(t.push(n),"HTML"===n.tagName)return t.push(document),t.push(window),t;n=n.parentElement}}function s(e){e.stopPropagation()}function u(e){!1!==e.cancelable&&e.preventDefault()}function c(e){!1!==e.cancelable&&e.preventDefault(),e.stopPropagation()}function d(e,t){if(void 0===e||!0===t&&!0===e.__dragPrevented)return;const n=!0===t?e=>{e.__dragPrevented=!0,e.addEventListener("dragstart",u,o.notPassiveCapture)}:e=>{delete e.__dragPrevented,e.removeEventListener("dragstart",u,o.notPassiveCapture)};e.querySelectorAll("a, img").forEach(n)}function f(e,t,n){const r=`__q_${t}_evt`;e[r]=void 0!==e[r]?e[r].concat(n):n,n.forEach((t=>{t[0].addEventListener(t[1],e[t[2]],o[t[3]])}))}function p(e,t){const n=`__q_${t}_evt`;void 0!==e[n]&&(e[n].forEach((t=>{t[0].removeEventListener(t[1],e[t[2]],o[t[3]])})),e[n]=void 0)}},321:(e,t,n)=>{"use strict";n.d(t,{Uz:()=>r,vX:()=>o});function o(e,t,n){return n<=t?t:Math.min(n,Math.max(t,e))}function r(e,t,n){if(n<=t)return t;const o=n-t+1;let r=t+(e-t)%o;return r<t&&(r=o+r),0===r?0:r}},2729:(e,t,n)=>{"use strict";function o(e,t=document.body){if("string"!==typeof e)throw new TypeError("Expected a string as propName");if(!(t instanceof Element))throw new TypeError("Expected a DOM element");return getComputedStyle(t).getPropertyValue(`--q-${e}`).trim()||null}n.d(t,{Z:()=>o})},4680:(e,t,n)=>{"use strict";n.d(t,{Kn:()=>r,hj:()=>l,xb:()=>o});n(3122);function o(e,t){if(e===t)return!0;if(null!==e&&null!==t&&"object"===typeof e&&"object"===typeof t){if(e.constructor!==t.constructor)return!1;let n,r;if(e.constructor===Array){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!0!==o(e[r],t[r]))return!1;return!0}if(e.constructor===Map){if(e.size!==t.size)return!1;let n=e.entries();r=n.next();while(!0!==r.done){if(!0!==t.has(r.value[0]))return!1;r=n.next()}n=e.entries(),r=n.next();while(!0!==r.done){if(!0!==o(r.value[1],t.get(r.value[0])))return!1;r=n.next()}return!0}if(e.constructor===Set){if(e.size!==t.size)return!1;const n=e.entries();r=n.next();while(!0!==r.done){if(!0!==t.has(r.value[0]))return!1;r=n.next()}return!0}if(null!=e.buffer&&e.buffer.constructor===ArrayBuffer){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(e[r]!==t[r])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const l=Object.keys(e).filter((t=>void 0!==e[t]));if(n=l.length,n!==Object.keys(t).filter((e=>void 0!==t[e])).length)return!1;for(r=n;0!==r--;){const n=l[r];if(!0!==o(e[n],t[n]))return!1}return!0}return e!==e&&t!==t}function r(e){return null!==e&&"object"===typeof e&&!0!==Array.isArray(e)}function l(e){return"number"===typeof e&&isFinite(e)}},3628:(e,t,n)=>{"use strict";n.d(t,{E:()=>s});const o=/^#[0-9a-fA-F]{3}([0-9a-fA-F]{3})?$/,r=/^#[0-9a-fA-F]{4}([0-9a-fA-F]{4})?$/,l=/^#([0-9a-fA-F]{3}|[0-9a-fA-F]{4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/,i=/^rgb\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5])\)$/,a=/^rgba\(((0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),){2}(0|[1-9][\d]?|1[\d]{0,2}|2[\d]?|2[0-4][\d]|25[0-5]),(0|0\.[0-9]+[1-9]|0\.[1-9]+|1)\)$/,s={date:e=>/^-?[\d]+\/[0-1]\d\/[0-3]\d$/.test(e),time:e=>/^([0-1]?\d|2[0-3]):[0-5]\d$/.test(e),fulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d:[0-5]\d$/.test(e),timeOrFulltime:e=>/^([0-1]?\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/.test(e),email:e=>/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(e),hexColor:e=>o.test(e),hexaColor:e=>r.test(e),hexOrHexaColor:e=>l.test(e),rgbColor:e=>i.test(e),rgbaColor:e=>a.test(e),rgbOrRgbaColor:e=>i.test(e)||a.test(e),hexOrRgbColor:e=>o.test(e)||i.test(e),hexaOrRgbaColor:e=>r.test(e)||a.test(e),anyColor:e=>l.test(e)||i.test(e)||a.test(e)}},5984:(e,t,n)=>{"use strict";n.d(t,{Z:()=>_});var o=n(1384),r=n(3701),l=n(7506);let i,a,s,u,c,d,f=0,p=!1,v=null;function h(e){m(e)&&(0,o.NS)(e)}function m(e){if(e.target===document.body||e.target.classList.contains("q-layout__backdrop"))return!0;const t=(0,o.AZ)(e),n=e.shiftKey&&!e.deltaX,l=!n&&Math.abs(e.deltaX)<=Math.abs(e.deltaY),i=n||l?e.deltaY:e.deltaX;for(let o=0;o<t.length;o++){const e=t[o];if((0,r.QA)(e,l))return l?i<0&&0===e.scrollTop||i>0&&e.scrollTop+e.clientHeight===e.scrollHeight:i<0&&0===e.scrollLeft||i>0&&e.scrollLeft+e.clientWidth===e.scrollWidth}return!0}function g(e){e.target===document&&(document.scrollingElement.scrollTop=document.scrollingElement.scrollTop)}function b(e){!0!==p&&(p=!0,requestAnimationFrame((()=>{p=!1;const{height:t}=e.target,{clientHeight:n,scrollTop:o}=document.scrollingElement;void 0!==s&&t===window.innerHeight||(s=n-t,document.scrollingElement.scrollTop=o),o>s&&(document.scrollingElement.scrollTop-=Math.ceil((o-s)/8))})))}function y(e){const t=document.body,n=void 0!==window.visualViewport;if("add"===e){const{overflowY:e,overflowX:s}=window.getComputedStyle(t);i=(0,r.OI)(window),a=(0,r.u3)(window),u=t.style.left,c=t.style.top,d=window.location.href,t.style.left=`-${i}px`,t.style.top=`-${a}px`,"hidden"!==s&&("scroll"===s||t.scrollWidth>window.innerWidth)&&t.classList.add("q-body--force-scrollbar-x"),"hidden"!==e&&("scroll"===e||t.scrollHeight>window.innerHeight)&&t.classList.add("q-body--force-scrollbar-y"),t.classList.add("q-body--prevent-scroll"),document.qScrollPrevented=!0,!0===l.client.is.ios&&(!0===n?(window.scrollTo(0,0),window.visualViewport.addEventListener("resize",b,o.listenOpts.passiveCapture),window.visualViewport.addEventListener("scroll",b,o.listenOpts.passiveCapture),window.scrollTo(0,0)):window.addEventListener("scroll",g,o.listenOpts.passiveCapture))}!0===l.client.is.desktop&&!0===l.client.is.mac&&window[`${e}EventListener`]("wheel",h,o.listenOpts.notPassive),"remove"===e&&(!0===l.client.is.ios&&(!0===n?(window.visualViewport.removeEventListener("resize",b,o.listenOpts.passiveCapture),window.visualViewport.removeEventListener("scroll",b,o.listenOpts.passiveCapture)):window.removeEventListener("scroll",g,o.listenOpts.passiveCapture)),t.classList.remove("q-body--prevent-scroll"),t.classList.remove("q-body--force-scrollbar-x"),t.classList.remove("q-body--force-scrollbar-y"),document.qScrollPrevented=!1,t.style.left=u,t.style.top=c,window.location.href===d&&window.scrollTo(i,a),s=void 0)}function _(e){let t="add";if(!0===e){if(f++,null!==v)return clearTimeout(v),void(v=null);if(f>1)return}else{if(0===f)return;if(f--,f>0)return;if(t="remove",!0===l.client.is.ios&&!0===l.client.is.nativeMobile)return null!==v&&clearTimeout(v),void(v=setTimeout((()=>{y(t),v=null}),100))}y(t)}},9092:(e,t,n)=>{"use strict";n.d(t,{D:()=>c,m:()=>u});n(9665);var o=n(1384),r=n(2909);let l=null;const{notPassiveCapture:i}=o.listenOpts,a=[];function s(e){null!==l&&(clearTimeout(l),l=null);const t=e.target;if(void 0===t||8===t.nodeType||!0===t.classList.contains("no-pointer-events"))return;let n=r.Q$.length-1;while(n>=0){const e=r.Q$[n].$;if("QTooltip"!==e.type.name){if("QDialog"!==e.type.name)break;if(!0!==e.props.seamless)return;n--}else n--}for(let o=a.length-1;o>=0;o--){const n=a[o];if(null!==n.anchorEl.value&&!1!==n.anchorEl.value.contains(t)||t!==document.body&&(null===n.innerRef.value||!1!==n.innerRef.value.contains(t)))return;e.qClickOutside=!0,n.onClickOutside(e)}}function u(e){a.push(e),1===a.length&&(document.addEventListener("mousedown",s,i),document.addEventListener("touchstart",s,i))}function c(e){const t=a.findIndex((t=>t===e));t>-1&&(a.splice(t,1),0===a.length&&(null!==l&&(clearTimeout(l),l=null),document.removeEventListener("mousedown",s,i),document.removeEventListener("touchstart",s,i)))}},5987:(e,t,n)=>{"use strict";n.d(t,{L:()=>l,f:()=>i});var o=n(499),r=n(9835);const l=e=>(0,o.Xl)((0,r.aZ)(e)),i=e=>(0,o.Xl)(e)},4124:(e,t,n)=>{"use strict";n.d(t,{Z:()=>l});var o=n(499),r=n(3251);const l=(e,t)=>{const n=(0,o.qj)(e);for(const o in e)(0,r.g)(t,o,(()=>n[o]),(e=>{n[o]=e}));return t}},7026:(e,t,n)=>{"use strict";n.d(t,{YX:()=>i,fP:()=>u,jd:()=>s,xF:()=>a});n(9665);let o=[],r=[];function l(e){r=r.filter((t=>t!==e))}function i(e){l(e),r.push(e)}function a(e){l(e),0===r.length&&0!==o.length&&(o[o.length-1](),o=[])}function s(e){0===r.length?e():o.push(e)}function u(e){o=o.filter((t=>t!==e))}},7495:(e,t,n)=>{"use strict";n.d(t,{Uf:()=>r,tP:()=>l,w6:()=>o});const o={};let r=!1;function l(){r=!0}},6669:(e,t,n)=>{"use strict";n.d(t,{pB:()=>u,q_:()=>s});n(9665);var o=n(7495);const r=[],l=[];let i=1,a=document.body;function s(e,t){const n=document.createElement("div");if(n.id=void 0!==t?`q-portal--${t}--${i++}`:e,void 0!==o.w6.globalNodes){const e=o.w6.globalNodes.class;void 0!==e&&(n.className=e)}return a.appendChild(n),r.push(n),l.push(t),n}function u(e){const t=r.indexOf(e);r.splice(t,1),l.splice(t,1),e.remove()}},3251:(e,t,n)=>{"use strict";function o(e,t,n,o){return Object.defineProperty(e,t,{get:n,set:o,enumerable:!0}),e}n.d(t,{g:()=>o})},1705:(e,t,n)=>{"use strict";n.d(t,{So:()=>i,Wm:()=>l,ZK:()=>r});let o=!1;function r(e){o=!0===e.isComposing}function l(e){return!0===o||e!==Object(e)||!0===e.isComposing||!0===e.qKeyEvent}function i(e,t){return!0!==l(e)&&[].concat(t).includes(e.keyCode)}},9480:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});const o={xs:30,sm:35,md:40,lg:50,xl:60}},2909:(e,t,n)=>{"use strict";n.d(t,{AH:()=>i,Q$:()=>r,S7:()=>a,je:()=>l});var o=n(2046);const r=[];function l(e){return r.find((t=>null!==t.contentEl&&t.contentEl.contains(e)))}function i(e,t){do{if("QMenu"===e.$options.name){if(e.hide(t),!0===e.$props.separateClosePopup)return(0,o.O2)(e)}else if(!0===e.__qPortal){const n=(0,o.O2)(e);return void 0!==n&&"QPopupProxy"===n.$options.name?(e.hide(t),n):e}e=(0,o.O2)(e)}while(void 0!==e&&null!==e)}function a(e,t,n){while(0!==n&&void 0!==e&&null!==e){if(!0===e.__qPortal){if(n--,"QMenu"===e.$options.name){e=i(e,t);continue}e.hide(t)}e=(0,o.O2)(e)}}},9388:(e,t,n)=>{"use strict";n.d(t,{$:()=>a,io:()=>s,li:()=>c,wq:()=>h});var o=n(3701),r=n(7506);let l,i;function a(e){const t=e.split(" ");return 2===t.length&&(!0!==["top","center","bottom"].includes(t[0])?(console.error("Anchor/Self position must start with one of top/center/bottom"),!1):!0===["left","middle","right","start","end"].includes(t[1])||(console.error("Anchor/Self position must end with one of left/middle/right/start/end"),!1))}function s(e){return!e||2===e.length&&("number"===typeof e[0]&&"number"===typeof e[1])}const u={"start#ltr":"left","start#rtl":"right","end#ltr":"right","end#rtl":"left"};function c(e,t){const n=e.split(" ");return{vertical:n[0],horizontal:u[`${n[1]}#${!0===t?"rtl":"ltr"}`]}}function d(e,t){let{top:n,left:o,right:r,bottom:l,width:i,height:a}=e.getBoundingClientRect();return void 0!==t&&(n-=t[1],o-=t[0],l+=t[1],r+=t[0],i+=t[0],a+=t[1]),{top:n,bottom:l,height:a,left:o,right:r,width:i,middle:o+(r-o)/2,center:n+(l-n)/2}}function f(e,t,n){let{top:o,left:r}=e.getBoundingClientRect();return o+=t.top,r+=t.left,void 0!==n&&(o+=n[1],r+=n[0]),{top:o,bottom:o+1,height:1,left:r,right:r+1,width:1,middle:r,center:o}}function p(e,t){return{top:0,center:t/2,bottom:t,left:0,middle:e/2,right:e}}function v(e,t,n,o){return{top:e[n.vertical]-t[o.vertical],left:e[n.horizontal]-t[o.horizontal]}}function h(e,t=0){if(null===e.targetEl||null===e.anchorEl||t>5)return;if(0===e.targetEl.offsetHeight||0===e.targetEl.offsetWidth)return void setTimeout((()=>{h(e,t+1)}),10);const{targetEl:n,offset:o,anchorEl:a,anchorOrigin:s,selfOrigin:u,absoluteOffset:c,fit:g,cover:b,maxHeight:y,maxWidth:_}=e;if(!0===r.client.is.ios&&void 0!==window.visualViewport){const e=document.body.style,{offsetLeft:t,offsetTop:n}=window.visualViewport;t!==l&&(e.setProperty("--q-pe-left",t+"px"),l=t),n!==i&&(e.setProperty("--q-pe-top",n+"px"),i=n)}const{scrollLeft:w,scrollTop:S}=n,k=void 0===c?d(a,!0===b?[0,0]:o):f(a,c,o);Object.assign(n.style,{top:0,left:0,minWidth:null,minHeight:null,maxWidth:_||"100vw",maxHeight:y||"100vh",visibility:"visible"});const{offsetWidth:x,offsetHeight:E}=n,{elWidth:C,elHeight:T}=!0===g||!0===b?{elWidth:Math.max(k.width,x),elHeight:!0===b?Math.max(k.height,E):E}:{elWidth:x,elHeight:E};let O={maxWidth:_,maxHeight:y};!0!==g&&!0!==b||(O.minWidth=k.width+"px",!0===b&&(O.minHeight=k.height+"px")),Object.assign(n.style,O);const F=p(C,T);let L=v(k,F,s,u);if(void 0===c||void 0===o)m(L,k,F,s,u);else{const{top:e,left:t}=L;m(L,k,F,s,u);let n=!1;if(L.top!==e){n=!0;const e=2*o[1];k.center=k.top-=e,k.bottom-=e+2}if(L.left!==t){n=!0;const e=2*o[0];k.middle=k.left-=e,k.right-=e+2}!0===n&&(L=v(k,F,s,u),m(L,k,F,s,u))}O={top:L.top+"px",left:L.left+"px"},void 0!==L.maxHeight&&(O.maxHeight=L.maxHeight+"px",k.height>L.maxHeight&&(O.minHeight=O.maxHeight)),void 0!==L.maxWidth&&(O.maxWidth=L.maxWidth+"px",k.width>L.maxWidth&&(O.minWidth=O.maxWidth)),Object.assign(n.style,O),n.scrollTop!==S&&(n.scrollTop=S),n.scrollLeft!==w&&(n.scrollLeft=w)}function m(e,t,n,r,l){const i=n.bottom,a=n.right,s=(0,o.np)(),u=window.innerHeight-s,c=document.body.clientWidth;if(e.top<0||e.top+i>u)if("center"===l.vertical)e.top=t[r.vertical]>u/2?Math.max(0,u-i):0,e.maxHeight=Math.min(i,u);else if(t[r.vertical]>u/2){const n=Math.min(u,"center"===r.vertical?t.center:r.vertical===l.vertical?t.bottom:t.top);e.maxHeight=Math.min(i,n),e.top=Math.max(0,n-i)}else e.top=Math.max(0,"center"===r.vertical?t.center:r.vertical===l.vertical?t.top:t.bottom),e.maxHeight=Math.min(i,u-e.top);if(e.left<0||e.left+a>c)if(e.maxWidth=Math.min(a,c),"middle"===l.horizontal)e.left=t[r.horizontal]>c/2?Math.max(0,c-a):0;else if(t[r.horizontal]>c/2){const n=Math.min(c,"middle"===r.horizontal?t.middle:r.horizontal===l.horizontal?t.right:t.left);e.maxWidth=Math.min(a,n),e.left=Math.max(0,n-e.maxWidth)}else e.left=Math.max(0,"middle"===r.horizontal?t.middle:r.horizontal===l.horizontal?t.left:t.right),e.maxWidth=Math.min(a,c-e.left)}["left","middle","right"].forEach((e=>{u[`${e}#ltr`]=e,u[`${e}#rtl`]=e}))},2026:(e,t,n)=>{"use strict";n.d(t,{Bl:()=>l,Jl:()=>s,KR:()=>r,pf:()=>a,vs:()=>i});var o=n(9835);function r(e,t){return void 0!==e&&e()||t}function l(e,t){if(void 0!==e){const t=e();if(void 0!==t&&null!==t)return t.slice()}return t}function i(e,t){return void 0!==e?t.concat(e()):t}function a(e,t){return void 0===e?t:void 0!==t?t.concat(e()):e()}function s(e,t,n,r,l,i){t.key=r+l;const a=(0,o.h)(e,t,n);return!0===l?(0,o.wy)(a,i()):a}},8383:(e,t,n)=>{"use strict";n.d(t,{e:()=>o});let o=!1;{const e=document.createElement("div");e.setAttribute("dir","rtl"),Object.assign(e.style,{width:"1px",height:"1px",overflow:"auto"});const t=document.createElement("div");Object.assign(t.style,{width:"1000px",height:"1px"}),document.body.appendChild(e),e.appendChild(t),e.scrollLeft=-1e3,o=e.scrollLeft>=0,e.remove()}},2589:(e,t,n)=>{"use strict";n.d(t,{M:()=>r});var o=n(7506);function r(){if(void 0!==window.getSelection){const e=window.getSelection();void 0!==e.empty?e.empty():void 0!==e.removeAllRanges&&(e.removeAllRanges(),!0!==o.ZP.is.mobile&&e.addRange(document.createRange()))}else void 0!==document.selection&&document.selection.empty()}},5439:(e,t,n)=>{"use strict";n.d(t,{Lu:()=>r,Mw:()=>i,Nd:()=>s,Ng:()=>o,YE:()=>l,qO:()=>u,vh:()=>a});const o="_q_",r="_q_s_",l="_q_l_",i="_q_pc_",a="_q_fo_",s="_q_tabs_",u=()=>{}},9367:(e,t,n)=>{"use strict";n.d(t,{R:()=>l,n:()=>a});const o={left:!0,right:!0,up:!0,down:!0,horizontal:!0,vertical:!0},r=Object.keys(o);function l(e){const t={};for(const n of r)!0===e[n]&&(t[n]=!0);return 0===Object.keys(t).length?o:(!0===t.horizontal?t.left=t.right=!0:!0===t.left&&!0===t.right&&(t.horizontal=!0),!0===t.vertical?t.up=t.down=!0:!0===t.up&&!0===t.down&&(t.vertical=!0),!0===t.horizontal&&!0===t.vertical&&(t.all=!0),t)}o.all=!0;const i=["INPUT","TEXTAREA"];function a(e,t){return void 0===t.event&&void 0!==e.target&&!0!==e.target.draggable&&"function"===typeof t.handler&&!1===i.includes(e.target.nodeName.toUpperCase())&&(void 0===e.qClonedBy||-1===e.qClonedBy.indexOf(t.uid))}},2046:(e,t,n)=>{"use strict";function o(e){if(Object(e.$parent)===e.$parent)return e.$parent;let{parent:t}=e.$;while(Object(t)===t){if(Object(t.proxy)===t.proxy)return t.proxy;t=t.parent}}function r(e,t){"symbol"===typeof t.type?!0===Array.isArray(t.children)&&t.children.forEach((t=>{r(e,t)})):e.add(t)}function l(e){const t=new Set;return e.forEach((e=>{r(t,e)})),Array.from(t)}function i(e){return void 0!==e.appContext.config.globalProperties.$router}function a(e){return!0===e.isUnmounted||!0===e.isDeactivated}n.d(t,{$D:()=>a,O2:()=>o,Pf:()=>l,Rb:()=>i})},3701:(e,t,n)=>{"use strict";n.d(t,{OI:()=>a,QA:()=>m,b0:()=>l,f3:()=>f,ik:()=>p,np:()=>h,u3:()=>i});var o=n(223);const r=[null,document,document.body,document.scrollingElement,document.documentElement];function l(e,t){let n=(0,o.sb)(t);if(void 0===n){if(void 0===e||null===e)return window;n=e.closest(".scroll,.scroll-y,.overflow-auto")}return r.includes(n)?window:n}function i(e){return e===window?window.pageYOffset||window.scrollY||document.body.scrollTop||0:e.scrollTop}function a(e){return e===window?window.pageXOffset||window.scrollX||document.body.scrollLeft||0:e.scrollLeft}function s(e,t,n=0){const o=void 0===arguments[3]?performance.now():arguments[3],r=i(e);n<=0?r!==t&&c(e,t):requestAnimationFrame((l=>{const i=l-o,a=r+(t-r)/Math.max(i,n)*i;c(e,a),a!==t&&s(e,t,n-i,l)}))}function u(e,t,n=0){const o=void 0===arguments[3]?performance.now():arguments[3],r=a(e);n<=0?r!==t&&d(e,t):requestAnimationFrame((l=>{const i=l-o,a=r+(t-r)/Math.max(i,n)*i;d(e,a),a!==t&&u(e,t,n-i,l)}))}function c(e,t){e!==window?e.scrollTop=t:window.scrollTo(window.pageXOffset||window.scrollX||document.body.scrollLeft||0,t)}function d(e,t){e!==window?e.scrollLeft=t:window.scrollTo(t,window.pageYOffset||window.scrollY||document.body.scrollTop||0)}function f(e,t,n){n?s(e,t,n):c(e,t)}function p(e,t,n){n?u(e,t,n):d(e,t)}let v;function h(){if(void 0!==v)return v;const e=document.createElement("p"),t=document.createElement("div");(0,o.iv)(e,{width:"100%",height:"200px"}),(0,o.iv)(t,{position:"absolute",top:"0px",left:"0px",visibility:"hidden",width:"200px",height:"150px",overflow:"hidden"}),t.appendChild(e),document.body.appendChild(t);const n=e.offsetWidth;t.style.overflow="scroll";let r=e.offsetWidth;return n===r&&(r=t.clientWidth),t.remove(),v=n-r,v}function m(e,t=!0){return!(!e||e.nodeType!==Node.ELEMENT_NODE)&&(t?e.scrollHeight>e.clientHeight&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-y"])):e.scrollWidth>e.clientWidth&&(e.classList.contains("scroll")||e.classList.contains("overflow-auto")||["auto","scroll"].includes(window.getComputedStyle(e)["overflow-x"])))}},7674:(e,t,n)=>{"use strict";function o(e,t,n=document.body){if("string"!==typeof e)throw new TypeError("Expected a string as propName");if("string"!==typeof t)throw new TypeError("Expected a string as value");if(!(n instanceof Element))throw new TypeError("Expected a DOM element");n.style.setProperty(`--q-${e}`,t)}n.d(t,{Z:()=>o})},8633:(e,t,n)=>{"use strict";function o(e,t=250){let n,o=!1;return function(){return!1===o&&(o=!0,setTimeout((()=>{o=!1}),t),n=e.apply(this,arguments)),n}}n.d(t,{Z:()=>o})},796:(e,t,n)=>{"use strict";n.d(t,{Z:()=>s});n(5231),n(3075),n(548),n(2279),n(2157),n(6735),n(9665);let o,r=0;const l=new Array(256);for(let u=0;u<256;u++)l[u]=(u+256).toString(16).substring(1);const i=(()=>{const e="undefined"!==typeof crypto?crypto:"undefined"!==typeof window?window.crypto||window.msCrypto:void 0;if(void 0!==e){if(void 0!==e.randomBytes)return e.randomBytes;if(void 0!==e.getRandomValues)return t=>{const n=new Uint8Array(t);return e.getRandomValues(n),n}}return e=>{const t=[];for(let n=e;n>0;n--)t.push(Math.floor(256*Math.random()));return t}})(),a=4096;function s(){(void 0===o||r+16>a)&&(r=0,o=i(a));const e=Array.prototype.slice.call(o,r,r+=16);return e[6]=15&e[6]|64,e[8]=63&e[8]|128,l[e[0]]+l[e[1]]+l[e[2]]+l[e[3]]+"-"+l[e[4]]+l[e[5]]+"-"+l[e[6]]+l[e[7]]+"-"+l[e[8]]+l[e[9]]+"-"+l[e[10]]+l[e[11]]+l[e[12]]+l[e[13]]+l[e[14]]+l[e[15]]}},1947:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});var o=n(1583),r=n(3558),l=n(2289);const i={version:"2.12.5",install:o.Z,lang:r.Z,iconSet:l.Z}},8762:(e,t,n)=>{"use strict";var o=n(6107),r=n(7545),l=TypeError;e.exports=function(e){if(o(e))return e;throw l(r(e)+" is not a function")}},9220:(e,t,n)=>{"use strict";var o=n(6107),r=String,l=TypeError;e.exports=function(e){if("object"==typeof e||o(e))return e;throw l("Can't set "+r(e)+" as a prototype")}},616:(e,t,n)=>{"use strict";var o=n(1419),r=String,l=TypeError;e.exports=function(e){if(o(e))return e;throw l(r(e)+" is not an object")}},8389:e=>{"use strict";e.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},8086:(e,t,n)=>{"use strict";var o,r,l,i=n(8389),a=n(4133),s=n(3834),u=n(6107),c=n(1419),d=n(2924),f=n(4239),p=n(7545),v=n(4722),h=n(4076),m=n(9570),g=n(6123),b=n(7886),y=n(6534),_=n(4103),w=n(3965),S=n(780),k=S.enforce,x=S.get,E=s.Int8Array,C=E&&E.prototype,T=s.Uint8ClampedArray,O=T&&T.prototype,F=E&&b(E),L=C&&b(C),A=Object.prototype,P=s.TypeError,q=_("toStringTag"),R=w("TYPED_ARRAY_TAG"),N="TypedArrayConstructor",M=i&&!!y&&"Opera"!==f(s.opera),I=!1,$={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},D={BigInt64Array:8,BigUint64Array:8},V=function(e){if(!c(e))return!1;var t=f(e);return"DataView"===t||d($,t)||d(D,t)},j=function(e){var t=b(e);if(c(t)){var n=x(t);return n&&d(n,N)?n[N]:j(t)}},B=function(e){if(!c(e))return!1;var t=f(e);return d($,t)||d(D,t)},H=function(e){if(B(e))return e;throw P("Target is not a typed array")},z=function(e){if(u(e)&&(!y||g(F,e)))return e;throw P(p(e)+" is not a typed array constructor")},U=function(e,t,n,o){if(a){if(n)for(var r in $){var l=s[r];if(l&&d(l.prototype,e))try{delete l.prototype[e]}catch(i){try{l.prototype[e]=t}catch(u){}}}L[e]&&!n||h(L,e,n?t:M&&C[e]||t,o)}},W=function(e,t,n){var o,r;if(a){if(y){if(n)for(o in $)if(r=s[o],r&&d(r,e))try{delete r[e]}catch(l){}if(F[e]&&!n)return;try{return h(F,e,n?t:M&&F[e]||t)}catch(l){}}for(o in $)r=s[o],!r||r[e]&&!n||h(r,e,t)}};for(o in $)r=s[o],l=r&&r.prototype,l?k(l)[N]=r:M=!1;for(o in D)r=s[o],l=r&&r.prototype,l&&(k(l)[N]=r);if((!M||!u(F)||F===Function.prototype)&&(F=function(){throw P("Incorrect invocation")},M))for(o in $)s[o]&&y(s[o],F);if((!M||!L||L===A)&&(L=F.prototype,M))for(o in $)s[o]&&y(s[o].prototype,L);if(M&&b(O)!==L&&y(O,L),a&&!d(L,q))for(o in I=!0,m(L,q,{configurable:!0,get:function(){return c(this)?this[R]:void 0}}),$)s[o]&&v(s[o],R,o);e.exports={NATIVE_ARRAY_BUFFER_VIEWS:M,TYPED_ARRAY_TAG:I&&R,aTypedArray:H,aTypedArrayConstructor:z,exportTypedArrayMethod:U,exportTypedArrayStaticMethod:W,getTypedArrayConstructor:j,isView:V,isTypedArray:B,TypedArray:F,TypedArrayPrototype:L}},3364:(e,t,n)=>{"use strict";var o=n(8600);e.exports=function(e,t){var n=0,r=o(t),l=new e(r);while(r>n)l[n]=t[n++];return l}},7714:(e,t,n)=>{"use strict";var o=n(7447),r=n(2661),l=n(8600),i=function(e){return function(t,n,i){var a,s=o(t),u=l(s),c=r(i,u);if(e&&n!==n){while(u>c)if(a=s[c++],a!==a)return!0}else for(;u>c;c++)if((e||c in s)&&s[c]===n)return e||c||0;return!e&&-1}};e.exports={includes:i(!0),indexOf:i(!1)}},9275:(e,t,n)=>{"use strict";var o=n(6158),r=n(3972),l=n(8332),i=n(8600),a=function(e){var t=1===e;return function(n,a,s){var u,c,d=l(n),f=r(d),p=o(a,s),v=i(f);while(v-- >0)if(u=f[v],c=p(u,v,d),c)switch(e){case 0:return u;case 1:return v}return t?-1:void 0}};e.exports={findLast:a(0),findLastIndex:a(1)}},3614:(e,t,n)=>{"use strict";var o=n(4133),r=n(6555),l=TypeError,i=Object.getOwnPropertyDescriptor,a=o&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=a?function(e,t){if(r(e)&&!i(e,"length").writable)throw l("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},7579:(e,t,n)=>{"use strict";var o=n(8600);e.exports=function(e,t){for(var n=o(e),r=new t(n),l=0;l<n;l++)r[l]=e[n-l-1];return r}},5330:(e,t,n)=>{"use strict";var o=n(8600),r=n(6675),l=RangeError;e.exports=function(e,t,n,i){var a=o(e),s=r(n),u=s<0?a+s:s;if(u>=a||u<0)throw l("Incorrect index");for(var c=new t(a),d=0;d<a;d++)c[d]=d===u?i:e[d];return c}},6749:(e,t,n)=>{"use strict";var o=n(1636),r=o({}.toString),l=o("".slice);e.exports=function(e){return l(r(e),8,-1)}},4239:(e,t,n)=>{"use strict";var o=n(4130),r=n(6107),l=n(6749),i=n(4103),a=i("toStringTag"),s=Object,u="Arguments"===l(function(){return arguments}()),c=function(e,t){try{return e[t]}catch(n){}};e.exports=o?l:function(e){var t,n,o;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=c(t=s(e),a))?n:u?l(t):"Object"===(o=l(t))&&r(t.callee)?"Arguments":o}},7366:(e,t,n)=>{"use strict";var o=n(2924),r=n(1240),l=n(863),i=n(1012);e.exports=function(e,t,n){for(var a=r(t),s=i.f,u=l.f,c=0;c<a.length;c++){var d=a[c];o(e,d)||n&&o(n,d)||s(e,d,u(t,d))}}},911:(e,t,n)=>{"use strict";var o=n(8814);e.exports=!o((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype}))},4722:(e,t,n)=>{"use strict";var o=n(4133),r=n(1012),l=n(3386);e.exports=o?function(e,t,n){return r.f(e,t,l(1,n))}:function(e,t,n){return e[t]=n,e}},3386:e=>{"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},9570:(e,t,n)=>{"use strict";var o=n(2358),r=n(1012);e.exports=function(e,t,n){return n.get&&o(n.get,t,{getter:!0}),n.set&&o(n.set,t,{setter:!0}),r.f(e,t,n)}},4076:(e,t,n)=>{"use strict";var o=n(6107),r=n(1012),l=n(2358),i=n(5437);e.exports=function(e,t,n,a){a||(a={});var s=a.enumerable,u=void 0!==a.name?a.name:t;if(o(n)&&l(n,u,a),a.global)s?e[t]=n:i(t,n);else{try{a.unsafe?e[t]&&(s=!0):delete e[t]}catch(c){}s?e[t]=n:r.f(e,t,{value:n,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return e}},5437:(e,t,n)=>{"use strict";var o=n(3834),r=Object.defineProperty;e.exports=function(e,t){try{r(o,e,{value:t,configurable:!0,writable:!0})}catch(n){o[e]=t}return t}},6405:(e,t,n)=>{"use strict";var o=n(7545),r=TypeError;e.exports=function(e,t){if(!delete e[t])throw r("Cannot delete property "+o(t)+" of "+o(e))}},4133:(e,t,n)=>{"use strict";var o=n(8814);e.exports=!o((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},948:e=>{"use strict";var t="object"==typeof document&&document.all,n="undefined"==typeof t&&void 0!==t;e.exports={all:t,IS_HTMLDDA:n}},1657:(e,t,n)=>{"use strict";var o=n(3834),r=n(1419),l=o.document,i=r(l)&&r(l.createElement);e.exports=function(e){return i?l.createElement(e):{}}},6689:e=>{"use strict";var t=TypeError,n=9007199254740991;e.exports=function(e){if(e>n)throw t("Maximum allowed index exceeded");return e}},322:e=>{"use strict";e.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},1418:(e,t,n)=>{"use strict";var o,r,l=n(3834),i=n(322),a=l.process,s=l.Deno,u=a&&a.versions||s&&s.version,c=u&&u.v8;c&&(o=c.split("."),r=o[0]>0&&o[0]<4?1:+(o[0]+o[1])),!r&&i&&(o=i.match(/Edge\/(\d+)/),(!o||o[1]>=74)&&(o=i.match(/Chrome\/(\d+)/),o&&(r=+o[1]))),e.exports=r},203:e=>{"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},6943:(e,t,n)=>{"use strict";var o=n(3834),r=n(863).f,l=n(4722),i=n(4076),a=n(5437),s=n(7366),u=n(2764);e.exports=function(e,t){var n,c,d,f,p,v,h=e.target,m=e.global,g=e.stat;if(c=m?o:g?o[h]||a(h,{}):(o[h]||{}).prototype,c)for(d in t){if(p=t[d],e.dontCallGetSet?(v=r(c,d),f=v&&v.value):f=c[d],n=u(m?d:h+(g?".":"#")+d,e.forced),!n&&void 0!==f){if(typeof p==typeof f)continue;s(p,f)}(e.sham||f&&f.sham)&&l(p,"sham",!0),i(c,d,p,e)}}},8814:e=>{"use strict";e.exports=function(e){try{return!!e()}catch(t){return!0}}},6158:(e,t,n)=>{"use strict";var o=n(9287),r=n(8762),l=n(9793),i=o(o.bind);e.exports=function(e,t){return r(e),void 0===t?e:l?i(e,t):function(){return e.apply(t,arguments)}}},9793:(e,t,n)=>{"use strict";var o=n(8814);e.exports=!o((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},6654:(e,t,n)=>{"use strict";var o=n(9793),r=Function.prototype.call;e.exports=o?r.bind(r):function(){return r.apply(r,arguments)}},9104:(e,t,n)=>{"use strict";var o=n(4133),r=n(2924),l=Function.prototype,i=o&&Object.getOwnPropertyDescriptor,a=r(l,"name"),s=a&&"something"===function(){}.name,u=a&&(!o||o&&i(l,"name").configurable);e.exports={EXISTS:a,PROPER:s,CONFIGURABLE:u}},5478:(e,t,n)=>{"use strict";var o=n(1636),r=n(8762);e.exports=function(e,t,n){try{return o(r(Object.getOwnPropertyDescriptor(e,t)[n]))}catch(l){}}},9287:(e,t,n)=>{"use strict";var o=n(6749),r=n(1636);e.exports=function(e){if("Function"===o(e))return r(e)}},1636:(e,t,n)=>{"use strict";var o=n(9793),r=Function.prototype,l=r.call,i=o&&r.bind.bind(l,l);e.exports=o?i:function(e){return function(){return l.apply(e,arguments)}}},7859:(e,t,n)=>{"use strict";var o=n(3834),r=n(6107),l=function(e){return r(e)?e:void 0};e.exports=function(e,t){return arguments.length<2?l(o[e]):o[e]&&o[e][t]}},7689:(e,t,n)=>{"use strict";var o=n(8762),r=n(3873);e.exports=function(e,t){var n=e[t];return r(n)?void 0:o(n)}},3834:function(e,t,n){"use strict";var o=function(e){return e&&e.Math===Math&&e};e.exports=o("object"==typeof globalThis&&globalThis)||o("object"==typeof window&&window)||o("object"==typeof self&&self)||o("object"==typeof n.g&&n.g)||function(){return this}()||this||Function("return this")()},2924:(e,t,n)=>{"use strict";var o=n(1636),r=n(8332),l=o({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return l(r(e),t)}},1999:e=>{"use strict";e.exports={}},6335:(e,t,n)=>{"use strict";var o=n(4133),r=n(8814),l=n(1657);e.exports=!o&&!r((function(){return 7!==Object.defineProperty(l("div"),"a",{get:function(){return 7}}).a}))},3972:(e,t,n)=>{"use strict";var o=n(1636),r=n(8814),l=n(6749),i=Object,a=o("".split);e.exports=r((function(){return!i("z").propertyIsEnumerable(0)}))?function(e){return"String"===l(e)?a(e,""):i(e)}:i},6461:(e,t,n)=>{"use strict";var o=n(1636),r=n(6107),l=n(6081),i=o(Function.toString);r(l.inspectSource)||(l.inspectSource=function(e){return i(e)}),e.exports=l.inspectSource},780:(e,t,n)=>{"use strict";var o,r,l,i=n(5779),a=n(3834),s=n(1419),u=n(4722),c=n(2924),d=n(6081),f=n(5315),p=n(1999),v="Object already initialized",h=a.TypeError,m=a.WeakMap,g=function(e){return l(e)?r(e):o(e,{})},b=function(e){return function(t){var n;if(!s(t)||(n=r(t)).type!==e)throw h("Incompatible receiver, "+e+" required");return n}};if(i||d.state){var y=d.state||(d.state=new m);y.get=y.get,y.has=y.has,y.set=y.set,o=function(e,t){if(y.has(e))throw h(v);return t.facade=e,y.set(e,t),t},r=function(e){return y.get(e)||{}},l=function(e){return y.has(e)}}else{var _=f("state");p[_]=!0,o=function(e,t){if(c(e,_))throw h(v);return t.facade=e,u(e,_,t),t},r=function(e){return c(e,_)?e[_]:{}},l=function(e){return c(e,_)}}e.exports={set:o,get:r,has:l,enforce:g,getterFor:b}},6555:(e,t,n)=>{"use strict";var o=n(6749);e.exports=Array.isArray||function(e){return"Array"===o(e)}},354:(e,t,n)=>{"use strict";var o=n(4239);e.exports=function(e){var t=o(e);return"BigInt64Array"===t||"BigUint64Array"===t}},6107:(e,t,n)=>{"use strict";var o=n(948),r=o.all;e.exports=o.IS_HTMLDDA?function(e){return"function"==typeof e||e===r}:function(e){return"function"==typeof e}},2764:(e,t,n)=>{"use strict";var o=n(8814),r=n(6107),l=/#|\.prototype\./,i=function(e,t){var n=s[a(e)];return n===c||n!==u&&(r(t)?o(t):!!t)},a=i.normalize=function(e){return String(e).replace(l,".").toLowerCase()},s=i.data={},u=i.NATIVE="N",c=i.POLYFILL="P";e.exports=i},3873:e=>{"use strict";e.exports=function(e){return null===e||void 0===e}},1419:(e,t,n)=>{"use strict";var o=n(6107),r=n(948),l=r.all;e.exports=r.IS_HTMLDDA?function(e){return"object"==typeof e?null!==e:o(e)||e===l}:function(e){return"object"==typeof e?null!==e:o(e)}},200:e=>{"use strict";e.exports=!1},1637:(e,t,n)=>{"use strict";var o=n(7859),r=n(6107),l=n(6123),i=n(49),a=Object;e.exports=i?function(e){return"symbol"==typeof e}:function(e){var t=o("Symbol");return r(t)&&l(t.prototype,a(e))}},8600:(e,t,n)=>{"use strict";var o=n(7302);e.exports=function(e){return o(e.length)}},2358:(e,t,n)=>{"use strict";var o=n(1636),r=n(8814),l=n(6107),i=n(2924),a=n(4133),s=n(9104).CONFIGURABLE,u=n(6461),c=n(780),d=c.enforce,f=c.get,p=String,v=Object.defineProperty,h=o("".slice),m=o("".replace),g=o([].join),b=a&&!r((function(){return 8!==v((function(){}),"length",{value:8}).length})),y=String(String).split("String"),_=e.exports=function(e,t,n){"Symbol("===h(p(t),0,7)&&(t="["+m(p(t),/^Symbol\(([^)]*)\)/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!i(e,"name")||s&&e.name!==t)&&(a?v(e,"name",{value:t,configurable:!0}):e.name=t),b&&n&&i(n,"arity")&&e.length!==n.arity&&v(e,"length",{value:n.arity});try{n&&i(n,"constructor")&&n.constructor?a&&v(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(r){}var o=d(e);return i(o,"source")||(o.source=g(y,"string"==typeof t?t:"")),e};Function.prototype.toString=_((function(){return l(this)&&f(this).source||u(this)}),"toString")},7233:e=>{"use strict";var t=Math.ceil,n=Math.floor;e.exports=Math.trunc||function(e){var o=+e;return(o>0?n:t)(o)}},1012:(e,t,n)=>{"use strict";var o=n(4133),r=n(6335),l=n(64),i=n(616),a=n(1017),s=TypeError,u=Object.defineProperty,c=Object.getOwnPropertyDescriptor,d="enumerable",f="configurable",p="writable";t.f=o?l?function(e,t,n){if(i(e),t=a(t),i(n),"function"===typeof e&&"prototype"===t&&"value"in n&&p in n&&!n[p]){var o=c(e,t);o&&o[p]&&(e[t]=n.value,n={configurable:f in n?n[f]:o[f],enumerable:d in n?n[d]:o[d],writable:!1})}return u(e,t,n)}:u:function(e,t,n){if(i(e),t=a(t),i(n),r)try{return u(e,t,n)}catch(o){}if("get"in n||"set"in n)throw s("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},863:(e,t,n)=>{"use strict";var o=n(4133),r=n(6654),l=n(8068),i=n(3386),a=n(7447),s=n(1017),u=n(2924),c=n(6335),d=Object.getOwnPropertyDescriptor;t.f=o?d:function(e,t){if(e=a(e),t=s(t),c)try{return d(e,t)}catch(n){}if(u(e,t))return i(!r(l.f,e,t),e[t])}},3450:(e,t,n)=>{"use strict";var o=n(6682),r=n(203),l=r.concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return o(e,l)}},1996:(e,t)=>{"use strict";t.f=Object.getOwnPropertySymbols},7886:(e,t,n)=>{"use strict";var o=n(2924),r=n(6107),l=n(8332),i=n(5315),a=n(911),s=i("IE_PROTO"),u=Object,c=u.prototype;e.exports=a?u.getPrototypeOf:function(e){var t=l(e);if(o(t,s))return t[s];var n=t.constructor;return r(n)&&t instanceof n?n.prototype:t instanceof u?c:null}},6123:(e,t,n)=>{"use strict";var o=n(1636);e.exports=o({}.isPrototypeOf)},6682:(e,t,n)=>{"use strict";var o=n(1636),r=n(2924),l=n(7447),i=n(7714).indexOf,a=n(1999),s=o([].push);e.exports=function(e,t){var n,o=l(e),u=0,c=[];for(n in o)!r(a,n)&&r(o,n)&&s(c,n);while(t.length>u)r(o,n=t[u++])&&(~i(c,n)||s(c,n));return c}},8068:(e,t)=>{"use strict";var n={}.propertyIsEnumerable,o=Object.getOwnPropertyDescriptor,r=o&&!n.call({1:2},1);t.f=r?function(e){var t=o(this,e);return!!t&&t.enumerable}:n},6534:(e,t,n)=>{"use strict";var o=n(5478),r=n(616),l=n(9220);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{e=o(Object.prototype,"__proto__","set"),e(n,[]),t=n instanceof Array}catch(i){}return function(n,o){return r(n),l(o),t?e(n,o):n.__proto__=o,n}}():void 0)},9370:(e,t,n)=>{"use strict";var o=n(6654),r=n(6107),l=n(1419),i=TypeError;e.exports=function(e,t){var n,a;if("string"===t&&r(n=e.toString)&&!l(a=o(n,e)))return a;if(r(n=e.valueOf)&&!l(a=o(n,e)))return a;if("string"!==t&&r(n=e.toString)&&!l(a=o(n,e)))return a;throw i("Can't convert object to primitive value")}},1240:(e,t,n)=>{"use strict";var o=n(7859),r=n(1636),l=n(3450),i=n(1996),a=n(616),s=r([].concat);e.exports=o("Reflect","ownKeys")||function(e){var t=l.f(a(e)),n=i.f;return n?s(t,n(e)):t}},9592:(e,t,n)=>{"use strict";var o=n(616);e.exports=function(){var e=o(this),t="";return e.hasIndices&&(t+="d"),e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.dotAll&&(t+="s"),e.unicode&&(t+="u"),e.unicodeSets&&(t+="v"),e.sticky&&(t+="y"),t}},5177:(e,t,n)=>{"use strict";var o=n(3873),r=TypeError;e.exports=function(e){if(o(e))throw r("Can't call method on "+e);return e}},5315:(e,t,n)=>{"use strict";var o=n(8850),r=n(3965),l=o("keys");e.exports=function(e){return l[e]||(l[e]=r(e))}},6081:(e,t,n)=>{"use strict";var o=n(3834),r=n(5437),l="__core-js_shared__",i=o[l]||r(l,{});e.exports=i},8850:(e,t,n)=>{"use strict";var o=n(200),r=n(6081);(e.exports=function(e,t){return r[e]||(r[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.32.1",mode:o?"pure":"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.32.1/LICENSE",source:"https://github.com/zloirock/core-js"})},4651:(e,t,n)=>{"use strict";var o=n(1418),r=n(8814),l=n(3834),i=l.String;e.exports=!!Object.getOwnPropertySymbols&&!r((function(){var e=Symbol("symbol detection");return!i(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&o&&o<41}))},2661:(e,t,n)=>{"use strict";var o=n(6675),r=Math.max,l=Math.min;e.exports=function(e,t){var n=o(e);return n<0?r(n+t,0):l(n,t)}},7385:(e,t,n)=>{"use strict";var o=n(4384),r=TypeError;e.exports=function(e){var t=o(e,"number");if("number"==typeof t)throw r("Can't convert number to bigint");return BigInt(t)}},7447:(e,t,n)=>{"use strict";var o=n(3972),r=n(5177);e.exports=function(e){return o(r(e))}},6675:(e,t,n)=>{"use strict";var o=n(7233);e.exports=function(e){var t=+e;return t!==t||0===t?0:o(t)}},7302:(e,t,n)=>{"use strict";var o=n(6675),r=Math.min;e.exports=function(e){return e>0?r(o(e),9007199254740991):0}},8332:(e,t,n)=>{"use strict";var o=n(5177),r=Object;e.exports=function(e){return r(o(e))}},4384:(e,t,n)=>{"use strict";var o=n(6654),r=n(1419),l=n(1637),i=n(7689),a=n(9370),s=n(4103),u=TypeError,c=s("toPrimitive");e.exports=function(e,t){if(!r(e)||l(e))return e;var n,s=i(e,c);if(s){if(void 0===t&&(t="default"),n=o(s,e,t),!r(n)||l(n))return n;throw u("Can't convert object to primitive value")}return void 0===t&&(t="number"),a(e,t)}},1017:(e,t,n)=>{"use strict";var o=n(4384),r=n(1637);e.exports=function(e){var t=o(e,"string");return r(t)?t:t+""}},4130:(e,t,n)=>{"use strict";var o=n(4103),r=o("toStringTag"),l={};l[r]="z",e.exports="[object z]"===String(l)},6975:(e,t,n)=>{"use strict";var o=n(4239),r=String;e.exports=function(e){if("Symbol"===o(e))throw TypeError("Cannot convert a Symbol value to a string");return r(e)}},7545:e=>{"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(n){return"Object"}}},3965:(e,t,n)=>{"use strict";var o=n(1636),r=0,l=Math.random(),i=o(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+i(++r+l,36)}},49:(e,t,n)=>{"use strict";var o=n(4651);e.exports=o&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},64:(e,t,n)=>{"use strict";var o=n(4133),r=n(8814);e.exports=o&&r((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},5809:e=>{"use strict";var t=TypeError;e.exports=function(e,n){if(e<n)throw t("Not enough arguments");return e}},5779:(e,t,n)=>{"use strict";var o=n(3834),r=n(6107),l=o.WeakMap;e.exports=r(l)&&/native code/.test(String(l))},4103:(e,t,n)=>{"use strict";var o=n(3834),r=n(8850),l=n(2924),i=n(3965),a=n(4651),s=n(49),u=o.Symbol,c=r("wks"),d=s?u["for"]||u:u&&u.withoutSetter||i;e.exports=function(e){return l(c,e)||(c[e]=a&&l(u,e)?u[e]:d("Symbol."+e)),c[e]}},9665:(e,t,n)=>{"use strict";var o=n(6943),r=n(8332),l=n(8600),i=n(3614),a=n(6689),s=n(8814),u=s((function(){return 4294967297!==[].push.call({length:4294967296},1)})),c=function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}},d=u||!c();o({target:"Array",proto:!0,arity:1,forced:d},{push:function(e){var t=r(this),n=l(t),o=arguments.length;a(n+o);for(var s=0;s<o;s++)t[n]=arguments[s],n++;return i(t,n),n}})},6890:(e,t,n)=>{"use strict";var o=n(6943),r=n(8332),l=n(8600),i=n(3614),a=n(6405),s=n(6689),u=1!==[].unshift(0),c=function(){try{Object.defineProperty([],"length",{writable:!1}).unshift()}catch(e){return e instanceof TypeError}},d=u||!c();o({target:"Array",proto:!0,arity:1,forced:d},{unshift:function(e){var t=r(this),n=l(t),o=arguments.length;if(o){s(n+o);var u=n;while(u--){var c=u+o;u in t?t[c]=t[u]:a(t,c)}for(var d=0;d<o;d++)t[d]=arguments[d]}return i(t,n+o)}})},3122:(e,t,n)=>{"use strict";var o=n(3834),r=n(4133),l=n(9570),i=n(9592),a=n(8814),s=o.RegExp,u=s.prototype,c=r&&a((function(){var e=!0;try{s(".","d")}catch(c){e=!1}var t={},n="",o=e?"dgimsy":"gimsy",r=function(e,o){Object.defineProperty(t,e,{get:function(){return n+=o,!0}})},l={dotAll:"s",global:"g",ignoreCase:"i",multiline:"m",sticky:"y"};for(var i in e&&(l.hasIndices="d"),l)r(i,l[i]);var a=Object.getOwnPropertyDescriptor(u,"flags").get.call(t);return a!==o||n!==o}));c&&l(u,"flags",{configurable:!0,get:i})},5231:(e,t,n)=>{"use strict";var o=n(8086),r=n(8600),l=n(6675),i=o.aTypedArray,a=o.exportTypedArrayMethod;a("at",(function(e){var t=i(this),n=r(t),o=l(e),a=o>=0?o:n+o;return a<0||a>=n?void 0:t[a]}))},548:(e,t,n)=>{"use strict";var o=n(8086),r=n(9275).findLastIndex,l=o.aTypedArray,i=o.exportTypedArrayMethod;i("findLastIndex",(function(e){return r(l(this),e,arguments.length>1?arguments[1]:void 0)}))},3075:(e,t,n)=>{"use strict";var o=n(8086),r=n(9275).findLast,l=o.aTypedArray,i=o.exportTypedArrayMethod;i("findLast",(function(e){return r(l(this),e,arguments.length>1?arguments[1]:void 0)}))},2279:(e,t,n)=>{"use strict";var o=n(7579),r=n(8086),l=r.aTypedArray,i=r.exportTypedArrayMethod,a=r.getTypedArrayConstructor;i("toReversed",(function(){return o(l(this),a(this))}))},2157:(e,t,n)=>{"use strict";var o=n(8086),r=n(1636),l=n(8762),i=n(3364),a=o.aTypedArray,s=o.getTypedArrayConstructor,u=o.exportTypedArrayMethod,c=r(o.TypedArrayPrototype.sort);u("toSorted",(function(e){void 0!==e&&l(e);var t=a(this),n=i(s(t),t);return c(n,e)}))},6735:(e,t,n)=>{"use strict";var o=n(5330),r=n(8086),l=n(354),i=n(6675),a=n(7385),s=r.aTypedArray,u=r.getTypedArrayConstructor,c=r.exportTypedArrayMethod,d=!!function(){try{new Int8Array(1)["with"](2,{valueOf:function(){throw 8}})}catch(e){return 8===e}}();c("with",{with:function(e,t){var n=s(this),r=i(e),c=l(n)?a(t):+t;return o(n,u(n),r,c)}}["with"],!d)},5516:(e,t,n)=>{"use strict";var o=n(4076),r=n(1636),l=n(6975),i=n(5809),a=URLSearchParams,s=a.prototype,u=r(s.append),c=r(s["delete"]),d=r(s.forEach),f=r([].push),p=new a("a=1&a=2&b=3");p["delete"]("a",1),p["delete"]("b",void 0),p+""!=="a=2"&&o(s,"delete",(function(e){var t=arguments.length,n=t<2?void 0:arguments[1];if(t&&void 0===n)return c(this,e);var o=[];d(this,(function(e,t){f(o,{key:t,value:e})})),i(t,1);var r,a=l(e),s=l(n),p=0,v=0,h=!1,m=o.length;while(p<m)r=o[p++],h||r.key===a?(h=!0,c(this,r.key)):v++;while(v<m)r=o[v++],r.key===a&&r.value===s||u(this,r.key,r.value)}),{enumerable:!0,unsafe:!0})},3036:(e,t,n)=>{"use strict";var o=n(4076),r=n(1636),l=n(6975),i=n(5809),a=URLSearchParams,s=a.prototype,u=r(s.getAll),c=r(s.has),d=new a("a=1");!d.has("a",2)&&d.has("a",void 0)||o(s,"has",(function(e){var t=arguments.length,n=t<2?void 0:arguments[1];if(t&&void 0===n)return c(this,e);var o=u(this,e);i(t,1);var r=l(n),a=0;while(a<o.length)if(o[a++]===r)return!0;return!1}),{enumerable:!0,unsafe:!0})},2309:(e,t,n)=>{"use strict";var o=n(4133),r=n(1636),l=n(9570),i=URLSearchParams.prototype,a=r(i.forEach);o&&!("size"in i)&&l(i,"size",{get:function(){var e=0;return a(this,(function(){e++})),e},configurable:!0,enumerable:!0})},7712:(e,t,n)=>{"use strict";n.d(t,{o:()=>pn});
/*!
  * shared v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */
const o="undefined"!==typeof window;const r="function"===typeof Symbol&&"symbol"===typeof Symbol.toStringTag,l=e=>r?Symbol(e):e,i=(e,t,n)=>a({l:e,k:t,s:n}),a=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),s=e=>"number"===typeof e&&isFinite(e),u=e=>"[object Date]"===k(e),c=e=>"[object RegExp]"===k(e),d=e=>x(e)&&0===Object.keys(e).length;function f(e,t){"undefined"!==typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const p=Object.assign;function v(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const h=Object.prototype.hasOwnProperty;function m(e,t){return h.call(e,t)}const g=Array.isArray,b=e=>"function"===typeof e,y=e=>"string"===typeof e,_=e=>"boolean"===typeof e,w=e=>null!==e&&"object"===typeof e,S=Object.prototype.toString,k=e=>S.call(e),x=e=>"[object Object]"===k(e),E=e=>null==e?"":g(e)||x(e)&&e.toString===S?JSON.stringify(e,null,2):String(e);
/*!
  * message-compiler v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */
const C={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,__EXTEND_POINT__:15};C.EXPECTED_TOKEN,C.INVALID_TOKEN_IN_PLACEHOLDER,C.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,C.UNKNOWN_ESCAPE_SEQUENCE,C.INVALID_UNICODE_ESCAPE_SEQUENCE,C.UNBALANCED_CLOSING_BRACE,C.UNTERMINATED_CLOSING_BRACE,C.EMPTY_PLACEHOLDER,C.NOT_ALLOW_NEST_PLACEHOLDER,C.INVALID_LINKED_FORMAT,C.MUST_HAVE_MESSAGES_IN_PLURAL,C.UNEXPECTED_EMPTY_LINKED_MODIFIER,C.UNEXPECTED_EMPTY_LINKED_KEY,C.UNEXPECTED_LEXICAL_ANALYSIS;function T(e,t,n={}){const{domain:o,messages:r,args:l}=n,i=e,a=new SyntaxError(String(i));return a.code=e,t&&(a.location=t),a.domain=o,a}function O(e){throw e}function F(e,t,n){return{line:e,column:t,offset:n}}function L(e,t,n){const o={start:e,end:t};return null!=n&&(o.source=n),o}const A=" ",P="\r",q="\n",R=String.fromCharCode(8232),N=String.fromCharCode(8233);function M(e){const t=e;let n=0,o=1,r=1,l=0;const i=e=>t[e]===P&&t[e+1]===q,a=e=>t[e]===q,s=e=>t[e]===N,u=e=>t[e]===R,c=e=>i(e)||a(e)||s(e)||u(e),d=()=>n,f=()=>o,p=()=>r,v=()=>l,h=e=>i(e)||s(e)||u(e)?q:t[e],m=()=>h(n),g=()=>h(n+l);function b(){return l=0,c(n)&&(o++,r=0),i(n)&&n++,n++,r++,t[n]}function y(){return i(n+l)&&l++,l++,t[n+l]}function _(){n=0,o=1,r=1,l=0}function w(e=0){l=e}function S(){const e=n+l;while(e!==n)b();l=0}return{index:d,line:f,column:p,peekOffset:v,charAt:h,currentChar:m,currentPeek:g,next:b,peek:y,reset:_,resetPeek:w,skipToPeek:S}}const I=void 0,$="'",D="tokenizer";function V(e,t={}){const n=!1!==t.location,o=M(e),r=()=>o.index(),l=()=>F(o.line(),o.column(),o.index()),i=l(),a=r(),s={currentType:14,offset:a,startLoc:i,endLoc:i,lastType:14,lastOffset:a,lastStartLoc:i,lastEndLoc:i,braceNest:0,inLinked:!1,text:""},u=()=>s,{onError:c}=t;function d(e,t,n,...o){const r=u();if(t.column+=n,t.offset+=n,c){const n=L(r.startLoc,t),l=T(e,n,{domain:D,args:o});c(l)}}function f(e,t,o){e.endLoc=l(),e.currentType=t;const r={type:t};return n&&(r.loc=L(e.startLoc,e.endLoc)),null!=o&&(r.value=o),r}const p=e=>f(e,14);function v(e,t){return e.currentChar()===t?(e.next(),t):(d(C.EXPECTED_TOKEN,l(),0,t),"")}function h(e){let t="";while(e.currentPeek()===A||e.currentPeek()===q)t+=e.currentPeek(),e.peek();return t}function m(e){const t=h(e);return e.skipToPeek(),t}function g(e){if(e===I)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function b(e){if(e===I)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}function y(e,t){const{currentType:n}=t;if(2!==n)return!1;h(e);const o=g(e.currentPeek());return e.resetPeek(),o}function _(e,t){const{currentType:n}=t;if(2!==n)return!1;h(e);const o="-"===e.currentPeek()?e.peek():e.currentPeek(),r=b(o);return e.resetPeek(),r}function w(e,t){const{currentType:n}=t;if(2!==n)return!1;h(e);const o=e.currentPeek()===$;return e.resetPeek(),o}function S(e,t){const{currentType:n}=t;if(8!==n)return!1;h(e);const o="."===e.currentPeek();return e.resetPeek(),o}function k(e,t){const{currentType:n}=t;if(9!==n)return!1;h(e);const o=g(e.currentPeek());return e.resetPeek(),o}function x(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;h(e);const o=":"===e.currentPeek();return e.resetPeek(),o}function E(e,t){const{currentType:n}=t;if(10!==n)return!1;const o=()=>{const t=e.currentPeek();return"{"===t?g(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===A||!t)&&(t===q?(e.peek(),o()):g(t))},r=o();return e.resetPeek(),r}function O(e){h(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function P(e){const t=h(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}function R(e,t=!0){const n=(t=!1,o="",r=!1)=>{const l=e.currentPeek();return"{"===l?"%"!==o&&t:"@"!==l&&l?"%"===l?(e.peek(),n(t,"%",!0)):"|"===l?!("%"!==o&&!r)||!(o===A||o===q):l===A?(e.peek(),n(!0,A,r)):l!==q||(e.peek(),n(!0,q,r)):"%"===o||t},o=n();return t&&e.resetPeek(),o}function N(e,t){const n=e.currentChar();return n===I?I:t(n)?(e.next(),n):null}function V(e){const t=e=>{const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t};return N(e,t)}function j(e){const t=e=>{const t=e.charCodeAt(0);return t>=48&&t<=57};return N(e,t)}function B(e){const t=e=>{const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102};return N(e,t)}function H(e){let t="",n="";while(t=j(e))n+=t;return n}function z(e){m(e);const t=e.currentChar();return"%"!==t&&d(C.EXPECTED_TOKEN,l(),0,t),e.next(),"%"}function U(e){let t="";while(1){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!R(e))break;t+=n,e.next()}else if(n===A||n===q)if(R(e))t+=n,e.next();else{if(O(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function W(e){m(e);let t="",n="";while(t=V(e))n+=t;return e.currentChar()===I&&d(C.UNTERMINATED_CLOSING_BRACE,l(),0),n}function Z(e){m(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${H(e)}`):t+=H(e),e.currentChar()===I&&d(C.UNTERMINATED_CLOSING_BRACE,l(),0),t}function K(e){m(e),v(e,"'");let t="",n="";const o=e=>e!==$&&e!==q;while(t=N(e,o))n+="\\"===t?Y(e):t;const r=e.currentChar();return r===q||r===I?(d(C.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,l(),0),r===q&&(e.next(),v(e,"'")),n):(v(e,"'"),n)}function Y(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return J(e,t,4);case"U":return J(e,t,6);default:return d(C.UNKNOWN_ESCAPE_SEQUENCE,l(),0,t),""}}function J(e,t,n){v(e,t);let o="";for(let r=0;r<n;r++){const n=B(e);if(!n){d(C.INVALID_UNICODE_ESCAPE_SEQUENCE,l(),0,`\\${t}${o}${e.currentChar()}`);break}o+=n}return`\\${t}${o}`}function X(e){m(e);let t="",n="";const o=e=>"{"!==e&&"}"!==e&&e!==A&&e!==q;while(t=N(e,o))n+=t;return n}function G(e){let t="",n="";while(t=V(e))n+=t;return n}function Q(e){const t=(n=!1,o)=>{const r=e.currentChar();return"{"!==r&&"%"!==r&&"@"!==r&&"|"!==r&&r?r===A?o:r===q?(o+=r,e.next(),t(n,o)):(o+=r,e.next(),t(!0,o)):o};return t(!1,"")}function ee(e){m(e);const t=v(e,"|");return m(e),t}function te(e,t){let n=null;const o=e.currentChar();switch(o){case"{":return t.braceNest>=1&&d(C.NOT_ALLOW_NEST_PLACEHOLDER,l(),0),e.next(),n=f(t,2,"{"),m(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&d(C.EMPTY_PLACEHOLDER,l(),0),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&m(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&d(C.UNTERMINATED_CLOSING_BRACE,l(),0),n=ne(e,t)||p(t),t.braceNest=0,n;default:let o=!0,r=!0,i=!0;if(O(e))return t.braceNest>0&&d(C.UNTERMINATED_CLOSING_BRACE,l(),0),n=f(t,1,ee(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return d(C.UNTERMINATED_CLOSING_BRACE,l(),0),t.braceNest=0,oe(e,t);if(o=y(e,t))return n=f(t,5,W(e)),m(e),n;if(r=_(e,t))return n=f(t,6,Z(e)),m(e),n;if(i=w(e,t))return n=f(t,7,K(e)),m(e),n;if(!o&&!r&&!i)return n=f(t,13,X(e)),d(C.INVALID_TOKEN_IN_PLACEHOLDER,l(),0,n.value),m(e),n;break}return n}function ne(e,t){const{currentType:n}=t;let o=null;const r=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||r!==q&&r!==A||d(C.INVALID_LINKED_FORMAT,l(),0),r){case"@":return e.next(),o=f(t,8,"@"),t.inLinked=!0,o;case".":return m(e),e.next(),f(t,9,".");case":":return m(e),e.next(),f(t,10,":");default:return O(e)?(o=f(t,1,ee(e)),t.braceNest=0,t.inLinked=!1,o):S(e,t)||x(e,t)?(m(e),ne(e,t)):k(e,t)?(m(e),f(t,12,G(e))):E(e,t)?(m(e),"{"===r?te(e,t)||o:f(t,11,Q(e))):(8===n&&d(C.INVALID_LINKED_FORMAT,l(),0),t.braceNest=0,t.inLinked=!1,oe(e,t))}}function oe(e,t){let n={type:14};if(t.braceNest>0)return te(e,t)||p(t);if(t.inLinked)return ne(e,t)||p(t);const o=e.currentChar();switch(o){case"{":return te(e,t)||p(t);case"}":return d(C.UNBALANCED_CLOSING_BRACE,l(),0),e.next(),f(t,3,"}");case"@":return ne(e,t)||p(t);default:if(O(e))return n=f(t,1,ee(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:o,hasSpace:r}=P(e);if(o)return r?f(t,0,U(e)):f(t,4,z(e));if(R(e))return f(t,0,U(e));break}return n}function re(){const{currentType:e,offset:t,startLoc:n,endLoc:i}=s;return s.lastType=e,s.lastOffset=t,s.lastStartLoc=n,s.lastEndLoc=i,s.offset=r(),s.startLoc=l(),o.currentChar()===I?f(s,14):oe(o,s)}return{nextToken:re,currentOffset:r,currentPosition:l,context:u}}const j="parser",B=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function H(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function z(e={}){const t=!1!==e.location,{onError:n}=e;function o(e,t,o,r,...l){const i=e.currentPosition();if(i.offset+=r,i.column+=r,n){const e=L(o,i),r=T(t,e,{domain:j,args:l});n(r)}}function r(e,n,o){const r={type:e,start:n,end:n};return t&&(r.loc={start:o,end:o}),r}function l(e,n,o,r){e.end=n,r&&(e.type=r),t&&e.loc&&(e.loc.end=o)}function i(e,t){const n=e.context(),o=r(3,n.offset,n.startLoc);return o.value=t,l(o,e.currentOffset(),e.currentPosition()),o}function a(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:i}=n,a=r(5,o,i);return a.index=parseInt(t,10),e.nextToken(),l(a,e.currentOffset(),e.currentPosition()),a}function s(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:i}=n,a=r(4,o,i);return a.key=t,e.nextToken(),l(a,e.currentOffset(),e.currentPosition()),a}function u(e,t){const n=e.context(),{lastOffset:o,lastStartLoc:i}=n,a=r(9,o,i);return a.value=t.replace(B,H),e.nextToken(),l(a,e.currentOffset(),e.currentPosition()),a}function c(e){const t=e.nextToken(),n=e.context(),{lastOffset:i,lastStartLoc:a}=n,s=r(8,i,a);return 12!==t.type?(o(e,C.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,0),s.value="",l(s,i,a),{nextConsumeToken:t,node:s}):(null==t.value&&o(e,C.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,0,U(t)),s.value=t.value||"",l(s,e.currentOffset(),e.currentPosition()),{node:s})}function d(e,t){const n=e.context(),o=r(7,n.offset,n.startLoc);return o.value=t,l(o,e.currentOffset(),e.currentPosition()),o}function f(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let i=e.nextToken();if(9===i.type){const t=c(e);n.modifier=t.node,i=t.nextConsumeToken||e.nextToken()}switch(10!==i.type&&o(e,C.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,U(i)),i=e.nextToken(),2===i.type&&(i=e.nextToken()),i.type){case 11:null==i.value&&o(e,C.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,U(i)),n.key=d(e,i.value||"");break;case 5:null==i.value&&o(e,C.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,U(i)),n.key=s(e,i.value||"");break;case 6:null==i.value&&o(e,C.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,U(i)),n.key=a(e,i.value||"");break;case 7:null==i.value&&o(e,C.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,U(i)),n.key=u(e,i.value||"");break;default:o(e,C.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc,0);const c=e.context(),f=r(7,c.offset,c.startLoc);return f.value="",l(f,c.offset,c.startLoc),n.key=f,l(n,c.offset,c.startLoc),{nextConsumeToken:i,node:n}}return l(n,e.currentOffset(),e.currentPosition()),{node:n}}function v(e){const t=e.context(),n=1===t.currentType?e.currentOffset():t.offset,c=1===t.currentType?t.endLoc:t.startLoc,d=r(2,n,c);d.items=[];let p=null;do{const n=p||e.nextToken();switch(p=null,n.type){case 0:null==n.value&&o(e,C.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,U(n)),d.items.push(i(e,n.value||""));break;case 6:null==n.value&&o(e,C.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,U(n)),d.items.push(a(e,n.value||""));break;case 5:null==n.value&&o(e,C.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,U(n)),d.items.push(s(e,n.value||""));break;case 7:null==n.value&&o(e,C.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,0,U(n)),d.items.push(u(e,n.value||""));break;case 8:const r=f(e);d.items.push(r.node),p=r.nextConsumeToken||null;break}}while(14!==t.currentType&&1!==t.currentType);const v=1===t.currentType?t.lastOffset:e.currentOffset(),h=1===t.currentType?t.lastEndLoc:e.currentPosition();return l(d,v,h),d}function h(e,t,n,i){const a=e.context();let s=0===i.items.length;const u=r(1,t,n);u.cases=[],u.cases.push(i);do{const t=v(e);s||(s=0===t.items.length),u.cases.push(t)}while(14!==a.currentType);return s&&o(e,C.MUST_HAVE_MESSAGES_IN_PLURAL,n,0),l(u,e.currentOffset(),e.currentPosition()),u}function m(e){const t=e.context(),{offset:n,startLoc:o}=t,r=v(e);return 14===t.currentType?r:h(e,n,o,r)}function g(n){const i=V(n,p({},e)),a=i.context(),s=r(0,a.offset,a.startLoc);return t&&s.loc&&(s.loc.source=n),s.body=m(i),14!==a.currentType&&o(i,C.UNEXPECTED_LEXICAL_ANALYSIS,a.lastStartLoc,0,n[a.offset]||""),l(s,i.currentOffset(),i.currentPosition()),s}return{parse:g}}function U(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function W(e,t={}){const n={ast:e,helpers:new Set},o=()=>n,r=e=>(n.helpers.add(e),e);return{context:o,helper:r}}function Z(e,t){for(let n=0;n<e.length;n++)K(e[n],t)}function K(e,t){switch(e.type){case 1:Z(e.cases,t),t.helper("plural");break;case 2:Z(e.items,t);break;case 6:const n=e;K(n.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function Y(e,t={}){const n=W(e);n.helper("normalize"),e.body&&K(e.body,n);const o=n.context();e.helpers=Array.from(o.helpers)}function J(e,t){const{sourceMap:n,filename:o,breakLineCode:r,needIndent:l}=t,i={source:e.loc.source,filename:o,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:r,needIndent:l,indentLevel:0},a=()=>i;function s(e,t){i.code+=e}function u(e,t=!0){const n=t?r:"";s(l?n+"  ".repeat(e):n)}function c(e=!0){const t=++i.indentLevel;e&&u(t)}function d(e=!0){const t=--i.indentLevel;e&&u(t)}function f(){u(i.indentLevel)}const p=e=>`_${e}`,v=()=>i.needIndent;return{context:a,push:s,indent:c,deindent:d,newline:f,helper:p,needIndent:v}}function X(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),te(e,t.key),t.modifier?(e.push(", "),te(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function G(e,t){const{helper:n,needIndent:o}=e;e.push(`${n("normalize")}([`),e.indent(o());const r=t.items.length;for(let l=0;l<r;l++){if(te(e,t.items[l]),l===r-1)break;e.push(", ")}e.deindent(o()),e.push("])")}function Q(e,t){const{helper:n,needIndent:o}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(o());const r=t.cases.length;for(let n=0;n<r;n++){if(te(e,t.cases[n]),n===r-1)break;e.push(", ")}e.deindent(o()),e.push("])")}}function ee(e,t){t.body?te(e,t.body):e.push("null")}function te(e,t){const{helper:n}=e;switch(t.type){case 0:ee(e,t);break;case 1:Q(e,t);break;case 2:G(e,t);break;case 6:X(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break;default:0}}const ne=(e,t={})=>{const n=y(t.mode)?t.mode:"normal",o=y(t.filename)?t.filename:"message.intl",r=!!t.sourceMap,l=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",i=t.needIndent?t.needIndent:"arrow"!==n,a=e.helpers||[],s=J(e,{mode:n,filename:o,sourceMap:r,breakLineCode:l,needIndent:i});s.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),s.indent(i),a.length>0&&(s.push(`const { ${a.map((e=>`${e}: _${e}`)).join(", ")} } = ctx`),s.newline()),s.push("return "),te(s,e),s.deindent(i),s.push("}");const{code:u,map:c}=s.context();return{ast:e,code:u,map:c?c.toJSON():void 0}};function oe(e,t={}){const n=p({},t),o=z(n),r=o.parse(e);return Y(r,n),ne(r,n)}
/*!
  * devtools-if v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */
const re={I18nInit:"i18n:init",FunctionTranslate:"function:translate"},le=[];le[0]={["w"]:[0],["i"]:[3,0],["["]:[4],["o"]:[7]},le[1]={["w"]:[1],["."]:[2],["["]:[4],["o"]:[7]},le[2]={["w"]:[2],["i"]:[3,0],["0"]:[3,0]},le[3]={["i"]:[3,0],["0"]:[3,0],["w"]:[1,1],["."]:[2,1],["["]:[4,1],["o"]:[7,1]},le[4]={["'"]:[5,0],['"']:[6,0],["["]:[4,2],["]"]:[1,3],["o"]:8,["l"]:[4,0]},le[5]={["'"]:[4,0],["o"]:8,["l"]:[5,0]},le[6]={['"']:[4,0],["o"]:8,["l"]:[6,0]};const ie=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function ae(e){return ie.test(e)}function se(e){const t=e.charCodeAt(0),n=e.charCodeAt(e.length-1);return t!==n||34!==t&&39!==t?e:e.slice(1,-1)}function ue(e){if(void 0===e||null===e)return"o";const t=e.charCodeAt(0);switch(t){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function ce(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(ae(t)?se(t):"*"+t)}function de(e){const t=[];let n,o,r,l,i,a,s,u=-1,c=0,d=0;const f=[];function p(){const t=e[u+1];if(5===c&&"'"===t||6===c&&'"'===t)return u++,r="\\"+t,f[0](),!0}f[0]=()=>{void 0===o?o=r:o+=r},f[1]=()=>{void 0!==o&&(t.push(o),o=void 0)},f[2]=()=>{f[0](),d++},f[3]=()=>{if(d>0)d--,c=4,f[0]();else{if(d=0,void 0===o)return!1;if(o=ce(o),!1===o)return!1;f[1]()}};while(null!==c)if(u++,n=e[u],"\\"!==n||!p()){if(l=ue(n),s=le[c],i=s[l]||s["l"]||8,8===i)return;if(c=i[0],void 0!==i[1]&&(a=f[i[1]],a&&(r=n,!1===a())))return;if(7===c)return t}}const fe=new Map;function pe(e,t){return w(e)?e[t]:null}function ve(e,t){if(!w(e))return null;let n=fe.get(t);if(n||(n=de(t),n&&fe.set(t,n)),!n)return null;const o=n.length;let r=e,l=0;while(l<o){const e=r[n[l]];if(void 0===e)return null;r=e,l++}return r}const he=e=>e,me=e=>"",ge="text",be=e=>0===e.length?"":e.join(""),ye=E;function _e(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function we(e){const t=s(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(s(e.named.count)||s(e.named.n))?s(e.named.count)?e.named.count:s(e.named.n)?e.named.n:t:t}function Se(e,t){t.count||(t.count=e),t.n||(t.n=e)}function ke(e={}){const t=e.locale,n=we(e),o=w(e.pluralRules)&&y(t)&&b(e.pluralRules[t])?e.pluralRules[t]:_e,r=w(e.pluralRules)&&y(t)&&b(e.pluralRules[t])?_e:void 0,l=e=>e[o(n,e.length,r)],i=e.list||[],a=e=>i[e],u=e.named||{};s(e.pluralIndex)&&Se(n,u);const c=e=>u[e];function d(t){const n=b(e.messages)?e.messages(t):!!w(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):me)}const f=t=>e.modifiers?e.modifiers[t]:he,p=x(e.processor)&&b(e.processor.normalize)?e.processor.normalize:be,v=x(e.processor)&&b(e.processor.interpolate)?e.processor.interpolate:ye,h=x(e.processor)&&y(e.processor.type)?e.processor.type:ge,m=(e,...t)=>{const[n,o]=t;let r="text",l="";1===t.length?w(n)?(l=n.modifier||l,r=n.type||r):y(n)&&(l=n||l):2===t.length&&(y(n)&&(l=n||l),y(o)&&(r=o||r));let i=d(e)(_);return"vnode"===r&&g(i)&&l&&(i=i[0]),l?f(l)(i,r):i},_={["list"]:a,["named"]:c,["plural"]:l,["linked"]:m,["message"]:d,["type"]:h,["interpolate"]:v,["normalize"]:p};return _}let xe=null;re.FunctionTranslate;function Ee(e){return t=>xe&&xe.emit(e,t)}const Ce={NOT_FOUND_KEY:1,FALLBACK_TO_TRANSLATE:2,CANNOT_FORMAT_NUMBER:3,FALLBACK_TO_NUMBER_FORMAT:4,CANNOT_FORMAT_DATE:5,FALLBACK_TO_DATE_FORMAT:6,__EXTEND_POINT__:7};Ce.NOT_FOUND_KEY,Ce.FALLBACK_TO_TRANSLATE,Ce.CANNOT_FORMAT_NUMBER,Ce.FALLBACK_TO_NUMBER_FORMAT,Ce.CANNOT_FORMAT_DATE,Ce.FALLBACK_TO_DATE_FORMAT;function Te(e,t,n){return[...new Set([n,...g(t)?t:w(t)?Object.keys(t):y(t)?[t]:[n]])]}function Oe(e,t,n){const o=y(n)?n:Re,r=e;r.__localeChainCache||(r.__localeChainCache=new Map);let l=r.__localeChainCache.get(o);if(!l){l=[];let e=[n];while(g(e))e=Fe(l,e,t);const i=g(t)||!x(t)?t:t["default"]?t["default"]:null;e=y(i)?[i]:i,g(e)&&Fe(l,e,!1),r.__localeChainCache.set(o,l)}return l}function Fe(e,t,n){let o=!0;for(let r=0;r<t.length&&_(o);r++){const l=t[r];y(l)&&(o=Le(e,t[r],n))}return o}function Le(e,t,n){let o;const r=t.split("-");do{const t=r.join("-");o=Ae(e,t,n),r.splice(-1,1)}while(r.length&&!0===o);return o}function Ae(e,t,n){let o=!1;if(!e.includes(t)&&(o=!0,t)){o="!"!==t[t.length-1];const r=t.replace(/!/g,"");e.push(r),(g(n)||x(n))&&n[r]&&(o=n[r])}return o}const Pe="9.2.2",qe=-1,Re="en-US",Ne="",Me=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function Ie(){return{upper:(e,t)=>"text"===t&&y(e)?e.toUpperCase():"vnode"===t&&w(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&y(e)?e.toLowerCase():"vnode"===t&&w(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&y(e)?Me(e):"vnode"===t&&w(e)&&"__v_isVNode"in e?Me(e.children):e}}let $e,De,Ve;function je(e){$e=e}function Be(e){De=e}function He(e){Ve=e}let ze=null;const Ue=e=>{ze=e};let We=0;function Ze(e={}){const t=y(e.version)?e.version:Pe,n=y(e.locale)?e.locale:Re,o=g(e.fallbackLocale)||x(e.fallbackLocale)||y(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:n,r=x(e.messages)?e.messages:{[n]:{}},l=x(e.datetimeFormats)?e.datetimeFormats:{[n]:{}},i=x(e.numberFormats)?e.numberFormats:{[n]:{}},a=p({},e.modifiers||{},Ie()),s=e.pluralRules||{},u=b(e.missing)?e.missing:null,d=!_(e.missingWarn)&&!c(e.missingWarn)||e.missingWarn,v=!_(e.fallbackWarn)&&!c(e.fallbackWarn)||e.fallbackWarn,h=!!e.fallbackFormat,m=!!e.unresolving,S=b(e.postTranslation)?e.postTranslation:null,k=x(e.processor)?e.processor:null,E=!_(e.warnHtmlMessage)||e.warnHtmlMessage,C=!!e.escapeParameter,T=b(e.messageCompiler)?e.messageCompiler:$e,O=b(e.messageResolver)?e.messageResolver:De||pe,F=b(e.localeFallbacker)?e.localeFallbacker:Ve||Te,L=w(e.fallbackContext)?e.fallbackContext:void 0,A=b(e.onWarn)?e.onWarn:f,P=e,q=w(P.__datetimeFormatters)?P.__datetimeFormatters:new Map,R=w(P.__numberFormatters)?P.__numberFormatters:new Map,N=w(P.__meta)?P.__meta:{};We++;const M={version:t,cid:We,locale:n,fallbackLocale:o,messages:r,modifiers:a,pluralRules:s,missing:u,missingWarn:d,fallbackWarn:v,fallbackFormat:h,unresolving:m,postTranslation:S,processor:k,warnHtmlMessage:E,escapeParameter:C,messageCompiler:T,messageResolver:O,localeFallbacker:F,fallbackContext:L,onWarn:A,__meta:N};return M.datetimeFormats=l,M.numberFormats=i,M.__datetimeFormatters=q,M.__numberFormatters=R,M}function Ke(e,t,n,o,r){const{missing:l,onWarn:i}=e;if(null!==l){const o=l(e,n,t,r);return y(o)?o:t}return t}function Ye(e,t,n){const o=e;o.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}const Je=e=>e;let Xe=Object.create(null);function Ge(e,t={}){{const n=t.onCacheKey||Je,o=n(e),r=Xe[o];if(r)return r;let l=!1;const i=t.onError||O;t.onError=e=>{l=!0,i(e)};const{code:a}=oe(e,t),s=new Function(`return ${a}`)();return l?s:Xe[o]=s}}let Qe=C.__EXTEND_POINT__;const et=()=>++Qe,tt={INVALID_ARGUMENT:Qe,INVALID_DATE_ARGUMENT:et(),INVALID_ISO_DATE_ARGUMENT:et(),__EXTEND_POINT__:et()};function nt(e){return T(e,null,void 0)}tt.INVALID_ARGUMENT,tt.INVALID_DATE_ARGUMENT,tt.INVALID_ISO_DATE_ARGUMENT;const ot=()=>"",rt=e=>b(e);function lt(e,...t){const{fallbackFormat:n,postTranslation:o,unresolving:r,messageCompiler:l,fallbackLocale:i,messages:a}=e,[s,u]=ct(...t),c=_(u.missingWarn)?u.missingWarn:e.missingWarn,d=_(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,f=_(u.escapeParameter)?u.escapeParameter:e.escapeParameter,p=!!u.resolvedMessage,v=y(u.default)||_(u.default)?_(u.default)?l?s:()=>s:u.default:n?l?s:()=>s:"",h=n||""!==v,m=y(u.locale)?u.locale:e.locale;f&&it(u);let[g,b,w]=p?[s,m,a[m]||{}]:at(e,s,m,i,d,c),S=g,k=s;if(p||y(S)||rt(S)||h&&(S=v,k=S),!p&&(!y(S)&&!rt(S)||!y(b)))return r?qe:s;let x=!1;const E=()=>{x=!0},C=rt(S)?S:st(e,s,b,S,k,E);if(x)return S;const T=ft(e,b,w,u),O=ke(T),F=ut(e,C,O),L=o?o(F,s):F;return L}function it(e){g(e.list)?e.list=e.list.map((e=>y(e)?v(e):e)):w(e.named)&&Object.keys(e.named).forEach((t=>{y(e.named[t])&&(e.named[t]=v(e.named[t]))}))}function at(e,t,n,o,r,l){const{messages:i,onWarn:a,messageResolver:s,localeFallbacker:u}=e,c=u(e,o,n);let d,f={},p=null,v=n,h=null;const m="translate";for(let g=0;g<c.length;g++){d=h=c[g],f=i[d]||{};if(null===(p=s(f,t))&&(p=f[t]),y(p)||b(p))break;const n=Ke(e,t,d,l,m);n!==t&&(p=n),v=h}return[p,d,f]}function st(e,t,n,o,r,l){const{messageCompiler:i,warnHtmlMessage:a}=e;if(rt(o)){const e=o;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==i){const e=()=>o;return e.locale=n,e.key=t,e}const s=i(o,dt(e,n,r,o,a,l));return s.locale=n,s.key=t,s.source=o,s}function ut(e,t,n){const o=t(n);return o}function ct(...e){const[t,n,o]=e,r={};if(!y(t)&&!s(t)&&!rt(t))throw nt(tt.INVALID_ARGUMENT);const l=s(t)?String(t):(rt(t),t);return s(n)?r.plural=n:y(n)?r.default=n:x(n)&&!d(n)?r.named=n:g(n)&&(r.list=n),s(o)?r.plural=o:y(o)?r.default=o:x(o)&&p(r,o),[l,r]}function dt(e,t,n,o,r,l){return{warnHtmlMessage:r,onError:e=>{throw l&&l(e),e},onCacheKey:e=>i(t,n,e)}}function ft(e,t,n,o){const{modifiers:r,pluralRules:l,messageResolver:i,fallbackLocale:a,fallbackWarn:u,missingWarn:c,fallbackContext:d}=e,f=o=>{let r=i(n,o);if(null==r&&d){const[,,e]=at(d,o,t,a,u,c);r=i(e,o)}if(y(r)){let n=!1;const l=()=>{n=!0},i=st(e,o,t,r,o,l);return n?ot:i}return rt(r)?r:ot},p={locale:t,modifiers:r,pluralRules:l,messages:f};return e.processor&&(p.processor=e.processor),o.list&&(p.list=o.list),o.named&&(p.named=o.named),s(o.plural)&&(p.pluralIndex=o.plural),p}const pt="undefined"!==typeof Intl;pt&&Intl.DateTimeFormat,pt&&Intl.NumberFormat;function vt(e,...t){const{datetimeFormats:n,unresolving:o,fallbackLocale:r,onWarn:l,localeFallbacker:i}=e,{__datetimeFormatters:a}=e;const[s,u,c,f]=mt(...t),v=_(c.missingWarn)?c.missingWarn:e.missingWarn,h=(_(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,!!c.part),m=y(c.locale)?c.locale:e.locale,g=i(e,r,m);if(!y(s)||""===s)return new Intl.DateTimeFormat(m,f).format(u);let b,w={},S=null,k=m,E=null;const C="datetime format";for(let d=0;d<g.length;d++){if(b=E=g[d],w=n[b]||{},S=w[s],x(S))break;Ke(e,s,b,v,C),k=E}if(!x(S)||!y(b))return o?qe:s;let T=`${b}__${s}`;d(f)||(T=`${T}__${JSON.stringify(f)}`);let O=a.get(T);return O||(O=new Intl.DateTimeFormat(b,p({},S,f)),a.set(T,O)),h?O.formatToParts(u):O.format(u)}const ht=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function mt(...e){const[t,n,o,r]=e,l={};let i,a={};if(y(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw nt(tt.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();i=new Date(n);try{i.toISOString()}catch(c){throw nt(tt.INVALID_ISO_DATE_ARGUMENT)}}else if(u(t)){if(isNaN(t.getTime()))throw nt(tt.INVALID_DATE_ARGUMENT);i=t}else{if(!s(t))throw nt(tt.INVALID_ARGUMENT);i=t}return y(n)?l.key=n:x(n)&&Object.keys(n).forEach((e=>{ht.includes(e)?a[e]=n[e]:l[e]=n[e]})),y(o)?l.locale=o:x(o)&&(a=o),x(r)&&(a=r),[l.key||"",i,l,a]}function gt(e,t,n){const o=e;for(const r in n){const e=`${t}__${r}`;o.__datetimeFormatters.has(e)&&o.__datetimeFormatters.delete(e)}}function bt(e,...t){const{numberFormats:n,unresolving:o,fallbackLocale:r,onWarn:l,localeFallbacker:i}=e,{__numberFormatters:a}=e;const[s,u,c,f]=_t(...t),v=_(c.missingWarn)?c.missingWarn:e.missingWarn,h=(_(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,!!c.part),m=y(c.locale)?c.locale:e.locale,g=i(e,r,m);if(!y(s)||""===s)return new Intl.NumberFormat(m,f).format(u);let b,w={},S=null,k=m,E=null;const C="number format";for(let d=0;d<g.length;d++){if(b=E=g[d],w=n[b]||{},S=w[s],x(S))break;Ke(e,s,b,v,C),k=E}if(!x(S)||!y(b))return o?qe:s;let T=`${b}__${s}`;d(f)||(T=`${T}__${JSON.stringify(f)}`);let O=a.get(T);return O||(O=new Intl.NumberFormat(b,p({},S,f)),a.set(T,O)),h?O.formatToParts(u):O.format(u)}const yt=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function _t(...e){const[t,n,o,r]=e,l={};let i={};if(!s(t))throw nt(tt.INVALID_ARGUMENT);const a=t;return y(n)?l.key=n:x(n)&&Object.keys(n).forEach((e=>{yt.includes(e)?i[e]=n[e]:l[e]=n[e]})),y(o)?l.locale=o:x(o)&&(i=o),x(r)&&(i=r),[l.key||"",a,l,i]}function wt(e,t,n){const o=e;for(const r in n){const e=`${t}__${r}`;o.__numberFormatters.has(e)&&o.__numberFormatters.delete(e)}}var St=n(9835),kt=n(499);
/*!
  * vue-i18n v9.2.2
  * (c) 2022 kazuya kawaguchi
  * Released under the MIT License.
  */
const xt="9.2.2";function Et(){}let Ct=Ce.__EXTEND_POINT__;const Tt=()=>++Ct,Ot={FALLBACK_TO_ROOT:Ct,NOT_SUPPORTED_PRESERVE:Tt(),NOT_SUPPORTED_FORMATTER:Tt(),NOT_SUPPORTED_PRESERVE_DIRECTIVE:Tt(),NOT_SUPPORTED_GET_CHOICE_INDEX:Tt(),COMPONENT_NAME_LEGACY_COMPATIBLE:Tt(),NOT_FOUND_PARENT_SCOPE:Tt()};Ot.FALLBACK_TO_ROOT,Ot.NOT_SUPPORTED_PRESERVE,Ot.NOT_SUPPORTED_FORMATTER,Ot.NOT_SUPPORTED_PRESERVE_DIRECTIVE,Ot.NOT_SUPPORTED_GET_CHOICE_INDEX,Ot.COMPONENT_NAME_LEGACY_COMPATIBLE,Ot.NOT_FOUND_PARENT_SCOPE;let Ft=C.__EXTEND_POINT__;const Lt=()=>++Ft,At={UNEXPECTED_RETURN_TYPE:Ft,INVALID_ARGUMENT:Lt(),MUST_BE_CALL_SETUP_TOP:Lt(),NOT_INSLALLED:Lt(),NOT_AVAILABLE_IN_LEGACY_MODE:Lt(),REQUIRED_VALUE:Lt(),INVALID_VALUE:Lt(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:Lt(),NOT_INSLALLED_WITH_PROVIDE:Lt(),UNEXPECTED_ERROR:Lt(),NOT_COMPATIBLE_LEGACY_VUE_I18N:Lt(),BRIDGE_SUPPORT_VUE_2_ONLY:Lt(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:Lt(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:Lt(),__EXTEND_POINT__:Lt()};function Pt(e,...t){return T(e,null,void 0)}At.UNEXPECTED_RETURN_TYPE,At.INVALID_ARGUMENT,At.MUST_BE_CALL_SETUP_TOP,At.NOT_INSLALLED,At.UNEXPECTED_ERROR,At.NOT_AVAILABLE_IN_LEGACY_MODE,At.REQUIRED_VALUE,At.INVALID_VALUE,At.CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN,At.NOT_INSLALLED_WITH_PROVIDE,At.NOT_COMPATIBLE_LEGACY_VUE_I18N,At.BRIDGE_SUPPORT_VUE_2_ONLY,At.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION,At.NOT_AVAILABLE_COMPOSITION_IN_LEGACY;const qt=l("__transrateVNode"),Rt=l("__datetimeParts"),Nt=l("__numberParts"),Mt=l("__setPluralRules");l("__intlifyMeta");const It=l("__injectWithOption");function $t(e){if(!w(e))return e;for(const t in e)if(m(e,t))if(t.includes(".")){const n=t.split("."),o=n.length-1;let r=e;for(let e=0;e<o;e++)n[e]in r||(r[n[e]]={}),r=r[n[e]];r[n[o]]=e[t],delete e[t],w(r[n[o]])&&$t(r[n[o]])}else w(e[t])&&$t(e[t]);return e}function Dt(e,t){const{messages:n,__i18n:o,messageResolver:r,flatJson:l}=t,i=x(n)?n:g(o)?{}:{[e]:{}};if(g(o)&&o.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(i[t]=i[t]||{},jt(n,i[t])):jt(n,i)}else y(e)&&jt(JSON.parse(e),i)})),null==r&&l)for(const a in i)m(i,a)&&$t(i[a]);return i}const Vt=e=>!w(e)||g(e);function jt(e,t){if(Vt(e)||Vt(t))throw Pt(At.INVALID_VALUE);for(const n in e)m(e,n)&&(Vt(e[n])||Vt(t[n])?t[n]=e[n]:jt(e[n],t[n]))}function Bt(e){return e.type}function Ht(e,t,n){let o=w(t.messages)?t.messages:{};"__i18nGlobal"in n&&(o=Dt(e.locale.value,{messages:o,__i18n:n.__i18nGlobal}));const r=Object.keys(o);if(r.length&&r.forEach((t=>{e.mergeLocaleMessage(t,o[t])})),w(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach((n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])}))}if(w(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach((n=>{e.mergeNumberFormat(n,t.numberFormats[n])}))}}function zt(e){return(0,St.Wm)(St.xv,null,e,0)}let Ut=0;function Wt(e){return(t,n,o,r)=>e(n,o,(0,St.FN)()||void 0,r)}function Zt(e={},t){const{__root:n}=e,r=void 0===n;let l=!_(e.inheritLocale)||e.inheritLocale;const i=(0,kt.iH)(n&&l?n.locale.value:y(e.locale)?e.locale:Re),a=(0,kt.iH)(n&&l?n.fallbackLocale.value:y(e.fallbackLocale)||g(e.fallbackLocale)||x(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:i.value),u=(0,kt.iH)(Dt(i.value,e)),d=(0,kt.iH)(x(e.datetimeFormats)?e.datetimeFormats:{[i.value]:{}}),f=(0,kt.iH)(x(e.numberFormats)?e.numberFormats:{[i.value]:{}});let v=n?n.missingWarn:!_(e.missingWarn)&&!c(e.missingWarn)||e.missingWarn,h=n?n.fallbackWarn:!_(e.fallbackWarn)&&!c(e.fallbackWarn)||e.fallbackWarn,m=n?n.fallbackRoot:!_(e.fallbackRoot)||e.fallbackRoot,S=!!e.fallbackFormat,k=b(e.missing)?e.missing:null,E=b(e.missing)?Wt(e.missing):null,C=b(e.postTranslation)?e.postTranslation:null,T=n?n.warnHtmlMessage:!_(e.warnHtmlMessage)||e.warnHtmlMessage,O=!!e.escapeParameter;const F=n?n.modifiers:x(e.modifiers)?e.modifiers:{};let L,A=e.pluralRules||n&&n.pluralRules;const P=()=>{r&&Ue(null);const t={version:xt,locale:i.value,fallbackLocale:a.value,messages:u.value,modifiers:F,pluralRules:A,missing:null===E?void 0:E,missingWarn:v,fallbackWarn:h,fallbackFormat:S,unresolving:!0,postTranslation:null===C?void 0:C,warnHtmlMessage:T,escapeParameter:O,messageResolver:e.messageResolver,__meta:{framework:"vue"}};t.datetimeFormats=d.value,t.numberFormats=f.value,t.__datetimeFormatters=x(L)?L.__datetimeFormatters:void 0,t.__numberFormatters=x(L)?L.__numberFormatters:void 0;const n=Ze(t);return r&&Ue(n),n};function q(){return[i.value,a.value,u.value,d.value,f.value]}L=P(),Ye(L,i.value,a.value);const R=(0,St.Fl)({get:()=>i.value,set:e=>{i.value=e,L.locale=i.value}}),N=(0,St.Fl)({get:()=>a.value,set:e=>{a.value=e,L.fallbackLocale=a.value,Ye(L,i.value,e)}}),M=(0,St.Fl)((()=>u.value)),I=(0,St.Fl)((()=>d.value)),$=(0,St.Fl)((()=>f.value));function D(){return b(C)?C:null}function V(e){C=e,L.postTranslation=e}function j(){return k}function B(e){null!==e&&(E=Wt(e)),k=e,L.missing=E}const H=(e,t,o,r,l,i)=>{let a;if(q(),a=e(L),s(a)&&a===qe){const[e,o]=t();return n&&m?r(n):l(e)}if(i(a))return a;throw Pt(At.UNEXPECTED_RETURN_TYPE)};function z(...e){return H((t=>Reflect.apply(lt,null,[t,...e])),(()=>ct(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>y(e)))}function U(...e){const[t,n,o]=e;if(o&&!w(o))throw Pt(At.INVALID_ARGUMENT);return z(t,n,p({resolvedMessage:!0},o||{}))}function W(...e){return H((t=>Reflect.apply(vt,null,[t,...e])),(()=>mt(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>Ne),(e=>y(e)))}function Z(...e){return H((t=>Reflect.apply(bt,null,[t,...e])),(()=>_t(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>Ne),(e=>y(e)))}function K(e){return e.map((e=>y(e)||s(e)||_(e)?zt(String(e)):e))}const Y=e=>e,J={normalize:K,interpolate:Y,type:"vnode"};function X(...e){return H((t=>{let n;const o=t;try{o.processor=J,n=Reflect.apply(lt,null,[o,...e])}finally{o.processor=null}return n}),(()=>ct(...e)),"translate",(t=>t[qt](...e)),(e=>[zt(e)]),(e=>g(e)))}function G(...e){return H((t=>Reflect.apply(bt,null,[t,...e])),(()=>_t(...e)),"number format",(t=>t[Nt](...e)),(()=>[]),(e=>y(e)||g(e)))}function Q(...e){return H((t=>Reflect.apply(vt,null,[t,...e])),(()=>mt(...e)),"datetime format",(t=>t[Rt](...e)),(()=>[]),(e=>y(e)||g(e)))}function ee(e){A=e,L.pluralRules=A}function te(e,t){const n=y(t)?t:i.value,o=re(n);return null!==L.messageResolver(o,e)}function ne(e){let t=null;const n=Oe(L,a.value,i.value);for(let o=0;o<n.length;o++){const r=u.value[n[o]]||{},l=L.messageResolver(r,e);if(null!=l){t=l;break}}return t}function oe(e){const t=ne(e);return null!=t?t:n&&n.tm(e)||{}}function re(e){return u.value[e]||{}}function le(e,t){u.value[e]=t,L.messages=u.value}function ie(e,t){u.value[e]=u.value[e]||{},jt(t,u.value[e]),L.messages=u.value}function ae(e){return d.value[e]||{}}function se(e,t){d.value[e]=t,L.datetimeFormats=d.value,gt(L,e,t)}function ue(e,t){d.value[e]=p(d.value[e]||{},t),L.datetimeFormats=d.value,gt(L,e,t)}function ce(e){return f.value[e]||{}}function de(e,t){f.value[e]=t,L.numberFormats=f.value,wt(L,e,t)}function fe(e,t){f.value[e]=p(f.value[e]||{},t),L.numberFormats=f.value,wt(L,e,t)}Ut++,n&&o&&((0,St.YP)(n.locale,(e=>{l&&(i.value=e,L.locale=e,Ye(L,i.value,a.value))})),(0,St.YP)(n.fallbackLocale,(e=>{l&&(a.value=e,L.fallbackLocale=e,Ye(L,i.value,a.value))})));const pe={id:Ut,locale:R,fallbackLocale:N,get inheritLocale(){return l},set inheritLocale(e){l=e,e&&n&&(i.value=n.locale.value,a.value=n.fallbackLocale.value,Ye(L,i.value,a.value))},get availableLocales(){return Object.keys(u.value).sort()},messages:M,get modifiers(){return F},get pluralRules(){return A||{}},get isGlobal(){return r},get missingWarn(){return v},set missingWarn(e){v=e,L.missingWarn=v},get fallbackWarn(){return h},set fallbackWarn(e){h=e,L.fallbackWarn=h},get fallbackRoot(){return m},set fallbackRoot(e){m=e},get fallbackFormat(){return S},set fallbackFormat(e){S=e,L.fallbackFormat=S},get warnHtmlMessage(){return T},set warnHtmlMessage(e){T=e,L.warnHtmlMessage=e},get escapeParameter(){return O},set escapeParameter(e){O=e,L.escapeParameter=e},t:z,getLocaleMessage:re,setLocaleMessage:le,mergeLocaleMessage:ie,getPostTranslationHandler:D,setPostTranslationHandler:V,getMissingHandler:j,setMissingHandler:B,[Mt]:ee};return pe.datetimeFormats=I,pe.numberFormats=$,pe.rt=U,pe.te=te,pe.tm=oe,pe.d=W,pe.n=Z,pe.getDateTimeFormat=ae,pe.setDateTimeFormat=se,pe.mergeDateTimeFormat=ue,pe.getNumberFormat=ce,pe.setNumberFormat=de,pe.mergeNumberFormat=fe,pe[It]=e.__injectWithOption,pe[qt]=X,pe[Rt]=Q,pe[Nt]=G,pe}function Kt(e){const t=y(e.locale)?e.locale:Re,n=y(e.fallbackLocale)||g(e.fallbackLocale)||x(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,o=b(e.missing)?e.missing:void 0,r=!_(e.silentTranslationWarn)&&!c(e.silentTranslationWarn)||!e.silentTranslationWarn,l=!_(e.silentFallbackWarn)&&!c(e.silentFallbackWarn)||!e.silentFallbackWarn,i=!_(e.fallbackRoot)||e.fallbackRoot,a=!!e.formatFallbackMessages,s=x(e.modifiers)?e.modifiers:{},u=e.pluralizationRules,d=b(e.postTranslation)?e.postTranslation:void 0,f=!y(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,v=!!e.escapeParameterHtml,h=!_(e.sync)||e.sync;let m=e.messages;if(x(e.sharedMessages)){const t=e.sharedMessages,n=Object.keys(t);m=n.reduce(((e,n)=>{const o=e[n]||(e[n]={});return p(o,t[n]),e}),m||{})}const{__i18n:w,__root:S,__injectWithOption:k}=e,E=e.datetimeFormats,C=e.numberFormats,T=e.flatJson;return{locale:t,fallbackLocale:n,messages:m,flatJson:T,datetimeFormats:E,numberFormats:C,missing:o,missingWarn:r,fallbackWarn:l,fallbackRoot:i,fallbackFormat:a,modifiers:s,pluralRules:u,postTranslation:d,warnHtmlMessage:f,escapeParameter:v,messageResolver:e.messageResolver,inheritLocale:h,__i18n:w,__root:S,__injectWithOption:k}}function Yt(e={},t){{const t=Zt(Kt(e)),n={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate(){return[]}}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return _(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=_(e)?!e:e},get silentFallbackWarn(){return _(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=_(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[n,o,r]=e,l={};let i=null,a=null;if(!y(n))throw Pt(At.INVALID_ARGUMENT);const s=n;return y(o)?l.locale=o:g(o)?i=o:x(o)&&(a=o),g(r)?i=r:x(r)&&(a=r),Reflect.apply(t.t,t,[s,i||a||{},l])},rt(...e){return Reflect.apply(t.rt,t,[...e])},tc(...e){const[n,o,r]=e,l={plural:1};let i=null,a=null;if(!y(n))throw Pt(At.INVALID_ARGUMENT);const u=n;return y(o)?l.locale=o:s(o)?l.plural=o:g(o)?i=o:x(o)&&(a=o),y(r)?l.locale=r:g(r)?i=r:x(r)&&(a=r),Reflect.apply(t.t,t,[u,i||a||{},l])},te(e,n){return t.te(e,n)},tm(e){return t.tm(e)},getLocaleMessage(e){return t.getLocaleMessage(e)},setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d(...e){return Reflect.apply(t.d,t,[...e])},getDateTimeFormat(e){return t.getDateTimeFormat(e)},setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n(...e){return Reflect.apply(t.n,t,[...e])},getNumberFormat(e){return t.getNumberFormat(e)},setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)},getChoiceIndex(e,t){return-1},__onComponentInstanceCreated(t){const{componentInstanceCreatedListener:o}=e;o&&o(t,n)}};return n}}const Jt={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function Xt({slots:e},t){if(1===t.length&&"default"===t[0]){const t=e.default?e.default():[];return t.reduce(((e,t)=>[...e,...g(t.children)?t.children:[t]]),[])}return t.reduce(((t,n)=>{const o=e[n];return o&&(t[n]=o()),t}),{})}function Gt(e){return St.HY}const Qt={name:"i18n-t",props:p({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>s(e)||!isNaN(e)}},Jt),setup(e,t){const{slots:n,attrs:o}=t,r=e.i18n||vn({useScope:e.scope,__useComponent:!0});return()=>{const l=Object.keys(n).filter((e=>"_"!==e)),i={};e.locale&&(i.locale=e.locale),void 0!==e.plural&&(i.plural=y(e.plural)?+e.plural:e.plural);const a=Xt(t,l),s=r[qt](e.keypath,a,i),u=p({},o),c=y(e.tag)||w(e.tag)?e.tag:Gt();return(0,St.h)(c,u,s)}}};function en(e){return g(e)&&!y(e[0])}function tn(e,t,n,o){const{slots:r,attrs:l}=t;return()=>{const t={part:!0};let i={};e.locale&&(t.locale=e.locale),y(e.format)?t.key=e.format:w(e.format)&&(y(e.format.key)&&(t.key=e.format.key),i=Object.keys(e.format).reduce(((t,o)=>n.includes(o)?p({},t,{[o]:e.format[o]}):t),{}));const a=o(e.value,t,i);let s=[t.key];g(a)?s=a.map(((e,t)=>{const n=r[e.type],o=n?n({[e.type]:e.value,index:t,parts:a}):[e.value];return en(o)&&(o[0].key=`${e.type}-${t}`),o})):y(a)&&(s=[a]);const u=p({},l),c=y(e.tag)||w(e.tag)?e.tag:Gt();return(0,St.h)(c,u,s)}}const nn={name:"i18n-n",props:p({value:{type:Number,required:!0},format:{type:[String,Object]}},Jt),setup(e,t){const n=e.i18n||vn({useScope:"parent",__useComponent:!0});return tn(e,t,yt,((...e)=>n[Nt](...e)))}},on={name:"i18n-d",props:p({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Jt),setup(e,t){const n=e.i18n||vn({useScope:"parent",__useComponent:!0});return tn(e,t,ht,((...e)=>n[Rt](...e)))}};function rn(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const o=n.__getInstance(t);return null!=o?o.__composer:e.global.__composer}}function ln(e){const t=t=>{const{instance:n,modifiers:o,value:r}=t;if(!n||!n.$)throw Pt(At.UNEXPECTED_ERROR);const l=rn(e,n.$);const i=an(r);return[Reflect.apply(l.t,l,[...sn(i)]),l]},n=(n,r)=>{const[l,i]=t(r);o&&e.global===i&&(n.__i18nWatcher=(0,St.YP)(i.locale,(()=>{r.instance&&r.instance.$forceUpdate()}))),n.__composer=i,n.textContent=l},r=e=>{o&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},l=(e,{value:t})=>{if(e.__composer){const n=e.__composer,o=an(t);e.textContent=Reflect.apply(n.t,n,[...sn(o)])}},i=e=>{const[n]=t(e);return{textContent:n}};return{created:n,unmounted:r,beforeUpdate:l,getSSRProps:i}}function an(e){if(y(e))return{path:e};if(x(e)){if(!("path"in e))throw Pt(At.REQUIRED_VALUE,"path");return e}throw Pt(At.INVALID_VALUE)}function sn(e){const{path:t,locale:n,args:o,choice:r,plural:l}=e,i={},a=o||{};return y(n)&&(i.locale=n),s(r)&&(i.plural=r),s(l)&&(i.plural=l),[t,a,i]}function un(e,t,...n){const o=x(n[0])?n[0]:{},r=!!o.useI18nComponentName,l=!_(o.globalInstall)||o.globalInstall;l&&(e.component(r?"i18n":Qt.name,Qt),e.component(nn.name,nn),e.component(on.name,on)),e.directive("t",ln(t))}function cn(e,t,n){return{beforeCreate(){const o=(0,St.FN)();if(!o)throw Pt(At.UNEXPECTED_ERROR);const r=this.$options;if(r.i18n){const n=r.i18n;r.__i18n&&(n.__i18n=r.__i18n),n.__root=t,this===this.$root?this.$i18n=dn(e,n):(n.__injectWithOption=!0,this.$i18n=Yt(n))}else r.__i18n?this===this.$root?this.$i18n=dn(e,r):this.$i18n=Yt({__i18n:r.__i18n,__injectWithOption:!0,__root:t}):this.$i18n=e;r.__i18nGlobal&&Ht(t,r,r),e.__onComponentInstanceCreated(this.$i18n),n.__setInstance(o,this.$i18n),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e)},mounted(){0},unmounted(){const e=(0,St.FN)();if(!e)throw Pt(At.UNEXPECTED_ERROR);delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__deleteInstance(e),delete this.$i18n}}}function dn(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Mt](t.pluralizationRules||e.pluralizationRules);const n=Dt(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach((t=>e.mergeLocaleMessage(t,n[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((n=>e.mergeNumberFormat(n,t.numberFormats[n]))),e}const fn=l("global-vue-i18n");function pn(e={},t){const n=!_(e.legacy)||e.legacy,o=!_(e.globalInjection)||e.globalInjection,r=!n||!!e.allowComposition,i=new Map,[a,s]=hn(e,n),u=l("");function c(e){return i.get(e)||null}function d(e,t){i.set(e,t)}function f(e){i.delete(e)}{const e={get mode(){return n?"legacy":"composition"},get allowComposition(){return r},async install(t,...r){t.__VUE_I18N_SYMBOL__=u,t.provide(t.__VUE_I18N_SYMBOL__,e),!n&&o&&xn(t,e.global),un(t,e,...r),n&&t.mixin(cn(s,s.__composer,e));const l=t.unmount;t.unmount=()=>{e.dispose(),l()}},get global(){return s},dispose(){a.stop()},__instances:i,__getInstance:c,__setInstance:d,__deleteInstance:f};return e}}function vn(e={}){const t=(0,St.FN)();if(null==t)throw Pt(At.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Pt(At.NOT_INSLALLED);const n=mn(t),o=bn(n),r=Bt(t),l=gn(e,r);if("legacy"===n.mode&&!e.__useComponent){if(!n.allowComposition)throw Pt(At.NOT_AVAILABLE_IN_LEGACY_MODE);return wn(t,l,o,e)}if("global"===l)return Ht(o,e,r),o;if("parent"===l){let r=yn(n,t,e.__useComponent);return null==r&&(r=o),r}const i=n;let a=i.__getInstance(t);if(null==a){const n=p({},e);"__i18n"in r&&(n.__i18n=r.__i18n),o&&(n.__root=o),a=Zt(n),_n(i,t,a),i.__setInstance(t,a)}return a}function hn(e,t,n){const o=(0,kt.B)();{const n=t?o.run((()=>Yt(e))):o.run((()=>Zt(e)));if(null==n)throw Pt(At.UNEXPECTED_ERROR);return[o,n]}}function mn(e){{const t=(0,St.f3)(e.isCE?fn:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw Pt(e.isCE?At.NOT_INSLALLED_WITH_PROVIDE:At.UNEXPECTED_ERROR);return t}}function gn(e,t){return d(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function bn(e){return"composition"===e.mode?e.global:e.global.__composer}function yn(e,t,n=!1){let o=null;const r=t.root;let l=t.parent;while(null!=l){const t=e;if("composition"===e.mode)o=t.__getInstance(l);else{const e=t.__getInstance(l);null!=e&&(o=e.__composer,n&&o&&!o[It]&&(o=null))}if(null!=o)break;if(r===l)break;l=l.parent}return o}function _n(e,t,n){(0,St.bv)((()=>{0}),t),(0,St.Ah)((()=>{e.__deleteInstance(t)}),t)}function wn(e,t,n,o={}){const r="local"===t,l=(0,kt.XI)(null);if(r&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Pt(At.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const i=!_(o.inheritLocale)||o.inheritLocale,a=(0,kt.iH)(r&&i?n.locale.value:y(o.locale)?o.locale:Re),s=(0,kt.iH)(r&&i?n.fallbackLocale.value:y(o.fallbackLocale)||g(o.fallbackLocale)||x(o.fallbackLocale)||!1===o.fallbackLocale?o.fallbackLocale:a.value),u=(0,kt.iH)(Dt(a.value,o)),d=(0,kt.iH)(x(o.datetimeFormats)?o.datetimeFormats:{[a.value]:{}}),f=(0,kt.iH)(x(o.numberFormats)?o.numberFormats:{[a.value]:{}}),p=r?n.missingWarn:!_(o.missingWarn)&&!c(o.missingWarn)||o.missingWarn,v=r?n.fallbackWarn:!_(o.fallbackWarn)&&!c(o.fallbackWarn)||o.fallbackWarn,h=r?n.fallbackRoot:!_(o.fallbackRoot)||o.fallbackRoot,m=!!o.fallbackFormat,w=b(o.missing)?o.missing:null,S=b(o.postTranslation)?o.postTranslation:null,k=r?n.warnHtmlMessage:!_(o.warnHtmlMessage)||o.warnHtmlMessage,E=!!o.escapeParameter,C=r?n.modifiers:x(o.modifiers)?o.modifiers:{},T=o.pluralRules||r&&n.pluralRules;function O(){return[a.value,s.value,u.value,d.value,f.value]}const F=(0,St.Fl)({get:()=>l.value?l.value.locale.value:a.value,set:e=>{l.value&&(l.value.locale.value=e),a.value=e}}),L=(0,St.Fl)({get:()=>l.value?l.value.fallbackLocale.value:s.value,set:e=>{l.value&&(l.value.fallbackLocale.value=e),s.value=e}}),A=(0,St.Fl)((()=>l.value?l.value.messages.value:u.value)),P=(0,St.Fl)((()=>d.value)),q=(0,St.Fl)((()=>f.value));function R(){return l.value?l.value.getPostTranslationHandler():S}function N(e){l.value&&l.value.setPostTranslationHandler(e)}function M(){return l.value?l.value.getMissingHandler():w}function I(e){l.value&&l.value.setMissingHandler(e)}function $(e){return O(),e()}function D(...e){return l.value?$((()=>Reflect.apply(l.value.t,null,[...e]))):$((()=>""))}function V(...e){return l.value?Reflect.apply(l.value.rt,null,[...e]):""}function j(...e){return l.value?$((()=>Reflect.apply(l.value.d,null,[...e]))):$((()=>""))}function B(...e){return l.value?$((()=>Reflect.apply(l.value.n,null,[...e]))):$((()=>""))}function H(e){return l.value?l.value.tm(e):{}}function z(e,t){return!!l.value&&l.value.te(e,t)}function U(e){return l.value?l.value.getLocaleMessage(e):{}}function W(e,t){l.value&&(l.value.setLocaleMessage(e,t),u.value[e]=t)}function Z(e,t){l.value&&l.value.mergeLocaleMessage(e,t)}function K(e){return l.value?l.value.getDateTimeFormat(e):{}}function Y(e,t){l.value&&(l.value.setDateTimeFormat(e,t),d.value[e]=t)}function J(e,t){l.value&&l.value.mergeDateTimeFormat(e,t)}function X(e){return l.value?l.value.getNumberFormat(e):{}}function G(e,t){l.value&&(l.value.setNumberFormat(e,t),f.value[e]=t)}function Q(e,t){l.value&&l.value.mergeNumberFormat(e,t)}const ee={get id(){return l.value?l.value.id:-1},locale:F,fallbackLocale:L,messages:A,datetimeFormats:P,numberFormats:q,get inheritLocale(){return l.value?l.value.inheritLocale:i},set inheritLocale(e){l.value&&(l.value.inheritLocale=e)},get availableLocales(){return l.value?l.value.availableLocales:Object.keys(u.value)},get modifiers(){return l.value?l.value.modifiers:C},get pluralRules(){return l.value?l.value.pluralRules:T},get isGlobal(){return!!l.value&&l.value.isGlobal},get missingWarn(){return l.value?l.value.missingWarn:p},set missingWarn(e){l.value&&(l.value.missingWarn=e)},get fallbackWarn(){return l.value?l.value.fallbackWarn:v},set fallbackWarn(e){l.value&&(l.value.missingWarn=e)},get fallbackRoot(){return l.value?l.value.fallbackRoot:h},set fallbackRoot(e){l.value&&(l.value.fallbackRoot=e)},get fallbackFormat(){return l.value?l.value.fallbackFormat:m},set fallbackFormat(e){l.value&&(l.value.fallbackFormat=e)},get warnHtmlMessage(){return l.value?l.value.warnHtmlMessage:k},set warnHtmlMessage(e){l.value&&(l.value.warnHtmlMessage=e)},get escapeParameter(){return l.value?l.value.escapeParameter:E},set escapeParameter(e){l.value&&(l.value.escapeParameter=e)},t:D,getPostTranslationHandler:R,setPostTranslationHandler:N,getMissingHandler:M,setMissingHandler:I,rt:V,d:j,n:B,tm:H,te:z,getLocaleMessage:U,setLocaleMessage:W,mergeLocaleMessage:Z,getDateTimeFormat:K,setDateTimeFormat:Y,mergeDateTimeFormat:J,getNumberFormat:X,setNumberFormat:G,mergeNumberFormat:Q};function te(e){e.locale.value=a.value,e.fallbackLocale.value=s.value,Object.keys(u.value).forEach((t=>{e.mergeLocaleMessage(t,u.value[t])})),Object.keys(d.value).forEach((t=>{e.mergeDateTimeFormat(t,d.value[t])})),Object.keys(f.value).forEach((t=>{e.mergeNumberFormat(t,f.value[t])})),e.escapeParameter=E,e.fallbackFormat=m,e.fallbackRoot=h,e.fallbackWarn=v,e.missingWarn=p,e.warnHtmlMessage=k}return(0,St.wF)((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Pt(At.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const n=l.value=e.proxy.$i18n.__composer;"global"===t?(a.value=n.locale.value,s.value=n.fallbackLocale.value,u.value=n.messages.value,d.value=n.datetimeFormats.value,f.value=n.numberFormats.value):r&&te(n)})),ee}const Sn=["locale","fallbackLocale","availableLocales"],kn=["t","rt","d","n","tm"];function xn(e,t){const n=Object.create(null);Sn.forEach((e=>{const o=Object.getOwnPropertyDescriptor(t,e);if(!o)throw Pt(At.UNEXPECTED_ERROR);const r=(0,kt.dq)(o.value)?{get(){return o.value.value},set(e){o.value.value=e}}:{get(){return o.get&&o.get()}};Object.defineProperty(n,e,r)})),e.config.globalProperties.$i18n=n,kn.forEach((n=>{const o=Object.getOwnPropertyDescriptor(t,n);if(!o||!o.value)throw Pt(At.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${n}`,o)}))}je(Ge),Be(ve),He(Oe),Et()},1639:(e,t)=>{"use strict";t.Z=(e,t)=>{const n=e.__vccOpts||e;for(const[o,r]of t)n[o]=r;return n}},3100:(e,t,n)=>{"use strict";n.d(t,{MT:()=>ee,nv:()=>re,rn:()=>oe});var o=n(9835),r=n(499);function l(){return i().__VUE_DEVTOOLS_GLOBAL_HOOK__}function i(){return"undefined"!==typeof navigator&&"undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:{}}const a="function"===typeof Proxy,s="devtools-plugin:setup",u="plugin:settings:set";let c,d;function f(){var e;return void 0!==c||("undefined"!==typeof window&&window.performance?(c=!0,d=window.performance):"undefined"!==typeof n.g&&(null===(e=n.g.perf_hooks)||void 0===e?void 0:e.performance)?(c=!0,d=n.g.perf_hooks.performance):c=!1),c}function p(){return f()?d.now():Date.now()}class v{constructor(e,t){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=t;const n={};if(e.settings)for(const i in e.settings){const t=e.settings[i];n[i]=t.defaultValue}const o=`__vue-devtools-plugin-settings__${e.id}`;let r=Object.assign({},n);try{const e=localStorage.getItem(o),t=JSON.parse(e);Object.assign(r,t)}catch(l){}this.fallbacks={getSettings(){return r},setSettings(e){try{localStorage.setItem(o,JSON.stringify(e))}catch(l){}r=e},now(){return p()}},t&&t.on(u,((e,t)=>{e===this.plugin.id&&this.fallbacks.setSettings(t)})),this.proxiedOn=new Proxy({},{get:(e,t)=>this.target?this.target.on[t]:(...e)=>{this.onQueue.push({method:t,args:e})}}),this.proxiedTarget=new Proxy({},{get:(e,t)=>this.target?this.target[t]:"on"===t?this.proxiedOn:Object.keys(this.fallbacks).includes(t)?(...e)=>(this.targetQueue.push({method:t,args:e,resolve:()=>{}}),this.fallbacks[t](...e)):(...e)=>new Promise((n=>{this.targetQueue.push({method:t,args:e,resolve:n})}))})}async setRealTarget(e){this.target=e;for(const t of this.onQueue)this.target.on[t.method](...t.args);for(const t of this.targetQueue)t.resolve(await this.target[t.method](...t.args))}}function h(e,t){const n=e,o=i(),r=l(),u=a&&n.enableEarlyProxy;if(!r||!o.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&u){const e=u?new v(n,r):null,l=o.__VUE_DEVTOOLS_PLUGINS__=o.__VUE_DEVTOOLS_PLUGINS__||[];l.push({pluginDescriptor:n,setupFn:t,proxy:e}),e&&t(e.proxiedTarget)}else r.emit(s,e,t)}
/*!
 * vuex v4.1.0
 * (c) 2022 Evan You
 * @license MIT
 */
var m="store";function g(e,t){Object.keys(e).forEach((function(n){return t(e[n],n)}))}function b(e){return null!==e&&"object"===typeof e}function y(e){return e&&"function"===typeof e.then}function _(e,t){return function(){return e(t)}}function w(e,t,n){return t.indexOf(e)<0&&(n&&n.prepend?t.unshift(e):t.push(e)),function(){var n=t.indexOf(e);n>-1&&t.splice(n,1)}}function S(e,t){e._actions=Object.create(null),e._mutations=Object.create(null),e._wrappedGetters=Object.create(null),e._modulesNamespaceMap=Object.create(null);var n=e.state;x(e,n,[],e._modules.root,!0),k(e,n,t)}function k(e,t,n){var l=e._state,i=e._scope;e.getters={},e._makeLocalGettersCache=Object.create(null);var a=e._wrappedGetters,s={},u={},c=(0,r.B)(!0);c.run((function(){g(a,(function(t,n){s[n]=_(t,e),u[n]=(0,o.Fl)((function(){return s[n]()})),Object.defineProperty(e.getters,n,{get:function(){return u[n].value},enumerable:!0})}))})),e._state=(0,r.qj)({data:t}),e._scope=c,e.strict&&L(e),l&&n&&e._withCommit((function(){l.data=null})),i&&i.stop()}function x(e,t,n,o,r){var l=!n.length,i=e._modules.getNamespace(n);if(o.namespaced&&(e._modulesNamespaceMap[i],e._modulesNamespaceMap[i]=o),!l&&!r){var a=A(t,n.slice(0,-1)),s=n[n.length-1];e._withCommit((function(){a[s]=o.state}))}var u=o.context=E(e,i,n);o.forEachMutation((function(t,n){var o=i+n;T(e,o,t,u)})),o.forEachAction((function(t,n){var o=t.root?n:i+n,r=t.handler||t;O(e,o,r,u)})),o.forEachGetter((function(t,n){var o=i+n;F(e,o,t,u)})),o.forEachChild((function(o,l){x(e,t,n.concat(l),o,r)}))}function E(e,t,n){var o=""===t,r={dispatch:o?e.dispatch:function(n,o,r){var l=P(n,o,r),i=l.payload,a=l.options,s=l.type;return a&&a.root||(s=t+s),e.dispatch(s,i)},commit:o?e.commit:function(n,o,r){var l=P(n,o,r),i=l.payload,a=l.options,s=l.type;a&&a.root||(s=t+s),e.commit(s,i,a)}};return Object.defineProperties(r,{getters:{get:o?function(){return e.getters}:function(){return C(e,t)}},state:{get:function(){return A(e.state,n)}}}),r}function C(e,t){if(!e._makeLocalGettersCache[t]){var n={},o=t.length;Object.keys(e.getters).forEach((function(r){if(r.slice(0,o)===t){var l=r.slice(o);Object.defineProperty(n,l,{get:function(){return e.getters[r]},enumerable:!0})}})),e._makeLocalGettersCache[t]=n}return e._makeLocalGettersCache[t]}function T(e,t,n,o){var r=e._mutations[t]||(e._mutations[t]=[]);r.push((function(t){n.call(e,o.state,t)}))}function O(e,t,n,o){var r=e._actions[t]||(e._actions[t]=[]);r.push((function(t){var r=n.call(e,{dispatch:o.dispatch,commit:o.commit,getters:o.getters,state:o.state,rootGetters:e.getters,rootState:e.state},t);return y(r)||(r=Promise.resolve(r)),e._devtoolHook?r.catch((function(t){throw e._devtoolHook.emit("vuex:error",t),t})):r}))}function F(e,t,n,o){e._wrappedGetters[t]||(e._wrappedGetters[t]=function(e){return n(o.state,o.getters,e.state,e.getters)})}function L(e){(0,o.YP)((function(){return e._state.data}),(function(){0}),{deep:!0,flush:"sync"})}function A(e,t){return t.reduce((function(e,t){return e[t]}),e)}function P(e,t,n){return b(e)&&e.type&&(n=t,t=e,e=e.type),{type:e,payload:t,options:n}}var q="vuex bindings",R="vuex:mutations",N="vuex:actions",M="vuex",I=0;function $(e,t){h({id:"org.vuejs.vuex",app:e,label:"Vuex",homepage:"https://next.vuex.vuejs.org/",logo:"https://vuejs.org/images/icons/favicon-96x96.png",packageName:"vuex",componentStateTypes:[q]},(function(n){n.addTimelineLayer({id:R,label:"Vuex Mutations",color:D}),n.addTimelineLayer({id:N,label:"Vuex Actions",color:D}),n.addInspector({id:M,label:"Vuex",icon:"storage",treeFilterPlaceholder:"Filter stores..."}),n.on.getInspectorTree((function(n){if(n.app===e&&n.inspectorId===M)if(n.filter){var o=[];U(o,t._modules.root,n.filter,""),n.rootNodes=o}else n.rootNodes=[z(t._modules.root,"")]})),n.on.getInspectorState((function(n){if(n.app===e&&n.inspectorId===M){var o=n.nodeId;C(t,o),n.state=W(K(t._modules,o),"root"===o?t.getters:t._makeLocalGettersCache,o)}})),n.on.editInspectorState((function(n){if(n.app===e&&n.inspectorId===M){var o=n.nodeId,r=n.path;"root"!==o&&(r=o.split("/").filter(Boolean).concat(r)),t._withCommit((function(){n.set(t._state.data,r,n.state.value)}))}})),t.subscribe((function(e,t){var o={};e.payload&&(o.payload=e.payload),o.state=t,n.notifyComponentUpdate(),n.sendInspectorTree(M),n.sendInspectorState(M),n.addTimelineEvent({layerId:R,event:{time:Date.now(),title:e.type,data:o}})})),t.subscribeAction({before:function(e,t){var o={};e.payload&&(o.payload=e.payload),e._id=I++,e._time=Date.now(),o.state=t,n.addTimelineEvent({layerId:N,event:{time:e._time,title:e.type,groupId:e._id,subtitle:"start",data:o}})},after:function(e,t){var o={},r=Date.now()-e._time;o.duration={_custom:{type:"duration",display:r+"ms",tooltip:"Action duration",value:r}},e.payload&&(o.payload=e.payload),o.state=t,n.addTimelineEvent({layerId:N,event:{time:Date.now(),title:e.type,groupId:e._id,subtitle:"end",data:o}})}})}))}var D=8702998,V=6710886,j=16777215,B={label:"namespaced",textColor:j,backgroundColor:V};function H(e){return e&&"root"!==e?e.split("/").slice(-2,-1)[0]:"Root"}function z(e,t){return{id:t||"root",label:H(t),tags:e.namespaced?[B]:[],children:Object.keys(e._children).map((function(n){return z(e._children[n],t+n+"/")}))}}function U(e,t,n,o){o.includes(n)&&e.push({id:o||"root",label:o.endsWith("/")?o.slice(0,o.length-1):o||"Root",tags:t.namespaced?[B]:[]}),Object.keys(t._children).forEach((function(r){U(e,t._children[r],n,o+r+"/")}))}function W(e,t,n){t="root"===n?t:t[n];var o=Object.keys(t),r={state:Object.keys(e.state).map((function(t){return{key:t,editable:!0,value:e.state[t]}}))};if(o.length){var l=Z(t);r.getters=Object.keys(l).map((function(e){return{key:e.endsWith("/")?H(e):e,editable:!1,value:Y((function(){return l[e]}))}}))}return r}function Z(e){var t={};return Object.keys(e).forEach((function(n){var o=n.split("/");if(o.length>1){var r=t,l=o.pop();o.forEach((function(e){r[e]||(r[e]={_custom:{value:{},display:e,tooltip:"Module",abstract:!0}}),r=r[e]._custom.value})),r[l]=Y((function(){return e[n]}))}else t[n]=Y((function(){return e[n]}))})),t}function K(e,t){var n=t.split("/").filter((function(e){return e}));return n.reduce((function(e,o,r){var l=e[o];if(!l)throw new Error('Missing module "'+o+'" for path "'+t+'".');return r===n.length-1?l:l._children}),"root"===t?e:e.root._children)}function Y(e){try{return e()}catch(t){return t}}var J=function(e,t){this.runtime=t,this._children=Object.create(null),this._rawModule=e;var n=e.state;this.state=("function"===typeof n?n():n)||{}},X={namespaced:{configurable:!0}};X.namespaced.get=function(){return!!this._rawModule.namespaced},J.prototype.addChild=function(e,t){this._children[e]=t},J.prototype.removeChild=function(e){delete this._children[e]},J.prototype.getChild=function(e){return this._children[e]},J.prototype.hasChild=function(e){return e in this._children},J.prototype.update=function(e){this._rawModule.namespaced=e.namespaced,e.actions&&(this._rawModule.actions=e.actions),e.mutations&&(this._rawModule.mutations=e.mutations),e.getters&&(this._rawModule.getters=e.getters)},J.prototype.forEachChild=function(e){g(this._children,e)},J.prototype.forEachGetter=function(e){this._rawModule.getters&&g(this._rawModule.getters,e)},J.prototype.forEachAction=function(e){this._rawModule.actions&&g(this._rawModule.actions,e)},J.prototype.forEachMutation=function(e){this._rawModule.mutations&&g(this._rawModule.mutations,e)},Object.defineProperties(J.prototype,X);var G=function(e){this.register([],e,!1)};function Q(e,t,n){if(t.update(n),n.modules)for(var o in n.modules){if(!t.getChild(o))return void 0;Q(e.concat(o),t.getChild(o),n.modules[o])}}G.prototype.get=function(e){return e.reduce((function(e,t){return e.getChild(t)}),this.root)},G.prototype.getNamespace=function(e){var t=this.root;return e.reduce((function(e,n){return t=t.getChild(n),e+(t.namespaced?n+"/":"")}),"")},G.prototype.update=function(e){Q([],this.root,e)},G.prototype.register=function(e,t,n){var o=this;void 0===n&&(n=!0);var r=new J(t,n);if(0===e.length)this.root=r;else{var l=this.get(e.slice(0,-1));l.addChild(e[e.length-1],r)}t.modules&&g(t.modules,(function(t,r){o.register(e.concat(r),t,n)}))},G.prototype.unregister=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1],o=t.getChild(n);o&&o.runtime&&t.removeChild(n)},G.prototype.isRegistered=function(e){var t=this.get(e.slice(0,-1)),n=e[e.length-1];return!!t&&t.hasChild(n)};function ee(e){return new te(e)}var te=function(e){var t=this;void 0===e&&(e={});var n=e.plugins;void 0===n&&(n=[]);var o=e.strict;void 0===o&&(o=!1);var r=e.devtools;this._committing=!1,this._actions=Object.create(null),this._actionSubscribers=[],this._mutations=Object.create(null),this._wrappedGetters=Object.create(null),this._modules=new G(e),this._modulesNamespaceMap=Object.create(null),this._subscribers=[],this._makeLocalGettersCache=Object.create(null),this._scope=null,this._devtools=r;var l=this,i=this,a=i.dispatch,s=i.commit;this.dispatch=function(e,t){return a.call(l,e,t)},this.commit=function(e,t,n){return s.call(l,e,t,n)},this.strict=o;var u=this._modules.root.state;x(this,u,[],this._modules.root),k(this,u),n.forEach((function(e){return e(t)}))},ne={state:{configurable:!0}};te.prototype.install=function(e,t){e.provide(t||m,this),e.config.globalProperties.$store=this;var n=void 0!==this._devtools&&this._devtools;n&&$(e,this)},ne.state.get=function(){return this._state.data},ne.state.set=function(e){0},te.prototype.commit=function(e,t,n){var o=this,r=P(e,t,n),l=r.type,i=r.payload,a=(r.options,{type:l,payload:i}),s=this._mutations[l];s&&(this._withCommit((function(){s.forEach((function(e){e(i)}))})),this._subscribers.slice().forEach((function(e){return e(a,o.state)})))},te.prototype.dispatch=function(e,t){var n=this,o=P(e,t),r=o.type,l=o.payload,i={type:r,payload:l},a=this._actions[r];if(a){try{this._actionSubscribers.slice().filter((function(e){return e.before})).forEach((function(e){return e.before(i,n.state)}))}catch(u){0}var s=a.length>1?Promise.all(a.map((function(e){return e(l)}))):a[0](l);return new Promise((function(e,t){s.then((function(t){try{n._actionSubscribers.filter((function(e){return e.after})).forEach((function(e){return e.after(i,n.state)}))}catch(u){0}e(t)}),(function(e){try{n._actionSubscribers.filter((function(e){return e.error})).forEach((function(t){return t.error(i,n.state,e)}))}catch(u){0}t(e)}))}))}},te.prototype.subscribe=function(e,t){return w(e,this._subscribers,t)},te.prototype.subscribeAction=function(e,t){var n="function"===typeof e?{before:e}:e;return w(n,this._actionSubscribers,t)},te.prototype.watch=function(e,t,n){var r=this;return(0,o.YP)((function(){return e(r.state,r.getters)}),t,Object.assign({},n))},te.prototype.replaceState=function(e){var t=this;this._withCommit((function(){t._state.data=e}))},te.prototype.registerModule=function(e,t,n){void 0===n&&(n={}),"string"===typeof e&&(e=[e]),this._modules.register(e,t),x(this,this.state,e,this._modules.get(e),n.preserveState),k(this,this.state)},te.prototype.unregisterModule=function(e){var t=this;"string"===typeof e&&(e=[e]),this._modules.unregister(e),this._withCommit((function(){var n=A(t.state,e.slice(0,-1));delete n[e[e.length-1]]})),S(this)},te.prototype.hasModule=function(e){return"string"===typeof e&&(e=[e]),this._modules.isRegistered(e)},te.prototype.hotUpdate=function(e){this._modules.update(e),S(this,!0)},te.prototype._withCommit=function(e){var t=this._committing;this._committing=!0,e(),this._committing=t},Object.defineProperties(te.prototype,ne);var oe=ae((function(e,t){var n={};return le(t).forEach((function(t){var o=t.key,r=t.val;n[o]=function(){var t=this.$store.state,n=this.$store.getters;if(e){var o=se(this.$store,"mapState",e);if(!o)return;t=o.context.state,n=o.context.getters}return"function"===typeof r?r.call(this,t,n):t[r]},n[o].vuex=!0})),n})),re=(ae((function(e,t){var n={};return le(t).forEach((function(t){var o=t.key,r=t.val;n[o]=function(){var t=[],n=arguments.length;while(n--)t[n]=arguments[n];var o=this.$store.commit;if(e){var l=se(this.$store,"mapMutations",e);if(!l)return;o=l.context.commit}return"function"===typeof r?r.apply(this,[o].concat(t)):o.apply(this.$store,[r].concat(t))}})),n})),ae((function(e,t){var n={};return le(t).forEach((function(t){var o=t.key,r=t.val;r=e+r,n[o]=function(){if(!e||se(this.$store,"mapGetters",e))return this.$store.getters[r]},n[o].vuex=!0})),n})),ae((function(e,t){var n={};return le(t).forEach((function(t){var o=t.key,r=t.val;n[o]=function(){var t=[],n=arguments.length;while(n--)t[n]=arguments[n];var o=this.$store.dispatch;if(e){var l=se(this.$store,"mapActions",e);if(!l)return;o=l.context.dispatch}return"function"===typeof r?r.apply(this,[o].concat(t)):o.apply(this.$store,[r].concat(t))}})),n})));function le(e){return ie(e)?Array.isArray(e)?e.map((function(e){return{key:e,val:e}})):Object.keys(e).map((function(t){return{key:t,val:e[t]}})):[]}function ie(e){return Array.isArray(e)||b(e)}function ae(e){return function(t,n){return"string"!==typeof t?(n=t,t=""):"/"!==t.charAt(t.length-1)&&(t+="/"),e(t,n)}}function se(e,t,n){var o=e._modulesNamespaceMap[n];return o}},2670:(e,t,n)=>{"use strict";n.d(t,{Pa3:()=>o,WkI:()=>i,gzC:()=>r,lY3:()=>l});const o="M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8Z",r="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M12,19C10.1,19 8.3,18.2 7.1,16.9C5.9,15.6 5,13.9 5,12C5,10.1 5.8,8.3 7.1,7.1C8.4,5.9 10.1,5 12,5C13.9,5 15.7,5.8 16.9,7.1C18.1,8.4 19,10.1 19,12C19,13.9 18.2,15.7 16.9,16.9C15.6,18.1 13.9,19 12,19Z",l="M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z",i="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.21,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"},687:(e,t,n)=>{"use strict";function o(e){return{all:e=e||new Map,on:function(t,n){var o=e.get(t);o?o.push(n):e.set(t,[n])},off:function(t,n){var o=e.get(t);o&&(n?o.splice(o.indexOf(n)>>>0,1):e.set(t,[]))},emit:function(t,n){var o=e.get(t);o&&o.slice().map((function(e){e(n)})),(o=e.get("*"))&&o.slice().map((function(e){e(t,n)}))}}}n.d(t,{Z:()=>o})},7396:(e,t,n)=>{"use strict";n.d(t,{Z:()=>o});const o={name:"mdi-v5",type:{positive:"mdi-check-circle",negative:"mdi-alert",info:"mdi-information",warning:"mdi-exclamation"},arrow:{up:"mdi-arrow-up",right:"mdi-arrow-right",down:"mdi-arrow-down",left:"mdi-arrow-left",dropdown:"mdi-menu-down"},chevron:{left:"mdi-chevron-left",right:"mdi-chevron-right"},colorPicker:{spectrum:"mdi-gradient",tune:"mdi-tune",palette:"mdi-palette-swatch"},pullToRefresh:{icon:"mdi-refresh"},carousel:{left:"mdi-chevron-left",right:"mdi-chevron-right",up:"mdi-chevron-up",down:"mdi-chevron-down",navigationIcon:"mdi-circle"},chip:{remove:"mdi-close-circle",selected:"mdi-check"},datetime:{arrowLeft:"mdi-chevron-left",arrowRight:"mdi-chevron-right",now:"mdi-clock-outline",today:"mdi-calendar-today"},editor:{bold:"mdi-format-bold",italic:"mdi-format-italic",strikethrough:"mdi-format-strikethrough-variant",underline:"mdi-format-underline",unorderedList:"mdi-format-list-bulleted",orderedList:"mdi-format-list-numbered",subscript:"mdi-format-subscript",superscript:"mdi-format-superscript",hyperlink:"mdi-link",toggleFullscreen:"mdi-fullscreen",quote:"mdi-format-quote-close",left:"mdi-format-align-left",center:"mdi-format-align-center",right:"mdi-format-align-right",justify:"mdi-format-align-justify",print:"mdi-printer",outdent:"mdi-format-indent-decrease",indent:"mdi-format-indent-increase",removeFormat:"mdi-format-clear",formatting:"mdi-format-color-text",fontSize:"mdi-format-size",align:"mdi-format-align-left",hr:"mdi-minus",undo:"mdi-undo",redo:"mdi-redo",heading:"mdi-format-size",heading1:"mdi-format-header-1",heading2:"mdi-format-header-2",heading3:"mdi-format-header-3",heading4:"mdi-format-header-4",heading5:"mdi-format-header-5",heading6:"mdi-format-header-6",code:"mdi-code-tags",size:"mdi-format-size",size1:"mdi-numeric-1-box",size2:"mdi-numeric-2-box",size3:"mdi-numeric-3-box",size4:"mdi-numeric-4-box",size5:"mdi-numeric-5-box",size6:"mdi-numeric-6-box",size7:"mdi-numeric-7-box",font:"mdi-format-font",viewSource:"mdi-code-tags"},expansionItem:{icon:"mdi-chevron-down",denseIcon:"mdi-menu-down"},fab:{icon:"mdi-plus",activeIcon:"mdi-close"},field:{clear:"mdi-close-circle",error:"mdi-alert-circle"},pagination:{first:"mdi-chevron-double-left",prev:"mdi-chevron-left",next:"mdi-chevron-right",last:"mdi-chevron-double-right"},rating:{icon:"mdi-star"},stepper:{done:"mdi-check",active:"mdi-pencil",error:"mdi-alert"},tabs:{left:"mdi-chevron-left",right:"mdi-chevron-right",up:"mdi-chevron-up",down:"mdi-chevron-down"},table:{arrowUp:"mdi-arrow-up",warning:"mdi-alert",firstPage:"mdi-chevron-double-left",prevPage:"mdi-chevron-left",nextPage:"mdi-chevron-right",lastPage:"mdi-chevron-double-right"},tree:{icon:"mdi-play"},uploader:{done:"mdi-check",clear:"mdi-close",add:"mdi-plus-box",upload:"mdi-cloud-upload",removeQueue:"mdi-notification-clear-all",removeUploaded:"mdi-check-all"}}},3340:(e,t,n)=>{"use strict";function o(e){return e}function r(e){return e}function l(e){return e}n.d(t,{BC:()=>r,h:()=>l,xr:()=>o})},8339:(e,t,n)=>{"use strict";n.d(t,{p7:()=>nt,r5:()=>V});var o=n(9835),r=n(499);
/*!
  * vue-router v4.2.4
  * (c) 2023 Eduardo San Martin Morote
  * @license MIT
  */
const l="undefined"!==typeof window;function i(e){return e.__esModule||"Module"===e[Symbol.toStringTag]}const a=Object.assign;function s(e,t){const n={};for(const o in t){const r=t[o];n[o]=c(r)?r.map(e):e(r)}return n}const u=()=>{},c=Array.isArray;const d=/\/$/,f=e=>e.replace(d,"");function p(e,t,n="/"){let o,r={},l="",i="";const a=t.indexOf("#");let s=t.indexOf("?");return a<s&&a>=0&&(s=-1),s>-1&&(o=t.slice(0,s),l=t.slice(s+1,a>-1?a:t.length),r=e(l)),a>-1&&(o=o||t.slice(0,a),i=t.slice(a,t.length)),o=w(null!=o?o:t,n),{fullPath:o+(l&&"?")+l+i,path:o,query:r,hash:i}}function v(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function h(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function m(e,t,n){const o=t.matched.length-1,r=n.matched.length-1;return o>-1&&o===r&&g(t.matched[o],n.matched[r])&&b(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function g(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function b(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!y(e[n],t[n]))return!1;return!0}function y(e,t){return c(e)?_(e,t):c(t)?_(t,e):e===t}function _(e,t){return c(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}function w(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),o=e.split("/"),r=o[o.length-1];".."!==r&&"."!==r||o.push("");let l,i,a=n.length-1;for(l=0;l<o.length;l++)if(i=o[l],"."!==i){if(".."!==i)break;a>1&&a--}return n.slice(0,a).join("/")+"/"+o.slice(l-(l===o.length?1:0)).join("/")}var S,k;(function(e){e["pop"]="pop",e["push"]="push"})(S||(S={})),function(e){e["back"]="back",e["forward"]="forward",e["unknown"]=""}(k||(k={}));function x(e){if(!e)if(l){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),f(e)}const E=/^[^#]+#/;function C(e,t){return e.replace(E,"#")+t}function T(e,t){const n=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-n.left-(t.left||0),top:o.top-n.top-(t.top||0)}}const O=()=>({left:window.pageXOffset,top:window.pageYOffset});function F(e){let t;if("el"in e){const n=e.el,o="string"===typeof n&&n.startsWith("#");0;const r="string"===typeof n?o?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=T(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.pageXOffset,null!=t.top?t.top:window.pageYOffset)}function L(e,t){const n=history.state?history.state.position-t:-1;return n+e}const A=new Map;function P(e,t){A.set(e,t)}function q(e){const t=A.get(e);return A.delete(e),t}let R=()=>location.protocol+"//"+location.host;function N(e,t){const{pathname:n,search:o,hash:r}=t,l=e.indexOf("#");if(l>-1){let t=r.includes(e.slice(l))?e.slice(l).length:1,n=r.slice(t);return"/"!==n[0]&&(n="/"+n),h(n,"")}const i=h(n,e);return i+o+r}function M(e,t,n,o){let r=[],l=[],i=null;const s=({state:l})=>{const a=N(e,location),s=n.value,u=t.value;let c=0;if(l){if(n.value=a,t.value=l,i&&i===s)return void(i=null);c=u?l.position-u.position:0}else o(a);r.forEach((e=>{e(n.value,s,{delta:c,type:S.pop,direction:c?c>0?k.forward:k.back:k.unknown})}))};function u(){i=n.value}function c(e){r.push(e);const t=()=>{const t=r.indexOf(e);t>-1&&r.splice(t,1)};return l.push(t),t}function d(){const{history:e}=window;e.state&&e.replaceState(a({},e.state,{scroll:O()}),"")}function f(){for(const e of l)e();l=[],window.removeEventListener("popstate",s),window.removeEventListener("beforeunload",d)}return window.addEventListener("popstate",s),window.addEventListener("beforeunload",d,{passive:!0}),{pauseListeners:u,listen:c,destroy:f}}function I(e,t,n,o=!1,r=!1){return{back:e,current:t,forward:n,replaced:o,position:window.history.length,scroll:r?O():null}}function $(e){const{history:t,location:n}=window,o={value:N(e,n)},r={value:t.state};function l(o,l,i){const a=e.indexOf("#"),s=a>-1?(n.host&&document.querySelector("base")?e:e.slice(a))+o:R()+e+o;try{t[i?"replaceState":"pushState"](l,"",s),r.value=l}catch(u){console.error(u),n[i?"replace":"assign"](s)}}function i(e,n){const i=a({},t.state,I(r.value.back,e,r.value.forward,!0),n,{position:r.value.position});l(e,i,!0),o.value=e}function s(e,n){const i=a({},r.value,t.state,{forward:e,scroll:O()});l(i.current,i,!0);const s=a({},I(o.value,e,null),{position:i.position+1},n);l(e,s,!1),o.value=e}return r.value||l(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:o,state:r,push:s,replace:i}}function D(e){e=x(e);const t=$(e),n=M(e,t.state,t.location,t.replace);function o(e,t=!0){t||n.pauseListeners(),history.go(e)}const r=a({location:"",base:e,go:o,createHref:C.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function V(e){return e=location.host?e||location.pathname+location.search:"",e.includes("#")||(e+="#"),D(e)}function j(e){return"string"===typeof e||e&&"object"===typeof e}function B(e){return"string"===typeof e||"symbol"===typeof e}const H={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},z=Symbol("");var U;(function(e){e[e["aborted"]=4]="aborted",e[e["cancelled"]=8]="cancelled",e[e["duplicated"]=16]="duplicated"})(U||(U={}));function W(e,t){return a(new Error,{type:e,[z]:!0},t)}function Z(e,t){return e instanceof Error&&z in e&&(null==t||!!(e.type&t))}const K="[^/]+?",Y={sensitive:!1,strict:!1,start:!0,end:!0},J=/[.+*?^${}()[\]/\\]/g;function X(e,t){const n=a({},Y,t),o=[];let r=n.start?"^":"";const l=[];for(const a of e){const e=a.length?[]:[90];n.strict&&!a.length&&(r+="/");for(let t=0;t<a.length;t++){const o=a[t];let i=40+(n.sensitive?.25:0);if(0===o.type)t||(r+="/"),r+=o.value.replace(J,"\\$&"),i+=40;else if(1===o.type){const{value:e,repeatable:n,optional:s,regexp:u}=o;l.push({name:e,repeatable:n,optional:s});const c=u||K;if(c!==K){i+=10;try{new RegExp(`(${c})`)}catch(d){throw new Error(`Invalid custom RegExp for param "${e}" (${c}): `+d.message)}}let f=n?`((?:${c})(?:/(?:${c}))*)`:`(${c})`;t||(f=s&&a.length<2?`(?:/${f})`:"/"+f),s&&(f+="?"),r+=f,i+=20,s&&(i+=-8),n&&(i+=-20),".*"===c&&(i+=-50)}e.push(i)}o.push(e)}if(n.strict&&n.end){const e=o.length-1;o[e][o[e].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function s(e){const t=e.match(i),n={};if(!t)return null;for(let o=1;o<t.length;o++){const e=t[o]||"",r=l[o-1];n[r.name]=e&&r.repeatable?e.split("/"):e}return n}function u(t){let n="",o=!1;for(const r of e){o&&n.endsWith("/")||(n+="/"),o=!1;for(const e of r)if(0===e.type)n+=e.value;else if(1===e.type){const{value:l,repeatable:i,optional:a}=e,s=l in t?t[l]:"";if(c(s)&&!i)throw new Error(`Provided param "${l}" is an array but it is not repeatable (* or + modifiers)`);const u=c(s)?s.join("/"):s;if(!u){if(!a)throw new Error(`Missing required param "${l}"`);r.length<2&&(n.endsWith("/")?n=n.slice(0,-1):o=!0)}n+=u}}return n||"/"}return{re:i,score:o,keys:l,parse:s,stringify:u}}function G(e,t){let n=0;while(n<e.length&&n<t.length){const o=t[n]-e[n];if(o)return o;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function Q(e,t){let n=0;const o=e.score,r=t.score;while(n<o.length&&n<r.length){const e=G(o[n],r[n]);if(e)return e;n++}if(1===Math.abs(r.length-o.length)){if(ee(o))return 1;if(ee(r))return-1}return r.length-o.length}function ee(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const te={type:0,value:""},ne=/[a-zA-Z0-9_]/;function oe(e){if(!e)return[[]];if("/"===e)return[[te]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${u}": ${e}`)}let n=0,o=n;const r=[];let l;function i(){l&&r.push(l),l=[]}let a,s=0,u="",c="";function d(){u&&(0===n?l.push({type:0,value:u}):1===n||2===n||3===n?(l.length>1&&("*"===a||"+"===a)&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),l.push({type:1,value:u,regexp:c,repeatable:"*"===a||"+"===a,optional:"*"===a||"?"===a})):t("Invalid state to consume buffer"),u="")}function f(){u+=a}while(s<e.length)if(a=e[s++],"\\"!==a||2===n)switch(n){case 0:"/"===a?(u&&d(),i()):":"===a?(d(),n=1):f();break;case 4:f(),n=o;break;case 1:"("===a?n=2:ne.test(a)?f():(d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&s--);break;case 2:")"===a?"\\"==c[c.length-1]?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:d(),n=0,"*"!==a&&"?"!==a&&"+"!==a&&s--,c="";break;default:t("Unknown state");break}else o=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${u}"`),d(),i(),r}function re(e,t,n){const o=X(oe(e.path),n);const r=a(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf===!t.record.aliasOf&&t.children.push(r),r}function le(e,t){const n=[],o=new Map;function r(e){return o.get(e)}function l(e,n,o){const r=!o,s=ae(e);s.aliasOf=o&&o.record;const d=de(t,e),f=[s];if("alias"in e){const t="string"===typeof e.alias?[e.alias]:e.alias;for(const e of t)f.push(a({},s,{components:o?o.record.components:s.components,path:e,aliasOf:o?o.record:s}))}let p,v;for(const t of f){const{path:a}=t;if(n&&"/"!==a[0]){const e=n.record.path,o="/"===e[e.length-1]?"":"/";t.path=n.record.path+(a&&o+a)}if(p=re(t,n,d),o?o.alias.push(p):(v=v||p,v!==p&&v.alias.push(p),r&&e.name&&!ue(p)&&i(e.name)),s.children){const e=s.children;for(let t=0;t<e.length;t++)l(e[t],p,o&&o.children[t])}o=o||p,(p.record.components&&Object.keys(p.record.components).length||p.record.name||p.record.redirect)&&c(p)}return v?()=>{i(v)}:u}function i(e){if(B(e)){const t=o.get(e);t&&(o.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&o.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function s(){return n}function c(e){let t=0;while(t<n.length&&Q(e,n[t])>=0&&(e.record.path!==n[t].record.path||!fe(e,n[t])))t++;n.splice(t,0,e),e.record.name&&!ue(e)&&o.set(e.record.name,e)}function d(e,t){let r,l,i,s={};if("name"in e&&e.name){if(r=o.get(e.name),!r)throw W(1,{location:e});0,i=r.record.name,s=a(ie(t.params,r.keys.filter((e=>!e.optional)).map((e=>e.name))),e.params&&ie(e.params,r.keys.map((e=>e.name)))),l=r.stringify(s)}else if("path"in e)l=e.path,r=n.find((e=>e.re.test(l))),r&&(s=r.parse(l),i=r.record.name);else{if(r=t.name?o.get(t.name):n.find((e=>e.re.test(t.path))),!r)throw W(1,{location:e,currentLocation:t});i=r.record.name,s=a({},t.params,e.params),l=r.stringify(s)}const u=[];let c=r;while(c)u.unshift(c.record),c=c.parent;return{name:i,path:l,params:s,matched:u,meta:ce(u)}}return t=de({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>l(e))),{addRoute:l,resolve:d,removeRoute:i,getRoutes:s,getRecordMatcher:r}}function ie(e,t){const n={};for(const o of t)o in e&&(n[o]=e[o]);return n}function ae(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:se(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}}}function se(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const o in e.components)t[o]="object"===typeof n?n[o]:n;return t}function ue(e){while(e){if(e.record.aliasOf)return!0;e=e.parent}return!1}function ce(e){return e.reduce(((e,t)=>a(e,t.meta)),{})}function de(e,t){const n={};for(const o in e)n[o]=o in t?t[o]:e[o];return n}function fe(e,t){return t.children.some((t=>t===e||fe(e,t)))}const pe=/#/g,ve=/&/g,he=/\//g,me=/=/g,ge=/\?/g,be=/\+/g,ye=/%5B/g,_e=/%5D/g,we=/%5E/g,Se=/%60/g,ke=/%7B/g,xe=/%7C/g,Ee=/%7D/g,Ce=/%20/g;function Te(e){return encodeURI(""+e).replace(xe,"|").replace(ye,"[").replace(_e,"]")}function Oe(e){return Te(e).replace(ke,"{").replace(Ee,"}").replace(we,"^")}function Fe(e){return Te(e).replace(be,"%2B").replace(Ce,"+").replace(pe,"%23").replace(ve,"%26").replace(Se,"`").replace(ke,"{").replace(Ee,"}").replace(we,"^")}function Le(e){return Fe(e).replace(me,"%3D")}function Ae(e){return Te(e).replace(pe,"%23").replace(ge,"%3F")}function Pe(e){return null==e?"":Ae(e).replace(he,"%2F")}function qe(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function Re(e){const t={};if(""===e||"?"===e)return t;const n="?"===e[0],o=(n?e.slice(1):e).split("&");for(let r=0;r<o.length;++r){const e=o[r].replace(be," "),n=e.indexOf("="),l=qe(n<0?e:e.slice(0,n)),i=n<0?null:qe(e.slice(n+1));if(l in t){let e=t[l];c(e)||(e=t[l]=[e]),e.push(i)}else t[l]=i}return t}function Ne(e){let t="";for(let n in e){const o=e[n];if(n=Le(n),null==o){void 0!==o&&(t+=(t.length?"&":"")+n);continue}const r=c(o)?o.map((e=>e&&Fe(e))):[o&&Fe(o)];r.forEach((e=>{void 0!==e&&(t+=(t.length?"&":"")+n,null!=e&&(t+="="+e))}))}return t}function Me(e){const t={};for(const n in e){const o=e[n];void 0!==o&&(t[n]=c(o)?o.map((e=>null==e?null:""+e)):null==o?o:""+o)}return t}const Ie=Symbol(""),$e=Symbol(""),De=Symbol(""),Ve=Symbol(""),je=Symbol("");function Be(){let e=[];function t(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function He(e,t,n,o,r){const l=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise(((i,a)=>{const s=e=>{!1===e?a(W(4,{from:n,to:t})):e instanceof Error?a(e):j(e)?a(W(2,{from:t,to:e})):(l&&o.enterCallbacks[r]===l&&"function"===typeof e&&l.push(e),i())},u=e.call(o&&o.instances[r],t,n,s);let c=Promise.resolve(u);e.length<3&&(c=c.then(s)),c.catch((e=>a(e)))}))}function ze(e,t,n,o){const r=[];for(const l of e){0;for(const e in l.components){let a=l.components[e];if("beforeRouteEnter"===t||l.instances[e])if(Ue(a)){const i=a.__vccOpts||a,s=i[t];s&&r.push(He(s,n,o,l,e))}else{let s=a();0,r.push((()=>s.then((r=>{if(!r)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${l.path}"`));const a=i(r)?r.default:r;l.components[e]=a;const s=a.__vccOpts||a,u=s[t];return u&&He(u,n,o,l,e)()}))))}}}return r}function Ue(e){return"object"===typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}function We(e){const t=(0,o.f3)(De),n=(0,o.f3)(Ve),l=(0,o.Fl)((()=>t.resolve((0,r.SU)(e.to)))),i=(0,o.Fl)((()=>{const{matched:e}=l.value,{length:t}=e,o=e[t-1],r=n.matched;if(!o||!r.length)return-1;const i=r.findIndex(g.bind(null,o));if(i>-1)return i;const a=Xe(e[t-2]);return t>1&&Xe(o)===a&&r[r.length-1].path!==a?r.findIndex(g.bind(null,e[t-2])):i})),a=(0,o.Fl)((()=>i.value>-1&&Je(n.params,l.value.params))),s=(0,o.Fl)((()=>i.value>-1&&i.value===n.matched.length-1&&b(n.params,l.value.params)));function c(n={}){return Ye(n)?t[(0,r.SU)(e.replace)?"replace":"push"]((0,r.SU)(e.to)).catch(u):Promise.resolve()}return{route:l,href:(0,o.Fl)((()=>l.value.href)),isActive:a,isExactActive:s,navigate:c}}const Ze=(0,o.aZ)({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:We,setup(e,{slots:t}){const n=(0,r.qj)(We(e)),{options:l}=(0,o.f3)(De),i=(0,o.Fl)((()=>({[Ge(e.activeClass,l.linkActiveClass,"router-link-active")]:n.isActive,[Ge(e.exactActiveClass,l.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive})));return()=>{const r=t.default&&t.default(n);return e.custom?r:(0,o.h)("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},r)}}}),Ke=Ze;function Ye(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&(void 0===e.button||0===e.button)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Je(e,t){for(const n in t){const o=t[n],r=e[n];if("string"===typeof o){if(o!==r)return!1}else if(!c(r)||r.length!==o.length||o.some(((e,t)=>e!==r[t])))return!1}return!0}function Xe(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ge=(e,t,n)=>null!=e?e:null!=t?t:n,Qe=(0,o.aZ)({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const l=(0,o.f3)(je),i=(0,o.Fl)((()=>e.route||l.value)),s=(0,o.f3)($e,0),u=(0,o.Fl)((()=>{let e=(0,r.SU)(s);const{matched:t}=i.value;let n;while((n=t[e])&&!n.components)e++;return e})),c=(0,o.Fl)((()=>i.value.matched[u.value]));(0,o.JJ)($e,(0,o.Fl)((()=>u.value+1))),(0,o.JJ)(Ie,c),(0,o.JJ)(je,i);const d=(0,r.iH)();return(0,o.YP)((()=>[d.value,c.value,e.name]),(([e,t,n],[o,r,l])=>{t&&(t.instances[n]=e,r&&r!==t&&e&&e===o&&(t.leaveGuards.size||(t.leaveGuards=r.leaveGuards),t.updateGuards.size||(t.updateGuards=r.updateGuards))),!e||!t||r&&g(t,r)&&o||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const r=i.value,l=e.name,s=c.value,u=s&&s.components[l];if(!u)return et(n.default,{Component:u,route:r});const f=s.props[l],p=f?!0===f?r.params:"function"===typeof f?f(r):f:null,v=e=>{e.component.isUnmounted&&(s.instances[l]=null)},h=(0,o.h)(u,a({},p,t,{onVnodeUnmounted:v,ref:d}));return et(n.default,{Component:h,route:r})||h}}});function et(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const tt=Qe;function nt(e){const t=le(e.routes,e),n=e.parseQuery||Re,i=e.stringifyQuery||Ne,d=e.history;const f=Be(),h=Be(),g=Be(),b=(0,r.XI)(H);let y=H;l&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const _=s.bind(null,(e=>""+e)),w=s.bind(null,Pe),k=s.bind(null,qe);function x(e,n){let o,r;return B(e)?(o=t.getRecordMatcher(e),r=n):r=e,t.addRoute(r,o)}function E(e){const n=t.getRecordMatcher(e);n&&t.removeRoute(n)}function C(){return t.getRoutes().map((e=>e.record))}function T(e){return!!t.getRecordMatcher(e)}function A(e,o){if(o=a({},o||b.value),"string"===typeof e){const r=p(n,e,o.path),l=t.resolve({path:r.path},o),i=d.createHref(r.fullPath);return a(r,l,{params:k(l.params),hash:qe(r.hash),redirectedFrom:void 0,href:i})}let r;if("path"in e)r=a({},e,{path:p(n,e.path,o.path).path});else{const t=a({},e.params);for(const e in t)null==t[e]&&delete t[e];r=a({},e,{params:w(t)}),o.params=w(o.params)}const l=t.resolve(r,o),s=e.hash||"";l.params=_(k(l.params));const u=v(i,a({},e,{hash:Oe(s),path:l.path})),c=d.createHref(u);return a({fullPath:u,hash:s,query:i===Ne?Me(e.query):e.query||{}},l,{redirectedFrom:void 0,href:c})}function R(e){return"string"===typeof e?p(n,e,b.value.path):a({},e)}function N(e,t){if(y!==e)return W(8,{from:t,to:e})}function M(e){return D(e)}function I(e){return M(a(R(e),{replace:!0}))}function $(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let o="function"===typeof n?n(e):n;return"string"===typeof o&&(o=o.includes("?")||o.includes("#")?o=R(o):{path:o},o.params={}),a({query:e.query,hash:e.hash,params:"path"in o?{}:e.params},o)}}function D(e,t){const n=y=A(e),o=b.value,r=e.state,l=e.force,s=!0===e.replace,u=$(n);if(u)return D(a(R(u),{state:"object"===typeof u?a({},r,u.state):r,force:l,replace:s}),t||n);const c=n;let d;return c.redirectedFrom=t,!l&&m(i,o,n)&&(d=W(16,{to:c,from:o}),oe(o,o,!0,!1)),(d?Promise.resolve(d):z(c,o)).catch((e=>Z(e)?Z(e,2)?e:ne(e):ee(e,c,o))).then((e=>{if(e){if(Z(e,2))return D(a({replace:s},R(e.to),{state:"object"===typeof e.to?a({},r,e.to.state):r,force:l}),t||c)}else e=K(c,o,!0,s,r);return U(c,o,e),e}))}function V(e,t){const n=N(e,t);return n?Promise.reject(n):Promise.resolve()}function j(e){const t=ae.values().next().value;return t&&"function"===typeof t.runWithContext?t.runWithContext(e):e()}function z(e,t){let n;const[o,r,l]=ot(e,t);n=ze(o.reverse(),"beforeRouteLeave",e,t);for(const a of o)a.leaveGuards.forEach((o=>{n.push(He(o,e,t))}));const i=V.bind(null,e,t);return n.push(i),ue(n).then((()=>{n=[];for(const o of f.list())n.push(He(o,e,t));return n.push(i),ue(n)})).then((()=>{n=ze(r,"beforeRouteUpdate",e,t);for(const o of r)o.updateGuards.forEach((o=>{n.push(He(o,e,t))}));return n.push(i),ue(n)})).then((()=>{n=[];for(const o of l)if(o.beforeEnter)if(c(o.beforeEnter))for(const r of o.beforeEnter)n.push(He(r,e,t));else n.push(He(o.beforeEnter,e,t));return n.push(i),ue(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=ze(l,"beforeRouteEnter",e,t),n.push(i),ue(n)))).then((()=>{n=[];for(const o of h.list())n.push(He(o,e,t));return n.push(i),ue(n)})).catch((e=>Z(e,8)?e:Promise.reject(e)))}function U(e,t,n){g.list().forEach((o=>j((()=>o(e,t,n)))))}function K(e,t,n,o,r){const i=N(e,t);if(i)return i;const s=t===H,u=l?history.state:{};n&&(o||s?d.replace(e.fullPath,a({scroll:s&&u&&u.scroll},r)):d.push(e.fullPath,r)),b.value=e,oe(e,t,n,s),ne()}let Y;function J(){Y||(Y=d.listen(((e,t,n)=>{if(!se.listening)return;const o=A(e),r=$(o);if(r)return void D(a(r,{replace:!0}),o).catch(u);y=o;const i=b.value;l&&P(L(i.fullPath,n.delta),O()),z(o,i).catch((e=>Z(e,12)?e:Z(e,2)?(D(e.to,o).then((e=>{Z(e,20)&&!n.delta&&n.type===S.pop&&d.go(-1,!1)})).catch(u),Promise.reject()):(n.delta&&d.go(-n.delta,!1),ee(e,o,i)))).then((e=>{e=e||K(o,i,!1),e&&(n.delta&&!Z(e,8)?d.go(-n.delta,!1):n.type===S.pop&&Z(e,20)&&d.go(-1,!1)),U(o,i,e)})).catch(u)})))}let X,G=Be(),Q=Be();function ee(e,t,n){ne(e);const o=Q.list();return o.length?o.forEach((o=>o(e,t,n))):console.error(e),Promise.reject(e)}function te(){return X&&b.value!==H?Promise.resolve():new Promise(((e,t)=>{G.add([e,t])}))}function ne(e){return X||(X=!e,J(),G.list().forEach((([t,n])=>e?n(e):t())),G.reset()),e}function oe(t,n,r,i){const{scrollBehavior:a}=e;if(!l||!a)return Promise.resolve();const s=!r&&q(L(t.fullPath,0))||(i||!r)&&history.state&&history.state.scroll||null;return(0,o.Y3)().then((()=>a(t,n,s))).then((e=>e&&F(e))).catch((e=>ee(e,t,n)))}const re=e=>d.go(e);let ie;const ae=new Set,se={currentRoute:b,listening:!0,addRoute:x,removeRoute:E,hasRoute:T,getRoutes:C,resolve:A,options:e,push:M,replace:I,go:re,back:()=>re(-1),forward:()=>re(1),beforeEach:f.add,beforeResolve:h.add,afterEach:g.add,onError:Q.add,isReady:te,install(e){const t=this;e.component("RouterLink",Ke),e.component("RouterView",tt),e.config.globalProperties.$router=t,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>(0,r.SU)(b)}),l&&!ie&&b.value===H&&(ie=!0,M(d.location).catch((e=>{0})));const n={};for(const r in H)Object.defineProperty(n,r,{get:()=>b.value[r],enumerable:!0});e.provide(De,t),e.provide(Ve,(0,r.Um)(n)),e.provide(je,b);const o=e.unmount;ae.add(e),e.unmount=function(){ae.delete(e),ae.size<1&&(y=H,Y&&Y(),Y=null,b.value=H,ie=!1,X=!1),o()}}};function ue(e){return e.reduce(((e,t)=>e.then((()=>j(t)))),Promise.resolve())}return se}function ot(e,t){const n=[],o=[],r=[],l=Math.max(t.matched.length,e.matched.length);for(let i=0;i<l;i++){const l=t.matched[i];l&&(e.matched.find((e=>g(e,l)))?o.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find((e=>g(e,a)))||r.push(a))}return[n,o,r]}},7524:(e,t,n)=>{"use strict";function o(e,t){return function(){return e.apply(t,arguments)}}n.d(t,{Z:()=>$t});const{toString:r}=Object.prototype,{getPrototypeOf:l}=Object,i=(e=>t=>{const n=r.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),a=e=>(e=e.toLowerCase(),t=>i(t)===e),s=e=>t=>typeof t===e,{isArray:u}=Array,c=s("undefined");function d(e){return null!==e&&!c(e)&&null!==e.constructor&&!c(e.constructor)&&h(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const f=a("ArrayBuffer");function p(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&f(e.buffer),t}const v=s("string"),h=s("function"),m=s("number"),g=e=>null!==e&&"object"===typeof e,b=e=>!0===e||!1===e,y=e=>{if("object"!==i(e))return!1;const t=l(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},_=a("Date"),w=a("File"),S=a("Blob"),k=a("FileList"),x=e=>g(e)&&h(e.pipe),E=e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||h(e.append)&&("formdata"===(t=i(e))||"object"===t&&h(e.toString)&&"[object FormData]"===e.toString()))},C=a("URLSearchParams"),T=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function O(e,t,{allOwnKeys:n=!1}={}){if(null===e||"undefined"===typeof e)return;let o,r;if("object"!==typeof e&&(e=[e]),u(e))for(o=0,r=e.length;o<r;o++)t.call(null,e[o],o,e);else{const r=n?Object.getOwnPropertyNames(e):Object.keys(e),l=r.length;let i;for(o=0;o<l;o++)i=r[o],t.call(null,e[i],i,e)}}function F(e,t){t=t.toLowerCase();const n=Object.keys(e);let o,r=n.length;while(r-- >0)if(o=n[r],t===o.toLowerCase())return o;return null}const L=(()=>"undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global)(),A=e=>!c(e)&&e!==L;function P(){const{caseless:e}=A(this)&&this||{},t={},n=(n,o)=>{const r=e&&F(t,o)||o;y(t[r])&&y(n)?t[r]=P(t[r],n):y(n)?t[r]=P({},n):u(n)?t[r]=n.slice():t[r]=n};for(let o=0,r=arguments.length;o<r;o++)arguments[o]&&O(arguments[o],n);return t}const q=(e,t,n,{allOwnKeys:r}={})=>(O(t,((t,r)=>{n&&h(t)?e[r]=o(t,n):e[r]=t}),{allOwnKeys:r}),e),R=e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),N=(e,t,n,o)=>{e.prototype=Object.create(t.prototype,o),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},M=(e,t,n,o)=>{let r,i,a;const s={};if(t=t||{},null==e)return t;do{r=Object.getOwnPropertyNames(e),i=r.length;while(i-- >0)a=r[i],o&&!o(a,e,t)||s[a]||(t[a]=e[a],s[a]=!0);e=!1!==n&&l(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},I=(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const o=e.indexOf(t,n);return-1!==o&&o===n},$=e=>{if(!e)return null;if(u(e))return e;let t=e.length;if(!m(t))return null;const n=new Array(t);while(t-- >0)n[t]=e[t];return n},D=(e=>t=>e&&t instanceof e)("undefined"!==typeof Uint8Array&&l(Uint8Array)),V=(e,t)=>{const n=e&&e[Symbol.iterator],o=n.call(e);let r;while((r=o.next())&&!r.done){const n=r.value;t.call(e,n[0],n[1])}},j=(e,t)=>{let n;const o=[];while(null!==(n=e.exec(t)))o.push(n);return o},B=a("HTMLFormElement"),H=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),z=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),U=a("RegExp"),W=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),o={};O(n,((n,r)=>{!1!==t(n,r,e)&&(o[r]=n)})),Object.defineProperties(e,o)},Z=e=>{W(e,((t,n)=>{if(h(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const o=e[n];h(o)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},K=(e,t)=>{const n={},o=e=>{e.forEach((e=>{n[e]=!0}))};return u(e)?o(e):o(String(e).split(t)),n},Y=()=>{},J=(e,t)=>(e=+e,Number.isFinite(e)?e:t),X="abcdefghijklmnopqrstuvwxyz",G="0123456789",Q={DIGIT:G,ALPHA:X,ALPHA_DIGIT:X+X.toUpperCase()+G},ee=(e=16,t=Q.ALPHA_DIGIT)=>{let n="";const{length:o}=t;while(e--)n+=t[Math.random()*o|0];return n};function te(e){return!!(e&&h(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])}const ne=e=>{const t=new Array(10),n=(e,o)=>{if(g(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[o]=e;const r=u(e)?[]:{};return O(e,((e,t)=>{const l=n(e,o+1);!c(l)&&(r[t]=l)})),t[o]=void 0,r}}return e};return n(e,0)},oe=a("AsyncFunction"),re=e=>e&&(g(e)||h(e))&&h(e.then)&&h(e.catch),le={isArray:u,isArrayBuffer:f,isBuffer:d,isFormData:E,isArrayBufferView:p,isString:v,isNumber:m,isBoolean:b,isObject:g,isPlainObject:y,isUndefined:c,isDate:_,isFile:w,isBlob:S,isRegExp:U,isFunction:h,isStream:x,isURLSearchParams:C,isTypedArray:D,isFileList:k,forEach:O,merge:P,extend:q,trim:T,stripBOM:R,inherits:N,toFlatObject:M,kindOf:i,kindOfTest:a,endsWith:I,toArray:$,forEachEntry:V,matchAll:j,isHTMLForm:B,hasOwnProperty:z,hasOwnProp:z,reduceDescriptors:W,freezeMethods:Z,toObjectSet:K,toCamelCase:H,noop:Y,toFiniteNumber:J,findKey:F,global:L,isContextDefined:A,ALPHABET:Q,generateString:ee,isSpecCompliantForm:te,toJSONObject:ne,isAsyncFn:oe,isThenable:re};function ie(e,t,n,o,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),o&&(this.request=o),r&&(this.response=r)}le.inherits(ie,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:le.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const ae=ie.prototype,se={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{se[e]={value:e}})),Object.defineProperties(ie,se),Object.defineProperty(ae,"isAxiosError",{value:!0}),ie.from=(e,t,n,o,r,l)=>{const i=Object.create(ae);return le.toFlatObject(e,i,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),ie.call(i,e.message,t,n,o,r),i.cause=e,i.name=e.name,l&&Object.assign(i,l),i};const ue=ie,ce=null;function de(e){return le.isPlainObject(e)||le.isArray(e)}function fe(e){return le.endsWith(e,"[]")?e.slice(0,-2):e}function pe(e,t,n){return e?e.concat(t).map((function(e,t){return e=fe(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}function ve(e){return le.isArray(e)&&!e.some(de)}const he=le.toFlatObject(le,{},null,(function(e){return/^is[A-Z]/.test(e)}));function me(e,t,n){if(!le.isObject(e))throw new TypeError("target must be an object");t=t||new(ce||FormData),n=le.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!le.isUndefined(t[e])}));const o=n.metaTokens,r=n.visitor||c,l=n.dots,i=n.indexes,a=n.Blob||"undefined"!==typeof Blob&&Blob,s=a&&le.isSpecCompliantForm(t);if(!le.isFunction(r))throw new TypeError("visitor must be a function");function u(e){if(null===e)return"";if(le.isDate(e))return e.toISOString();if(!s&&le.isBlob(e))throw new ue("Blob is not supported. Use a Buffer instead.");return le.isArrayBuffer(e)||le.isTypedArray(e)?s&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,n,r){let a=e;if(e&&!r&&"object"===typeof e)if(le.endsWith(n,"{}"))n=o?n:n.slice(0,-2),e=JSON.stringify(e);else if(le.isArray(e)&&ve(e)||(le.isFileList(e)||le.endsWith(n,"[]"))&&(a=le.toArray(e)))return n=fe(n),a.forEach((function(e,o){!le.isUndefined(e)&&null!==e&&t.append(!0===i?pe([n],o,l):null===i?n:n+"[]",u(e))})),!1;return!!de(e)||(t.append(pe(r,n,l),u(e)),!1)}const d=[],f=Object.assign(he,{defaultVisitor:c,convertValue:u,isVisitable:de});function p(e,n){if(!le.isUndefined(e)){if(-1!==d.indexOf(e))throw Error("Circular reference detected in "+n.join("."));d.push(e),le.forEach(e,(function(e,o){const l=!(le.isUndefined(e)||null===e)&&r.call(t,e,le.isString(o)?o.trim():o,n,f);!0===l&&p(e,n?n.concat(o):[o])})),d.pop()}}if(!le.isObject(e))throw new TypeError("data must be an object");return p(e),t}const ge=me;function be(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function ye(e,t){this._pairs=[],e&&ge(e,this,t)}const _e=ye.prototype;_e.append=function(e,t){this._pairs.push([e,t])},_e.toString=function(e){const t=e?function(t){return e.call(this,t,be)}:be;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const we=ye;function Se(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ke(e,t,n){if(!t)return e;const o=n&&n.encode||Se,r=n&&n.serialize;let l;if(l=r?r(t,n):le.isURLSearchParams(t)?t.toString():new we(t,n).toString(o),l){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+l}return e}class xe{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){le.forEach(this.handlers,(function(t){null!==t&&e(t)}))}}const Ee=xe,Ce={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Te="undefined"!==typeof URLSearchParams?URLSearchParams:we,Oe="undefined"!==typeof FormData?FormData:null,Fe="undefined"!==typeof Blob?Blob:null,Le=(()=>{let e;return("undefined"===typeof navigator||"ReactNative"!==(e=navigator.product)&&"NativeScript"!==e&&"NS"!==e)&&("undefined"!==typeof window&&"undefined"!==typeof document)})(),Ae=(()=>"undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts)(),Pe={isBrowser:!0,classes:{URLSearchParams:Te,FormData:Oe,Blob:Fe},isStandardBrowserEnv:Le,isStandardBrowserWebWorkerEnv:Ae,protocols:["http","https","file","blob","url","data"]};function qe(e,t){return ge(e,new Pe.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,o){return Pe.isNode&&le.isBuffer(e)?(this.append(t,e.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Re(e){return le.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}function Ne(e){const t={},n=Object.keys(e);let o;const r=n.length;let l;for(o=0;o<r;o++)l=n[o],t[l]=e[l];return t}function Me(e){function t(e,n,o,r){let l=e[r++];const i=Number.isFinite(+l),a=r>=e.length;if(l=!l&&le.isArray(o)?o.length:l,a)return le.hasOwnProp(o,l)?o[l]=[o[l],n]:o[l]=n,!i;o[l]&&le.isObject(o[l])||(o[l]=[]);const s=t(e,n,o[l],r);return s&&le.isArray(o[l])&&(o[l]=Ne(o[l])),!i}if(le.isFormData(e)&&le.isFunction(e.entries)){const n={};return le.forEachEntry(e,((e,o)=>{t(Re(e),o,n,0)})),n}return null}const Ie=Me,$e={"Content-Type":void 0};function De(e,t,n){if(le.isString(e))try{return(t||JSON.parse)(e),le.trim(e)}catch(o){if("SyntaxError"!==o.name)throw o}return(n||JSON.stringify)(e)}const Ve={transitional:Ce,adapter:["xhr","http"],transformRequest:[function(e,t){const n=t.getContentType()||"",o=n.indexOf("application/json")>-1,r=le.isObject(e);r&&le.isHTMLForm(e)&&(e=new FormData(e));const l=le.isFormData(e);if(l)return o&&o?JSON.stringify(Ie(e)):e;if(le.isArrayBuffer(e)||le.isBuffer(e)||le.isStream(e)||le.isFile(e)||le.isBlob(e))return e;if(le.isArrayBufferView(e))return e.buffer;if(le.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let i;if(r){if(n.indexOf("application/x-www-form-urlencoded")>-1)return qe(e,this.formSerializer).toString();if((i=le.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return ge(i?{"files[]":e}:e,t&&new t,this.formSerializer)}}return r||o?(t.setContentType("application/json",!1),De(e)):e}],transformResponse:[function(e){const t=this.transitional||Ve.transitional,n=t&&t.forcedJSONParsing,o="json"===this.responseType;if(e&&le.isString(e)&&(n&&!this.responseType||o)){const n=t&&t.silentJSONParsing,l=!n&&o;try{return JSON.parse(e)}catch(r){if(l){if("SyntaxError"===r.name)throw ue.from(r,ue.ERR_BAD_RESPONSE,this,null,this.response);throw r}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Pe.classes.FormData,Blob:Pe.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};le.forEach(["delete","get","head"],(function(e){Ve.headers[e]={}})),le.forEach(["post","put","patch"],(function(e){Ve.headers[e]=le.merge($e)}));const je=Ve,Be=le.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),He=e=>{const t={};let n,o,r;return e&&e.split("\n").forEach((function(e){r=e.indexOf(":"),n=e.substring(0,r).trim().toLowerCase(),o=e.substring(r+1).trim(),!n||t[n]&&Be[n]||("set-cookie"===n?t[n]?t[n].push(o):t[n]=[o]:t[n]=t[n]?t[n]+", "+o:o)})),t},ze=Symbol("internals");function Ue(e){return e&&String(e).trim().toLowerCase()}function We(e){return!1===e||null==e?e:le.isArray(e)?e.map(We):String(e)}function Ze(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let o;while(o=n.exec(e))t[o[1]]=o[2];return t}const Ke=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Ye(e,t,n,o,r){return le.isFunction(o)?o.call(this,t,n):(r&&(t=n),le.isString(t)?le.isString(o)?-1!==t.indexOf(o):le.isRegExp(o)?o.test(t):void 0:void 0)}function Je(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}function Xe(e,t){const n=le.toCamelCase(" "+t);["get","set","has"].forEach((o=>{Object.defineProperty(e,o+n,{value:function(e,n,r){return this[o].call(this,t,e,n,r)},configurable:!0})}))}class Ge{constructor(e){e&&this.set(e)}set(e,t,n){const o=this;function r(e,t,n){const r=Ue(t);if(!r)throw new Error("header name must be a non-empty string");const l=le.findKey(o,r);(!l||void 0===o[l]||!0===n||void 0===n&&!1!==o[l])&&(o[l||t]=We(e))}const l=(e,t)=>le.forEach(e,((e,n)=>r(e,n,t)));return le.isPlainObject(e)||e instanceof this.constructor?l(e,t):le.isString(e)&&(e=e.trim())&&!Ke(e)?l(He(e),t):null!=e&&r(t,e,n),this}get(e,t){if(e=Ue(e),e){const n=le.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return Ze(e);if(le.isFunction(t))return t.call(this,e,n);if(le.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Ue(e),e){const n=le.findKey(this,e);return!(!n||void 0===this[n]||t&&!Ye(this,this[n],n,t))}return!1}delete(e,t){const n=this;let o=!1;function r(e){if(e=Ue(e),e){const r=le.findKey(n,e);!r||t&&!Ye(n,n[r],r,t)||(delete n[r],o=!0)}}return le.isArray(e)?e.forEach(r):r(e),o}clear(e){const t=Object.keys(this);let n=t.length,o=!1;while(n--){const r=t[n];e&&!Ye(this,this[r],r,e,!0)||(delete this[r],o=!0)}return o}normalize(e){const t=this,n={};return le.forEach(this,((o,r)=>{const l=le.findKey(n,r);if(l)return t[l]=We(o),void delete t[r];const i=e?Je(r):String(r).trim();i!==r&&delete t[r],t[i]=We(o),n[i]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return le.forEach(this,((n,o)=>{null!=n&&!1!==n&&(t[o]=e&&le.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach((e=>n.set(e))),n}static accessor(e){const t=this[ze]=this[ze]={accessors:{}},n=t.accessors,o=this.prototype;function r(e){const t=Ue(e);n[t]||(Xe(o,e),n[t]=!0)}return le.isArray(e)?e.forEach(r):r(e),this}}Ge.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),le.freezeMethods(Ge.prototype),le.freezeMethods(Ge);const Qe=Ge;function et(e,t){const n=this||je,o=t||n,r=Qe.from(o.headers);let l=o.data;return le.forEach(e,(function(e){l=e.call(n,l,r.normalize(),t?t.status:void 0)})),r.normalize(),l}function tt(e){return!(!e||!e.__CANCEL__)}function nt(e,t,n){ue.call(this,null==e?"canceled":e,ue.ERR_CANCELED,t,n),this.name="CanceledError"}le.inherits(nt,ue,{__CANCEL__:!0});const ot=nt;function rt(e,t,n){const o=n.config.validateStatus;n.status&&o&&!o(n.status)?t(new ue("Request failed with status code "+n.status,[ue.ERR_BAD_REQUEST,ue.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const lt=Pe.isStandardBrowserEnv?function(){return{write:function(e,t,n,o,r,l){const i=[];i.push(e+"="+encodeURIComponent(t)),le.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),le.isString(o)&&i.push("path="+o),le.isString(r)&&i.push("domain="+r),!0===l&&i.push("secure"),document.cookie=i.join("; ")},read:function(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}}():function(){return{write:function(){},read:function(){return null},remove:function(){}}}();function it(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function at(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}function st(e,t){return e&&!it(t)?at(e,t):t}const ut=Pe.isStandardBrowserEnv?function(){const e=/(msie|trident)/i.test(navigator.userAgent),t=document.createElement("a");let n;function o(n){let o=n;return e&&(t.setAttribute("href",o),o=t.href),t.setAttribute("href",o),{href:t.href,protocol:t.protocol?t.protocol.replace(/:$/,""):"",host:t.host,search:t.search?t.search.replace(/^\?/,""):"",hash:t.hash?t.hash.replace(/^#/,""):"",hostname:t.hostname,port:t.port,pathname:"/"===t.pathname.charAt(0)?t.pathname:"/"+t.pathname}}return n=o(window.location.href),function(e){const t=le.isString(e)?o(e):e;return t.protocol===n.protocol&&t.host===n.host}}():function(){return function(){return!0}}();function ct(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function dt(e,t){e=e||10;const n=new Array(e),o=new Array(e);let r,l=0,i=0;return t=void 0!==t?t:1e3,function(a){const s=Date.now(),u=o[i];r||(r=s),n[l]=a,o[l]=s;let c=i,d=0;while(c!==l)d+=n[c++],c%=e;if(l=(l+1)%e,l===i&&(i=(i+1)%e),s-r<t)return;const f=u&&s-u;return f?Math.round(1e3*d/f):void 0}}const ft=dt;function pt(e,t){let n=0;const o=ft(50,250);return r=>{const l=r.loaded,i=r.lengthComputable?r.total:void 0,a=l-n,s=o(a),u=l<=i;n=l;const c={loaded:l,total:i,progress:i?l/i:void 0,bytes:a,rate:s||void 0,estimated:s&&i&&u?(i-l)/s:void 0,event:r};c[t?"download":"upload"]=!0,e(c)}}const vt="undefined"!==typeof XMLHttpRequest,ht=vt&&function(e){return new Promise((function(t,n){let o=e.data;const r=Qe.from(e.headers).normalize(),l=e.responseType;let i;function a(){e.cancelToken&&e.cancelToken.unsubscribe(i),e.signal&&e.signal.removeEventListener("abort",i)}le.isFormData(o)&&(Pe.isStandardBrowserEnv||Pe.isStandardBrowserWebWorkerEnv?r.setContentType(!1):r.setContentType("multipart/form-data;",!1));let s=new XMLHttpRequest;if(e.auth){const t=e.auth.username||"",n=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";r.set("Authorization","Basic "+btoa(t+":"+n))}const u=st(e.baseURL,e.url);function c(){if(!s)return;const o=Qe.from("getAllResponseHeaders"in s&&s.getAllResponseHeaders()),r=l&&"text"!==l&&"json"!==l?s.response:s.responseText,i={data:r,status:s.status,statusText:s.statusText,headers:o,config:e,request:s};rt((function(e){t(e),a()}),(function(e){n(e),a()}),i),s=null}if(s.open(e.method.toUpperCase(),ke(u,e.params,e.paramsSerializer),!0),s.timeout=e.timeout,"onloadend"in s?s.onloadend=c:s.onreadystatechange=function(){s&&4===s.readyState&&(0!==s.status||s.responseURL&&0===s.responseURL.indexOf("file:"))&&setTimeout(c)},s.onabort=function(){s&&(n(new ue("Request aborted",ue.ECONNABORTED,e,s)),s=null)},s.onerror=function(){n(new ue("Network Error",ue.ERR_NETWORK,e,s)),s=null},s.ontimeout=function(){let t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const o=e.transitional||Ce;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(new ue(t,o.clarifyTimeoutError?ue.ETIMEDOUT:ue.ECONNABORTED,e,s)),s=null},Pe.isStandardBrowserEnv){const t=(e.withCredentials||ut(u))&&e.xsrfCookieName&&lt.read(e.xsrfCookieName);t&&r.set(e.xsrfHeaderName,t)}void 0===o&&r.setContentType(null),"setRequestHeader"in s&&le.forEach(r.toJSON(),(function(e,t){s.setRequestHeader(t,e)})),le.isUndefined(e.withCredentials)||(s.withCredentials=!!e.withCredentials),l&&"json"!==l&&(s.responseType=e.responseType),"function"===typeof e.onDownloadProgress&&s.addEventListener("progress",pt(e.onDownloadProgress,!0)),"function"===typeof e.onUploadProgress&&s.upload&&s.upload.addEventListener("progress",pt(e.onUploadProgress)),(e.cancelToken||e.signal)&&(i=t=>{s&&(n(!t||t.type?new ot(null,e,s):t),s.abort(),s=null)},e.cancelToken&&e.cancelToken.subscribe(i),e.signal&&(e.signal.aborted?i():e.signal.addEventListener("abort",i)));const d=ct(u);d&&-1===Pe.protocols.indexOf(d)?n(new ue("Unsupported protocol "+d+":",ue.ERR_BAD_REQUEST,e)):s.send(o||null)}))},mt={http:ce,xhr:ht};le.forEach(mt,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(n){}Object.defineProperty(e,"adapterName",{value:t})}}));const gt={getAdapter:e=>{e=le.isArray(e)?e:[e];const{length:t}=e;let n,o;for(let r=0;r<t;r++)if(n=e[r],o=le.isString(n)?mt[n.toLowerCase()]:n)break;if(!o){if(!1===o)throw new ue(`Adapter ${n} is not supported by the environment`,"ERR_NOT_SUPPORT");throw new Error(le.hasOwnProp(mt,n)?`Adapter '${n}' is not available in the build`:`Unknown adapter '${n}'`)}if(!le.isFunction(o))throw new TypeError("adapter is not a function");return o},adapters:mt};function bt(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ot(null,e)}function yt(e){bt(e),e.headers=Qe.from(e.headers),e.data=et.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);const t=gt.getAdapter(e.adapter||je.adapter);return t(e).then((function(t){return bt(e),t.data=et.call(e,e.transformResponse,t),t.headers=Qe.from(t.headers),t}),(function(t){return tt(t)||(bt(e),t&&t.response&&(t.response.data=et.call(e,e.transformResponse,t.response),t.response.headers=Qe.from(t.response.headers))),Promise.reject(t)}))}const _t=e=>e instanceof Qe?e.toJSON():e;function wt(e,t){t=t||{};const n={};function o(e,t,n){return le.isPlainObject(e)&&le.isPlainObject(t)?le.merge.call({caseless:n},e,t):le.isPlainObject(t)?le.merge({},t):le.isArray(t)?t.slice():t}function r(e,t,n){return le.isUndefined(t)?le.isUndefined(e)?void 0:o(void 0,e,n):o(e,t,n)}function l(e,t){if(!le.isUndefined(t))return o(void 0,t)}function i(e,t){return le.isUndefined(t)?le.isUndefined(e)?void 0:o(void 0,e):o(void 0,t)}function a(n,r,l){return l in t?o(n,r):l in e?o(void 0,n):void 0}const s={url:l,method:l,data:l,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(e,t)=>r(_t(e),_t(t),!0)};return le.forEach(Object.keys(Object.assign({},e,t)),(function(o){const l=s[o]||r,i=l(e[o],t[o],o);le.isUndefined(i)&&l!==a||(n[o]=i)})),n}const St="1.4.0",kt={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{kt[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const xt={};function Et(e,t,n){if("object"!==typeof e)throw new ue("options must be an object",ue.ERR_BAD_OPTION_VALUE);const o=Object.keys(e);let r=o.length;while(r-- >0){const l=o[r],i=t[l];if(i){const t=e[l],n=void 0===t||i(t,l,e);if(!0!==n)throw new ue("option "+l+" must be "+n,ue.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new ue("Unknown option "+l,ue.ERR_BAD_OPTION)}}kt.transitional=function(e,t,n){function o(e,t){return"[Axios v"+St+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,r,l)=>{if(!1===e)throw new ue(o(r," has been removed"+(t?" in "+t:"")),ue.ERR_DEPRECATED);return t&&!xt[r]&&(xt[r]=!0,console.warn(o(r," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,r,l)}};const Ct={assertOptions:Et,validators:kt},Tt=Ct.validators;class Ot{constructor(e){this.defaults=e,this.interceptors={request:new Ee,response:new Ee}}request(e,t){"string"===typeof e?(t=t||{},t.url=e):t=e||{},t=wt(this.defaults,t);const{transitional:n,paramsSerializer:o,headers:r}=t;let l;void 0!==n&&Ct.assertOptions(n,{silentJSONParsing:Tt.transitional(Tt.boolean),forcedJSONParsing:Tt.transitional(Tt.boolean),clarifyTimeoutError:Tt.transitional(Tt.boolean)},!1),null!=o&&(le.isFunction(o)?t.paramsSerializer={serialize:o}:Ct.assertOptions(o,{encode:Tt.function,serialize:Tt.function},!0)),t.method=(t.method||this.defaults.method||"get").toLowerCase(),l=r&&le.merge(r.common,r[t.method]),l&&le.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete r[e]})),t.headers=Qe.concat(l,r);const i=[];let a=!0;this.interceptors.request.forEach((function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,i.unshift(e.fulfilled,e.rejected))}));const s=[];let u;this.interceptors.response.forEach((function(e){s.push(e.fulfilled,e.rejected)}));let c,d=0;if(!a){const e=[yt.bind(this),void 0];e.unshift.apply(e,i),e.push.apply(e,s),c=e.length,u=Promise.resolve(t);while(d<c)u=u.then(e[d++],e[d++]);return u}c=i.length;let f=t;d=0;while(d<c){const e=i[d++],t=i[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{u=yt.call(this,f)}catch(p){return Promise.reject(p)}d=0,c=s.length;while(d<c)u=u.then(s[d++],s[d++]);return u}getUri(e){e=wt(this.defaults,e);const t=st(e.baseURL,e.url);return ke(t,e.params,e.paramsSerializer)}}le.forEach(["delete","get","head","options"],(function(e){Ot.prototype[e]=function(t,n){return this.request(wt(n||{},{method:e,url:t,data:(n||{}).data}))}})),le.forEach(["post","put","patch"],(function(e){function t(t){return function(n,o,r){return this.request(wt(r||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:o}))}}Ot.prototype[e]=t(),Ot.prototype[e+"Form"]=t(!0)}));const Ft=Ot;class Lt{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;while(t-- >0)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const o=new Promise((e=>{n.subscribe(e),t=e})).then(e);return o.cancel=function(){n.unsubscribe(t)},o},e((function(e,o,r){n.reason||(n.reason=new ot(e,o,r),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}static source(){let e;const t=new Lt((function(t){e=t}));return{token:t,cancel:e}}}const At=Lt;function Pt(e){return function(t){return e.apply(null,t)}}function qt(e){return le.isObject(e)&&!0===e.isAxiosError}const Rt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Rt).forEach((([e,t])=>{Rt[t]=e}));const Nt=Rt;function Mt(e){const t=new Ft(e),n=o(Ft.prototype.request,t);return le.extend(n,Ft.prototype,t,{allOwnKeys:!0}),le.extend(n,t,null,{allOwnKeys:!0}),n.create=function(t){return Mt(wt(e,t))},n}const It=Mt(je);It.Axios=Ft,It.CanceledError=ot,It.CancelToken=At,It.isCancel=tt,It.VERSION=St,It.toFormData=ge,It.AxiosError=ue,It.Cancel=It.CanceledError,It.all=function(e){return Promise.all(e)},It.spread=Pt,It.isAxiosError=qt,It.mergeConfig=wt,It.AxiosHeaders=Qe,It.formToJSON=e=>Ie(le.isHTMLForm(e)?new FormData(e):e),It.HttpStatusCode=Nt,It.default=It;const $t=It}}]);