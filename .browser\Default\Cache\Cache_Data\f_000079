{"html": "\n   <article>\n    \n    <div class=\"header\">\n     <div class=\"headertitle\">\n      <h1 class=\"title\">\n       Sleep Timer\n      </h1>\n     </div>\n    </div>\n    <div class=\"contents\">\n     <a id=\"details\" name=\"details\">\n     </a>\n     <h2 class=\"groupheader\">\n      Description\n     </h2>\n     <p>\n      Sleep Timer can be used for creating timers which are tightly integrated with power management. The Power Manager requires precision timing to have all clocks ready on time, so that wakeup happens a little bit earlier to prepare the system to be ready at the right time. Sleep Timer uses one Hardware Timer and creates multiple software timer instances.\n     </p>\n     <p>\n      The sleeptimer.c and sleeptimer.h source files for the SLEEPTIMER device driver library are in the service/sleeptimer folder.\n     </p>\n     <ul>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#sleeptimer-intro\" target=\"_blank\">\n        Introduction\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#sleeptimer-functionalities-overview\" target=\"_blank\">\n        Functionalities overview\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#sleeptimer-getting-started\" target=\"_blank\">\n        Getting Started\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#sleeptimer-conf\" target=\"_blank\">\n        Configuration Options\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#sleeptimer-api\" target=\"_blank\">\n        The API\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#sleeptimer-example\" target=\"_blank\">\n        Example\n       </a>\n      </li>\n     </ul>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"sleeptimer-intro\">\n      </a>\n      Introduction\n     </h1>\n     <p>\n      The Sleeptimer driver provides software timers, delays, timekeeping and date functionalities using a low-frequency real-time clock peripheral.\n     </p>\n     <p>\n      All Silicon Labs microcontrollers equipped with the RTC or RTCC peripheral are currently supported. Only one instance of this driver can be initialized by the application.\n     </p>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"sleeptimer-functionalities-overview\">\n      </a>\n      Functionalities overview\n     </h1>\n     <p>\n      <br>\n     </p>\n     <h2>\n      <a class=\"anchor\" id=\"software-timers\">\n      </a>\n      Software Timers\n     </h2>\n     <p>\n      This functionality allows the user to create periodic and one shot timers. A user callback can be associated with a timer and is called when the timer expires.\n     </p>\n     <p>\n      Timer structures must be allocated by the user. The function is called from within an interrupt handler with interrupts enabled.\n     </p>\n     <p>\n      <br>\n     </p>\n     <h2>\n      <a class=\"anchor\" id=\"timekeeping\">\n      </a>\n      Timekeeping\n     </h2>\n     <p>\n      A 64-bits tick counter is accessible through the\n     </p>\n     <ul>\n      <li>\n       uint64_t\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaaf928eeb3dad0d43098b3aed9049959a\" title=\"Gets current 64 bits global tick count.\" target=\"_blank\">\n        sl_sleeptimer_get_tick_count64(void)\n       </a>\n       API. It keeps the tick count since the initialization of the driver\n      </li>\n     </ul>\n     <p>\n      The\n      <code>\n       SL_SLEEPTIMER_WALLCLOCK_CONFIG\n      </code>\n      configuration enables a UNIX timestamp (seconds count since January 1, 1970, 00:00:00).\n     </p>\n     <p>\n      This timestamp can be retrieved/modified using the following API:\n     </p>\n     <ul>\n      <li>\n       sl_sleeptimer_timestamp_t\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga4d79ef7aa10606814bcc21053bfca558\" title=\"Retrieves current time.\" target=\"_blank\">\n        sl_sleeptimer_get_time(void)\n       </a>\n       ;\n      </li>\n      <li>\n       sl_status_t\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gafbd64c7fbf7b0dfb7947a6f7bf288dca\" title=\"Sets current time.\" target=\"_blank\">\n        sl_sleeptimer_set_time(sl_sleeptimer_timestamp_t time)\n       </a>\n       ;\n      </li>\n     </ul>\n     <p>\n      Convenience conversion functions are provided to convert UNIX timestamp to/from NTP and Zigbee cluster format :\n     </p>\n     <ul>\n      <li>\n       sl_status_t\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf63bbad4e29c0b655b3d210bf3b3b086\" title=\"Converts Unix timestamp into NTP timestamp.\" target=\"_blank\">\n        sl_sleeptimer_convert_unix_time_to_ntp(sl_sleeptimer_timestamp_t time, uint32_t *ntp_time)\n       </a>\n       ;\n      </li>\n      <li>\n       sl_status_t\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaff086ed922350c8a4cffeed024d03871\" title=\"Converts NTP timestamp into Unix timestamp.\" target=\"_blank\">\n        sl_sleeptimer_convert_ntp_time_to_unix(uint32_t ntp_time, sl_sleeptimer_timestamp_t *time)\n       </a>\n       ;\n      </li>\n      <li>\n       sl_status_t\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaa73b94e0c15a1e2cf6b3cb0b37120ed4\" title=\"Converts Unix timestamp into Zigbee timestamp.\" target=\"_blank\">\n        sl_sleeptimer_convert_unix_time_to_zigbee(sl_sleeptimer_timestamp_t time, uint32_t *zigbee_time)\n       </a>\n       ;\n      </li>\n      <li>\n       sl_status_t\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaab1267658b2ac5e4249e87412ce7dede\" title=\"Converts Zigbee timestamp into Unix timestamp.\" target=\"_blank\">\n        sl_sleeptimer_convert_zigbee_time_to_unix(uint32_t zigbee_time, sl_sleeptimer_timestamp_t *time)\n       </a>\n       ;\n      </li>\n     </ul>\n     <p>\n      <br>\n     </p>\n     <h2>\n      <a class=\"anchor\" id=\"date\">\n      </a>\n      Date\n     </h2>\n     <p>\n      The previously described internal timestamp can also be retrieved/modified in a date format sl_sleeptimer_date_t.\n     </p>\n     <p>\n      <br>\n      <b>\n       API :\n      </b>\n      <br>\n     </p>\n     <ul>\n      <li>\n       sl_status_t\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga2955c70feb8e0082ba54e30d2629d840\" title=\"Gets current date.\" target=\"_blank\">\n        sl_sleeptimer_get_datetime(sl_sleeptimer_date_t *date)\n       </a>\n       ;\n      </li>\n      <li>\n       sl_status_t\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga840c8ef8049b364a45899320a9f0c8bf\" title=\"Sets current time, in date format.\" target=\"_blank\">\n        sl_sleeptimer_set_datetime(sl_sleeptimer_date_t *date)\n       </a>\n       ;\n      </li>\n     </ul>\n     <p>\n      <br>\n     </p>\n     <h2>\n      <a class=\"anchor\" id=\"frequency-setup\">\n      </a>\n      Frequency Setup and Tick Count\n     </h2>\n     <p>\n      This driver works with a configurable time unit called tick.\n     </p>\n     <p>\n      The frequency of the ticks is based on the clock source and the internal frequency divider.\n     </p>\n     <p>\n      One of the following clock sources must be enabled before initializing the sleeptimer:\n     </p>\n     <ul>\n      <li>\n       LFXO: external crystal oscillator. Typically running at 32.768 kHz.\n      </li>\n      <li>\n       LFRCO: internal oscillator running at 32.768 kHz\n      </li>\n      <li>\n       ULFRCO: Ultra low-frequency oscillator running at 1.000 kHz\n      </li>\n     </ul>\n     <p>\n      The frequency divider is selected with the\n      <code>\n       SL_SLEEPTIMER_FREQ_DIVIDER\n      </code>\n      configuration. Its value must be a power of two within the range of 1 to 32. The number of ticks per second (sleeptimer frequency) is dictated by the following formula:\n     </p>\n     <p>\n      Tick (seconds) = 1 / (clock_frequency / frequency_divider)\n     </p>\n     <p>\n      The highest resolution for a tick is 30.5 us. It is achieved with a 32.768 kHz clock and a divider of 1.\n     </p>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"sleeptimer-getting-started\">\n      </a>\n      Getting Started\n     </h1>\n     <p>\n      <br>\n     </p>\n     <h2>\n      <a class=\"anchor\" id=\"clock-selection\">\n      </a>\n      Clock Selection\n     </h2>\n     <p>\n      The sleeptimer relies on the hardware timer to operate. The hardware timer peripheral must be properly clocked from the application. Selecting the appropriate timer is crucial for design considerations. Each timer can potentially be used as a sleeptimer and is also available to the user. However, note that if a timer is used by the sleeptimer, it can't be used by the application and vice versa.\n     </p>\n     <p>\n      <br>\n     </p>\n     <h2>\n      <a class=\"anchor\" id=\"Clock\">\n      </a>\n      Selection in a Project without Micrium OS\n     </h2>\n     <p>\n      When RTC, RTCC, or BURTC is selected, the clock source for the peripheral must be configured and enabled in the application before initializing the sleeptimer module or any communication stacks. Most of the time, it consists in enabling the desired oscillators and setting up the clock source for the peripheral, like in the following example:\n     </p>\n     <div class=\"fragment\">\n      <div class=\"line\">\n       CMU_ClockSelectSet(cmuClock_LFE, cmuSelect_LFRCO);\n      </div>\n      <div class=\"line\">\n       CMU_ClockEnable(cmuClock_RTCC,\n       <span class=\"keyword\">\n        true\n       </span>\n       );\n      </div>\n     </div>\n     <p>\n      <br>\n     </p>\n     <h2>\n      <a class=\"anchor\" id=\"clock-branch-select\">\n      </a>\n      Clock Branch Select\n     </h2>\n     <table class=\"markdownTable\">\n      <tbody>\n       <tr class=\"markdownTableHead\">\n        <th class=\"markdownTableHeadNone\">\n         Clock\n        </th>\n        <th class=\"markdownTableHeadNone\">\n         Enum\n        </th>\n        <th class=\"markdownTableHeadNone\">\n         Description\n        </th>\n        <th class=\"markdownTableHeadNone\">\n         Frequency\n        </th>\n       </tr>\n       <tr class=\"markdownTableRowOdd\">\n        <td class=\"markdownTableBodyNone\">\n         LFXO\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         <b>\n          cmuSelect_LFXO\n         </b>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         Low-frequency crystal oscillator\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         32.768 Khz\n        </td>\n       </tr>\n       <tr class=\"markdownTableRowEven\">\n        <td class=\"markdownTableBodyNone\">\n         LFRCO\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         <b>\n          cmuSelect_LFRCO\n         </b>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         Low-frequency RC oscillator\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         32.768 Khz\n        </td>\n       </tr>\n       <tr class=\"markdownTableRowOdd\">\n        <td class=\"markdownTableBodyNone\">\n         ULFRCO\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         <b>\n          cmuSelect_ULFRCO\n         </b>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         Ultra low-frequency RC oscillator\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         1 Khz\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <p>\n      <br>\n     </p>\n     <h2>\n      <a class=\"anchor\" id=\"timer-clock-enable\">\n      </a>\n      Timer Clock Enable\n     </h2>\n     <table class=\"markdownTable\">\n      <tbody>\n       <tr class=\"markdownTableHead\">\n        <th class=\"markdownTableHeadNone\">\n         Module\n        </th>\n        <th class=\"markdownTableHeadNone\">\n         Enum\n        </th>\n        <th class=\"markdownTableHeadNone\">\n         Description\n        </th>\n       </tr>\n       <tr class=\"markdownTableRowOdd\">\n        <td class=\"markdownTableBodyNone\">\n         RTCC\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         <b>\n          cmuClock_RTCC\n         </b>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         Real-time counter and calendar clock (LF E branch)\n        </td>\n       </tr>\n       <tr class=\"markdownTableRowEven\">\n        <td class=\"markdownTableBodyNone\">\n         RTC\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         <b>\n          cmuClock_RTC\n         </b>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         Real time counter clock (LF A branch)\n        </td>\n       </tr>\n       <tr class=\"markdownTableRowOdd\">\n        <td class=\"markdownTableBodyNone\">\n         BURTC\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         <b>\n          cmuClock_BURTC\n         </b>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         BURTC clock (EM4 Group A branch)\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <p>\n      When the Radio internal RTC (PRORTC) is selected, it is not necessary to configure the clock source for the peripheral. However, it is important to enable the desired oscillator before initializing the sleeptimer module or any communication stacks. The best oscillator available (LFXO being the first choice) will be used by the sleeptimer at initalization. The following example shows how the desired oscilator should be enabled:\n     </p>\n     <div class=\"fragment\">\n      <div class=\"line\">\n       CMU_OscillatorEnable(cmuSelect_LFXO,\n       <span class=\"keyword\">\n        true\n       </span>\n       ,\n       <span class=\"keyword\">\n        true\n       </span>\n       );\n      </div>\n     </div>\n     <p>\n      <br>\n     </p>\n     <h2>\n      <a class=\"anchor\" id=\"clock-micrium-os\">\n      </a>\n      Clock Selection in a Project with Micrium OS\n     </h2>\n     <p>\n      When Micrium OS is used, a BSP (all instances) is provided that sets up some parts of the clock tree. The sleeptimer clock source will be enabled by this bsp. However, the desired oscillator remains configurable from the file\n      <b>\n       bsp_cfg.h\n      </b>\n      .\n     </p>\n     <p>\n      The configuration\n      <code>\n       BSP_LF_CLK_SEL\n      </code>\n      determines which oscillator will be used by the sleeptimer's hardware timer peripheral. It can take the following values:\n     </p>\n     <table class=\"markdownTable\">\n      <tbody>\n       <tr class=\"markdownTableHead\">\n        <th class=\"markdownTableHeadNone\">\n         Config\n        </th>\n        <th class=\"markdownTableHeadNone\">\n         Description\n        </th>\n        <th class=\"markdownTableHeadNone\">\n         Frequency\n        </th>\n       </tr>\n       <tr class=\"markdownTableRowOdd\">\n        <td class=\"markdownTableBodyNone\">\n         <b>\n          BSP_LF_CLK_LFXO\n         </b>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         Low-frequency crystal oscillator\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         32.768 Khz\n        </td>\n       </tr>\n       <tr class=\"markdownTableRowEven\">\n        <td class=\"markdownTableBodyNone\">\n         <b>\n          BSP_LF_CLK_LFRCO\n         </b>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         Low-frequency RC oscillator\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         32.768 Khz\n        </td>\n       </tr>\n       <tr class=\"markdownTableRowOdd\">\n        <td class=\"markdownTableBodyNone\">\n         <b>\n          BSP_LF_CLK_ULFRCO\n         </b>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         Ultra low-frequency RC oscillator\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         1 Khz\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"sleeptimer-conf\">\n      </a>\n      Configuration Options\n     </h1>\n     <p>\n      <code>\n       SL_SLEEPTIMER_PERIPHERAL\n      </code>\n      can be set to one of the following values:\n     </p>\n     <table class=\"markdownTable\">\n      <tbody>\n       <tr class=\"markdownTableHead\">\n        <th class=\"markdownTableHeadNone\">\n         Config\n        </th>\n        <th class=\"markdownTableHeadNone\">\n         Description\n        </th>\n       </tr>\n       <tr class=\"markdownTableRowOdd\">\n        <td class=\"markdownTableBodyNone\">\n         <code>\n          SL_SLEEPTIMER_PERIPHERAL_DEFAULT\n         </code>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         Selects either RTC or RTCC, depending of what is available on the platform.\n        </td>\n       </tr>\n       <tr class=\"markdownTableRowEven\">\n        <td class=\"markdownTableBodyNone\">\n         <code>\n          SL_SLEEPTIMER_PERIPHERAL_RTCC\n         </code>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         Selects RTCC\n        </td>\n       </tr>\n       <tr class=\"markdownTableRowOdd\">\n        <td class=\"markdownTableBodyNone\">\n         <code>\n          SL_SLEEPTIMER_PERIPHERAL_RTC\n         </code>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         Selects RTC\n        </td>\n       </tr>\n       <tr class=\"markdownTableRowEven\">\n        <td class=\"markdownTableBodyNone\">\n         <code>\n          SL_SLEEPTIMER_PERIPHERAL_PRORTC\n         </code>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         Selects Internal radio RTC. Available only on EFR32XG13, EFR32XG14, EFR32XG21 and EFR32XG22 families.\n        </td>\n       </tr>\n       <tr class=\"markdownTableRowOdd\">\n        <td class=\"markdownTableBodyNone\">\n         <code>\n          SL_SLEEPTIMER_PERIPHERAL_BURTC\n         </code>\n        </td>\n        <td class=\"markdownTableBodyNone\">\n         Selects BURTC. Not available on Series 0 devices.\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <p>\n      <code>\n       SL_SLEEPTIMER_WALLCLOCK_CONFIG\n      </code>\n      must be set to 1 to enable timestamp and date functionnalities.\n     </p>\n     <p>\n      <code>\n       SL_SLEEPTIMER_FREQ_DIVIDER\n      </code>\n      must be a power of 2 within the range 1 to 32. When\n      <code>\n       SL_SLEEPTIMER_PERIPHERAL\n      </code>\n      is set to\n      <code>\n       SL_SLEEPTIMER_PERIPHERAL_PRORTC\n      </code>\n      ,\n      <code>\n       SL_SLEEPTIMER_FREQ_DIVIDER\n      </code>\n      must be set to 1.\n     </p>\n     <p>\n      <code>\n       SL_SLEEPTIMER_PRORTC_HAL_OWNS_IRQ_HANDLER\n      </code>\n      is only meaningful when\n      <code>\n       SL_SLEEPTIMER_PERIPHERAL\n      </code>\n      is set to\n      <code>\n       SL_SLEEPTIMER_PERIPHERAL_PRORTC\n      </code>\n      . Set to 1 if no communication stack is used in your project. Otherwise, must be set to 0.\n     </p>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"sleeptimer-api\">\n      </a>\n      The API\n     </h1>\n     <p>\n      This section contains brief descriptions of the API functions. For more information about input and output parameters and return values, click on the hyperlinked function names. Most functions return an error code,\n      <code>\n       SL_STATUS_OK\n      </code>\n      is returned on success, see sl_status.h for other error codes.\n     </p>\n     <p>\n      The application code must include the\n      <em>\n       sl_sleeptimer.h\n      </em>\n      header file.\n     </p>\n     <p>\n      All API functions can be called from within interrupt handlers.\n     </p>\n     <p>\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gae661a78719a6acc014efa15216ae4a54\" target=\"_blank\">\n       sl_sleeptimer_init()\n      </a>\n      <br>\n      These functions initialize the sleeptimer driver. Typically, sl_sleeptimer_init() is called once in the startup code.\n     </p>\n     <p>\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga43c32bdb2a9fcfbe279563a9fd689711\" target=\"_blank\">\n       sl_sleeptimer_start_timer()\n      </a>\n      <br>\n      Start a one shot 32 bits timer. When a timer expires, a user-supplied callback function is called. A pointer to this function is passed to sl_sleeptimer_start_timer(). See\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#callback\" target=\"_blank\">\n       callback\n      </a>\n      for details of the callback prototype.\n     </p>\n     <p>\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaacd5a666731a0ba4823107e552e23e69\" target=\"_blank\">\n       sl_sleeptimer_restart_timer()\n      </a>\n      <br>\n      Restart a one shot 32 bits timer. When a timer expires, a user-supplied callback function is called. A pointer to this function is passed to sl_sleeptimer_start_timer(). See\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#callback\" target=\"_blank\">\n       callback\n      </a>\n      for details of the callback prototype.\n     </p>\n     <p>\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga2ceb1b76334902525dd32e937e90bb16\" target=\"_blank\">\n       sl_sleeptimer_start_periodic_timer()\n      </a>\n      <br>\n      Start a periodic 32 bits timer. When a timer expires, a user-supplied callback function is called. A pointer to this function is passed to sl_sleeptimer_start_timer(). See\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#callback\" target=\"_blank\">\n       callback\n      </a>\n      for details of the callback prototype.\n     </p>\n     <p>\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf54408a7f172b684de33d9a7f4e01be9\" target=\"_blank\">\n       sl_sleeptimer_restart_periodic_timer()\n      </a>\n      <br>\n      Restart a periodic 32 bits timer. When a timer expires, a user-supplied callback function is called. A pointer to this function is passed to sl_sleeptimer_start_timer(). See\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#callback\" target=\"_blank\">\n       callback\n      </a>\n      for details of the callback prototype.\n     </p>\n     <p>\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga1f4f25aa754cd35214dba9762450b0ca\" target=\"_blank\">\n       sl_sleeptimer_stop_timer()\n      </a>\n      <br>\n      Stop a timer.\n     </p>\n     <p>\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga6a06acb331e2e3c0efeb83f42a4d9c16\" target=\"_blank\">\n       sl_sleeptimer_get_timer_time_remaining()\n      </a>\n      <br>\n      Get the time remaining before the timer expires.\n     </p>\n     <p>\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga7d7c4ae596755e3e5e3c0c0aa5e749b3\" target=\"_blank\">\n       sl_sleeptimer_delay_millisecond()\n      </a>\n      <br>\n      Delay for the given number of milliseconds. This is an \"active wait\" delay function.\n     </p>\n     <p>\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gac0236c1c353317c41d577867e7429874\" target=\"_blank\">\n       sl_sleeptimer_is_timer_running()\n      </a>\n      <br>\n      Check if a timer is running.\n     </p>\n     <p>\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga4d79ef7aa10606814bcc21053bfca558\" target=\"_blank\">\n       sl_sleeptimer_get_time()\n      </a>\n      ,\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gafbd64c7fbf7b0dfb7947a6f7bf288dca\" target=\"_blank\">\n       sl_sleeptimer_set_time()\n      </a>\n      <br>\n      Get or set wallclock time.\n     </p>\n     <p>\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gadf3a3eb05b0f5adbc7ee488113299f61\" target=\"_blank\">\n       sl_sleeptimer_ms_to_tick()\n      </a>\n      ,\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga7484d4be4877b94a687f4410fccc6271\" target=\"_blank\">\n       sl_sleeptimer_ms32_to_tick()\n      </a>\n      ,\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga2c2a2a131e401894d3e09513442667b5\" target=\"_blank\">\n       sl_sleeptimer_tick_to_ms()\n      </a>\n      ,\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga6882d0577137d8e20ec531be45678fba\" target=\"_blank\">\n       sl_sleeptimer_tick64_to_ms()\n      </a>\n      <br>\n      Convert between milliseconds and RTC/RTCC counter ticks.\n     </p>\n     <p>\n      <br>\n      <a class=\"anchor\" id=\"callback\">\n      </a>\n      <b>\n       The timer expiry callback function:\n      </b>\n      <br>\n      The callback function, prototyped as\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n       sl_sleeptimer_timer_callback_t()\n      </a>\n      , is called from within the RTC peripheral interrupt handler on timer expiration. sl_sleeptimer_timer_callback_t(sl_sleeptimer_timer_handle_t *handle, void *data)\n     </p>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"sleeptimer-example\">\n      </a>\n      Example\n     </h1>\n     <div class=\"fragment\">\n      <div class=\"line\">\n       <span class=\"preprocessor\">\n        #include \"sl_sleeptimer.h\"\n       </span>\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"keywordtype\">\n        void\n       </span>\n       my_timer_callback(sl_sleeptimer_timer_handle_t *handle,\n       <span class=\"keywordtype\">\n        void\n       </span>\n       *data)\n      </div>\n      <div class=\"line\">\n       {\n      </div>\n      <div class=\"line\">\n       <span class=\"comment\">\n        //Code executed when the timer expire.\n       </span>\n      </div>\n      <div class=\"line\">\n       }\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"keywordtype\">\n        int\n       </span>\n       start_timer(\n       <span class=\"keywordtype\">\n        void\n       </span>\n       )\n      </div>\n      <div class=\"line\">\n       {\n      </div>\n      <div class=\"line\">\n       sl_status_t status;\n      </div>\n      <div class=\"line\">\n       sl_sleeptimer_timer_handle_t my_timer;\n      </div>\n      <div class=\"line\">\n       uint32_t timer_timeout = 300;\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       <span class=\"comment\">\n        // We assume the sleeptimer is initialized properly\n       </span>\n      </div>\n      <div class=\"line\">\n      </div>\n      <div class=\"line\">\n       status =\n       <a class=\"code\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga43c32bdb2a9fcfbe279563a9fd689711\" target=\"_blank\">\n        sl_sleeptimer_start_timer\n       </a>\n       (&amp;my_timer,\n      </div>\n      <div class=\"line\">\n       timer_timeout,\n      </div>\n      <div class=\"line\">\n       my_timer_callback,\n      </div>\n      <div class=\"line\">\n       (\n       <span class=\"keywordtype\">\n        void\n       </span>\n       *)NULL,\n      </div>\n      <div class=\"line\">\n       0,\n      </div>\n      <div class=\"line\">\n       0);\n      </div>\n      <div class=\"line\">\n       <span class=\"keywordflow\">\n        if\n       </span>\n       (status != SL_STATUS_OK) {\n      </div>\n      <div class=\"line\">\n       <span class=\"keywordflow\">\n        return\n       </span>\n       -1;\n      </div>\n      <div class=\"line\">\n       }\n      </div>\n      <div class=\"line\">\n       <span class=\"keywordflow\">\n        return\n       </span>\n       1;\n      </div>\n      <div class=\"line\">\n       }\n      </div>\n     </div>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"nested-classes\">\n          </a>\n          Data Structures\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/structsl-sleeptimer-timer-handle\" target=\"_blank\">\n          sl_sleeptimer_timer_handle_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Timer structure for sleeptimer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/structtime-date\" target=\"_blank\">\n          time_date\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Time and Date structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"func-members\">\n          </a>\n          Functions\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga147e840cf5d02d54a06e16bccb8e088d\" target=\"_blank\">\n          SLEEPTIMER_ENUM\n         </a>\n         (sl_sleeptimer_month_t)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescRight\">\n         Month enum.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gae1c4c08acb9f670ceb295a6eca059d31\" target=\"_blank\">\n          SLEEPTIMER_ENUM\n         </a>\n         (sl_sleeptimer_weekDay_t)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescRight\">\n         Week Day enum.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gae661a78719a6acc014efa15216ae4a54\" target=\"_blank\">\n          sl_sleeptimer_init\n         </a>\n         (void)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Initializes the Sleeptimer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga43c32bdb2a9fcfbe279563a9fd689711\" target=\"_blank\">\n          sl_sleeptimer_start_timer\n         </a>\n         (sl_sleeptimer_timer_handle_t *handle, uint32_t timeout,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n          sl_sleeptimer_timer_callback_t\n         </a>\n         callback, void *callback_data, uint8_t priority, uint16_t option_flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Starts a 32 bits timer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaacd5a666731a0ba4823107e552e23e69\" target=\"_blank\">\n          sl_sleeptimer_restart_timer\n         </a>\n         (sl_sleeptimer_timer_handle_t *handle, uint32_t timeout,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n          sl_sleeptimer_timer_callback_t\n         </a>\n         callback, void *callback_data, uint8_t priority, uint16_t option_flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Restarts a 32 bits timer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga2ceb1b76334902525dd32e937e90bb16\" target=\"_blank\">\n          sl_sleeptimer_start_periodic_timer\n         </a>\n         (sl_sleeptimer_timer_handle_t *handle, uint32_t timeout,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n          sl_sleeptimer_timer_callback_t\n         </a>\n         callback, void *callback_data, uint8_t priority, uint16_t option_flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Starts a 32 bits periodic timer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf54408a7f172b684de33d9a7f4e01be9\" target=\"_blank\">\n          sl_sleeptimer_restart_periodic_timer\n         </a>\n         (sl_sleeptimer_timer_handle_t *handle, uint32_t timeout,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n          sl_sleeptimer_timer_callback_t\n         </a>\n         callback, void *callback_data, uint8_t priority, uint16_t option_flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Restarts a 32 bits periodic timer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga1f4f25aa754cd35214dba9762450b0ca\" target=\"_blank\">\n          sl_sleeptimer_stop_timer\n         </a>\n         (sl_sleeptimer_timer_handle_t *handle)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Stops a timer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gac0236c1c353317c41d577867e7429874\" target=\"_blank\">\n          sl_sleeptimer_is_timer_running\n         </a>\n         (sl_sleeptimer_timer_handle_t *handle, bool *running)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Gets the status of a timer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga6a06acb331e2e3c0efeb83f42a4d9c16\" target=\"_blank\">\n          sl_sleeptimer_get_timer_time_remaining\n         </a>\n         (sl_sleeptimer_timer_handle_t *handle, uint32_t *time)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Gets remaining time until timer expires.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf96ccea3e4d1727dc2fd2fa98f94ccb1\" target=\"_blank\">\n          sl_sleeptimer_get_remaining_time_of_first_timer\n         </a>\n         (uint16_t option_flags, uint32_t *time_remaining)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Gets the time remaining until the first timer with the matching set of flags expires.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gae0397d420800b7c2d2afd5bbb9710067\" target=\"_blank\">\n          sl_sleeptimer_get_tick_count\n         </a>\n         (void)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Gets current 32 bits global tick count.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint64_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaaf928eeb3dad0d43098b3aed9049959a\" target=\"_blank\">\n          sl_sleeptimer_get_tick_count64\n         </a>\n         (void)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Gets current 64 bits global tick count.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga69085e47b9cc43ac0cfdc08783c13aed\" target=\"_blank\">\n          sl_sleeptimer_get_timer_frequency\n         </a>\n         (void)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get timer frequency.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga157d43fdb64cb0d4afc864155bcf9bc8\" target=\"_blank\">\n          sl_sleeptimer_convert_time_to_date\n         </a>\n         (\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n          sl_sleeptimer_timestamp_t\n         </a>\n         time,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n          sl_sleeptimer_time_zone_offset_t\n         </a>\n         time_zone, sl_sleeptimer_date_t *date)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Converts a Unix timestamp into a date.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gab3c762fbb3fed88f5f256d4c9a03fc67\" target=\"_blank\">\n          sl_sleeptimer_convert_date_to_time\n         </a>\n         (sl_sleeptimer_date_t *date,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n          sl_sleeptimer_timestamp_t\n         </a>\n         *time)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Converts a date into a Unix timestamp.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga28561cd8d4b4e31153d3dc02e68f2645\" target=\"_blank\">\n          sl_sleeptimer_convert_date_to_str\n         </a>\n         (char *str, size_t size, const uint8_t *format, sl_sleeptimer_date_t *date)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Convert date to string.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gafbd748d9a4654e9ae474a7020a5d9813\" target=\"_blank\">\n          sl_sleeptimer_set_tz\n         </a>\n         (\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n          sl_sleeptimer_time_zone_offset_t\n         </a>\n         offset)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Sets time zone offset.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n          sl_sleeptimer_time_zone_offset_t\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga91ebac6960e114d933e1ef7c4f32294a\" target=\"_blank\">\n          sl_sleeptimer_get_tz\n         </a>\n         (void)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Gets time zone offset.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n          sl_sleeptimer_timestamp_t\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga4d79ef7aa10606814bcc21053bfca558\" target=\"_blank\">\n          sl_sleeptimer_get_time\n         </a>\n         (void)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Retrieves current time.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gafbd64c7fbf7b0dfb7947a6f7bf288dca\" target=\"_blank\">\n          sl_sleeptimer_set_time\n         </a>\n         (\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n          sl_sleeptimer_timestamp_t\n         </a>\n         time)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Sets current time.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga2955c70feb8e0082ba54e30d2629d840\" target=\"_blank\">\n          sl_sleeptimer_get_datetime\n         </a>\n         (sl_sleeptimer_date_t *date)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Gets current date.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga840c8ef8049b364a45899320a9f0c8bf\" target=\"_blank\">\n          sl_sleeptimer_set_datetime\n         </a>\n         (sl_sleeptimer_date_t *date)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Sets current time, in date format.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga726d2e276fbac02b2246a446c3dcf755\" target=\"_blank\">\n          sl_sleeptimer_build_datetime\n         </a>\n         (sl_sleeptimer_date_t *date, uint16_t year, sl_sleeptimer_month_t month, uint8_t month_day, uint8_t hour, uint8_t min, uint8_t sec,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n          sl_sleeptimer_time_zone_offset_t\n         </a>\n         tzOffset)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Builds a date time structure based on the provided parameters.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf63bbad4e29c0b655b3d210bf3b3b086\" target=\"_blank\">\n          sl_sleeptimer_convert_unix_time_to_ntp\n         </a>\n         (\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n          sl_sleeptimer_timestamp_t\n         </a>\n         time, uint32_t *ntp_time)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Converts Unix timestamp into NTP timestamp.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaff086ed922350c8a4cffeed024d03871\" target=\"_blank\">\n          sl_sleeptimer_convert_ntp_time_to_unix\n         </a>\n         (uint32_t ntp_time,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n          sl_sleeptimer_timestamp_t\n         </a>\n         *time)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Converts NTP timestamp into Unix timestamp.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaa73b94e0c15a1e2cf6b3cb0b37120ed4\" target=\"_blank\">\n          sl_sleeptimer_convert_unix_time_to_zigbee\n         </a>\n         (\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n          sl_sleeptimer_timestamp_t\n         </a>\n         time, uint32_t *zigbee_time)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Converts Unix timestamp into Zigbee timestamp.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaab1267658b2ac5e4249e87412ce7dede\" target=\"_blank\">\n          sl_sleeptimer_convert_zigbee_time_to_unix\n         </a>\n         (uint32_t zigbee_time,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n          sl_sleeptimer_timestamp_t\n         </a>\n         *time)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Converts Zigbee timestamp into Unix timestamp.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n          sl_sleeptimer_time_zone_offset_t\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga612f86ff5d65f445efb71d4a1eef7386\" target=\"_blank\">\n          sl_sleeptimer_set_tz_ahead_utc\n         </a>\n         (uint8_t hours, uint8_t minutes)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Calculates offset for time zone after UTC-0.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n          sl_sleeptimer_time_zone_offset_t\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaae029d2985822af973e4cd0333fa92e1\" target=\"_blank\">\n          sl_sleeptimer_set_tz_behind_utc\n         </a>\n         (uint8_t hours, uint8_t minutes)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Calculates offset for time zone before UTC-0.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga7d7c4ae596755e3e5e3c0c0aa5e749b3\" target=\"_blank\">\n          sl_sleeptimer_delay_millisecond\n         </a>\n         (uint16_t time_ms)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Active delay.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gadf3a3eb05b0f5adbc7ee488113299f61\" target=\"_blank\">\n          sl_sleeptimer_ms_to_tick\n         </a>\n         (uint16_t time_ms)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Converts milliseconds in ticks.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga7484d4be4877b94a687f4410fccc6271\" target=\"_blank\">\n          sl_sleeptimer_ms32_to_tick\n         </a>\n         (uint32_t time_ms, uint32_t *tick)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Converts 32-bits milliseconds in ticks.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga3218039eab8d09231cc092fdc2a1dba5\" target=\"_blank\">\n          sl_sleeptimer_get_max_ms32_conversion\n         </a>\n         (void)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Gets the maximum value that can be passed to the functions that have a 32-bits time or timeout argument expressed in milliseconds.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga2c2a2a131e401894d3e09513442667b5\" target=\"_blank\">\n          sl_sleeptimer_tick_to_ms\n         </a>\n         (uint32_t tick)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Converts ticks in milliseconds.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga6882d0577137d8e20ec531be45678fba\" target=\"_blank\">\n          sl_sleeptimer_tick64_to_ms\n         </a>\n         (uint64_t tick, uint64_t *ms)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Converts 64-bit ticks in milliseconds.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         bool\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga0b38592836a9a01d6f315d417d3d6cd9\" target=\"_blank\">\n          sl_sleeptimer_is_power_manager_early_restore_timer_latest_to_expire\n         </a>\n         (void)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Allow sleep after ISR exit.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gac8c20444c4572f93db0fb3ad3bc2305e\" target=\"_blank\">\n          sl_sleeptimer_start_timer_ms\n         </a>\n         (sl_sleeptimer_timer_handle_t *handle, uint32_t timeout_ms,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n          sl_sleeptimer_timer_callback_t\n         </a>\n         callback, void *callback_data, uint8_t priority, uint16_t option_flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Starts a 32 bits timer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga3f3cefcf3dba6d516ca69444d25d8c09\" target=\"_blank\">\n          sl_sleeptimer_restart_timer_ms\n         </a>\n         (sl_sleeptimer_timer_handle_t *handle, uint32_t timeout_ms,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n          sl_sleeptimer_timer_callback_t\n         </a>\n         callback, void *callback_data, uint8_t priority, uint16_t option_flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Restarts a 32 bits timer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gae4639653b4bf4ed68049b2a12a6c6d83\" target=\"_blank\">\n          sl_sleeptimer_start_periodic_timer_ms\n         </a>\n         (sl_sleeptimer_timer_handle_t *handle, uint32_t timeout_ms,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n          sl_sleeptimer_timer_callback_t\n         </a>\n         callback, void *callback_data, uint8_t priority, uint16_t option_flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Starts a 32 bits periodic timer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga1a44174e7a8eccbabde499875cb8462a\" target=\"_blank\">\n          sl_sleeptimer_restart_periodic_timer_ms\n         </a>\n         (sl_sleeptimer_timer_handle_t *handle, uint32_t timeout_ms,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n          sl_sleeptimer_timer_callback_t\n         </a>\n         callback, void *callback_data, uint8_t priority, uint16_t option_flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Restarts a 32 bits periodic timer.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"typedef-members\">\n          </a>\n          Typedefs\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         typedef uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n          sl_sleeptimer_timestamp_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Timestamp, wall clock time in seconds.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         typedef int32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n          sl_sleeptimer_time_zone_offset_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Time zone offset from UTC(second).\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         typedef void(*\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n          sl_sleeptimer_timer_callback_t\n         </a>\n         ) (sl_sleeptimer_timer_handle_t *handle, void *data)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Typedef for the user supplied callback function which is called when a timer expires.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <h2 class=\"groupheader\">\n      Function Documentation\n     </h2>\n     <a id=\"ga147e840cf5d02d54a06e16bccb8e088d\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga147e840cf5d02d54a06e16bccb8e088d\">\n        ◆\n       </a>\n      </span>\n      SLEEPTIMER_ENUM()\n      <span class=\"overload\">\n       [1/2]\n      </span>\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           SLEEPTIMER_ENUM\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_month_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Month enum.\n       </p>\n      </div>\n     </div>\n     <a id=\"gae1c4c08acb9f670ceb295a6eca059d31\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gae1c4c08acb9f670ceb295a6eca059d31\">\n        ◆\n       </a>\n      </span>\n      SLEEPTIMER_ENUM()\n      <span class=\"overload\">\n       [2/2]\n      </span>\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           SLEEPTIMER_ENUM\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_weekDay_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Week Day enum.\n       </p>\n      </div>\n     </div>\n     <a id=\"gae661a78719a6acc014efa15216ae4a54\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gae661a78719a6acc014efa15216ae4a54\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_init()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_init\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void\n          </td>\n          <td class=\"paramname\">\n           <code>\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Initializes the Sleeptimer.\n       </p>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga43c32bdb2a9fcfbe279563a9fd689711\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga43c32bdb2a9fcfbe279563a9fd689711\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_start_timer()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_start_timer\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_timer_handle_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            handle,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            timeout,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n            sl_sleeptimer_timer_callback_t\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            callback,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            callback_data,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint8_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            priority,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            option_flags\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Starts a 32 bits timer.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              handle\n             </code>\n            </td>\n            <td>\n             Pointer to handle to timer.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              timeout\n             </code>\n            </td>\n            <td>\n             Timer timeout, in timer ticks.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback\n             </code>\n            </td>\n            <td>\n             Callback function that will be called when initial/periodic timeout expires.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback_data\n             </code>\n            </td>\n            <td>\n             Pointer to user data that will be passed to callback.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              priority\n             </code>\n            </td>\n            <td>\n             Priority of callback. Useful in case multiple timer expire at the same time. 0 = highest priority.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              option_flags\n             </code>\n            </td>\n            <td>\n             Bit array of option flags for the timer. Valid bit-wise OR of one or more of the following:\n             <ul>\n              <li>\n               SL_SLEEPTIMER_NO_HIGH_PRECISION_HF_CLOCKS_REQUIRED_FLAG\n              </li>\n             </ul>\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaacd5a666731a0ba4823107e552e23e69\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaacd5a666731a0ba4823107e552e23e69\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_restart_timer()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_restart_timer\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_timer_handle_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            handle,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            timeout,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n            sl_sleeptimer_timer_callback_t\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            callback,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            callback_data,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint8_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            priority,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            option_flags\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Restarts a 32 bits timer.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              handle\n             </code>\n            </td>\n            <td>\n             Pointer to handle to timer.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              timeout\n             </code>\n            </td>\n            <td>\n             Timer timeout, in timer ticks.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback\n             </code>\n            </td>\n            <td>\n             Callback function that will be called when initial/periodic timeout expires.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback_data\n             </code>\n            </td>\n            <td>\n             Pointer to user data that will be passed to callback.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              priority\n             </code>\n            </td>\n            <td>\n             Priority of callback. Useful in case multiple timer expire at the same time. 0 = highest priority.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              option_flags\n             </code>\n            </td>\n            <td>\n             Bit array of option flags for the timer. Valid bit-wise OR of one or more of the following:\n             <ul>\n              <li>\n               SL_SLEEPTIMER_NO_HIGH_PRECISION_HF_CLOCKS_REQUIRED_FLAG or 0 for not flags.\n              </li>\n             </ul>\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga2ceb1b76334902525dd32e937e90bb16\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga2ceb1b76334902525dd32e937e90bb16\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_start_periodic_timer()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_start_periodic_timer\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_timer_handle_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            handle,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            timeout,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n            sl_sleeptimer_timer_callback_t\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            callback,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            callback_data,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint8_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            priority,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            option_flags\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Starts a 32 bits periodic timer.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              handle\n             </code>\n            </td>\n            <td>\n             Pointer to handle to timer.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              timeout\n             </code>\n            </td>\n            <td>\n             Timer periodic timeout, in timer ticks.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback\n             </code>\n            </td>\n            <td>\n             Callback function that will be called when initial/periodic timeout expires.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback_data\n             </code>\n            </td>\n            <td>\n             Pointer to user data that will be passed to callback.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              priority\n             </code>\n            </td>\n            <td>\n             Priority of callback. Useful in case multiple timer expire at the same time. 0 = highest priority.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              option_flags\n             </code>\n            </td>\n            <td>\n             Bit array of option flags for the timer. Valid bit-wise OR of one or more of the following:\n             <ul>\n              <li>\n               SL_SLEEPTIMER_NO_HIGH_PRECISION_HF_CLOCKS_REQUIRED_FLAG or 0 for not flags.\n              </li>\n             </ul>\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaf54408a7f172b684de33d9a7f4e01be9\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf54408a7f172b684de33d9a7f4e01be9\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_restart_periodic_timer()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_restart_periodic_timer\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_timer_handle_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            handle,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            timeout,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n            sl_sleeptimer_timer_callback_t\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            callback,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           void *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            callback_data,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint8_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            priority,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            option_flags\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Restarts a 32 bits periodic timer.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              handle\n             </code>\n            </td>\n            <td>\n             Pointer to handle to timer.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              timeout\n             </code>\n            </td>\n            <td>\n             Timer periodic timeout, in timer ticks.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback\n             </code>\n            </td>\n            <td>\n             Callback function that will be called when initial/periodic timeout expires.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback_data\n             </code>\n            </td>\n            <td>\n             Pointer to user data that will be passed to callback.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              priority\n             </code>\n            </td>\n            <td>\n             Priority of callback. Useful in case multiple timer expire at the same time. 0 = highest priority.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              option_flags\n             </code>\n            </td>\n            <td>\n             Bit array of option flags for the timer. Valid bit-wise OR of one or more of the following:\n             <ul>\n              <li>\n               SL_SLEEPTIMER_NO_HIGH_PRECISION_HF_CLOCKS_REQUIRED_FLAG or 0 for not flags.\n              </li>\n             </ul>\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga1f4f25aa754cd35214dba9762450b0ca\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga1f4f25aa754cd35214dba9762450b0ca\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_stop_timer()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_stop_timer\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_timer_handle_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            handle\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Stops a timer.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              handle\n             </code>\n            </td>\n            <td>\n             Pointer to handle to timer.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gac0236c1c353317c41d577867e7429874\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gac0236c1c353317c41d577867e7429874\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_is_timer_running()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_is_timer_running\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_timer_handle_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            handle,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           bool *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            running\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Gets the status of a timer.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              handle\n             </code>\n            </td>\n            <td>\n             Pointer to handle to timer.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              running\n             </code>\n            </td>\n            <td>\n             Pointer to the status of the timer.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         A non periodic timer is considered not running during its callback.\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga6a06acb331e2e3c0efeb83f42a4d9c16\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga6a06acb331e2e3c0efeb83f42a4d9c16\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_get_timer_time_remaining()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_get_timer_time_remaining\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_timer_handle_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            handle,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            time\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Gets remaining time until timer expires.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              handle\n             </code>\n            </td>\n            <td>\n             Pointer to handle to timer.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              time\n             </code>\n            </td>\n            <td>\n             Time left in timer ticks.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaf96ccea3e4d1727dc2fd2fa98f94ccb1\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf96ccea3e4d1727dc2fd2fa98f94ccb1\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_get_remaining_time_of_first_timer()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_get_remaining_time_of_first_timer\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            option_flags,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            time_remaining\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Gets the time remaining until the first timer with the matching set of flags expires.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              option_flags\n             </code>\n            </td>\n            <td>\n             Set of flags to match.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              time_remaining\n             </code>\n            </td>\n            <td>\n             Time left in timer ticks.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gae0397d420800b7c2d2afd5bbb9710067\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gae0397d420800b7c2d2afd5bbb9710067\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_get_tick_count()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           uint32_t sl_sleeptimer_get_tick_count\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void\n          </td>\n          <td class=\"paramname\">\n           <code>\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Gets current 32 bits global tick count.\n       </p>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Current tick count.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaaf928eeb3dad0d43098b3aed9049959a\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaaf928eeb3dad0d43098b3aed9049959a\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_get_tick_count64()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           uint64_t sl_sleeptimer_get_tick_count64\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void\n          </td>\n          <td class=\"paramname\">\n           <code>\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Gets current 64 bits global tick count.\n       </p>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Current tick count.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga69085e47b9cc43ac0cfdc08783c13aed\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga69085e47b9cc43ac0cfdc08783c13aed\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_get_timer_frequency()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           uint32_t sl_sleeptimer_get_timer_frequency\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void\n          </td>\n          <td class=\"paramname\">\n           <code>\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get timer frequency.\n       </p>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga157d43fdb64cb0d4afc864155bcf9bc8\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga157d43fdb64cb0d4afc864155bcf9bc8\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_convert_time_to_date()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_convert_time_to_date\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n            sl_sleeptimer_timestamp_t\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            time,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n            sl_sleeptimer_time_zone_offset_t\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            time_zone,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_date_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            date\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Converts a Unix timestamp into a date.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              time\n             </code>\n            </td>\n            <td>\n             Unix timestamp to convert.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              time_zone\n             </code>\n            </td>\n            <td>\n             Offset from UTC in second.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              date\n             </code>\n            </td>\n            <td>\n             Pointer to converted date.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gab3c762fbb3fed88f5f256d4c9a03fc67\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gab3c762fbb3fed88f5f256d4c9a03fc67\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_convert_date_to_time()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_convert_date_to_time\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_date_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            date,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n            sl_sleeptimer_timestamp_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            time\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Converts a date into a Unix timestamp.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              date\n             </code>\n            </td>\n            <td>\n             Pointer to date to convert.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              time\n             </code>\n            </td>\n            <td>\n             Pointer to converted Unix timestamp.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Dates are based on the Unix time representation. Range of dates supported :\n         <ul>\n          <li>\n           January 1, 1970, 00:00:00 to January 19, 2038, 03:14:00\n          </li>\n         </ul>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga28561cd8d4b4e31153d3dc02e68f2645\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga28561cd8d4b4e31153d3dc02e68f2645\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_convert_date_to_str()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           uint32_t sl_sleeptimer_convert_date_to_str\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           char *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            str,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           size_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            size,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           const uint8_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            format,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_date_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            date\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Convert date to string.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              str\n             </code>\n            </td>\n            <td>\n             Output string.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              size\n             </code>\n            </td>\n            <td>\n             Size of the input array.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              format\n             </code>\n            </td>\n            <td>\n             The format specification character.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              date\n             </code>\n            </td>\n            <td>\n             Pointer to date structure.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if error. Number of character in the output string.\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Refer strftime() from UNIX.\n         <a href=\"http://man7.org/linux/man-pages/man3/strftime.3\">\n          http://man7.org/linux/man-pages/man3/strftime.3.html\n         </a>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gafbd748d9a4654e9ae474a7020a5d9813\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gafbd748d9a4654e9ae474a7020a5d9813\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_set_tz()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_sleeptimer_set_tz\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n            sl_sleeptimer_time_zone_offset_t\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            offset\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Sets time zone offset.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              offset\n             </code>\n            </td>\n            <td>\n             Time zone offset, in seconds.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga91ebac6960e114d933e1ef7c4f32294a\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga91ebac6960e114d933e1ef7c4f32294a\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_get_tz()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n            sl_sleeptimer_time_zone_offset_t\n           </a>\n           sl_sleeptimer_get_tz\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void\n          </td>\n          <td class=\"paramname\">\n           <code>\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Gets time zone offset.\n       </p>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Time zone offset, in seconds.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga4d79ef7aa10606814bcc21053bfca558\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga4d79ef7aa10606814bcc21053bfca558\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_get_time()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n            sl_sleeptimer_timestamp_t\n           </a>\n           sl_sleeptimer_get_time\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void\n          </td>\n          <td class=\"paramname\">\n           <code>\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Retrieves current time.\n       </p>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Current timestamps in Unix format.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gafbd64c7fbf7b0dfb7947a6f7bf288dca\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gafbd64c7fbf7b0dfb7947a6f7bf288dca\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_set_time()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_set_time\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n            sl_sleeptimer_timestamp_t\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            time\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Sets current time.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              time\n             </code>\n            </td>\n            <td>\n             Time to set.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga2955c70feb8e0082ba54e30d2629d840\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga2955c70feb8e0082ba54e30d2629d840\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_get_datetime()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_get_datetime\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_date_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            date\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Gets current date.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              date\n             </code>\n            </td>\n            <td>\n             Pointer to a sl_sleeptimer_date_t structure.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga840c8ef8049b364a45899320a9f0c8bf\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga840c8ef8049b364a45899320a9f0c8bf\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_set_datetime()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_set_datetime\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_date_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            date\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Sets current time, in date format.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              date\n             </code>\n            </td>\n            <td>\n             Pointer to current date.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga726d2e276fbac02b2246a446c3dcf755\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga726d2e276fbac02b2246a446c3dcf755\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_build_datetime()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_build_datetime\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_date_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            date,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            year,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           sl_sleeptimer_month_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            month,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint8_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            month_day,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint8_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            hour,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint8_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            min,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint8_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            sec,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n            sl_sleeptimer_time_zone_offset_t\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            tzOffset\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Builds a date time structure based on the provided parameters.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              date\n             </code>\n            </td>\n            <td>\n             Pointer to the structure to be populated.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              year\n             </code>\n            </td>\n            <td>\n             Current year. May be provided based on a 0 Epoch or a 1900 Epoch.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              month\n             </code>\n            </td>\n            <td>\n             Months since January. Expected value: 0-11.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              month_day\n             </code>\n            </td>\n            <td>\n             Day of the month. Expected value: 1-31.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              hour\n             </code>\n            </td>\n            <td>\n             Hours since midnight. Expected value: 0-23.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              min\n             </code>\n            </td>\n            <td>\n             Minutes after the hour. Expected value: 0-59.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              sec\n             </code>\n            </td>\n            <td>\n             Seconds after the minute. Expected value: 0-59.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              tzOffset\n             </code>\n            </td>\n            <td>\n             Offset, in seconds, from UTC.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaf63bbad4e29c0b655b3d210bf3b3b086\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf63bbad4e29c0b655b3d210bf3b3b086\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_convert_unix_time_to_ntp()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_convert_unix_time_to_ntp\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n            sl_sleeptimer_timestamp_t\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            time,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            ntp_time\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Converts Unix timestamp into NTP timestamp.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              time\n             </code>\n            </td>\n            <td>\n             Unix timestamp.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              ntp_time\n             </code>\n            </td>\n            <td>\n             Pointer to NTP Timestamp.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Unix timestamp range supported : 0x0 to 0x7C55 817F ie. January 1, 1970, 00:00:00 to February 07, 2036, 06:28:15\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaff086ed922350c8a4cffeed024d03871\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaff086ed922350c8a4cffeed024d03871\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_convert_ntp_time_to_unix()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_convert_ntp_time_to_unix\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            ntp_time,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n            sl_sleeptimer_timestamp_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            time\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Converts NTP timestamp into Unix timestamp.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              ntp_time\n             </code>\n            </td>\n            <td>\n             NTP Timestamp.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              time\n             </code>\n            </td>\n            <td>\n             Pointer to Unix timestamp.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         NTP timestamp range supported : 0x83AA 7E80 to 0xFFFF FFFF ie. January 1, 1970, 00:00:00 to February 07, 2036, 06:28:15\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaa73b94e0c15a1e2cf6b3cb0b37120ed4\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaa73b94e0c15a1e2cf6b3cb0b37120ed4\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_convert_unix_time_to_zigbee()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_convert_unix_time_to_zigbee\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n            sl_sleeptimer_timestamp_t\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            time,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            zigbee_time\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Converts Unix timestamp into Zigbee timestamp.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              time\n             </code>\n            </td>\n            <td>\n             Unix timestamp.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              zigbee_time\n             </code>\n            </td>\n            <td>\n             Pointer to NTP Timestamp.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Unix timestamp range supported : 0x386D 4380 to 0x7FFF FFFF ie. January 1, 2000, 00:00:0 to January 19, 2038, 03:14:00\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaab1267658b2ac5e4249e87412ce7dede\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaab1267658b2ac5e4249e87412ce7dede\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_convert_zigbee_time_to_unix()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_convert_zigbee_time_to_unix\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            zigbee_time,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n            sl_sleeptimer_timestamp_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            time\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Converts Zigbee timestamp into Unix timestamp.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              zigbee_time\n             </code>\n            </td>\n            <td>\n             NTP Timestamp.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              time\n             </code>\n            </td>\n            <td>\n             Pointer to Unix timestamp.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         ZIGBEE timestamp range supported : 0x0 to 0x4792 BC7F ie. January 1, 2000, 00:00:00 to January 19, 2038, 03:14:00\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga612f86ff5d65f445efb71d4a1eef7386\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga612f86ff5d65f445efb71d4a1eef7386\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_set_tz_ahead_utc()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n                sl_sleeptimer_time_zone_offset_t\n               </a>\n               sl_sleeptimer_set_tz_ahead_utc\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               uint8_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                hours,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint8_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                minutes\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Calculates offset for time zone after UTC-0.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              hours\n             </code>\n            </td>\n            <td>\n             Number of hours from UTC-0.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              minutes\n             </code>\n            </td>\n            <td>\n             Number of minutes from UTC-0.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         The time zone offset in seconds.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaae029d2985822af973e4cd0333fa92e1\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaae029d2985822af973e4cd0333fa92e1\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_set_tz_behind_utc()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n                sl_sleeptimer_time_zone_offset_t\n               </a>\n               sl_sleeptimer_set_tz_behind_utc\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               uint8_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                hours,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint8_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                minutes\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Calculates offset for time zone before UTC-0.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              hours\n             </code>\n            </td>\n            <td>\n             Number of hours to UTC-0.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              minutes\n             </code>\n            </td>\n            <td>\n             Number of minutes to UTC-0.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         The time zone offset in seconds.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga7d7c4ae596755e3e5e3c0c0aa5e749b3\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga7d7c4ae596755e3e5e3c0c0aa5e749b3\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_delay_millisecond()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_sleeptimer_delay_millisecond\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            time_ms\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Active delay.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              time_ms\n             </code>\n            </td>\n            <td>\n             Delay duration in milliseconds.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gadf3a3eb05b0f5adbc7ee488113299f61\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gadf3a3eb05b0f5adbc7ee488113299f61\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_ms_to_tick()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           uint32_t sl_sleeptimer_ms_to_tick\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           uint16_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            time_ms\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Converts milliseconds in ticks.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              time_ms\n             </code>\n            </td>\n            <td>\n             Number of milliseconds.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Corresponding ticks number.\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         The result is \"rounded\" to the superior tick number. This function is light and cannot fail so it should be privilegied to perform a millisecond to tick conversion.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga7484d4be4877b94a687f4410fccc6271\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga7484d4be4877b94a687f4410fccc6271\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_ms32_to_tick()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_ms32_to_tick\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            time_ms,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            tick\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Converts 32-bits milliseconds in ticks.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              time_ms\n             </code>\n            </td>\n            <td>\n             Number of milliseconds.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              tick\n             </code>\n            </td>\n            <td>\n             Pointer to the converted tick number.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         The result is \"rounded\" to the superior tick number. If possible the\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gadf3a3eb05b0f5adbc7ee488113299f61\" title=\"Converts milliseconds in ticks.\" target=\"_blank\">\n          sl_sleeptimer_ms_to_tick()\n         </a>\n         function should be used.\n        </dd>\n        <dd>\n         This function converts the delay expressed in milliseconds to timer ticks (represented on 32 bits). This means that the value that can be passed to the argument 'time_ms' is limited. The maximum timeout value that can be passed to this function can be retrieved by calling\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga3218039eab8d09231cc092fdc2a1dba5\" title=\"Gets the maximum value that can be passed to the functions that have a 32-bits time or timeout argume...\" target=\"_blank\">\n          sl_sleeptimer_get_max_ms32_conversion()\n         </a>\n         . If the value passed to 'time_ms' is too large, SL_STATUS_INVALID_PARAMETER will be returned.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga3218039eab8d09231cc092fdc2a1dba5\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga3218039eab8d09231cc092fdc2a1dba5\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_get_max_ms32_conversion()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           uint32_t sl_sleeptimer_get_max_ms32_conversion\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void\n          </td>\n          <td class=\"paramname\">\n           <code>\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Gets the maximum value that can be passed to the functions that have a 32-bits time or timeout argument expressed in milliseconds.\n       </p>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Maximum time or timeout value in milliseconds.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga2c2a2a131e401894d3e09513442667b5\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga2c2a2a131e401894d3e09513442667b5\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_tick_to_ms()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           uint32_t sl_sleeptimer_tick_to_ms\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            tick\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Converts ticks in milliseconds.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              tick\n             </code>\n            </td>\n            <td>\n             Number of tick.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Corresponding milliseconds number.\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         The result is rounded to the inferior millisecond.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga6882d0577137d8e20ec531be45678fba\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga6882d0577137d8e20ec531be45678fba\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_tick64_to_ms()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_sleeptimer_tick64_to_ms\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           uint64_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            tick,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint64_t *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            ms\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Converts 64-bit ticks in milliseconds.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              tick\n             </code>\n            </td>\n            <td>\n             Number of tick.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              ms\n             </code>\n            </td>\n            <td>\n             Pointer to the converted milliseconds number.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         The result is rounded to the inferior millisecond.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga0b38592836a9a01d6f315d417d3d6cd9\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga0b38592836a9a01d6f315d417d3d6cd9\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_is_power_manager_early_restore_timer_latest_to_expire()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           bool sl_sleeptimer_is_power_manager_early_restore_timer_latest_to_expire\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           void\n          </td>\n          <td class=\"paramname\">\n           <code>\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Allow sleep after ISR exit.\n       </p>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         true if sleep is allowed after ISR exit. False otherwise.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gac8c20444c4572f93db0fb3ad3bc2305e\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gac8c20444c4572f93db0fb3ad3bc2305e\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_start_timer_ms()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               sl_status_t sl_sleeptimer_start_timer_ms\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               sl_sleeptimer_timer_handle_t *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                handle,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                timeout_ms,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n                sl_sleeptimer_timer_callback_t\n               </a>\n              </td>\n              <td class=\"paramname\">\n               <code>\n                callback,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               void *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                callback_data,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint8_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                priority,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint16_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                option_flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Starts a 32 bits timer.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              handle\n             </code>\n            </td>\n            <td>\n             Pointer to handle to timer.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              timeout_ms\n             </code>\n            </td>\n            <td>\n             Timer timeout, in milliseconds.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback\n             </code>\n            </td>\n            <td>\n             Callback function that will be called when initial/periodic timeout expires.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback_data\n             </code>\n            </td>\n            <td>\n             Pointer to user data that will be passed to callback.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              priority\n             </code>\n            </td>\n            <td>\n             Priority of callback. Useful in case multiple timer expire at the same time. 0 = highest priority.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              option_flags\n             </code>\n            </td>\n            <td>\n             Bit array of option flags for the timer. Valid bit-wise OR of one or more of the following:\n             <ul>\n              <li>\n               SL_SLEEPTIMER_NO_HIGH_PRECISION_HF_CLOCKS_REQUIRED_FLAG or 0 for not flags.\n              </li>\n             </ul>\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         This function converts the delay expressed in milliseconds to timer ticks (represented on 32 bits). This means that the value that can be passed to the argument 'timeout_ms' is limited. The maximum timeout value that can be passed to this function can be retrieved by calling\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga3218039eab8d09231cc092fdc2a1dba5\" title=\"Gets the maximum value that can be passed to the functions that have a 32-bits time or timeout argume...\" target=\"_blank\">\n          sl_sleeptimer_get_max_ms32_conversion()\n         </a>\n         . If the value passed to 'timeout_ms' is too large, SL_STATUS_INVALID_PARAMETER will be returned.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga3f3cefcf3dba6d516ca69444d25d8c09\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga3f3cefcf3dba6d516ca69444d25d8c09\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_restart_timer_ms()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               sl_status_t sl_sleeptimer_restart_timer_ms\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               sl_sleeptimer_timer_handle_t *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                handle,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                timeout_ms,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n                sl_sleeptimer_timer_callback_t\n               </a>\n              </td>\n              <td class=\"paramname\">\n               <code>\n                callback,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               void *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                callback_data,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint8_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                priority,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint16_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                option_flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Restarts a 32 bits timer.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              handle\n             </code>\n            </td>\n            <td>\n             Pointer to handle to timer.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              timeout_ms\n             </code>\n            </td>\n            <td>\n             Timer timeout, in milliseconds.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback\n             </code>\n            </td>\n            <td>\n             Callback function that will be called when initial/periodic timeout expires.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback_data\n             </code>\n            </td>\n            <td>\n             Pointer to user data that will be passed to callback.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              priority\n             </code>\n            </td>\n            <td>\n             Priority of callback. Useful in case multiple timer expire at the same time. 0 = highest priority.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              option_flags\n             </code>\n            </td>\n            <td>\n             Bit array of option flags for the timer. Valid bit-wise OR of one or more of the following:\n             <ul>\n              <li>\n               SL_SLEEPTIMER_NO_HIGH_PRECISION_HF_CLOCKS_REQUIRED_FLAG or 0 for not flags.\n              </li>\n             </ul>\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         This function converts the delay expressed in milliseconds to timer ticks (represented on 32 bits). This means that the value that can be passed to the argument 'timeout_ms' is limited. The maximum timeout value that can be passed to this function can be retrieved by calling\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga3218039eab8d09231cc092fdc2a1dba5\" title=\"Gets the maximum value that can be passed to the functions that have a 32-bits time or timeout argume...\" target=\"_blank\">\n          sl_sleeptimer_get_max_ms32_conversion()\n         </a>\n         . If the value passed to 'timeout_ms' is too large, SL_STATUS_INVALID_PARAMETER will be returned.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gae4639653b4bf4ed68049b2a12a6c6d83\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gae4639653b4bf4ed68049b2a12a6c6d83\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_start_periodic_timer_ms()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               sl_status_t sl_sleeptimer_start_periodic_timer_ms\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               sl_sleeptimer_timer_handle_t *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                handle,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                timeout_ms,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n                sl_sleeptimer_timer_callback_t\n               </a>\n              </td>\n              <td class=\"paramname\">\n               <code>\n                callback,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               void *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                callback_data,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint8_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                priority,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint16_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                option_flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Starts a 32 bits periodic timer.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              handle\n             </code>\n            </td>\n            <td>\n             Pointer to handle to timer.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              timeout_ms\n             </code>\n            </td>\n            <td>\n             Timer periodic timeout, in milliseconds.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback\n             </code>\n            </td>\n            <td>\n             Callback function that will be called when initial/periodic timeout expires.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback_data\n             </code>\n            </td>\n            <td>\n             Pointer to user data that will be passed to callback.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              priority\n             </code>\n            </td>\n            <td>\n             Priority of callback. Useful in case multiple timer expire at the same time. 0 = highest priority.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              option_flags\n             </code>\n            </td>\n            <td>\n             Bit array of option flags for the timer. Valid bit-wise OR of one or more of the following:\n             <ul>\n              <li>\n               SL_SLEEPTIMER_NO_HIGH_PRECISION_HF_CLOCKS_REQUIRED_FLAG or 0 for not flags.\n              </li>\n             </ul>\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         This function converts the delay expressed in milliseconds to timer ticks (represented on 32 bits). This means that the value that can be passed to the argument 'timeout_ms' is limited. The maximum timeout value that can be passed to this function can be retrieved by calling\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga3218039eab8d09231cc092fdc2a1dba5\" title=\"Gets the maximum value that can be passed to the functions that have a 32-bits time or timeout argume...\" target=\"_blank\">\n          sl_sleeptimer_get_max_ms32_conversion()\n         </a>\n         . If the value passed to 'timeout_ms' is too large, SL_STATUS_INVALID_PARAMETER will be returned.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga1a44174e7a8eccbabde499875cb8462a\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga1a44174e7a8eccbabde499875cb8462a\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_restart_periodic_timer_ms()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               sl_status_t sl_sleeptimer_restart_periodic_timer_ms\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               sl_sleeptimer_timer_handle_t *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                handle,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                timeout_ms,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga9049589e4153204fb3e97cbe1ecda89b\" target=\"_blank\">\n                sl_sleeptimer_timer_callback_t\n               </a>\n              </td>\n              <td class=\"paramname\">\n               <code>\n                callback,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               void *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                callback_data,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint8_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                priority,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint16_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                option_flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Restarts a 32 bits periodic timer.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              handle\n             </code>\n            </td>\n            <td>\n             Pointer to handle to timer.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              timeout_ms\n             </code>\n            </td>\n            <td>\n             Timer periodic timeout, in milliseconds.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback\n             </code>\n            </td>\n            <td>\n             Callback function that will be called when initial/periodic timeout expires.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              callback_data\n             </code>\n            </td>\n            <td>\n             Pointer to user data that will be passed to callback.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              priority\n             </code>\n            </td>\n            <td>\n             Priority of callback. Useful in case multiple timer expire at the same time. 0 = highest priority.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              option_flags\n             </code>\n            </td>\n            <td>\n             Bit array of option flags for the timer. Valid bit-wise OR of one or more of the following:\n             <ul>\n              <li>\n               SL_SLEEPTIMER_NO_HIGH_PRECISION_HF_CLOCKS_REQUIRED_FLAG or 0 for not flags.\n              </li>\n             </ul>\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         0 if successful. Error code otherwise.\n        </dd>\n       </dl>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         This function converts the delay expressed in milliseconds to timer ticks (represented on 32 bits). This means that the value that can be passed to the argument 'timeout_ms' is limited. The maximum timeout value that can be passed to this function can be retrieved by calling\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga3218039eab8d09231cc092fdc2a1dba5\" title=\"Gets the maximum value that can be passed to the functions that have a 32-bits time or timeout argume...\" target=\"_blank\">\n          sl_sleeptimer_get_max_ms32_conversion()\n         </a>\n         . If the value passed to 'timeout_ms' is too large, SL_STATUS_INVALID_PARAMETER will be returned.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <h2 class=\"groupheader\">\n      Typedef Documentation\n     </h2>\n     <a id=\"gaf41c2bc4240e5b819fc7f285f62172a2\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf41c2bc4240e5b819fc7f285f62172a2\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_timestamp_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           typedef uint32_t\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#gaf41c2bc4240e5b819fc7f285f62172a2\" target=\"_blank\">\n            sl_sleeptimer_timestamp_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Timestamp, wall clock time in seconds.\n       </p>\n      </div>\n     </div>\n     <a id=\"ga27148a3d37b9f3728cbd18b56b4c9979\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga27148a3d37b9f3728cbd18b56b4c9979\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_time_zone_offset_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           typedef int32_t\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer#ga27148a3d37b9f3728cbd18b56b4c9979\" target=\"_blank\">\n            sl_sleeptimer_time_zone_offset_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Time zone offset from UTC(second).\n       </p>\n      </div>\n     </div>\n     <a id=\"ga9049589e4153204fb3e97cbe1ecda89b\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga9049589e4153204fb3e97cbe1ecda89b\">\n        ◆\n       </a>\n      </span>\n      sl_sleeptimer_timer_callback_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           typedef void(* sl_sleeptimer_timer_callback_t) (sl_sleeptimer_timer_handle_t *handle, void *data)\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Typedef for the user supplied callback function which is called when a timer expires.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              handle\n             </code>\n            </td>\n            <td>\n             The timer handle.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramname\">\n             <code>\n              data\n             </code>\n            </td>\n            <td>\n             An extra parameter for the user application.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n    </div>\n   </article>\n  ", "url": "http://docs.silabs.com/gecko-platform/4.1/service/api/group-sleeptimer", "status": "success"}