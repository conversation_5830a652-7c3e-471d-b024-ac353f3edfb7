{"categoryPackages": [{"category": "Software Updates", "packages": [{"installState": "Update", "releaseNotes": "5.1.2\n\tUpdate to BgApi 9.1.0\n5.1.1\n\tUpdate to BgApi 9.0.1\n5.1.0\n\tUpdate to BgApi 9.0.0\n\tMigrate reset to reboot command\n\tMinor improvements\n5.0.2\n\tUpdate to BgApi 8.2.0\n5.0.1\n\tUpdate to BgApi 8.1.0\n5.0.0\n\tUpdate to BgApi 8.0.0\n\tMinor improvements\n4.3.9\n\tUpdate to BgApi 7.1.0\n4.3.8\n\tUpdate to BgApi 7.0.1\n4.3.7\n\tUpdate to BgApi 7.0.0\n\tRemove deprecated Advertiser and Scanner APIs usage from UI, Console still supported\n\tMinor improvements\n4.3.6\n\tUpdate to BgApi 6.2.0\n4.3.5\n\tUpdate to BgApi 6.1.0\n4.3.4\n\tUpdate to BgApi 6.0.0\n\tFix passkey copy\n\tFix Discovered Devices table\n4.3.3\n\tFix keyboard only bonding dialog\n4.3.2\n\tBluetooth Mesh 1.1 Support\n\tUpdate to BgApi 5.1.1\n4.3.1\n\tUpdate to BgApi 5.1.0\n4.3.0\n\tUpdate to BgApi 5.0.0\n\tImprove RSSI filter slider\n\tNew API reference Search options\n\tOrder API reference entries\n\tAuto-pad bytes in Local GATT\n\tShow number of subscribed devices in Local GATT\n\tReject UI refresh on permission error in Remote GATT\n\tImprove Advertise Set Power Section\n\tAllow unloading custom API\n\tStore Scan table's pagination as persistent Setting\n\tNew: Models page, support for Generic OnOff and Level Get, Set and State\n\tNew: Key Refresh section, reworked DDB deletion\n\tNew: automatic retry option for config_client commands failed with timeout (enable in Settings)\n\tNew: ability to create App Keys and Networks with custom key, and group with custom address\n\tNew: App Keys, Subscriptions and Publish can be removed\n\tNew: Groups, App Keys, Networks and Nodes can be renamed\n\tNew: Mesh operations supported by the UI also work when typed manually in the console\n\tReworked local mesh data saving: Only labels (names) are saved for Networks, App Keys & Nodes. Groups are re-created when adding an already known network. Stored data clearable in Settings\n\tButtons, labels and pages renamed to align with mesh terminology\n\tImproved flow and UI for Initialize as Provisioner\n\tIntroduced app-wide colours for App Keys and Groups (orange and purple)\n\tApp Keys and Networks show their indices instead of their keys\n\tAdded ability to copy unformatted keys and UUIDs (on hover)\n\tNode Configuration no longer shows false values initially\n\tLong mesh operations have in-progress indication\n\tBind to All Models is now in the DCD Section\n\tImproved Provision page\n\tImproved Networks & Devices page\n\tImproved Node Configure page\n\tImproved individual model configuration\n\tImproved Mesh Settings page\n\tUnknown models In DCD now show up and are configurable\n\tMesh stability improvements and bugfixes\n4.2.2\n\tFixed issue with read and write request dialog.\n4.2.1\n\tUpdate to BgApi 4.2.0\n4.2.0\n\tSupport new BLE scan events on the Discovered Devices table.\n\tImprove Dark Theme UI\n4.1.2\n\tReshape smart console helper popup\n\tShow public Bluetooth address on provision device scan\n\tVisual and UX improvements\n\tSIG Models renamed to DCD (Device Composition Data)\n\tFix adding model subscriptions\n\tFix adding model publish addresses\n\tFix binding App Key to Vendor Models\n\tFix getting DCD via UI button and console\n4.1.1\n\tEnable unlisted enum value entry in Smart Console\n\tUpdate BG API: reset_to_dfu command, fix some bit mask values\n\tMesh Provision: Correct Device Key to Bluetooth Address in Discovered Devices table\n\tMesh Provision: Added default ordering by UUID to Discovered Devices table\n4.1.0\n\tAdd new security manager configuration flag\n\tFix CTE Length setting issue\n\tUpdate to latest BGAPI\n\tFix issue with Global Transmit Power Slider\n\tDeleting a Mesh Group now requires confirmation\n\tFixed issue in Device Tab name did not rename when the device was renamed\n\tChanged default Mesh LPN Timeout in Settings\n\tAdded Secure Network Beacon switch to Mesh device setting tab\n\tImplemented setting Unicast Address Range during Mesh Provisioning\n4.0.6\n\tFixed issues with symbolic links on Mac OS\n\tFixed issue with 20dBm for CW mode in regulatory test\n\tFixed issue with Transmit Power unit in RF Regulatory Test\n4.0.5\n\tFix standalone build issue on Mac.\n\tFix restricted enum options.\n\tSecure Network Beacon toggle is added to device tab.\n4.0.3\n\tUpdates for Gecko SDK 3.2.2 release.\n\tImprove usability while connecting to a node.\n\tFixed issue with transmit power slider.\n4.0.2\n\tMinor bug fixes.\n4.0.1\n\tInfrastructure enhancements and bug fixes.\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.bluetooth.tool_ncp_commander.feature.metadata.resource.feature.group/bluetooth_brackets_40x40.png", "installEnabled": true, "id": "com.silabs.ss.bluetooth.tool_ncp_commander.feature.metadata.resource.feature.group", "label": "Bluetooth NCP Commander - 5.1.2", "featurePackageId": "com.silabs.ss.bluetooth.tool_ncp_commander.feature", "desc": "Graphical UI to control an EFR32 running the NCP firmware"}, {"installState": "Update", "releaseNotes": "1.3.1\n\tDPP mode improvements\n1.3.0\n    New security method has been added\n        Dynamic Data Provisioning option for compatible devices\n        Local signing server creation\n    Minor UI fixes\n    Fixed invalid radio channel ranges\n    New fan 1.1 profiles\n1.2.5\n\tMinor bug fixes and improvements.\n1.2.4\n    Updates for SiSDK 2024.6.0\n1.2.3\n    Radio Configurator metadata update\n    Data loading error fix for GSDK patches (4.2.2-4.2.5)\n    Other minor maintenance fixes\n1.2.2\n    Issue with deviceProfile property setting is fixed\n    OFDM PHY-s are hidden for xG28 projects\n    FAN1.0 & FAN1.1 PHY metadata update for GSDK v4.4.0\n1.2.1\n    Mode Switch Checkbox UI Improvements\n    CA Private Key upload fix\n    Manual is updated with v5.7 features\n    Helper texts and links are updated\n1.2.0\n    Backwards compatibility for older SDKs\n    Application Page:\n        Broadcast Retransmissions Input\n        Device Type and Device Profile Selectors\n    Security Page:\n        Generate Device Private Key and Device Certificate\n    Radio Page:\n        Reference PHY table filtering is improved\n        Support for Mode Switch feature\n        Allowed Channels Input\n        Custom Protocols Table is realigned\n        Already selected PHYs are signed\n1.1.4\n    Fix issue with Regulatory Domain filtering\n\tFix issue with Channel Plan ID list decimal value\n\tFix issue with Application Tab MAC Allow/Deny List\n1.1.3\n    Fix issue with default PHY naming\n1.1.2\n    Fix datarate unit label\n1.1.1\n    Added JSON validation for Wi-SUN configuration file\n    Stabilization and bugfixes\n1.1.0\n\tAdded Wi-SUN FAN 1.1 PHY support\n\tDefault PHY selection feature for the application\n\tVisualization of Errors from the Radio Configuration and PHY duplications\n\tDelete All PHYs button and ordering feature for selected PHYs table\n1.0.1\n\tFix Brazilian PHY configurations channel number\n1.0.0\n\tInitial release of the The Wi-SUN Configurator.\n\tIt is an easy-to-use user interface to help you configure your Wi-SUN application. \n\tFor a detailed description see UG495: Silicon Labs Wi-SUN Developer's Guide.", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.proprietary.wisun_configurator.feature.metadata.resource.feature.group/icon.png", "installEnabled": true, "id": "com.silabs.ss.proprietary.wisun_configurator.feature.metadata.resource.feature.group", "label": "Wi-SUN Configurator - 1.3.1", "featurePackageId": "com.silabs.ss.proprietary.wisun_configurator.feature", "desc": "Wi-SUN Configurator"}, {"installState": "Update", "releaseNotes": "# Release notes\n\nPlease visit https://github.com/project-chip/zap/releases for detailed release notes.", "installEnabled": true, "id": "com.silabs.apack.zap.feature.metadata.resource.feature.group", "label": "Adapter Pack for ZCL Advanced Platform - 2025.2.26", "featurePackageId": "com.silabs.apack.zap.feature", "desc": "Provides a runtime interface to ZCL Advanced Platform"}, {"installState": "Update", "releaseNotes": "5.3.20\n   SV5.10.3.0 release\n5.3.19\n\tSV5.10.1 release changes\n  * Update copyright year\n5.3.18\n\tSV5.10.0 release changes\n5.3.17\n   SV5.9.3.1 release.\n5.3.16\n   SV5.9.3.0 release.\n5.3.15\n   SV5.9.2.0 release.\n5.3.14\n  Minor bug fixes and improvements.\n5.3.13\n\tMinor improvements.\n5.3.12\n\tMinor bug fixes and improvements.\n5.3.11\n   SV5.8.1.1 release - fix copyright year notice\n5.3.10\n   SV5.8.0.1 release.\n   Fixes for duplicate IAR installation preventing proper detection of toolchains in studio.\n5.3.9\n   SV5.8.0.0 release.\n   GIT LFS support changes.\n5.3.8\n    Bug fixes for special characters in generated files\n5.3.7\n  SV5.7.2.0 release\n    Bug Fixes\n5.3.6\n  SV5.7.1.1 release\n\t* Fix issue with pintool file when importing SLC CLI generated project into Studio.\n5.3.5\n\tSV5.7 release notes\n5.3.4\n\tBug Fixes\n5.3.3\n\tBug Fixes\n5.3.2\n\tAdd Generic Templates framework to support third party IDEs and build systems\n\tBug Fixes\n5.3.1\n\tAdding infrastructure for post-build and workspaces support.\n5.3.0\n\tPlatform changes for SV5.5.0.0 release.\n\tEnabling Silicon Labs Matter package integration.\n\tFixed issue with external repo and default branch name.\n5.2.2\n\tBug fixes\n\t- consistent folder picking for offline archives\n5.2.1\n\tBug fix updating usb_monitor\n5.2.0\n  Bug fixes\n    - Secure library access\n    - 3rd party installation tools\n5.1.0\n  Bug fixes and new features for Studio SV5.4.0.0 release\n    - Improved Linux installation flow\n    - Improved Launcher view filtering of Examples and Demos\n    - Addressing various code quality issues\n5.0.11\n  Bug fix: Using proxy when accessing github\n5.0.10\n  Minor Bug fixes\n5.0.9\n  Bug fix for the External Repos support\n5.0.8\n  Bug fixes to GSDK-on-GitHub support\n5.0.7\n  GSDK to GitHub support\n5.0.6\n  Bug fixes - better support for non-english characters in file names\n5.0.5\n  SQ clean up and performance enhancement\n5.0.4\n  Improve picking target part when determining preferred SDK\n5.0.3\n  Improved project archives handling method\n5.0.2\n  Add support to collect GDB Debugger server logs.\n  Add support to display Silicon Labs GitHub example projects in Launcher and project wizard.\n5.0.1\n  Bug fixes\n5.0.0\n  Initial release, includes these key features:\n \n  * Launcher\n    - Fresh, clean user interface\n    - Automatic detection of connected development boards\n    - Context-aware development board and target device developer resources\n    - SDK download and update manager\n    - Easy programming of pre-built demo apps\n    - Simple cloning of software examples\n    - Quick access to software and hardware documentation with search and filter capabilities\n  * Software creation & management tools\n    - Support for Gecko SDK Suite 3.0 and later\n    - Creation of projects for\n      o The integrated Simplicity Studio IDE\n      o IAR Embedded Workbench\n      o Command-line GNU toolchain (GNU makefiles)\n    - Searchable library of device-relevant software making it easy to add software components to projects\n    - Configure software components in a GUI or text (C source) editor\n      o Configurable peripheral initialization, drivers, middleware, kernels and utilities\n    - Built-in software configuration error-checking\n    - Software dependency manager simplifies porting from\n      o SDK to SDK\n      o Silicon Labs development kits to custom hardware designs\n    - Graphical configuration tools\n      o Pin tool to assign pin and peripheral hardware resources\n      o Proprietary radio configurator tool\n      o Editor for Bluetooth LE Generic Attribute Profile (GATT)\n    - Source/Project management and revision control\n      o Option to copy or link SDK source\n      o No hard-coded paths\n      o Clear separation between SDK and customer source code\n      o Easy transfer of projects (share/import/export of projects)\n      o Source code management and software engineering workflows\n  * Development Tools\n    - Powerful integrated development environment (IDE)\n      o Built on latest Eclipse framework (v4.14 and newer)\n      o C/C++ Development Tooling (CDT, v9.10 and newer)\n      o Eclipse marketplace to enhance and customize\n    - Valuable insights with Silicon Labs analysis tools\n      o Wireless network traffic capture and analysis\n        - Test and analyze the traffic of your wireless devices and networks\n        - Data collected directly from devices under test\n        - Includes signal strength, LQI (Link Quality Indicator) and filtering information (unique to Silicon Labs)\n      o Capture and display energy usage\n        - Monitor single device or multiple nodes on a wireless network\n        - Code-correlated energy data – identify where your embedded application is consuming energy and design power-optimized applications\n        - Advanced trigger and search functions locate power events\n      o Energy monitoring and profiling tools", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.platform.feature.metadata.resource.feature.group/icon_studio_32x32.png", "installEnabled": true, "id": "com.silabs.ss.platform.feature.metadata.resource.feature.group", "label": "Simplicity Studio Platform - 5.3.20", "featurePackageId": "com.silabs.ss.platform.feature", "desc": "Simplicity Studio Platform"}, {"installState": "Update", "releaseNotes": "4.0.14\r\n\tUse the auto-install feature to address connected device and demo-only install.\r\n4.0.13\r\n\tAddress issue when installing by connected Si91x device\r\n4.0.12\r\n\tSV5.7 release notes\r\n4.0.11\r\n\tFeature build with Eclipse 4.23\r\n4.0.10\r\n   Updated signing\r\n4.0.9\r\n\tUpdate to JLink version 6.62d\r\n4.0.8\r\n\tUpdate to JLink version 6.34f\r\n4.0.7\r\n\tUpdate to JLink version 6.30k\r\n4.0.6\r\n\tUpdate to JLink version 6.22d\r\n4.0.5\r\n\tAdded flag to elevate permissions when installing drivers\r\n4.0.4\r\n\tUpdate JLink libraries to V6.18c\r\n4.0.3\r\n\tUpdate to JLink version 6.12f\r\n4.0.2\r\n\tUpdate to JLink version 6.10g\r\n4.0.1\r\n\tUpdate to JLink version 6.10d\r\n4.0.0\r\n\tInitial Release", "installEnabled": true, "id": "com.silabs.installer.jlink.feature.metadata.resource.feature.group", "label": "<PERSON>gger J-Link Driver installer - 4.0.14", "featurePackageId": "com.silabs.installer.jlink.feature", "desc": "Contains Segger J-Link Drivers for Windows"}, {"installState": "Update", "releaseNotes": "1.1.18\n\tMinor bug fixes and improvements.\n1.1.17\n\tMinor bug fixes and improvements.\n1.1.16\n\tMinor bug fixes and improvements.\n1.1.15\n\tMinor bug fixes and improvements.\n1.1.14\n\tMinor bug fixes and improvements.\n1.1.13\n\tMinor bug fixes and improvements.\n1.1.12\n\tUpdate Radio Configurator generator to v2302.7.2\n1.1.11\n\tUpdate Radio Configurator generator to v2304.5.2\n1.1.10\n\tUpdate Radio Configurator generator to v2304.4.4\n1.1.9\n\tUpdate Radio Configurator generator to v2302.6.1\n1.1.8\n\tUpdate Radio Configurator generator to v2302.5.1\n1.1.7\n\tRX Direct Mode support for Series-2\n\tWi-SUN Fan 1.1 Concurrent OFDM option support\n\tUpdate Radio Configurator generator to v2302.4.4\n1.1.6\n\tFix encoding issue in backing data.\n1.1.5\n\tUpdate Radio Configurator generator to v2204.6.2\n1.1.4\n\tUpdate Radio Configurator generator to v2023.2.1\n1.1.3\n\tImprove layour handling\n\tFix issue with EFRXG22 restrictions\n\tUpdate Radio Configurator generator to v2023.1.1\n1.1.2\n\tA preselector was added to select the regulatory domain, operating class, and operating mode for Wi-Sun PHYs, which supports the selection of the right PHY.\n\tPHY override capability for channel settings was added to allow users to override the PHY settings derived from the protocol level.\n1.1.1\n\tAdd Symbol map selection to OOK modulation for xG23 and up\n\tAdd .radioconf import functionality \n\tFix Save issue from Wi-SUN Configurator apply action\n1.1.0\n\tAdd new Wi-SUN PHY selector support\n\tImprove dependency between symbol map and modulation\n\tAdd Backwards Compatibility for Radio Configurator\n\tUpdate Radio Configurator generator to v2022.5.2\n1.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.ss.proprietary.multiphy_radio_configurator.feature.metadata.resource.feature.group/icon.png", "installEnabled": true, "id": "com.silabs.ss.proprietary.multiphy_radio_configurator.feature.metadata.resource.feature.group", "label": "Multiphy Radio Configurator - 1.1.18", "featurePackageId": "com.silabs.ss.proprietary.multiphy_radio_configurator.feature", "desc": "Multiphy Radio Configurator"}, {"installState": "Update", "releaseNotes": "4.0.72\n\tUpdate Commander utility to version 1.17.5\n4.0.71\n\tUpdate Commander utility to version 1.17.4\n4.0.70\n\tUpdate Commander utility to version 1.17.3\n4.0.69\n\tUpdate Commander utility to version 1.17.2\n4.0.68\n\tUpdate Commander utility to version 1.17.0\n4.0.67\n\tUpdate Commander utility to version 1.16.15\n4.0.66\n\tUpdate Commander utility to version 1.16.14\n4.0.65\n\tUpdate Commander utility to version 1.16.13\n4.0.64\n\tUpdate Commander utility to version 1.16.12\n4.0.63\n\tUpdate Commander utility to version 1.16.11\n4.0.62\n\tUpdate Commander utility to version 1.16.10\n4.0.61\n\tUpdate Commander utility to version 1.16.8 demoonly\n4.0.60\n\tUpdate Commander utility to version 1.16.8\n4.0.59\n\tUpdate Commander utility to version 1.16.6\n4.0.58\n\tUpdate Commander utility to version 1.16.5\n4.0.57\n\tUpdate Commander utility to version 1.16.4\n4.0.56\n\tUpdate Commander utility to version 1.16.2\n4.0.54\n    Update apack.info to export J-Link library\n    Update Commander utility to version 1.16.1\n4.0.53\n    Update Commander utility to version 1.16.0\n4.0.52\n    Update Commander utility to version 1.15.3\n4.0.51\n    Update Commander utility to version 1.15.2\n4.0.50\n    Update Commander utility to version 1.15.0\n4.0.49\n    Update Commander utility to version 1.14.6\n4.0.48\n    Update Commander utility to version 1.14.5\n4.0.47\n    Update Commander utility to version 1.14.4\n4.0.46\n    Update Commander utility to version 1.14.2\n4.0.45\n\tUpdate Commander utility to version 1.13.3\n4.0.44\n\tUpdate Commander utility to version 1.13.2\n4.0.43\n\tUpdate Commander utility to version 1.13.1\n4.0.42\n\tUpdate Commander utility to version 1.13.0\n\tEnable Si917 functionality\n4.0.41\n\tUpdate Commander utility to version 1.12.2\n4.0.40\n\tUpdate Commander utility to version 1.12.0\n4.0.39\n\tUpdate to the package for SV5.2 and GSDK 3.2 releases\n\tUpdate Commander utility to version 1.11.2\n4.0.38\n\tUpdate Commander utility to version 1.11.1\n4.0.37\n\tUpdate Commander utility to version 1.10.3\n4.0.36\n\tUpdate Commander utility to version 1.10.2\n4.0.35\n\tUpdate Commander utility to version 1.10.1\n4.0.34\n\tUpdate Commander utility to version 1.10.0\n4.0.33\n\tUpdate Commander utility to version 1.9.2\n4.0.32\n\tUpdate Commander utility to version 1.9.1\n4.0.31\n\tUpdate Commander utility to version 1.8.2\n4.0.30\n\tUpdate Commander utility to version 1.8.1\n4.0.29\n\tFix packaging issue with Commander version 1.7.7\n4.0.28\n\tUpdate Commander utility to version 1.7.7\n4.0.27\n\tUpdate Commander utility to version 1.7.5\n4.0.26\n\tUpdate Commander utility to version 1.7.4\n4.0.25\n\tUpdate Commander utility to version 1.7.3\n4.0.24\n\tUpdate Commander utility to version 1.7.2\n4.0.23\n\tUpdate Commander utility to version 1.7.1\n4.0.22\n\tUpdate Commander utility to version 1.7.0\n4.0.21\n\tUpdate Commander utility to version 1.6.0\n4.0.20\n\tUpdate Commander utility to version 1.5.0\n4.0.19\n\tUpdate Commander utility to version 1.3.2\n4.0.17\n\tUpdate Commander utility to version 1.3.1\n4.0.16\n\tUpdate Commander utility to version 1.2.3\n4.0.15\n\tUpdate Commander utility to version 1.2.2\n4.0.14\n\tUpdate Commander utility to version 1.2.0\n4.0.13\n\tUpdate Commander utility to version 1.1.0\n4.0.12\n\tUpdate Commander utility to version 1.0.0\n4.0.11\n\tUpdate Commander utility to version 0.25.3\n4.0.10\n\tUpdate Commander utility to version 0.25.0\n4.0.9\n\tUpdate Commander utility to version 0.24.1\n4.0.8\n\tFix symlinks on some MacOSX installations\n4.0.7\n\tUpdate Commander utility to version 0.22.1\n4.0.6\n\tUpdate Commander utility to version 0.21.2\n4.0.5\n\tUpdate Commander utility to version 0.21.1\n4.0.4\n\tcorrectly set device name when flashing\n4.0.3\n\tUpdate Commander utility to version 0.18.1\n4.0.2\n\tcorrectly set jlink speed when flashing\n4.0.1\n\tUpdate Commander utility to version 0.17.3\n4.0.0\n\tInitial release", "installEnabled": true, "id": "com.silabs.apack.commander.feature.metadata.resource.feature.group", "label": "Adapter Pack for Simplicity Commander - 4.0.72", "featurePackageId": "com.silabs.apack.commander.feature", "desc": "Provides a runtime interface to Simplicity Commander"}, {"installState": "Update", "releaseNotes": "5.6.25\n\tFix for upgrades of components that have conditional instances.\n5.6.24\n\tFix for loading adapter packs with new apack metadata\n5.6.23\n\tSV5.10.0 release changes\n5.6.22\n\tSV5.9.3.3. release\n\tDeal with parsing inappropriately annotated CMSIS content\n5.6.21\n    SV5.9.3.2 release\n5.6.20\n\tSV5.9.3.1 release\n5.6.19\n\tSV5.9.3.0 release.\n5.6.18\n\tSV5.9.2.0 release\n5.6.17\n\tSV5.9.1.1 release\n5.6.16\n\tMinor bug fixes and improvements.\n5.6.15\n\tMinor bug fixes and improvements.\n5.6.14\n\tMinor bug fixes and improvements.\n5.6.13\n\tFix issue with upgrading the project file on an extension update\n5.6.12\n\tFix issue with tracking currently running commands and don't start daemon shutdown timer until they complete\n5.6.11\n\tFix issue with upgrading an extension immediately after creating a new project\n5.6.10\n\tGreat number of bug fixes for SV5.8 release.\n5.6.9\n\tAdd feature to enable extension project upgrades when an extension is updated without a GSDK update.\n5.6.8\n\tcomponent selector performance improvements\n5.6.7\n\tSV5.7.2.0 release\n5.6.6\n\tSV5.7.1.0 release\n5.6.5\n\tFix bug where sometimes SDK changes in the SLC editor did not correctly apply to Simplicity IDE projects\n5.6.4\n\tSV5.7 release notes\n5.6.3\n\tFor IDEs that dont support solution build steps, inject them into a valid project's build step.\n\tAdd support to find SLCW files in the same project even if the name is different.\n5.6.2\n\tInclude ID generic-template within ideCompatibility property in software example metadata\n5.6.1\n\tBug fixes for studio 5.6.1\n5.2.3\n\tAdding support for post-build step in projects.\n\tAdding support for workspaces of projects.\n5.2.2\n  Fix issue where a selected board could not be removed from the slcp editor\n5.2.1\n  Minor bug fixes and improvements\n5.0.13\n  Minor bug fixes\n5.0.12\n\tMinor Bug fixes, including to upgrade rules.\n5.0.11\n\tMinor bug fixes\n5.0.9\n   Case sensitivity fix for Turkish language\n5.0.8\n\tVarious bug fixes.\n5.0.7\n\tAdd infrastructure for the command-line utility for project generation.\n5.0.6\n\tFix issue where the component editor remains open for a removed component when changing SDK.\n\tFix issue where a project was not handling slcp file's config_file flag.\t\n5.0.5\n\tInfrastructure improvements for future extensions\n\tMinor bug fixes\n5.0.4\n\tInfrastructure improvements for future extensions\n5.0.3\n\tInfrastructure improvements for future extensions\n5.0.2\n\tVarious infrastructure additions for future extensions\n5.0.1\n\tVarious infrastructure improvements for future extensions\n5.0.0\n\tInitial release", "installEnabled": true, "id": "com.silabs.ss.framework.uc.feature.metadata.resource.feature.group", "label": "Universal Configurator Framework - 5.6.27", "featurePackageId": "com.silabs.ss.framework.uc.feature", "desc": "Universal Configurator Framework"}, {"installState": "Update", "releaseNotes": "5.6.21.0\n   SV5.10.3.0, release of Simplicity Commander 1.17.5\n5.6.20.0\n   SV5.10.2.1 release of new Simplicity Commander\n5.6.19.0\n   SV5.10.1 release changes\n5.6.18.0\n   SV5.10.0 release changes\n5.6.17.0\n   SV5.10.0.0 release\n5.6.16.0\n   SV5.9.3.1 release\n5.6.15.0\n   SV5.9.3.0 release\n5.6.14.0\n   SV5.9.2.1 release\n5.6.13.0\n   SV5.9.2.0 release\n5.6.12.0\n   SV5.9.1.0 release\n5.6.11.0\n   SV5.9.0.1 release\n5.6.10.0\n   SV5.9.0.0 release\n5.6.9.0\n   SV5.8.2.0 release\n5.6.8.0\n   SV5.8.1.1 release - fix copyright year notice\n5.6.7.0\n\tSV5.8.1.0 release\n5.6.6.0\n   SV5.8.0.1 release\n5.6.5.0\n   SV5.8.0.0 release\n5.6.4.0\n   SV5.7.3.0 release\n5.6.3.0\n   SV5.7.2.0 release\n   Bug Fixes\n5.6.2.0\n   SV5.7.1.1 release\n   * Improve loading of example projects.\n5.6.1.0\n   SV5.7.1.0 release\n5.6.0.0\n   SV5.7.0.1 updates\n5.5.9.0\n   SV5.7 release notes\n5.5.8.0\n   Update package for SV5.6.4.0 release\n5.5.7.0\n   Update package for SV5.6.3.0 release\n5.5.6.0\n   Fix for the user preferences settings.\n5.5.5.0\n   Update package for SV5.6.0.0 release.\n5.5.4.0\n   Update package for SV5.5.1.0 release.\n5.5.3.0\n   Update package for SV5.5.0.0 release.\n5.5.2.0\n   Update packages for SV5.4.2.0 release.\n5.5.1.0\n   Bug fix: install by device now includes GSDK\n5.5.0.0\n   Updates for Stuidio 5.4.0.0\n5.3.3.0\n   Updates for studio 5.3.3.0\n5.3.2.0\n   Updates for Studio 5.3.2.0\n5.3.1.0\n   Ensure Detect Part UI is visible for JLink PRO and debug mode MINI\n5.3.0.0\n   Updates for studio 5.3.0.0\n5.2.4.0\n   Bug fix\n5.2.3.0\n   Updates for 5.2.3.0 release\n5.2.2.0\n   Updates for 5.2.2.0 release\n5.2.1.0\n   Updates for 5.2.1.0\n5.2.0.0\n   Updates for 5.2.0.0\n5.1.2.0\n   Part compatibility fix\n5.1.1.0\n   Bug fixes\n5.1.0.0\n   UI Improvements\n5.0.2.1\n   Fixed editor performance issue\n5.0.2.0\n   Fixed Installer UI bug with non-responsive dialog\n5.0.1.1\n   Fixed Update issue with 5.0.0.0\n5.0.1.0\n   Updates to Wireless Tools. \n5.0.0.0\n\tStudio V5 GA\n5.0.0.rc1\n\tFirst release candidate of V5\n5.0.0.beta3\n\tInitial release", "installEnabled": true, "id": "com.silabs.ss.tool.launcher.product.id.metadata.resource.feature.group", "label": "Tool Launcher - 5.6.21", "featurePackageId": "com.silabs.ss.tool.launcher.product.id", "desc": "Launcher UI Tool"}, {"installState": "Update", "releaseNotes": "5.1.2\n\tUpdate to BgApi 9.1.0\n5.1.1\n\tUpdate to BgApi 9.0.1\n5.1.0\n\tUpdate to BgApi 9.0.0\n\tMigrate reset to reboot command\n\tMinor improvements\n5.0.2\n\tUpdate to BgApi 8.2.0\n5.0.1\n\tUpdate to BgApi 8.1.0\n5.0.0\n\tUpdate to BgApi 8.0.0\n\tMinor improvements\n4.3.9\n\tUpdate to BgApi 7.1.0\n4.3.8\n\tUpdate to BgApi 7.0.1\n4.1.10\n\tAdding native builds for Mac ARM64 (macosx_aarch64) systems\n\tUpdate to BgApi 7.0.0\n\tRemove deprecated Advertiser and Scanner APIs usage from UI, Console still supported\n\tMinor improvements\n4.1.9\n\tUpdate to BgApi 6.2.0\n4.1.8\n\tReplace nwjs to electron\n\tUpdate to BgApi 6.1.0\n4.1.7\n\tUpdate to BgApi 6.0.0\n\tFix passkey copy\n\tFix Discovered Devices table\n\tSupport custom baud rate\n4.1.6\n\tFix keyboard only bonding dialog\n4.1.5\n\tBluetooth Mesh 1.1 Support\n\tUpdate to BgApi 5.1.1\n4.1.4\n\tUpdate to BgApi 5.1.0\n4.1.3\n\tUpdate to BgApi 5.0.0\n\tImprove RSSI filter slider\n\tNew API reference Search options\n\tOrder API reference entries\n\tAuto-pad bytes in Local GATT\n\tShow number of subscribed devices in Local GATT\n\tReject UI refresh on permission error in Remote GATT\n\tImprove Advertise Set Power Section\n\tAllow unloading custom API\n\tStore Scan table's pagination as persistent Setting\n\tNew: Models page, support for Generic OnOff and Level Get, Set and State\n\tNew: Key Refresh section, reworked DDB deletion\n\tNew: automatic retry option for config_client commands failed with timeout (enable in Settings)\n\tNew: ability to create App Keys and Networks with custom key, and group with custom address\n\tNew: App Keys, Subscriptions and Publish can be removed\n\tNew: Groups, App Keys, Networks and Nodes can be renamed\n\tNew: Mesh operations supported by the UI also work when typed manually in the console\n\tReworked local mesh data saving: Only labels (names) are saved for Networks, App Keys & Nodes. Groups are re-created when adding an already known network. Stored data clearable in Settings\n\tButtons, labels and pages renamed to align with mesh terminology\n\tImproved flow and UI for Initialize as Provisioner\n\tIntroduced app-wide colours for App Keys and Groups (orange and purple)\n\tApp Keys and Networks show their indices instead of their keys\n\tAdded ability to copy unformatted keys and UUIDs (on hover)\n\tNode Configuration no longer shows false values initially\n\tLong mesh operations have in-progress indication\n\tBind to All Models is now in the DCD Section\n\tImproved Provision page\n\tImproved Networks & Devices page\n\tImproved Node Configure page\n\tImproved individual model configuration\n\tImproved Mesh Settings page\n\tUnknown models In DCD now show up and are configurable\n\tMesh stability improvements and bugfixes\n4.1.2\n\tFixed issue with read and write request dialog.\n4.1.1\n\tUpdate to BgApi 4.2.0\n4.1.0\n\tSupport new BLE scan events on the Discovered Devices table.\n\tImprove Dark Theme UI\n4.0.8\n\tReshape smart console helper popup\n\tShow public Bluetooth address on provision device scan\n\tVisual and UX improvements\n\tSIG Models renamed to DCD (Device Composition Data)\n\tFix adding model subscriptions\n\tFix adding model publish addresses\n\tFix binding App Key to Vendor Models\n\tFix getting DCD via UI button and console\n4.0.7\n\tEnable unlisted enum value entry in Smart Console\n\tUpdate BG API: reset_to_dfu command, fix some bit mask values\n\tMesh Provision: Correct Device Key to Bluetooth Address in Discovered Devices table\n\tMesh Provision: Added default ordering by UUID to Discovered Devices table\n4.0.6\n\tAdd new security manager configuration flag\n\tFix CTE Length setting issue\n\tUpdate to latest BGAPI\n\tFix issue with Global Transmit Power Slider\n\tDeleting a Mesh Group now requires confirmation\n\tFixed issue in Device Tab name did not rename when the device was renamed\n\tChanged default Mesh LPN Timeout in Settings\n\tAdded Secure Network Beacon switch to Mesh device setting tab\n\tImplemented setting Unicast Address Range during Mesh Provisioning\n4.0.5\n   Fix issues with symbolic links on Mac OS and property values with spaces\n4.0.4\n   Updates for Studio SV5.3.1.0.\n4.0.3\n   Updated signing\n4.0.2\n   Updates for Gecko SDK 3.2.2.\n4.0.1\n   Maintenance\n4.0.0\n\tInitial release", "imageUrl": "studiofile://localhost/C:/SiliconLabs/SimplicityStudio/v5/configuration/cache/com.silabs.apack.ncp_commander.feature.metadata.resource.feature.group/bluetooth_brackets_40x40.png", "installEnabled": true, "id": "com.silabs.apack.ncp_commander.feature.metadata.resource.feature.group", "label": "Bluetooth NCP Commander Standalone for EFR32 - 5.1.2", "featurePackageId": "com.silabs.apack.ncp_commander.feature", "desc": "Standalone executable version of Bluetooth NCP Commander"}]}, {"category": "Asset Updates", "packages": [{"assetSize": "473.965KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an0016.2-efr32-series-2-oscillator-design-considerations.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an0016.2-efr32-series-2-oscillator-design-considerations.pdf"}, {"assetSize": "4665.8KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an1218-secure-boot-with-rtsl.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an1218-secure-boot-with-rtsl.pdf"}, {"assetSize": "1063.886KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "BRD4184A-A00-schematic.pdf", "desc": "/aem/www.silabs.com/documents/public/schematic-files/BRD4184A-A00-schematic.pdf"}, {"assetSize": "510.561KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efm32pg1b", "desc": "/efm32/configurator/efm32pg1b"}, {"assetSize": "3985.998KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efr32bg29_core.json", "desc": "/efr32/debug/bg/efr32bg29_core.json"}, {"assetSize": "1128.149KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an0948.2-efr32-series-2-power-configurations-and-dcdc.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an0948.2-efr32-series-2-power-configurations-and-dcdc.pdf"}, {"assetSize": "1414.926KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an1256-using-sl-rcp-with-openthread-border-router.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an1256-using-sl-rcp-with-openthread-border-router.pdf"}, {"assetSize": "2797.255KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "ug431-brd4183a-user-guide.pdf", "desc": "/aem/www.silabs.com/documents/public/user-guides/ug431-brd4183a-user-guide.pdf"}, {"assetSize": "2991.045KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an1296-application-development-with-rtl-library.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an1296-application-development-with-rtl-library.pdf"}, {"assetSize": "1066.892KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "AN0823 - 0.3", "desc": "/common/appnotes/AN0823"}, {"assetSize": "1089.244KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an1144-using-device-mgmt-service-zigbee-gateways.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an1144-using-device-mgmt-service-zigbee-gateways.pdf"}, {"assetSize": "500.543KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efm32jg12b", "desc": "/efm32/configurator/efm32jg12b"}, {"assetSize": "506.297KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efm32jg13b", "desc": "/efm32/configurator/efm32jg13b"}, {"assetSize": "479.677KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efm32jg1b", "desc": "/efm32/configurator/efm32jg1b"}, {"assetSize": "55.495KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "PCB4201-A02-assy-draw.pdf", "desc": "/aem/www.silabs.com/documents/public/schematic-files/PCB4201-A02-assy-draw.pdf"}, {"assetSize": "2139.754KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "brd4152a-rm.pdf", "desc": "/aem/www.silabs.com/documents/public/reference-manuals/brd4152a-rm.pdf"}, {"assetSize": "1045.41KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an1246-efr32bg-bluetooth-power-consumption.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an1246-efr32bg-bluetooth-power-consumption.pdf"}, {"assetSize": "560.71KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an0002.2-efr32-efm32-series-2-hardware-design-considerations.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an0002.2-efr32-efm32-series-2-hardware-design-considerations.pdf"}, {"assetSize": "592.217KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an1300-understanding-bluetooth-mesh-sensor-model-demo-sdk-2x.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an1300-understanding-bluetooth-mesh-sensor-model-demo-sdk-2x.pdf"}, {"assetSize": "2652.463KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "ug438-gatt-configurator-users-guide-sdk-v3x.pdf", "desc": "/aem/www.silabs.com/documents/public/user-guides/ug438-gatt-configurator-users-guide-sdk-v3x.pdf"}, {"assetSize": "672.357KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "sub-GHz_wireless_design_choices_for_smart_metering_wp.pdf", "desc": "/aem/www.silabs.com/documents/public/white-papers/sub-GHz_wireless_design_choices_for_smart_metering_wp.pdf"}, {"assetSize": "2.092KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "certificates", "desc": "/common/certificates"}, {"assetSize": "1926.485KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "ug358-brd4174b-user-guide.pdf", "desc": "/aem/www.silabs.com/documents/public/user-guides/ug358-brd4174b-user-guide.pdf"}, {"assetSize": "4.924KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "z_wave_development_kit_license_agreement", "desc": "/common/z_wave_development_kit_license_agreement"}, {"assetSize": "1114.684KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an1299-understanding-bluetooth-mesh-lighting-demo-sdk-2x.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an1299-understanding-bluetooth-mesh-lighting-demo-sdk-2x.pdf"}, {"assetSize": "8551.93KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efr32xg21-rm.pdf", "desc": "/aem/www.silabs.com/documents/public/reference-manuals/efr32xg21-rm.pdf"}, {"assetSize": "2280.953KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an930.2-efr32-series-2.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an930.2-efr32-series-2.pdf"}, {"assetSize": "434.642KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efm32hg", "desc": "/efm32/configurator/efm32hg"}, {"assetSize": "6613.886KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "smart-connected-lighting.pdf", "desc": "/aem/www.silabs.com/documents/referenced/white-papers/smart-connected-lighting.pdf"}, {"assetSize": "1050.495KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efm32gg", "desc": "/efm32/configurator/efm32gg"}, {"assetSize": "3322.676KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an1259-bt-ncp-mode-sdk-v3x.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an1259-bt-ncp-mode-sdk-v3x.pdf"}, {"assetSize": "290.665KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "brd4314a-unmounted.png", "desc": "/aem/www.silabs.com/content/dam/siliconlabs/images/products/simplicity-boards/brd4314a-unmounted.png"}, {"assetSize": "1719.935KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "bluetooth-smart-technologies-fundamentals.pdf", "desc": "/aem/www.silabs.com/documents/referenced/white-papers/bluetooth-smart-technologies-fundamentals.pdf"}, {"assetSize": "979.81KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "ug162-simplicity-commander-reference-guide.pdf", "desc": "/aem/www.silabs.com/documents/public/user-guides/ug162-simplicity-commander-reference-guide.pdf"}, {"assetSize": "669.016KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efm32g", "desc": "/efm32/configurator/efm32g"}, {"assetSize": "698.154KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "brd4164a-rm.pdf", "desc": "/aem/www.silabs.com/documents/public/reference-manuals/brd4164a-rm.pdf"}, {"assetSize": "11616.92KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efr32xg12-rm.pdf", "desc": "/aem/www.silabs.com/documents/public/reference-manuals/efr32xg12-rm.pdf"}, {"assetSize": "26707.119KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an928.2-efr32-series2-layout-design-guide.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an928.2-efr32-series2-layout-design-guide.pdf"}, {"assetSize": "987.061KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an1315-bluetooth-mesh-power-consumption-measurements.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an1315-bluetooth-mesh-power-consumption-measurements.pdf"}, {"assetSize": "31.289KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "license_txt", "desc": "/common/license_txt"}, {"assetSize": "822.267KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "an1318-bluetooth-mesh-iv-update.pdf", "desc": "/aem/www.silabs.com/documents/public/application-notes/an1318-bluetooth-mesh-iv-update.pdf"}, {"assetSize": "1444.502KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efm32lg", "desc": "/efm32/configurator/efm32lg"}, {"assetSize": "506.223KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efm32pg13b", "desc": "/efm32/configurator/efm32pg13b"}, {"assetSize": "500.48KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efm32pg12b", "desc": "/efm32/configurator/efm32pg12b"}, {"assetSize": "1446.959KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efm32wg", "desc": "/efm32/configurator/efm32wg"}, {"assetSize": "680.952KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efm32tg", "desc": "/efm32/configurator/efm32tg"}, {"assetSize": "402.67KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efm32zg", "desc": "/efm32/configurator/efm32zg"}, {"assetSize": "349.005KB", "imageUrl": "bundleentry://90.fwk1120013796/icons/icon_asset.png", "label": "efr32mg13-errata.pdf", "desc": "/aem/www.silabs.com/documents/public/errata/efr32mg13-errata.pdf"}]}], "productUpdates": 10, "sdkUpdatesSize": 1, "assetUpdates": 48, "updates": 58}