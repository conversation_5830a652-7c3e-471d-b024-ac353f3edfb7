[{"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Mini Simplicity Debugger Board", "description": "", "id": "brd1015a:0.0.0.A01", "label": "Mini Simplicity Debugger Board (BRD1015A Rev A01)", "type": "Board", "opn": "BRD1015"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.si.mg301.simg301m113wih"], "imageURL": "", "name": "SixG301 QFN32 Socket Radio Board", "description": "", "id": "brd1019a:0.0.0.A01", "label": "SixG301 QFN32 Socket Radio Board (BRD1019A Rev A01)", "type": "Board", "opn": "BRD1019"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg29.efr32bg29b221f1024cj45"], "imageURL": "", "name": "EFR32BG29 Bluetooth LE 4 dBm Boost WLCSP Socket Radio Board", "description": "", "id": "brd1021a:0.0.0.A01", "label": "EFR32BG29 Bluetooth LE 4 dBm Boost WLCSP Socket Radio Board (BRD1021A Rev A01)", "type": "Board", "opn": "BRD1021"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.g.efm32g890f128"], "imageURL": "", "name": "EFM32 Gecko Starter Kit board", "description": "", "id": "brd2001a:0.0.0.A04", "label": "EFM32 Gecko Starter Kit board (BRD2001A Rev A04)", "type": "Board", "opn": "BRD2001"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.g.efm32g890f128"], "imageURL": "", "name": "EFM32 Gecko Starter Kit board", "description": "", "id": "brd2001b:0.0.0.A00", "label": "EFM32 Gecko Starter Kit board (BRD2001B Rev A00)", "type": "Board", "opn": "BRD2001"}, {"rev": "B01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.zg.efm32zg222f32"], "imageURL": "", "name": "EFM32 Zero Gecko Starter Kit board", "description": "", "id": "brd2010a:0.0.0.B01", "label": "EFM32 Zero Gecko Starter Kit board (BRD2010A Rev B01)", "type": "Board", "opn": "BRD2010"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.zg.efm32zg222f32"], "imageURL": "", "name": "EFM32 Zero Gecko Starter Kit board", "description": "", "id": "brd2010b:0.0.0.A00", "label": "EFM32 Zero Gecko Starter Kit board (BRD2010B Rev A00)", "type": "Board", "opn": "BRD2010"}, {"rev": "B01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.hg.efm32hg322f64"], "imageURL": "", "name": "EFM32 Happy Gecko Starter Kit Board", "description": "", "id": "brd2012a:0.0.0.B01", "label": "EFM32 Happy Gecko Starter Kit Board (BRD2012A Rev B01)", "type": "Board", "opn": "BRD2012"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.hg.efm32hg322f64"], "imageURL": "", "name": "EFM32 Happy Gecko Starter Kit Board", "description": "", "id": "brd2012b:0.0.0.A00", "label": "EFM32 Happy Gecko Starter Kit Board (BRD2012B Rev A00)", "type": "Board", "opn": "BRD2012"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.tg.efm32tg840f32"], "imageURL": "", "name": "EFM32 Tiny Gecko Starter Kit board", "description": "", "id": "brd2100a:0.0.0.A05", "label": "EFM32 Tiny Gecko Starter Kit board (BRD2100A Rev A05)", "type": "Board", "opn": "BRD2100"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.tg.efm32tg840f32"], "imageURL": "", "name": "EFM32 Tiny Gecko Starter Kit board", "description": "", "id": "brd2100b:0.0.0.A00", "label": "EFM32 Tiny Gecko Starter Kit board (BRD2100B Rev A00)", "type": "Board", "opn": "BRD2100"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.tg11.efm32tg11b520f128gm80"], "imageURL": "", "name": "EFM32TG11 Tiny Gecko Starter Kit board", "description": "", "id": "brd2102a:0.0.0.A04", "label": "EFM32TG11 Tiny Gecko Starter Kit board (BRD2102A Rev A04)", "type": "Board", "opn": "BRD2102"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.tg11.efm32tg11b520f128gm80"], "imageURL": "", "name": "EFM32TG11 Tiny Gecko Starter Kit board", "description": "", "id": "brd2102b:0.0.0.A00", "label": "EFM32TG11 Tiny Gecko Starter Kit board (BRD2102B Rev A00)", "type": "Board", "opn": "BRD2102"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.gg.efm32gg990f1024"], "imageURL": "", "name": "EFM32 Giant Gecko Starter Kit board", "description": "", "id": "brd2200a:0.0.0.A03", "label": "EFM32 Giant Gecko Starter Kit board (BRD2200A Rev A03)", "type": "Board", "opn": "BRD2200"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.gg.efm32gg990f1024"], "imageURL": "", "name": "EFM32 Giant Gecko Starter Kit board", "description": "", "id": "brd2200c:0.0.0.A00", "label": "EFM32 Giant Gecko Starter Kit board (BRD2200C Rev A00)", "type": "Board", "opn": "BRD2200"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.gg.efm32gg990f1024"], "imageURL": "", "name": "EFM32 Giant Gecko Starter Kit board", "description": "", "id": "brd2200c:0.0.0.A01", "label": "EFM32 Giant Gecko Starter Kit board (BRD2200C Rev A01)", "type": "Board", "opn": "BRD2200"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.lg.efm32lg990f256"], "imageURL": "", "name": "EFM32 Leopard Gecko Starter Kit board", "description": "", "id": "brd2201a:0.0.0.A02", "label": "EFM32 Leopard Gecko Starter Kit board (BRD2201A Rev A02)", "type": "Board", "opn": "BRD2201"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.lg.efm32lg990f256"], "imageURL": "", "name": "EFM32 Leopard Gecko Starter Kit board", "description": "", "id": "brd2201a:0.0.0.A03", "label": "EFM32 Leopard Gecko Starter Kit board (BRD2201A Rev A03)", "type": "Board", "opn": "BRD2201"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.lg.efm32lg990f256"], "imageURL": "", "name": "EFM32 Leopard Gecko Starter Kit board", "description": "", "id": "brd2201b:0.0.0.A00", "label": "EFM32 Leopard Gecko Starter Kit board (BRD2201B Rev A00)", "type": "Board", "opn": "BRD2201"}, {"rev": "B00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.gg11.efm32gg11b820f2048gl192"], "imageURL": "", "name": "EFM32GG11 Giant Gecko Starter Kit board", "description": "", "id": "brd2204a:0.0.0.B00", "label": "EFM32GG11 Giant Gecko Starter Kit board (BRD2204A Rev B00)", "type": "Board", "opn": "BRD2204"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.gg11.efm32gg11b820f2048gl192"], "imageURL": "", "name": "EFM32GG11 Giant Gecko Starter Kit board", "description": "", "id": "brd2204c:0.0.0.A02", "label": "EFM32GG11 Giant Gecko Starter Kit board (BRD2204C Rev A02)", "type": "Board", "opn": "BRD2204"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.gg12.efm32gg12b810f1024gm64"], "imageURL": "", "name": "Thunderboard EFM32GG12", "description": "", "id": "brd2207a:0.0.0", "label": "Thunderboard EFM32GG12 (BRD2207A)", "type": "Board", "opn": "BRD2207"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.wg.efm32wg990f256"], "imageURL": "", "name": "EFM32 Wonder Gecko Starter Kit board", "description": "", "id": "brd2400a:0.0.0.A00", "label": "EFM32 Wonder Gecko Starter Kit board (BRD2400A Rev A00)", "type": "Board", "opn": "BRD2400"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.wg.efm32wg990f256"], "imageURL": "", "name": "EFM32 Wonder Gecko Starter Kit board", "description": "", "id": "brd2400b:0.0.0.A00", "label": "EFM32 Wonder Gecko Starter Kit board (BRD2400B Rev A00)", "type": "Board", "opn": "BRD2400"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg1.efm32pg1b200f256gm48"], "imageURL": "", "name": "EFM32 Pearl Gecko Starter Kit board", "description": "", "id": "brd2500a:0.0.0", "label": "EFM32 Pearl Gecko Starter Kit board (BRD2500A)", "type": "Board", "opn": "BRD2500"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg1.efm32pg1b200f256gm48"], "imageURL": "", "name": "EFM32PG1 Pearl Gecko Starter Kit board", "description": "", "id": "brd2500b:0.0.0.A00", "label": "EFM32PG1 Pearl Gecko Starter Kit board (BRD2500B Rev A00)", "type": "Board", "opn": "BRD2500"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg12.efm32pg12b500f1024gl125"], "imageURL": "", "name": "EFM32PG12 Pearl Gecko Starter Kit board", "description": "", "id": "brd2501a:0.0.0.A00", "label": "EFM32PG12 Pearl Gecko Starter Kit board (BRD2501A Rev A00)", "type": "Board", "opn": "BRD2501"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg12.efm32pg12b500f1024gl125"], "imageURL": "", "name": "EFM32PG12 Pearl Gecko Starter Kit board", "description": "", "id": "brd2501a:0.0.0.A01", "label": "EFM32PG12 Pearl Gecko Starter Kit board (BRD2501A Rev A01)", "type": "Board", "opn": "BRD2501"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg12.efm32pg12b500f1024gl125"], "imageURL": "", "name": "EFM32PG12 Pearl Gecko Starter Kit", "description": "", "id": "brd2501b:0.0.0.A00", "label": "EFM32PG12 Pearl Gecko Starter Kit (BRD2501B Rev A00)", "type": "Board", "opn": "BRD2501"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg22.efm32pg22c200f512im40"], "imageURL": "", "name": "PG22 Dev Kit Board", "description": "", "id": "brd2503a:0.0.0", "label": "PG22 Dev Kit Board (BRD2503A)", "type": "Board", "opn": "BRD2503"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg22.efm32pg22c200f512im40"], "imageURL": "", "name": "PG22 Dev Kit Board", "description": "", "id": "brd2503b:0.0.0.A00", "label": "PG22 Dev Kit Board (BRD2503B Rev A00)", "type": "Board", "opn": "BRD2503"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg23.efm32pg23b310f512im48"], "imageURL": "", "name": "EFM32PG23 Pearl Gecko Starter Kit board", "description": "", "id": "brd2504a:0.0.0", "label": "EFM32PG23 Pearl Gecko Starter Kit board (BRD2504A)", "type": "Board", "opn": "BRD2504"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg23.efm32pg23b310f512im48"], "imageURL": "", "name": "PG23 Pro Kit Board", "description": "", "id": "brd2504a:0.0.0.A01", "label": "PG23 Pro Kit Board (BRD2504A Rev A01)", "type": "Board", "opn": "BRD2504"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg23.efm32pg23b310f512im48"], "imageURL": "", "name": "PG23 Pro Kit Board", "description": "", "id": "brd2504a:0.0.0.A02", "label": "PG23 Pro Kit Board (BRD2504A Rev A02)", "type": "Board", "opn": "BRD2504"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg23.efm32pg23b310f512im48"], "imageURL": "", "name": "PG23 Pro Kit Board", "description": "", "id": "brd2504a:0.0.0.A03", "label": "PG23 Pro Kit Board (BRD2504A Rev A03)", "type": "Board", "opn": "BRD2504"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg23.efm32pg23b310f512im48"], "imageURL": "", "name": "PG23 Pro Kit Board", "description": "", "id": "brd2504a:0.0.0.A04", "label": "PG23 Pro Kit Board (BRD2504A Rev A04)", "type": "Board", "opn": "BRD2504"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg26.efm32pg26b500f3200il136"], "imageURL": "", "name": "EFM32PG26 Pro Kit Board", "description": "", "id": "brd2505a:0.0.0.A02", "label": "EFM32PG26 Pro Kit Board (BRD2505A Rev A02)", "type": "Board", "opn": "BRD2505"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg28.efm32pg28b310f1024im68"], "imageURL": "", "name": "PG28 Pro Kit Board", "description": "", "id": "brd2506a:0.0.0.A03", "label": "PG28 Pro Kit Board (BRD2506A Rev A03)", "type": "Board", "opn": "BRD2506"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg23.efr32fg23b010f512gm48"], "imageURL": "", "name": "FG23 Dev Kit Board", "description": "", "id": "brd2600a:0.0.0", "label": "FG23 Dev Kit Board (BRD2600A)", "type": "Board", "opn": "BRD2600"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg23.efr32fg23b010f512im48"], "imageURL": "", "name": "EFR32FG23 Dev Kit Board", "description": "", "id": "brd2600a:0.0.0.A01", "label": "EFR32FG23 Dev Kit Board (BRD2600A Rev A01)", "type": "Board", "opn": "BRD2600"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b110f1536gm48"], "imageURL": "", "name": "EFR32xG24 Dev Kit Board", "description": "", "id": "brd2601a:0.0.0.A00", "label": "EFR32xG24 Dev Kit Board (BRD2601A Rev A00)", "type": "Board", "opn": "BRD2601"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b310f1536im48"], "imageURL": "", "name": "EFR32xG24 Dev Kit Board", "description": "", "id": "brd2601b:0.0.0.A00", "label": "EFR32xG24 Dev Kit Board (BRD2601B Rev A00)", "type": "Board", "opn": "BRD2601"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b310f1536im48"], "imageURL": "", "name": "EFR32xG24 Dev Kit Board", "description": "", "id": "brd2601b:0.0.0.A01", "label": "EFR32xG24 Dev Kit Board (BRD2601B Rev A01)", "type": "Board", "opn": "BRD2601"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b310f1536im48"], "imageURL": "", "name": "EFR32xG24 Dev Kit Board", "description": "", "id": "brd2601b:0.0.0.A02", "label": "EFR32xG24 Dev Kit Board (BRD2601B Rev A02)", "type": "Board", "opn": "BRD2601"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg27.efr32bg27c140f768im40"], "imageURL": "", "name": "EFR32BG27 +8 dBm Dev Kit Board", "description": "", "id": "brd2602a:0.0.0.A02", "label": "EFR32BG27 +8 dBm Dev Kit Board (BRD2602A Rev A02)", "type": "Board", "opn": "BRD2602"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg23.zgm230sb27hgn"], "imageURL": "", "name": "ZGM230 +14 dBm Dev Kit Board", "description": "", "id": "brd2603a:0.0.0.A00", "label": "ZGM230 +14 dBm Dev Kit Board (BRD2603A Rev A00)", "type": "Board", "opn": "BRD2603"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg23.zgm230sa27hgn"], "imageURL": "", "name": "ZGM230 +14 dBm Dev Kit Board", "description": "", "id": "brd2603b:0.0.0.A00", "label": "ZGM230 +14 dBm Dev Kit Board (BRD2603B Rev A00)", "type": "Board", "opn": "BRD2603"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba"], "imageURL": "", "name": "SiWG917 Dev Kit Board", "description": "", "id": "brd2605a:0.0.0.A02", "label": "SiWG917 Dev Kit Board (BRD2605A Rev A02)", "type": "Board", "opn": "BRD2605"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b210f1536im48"], "imageURL": "", "name": "EFR32xG24 Channel Sounding Dev Kit Board", "description": "", "id": "brd2606a:0.0.0.A01", "label": "EFR32xG24 Channel Sounding Dev Kit Board (BRD2606A Rev A01)", "type": "Board", "opn": "BRD2606"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b210f1536im48"], "imageURL": "", "name": "EFR32xG24 Channel Sounding Dev Kit Board", "description": "", "id": "brd2606a:0.0.0.A03", "label": "EFR32xG24 Channel Sounding Dev Kit Board (BRD2606A Rev A03)", "type": "Board", "opn": "BRD2606"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b210f1536im48"], "imageURL": "", "name": "EFR32xG24 Channel Sounding Dev Kit Board", "description": "", "id": "brd2606a:0.0.0.A04", "label": "EFR32xG24 Channel Sounding Dev Kit Board (BRD2606A Rev A04)", "type": "Board", "opn": "BRD2606"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.efr32mg26b510f3200im68"], "imageURL": "", "name": "EFR32xG26 Dev Kit Board", "description": "", "id": "brd2608a:0.0.0.A02", "label": "EFR32xG26 Dev Kit Board (BRD2608A Rev A02)", "type": "Board", "opn": "BRD2608"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b020f1536im48"], "imageURL": "", "name": "EFR32xG24 Explorer Kit", "description": "", "id": "brd2703a:0.0.0.A00", "label": "EFR32xG24 Explorer Kit (BRD2703A Rev A00)", "type": "Board", "opn": "BRD2703"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b210f1536im48"], "imageURL": "", "name": "EFR32xG24 Explorer Kit", "description": "", "id": "brd2703a:0.0.0.A01", "label": "EFR32xG24 Explorer Kit (BRD2703A Rev A01)", "type": "Board", "opn": "BRD2703"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b210f1536im48"], "imageURL": "", "name": "EFR32xG24 Explorer Kit", "description": "", "id": "brd2703a:0.0.0.A02", "label": "EFR32xG24 Explorer Kit (BRD2703A Rev A02)", "type": "Board", "opn": "BRD2703"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240pb32vna"], "imageURL": "", "name": "Sparkfun Thing Plus MGM240P", "description": "", "id": "brd2704a:0.0.0.A00", "label": "Sparkfun Thing Plus MGM240P (BRD2704A Rev A00)", "type": "Board", "opn": "BRD2704"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg28.efr32zg28b312f1024im48"], "imageURL": "", "name": "EFR32xG28 Explorer Kit", "description": "", "id": "brd2705a:0.0.0.A01", "label": "EFR32xG28 Explorer Kit (BRD2705A Rev A01)", "type": "Board", "opn": "BRD2705"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917y111mgaba"], "imageURL": "", "name": "SiWx917Y Module Wi-Fi 6 and Bluetooth LE Explorer Kit Board", "description": "", "id": "brd2708a:0.0.0.A02", "label": "SiWx917Y Module Wi-Fi 6 and Bluetooth LE Explorer Kit Board (BRD2708A Rev A02)", "type": "Board", "opn": "BRD2708"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917y111mgaba"], "imageURL": "", "name": "SiWx917Y Module Wi-Fi 6 and Bluetooth LE Explorer Kit Board", "description": "", "id": "brd2708a:0.0.0.A03", "label": "SiWx917Y Module Wi-Fi 6 and Bluetooth LE Explorer Kit Board (BRD2708A Rev A03)", "type": "Board", "opn": "BRD2708"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.efr32mg26b510f3200im48"], "imageURL": "", "name": "EFR32xG26 Explorer Kit Board", "description": "", "id": "brd2709a:0.0.0.A02", "label": "EFR32xG26 Explorer Kit Board (BRD2709A Rev A02)", "type": "Board", "opn": "BRD2709"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg22.efr32mg22e224f512im40"], "imageURL": "", "name": "EFR32MG22E Explorer Kit Board", "description": "", "id": "brd2710a:0.0.0.A00", "label": "EFR32MG22E Explorer Kit Board (BRD2710A Rev A00)", "type": "Board", "opn": "BRD2710"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg22.efr32mg22e224f512im40"], "imageURL": "", "name": "EFR32xG22E Explorer Kit Board", "description": "", "id": "brd2710a:0.0.0.A01", "label": "EFR32xG22E Explorer Kit Board (BRD2710A Rev A01)", "type": "Board", "opn": "BRD2710"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.pg26.efm32pg26b500f3200im68"], "imageURL": "", "name": "EFM32PG26 MCU Explorer Kit Board", "description": "", "id": "brd2711a:0.0.0.A01", "label": "EFM32PG26 MCU Explorer Kit Board (BRD2711A Rev A01)", "type": "Board", "opn": "BRD2711"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.mgm260pb32vna"], "imageURL": "", "name": "xGM260P 20 dBm Explorer Kit Board", "description": "", "id": "brd2713a:0.0.0.A03", "label": "xGM260P 20 dBm Explorer Kit Board (BRD2713A Rev A03)", "type": "Board", "opn": "BRD2713"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.mgm260pd32vna"], "imageURL": "", "name": "xGM260P Explorer Kit Board", "description": "", "id": "brd2713a:0.0.0.A04", "label": "xGM260P Explorer Kit Board (BRD2713A Rev A04)", "type": "Board", "opn": "BRD2713"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "Laird Connectivity RM1261 Development Kit", "description": "", "id": "brd2900a:0.0.0.A00", "label": "Laird Connectivity RM1261 Development Kit (BRD2900A Rev A00)", "type": "Board", "opn": "BRD2900"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "Laird Connectivity RM1262 Development Kit", "description": "", "id": "brd2901a:0.0.0.A00", "label": "Laird Connectivity RM1262 Development Kit (BRD2901A Rev A00)", "type": "Board", "opn": "BRD2901"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg24.bgm240pb22vna"], "imageURL": "", "name": "Laird Connectivity Lyra 24P 10dBm (Built-in) Ant Development Kit", "description": "", "id": "brd2902a:0.0.0.A00", "label": "Laird Connectivity Lyra 24P 10dBm (Built-in) Ant Development Kit (BRD2902A Rev A00)", "type": "Board", "opn": "BRD2902"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg24.bgm240pb32vnn"], "imageURL": "", "name": "Laird Connectivity Lyra 24P 20dBm (RF) Pin Development Kit", "description": "", "id": "brd2903a:0.0.0.A00", "label": "Laird Connectivity Lyra 24P 20dBm (RF) Pin Development Kit (BRD2903A Rev A00)", "type": "Board", "opn": "BRD2903"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg24.bgm240pb32vna"], "imageURL": "", "name": "Laird Connectivity Lyra 24P 20dBm (Built-in) Ant Development Kit", "description": "", "id": "brd2904a:0.0.0.A00", "label": "Laird Connectivity Lyra 24P 20dBm (Built-in) Ant Development Kit (BRD2904A Rev A00)", "type": "Board", "opn": "BRD2904"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg24.bgm240sb22vna"], "imageURL": "", "name": "Laird Connectivity Lyra 24S 10dBm Development Kit", "description": "", "id": "brd2905a:0.0.0.A00", "label": "Laird Connectivity Lyra 24S 10dBm Development Kit (BRD2905A Rev A00)", "type": "Board", "opn": "BRD2905"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917y111mgaba"], "imageURL": "", "name": "<PERSON><PERSON><PERSON> Veda SL917 Explorer Kit Board", "description": "", "id": "brd2911a:0.0.0.A00", "label": "<PERSON><PERSON><PERSON> Veda SL917 Explorer Kit Board (BRD2911A Rev A00)", "type": "Board", "opn": "BRD2911"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "EFM32 Gecko Development Kit Mainboard", "description": "", "id": "brd3200c:0.0.0", "label": "EFM32 Gecko Development Kit Mainboard (BRD3200C)", "type": "Board", "opn": "BRD3200"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "EFM32 Development Kit Mainboard", "description": "", "id": "brd3201a:0.0.0.A03", "label": "EFM32 Development Kit Mainboard (BRD3201A Rev A03)", "type": "Board", "opn": "BRD3201"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "EFM32 Development Kit Mainboard - FPGA Edition", "description": "", "id": "brd3201b:0.0.0", "label": "EFM32 Development Kit Mainboard - FPGA Edition (BRD3201B)", "type": "Board", "opn": "BRD3201"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.g.efm32g890f128"], "imageURL": "", "name": "EFM32 Gecko Development Kit G890 MCU board with segment LCD", "description": "", "id": "brd3300a:0.0.0", "label": "EFM32 Gecko Development Kit G890 MCU board with segment LCD (BRD3300A)", "type": "Board", "opn": "BRD3300"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.g.efm32g290f128"], "imageURL": "", "name": "EFM32 Gecko Development Kit G290 MCU board without segment LCD", "description": "", "id": "brd3300b:0.0.0", "label": "EFM32 Gecko Development Kit G290 MCU board without segment LCD (BRD3300B)", "type": "Board", "opn": "BRD3300"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.g.efm32g890f128"], "imageURL": "", "name": "EFM32 Gecko DK MCU Board (for BRD3201)", "description": "", "id": "brd3302a:0.0.0.A03", "label": "EFM32 Gecko DK MCU Board (for BRD3201) (BRD3302A Rev A03)", "type": "Board", "opn": "BRD3302"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "EXP32 Prototyping board", "description": "", "id": "brd3500a:0.0.0", "label": "EXP32 Prototyping board (BRD3500A)", "type": "Board", "opn": "BRD3500"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "EXP32 Prototyping board - Black Edition", "description": "", "id": "brd3500b:0.0.0.A00", "label": "EXP32 Prototyping board - Black Edition (BRD3500B Rev A00)", "type": "Board", "opn": "BRD3500"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.gg.efm32gg990f1024"], "imageURL": "", "name": "EFM32 Giant Gecko DK MCU Plugin Board", "description": "", "id": "brd3600a:0.0.0.A05", "label": "EFM32 Giant Gecko DK MCU Plugin Board (BRD3600A Rev A05)", "type": "Board", "opn": "BRD3600"}, {"rev": "B01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.lg.efm32lg990f256"], "imageURL": "", "name": "EFM32 Leopard Gecko DK MCU Plugin Board", "description": "", "id": "brd3601a:0.0.0.B01", "label": "EFM32 Leopard Gecko DK MCU Plugin Board (BRD3601A Rev B01)", "type": "Board", "opn": "BRD3601"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.wg.efm32wg990f256"], "imageURL": "", "name": "EFM32 Wonder Gecko DK MCU Plugin Board", "description": "", "id": "brd3800a:0.0.0.A00", "label": "EFM32 Wonder Gecko DK MCU Plugin Board (BRD3800A Rev A00)", "type": "Board", "opn": "BRD3800"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.wg.efm32wg360f256"], "imageURL": "", "name": "EFM32 Wonder Gecko CSP DK MCU Plugin Board", "description": "", "id": "brd3801a:0.0.0", "label": "EFM32 Wonder Gecko CSP DK MCU Plugin Board (BRD3801A)", "type": "Board", "opn": "BRD3801"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Wireless Starter Kit Mainboard", "description": "", "id": "brd4001a:0.0.0.A01", "label": "Wireless Starter Kit Mainboard (BRD4001A Rev A01)", "type": "Board", "opn": "BRD4001"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Wireless Pro Kit Mainboard", "description": "", "id": "brd4002a:0.0.0.A03", "label": "Wireless Pro Kit Mainboard (BRD4002A Rev A03)", "type": "Board", "opn": "BRD4002"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Wireless Pro Kit Mainboard", "description": "", "id": "brd4002a:0.0.0.A04", "label": "Wireless Pro Kit Mainboard (BRD4002A Rev A04)", "type": "Board", "opn": "BRD4002"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Wireless Pro Kit Mainboard", "description": "", "id": "brd4002a:0.0.0.A05", "label": "Wireless Pro Kit Mainboard (BRD4002A Rev A05)", "type": "Board", "opn": "BRD4002"}, {"rev": "A06", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Wireless Pro Kit Mainboard", "description": "", "id": "brd4002a:0.0.0.A06", "label": "Wireless Pro Kit Mainboard (BRD4002A Rev A06)", "type": "Board", "opn": "BRD4002"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg1.efr32bg1p232f256gm48"], "imageURL": "", "name": "EFR32BG 2.4 GHz 10.5 dBm Radio Board", "description": "", "id": "brd4100a:0.0.0", "label": "EFR32BG 2.4 GHz 10.5 dBm Radio Board (BRD4100A)", "type": "Board", "opn": "BRD4100"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg1.efr32bg1p332f256gj43"], "imageURL": "", "name": "EFR32BG 2.4 GHz 19.5 dBm WLCSP Radio Board", "description": "", "id": "brd4101a:0.0.0", "label": "EFR32BG 2.4 GHz 19.5 dBm WLCSP Radio Board (BRD4101A)", "type": "Board", "opn": "BRD4101"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg1.efr32bg1p332f256gj43"], "imageURL": "", "name": "EFR32BG 2.4 GHz 8 dBm WLCSP Radio Board", "description": "", "id": "brd4101b:0.0.0.A01", "label": "EFR32BG 2.4 GHz 8 dBm WLCSP Radio Board (BRD4101B Rev A01)", "type": "Board", "opn": "BRD4101"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg1.efr32bg1b232f256gj43"], "imageURL": "", "name": "EFR32BG 2.4 GHz 8 dBm WLCSP Radio Board", "description": "", "id": "brd4101b:0.0.0.A02", "label": "EFR32BG 2.4 GHz 8 dBm WLCSP Radio Board (BRD4101B Rev A02)", "type": "Board", "opn": "BRD4101"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg12.efr32bg12p332f1024gl125"], "imageURL": "", "name": "EFR32BG12 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4103a:0.0.0.A00", "label": "EFR32BG12 2.4 GHz 10 dBm Radio Board (BRD4103A Rev A00)", "type": "Board", "opn": "BRD4103"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg12.efr32bg12p332f1024gl125"], "imageURL": "", "name": "EFR32BG12 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4103a:0.0.0.A01", "label": "EFR32BG12 2.4 GHz 10 dBm Radio Board (BRD4103A Rev A01)", "type": "Board", "opn": "BRD4103"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg12.efr32bg12p332f1024gl125"], "imageURL": "", "name": "EFR32BG12 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4103a:0.0.0.A02", "label": "EFR32BG12 2.4 GHz 10 dBm Radio Board (BRD4103A Rev A02)", "type": "Board", "opn": "BRD4103"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg13.efr32bg13p632f512gm48"], "imageURL": "", "name": "EFR32BG13 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4104a:0.0.0.A00", "label": "EFR32BG13 2.4 GHz 10 dBm Radio Board (BRD4104A Rev A00)", "type": "Board", "opn": "BRD4104"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "EFR32BG22 Explorer Kit Board", "description": "", "id": "brd4108a:0.0.0", "label": "EFR32BG22 Explorer Kit Board (BRD4108A)", "type": "Board", "opn": "BRD4108"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg27.efr32bg27c320f768gj39"], "imageURL": "", "name": "BG27 2.4 GHz 4 dBm DCDC Buck CSP Radio Board", "description": "", "id": "brd4110a:0.0.0.A00", "label": "BG27 2.4 GHz 4 dBm DCDC Buck CSP Radio Board (BRD4110A Rev A00)", "type": "Board", "opn": "BRD4110"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg27.efr32bg27c320f768gj39"], "imageURL": "", "name": "BG27 2.4 GHz 4 dBm DCDC Buck CSP Radio Board", "description": "", "id": "brd4110b:0.0.0.A01", "label": "BG27 2.4 GHz 4 dBm DCDC Buck CSP Radio Board (BRD4110B Rev A01)", "type": "Board", "opn": "BRD4110"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg27.efr32bg27c320f768gj39"], "imageURL": "", "name": "BG27 2.4 GHz 4 dBm DCDC Boost CSP Radio Board", "description": "", "id": "brd4111a:0.0.0.A00", "label": "BG27 2.4 GHz 4 dBm DCDC Boost CSP Radio Board (BRD4111A Rev A00)", "type": "Board", "opn": "BRD4111"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg27.efr32bg27c320f768gj39"], "imageURL": "", "name": "BG27 2.4 GHz 4 dBm DCDC Boost CSP Radio Board", "description": "", "id": "brd4111a:0.0.0.A01", "label": "BG27 2.4 GHz 4 dBm DCDC Boost CSP Radio Board (BRD4111A Rev A01)", "type": "Board", "opn": "BRD4111"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg27.efr32bg27c320f768gj39"], "imageURL": "", "name": "BG27 2.4 GHz 4 dBm DCDC Boost CSP Radio Board", "description": "", "id": "brd4111b:0.0.0.A01", "label": "BG27 2.4 GHz 4 dBm DCDC Boost CSP Radio Board (BRD4111B Rev A01)", "type": "Board", "opn": "BRD4111"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg24.efr32bg24b410f1536ij42"], "imageURL": "", "name": "BG24 2.4 Ghz 4 dBm WLCSP Radio Board", "description": "", "id": "brd4115a:0.0.0.A00", "label": "BG24 2.4 Ghz 4 dBm WLCSP Radio Board (BRD4115A Rev A00)", "type": "Board", "opn": "BRD4115"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg24.efr32bg24b310f1536ij42"], "imageURL": "", "name": "BG24 2.4 Ghz 4 dBm WLCSP Radio Board", "description": "", "id": "brd4115b:0.0.0.A00", "label": "BG24 2.4 Ghz 4 dBm WLCSP Radio Board (BRD4115B Rev A00)", "type": "Board", "opn": "BRD4115"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.efr32mg26b410f3200im48"], "imageURL": "", "name": "EFR32xG26 2.4 GHz 10 dBm QFN48 Radio Board", "description": "", "id": "brd4116a:0.0.0.A00", "label": "EFR32xG26 2.4 GHz 10 dBm QFN48 Radio Board (BRD4116A Rev A00)", "type": "Board", "opn": "BRD4116"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.efr32mg26b420f3200im48"], "imageURL": "", "name": "EFR32xG26 2.4 GHz 20 dBm QFN48 Radio Board", "description": "", "id": "brd4117a:0.0.0.A00", "label": "EFR32xG26 2.4 GHz 20 dBm QFN48 Radio Board (BRD4117A Rev A00)", "type": "Board", "opn": "BRD4117"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.efr32mg26b510f3200il136"], "imageURL": "", "name": "EFR32xG26 2.4 GHz 10 dBm BGA136 Radio Board", "description": "", "id": "brd4118a:0.0.0.A02", "label": "EFR32xG26 2.4 GHz 10 dBm BGA136 Radio Board (BRD4118A Rev A02)", "type": "Board", "opn": "BRD4118"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg1.efr32mg1p233f256gm48"], "imageURL": "", "name": "EFR32MG 2400/868 MHz 13 dBm Dual Band Radio Board", "description": "", "id": "brd4150a:0.0.0", "label": "EFR32MG 2400/868 MHz 13 dBm Dual Band Radio Board (BRD4150A)", "type": "Board", "opn": "BRD4150"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg1.efr32mg1p233f256gm48"], "imageURL": "", "name": "EFR32MG 2400/915 MHz 19.5 dBm Dual Band Radio Board", "description": "", "id": "brd4150b:0.0.0", "label": "EFR32MG 2400/915 MHz 19.5 dBm Dual Band Radio Board (BRD4150B)", "type": "Board", "opn": "BRD4150"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg1.efr32mg1p133f256gm48"], "imageURL": "", "name": "EFR32MG 2.4 GHz 13 dBm / 868-915 MHz 14 dBm Dual Band Radio Board", "description": "", "id": "brd4150c:0.0.0", "label": "EFR32MG 2.4 GHz 13 dBm / 868-915 MHz 14 dBm Dual Band Radio Board (BRD4150C)", "type": "Board", "opn": "BRD4150"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg1.efr32mg1p232f256gm48"], "imageURL": "", "name": "EFR32MG 2.4 GHz 19.5 dBm Radio Board", "description": "", "id": "brd4151a:0.0.0", "label": "EFR32MG 2.4 GHz 19.5 dBm Radio Board (BRD4151A)", "type": "Board", "opn": "BRD4151"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg1.efr32mg1p233f256gm48"], "imageURL": "", "name": "EFR32MG 2400/434 MHz 10 dBm Dual Band Radio Board", "description": "", "id": "brd4152a:0.0.0", "label": "EFR32MG 2400/434 MHz 10 dBm Dual Band Radio Board (BRD4152A)", "type": "Board", "opn": "BRD4152"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg1.efr32mg1p233f256gm48"], "imageURL": "", "name": "EFR32MG 2400/434 MHz 16 dBm Dual Band Radio Board", "description": "", "id": "brd4152b:0.0.0", "label": "EFR32MG 2400/434 MHz 16 dBm Dual Band Radio Board (BRD4152B)", "type": "Board", "opn": "BRD4152"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg1.efr32mg1p132f256gm48"], "imageURL": "", "name": "EFR32MG 2.4 GHz 13 dBm Radio Board", "description": "", "id": "brd4153a:0.0.0", "label": "EFR32MG 2.4 GHz 13 dBm Radio Board (BRD4153A)", "type": "Board", "opn": "BRD4153"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg1.efr32mg1p732f256gm32"], "imageURL": "", "name": "EFR32MG1P732 2.4 GHz 19.5 dBm Radio Board", "description": "", "id": "brd4154a:0.0.0", "label": "EFR32MG1P732 2.4 GHz 19.5 dBm Radio Board (BRD4154A)", "type": "Board", "opn": "BRD4154"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg1.efr32mg1p132f256gm48"], "imageURL": "", "name": "EFR32MG 2.4 GHz 13 dBm / Si4468 868-915 MHz 14 dBm Radio Board", "description": "", "id": "brd4155a:0.0.0", "label": "EFR32MG 2.4 GHz 13 dBm / Si4468 868-915 MHz 14 dBm Radio Board (BRD4155A)", "type": "Board", "opn": "BRD4155"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg2.efr32mg2p233f1024gm48"], "imageURL": "", "name": "EFR32MG2P233 2400/915 19.5 dBm Dual Band Radio Board", "description": "", "id": "brd4156a:0.0.0", "label": "EFR32MG2P233 2400/915 19.5 dBm Dual Band Radio Board (BRD4156A)", "type": "Board", "opn": "BRD4156"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg2.efr32mg2p233f1024gl125"], "imageURL": "", "name": "EFR32MG2 2400/915 19.5 dBm Dual Band Radio Board", "description": "", "id": "brd4157a:0.0.0.A01", "label": "EFR32MG2 2400/915 19.5 dBm Dual Band Radio Board (BRD4157A Rev A01)", "type": "Board", "opn": "BRD4157"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg2.efr32mg2p233f1024gl125"], "imageURL": "", "name": "EFR32MG2 2400/868 13 dBm Dual Band Radio Board", "description": "", "id": "brd4157b:0.0.0.A03", "label": "EFR32MG2 2400/868 13 dBm Dual Band Radio Board (BRD4157B Rev A03)", "type": "Board", "opn": "BRD4157"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.efr32mg13p733f512gm48"], "imageURL": "", "name": "EFR32MG13 2400/915 MHz 19 dBm Radio Board", "description": "", "id": "brd4158a:0.0.0", "label": "EFR32MG13 2400/915 MHz 19 dBm Radio Board (BRD4158A)", "type": "Board", "opn": "BRD4158"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.efr32mg13p632f512gm48"], "imageURL": "", "name": "EFR32MG13 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4159a:0.0.0.A00", "label": "EFR32MG13 2.4 GHz 10 dBm Radio Board (BRD4159A Rev A00)", "type": "Board", "opn": "BRD4159"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.efr32mg13p632f512gm48"], "imageURL": "", "name": "EFR32MG13 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4159a:0.0.0.A01", "label": "EFR32MG13 2.4 GHz 10 dBm Radio Board (BRD4159A Rev A01)", "type": "Board", "opn": "BRD4159"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p432f1024gl125"], "imageURL": "", "name": "EFR32MG12 2.4 GHz 19 dBm Radio Board", "description": "", "id": "brd4161a:0.0.0.A00", "label": "EFR32MG12 2.4 GHz 19 dBm Radio Board (BRD4161A Rev A00)", "type": "Board", "opn": "BRD4161"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p432f1024gl125"], "imageURL": "", "name": "EFR32MG12 2.4 GHz 19 dBm Radio Board", "description": "", "id": "brd4161a:0.0.0.A01", "label": "EFR32MG12 2.4 GHz 19 dBm Radio Board (BRD4161A Rev A01)", "type": "Board", "opn": "BRD4161"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p432f1024gl125"], "imageURL": "", "name": "EFR32MG12 2.4 GHz 19 dBm Radio Board", "description": "", "id": "brd4161a:0.0.0.A02", "label": "EFR32MG12 2.4 GHz 19 dBm Radio Board (BRD4161A Rev A02)", "type": "Board", "opn": "BRD4161"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p332f1024gl125"], "imageURL": "", "name": "EFR32MG12 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4162a:0.0.0.A00", "label": "EFR32MG12 2.4 GHz 10 dBm Radio Board (BRD4162A Rev A00)", "type": "Board", "opn": "BRD4162"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p332f1024gl125"], "imageURL": "", "name": "EFR32MG12 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4162a:0.0.0.A01", "label": "EFR32MG12 2.4 GHz 10 dBm Radio Board (BRD4162A Rev A01)", "type": "Board", "opn": "BRD4162"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p332f1024gl125"], "imageURL": "", "name": "EFR32MG12 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4162a:0.0.0.A02", "label": "EFR32MG12 2.4 GHz 10 dBm Radio Board (BRD4162A Rev A02)", "type": "Board", "opn": "BRD4162"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p433f1024gl125"], "imageURL": "", "name": "EFR32MG12 2400/868 MHz 10 dBm Dual Band Radio Board", "description": "", "id": "brd4163a:0.0.0.A00", "label": "EFR32MG12 2400/868 MHz 10 dBm Dual Band Radio Board (BRD4163A Rev A00)", "type": "Board", "opn": "BRD4163"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p433f1024gl125"], "imageURL": "", "name": "EFR32MG12 2400/868 MHz 10 dBm Dual Band Radio Board", "description": "", "id": "brd4163a:0.0.0.A01", "label": "EFR32MG12 2400/868 MHz 10 dBm Dual Band Radio Board (BRD4163A Rev A01)", "type": "Board", "opn": "BRD4163"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p433f1024gl125"], "imageURL": "", "name": "EFR32MG12 2400/868 MHz 10 dBm Dual Band Radio Board", "description": "", "id": "brd4163a:0.0.0.A02", "label": "EFR32MG12 2400/868 MHz 10 dBm Dual Band Radio Board (BRD4163A Rev A02)", "type": "Board", "opn": "BRD4163"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p433f1024gl125"], "imageURL": "", "name": "EFR32MG12 2400/915 MHz 19 dBm Dual Band Radio Board", "description": "", "id": "brd4164a:0.0.0.A01", "label": "EFR32MG12 2400/915 MHz 19 dBm Dual Band Radio Board (BRD4164A Rev A01)", "type": "Board", "opn": "BRD4164"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p433f1024gl125"], "imageURL": "", "name": "EFR32MG12 2400/915 MHz 19 dBm Dual Band Radio Board", "description": "", "id": "brd4164a:0.0.0.A02", "label": "EFR32MG12 2400/915 MHz 19 dBm Dual Band Radio Board (BRD4164A Rev A02)", "type": "Board", "opn": "BRD4164"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.efr32mg13p932f512gm48"], "imageURL": "", "name": "EFR32MG13P932 2.4 GHz 19 dBm Radio Board", "description": "", "id": "brd4165a:0.0.0", "label": "EFR32MG13P932 2.4 GHz 19 dBm Radio Board (BRD4165A)", "type": "Board", "opn": "BRD4165"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.efr32mg13p832f512im48"], "imageURL": "", "name": "EFR32MG13P832 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4165b:0.0.0", "label": "EFR32MG13P832 2.4 GHz 10 dBm Radio Board (BRD4165B)", "type": "Board", "opn": "BRD4165"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p332f1024gl125"], "imageURL": "", "name": "Thunderboard Sense 2", "description": "", "id": "brd4166a:0.0.0", "label": "Thunderboard Sense 2 (BRD4166A)", "type": "Board", "opn": "BRD4166"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p332f1024gl125"], "imageURL": "", "name": "Thunderboard Sense 2 for Matter wo/Sensors", "description": "", "id": "brd4166c:0.0.0.A01", "label": "Thunderboard Sense 2 for Matter wo/Sensors (BRD4166C Rev A01)", "type": "Board", "opn": "BRD4166"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.efr32mg13p733f512gm48"], "imageURL": "", "name": "EFR32MG13 2400/868 MHz 10 dBm Dual Band Radio Board", "description": "", "id": "brd4167a:0.0.0.A00", "label": "EFR32MG13 2400/868 MHz 10 dBm Dual Band Radio Board (BRD4167A Rev A00)", "type": "Board", "opn": "BRD4167"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.efr32mg13p732f512gm48"], "imageURL": "", "name": "EFR32MG13 2.4 GHz 19 dBm Radio Board", "description": "", "id": "brd4168a:0.0.0.A00", "label": "EFR32MG13 2.4 GHz 19 dBm Radio Board (BRD4168A Rev A00)", "type": "Board", "opn": "BRD4168"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg14.efr32mg14p733f256gm48"], "imageURL": "", "name": "EFR32MG14 2400/868 MHz 13 dBm Radio Board", "description": "", "id": "brd4169a:0.0.0", "label": "EFR32MG14 2400/868 MHz 13 dBm Radio Board (BRD4169A)", "type": "Board", "opn": "BRD4169"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg14.efr32mg14p733f256gm48"], "imageURL": "", "name": "EFR32MG14 2400/915 MHz 19 dBm Radio Board", "description": "", "id": "brd4169b:0.0.0", "label": "EFR32MG14 2400/915 MHz 19 dBm Radio Board (BRD4169B)", "type": "Board", "opn": "BRD4169"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p433f1024gm68"], "imageURL": "", "name": "EFR32MG12 2400/868-915 MHz 19 dBm Dual Band Radio Board", "description": "", "id": "brd4170a:0.0.0.A00", "label": "EFR32MG12 2400/868-915 MHz 19 dBm Dual Band Radio Board (BRD4170A Rev A00)", "type": "Board", "opn": "BRD4170"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.rm21z000f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4171a:0.0.0.A00", "label": "EFR32MG21 2.4 GHz 10 dBm Radio Board (BRD4171A Rev A00)", "type": "Board", "opn": "BRD4171"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.rm21z000f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4171a:0.0.0.A01", "label": "EFR32MG21 2.4 GHz 10 dBm Radio Board (BRD4171A Rev A01)", "type": "Board", "opn": "BRD4171"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.rm21z000f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4171a:0.0.0.A02", "label": "EFR32MG21 2.4 GHz 10 dBm Radio Board (BRD4171A Rev A02)", "type": "Board", "opn": "BRD4171"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.rm21z000f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4171a:0.0.0.A03", "label": "EFR32MG21 2.4 GHz 10 dBm Radio Board (BRD4171A Rev A03)", "type": "Board", "opn": "BRD4171"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.rm21z000f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4171a:0.0.0.A04", "label": "EFR32MG21 2.4 GHz 10 dBm Radio Board (BRD4171A Rev A04)", "type": "Board", "opn": "BRD4171"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.rm21z000f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4171a:0.0.0.A05", "label": "EFR32MG21 2.4 GHz 10 dBm Radio Board (BRD4171A Rev A05)", "type": "Board", "opn": "BRD4171"}, {"rev": "A06", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.rm21z000f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4171a:0.0.0.A06", "label": "EFR32MG21 2.4 GHz 10 dBm Radio Board (BRD4171A Rev A06)", "type": "Board", "opn": "BRD4171"}, {"rev": "A07", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.rm21z000f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4171a:0.0.0.A07", "label": "EFR32MG21 2.4 GHz 10 dBm Radio Board (BRD4171A Rev A07)", "type": "Board", "opn": "BRD4171"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p433f1024gm48"], "imageURL": "", "name": "EFR32MG12 2400/490 MHz 19 dBm Radio Board", "description": "", "id": "brd4172a:0.0.0.A00", "label": "EFR32MG12 2400/490 MHz 19 dBm Radio Board (BRD4172A Rev A00)", "type": "Board", "opn": "BRD4172"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p433f1024gm48"], "imageURL": "", "name": "EFR32MG12 2400/169 MHz 19 dBm Radio Board", "description": "", "id": "brd4172b:0.0.0.A00", "label": "EFR32MG12 2400/169 MHz 19 dBm Radio Board (BRD4172B Rev A00)", "type": "Board", "opn": "BRD4172"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p433f1024gm48"], "imageURL": "", "name": "EFR32MG12 2400/434 MHz 10 dBm Radio Board", "description": "", "id": "brd4173a:0.0.0.A00", "label": "EFR32MG12 2400/434 MHz 10 dBm Radio Board (BRD4173A Rev A00)", "type": "Board", "opn": "BRD4173"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.efr32mg13p733f512gm48"], "imageURL": "", "name": "EFR32MG13 2400/490 MHz 19 dBm Radio Board", "description": "", "id": "brd4174a:0.0.0.A00", "label": "EFR32MG13 2400/490 MHz 19 dBm Radio Board (BRD4174A Rev A00)", "type": "Board", "opn": "BRD4174"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.efr32mg13p733f512gm48"], "imageURL": "", "name": "EFR32MG13 2400/169 MHz 19 dBm Radio Board", "description": "", "id": "brd4174b:0.0.0.A00", "label": "EFR32MG13 2400/169 MHz 19 dBm Radio Board (BRD4174B Rev A00)", "type": "Board", "opn": "BRD4174"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.efr32mg13p733f512gm48"], "imageURL": "", "name": "EFR32MG13 2400/434 MHz 10 dBm Radio Board", "description": "", "id": "brd4175a:0.0.0.A00", "label": "EFR32MG13 2400/434 MHz 10 dBm Radio Board (BRD4175A Rev A00)", "type": "Board", "opn": "BRD4175"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a010f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4176a:0.0.0", "label": "EFR32MG21 2.4 GHz 10 dBm Radio Board (BRD4176A)", "type": "Board", "opn": "BRD4176"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.efr32mg13p732f512gm48"], "imageURL": "", "name": "EFR32MG13 2.4 GHz Antenna Diversity Radio Board", "description": "", "id": "brd4177a:0.0.0", "label": "EFR32MG13 2.4 GHz Antenna Diversity Radio Board (BRD4177A)", "type": "Board", "opn": "BRD4177"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a010f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board", "description": "", "id": "brd4179a:0.0.0", "label": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board (BRD4179A)", "type": "Board", "opn": "BRD4179"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["efp.efp01.efp0104_g", "mcu.arm.efr32.mg21.efr32mg21a010f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board", "description": "", "id": "brd4179b:0.0.0", "label": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board (BRD4179B)", "type": "Board", "opn": "BRD4179"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a010f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board", "description": "", "id": "brd4179b:0.0.0.A00", "label": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board (BRD4179B Rev A00)", "type": "Board", "opn": "BRD4179"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a010f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board", "description": "", "id": "brd4179b:0.0.0.A01", "label": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board (BRD4179B Rev A01)", "type": "Board", "opn": "BRD4179"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a010f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board", "description": "", "id": "brd4179b:0.0.0.A02", "label": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board (BRD4179B Rev A02)", "type": "Board", "opn": "BRD4179"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a010f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board", "description": "", "id": "brd4179b:0.0.0.A03", "label": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board (BRD4179B Rev A03)", "type": "Board", "opn": "BRD4179"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a010f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board", "description": "", "id": "brd4179b:0.0.0.A04", "label": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board (BRD4179B Rev A04)", "type": "Board", "opn": "BRD4179"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a010f1024im32"], "imageURL": "", "name": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board", "description": "", "id": "brd4179b:0.0.0.A05", "label": "EFR32MG21 2.4 GHz 10 dBm with EFP Radio Board (BRD4179B Rev A05)", "type": "Board", "opn": "BRD4179"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a020f1024im32"], "imageURL": "", "name": "EFR32xG21 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4180a:0.0.0", "label": "EFR32xG21 2.4 GHz 20 dBm Radio Board (BRD4180A)", "type": "Board", "opn": "BRD4180"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a020f1024im32"], "imageURL": "", "name": "EFR32xG21 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4180a:0.0.0.A00", "label": "EFR32xG21 2.4 GHz 20 dBm Radio Board (BRD4180A Rev A00)", "type": "Board", "opn": "BRD4180"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a020f1024im32"], "imageURL": "", "name": "EFR32xG21 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4180a:0.0.0.A01", "label": "EFR32xG21 2.4 GHz 20 dBm Radio Board (BRD4180A Rev A01)", "type": "Board", "opn": "BRD4180"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a020f1024im32"], "imageURL": "", "name": "EFR32xG21 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4180a:0.0.0.A02", "label": "EFR32xG21 2.4 GHz 20 dBm Radio Board (BRD4180A Rev A02)", "type": "Board", "opn": "BRD4180"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a020f1024im32"], "imageURL": "", "name": "EFR32xG21 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4180b:0.0.0", "label": "EFR32xG21 2.4 GHz 20 dBm Radio Board (BRD4180B)", "type": "Board", "opn": "BRD4180"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a020f1024im32"], "imageURL": "", "name": "EFR32xG21 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4180b:0.0.0.A00", "label": "EFR32xG21 2.4 GHz 20 dBm Radio Board (BRD4180B Rev A00)", "type": "Board", "opn": "BRD4180"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a020f1024im32"], "imageURL": "", "name": "EFR32xG21 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4180b:0.0.0.A01", "label": "EFR32xG21 2.4 GHz 20 dBm Radio Board (BRD4180B Rev A01)", "type": "Board", "opn": "BRD4180"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a020f1024im32"], "imageURL": "", "name": "EFR32xG21 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4180b:0.0.0.A02", "label": "EFR32xG21 2.4 GHz 20 dBm Radio Board (BRD4180B Rev A02)", "type": "Board", "opn": "BRD4180"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a020f1024im32"], "imageURL": "", "name": "EFR32xG21 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4180b:0.0.0.A03", "label": "EFR32xG21 2.4 GHz 20 dBm Radio Board (BRD4180B Rev A03)", "type": "Board", "opn": "BRD4180"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a010f1024im32"], "imageURL": "", "name": "EFR32xG21 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4181a:0.0.0", "label": "EFR32xG21 2.4 GHz 10 dBm Radio Board (BRD4181A)", "type": "Board", "opn": "BRD4181"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21a010f1024im32"], "imageURL": "", "name": "EFR32xG21 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4181b:0.0.0", "label": "EFR32xG21 2.4 GHz 10 dBm Radio Board (BRD4181B)", "type": "Board", "opn": "BRD4181"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b010f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4181c:0.0.0", "label": "EFR32xG21B 2.4 GHz 10 dBm Radio Board (BRD4181C)", "type": "Board", "opn": "BRD4181"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg22.efr32mg22c224f512im40"], "imageURL": "", "name": "EFR32xG22 2.4 GHz 6 dBm Radio Board", "description": "", "id": "brd4182a:0.0.0", "label": "EFR32xG22 2.4 GHz 6 dBm Radio Board (BRD4182A)", "type": "Board", "opn": "BRD4182"}, {"rev": "B02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg22.efr32mg22a224f512im40"], "imageURL": "", "name": "EFR32xG22 2.4 GHz 6 dBm Radio Board", "description": "", "id": "brd4182a:0.0.0.B02", "label": "EFR32xG22 2.4 GHz 6 dBm Radio Board (BRD4182A Rev B02)", "type": "Board", "opn": "BRD4182"}, {"rev": "B03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg22.efr32mg22c224f512im40"], "imageURL": "", "name": "EFR32xG22 2.4 GHz 6 dBm Radio Board", "description": "", "id": "brd4182a:0.0.0.B03", "label": "EFR32xG22 2.4 GHz 6 dBm Radio Board (BRD4182A Rev B03)", "type": "Board", "opn": "BRD4182"}, {"rev": "B04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg22.efr32mg22c224f512im40"], "imageURL": "", "name": "EFR32xG22 2.4 GHz 6 dBm Radio Board", "description": "", "id": "brd4182a:0.0.0.B04", "label": "EFR32xG22 2.4 GHz 6 dBm Radio Board (BRD4182A Rev B04)", "type": "Board", "opn": "BRD4182"}, {"rev": "B05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg22.efr32mg22c224f512im40"], "imageURL": "", "name": "EFR32xG22 2.4 GHz 6 dBm Radio Board", "description": "", "id": "brd4182a:0.0.0.B05", "label": "EFR32xG22 2.4 GHz 6 dBm Radio Board (BRD4182A Rev B05)", "type": "Board", "opn": "BRD4182"}, {"rev": "B06", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg22.efr32mg22c224f512im40"], "imageURL": "", "name": "EFR32xG22 2.4 GHz 6 dBm Radio Board", "description": "", "id": "brd4182a:0.0.0.B06", "label": "EFR32xG22 2.4 GHz 6 dBm Radio Board (BRD4182A Rev B06)", "type": "Board", "opn": "BRD4182"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg22.efr32mg22c224f512im32"], "imageURL": "", "name": "EFR32xG22 2.4 GHz 6 dBm QFN32 Radio Board", "description": "", "id": "brd4183a:0.0.0", "label": "EFR32xG22 2.4 GHz 6 dBm QFN32 Radio Board (BRD4183A)", "type": "Board", "opn": "BRD4183"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c112f352gm32"], "imageURL": "", "name": "EFR32BG22C112 QFN32 Radio Board", "description": "", "id": "brd4183b:0.0.0.A00", "label": "EFR32BG22C112 QFN32 Radio Board (BRD4183B Rev A00)", "type": "Board", "opn": "BRD4183"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg22.efr32mg22c224f512im32"], "imageURL": "", "name": "EFR32MG22 2.4 GHz 6 dBm QFN32 Radio Board", "description": "", "id": "brd4183c:0.0.0", "label": "EFR32MG22 2.4 GHz 6 dBm QFN32 Radio Board (BRD4183C)", "type": "Board", "opn": "BRD4183"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "Thunderboard EFR32BG22", "description": "", "id": "brd4184a:0.0.0", "label": "Thunderboard EFR32BG22 (BRD4184A)", "type": "Board", "opn": "BRD4184"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "Thunderboard EFR32BG22", "description": "", "id": "brd4184a:0.0.0.A01", "label": "Thunderboard EFR32BG22 (BRD4184A Rev A01)", "type": "Board", "opn": "BRD4184"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "Thunderboard EFR32BG22", "description": "", "id": "brd4184b:0.0.0", "label": "Thunderboard EFR32BG22 (BRD4184B)", "type": "Board", "opn": "BRD4184"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "Thunderboard EFR32BG22", "description": "", "id": "brd4184b:0.0.0.A02", "label": "Thunderboard EFR32BG22 (BRD4184B Rev A02)", "type": "Board", "opn": "BRD4184"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "EFR32BG22 Direction Finding Radio Board", "description": "", "id": "brd4185a:0.0.0", "label": "EFR32BG22 Direction Finding Radio Board (BRD4185A)", "type": "Board", "opn": "BRD4185"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24a010f1536gm48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4186a:0.0.0.A03", "label": "EFR32xG24 2.4 GHz 10 dBm Radio Board (BRD4186A Rev A03)", "type": "Board", "opn": "BRD4186"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b010f1536im48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4186b:0.0.0.A00", "label": "EFR32xG24 2.4 GHz 10 dBm Radio Board (BRD4186B Rev A00)", "type": "Board", "opn": "BRD4186"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b210f1536im48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4186c:0.0.0.A00", "label": "EFR32xG24 2.4 GHz 10 dBm Radio Board (BRD4186C Rev A00)", "type": "Board", "opn": "BRD4186"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b210f1536im48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4186c:0.0.0.A01", "label": "EFR32xG24 2.4 GHz 10 dBm Radio Board (BRD4186C Rev A01)", "type": "Board", "opn": "BRD4186"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24a020f1536gm48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4187a:0.0.0.A01", "label": "EFR32xG24 2.4 GHz 20 dBm Radio Board (BRD4187A Rev A01)", "type": "Board", "opn": "BRD4187"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b020f1536im48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4187b:0.0.0.A00", "label": "EFR32xG24 2.4 GHz 20 dBm Radio Board (BRD4187B Rev A00)", "type": "Board", "opn": "BRD4187"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b220f1536im48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4187c:0.0.0.A00", "label": "EFR32xG24 2.4 GHz 20 dBm Radio Board (BRD4187C Rev A00)", "type": "Board", "opn": "BRD4187"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b220f1536im48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4187c:0.0.0.A01", "label": "EFR32xG24 2.4 GHz 20 dBm Radio Board (BRD4187C Rev A01)", "type": "Board", "opn": "BRD4187"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24a020f1536gm48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz 20 dBm Antenna Diversity Radio Board", "description": "", "id": "brd4188a:0.0.0.A00", "label": "EFR32xG24 2.4 GHz 20 dBm Antenna Diversity Radio Board (BRD4188A Rev A00)", "type": "Board", "opn": "BRD4188"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b020f1536im48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz 20 dBm Antenna Diversity Radio Board", "description": "", "id": "brd4188a:0.0.0.A01", "label": "EFR32xG24 2.4 GHz 20 dBm Antenna Diversity Radio Board (BRD4188A Rev A01)", "type": "Board", "opn": "BRD4188"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b220f1536im48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz 20 dBm Antenna Diversity Radio Board", "description": "", "id": "brd4188b:0.0.0.A00", "label": "EFR32xG24 2.4 GHz 20 dBm Antenna Diversity Radio Board (BRD4188B Rev A00)", "type": "Board", "opn": "BRD4188"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b220f1536im48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz 20 dBm Antenna Diversity Radio Board", "description": "", "id": "brd4188b:0.0.0.A02", "label": "EFR32xG24 2.4 GHz 20 dBm Antenna Diversity Radio Board (BRD4188B Rev A02)", "type": "Board", "opn": "BRD4188"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p232f1024gm48"], "imageURL": "", "name": "EFR32MG12 2.4 GHz 13 dBm/Si4468 868-915 MHz 14 dBm Radio Board", "description": "", "id": "brd4190a:0.0.0", "label": "EFR32MG12 2.4 GHz 13 dBm/Si4468 868-915 MHz 14 dBm Radio Board (BRD4190A)", "type": "Board", "opn": "BRD4190"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "BG22 Dual Polarized Antenna Array Radio Board", "description": "", "id": "brd4191a:0.0.0.A00", "label": "BG22 Dual Polarized Antenna Array Radio Board (BRD4191A Rev A00)", "type": "Board", "opn": "BRD4191"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "BG22 Dual Polarized Antenna Array Radio Board", "description": "", "id": "brd4191a:0.0.0.A01", "label": "BG22 Dual Polarized Antenna Array Radio Board (BRD4191A Rev A01)", "type": "Board", "opn": "BRD4191"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "BG22 Dual Polarized Antenna Array Radio Board", "description": "", "id": "brd4191a:0.0.0.A02", "label": "BG22 Dual Polarized Antenna Array Radio Board (BRD4191A Rev A02)", "type": "Board", "opn": "BRD4191"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "BG22 Dual Polarized Antenna Array Radio Board", "description": "", "id": "brd4191a:0.0.0.A03", "label": "BG22 Dual Polarized Antenna Array Radio Board (BRD4191A Rev A03)", "type": "Board", "opn": "BRD4191"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg27.efr32mg27c140f768im40"], "imageURL": "", "name": "EFR32xG27 2.4 GHz 8 dBm Radio Board", "description": "", "id": "brd4194a:0.0.0.A03", "label": "EFR32xG27 2.4 GHz 8 dBm Radio Board (BRD4194A Rev A03)", "type": "Board", "opn": "BRD4194"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg27.efr32mg27c140f768im40"], "imageURL": "", "name": "EFR32xG27 2.4 GHz 8 dBm Radio Board", "description": "", "id": "brd4194a:0.0.0.A04", "label": "EFR32xG27 2.4 GHz 8 dBm Radio Board (BRD4194A Rev A04)", "type": "Board", "opn": "BRD4194"}, {"rev": "A06", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg27.efr32mg27c140f768im40"], "imageURL": "", "name": "EFR32xG27 2.4 GHz 8 dBm Radio Board", "description": "", "id": "brd4194a:0.0.0.A06", "label": "EFR32xG27 2.4 GHz 8 dBm Radio Board (BRD4194A Rev A06)", "type": "Board", "opn": "BRD4194"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b010f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4195a:0.0.0.A03", "label": "EFR32xG21B 2.4 GHz 10 dBm Radio Board (BRD4195A Rev A03)", "type": "Board", "opn": "BRD4195"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b010f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4195a:0.0.0.A04", "label": "EFR32xG21B 2.4 GHz 10 dBm Radio Board (BRD4195A Rev A04)", "type": "Board", "opn": "BRD4195"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b010f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4195a:0.0.0.A05", "label": "EFR32xG21B 2.4 GHz 10 dBm Radio Board (BRD4195A Rev A05)", "type": "Board", "opn": "BRD4195"}, {"rev": "A06", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b010f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4195a:0.0.0.A06", "label": "EFR32xG21B 2.4 GHz 10 dBm Radio Board (BRD4195A Rev A06)", "type": "Board", "opn": "BRD4195"}, {"rev": "A07", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b010f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4195a:0.0.0.A07", "label": "EFR32xG21B 2.4 GHz 10 dBm Radio Board (BRD4195A Rev A07)", "type": "Board", "opn": "BRD4195"}, {"rev": "A08", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b010f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4195a:0.0.0.A08", "label": "EFR32xG21B 2.4 GHz 10 dBm Radio Board (BRD4195A Rev A08)", "type": "Board", "opn": "BRD4195"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b010f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 10 dBm Radio Board", "description": "", "id": "brd4195b:0.0.0.A00", "label": "EFR32xG21B 2.4 GHz 10 dBm Radio Board (BRD4195B Rev A00)", "type": "Board", "opn": "BRD4195"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b020f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4196a:0.0.0.A03", "label": "EFR32xG21B 2.4 GHz 20 dBm Radio Board (BRD4196A Rev A03)", "type": "Board", "opn": "BRD4196"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b020f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4196a:0.0.0.A04", "label": "EFR32xG21B 2.4 GHz 20 dBm Radio Board (BRD4196A Rev A04)", "type": "Board", "opn": "BRD4196"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b020f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4196a:0.0.0.A05", "label": "EFR32xG21B 2.4 GHz 20 dBm Radio Board (BRD4196A Rev A05)", "type": "Board", "opn": "BRD4196"}, {"rev": "A06", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b020f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4196a:0.0.0.A06", "label": "EFR32xG21B 2.4 GHz 20 dBm Radio Board (BRD4196A Rev A06)", "type": "Board", "opn": "BRD4196"}, {"rev": "A07", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b020f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4196a:0.0.0.A07", "label": "EFR32xG21B 2.4 GHz 20 dBm Radio Board (BRD4196A Rev A07)", "type": "Board", "opn": "BRD4196"}, {"rev": "A08", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b020f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4196a:0.0.0.A08", "label": "EFR32xG21B 2.4 GHz 20 dBm Radio Board (BRD4196A Rev A08)", "type": "Board", "opn": "BRD4196"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.efr32mg21b020f1024im32"], "imageURL": "", "name": "EFR32xG21B 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4196b:0.0.0.A00", "label": "EFR32xG21B 2.4 GHz 20 dBm Radio Board (BRD4196B Rev A00)", "type": "Board", "opn": "BRD4196"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b220f1536im48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz x dBm /Si4468 868-915 MHz 14 dBm Radio Board", "description": "", "id": "brd4197a:0.0.0.A01", "label": "EFR32xG24 2.4 GHz x dBm /Si4468 868-915 MHz 14 dBm Radio Board (BRD4197A Rev A01)", "type": "Board", "opn": "BRD4197"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b220f1536im48"], "imageURL": "", "name": "EFR32xG24 2.4 GHz 20 dBm /Si4468 868-915 MHz 14 dBm Radio Board", "description": "", "id": "brd4197b:0.0.0.A00", "label": "EFR32xG24 2.4 GHz 20 dBm /Si4468 868-915 MHz 14 dBm Radio Board (BRD4197B Rev A00)", "type": "Board", "opn": "BRD4197"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b210f1536im48"], "imageURL": "", "name": "BG24 Ranging Radio Board", "description": "", "id": "brd4198a:0.0.0.A00", "label": "BG24 Ranging Radio Board (BRD4198A Rev A00)", "type": "Board", "opn": "BRD4198"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b210f1536im48"], "imageURL": "", "name": "BG24 Ranging Radio Board", "description": "", "id": "brd4198a:0.0.0.A01", "label": "BG24 Ranging Radio Board (BRD4198A Rev A01)", "type": "Board", "opn": "BRD4198"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b210f1536im48"], "imageURL": "", "name": "BG24 Ranging Radio Board", "description": "", "id": "brd4198b:0.0.0.A00", "label": "BG24 Ranging Radio Board (BRD4198B Rev A00)", "type": "Board", "opn": "BRD4198"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b210f1536im48"], "imageURL": "", "name": "EFR32xG24 Direction Finding Radio Board", "description": "", "id": "brd4199a:0.0.0.A01", "label": "EFR32xG24 Direction Finding Radio Board (BRD4199A Rev A01)", "type": "Board", "opn": "BRD4199"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg13.zgm130s037hgn"], "imageURL": "", "name": "ZGM130S Radio Board", "description": "", "id": "brd4200a:0.0.0.A03", "label": "ZGM130S Radio Board (BRD4200A Rev A03)", "type": "Board", "opn": "BRD4200"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg13.zgm130s037hgn"], "imageURL": "", "name": "ZGM130S Radio Board", "description": "", "id": "brd4200a:0.0.0.A04", "label": "ZGM130S Radio Board (BRD4200A Rev A04)", "type": "Board", "opn": "BRD4200"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg14.efr32zg14p231f256gm32"], "imageURL": "", "name": "EFR32ZG14 Radio Board", "description": "", "id": "brd4201a:0.0.0", "label": "EFR32ZG14 Radio Board (BRD4201A)", "type": "Board", "opn": "BRD4201"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg13.efr32zg13p531f512gm32"], "imageURL": "", "name": "EFR32RZ13 QFN32 Radio Board", "description": "", "id": "brd4201b:0.0.0", "label": "EFR32RZ13 QFN32 Radio Board (SLWRB4201B)", "type": "Board", "opn": "BRD4201"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg13.efr32zg13l231f512gm32"], "imageURL": "", "name": "EFR32ZG13L Radio Board", "description": "", "id": "brd4201c:0.0.0", "label": "EFR32ZG13L Radio Board (BRD4201C)", "type": "Board", "opn": "BRD4201"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg13.efr32zg13s231f512gm32"], "imageURL": "", "name": "EFR32ZG13S Radio Board", "description": "", "id": "brd4201d:0.0.0", "label": "EFR32ZG13S Radio Board (BRD4201D)", "type": "Board", "opn": "BRD4201"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg13.zgm130s037hgn"], "imageURL": "", "name": "ZGM130S Radio Board", "description": "", "id": "brd4202a:0.0.0.A00", "label": "ZGM130S Radio Board (BRD4202A Rev A00)", "type": "Board", "opn": "BRD4202"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg13.zgm130s037hgn"], "imageURL": "", "name": "ZGM130S Radio Board", "description": "", "id": "brd4202a:0.0.0.A01", "label": "ZGM130S Radio Board (BRD4202A Rev A01)", "type": "Board", "opn": "BRD4202"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg13.efr32zg13p531f512gm48"], "imageURL": "", "name": "EFR32RZ13 QFN48 Radio Board", "description": "", "id": "brd4203a:0.0.0", "label": "EFR32RZ13 QFN48 Radio Board (SLWRB4203A)", "type": "Board", "opn": "BRD4203"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg23.efr32zg23a010f512gm48"], "imageURL": "", "name": "EFR32ZG23 868/915 MHz 14 dBm Radio Board", "description": "", "id": "brd4204a:0.0.0", "label": "EFR32ZG23 868/915 MHz 14 dBm Radio Board (BRD4204A)", "type": "Board", "opn": "BRD4204"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg23.efr32zg23a010f512gm48"], "imageURL": "", "name": "ZG23 868-915 MHz 14 dBm Radio Board", "description": "", "id": "brd4204b:0.0.0", "label": "ZG23 868-915 MHz 14 dBm Radio Board (BRD4204B)", "type": "Board", "opn": "BRD4204"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg23.efr32zg23b010f512im48"], "imageURL": "", "name": "ZG23 868-915 MHz 14 dBm Radio Board", "description": "", "id": "brd4204c:0.0.0", "label": "ZG23 868-915 MHz 14 dBm Radio Board (BRD4204C)", "type": "Board", "opn": "BRD4204"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg23.efr32zg23b010f512im48"], "imageURL": "", "name": "EFR32ZG23 868-915 MHz 14 dBm Radio Board", "description": "", "id": "brd4204d:0.0.0", "label": "EFR32ZG23 868-915 MHz 14 dBm Radio Board (BRD4204D)", "type": "Board", "opn": "BRD4204"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg23.zgm230sa27hnn"], "imageURL": "", "name": "ZGM230S Radio Board", "description": "", "id": "brd4205a:0.0.0.A00", "label": "ZGM230S Radio Board (BRD4205A Rev A00)", "type": "Board", "opn": "BRD4205"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg23.zgm230sb27hgn"], "imageURL": "", "name": "ZGM230S Radio Board", "description": "", "id": "brd4205b:0.0.0.A00", "label": "ZGM230S Radio Board (SLWRB4205B)", "type": "Board", "opn": "BRD4205"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg14.efr32zg14p231f256gm32"], "imageURL": "", "name": "EFR32ZG14 Z-Wave Long Range", "description": "", "id": "brd4206a:0.0.0", "label": "EFR32ZG14 Z-Wave Long Range (BRD4206A)", "type": "Board", "opn": "BRD4206"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg13.zgm130s037hgn"], "imageURL": "", "name": "ZGM130S Z-Wave Long Range", "description": "", "id": "brd4207a:0.0.0", "label": "ZGM130S Z-Wave Long Range (BRD4207A)", "type": "Board", "opn": "BRD4207"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg14.efr32zg14p731f256gm32"], "imageURL": "", "name": "EFR32ZG14 915 MHz 20 dBm Radio Board", "description": "", "id": "brd4208a:0.0.0", "label": "EFR32ZG14 915 MHz 20 dBm Radio Board (SLWRB4208A)", "type": "Board", "opn": "BRD4208"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg13.efr32zg13p531f512gm48"], "imageURL": "", "name": "EFR32RZ13 915 MHz 20 dBm Radio Board", "description": "", "id": "brd4209a:0.0.0", "label": "EFR32RZ13 915 MHz 20 dBm Radio Board (SLWRB4209A)", "type": "Board", "opn": "BRD4209"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg23.efr32zg23b020f512im48"], "imageURL": "", "name": "EFR32ZG23 868-915 MHz 20 dBm Radio Board", "description": "", "id": "brd4210a:0.0.0", "label": "EFR32ZG23 868-915 MHz 20 dBm Radio Board (BRD4210A)", "type": "Board", "opn": "BRD4210"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg1.efr32fg1p133f256gm48"], "imageURL": "", "name": "EFR32FG 2400/915 MHz 19.5 dBm Dual Band Radio Board", "description": "", "id": "brd4250a:0.0.0", "label": "EFR32FG 2400/915 MHz 19.5 dBm Dual Band Radio Board (BRD4250A)", "type": "Board", "opn": "BRD4250"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg1.efr32fg1p133f256gm48"], "imageURL": "", "name": "EFR32FG 2400/868 MHz 13 dBm Dual Band Radio Board", "description": "", "id": "brd4250b:0.0.0", "label": "EFR32FG 2400/868 MHz 13 dBm Dual Band Radio Board (BRD4250B)", "type": "Board", "opn": "BRD4250"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg1.efr32fg1p133f256gm48"], "imageURL": "", "name": "EFR32FG 2400/490 MHz 19.5 dBm Dual Band Radio Board", "description": "", "id": "brd4251a:0.0.0", "label": "EFR32FG 2400/490 MHz 19.5 dBm Dual Band Radio Board (BRD4251A)", "type": "Board", "opn": "BRD4251"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg1.efr32fg1p133f256gm48"], "imageURL": "", "name": "EFR32FG 2400/434 MHz 10 dBm Dual Band Radio Board", "description": "", "id": "brd4251b:0.0.0", "label": "EFR32FG 2400/434 MHz 10 dBm Dual Band Radio Board (BRD4251B)", "type": "Board", "opn": "BRD4251"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg1.efr32fg1p133f256gm48"], "imageURL": "", "name": "EFR32FG 2400/169 MHz 19.5 dBm Dual Band Radio Board", "description": "", "id": "brd4251d:0.0.0", "label": "EFR32FG 2400/169 MHz 19.5 dBm Dual Band Radio Board (BRD4251D)", "type": "Board", "opn": "BRD4251"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg1.efr32fg1p132f256gm48"], "imageURL": "", "name": "EFR32FG 2.4 GHz 19.5 dBm Radio Board", "description": "", "id": "brd4252a:0.0.0", "label": "EFR32FG 2.4 GHz 19.5 dBm Radio Board (BRD4252A)", "type": "Board", "opn": "BRD4252"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg12.efr32fg12p433f1024gl125"], "imageURL": "", "name": "EFR32FG12 2400/915 MHz 19 dBm Dual Band Radio Board", "description": "", "id": "brd4253a:0.0.0.A01", "label": "EFR32FG12 2400/915 MHz 19 dBm Dual Band Radio Board (BRD4253A Rev A01)", "type": "Board", "opn": "BRD4253"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg12.efr32fg12p433f1024gl125"], "imageURL": "", "name": "EFR32FG12 2400/915 MHz 19 dBm Dual Band Radio Board", "description": "", "id": "brd4253a:0.0.0.A02", "label": "EFR32FG12 2400/915 MHz 19 dBm Dual Band Radio Board (BRD4253A Rev A02)", "type": "Board", "opn": "BRD4253"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg12.efr32fg12p433f1024gl125"], "imageURL": "", "name": "EFR32FG12 2400/868 MHz 10 dBm Dual Band Radio Board", "description": "", "id": "brd4254a:0.0.0.A01", "label": "EFR32FG12 2400/868 MHz 10 dBm Dual Band Radio Board (BRD4254A Rev A01)", "type": "Board", "opn": "BRD4254"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg12.efr32fg12p433f1024gl125"], "imageURL": "", "name": "EFR32FG12 2400/868 MHz 10 dBm Dual Band Radio Board", "description": "", "id": "brd4254a:0.0.0.A02", "label": "EFR32FG12 2400/868 MHz 10 dBm Dual Band Radio Board (BRD4254A Rev A02)", "type": "Board", "opn": "BRD4254"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg13.efr32fg13p233f512gm48"], "imageURL": "", "name": "EFR32FG13 2400/915 MHz 19 dBm Dual Band Radio Board", "description": "", "id": "brd4255a:0.0.0.A00", "label": "EFR32FG13 2400/915 MHz 19 dBm Dual Band Radio Board (BRD4255A Rev A00)", "type": "Board", "opn": "BRD4255"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg13.efr32fg13p233f512gm48"], "imageURL": "", "name": "EFR32FG13 2400/868 MHz 10 dBm Dual Band Radio Board", "description": "", "id": "brd4256a:0.0.0.A00", "label": "EFR32FG13 2400/868 MHz 10 dBm Dual Band Radio Board (BRD4256A Rev A00)", "type": "Board", "opn": "BRD4256"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg14.efr32fg14p233f256gm48"], "imageURL": "", "name": "EFR32FG14 2400/915 MHz 19 dBm Dual Band Radio Board", "description": "", "id": "brd4257a:0.0.0.A01", "label": "EFR32FG14 2400/915 MHz 19 dBm Dual Band Radio Board (BRD4257A Rev A01)", "type": "Board", "opn": "BRD4257"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg14.efr32fg14p233f256gm48"], "imageURL": "", "name": "EFR32FG14 2400/868 MHz 13 dBm Dual Band Radio Board", "description": "", "id": "brd4257b:0.0.0.A01", "label": "EFR32FG14 2400/868 MHz 13 dBm Dual Band Radio Board (BRD4257B Rev A01)", "type": "Board", "opn": "BRD4257"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg14.efr32fg14p233f256gm48"], "imageURL": "", "name": "EFR32FG14 2400/490 MHz 19 dBm Dual Band Radio Board", "description": "", "id": "brd4258a:0.0.0.A01", "label": "EFR32FG14 2400/490 MHz 19 dBm Dual Band Radio Board (BRD4258A Rev A01)", "type": "Board", "opn": "BRD4258"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg14.efr32fg14p233f256gm48"], "imageURL": "", "name": "EFR32FG14 2400/169 MHz 19 dBm Dual Band Radio Board", "description": "", "id": "brd4258b:0.0.0.A01", "label": "EFR32FG14 2400/169 MHz 19 dBm Dual Band Radio Board (BRD4258B Rev A01)", "type": "Board", "opn": "BRD4258"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg14.efr32fg14p233f256gm48"], "imageURL": "", "name": "EFR32FG14 2400/434 MHz 10 dBm Dual Band Radio Board", "description": "", "id": "brd4259a:0.0.0.A01", "label": "EFR32FG14 2400/434 MHz 10 dBm Dual Band Radio Board (BRD4259A Rev A01)", "type": "Board", "opn": "BRD4259"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg14.efr32fg14p233f256gm48"], "imageURL": "", "name": "EFR32FG14 2400/490 MHz 19 dBm Radio Board with TCXO", "description": "", "id": "brd4261a:0.0.0", "label": "EFR32FG14 2400/490 MHz 19 dBm Radio Board with TCXO (BRD4261A)", "type": "Board", "opn": "BRD4261"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg14.efr32fg14p233f256gm48"], "imageURL": "", "name": "EFR32FG14 2400/915 MHz 19 dBm Radio Board with TCXO", "description": "", "id": "brd4262a:0.0.0", "label": "EFR32FG14 2400/915 MHz 19 dBm Radio Board with TCXO (BRD4262A)", "type": "Board", "opn": "BRD4262"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg14.efr32fg14p233f256gm48"], "imageURL": "", "name": "EFR32FG14 2400/868 MHz 13 dBm Radio Board with TCXO", "description": "", "id": "brd4262b:0.0.0", "label": "EFR32FG14 2400/868 MHz 13 dBm Radio Board with TCXO (BRD4262B)", "type": "Board", "opn": "BRD4262"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg23.efr32fg23a010f512gm48"], "imageURL": "", "name": "EFR32FG23 868 MHz 14 dBm Radio Board", "description": "", "id": "brd4263a:0.0.0", "label": "EFR32FG23 868 MHz 14 dBm Radio Board (BRD4263A)", "type": "Board", "opn": "BRD4263"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg23.efr32fg23a010f512gm48"], "imageURL": "", "name": "FG23 868-915 MHz 14 dBm Radio Board", "description": "", "id": "brd4263b:0.0.0", "label": "FG23 868-915 MHz 14 dBm Radio Board (BRD4263B)", "type": "Board", "opn": "BRD4263"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg23.efr32fg23b010f512im48"], "imageURL": "", "name": "FG23 868-915 MHz 14 dBm Radio Board", "description": "", "id": "brd4263c:0.0.0", "label": "FG23 868-915 MHz 14 dBm Radio Board (BRD4263C)", "type": "Board", "opn": "BRD4263"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg23.efr32fg23a020f512gm48"], "imageURL": "", "name": "FG23 915 MHz 20 dBm Radio Board", "description": "", "id": "brd4264a:0.0.0", "label": "FG23 915 MHz 20 dBm Radio Board (BRD4264A)", "type": "Board", "opn": "BRD4264"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg23.efr32fg23a020f512gm48"], "imageURL": "", "name": "FG23 868-915 MHz 20 dBm Radio Board", "description": "", "id": "brd4264b:0.0.0", "label": "FG23 868-915 MHz 20 dBm Radio Board (BRD4264B)", "type": "Board", "opn": "BRD4264"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg23.efr32fg23b020f512im48"], "imageURL": "", "name": "FG23 868-915 MHz 20 dBm Radio Board", "description": "", "id": "brd4264c:0.0.0", "label": "FG23 868-915 MHz 20 dBm Radio Board (BRD4264C)", "type": "Board", "opn": "BRD4264"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg23.efr32fg23a010f512gm48"], "imageURL": "", "name": "FG23 433 MHz 10 dBm Radio Board", "description": "", "id": "brd4265a:0.0.0", "label": "FG23 433 MHz 10 dBm Radio Board (BRD4265A)", "type": "Board", "opn": "BRD4265"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg23.efr32fg23b010f512im48"], "imageURL": "", "name": "FG23 433 MHz 10 dBm Radio Board", "description": "", "id": "brd4265b:0.0.0", "label": "FG23 433 MHz 10 dBm Radio Board (BRD4265B)", "type": "Board", "opn": "BRD4265"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "FG25 902-928 MHz +14 dBm Radio Board", "description": "", "id": "brd4270a:0.0.0", "label": "FG25 902-928 MHz +14 dBm Radio Board (BRD4270A)", "type": "Board", "opn": "BRD4270"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25 902-928 MHz +14 dBm Radio Board", "description": "", "id": "brd4270b:0.0.0.A01", "label": "EFR32FG25 902-928 MHz +14 dBm Radio Board (BRD4270B Rev A01)", "type": "Board", "opn": "BRD4270"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25 902-928 MHz +14 dBm Radio Board", "description": "", "id": "brd4270b:0.0.0.A02", "label": "EFR32FG25 902-928 MHz +14 dBm Radio Board (BRD4270B Rev A02)", "type": "Board", "opn": "BRD4270"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25 902-928 MHz +14 dBm Radio Board", "description": "", "id": "brd4270b:0.0.0.A03", "label": "EFR32FG25 902-928 MHz +14 dBm Radio Board (BRD4270B Rev A03)", "type": "Board", "opn": "BRD4270"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25 902-928 MHz +14 dBm Radio Board", "description": "", "id": "brd4270b:0.0.0.A04", "label": "EFR32FG25 902-928 MHz +14 dBm Radio Board (BRD4270B Rev A04)", "type": "Board", "opn": "BRD4270"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25 902-928 MHz +16 dBm Radio Board", "description": "", "id": "brd4270b:0.0.0.A05", "label": "EFR32FG25 902-928 MHz +16 dBm Radio Board (BRD4270B Rev A05)", "type": "Board", "opn": "BRD4270"}, {"rev": "A06", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25 902-928 MHz +16 dBm Radio Board", "description": "", "id": "brd4270b:0.0.0.A06", "label": "EFR32FG25 902-928 MHz +16 dBm Radio Board (BRD4270B Rev A06)", "type": "Board", "opn": "BRD4270"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25 863-870 MHz +14 dBm Radio Board", "description": "", "id": "brd4271a:0.0.0.A01", "label": "EFR32FG25 863-870 MHz +14 dBm Radio Board (BRD4271A Rev A01)", "type": "Board", "opn": "BRD4271"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25 863-870 MHz +14 dBm Radio Board", "description": "", "id": "brd4271a:0.0.0.A02", "label": "EFR32FG25 863-870 MHz +14 dBm Radio Board (BRD4271A Rev A02)", "type": "Board", "opn": "BRD4271"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25 863-870 MHz +14 dBm Radio Board", "description": "", "id": "brd4271a:0.0.0.A03", "label": "EFR32FG25 863-870 MHz +14 dBm Radio Board (BRD4271A Rev A03)", "type": "Board", "opn": "BRD4271"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25 863-870 MHz +14 dBm Radio Board", "description": "", "id": "brd4271a:0.0.0.A04", "label": "EFR32FG25 863-870 MHz +14 dBm Radio Board (BRD4271A Rev A04)", "type": "Board", "opn": "BRD4271"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25 863-870 MHz +16 dBm Radio Board", "description": "", "id": "brd4271a:0.0.0.A05", "label": "EFR32FG25 863-870 MHz +16 dBm Radio Board (BRD4271A Rev A05)", "type": "Board", "opn": "BRD4271"}, {"rev": "A06", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25 863-870 MHz +16 dBm Radio Board", "description": "", "id": "brd4271a:0.0.0.A06", "label": "EFR32FG25 863-870 MHz +16 dBm Radio Board (BRD4271A Rev A06)", "type": "Board", "opn": "BRD4271"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25 470 MHz +16 dBm Radio Board", "description": "", "id": "brd4272a:0.0.0.A03", "label": "EFR32FG25 470 MHz +16 dBm Radio Board (BRD4272A Rev A03)", "type": "Board", "opn": "BRD4272"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board", "description": "", "id": "brd4273a:0.0.0.A03", "label": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board (BRD4273A Rev A03)", "type": "Board", "opn": "BRD4273"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board", "description": "", "id": "brd4273a:0.0.0.A04", "label": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board (BRD4273A Rev A04)", "type": "Board", "opn": "BRD4273"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board", "description": "", "id": "brd4273a:0.0.0.A05", "label": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board (BRD4273A Rev A05)", "type": "Board", "opn": "BRD4273"}, {"rev": "A06", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board", "description": "", "id": "brd4273a:0.0.0.A06", "label": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board (BRD4273A Rev A06)", "type": "Board", "opn": "BRD4273"}, {"rev": "A07", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board", "description": "", "id": "brd4273a:0.0.0.A07", "label": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board (BRD4273A Rev A07)", "type": "Board", "opn": "BRD4273"}, {"rev": "A08", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board", "description": "", "id": "brd4273a:0.0.0.A08", "label": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board (BRD4273A Rev A08)", "type": "Board", "opn": "BRD4273"}, {"rev": "A09", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board", "description": "", "id": "brd4273a:0.0.0.A09", "label": "EFR32FG25+EFF01 902-928 MHz +30 dBm Radio Board (BRD4273A Rev A09)", "type": "Board", "opn": "BRD4273"}, {"rev": "A10", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 902-928 MHz 30 dBm Radio Board", "description": "", "id": "brd4273a:0.0.0.A10", "label": "EFR32FG25+EFF01 902-928 MHz 30 dBm Radio Board (BRD4273A Rev A10)", "type": "Board", "opn": "BRD4273"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 863-870 MHz +30 dBm Radio Board", "description": "", "id": "brd4274a:0.0.0.A01", "label": "EFR32FG25+EFF01 863-870 MHz +30 dBm Radio Board (BRD4274A Rev A01)", "type": "Board", "opn": "BRD4274"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 863-870 MHz +30 dBm Radio Board", "description": "", "id": "brd4274a:0.0.0.A02", "label": "EFR32FG25+EFF01 863-870 MHz +30 dBm Radio Board (BRD4274A Rev A02)", "type": "Board", "opn": "BRD4274"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 863-870 MHz +30 dBm Radio Board", "description": "", "id": "brd4274a:0.0.0.A03", "label": "EFR32FG25+EFF01 863-870 MHz +30 dBm Radio Board (BRD4274A Rev A03)", "type": "Board", "opn": "BRD4274"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 863-870 MHz +30 dBm Radio Board", "description": "", "id": "brd4274a:0.0.0.A04", "label": "EFR32FG25+EFF01 863-870 MHz +30 dBm Radio Board (BRD4274A Rev A04)", "type": "Board", "opn": "BRD4274"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 863-870 MHz +30 dBm Radio Board", "description": "", "id": "brd4274a:0.0.0.A05", "label": "EFR32FG25+EFF01 863-870 MHz +30 dBm Radio Board (BRD4274A Rev A05)", "type": "Board", "opn": "BRD4274"}, {"rev": "A06", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg25.efr32fg25b222f1920im56"], "imageURL": "", "name": "EFR32FG25+EFF01 863-870 MHz 30 dBm Radio Board", "description": "", "id": "brd4274a:0.0.0.A06", "label": "EFR32FG25+EFF01 863-870 MHz 30 dBm Radio Board (BRD4274A Rev A06)", "type": "Board", "opn": "BRD4274"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg1.bgm111a256v2"], "imageURL": "", "name": "BGM111 Blue Gecko Module Radio Board", "description": "", "id": "brd4300a:0.0.0", "label": "BGM111 Blue Gecko Module Radio Board (BRD4300A)", "type": "Board", "opn": "BRD4300"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg1.mgm111a256v2"], "imageURL": "", "name": "MGM111 Mighty Gecko Module Radio Board", "description": "", "id": "brd4300b:0.0.0", "label": "MGM111 Mighty Gecko Module Radio Board (BRD4300B)", "type": "Board", "opn": "BRD4300"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg1.bgm113a256v2"], "imageURL": "", "name": "BGM113 Blue Gecko Module Radio Board", "description": "", "id": "brd4301a:0.0.0", "label": "BGM113 Blue Gecko Module Radio Board (BRD4301A)", "type": "Board", "opn": "BRD4301"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg1.bgm121a256v2"], "imageURL": "", "name": "BGM121 Blue Gecko Module Radio Board", "description": "", "id": "brd4302a:0.0.0", "label": "BGM121 Blue Gecko Module Radio Board (BRD4302A)", "type": "Board", "opn": "BRD4302"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg1.bgm11s22f256ga"], "imageURL": "", "name": "BGM11S Blue Gecko Module Radio Board", "description": "", "id": "brd4303a:0.0.0", "label": "BGM11S Blue Gecko Module Radio Board (BRD4303A)", "type": "Board", "opn": "BRD4303"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.mgm12p32f1024ga"], "imageURL": "", "name": "MGM12P Mighty Gecko Module Radio Board", "description": "", "id": "brd4304a:0.0.0", "label": "MGM12P Mighty Gecko Module Radio Board (BRD4304A)", "type": "Board", "opn": "BRD4304"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg13.bgm13s32f512ga"], "imageURL": "", "name": "BGM13S32 Blue Gecko Module Radio Board", "description": "", "id": "brd4305a:0.0.0", "label": "BGM13S32 Blue Gecko Module Radio Board (BRD4305A)", "type": "Board", "opn": "BRD4305"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg13.bgm13s22f512ga"], "imageURL": "", "name": "BGM13S22 Blue Gecko Module Radio Board", "description": "", "id": "brd4305c:0.0.0", "label": "BGM13S22 Blue Gecko Module Radio Board (BRD4305C)", "type": "Board", "opn": "BRD4305"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.mgm13s02f512ga"], "imageURL": "", "name": "MGM13S02 Mighty Gecko Module Radio Board", "description": "", "id": "brd4305d:0.0.0", "label": "MGM13S02 Mighty Gecko Module Radio Board (BRD4305D)", "type": "Board", "opn": "BRD4305"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.mgm13s12f512ga"], "imageURL": "", "name": "MGM13S12 Mighty Gecko Module Radio Board", "description": "", "id": "brd4305e:0.0.0", "label": "MGM13S12 Mighty Gecko Module Radio Board (BRD4305E)", "type": "Board", "opn": "BRD4305"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg13.bgm13p22f512ga"], "imageURL": "", "name": "BGM13P22 Blue Gecko Module Radio Board", "description": "", "id": "brd4306a:0.0.0", "label": "BGM13P22 Blue Gecko Module Radio Board (BRD4306A)", "type": "Board", "opn": "BRD4306"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg13.bgm13p32f512ga"], "imageURL": "", "name": "BGM13P32 Blue Gecko Module Radio Board", "description": "", "id": "brd4306b:0.0.0", "label": "BGM13P32 Blue Gecko Module Radio Board (BRD4306B)", "type": "Board", "opn": "BRD4306"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.mgm13p02f512ga"], "imageURL": "", "name": "MGM13P02 Mighty Gecko Module Radio Board", "description": "", "id": "brd4306c:0.0.0", "label": "MGM13P02 Mighty Gecko Module Radio Board (BRD4306C)", "type": "Board", "opn": "BRD4306"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg13.mgm13p12f512ga"], "imageURL": "", "name": "MGM13P12 Mighty Gecko Module Radio Board", "description": "", "id": "brd4306d:0.0.0", "label": "MGM13P12 Mighty Gecko Module Radio Board (BRD4306D)", "type": "Board", "opn": "BRD4306"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.mgm210pa32jia"], "imageURL": "", "name": "xGM210PA32 Wireless Gecko Module Radio Board", "description": "", "id": "brd4308a:0.0.0", "label": "xGM210PA32 Wireless Gecko Module Radio Board (BRD4308A)", "type": "Board", "opn": "BRD4308"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.mgm210p032jia"], "imageURL": "", "name": "xGM210P032 Wireless Gecko Module Radio Board", "description": "", "id": "brd4308a:0.0.0.A01", "label": "xGM210P032 Wireless Gecko Module Radio Board (BRD4308A Rev A01)", "type": "Board", "opn": "BRD4308"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.mgm210pa32jia"], "imageURL": "", "name": "xGM210PA32 Wireless Gecko Module Radio Board", "description": "", "id": "brd4308a:0.0.0.A02", "label": "xGM210PA32 Wireless Gecko Module Radio Board (BRD4308A Rev A02)", "type": "Board", "opn": "BRD4308"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.mgm210pa22jia"], "imageURL": "", "name": "xGM210PA22 Wireless Gecko Module Radio Board", "description": "", "id": "brd4308b:0.0.0", "label": "xGM210PA22 Wireless Gecko Module Radio Board (BRD4308B)", "type": "Board", "opn": "BRD4308"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.mgm210p022jia"], "imageURL": "", "name": "xGM210P022 Wireless Gecko Module Radio Board", "description": "", "id": "brd4308b:0.0.0.A01", "label": "xGM210P022 Wireless Gecko Module Radio Board (BRD4308B Rev A01)", "type": "Board", "opn": "BRD4308"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.mgm210pa22jia"], "imageURL": "", "name": "xGM210PA22 Wireless Gecko Module Radio Board", "description": "", "id": "brd4308b:0.0.0.A02", "label": "xGM210PA22 Wireless Gecko Module Radio Board (BRD4308B Rev A02)", "type": "Board", "opn": "BRD4308"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.mgm210pb32jia"], "imageURL": "", "name": "xGM210PB32 Wireless Gecko Module Radio Board", "description": "", "id": "brd4308c:0.0.0", "label": "xGM210PB32 Wireless Gecko Module Radio Board (BRD4308C)", "type": "Board", "opn": "BRD4308"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.mgm210pb22jia"], "imageURL": "", "name": "xGM210PB22 Wireless Gecko Module Radio Board", "description": "", "id": "brd4308d:0.0.0", "label": "xGM210PB22 Wireless Gecko Module Radio Board (BRD4308D)", "type": "Board", "opn": "BRD4308"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.mgm210l022jif"], "imageURL": "", "name": "xGM210L Wireless Gecko Lighting Module Radio Board", "description": "", "id": "brd4309a:0.0.0", "label": "xGM210L Wireless Gecko Lighting Module Radio Board (BRD4309A)", "type": "Board", "opn": "BRD4309"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.mgm210la22jif"], "imageURL": "", "name": "xGM210LA Wireless Gecko Module Radio Board", "description": "", "id": "brd4309b:0.0.0", "label": "xGM210LA Wireless Gecko Module Radio Board (BRD4309B)", "type": "Board", "opn": "BRD4309"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.bgm220sc12wga"], "imageURL": "", "name": "BGM220S Wireless Gecko Module Radio Board", "description": "", "id": "brd4310a:0.0.0", "label": "BGM220S Wireless Gecko Module Radio Board (BRD4310A)", "type": "Board", "opn": "BRD4310"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.bgm220pc22hna"], "imageURL": "", "name": "BGM220P Wireless Gecko Module Radio Board", "description": "", "id": "brd4311a:0.0.0.A01", "label": "BGM220P Wireless Gecko Module Radio Board (BRD4311A Rev A01)", "type": "Board", "opn": "BRD4311"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg22.mgm220pc22hna"], "imageURL": "", "name": "MGM220P Wireless Gecko Module Radio Board", "description": "", "id": "brd4311b:0.0.0", "label": "MGM220P Wireless Gecko Module Radio Board (BRD4311B)", "type": "Board", "opn": "BRD4311"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.bgm220sc22hna"], "imageURL": "", "name": "BGM220SC22 Wireless Gecko Module Radio Board", "description": "", "id": "brd4312a:0.0.0.A00", "label": "BGM220SC22 Wireless Gecko Module Radio Board (BRD4312A Rev A00)", "type": "Board", "opn": "BRD4312"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.bgm220pc22hna"], "imageURL": "", "name": "BGM220 Explorer Kit Board", "description": "", "id": "brd4314a:0.0.0", "label": "BGM220 Explorer Kit Board (BRD4314A)", "type": "Board", "opn": "BRD4314"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240pb22vna"], "imageURL": "", "name": "xGM240P 10 dBm Module Radio Board", "description": "", "id": "brd4316a:0.0.0.A01", "label": "xGM240P 10 dBm Module Radio Board (BRD4316A Rev A01)", "type": "Board", "opn": "BRD4316"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240pb22vna"], "imageURL": "", "name": "xGM240P 10 dBm Module Radio Board", "description": "", "id": "brd4316a:0.0.0.A02", "label": "xGM240P 10 dBm Module Radio Board (BRD4316A Rev A02)", "type": "Board", "opn": "BRD4316"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240pb32vna"], "imageURL": "", "name": "xGM240P 20 dBm Module Radio Board", "description": "", "id": "brd4317a:0.0.0.A01", "label": "xGM240P 20 dBm Module Radio Board (BRD4317A Rev A01)", "type": "Board", "opn": "BRD4317"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240pb32vna"], "imageURL": "", "name": "xGM240P 20 dBm Module Radio Board", "description": "", "id": "brd4317a:0.0.0.A02", "label": "xGM240P 20 dBm Module Radio Board (BRD4317A Rev A02)", "type": "Board", "opn": "BRD4317"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240sb22vna"], "imageURL": "", "name": "xGM240S 10 dBm Module Radio Board", "description": "", "id": "brd4318a:0.0.0.A02", "label": "xGM240S 10 dBm Module Radio Board (BRD4318A Rev A02)", "type": "Board", "opn": "BRD4318"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240sd22vna"], "imageURL": "", "name": "xGM240S 10 dBm Module Radio Board", "description": "", "id": "brd4318a:0.0.0.A03", "label": "xGM240S 10 dBm Module Radio Board (BRD4318A Rev A03)", "type": "Board", "opn": "BRD4318"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240sd22vna"], "imageURL": "", "name": "xGM240S 10 dBm Module Radio Board", "description": "", "id": "brd4318a:0.0.0.A04", "label": "xGM240S 10 dBm Module Radio Board (BRD4318A Rev A04)", "type": "Board", "opn": "BRD4318"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240l022vnf"], "imageURL": "", "name": "xGM240L Bobcat Lighting Module Radio Board", "description": "", "id": "brd4319a:0.0.0.A00", "label": "xGM240L Bobcat Lighting Module Radio Board (BRD4319A Rev A00)", "type": "Board", "opn": "BRD4319"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240l022rnf"], "imageURL": "", "name": "xGM240L Bobcat Lighting Module Radio Board", "description": "", "id": "brd4319a:0.0.0.A01", "label": "xGM240L Bobcat Lighting Module Radio Board (BRD4319A Rev A01)", "type": "Board", "opn": "BRD4319"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240l022vif"], "imageURL": "", "name": "MGM240L022VIFx Lighting Module Radio Board", "description": "", "id": "brd4319b:0.0.0.A00", "label": "MGM240L022VIFx Lighting Module Radio Board (BRD4319B Rev A00)", "type": "Board", "opn": "BRD4319"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240l022vif"], "imageURL": "", "name": "MGM240L022VIFx UNL Lighting Module Radio Board", "description": "", "id": "brd4319c:0.0.0.A00", "label": "MGM240L022VIFx UNL Lighting Module Radio Board (BRD4319C Rev A00)", "type": "Board", "opn": "BRD4319"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240la22uif"], "imageURL": "", "name": "MGM240LA22UIFx UNL Lighting Module Radio Board", "description": "", "id": "brd4319d:0.0.0.A00", "label": "MGM240LA22UIFx UNL Lighting Module Radio Board (BRD4319D Rev A00)", "type": "Board", "opn": "BRD4319"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240la22vif"], "imageURL": "", "name": "MGM240L", "description": "", "id": "brd4319e:0.0.0.A00", "label": "MGM240L (BRD4319E Rev A00)", "type": "Board", "opn": "BRD4319"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240ld22vif"], "imageURL": "", "name": "MGM240LD22VIFx UNL Lighting Module Radio Board", "description": "", "id": "brd4319f:0.0.0.A00", "label": "MGM240LD22VIFx UNL Lighting Module Radio Board (BRD4319F Rev A00)", "type": "Board", "opn": "BRD4319"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240la22uif"], "imageURL": "", "name": "MGM240LA22UIFx Lighting Module Radio Board", "description": "", "id": "brd4319g:0.0.0.A00", "label": "MGM240LA22UIFx Lighting Module Radio Board (BRD4319G Rev A00)", "type": "Board", "opn": "BRD4319"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240la22vif"], "imageURL": "", "name": "MGM240LA22VIFx Lighting Module Radio Board", "description": "", "id": "brd4319h:0.0.0.A00", "label": "MGM240LA22VIFx Lighting Module Radio Board (BRD4319H Rev A00)", "type": "Board", "opn": "BRD4319"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.gg.efm32gg395f1024"], "imageURL": "", "name": "WGM110 Wi-Fi Module Radio Board", "description": "", "id": "brd4320a:0.0.0", "label": "WGM110 Wi-Fi Module Radio Board (BRD4320A)", "type": "Board", "opn": "BRD4320"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wf.wgm160px22kga"], "imageURL": "", "name": "WGM160P Wi-Fi Module Radio Board", "description": "", "id": "brd4321a:0.0.0", "label": "WGM160P Wi-Fi Module Radio Board (BRD4321A)", "type": "Board", "opn": "BRD4321"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wf.wgm160px22kga2"], "imageURL": "", "name": "WGM160P Wi-Fi Module Radio Board", "description": "", "id": "brd4321a:0.0.0.A05", "label": "WGM160P Wi-Fi Module Radio Board (BRD4321A Rev A05)", "type": "Board", "opn": "BRD4321"}, {"rev": "A06", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wf.wgm160px22kga2"], "imageURL": "", "name": "WGM160P Wi-Fi Module Radio Board", "description": "", "id": "brd4321a:0.0.0.A06", "label": "WGM160P Wi-Fi Module Radio Board (BRD4321A Rev A06)", "type": "Board", "opn": "BRD4321"}, {"rev": "A07", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wf.wgm160px22kga3"], "imageURL": "", "name": "WGM160P Wi-Fi Module Radio Board", "description": "", "id": "brd4321a:0.0.0.A07", "label": "WGM160P Wi-Fi Module Radio Board (BRD4321A Rev A07)", "type": "Board", "opn": "BRD4321"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.si917-sb00"], "imageURL": "", "name": "Si917 Single Band Wi-Fi Transceiver Radio Board", "description": "", "id": "brd4325a:0.0.0.A00", "label": "Si917 Single Band Wi-Fi Transceiver Radio Board (BRD4325A Rev A00)", "type": "Board", "opn": "BRD4325"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Si917 Single Band Wi-Fi Transceiver Radio Board", "description": "", "id": "brd4325a:0.0.0.A01", "label": "Si917 Single Band Wi-Fi Transceiver Radio Board (BRD4325A Rev A01)", "type": "Board", "opn": "BRD4325"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.si917-sb00 (dual flash)"], "imageURL": "", "name": "Si917 Single Band Wi-Fi Transceiver Radio Board", "description": "", "id": "brd4325b:0.0.0.A00", "label": "Si917 Single Band Wi-Fi Transceiver Radio Board (BRD4325B Rev A00)", "type": "Board", "opn": "BRD4325"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba"], "imageURL": "", "name": "Si917 2.4GHz Wi-Fi Radio Board Common Flash", "description": "", "id": "brd4325c:0.0.0.A01", "label": "Si917 2.4GHz Wi-Fi Radio Board Common Flash (BRD4325C Rev A01)", "type": "Board", "opn": "BRD4325"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m100xntba"], "imageURL": "", "name": "Si917 2.4GHz Wi-Fi Radio Board + Ext Flash + Ext PSRAM", "description": "", "id": "brd4325d:0.0.0.A00", "label": "Si917 2.4GHz Wi-Fi Radio Board + Ext Flash + Ext PSRAM (BRD4325D Rev A00)", "type": "Board", "opn": "BRD4325"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba (dual flash)"], "imageURL": "", "name": "Si917 2.4GHz Wi-Fi Radio Board + Internal Flash + Ext Flash", "description": "", "id": "brd4325e:0.0.0.A01", "label": "Si917 2.4GHz Wi-Fi Radio Board + Internal Flash + Ext Flash (BRD4325E Rev A01)", "type": "Board", "opn": "BRD4325"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m141xgtba"], "imageURL": "", "name": "Si917 2.4GHz Wi-Fi Radio Board + Ext Flash + Internal PSRAM", "description": "", "id": "brd4325f:0.0.0.A01", "label": "Si917 2.4GHz Wi-Fi Radio Board + Ext Flash + Internal PSRAM (BRD4325F Rev A01)", "type": "Board", "opn": "BRD4325"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba"], "imageURL": "", "name": "Si917 2.4GHz Wi-Fi Radio Board + Internal Flash + Ext PSRAM", "description": "", "id": "brd4325g:0.0.0.A01", "label": "Si917 2.4GHz Wi-Fi Radio Board + Internal Flash + Ext PSRAM (BRD4325G Rev A01)", "type": "Board", "opn": "BRD4325"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg23.fgm230sb27hgn"], "imageURL": "", "name": "FGM230S Radio Board", "description": "", "id": "brd4328a:0.0.0.A00", "label": "FGM230S Radio Board (BRD4328A Rev A00)", "type": "Board", "opn": "BRD4328"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.fg23.fgm230sa27hgn"], "imageURL": "", "name": "FGM230SA Radio Board", "description": "", "id": "brd4328b:0.0.0.A00", "label": "FGM230SA Radio Board (BRD4328B Rev A00)", "type": "Board", "opn": "BRD4328"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg21.mgm210la22jif"], "imageURL": "", "name": "xGM210L-3 Wireless Gecko Module Radio Board", "description": "", "id": "brd4329a:0.0.0.A00", "label": "xGM210L-3 Wireless Gecko Module Radio Board (BRD4329A Rev A00)", "type": "Board", "opn": "BRD4329"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.bgm220pc22hna"], "imageURL": "", "name": "Lyra P Development Kit", "description": "", "id": "brd4330a:0.0.0.A00", "label": "Lyra P Development Kit (BRD4330A Rev A00)", "type": "Board", "opn": "BRD4330"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.bgm220sc22hna"], "imageURL": "", "name": "Lyra S Development Kit", "description": "", "id": "brd4331a:0.0.0.A00", "label": "Lyra S Development Kit (BRD4331A Rev A00)", "type": "Board", "opn": "BRD4331"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg21.efr32bg21b010f1024im32"], "imageURL": "", "name": "KG100S for Amazon Sidewalk Radio Board", "description": "", "id": "brd4332a:0.0.0.A03", "label": "KG100S for Amazon Sidewalk Radio Board (BRD4332A Rev A03)", "type": "Board", "opn": "BRD4332"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.mgm240ld22vif"], "imageURL": "", "name": "xGM240L Lighting Module Radio Board", "description": "", "id": "brd4337a:0.0.0.A00", "label": "xGM240L Lighting Module Radio Board (BRD4337A Rev A00)", "type": "Board", "opn": "BRD4337"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba"], "imageURL": "", "name": "SiWG917 Single Band Wi-Fi and BLE 8MB Flash Radio Board", "description": "", "id": "brd4338a:0.0.0.A00", "label": "SiWG917 Single Band Wi-Fi and BLE 8MB Flash Radio Board (BRD4338A Rev A00)", "type": "Board", "opn": "BRD4338"}, {"rev": "A11", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba"], "imageURL": "", "name": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash Radio Board", "description": "", "id": "brd4338a:0.0.0.A11", "label": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash Radio Board (BRD4338A Rev A11)", "type": "Board", "opn": "BRD4338"}, {"rev": "A12", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba"], "imageURL": "", "name": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash Radio Board", "description": "", "id": "brd4338a:0.0.0.A12", "label": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash Radio Board (BRD4338A Rev A12)", "type": "Board", "opn": "BRD4338"}, {"rev": "A13", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba"], "imageURL": "", "name": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash Radio Board", "description": "", "id": "brd4338a:0.0.0.A13", "label": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash Radio Board (BRD4338A Rev A13)", "type": "Board", "opn": "BRD4338"}, {"rev": "A14", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba"], "imageURL": "", "name": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash Radio Board", "description": "", "id": "brd4338a:0.0.0.A14", "label": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash Radio Board (BRD4338A Rev A14)", "type": "Board", "opn": "BRD4338"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m141xgtba"], "imageURL": "", "name": "SiWG917 Single Band Wi-Fi and BLE 8MB PSRAM + 8MB ext Flash Radio Board", "description": "", "id": "brd4339a:0.0.0.A00", "label": "SiWG917 Single Band Wi-Fi and BLE 8MB PSRAM + 8MB ext Flash Radio Board (BRD4339A Rev A00)", "type": "Board", "opn": "BRD4339"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba (dual flash)"], "imageURL": "", "name": "SiWG917 Single Band Wi-Fi and BLE 8MB Flash + 8MB ext Flash Radio Board", "description": "", "id": "brd4339b:0.0.0.A00", "label": "SiWG917 Single Band Wi-Fi and BLE 8MB Flash + 8MB ext Flash Radio Board (BRD4339B Rev A00)", "type": "Board", "opn": "BRD4339"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba"], "imageURL": "", "name": "SiWG917 Single Band Wi-Fi and BLE 8MB Flash + 8MB ext PSRAM Radio Board", "description": "", "id": "brd4340a:0.0.0.A00", "label": "SiWG917 Single Band Wi-Fi and BLE 8MB Flash + 8MB ext PSRAM Radio Board (BRD4340A Rev A00)", "type": "Board", "opn": "BRD4340"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba"], "imageURL": "", "name": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash + 8MB Ext. PSRAM Radio Board", "description": "", "id": "brd4340a:0.0.0.A01", "label": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash + 8MB Ext. PSRAM Radio Board (BRD4340A Rev A01)", "type": "Board", "opn": "BRD4340"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba"], "imageURL": "", "name": "SiWG917 Single Band Wi-Fi and BLE 8MB Flash Radio Board", "description": "", "id": "brd4340b:0.0.0.A00", "label": "SiWG917 Single Band Wi-Fi and BLE 8MB Flash Radio Board (BRD4340B Rev A00)", "type": "Board", "opn": "BRD4340"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m100xntba"], "imageURL": "", "name": "SiWG917 Single-band Wi-Fi and BLE 8MB Ext. Flash High Temp Radio Board", "description": "", "id": "brd4341a:0.0.0.A00", "label": "SiWG917 Single-band Wi-Fi and BLE 8MB Ext. Flash High Temp Radio Board (BRD4341A Rev A00)", "type": "Board", "opn": "BRD4341"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba"], "imageURL": "", "name": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash + 8MB ext PSRAM Radio Board", "description": "", "id": "brd4342a:0.0.0.A01", "label": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash + 8MB ext PSRAM Radio Board (BRD4342A Rev A01)", "type": "Board", "opn": "BRD4342"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917m111mgtba"], "imageURL": "", "name": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash + 8MB ext PSRAM Radio Board", "description": "", "id": "brd4342a:0.0.0.A03", "label": "SiWG917 Single-band Wi-Fi and BLE 8MB Flash + 8MB ext PSRAM Radio Board (BRD4342A Rev A03)", "type": "Board", "opn": "BRD4342"}, {"rev": "A07", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917y111mgnba"], "imageURL": "", "name": "SiWG917Y Module Wi-Fi 6 and Bluetooth LE 8MB Flash RF-Pin Radio Board", "description": "", "id": "brd4343a:0.0.0.A07", "label": "SiWG917Y Module Wi-Fi 6 and Bluetooth LE 8MB Flash RF-Pin Radio Board (BRD4343A Rev A07)", "type": "Board", "opn": "BRD4343"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917y111mgaba"], "imageURL": "", "name": "SiWx917YACx Module Early Access Radio Board (Internal sampling)", "description": "", "id": "brd4343b:0.0.0.A02", "label": "SiWx917YACx Module Early Access Radio Board (Internal sampling) (BRD4343B Rev A02)", "type": "Board", "opn": "BRD4343"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "SiWx917YACx Module Early Access Radio Board (Alpha sampling)", "description": "", "id": "brd4343c:0.0.0.A02", "label": "SiWx917YACx Module Early Access Radio Board (Alpha sampling) (BRD4343C Rev A02)", "type": "Board", "opn": "BRD4343"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917y111mgaba"], "imageURL": "", "name": "SiWx917YACx Module Early Access Radio Board (Alpha sampling)", "description": "", "id": "brd4343d:0.0.0.A02", "label": "SiWx917YACx Module Early Access Radio Board (Alpha sampling) (BRD4343D Rev A02)", "type": "Board", "opn": "BRD4343"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwg917y121mgnb"], "imageURL": "", "name": "SiWx917", "description": "", "id": "brd4343q:0.0.0.A02", "label": "SiWx917 (BRD4343Q Rev A02)", "type": "Board", "opn": "BRD4343"}, {"rev": "A12", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwn917m100lgtba"], "imageURL": "", "name": "SiWN917 NCP/RCP Radio Board", "description": "", "id": "brd4346a:0.0.0.A12", "label": "SiWN917 NCP/RCP Radio Board (BRD4346A Rev A12)", "type": "Board", "opn": "BRD4346"}, {"rev": "A13", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwn917m100lgtba"], "imageURL": "", "name": "SiWN917 NCP/RCP Radio Board", "description": "", "id": "brd4346a:0.0.0.A13", "label": "SiWN917 NCP/RCP Radio Board (BRD4346A Rev A13)", "type": "Board", "opn": "BRD4346"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.mgm260pb22vna"], "imageURL": "", "name": "MGM260P", "description": "", "id": "brd4350a:0.0.0.A02", "label": "MGM260P (BRD4350A Rev A02)", "type": "Board", "opn": "BRD4350"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.mgm260pb22vna"], "imageURL": "", "name": "MGM260P", "description": "", "id": "brd4350a:0.0.0.A03", "label": "MGM260P (BRD4350A Rev A03)", "type": "Board", "opn": "BRD4350"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.mgm260pb22vna"], "imageURL": "", "name": "MGM260P", "description": "", "id": "brd4350a:0.0.0.A04", "label": "MGM260P (BRD4350A Rev A04)", "type": "Board", "opn": "BRD4350"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.mgm260pd22vna"], "imageURL": "", "name": "xGM260P 10dBm Module Radio Board", "description": "", "id": "brd4350a:0.0.0.A05", "label": "xGM260P 10dBm Module Radio Board (BRD4350A Rev A05)", "type": "Board", "opn": "BRD4350"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.mgm260pb32vna"], "imageURL": "", "name": "MGM260P", "description": "", "id": "brd4351a:0.0.0.A02", "label": "MGM260P (BRD4351A Rev A02)", "type": "Board", "opn": "BRD4351"}, {"rev": "A03", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.mgm260pb32vna"], "imageURL": "", "name": "MGM260P", "description": "", "id": "brd4351a:0.0.0.A03", "label": "MGM260P (BRD4351A Rev A03)", "type": "Board", "opn": "BRD4351"}, {"rev": "A04", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.mgm260pb32vna"], "imageURL": "", "name": "MGM260P", "description": "", "id": "brd4351a:0.0.0.A04", "label": "MGM260P (BRD4351A Rev A04)", "type": "Board", "opn": "BRD4351"}, {"rev": "A05", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg26.mgm260pd32vna"], "imageURL": "", "name": "xGM260P 20dBm Module Radio Board", "description": "", "id": "brd4351a:0.0.0.A05", "label": "xGM260P 20dBm Module Radio Board (BRD4351A Rev A05)", "type": "Board", "opn": "BRD4351"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.siwn917y100lgnba"], "imageURL": "", "name": "SiWx917Y Wi-Fi 6 2.4 GHz Bluetooth LE 5.4 4MB Flash RF-Pin Co-processor Radio Board", "description": "", "id": "brd4357a:0.0.0.A02", "label": "SiWx917Y Wi-Fi 6 2.4 GHz Bluetooth LE 5.4 4MB Flash RF-Pin Co-processor Radio Board (BRD4357A Rev A02)", "type": "Board", "opn": "BRD4357"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg28.efr32zg28b312f1024im68"], "imageURL": "", "name": "EFR32xG28 868/915 MHz +14 dBm + 2.4 GHz +10 dBm Radio Board", "description": "", "id": "brd4400b:0.0.0.A00", "label": "EFR32xG28 868/915 MHz +14 dBm + 2.4 GHz +10 dBm Radio Board (BRD4400B Rev A00)", "type": "Board", "opn": "BRD4400"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg28.efr32zg28b312f1024im68"], "imageURL": "", "name": "EFR32xG28 868/915 MHz +14 dBm + 2.4 GHz +10 dBm Radio Board", "description": "", "id": "brd4400b:0.0.0.A01", "label": "EFR32xG28 868/915 MHz +14 dBm + 2.4 GHz +10 dBm Radio Board (BRD4400B Rev A01)", "type": "Board", "opn": "BRD4400"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg28.efr32zg28b312f1024im68"], "imageURL": "", "name": "EFR32xG28 868/915 MHz +14 dBm + 2.4 GHz +10 dBm Radio Board", "description": "", "id": "brd4400c:0.0.0.A00", "label": "EFR32xG28 868/915 MHz +14 dBm + 2.4 GHz +10 dBm Radio Board (BRD4400C Rev A00)", "type": "Board", "opn": "BRD4400"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg28.efr32zg28b322f1024im68"], "imageURL": "", "name": "EFR32xG28 868/915 MHz +20 dBm + 2.4 GHz +10 dBm Radio Board", "description": "", "id": "brd4401b:0.0.0.A00", "label": "EFR32xG28 868/915 MHz +20 dBm + 2.4 GHz +10 dBm Radio Board (BRD4401B Rev A00)", "type": "Board", "opn": "BRD4401"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg28.efr32zg28b322f1024im68"], "imageURL": "", "name": "EFR32xG28 868/915 MHz +20 dBm + 2.4 GHz +10 dBm Radio Board", "description": "", "id": "brd4401b:0.0.0.A01", "label": "EFR32xG28 868/915 MHz +20 dBm + 2.4 GHz +10 dBm Radio Board (BRD4401B Rev A01)", "type": "Board", "opn": "BRD4401"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.zg28.efr32zg28b322f1024im68"], "imageURL": "", "name": "EFR32xG28 868/915 MHz +20 dBm + 2.4 GHz +10 dBm Radio Board", "description": "", "id": "brd4401c:0.0.0.A00", "label": "EFR32xG28 868/915 MHz +20 dBm + 2.4 GHz +10 dBm Radio Board (BRD4401C Rev A00)", "type": "Board", "opn": "BRD4401"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "EFR32xG22B 2.4 GHz 6 dBm Radio Board", "description": "", "id": "brd4402a:0.0.0.A02", "label": "EFR32xG22B 2.4 GHz 6 dBm Radio Board (BRD4402A Rev A02)", "type": "Board", "opn": "BRD4402"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "EFR32xG22B 2.4 GHz 6 dBm Radio Board", "description": "", "id": "brd4402b:0.0.0.A00", "label": "EFR32xG22B 2.4 GHz 6 dBm Radio Board (BRD4402B Rev A00)", "type": "Board", "opn": "BRD4402"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im40"], "imageURL": "", "name": "EFR32BG22 Bluetooth LE 6 dBm QFN40 Rev F Radio Board", "description": "", "id": "brd4402c:0.0.0.A01", "label": "EFR32BG22 Bluetooth LE 6 dBm QFN40 Rev F Radio Board (BRD4402C Rev A01)", "type": "Board", "opn": "BRD4402"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im32"], "imageURL": "", "name": "EFR32xG22B 2.4 GHz 6 dBm QFN32 Radio Board", "description": "", "id": "brd4403a:0.0.0.A02", "label": "EFR32xG22B 2.4 GHz 6 dBm QFN32 Radio Board (BRD4403A Rev A02)", "type": "Board", "opn": "BRD4403"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im32"], "imageURL": "", "name": "EFR32xG22B 2.4 GHz 6 dBm QFN32 Radio Board", "description": "", "id": "brd4403b:0.0.0.A00", "label": "EFR32xG22B 2.4 GHz 6 dBm QFN32 Radio Board (BRD4403B Rev A00)", "type": "Board", "opn": "BRD4403"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22c224f512im32"], "imageURL": "", "name": "EFR32BG22 Bluetooth LE 6 dBm QFN32 Rev F Radio Board", "description": "", "id": "brd4403c:0.0.0.A01", "label": "EFR32BG22 Bluetooth LE 6 dBm QFN32 Rev F Radio Board (BRD4403C Rev A01)", "type": "Board", "opn": "BRD4403"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg24.efr32mg24b210f1536im48"], "imageURL": "", "name": "EFR32xG24 Channel Sounding 20 dBm Radio Board", "description": "", "id": "brd4406a:0.0.0.A00", "label": "EFR32xG24 Channel Sounding 20 dBm Radio Board (BRD4406A Rev A00)", "type": "Board", "opn": "BRD4406"}, {"rev": "A06", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg29.efr32bg29b220f1024cj45"], "imageURL": "", "name": "EFR32BG29 Bluetooth LE 4 dBm Boost WLCSP Radio Board", "description": "", "id": "brd4414a:0.0.0.A06", "label": "EFR32BG29 Bluetooth LE 4 dBm Boost WLCSP Radio Board (BRD4414A Rev A06)", "type": "Board", "opn": "BRD4414"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg22.efr32mg22e224f512im40"], "imageURL": "", "name": "EFR32xG22E 2.4 GHz 6 dBm Radio Board", "description": "", "id": "brd4415a:0.0.0.A01", "label": "EFR32xG22E 2.4 GHz 6 dBm Radio Board (BRD4415A Rev A01)", "type": "Board", "opn": "BRD4415"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg22.efr32bg22e224f512im32"], "imageURL": "", "name": "EFR32xG22E 2.4 GHz 6 dBm QFN32 Radio Board", "description": "", "id": "brd4416a:0.0.0.A00", "label": "EFR32xG22E 2.4 GHz 6 dBm QFN32 Radio Board (BRD4416A Rev A00)", "type": "Board", "opn": "BRD4416"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg22.efr32mg22e224f512im32"], "imageURL": "", "name": "EFR32xG22E 2.4 GHz 6 dBm QFN32 Radio Board", "description": "", "id": "brd4416a:0.0.0.A01", "label": "EFR32xG22E 2.4 GHz 6 dBm QFN32 Radio Board (BRD4416A Rev A01)", "type": "Board", "opn": "BRD4416"}, {"rev": "B00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.lg.ezr32lg330f256r60g"], "imageURL": "", "name": "EZR32LG 868 MHz 13 dBm Radio Board", "description": "", "id": "brd4502a:0.0.0.B00", "label": "EZR32LG 868 MHz 13 dBm Radio Board (BRD4502A Rev B00)", "type": "Board", "opn": "BRD4502"}, {"rev": "B00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.lg.ezr32lg330f256r55g"], "imageURL": "", "name": "EZR32LG 434 MHz 10 dBm Radio Board", "description": "", "id": "brd4502b:0.0.0.B00", "label": "EZR32LG 434 MHz 10 dBm Radio Board (BRD4502B Rev B00)", "type": "Board", "opn": "BRD4502"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.wg.ezr32wg330f256r60g"], "imageURL": "", "name": "EZR32WG 868 MHz 13 dBm Radio Board", "description": "", "id": "brd4502c:0.0.0.A00", "label": "EZR32WG 868 MHz 13 dBm Radio Board (BRD4502C Rev A00)", "type": "Board", "opn": "BRD4502"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.wg.ezr32wg330f256r55g"], "imageURL": "", "name": "EZR32WG 434 MHz 10 dBm Radio Board", "description": "", "id": "brd4502d:0.0.0.A00", "label": "EZR32WG 434 MHz 10 dBm Radio Board (BRD4502D Rev A00)", "type": "Board", "opn": "BRD4502"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.wg.ezr32wg330f256r67g"], "imageURL": "", "name": "EZR32WG 868 MHz 13 dBm Radio Board", "description": "", "id": "brd4502e:0.0.0", "label": "EZR32WG 868 MHz 13 dBm Radio Board (BRD4502E)", "type": "Board", "opn": "BRD4502"}, {"rev": "B00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.lg.ezr32lg330f256r63g"], "imageURL": "", "name": "EZR32LG 915 MHz 20 dBm Radio Board", "description": "", "id": "brd4503a:0.0.0.B00", "label": "EZR32LG 915 MHz 20 dBm Radio Board (BRD4503A Rev B00)", "type": "Board", "opn": "BRD4503"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.wg.ezr32wg330f256r63g"], "imageURL": "", "name": "EZR32WG 915 MHz 20 dBm Radio Board", "description": "", "id": "brd4503b:0.0.0.A00", "label": "EZR32WG 915 MHz 20 dBm Radio Board (BRD4503B Rev A00)", "type": "Board", "opn": "BRD4503"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.wg.ezr32wg330f256r68g"], "imageURL": "", "name": "EZR32WG 915 MHz 20 dBm Radio Board", "description": "", "id": "brd4503c:0.0.0", "label": "EZR32WG 915 MHz 20 dBm Radio Board (BRD4503C)", "type": "Board", "opn": "BRD4503"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.lg.ezr32lg330f256r63g"], "imageURL": "", "name": "EZR32LG 169 MHz 20 dBm Radio Board", "description": "", "id": "brd4504a:0.0.0.A00", "label": "EZR32LG 169 MHz 20 dBm Radio Board (BRD4504A Rev A00)", "type": "Board", "opn": "BRD4504"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.wg.ezr32wg330f256r63g"], "imageURL": "", "name": "EZR32WG 169 MHz 20 dBm Radio Board", "description": "", "id": "brd4504b:0.0.0.A00", "label": "EZR32WG 169 MHz 20 dBm Radio Board (BRD4504B Rev A00)", "type": "Board", "opn": "BRD4504"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.lg.ezr32lg330f256r63g"], "imageURL": "", "name": "EZR32LG 490 MHz 20 dBm Radio Board", "description": "", "id": "brd4505a:0.0.0.A00", "label": "EZR32LG 490 MHz 20 dBm Radio Board (BRD4505A Rev A00)", "type": "Board", "opn": "BRD4505"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.wg.ezr32wg330f256r63g"], "imageURL": "", "name": "EZR32WG 490 MHz 20 dBm Radio Board", "description": "", "id": "brd4505b:0.0.0.A00", "label": "EZR32WG 490 MHz 20 dBm Radio Board (BRD4505B Rev A00)", "type": "Board", "opn": "BRD4505"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.wg.ezr32wg330f256r68g"], "imageURL": "", "name": "EZR32WG 868 MHz 16 dBm Radio Board", "description": "", "id": "brd4506a:0.0.0", "label": "EZR32WG 868 MHz 16 dBm Radio Board (BRD4506A)", "type": "Board", "opn": "BRD4506"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.hg.ezr32hg320f64r61g"], "imageURL": "", "name": "EZR32HG 868 MHz 16 dBm Radio Board", "description": "", "id": "brd4542a:0.0.0", "label": "EZR32HG 868 MHz 16 dBm Radio Board (BRD4542A)", "type": "Board", "opn": "BRD4542"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.hg.ezr32hg320f64r55g"], "imageURL": "", "name": "EZR32HG 434 MHz 10 dBm Radio Board", "description": "", "id": "brd4542b:0.0.0", "label": "EZR32HG 434 MHz 10 dBm Radio Board (BRD4542B)", "type": "Board", "opn": "BRD4542"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.hg.ezr32hg320f64r67g"], "imageURL": "", "name": "EZR32HG 868 MHz 13 dBm Radio Board", "description": "", "id": "brd4542c:0.0.0", "label": "EZR32HG 868 MHz 13 dBm Radio Board (BRD4542C)", "type": "Board", "opn": "BRD4542"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.hg.ezr32hg320f64r63g"], "imageURL": "", "name": "EZR32HG 915 MHz 20 dBm Radio Board", "description": "", "id": "brd4543a:0.0.0", "label": "EZR32HG 915 MHz 20 dBm Radio Board (BRD4543A)", "type": "Board", "opn": "BRD4543"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.hg.ezr32hg320f64r68g"], "imageURL": "", "name": "EZR32HG 915 MHz 20 dBm Radio Board", "description": "", "id": "brd4543b:0.0.0", "label": "EZR32HG 915 MHz 20 dBm Radio Board (BRD4543B)", "type": "Board", "opn": "BRD4543"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.hg.ezr32hg320f64r63g"], "imageURL": "", "name": "EZR32HG 169 MHz 20 dBm Radio Board", "description": "", "id": "brd4544a:0.0.0", "label": "EZR32HG 169 MHz 20 dBm Radio Board (BRD4544A)", "type": "Board", "opn": "BRD4544"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.hg.ezr32hg320f64r63g"], "imageURL": "", "name": "EZR32HG 490 MHz 20 dBm Radio Board", "description": "", "id": "brd4545a:0.0.0", "label": "EZR32HG 490 MHz 20 dBm Radio Board (BRD4545A)", "type": "Board", "opn": "BRD4545"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ezr32.hg.ezr32hg320f64r68g"], "imageURL": "", "name": "EZR32HG 868 MHz 16 dBm Radio Board", "description": "", "id": "brd4546a:0.0.0", "label": "EZR32HG 868 MHz 16 dBm Radio Board (BRD4546A)", "type": "Board", "opn": "BRD4546"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em357"], "imageURL": "", "name": "EM357 2.4 GHz 8 dBm Module Radio Board with SMA Connector", "description": "", "id": "brd4600a:0.0.0", "label": "EM357 2.4 GHz 8 dBm Module Radio Board with SMA Connector (BRD4600A)", "type": "Board", "opn": "BRD4600"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em3598"], "imageURL": "", "name": "EM3598 2.4 GHz 20 dBm Radio Board", "description": "", "id": "brd4601a:0.0.0", "label": "EM3598 2.4 GHz 20 dBm Radio Board (BRD4601A)", "type": "Board", "opn": "BRD4601"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Lighting Demo Board", "description": "", "id": "brd7500a:0.0.0", "label": "Lighting Demo Board (BRD7500A)", "type": "Board", "opn": "BRD7500"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Lighting Module Board", "description": "", "id": "brd7501a:0.0.0", "label": "Lighting Module Board (BRD7501A)", "type": "Board", "opn": "BRD7501"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Blue Gecko Module Kit Add-on Board", "description": "", "id": "brd8006a:0.0.0", "label": "Blue Gecko Module Kit Add-on Board (BRD8006A)", "type": "Board", "opn": "BRD8006"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "STK Debug Adapter Board", "description": "", "id": "brd8010a:0.0.0.A02", "label": "STK Debug Adapter Board (BRD8010A Rev A02)", "type": "Board", "opn": "BRD8010"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Wireless Expansion Board", "description": "", "id": "brd8016a:0.0.0.A01", "label": "Wireless Expansion Board (BRD8016A Rev A01)", "type": "Board", "opn": "BRD8016"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wf.wf200sc"], "imageURL": "", "name": "WF200 EXP Board", "description": "", "id": "brd8022a:0.0.0", "label": "WF200 EXP Board (BRD8022A)", "type": "Board", "opn": "BRD8022"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.wfm200ss22xna2"], "imageURL": "", "name": "WFM200S SiP Module EXP Board", "description": "", "id": "brd8023a:0.0.0", "label": "WFM200S SiP Module EXP Board (BRD8023A)", "type": "Board", "opn": "BRD8023"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "RMII Ethernet EXP Board", "description": "", "id": "brd8026a:0.0.0.A01", "label": "RMII Ethernet EXP Board (BRD8026A Rev A01)", "type": "Board", "opn": "BRD8026"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Buttons and LEDs EXP Board", "description": "", "id": "brd8029a:0.0.0.A00", "label": "Buttons and LEDs EXP Board (BRD8029A Rev A00)", "type": "Board", "opn": "BRD8029"}, {"rev": "A00", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "RS9116 Single Band EXP Board", "description": "", "id": "brd8035a:0.0.0.A00", "label": "RS9116 Single Band EXP Board (BRD8035A Rev A00)", "type": "Board", "opn": "BRD8035"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.wfm.si917-sb00"], "imageURL": "", "name": "Si917 Single Band EXP Board", "description": "", "id": "brd8036a:0.0.0", "label": "Si917 Single Band EXP Board (BRD8036A)", "type": "Board", "opn": "BRD8036"}, {"rev": "A01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "RS9116 AC1 EXP Board", "description": "", "id": "brd8037b:0.0.0.A01", "label": "RS9116 AC1 EXP Board (BRD8037B Rev A01)", "type": "Board", "opn": "BRD8037"}, {"rev": "A02", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Adapter Board for SX1262MB2CAS", "description": "", "id": "brd8042a:0.0.0.A02", "label": "Adapter Board for SX1262MB2CAS (BRD8042A Rev A02)", "type": "Board", "opn": "BRD8042"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "EXP Adapter Board for Co-Processor", "description": "", "id": "brd8045a:0.0.0", "label": "EXP Adapter Board for Co-Processor (BRD8045A)", "type": "Board", "opn": "BRD8045"}, {"rev": "A06", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "EXP Adapter Board for Co-Processor", "description": "", "id": "brd8045a:0.0.0.A06", "label": "EXP Adapter Board for Co-Processor (BRD8045A Rev A06)", "type": "Board", "opn": "BRD8045"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["efp.efp01.efp0108_g"], "imageURL": "", "name": "EFP0108 Reference Design Board", "description": "", "id": "brd8100a:0.0.0", "label": "EFP0108 Reference Design Board (BRD8100A)", "type": "Board", "opn": "BRD8100"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["efp.efp01.efp0111_g"], "imageURL": "", "name": "EFP0111 Reference Design Board", "description": "", "id": "brd8100b:0.0.0", "label": "EFP0111 Reference Design Board (BRD8100B)", "type": "Board", "opn": "BRD8100"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "com.silabs.board.none", "description": "This entry allows any part to be used.  Otherwise, only parts supported by a board are allowed.", "id": "com.silabs.board.none:0.0.0", "label": "Custom Board", "type": "Board"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em357"], "imageURL": "", "name": "Telegesis/Silicon Labs EM357 +8dBm module, antenna", "description": "", "id": "etrx357:0.0.0", "label": "Telegesis/Silicon Labs EM357 +8dBm module, antenna (etrx357)", "type": "Board", "opn": "etrx357"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em357"], "imageURL": "", "name": "Telegesis/Silicon Labs EM357 +20dBm module, RF connector", "description": "", "id": "etrx357hr-lrs:0.0.0", "label": "Telegesis/Silicon Labs EM357 +20dBm module, RF connector (etrx357hr-lrs)", "type": "Board", "opn": "etrx357hr-lrs"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em357"], "imageURL": "", "name": "Telegesis/Silicon Labs EM357 +8dBm module, RF connector", "description": "", "id": "etrx357hr:0.0.0", "label": "Telegesis/Silicon Labs EM357 +8dBm module, RF connector (etrx357hr)", "type": "Board", "opn": "etrx357hr"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em357"], "imageURL": "", "name": "Telegesis/Silicon Labs EM357 +8dBm module, antenna", "description": "", "id": "etrx357lrs:0.0.0", "label": "Telegesis/Silicon Labs EM357 +8dBm module, antenna (etrx357lrs)", "type": "Board", "opn": "etrx357lrs"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "zigbee Wi-Fi/Ethernet Gateway Reference Design", "description": "", "id": "rd-0001-0201:0.0.0", "label": "zigbee Wi-Fi/Ethernet Gateway Reference Design (RD-0001-0201)", "type": "Board", "opn": "RD-0001-"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "zigbee USB Virtual Gateway Reference Design", "description": "", "id": "rd-0002-0201:0.0.0", "label": "zigbee USB Virtual Gateway Reference Design (RD-0002-0201)", "type": "Board", "opn": "RD-0002-"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": [], "imageURL": "", "name": "Thread Border Router Reference Design", "description": "", "id": "rd-0004-0201:0.0.0", "label": "Thread Border Router Reference Design (RD-0004-0201)", "type": "Board", "opn": "RD-0004-"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em3587"], "imageURL": "", "name": "Contact Sensor EM3587", "description": "", "id": "rd-0030-0101:0.0.0", "label": "Contact Sensor EM3587 (RD-0030-0101)", "type": "Board", "opn": "RD-0030-"}, {"rev": "01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em3585"], "imageURL": "", "name": "Lighting EM3585", "description": "", "id": "rd-003506:0.0.0.01", "label": "Lighting EM3585 (RD-0035-0501)", "type": "Board", "opn": "RD-0035"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em3587"], "imageURL": "", "name": "Dimmer Switch EM3587", "description": "", "id": "rd-0039-0101:0.0.0", "label": "Dimmer Switch EM3587 (RD-0039-0101)", "type": "Board", "opn": "RD-0039-"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg1.efr32mg1p232f256gm48"], "imageURL": "", "name": "Smart Outlet EFR32MG1P", "description": "", "id": "rd-0051-0101:0.0.0", "label": "Smart Outlet EFR32MG1P (RD-0051-0101)", "type": "Board", "opn": "RD-0051-"}, {"rev": "01", "docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.bg1.efr32bg1b232f256gm48"], "imageURL": "", "name": "ThunderBoard React", "description": "", "id": "rd-005701:0.0.0.01", "label": "ThunderBoard React (RD-005701 Rev 01)", "type": "Board", "opn": "RD-0057"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg1.efr32mg1p232f256gm48"], "imageURL": "", "name": "Occupancy Sensor EFR32MG1P", "description": "", "id": "rd-0078-0101:0.0.0", "label": "Occupancy Sensor EFR32MG1P (RD-0078-0101)", "type": "Board", "opn": "RD-0078-"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg1.efr32mg1p732f256im32"], "imageURL": "", "name": "Lighting EFR32MG1P", "description": "", "id": "rd-0085-0201:0.0.0", "label": "Lighting EFR32MG1P (RD-0085-0201)", "type": "Board", "opn": "RD-0085-"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p432f1024im48"], "imageURL": "", "name": "Lighting EFR32MG12P", "description": "", "id": "rd-0098-0401:0.0.0", "label": "Lighting EFR32MG12P (RD-0098-0401)", "type": "Board", "opn": "RD-0098-"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p432f1024gm48"], "imageURL": "", "name": "Occupancy Sensor EFR32MG12P", "description": "", "id": "rd-0099-0201:0.0.0", "label": "Occupancy Sensor EFR32MG12P (RD-0099-0201)", "type": "Board", "opn": "RD-0099-"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efr32.mg12.efr32mg12p432f1024gm48"], "imageURL": "", "name": "Smart Outlet EFR32MG12P", "description": "", "id": "rd-0100-0201:0.0.0", "label": "Smart Outlet EFR32MG12P (RD-0100-0201)", "type": "Board", "opn": "RD-0100-"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["ffd.rs.rs9116w.rs9116w-db00-cc0"], "imageURL": "", "name": "RS9116 Wireless Dual Band Evaluation Board", "description": "", "id": "rs9116x-db-evk1:0.0.0", "label": "RS9116 Wireless Dual Band Evaluation Board (RS9116X-DB-EVK1)", "type": "Board", "opn": "RS9116X-DB-EVK1"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.efm32.g.efm32g210f128"], "imageURL": "", "name": "Environmental and Biometric Sensor Puck", "description": "", "id": "slsensorpuck.efm32g:0.0.0", "label": "Environmental and Biometric Sensor Puck (slsensorpuck.efm32g)", "type": "Board", "opn": "slsensorpuck.efm32g"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em34x.em341"], "imageURL": "", "name": "CEL EM341 +8dBm module on daughtercard, trace antenna", "description": "", "id": "zicm341sp0-1:0.0.0", "label": "CEL EM341 +8dBm module on daughtercard, trace antenna (zicm341sp0-1)", "type": "Board", "opn": "zicm341sp0-1"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em34x.em341"], "imageURL": "", "name": "CEL EM341 +8dBm module on daughtercard, RF connector", "description": "", "id": "zicm341sp0-1c:0.0.0", "label": "CEL EM341 +8dBm module on daughtercard, RF connector (zicm341sp0-1c)", "type": "Board", "opn": "zicm341sp0-1c"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em34x.em346"], "imageURL": "", "name": "CEL EM346 +8dBm module on daughtercard, trace antenna", "description": "", "id": "zicm346sp0-1:0.0.0", "label": "CEL EM346 +8dBm module on daughtercard, trace antenna (zicm346sp0-1)", "type": "Board", "opn": "zicm346sp0-1"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em34x.em346"], "imageURL": "", "name": "CEL EM346 +8dBm module on daughtercard, RF connector", "description": "", "id": "zicm346sp0-1c:0.0.0", "label": "CEL EM346 +8dBm module on daughtercard, RF connector (zicm346sp0-1c)", "type": "Board", "opn": "zicm346sp0-1c"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em357"], "imageURL": "", "name": "CEL EM357 +8dBm module on daughtercard, trace antenna", "description": "", "id": "zicm357sp0-1:0.0.0", "label": "CEL EM357 +8dBm module on daughtercard, trace antenna (zicm357sp0-1)", "type": "Board", "opn": "zicm357sp0-1"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em357"], "imageURL": "", "name": "CEL EM357 +8dBm module on daughtercard, RF connector", "description": "", "id": "zicm357sp0-1c:0.0.0", "label": "CEL EM357 +8dBm module on daughtercard, RF connector (zicm357sp0-1c)", "type": "Board", "opn": "zicm357sp0-1c"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em357"], "imageURL": "", "name": "CEL EM357 +20dBm module on daughtercard, trace antenna", "description": "", "id": "zicm357sp2-1:0.0.0", "label": "CEL EM357 +20dBm module on daughtercard, trace antenna (zicm357sp2-1)", "type": "Board", "opn": "zicm357sp2-1"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em357"], "imageURL": "", "name": "CEL EM357 +20dBm module on daughtercard, RF connector", "description": "", "id": "zicm357sp2-1c:0.0.0", "label": "CEL EM357 +20dBm module on daughtercard, RF connector (zicm357sp2-1c)", "type": "Board", "opn": "zicm357sp2-1c"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em3588"], "imageURL": "", "name": "CEL EM3588 +8dBm module on daughtercard, trace antenna", "description": "", "id": "zicm3588sp0-1:0.0.0", "label": "CEL EM3588 +8dBm module on daughtercard, trace antenna (zicm3588sp0-1)", "type": "Board", "opn": "zicm3588sp0-1"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em3588"], "imageURL": "", "name": "CEL EM3588 +8dBm module on daughtercard, RF connector", "description": "", "id": "zicm3588sp0-1c:0.0.0", "label": "CEL EM3588 +8dBm module on daughtercard, RF connector (zicm3588sp0-1c)", "type": "Board", "opn": "zicm3588sp0-1c"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em3588"], "imageURL": "", "name": "CEL EM3588 +20dBm module on daughtercard, trace antenna", "description": "", "id": "zicm3588sp2-1:0.0.0", "label": "CEL EM3588 +20dBm module on daughtercard, trace antenna (zicm3588sp2-1)", "type": "Board", "opn": "zicm3588sp2-1"}, {"docs": [], "simpleName": "BoardDescriptor", "allowedParts": ["mcu.arm.ember.em35x.em3588"], "imageURL": "", "name": "CEL EM3588 +20dBm module on daughtercard, RF connector", "description": "", "id": "zicm3588sp2-1c:0.0.0", "label": "CEL EM3588 +20dBm module on daughtercard, RF connector (zicm3588sp2-1c)", "type": "Board", "opn": "zicm3588sp2-1c"}]