{"html": "\n   <article>\n    <div class=\"header\">\n     <div class=\"headertitle\">\n      <h1 class=\"title\">\n       I2C - Inter-Integrated Circuit\n      </h1>\n     </div>\n    </div>\n    <div class=\"contents\">\n     <a id=\"details\" name=\"details\">\n     </a>\n     <h2 class=\"groupheader\">\n      Description\n     </h2>\n     <p>\n      Inter-integrated Circuit (I2C) Peripheral API.\n     </p>\n     <p>\n      This module contains functions to control the I2C peripheral of Silicon Labs 32-bit MCUs and SoCs. The I2C interface allows communication on I2C buses with the lowest energy consumption possible.\n     </p>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"nested-classes\">\n          </a>\n          Data Structures\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i2-c-init-type-def\" target=\"_blank\">\n          I2C_Init_TypeDef\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         I2C initialization structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i2-c-transfer-seq-type-def\" target=\"_blank\">\n          I2C_TransferSeq_TypeDef\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Master mode transfer message structure used to define a complete I2C transfer sequence (from start to stop).\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"func-members\">\n          </a>\n          Functions\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gaf2563ff04d4b24825a6c67f9ec4dcb80\">\n          I2C_BusFreqGet\n         </a>\n         (I2C_TypeDef *i2c)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get the current configured I2C bus frequency.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga02fbf32736b2e6c756399ceeba9563a1\">\n          I2C_BusFreqSet\n         </a>\n         (I2C_TypeDef *i2c, uint32_t freqRef, uint32_t freqScl,\n         <a class=\"el\" href=\"#gabb1516548b4528328682d6be09a3e3a5\">\n          I2C_ClockHLR_TypeDef\n         </a>\n         i2cMode)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Set the I2C bus frequency.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gaca1c9ca46d6945a4696f8a34ab8c2ddf\">\n          I2C_Enable\n         </a>\n         (I2C_TypeDef *i2c, bool enable)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Enable/disable I2C.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gab52e0fc0d3690e40f80a193d5b6fdd51\">\n          I2C_Init\n         </a>\n         (I2C_TypeDef *i2c, const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i2-c-init-type-def\" target=\"_blank\">\n          I2C_Init_TypeDef\n         </a>\n         *init)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Initialize I2C.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga6cba16ae44e25e0e9f85e7fdf9f09956\">\n          I2C_IntClear\n         </a>\n         (I2C_TypeDef *i2c, uint32_t flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Clear one or more pending I2C interrupts.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gae2f663bf8e0a7dfe3f612ad9ed409260\">\n          I2C_IntDisable\n         </a>\n         (I2C_TypeDef *i2c, uint32_t flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Disable one or more I2C interrupts.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga4e7ec23adf30a399590113d8b98ec4f1\">\n          I2C_IntEnable\n         </a>\n         (I2C_TypeDef *i2c, uint32_t flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Enable one or more I2C interrupts.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gad969f558d7a35d5235dd174c9eb3aed0\">\n          I2C_IntGet\n         </a>\n         (I2C_TypeDef *i2c)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get pending I2C interrupt flags.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint32_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gab808cebf4e4caca28c4cf59b4d596b32\">\n          I2C_IntGetEnabled\n         </a>\n         (I2C_TypeDef *i2c)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get enabled and pending I2C interrupt flags.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gab76ed891da5ae8e6736d22a9570cb4a5\">\n          I2C_IntSet\n         </a>\n         (I2C_TypeDef *i2c, uint32_t flags)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Set one or more pending I2C interrupts from SW.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga602a1d2c64c05453bf9c1b1d52194678\">\n          I2C_Reset\n         </a>\n         (I2C_TypeDef *i2c)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Reset I2C to the same state that it was in after a hardware reset.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint8_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gab7373d2618562755e7f57753c824417e\">\n          I2C_SlaveAddressGet\n         </a>\n         (I2C_TypeDef *i2c)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get Target address used for I2C peripheral (when operating in Target mode).\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gaf6c8d330247b9da600468307689f7df0\">\n          I2C_SlaveAddressSet\n         </a>\n         (I2C_TypeDef *i2c, uint8_t addr)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Set Target address to use for I2C peripheral (when operating in Target mode).\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         uint8_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gacb6d14f920b35944800b65b71347428c\">\n          I2C_SlaveAddressMaskGet\n         </a>\n         (I2C_TypeDef *i2c)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get Target address mask used for I2C peripheral (when operating in Target mode).\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga386dbe1bb286a066925936d131f255ec\">\n          I2C_SlaveAddressMaskSet\n         </a>\n         (I2C_TypeDef *i2c, uint8_t mask)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Set Target address mask used for I2C peripheral (when operating in Target mode).\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"#ga7c781ec28ae11e3e28892de7aa07a00f\">\n          I2C_TransferReturn_TypeDef\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga67983b3dbe7f028a97f765e421b3df56\">\n          I2C_Transfer\n         </a>\n         (I2C_TypeDef *i2c)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Continue an initiated I2C transfer (single master mode only).\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"#ga7c781ec28ae11e3e28892de7aa07a00f\">\n          I2C_TransferReturn_TypeDef\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gae2bf2ecc3d0c4f55e3511d4871b64b16\">\n          I2C_TransferInit\n         </a>\n         (I2C_TypeDef *i2c,\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i2-c-transfer-seq-type-def\" target=\"_blank\">\n          I2C_TransferSeq_TypeDef\n         </a>\n         *seq)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Prepare and start an I2C transfer (single master mode only).\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"define-members\">\n          </a>\n          Macros\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga0a2167eb1e00a8da86bf0826f712f0f8\">\n          I2C_FREQ_STANDARD_MAX\n         </a>\n         100000\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Standard mode max frequency assuming using 4:4 ratio for Nlow:Nhigh.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga29a3fbc14b30b68601118a9866e7440d\">\n          I2C_FREQ_FAST_MAX\n         </a>\n         392157\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Fast mode max frequency assuming using 6:3 ratio for Nlow:Nhigh.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga6961334be5d2f1d6e98d8f48de3b040e\">\n          I2C_FREQ_FASTPLUS_MAX\n         </a>\n         987167\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Fast mode+ max frequency assuming using 11:6 ratio for Nlow:Nhigh.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga091476f45133e8a6deb9dbd7a3ea6229\">\n          I2C_FLAG_WRITE\n         </a>\n         0x0001\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Indicate plain write sequence: S+ADDR(W)+DATA0+P.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga0c1779663a5ebf44e956ea3f591b4e61\">\n          I2C_FLAG_READ\n         </a>\n         0x0002\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Indicate plain read sequence: S+ADDR(R)+DATA0+P.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gaf9fc47d6ee1c66405dc491f7a4c3b8fc\">\n          I2C_FLAG_WRITE_READ\n         </a>\n         0x0004\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Indicate combined write/read sequence: S+ADDR(W)+DATA0+Sr+ADDR(R)+DATA1+P.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga28bb5f9d0559f3af24c0554e497ba2b3\">\n          I2C_FLAG_WRITE_WRITE\n         </a>\n         0x0008\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Indicate write sequence using two buffers: S+ADDR(W)+DATA0+DATA1+P.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga6fd13e29a6b6315de051549037ac176f\">\n          I2C_FLAG_10BIT_ADDR\n         </a>\n         0x0010\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Use 10 bit address.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga5c7fbd27ef311914288d8e7ce02f1528\">\n          I2C_INIT_DEFAULT\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Suggested default configuration for I2C initialization structure.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"enum-members\">\n          </a>\n          Enumerations\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#gabb1516548b4528328682d6be09a3e3a5\">\n          I2C_ClockHLR_TypeDef\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#ggabb1516548b4528328682d6be09a3e3a5a72c81018da3424addf47b44341acd111\">\n          i2cClockHLRStandard\n         </a>\n         = _I2C_CTRL_CLHR_STANDARD,\n         <br>\n         <a class=\"el\" href=\"#ggabb1516548b4528328682d6be09a3e3a5a7bfc41c09113d46c1247b598c80a3b2e\">\n          i2cClockHLRAsymetric\n         </a>\n         = _I2C_CTRL_CLHR_ASYMMETRIC,\n         <br>\n         <a class=\"el\" href=\"#ggabb1516548b4528328682d6be09a3e3a5a177d1faa325c634cbf56d2a4b4de9425\">\n          i2cClockHLRFast\n         </a>\n         = _I2C_CTRL_CLHR_FAST\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Clock low to high ratio settings.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         enum\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"#ga7c781ec28ae11e3e28892de7aa07a00f\">\n          I2C_TransferReturn_TypeDef\n         </a>\n         {\n         <br>\n         <a class=\"el\" href=\"#gga7c781ec28ae11e3e28892de7aa07a00fa1f1df5a76a71b423083d6a327d38ca1c\">\n          i2cTransferInProgress\n         </a>\n         = 1,\n         <br>\n         <a class=\"el\" href=\"#gga7c781ec28ae11e3e28892de7aa07a00fa01149f9a11ada8a9b05e7104f873de66\">\n          i2cTransferDone\n         </a>\n         = 0,\n         <br>\n         <a class=\"el\" href=\"#gga7c781ec28ae11e3e28892de7aa07a00fa54895c4189c89f4410a3ce1a15822725\">\n          i2cTransferNack\n         </a>\n         = -1,\n         <br>\n         <a class=\"el\" href=\"#gga7c781ec28ae11e3e28892de7aa07a00fa75da099dd69e053ee12d38fdd76a22e6\">\n          i2cTransferBusErr\n         </a>\n         = -2,\n         <br>\n         <a class=\"el\" href=\"#gga7c781ec28ae11e3e28892de7aa07a00fac15d6d57e2c8b0fca04fc6fbd80b824e\">\n          i2cTransferArbLost\n         </a>\n         = -3,\n         <br>\n         <a class=\"el\" href=\"#gga7c781ec28ae11e3e28892de7aa07a00fa49fe8ab19b9167d78c4b2abec7a511fc\">\n          i2cTransferUsageFault\n         </a>\n         = -4,\n         <br>\n         <a class=\"el\" href=\"#gga7c781ec28ae11e3e28892de7aa07a00faa5de823251279b281dcabbd1ec4f693d\">\n          i2cTransferSwFault\n         </a>\n         = -5\n         <br>\n         }\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Return codes for single Controller mode transfer function.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <h2 class=\"groupheader\">\n      Function Documentation\n     </h2>\n     <a id=\"gaf2563ff04d4b24825a6c67f9ec4dcb80\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf2563ff04d4b24825a6c67f9ec4dcb80\">\n        ◆\n       </a>\n      </span>\n      I2C_BusFreqGet()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           uint32_t I2C_BusFreqGet\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           I2C_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            i2c\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get the current configured I2C bus frequency.\n       </p>\n       <p>\n        This frequency is only relevant when acting as master.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         The actual frequency is a real number, this function returns a rounded down (truncated) integer value.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             A pointer to the I2C peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         The current I2C frequency in Hz.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga02fbf32736b2e6c756399ceeba9563a1\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga02fbf32736b2e6c756399ceeba9563a1\">\n        ◆\n       </a>\n      </span>\n      I2C_BusFreqSet()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void I2C_BusFreqSet\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           I2C_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            i2c,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            freqRef,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           uint32_t\n          </td>\n          <td class=\"paramname\">\n           <code>\n            freqScl,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"#gabb1516548b4528328682d6be09a3e3a5\">\n            I2C_ClockHLR_TypeDef\n           </a>\n          </td>\n          <td class=\"paramname\">\n           <code>\n            i2cMode\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Set the I2C bus frequency.\n       </p>\n       <p>\n        The bus frequency is only relevant when acting as master. The bus frequency should not be set higher than the maximum frequency accepted by the slowest device on the bus.\n       </p>\n       <p>\n        Notice that, due to asymmetric requirements on low and high I2C clock cycles in the I2C specification, the maximum frequency allowed to comply with the specification may be somewhat lower than expected.\n       </p>\n       <p>\n        See the reference manual, details on I2C clock generation, for maximum allowed theoretical frequencies for different modes.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             A pointer to the I2C peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              freqRef\n             </code>\n            </td>\n            <td>\n             An I2C reference clock frequency in Hz that will be used. If set to 0, HFPERCLK / HFPERCCLK clock is used. Setting it to a higher than actual configured value has the consequence of reducing the real I2C frequency.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              freqScl\n             </code>\n            </td>\n            <td>\n             A bus frequency to set (bus speed may be lower due to integer prescaling). Safe (according to the I2C specification) maximum frequencies for standard fast and fast+ modes are available using I2C_FREQ_ defines. (Using I2C_FREQ_ defines requires corresponding setting of\n             <code>\n              type\n             </code>\n             .) The slowest slave device on a bus must always be considered.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2cMode\n             </code>\n            </td>\n            <td>\n             A clock low-to-high ratio type to use. If not using i2cClockHLRStandard, make sure all devices on the bus support the specified mode. Using a non-standard ratio is useful to achieve a higher bus clock in fast and fast+ modes.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaca1c9ca46d6945a4696f8a34ab8c2ddf\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaca1c9ca46d6945a4696f8a34ab8c2ddf\">\n        ◆\n       </a>\n      </span>\n      I2C_Enable()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void I2C_Enable\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           I2C_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            i2c,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           bool\n          </td>\n          <td class=\"paramname\">\n           <code>\n            enable\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Enable/disable I2C.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         After enabling the I2C (from being disabled), the I2C is in BUSY state.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             A pointer to the I2C peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              enable\n             </code>\n            </td>\n            <td>\n             True to enable counting, false to disable.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gab52e0fc0d3690e40f80a193d5b6fdd51\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gab52e0fc0d3690e40f80a193d5b6fdd51\">\n        ◆\n       </a>\n      </span>\n      I2C_Init()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void I2C_Init\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           I2C_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            i2c,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i2-c-init-type-def\" target=\"_blank\">\n            I2C_Init_TypeDef\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            init\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Initialize I2C.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             A pointer to the I2C peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              init\n             </code>\n            </td>\n            <td>\n             A pointer to the I2C initialization structure.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga6cba16ae44e25e0e9f85e7fdf9f09956\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga6cba16ae44e25e0e9f85e7fdf9f09956\">\n        ◆\n       </a>\n      </span>\n      I2C_IntClear()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void I2C_IntClear\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               I2C_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                i2c,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Clear one or more pending I2C interrupts.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             Pointer to I2C peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              flags\n             </code>\n            </td>\n            <td>\n             Pending I2C interrupt source to clear. Use a bitwise logic OR combination of valid interrupt flags for the I2C module (I2C_IF_nnn).\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gae2f663bf8e0a7dfe3f612ad9ed409260\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gae2f663bf8e0a7dfe3f612ad9ed409260\">\n        ◆\n       </a>\n      </span>\n      I2C_IntDisable()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void I2C_IntDisable\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               I2C_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                i2c,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Disable one or more I2C interrupts.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             Pointer to I2C peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              flags\n             </code>\n            </td>\n            <td>\n             I2C interrupt sources to disable. Use a bitwise logic OR combination of valid interrupt flags for the I2C module (I2C_IF_nnn).\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga4e7ec23adf30a399590113d8b98ec4f1\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga4e7ec23adf30a399590113d8b98ec4f1\">\n        ◆\n       </a>\n      </span>\n      I2C_IntEnable()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void I2C_IntEnable\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               I2C_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                i2c,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Enable one or more I2C interrupts.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Depending on the use, a pending interrupt may already be set prior to enabling the interrupt. To ignore a pending interrupt, consider using\n         <a class=\"el\" href=\"#ga6cba16ae44e25e0e9f85e7fdf9f09956\" title=\"Clear one or more pending I2C interrupts.\">\n          I2C_IntClear()\n         </a>\n         prior to enabling the interrupt.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             Pointer to I2C peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              flags\n             </code>\n            </td>\n            <td>\n             I2C interrupt sources to enable. Use a bitwise logic OR combination of valid interrupt flags for the I2C module (I2C_IF_nnn).\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gad969f558d7a35d5235dd174c9eb3aed0\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gad969f558d7a35d5235dd174c9eb3aed0\">\n        ◆\n       </a>\n      </span>\n      I2C_IntGet()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint32_t I2C_IntGet\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               I2C_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                i2c\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get pending I2C interrupt flags.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Event bits are not cleared by the use of this function.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             Pointer to I2C peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         I2C interrupt sources pending. A bitwise logic OR combination of valid interrupt flags for the I2C module (I2C_IF_nnn).\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gab808cebf4e4caca28c4cf59b4d596b32\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gab808cebf4e4caca28c4cf59b4d596b32\">\n        ◆\n       </a>\n      </span>\n      I2C_IntGetEnabled()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint32_t I2C_IntGetEnabled\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               I2C_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                i2c\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get enabled and pending I2C interrupt flags.\n       </p>\n       <p>\n        Useful for handling more interrupt sources in the same interrupt handler.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Interrupt flags are not cleared by the use of this function.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             Pointer to I2C peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Pending and enabled I2C interrupt sources Return value is the bitwise AND of\n         <ul>\n          <li>\n           the enabled interrupt sources in I2Cn_IEN and\n          </li>\n          <li>\n           the pending interrupt flags I2Cn_IF\n          </li>\n         </ul>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gab76ed891da5ae8e6736d22a9570cb4a5\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gab76ed891da5ae8e6736d22a9570cb4a5\">\n        ◆\n       </a>\n      </span>\n      I2C_IntSet()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void I2C_IntSet\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               I2C_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                i2c,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint32_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                flags\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Set one or more pending I2C interrupts from SW.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             Pointer to I2C peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              flags\n             </code>\n            </td>\n            <td>\n             I2C interrupt sources to set to pending. Use a bitwise logic OR combination of valid interrupt flags for the I2C module (I2C_IF_nnn).\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga602a1d2c64c05453bf9c1b1d52194678\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga602a1d2c64c05453bf9c1b1d52194678\">\n        ◆\n       </a>\n      </span>\n      I2C_Reset()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void I2C_Reset\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           I2C_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            i2c\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Reset I2C to the same state that it was in after a hardware reset.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         The ROUTE register is NOT reset by this function to allow for centralized setup of this feature.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             A pointer to the I2C peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gab7373d2618562755e7f57753c824417e\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gab7373d2618562755e7f57753c824417e\">\n        ◆\n       </a>\n      </span>\n      I2C_SlaveAddressGet()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint8_t I2C_SlaveAddressGet\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               I2C_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                i2c\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get Target address used for I2C peripheral (when operating in Target mode).\n       </p>\n       <p>\n        For 10-bit addressing mode, the address is split in two bytes, and only the first byte setting is fetched, effectively only controlling the 2 most significant bits of the 10-bit address. Full handling of 10-bit addressing in Target mode requires additional SW handling.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             Pointer to I2C peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         I2C Target address in use. The 7 most significant bits define the actual address, the least significant bit is reserved and always returned as 0.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaf6c8d330247b9da600468307689f7df0\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf6c8d330247b9da600468307689f7df0\">\n        ◆\n       </a>\n      </span>\n      I2C_SlaveAddressSet()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void I2C_SlaveAddressSet\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               I2C_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                i2c,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint8_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                addr\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Set Target address to use for I2C peripheral (when operating in Target mode).\n       </p>\n       <p>\n        For 10- bit addressing mode, the address is split in two bytes, and only the first byte is set, effectively only controlling the 2 most significant bits of the 10-bit address. Full handling of 10-bit addressing in Target mode requires additional SW handling.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             Pointer to I2C peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              addr\n             </code>\n            </td>\n            <td>\n             I2C Target address to use. The 7 most significant bits define the actual address, the least significant bit is reserved and always set to 0.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gacb6d14f920b35944800b65b71347428c\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gacb6d14f920b35944800b65b71347428c\">\n        ◆\n       </a>\n      </span>\n      I2C_SlaveAddressMaskGet()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               uint8_t I2C_SlaveAddressMaskGet\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               I2C_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                i2c\n               </code>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get Target address mask used for I2C peripheral (when operating in Target mode).\n       </p>\n       <p>\n        The address mask defines how the comparator works. A bit position with value 0 means that the corresponding Target address bit is ignored during comparison (don't care). A bit position with value 1 means that the corresponding Target address bit must match.\n       </p>\n       <p>\n        For 10-bit addressing mode, the address is split in two bytes, and only the mask for the first address byte is fetched, effectively only controlling the 2 most significant bits of the 10-bit address.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             Pointer to I2C peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         I2C Target address mask in use. The 7 most significant bits define the actual address mask, the least significant bit is reserved and always returned as 0.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga386dbe1bb286a066925936d131f255ec\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga386dbe1bb286a066925936d131f255ec\">\n        ◆\n       </a>\n      </span>\n      I2C_SlaveAddressMaskSet()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"mlabels\">\n        <tbody>\n         <tr>\n          <td class=\"mlabels-left\">\n           <table class=\"memname\">\n            <tbody>\n             <tr>\n              <td class=\"memname\">\n               void I2C_SlaveAddressMaskSet\n              </td>\n              <td>\n               (\n              </td>\n              <td class=\"paramtype\">\n               I2C_TypeDef *\n              </td>\n              <td class=\"paramname\">\n               <code>\n                i2c,\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td class=\"paramkey\">\n              </td>\n              <td>\n              </td>\n              <td class=\"paramtype\">\n               uint8_t\n              </td>\n              <td class=\"paramname\">\n               <code>\n                mask\n               </code>\n              </td>\n             </tr>\n             <tr>\n              <td>\n              </td>\n              <td>\n               )\n              </td>\n              <td>\n              </td>\n              <td>\n              </td>\n             </tr>\n            </tbody>\n           </table>\n          </td>\n          <td class=\"mlabels-right\">\n           <span class=\"mlabels\">\n            <span class=\"mlabel\">\n             inline\n            </span>\n           </span>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Set Target address mask used for I2C peripheral (when operating in Target mode).\n       </p>\n       <p>\n        The address mask defines how the comparator works. A bit position with value 0 means that the corresponding Target address bit is ignored during comparison (don't care). A bit position with value 1 means that the corresponding Target address bit must match.\n       </p>\n       <p>\n        For 10-bit addressing mode, the address is split in two bytes, and only the mask for the first address byte is set, effectively only controlling the 2 most significant bits of the 10-bit address.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             Pointer to I2C peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              mask\n             </code>\n            </td>\n            <td>\n             I2C Target address mask to use. The 7 most significant bits define the actual address mask, the least significant bit is reserved and should be 0.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga67983b3dbe7f028a97f765e421b3df56\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga67983b3dbe7f028a97f765e421b3df56\">\n        ◆\n       </a>\n      </span>\n      I2C_Transfer()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           <a class=\"el\" href=\"#ga7c781ec28ae11e3e28892de7aa07a00f\">\n            I2C_TransferReturn_TypeDef\n           </a>\n           I2C_Transfer\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           I2C_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            i2c\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Continue an initiated I2C transfer (single master mode only).\n       </p>\n       <p>\n        This function is used repeatedly after a\n        <a class=\"el\" href=\"#gae2bf2ecc3d0c4f55e3511d4871b64b16\" title=\"Prepare and start an I2C transfer (single master mode only).\">\n         I2C_TransferInit()\n        </a>\n        to complete a transfer. It may be used in polled mode as the below example shows:\n       </p>\n       <div class=\"fragment\">\n        <div class=\"line\">\n         <a class=\"code\" href=\"#ga7c781ec28ae11e3e28892de7aa07a00f\">\n          I2C_TransferReturn_TypeDef\n         </a>\n         ret;\n        </div>\n        <div class=\"line\">\n        </div>\n        <div class=\"line\">\n         <span class=\"comment\">\n          // Do a polled transfer\n         </span>\n        </div>\n        <div class=\"line\">\n         ret =\n         <a class=\"code\" href=\"#gae2bf2ecc3d0c4f55e3511d4871b64b16\">\n          I2C_TransferInit\n         </a>\n         (I2C0, seq);\n        </div>\n        <div class=\"line\">\n         <span class=\"keywordflow\">\n          while\n         </span>\n         (ret ==\n         <a class=\"code\" href=\"#gga7c781ec28ae11e3e28892de7aa07a00fa1f1df5a76a71b423083d6a327d38ca1c\">\n          i2cTransferInProgress\n         </a>\n         )\n        </div>\n        <div class=\"line\">\n         {\n        </div>\n        <div class=\"line\">\n         ret =\n         <a class=\"code\" href=\"#ga67983b3dbe7f028a97f765e421b3df56\">\n          I2C_Transfer\n         </a>\n         (I2C0);\n        </div>\n        <div class=\"line\">\n         }\n        </div>\n       </div>\n       <p>\n        It may also be used in interrupt driven mode, where this function is invoked from the interrupt handler. Notice that, if used in interrupt mode, NVIC interrupts must be configured and enabled for the I2C bus used. I2C peripheral specific interrupts are managed by this software.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Only single master mode is supported.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             A pointer to the I2C peripheral register block.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Returns status for an ongoing transfer.\n         <ul>\n          <li>\n           <a class=\"el\" href=\"#gga7c781ec28ae11e3e28892de7aa07a00fa1f1df5a76a71b423083d6a327d38ca1c\" title=\"Transfer in progress.\">\n            i2cTransferInProgress\n           </a>\n           - indicates that transfer not finished.\n          </li>\n          <li>\n           <a class=\"el\" href=\"#gga7c781ec28ae11e3e28892de7aa07a00fa01149f9a11ada8a9b05e7104f873de66\" title=\"Transfer completed successfully.\">\n            i2cTransferDone\n           </a>\n           - transfer completed successfully.\n          </li>\n          <li>\n           otherwise some sort of error has occurred.\n          </li>\n         </ul>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gae2bf2ecc3d0c4f55e3511d4871b64b16\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gae2bf2ecc3d0c4f55e3511d4871b64b16\">\n        ◆\n       </a>\n      </span>\n      I2C_TransferInit()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           <a class=\"el\" href=\"#ga7c781ec28ae11e3e28892de7aa07a00f\">\n            I2C_TransferReturn_TypeDef\n           </a>\n           I2C_TransferInit\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           I2C_TypeDef *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            i2c,\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"paramkey\">\n          </td>\n          <td>\n          </td>\n          <td class=\"paramtype\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/struct-i2-c-transfer-seq-type-def\" target=\"_blank\">\n            I2C_TransferSeq_TypeDef\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            seq\n           </code>\n          </td>\n         </tr>\n         <tr>\n          <td>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Prepare and start an I2C transfer (single master mode only).\n       </p>\n       <p>\n        This function must be invoked to start an I2C transfer sequence. To complete the transfer,\n        <a class=\"el\" href=\"#ga67983b3dbe7f028a97f765e421b3df56\" title=\"Continue an initiated I2C transfer (single master mode only).\">\n         I2C_Transfer()\n        </a>\n        must be used either in polled mode or by adding a small driver wrapper using interrupts.\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Only single master mode is supported.\n        </dd>\n       </dl>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              i2c\n             </code>\n            </td>\n            <td>\n             A pointer to the I2C peripheral register block.\n            </td>\n           </tr>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              seq\n             </code>\n            </td>\n            <td>\n             A pointer to the sequence structure defining the I2C transfer to take place. The referenced structure must exist until the transfer has fully completed.\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Returns the status for an ongoing transfer:\n         <ul>\n          <li>\n           <a class=\"el\" href=\"#gga7c781ec28ae11e3e28892de7aa07a00fa1f1df5a76a71b423083d6a327d38ca1c\" title=\"Transfer in progress.\">\n            i2cTransferInProgress\n           </a>\n           - indicates that the transfer is not finished.\n          </li>\n          <li>\n           Otherwise, an error has occurred.\n          </li>\n         </ul>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <h2 class=\"groupheader\">\n      Macro Definition Documentation\n     </h2>\n     <a id=\"ga0a2167eb1e00a8da86bf0826f712f0f8\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga0a2167eb1e00a8da86bf0826f712f0f8\">\n        ◆\n       </a>\n      </span>\n      I2C_FREQ_STANDARD_MAX\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define I2C_FREQ_STANDARD_MAX&nbsp;&nbsp;&nbsp;100000\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Standard mode max frequency assuming using 4:4 ratio for Nlow:Nhigh.\n       </p>\n       <p>\n        From I2C specification: Min Tlow = 4.7us, min Thigh = 4.0us, max Trise=1.0us, max Tfall=0.3us. Since ratio is 4:4, have to use worst case value of Tlow or Thigh as base.\n       </p>\n       <p>\n        1/(Tlow + Thigh + 1us + 0.3us) = 1/(4.7 + 4.7 + 1.3)us = 93458Hz\n       </p>\n       <dl class=\"section note\">\n        <dt>\n         Note\n        </dt>\n        <dd>\n         Due to chip characteristics, max value is somewhat reduced.\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga29a3fbc14b30b68601118a9866e7440d\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga29a3fbc14b30b68601118a9866e7440d\">\n        ◆\n       </a>\n      </span>\n      I2C_FREQ_FAST_MAX\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define I2C_FREQ_FAST_MAX&nbsp;&nbsp;&nbsp;392157\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Fast mode max frequency assuming using 6:3 ratio for Nlow:Nhigh.\n       </p>\n       <p>\n        From I2C specification: Min Tlow = 1.3us, min Thigh = 0.6us, max Trise=0.3us, max Tfall=0.3us. Since ratio is 6:3, have to use worst case value of Tlow or 2xThigh as base.\n       </p>\n       <p>\n        1/(Tlow + Thigh + 0.3us + 0.3us) = 1/(1.3 + 0.65 + 0.6)us = 392157Hz\n       </p>\n      </div>\n     </div>\n     <a id=\"ga6961334be5d2f1d6e98d8f48de3b040e\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga6961334be5d2f1d6e98d8f48de3b040e\">\n        ◆\n       </a>\n      </span>\n      I2C_FREQ_FASTPLUS_MAX\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define I2C_FREQ_FASTPLUS_MAX&nbsp;&nbsp;&nbsp;987167\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Fast mode+ max frequency assuming using 11:6 ratio for Nlow:Nhigh.\n       </p>\n       <p>\n        From I2C specification: Min Tlow = 0.5us, min Thigh = 0.26us, max Trise=0.12us, max Tfall=0.12us. Since ratio is 11:6, have to use worst case value of Tlow or (11/6)xThigh as base.\n       </p>\n       <p>\n        1/(Tlow + Thigh + 0.12us + 0.12us) = 1/(0.5 + 0.273 + 0.24)us = 987167Hz\n       </p>\n      </div>\n     </div>\n     <a id=\"ga091476f45133e8a6deb9dbd7a3ea6229\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga091476f45133e8a6deb9dbd7a3ea6229\">\n        ◆\n       </a>\n      </span>\n      I2C_FLAG_WRITE\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define I2C_FLAG_WRITE&nbsp;&nbsp;&nbsp;0x0001\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Indicate plain write sequence: S+ADDR(W)+DATA0+P.\n       </p>\n       <ul>\n        <li>\n         S - Start\n        </li>\n        <li>\n         ADDR(W) - address with W/R bit cleared\n        </li>\n        <li>\n         DATA0 - Data taken from buffer with index 0\n        </li>\n        <li>\n         P - Stop\n        </li>\n       </ul>\n      </div>\n     </div>\n     <a id=\"ga0c1779663a5ebf44e956ea3f591b4e61\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga0c1779663a5ebf44e956ea3f591b4e61\">\n        ◆\n       </a>\n      </span>\n      I2C_FLAG_READ\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define I2C_FLAG_READ&nbsp;&nbsp;&nbsp;0x0002\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Indicate plain read sequence: S+ADDR(R)+DATA0+P.\n       </p>\n       <ul>\n        <li>\n         S - Start\n        </li>\n        <li>\n         ADDR(R) - Address with W/R bit set\n        </li>\n        <li>\n         DATA0 - Data read into buffer with index 0\n        </li>\n        <li>\n         P - Stop\n        </li>\n       </ul>\n      </div>\n     </div>\n     <a id=\"gaf9fc47d6ee1c66405dc491f7a4c3b8fc\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf9fc47d6ee1c66405dc491f7a4c3b8fc\">\n        ◆\n       </a>\n      </span>\n      I2C_FLAG_WRITE_READ\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define I2C_FLAG_WRITE_READ&nbsp;&nbsp;&nbsp;0x0004\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Indicate combined write/read sequence: S+ADDR(W)+DATA0+Sr+ADDR(R)+DATA1+P.\n       </p>\n       <ul>\n        <li>\n         S - Start\n        </li>\n        <li>\n         Sr - Repeated start\n        </li>\n        <li>\n         ADDR(W) - Address with W/R bit cleared\n        </li>\n        <li>\n         ADDR(R) - Address with W/R bit set\n        </li>\n        <li>\n         DATAn - Data written from/read into buffer with index n\n        </li>\n        <li>\n         P - Stop\n        </li>\n       </ul>\n      </div>\n     </div>\n     <a id=\"ga28bb5f9d0559f3af24c0554e497ba2b3\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga28bb5f9d0559f3af24c0554e497ba2b3\">\n        ◆\n       </a>\n      </span>\n      I2C_FLAG_WRITE_WRITE\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define I2C_FLAG_WRITE_WRITE&nbsp;&nbsp;&nbsp;0x0008\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Indicate write sequence using two buffers: S+ADDR(W)+DATA0+DATA1+P.\n       </p>\n       <ul>\n        <li>\n         S - Start\n        </li>\n        <li>\n         ADDR(W) - Address with W/R bit cleared\n        </li>\n        <li>\n         DATAn - Data written from buffer with index n\n        </li>\n        <li>\n         P - Stop\n        </li>\n       </ul>\n      </div>\n     </div>\n     <a id=\"ga6fd13e29a6b6315de051549037ac176f\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga6fd13e29a6b6315de051549037ac176f\">\n        ◆\n       </a>\n      </span>\n      I2C_FLAG_10BIT_ADDR\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define I2C_FLAG_10BIT_ADDR&nbsp;&nbsp;&nbsp;0x0010\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Use 10 bit address.\n       </p>\n      </div>\n     </div>\n     <a id=\"ga5c7fbd27ef311914288d8e7ce02f1528\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga5c7fbd27ef311914288d8e7ce02f1528\">\n        ◆\n       </a>\n      </span>\n      I2C_INIT_DEFAULT\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define I2C_INIT_DEFAULT\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <b>\n        Value:\n       </b>\n       <div class=\"fragment\">\n        <div class=\"line\">\n         {                                                                        \\\n        </div>\n        <div class=\"line\">\n         true,\n         <span class=\"comment\">\n          /* Enable when initialization done. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         true,\n         <span class=\"comment\">\n          /* Set to Controller mode. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         0,\n         <span class=\"comment\">\n          /* Use currently configured reference clock. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         I2C_FREQ_STANDARD_MAX,\n         <span class=\"comment\">\n          /* Set to standard rate assuring being */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         <span class=\"comment\">\n          /*                        within I2C specification. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         i2cClockHLRStandard\n         <span class=\"comment\">\n          /* Set to use 4:4 low/high duty cycle. */\n         </span>\n         \\\n        </div>\n        <div class=\"line\">\n         }\n        </div>\n       </div>\n       <p>\n        Suggested default configuration for I2C initialization structure.\n       </p>\n      </div>\n     </div>\n     <h2 class=\"groupheader\">\n      Enumeration Type Documentation\n     </h2>\n     <a id=\"gabb1516548b4528328682d6be09a3e3a5\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gabb1516548b4528328682d6be09a3e3a5\">\n        ◆\n       </a>\n      </span>\n      I2C_ClockHLR_TypeDef\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#gabb1516548b4528328682d6be09a3e3a5\">\n            I2C_ClockHLR_TypeDef\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Clock low to high ratio settings.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabb1516548b4528328682d6be09a3e3a5a72c81018da3424addf47b44341acd111\">\n           </a>\n           i2cClockHLRStandard\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Ratio is 4:4.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabb1516548b4528328682d6be09a3e3a5a7bfc41c09113d46c1247b598c80a3b2e\">\n           </a>\n           i2cClockHLRAsymetric\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Ratio is 6:3.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"ggabb1516548b4528328682d6be09a3e3a5a177d1faa325c634cbf56d2a4b4de9425\">\n           </a>\n           i2cClockHLRFast\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Ratio is 11:3.\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n     <a id=\"ga7c781ec28ae11e3e28892de7aa07a00f\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga7c781ec28ae11e3e28892de7aa07a00f\">\n        ◆\n       </a>\n      </span>\n      I2C_TransferReturn_TypeDef\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           enum\n           <a class=\"el\" href=\"#ga7c781ec28ae11e3e28892de7aa07a00f\">\n            I2C_TransferReturn_TypeDef\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Return codes for single Controller mode transfer function.\n       </p>\n       <table class=\"fieldtable\">\n        <tbody>\n         <tr>\n          <th colspan=\"2\">\n           Enumerator\n          </th>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga7c781ec28ae11e3e28892de7aa07a00fa1f1df5a76a71b423083d6a327d38ca1c\">\n           </a>\n           i2cTransferInProgress\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Transfer in progress.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga7c781ec28ae11e3e28892de7aa07a00fa01149f9a11ada8a9b05e7104f873de66\">\n           </a>\n           i2cTransferDone\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Transfer completed successfully.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga7c781ec28ae11e3e28892de7aa07a00fa54895c4189c89f4410a3ce1a15822725\">\n           </a>\n           i2cTransferNack\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            NACK received during transfer.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga7c781ec28ae11e3e28892de7aa07a00fa75da099dd69e053ee12d38fdd76a22e6\">\n           </a>\n           i2cTransferBusErr\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Bus error during transfer (misplaced START/STOP).\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga7c781ec28ae11e3e28892de7aa07a00fac15d6d57e2c8b0fca04fc6fbd80b824e\">\n           </a>\n           i2cTransferArbLost\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Arbitration lost during transfer.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga7c781ec28ae11e3e28892de7aa07a00fa49fe8ab19b9167d78c4b2abec7a511fc\">\n           </a>\n           i2cTransferUsageFault\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            Usage fault.\n           </p>\n          </td>\n         </tr>\n         <tr>\n          <td class=\"fieldname\">\n           <a id=\"gga7c781ec28ae11e3e28892de7aa07a00faa5de823251279b281dcabbd1ec4f693d\">\n           </a>\n           i2cTransferSwFault\n          </td>\n          <td class=\"fielddoc\">\n           <p>\n            SW fault.\n           </p>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n     </div>\n    </div>\n   </article>\n  ", "url": "http://docs.silabs.com/gecko-platform/4.1/emlib/api/efr32xg21/group-i2c", "status": "success"}