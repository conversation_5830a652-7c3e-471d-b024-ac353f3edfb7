(()=>{var e={5984:(e,t,n)=>{"use strict";n(8964),n(702);var o=n(1957),i=n(1947),r=n(499),a=n(9835);function s(e,t,n,o,i,r){const s=(0,a.up)("primary-window");return(0,a.wg)(),(0,a.j4)(s)}n(3269);var l=n(6970);const c={class:"right_toolbar_container"},u={class:"toggle_menu_left_wrapper"};function d(e,t,n,o,i,r){const s=(0,a.up)("q-btn"),d=(0,a.up)("q-toolbar-title"),g=(0,a.up)("launchable-button"),m=(0,a.up)("open-source-files"),f=(0,a.up)("q-toolbar"),h=(0,a.up)("q-header"),p=(0,a.up)("category-menu-item"),b=(0,a.up)("q-drawer"),y=(0,a.up)("home-made-toast"),_=(0,a.up)("card"),v=(0,a.up)("q-page"),w=(0,a.up)("q-page-container"),S=(0,a.up)("q-layout");return(0,a.wg)(),(0,a.j4)(S,{view:"hHh Lpr lFf"},{default:(0,a.w5)((()=>[(0,a.Wm)(h,{class:"primary_header"},{default:(0,a.w5)((()=>[(0,a.Wm)(f,null,{default:(0,a.w5)((()=>[this.webAvailableDarkModeToggle()?((0,a.wg)(),(0,a.j4)(s,{key:0,flat:"",dense:"",icon:"D",onClick:r.toggleDark},null,8,["onClick"])):(0,a.kq)("",!0),(0,a.Wm)(d,{class:"header_text"},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(this.$store.getters.componentName),1)])),_:1}),(0,a._)("div",c,[(0,a.Wm)(g,{buttonLabel:"Documentation",type:"documentation",componentId:this.$store.getters.componentId,id:"documentation"},null,8,["componentId"]),(0,a.Wm)(g,{buttonLabel:"Pin Tool",type:"pintool",id:"pintool"}),(0,a.Wm)(m,{class:"source_files",sourceFiles:this.$store.getters.sourceFiles},null,8,["sourceFiles"]),(0,a.Wm)(s,{flat:"",dense:"",icon:i.closeIcon,id:"button_close_editor",title:"Close Editor",onClick:t[0]||(t[0]=e=>r.closeEditor())},null,8,["icon"])])])),_:1})])),_:1}),this.rootCategories.length>1?((0,a.wg)(),(0,a.j4)(b,{key:0,modelValue:i.leftDrawerOpen,"onUpdate:modelValue":t[3]||(t[3]=e=>i.leftDrawerOpen=e),breakpoint:0,"mini-width":33,bordered:"",mini:i.miniState},{mini:(0,a.w5)((()=>[(0,a.Wm)(s,{flat:"",dense:"",icon:i.chevronRight,id:"toggle_menu_right",title:"Open panel menu",class:"toggle_menu",onClick:t[1]||(t[1]=e=>r.setMiniState(!1))},null,8,["icon"])])),default:(0,a.w5)((()=>[(0,a._)("div",null,[(0,a._)("div",u,[(0,a.Wm)(s,{flat:"",dense:"",icon:i.chevronLeft,id:"toggle_menu_left",title:"Close panel menu",class:"toggle_menu",onClick:t[2]||(t[2]=e=>r.setMiniState(!0))},null,8,["icon"])]),((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(this.rootCategories,(e=>((0,a.wg)(),(0,a.iD)("div",{key:e.label},[(0,a.Wm)(p,{id:"cat_menu_item_"+e.label,category_name:e.label,category_id:e.id,selected:i.menuSelected,onSelected:r.selectMenuItem},null,8,["id","category_name","category_id","selected","onSelected"])])))),128))])])),_:1},8,["modelValue","mini"])):(0,a.kq)("",!0),(0,a.Wm)(w,null,{default:(0,a.w5)((()=>[(0,a.Wm)(y,{success:this.$store.getters.headerStatus.status,msg:this.$store.getters.headerStatus.msg,pipeline:this.$store.getters.headerStatus.pipeline},null,8,["success","msg","pipeline"]),(0,a.Wm)(v,null,{default:(0,a.w5)((()=>[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(this.rootCategories,(e=>((0,a.wg)(),(0,a.j4)(_,{key:e.id,id:"category_expansion_header_"+e.id,category:e},null,8,["id","category"])))),128))])),_:1})])),_:1})])),_:1})}const g=e=>((0,a.dD)("data-v-f80bc244"),e=e(),(0,a.Cn)(),e),m=g((()=>(0,a._)("div",{class:"card-title-block-bar"},null,-1)));function f(e,t,n,o,i,r){const s=(0,a.up)("configurable-section"),l=(0,a.up)("q-card");return e.$store.getters.visibilityOf(n.category.id)?((0,a.wg)(),(0,a.j4)(l,{key:0,class:"cat-card"},{default:(0,a.w5)((()=>[m,(0,a.Wm)(s,{category_name:n.category.label,category_id:n.category.id,category_desc:n.category.description,"data-conf-class":n.category.conf_class},null,8,["category_name","category_id","category_desc","data-conf-class"])])),_:1})):(0,a.kq)("",!0)}const h={class:"configurables_wrapper"},p={class:"configurables_header"},b={key:0,class:"configurables_components"},y={key:0,class:"nested_configurables"},_=["id"];function v(e,t,n,o,i,r){const s=(0,a.up)("q-tooltip"),c=(0,a.up)("q-toggle"),u=(0,a.up)("configurable"),d=(0,a.up)("q-separator"),g=(0,a.up)("configurable-section",!0);return(0,a.wg)(),(0,a.iD)("div",null,[(0,a._)("div",h,[(0,a._)("div",p,[(0,a._)("div",null,[(0,a.Uk)((0,l.zw)(n.category_name)+" ",1),null!=n.category_desc?((0,a.wg)(),(0,a.j4)(s,{key:0},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(n.category_desc),1)])),_:1})):(0,a.kq)("",!0)]),r.isToggle?((0,a.wg)(),(0,a.j4)(c,{key:0,modelValue:r.toggle,"onUpdate:modelValue":t[0]||(t[0]=e=>r.toggle=e),class:"header_toggle"},null,8,["modelValue"])):(0,a.kq)("",!0)]),r.displayable?((0,a.wg)(),(0,a.iD)("div",b,[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(r.configsInCategory,(e=>((0,a.wg)(),(0,a.j4)(u,{config_id:e.id,key:e.id},null,8,["config_id"])))),128))])):(0,a.kq)("",!0)]),r.displayable?((0,a.wg)(),(0,a.iD)("div",y,[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(r.catsInCategory,(t=>((0,a.wg)(),(0,a.iD)("div",{key:t.id,id:"category_expansion_header_"+t.id},[e.$store.getters.visibilityOf(t.id)?((0,a.wg)(),(0,a.iD)(a.HY,{key:0},[(0,a.Wm)(d),(0,a.Wm)(g,{category_name:t.label,category_desc:t.description,category_id:t.id,sub_level:n.sub_level+1},null,8,["category_name","category_desc","category_id","sub_level"])],64)):(0,a.kq)("",!0)],8,_)))),128))])):(0,a.kq)("",!0)])}const w={class:"configurable"},S={class:"configurable_label"};function C(e,t,n,o,i,r){const s=(0,a.up)("toggle-with-error"),c=(0,a.up)("q-input"),u=(0,a.up)("numeric-spinner"),d=(0,a.up)("q-select");return(0,a.wg)(),(0,a.iD)("div",w,[(0,a._)("div",S,(0,l.zw)(r.config.label),1),r.isToggle()?((0,a.wg)(),(0,a.j4)(s,{key:0,class:(0,l.C_)(["configurable_item",{configurable_toggle:!r.errorMessage}]),id:"configurable_"+r.config.id,title:r.config.desc,"left-label":"",disable:r.readonly,modelValue:r.currentValue,"onUpdate:modelValue":t[0]||(t[0]=e=>r.currentValue=e),color:r.readonly?"blue-grey-4":"blue",error:null!=r.errorMessage,"error-message":r.errorMessage},null,8,["class","id","title","disable","modelValue","color","error","error-message"])):(0,a.kq)("",!0),r.isString()?((0,a.wg)(),(0,a.j4)(c,{key:1,class:"configurable_item",id:"configurable_"+r.config.id,title:r.config.desc,outlined:"",dense:"",modelValue:r.currentValue,"onUpdate:modelValue":[t[1]||(t[1]=e=>r.currentValue=e),t[2]||(t[2]=e=>r.updateValidationState(e,this.stringErrorLabelAlert))],debounce:n.debounceTimeMs,readonly:r.readonly,error:null!=r.errorMessage,"error-message":r.errorMessage,"hide-bottom-space":""},null,8,["id","title","modelValue","debounce","readonly","error","error-message"])):(0,a.kq)("",!0),r.isIntArray()?((0,a.wg)(),(0,a.j4)(c,{key:2,class:"configurable_array_item",id:"configurable_"+r.config.id,title:r.config.desc,type:"textarea",outlined:"",dense:"",modelValue:r.currentValue,"onUpdate:modelValue":[t[3]||(t[3]=e=>r.currentValue=e),t[4]||(t[4]=e=>r.updateValidationState(e,this.arrayErrorLabelAlert))],debounce:n.debounceTimeMs,readonly:r.readonly,error:null!=r.errorMessage,"error-message":r.errorMessage,"hide-bottom-space":""},null,8,["id","title","modelValue","debounce","readonly","error","error-message"])):(0,a.kq)("",!0),r.isInteger()?((0,a.wg)(),(0,a.j4)(u,{key:3,class:"configurable_item",id:"configurable_spinner_"+r.config.id,title:r.config.desc,step:r.step,min:r.min,max:r.max,format:r.format,readonly:r.readonly,debounce:n.debounceTimeMs,modelValue:r.currentValue,"onUpdate:modelValue":t[5]||(t[5]=e=>r.currentValue=e),onRulesRan:t[6]||(t[6]=e=>r.alertServerIfValid(e))},null,8,["id","title","step","min","max","format","readonly","debounce","modelValue"])):(0,a.kq)("",!0),r.isCombo()?((0,a.wg)(),(0,a.j4)(d,{key:4,class:"configurable_item",id:"configurable_"+r.config.id,title:r.config.desc,dense:"",outlined:"",modelValue:r.currentValue,"onUpdate:modelValue":t[7]||(t[7]=e=>r.currentValue=e),options:r.config.combo_options,"options-dense":"",readonly:r.readonly,error:null!=r.errorMessage,"error-message":r.errorMessage,"hide-bottom-space":""},null,8,["id","title","modelValue","options","readonly","error","error-message"])):(0,a.kq)("",!0)])}n(6727);const k={class:"row no-wrap"},I={key:0,name:"spinner_buttons",class:"column no-wrap"};function V(e,t,n,o,i,r){const s=(0,a.up)("q-btn"),l=(0,a.up)("q-input");return(0,a.wg)(),(0,a.iD)("div",k,[n.readonly?(0,a.kq)("",!0):((0,a.wg)(),(0,a.iD)("div",I,[(0,a.Wm)(s,{id:"configurable_spinner_increment_"+n.id,icon:e.upIcon,name:"increment_button",dense:"",flat:"",size:"sm",onClick:t[0]||(t[0]=e=>r.increment())},null,8,["id","icon"]),(0,a.Wm)(s,{id:"configurable_spinner_decrement_"+n.id,icon:e.downIcon,name:"decrement_button",dense:"",flat:"",size:"sm",onClick:t[1]||(t[1]=e=>r.decrement())},null,8,["id","icon"])])),(0,a.Wm)(l,{id:"configurable_spinner_text_"+n.id,class:"fit",outlined:"",dense:"",type:"text",readonly:n.readonly,modelValue:r.numericalValue,"onUpdate:modelValue":t[2]||(t[2]=e=>r.numericalValue=e),debounce:n.debounce,"lazy-rules":!1,rules:[e=>this.validateInput(e)],"hide-bottom-space":""},null,8,["id","readonly","modelValue","debounce","rules"])])}var x=n(2670);const E={INVALID:{base:-1,label:"invalid",defaultPrefix:"",abbrev:""},DECIMAL:{base:10,label:"decimal",defaultPrefix:"",abbrev:"d"},HEX:{base:16,label:"hexidecimal",defaultPrefix:"0x",abbrev:"h"},OCTAL:{base:8,label:"octal",defaultPrefix:"0o",abbrev:"o"},BINARY:{base:2,label:"binary",defaultPrefix:"0b",abbrev:"b"}};class T{constructor(e,t,n="",o="",i=0){this.unsignedNumStr=e,this.format=t,this.prefix=n,this.sign=o,this.padding=0}numericValue(){return`${this.prefix}${this.unsignedNumStr}`*this.signMult()}signMult(){return"-"==this.sign?-1:1}cloneWithValue(e,t=null){var n="";e>0?"+"==this.sign&&(n=this.sign):e<0&&(n="-");var o=null==t?this.format:t,i=o!=this.format?o.defaultPrefix:this.prefix,r=0;return new T(`${Math.abs(e).toString(o.base)}`,o,i,n,r)}toString(){return`${this.sign}${this.prefix}${this.unsignedNumStr}`}}const $={name:"NumericSpinner",created(){this.upIcon=x.Waq,this.downIcon=x.CW;var e=this.toNumberFormat(this.format);this.numericalValue=new T("0",e,e.defaultPrefix).cloneWithValue(this.modelValue).toString()},data(){return{}},props:{id:{type:[String],required:!0},modelValue:{type:[String,Number]},step:{default:1,type:Number},min:[Number,Boolean],max:[Number,Boolean],readonly:{default:!1,type:[Boolean]},debounce:[String,Number],format:{default:"d",type:String}},computed:{numericalValue:{get(){return this.modelValue},set(e){this.$emit("update:modelValue",e)}}},methods:{increment:function(e){let t=this.parseNumber(this.numericalValue),n=t.numericValue()%this.step,o=t.numericValue();o+=n>0?this.step-n:this.step,this.setIfBoundsVerify(o,t)},decrement:function(e){let t=this.parseNumber(this.numericalValue),n=t.numericValue()%this.step,o=t.numericValue();o-=n>0?n:this.step,this.setIfBoundsVerify(o,t)},setIfBoundsVerify:function(e,t){if((!1===this.max||e<=this.max)&&(!1===this.min||e>=this.min)){if(t){var n=t.cloneWithValue(e);e=n.toString()}this.numericalValue=e}},validateInputParams:function(e){return!1!==this.max&&e>this.max?e+" too high: max is "+this.max:!1!==this.min&&e<this.min?e+" too low: min is "+this.min:!1===this.step||1===this.step||e%this.step===0||e+" not a multiple of "+this.step},parseNumber(e){"string"!==typeof e&&(e=`${e}`),e=e.replace(/[\s]/g,"");var t=E.DECIMAL,n="",o=e.length>0&&("+"==e[0]||"-"==e[0])?e[0]:"",i=0;if(e=e.substring(o.length),null!=e&&e.length>1&&"0"===e[0])switch(n=`0${e[1]}`,e[1]){case"x":case"X":t=E.HEX;break;case"o":case"O":t=E.OCTAL;break;case"b":case"B":t=E.BINARY;break;default:n="",e[1].match(/([^0-9,])/gi)&&(t=E.INVALID)}return new T(e.substring(n.length),t,n,o,i)},validateInput:function(e){var t=!0;if(null!=e){var n=this.parseNumber(e);switch(n.format){case E.HEX:t=this.validateNumberFormat(n.unsignedNumStr,"hexidecimal",/([^0-9a-f])/gi);break;case E.OCTAL:t=this.validateNumberFormat(n.unsignedNumStr,"octal",/([^0-7])/gi);break;case E.BINARY:t=this.validateNumberFormat(n.unsignedNumStr,"binary",/([^0-1])/gi);break;case E.DECIMAL:t=this.validateNumberFormat(n.unsignedNumStr,"decimal",/([^0-9,])/gi);break;case E.INVALID:t="Invalid numeric format. Support HEX(0x), OCTAL(0o), and BINARY(0b)";break}!0===t&&(t=this.validateInputParams(n.numericValue()))}else t="number required";return this.$emit("rulesRan",t),t},validateNumberFormat:function(e,t,n){if(""===e)return"number required";if(e.includes("."))return"natural numbers only -- no decimals";var o=e.match(n);return null!=e&&!o||`${o} ${o.length>1?"are":"is"} invalid in a ${t} value`},toNumberFormat(e){for(var t in E)if(E[t].abbrev==e)return E[t];return E.DECIMAL}}};var N=n(1639),Z=n(9379),q=n(6611),M=n(9984),A=n.n(M);const D=(0,N.Z)($,[["render",V],["__scopeId","data-v-91a9246e"]]),O=D;A()($,"components",{QBtn:Z.Z,QInput:q.Z});const W={class:"toggle-with-error-issue-text"};function j(e,t,n,i,r,s){const c=(0,a.up)("q-toggle"),u=(0,a.up)("q-icon");return(0,a.wg)(),(0,a.iD)("div",null,[(0,a.Wm)(c,(0,a.dG)(s.nonClassAttributes,{modelValue:n.modelValue,"onUpdate:modelValue":t[0]||(t[0]=t=>e.$emit("update:modelValue",t)),class:n.toggleClass}),null,16,["modelValue","class"]),(0,a.wy)((0,a._)("div",W,[(0,a.Wm)(u,{name:e.alert,size:"sm",color:"negative"},null,8,["name"]),(0,a.Uk)(" "+(0,l.zw)(n.errorMessage),1)],512),[[o.F8,n.error]])])}const L={name:"ToggleWithError",created(){this.alert=x.fr4},data(){return{}},props:{modelValue:{type:[Boolean]},toggleClass:{type:[String]},errorMessage:{type:[String],default:""},error:{type:[Boolean],default:!1}},computed:{nonClassAttributes:function(){return Object.keys(this.$attrs).filter((e=>"class"!==e)).reduce(((e,t)=>({...e,[t]:this.$attrs[t]})),{})}},methods:{}};var U=n(592),P=n(2857);const F=(0,N.Z)(L,[["render",j]]),H=F;A()(L,"components",{QToggle:U.Z,QIcon:P.Z});const B={name:"Configurable",components:{NumericSpinner:O,ToggleWithError:H},props:{config_id:String,debounceTimeMs:{type:[Number],default:1e3}},data(){return{cmsisError:null}},methods:{updateValidationState:function(e,t){let n=t(e);this.cmsisError=!0===n?null:n},fullyCommitChange:function(){this.$store.commit("commitValue",this.config.id)},alertServerIfValid:function(e){!0===e&&this.fullyCommitChange()},isToggle:function(){return"check"===this.config.conf_type},isInteger:function(){return"spinner"===this.config.conf_type},isString:function(){return"string"===this.config.conf_type},isCombo:function(){return"combo"===this.config.conf_type},isIntArray:function(){return"intarray"===this.config.conf_type},isByteArray:function(){return this.isIntArray()&&"min"in this.config&&"max"in this.config&&"format"in this.config&&0==this.config.min&&255==this.config.max&&"h"==this.config.format},hasHexPrefix:function(e){return e.startsWith("0x")||e.startsWith("0X")},arrayErrorLabelAlert:function(e){let t=this.stringErrorLabel(e);if(!1===t)return t;let n="",o=this.hexStringToByteArray(e,(e=>n=e));if(n)return n;null!=o&&(e=o.join(" "));let i=e.split(" ").map((e=>e.trim())).filter((e=>e));return"length"in this.config&&i.length>this.config.length?"Array length exceeds "+this.config.length:(i.forEach(((e,t)=>this.validateIntString(e,t,(e=>{n=n+(n?" ,":"")+e})))),n||(this.fullyCommitChange(),!0))},errorHandlerHelper(e,t,n=null){return e&&e(t),n},validHexValue(e){var t=/^[0-9a-fA-F]+$/;return t.test(this.hasHexPrefix(e)?e.substring(2):e)},hexStringToByteArray(e,t=null){if(e.trim().includes(" "))return null;if(!this.isByteArray())return null;if(this.hasHexPrefix(e)&&(e=e.substring(2)),0==e.length)return null;if(!this.validHexValue(e))return this.errorHandlerHelper(t,"Not a valid hex string",null);let n=[];for(var o=0;o<e.length;o+=2){let i=e.substring(o,o+2),r=parseInt(i,16);if(isNaN(r))return this.errorHandlerHelper(t,i+" is not a valid hex value",null);n.push(r)}return n},validateIntString:function(e,t,n){if(this.hasHexPrefix(e)&&!this.validHexValue(e))return void this.errorHandlerHelper(n,"invalid hex character");let o=parseInt(e);isNaN(o)&&this.errorHandlerHelper(n,"ERROR parsing "+e),"min"in this.config&&o<parseInt(this.config.min)&&this.errorHandlerHelper(n,e+" is less than minimum value("+this.config.min+")"),"max"in this.config&&o>parseInt(this.config.max)&&this.errorHandlerHelper(n,e+" is greater than maximum value("+this.config.max+")")},stringErrorLabelAlert:function(e){let t=this.stringErrorLabel(e);return!0===t&&this.fullyCommitChange(),t},stringErrorLabel:function(e){let t=0;{let n=!1;for(let o of e){if('"'===o&&!n)return'Unescaped " detected';"\\"===o?n=!n:(n=!1,++t)}}if("max_length"in this.config&&this.config.max_length>0){let n=this.config.max_length-t;if(n<0)return"Character limit of "+this.config.max_length+" exceeded by "+Math.abs(n)+(t!==e.length?" (Escape Backslashes Ignored)":"")}return!0}},computed:{config:function(){return this.$store.getters.dataForConfigurable(this.config_id)},errorMessage:function(){return null!=this.externalError?this.externalError:this.cmsisError},externalError:function(){let e=this.config.secondary_error;return void 0===e?null:e},currentValue:{get(){return this.isIntArray()?this.config.modified_value.split(",").map((e=>e.trim())).filter((e=>e)).join(" "):this.config.modified_value},set(e){if(this.isIntArray()){let t=this.hexStringToByteArray(e);e=null!=t&&t.length>0?t.map((e=>"0x"+e.toString(16).padStart(2,"0"))).join(","):e.split(" ").map((e=>e.trim())).filter((e=>e)).join(",")}this.$store.commit("modifyValue",{id:this.config.id,value:e}),(this.isToggle()||this.isCombo())&&this.fullyCommitChange()}},max:function(){return"max"in this.config&&this.config.max},min:function(){return"min"in this.config&&this.config.min},step:function(){return"step"in this.config?this.config.step:1},readonly:function(){return"readonly"in this.config&&this.config.readonly},format:function(){return"format"in this.config?this.config.format:"d"}}};var Q=n(3775),R=n(8149);const Y=(0,N.Z)(B,[["render",C],["__scopeId","data-v-1aa3be52"]]),z=Y;A()(B,"components",{QInput:q.Z,QSelect:Q.Z,QField:R.Z});var G=n(1463);const K={name:"ConfigurableSection",props:{category_name:String,category_id:String,category_desc:String,sub_level:{default:0,type:Number}},computed:{category:function(){return this.$store.getters.dataForConfigurable(this.category_id)},configsInCategory:function(){let e=this.$store.getters.childrenForCategory(this.category_id);return e.filter((e=>"conf"===e.type))},catsInCategory:function(){let e=this.$store.getters.childrenForCategory(this.category_id);return e.filter((e=>"cat"===e.type))},isToggle:function(){return"toggle"in this.category},catOpen:{get(){return this.$store.getters.visualDataFor(this.category.id).expanded},set(e){this.$store.commit("setCategoryExpansion",{id:this.category.id,toggle:e})}},toggle:{get(){return this.category.toggle},set(e){this.$store.commit("toggleCategory",{id:this.category_id,toggle:e}),this.$store.commit("commitValue",this.category.id)}},displayable:function(){return!this.isToggle||this.toggle}},components:{Configurable:z}};var X=n(6858),J=n(926);const ee=(0,N.Z)(K,[["render",v],["__scopeId","data-v-a2a48d6c"]]),te=ee;A()(K,"components",{QTooltip:X.Z,QToggle:U.Z,QSeparator:J.Z});const ne={name:"Many-Configurables",props:["category"],components:{ConfigurableSection:te}};var oe=n(4458);const ie=(0,N.Z)(ne,[["render",f],["__scopeId","data-v-f80bc244"]]),re=ie;function ae(e,t,n,o,i,r){const s=(0,a.up)("category-menu-item",!0),c=(0,a.up)("q-item-label"),u=(0,a.up)("q-item-section"),d=(0,a.up)("visibility-toggle"),g=(0,a.up)("q-expansion-item");return(0,a.wg)(),(0,a.j4)(g,{id:"menu_item_"+n.category_name,"header-inset-level":n.sub_level,"switch-toggle-side":"","expand-icon-toggle":"","expand-icon":0!==r.catsInCategory.length?null:" ","header-class":{"category-menu-expansion":!0,"category-menu-expansion-active":n.selected===n.category_id,shouldShowVis:r.shouldShowVis}},{header:(0,a.w5)((()=>[(0,a.Wm)(u,{onClick:r.scrollToCategory,class:"category-menu-name"},{default:(0,a.w5)((()=>[(0,a.Wm)(c,{title:r.shouldShowVis?"Show category to enable scroll ->":"Scroll to category"},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(n.category_name),1)])),_:1},8,["title"])])),_:1},8,["onClick"]),(0,a.Wm)(u,{side:"",class:"eye",title:r.shouldShowVis?"Show category":"Hide category"},{default:(0,a.w5)((()=>[(0,a.Wm)(d,{cat_id:n.category_id},null,8,["cat_id"])])),_:1},8,["title"])])),default:(0,a.w5)((()=>[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(r.catsInCategory,(e=>((0,a.wg)(),(0,a.iD)("div",{key:e.label},[(0,a.Wm)(s,{category_name:e.label,category_id:e.id,sub_level:n.sub_level+.5,selected:n.selected,onSelected:r.selectMenuItem},null,8,["category_name","category_id","sub_level","selected","onSelected"])])))),128))])),_:1},8,["id","header-inset-level","expand-icon","header-class"])}function se(e,t,n,i,r,s){const l=(0,a.up)("q-icon");return(0,a.wg)(),(0,a.iD)("div",null,[(0,a.Wm)(l,{id:"visibility_toggle_"+n.cat_id,size:"sm",name:s.currentVisibility,onClick:(0,o.iM)(s.toggleVisibility,["stop"])},null,8,["id","name","onClick"])])}A()(ne,"components",{QCard:oe.Z});const le={name:"VisibilityToggle",created(){this.eye=x.rgx,this.eye_off=x.DqW},props:{cat_id:{required:!0,type:String}},computed:{eyeVisible:{get(){return this.$store.getters.visibilityOf(this.cat_id)},set(e){this.$store.commit("setCategoryVisibility",{id:this.cat_id,toggle:e})}},currentVisibility:function(){return this.eyeVisible?this.eye:this.eye_off}},methods:{toggleVisibility:function(e){this.eyeVisible=!this.eyeVisible}}},ce=(0,N.Z)(le,[["render",se],["__scopeId","data-v-c0b2aed4"]]),ue=ce;function de(e){let t=document.getElementById("category_expansion_header_"+e);t.scrollIntoView({behavior:"smooth",block:"center",inline:"start"})}A()(le,"components",{QIcon:P.Z});const ge={name:"CategoryMenuItem",props:{category_name:String,category_id:String,sub_level:{default:0,type:Number},selected:String},computed:{catsInCategory:function(){return this.$store.getters.categories(this.category_id)},category:function(){return this.$store.getters.dataForConfigurable(this.category_id)},shouldShowVis:function(){return!this.$store.getters.visibilityOf(this.category_id)}},methods:{scrollToCategory:function(){if(this.shouldShowVis)return;let e=this.$store.getters.dataForConfigurable(this.category_id),t=!1,n=e.id;while(void 0!==n){let e=this.$store.getters.dataForConfigurable(n);n=e.parent_category_id;let o=this.$store.getters.visualDataFor(e.id);o.expanded||(t=!0,this.$store.commit("setCategoryExpansion",{id:e.id,toggle:!0}))}let o=e.id;t?setTimeout((()=>{de(o),this.selectMenuItem(o)}),300):(de(o),this.selectMenuItem(o))},selectMenuItem:function(e){this.$emit("selected",e)}},components:{VisibilityToggle:ue}};var me=n(1123),fe=n(1233),he=n(3115),pe=n(490);const be=(0,N.Z)(ge,[["render",ae]]),ye=be;function _e(e,t,n,o,i,r){const s=(0,a.up)("q-btn"),l=(0,a.up)("q-icon"),c=(0,a.up)("q-select");return r.isButton?((0,a.wg)(),(0,a.j4)(s,{key:0,id:"open_source_button",title:n.sourceFiles[0],label:"View Source",icon:i.codeIcon,"no-caps":"",onClick:t[0]||(t[0]=e=>r.openSource(n.sourceFiles[0])),class:"button_secondary"},null,8,["title","icon"])):((0,a.wg)(),(0,a.j4)(c,{key:1,modelValue:i.model,"onUpdate:modelValue":[t[1]||(t[1]=e=>i.model=e),t[2]||(t[2]=e=>r.openSource(i.model))],outlined:"",dense:"","options-dense":"",id:"select-source-files",options:n.sourceFiles,label:"View Source Files",class:"select-source-files"},{prepend:(0,a.w5)((()=>[(0,a.Wm)(l,{class:"icon-selected",name:i.codeIcon},null,8,["name"])])),_:1},8,["modelValue","options"]))}A()(ge,"components",{QExpansionItem:me.Z,QItemSection:fe.Z,QItemLabel:he.Z,QItem:pe.Z});const ve={name:"OpenSourceFiles",props:["sourceFiles"],data(){return{model:null,codeIcon:x.SMx}},computed:{isButton:function(){return 1===this.sourceFiles.length}},methods:{openSource:function(e){G.Z.openSource(e)}}},we=(0,N.Z)(ve,[["render",_e],["__scopeId","data-v-0a44e567"]]),Se=we;function Ce(e,t,n,o,i,r){const s=(0,a.up)("q-btn");return e.documentationExists&&""===this.documentationError||"pintool"===this.type?((0,a.wg)(),(0,a.j4)(s,{key:0,label:this.buttonLabel,"no-caps":"",onClick:r.onclick,class:"button_secondary"},null,8,["label","onClick"])):(0,a.kq)("",!0)}A()(ve,"components",{QBtn:Z.Z,QSelect:Q.Z,QIcon:P.Z});const ke={name:"LaunchableButton",props:{type:String,componentId:String,buttonLabel:String},data:()=>({documentationUrl:"",documentationError:"",documentationExists:!1}),watch:{componentId(e){this.getComponentDocumentation(e)}},methods:{onclick(){switch(this.type){case"pintool":this.openPinTool();break;case"documentation":this.openDocumentation();break}},openPinTool(){let e={launchableId:"pintool"};this.$store.dispatch("openLaunchable",e)},openDocumentation(){this.$store.dispatch("openExternalUrl",this.documentationUrl)},getComponentDocumentation(e){this.$store.dispatch("getComponentDocumentation",e).then((e=>{e&&"object"===typeof e&&this.setDocumentationValues(e)})).catch((e=>console.log(e)))},setDocumentationValues(e){"status"in e&&"success"===e.status?"url"in e&&(this.documentationUrl=e.url,this.documentationExists=!0):"reason"in e&&("NetworkError"===e.reason&&(this.documentationError="NetworkError"),"NotExist"===e.reason&&(this.documentationExists=!1))}}},Ie=(0,N.Z)(ke,[["render",Ce]]),Ve=Ie;function xe(e,t,n,i,r,s){const c=(0,a.up)("q-spinner"),u=(0,a.up)("q-icon"),d=(0,a.up)("q-btn");return"none"===n.pipeline||r.userClosed?(0,a.kq)("",!0):((0,a.wg)(),(0,a.j4)(o.uT,{key:0,appear:"","enter-active-class":"animated fadeIn","leave-active-class":"animated fadeOut"},{default:(0,a.w5)((()=>[(0,a._)("div",{class:(0,l.C_)(["toast",{toast_error:!n.success}])},["Saving..."===n.msg?((0,a.wg)(),(0,a.j4)(c,{key:0})):((0,a.wg)(),(0,a.j4)(u,{key:1,name:s.statusIcon,ref:"status-icon"},null,8,["name"])),(0,a.Uk)((0,l.zw)(n.msg)+" ",1),(0,a.Wm)(d,{flat:"",dense:"",icon:r.closeIcon,id:"button_close_toast",title:"Close Toast",class:"close",onClick:t[0]||(t[0]=e=>s.closeToast())},null,8,["icon"])],2)])),_:1}))}A()(ke,"components",{QBtn:Z.Z});const Ee={name:"HomeMadeToast",props:["msg","success","pipeline"],data(){return{closeIcon:x.UEB,userClosed:!1}},computed:{statusIcon:function(){return this.success?x.YKm:x.UEB}},methods:{closeToast:function(){this.userClosed=!0}},watch:{pipeline:function(e){this.userClosed=!1}}};var Te=n(3940);const $e=(0,N.Z)(Ee,[["render",xe],["__scopeId","data-v-3dd536f5"]]),Ne=$e;A()(Ee,"components",{QSpinner:Te.Z,QIcon:P.Z,QBtn:Z.Z});const Ze={name:"PrimaryWindow",data(){return{chevronRight:x.DDS,chevronLeft:x.zAB,closeIcon:x.UEB,leftDrawerOpen:!0,miniState:!0,menuSelected:"default"}},computed:{rootCategories:function(){return this.$store.getters.categories(null)}},watch:{rootCategories:function(e){"default"===this.menuSelected&&e&&e.length&&(this.menuSelected=e[0].id)}},methods:{setMiniState:function(e){window.localStorage.setItem("miniState",e),this.miniState=e},toggleDark:function(){G.Z.toggleDark()},webAvailableDarkModeToggle:function(){return G.Z.showInternalThemeSwitch()},closeEditor:function(){G.Z.closeEditor()},selectMenuItem(e){this.menuSelected=e}},created(){let e=JSON.parse(localStorage.getItem("miniState"));"boolean"===typeof e&&(this.miniState=e)},components:{Card:re,CategoryMenuItem:ye,OpenSourceFiles:Se,HomeMadeToast:Ne,LaunchableButton:Ve}};var qe=n(7605),Me=n(6602),Ae=n(1663),De=n(1973),Oe=n(3655),We=n(2133),je=n(9885);const Le=(0,N.Z)(Ze,[["render",d]]),Ue=Le;function Pe(e,t,n,o){if(e.data){let i=JSON.parse(e.data);if(!("clientId"in i)||i.clientId!==t)return;if("wsType"in i)if(console.log("handling websocket response"),"config_update"===i.wsType)n.commit("updateConfigurables",i.configurables),G.Z.changesCommitted(i.conf_class);else if("metadata_update"===i.wsType)n.commit("setMetadata",i);else if("problems_alert"===i.wsType){if(i.issues)for(let t of i.issues)n.commit("setServerError",t);"undefined"===typeof Pe.currentTimeout&&(Pe.currentTimeout=null);let e=void 0!==i.msg?i.msg:"Validation Error: See Problems View";null===Pe.currentTimeout&&(o.notify({message:e,icon:x.fr4,color:"negative",position:"top",group:!0,timeout:3e3}),Pe.currentTimeout=setTimeout((function(){Pe.currentTimeout=null}),3e3))}else if("clear_issues"===i.wsType)for(let e of i.ids)n.commit("setServerError",{id:e,msg:null})}}A()(Ze,"components",{QLayout:qe.Z,QHeader:Me.Z,QToolbar:Ae.Z,QBtn:Z.Z,QToolbarTitle:De.Z,QDrawer:Oe.Z,QPageContainer:We.Z,QPage:je.Z});class Fe{constructor(e){this.wsUrl=e,this.attemptReconnect=!1,window.addEventListener("beforeUnmount",(()=>{this.close()}))}init(e){return new Promise((t=>{this.socket=new WebSocket(this.wsUrl),this.socket.onopen=e=>{window.console.log("UCEdit: web socket connected"),G.Z.socketsReady(),this.attemptReconnect=!0},this.socket.onclose=e=>{setTimeout((()=>{window.console.log("Socket connection lost, attempting reconnect"),this.attemptReconnect&&this.reconnect()}),5e3)},e(this.socket)}))}reconnect(){return new Promise(((e,t)=>{this.socket.readyState!==WebSocket.CONNECTING&&this.socket.readyState!==WebSocket.OPEN?this.init().then((t=>{e(t)})).catch((e=>{t(e)})):e()}))}close(){this.socket&&(this.socket.onclose=function(){},this.socket.close())}getReadyState(){return this.socket.readyState}}var He=n(2497);const Be={name:"App",data(){return{wsUrl:""}},props:{wsOverride:String},created:function(){window.console.log("UCEdit: Setting origin "+window.location.origin),G.Z.setOrigin(this.$store,window.location.origin);var e=document.documentElement;this.setThemeMode(e),new MutationObserver((t=>{t.forEach((t=>{this.setThemeMode(e)}))})).observe(e,{attributes:!0,attributeFilter:["data-theme"],subtree:!1});let t=new URLSearchParams(window.location.search),n=t.get("component"),o=t.get("project");G.Z.setUniqueProject(this.$store,o),window.console.log("Setting up Component Editor with Component: "+n+" and project "+o);let i="project/"+o+"/component/"+n;G.Z.setUniqueKey(this.$store,i),(0,He.fetchInitialConfiguration)(this.$store),this.wsOverride?this.wsUrl=this.wsOverride:this.wsUrl="ws://"+window.location.host+"/ws/ucedit/filechanges/"+i,window.console.log("Attempting socket connection at "+this.wsUrl),this.socket=new Fe(this.wsUrl),this.socket.init((e=>{e.onmessage=e=>{Pe(e,i,this.$store,{notify:this.$q.notify})}}))},methods:{setThemeMode(e){const t=e.getAttribute("data-theme");"com.silabs.ss.platform.theme.dark"===t?this.$q.dark.set(!0):this.$q.dark.set(!1)}},components:{PrimaryWindow:Ue},beforeUnmount(){this.socket&&this.socket.close()}},Qe=(0,N.Z)(Be,[["render",s]]),Re=Qe;var Ye=n(8026),ze=n.n(Ye);async function Ge(e,t){const o=e(Re);o.use(i.Z,t);const a="function"===typeof He["default"]?await(0,He["default"])({}):He["default"],{storeKey:s}=await Promise.resolve().then(n.bind(n,2497)),l=(0,r.Xl)("function"===typeof ze()?await ze()({store:a}):ze());return a.$router=l,{app:o,store:a,storeKey:s,router:l}}var Ke=n(7396),Xe=n(4328);const Je={config:{dark:"auto"},iconSet:Ke.Z,plugins:{Notify:Xe.Z}},et="";async function tt({app:e,router:t,store:n,storeKey:o},i){let r=!1;const a=e=>{try{return t.resolve(e).href}catch(n){}return Object(e)===e?null:e},s=e=>{if(r=!0,"string"===typeof e&&/^https?:\/\//.test(e))return void(window.location.href=e);const t=a(e);null!==t&&(window.location.href=t,window.location.reload())},l=window.location.href.replace(window.location.origin,"");for(let u=0;!1===r&&u<i.length;u++)try{await i[u]({app:e,router:t,store:n,ssrContext:null,redirect:s,urlPath:l,publicPath:et})}catch(c){return c&&c.url?void s(c.url):void console.error("[Quasar] boot error:",c)}!0!==r&&(e.use(t),e.use(n,o),e.mount("#q-app"))}Ge(o.ri,Je).then((e=>Promise.all([Promise.resolve().then(n.bind(n,57)),Promise.resolve().then(n.bind(n,1569))]).then((t=>{const n=t.map((e=>e.default)).filter((e=>"function"===typeof e));tt(e,n)}))))},1463:(e,t,n)=>{"use strict";n.d(t,{Z:()=>d});var o=n(9981),i=n.n(o);const r={"Content-Type":"application/json"};var a=null,s=null;let l=null;function c(e,t={}){return i().get(e,{params:t})}function u(e,t,n={}){return i()({method:"post",url:e,params:n,data:t,headers:r})}const d={setOrigin(e,t){null===a?a=i().create({baseURL:t}):console.log("An attempt was made to set the origin twice. This is not acceptable. Will not set to "+t)},setUniqueKey(e,t){null==s?(s=t,console.log("set rest suffix to "+s)):console.log("An attempt was made to set the unique rest suffix. Will not set to "+origin)},setUniqueProject(e,t){l=t},showInternalThemeSwitch(){return!1},fetchMetadata(){return c("/rest/ucedit/metadata/"+s).then((e=>e.data))},getComponentDocumentation(e,t){var n="/rest/clic/component/details/project/"+l;n+="/getdocs";let o={componentId:t};return c(n,o).then((e=>e.data))},openSource(e){u("/rest/ucedit/action/opensource/"+s,{source:e})},openLaunchable(e){var t="/rest/clic/projectinfo/project/"+l;return t+="/openLaunchable",u(t,e).then((e=>e.data))},openExternalUrl(e){var t="/rest/studio/ui/services/openurl";let n={url:e};return console.log("opening external url: ",e),u(t,n).then((e=>e.data))},closeEditor(){u("/rest/ucedit/action/closeeditor/"+s,{})},cmsisChange(e,t){return u("/rest/ucedit/action/cmsisedit/"+s,{id:e,value:t})},socketsReady(){return console.log("Vue-side is ready! Alerting server to send data when available."),u("/rest/ucedit/action/socketsready/"+s)},changesCommitted(e){return console.log("Vue side reporting configurables update "+e),u("/rest/ucedit/action/configsready/"+s,{conf_class:e})}}},1569:(e,t,n)=>{"use strict";n.r(t),n.d(t,{api:()=>a,default:()=>s});var o=n(3340),i=n(9981),r=n.n(i);const a=r().create({baseURL:"/"}),s=(0,o.xr)((({app:e})=>{e.config.globalProperties.$axios=r(),e.config.globalProperties.$api=a}))},57:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l,i18n:()=>s});var o=n(3340),i=n(9991);const r={failed:"Action failed",success:"Action was successful"},a={"en-us":r},s=(0,i.o)({locale:"en-us",fallbackLocale:"en-us",messages:a}),l=(0,o.xr)((({app:e})=>{e.use(s)}))},8026:()=>{},2497:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>x,fetchInitialConfiguration:()=>E});var o=n(3100);n(702),n(6727);function i(e,t){return S(e,(e=>e.id===t))}function r(e,t){let n=-1,o=0;for(let i of e){if(i.id===t){n=o;break}++o}return n}function a(e,t,n){if(n&&t.parent_category_id){let o=i(e.configurables,t.parent_category_id);a(e,o,n)}else{p(`${f}${t.id}`,n),t.visibility=n;let i=s(e,t.id);for(var o of i)p(`${f}${o.id}`,n),o.visibility=n}}function s(e,t){let n=e.configurables.filter((e=>e.parent_category_id===t&&"cat"===e.type));for(var o of n){let t=s(e,o.id);n=n.concat(t)}return n}function l(e,t){return e.configurables.filter((e=>e.parent_category_id===t))}function c(e,t){for(let n of t)b(e,n)}function u(e){e.metadata.recentChange.pipeline="recent",e.metadata.recentChange.msg="Saving..."}function d(e,t){e.metadata.recentChange.pipeline="response",e.metadata.recentChange.status=t.ok,e.metadata.recentChange.msg=t.msg,void 0!==e.metadata.recentChange.timeout&&clearTimeout(e.metadata.recentChange.timeout),e.metadata.recentChange.timeout=setTimeout((function(){e.metadata.recentChange.pipeline="none"}),3e3)}function g(e,t){let n=i(e,t);return n.visibility}const m="EXPANSION-",f="CATEGORY-";function h(e,t){let n=JSON.parse(localStorage.getItem(e));return"boolean"===typeof n?n:t}function p(e,t){localStorage.setItem(e,t)}function b(e,t){t.visibility=!0,void 0===e[t.id]&&(e[t.id]={expanded:h(`${m}${t.id}`,!0)}),y(e,t)}function y(e,t){let n,o=t.initial;n="check"===t.conf_type?"true"===o||!0===o:(t.conf_type,o),t["modified_value"]=n,t["committed_value"]=n,t.secondary_error||(t["secondary_error"]=null)}function _(e,t){for(let n of t){let t=r(e.configurables,n.id);n.visibility=h(`${f}${n.id}`,n.visibility),-1===t?e.configurables.push(n):e.configurables.splice(t,1,n)}}function v(e,t){for(let n of t){let t=r(e.configurables,n.id),o=e.configurables[t];for(const e in n)"id"!==e&&(o[e]=n[e]);e.configurables.splice(t,1,o)}}function w(e,t){let n=new Set;e.configurables=e.configurables.filter((e=>!t.includes(e.id)||(void 0!==e.parent_category_id&&n.add(e.parent_category_id),!1)));let o=new Set;for(let i of n){let t=l(e,i);0===t.length&&o.add(i)}e.configurables=e.configurables.filter((e=>!o.has(e.id)))}function S(e,t){for(let n of e)if(t(n))return n;return null}function C(e,t){let n=t.filter((e=>"conf"===e.type&&void 0===e.parent_category_id));if(0===n.length)return t;let o=t.filter((e=>"General"===e.label));if(0===o.length){let n={type:"cat",conf_class:"cmsis",label:"General",id:"CMSIS_INTERNAL_CATEGORY_GENERAL"};b(e,n),n.visibility=h(`${f}${n.id}`,n.visibility),t.push(n)}for(let i of n)i["parent_category_id"]="CMSIS_INTERNAL_CATEGORY_GENERAL"}function k(e){let t=[],n=[],o=[];for(let i of e)"CMSIS_INTERNAL_CATEGORY_GENERAL"===i.id?t.push(i):"conf"===i.type||"cmsis"===i.conf_class?n.push(i):o.push(i);return t.concat(n.concat(o))}var I=n(1463);function V(){return{state:{metadata:{type:"meta",label:"",sources:[],componentId:"",recentChange:{pipeline:"none",msg:"",status:!0,timeout:void 0}},configurables:[],origin:null,visualState:{}},actions:{getComponentDocumentation(e,t){return I.Z.getComponentDocumentation(e,t)},openLaunchable(e,t){return I.Z.openLaunchable(t)},openExternalUrl(e,t){return I.Z.openExternalUrl(t)}},mutations:{setMetadata(e,t){void 0!==t.label&&(e.metadata.label=t.label),void 0!==t.sources&&(e.metadata.sources=t.sources),void 0!==t.componentId&&(e.metadata.componentId=t.componentId)},updateConfigurables(e,t){"updated"in t&&(c(e.visualState,t.updated),_(e,t.updated)),"updated_precise"in t&&v(e,t.updated_precise),"removed"in t&&w(e,t.removed),C(e.visualState,e.configurables),e.configurables=k(e.configurables)},toggleCategory(e,t){let n=i(e.configurables,t.id);n.toggle=t.toggle,n.modified_value=t.toggle,u(e)},setCategoryExpansion(e,t){p(`${m}${t.id}`,t.toggle);let n=e.visualState[t.id];n.expanded=t.toggle},setCategoryVisibility(e,t){let n=i(e.configurables,t.id);a(e,n,t.toggle)},setServerError(e,t){let n=i(e.configurables,t.id);if(n){let e=t.msg?t.msg:null;n.secondary_error=e}else console.log("Got a request to set error for "+t.id+" but that configurable does not exist.")},modifyValue(e,t){let n=i(e.configurables,t.id);n.modified_value=t.value},commitValue(e,t){let n=i(e.configurables,t);if(n.committed_value===n.modified_value)return;n.committed_value=n.modified_value,u(e);let o=n.committed_value;o.hasOwnProperty("value")&&(o=o.value),I.Z.cmsisChange(n.id,o).then((function(t){let n=t.data;void 0===n&&(n={msg:"Internal Server Error: No data",ok:!1}),d(e,n)}))}},getters:{componentName(e){return e.metadata.label},componentId(e){return e.metadata.componentId},headerStatus(e){return e.metadata.recentChange},sourceFiles(e){return e.metadata.sources},categories(e){return t=>e.configurables.filter((e=>"cat"===e.type)).filter((e=>null!==t?e.parent_category_id===t:!("parent_category_id"in e)))},dataForConfigurable(e){return t=>i(e.configurables,t)},childrenForCategory(e){return t=>l(e,t)},visibilityOf(e){return t=>g(e.configurables,t)},visualDataFor(e){return t=>e.visualState[t]}}}}const x=(0,o.MT)(V());function E(e){I.Z.fetchMockIfNeeded&&(I.Z.fetchMockIfNeeded().then((t=>e.commit("updateConfigurables",t))),console.log("Initialising using mock data")),I.Z.fetchMetadata().then((t=>e.commit("setMetadata",t)))}}},t={};function n(o){var i=t[o];if(void 0!==i)return i.exports;var r=t[o]={exports:{}};return e[o](r,r.exports,n),r.exports}n.m=e,(()=>{var e=[];n.O=(t,o,i,r)=>{if(!o){var a=1/0;for(u=0;u<e.length;u++){for(var[o,i,r]=e[u],s=!0,l=0;l<o.length;l++)(!1&r||a>=r)&&Object.keys(n.O).every((e=>n.O[e](o[l])))?o.splice(l--,1):(s=!1,r<a&&(a=r));if(s){e.splice(u--,1);var c=i();void 0!==c&&(t=c)}}return t}r=r||0;for(var u=e.length;u>0&&e[u-1][2]>r;u--)e[u]=e[u-1];e[u]=[o,i,r]}})(),(()=>{n.n=e=>{var t=e&&e.__esModule?()=>e["default"]:()=>e;return n.d(t,{a:t}),t}})(),(()=>{n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}})(),(()=>{n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{var e={143:0};n.O.j=t=>0===e[t];var t=(t,o)=>{var i,r,[a,s,l]=o,c=0;if(a.some((t=>0!==e[t]))){for(i in s)n.o(s,i)&&(n.m[i]=s[i]);if(l)var u=l(n)}for(t&&t(o);c<a.length;c++)r=a[c],n.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return n.O(u)},o=self["webpackChunkuc_component_editor"]=self["webpackChunkuc_component_editor"]||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var o=n.O(void 0,[736],(()=>n(5984)));o=n.O(o)})();