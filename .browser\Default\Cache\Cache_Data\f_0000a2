{"html": "\n   <article>\n    \n    <div class=\"header\">\n     <div class=\"headertitle\">\n      <h1 class=\"title\">\n       LED Driver\n      </h1>\n     </div>\n    </div>\n    <div class=\"contents\">\n     <a id=\"details\" name=\"details\">\n     </a>\n     <h2 class=\"groupheader\">\n      Description\n     </h2>\n     <p>\n      Generic LED Driver.\n     </p>\n     <ul>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#leddrv-intro\" target=\"_blank\">\n        Introduction\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#leddrv-config\" target=\"_blank\">\n        Configuration\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#leddrv-usage\" target=\"_blank\">\n        Usage\n       </a>\n      </li>\n     </ul>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"leddrv-intro\">\n      </a>\n      Introduction\n     </h1>\n     <p>\n      The LED driver is a platfom level software module that manages the control of various types of LEDs. There are currently two types of LEDs supported by the LED driver:\n     </p>\n     <ul>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led\" target=\"_blank\">\n        Simple LED Driver\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgbw-pwm-led\" target=\"_blank\">\n        Simple RGBW PWM LED Driver\n       </a>\n      </li>\n     </ul>\n     <p>\n      The common LED functions are called through the generic LED driver, while other functions specific to a certain type of LED are called directly through their own driver.\n     </p>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"leddrv-config\">\n      </a>\n      Configuration\n     </h1>\n     <p>\n      All LED instances are configured with an\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n       sl_led_t\n      </a>\n      struct and a type specific context struct, and sometimes additional structs. There is also a typedef sl_led_state_t to get the state of an LED. These structs are automatically generated after an LED is set up using Simplicity Studio's wizard, along with a function definition for initializing all LEDs of that type. Specific setups for the various LED types are in the following sections.\n     </p>\n     <ul>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#simple-led-config\" target=\"_blank\">\n        Simple LED Configuration\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgbw-pwm-led#rgbw-led-config\" target=\"_blank\">\n        RGBW PWM LED Configuration\n       </a>\n      </li>\n     </ul>\n     <p>\n      <br>\n     </p>\n     <h1>\n      <a class=\"anchor\" id=\"leddrv-usage\">\n      </a>\n      Usage\n     </h1>\n     <p>\n      Once the LED structs are defined, the common LED functions can be called being passed an instance of\n      <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" title=\"A LED instance.\" target=\"_blank\">\n       sl_led_t\n      </a>\n      , which will be redirected to calling the type specific version of that function. The common functions include the following:\n     </p>\n     <ul>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaeec4fd0fb9f100d0f357dbb7974cb6ae\" target=\"_blank\">\n        sl_led_init\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#ga330255a181ca62dfaede4428b71ab9ba\" target=\"_blank\">\n        sl_led_turn_on\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gae4396c5a5963f9fb87a072a98da0cc79\" target=\"_blank\">\n        sl_led_turn_off\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#ga800d7603e870e27d02a76a1438f80ece\" target=\"_blank\">\n        sl_led_toggle\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaeb2bbdaed9dbe3c3f777c75e4624b526\" target=\"_blank\">\n        sl_led_get_state\n       </a>\n      </li>\n     </ul>\n     <p>\n      These functions allow for initializing the LED, turning it on and off, toggling it, and getting the current on/off state of it. Other functions are called through the specific type of LED's API directly. The usages of the different types of LEDs are described in detail in the following sections.\n     </p>\n     <ul>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led#simple-led-usage\" target=\"_blank\">\n        Simple LED Usage\n       </a>\n      </li>\n      <li>\n       <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgbw-pwm-led#rgbw-led-usage\" target=\"_blank\">\n        RGBW PWM LED Usage\n       </a>\n      </li>\n     </ul>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"groups\">\n          </a>\n          Modules\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-led\" target=\"_blank\">\n          Simple LED Driver\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescRight\">\n         Simple LED Driver can be used to execute basic LED functionalities such as on, off, toggle, or retrive the on/off status on Silicon Labs devices. Subsequent sections provide more insight into this module.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgb-pwm-led\" target=\"_blank\">\n          Simple RGB PWM LED Driver\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescRight\">\n         Simple Red/Green/Blue PWM LED Driver.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-simple-rgbw-pwm-led\" target=\"_blank\">\n          Simple RGBW PWM LED Driver\n         </a>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"nested-classes\">\n          </a>\n          Data Structures\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         struct\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n          sl_led_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         A LED instance.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"func-members\">\n          </a>\n          Functions\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         sl_status_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaeec4fd0fb9f100d0f357dbb7974cb6ae\" target=\"_blank\">\n          sl_led_init\n         </a>\n         (const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n          sl_led_t\n         </a>\n         *led_handle)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Initialize the LED driver.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#ga330255a181ca62dfaede4428b71ab9ba\" target=\"_blank\">\n          sl_led_turn_on\n         </a>\n         (const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n          sl_led_t\n         </a>\n         *led_handle)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Turn on the LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gae4396c5a5963f9fb87a072a98da0cc79\" target=\"_blank\">\n          sl_led_turn_off\n         </a>\n         (const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n          sl_led_t\n         </a>\n         *led_handle)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Turn off the LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         void\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#ga800d7603e870e27d02a76a1438f80ece\" target=\"_blank\">\n          sl_led_toggle\n         </a>\n         (const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n          sl_led_t\n         </a>\n         *led_handle)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Toggle the LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaf7839e80cf60eefc89ef109b048bec32\" target=\"_blank\">\n          sl_led_state_t\n         </a>\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaeb2bbdaed9dbe3c3f777c75e4624b526\" target=\"_blank\">\n          sl_led_get_state\n         </a>\n         (const\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n          sl_led_t\n         </a>\n         *led_handle)\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         Get the current state of the LED.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"define-members\">\n          </a>\n          Macros\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#ga2b8ceb6836d2ddb2b1e407dc01cf72be\" target=\"_blank\">\n          SL_LED_CURRENT_STATE_OFF\n         </a>\n         0U\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         LED state off.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         #define\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#ga2ca4a80aaad1f9b67a659bf49b4f0f58\" target=\"_blank\">\n          SL_LED_CURRENT_STATE_ON\n         </a>\n         1U\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         LED state on.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <table class=\"memberdecls\">\n      <tbody>\n       <tr class=\"heading\">\n        <td colspan=\"2\">\n         <h2 class=\"groupheader\">\n          <a name=\"typedef-members\">\n          </a>\n          Typedefs\n         </h2>\n        </td>\n       </tr>\n       <tr class=\"memitem\">\n        <td align=\"right\" class=\"memItemLeft\" valign=\"top\">\n         typedef uint8_t\n        </td>\n        <td class=\"memItemRight\" valign=\"bottom\">\n         <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaf7839e80cf60eefc89ef109b048bec32\" target=\"_blank\">\n          sl_led_state_t\n         </a>\n        </td>\n       </tr>\n       <tr class=\"memdesc\">\n        <td class=\"mdescLeft\">\n        </td>\n        <td class=\"mdescRight\">\n         LED state.\n         <br>\n        </td>\n       </tr>\n       <tr class=\"separator\">\n        <td class=\"memSeparator\" colspan=\"2\">\n        </td>\n       </tr>\n      </tbody>\n     </table>\n     <h2 class=\"groupheader\">\n      Function Documentation\n     </h2>\n     <a id=\"gaeec4fd0fb9f100d0f357dbb7974cb6ae\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaeec4fd0fb9f100d0f357dbb7974cb6ae\">\n        ◆\n       </a>\n      </span>\n      sl_led_init()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           sl_status_t sl_led_init\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n            sl_led_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            led_handle\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Initialize the LED driver.\n       </p>\n       <p>\n        Call this function before any other LED function. Initializes the selected LED GPIO, mode, and polarity.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to instance of\n             <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" title=\"A LED instance.\" target=\"_blank\">\n              sl_led_t\n             </a>\n             to initialize\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         Status Code:\n         <ul>\n          <li>\n           SL_STATUS_OK\n          </li>\n         </ul>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga330255a181ca62dfaede4428b71ab9ba\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga330255a181ca62dfaede4428b71ab9ba\">\n        ◆\n       </a>\n      </span>\n      sl_led_turn_on()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_led_turn_on\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n            sl_led_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            led_handle\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Turn on the LED.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to instance of\n             <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" title=\"A LED instance.\" target=\"_blank\">\n              sl_led_t\n             </a>\n             to turn on\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gae4396c5a5963f9fb87a072a98da0cc79\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gae4396c5a5963f9fb87a072a98da0cc79\">\n        ◆\n       </a>\n      </span>\n      sl_led_turn_off()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_led_turn_off\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n            sl_led_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            led_handle\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Turn off the LED.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to instance of\n             <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" title=\"A LED instance.\" target=\"_blank\">\n              sl_led_t\n             </a>\n             to turn off\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"ga800d7603e870e27d02a76a1438f80ece\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga800d7603e870e27d02a76a1438f80ece\">\n        ◆\n       </a>\n      </span>\n      sl_led_toggle()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           void sl_led_toggle\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n            sl_led_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            led_handle\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Toggle the LED.\n       </p>\n       <p>\n        Turn it on if it is off, and off if it is on.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to instance of\n             <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" title=\"A LED instance.\" target=\"_blank\">\n              sl_led_t\n             </a>\n             to toggle\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <a id=\"gaeb2bbdaed9dbe3c3f777c75e4624b526\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaeb2bbdaed9dbe3c3f777c75e4624b526\">\n        ◆\n       </a>\n      </span>\n      sl_led_get_state()\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaf7839e80cf60eefc89ef109b048bec32\" target=\"_blank\">\n            sl_led_state_t\n           </a>\n           sl_led_get_state\n          </td>\n          <td>\n           (\n          </td>\n          <td class=\"paramtype\">\n           const\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" target=\"_blank\">\n            sl_led_t\n           </a>\n           *\n          </td>\n          <td class=\"paramname\">\n           <code>\n            led_handle\n           </code>\n          </td>\n          <td>\n           )\n          </td>\n          <td>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        Get the current state of the LED.\n       </p>\n       <dl class=\"params\">\n        <dt>\n         Parameters\n        </dt>\n        <dd>\n         <table class=\"params\">\n          <tbody>\n           <tr>\n            <td class=\"paramdir\">\n             [in]\n            </td>\n            <td class=\"paramname\">\n             <code>\n              led_handle\n             </code>\n            </td>\n            <td>\n             Pointer to instance of\n             <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/structsl-led-t\" title=\"A LED instance.\" target=\"_blank\">\n              sl_led_t\n             </a>\n             to check\n            </td>\n           </tr>\n          </tbody>\n         </table>\n        </dd>\n       </dl>\n       <dl class=\"section return\">\n        <dt>\n         Returns\n        </dt>\n        <dd>\n         sl_led_state_t Current state of LED. 1 for on, 0 for off\n        </dd>\n       </dl>\n      </div>\n     </div>\n     <h2 class=\"groupheader\">\n      Macro Definition Documentation\n     </h2>\n     <a id=\"ga2b8ceb6836d2ddb2b1e407dc01cf72be\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga2b8ceb6836d2ddb2b1e407dc01cf72be\">\n        ◆\n       </a>\n      </span>\n      SL_LED_CURRENT_STATE_OFF\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define SL_LED_CURRENT_STATE_OFF&nbsp;&nbsp;&nbsp;0U\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        LED state off.\n       </p>\n      </div>\n     </div>\n     <a id=\"ga2ca4a80aaad1f9b67a659bf49b4f0f58\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#ga2ca4a80aaad1f9b67a659bf49b4f0f58\">\n        ◆\n       </a>\n      </span>\n      SL_LED_CURRENT_STATE_ON\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           #define SL_LED_CURRENT_STATE_ON&nbsp;&nbsp;&nbsp;1U\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        LED state on.\n       </p>\n      </div>\n     </div>\n     <h2 class=\"groupheader\">\n      Typedef Documentation\n     </h2>\n     <a id=\"gaf7839e80cf60eefc89ef109b048bec32\">\n     </a>\n     <h2 class=\"memtitle\">\n      <span class=\"permalink\">\n       <a href=\"#gaf7839e80cf60eefc89ef109b048bec32\">\n        ◆\n       </a>\n      </span>\n      sl_led_state_t\n     </h2>\n     <div class=\"memitem\">\n      <div class=\"memproto\">\n       <table class=\"memname\">\n        <tbody>\n         <tr>\n          <td class=\"memname\">\n           typedef uint8_t\n           <a class=\"el\" href=\"http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led#gaf7839e80cf60eefc89ef109b048bec32\" target=\"_blank\">\n            sl_led_state_t\n           </a>\n          </td>\n         </tr>\n        </tbody>\n       </table>\n      </div>\n      <div class=\"memdoc\">\n       <p>\n        LED state.\n       </p>\n      </div>\n     </div>\n    </div>\n   </article>\n  ", "url": "http://docs.silabs.com/gecko-platform/4.1/driver/api/group-led", "status": "success"}