(()=>{var e={7434:(e,t,n)=>{"use strict";n(8964),n(702);var o=n(1957),s=n(1947),i=n(499),a=n(9835),l=n(6970);const r={class:"main-project-name"};function c(e,t,n,o,s,i){const c=(0,a.up)("home-made-toast"),d=(0,a.up)("q-tooltip"),p=(0,a.up)("q-tab"),u=(0,a.up)("q-tabs"),m=(0,a.up)("q-separator"),h=(0,a.up)("q-card"),f=(0,a.up)("q-header"),g=(0,a.up)("q-tab-panel"),w=(0,a.up)("q-tab-panels"),b=(0,a.up)("q-page"),k=(0,a.up)("q-page-container"),C=(0,a.up)("q-layout");return(0,a.wg)(),(0,a.j4)(C,{view:"hHh Lpr lFf"},{default:(0,a.w5)((()=>[(0,a.Wm)(c,{loading:i.projectReportInfo.loading,success:i.projectReportInfo.success,msg:i.projectReportInfo.msg},{default:(0,a.w5)((()=>[""!==i.projectReportInfo.path&&i.projectReportInfo.success?((0,a.wg)(),(0,a.iD)("button",{key:0,class:"linklike","no-caps":"",onClick:t[0]||(t[0]=e=>i.openProjectReport())}," View File ")):(0,a.kq)("",!0)])),_:1},8,["loading","success","msg"]),(0,a.Wm)(f,null,{default:(0,a.w5)((()=>[(0,a.Wm)(h,{square:"",flat:"",id:"main-app-card"},{default:(0,a.w5)((()=>[i.validationErrors?((0,a.wg)(),(0,a.iD)("div",{key:0,class:"error-bar row justify-center items-center",onClick:t[1]||(t[1]=(...e)=>i.showValidationErrors&&i.showValidationErrors(...e))},(0,l.zw)(e.errorBarMessage),1)):(0,a.kq)("",!0),(0,a.Wm)(u,{id:"main-app-tabs",modelValue:i.tab,"onUpdate:modelValue":t[2]||(t[2]=e=>i.tab=e),dense:"",shrink:"","active-color":"primary","indicator-color":"primary",align:"left","narrow-indicator":""},{default:(0,a.w5)((()=>[(0,a._)("span",r,(0,l.zw)(i.projectInfo.name),1),((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(e.rootMenu,(e=>((0,a.wg)(),(0,a.j4)(p,{class:"root-menu-text","no-caps":"",name:e.name,label:e.label,key:e.name},{default:(0,a.w5)((()=>[e.tooltip?((0,a.wg)(),(0,a.j4)(d,{key:0,"max-width":"250px"},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(e.tooltip),1)])),_:2},1024)):(0,a.kq)("",!0)])),_:2},1032,["name","label"])))),128))])),_:1},8,["modelValue"]),(0,a.Wm)(m)])),_:1})])),_:1}),(0,a.Wm)(k,{id:"main-page-container"},{default:(0,a.w5)((()=>[(0,a.Wm)(b,{"style-fn":i.getPageStyle},{default:(0,a.w5)((()=>[(0,a.Wm)(w,{modelValue:i.tab,"onUpdate:modelValue":t[3]||(t[3]=e=>i.tab=e),animated:"","keep-alive":""},{default:(0,a.w5)((()=>[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(e.rootMenu,(t=>((0,a.wg)(),(0,a.j4)(g,{name:t.name,key:t.name},{default:(0,a.w5)((()=>[((0,a.wg)(),(0,a.j4)((0,a.LL)(t.component),{class:"uc-overview-tab-component",projectInfo:i.projectInfo,defaultBoard:e.defaultBoardStr},null,8,["projectInfo","defaultBoard"]))])),_:2},1032,["name"])))),128))])),_:1},8,["modelValue"])])),_:1},8,["style-fn"])])),_:1})])),_:1})}n(3269);const d=e=>((0,a.dD)("data-v-d9d2def4"),e=e(),(0,a.Cn)(),e),p={class:"q-pa-md q-gutter-sm components-panel"},u={class:"filter-block col-auto row items-center justify-start no-wrap"},m={class:"row filter-elem-small"},h={id:"DDLConfigurableChkBox",class:"row justify-center"},f={class:"ddlElementsLabel"},g={key:0},w={key:1},b=(0,a.Uk)(" Show only components that are configurable "),k=(0,a.Uk)(" No configurable components are available "),C={id:"DDLInstalledChkBox",class:"row justify-center"},v={class:"ddlElementsLabel"},I={key:0},y={key:1},S=(0,a.Uk)(" Show only components that are installed in your project "),_=(0,a.Uk)(" No components are installed in your project "),x={id:"DDLUserSelectedChkBox",class:"row justify-center"},j={class:"ddlElementsLabel"},D={key:0},E={key:1},q=(0,a.Uk)(" Show only components explicitly installed by you or the initial project "),U=(0,a.Uk)(" No components explicitly installed by you or the initial project exist "),W={id:"DDLSDKExtensionsChkBox",class:"row justify-center"},N={class:"ddlElementsLabel"},T={key:0},P={key:1},V=(0,a.Uk)(" Show only SDK extensions "),L=(0,a.Uk)(" No SDK extensions components are available "),Q={key:0,id:"DDLShowAdvancedChkBox",class:"row justify-center"},O=d((()=>(0,a._)("label",{class:"ddlToggleLabel"}," Show advanced",-1))),Z=(0,a.Uk)(" Filter advanced components "),$=d((()=>(0,a._)("div",{id:"filterLabel",class:"col-auto filter-block-label filter-elem-wide"}," Filter components by ",-1))),F={key:0},M={key:1},B=(0,a.Uk)(" Show only components that are configurable "),R=(0,a.Uk)(" No configurable components are available "),A={key:0},z={key:1},K=(0,a.Uk)(" Show only components that are installed in your project "),H=(0,a.Uk)(" No components are installed in your project "),G={key:0},Y={key:1},J=(0,a.Uk)(" Show only components explicitly installed by you or the initial project "),X=(0,a.Uk)(" No components explicitly installed by you or the initial project exist "),ee={key:0},te={key:1},ne=(0,a.Uk)(" Show only SDK extensions "),oe=(0,a.Uk)(" No SDK extensions components are available "),se=(0,a.Uk)(" Filter advanced components "),ie={class:"select-keywords-container filter-block-col col items-end"},ae={class:"row"},le=(0,a.Uk)("Expand"),re=(0,a.Uk)("Collapse"),ce=(0,a.Uk)("Expand All"),de=(0,a.Uk)("Collapse All"),pe={key:0,class:"row justify-center items-center"},ue={class:"row items-center justify-left category-header"},me=["id"],he={class:"row items-center justify-left component-header"},fe={key:1,class:"no-icon-spacer"},ge=["id"],we=(0,a.Uk)(" Open editor for components configuration file(s) "),be=(0,a.Uk)(" No configuration file(s) have been created for this component "),ke={class:"row items-center justify-left component-header"},Ce=["id"],ve=(0,a.Uk)(" Open editor for components configuration file(s) "),Ie=(0,a.Uk)(" No configuration file(s) have been created for this component "),ye={class:"row items-center justify-left component-header"},Se=["id"];function _e(e,t,n,s,i,r){const c=(0,a.up)("q-icon"),d=(0,a.up)("q-tooltip"),_e=(0,a.up)("q-checkbox"),xe=(0,a.up)("q-toggle"),je=(0,a.up)("FilterSelectionGroup"),De=(0,a.up)("q-btn-dropdown"),Ee=(0,a.up)("q-select"),qe=(0,a.up)("q-item-section"),Ue=(0,a.up)("q-item"),We=(0,a.up)("q-list"),Ne=(0,a.up)("q-menu"),Te=(0,a.up)("q-spinner-ios"),Pe=(0,a.up)("q-tree"),Ve=(0,a.up)("q-splitter"),Le=(0,a.Q2)("close-popup");return(0,a.wg)(),(0,a.iD)("div",p,[(0,a._)("div",u,[(0,a.Wm)(De,{id:"DDList",outline:"","no-caps":"",class:"filter-elem-small",icon:e.mdiFilter,label:this.filterComponentsByLabel},{default:(0,a.w5)((()=>[(0,a._)("div",m,[(0,a._)("div",h,[(0,a.Wm)(_e,{"left-label":"",class:"row",modelValue:e.showConfigurable,"onUpdate:modelValue":t[0]||(t[0]=t=>e.showConfigurable=t),disable:!r.findConfigurableComponent()},{default:(0,a.w5)((()=>[(0,a._)("label",f,[(0,a.Wm)(c,{name:e.mdiCog,size:"xs"},null,8,["name"]),r.findConfigurableComponent()?((0,a.wg)(),(0,a.iD)("a",w," Configurable")):((0,a.wg)(),(0,a.iD)("em",g," Configurable"))]),r.findConfigurableComponent()?((0,a.wg)(),(0,a.j4)(d,{key:0,"max-width":"250px"},{default:(0,a.w5)((()=>[b])),_:1})):((0,a.wg)(),(0,a.j4)(d,{key:1,"max-width":"250px"},{default:(0,a.w5)((()=>[k])),_:1}))])),_:1},8,["modelValue","disable"])]),(0,a._)("div",C,[(0,a.Wm)(_e,{class:"row",modelValue:e.showInstalled,"onUpdate:modelValue":t[1]||(t[1]=t=>e.showInstalled=t),"left-label":"",disable:!r.findInstalledComponent()},{default:(0,a.w5)((()=>[(0,a._)("label",v,[(0,a.Wm)(c,{name:e.mdiCheckboxMarkedCircle,size:"xs"},null,8,["name"]),r.findInstalledComponent()?((0,a.wg)(),(0,a.iD)("a",y," Installed")):((0,a.wg)(),(0,a.iD)("em",I," Installed"))]),r.findInstalledComponent()?((0,a.wg)(),(0,a.j4)(d,{key:0,"max-width":"250px"},{default:(0,a.w5)((()=>[S])),_:1})):((0,a.wg)(),(0,a.j4)(d,{key:1,"max-width":"250px"},{default:(0,a.w5)((()=>[_])),_:1}))])),_:1},8,["modelValue","disable"])]),(0,a._)("div",x,[(0,a.Wm)(_e,{"left-label":"",class:"row",modelValue:e.showUserSelected,"onUpdate:modelValue":t[2]||(t[2]=t=>e.showUserSelected=t),disable:!r.findUserSelectedComponent()},{default:(0,a.w5)((()=>[(0,a._)("label",j,[(0,a.Wm)(c,{name:e.mdiAccount,size:"xs"},null,8,["name"]),r.findUserSelectedComponent()?((0,a.wg)(),(0,a.iD)("a",E," Installed by you")):((0,a.wg)(),(0,a.iD)("em",D," Installed by you"))]),r.findUserSelectedComponent()?((0,a.wg)(),(0,a.j4)(d,{key:0,"max-width":"250px"},{default:(0,a.w5)((()=>[q])),_:1})):((0,a.wg)(),(0,a.j4)(d,{key:1,"max-width":"250px"},{default:(0,a.w5)((()=>[U])),_:1}))])),_:1},8,["modelValue","disable"])]),(0,a._)("div",W,[(0,a.Wm)(_e,{class:"row","left-label":"",modelValue:e.showSDKExtensions,"onUpdate:modelValue":t[3]||(t[3]=t=>e.showSDKExtensions=t),disable:!r.findSDKExtensionComponent()},{default:(0,a.w5)((()=>[(0,a._)("label",N,[(0,a.Wm)(c,{name:e.mdiFolderMultiplePlus,size:"xs"},null,8,["name"]),r.findSDKExtensionComponent()?((0,a.wg)(),(0,a.iD)("a",P," SDK Extensions")):((0,a.wg)(),(0,a.iD)("em",T," SDK Extensions"))]),r.findSDKExtensionComponent()?((0,a.wg)(),(0,a.j4)(d,{key:0,"max-width":"250px"},{default:(0,a.w5)((()=>[V])),_:1})):((0,a.wg)(),(0,a.j4)(d,{key:1,"max-width":"250px"},{default:(0,a.w5)((()=>[L])),_:1}))])),_:1},8,["modelValue","disable"])]),r.functionRequired?((0,a.wg)(),(0,a.iD)("div",Q,[(0,a.Wm)(xe,{class:"row","left-label":"",modelValue:e.showAdvancedComponents,"onUpdate:modelValue":t[4]||(t[4]=t=>e.showAdvancedComponents=t)},{default:(0,a.w5)((()=>[O,(0,a.Wm)(d,{"max-width":"250px"},{default:(0,a.w5)((()=>[Z])),_:1})])),_:1},8,["modelValue"])])):(0,a.kq)("",!0),(0,a.Wm)(je,{filterGroupSelections:this.selectedQualities,filterGroupOptions:this.qualityOptions,filterGroupIcon:e.mdiSeal,labelTxt:this.qualityLabelTxt,toolTipTxt:this.qualityToolTipTxt,useDropdown:!1,onChangeFilterSelections:r.updateSelectedQualities},null,8,["filterGroupSelections","filterGroupOptions","filterGroupIcon","labelTxt","toolTipTxt","onChangeFilterSelections"])])])),_:1},8,["icon","label"]),(0,a.Wm)(c,{name:e.mdiFilter,class:"col-1 icon-filter filter-elem-wide"},null,8,["name"]),$,(0,a.Wm)(_e,{id:"ConfigurableChkBox",class:"filter-block-cb filter-block-col filter-elem-wide col-auto",modelValue:e.showConfigurable,"onUpdate:modelValue":t[5]||(t[5]=t=>e.showConfigurable=t),"left-label":"",disable:!r.findConfigurableComponent()},{default:(0,a.w5)((()=>[(0,a._)("label",null,[(0,a.Wm)(c,{name:e.mdiCog,size:"xs"},null,8,["name"]),r.findConfigurableComponent()?((0,a.wg)(),(0,a.iD)("a",M," Configurable")):((0,a.wg)(),(0,a.iD)("em",F," Configurable"))]),r.findConfigurableComponent()?((0,a.wg)(),(0,a.j4)(d,{key:0,"max-width":"250px"},{default:(0,a.w5)((()=>[B])),_:1})):((0,a.wg)(),(0,a.j4)(d,{key:1,"max-width":"250px"},{default:(0,a.w5)((()=>[R])),_:1}))])),_:1},8,["modelValue","disable"]),(0,a.Wm)(_e,{id:"InstalledChkBox",class:"filter-block-cb filter-block-col filter-elem-wide col-auto",modelValue:e.showInstalled,"onUpdate:modelValue":t[6]||(t[6]=t=>e.showInstalled=t),"left-label":"",disable:!r.findInstalledComponent()},{default:(0,a.w5)((()=>[(0,a._)("label",null,[(0,a.Wm)(c,{name:e.mdiCheckboxMarkedCircle,size:"xs"},null,8,["name"]),r.findInstalledComponent()?((0,a.wg)(),(0,a.iD)("a",z," Installed")):((0,a.wg)(),(0,a.iD)("em",A," Installed"))]),r.findInstalledComponent()?((0,a.wg)(),(0,a.j4)(d,{key:0,"max-width":"250px"},{default:(0,a.w5)((()=>[K])),_:1})):((0,a.wg)(),(0,a.j4)(d,{key:1,"max-width":"250px"},{default:(0,a.w5)((()=>[H])),_:1}))])),_:1},8,["modelValue","disable"]),(0,a.Wm)(_e,{id:"UserSelectedChkBox",class:"filter-block-cb filter-block-col filter-elem-wide col-auto",modelValue:e.showUserSelected,"onUpdate:modelValue":t[7]||(t[7]=t=>e.showUserSelected=t),"left-label":"",disable:!r.findUserSelectedComponent()},{default:(0,a.w5)((()=>[(0,a._)("label",null,[(0,a.Wm)(c,{name:e.mdiAccount,size:"xs"},null,8,["name"]),r.findUserSelectedComponent()?((0,a.wg)(),(0,a.iD)("a",Y," Installed by you")):((0,a.wg)(),(0,a.iD)("em",G," Installed by you"))]),r.findUserSelectedComponent()?((0,a.wg)(),(0,a.j4)(d,{key:0,"max-width":"250px"},{default:(0,a.w5)((()=>[J])),_:1})):((0,a.wg)(),(0,a.j4)(d,{key:1,"max-width":"250px"},{default:(0,a.w5)((()=>[X])),_:1}))])),_:1},8,["modelValue","disable"]),(0,a.Wm)(_e,{id:"SDKExtensionsChkBox",class:"filter-block-cb filter-block-col filter-elem-wide col-auto",modelValue:e.showSDKExtensions,"onUpdate:modelValue":t[8]||(t[8]=t=>e.showSDKExtensions=t),"left-label":"",disable:!r.findSDKExtensionComponent()},{default:(0,a.w5)((()=>[(0,a._)("label",null,[(0,a.Wm)(c,{name:e.mdiFolderMultiplePlus,size:"xs"},null,8,["name"]),r.findSDKExtensionComponent()?((0,a.wg)(),(0,a.iD)("a",te," SDK Extensions")):((0,a.wg)(),(0,a.iD)("em",ee," SDK Extensions"))]),r.findSDKExtensionComponent()?((0,a.wg)(),(0,a.j4)(d,{key:0,"max-width":"250px"},{default:(0,a.w5)((()=>[ne])),_:1})):((0,a.wg)(),(0,a.j4)(d,{key:1,"max-width":"250px"},{default:(0,a.w5)((()=>[oe])),_:1}))])),_:1},8,["modelValue","disable"]),(0,a.Wm)(je,{filterGroupSelections:this.selectedQualities,filterGroupOptions:this.qualityOptions,filterGroupIcon:e.mdiSeal,labelTxt:this.qualityLabelTxt,toolTipTxt:this.qualityToolTipTxt,useDropdown:!0,onChangeFilterSelections:r.updateSelectedQualities},null,8,["filterGroupSelections","filterGroupOptions","filterGroupIcon","labelTxt","toolTipTxt","onChangeFilterSelections"]),r.functionRequired?((0,a.wg)(),(0,a.j4)(xe,{key:0,id:"AdvancedComponentsChkBox",class:"filter-block-cb filter-block-col filter-elem-wide col-auto",modelValue:e.showAdvancedComponents,"onUpdate:modelValue":t[9]||(t[9]=t=>e.showAdvancedComponents=t),"left-label":"",label:"Show advanced"},{default:(0,a.w5)((()=>[(0,a.Wm)(d,{"max-width":"250px"},{default:(0,a.w5)((()=>[se])),_:1})])),_:1},8,["modelValue"])):(0,a.kq)("",!0),(0,a._)("div",ie,[(0,a._)("div",ae,[(0,a.Wm)(Ee,{ref:"selectFilter",outlined:"",label:"Search keywords, component's name",modelValue:e.selectFilter,"onUpdate:modelValue":t[10]||(t[10]=t=>e.selectFilter=t),"use-input":"","emit-value":"","use-chips":"",multiple:"",clearable:"","hide-dropdown-icon":"",dense:"","input-debounce":"100",onNewValue:r.newTreeFilterItem,onFilter:r.selectInputChanged,onBlur:r.filterInputFocusOut,id:"select-keywords",class:"select-keywords"},{prepend:(0,a.w5)((()=>[e.selectFilter&&0!==e.selectFilter.length?(0,a.kq)("",!0):((0,a.wg)(),(0,a.j4)(c,{key:0,name:e.mdiMagnify,class:"cursor-pointer"},null,8,["name"]))])),_:1},8,["modelValue","onNewValue","onFilter","onBlur"])])])]),(0,a.Wm)(Ve,{modelValue:e.splitterModel,"onUpdate:modelValue":t[15]||(t[15]=t=>e.splitterModel=t),limits:[30,70],class:"comp-splitter col items-stretch row","before-class":"comp-splitter-pane comp-splitter-pane-before","after-class":"comp-splitter-pane comp-splitter-pane-after"},{before:(0,a.w5)((()=>[(0,a._)("div",null,[(0,a.Wm)(Ne,{ref:"contextmenu","no-refocus":"","touch-position":"","context-menu":"","auto-close":"",onShow:r.contextMenuShown,onHide:r.contextMenuHidden},{default:(0,a.w5)((()=>[(0,a.Wm)(We,{dense:"",style:{"min-width":"100px"}},{default:(0,a.w5)((()=>[(0,a.wy)(((0,a.wg)(),(0,a.j4)(Ue,{clickable:"",disable:r.expandCollapseInactive},{default:(0,a.w5)((()=>[(0,a.Wm)(qe,{onClick:t[11]||(t[11]=e=>r.setExpandState(!0))},{default:(0,a.w5)((()=>[le])),_:1})])),_:1},8,["disable"])),[[Le]]),(0,a.wy)(((0,a.wg)(),(0,a.j4)(Ue,{clickable:"",disable:r.expandCollapseInactive},{default:(0,a.w5)((()=>[(0,a.Wm)(qe,{onClick:t[12]||(t[12]=e=>r.setExpandState(!1))},{default:(0,a.w5)((()=>[re])),_:1})])),_:1},8,["disable"])),[[Le]]),(0,a.wy)(((0,a.wg)(),(0,a.j4)(Ue,{clickable:""},{default:(0,a.w5)((()=>[(0,a.Wm)(qe,{onClick:r.expandAll},{default:(0,a.w5)((()=>[ce])),_:1},8,["onClick"])])),_:1})),[[Le]]),(0,a.wy)(((0,a.wg)(),(0,a.j4)(Ue,{clickable:""},{default:(0,a.w5)((()=>[(0,a.Wm)(qe,{onClick:r.collapseAll},{default:(0,a.w5)((()=>[de])),_:1},8,["onClick"])])),_:1})),[[Le]])])),_:1})])),_:1},8,["onShow","onHide"]),r.hasComponents?(0,a.kq)("",!0):((0,a.wg)(),(0,a.iD)("div",pe,[(0,a.Wm)(Te,{color:"primary",size:"4em"})])),r.hasComponents?((0,a.wg)(),(0,a.j4)(Pe,{key:1,ref:"comptree",class:"component-tree","no-connectors":"",nodes:r.uccomponents,"node-key":"id",color:"comptreecolor","text-color":"comptreecolor","selected-color":"primary",selected:e.selected,"onUpdate:selected":[t[13]||(t[13]=t=>e.selected=t),r.selectionChanged],expanded:e.expanded,"onUpdate:expanded":t[14]||(t[14]=t=>e.expanded=t),filter:e.treefilter,"filter-method":r.componentTreeFilter,dense:""},{"default-header":(0,a.w5)((e=>[(0,a._)("div",ue,[(0,a._)("div",{id:r.makeTreeId("",e.node.id),class:"col-12 component-tree-node"},(0,l.zw)(e.node.label),9,me)])])),"header-component":(0,a.w5)((t=>[(0,a._)("div",he,[t.node.isSelected&&!t.node.children?((0,a.wg)(),(0,a.j4)(c,{key:0,name:e.mdiCheckCircleOutline,class:"col-1"},null,8,["name"])):t.node.isSelected||t.node.children?(0,a.kq)("",!0):((0,a.wg)(),(0,a.iD)("span",fe)),(0,a._)("span",{id:r.makeTreeId("",t.node.id),class:"col component-tree-node"},(0,l.zw)(t.node.label),9,ge),t.node.isConfigurable?((0,a.wg)(),(0,a.j4)(c,{key:2,"data-studio-comp":t.node.componentId,"data-studio-inst":t.node.instanceName,class:(0,l.C_)(r.getConfigurableClass(t.node)),name:e.mdiCog,onClick:(0,o.iM)((e=>r.configureComponent(t.node)),["stop"])},{default:(0,a.w5)((()=>[t.node.isEditable?((0,a.wg)(),(0,a.j4)(d,{key:0,"max-width":"250px"},{default:(0,a.w5)((()=>[we])),_:1})):(0,a.kq)("",!0),t.node.isEditable?(0,a.kq)("",!0):((0,a.wg)(),(0,a.j4)(d,{key:1,"max-width":"250px"},{default:(0,a.w5)((()=>[be])),_:1}))])),_:2},1032,["data-studio-comp","data-studio-inst","class","name","onClick"])):(0,a.kq)("",!0)])])),"header-instance":(0,a.w5)((t=>[(0,a._)("div",ke,[t.node.isSelected&&!t.node.children?((0,a.wg)(),(0,a.j4)(c,{key:0,name:e.mdiCheckCircleOutline,class:"col-1 icon-installed"},null,8,["name"])):(0,a.kq)("",!0),(0,a._)("span",{id:r.makeTreeId("",t.node.id),class:"col component-tree-node"},(0,l.zw)(t.node.label),9,Ce),t.node.isConfigurable?((0,a.wg)(),(0,a.j4)(c,{key:1,"data-studio-comp":t.node.componentId,"data-studio-inst":t.node.instanceName,class:(0,l.C_)(r.getConfigurableClass(t.node)),name:e.mdiCog,onClick:(0,o.iM)((e=>r.configureComponent(t.node)),["stop"])},{default:(0,a.w5)((()=>[t.node.isEditable?((0,a.wg)(),(0,a.j4)(d,{key:0,"max-width":"250px"},{default:(0,a.w5)((()=>[ve])),_:1})):(0,a.kq)("",!0),t.node.isEditable?(0,a.kq)("",!0):((0,a.wg)(),(0,a.j4)(d,{key:1,"max-width":"250px"},{default:(0,a.w5)((()=>[Ie])),_:1}))])),_:2},1032,["data-studio-comp","data-studio-inst","class","name","onClick"])):(0,a.kq)("",!0)])])),"header-shortcut":(0,a.w5)((t=>[(0,a._)("div",ye,[(0,a.Wm)(c,{name:e.mdiCheckCircleOutline,class:"col-1"},null,8,["name"]),(0,a._)("span",{id:r.makeTreeId("",t.node.id),class:"col component-tree-node"},(0,l.zw)(t.node.label),9,Se),(0,a.Wm)(c,{"data-studio-comp":t.node.componentId,class:(0,l.C_)(r.getConfigurableClass(t.node)),name:e.mdiCogs,onClick:(0,o.iM)((e=>r.openLaunchable(t.node)),["stop"])},{default:(0,a.w5)((()=>[(0,a.Wm)(d,{"max-width":"250px"},{default:(0,a.w5)((()=>[(0,a.Uk)(" Open "+(0,l.zw)(t.node.label),1)])),_:2},1024)])),_:2},1032,["data-studio-comp","class","name","onClick"])])])),_:1},8,["nodes","selected","expanded","filter","filter-method","onUpdate:selected"])):(0,a.kq)("",!0)])])),after:(0,a.w5)((()=>[(0,a.Wm)(o.uT,{name:"component-fade",mode:"out-in"},{default:(0,a.w5)((()=>[void 0!==r.selectedComponent?((0,a.wg)(),(0,a.j4)((0,a.LL)(e.componentDetailsCard),{key:e.selected,component:r.selectedComponent,componentDetails:e.currentComponentDetails,onSelectComponent:r.selectComponent},null,40,["component","componentDetails","onSelectComponent"])):void 0!==r.selectedLaunchable?((0,a.wg)(),(0,a.j4)((0,a.LL)(e.launchableDetailsCard),{key:e.selected,launchable:r.selectedLaunchable},null,8,["launchable"])):void 0!==r.selectedSDKExtension?((0,a.wg)(),(0,a.j4)((0,a.LL)(e.SdkExtensionCard),{key:e.selected,component:r.selectedSDKExtension,onSelectComponent:r.selectComponent},null,40,["component","onSelectComponent"])):(0,a.kq)("",!0)])),_:1})])),_:1},8,["modelValue"])])}n(6727);function xe(e,t,n,s,i,l){const r=(0,a.up)("ActionBanner");return(0,a.wg)(),(0,a.iD)("div",null,[(0,a.Wm)(o.uT,{name:"component-fade",mode:"out-in"},{default:(0,a.w5)((()=>[((0,a.wg)(),(0,a.j4)((0,a.LL)(e.currentPage),{component:n.component,componentDetails:n.componentDetails,onShowDepends:l.showDependencies,onShowDetails:l.showDetails,onSelectComponent:l.selectComponent},{banner:(0,a.w5)((()=>["SDKExtensionNode"!==n.component.categoryType||n.component.isEnabled?(0,a.kq)("",!0):((0,a.wg)(),(0,a.j4)(r,{key:0,actionLabel:"View extension",onActionClick:t[0]||(t[0]=e=>l.selectRoot(n.component)),bannerText:"Extension must be enabled for this project to install this component."}))])),_:1},40,["component","componentDetails","onShowDepends","onShowDetails","onSelectComponent"]))])),_:1})])}const je=e=>((0,a.dD)("data-v-d6a4b644"),e=e(),(0,a.Cn)(),e),De={class:"component-template"},Ee={class:"component-column"},qe={class:"component-header-row"},Ue={class:"row items-center outer-row"},We=je((()=>(0,a._)("div",{class:"col-1 card-title-block-bar"},null,-1))),Ne={class:"col"},Te={class:"row items-center card-title-block-content inner-row"},Pe={key:0,class:"col-auto card-title-block-col ellipsis qa-details-title qa-component-details-title"},Ve={class:"col card-title-block-col config-button-col"},Le=(0,a.Uk)(" Open editor for components configuration file(s) "),Qe=(0,a.Uk)(" No configuration file(s) have been created for this component "),Oe=(0,a.Uk)(" Add component to project "),Ze={class:"component-content-row"},$e=je((()=>(0,a._)("div",{class:"text-lg"},"Description",-1))),Fe={key:0,class:"description text-sm"},Me={class:"description-code-block"},Be={class:"row justify-start"},Re={class:"col-auto"},Ae={class:"column"},ze=je((()=>(0,a._)("div",{class:"text-lg"},"Quality",-1))),Ke={key:0,class:"text-sm"},He={key:1,class:"col-auto"},Ge={class:"column"},Ye=je((()=>(0,a._)("div",{class:"text-lg"},"Tags",-1))),Je={class:"text-sm"},Xe={class:"component-control-row"},et=(0,a.Uk)(" Can not remove components added by dependency "),tt=(0,a.Uk)(" Remove component from project "),nt=je((()=>(0,a._)("div",{class:"text-h6 content-center"},"Create A Component Instance",-1))),ot=(0,a.Uk)(" This component allows multiple instances. Create a name for this instance in the field below. The name will be used to construct #defines in the source files of the instance. "),st=je((()=>(0,a._)("div",{class:"text-h6"},"INSTANCE NAME",-1))),it=je((()=>(0,a._)("div",{class:"text-h6 content-center"},"Delete A Component Instance",-1))),at=(0,a.Uk)(" This component has multiple instances. Please select the instance(s) that you want to remove. ");function lt(e,t,n,s,i,r){const c=(0,a.up)("q-tooltip"),d=(0,a.up)("q-btn"),p=(0,a.up)("q-card-section"),u=(0,a.up)("q-card"),m=(0,a.up)("q-space"),h=(0,a.up)("q-separator"),f=(0,a.up)("q-chip"),g=(0,a.up)("DependencyCardComponent"),w=(0,a.up)("DocumentationCardComponent"),b=(0,a.up)("q-input"),k=(0,a.up)("q-item-section"),C=(0,a.up)("q-item"),v=(0,a.up)("q-list"),I=(0,a.up)("q-expansion-item"),y=(0,a.up)("q-card-actions"),S=(0,a.up)("q-dialog"),_=(0,a.up)("q-option-group"),x=(0,a.Q2)("close-popup");return(0,a.wg)(),(0,a.iD)("div",De,[(0,a.WI)(e.$slots,"banner",{},void 0,!0),(0,a._)("div",Ee,[(0,a._)("div",qe,[(0,a.Wm)(u,{square:"",bordered:"",class:"component-template-header"},{default:(0,a.w5)((()=>[(0,a.Wm)(p,{class:"component-template-header card-title-block"},{default:(0,a.w5)((()=>[(0,a._)("div",Ue,[We,(0,a._)("div",Ne,[(0,a._)("div",Te,[void 0!==n.component?((0,a.wg)(),(0,a.iD)("span",Pe,[(0,a.Uk)((0,l.zw)(n.component.label)+" ",1),(0,a.Wm)(c,{"max-width":"250px"},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(n.component.label),1)])),_:1})])):(0,a.kq)("",!0),(0,a._)("span",Ve,[r.isConfigurable&&!r.canInstall?((0,a.wg)(),(0,a.j4)(d,{key:0,"data-studio-comp":n.component.componentId,"data-studio-inst":n.component.instanceName,id:"component-details-configure-button",flat:"",dense:"",align:"around",type:"a","no-caps":"",label:"Configure",disable:!r.isEditable,class:(0,l.C_)(r.getConfigurableClass()),icon:e.mdiCog,onClick:t[0]||(t[0]=e=>r.configureComponent(n.component))},{default:(0,a.w5)((()=>[r.isEditable?((0,a.wg)(),(0,a.j4)(c,{key:0,"max-width":"250px"},{default:(0,a.w5)((()=>[Le])),_:1})):(0,a.kq)("",!0),r.isEditable?(0,a.kq)("",!0):((0,a.wg)(),(0,a.j4)(c,{key:1,"max-width":"250px"},{default:(0,a.w5)((()=>[Qe])),_:1}))])),_:1},8,["data-studio-comp","data-studio-inst","disable","class","icon"])):(0,a.kq)("",!0),r.canInstall?((0,a.wg)(),(0,a.j4)(d,{key:1,id:"component-details-install-button",disable:!n.component.isEnabled,"no-caps":"",class:"positive-button col-auto",label:"Install",onClick:t[1]||(t[1]=e=>r.isInstantiable?r.showInstanceDialog():r.addComponent(n.component.componentId))},{default:(0,a.w5)((()=>[r.canInstall?((0,a.wg)(),(0,a.j4)(c,{key:0,"content-class":"bg-accent"},{default:(0,a.w5)((()=>[Oe])),_:1})):(0,a.kq)("",!0)])),_:1},8,["disable"])):(0,a.kq)("",!0)])])])])])),_:1})])),_:1})]),(0,a._)("div",Ze,[(0,a.Wm)(u,{square:"",bordered:"",class:"description-card card-content"},{default:(0,a.w5)((()=>[$e,void 0!==n.component?((0,a.wg)(),(0,a.iD)("div",Fe,[(0,a._)("code",Me,(0,l.zw)(n.component.description),1)])):(0,a.kq)("",!0),(0,a.Wm)(m),(0,a._)("div",Be,[(0,a._)("div",Re,[(0,a._)("div",Ae,[ze,void 0!==n.component?((0,a.wg)(),(0,a.iD)("div",Ke,(0,l.zw)(n.component.quality),1)):(0,a.kq)("",!0)])]),r.tags?((0,a.wg)(),(0,a.j4)(h,{key:0,vertical:""})):(0,a.kq)("",!0),r.tags?((0,a.wg)(),(0,a.iD)("div",He,[(0,a._)("div",Ge,[Ye,(0,a._)("div",Je,[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(r.tags,(e=>((0,a.wg)(),(0,a.j4)(f,{key:e,square:""},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(e),1)])),_:2},1024)))),128))])])])):(0,a.kq)("",!0)])])),_:1}),(0,a.Wm)(g,{component:n.component,onSelectComponent:r.selectComponent,onConfigureComponent:r.configureComponent,onRemoveComponent:r.removeComponent},null,8,["component","onSelectComponent","onConfigureComponent","onRemoveComponent"]),(0,a.Wm)(w,{component:n.component},null,8,["component"])]),(0,a._)("div",Xe,[(0,a.Wm)(u,{square:"",bordered:""},{default:(0,a.w5)((()=>[(0,a.Wm)(p,{class:"row"},{default:(0,a.w5)((()=>[(0,a._)("span",null,[r.isSelected?((0,a.wg)(),(0,a.j4)(d,{key:0,id:"component-details-uninstall-button",flat:"",align:"left",disable:!r.canUninstall,"no-caps":"",class:"uninstall-button col-auto",label:"Uninstall",icon:e.mdiCloseThick,onClick:t[2]||(t[2]=e=>r.removeComponent(n.component))},null,8,["disable","icon"])):(0,a.kq)("",!0),r.isSelected&&!r.canUninstall?((0,a.wg)(),(0,a.j4)(c,{key:1,"content-class":"bg-accent"},{default:(0,a.w5)((()=>[et])),_:1})):(0,a.kq)("",!0),r.isSelected&&r.canUninstall?((0,a.wg)(),(0,a.j4)(c,{key:2,"content-class":"bg-accent"},{default:(0,a.w5)((()=>[tt])),_:1})):(0,a.kq)("",!0)]),(0,a.Wm)(m),r.hasInstances?((0,a.wg)(),(0,a.j4)(d,{key:0,id:"component-details-add-instance-button",outline:"","no-caps":"",class:"studio-secondary-button add-instance-button col-auto self-end",label:"Add New Instances",icon:e.mdiPlusThick,onClick:t[3]||(t[3]=e=>r.showInstanceDialog())},null,8,["icon"])):(0,a.kq)("",!0),r.hasInstances?((0,a.wg)(),(0,a.j4)(d,{key:1,outline:"","no-caps":"",class:"view-dep-btn col-auto self-end",label:"Instances",id:"component-details-view-instances-button",onClick:t[4]||(t[4]=e=>r.showDependencies(n.component.componentId))})):(0,a.kq)("",!0)])),_:1})])),_:1})])]),(0,a.Wm)(S,{modelValue:e.instNameDlgShowing,"onUpdate:modelValue":t[10]||(t[10]=t=>e.instNameDlgShowing=t),persistent:"","transition-show":"scale","transition-hide":"scale"},{default:(0,a.w5)((()=>[(0,a.Wm)(u,{id:"inst-name-dialog-card",style:{width:"500px"}},{default:(0,a.w5)((()=>[(0,a.Wm)(p,{class:"row items-center"},{default:(0,a.w5)((()=>[(0,a.Wm)(m),(0,a.wy)((0,a.Wm)(d,{"icon-right":e.mdiClose,label:"Close",id:"inst-name-dialog-close-button","no-caps":"",flat:"",dense:""},null,8,["icon-right"]),[[x]])])),_:1}),(0,a.Wm)(p,null,{default:(0,a.w5)((()=>[nt])),_:1}),(0,a.Wm)(p,{class:"content-center"},{default:(0,a.w5)((()=>[ot])),_:1}),(0,a.Wm)(p,null,{default:(0,a.w5)((()=>[st,(0,a.Wm)(b,{outlined:"",dense:"",ref:"instNameInput",modelValue:e.curInstanceName,"onUpdate:modelValue":t[5]||(t[5]=t=>e.curInstanceName=t),autofocus:"",onFocus:t[6]||(t[6]=e=>r.autoSelectInstanceName()),onKeyup:t[7]||(t[7]=(0,o.D2)((t=>{r.addInstanceComponent(n.component.componentId,e.curInstanceName),r.hideInstanceDialog()}),["enter"])),hint:"Names must adhere to both C and POSIX filename rules",rules:[r.validateInstanceName]},null,8,["modelValue","rules"]),r.recommededInstNames?((0,a.wg)(),(0,a.j4)(I,{key:0,"switch-toggle-side":"","expand-separator":"",label:"Recommended Instance Names"},{default:(0,a.w5)((()=>[(0,a.Wm)(v,{dense:""},{default:(0,a.w5)((()=>[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(r.recommededInstNames,(e=>((0,a.wg)(),(0,a.j4)(C,{clickable:"",key:e.name},{default:(0,a.w5)((()=>[(0,a.Wm)(k,{onClick:t=>r.setInstanceName(e.name),onKeyup:t[8]||(t[8]=(0,o.D2)((e=>r.autoSelectInstanceName()),["enter"]))},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(e.name)+" ",1),e.tooltip?((0,a.wg)(),(0,a.j4)(c,{key:0},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(e.tooltip),1)])),_:2},1024)):(0,a.kq)("",!0)])),_:2},1032,["onClick"])])),_:2},1024)))),128))])),_:1})])),_:1})):(0,a.kq)("",!0)])),_:1}),(0,a.Wm)(y,{align:"center"},{default:(0,a.w5)((()=>[(0,a.wy)((0,a.Wm)(d,{id:"inst-name-dialog-cancel-button",class:"cancel-button","no-caps":"",label:"Cancel"},null,512),[[x]]),(0,a.wy)((0,a.Wm)(d,{id:"inst-name-dialog-done-button",class:"positive-button","no-caps":"",label:"Done",disable:!e.curInstanceNameValid,onClick:t[9]||(t[9]=t=>r.addInstanceComponent(n.component.componentId,e.curInstanceName))},null,8,["disable"]),[[x]])])),_:1})])),_:1})])),_:1},8,["modelValue"]),(0,a.Wm)(S,{modelValue:e.removeInstDlgShowing,"onUpdate:modelValue":t[13]||(t[13]=t=>e.removeInstDlgShowing=t),persistent:"","transition-show":"scale","transition-hide":"scale"},{default:(0,a.w5)((()=>[(0,a.Wm)(u,{id:"remove-inst-dialog-card",style:{width:"500px"}},{default:(0,a.w5)((()=>[(0,a.Wm)(p,{class:"row items-center"},{default:(0,a.w5)((()=>[(0,a.Wm)(m),(0,a.wy)((0,a.Wm)(d,{"icon-right":e.mdiClose,label:"Close",id:"remove-inst-dialog-close-button","no-caps":"",flat:"",dense:""},null,8,["icon-right"]),[[x]])])),_:1}),(0,a.Wm)(p,null,{default:(0,a.w5)((()=>[it])),_:1}),(0,a.Wm)(p,{class:"content-center"},{default:(0,a.w5)((()=>[at])),_:1}),(0,a.Wm)(p,null,{default:(0,a.w5)((()=>[(0,a.Wm)(_,{options:r.currentInstances,label:"Instances",type:"checkbox",modelValue:e.instancesToDelete,"onUpdate:modelValue":t[11]||(t[11]=t=>e.instancesToDelete=t)},null,8,["options","modelValue"])])),_:1}),(0,a.Wm)(y,{align:"center"},{default:(0,a.w5)((()=>[(0,a.wy)((0,a.Wm)(d,{id:"remove-inst-dialog-cancel-button",class:"cancel-button","no-caps":"",label:"Cancel"},null,512),[[x]]),(0,a.wy)((0,a.Wm)(d,{id:"remove-inst-dialog-done-button",class:"positive-button","no-caps":"",label:"Done",onClick:t[12]||(t[12]=t=>r.removeInstances(n.component,e.instancesToDelete))},null,512),[[x]])])),_:1})])),_:1})])),_:1},8,["modelValue"])])}var rt=n(9448);const ct=e=>((0,a.dD)("data-v-c8c64712"),e=e(),(0,a.Cn)(),e),dt={style:{display:"flex","justify-content":"space-between","align-content":"center"}},pt=ct((()=>(0,a._)("div",{class:"text-xlg card-content-primary-text"},"Dependencies",-1))),ut={id:"dependenciesHeader"},mt={class:"row items-center justify-between depends-header"},ht={class:"col-11"},ft={class:"col-1"},gt=["onClick"],wt={class:"row items-center justify-between depends-header"},bt={class:"col-11"},kt={class:"row items-center justify-between depends-header"},Ct={class:"col-10"},vt=(0,a.Uk)(" Remove component from project "),It=ct((()=>(0,a._)("div",{class:"text-xlg card-content-primary-text"}," Dependents ",-1))),yt={id:"DependentsHeader"},St={class:"row items-center justify-between depends-header"},_t={class:"col-11"},xt={class:"col-1"},jt=["onClick"],Dt={class:"row items-center justify-between depends-header"},Et={class:"col-11"},qt={class:"row items-center justify-between depends-header"},Ut={class:"col-10"},Wt=(0,a.Uk)(" Remove component from project ");function Nt(e,t,n,o,s,i){const r=(0,a.up)("q-btn"),c=(0,a.up)("q-icon"),d=(0,a.up)("q-tooltip"),p=(0,a.up)("q-tree"),u=(0,a.up)("q-card");return(0,a.wg)(),(0,a.j4)(u,{square:"",bordered:"",class:"description-card card-content"},{default:(0,a.w5)((()=>[(0,a._)("div",dt,[pt,(0,a.Wm)(r,{flat:"",icon:e.image,onClick:i.openCloseTrees},null,8,["icon","onClick"])]),(0,a._)("div",ut,(0,l.zw)(n.component.name)+" requires "+(0,l.zw)(i.uniqueComponentCount(e.dependencies))+" components ",1),(0,a.Wm)(p,{"no-connectors":"",class:"dependency-component-tree",ref:"dependstree",nodes:e.dependencies,"node-key":"id","no-nodes-label":"No Dependencies",selected:e.selected,"onUpdate:selected":[t[1]||(t[1]=t=>e.selected=t),i.selectionChanged],expanded:e.dependenciesExpanded,"onUpdate:expanded":[t[2]||(t[2]=t=>e.dependenciesExpanded=t),i.updateCollapseExpandButtonImage],"selected-color":"primary"},{"header-component":(0,a.w5)((e=>[(0,a._)("div",mt,[(0,a._)("div",ht,(0,l.zw)(e.node.label),1),(0,a._)("div",ft,[(0,a._)("a",{class:"card-content-link",href:"#",onClick:t=>i.selectComponent(e.node.id)},"View Component",8,gt)])])])),"default-header":(0,a.w5)((e=>[(0,a._)("div",wt,[(0,a._)("div",bt,(0,l.zw)(e.node.label),1)])])),"header-selected":(0,a.w5)((o=>[(0,a._)("div",kt,[(0,a._)("div",Ct,(0,l.zw)(o.node.label),1),i.isEditable?((0,a.wg)(),(0,a.j4)(c,{key:0,"data-studio-comp":o.node.componentId,"data-studio-inst":o.node.instanceName,class:"col-1 inline-tree-icon",onClick:t[0]||(t[0]=e=>i.configureComponent(n.component)),name:e.mdiSettings},null,8,["data-studio-comp","data-studio-inst","name"])):(0,a.kq)("",!0),i.canUninstall?((0,a.wg)(),(0,a.j4)(c,{key:1,class:"col-1 negative-button inline-tree-icon",onClick:e=>i.removeComponent(o.node),name:e.mdiClose},{default:(0,a.w5)((()=>[(0,a.Wm)(d,null,{default:(0,a.w5)((()=>[vt])),_:1})])),_:2},1032,["onClick","name"])):(0,a.kq)("",!0)])])),_:1},8,["nodes","selected","expanded","onUpdate:selected","onUpdate:expanded"]),It,(0,a._)("div",yt,(0,l.zw)(i.uniqueComponentCount(e.dependents))+" components require "+(0,l.zw)(n.component.name),1),(0,a.Wm)(p,{"no-connectors":"",class:"dependency-component-tree",ref:"dependentstree",nodes:e.dependents,"node-key":"id","no-nodes-label":"No Dependent Components",selected:e.selected,"onUpdate:selected":[t[4]||(t[4]=t=>e.selected=t),i.selectionChanged],expanded:e.dependentsExpanded,"onUpdate:expanded":[t[5]||(t[5]=t=>e.dependentsExpanded=t),i.updateCollapseExpandButtonImage],"selected-color":"primary"},{"header-component":(0,a.w5)((e=>[(0,a._)("div",St,[(0,a._)("div",_t,(0,l.zw)(e.node.label),1),(0,a._)("div",xt,[(0,a._)("a",{class:"card-content-link",href:"#",onClick:t=>i.selectComponent(e.node.id)},"View Component",8,jt)])])])),"default-header":(0,a.w5)((e=>[(0,a._)("div",Dt,[(0,a._)("div",Et,(0,l.zw)(e.node.label),1)])])),"header-selected":(0,a.w5)((o=>[(0,a._)("div",qt,[(0,a._)("div",Ut,(0,l.zw)(o.node.label),1),i.isEditable?((0,a.wg)(),(0,a.j4)(c,{key:0,"data-studio-comp":o.node.componentId,"data-studio-inst":o.node.instanceName,class:"col-1 inline-tree-icon",onClick:t[3]||(t[3]=e=>i.configureComponent(n.component)),name:e.mdiSettings},null,8,["data-studio-comp","data-studio-inst","name"])):(0,a.kq)("",!0),i.canUninstall?((0,a.wg)(),(0,a.j4)(c,{key:1,class:"col-1 negative-button inline-tree-icon",onClick:e=>i.removeComponent(o.node),name:e.mdiClose},{default:(0,a.w5)((()=>[(0,a.Wm)(d,null,{default:(0,a.w5)((()=>[Wt])),_:1})])),_:2},1032,["onClick","name"])):(0,a.kq)("",!0)])])),_:1},8,["nodes","selected","expanded","onUpdate:selected","onUpdate:expanded"])])),_:1})}var Tt=n(2670);const Pt={props:{component:Object,componentDetails:Object},data:()=>({dependencies:[],dependents:[],selected:void 0,dependenciesExpanded:[],dependentsExpanded:[],image:null}),created:function(){this.mdiSettings=Tt.WkI,this.mdiClose=Tt.r5M,this.image=Tt.CW,this.selected=this.component.id,this.$store.dispatch("fetchComponentDependencies",this.component.componentId).then((e=>{"requiresTree"in e&&(this.dependencies=JSON.parse(JSON.stringify(e.requiresTree)),this.addHeader(this.dependencies,(t=>"id"in t&&t["id"]===e.component.id&&(t["header"]="selected",t.selectable=!0,!0)))),"requiredByTree"in e&&(this.dependents=JSON.parse(JSON.stringify(e.requiredByTree)),this.addHeader(this.dependents,(t=>"id"in t&&t["id"]===e.component.id&&(t["header"]="selected",t.selectable=!0,!0))))}))},computed:{componentEditable(){return!(!this.componentDetails||!("isEditable"in this.componentDetails))&&this.componentDetails.isEditable},canUninstall(){let e=this.isSelected,t=this.componentUninstallable;return!!this.isInitialized&&(e&&t)},componentUninstallable(){return!(!this.componentDetails||!("canUninstall"in this.componentDetails))&&this.componentDetails.canUninstall},isEditable(){let e=this.componentEditable;return!!this.isInitialized&&e},componentId(){return this.componentDetails&&"componentId"in this.componentDetails?this.componentDetails.componentId:""},isInitialized(){return""!==this.componentId},isSelected(){let e=this.componentSelected;return!!this.isInitialized&&e},componentSelected(){return!(!this.componentDetails||!("isSelected"in this.componentDetails))&&this.componentDetails.isSelected}},methods:{configureComponent(e){this.$emit("configure-component",e)},updateCollapseExpandButtonImage(){this.dependenciesExpanded.length<1&&this.dependentsExpanded.length<1?this.image=Tt.CW:this.image=Tt.Waq},openCloseTrees(){this.dependenciesExpanded.length<1&&this.dependentsExpanded.length<1?(this.$refs.dependstree.expandAll(),this.$refs.dependentstree.expandAll()):(this.$refs.dependstree.collapseAll(),this.$refs.dependentstree.collapseAll())},removeComponent(e){this.$emit("remove-component",e)},addHeader(e,t){e&&e.forEach((e=>{t&&t(e)||("type"in e&&!("header"in e)&&(e["header"]=e["type"]),"children"in e&&this.addHeader(e.children,t),e.selectable=!1)}))},selectionChanged(){this.selected=this.component.id},selectComponent(e){this.$emit("select-component",e)},addComponentsToSet(e,t){e&&(Array.isArray(e)?e.forEach((e=>this.addComponentsToSet(e,t))):e.hasOwnProperty("children")?this.addComponentsToSet(e.children,t):e.hasOwnProperty("componentId")&&t.add(e.componentId))},uniqueComponentCount(e){const t=new Set;return this.addComponentsToSet(e,t),t.size}}};var Vt=n(1639),Lt=n(4458),Qt=n(8879),Ot=n(4749),Zt=n(2857),$t=n(6858),Ft=n(7691),Mt=n(9984),Bt=n.n(Mt);const Rt=(0,Vt.Z)(Pt,[["render",Nt],["__scopeId","data-v-c8c64712"]]),At=Rt;Bt()(Pt,"components",{QCard:Lt.Z,QBtn:Qt.Z,QTree:Ot.Z,QIcon:Zt.Z,QTooltip:$t.Z,QChip:Ft.Z});const zt={class:"documentation-card-pre-state"},Kt=["href"],Ht=(0,a.Uk)(" Open in Browser"),Gt={key:0},Yt=(0,a._)("div",{class:"documentationText"},"DOCUMENTATION",-1),Jt={class:"documentationLabel"},Xt={class:"documentationDescription"},en=["innerHTML"],tn=["innerHTML"],nn=(0,a.Uk)(" Error loading documentation from "),on=["href"];function sn(e,t,n,o,s,i){const r=(0,a.up)("q-icon"),c=(0,a.up)("q-btn"),d=(0,a.up)("q-card"),p=(0,a.up)("q-card-section");return(0,a.wg)(),(0,a.iD)("div",null,[i.componentDocumentationHtml&&!i.componentDocumentationError?((0,a.wg)(),(0,a.j4)(d,{key:0,square:"",bordered:"",class:"documentation-card card-content",onClick:i.catchDocsClick},{default:(0,a.w5)((()=>[(0,a._)("div",zt,[(0,a._)("a",{class:"card-content-link",href:e.documentationUrl,target:"_blank"},[(0,a.Wm)(r,{name:e.mdiOpenInNew,size:"xs"},null,8,["name"]),Ht],8,Kt),(0,a.Wm)(c,{flat:"",icon:e.ArrowIcon,onClick:i.changeBetweenTexts},null,8,["icon","onClick"])]),i.getSmallText?((0,a.wg)(),(0,a.iD)("div",Gt,[Yt,(0,a._)("div",Jt,(0,l.zw)(this.component.label),1),(0,a._)("div",Xt,(0,l.zw)(this.component.description),1),(0,a._)("div",{class:"documentation",innerHTML:this.getDocumentationSmallText()},null,8,en)])):(0,a.kq)("",!0),i.getSmallText?(0,a.kq)("",!0):((0,a.wg)(),(0,a.iD)("div",{key:1,innerHTML:i.componentDocumentationHtml},null,8,tn))])),_:1},8,["onClick"])):(0,a.kq)("",!0),i.componentDocumentationError&&!i.componentDocumentationHtml?((0,a.wg)(),(0,a.j4)(d,{key:1,square:"",bordered:"",class:"documentation-card card-content",onClick:i.catchDocsClick},{default:(0,a.w5)((()=>[(0,a.Wm)(p,{class:"network-error"},{default:(0,a.w5)((()=>[nn,(0,a._)("a",{href:i.componentDocumentationUrl,target:"_blank"},(0,l.zw)(i.componentDocumentationUrl),9,on)])),_:1})])),_:1},8,["onClick"])):(0,a.kq)("",!0)])}const an={props:{component:Object,componentDetails:Object},data:()=>({documentationHtml:null,documentationUrl:null,documentationError:null,selected:void 0,ArrowIcon:null,DocumentationSmallText:""}),created:function(){this.mdiOpenInNew=rt.fOx,this.mdiChevronUp=rt.Waq,this.mdiChevronDown=rt.CW,this.ArrowIcon=rt.CW,this.getComponentDocumentation()},computed:{getSmallText(){return this.ArrowIcon!==rt.Waq},componentDocumentation(){if(this.isInitialized&&"documentation"in this.component&&0!==this.component.documentation.length)return this.component.documentation},componentDocumentationHtml(){if(this.isInitialized&&this.documentationHtml)return this.documentationHtml},componentDocumentationUrl(){if(this.isInitialized&&this.documentationUrl)return this.documentationUrl},componentDocumentationError(){if(this.isInitialized&&this.documentationError)return this.documentationError},isInitialized(){return""!==this.componentId}},methods:{componentId(){return this.componentDetails&&"componentId"in this.componentDetails?this.componentDetails.componentId:""},trimBlock(e,t,n){const o=n.indexOf(e),s=n.indexOf(t);return o<0||s<0?n:n.substring(0,o)+n.substring(s+t.length)},getDocumentationSmallText(){let e=this.documentationHtml;const t=e.indexOf("<p>"),n=e.indexOf("</p>");return t<0||n<0?"":this.trimBlock("<a","/a>",e.substring(t+3,n))},changeBetweenTexts(){this.ArrowIcon==rt.CW?this.ArrowIcon=rt.Waq:this.ArrowIcon=rt.CW},catchDocsClick(e){"a"===e.target.tagName.toLowerCase()&&(!("href"in e.target)||e.target.href.startsWith(window.location.origin+window.location.pathname)||e.target.href.startsWith("#")||(e.preventDefault(),this.openExternalUrl(e.target.href)))},openExternalUrl(e){this.$store.dispatch("openExternalUrl",e)},openComponentDocumentation(){if(!this.component)return;let e={componentId:this.component.componentId};"instanceName"in this.component&&(e["instanceName"]=this.component.instanceName),this.$store.dispatch("openComponentDocumentation",e)},getComponentDocumentation(){if(this.documentationHtml=null,this.documentationUrl=null,this.documentationError=null,!this.component)return;let e={componentId:this.component.componentId};"instanceName"in this.component&&(e["instanceName"]=this.component.instanceName),this.$store.dispatch("getComponentDocumentation",e).then((e=>{e&&"object"===typeof e&&(this.documentationUrl=null,"url"in e&&(this.documentationUrl=e.url),"status"in e&&"success"===e.status?"html"in e&&(this.documentationHtml=e["html"]):"reason"in e&&"NetworkError"===e.reason&&(this.documentationError="NetworkError"))}))}}};var ln=n(3190);const rn=(0,Vt.Z)(an,[["render",sn]]),cn=rn;Bt()(an,"components",{QCard:Lt.Z,QIcon:Zt.Z,QBtn:Qt.Z,QCardSection:ln.Z});const dn=/^[A-Za-z0-9_]+$/,pn={components:{DependencyCardComponent:At,DocumentationCardComponent:cn},props:{component:Object,componentDetails:Object},data:()=>({curInstanceName:"",curInstanceNameValid:!1,instancesToDelete:[],instNameDlgShowing:!1,removeInstDlgShowing:!1,selected:void 0}),created:function(){this.mdiCog=rt.Shd,this.mdiPlusThick=rt.Q0_,this.mdiCloseThick=rt.mGH,this.mdiOpenInNew=rt.fOx,this.mdiClose=rt.r5M},computed:{tags(){if(this.isInitialized&&"tags"in this.component&&0!==this.component.tags.length)return this.component.tags.split(",")},isInitialized(){return""!==this.componentId},isEditable(){let e=this.componentEditable;return!!this.isInitialized&&e},isConfigurable(){let e=this.componentConfigurable;return!!this.isInitialized&&e},isSelected(){let e=this.componentSelected;return!!this.isInitialized&&e},isUserSelected(){let e=this.componentUserSelected;if(!this.isInitialized)return!1;if(e&&this.isInstantiable){let t=this.instances;void 0!==t&&0!==t.length||(e=!1)}return e},isInstantiable(){let e=this.componentInstantiable;return!!this.isInitialized&&e},instances(){let e=this.componentInstances;if(this.isInitialized)return e},hasInstances(){return void 0!==this.instances&&this.instances.length>0},prefix(){let e=this.componentPrefix;if(this.isInitialized)return e},canUninstall(){let e=this.isSelected,t=this.componentUninstallable;return!!this.isInitialized&&(e&&t)},canInstall(){let e=this.isSelected;return!!this.isInitialized&&!e},currentInstances(){let e=[];if(void 0!==this.componentInstances)for(let t=0;t<this.componentInstances.length;t++){let n={};n.componentId=this.componentId,n.label=this.componentInstances[t],n.value=this.componentInstances[t],n.id=this.componentInstances[t],e.push(n)}return e},componentId(){return this.componentDetails&&"componentId"in this.componentDetails?this.componentDetails.componentId:""},componentEditable(){return!(!this.componentDetails||!("isEditable"in this.componentDetails))&&this.componentDetails.isEditable},componentConfigurable(){return!(!this.componentDetails||!("isConfigurable"in this.componentDetails))&&this.componentDetails.isConfigurable},componentSelected(){return!(!this.componentDetails||!("isSelected"in this.componentDetails))&&this.componentDetails.isSelected},componentUserSelected(){return!(!this.componentDetails||!("isUserSelected"in this.componentDetails))&&this.componentDetails.isUserSelected},componentUninstallable(){return!(!this.componentDetails||!("canUninstall"in this.componentDetails))&&this.componentDetails.canUninstall},componentInstantiable(){return!(!this.componentDetails||!("isInstantiable"in this.componentDetails))&&this.componentDetails.isInstantiable},componentInstances(){return this.componentDetails&&"instances"in this.componentDetails?this.componentDetails.instances:void 0},componentPrefix(){return this.componentDetails&&"prefix"in this.componentDetails?this.componentDetails.prefix:void 0},generatedInstName(){if(this.componentDetails&&"generatedInstName"in this.componentDetails)return this.componentDetails.generatedInstName},componentRecommendedName(){if(this.componentDetails&&"recommendedInstNames"in this.componentDetails&&this.componentDetails.recommendedInstNames.length>0)return this.componentDetails.recommendedInstNames[0]["name"]},recommededInstNames(){if(!this.componentDetails||!("recommendedInstNames"in this.componentDetails))return;let e=this.componentDetails.recommendedInstNames.map((e=>(e["tooltip"]="Recommended by "+e.recommender,e)));return e&&this.generatedInstName&&e.push({name:this.generatedInstName,tooltip:"Auto Generated"}),e}},methods:{selectComponent(e){this.$emit("select-component",e)},getConfigurableClass(){let e="configure-component-button studio-primary-button";return this.isEditable||(e+=" disabled"),e},showDependencies(e){this.$emit("show-depends",e)},showInstanceDialog(){this.curInstanceName=this.componentRecommendedName,this.curInstanceName||(this.curInstanceName=this.generateInstanceName()),this.curInstanceNameValid=!0,this.instNameDlgShowing=!0},hideInstanceDialog(){this.instNameDlgShowing=!1},generateInstanceName(){let e=this.componentPrefix;if(e||(e="inst",this.componentDetails.prefix="inst"),void 0!==this.componentInstances)for(let t=0;t<this.componentInstances.length;t++){const n=e+t;if(!this.componentInstances.includes(n)){e=n;break}}return e},configureComponent(e){if(!this.isEditable)return;let t;"instanceName"in e?t=[e.instanceName]:"instantiable"in e&&"children"in e&&(t=[],e.children.forEach((e=>{"instanceName"in e&&t.push(e.instanceName)}))),t?t.forEach((t=>{this.doConfigureComponent(e.componentId,t)})):this.doConfigureComponent(e.componentId)},doConfigureComponent(e,t){let n={componentId:e};t&&(n["instanceName"]=t),this.$store.dispatch("configureComponent",n)},addComponent(e){this.$store.dispatch("addComponent",{componentId:e})},addInstanceComponent(e,t){this.$store.dispatch("addComponent",{componentId:e,instanceNames:t})},removeComponent(e){if("isInstance"in e)this.doRemoveComponent(e,[e.instanceName]);else{const t=this.instances;void 0!==t&&t.length>0?this.removeInstDlgShowing=!0:this.doRemoveComponent(e)}},removeInstances(e,t){void 0!==t&&this.doRemoveComponent(e,t)},doRemoveComponent(e,t){const n={componentId:e.componentId,instanceNames:t?t.join():void 0};this.$store.dispatch("removeComponent",n)},validateInstanceName(e){const t=dn.test(e);return this.curInstanceNameValid=t,!!t||"Valid characters are A-Z, a-z, 0-9, and _"},autoSelectInstanceName(){this.$refs.instNameInput.select()},setInstanceName(e){this.curInstanceName=e}}};var un=n(136),mn=n(926),hn=n(7743),fn=n(6611),gn=n(651),wn=n(3246),bn=n(490),kn=n(1233),Cn=n(1821),vn=n(2286),In=n(2146);const yn=(0,Vt.Z)(pn,[["render",lt],["__scopeId","data-v-d6a4b644"]]),Sn=yn;Bt()(pn,"components",{QCard:Lt.Z,QCardSection:ln.Z,QTooltip:$t.Z,QBtn:Qt.Z,QSpace:un.Z,QSeparator:mn.Z,QChip:Ft.Z,QDialog:hn.Z,QInput:fn.Z,QExpansionItem:gn.Z,QList:wn.Z,QItem:bn.Z,QItemSection:kn.Z,QCardActions:Cn.Z,QOptionGroup:vn.Z,QIcon:Zt.Z,QTree:Ot.Z}),Bt()(pn,"directives",{ClosePopup:In.Z});const _n=e=>((0,a.dD)("data-v-0acf5e24"),e=e(),(0,a.Cn)(),e),xn={class:"component-template"},jn={class:"component-column"},Dn={class:"component-header-row"},En={class:"row items-center outer-row"},qn=_n((()=>(0,a._)("div",{class:"col-1 card-title-block-bar"},null,-1))),Un={class:"col"},Wn={class:"row items-center card-title-block-content inner-row"},Nn={key:0,class:"col"},Tn={class:"component-content-row"},Pn=_n((()=>(0,a._)("div",{class:"text-xlg card-content-primary-text"},"Instances",-1))),Vn={class:"row items-center justify-between depends-header"},Ln={class:"col-10"},Qn=(0,a.Uk)(" Remove component from project "),On={class:"row items-center justify-between depends-header"},Zn={class:"col-10"},$n=(0,a.Uk)(" Remove component from project "),Fn={class:"component-control-row"},Mn=(0,a.Uk)("Back");function Bn(e,t,n,o,s,i){const r=(0,a.up)("q-card-section"),c=(0,a.up)("q-card"),d=(0,a.up)("q-icon"),p=(0,a.up)("q-tooltip"),u=(0,a.up)("q-tree"),m=(0,a.up)("q-btn");return(0,a.wg)(),(0,a.iD)("div",xn,[(0,a._)("div",jn,[(0,a._)("div",Dn,[(0,a.Wm)(c,{square:"",bordered:"",class:"component-template-header"},{default:(0,a.w5)((()=>[(0,a.Wm)(r,{class:"component-template-header card-title-block"},{default:(0,a.w5)((()=>[(0,a._)("div",En,[qn,(0,a._)("div",Un,[(0,a._)("div",Wn,[void 0!==n.component?((0,a.wg)(),(0,a.iD)("span",Nn,(0,l.zw)(n.component.label),1)):(0,a.kq)("",!0)])])])])),_:1})])),_:1})]),(0,a._)("div",Tn,[(0,a.Wm)(c,{square:"",bordered:"",class:"description-card card-content"},{default:(0,a.w5)((()=>[Pn,(0,a.Wm)(u,{"no-connectors":"",class:"component-tree",ref:"instancestree",nodes:e.instances,"node-key":"id",selected:e.selected,"onUpdate:selected":[t[1]||(t[1]=t=>e.selected=t),i.selectionChanged],"selected-color":"primary"},{"header-instance":(0,a.w5)((t=>[(0,a._)("div",Vn,[(0,a._)("div",Ln,(0,l.zw)(t.node.label),1),(0,a.Wm)(d,{"data-studio-comp":t.node.componentId,"data-studio-inst":t.node.instanceName,class:"col-1 inline-tree-icon",onClick:e=>i.configureComponent(t.node),name:e.mdiSettings},null,8,["data-studio-comp","data-studio-inst","onClick","name"]),i.canUninstall?((0,a.wg)(),(0,a.j4)(d,{key:0,class:"col-1 negative-button inline-tree-icon",onClick:e=>i.removeComponent(t.node),name:e.mdiClose},{default:(0,a.w5)((()=>[(0,a.Wm)(p,null,{default:(0,a.w5)((()=>[Qn])),_:1})])),_:2},1032,["onClick","name"])):(0,a.kq)("",!0)])])),"header-component":(0,a.w5)((o=>[(0,a._)("div",On,[(0,a._)("div",Zn,(0,l.zw)(o.node.label),1),i.isEditable?((0,a.wg)(),(0,a.j4)(d,{key:0,"data-studio-comp":o.node.componentId,"data-studio-inst":o.node.instanceName,class:"col-1 inline-tree-icon",onClick:t[0]||(t[0]=e=>i.configureComponent(n.component)),name:e.mdiSettings},null,8,["data-studio-comp","data-studio-inst","name"])):(0,a.kq)("",!0),i.canUninstall?((0,a.wg)(),(0,a.j4)(d,{key:1,class:"col-1 negative-button inline-tree-icon",onClick:e=>i.removeComponent(o.node),name:e.mdiClose},{default:(0,a.w5)((()=>[(0,a.Wm)(p,null,{default:(0,a.w5)((()=>[$n])),_:1})])),_:2},1032,["onClick","name"])):(0,a.kq)("",!0)])])),_:1},8,["nodes","selected","onUpdate:selected"])])),_:1})]),(0,a._)("div",Fn,[(0,a.Wm)(c,{square:"",bordered:""},{default:(0,a.w5)((()=>[(0,a.Wm)(r,null,{default:(0,a.w5)((()=>[(0,a.Wm)(m,{outline:"","no-caps":"",class:"backbtn",id:"comp-instances-back-button",onClick:i.showDetails},{default:(0,a.w5)((()=>[Mn])),_:1},8,["onClick"])])),_:1})])),_:1})])])])}const Rn={props:{component:Object,componentDetails:Object},data:()=>({instances:[],selected:void 0}),created:function(){this.mdiSettings=Tt.WkI,this.mdiClose=Tt.r5M,this.selected=this.component.id,"instantiable"in this.component&&!0===this.component["instantiable"]&&this.$store.dispatch("fetchComponentInstances",this.component.componentId).then((e=>{this.instances="instancesTree"in e?e.instancesTree:void 0,this.instances&&(this.addHeader(this.instances),this.$nextTick((()=>{this.$refs.instancestree&&this.$refs.instancestree.expandAll()})))}))},computed:{hasInstances(){return this.instances&&this.instances.length>0},isEditable(){return!(!this.componentDetails||!("isEditable"in this.componentDetails))&&this.componentDetails.isEditable},componentUserSelected(){return!(!this.componentDetails||!("isUserSelected"in this.componentDetails))&&this.componentDetails.isUserSelected},componentInstantiable(){return!(!this.componentDetails||!("isInstantiable"in this.componentDetails))&&this.componentDetails.isInstantiable},componentUninstallable(){return!(!this.componentDetails||!("canUninstall"in this.componentDetails))&&this.componentDetails.canUninstall},componentInstances(){return this.componentDetails&&"instances"in this.componentDetails?this.componentDetails.instances:void 0},canUninstall(){const e=this.isSelected;let t=this.componentUninstallable;return!!this.isInitialized&&(e&&t)},isInstantiable(){let e=this.componentInstantiable;return!!this.componentDetails&&e},isUserSelected(){let e=this.componentUserSelected;if(!this.componentDetails)return!1;if(e&&this.isInstantiable){const t=this.instances;void 0!==t&&0!==t.length||(e=!1)}return e}},methods:{selectionChanged(){this.selected=this.component.id},isComponentInstantiable(e){return!(!e||!("instantiable"in e))&&e.instantiable},componentHasChildren(e){return!(!e||!("children"in e))&&e.children.length>0},getComponentChildren(e){if(this.componentHasChildren(e))return e.children},configureComponent(e){if("isEditable"in e&&!e.isEditable)return;let t;"instanceName"in e?t=[e.instanceName]:"instantiable"in e&&"children"in e&&(t=[],e.children.forEach((e=>{"instanceName"in e&&t.push(e.instanceName)}))),t?t.forEach((t=>{this.doConfigureComponent(e.componentId,t)})):this.doConfigureComponent(e.componentId)},doConfigureComponent(e,t){let n={componentId:e};t&&(n["instanceName"]=t),this.$store.dispatch("configureComponent",n)},showDetails(){this.$emit("show-details")},selectComponent(e){this.$emit("select-component",e)},addHeader(e,t){e&&e.forEach((e=>{t&&t(e)||("type"in e&&!("header"in e)&&(e["header"]=e["type"]),"children"in e&&this.addHeader(e.children,t),e.selectable=!1)}))},removeComponent(e){let t;"isInstance"in e?t=[e.instanceName]:this.isComponentInstantiable(e)&&this.componentHasChildren(e)&&(t=[],this.getComponentChildren(e).forEach((e=>{"instanceName"in e&&t.push(e.instanceName)}))),this.doRemoveComponent(e,t)},removeInstances(e,t){void 0!==t&&this.doRemoveComponent(e,t)},doRemoveComponent(e,t){let n={componentId:e.componentId,instanceNames:t?t.join():void 0};this.$store.dispatch("removeComponent",n)}}},An=(0,Vt.Z)(Rn,[["render",Bn],["__scopeId","data-v-0acf5e24"]]),zn=An;Bt()(Rn,"components",{QCard:Lt.Z,QCardSection:ln.Z,QTree:Ot.Z,QIcon:Zt.Z,QTooltip:$t.Z,QBtn:Qt.Z});const Kn=e=>((0,a.dD)("data-v-6b486c58"),e=e(),(0,a.Cn)(),e),Hn=Kn((()=>(0,a._)("div",null,"Validation Errors",-1))),Gn={class:"main-message"},Yn=["innerHTML"],Jn=Kn((()=>(0,a._)("div",null,"See the Problems view for more information",-1)));function Xn(e,t,n,o,s,i){const r=(0,a.up)("q-space"),c=(0,a.up)("q-btn"),d=(0,a.up)("q-card-section"),p=(0,a.up)("q-icon"),u=(0,a.up)("q-item-section"),m=(0,a.up)("q-item"),h=(0,a.up)("q-list"),f=(0,a.up)("q-card"),g=(0,a.up)("q-dialog"),w=(0,a.Q2)("close-popup");return(0,a.wg)(),(0,a.j4)(g,{class:"validation-error-dialog",position:"top",ref:"validationErrorsDialog","transition-show":"scale","transition-hide":"scale",onHide:i.onDialogHide},{default:(0,a.w5)((()=>[(0,a.Wm)(f,{class:"q-dialog-plugin column inline"},{default:(0,a.w5)((()=>[(0,a.Wm)(d,{class:"col-1 self-end"},{default:(0,a.w5)((()=>[(0,a.Wm)(r),(0,a.wy)((0,a.Wm)(c,{"icon-right":e.mdiClose,label:"Close",id:"validation-error-dialog-close-button","no-caps":"",flat:"",dense:""},null,8,["icon-right"]),[[w]])])),_:1}),(0,a.Wm)(d,{class:"col-1 self-center validation-error-title text-h6"},{default:(0,a.w5)((()=>[(0,a.Wm)(m,null,{default:(0,a.w5)((()=>[(0,a.Wm)(u,{class:"col-1 validation-error-title-icon"},{default:(0,a.w5)((()=>[(0,a.Wm)(p,{name:e.mdiAlertCircleOutline,class:"content-center justify-center text-h6"},null,8,["name"])])),_:1}),(0,a.Wm)(u,{class:"text-h6"},{default:(0,a.w5)((()=>[Hn])),_:1})])),_:1})])),_:1}),(0,a.Wm)(d,{class:"col-2 self-center text-bold validation-error-sub-title"},{default:(0,a.w5)((()=>[(0,a._)("div",Gn,(0,l.zw)(this.mainMessage),1)])),_:1}),(0,a.Wm)(h,{dense:"",bordered:"",separator:"",padding:"",class:"col validation-error-message-box rounded-borders"},{default:(0,a.w5)((()=>[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(n.messages,(e=>((0,a.wg)(),(0,a.j4)(m,{key:e.basicMessage},{default:(0,a.w5)((()=>[(0,a.Wm)(u,{class:"validation-error-message-section"},{default:(0,a.w5)((()=>[(0,a._)("div",{onClick:t[0]||(t[0]=(...e)=>i.errorMessageClick&&i.errorMessageClick(...e)),innerHTML:i.formatMessage(e)},null,8,Yn)])),_:2},1024)])),_:2},1024)))),128))])),_:1}),(0,a.Wm)(d,{class:"col-1 validation-error-sub-title text-bold row content-center justify-center"},{default:(0,a.w5)((()=>[Jn])),_:1})])),_:1})])),_:1},8,["onHide"])}const eo={props:{theComponent:Object,messages:Array,mainMessage:String},created:function(){this.mdiClose=Tt.r5M,this.mdiAlertCircleOutline=Tt._gM},computed:{errorMessages(){let e=[],t=0;return this.messages.forEach((n=>{let o={id:t++,message:n};e.push(o)})),e}},methods:{show(){this.$refs.validationErrorsDialog.show()},hide(){this.$refs.validationErrorsDialog.hide()},onDialogHide(){this.$emit("hide")},onOKClick(){this.$emit("ok"),this.hide()},onCancelClick(){this.hide()},searchForComponent(e){this.$emit("ok",e),this.hide()},errorMessageClick(e){"a"===e.target.tagName.toLowerCase()&&e.target.hasAttribute("componentId")&&this.searchForComponent(e.target.getAttribute("componentId"))},formatMessage(e){let t=e.basicMessage;if("issueDetails"in e&&"type"in e.issueDetails&&"UnfulfilledIssue"==e.issueDetails.type){e.issueDetails.messageFormat;let n=e.issueDetails.providers,o=n.map((e=>"<a href='#' componentId='"+e.id+"'>"+e.label+"</a>")).join(", ");t+=e.issueDetails.messageFormat.replace("{0}",o)}return t}}},to=(0,Vt.Z)(eo,[["render",Xn],["__scopeId","data-v-6b486c58"]]),no=to;Bt()(eo,"components",{QDialog:hn.Z,QCard:Lt.Z,QCardSection:ln.Z,QSpace:un.Z,QBtn:Qt.Z,QItem:bn.Z,QItemSection:kn.Z,QIcon:Zt.Z,QList:wn.Z}),Bt()(eo,"directives",{ClosePopup:In.Z});const oo=e=>((0,a.dD)("data-v-fa994660"),e=e(),(0,a.Cn)(),e),so=oo((()=>(0,a._)("div",null,"The component was not added",-1))),io={class:"with-padding"},ao=(0,a.Uk)(" The "),lo=(0,a.Uk)(" you are attempting to install is conflicting with "),ro=(0,a.Uk)(". As a result, your component was not added. "),co={class:"warning_msg"},po=oo((()=>(0,a._)("strong",null," Removing the conflicting component(s) may cause irreversible changes!",-1))),uo=(0,a.Uk)(" Exchanging components in this situation could mean a major modification to the project structure for anything that depended on "),mo=oo((()=>(0,a._)("br",null,null,-1))),ho=(0,a.Uk)(" any of the components being removed. Attempting to exchange again may not fully undo this operation if you change your mind. ");function fo(e,t,n,o,s,i){const r=(0,a.up)("q-space"),c=(0,a.up)("q-btn"),d=(0,a.up)("q-card-section"),p=(0,a.up)("q-icon"),u=(0,a.up)("RadioButtonGroup"),m=(0,a.up)("q-tooltip"),h=(0,a.up)("q-card-actions"),f=(0,a.up)("q-card"),g=(0,a.up)("q-dialog"),w=(0,a.Q2)("close-popup");return(0,a.wg)(),(0,a.j4)(g,{class:"exclusivity-error-dialog scroll",position:"top",ref:"exclusivityErrorsDialog","transition-show":"scale","transition-hide":"scale",onHide:i.onDialogHide},{default:(0,a.w5)((()=>[(0,a.Wm)(f,{class:"q-dialog-plugin inline"},{default:(0,a.w5)((()=>[(0,a.Wm)(d,{align:"right"},{default:(0,a.w5)((()=>[(0,a.Wm)(r),(0,a.wy)((0,a.Wm)(c,{"icon-right":e.mdiClose,label:"Close",id:"exclusivity-error-dialog-close-button","no-caps":"",flat:"",dense:""},null,8,["icon-right"]),[[w]])])),_:1}),(0,a.Wm)(d,{class:"text-h6 title"},{default:(0,a.w5)((()=>[(0,a.Wm)(p,{name:e.mdiAlertCircleOutline,class:"text-h5"},null,8,["name"]),so])),_:1}),(0,a.Wm)(d,null,{default:(0,a.w5)((()=>[(0,a._)("div",io,[ao,(0,a._)("strong",null,(0,l.zw)(e.selectedComponentLabel),1),lo,(0,a._)("strong",null,(0,l.zw)(e.installedComponentLabel),1),ro])])),_:1}),(0,a.Wm)(d,null,{default:(0,a.w5)((()=>[(0,a.Wm)(u,{label:"Select one of the options to fix the issue",dataList:[e.firstOption,e.secondOption],onSelectionChanged:i.handleSelectionChange},null,8,["dataList","onSelectionChanged"])])),_:1}),(0,a._)("div",co,[(0,a.Wm)(p,{class:"warning_sign",name:e.mdiAlertCircleOutline,size:"2rem"},null,8,["name"]),po,(0,a.Wm)(m,{class:"warning_tooltip",anchor:"bottom middle",self:"top middle"},{default:(0,a.w5)((()=>[uo,mo,ho])),_:1})]),(0,a.Wm)(h,{align:"right"},{default:(0,a.w5)((()=>[(0,a.wy)((0,a.Wm)(c,{id:"cancelButton",flat:"",label:"Cancel",color:"primary"},null,512),[[w]]),(0,a.wy)((0,a.Wm)(c,{id:"okButton",label:"OK",disable:i.isDisabled,color:i.isDisabled?"grey-8":"primary",onClick:t[0]||(t[0]=t=>i.onOKClick(e.selection,e.selectedComponent,e.installedComponents))},null,8,["disable","color"]),[[w]])])),_:1})])),_:1})])),_:1},8,["onHide"])}const go=e=>((0,a.dD)("data-v-6f11174f"),e=e(),(0,a.Cn)(),e),wo={class:"with-padding",id:"button-group-label"},bo={class:"description"},ko=["onClick"],Co=go((()=>(0,a._)("span",null,"View Documentation",-1)));function vo(e,t,n,o,s,i){const r=(0,a.up)("q-radio"),c=(0,a.up)("q-tooltip"),d=(0,a.up)("q-icon"),p=(0,a.up)("q-item"),u=(0,a.up)("q-list");return(0,a.wg)(),(0,a.iD)("div",null,[(0,a._)("div",wo,(0,l.zw)(n.label),1),(0,a.Wm)(u,{id:"button-group"},{default:(0,a.w5)((()=>[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(n.dataList,(n=>((0,a.wg)(),(0,a.j4)(p,{class:"selection-wrapper",key:n.selection},{default:(0,a.w5)((()=>[(0,a.Wm)(r,{modelValue:e.selection,"onUpdate:modelValue":t[0]||(t[0]=t=>e.selection=t),val:n.value,label:n.label,class:"radio-text"},null,8,["modelValue","val","label"]),((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(n.data,(t=>((0,a.wg)(),(0,a.iD)("div",{class:"additional-info",key:t.description},[(0,a._)("div",bo,[(0,a.Uk)(" - "+(0,l.zw)(t.description)+" ",1),(0,a.Wm)(c,{anchor:"bottom middle",self:"top middle"},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(t.tooltip),1)])),_:2},1024)]),t.url?((0,a.wg)(),(0,a.iD)("div",{key:0,id:"new-component-documentation",class:"view-document",onClick:e=>i.openExternalUrl(t.url)},[(0,a.Wm)(d,{name:e.mdiOpenInNew,class:"text-h6"},null,8,["name"]),Co],8,ko)):(0,a.kq)("",!0)])))),128))])),_:2},1024)))),128))])),_:1})])}const Io={props:{label:String,dataList:Array},data:()=>({selection:null}),created:function(){this.mdiOpenInNew=Tt.fOx},watch:{selection(e,t){e&&this.$emit("selectionChanged",{newValue:e})}},methods:{openExternalUrl(e){this.$store.dispatch("openExternalUrl",e)}}};var yo=n(1480);const So=(0,Vt.Z)(Io,[["render",vo],["__scopeId","data-v-6f11174f"]]),_o=So;Bt()(Io,"components",{QList:wn.Z,QItem:bn.Z,QRadio:yo.Z,QTooltip:$t.Z,QIcon:Zt.Z});const xo={components:{RadioButtonGroup:_o},props:{selectedComponentId:String,installedComponentsId:Array},data:function(){return{selection:"",installedComponents:[],installedComponentData:[],selectedComponent:null,selectedComponentLabel:"",selectedComponentDescrition:"",installedComponentLabel:"",firstOption:{value:"replace"},secondOption:{value:"keep",label:"Keep "}}},created:function(){this.mdiClose=Tt.r5M,this.mdiAlertCircleOutline=Tt._gM;let e=this.installedComponentsId.length+1,t=0;this.$store.dispatch("fetchComponentDetails",{componentId:this.selectedComponentId}).then((n=>{this.selectedComponent=n,this.selectedComponentLabel=n.label,this.selectedComponentDescrition=n.description,t++,t==e&&this.loadOptions()})).catch((e=>{console.log("Error during fetching selected component details",e.response.data)})),this.installedComponentsId.forEach((n=>{this.$store.dispatch("fetchComponentDetails",{componentId:n}).then((o=>{this.installedComponents.push(o),""!=this.installedComponentLabel&&(this.installedComponentLabel+=", "),this.installedComponentLabel+=o.label;let s={description:o.description,tooltip:o.label,url:""},i=this.getUrl(n);null!=i&&(s.url=i),this.installedComponentData.push(s),t++,t==e&&this.loadOptions()})).catch((e=>{console.log("Error during fetching installed component details",e.response.data)}))}))},computed:{isDisabled(){return""===this.selection}},methods:{loadOptions(){let e=[{description:this.selectedComponentDescrition,tooltip:this.selectedComponentLabel,url:""}],t=this.getUrl(this.selectedComponentId);null!=t&&(e.url=t),this.firstOption={...this.firstOption,label:"Replace "+this.installedComponentLabel+" with "+this.selectedComponentLabel,data:e},this.secondOption={...this.secondOption,label:"Keep "+this.installedComponentLabel,data:this.installedComponentData}},show(){this.$refs.exclusivityErrorsDialog.show()},hide(){this.$refs.exclusivityErrorsDialog.hide()},onDialogHide(){this.$emit("hide")},getUrl(e){return this.$store.dispatch("getComponentDocumentation",{componentId:e}).then((e=>e&&e.url?e.url:null)).catch((e=>(console.log("Error during fetching installed component documentation",e.response.data),null)))},onOKClick(e,t,n){this.$emit("ok",{selection:e,selectedComponent:t,installedComponents:n}),this.hide()},handleSelectionChange(e){this.selection=e.newValue}}},jo=(0,Vt.Z)(xo,[["render",fo],["__scopeId","data-v-fa994660"]]),Do=jo;Bt()(xo,"components",{QDialog:hn.Z,QCard:Lt.Z,QCardSection:ln.Z,QSpace:un.Z,QBtn:Qt.Z,QIcon:Zt.Z,QTooltip:$t.Z,QCardActions:Cn.Z}),Bt()(xo,"directives",{ClosePopup:In.Z});const Eo=e=>((0,a.dD)("data-v-e5f69b84"),e=e(),(0,a.Cn)(),e),qo={class:"main-title text-h6"},Uo=(0,a.Uk)(" Additional component required! "),Wo={class:"first-message"},No=(0,a.Uk)(" Component "),To=(0,a.Uk)(" requires an additional component. "),Po=Eo((()=>(0,a._)("div",{class:"secondary-message"}," Please choose from the following compliant components. ",-1))),Vo=(0,a.Uk)(" Or see "),Lo=(0,a.Uk)(" for more information ");function Qo(e,t,n,o,s,i){const r=(0,a.up)("q-icon"),c=(0,a.up)("q-card-section"),d=(0,a.up)("RadioButtonGroup"),p=(0,a.up)("confirmation-dialog"),u=(0,a.up)("q-dialog");return(0,a.wg)(),(0,a.j4)(u,{position:"top",ref:"unfulfilledErrorsDialog","transition-show":"scale","transition-hide":"scale",onHide:i.onDialogHide},{default:(0,a.w5)((()=>[(0,a.Wm)(p,{onConfirmClicked:t[1]||(t[1]=t=>i.onOKClick(e.selection)),confirmLabel:"Install",confirmDisabled:i.isDisabled},{default:(0,a.w5)((()=>[(0,a._)("div",qo,[(0,a.Wm)(r,{name:e.mdiAlertCircleOutline},null,8,["name"]),Uo]),(0,a.Wm)(c,{id:"error_subtitle",class:"main-text"},{default:(0,a.w5)((()=>[(0,a._)("div",Wo,[No,(0,a._)("strong",null,(0,l.zw)(n.theComponentLabel),1),To]),Po])),_:1}),(0,a.Wm)(c,{id:"radioButtons",class:"options-content"},{default:(0,a.w5)((()=>[(0,a.Wm)(d,{dataList:this.selectionOptions,onSelectionChanged:i.handleSelectionChange},null,8,["dataList","onSelectionChanged"])])),_:1}),(0,a.Wm)(c,{id:"problemView",class:"main-text"},{default:(0,a.w5)((()=>[Vo,(0,a._)("a",{onClick:t[0]||(t[0]=e=>i.openProblemView()),href:"#"}," Problems View "),Lo])),_:1})])),_:1},8,["confirmDisabled"])])),_:1},8,["onHide"])}const Oo={class:"conf-bar"},Zo=["title"],$o=(0,a.Uk)(" Close "),Fo={class:"conf-card-content"},Mo={class:"action-content"};function Bo(e,t,n,o,s,i){const r=(0,a.up)("q-icon"),c=(0,a.up)("q-tooltip"),d=(0,a.up)("q-btn"),p=(0,a.up)("q-separator"),u=(0,a.up)("q-card-actions"),m=(0,a.up)("q-card"),h=(0,a.Q2)("close-popup");return(0,a.wg)(),(0,a.j4)(m,{class:"conf-card"},{default:(0,a.w5)((()=>[(0,a._)("div",Oo,[n.title?((0,a.wg)(),(0,a.iD)("div",{key:0,class:"title-bar text-subtitle1",title:n.title},[(0,a.Wm)(r,{name:s.alertIcon},null,8,["name"]),(0,a.Uk)(" "+(0,l.zw)(n.title),1)],8,Zo)):(0,a.kq)("",!0),(0,a.wy)(((0,a.wg)(),(0,a.j4)(d,{dense:"",flat:"","icon-right":s.closeIcon,label:"Close","no-caps":""},{default:(0,a.w5)((()=>[(0,a.Wm)(c,{"content-class":"bg-white text-primary"},{default:(0,a.w5)((()=>[$o])),_:1})])),_:1},8,["icon-right"])),[[h]])]),(0,a._)("div",Fo,[(0,a.WI)(e.$slots,"default",{},void 0,!0)]),(0,a._)("div",Mo,[(0,a.Wm)(p),(0,a.Wm)(u,{align:"right"},{default:(0,a.w5)((()=>[n.cancelButton?(0,a.wy)(((0,a.wg)(),(0,a.j4)(d,{key:0,flat:"",label:"Cancel",color:"primary","no-caps":""},null,512)),[[h]]):(0,a.kq)("",!0),(0,a.wy)((0,a.Wm)(d,{label:n.confirmLabel,color:"primary",class:"conf-confirm",disabled:n.confirmDisabled,onClick:i.confirmClicked,"no-caps":""},null,8,["label","disabled","onClick"]),[[h]])])),_:1})])])),_:3})}const Ro={props:{title:{type:String,default:""},cancelButton:{type:Boolean,default:!0},confirmLabel:{type:String,default:"Finish"},confirmDisabled:{type:Boolean,default:!1}},data(){return{selectedVersion:this.currentVersion,closeIcon:Tt.r5M,alertIcon:Tt._gM}},methods:{confirmClicked(){this.$emit("confirmClicked")}}},Ao=(0,Vt.Z)(Ro,[["render",Bo],["__scopeId","data-v-91ffb93a"]]),zo=Ao;Bt()(Ro,"components",{QCard:Lt.Z,QIcon:Zt.Z,QBtn:Qt.Z,QTooltip:$t.Z,QSeparator:mn.Z,QCardActions:Cn.Z}),Bt()(Ro,"directives",{ClosePopup:In.Z});const Ko={props:{theComponentLabel:String,errors:Array},data:()=>({selectionOptions:[],selection:""}),created:function(){this.mdiClose=Tt.r5M,this.mdiAlertCircleOutline=Tt._gM,this.requiredComponentsLoader()},components:{ConfirmationDialog:zo,RadioButtonGroup:_o},computed:{isDisabled(){return""==this.selection}},methods:{createRadioButton(e){Promise.all([this.fetchComponentDetails(e),this.fetchComponentDocumentation(e)]).then((t=>{this.selectionOptions.push({value:e,label:t[0].label,data:[{description:t[0].description,tooltip:t[0].description,url:t[1].url}]})}))},fetchComponentDetails(e){return this.$store.dispatch("fetchComponentDetails",{componentId:e})},fetchComponentDocumentation(e){return this.$store.dispatch("getComponentDocumentation",{componentId:e})},requiredComponentsLoader(){const e=new Set;for(let t of this.errors)if("issueDetails"in t&&"UnfulfilledIssue"===t.issueDetails.type){let n=t.issueDetails.providers;n.map((t=>{e.has(t.id)||(this.createRadioButton(t.id),e.add(t.id))}))}},show(){this.$refs.unfulfilledErrorsDialog.show()},hide(){this.$refs.unfulfilledErrorsDialog.hide()},openProblemView(){this.$store.dispatch("changeView",{View:"org.eclipse.ui.views.ProblemView"}),this.hide()},onDialogHide(){this.$emit("hide")},onOKClick(e){this.$emit("ok",{selection:e}),this.hide()},handleSelectionChange(e){this.selection=e.newValue}}},Ho=(0,Vt.Z)(Ko,[["render",Qo],["__scopeId","data-v-e5f69b84"]]),Go=Ho;function Yo(e,t,n,o,s,i){const r=(0,a.up)("q-btn"),c=(0,a.up)("q-banner");return(0,a.wg)(),(0,a.j4)(c,{id:n.id,"inline-actions":"",rounded:"",class:(0,l.C_)(["text-white",i.bannerTypeClass])},{action:(0,a.w5)((()=>[(0,a.Wm)(r,{onClick:t[0]||(t[0]=e=>i.actionClick()),flat:"",label:n.actionLabel},null,8,["label"])])),default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(n.bannerText)+" ",1)])),_:1},8,["id","class"])}Bt()(Ko,"components",{QDialog:hn.Z,QIcon:Zt.Z,QCardSection:ln.Z});const Jo=new Map;Jo.set("valid","bg-green"),Jo.set("warning","bg-orange"),Jo.set("error","bg-red");const Xo={props:{bannerText:String,bannerType:{default:"warning",type:String},actionLabel:String,id:String},computed:{bannerTypeClass(){return Jo.get(this.bannerType)}},methods:{actionClick(){this.$emit("action-click")}}};var es=n(7128);const ts=(0,Vt.Z)(Xo,[["render",Yo]]),ns=ts;Bt()(Xo,"components",{QBanner:es.Z,QBtn:Qt.Z});const os={curNotifyDlg:new Map},ss={props:{component:Object,componentDetails:Object},data:()=>({shared:os,currentPage:Sn,pages:[{component:Sn},{component:zn}]}),components:{ActionBanner:ns,ComponentDetails:Sn,ComponentDependencies:zn,ValidationErrors:no,ExclusivityError:Do,UnfulfilledErrors:Go},created:function(){window.__componentDetails=this,this.mdiCheck=Tt.oL1,this.mdiClose=Tt.r5M},watch:{latestCompDelta(e,t){e&&this.$nextTick((()=>{if("added"in e&&e.added.length>0){let t=0;"autoAdded"in e&&(t=e.autoAdded.length),this.showCompChangeNotification(e.added[0],!0,t,0)}else if("removed"in e&&e.removed.length>0){let t=0;"autoRemoved"in e&&(t=e.autoRemoved.length),this.showCompChangeNotification(e.removed[0],!1,t,0)}}))},latestValidationErrors(e,t){e&&"component"in e&&"errors"in e&&e.errors.length>0&&this.$nextTick((()=>{switch(e.errorType){case"EXCLUSIVITY_ERROR":this.showExclusivityErrorNotification(e.component,e.targetComponent);break;case"UNFULFILLED_ERROR":this.showUnfulfilledErrorNotification(e.component.label,e.component.id,e.errors);break;default:this.showValidationErrorNotification(e.component,e.errors,e.message)}}))}},computed:{latestCompDelta(){return this.$store.state.latestComponentDelta},latestValidationErrors(){return this.$store.state.latestValidationErrors}},methods:{showDependencies:function(e){this.component&&this.component.componentId!==e?this.selectComponent(e):this.currentPage=zn},showDetails:function(){this.currentPage=Sn},findRoot(e){return e.parent?this.findRoot(e.parent):e},selectComponent:function(e,t=!1){this.$emit("select-component",e,t)},selectRoot(e){let t=this.findRoot(e);this.selectComponent(t.id)},showCompChangeNotification(e,t,n,o){this.shared.curNotifyDlg&&this.shared.curNotifyDlg.has(e.id)&&(this.shared.curNotifyDlg.get(e.id)(),this.shared.curNotifyDlg.delete(e.id)),this.shared.curNotifyDlg.set(e.id,this.$q.notify({icon:this.mdiCheck,html:!0,message:this.getCompChangeNotificationContent(e,t,n,o),position:"top",classes:"comp-change-notif",actions:[{icon:this.mdiClose}],onDismiss:()=>{this.shared.curNotifyDlg&&this.shared.curNotifyDlg.has(e.id)&&this.shared.curNotifyDlg.delete(e.id)}}))},showValidationErrorNotification(e,t,n){this.$q.dialog({component:no,parent:this,componentProps:{theComponent:e,messages:t,mainMessage:n}}).onOk((e=>{e&&this.selectComponent(e,!0)}))},showExclusivityErrorNotification(e,t){this.$q.dialog({component:Do,parent:this,componentProps:{selectedComponentId:e.name,installedComponentsId:t}}).onOk((e=>{this.componentReplaceHandler(e.selection,e.installedComponents,e.selectedComponent)}))},async componentReplaceHandler(e,t,n){if("replace"!==e)return;let o={removeComponentsId:t.map((e=>e.componentId)),componentId:n.componentId};"instances"in t&&(o={...o,instanceNames:t.instances.join()}),await this.$store.dispatch("changeComponent",o)},showUnfulfilledErrorNotification(e,t,n){this.$q.dialog({component:Go,parent:this,componentProps:{theComponentLabel:e,errors:n}}).onOk((e=>{this.$store.dispatch("addComponent",{componentId:e.selection})})).onCancel((()=>{this.$store.dispatch("removeComponent",{componentId:t})}))},getCompChangeNotificationContent(e,t,n,o){let s='<div class="comp-change-notif-inner">';return s+='<div class="notify-title">Component was successfully ',s+=t?"added.</div>":"removed.</div>",s=s+'<div class="notify-details">The '+e.label+" was ",s+=t?"added":"removed",s+=n>0||o>0?" with ":".",n>0&&(s+=n,s+=1===n?" dependency":" dependencies",o>0&&(s+=" and ")),o>0&&(s=s+o+" instance",o>1&&(s+="s")),s+="</div>",t&&(s+='<div class="notify-details"><a href="#" onclick="window.__componentDetails.showInstances(',s=s+"'"+e.id+"'",s+=')" style="color: #0086D9;text-decoration: none;"/>View Instances</a></div>'),s+="</div>",s},showInstances(e){this.shared.curNotifyDlg&&this.shared.curNotifyDlg.has(e)&&this.shared.curNotifyDlg.get(e)(),this.$nextTick((()=>{this.showDependencies(e)}))}}},is=(0,Vt.Z)(ss,[["render",xe],["__scopeId","data-v-a3614dec"]]),as=is;Bt()(ss,"components",{QBtn:Qt.Z});const ls=e=>((0,a.dD)("data-v-58336f54"),e=e(),(0,a.Cn)(),e),rs={class:"launchable-template"},cs={class:"launchable-column"},ds={class:"launchable-header-row"},ps={class:"row items-center outer-row"},us=ls((()=>(0,a._)("div",{class:"col-1 card-title-block-bar"},null,-1))),ms={class:"col"},hs={class:"row items-center card-title-block-content inner-row"},fs={key:0,class:"col-auto card-title-block-col ellipsis qa-details-title qa-launchable-details-title"},gs={class:"col card-title-block-col config-button-col"},ws={class:"launchable-content-row"},bs={key:0},ks=ls((()=>(0,a._)("div",{class:"text-lg"},"Description",-1))),Cs={key:0,class:"description text-sm"},vs={class:"description-code-block"},Is={key:1},ys={class:"text-lg"};function Ss(e,t,n,o,s,i){const r=(0,a.up)("q-tooltip"),c=(0,a.up)("q-btn"),d=(0,a.up)("q-card-section"),p=(0,a.up)("q-card");return(0,a.wg)(),(0,a.iD)("div",rs,[(0,a._)("div",cs,[(0,a._)("div",ds,[(0,a.Wm)(p,{square:"",bordered:"",class:"launchable-template-header"},{default:(0,a.w5)((()=>[(0,a.Wm)(d,{class:"launchable-template-header card-title-block"},{default:(0,a.w5)((()=>[(0,a._)("div",ps,[us,(0,a._)("div",ms,[(0,a._)("div",hs,[void 0!==n.launchable?((0,a.wg)(),(0,a.iD)("span",fs,[(0,a.Uk)((0,l.zw)(n.launchable.label)+" ",1),(0,a.Wm)(r,{"max-width":"250px"},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(n.launchable.label),1)])),_:1})])):(0,a.kq)("",!0),(0,a._)("span",gs,[(0,a.Wm)(c,{"data-studio-comp":n.launchable.launchableId,id:"launchable-details-configure-button",flat:"",dense:"",align:"center",type:"a","no-caps":"",label:"Open",class:"configure-launchable-button studio-primary-button",icon:e.mdiCogs,onClick:t[0]||(t[0]=e=>i.openLaunchable(n.launchable))},{default:(0,a.w5)((()=>[(0,a.Wm)(r,{"max-width":"250px"},{default:(0,a.w5)((()=>[(0,a.Uk)(" Open "+(0,l.zw)(n.launchable.label),1)])),_:1})])),_:1},8,["data-studio-comp","icon"])])])])])])),_:1})])),_:1})]),(0,a._)("div",ws,[(0,a.Wm)(p,{square:"",bordered:"",class:"description-card card-content"},{default:(0,a.w5)((()=>[n.launchable.description?((0,a.wg)(),(0,a.iD)("div",bs,[ks,void 0!==n.launchable?((0,a.wg)(),(0,a.iD)("div",Cs,[(0,a._)("code",vs,(0,l.zw)(n.launchable.description),1)])):(0,a.kq)("",!0)])):(0,a.kq)("",!0),n.launchable.description?(0,a.kq)("",!0):((0,a.wg)(),(0,a.iD)("div",Is,[(0,a._)("div",ys,(0,l.zw)(n.launchable.label),1)]))])),_:1})])])])}const _s={props:{launchable:Object},created:function(){this.mdiCogs=rt.pcj},methods:{openLaunchable(e){let t={launchableId:e.launchableId};this.$store.dispatch("openLaunchable",t)}}},xs=(0,Vt.Z)(_s,[["render",Ss],["__scopeId","data-v-58336f54"]]),js=xs;Bt()(_s,"components",{QCard:Lt.Z,QCardSection:ln.Z,QTooltip:$t.Z,QBtn:Qt.Z,QChip:Ft.Z,QSeparator:mn.Z});const Ds={key:0,id:"FiltersExpansion"},Es={class:"ddlExpansionLabel"},qs={class:"row"},Us={class:"row"},Ws={key:0},Ns={key:1},Ts={key:1,id:"FiltersDropdown",class:"filter-block-cb filter-block-col filter-elem-wide col-auto"},Ps={class:"row"},Vs={class:"row items-center"},Ls={key:0},Qs={key:1},Os={__name:"FilterSelectionGroup",props:{filterGroupSelections:{type:Array,default:()=>[]},filterGroupOptions:{type:Array,default:()=>[]},filterGroupIcon:{type:String,default:""},labelTxt:{type:String,default:""},toolTipTxt:{type:String,default:""},useDropdown:{type:Boolean,default:!0}},emits:["changeFilterSelections"],setup(e,{emit:t}){const n=e=>{t("changeFilterSelections",e)},o=()=>{t("changeFilterSelections",[])};return(t,s)=>{const i=(0,a.up)("q-tooltip"),r=(0,a.up)("q-icon"),c=(0,a.up)("q-space"),d=(0,a.up)("q-btn"),p=(0,a.up)("q-option-group"),u=(0,a.up)("q-expansion-item"),m=(0,a.up)("q-btn-dropdown");return e.useDropdown?((0,a.wg)(),(0,a.iD)("div",Ts,[(0,a.Wm)(i,{class:"filterListToolTip"},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(e.toolTipTxt),1)])),_:1}),(0,a.Wm)(m,{class:"filter-group-dropdown",outline:"","no-caps":""},{label:(0,a.w5)((()=>[(0,a.Wm)(r,{name:e.filterGroupIcon,size:"xs"},null,8,["name"]),(0,a.Uk)(" "+(0,l.zw)(e.labelTxt),1)])),default:(0,a.w5)((()=>[(0,a._)("div",Ps,[(0,a.Wm)(c),(0,a.Wm)(d,{class:"filter-block-col filter-elem-wide col-auto clearLabel",size:"8px",label:"Clear",flat:"",onClick:o})]),(0,a.Wm)(p,{"model-value":e.filterGroupSelections,"onUpdate:modelValue":n,options:e.filterGroupOptions,type:"checkbox",class:"q-px-sm filter-block-col filter-elem-wide col-auto"},{label:(0,a.w5)((e=>[(0,a._)("div",Vs,[e.disable?((0,a.wg)(),(0,a.iD)("em",Ls,(0,l.zw)(e.label),1)):((0,a.wg)(),(0,a.iD)("a",Qs,(0,l.zw)(e.label),1))])])),_:1},8,["model-value","options"])])),_:1})])):((0,a.wg)(),(0,a.iD)("div",Ds,[(0,a.Wm)(i,{class:"filterListToolTip"},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(e.toolTipTxt),1)])),_:1}),(0,a.Wm)(u,{"no-caps":"","dense-toggle":"",dense:"","content-inset-level":.4,"expand-icon-class":"ddlExpansionIcon"},{header:(0,a.w5)((()=>[(0,a._)("label",Es,[(0,a.Wm)(r,{name:e.filterGroupIcon,size:"xs"},null,8,["name"]),(0,a.Uk)(" "+(0,l.zw)(e.labelTxt),1)])])),default:(0,a.w5)((()=>[(0,a._)("div",qs,[(0,a.Wm)(c),(0,a.Wm)(d,{class:"clearLabel",size:"8px",label:"Clear",flat:"",onClick:o})]),(0,a.Wm)(p,{"model-value":e.filterGroupSelections,"onUpdate:modelValue":n,options:e.filterGroupOptions,type:"checkbox",class:"q-mb-sm"},{label:(0,a.w5)((e=>[(0,a._)("div",Us,[e.disable?((0,a.wg)(),(0,a.iD)("em",Ws,(0,l.zw)(e.label),1)):((0,a.wg)(),(0,a.iD)("a",Ns,(0,l.zw)(e.label),1)),(0,a.Wm)(c)])])),_:1},8,["model-value","options"])])),_:1},8,["content-inset-level"])]))}}};var Zs=n(8479);const $s=(0,Vt.Z)(Os,[["__scopeId","data-v-94661a5c"]]),Fs=$s;Bt()(Os,"components",{QTooltip:$t.Z,QExpansionItem:gn.Z,QIcon:Zt.Z,QSpace:un.Z,QBtn:Qt.Z,QOptionGroup:vn.Z,QBtnDropdown:Zs.Z});const Ms=e=>((0,a.dD)("data-v-a14c777a"),e=e(),(0,a.Cn)(),e),Bs={class:"component-template"},Rs={class:"component-column"},As={class:"component-header-row"},zs={class:"row items-center outer-row"},Ks=Ms((()=>(0,a._)("div",{class:"col-1 card-title-block-bar"},null,-1))),Hs={class:"col"},Gs={class:"extension-title card-title-block-content inner-row"},Ys={key:0},Js=(0,a.Uk)(" Disable SDK extension for this project "),Xs=(0,a.Uk)(" Enable SDK extension for this project "),ei={class:"component-content-row"},ti=Ms((()=>(0,a._)("div",{class:"text-lg"},"Name",-1))),ni={key:0,class:"description text-sm"},oi={class:"description-code-block"},si=Ms((()=>(0,a._)("div",{class:"text-lg"},"ID",-1))),ii={key:1,class:"description text-sm"},ai={class:"description-code-block"},li=Ms((()=>(0,a._)("div",{class:"text-lg"},"Description",-1))),ri={key:2,class:"description text-sm"},ci={class:"description-code-block"},di={class:"row justify-start"},pi={class:"col-auto"},ui={class:"column"},mi=Ms((()=>(0,a._)("div",{class:"text-lg"},"Version",-1))),hi={key:0,class:"text-sm"};function fi(e,t,n,s,i,r){const c=(0,a.up)("ActionBanner"),d=(0,a.up)("q-tooltip"),p=(0,a.up)("q-btn"),u=(0,a.up)("q-card-section"),m=(0,a.up)("q-card"),h=(0,a.up)("q-space");return(0,a.wg)(),(0,a.iD)("div",null,[(0,a.Wm)(o.uT,{name:"component-fade",mode:"out-in"},{default:(0,a.w5)((()=>[(0,a._)("div",Bs,[n.component.compatibilty.isCompatible||n.component.compatibilty.hasExtensionUpgrade?(0,a.kq)("",!0):((0,a.wg)(),(0,a.j4)(c,{key:0,id:"component-template-view-conflict-banner",actionLabel:"View conflict",onActionClick:t[0]||(t[0]=e=>r.selectComponent(n.component.compatibilty.conflictId)),bannerText:`Extension ${n.component.id} cannot be added to the project when ${n.component.compatibilty.conflictId} is installed.`,bannerType:"error"},null,8,["bannerText"])),!n.component.compatibilty.isCompatible&&n.component.compatibilty.hasExtensionUpgrade?((0,a.wg)(),(0,a.j4)(c,{key:1,id:"component-template-upgrade-extension-banner",actionLabel:"Upgrade Extension",onActionClick:t[1]||(t[1]=e=>r.upgradeExtension(n.component,n.component.compatibilty.conflictId)),bannerText:`Extension ${n.component.compatibilty.conflictId} can be upgraded to ${n.component.id}. Do you want to upgrade?`,bannerType:"warning"},null,8,["bannerText"])):(0,a.kq)("",!0),(0,a._)("div",Rs,[(0,a._)("div",As,[(0,a.Wm)(m,{square:"",bordered:"",class:"component-template-header"},{default:(0,a.w5)((()=>[(0,a.Wm)(u,{class:"component-template-header card-title-block"},{default:(0,a.w5)((()=>[(0,a._)("div",zs,[Ks,(0,a._)("div",Hs,[(0,a._)("div",Gs,[void 0!==n.component?((0,a.wg)(),(0,a.iD)("div",Ys,[(0,a.Uk)(" SDK Extension: "+(0,l.zw)(n.component.label)+" ",1),(0,a.Wm)(d,{"max-width":"250px"},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(n.component.label),1)])),_:1})])):(0,a.kq)("",!0),n.component.isEnabled?((0,a.wg)(),(0,a.j4)(p,{key:1,id:"component-details-install-button","no-caps":"",class:"positive-button col-auto",label:"Disable Extension",onClick:t[2]||(t[2]=e=>r.postSDKExtensionEnabled(n.component,!1))},{default:(0,a.w5)((()=>[(0,a.Wm)(d,{"content-class":"bg-accent"},{default:(0,a.w5)((()=>[Js])),_:1})])),_:1})):((0,a.wg)(),(0,a.j4)(p,{key:2,id:"component-details-install-button","no-caps":"",class:"positive-button col-auto",label:"Enable Extension",disabled:!n.component.compatibilty.isCompatible,onClick:t[3]||(t[3]=e=>r.postSDKExtensionEnabled(n.component,!0))},{default:(0,a.w5)((()=>[(0,a.Wm)(d,{"content-class":"bg-accent"},{default:(0,a.w5)((()=>[Xs])),_:1})])),_:1},8,["disabled"]))])])])])),_:1})])),_:1})]),(0,a._)("div",ei,[(0,a.Wm)(m,{square:"",bordered:"",class:"description-card card-content"},{default:(0,a.w5)((()=>[ti,void 0!==n.component?((0,a.wg)(),(0,a.iD)("div",ni,[(0,a._)("code",oi,(0,l.zw)(n.component.label),1)])):(0,a.kq)("",!0),(0,a.Wm)(h),si,void 0!==n.component?((0,a.wg)(),(0,a.iD)("div",ii,[(0,a._)("code",ai,(0,l.zw)(n.component.id),1)])):(0,a.kq)("",!0),(0,a.Wm)(h),li,void 0!==n.component?((0,a.wg)(),(0,a.iD)("div",ri,[(0,a._)("code",ci,(0,l.zw)(n.component.description),1)])):(0,a.kq)("",!0),(0,a.Wm)(h),(0,a._)("div",di,[(0,a._)("div",pi,[(0,a._)("div",ui,[mi,void 0!==n.component?((0,a.wg)(),(0,a.iD)("div",hi,(0,l.zw)(n.component.version),1)):(0,a.kq)("",!0)])])])])),_:1})])])])])),_:1})])}const gi={props:{component:Object},components:{ActionBanner:ns},data:function(){return{}},methods:{postSDKExtensionEnabled(e,t){const n={extensionId:e.id,enabled:t};this.$store.dispatch("enableExtension",n)},selectComponent:function(e){this.$emit("select-component",e)},upgradeExtension(e,t){const n={extensionId:e.id,extensionVersion:e.version,oldExtensionId:t};this.$store.commit("saveDeviceAndSdkSelection",n)}}},wi=(0,Vt.Z)(gi,[["render",fi],["__scopeId","data-v-a14c777a"]]),bi=wi;Bt()(gi,"components",{QCard:Lt.Z,QCardSection:ln.Z,QTooltip:$t.Z,QBtn:Qt.Z,QSpace:un.Z,QSeparator:mn.Z});var ki=n(3703);const Ci={components:{FilterSelectionGroup:Fs},data:()=>({filterComponentsByLabel:"Filter components by",treefilter:"",selectFilter:void 0,selectInputVal:void 0,showUserSelected:!1,showConfigurable:!1,showInstalled:!1,showSDKExtensions:!1,showAdvancedComponents:!1,selectedQualities:[],splitterModel:40,selected:"",prevSelected:"",selectedEditable:void 0,selectedConfigurable:void 0,selectedInstanceName:void 0,instancesToDelete:[],componentDetailsCard:as,launchableDetailsCard:js,SdkExtensionCard:bi,currentComponentDetails:void 0,expanded:[],expandedBackup:[],matchesFilter:[],nodeForMenu:void 0,projectName:"",projectInstanceId:"",qualityLabelTxt:"Quality",qualityToolTipTxt:"Show only components with specific qualities"}),created:function(){if(this.mdiCog=rt.Shd,this.mdiCogs=rt.pcj,this.mdiAccount=rt.rI3,this.mdiFolderMultiplePlus=rt.KRk,this.mdiMagnify=rt.I0v,this.mdiCheckboxMarkedCircle=rt.i4Z,this.mdiClose=rt.r5M,this.mdiCheckCircleOutline=rt.k6A,this.mdiFilter=rt.k0z,this.mdiSeal=rt.s1P,this.$store.dispatch("fetchAllComponents"),this.setFilter(this.globalSearchTerm),this.projectName=this.$store.state.projectInfo.name,this.projectInstanceId=this.$store.state.projectInfo.instanceId,ki.Z.has(this.oldUniqueStorageKey())&&(this.setLocalStorageVal(this.getUniqueStorageKey(),ki.Z.getItem(this.oldUniqueStorageKey())),ki.Z.remove(this.oldUniqueStorageKey())),ki.Z.has(this.getUniqueStorageKey())){const e=ki.Z.getItem(this.getUniqueStorageKey());this.selectFilter=e.selectFilter,this.showUserSelected=e.showUserSelected,this.showConfigurable=e.showConfigurable,this.showInstalled=e.showInstalled,this.showSDKExtensions=e.showSDKExtensions,this.showAdvancedComponents=e.showAdvancedComponents,this.selectedQualities=e.selectedQualities}else this.selectFilter=void 0,this.showUserSelected=!1,this.showConfigurable=!1,this.showInstalled=!1,this.showSDKExtensions=!1,this.showAdvancedComponents=!1,this.selectedQualities=["PRODUCTION"]},computed:{functionRequired(){return!1},expandCollapseInactive(){return!(this.nodeForMenu&&"children"in this.nodeForMenu)},qualityOptions(){let e=this.$store.state.projectInfo.qualityOptions;for(const t of e){const e=t.value;this.findByComponentQuality(e)?t.disable=!1:t.disable=!0}return e},uccomponents(){const e=JSON.parse(JSON.stringify(this.$store.state.allComponents));return this.visitNodes(e,void 0),e},selectedComponents(){return this.$store.state.selectedComponents},allComponents(){return this.$store.state.allComponents},hasComponents(){return this.allComponents&&this.allComponents.length>0},selectedComponent(){const e=this.selected;if("comptree"in this.$refs){const t=this.$refs.comptree.getNodeByKey(e);if(!t||!("componentId"in t))return;return t}},selectedLaunchable(){const e=this.selected;if("comptree"in this.$refs){const t=this.$refs.comptree.getNodeByKey(e);if(!t||!("launchableId"in t))return;return t}},selectedSDKExtension(){const e=this.selected;if("comptree"in this.$refs){const t=this.$refs.comptree.getNodeByKey(e);if(!t||"SDKExtensionNode"!==t.type)return;return t}},themeMode(){return this.$q.dark.mode},contextChangedCnt(){return this.$store.state.contextChangedCnt},latestValidationErrors(){return this.$store.state.latestValidationErrors},globalComponentSelection(){return this.$store.state.globalComponentSelection},globalSearchTerm(){return this.$store.state.globalSearchTerm}},watch:{contextChangedCnt(e,t){this.fetchComponentDetails(this.component),this.$store.dispatch("fetchAllComponents")},latestValidationErrors(e,t){this.fetchComponentDetails(this.component),this.$store.dispatch("fetchAllComponents")},themeMode(){this.$store.dispatch("fetchAllComponents")},selectedComponents(e,t){this.$store.dispatch("fetchAllComponents"),this.refreshComponentDetails()},allComponents(e,t){this.refreshComponentDetails()},selectFilter(e,t){this.$nextTick((()=>{this.updateTreeFilter(e,this.selectInputVal,this.showUserSelected,this.showConfigurable,this.showInstalled,this.showSDKExtensions,this.showAdvancedComponents,this.selectedQualities)}))},selectInputVal(e,t){this.$nextTick((()=>{this.updateTreeFilter(this.selectFilter,e,this.showUserSelected,this.showConfigurable,this.showInstalled,this.showSDKExtensions,this.showAdvancedComponents,this.selectedQualities)}))},showUserSelected(e,t){this.$nextTick((()=>{this.updateTreeFilter(this.selectFilter,this.selectInputVal,e,this.showConfigurable,this.showInstalled,this.showSDKExtensions,this.showAdvancedComponents,this.selectedQualities)}))},showConfigurable(e,t){this.$nextTick((()=>{this.updateTreeFilter(this.selectFilter,this.selectInputVal,this.showUserSelected,e,this.showInstalled,this.showSDKExtensions,this.showAdvancedComponents,this.selectedQualities)}))},showInstalled(e,t){this.$nextTick((()=>{this.updateTreeFilter(this.selectFilter,this.selectInputVal,this.showUserSelected,this.showConfigurable,e,this.showSDKExtensions,this.showAdvancedComponents,this.selectedQualities)}))},showSDKExtensions(e,t){this.$nextTick((()=>{this.updateTreeFilter(this.selectFilter,this.selectInputVal,this.showUserSelected,this.showConfigurable,this.showInstalled,e,this.showAdvancedComponents,this.selectedQualities)}))},showAdvancedComponents(e,t){this.$nextTick((()=>{this.updateTreeFilter(this.selectFilter,this.selectInputVal,this.showUserSelected,this.showConfigurable,this.showInstalled,this.showSDKExtensions,e,this.selectedQualities)}))},selectedQualities(e,t){this.$nextTick((()=>{this.updateTreeFilter(this.selectFilter,this.selectInputVal,this.showUserSelected,this.showConfigurable,this.showInstalled,this.showSDKExtensions,this.showAdvancedComponents,e)}))},treefilter(e,t){if(0===t.length&&0!==e.length)this.expandedBackup=[...this.expanded];else if(0===e.length){let e=[...this.matchesFilter];this.matchesFilter.forEach((t=>{let n=this.$refs.comptree.getNodeByKey(t);n&&"path"in n&&(e=[...e,...n.path.split("|")])})),this.expanded=[...this.expandedBackup,...e]}this.matchesFilter=[]},globalComponentSelection(e,t){e&&(this.selectComponent(e,!0),this.$nextTick((()=>{this.$store.commit("setGlobalComponentSelection",null)})))},globalSearchTerm(e,t){e&&(this.clearFilterValue(),this.setFilter(e))}},methods:{setLocalStorageVal(e,t){try{ki.Z.set(e,t)}catch(n){let o=`Failed to save ${e}:${t} into local storage. Error: ${n}`;window.console.log(o)}},oldUniqueStorageKey(){return this.projectName+"_filters"},getUniqueStorageKey(){return this.projectName+this.projectInstanceId+"_filters"},updateSelectedQualities(e){this.selectedQualities=e},setFilter(e){let t=[];e&&e.split(",").forEach((e=>t.push({type:"user",label:e,value:e}))),this.selectFilter=t},getConfigurableClass(e){return e.isEditable?"col-1 icon-configurable":"col-1 icon-configurable disabled"},isFiltered(e,t,n,o,s,i,a,l){return!!(e&&e.length>0||t&&t.length>0||n||o||s||i||a||l&&l.length>0)},selectInputChanged(e,t){t((()=>{this.selectInputVal=e}))},clearFilterValue(){this.$nextTick((()=>{this.$refs.selectFilter.updateInputValue("",!0)}))},filterInputFocusOut(){this.newTreeFilterItem(this.selectInputVal),this.selectInputVal=void 0},newTreeFilterItem(e,t){if(e&&e.length>0){const n=(this.selectFilter||[]).slice();e=e.trim().toLowerCase(),e.length>0&&0===n.filter((t=>t.label.toLowerCase()===e.toLowerCase())).length&&n.push({type:"user",label:e,value:e}),t&&t(e,"add-unique"),this.selectFilter=n,this.clearFilterValue()}},updateTreeFilter(e,t,n,o,s,i,a,l){if(this.isFiltered(e,t,n,o,s,i,a,l)){let r={};r.showUserSelected=n,r.showConfigurable=o,r.showInstalled=s,r.showSDKExtensions=i,r.showAdvancedComponents=a;let c=Object.values(r).reduce(((e,t)=>e+(t?1:0)),0);r.selectedQualities=l,c+=l.length,r.selectFilter=e,this.setLocalStorageVal(this.getUniqueStorageKey(),r),r.selectInputVal=t,this.treefilter=JSON.stringify(r),this.filterComponentsByLabel=0==c?"Filter components by":c+" filters applied"}else this.setLocalStorageVal(this.getUniqueStorageKey(),{showUserSelected:!1,showConfigurable:!1,showInstalled:!1,showSDKExtensions:!1,showAdvancedComponents:!1,selectedQualities:[],selectFilter:void 0}),this.treefilter="",this.filterComponentsByLabel="Filter components by"},refreshComponentDetails(){let e=this.selected;this.$nextTick((()=>{this.selected=e,this.selectionChanged(e),this.findConfigurableComponent()||(this.showConfigurable=!1),this.findInstalledComponent()||(this.showInstalled=!1),this.findUserSelectedComponent()||(this.showUserSelected=!1),this.findSDKExtensionComponent()||(this.showSDKExtensions=!1);for(const e of this.qualityOptions)if(e.disable){const t=this.selectedQualities.indexOf(e.value);t>-1&&this.selectedQualities.splice(t,1)}}))},getTreeNodeForEvent(e){let t;if(e&&e.target&&(e.target.classList.contains("component-tree-node")?t=e.target:e.target.classList.contains("component-tree")||(t=e.target.querySelector(".component-tree-node"))),t&&t.id)return this.$refs.comptree.getNodeByKey(t.id)},contextMenuShown(e){this.nodeForMenu=this.getTreeNodeForEvent(e)},contextMenuHidden(e){this.nodeForMenu=void 0},expandAll(e){this.$refs.comptree.expandAll()},collapseAll(e){this.$refs.comptree&&(this.$refs.comptree.collapseAll(),this.expanded=[])},setExpandState(e,t=!0){this.nodeForMenu&&this.setNodeExpanded(this.nodeForMenu,e,t)},setNodeExpanded(e,t,n){e&&e.id&&(this.doSetNodeExpanded(e.id,t),n&&"children"in e&&e.children.forEach((e=>{this.setNodeExpanded(e,t,n)})))},doSetNodeExpanded(e,t){t?this.expanded.includes(e)||this.expanded.push(e):this.expanded.includes(e)&&(this.expanded=this.expanded.filter(((t,n,o)=>t!==e)))},selectionChanged(e){null==e&&null!==this.prevSelected&&(this.selected=this.prevSelected),this.selectedEditable=void 0,this.selectedConfigurable=void 0,this.selectedInstanceName=void 0;let t=this.$refs.comptree.getNodeByKey(e);t&&(this.prevSelected=e,this.fetchComponentDetails(t))},fetchComponentDetails:function(e){e&&"componentId"in e&&("isEditable"in e&&e.isEditable&&(this.selectedEditable=e.componentId,this.selectedInstanceName=e.instanceName),"isConfigurable"in e&&e.isConfigurable&&(this.selectedConfigurable=e.componentId,this.selectedInstanceName=e.instanceName),this.$store.dispatch("fetchComponentDetails",e).then((e=>(this.currentComponentDetails=e,0))))},makeTreeId(e,t){return e+t},componentTreeFilter(e,t){let n=!0;if(this.showUserSelected&&(this.isUserSelectedComponent(e)||(n&=!1)),this.showConfigurable&&(this.isConfigurableComponent(e)||(n&=!1)),this.showInstalled&&(this.isInstalledComponent(e)||(n&=!1)),this.showSDKExtensions&&(this.isSdkExtensionComponent(e)||(n&=!1)),this.showAdvancedComponents&&(n&=!1),this.selectedQualities&&this.selectedQualities.length>0&&("quality"in e&&this.selectedQualities.includes(e.quality)||(n&=!1)),!n)return!1;let o=!0;if(this.selectFilter&&this.selectFilter.length>0||this.selectInputVal&&this.selectInputVal.length>0){if(this.selectFilter&&this.selectFilter.length>0&&this.selectFilter.forEach((t=>{let n=!1;"type"in t&&"user"===t.type&&(n|=e.label&&e.label.toLowerCase().indexOf(t.value.toLowerCase())>-1,!n&&"keyWords"in e&&(n|=e.keyWords.toLowerCase().indexOf(t.value.toLowerCase())>-1),!n&&"tags"in e&&(n|=e.tags.toLowerCase().indexOf(t.value.toLowerCase())>-1),o&=n)})),this.selectInputVal&&this.selectInputVal.length>0){let t=!1;t|=e.label&&e.label.toLowerCase().indexOf(this.selectInputVal.toLowerCase())>-1,!t&&"keyWords"in e&&(t|=e.keyWords.toLowerCase().indexOf(this.selectInputVal.toLowerCase())>-1),!t&&"tags"in e&&(t|=e.tags.toLowerCase().indexOf(this.selectInputVal.toLowerCase())>-1),o&=t}o&&this.matchesFilter.push(e.id)}return o},findInstalledComponent(){return this.findComponentWithAttribute(this.isInstalledComponent)},isInstalledComponent(e){return"isSelected"in e&&e.isSelected},findConfigurableComponent(){return this.findComponentWithAttribute(this.isConfigurableComponent)},isConfigurableComponent(e){return"isConfigurable"in e&&e.isConfigurable},findUserSelectedComponent(){return this.findComponentWithAttribute(this.isUserSelectedComponent)},isUserSelectedComponent(e){return"isUserSelected"in e&&e.isUserSelected},findSDKExtensionComponent(){return this.findComponentWithAttribute(this.isSdkExtensionComponent)},isSdkExtensionComponent(e){return"categoryType"in e&&"SDKExtensionNode"===e.categoryType},findComponentWithAttribute(e){let t,n=this.allComponents;for(const o of n)if(t=this.doFindComponentWithAttribute(o,e),t)break;return t},doFindComponentWithAttribute(e,t){if(!e)return;let n;if("children"in e)e.children.forEach((e=>{void 0===n&&(n=this.doFindComponentWithAttribute(e,t))}));else if(t(e))return e;return n},findByComponentQuality(e){return this.findComponentWithExpectedAttributeVal(this.isComponentWithQuality,e)},isComponentWithQuality(e,t){return"quality"in e&&e.quality===t},findByComponentId(e){return this.findComponentWithExpectedAttributeVal(this.isComponentWithId,e)},isComponentWithId(e,t){return"componentId"in e&&e.componentId===t},findComponentWithExpectedAttributeVal(e,t){let n,o=this.allComponents;for(const s of o)if(n=this.doFindComponentWithExpectedAttributeVal(s,e,t),n)break;return n},doFindComponentWithExpectedAttributeVal(e,t,n){if(!e)return;let o;if("children"in e)e.children.forEach((e=>{void 0===o&&(o=this.doFindComponentWithExpectedAttributeVal(e,t,n))}));else if(t(e,n))return e;return o},selectComponent(e,t=!1){t?this.$nextTick((()=>{this.clearFilterValue(),this.selectFilter=void 0,setTimeout((()=>{this.doSelectComponent(e)}),500)})):this.doSelectComponent(e)},doSelectComponent(e){let t=this.$refs.comptree.getNodeByKey(e);if(!t){let n=this.findByComponentId(e);n&&(e=n.id,t=this.$refs.comptree.getNodeByKey(e)),t||window.console.log("CLIC: node not found for component id = "+e)}this.makeComponentVisible(t),this.selected=e;let n=document.getElementById(e);n&&setTimeout((()=>{n.scrollIntoView({behavior:"smooth",block:"nearest",inline:"end"})}),500),this.selectionChanged(e)},categorySelected(e){e&&this.setNodeExpanded(e,!this.expanded.includes(e.id),!1)},visitNodes(e,t){e&&e.forEach((e=>{t&&!("parent"in e)&&(e["parent"]=t),"type"in e&&!("header"in e)&&(e["header"]=e["type"],"category"===e.type&&(e["selectable"]=!1,e["handler"]=this.categorySelected)),"children"in e?this.visitNodes(e["children"],e):"img"in e&&(e.img.startsWith("http:")||e.img.startsWith("file:"))&&(e.img="img:"+e.img)}))},makeComponentVisible(e){e&&"parent"in e&&"id"in e["parent"]&&(this.expanded.push(e["parent"].id),this.makeComponentVisible(e["parent"]))},openLaunchable(e){let t={launchableId:e.launchableId};this.$store.dispatch("openLaunchable",t)},configureComponent(e){if("isEditable"in e&&!e.isEditable)return;let t;"instanceName"in e?t=[e.instanceName]:"instantiable"in e&&"children"in e&&(t=[],e.children.forEach((e=>{"instanceName"in e&&t.push(e.instanceName)}))),t?t.forEach((t=>{this.doConfigureComponent(e.componentId,t)})):this.doConfigureComponent(e.componentId)},doConfigureComponent(e,t){let n={componentId:e};t&&(n["instanceName"]=t),this.$store.dispatch("configureComponent",n)}}};var vi=n(1221),Ii=n(3175),yi=n(7786),Si=n(7498),_i=n(6362),xi=n(8359);const ji=(0,Vt.Z)(Ci,[["render",_e],["__scopeId","data-v-d9d2def4"]]),Di=ji;Bt()(Ci,"components",{QBtnDropdown:Zs.Z,QCheckbox:vi.Z,QIcon:Zt.Z,QTooltip:$t.Z,QToggle:Ii.Z,QSelect:yi.Z,QSplitter:Si.Z,QMenu:_i.Z,QList:wn.Z,QItem:bn.Z,QItemSection:kn.Z,QSpinnerIos:xi.Z,QTree:Ot.Z}),Bt()(Ci,"directives",{ClosePopup:In.Z});const Ei={id:"project-overview-layout"},qi={class:"row"};function Ui(e,t,n,o,s,i){const l=(0,a.up)("q-card");return(0,a.wg)(),(0,a.iD)("div",Ei,[(0,a._)("div",qi,[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(e.sections,(e=>((0,a.wg)(),(0,a.iD)("div",{class:"col",key:e.id},[(0,a.Wm)(l,{elevation:"12",class:"project-overview-card"},{default:(0,a.w5)((()=>[((0,a.wg)(),(0,a.j4)((0,a.LL)(e.component),{class:"project-overview-card-component",title:e.title,titleClass:e.titleClass,projectInfo:n.projectInfo,defaultBoard:n.defaultBoard},null,8,["title","titleClass","projectInfo","defaultBoard"]))])),_:2},1024)])))),128))])])}const Wi={class:"project-device-card"};function Ni(e,t,n,s,i,r){const c=(0,a.up)("CardHeaderWithMenu"),d=(0,a.up)("q-card-section"),p=(0,a.up)("q-card");return(0,a.wg)(),(0,a.iD)("div",Wi,[(0,a.Wm)(p,{flat:"",class:"col-1"},{default:(0,a.w5)((()=>[(0,a.Wm)(d,{class:(0,l.C_)(n.titleClass)},{default:(0,a.w5)((()=>[(0,a.Wm)(c,{title:n.title},null,8,["title"])])),_:1},8,["class"])])),_:1}),(0,a.Wm)(o.uT,{name:"component-fade",mode:"out-in"},{default:(0,a.w5)((()=>[((0,a.wg)(),(0,a.j4)((0,a.LL)(e.currentPage),{projectInfo:n.projectInfo,defaultBoard:n.defaultBoard,onEditDeviceSdk:r.showEditor,onCancelEdit:r.showOverview,onSaveEdit:r.showOverview},null,40,["projectInfo","defaultBoard","onEditDeviceSdk","onCancelEdit","onSaveEdit"]))])),_:1})])}const Ti=e=>((0,a.dD)("data-v-19dd994c"),e=e(),(0,a.Cn)(),e),Pi={id:"device-overview",class:"project-overview-card-outer"},Vi={class:"col"},Li={id:"partLabel",class:"title card-content-primary-text"},Qi={id:"boardLabels"},Oi=["id"],Zi=Ti((()=>(0,a._)("div",{id:"sdk-title",class:"title card-content-primary-text"}," Selected SDK ",-1))),$i={id:"sdk-label",class:"content card-content-secondary-text section-text"},Fi=Ti((()=>(0,a._)("div",{id:"generator-title",class:"title card-content-primary-text"}," Project Generators ",-1))),Mi={id:"generator-labels"},Bi=(0,a.Uk)(" Change projects target boards, part, SDK or generator ");function Ri(e,t,n,o,s,i){const r=(0,a.up)("q-circular-progress"),c=(0,a.up)("q-carousel-slide"),d=(0,a.up)("q-tooltip"),p=(0,a.up)("q-carousel"),u=(0,a.up)("q-card-section"),m=(0,a.up)("q-btn"),h=(0,a.up)("q-card");return(0,a.wg)(),(0,a.iD)("div",Pi,[(0,a.Wm)(h,{flat:"",class:"info-container card-content column justify-between"},{default:(0,a.w5)((()=>[(0,a._)("div",Vi,[(0,a.Wm)(u,{class:"board-card"},{default:(0,a.w5)((()=>[(0,a.Wm)(p,{"control-color":"blue",animated:"",modelValue:e.slide,"onUpdate:modelValue":t[0]||(t[0]=t=>e.slide=t),arrows:"",navigation:"",infinite:"",class:"board-carousel"},{default:(0,a.w5)((()=>[e.photosLoading?((0,a.wg)(),(0,a.j4)(c,{key:"loading-slide",name:"loading-slide"},{default:(0,a.w5)((()=>[(0,a.Wm)(r,{indeterminate:"",size:"50px",class:"absolute-center loading-progress"})])),_:1})):(0,a.kq)("",!0),((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(e.boardPhotos,(e=>((0,a.wg)(),(0,a.j4)(c,{key:e.id,name:e.id,"img-src":e.photo},{default:(0,a.w5)((()=>[(0,a.Wm)(d,null,{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(e.label),1)])),_:2},1024)])),_:2},1032,["name","img-src"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,a.Wm)(u,null,{default:(0,a.w5)((()=>[(0,a._)("div",Li,(0,l.zw)(i.partLabel),1),(0,a._)("div",Qi,[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(n.projectInfo.boards,(e=>((0,a.wg)(),(0,a.iD)("div",{class:"content card-content-secondary-text section-text",key:e.opn,id:e.opn},(0,l.zw)(e.label),9,Oi)))),128))])])),_:1}),(0,a.Wm)(u,null,{default:(0,a.w5)((()=>[Zi,(0,a._)("div",$i,(0,l.zw)(i.sdkLabel),1)])),_:1}),(0,a.Wm)(u,null,{default:(0,a.w5)((()=>[Fi,(0,a._)("div",Mi,[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(i.filteredGenerators,(e=>((0,a.wg)(),(0,a.iD)("div",{class:"content card-content-secondary-text section-text",key:e.id},(0,l.zw)(e.name),1)))),128))])])),_:1})]),(0,a.Wm)(m,{class:"edit-button project-overview-button self-end",id:"device-overview-edit-device",label:"Change Target/SDK/Generators",onClick:i.editDevice},{default:(0,a.w5)((()=>[(0,a.Wm)(d,null,{default:(0,a.w5)((()=>[Bi])),_:1})])),_:1},8,["onClick"])])),_:1})])}const Ai={props:{projectInfo:Object,defaultBoard:String},created:function(){this.updateBoardsList(this.projectInfo)},computed:{partLabel(){return this.projectInfo&&"part"in this.projectInfo&&"label"in this.projectInfo.part?this.projectInfo.part.label:""},sdkLabel(){return this.projectInfo&&"sdk"in this.projectInfo&&"label"in this.projectInfo.sdk?this.projectInfo.sdk.label:""},filteredGenerators(){return this.projectInfo&&"generators"in this.projectInfo?this.projectInfo.generators.filter((e=>e.selected)):[]}},methods:{editDevice:function(){this.$emit("edit-device-sdk")},updateBoardsList(e){if(!e)return;let t=[];"boards"in e&&e.boards.forEach((e=>{t.push(e.id)}));let n={};t.length>0&&(n.boards=t.join(",")),"part"in e&&(n.parts=e.part.id),Object.keys(n).length>0?(this.photosLoading=!0,this.$store.dispatch("fetchBoardPhotos",n).then((t=>{this.photosLoading=!1,this.boardPhotos=t,this.slide=this.defaultBoard?this.defaultBoard:e.part.id}))):(this.photosLoading=!1,this.boardPhotos=[{id:"no-boards-slide",photo:"./statics/part.png"}],this.slide="no-boards-slide")}},data:function(){return{slide:"loading-slide",boardPhotos:[],photosLoading:!0}},watch:{projectInfo(e,t){this.updateBoardsList(e)},defaultBoard(e,t){e&&e.length>0&&(this.slide=this.photosLoading?"loading-slide":e)}}};var zi=n(960),Ki=n(1694),Hi=n(3302);const Gi=(0,Vt.Z)(Ai,[["render",Ri],["__scopeId","data-v-19dd994c"]]),Yi=Gi;Bt()(Ai,"components",{QCard:Lt.Z,QCardSection:ln.Z,QCarousel:zi.Z,QCarouselSlide:Ki.Z,QCircularProgress:Hi.Z,QTooltip:$t.Z,QBtn:Qt.Z});const Ji=e=>((0,a.dD)("data-v-037849e5"),e=e(),(0,a.Cn)(),e),Xi={id:"device-editor",class:"project-overview-card-outer"},ea=(0,a.Uk)(" Select the board, part and SDK for the project. "),ta={class:"row items-center q-gutter-x-xs error-message-text"},na={class:"error-message-line"},oa=Ji((()=>(0,a._)("div",{class:"selector-title"},"BOARDS",-1))),sa=(0,a.Uk)(" No results "),ia={key:0,class:"chip-block row"},aa=Ji((()=>(0,a._)("div",{class:"selector-title"},"PART",-1))),la=(0,a.Uk)(" No results "),ra={key:0,class:"chip-block row"},ca={class:"sdk-title"},da=Ji((()=>(0,a._)("div",{class:"selector-title"},"CHANGE SDK",-1))),pa=(0,a.Uk)(" Open dialog to manage available SDKs "),ua=(0,a.Uk)(" No results "),ma=Ji((()=>(0,a._)("div",{class:"selector-title"},"CHANGE PROJECT GENERATORS",-1))),ha=(0,a.Uk)(" No results "),fa={key:0,class:"chip-block row"},ga={class:"actions"};function wa(e,t,n,o,s,i){const r=(0,a.up)("q-card-section"),c=(0,a.up)("q-icon"),d=(0,a.up)("q-item-section"),p=(0,a.up)("q-item"),u=(0,a.up)("q-select"),m=(0,a.up)("q-tooltip"),h=(0,a.up)("q-chip"),f=(0,a.up)("q-btn"),g=(0,a.up)("q-card-actions"),w=(0,a.up)("q-card");return(0,a.wg)(),(0,a.iD)("div",Xi,[(0,a.Wm)(w,{flat:"",class:"fit card-content"},{default:(0,a.w5)((()=>[(0,a.Wm)(r,null,{default:(0,a.w5)((()=>[ea])),_:1}),e.errorMessage?((0,a.wg)(),(0,a.j4)(r,{key:0,class:"instructions studio-negative-icon"},{default:(0,a.w5)((()=>[(0,a._)("div",ta,[e.errorMessage?((0,a.wg)(),(0,a.j4)(c,{key:0,class:"error-message-line",name:e.mdiCloseCircle},null,8,["name"])):(0,a.kq)("",!0),(0,a._)("div",na,(0,l.zw)(e.errorMessage),1)])])),_:1})):(0,a.kq)("",!0),(0,a.Wm)(r,null,{default:(0,a.w5)((()=>[oa,(0,a.Wm)(u,{filled:"",modelValue:e.boardmodel,"onUpdate:modelValue":t[0]||(t[0]=t=>e.boardmodel=t),"use-input":"","hide-hint":"","hide-selected":"",multiple:"",dense:"","input-debounce":"0",label:"Search or Select",options:e.boardoptions,"option-label":"label","option-value":"opn",ref:"boardSelect",onFilter:i.boardFilterFn,onFilterAbort:i.abortBoardFilterFn,onAdd:i.onNewBoardValue},{"no-option":(0,a.w5)((()=>[(0,a.Wm)(p,null,{default:(0,a.w5)((()=>[(0,a.Wm)(d,{class:"text-grey"},{default:(0,a.w5)((()=>[sa])),_:1})])),_:1})])),_:1},8,["modelValue","options","onFilter","onFilterAbort","onAdd"]),e.boardmodel?((0,a.wg)(),(0,a.iD)("div",ia,[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(e.boardmodel,(e=>((0,a.wg)(),(0,a.j4)(h,{key:e.opn,removable:"",onRemove:t=>i.removeBoard(e.opn)},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(e.label)+" ",1),(0,a.Wm)(m,null,{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(e.label),1)])),_:2},1024)])),_:2},1032,["onRemove"])))),128))])):(0,a.kq)("",!0)])),_:1}),(0,a.Wm)(r,null,{default:(0,a.w5)((()=>[aa,(0,a.Wm)(u,{filled:"",modelValue:e.partmodel,"onUpdate:modelValue":t[1]||(t[1]=t=>e.partmodel=t),dense:"","use-input":"","hide-hint":"","hide-selected":"","input-debounce":"0",label:"Search or Select",options:e.partoptions,"option-label":"label","option-value":"id",ref:"partSelect",onFilter:i.partFilterFn,onFilterAbort:i.abortPartFilterFn},{"no-option":(0,a.w5)((()=>[(0,a.Wm)(p,null,{default:(0,a.w5)((()=>[(0,a.Wm)(d,{class:"text-grey"},{default:(0,a.w5)((()=>[la])),_:1})])),_:1})])),_:1},8,["modelValue","options","onFilter","onFilterAbort"]),e.partmodel?((0,a.wg)(),(0,a.iD)("div",ra,[(0,a.Wm)(h,{key:"partmodel.id"},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(e.partmodel.label)+" ",1),(0,a.Wm)(m,null,{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(e.partmodel.label),1)])),_:1})])),_:1})])):(0,a.kq)("",!0)])),_:1}),(0,a.Wm)(r,null,{default:(0,a.w5)((()=>[(0,a._)("div",ca,[da,(0,a._)("span",null,[(0,a.Wm)(f,{id:"device-editor-manage-sdk-button",flat:"",dense:"",align:"right",type:"a","no-caps":"",class:"manage-sdk-button",label:"Manage SDKs",onClick:i.manageSdks,icon:e.mdiSettings},{default:(0,a.w5)((()=>[(0,a.Wm)(m,null,{default:(0,a.w5)((()=>[pa])),_:1})])),_:1},8,["onClick","icon"])])]),(0,a.Wm)(u,{filled:"",clearable:"",modelValue:e.sdkmodel,"onUpdate:modelValue":t[2]||(t[2]=t=>e.sdkmodel=t),dense:"","use-input":"","hide-hint":"","hide-selected":"","fill-input":"","input-debounce":"0",label:"Select SDK",options:e.sdkoptions,"option-label":"label","option-value":"id",onFilter:i.sdkFilterFn,onFilterAbort:i.abortSdkFilterFn,ref:"sdkSelect"},{"no-option":(0,a.w5)((()=>[(0,a.Wm)(p,null,{default:(0,a.w5)((()=>[(0,a.Wm)(d,{class:"text-grey"},{default:(0,a.w5)((()=>[ua])),_:1})])),_:1})])),default:(0,a.w5)((()=>[e.sdkmodel?((0,a.wg)(),(0,a.j4)(m,{key:0},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(e.sdkmodel.label),1)])),_:1})):(0,a.kq)("",!0)])),_:1},8,["modelValue","options","onFilter","onFilterAbort"])])),_:1}),(0,a.Wm)(r,null,{default:(0,a.w5)((()=>[ma,(0,a.Wm)(u,{filled:"",modelValue:e.generatorModel,"onUpdate:modelValue":t[3]||(t[3]=t=>e.generatorModel=t),dense:"","use-input":"","hide-hint":"","hide-selected":"",multiple:"","input-debounce":"0",label:"Search or Select",options:e.generatorOptions,"option-label":"name","option-value":"id",onAdd:i.updateGeneratorSelection,onRemove:i.removeGeneratorSelection,ref:"generatorSelect"},{"no-option":(0,a.w5)((()=>[(0,a.Wm)(p,null,{default:(0,a.w5)((()=>[(0,a.Wm)(d,{class:"text-grey"},{default:(0,a.w5)((()=>[ha])),_:1})])),_:1})])),_:1},8,["modelValue","options","onAdd","onRemove"]),e.generatorModel?((0,a.wg)(),(0,a.iD)("div",fa,[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(e.generatorModel,(e=>((0,a.wg)(),(0,a.j4)(h,{class:"max-width-chip",key:e.id,label:e.name,removable:"",onRemove:t=>i.removeGenerator(e.id)},{default:(0,a.w5)((()=>[(0,a.Wm)(m,null,{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(e.description),1)])),_:2},1024)])),_:2},1032,["label","onRemove"])))),128))])):(0,a.kq)("",!0)])),_:1}),(0,a._)("div",ga,[(0,a.Wm)(g,{align:"around"},{default:(0,a.w5)((()=>[(0,a.Wm)(f,{"text-user":"",outline:"",class:"cancel-button board-cancel-button",id:"device-editor-cancel-button",label:"Cancel",onClick:i.cancelEdit},null,8,["onClick"]),(0,a.Wm)(f,{disabled:e.errorMessage,"text-user":"",class:"edit-button board-edit-button",id:"device-editor-save-button",label:"Save",onClick:i.saveEdit},null,8,["disabled","onClick"])])),_:1})])])),_:1})])}const ba={created:function(){this.mdiSettings=Tt.WkI,this.mdiCloseCircle=Tt.lY3,this.boardsFetchHandler(),this.sdkFetchHandler(),this.generatorsHandler()},props:{projectInfo:Object,title:String,titleClass:String},data:function(){return{errorMessage:null,boardmodel:null,partmodel:null,sdkmodel:null,generatorModel:[],boardoptions:[],partoptions:[],sdkoptions:[],generatorOptions:[],partsForBoards:null}},watch:{boardmodel(e,t){let n=!0,o=0;if(t&&(o=t.length),e&&e.length>o){let o=this.findNewBoard(e,t);if(o){let e={newBoardId:o.id,currentBoardIds:[]};t&&t.length>0&&t.forEach((t=>{e.currentBoardIds.push(t.id)})),this.$store.dispatch("checkBoardListConsistency",e).then((e=>{this.updateBoardSelection(o.id,e.autoAddBoards,e.autoRemoveBoards),this.fetchPartsForBoards(this.boardmodel)})).catch((()=>{this.fetchPartsForBoards(this.boardmodel)})),n=!1}}n&&this.fetchPartsForBoards(e)}},computed:{boards(){return this.$store.state.boards},parts(){if(!this.partsForBoards)return;const e=this.partsForBoards;return Object.keys(e).map((t=>e[t]))},sdks(){return this.$store.state.sdks}},methods:{boardsFetchHandler(){this.$store.dispatch("fetchBoards").then((e=>{var t,n;if(e&&(null===(t=this.projectInfo)||void 0===t||null===(n=t.boards)||void 0===n?void 0:n.length)>0){let t=[];e.forEach((e=>{this.projectInfo.boards.filter((t=>t.id===e.id)).length>0&&t.push(e)})),t.length>0&&(this.boardmodel=JSON.parse(JSON.stringify(t)))}this.boardmodel||this.fetchPartsForBoards(this.boardmodel)}))},sdkFetchHandler(){this.$store.dispatch("fetchSdks",{filter:"ucsdks"}).then((e=>{var t;if(e&&null!==(t=this.projectInfo)&&void 0!==t&&t.sdk){let t;e.some((e=>this.projectInfo.sdk.id===e.id&&(t=e,!0))),t&&(this.sdkmodel=JSON.parse(JSON.stringify(t)))}}))},generatorsHandler(){var e;null!==(e=this.projectInfo)&&void 0!==e&&e.generators&&(this.generatorOptions=JSON.parse(JSON.stringify(this.projectInfo.generators)),this.generatorOptions.forEach((e=>{e.selected&&this.generatorModel.push(e)})))},fetchPartsForBoards(e){this.errorMessage=null;let t=[];this.partsForBoards=null;const n=this.partmodel;this.partmodel=null,e&&e.forEach((e=>{t.push(e.id)}));let o={};t.length>0&&(o["filter"]="partsForBoards",t.length>0&&(o["boards"]=t.join(","))),this.$store.dispatch("fetchParts",o).then((e=>{if(this.partsForBoards=e,!n&&e&&Object.keys(e).length>0&&this.projectInfo&&"part"in this.projectInfo){let t;Object.keys(e).some((n=>{let o=e[n];return o.id===this.projectInfo.part.id&&(t=o,!0)})),t&&(this.partmodel=JSON.parse(JSON.stringify(t)))}!this.partmodel&&n&&this.partsForBoards&&Object.values(this.partsForBoards).forEach((e=>{this.partmodel||n.id===e.id&&(this.partmodel=n)})),!this.partmodel&&this.partsForBoards&&Object.keys(this.partsForBoards).length>0&&(this.partmodel=this.partsForBoards[Object.keys(this.partsForBoards).sort()[0]]),this.partmodel||(this.errorMessage="Select a part to develop with")}))},findNewBoard(e,t){let n=null;return e.forEach((e=>{if(n)return;let o=this.findBoardById(e.id,t);o||(n=e)})),n},updateBoardSelection(e,t,n){this.addBoardById(e),t&&t.forEach((e=>{this.addBoardById(e)})),n&&n.forEach((e=>{this.removeBoardById(e)}))},addBoardById(e){let t=this.findBoardById(e,this.boards);t&&!this.findBoardById(e,this.boardmodel)&&this.boardmodel.push(t)},removeBoardById(e){let t=this.findBoardById(e,this.boardmodel);if(t){let e=this.boardmodel.indexOf(t);e>-1&&this.boardmodel.splice(e,1)}},findBoardById(e,t){if(!e||!t)return null;let n=null;return t.forEach((t=>{n||e===t.id&&(n=t)})),n},saveEdit:function(){this.$emit("save-edit",this.boardmodel,this.partmodel,this.sdkmodel,this.generatorModel)},cancelEdit:function(){this.$emit("cancel-edit")},manageSdks:function(){this.$store.dispatch("showManageSdks")},brdFilter(e,t){if(!e||"string"!==typeof t)return!1;const n=t.toLowerCase();return e.opn&&e.opn.toLowerCase().includes(n)||e.label&&e.label.toLowerCase().includes(n)||e.name&&e.name.toLowerCase().includes(n)},boardFilterFn(e,t,n){t((()=>{this.boardoptions=""===e?this.boards:this.boards.filter((t=>this.brdFilter(t,e)))}))},partFilter(e,t){if(!e||!e.name||"string"!==typeof e.name||"string"!==typeof t)return!1;let n=t.toLowerCase();return e.name.toLowerCase().includes(n)||e.label.toLowerCase().includes(n)},partFilterFn(e,t,n){t((()=>{this.partoptions=""===e?this.parts:this.parts.filter((t=>this.partFilter(t,e)))}))},abortPartFilterFn(){},sdkFilter(e,t){if(!e||!e.label||"string"!==typeof e.label||"string"!==typeof t)return!1;const n=t.toLowerCase();return e.label.toLowerCase().includes(n)},sdkFilterFn(e,t,n){t((()=>{this.sdkoptions=""===e?this.sdks:this.sdks.filter((t=>this.sdkFilter(t,e)))}))},abortSdkFilterFn(){},abortBoardFilterFn(){},updateGeneratorSelection(e){e.value.selected=!0,this.$nextTick((()=>{this.$refs.generatorSelect.updateInputValue("",!0),this.$refs.generatorSelect.hidePopup()}))},removeGeneratorSelection(e){this.removeGenerator(e.value.id)},removeGenerator(e){let t=this.projectInfo.generators.filter((t=>t.id===e));t.selected=!1,this.generatorModel=this.generatorModel.filter((t=>t.id!==e))},onNewBoardValue(e){this.$nextTick((()=>{this.$refs.boardSelect.updateInputValue("",!0),this.$refs.boardSelect.hidePopup()}))},removeBoard(e){let t=-1;for(const n in this.boardmodel)if(e===this.boardmodel[n].opn){t=n;break}t>=0&&this.boardmodel.splice(t,1)},removePart(e){this.partmodel=null}}};var ka=n(8149);const Ca=(0,Vt.Z)(ba,[["render",wa],["__scopeId","data-v-037849e5"]]),va=Ca;Bt()(ba,"components",{QCard:Lt.Z,QCardSection:ln.Z,QIcon:Zt.Z,QSelect:yi.Z,QItem:bn.Z,QItemSection:kn.Z,QChip:Ft.Z,QTooltip:$t.Z,QBtn:Qt.Z,QCardActions:Cn.Z,QField:ka.Z});const Ia={class:"row items-center"},ya=(0,a._)("div",{id:"bar",class:"card-title-block-bar"},null,-1);function Sa(e,t,n,o,s,i){const r=(0,a.up)("q-toolbar-title"),c=(0,a.up)("q-menu"),d=(0,a.up)("q-btn");return(0,a.wg)(),(0,a.iD)("div",Ia,[ya,(0,a.Wm)(r,{id:"title",class:"card-title-block-content"},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(n.title),1)])),_:1}),n.withOptions?((0,a.wg)(),(0,a.j4)(d,{key:0,flat:"",id:"showMore",icon:e.showMoreIcon},{default:(0,a.w5)((()=>[(0,a.Wm)(c,{id:"options"},{default:(0,a.w5)((()=>[(0,a.WI)(e.$slots,"default")])),_:3})])),_:3},8,["icon"])):(0,a.kq)("",!0)])}const _a={props:{title:String,withOptions:Boolean},data:function(){return{showMoreIcon:Tt.XMI}}};var xa=n(1973);const ja=(0,Vt.Z)(_a,[["render",Sa]]),Da=ja;Bt()(_a,"components",{QToolbarTitle:xa.Z,QBtn:Qt.Z,QMenu:_i.Z});const Ea={components:{DeviceOverview:Yi,DeviceSDKEditor:va,CardHeaderWithMenu:Da},props:{projectInfo:Object,defaultBoard:String,title:String,titleClass:String},data:()=>({currentPage:(0,i.XI)(Yi),pages:[{component:Yi},{component:va}]}),methods:{showEditor:function(){this.currentPage=va},showOverview:function(e,t,n,o){if(e||t||n){let o={};e&&(o["boards"]=e),t&&(o["part"]=t),n&&(o["sdk"]=n),this.$store.commit("saveDeviceAndSdkSelection",o)}o&&this.$store.commit("saveSelectedGenerators",o.map((e=>e.id))),this.currentPage=Yi}}},qa=(0,Vt.Z)(Ea,[["render",Ni]]),Ua=qa;Bt()(Ea,"components",{QCard:Lt.Z,QCardSection:ln.Z});const Wa=e=>((0,a.dD)("data-v-645bf613"),e=e(),(0,a.Cn)(),e),Na={class:"project-details-card"},Ta=(0,a.Uk)("Configuration"),Pa=(0,a.Uk)("Force Generation"),Va=(0,a.Uk)("Generate Project Report"),La={class:"project-overview-card-outer"},Qa={class:"col"},Oa={class:"heading card-content-primary-text"},Za={id:"proj-details-proj-name"},$a=(0,a.Uk)(" Change project name "),Fa=Wa((()=>(0,a._)("div",{class:"row items-center all-pointer-events",id:"rename-warning"}," Warning: Renaming your project will automatically close this view ",-1))),Ma={class:"content card-content-secondary-text",id:"proj-details-proj-description"},Ba=Wa((()=>(0,a._)("div",{class:"heading card-content-primary-text"}," Category ",-1))),Ra={class:"content card-content-secondary-text",id:"proj-details-proj-category"},Aa=Wa((()=>(0,a._)("div",{class:"heading card-content-primary-text"}," Preferred SDK ",-1))),za={class:"content card-content-secondary-text",id:"proj-details-proj-sdk"},Ka=Wa((()=>(0,a._)("div",{class:"heading card-content-primary-text"}," Import Mode ",-1))),Ha={key:0,class:"report-link-container"},Ga=Wa((()=>(0,a._)("div",{class:"title card-content-primary-text"}," Recent Project Report: ",-1))),Ya=["disabled"],Ja={key:0,class:"warning-msg info-container content"},Xa=(0,a.Uk)(" Generates a PDF summary of project configuration ");function el(e,t,n,s,i,r){const c=(0,a.up)("q-item-section"),d=(0,a.up)("q-item"),p=(0,a.up)("q-list"),u=(0,a.up)("CardHeaderWithMenu"),m=(0,a.up)("q-card-section"),h=(0,a.up)("q-card"),f=(0,a.up)("q-tooltip"),g=(0,a.up)("q-icon"),w=(0,a.up)("q-input"),b=(0,a.up)("q-popup-edit"),k=(0,a.up)("q-select"),C=(0,a.up)("q-btn"),v=(0,a.Q2)("close-popup");return(0,a.wg)(),(0,a.iD)("div",null,[(0,a._)("div",Na,[(0,a.Wm)(h,{flat:"",class:"col-1"},{default:(0,a.w5)((()=>[(0,a.Wm)(m,{class:(0,l.C_)(n.titleClass)},{default:(0,a.w5)((()=>[(0,a.Wm)(u,{title:n.title,withOptions:!0},{default:(0,a.w5)((()=>[(0,a.wy)(((0,a.wg)(),(0,a.j4)(p,{style:{"min-width":"100px"}},{default:(0,a.w5)((()=>[r.isConfigurable?((0,a.wg)(),(0,a.j4)(d,{key:0,clickable:"",onClick:r.openProjectConfigFile},{default:(0,a.w5)((()=>[(0,a.Wm)(c,null,{default:(0,a.w5)((()=>[Ta])),_:1})])),_:1},8,["onClick"])):(0,a.kq)("",!0),(0,a.Wm)(d,{clickable:"",onClick:r.forceGeneration},{default:(0,a.w5)((()=>[(0,a.Wm)(c,null,{default:(0,a.w5)((()=>[Pa])),_:1})])),_:1},8,["onClick"]),(0,a.Wm)(d,{clickable:"",onClick:r.generateProjectReport},{default:(0,a.w5)((()=>[(0,a.Wm)(c,null,{default:(0,a.w5)((()=>[Va])),_:1})])),_:1},8,["onClick"])])),_:1})),[[v]])])),_:1},8,["title"])])),_:1},8,["class"])])),_:1}),(0,a._)("div",La,[(0,a.Wm)(h,{flat:"",class:"info-container card-content column justify-between"},{default:(0,a.w5)((()=>[(0,a._)("div",Qa,[(0,a.Wm)(m,null,{default:(0,a.w5)((()=>[(0,a._)("div",Oa,[(0,a._)("div",Za,[(0,a.Uk)((0,l.zw)(r.projectName)+" ",1),(0,a.Wm)(g,{class:"col-1",id:"proj-rename-icon",name:e.mdiPencil},{default:(0,a.w5)((()=>[(0,a.Wm)(f,null,{default:(0,a.w5)((()=>[$a])),_:1})])),_:1},8,["name"])]),(0,a.Wm)(b,{modelValue:e.newProjectName,"onUpdate:modelValue":t[0]||(t[0]=t=>e.newProjectName=t),buttons:"",title:"Enter new project name","label-set":"Save","label-cancel":"Cancel",cover:!1,validate:r.validateNewProjectName,onCancel:r.resetNewProjectName,onSave:r.changeProjectName},{default:(0,a.w5)((t=>[(0,a.Wm)(w,{modelValue:t.value,"onUpdate:modelValue":[e=>t.value=e,n=>e.newProjectName=t.value],type:"text",error:e.errorProjectName,"error-message":e.errorMessageProjectName,dense:"",autofocus:"",onKeyup:(0,o.D2)(t.set,["enter"])},{hint:(0,a.w5)((()=>[Fa])),_:2},1032,["modelValue","onUpdate:modelValue","error","error-message","onKeyup"])])),_:1},8,["modelValue","validate","onCancel","onSave"])]),(0,a._)("div",Ma,(0,l.zw)(n.projectInfo.description),1)])),_:1}),(0,a.Wm)(m,null,{default:(0,a.w5)((()=>[Ba,(0,a._)("div",Ra,(0,l.zw)(n.projectInfo.category),1)])),_:1}),(0,a.Wm)(m,null,{default:(0,a.w5)((()=>[Aa,(0,a._)("div",za,(0,l.zw)(r.sdkLabel),1)])),_:1}),(0,a.Wm)(m,{class:"import-mode-section"},{default:(0,a.w5)((()=>[Ka,(0,a.Wm)(k,{borderless:"",outlined:"",dense:"","options-dense":"",modelValue:e.currentImportMode,"onUpdate:modelValue":t[1]||(t[1]=e=>r.importModeChanged(e)),options:n.projectInfo.importModes,"option-value":"id"},null,8,["modelValue","options"])])),_:1})]),this.$store.getters.projectReportInfo.generated?((0,a.wg)(),(0,a.iD)("div",Ha,[Ga,(0,a._)("button",{class:"linklike content",disabled:r.isDisabled,onClick:t[2]||(t[2]=e=>r.openProjectReport())},(0,l.zw)(this.getPdfFileName),9,Ya),this.$store.getters.projectReportInfo.outdated?((0,a.wg)(),(0,a.iD)("p",Ja," Project Report is outdated ")):(0,a.kq)("",!0)])):(0,a.kq)("",!0),(0,a.Wm)(C,{class:"edit-button project-overview-button self-end",label:"Generate Project Report",id:"generate-project-report","no-caps":"",onClick:r.generateProjectReport},{default:(0,a.w5)((()=>[(0,a.Wm)(f,{"max-width":"250px"},{default:(0,a.w5)((()=>[Xa])),_:1})])),_:1},8,["onClick"])])),_:1})])])])}const tl={components:{CardHeaderWithMenu:Da},created:function(){this.mdiSettings=Tt.WkI,this.mdiPencil=Tt.r9},props:{projectInfo:Object,title:String,titleClass:String},data:()=>({currentImportMode:null,newProjectName:"",errorProjectName:!1,errorMessageProjectName:"",errorMessageReport:"Couldn't find local report file."}),computed:{sdkLabel(){return this.projectInfo&&"sdk"in this.projectInfo&&"label"in this.projectInfo.sdk?this.projectInfo.sdk.label:""},isConfigurable(){let e=!1;return this.projectInfo&&"isConfigurable"in this.projectInfo&&this.projectInfo.isConfigurable&&(e=!0),e},projectName(){return"name"in this.projectInfo?this.projectInfo.name:""},getPdfFileName(){let e=this.$store.getters.projectReportInfo.path;return e.includes("\\")?e.split("\\").pop():e.includes("/")?e.split("/").pop():this.$store.getters.projectReportInfo.loading?"Generating...":this.errorMessageReport},isDisabled(){return this.getPdfFileName===this.errorMessageReport||this.$store.getters.projectReportInfo.loading}},methods:{async generateProjectReport(){this.$store.commit("setProjectReportInfo",{path:"",loading:!0,success:!0,msg:"Generating Report..."}),await this.$store.dispatch("generateProjectReport").then((e=>{this.$store.commit("setProjectReportInfo",{path:e.data.path,loading:!1,generated:!0,msg:"Project Report Generated - "})})).catch((e=>{this.$store.commit("setProjectReportInfo",{success:!1,loading:!1,msg:"Failed To Generate Project Report"})}))},forceGeneration(){this.$store.dispatch("forceGeneration")},importModeChanged(e){this.$store.commit("setImportMode",e)},openProjectConfigFile(){if(this.isConfigurable&&"componentId"in this.projectInfo){let e={componentId:this.projectInfo.componentId};this.$store.dispatch("configureProject",e)}},resetNewProjectName(){this.newProjectName="","name"in this.projectInfo&&(this.newProjectName=this.projectInfo.name),this.errorProjectName=!1,this.errorMessageProjectName=""},changeProjectName(){this.$store.commit("setProjectName",this.newProjectName)},validateNewProjectName(e){const t=/^[\-a-zA-Z0-9_]+$/;if(!t.test(e)){const t=/[\-a-zA-Z0-9_]/g,n=e.replace(t,"");if(n){this.errorProjectName=!0,this.errorMessageProjectName="Invalid Project name. Characters not allowed: ";for(const e of n)this.errorMessageProjectName=this.errorMessageProjectName+e+", ";return this.errorMessageProjectName.length>2&&(this.errorMessageProjectName=this.errorMessageProjectName.slice(0,-2)),!1}}return this.errorProjectName=!1,this.errorMessageProjectName="",!0},openProjectReport:function(){const e=this.$store.getters.projectReportInfo.path;this.$store.dispatch("openEditor",e)}},watch:{projectInfo(e,t){if("name"in e&&(this.newProjectName=e.name),"importModes"in e&&"currentImportMode"in e){let t;e.importModes.some((n=>e.currentImportMode===n.id&&(t=n,!0))),t&&(this.currentImportMode=JSON.parse(JSON.stringify(t)))}}}};var nl=n(9843);const ol=(0,Vt.Z)(tl,[["render",el],["__scopeId","data-v-645bf613"]]),sl=ol;Bt()(tl,"components",{QCard:Lt.Z,QCardSection:ln.Z,QList:wn.Z,QItem:bn.Z,QItemSection:kn.Z,QIcon:Zt.Z,QTooltip:$t.Z,QPopupEdit:nl.Z,QInput:fn.Z,QSelect:yi.Z,QBtn:Qt.Z}),Bt()(tl,"directives",{ClosePopup:In.Z});const il={class:"quick-link-label-sm"},al={class:"quick-link-label"};function ll(e,t,n,o,s,i){const r=(0,a.up)("CardHeaderWithMenu"),c=(0,a.up)("q-card-section"),d=(0,a.up)("q-icon"),p=(0,a.up)("q-card"),u=(0,a.up)("q-scroll-area");return(0,a.wg)(),(0,a.j4)(p,{class:"project-quicklink-card"},{default:(0,a.w5)((()=>[(0,a.Wm)(c,{class:(0,l.C_)(n.titleClass)},{default:(0,a.w5)((()=>[(0,a.Wm)(r,{title:n.title},null,8,["title"])])),_:1},8,["class"]),i.isGrid?((0,a.wg)(),(0,a.j4)(c,{key:0,class:"fit q-pt-none q-pb-xl row"},{default:(0,a.w5)((()=>[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(this.links,(e=>((0,a.wg)(),(0,a.iD)("div",{class:"col-6 q-px-sm q-py-md",key:e.data},[(0,a.Wm)(p,{flat:"",bordered:"",class:"grid-element",onClick:t=>i.onLinkClick(e)},{default:(0,a.w5)((()=>[(0,a._)("div",null,[(0,a.Wm)(d,{name:i.getIconName(e),color:"grey",size:"4rem"},null,8,["name"])]),(0,a._)("div",il,(0,l.zw)(e.alias),1)])),_:2},1032,["onClick"])])))),128))])),_:1})):((0,a.wg)(),(0,a.j4)(c,{key:1,class:"fit q-pt-none"},{default:(0,a.w5)((()=>[(0,a.Wm)(u,{class:"scrollable-card-body"},{default:(0,a.w5)((()=>[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(this.links,(e=>((0,a.wg)(),(0,a.iD)("div",{key:e.data},[(0,a.Wm)(p,{flat:"",bordered:"",class:"q-pa-md q-my-sm q-mx-md cursor-pointer bg-transparent",onClick:t=>i.onLinkClick(e)},{default:(0,a.w5)((()=>[(0,a.Wm)(d,{name:i.getIconName(e),color:"grey-5",size:"3rem"},null,8,["name"]),(0,a._)("span",al,(0,l.zw)(e.alias),1)])),_:2},1032,["onClick"])])))),128))])),_:1})])),_:1}))])),_:1})}const rl={components:{CardHeaderWithMenu:Da},props:{title:String,titleClass:String},created:function(){this.mdiFileCode=rt.g2y,this.mdiBluetooth=rt.iwE,this.mdiMemory=rt.Mp7,this.mdiCog=rt.Shd,this.mdiOpenInNew=rt.fOx,this.mdiHexagonMultiple=rt.cm3,this.mdiHammerWrench=rt.tpc,this.mdiMicrosoftVisualStudioCode=rt.yqg,this.fetchLinks()},computed:{isGrid(){return!this.links||this.links.length<=6},links(){return this.$store.state.quicklinks.data}},methods:{fetchLinks(){this.$store.dispatch("fetchQuickLinks")},getIconName(e){if(e)switch(e.type){case"FILE":return this.mdiFileCode;case"EXTERNAL_URL":return this.mdiOpenInNew;case"NAVIGATION":return this.mdiHexagonMultiple;case"TOOL":return e.alias.toLowerCase().includes("bluetooth")?this.mdiBluetooth:e.alias.toLowerCase().includes("memory")||e.alias.toLowerCase().includes("pin")?this.mdiMemory:this.mdiHammerWrench;case"COMPONENT":return this.mdiCog;case"EXTERNAL_IDE":return this.mdiMicrosoftVisualStudioCode;default:return this.mdiFileCode}},onLinkClick(e){switch(e.type){case"FILE":this.$store.dispatch("openEditorQuickLink",e.data);break;case"EXTERNAL_URL":this.$store.dispatch("openExternalUrl",e.data);break;case"NAVIGATION":this.$store.dispatch("changeTab","compLib");break;case"TOOL":this.$store.dispatch("openLaunchable",{launchableId:e.data});break;case"COMPONENT":this.$store.dispatch("configureComponent",e.data);break;case"EXTERNAL_IDE":const t={IDE:"Visual Studio Code",projectPath:e.data};this.$store.dispatch("openExternalIDE",t);break;default:break}}}};var cl=n(6663);const dl=(0,Vt.Z)(rl,[["render",ll]]),pl=dl;Bt()(rl,"components",{QCard:Lt.Z,QCardSection:ln.Z,QIcon:Zt.Z,QScrollArea:cl.Z});const ul={props:{projectInfo:Object,defaultBoard:String},data:()=>({sections:[{id:1,component:(0,i.XI)(Ua),title:"Target and Tool Settings",titleClass:"card-title-block"},{id:2,component:(0,i.XI)(sl),title:"Project Details",titleClass:"card-title-block"},{id:3,component:(0,i.XI)(pl),title:"Quick Links",titleClass:"card-title-block"}]})},ml=(0,Vt.Z)(ul,[["render",Ui],["__scopeId","data-v-5b4ffcea"]]),hl=ml;Bt()(ul,"components",{QCard:Lt.Z});const fl=e=>((0,a.dD)("data-v-f1f77c2a"),e=e(),(0,a.Cn)(),e),gl={class:"q-pa-md q-gutter-sm config-tools-panel"},wl={class:"launchable-column"},bl={class:"row items-center outer-row"},kl=fl((()=>(0,a._)("div",{class:"col-1 card-title-block-bar"},null,-1))),Cl={class:"col"},vl={class:"row items-center card-title-block-content inner-row"},Il={class:"col-auto card-title-block-col ellipsis qa-details-title qa-launchable-details-title"},yl={class:"col card-title-block-col config-button-col"},Sl={class:"launchable-content-row"},_l={key:0},xl=fl((()=>(0,a._)("div",{class:"text-lg"},"Description",-1))),jl={key:0,class:"description text-sm"},Dl={class:"description-code-block"},El={key:1},ql={class:"text-lg"};function Ul(e,t,n,o,s,i){const r=(0,a.up)("q-tooltip"),c=(0,a.up)("q-btn"),d=(0,a.up)("q-card-section"),p=(0,a.up)("q-card");return(0,a.wg)(),(0,a.iD)("div",gl,[(0,a._)("div",wl,[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(e.configurationTools,(t=>((0,a.wg)(),(0,a.iD)("div",{class:"launchable-header-row",key:t.id},[(0,a.Wm)(p,{square:"",bordered:"",class:"launchable-template-header"},{default:(0,a.w5)((()=>[(0,a.Wm)(d,{class:"launchable-template-header card-title-block"},{default:(0,a.w5)((()=>[(0,a._)("div",bl,[kl,(0,a._)("div",Cl,[(0,a._)("div",vl,[(0,a._)("span",Il,[(0,a.Uk)((0,l.zw)(t.label)+" ",1),(0,a.Wm)(r,{"max-width":"250px"},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(t.label),1)])),_:2},1024)]),(0,a._)("span",yl,[(0,a.Wm)(c,{"data-studio-comp":t.id,id:i.makeId("launchable-details-configure-button-",t.id),flat:"",dense:"",align:"center",type:"a","no-caps":"",label:"Open",class:"configure-launchable-button studio-primary-button",icon:e.mdiCogs,onClick:e=>i.openLaunchable(t)},{default:(0,a.w5)((()=>[(0,a.Wm)(r,{"max-width":"250px"},{default:(0,a.w5)((()=>[(0,a.Uk)(" Open "+(0,l.zw)(t.label),1)])),_:2},1024)])),_:2},1032,["data-studio-comp","id","icon","onClick"])])])])])])),_:2},1024)])),_:2},1024),(0,a._)("div",Sl,[(0,a.Wm)(p,{square:"",bordered:"",class:"description-card card-content"},{default:(0,a.w5)((()=>[t.description?((0,a.wg)(),(0,a.iD)("div",_l,[xl,void 0!==t?((0,a.wg)(),(0,a.iD)("div",jl,[(0,a._)("code",Dl,(0,l.zw)(t.description),1)])):(0,a.kq)("",!0)])):(0,a.kq)("",!0),t.description?(0,a.kq)("",!0):((0,a.wg)(),(0,a.iD)("div",El,[(0,a._)("div",ql,(0,l.zw)(t.label),1)]))])),_:2},1024)])])))),128))])])}const Wl={created:function(){this.mdiCogs=rt.pcj,this.fetchConfigTools()},data:function(){return{configurationTools:[]}},computed:{contextChangedCnt(){return this.$store.state.contextChangedCnt}},watch:{contextChangedCnt(e,t){this.fetchConfigTools()}},methods:{fetchConfigTools(){this.$store.dispatch("fetchConfigurationTools").then((e=>{this.configurationTools=e.configtools}))},makeId(e,t){return e+t},openLaunchable(e){let t={launchableId:e.id};this.$store.dispatch("openLaunchable",t)}}},Nl=(0,Vt.Z)(Wl,[["render",Ul],["__scopeId","data-v-f1f77c2a"]]),Tl=Nl;Bt()(Wl,"components",{QCard:Lt.Z,QCardSection:ln.Z,QTooltip:$t.Z,QBtn:Qt.Z,QChip:Ft.Z,QSeparator:mn.Z});const Pl=e=>((0,a.dD)("data-v-4d1edee0"),e=e(),(0,a.Cn)(),e),Vl={key:0,class:"message-scroller"},Ll=Pl((()=>(0,a._)("div",{class:"text-sm"}," Some SDK Extensions can be upgraded to later versions. ",-1))),Ql={key:0,class:"message-scroller",type:"rect"},Ol={key:1},Zl={class:"text-sm"},$l={class:"text-sm"},Fl=(0,a.Uk)("Retrieving Compatible SDKs"),Ml={key:1,class:"message-scroller",type:"rect"},Bl={class:"sdk-upgrade-card"},Rl=(0,a.Uk)("Calculating Upgradeability"),Al={class:"message-scroller"},zl={key:1,id:"editor-warning"},Kl={key:2,class:"text-h6"},Hl=(0,a.Uk)(' "Verify upgrade to selected SDK" '),Gl=(0,a.Uk)(' "Revert to Project SDK" '),Yl=(0,a.Uk)(' "Open SDK Manager" '),Jl={key:5,class:"row no-wrap"},Xl=(0,a.Uk)("I understand the risk");function er(e,t,n,o,s,i){const r=(0,a.up)("q-item-label"),c=(0,a.up)("q-option-group"),d=(0,a.up)("q-item"),p=(0,a.up)("q-list"),u=(0,a.up)("q-skeleton"),m=(0,a.up)("q-step"),h=(0,a.up)("q-item-section"),f=(0,a.up)("q-scroll-area"),g=(0,a.up)("q-icon"),w=(0,a.up)("q-tooltip"),b=(0,a.up)("q-btn"),k=(0,a.up)("q-space"),C=(0,a.up)("q-checkbox"),v=(0,a.up)("q-stepper-navigation"),I=(0,a.up)("q-stepper"),y=(0,a.up)("q-dialog");return(0,a.wg)(),(0,a.j4)(y,{class:"sdk-upgrade-wizard",onHide:i.onDialogHide,ref:"sdkUpgradeWizard",persistent:"","transition-show":"scale","transition-hide":"scale",position:"top"},{default:(0,a.w5)((()=>[(0,a.Wm)(I,{class:"sdk-stepper",modelValue:s.step,"onUpdate:modelValue":[t[4]||(t[4]=e=>s.step=e),i.stepperChanged],ref:"stepper",color:"primary","header-class":"text-bold",animated:""},{navigation:(0,a.w5)((()=>[(0,a.Wm)(v,{class:"row no-wrap q-gutter-sm"},{default:(0,a.w5)((()=>[1==s.step?((0,a.wg)(),(0,a.j4)(b,{key:0,class:"studio-primary-button",disable:!i.enableSdkUpgradeBtn,onClick:t[1]||(t[1]=t=>e.$refs.stepper.next()),label:"Verify"},{default:(0,a.w5)((()=>[(0,a.Wm)(w,null,{default:(0,a.w5)((()=>[Hl])),_:1})])),_:1},8,["disable"])):(0,a.kq)("",!0),1==s.step&&i.showRevert?((0,a.wg)(),(0,a.j4)(b,{key:1,class:"studio-secondary-button",outline:"",disable:!i.enableSdkRevertBtn,onClick:i.handleRevert,label:"Revert"},{default:(0,a.w5)((()=>[(0,a.Wm)(w,null,{default:(0,a.w5)((()=>[Gl])),_:1})])),_:1},8,["disable","onClick"])):(0,a.kq)("",!0),1==s.step?((0,a.wg)(),(0,a.j4)(b,{key:2,class:"studio-secondary-button",outline:"",onClick:i.handleOpenSdkManager,label:"Manage SDKs"},{default:(0,a.w5)((()=>[(0,a.Wm)(w,null,{default:(0,a.w5)((()=>[Yl])),_:1})])),_:1},8,["onClick"])):(0,a.kq)("",!0),s.step>1?((0,a.wg)(),(0,a.j4)(b,{key:3,class:"studio-secondary-button",outline:"",onClick:t[2]||(t[2]=t=>e.$refs.stepper.previous()),label:"Back"})):(0,a.kq)("",!0),s.step>1?((0,a.wg)(),(0,a.j4)(b,{key:4,class:"sdk-upgrade-btn studio-primary-button",dense:"",flat:"",onClick:i.doUpgradeProject,disable:!i.upgradeEnabled,label:"Upgrade"},null,8,["onClick","disable"])):(0,a.kq)("",!0),s.step>1?((0,a.wg)(),(0,a.iD)("div",Jl,[(0,a.Wm)(k),i.hasErrors?((0,a.wg)(),(0,a.j4)(C,{key:0,modelValue:s.riskUnderstood,"onUpdate:modelValue":t[3]||(t[3]=e=>s.riskUnderstood=e)},{default:(0,a.w5)((()=>[Xl])),_:1},8,["modelValue"])):(0,a.kq)("",!0)])):(0,a.kq)("",!0)])),_:1})])),default:(0,a.w5)((()=>[(0,a.Wm)(m,{class:"sdk-step",name:1,title:"Select SDK",prefix:"1",done:s.step>1},{default:(0,a.w5)((()=>[s.isExtensionUpgrade?((0,a.wg)(),(0,a.iD)("div",Vl,[Ll,((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(s.upgradeExtensions,(e=>((0,a.wg)(),(0,a.j4)(p,{padding:"",dense:"",bordered:"",key:e},{default:(0,a.w5)((()=>[(0,a.Wm)(r,{header:""},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(i.selectExtensionMessage(e.recommendedExtension)),1)])),_:2},1024),(0,a.Wm)(d,null,{default:(0,a.w5)((()=>{var t;return[null!==(t=e.availableExtensions)&&void 0!==t&&t.length?((0,a.wg)(),(0,a.j4)(c,{key:1,modelValue:this.selectedExtensions[i.selectedExtensionKey(e)],"onUpdate:modelValue":t=>this.selectedExtensions[i.selectedExtensionKey(e)]=t,options:i.upgradeExtensionsOptions(e.availableExtensions),color:"primary",dense:"",size:"xs"},null,8,["modelValue","onUpdate:modelValue","options"])):((0,a.wg)(),(0,a.iD)("div",Ql," No Compatible Extensions Available. "))]})),_:2},1024)])),_:2},1024)))),128))])):((0,a.wg)(),(0,a.iD)("div",Ol,[(0,a._)("div",Zl,(0,l.zw)(i.selectSdkMessage()),1),(0,a._)("div",$l,[s.sdksInitialized?0==s.upgradeableSdks.length?((0,a.wg)(),(0,a.iD)("div",Ml," No Compatible SDKs Available ")):((0,a.wg)(),(0,a.j4)(c,{key:2,modelValue:s.selectedSdk,"onUpdate:modelValue":t[0]||(t[0]=e=>s.selectedSdk=e),options:i.upgradeableSdksOptions,color:"primary",icon:this.upgradeIcon,dense:"",size:"xs"},null,8,["modelValue","options","icon"])):((0,a.wg)(),(0,a.j4)(u,{key:0,class:"skeleton-message-scroller message-scroller",type:"rect"},{default:(0,a.w5)((()=>[Fl])),_:1}))])]))])),_:1},8,["done"]),(0,a.Wm)(m,{class:"sdk-step",name:2,title:"Verify Upgrade",prefix:"2",done:s.step>2},{default:(0,a.w5)((()=>[(0,a._)("div",Bl,[s.upgradeStepInitialized?((0,a.wg)(),(0,a.j4)(f,{key:1,visible:"",bordered:"",class:"scroll-area"},{default:(0,a.w5)((()=>[(0,a._)("div",Al,[((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(s.statusTypes.filter((e=>s.upgradeResultsMap.has(e.name))),(e=>((0,a.wg)(),(0,a.j4)(p,{bordered:"",padding:"",dense:"",key:e},{default:(0,a.w5)((()=>[(0,a.Wm)(r,{header:""},{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(i.statusDescription(e)),1)])),_:2},1024),((0,a.wg)(!0),(0,a.iD)(a.HY,null,(0,a.Ko)(s.upgradeResultsMap.get(e.name),(e=>((0,a.wg)(),(0,a.j4)(d,{key:e.id},{default:(0,a.w5)((()=>[(0,a.Wm)(h,null,{default:(0,a.w5)((()=>[(0,a.Uk)((0,l.zw)(i.upgradeMessage(e)),1)])),_:2},1024)])),_:2},1024)))),128))])),_:2},1024)))),128))])])),_:1})):((0,a.wg)(),(0,a.j4)(u,{key:0,class:"skeleton-message-scroller message-scroller",type:"rect"},{default:(0,a.w5)((()=>[Rl])),_:1}))]),(0,a._)("div",null,[s.upgradeStepInitialized?(0,a.kq)("",!0):((0,a.wg)(),(0,a.j4)(u,{key:0,type:"text",class:"text-h6"})),s.upgradeStepInitialized?((0,a.wg)(),(0,a.iD)("div",zl," Warning: All open editors for this project will be closed before upgrade ")):(0,a.kq)("",!0),s.upgradeStepInitialized?((0,a.wg)(),(0,a.iD)("div",Kl,[i.upgradeIcon?((0,a.wg)(),(0,a.j4)(g,{key:0,name:i.upgradeIcon},null,8,["name"])):(0,a.kq)("",!0),(0,a.Uk)(" "+(0,l.zw)(this.upgradeLabel),1)])):(0,a.kq)("",!0)])])),_:1},8,["done"])])),_:1},8,["modelValue","onUpdate:modelValue"])])),_:1},8,["onHide"])}const tr={props:{projectInfo:Object},created:function(){this.refreshSdkList()},data(){return{step:1,selectedSdk:"",selectedExtensions:{},allExtensionsMap:{},upgradeableSdks:[],upgradeExtensions:[],sdksInitialized:!1,upgradeResults:[],upgradeResultsMap:{},upgradeContext:{},riskUnderstood:!0,upgradeStepInitialized:!1,isExtensionUpgrade:!1,statusTypes:[{name:"IMPOSSIBLE",description:"NO UPGRADE POSSIBLE"},{name:"USER_VERIFICATION",description:"USER VERIFICATION REQUIRED"},{name:"AUTOMATIC",description:"AUTOMATIC UPGRADE"},{name:"NOTHING",description:"NO ISSUES FOUND"}]}},watch:{upgradeStatusState(e,t){null!==e&&void 0!==e&&e.isUpgradeable?e&&(this.projectInfo.upgradeStatus=e,this.refreshSdkList()):this.onOKClick()}},computed:{upgradeStatusState(){var e,t;return null===(e=this.$store.state)||void 0===e||null===(t=e.projectInfo)||void 0===t?void 0:t.upgradeStatus},upgradeableSdksOptions(){var e,t;const n=this.preferredSdk(this.upgradeableSdks,null===(e=this.projectInfo)||void 0===e||null===(t=e.upgradeStatus)||void 0===t?void 0:t.preferredSdk);return this.upgradeableSdks.map((e=>({label:this.sdkToLabel(e,n==e),value:this.sdkToValue(e)})))},upgradeLabel(){let e="";return this.upgradeStepInitialized&&(e=this.containsErrors()?"Project will not be valid after upgrade":this.verificationRequired()?"Project is upgradeable, but there may be issues":"Project is upgradeable"),e},upgradeIcon(){let e="";return this.upgradeStepInitialized&&(this.containsErrors()||this.verificationRequired())&&(e=rt.O8k),e},upgradeInfos(){return this.upgradeResults},upgradeEnabled(){return this.upgradeStepInitialized&&this.riskUnderstood},hasErrors(){return this.containsErrors()},needsVerifcation(){return this.verificationRequired()},enableSdkButtons(){return!!this.sdksInitialized&&(this.isExtensionUpgrade?null===(e=this.upgradeExtensions)||void 0===e?void 0:e.some((e=>e.availableExtensions.length>0)):this.upgradeableSdks.length>0);var e},enableSdkUpgradeBtn(){return this.enableSdkButtons},enableSdkRevertBtn(){return this.sdksInitialized},latestUserSelectedSdk(){return this.$store.state.latestUserSelectedSdk},showRevert(){if(!this.projectInfo||!("upgradeStatus"in this.projectInfo))return!1;if(this.isExtensionUpgrade){const e=this.projectInfo.upgradeStatus.extensions.every((e=>this.containsSdk(e.recommendedExtension,e.availableExtensions)));if(e)return e;const t=this.projectInfo.upgradeStatus.recommendedSdk,n=this.projectInfo.upgradeStatus.preferredSdk,o=this.projectInfo.upgradeStatus.availableSdks;return t&&n&&!this.sdksEqual(t,n)&&this.containsSdk(t,o)}if("recommendedSdk"in this.projectInfo.upgradeStatus&&"availableSdks"in this.projectInfo.upgradeStatus){const e=this.projectInfo.upgradeStatus.recommendedSdk,t=this.projectInfo.upgradeStatus.availableSdks;return this.containsSdk(e,t)}return!1}},methods:{show(){this.$refs.sdkUpgradeWizard.show()},hide(){this.$refs.sdkUpgradeWizard.hide()},onDialogHide(){this.$refs.sdkUpgradeWizard.hide()},onOKClick(){this.$emit("ok"),this.hide()},onCancelClick(){this.hide()},doUpgradeProject(){this.upgradeContext&&this.$store.dispatch("upgradeProject",this.upgradeContext),this.onOKClick()},statusDescription(e){return e.description},containsErrors(){return this.containsResultType("IMPOSSIBLE")},verificationRequired(){return this.containsResultType("USER_VERIFICATION")},containsResultType(e){return!!this.upgradeStepInitialized&&(!("undefined"==typeof this.upgradeResultsMap||!this.upgradeResultsMap)&&(!!this.upgradeResultsMap.has(e)&&this.upgradeResultsMap.get(e).length>0))},containsSdk(e,t){return t.some((t=>this.sdksEqual(e,t)))},sdksEqual(e,t){return this.sdkToValue(e)==this.sdkToValue(t)},upgradeMessage(e){return e.message},upgradeExtensionsOptions(e){const t=this.preferredSdk(e);return e.map((e=>({label:this.sdkToLabel(e,t==e),value:this.sdkToValue(e)})))},selectSdkMessage(){return this.sdksInitialized?this.projectInfo&&"upgradeStatus"in this.projectInfo&&"recommendedSdk"in this.projectInfo.upgradeStatus?"This project was created with SDK "+this.projectInfo.upgradeStatus.recommendedSdk.sdkLabel+".  You can upgrade your project to one of the following SDKs.":"This project was created with an older SDK.  You can upgrade your project to one of the following SDKs:":""},selectExtensionMessage(e){return e?"This project contains extension "+e.sdkLabel+".  You can upgrade your project to one of the following SDK Extensions.":"This project was created with an older SDK Extension.  You can upgrade your project to one of the following SDK Extensions:"},refreshSdkList(){this.sdksInitialized=!1,this.upgradeableSdks=[],this.$store.dispatch("fetchUpgradeableSdks").then((e=>{var t,n,o,s,i,a,l,r,c;this.upgradeableSdks=e.hasOwnProperty("sdks")?e.sdks:[],this.sdksInitialized=!0;let d=this.preferredSdk(this.upgradeableSdks,null===(t=this.projectInfo)||void 0===t||null===(n=t.upgradeStatus)||void 0===n?void 0:n.preferredSdk);this.selectedSdk=d?this.sdkToValue(d):"",this.isExtensionUpgrade=null===(o=this.projectInfo)||void 0===o||null===(s=o.upgradeStatus)||void 0===s||null===(i=s.extensions)||void 0===i?void 0:i.length,this.upgradeExtensions=null===(a=this.projectInfo)||void 0===a||null===(l=a.upgradeStatus)||void 0===l?void 0:l.extensions,null===(r=this.upgradeExtensions)||void 0===r||r.forEach((e=>{this.selectedExtensions[this.selectedExtensionKey(e)]=this.sdkToValue(e.availableExtensions[0])})),null===(c=this.upgradeExtensions)||void 0===c||c.forEach((e=>{e.availableExtensions.forEach((e=>this.allExtensionsMap[this.sdkToValue(e)]=e))}))}))},selectedExtensionKey(e){return this.sdkToValue(e.recommendedExtension)},preferredSdk(e,t){if(!Array.isArray(e)||!e.length)return null;let n=null;return this.latestUserSelectedSdk&&(n=e.find((e=>this.sdkToValue(e)==this.latestUserSelectedSdk.id))),!n&&t&&(n=e.find((e=>this.sdksEqual(e,t)))),n||(n=e[0]),n},handleRevert(){this.$store.dispatch("revertProjectSdk"),this.onOKClick()},handleOpenSdkManager(){this.$store.dispatch("showManageSdks")},selectedExtensionsMap(){return Object.values(this.selectedExtensions).map((e=>{const t=this.allExtensionsMap[e];return{extensionId:t.sdkId,extensionVersion:t.sdkVersion}}))},sdkToLabel:function(e,t=!1){let n=e.sdkLabel;return t&&(n+=" (preferred)"),n},sdkToValue:function(e){return e?e.sdkId+":"+e.sdkVersion:""},stepperChanged:function(e){1==e?this.upgradeStepInitialized=!1:2==e&&this.checkUpgrade(this.upgradeableSdks.find((e=>this.selectedSdk==this.sdkToValue(e))),this.selectedExtensionsMap())},checkUpgrade:function(e,t){if(!e&&!t)return;const n={sdkId:null===e||void 0===e?void 0:e.sdkId,sdkVersion:null===e||void 0===e?void 0:e.sdkVersion,extensions:t};this.$store.dispatch("checkProjectUpgradeability",n).then((n=>{this.upgradeStepInitialized=!0,this.upgradeResults=n.upgradeResults,this.upgradeContext=n.upgradeContext,this.upgradeContext["sdkId"]=null===e||void 0===e?void 0:e.sdkId,this.upgradeContext["sdkVersion"]=null===e||void 0===e?void 0:e.sdkVersion,this.upgradeContext["extensions"]=t,this.upgradeResultsMap=new Map,this.statusTypes.forEach(((e,t)=>{const n=this.upgradeResults.filter((t=>t.status.name==e.name));n.length>0&&this.upgradeResultsMap.set(e.name,n)})),0==this.upgradeResultsMap.size&&this.upgradeResultsMap.set("NOTHING",[{id:0,message:"The project can be upgraded with no issues.",status:{name:"NOTHING",description:"NO ISSUES FOUND"}}]),this.riskUnderstood=!this.containsErrors()}))}}};var nr=n(8225),or=n(6017),sr=n(3115),ir=n(7133),ar=n(1992);const lr=(0,Vt.Z)(tr,[["render",er],["__scopeId","data-v-4d1edee0"]]),rr=lr;function cr(e,t,n,s,i,r){const c=(0,a.up)("q-spinner"),d=(0,a.up)("q-icon"),p=(0,a.up)("q-btn");return i.isVisible?((0,a.wg)(),(0,a.j4)(o.uT,{key:0,appear:"","enter-active-class":"animated fadeIn","leave-active-class":"animated fadeOut"},{default:(0,a.w5)((()=>[(0,a._)("div",{class:(0,l.C_)(["toast",{toast_error:!n.success,toast_loading:n.loading}])},[n.loading?((0,a.wg)(),(0,a.j4)(c,{key:0})):((0,a.wg)(),(0,a.j4)(d,{key:1,name:r.statusIcon,ref:"status-icon"},null,8,["name"])),(0,a.Uk)((0,l.zw)(n.msg)+" ",1),(0,a.WI)(e.$slots,"default",{},void 0,!0),(0,a.Wm)(p,{flat:"",dense:"",icon:i.closeIcon,id:"button_close_toast",title:"Close Toast",class:"close",onClick:t[0]||(t[0]=e=>r.closeToast())},null,8,["icon"])],2)])),_:3})):(0,a.kq)("",!0)}Bt()(tr,"components",{QDialog:hn.Z,QStepper:nr.Z,QStep:or.Z,QList:wn.Z,QItemLabel:sr.Z,QItem:bn.Z,QOptionGroup:vn.Z,QSkeleton:ir.ZP,QScrollArea:cl.Z,QItemSection:kn.Z,QIcon:Zt.Z,QStepperNavigation:ar.Z,QBtn:Qt.Z,QTooltip:$t.Z,QSpace:un.Z,QCheckbox:vi.Z});const dr={name:"HomeMadeToast",props:["success","loading","msg"],data(){return{closeIcon:Tt.UEB,isVisible:!1}},computed:{statusIcon:function(){return this.success?Tt.YKm:Tt.UEB}},methods:{closeToast(){this.isVisible=!1}},watch:{loading:function(e){this.isVisible=!0,e||setTimeout((()=>{this.isVisible=!1}),3e3)}}};var pr=n(3902);const ur=(0,Vt.Z)(dr,[["render",cr],["__scopeId","data-v-ce28b5aa"]]),mr=ur;Bt()(dr,"components",{QSpinner:pr.Z,QIcon:Zt.Z,QBtn:Qt.Z});var hr=n(9302);const fr={name:"ComponentSelectorApp",created:function(){this.tab="overview";let e=document.documentElement;this.setThemeMode(e),new MutationObserver((t=>{t.forEach((t=>{this.setThemeMode(e)}))})).observe(e,{attributes:!0,attributeFilter:["data-theme"],subtree:!1});let t=this.getSearchParams();null!==hr.process&&void 0!==hr.process&&process.env;let n=new URLSearchParams(t);if(n){if(n.has("tabIndex")){let e=parseInt(n.get("tabIndex"),10);isNaN(e)||e>=0&&e<this.rootMenu.length&&(this.tab=this.rootMenu[e].name)}n.has("searchFor")&&this.openTabAndSetSearch(n.get("searchFor")),n.has("project")&&(this.projectContext=n.get("project"),this.wsUrl="ws://"+window.location.host+"/ws/clic/server/notifications/project/"+this.projectContext,this.initWebSocket(this.wsUrl,this.projectContext))}this.$store.commit("setOrigin",window.location.origin),this.$store.commit("setSearchParams",n),this.$store.dispatch("fetchProjectInfo"),this.$store.dispatch("fetchProjectReportInfo")},computed:{projectInfo(){return this.$store.state.projectInfo},projectReportInfo(){return this.$store.state.projectReportInfo},defaultBoard(){return this.$store.state.projectInfo.defaultBoard},validationErrors(){let e=this.$store.state.latestValidationErrors;return null==e?null:"errors"in e&&(!("errorType"in e)||"EXCLUSIVITY_ERROR"!=e.errorType&&"UNFULFILLED_ERROR"!=e.errorType)?e:null},isUpgradeable(){return"upgradeStatus"in this.$store.state.projectInfo?this.$store.state.projectInfo.upgradeStatus.isUpgradeable:null},tab:{get(){return this.$store.state.tab},set(e){this.$store.dispatch("changeTab",e)}}},watch:{defaultBoard(e,t){this.defaultBoardStr=e},isUpgradeable(e,t){e&&!t&&this.showNotificationDialog()}},components:{ComponentsListVue:Di,ProjectOverviewVue:hl,ConfigToolsVue:Tl,HomeMadeToast:mr},data:()=>({defaultBoardStr:"",rootMenu:[{name:"overview",label:"OVERVIEW",component:(0,i.XI)(hl),tooltip:"Manage target and SDK for this project, read project details and edit project generators"},{name:"compLib",label:"SOFTWARE COMPONENTS",component:(0,i.XI)(Di),tooltip:"Manage software components for this project"},{name:"configTools",label:"CONFIGURATION TOOLS",component:(0,i.XI)(Tl),tooltip:"Launch advanced configurations compatible with this project"}],wsUrl:"",projectContext:"",errorBarMessage:"Validation Errors"}),methods:{getSearchParams(){return window.location.search},initWebSocket(e,t){return new Promise((n=>{this.webSocket=new WebSocket(e),this.webSocket.onopen=e=>{},this.webSocket.onmessage=e=>{if(e.data){let n=JSON.parse(e.data);if("project"in n&&n.project!==t)return;if("msgType"in n)if("updateComponents"===n.msgType)this.updateComponentTree(n);else if("contextChanged"===n.msgType)this.notifyContextChanged(n);else if("setTabIndex"===n.msgType){if("tabIndex"in n){let e=parseInt(n.tabIndex,10);isNaN(e)||e>=0&&e<this.rootMenu.length&&(this.tab=this.rootMenu[e].name)}}else"setSearchString"==n.msgType?"searchFor"in n&&this.openTabAndSetSearch(n.searchFor):"sdksChanged"==n.msgType?this.notifySdksChanged(n):"projectReportInfo"==n.msgType&&this.$store.commit("setProjectReportInfo",n)}},this.webSocket.onerror=e=>{},this.webSocket.onclose=e=>{setTimeout((()=>{this.reconnect()}),5e3)}}))},reconnect(){return new Promise(((e,t)=>{this.webSocket.readyState!==WebSocket.CONNECTING&&this.webSocket.readyState!==WebSocket.OPEN?this.initWebSocket(this.wsUrl,this.projectContext).then((t=>{e(t)})).catch((e=>{t(e)})):e()}))},setThemeMode(e){const t=e.getAttribute("data-theme");"com.silabs.ss.platform.theme.dark"===t?this.$q.dark.set(!0):this.$q.dark.set(!1)},updateComponentTree(e){this.$store.commit("updateSelectedComponents",e),this.$store.commit("setLatestComponentDelta",e)},notifyContextChanged(e){this.$store.commit("notifyContextChanged",e),this.$store.dispatch("fetchProjectInfo")},notifySdksChanged(e){this.$store.commit("notifySdksChanged"),this.$store.dispatch("fetchProjectInfo")},getPageStyle(e,t){return{height:e?`calc(100vh - ${e}px)`:"100vh","overflow-x":"hidden","overflow-y":"hidden"}},openTabAndSetSearch(e){this.tab=this.rootMenu[1].name,this.$store.commit("setGlobalSearchTerm",e)},openTabAndSelectComponent(e){this.tab=this.rootMenu[1].name,this.$store.commit("setGlobalComponentSelection",e)},showValidationErrors(){this.validationErrors&&this.$q.dialog({component:no,parent:this,componentProps:{theComponent:this.validationErrors.component,messages:this.validationErrors.errors,mainMessage:this.validationErrors.message}}).onOk((e=>{e&&this.openTabAndSelectComponent(e)}))},showNotificationDialog(){this.$q.dialog({component:rr,parent:this,componentProps:{persistent:!0,projectInfo:this.projectInfo}}).onOk((()=>{}))},openProjectReport:function(){this.$store.dispatch("openEditor",this.projectReportInfo.path)}}};var gr=n(249),wr=n(6602),br=n(7817),kr=n(900),Cr=n(2133),vr=n(9885),Ir=n(9800),yr=n(4106);const Sr=(0,Vt.Z)(fr,[["render",c],["__scopeId","data-v-46ed2321"]]),_r=Sr;Bt()(fr,"components",{QLayout:gr.Z,QHeader:wr.Z,QCard:Lt.Z,QTabs:br.Z,QTab:kr.Z,QTooltip:$t.Z,QSeparator:mn.Z,QPageContainer:Cr.Z,QPage:vr.Z,QTabPanels:Ir.Z,QTabPanel:yr.Z,QBtn:Qt.Z,QIcon:Zt.Z});var xr=n(6445),jr=n(8026),Dr=n.n(jr);async function Er(e,t){const o=e(_r);o.use(s.Z,t);const a="function"===typeof xr["default"]?await(0,xr["default"])({}):xr["default"],{storeKey:l}=await Promise.resolve().then(n.bind(n,6445)),r=(0,i.Xl)("function"===typeof Dr()?await Dr()({store:a}):Dr());return a.$router=r,{app:o,store:a,storeKey:l,router:r}}var qr=n(7396),Ur=n(4328),Wr=n(4462);const Nr={config:{dark:"auto"},iconSet:qr.Z,plugins:{Notify:Ur.Z,Dialog:Wr.Z}},Tr="";async function Pr({app:e,router:t,store:n,storeKey:o},s){let i=!1;const a=e=>{try{return t.resolve(e).href}catch(n){}return Object(e)===e?null:e},l=e=>{if(i=!0,"string"===typeof e&&/^https?:\/\//.test(e))return void(window.location.href=e);const t=a(e);null!==t&&(window.location.href=t,window.location.reload())},r=window.location.href.replace(window.location.origin,"");for(let d=0;!1===i&&d<s.length;d++)try{await s[d]({app:e,router:t,store:n,ssrContext:null,redirect:l,urlPath:r,publicPath:Tr})}catch(c){return c&&c.url?void l(c.url):void console.error("[Quasar] boot error:",c)}!0!==i&&(e.use(t),e.use(n,o),e.mount("#q-app"))}Er(o.ri,Nr).then((e=>Promise.all([Promise.resolve().then(n.bind(n,57)),Promise.resolve().then(n.bind(n,1569)),Promise.resolve().then(n.bind(n,522))]).then((t=>{const n=t.map((e=>e.default)).filter((e=>"function"===typeof e));Pr(e,n)}))))},1569:(e,t,n)=>{"use strict";n.r(t),n.d(t,{api:()=>a,default:()=>l});var o=n(3340),s=n(9981),i=n.n(s);const a=i().create({baseURL:"/"}),l=(0,o.xr)((({app:e})=>{e.config.globalProperties.$axios=i(),e.config.globalProperties.$api=a}))},57:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r,i18n:()=>l});var o=n(3340),s=n(7712);const i={failed:"Action failed",success:"Action was successful"},a={"en-us":i},l=(0,s.o)({locale:"en-us",fallbackLocale:"en-us",messages:a}),r=(0,o.xr)((({app:e})=>{e.use(l)}))},8026:()=>{},6445:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d});n(6727);var o=n(3100),s=(n(7571),n(9981)),i=n.n(s);const a={"Content-Type":"application/json"},l=(e,t={})=>i().get(e,{params:t}),r=(e,t,n)=>i()({method:"post",url:e,params:n,data:t,headers:a}),c={_getUrlWithProject(e,t,n){let o=e+n;return t&&t.has("project")&&(o=o+"project/"+t.get("project")),o},_createParamsForMap(e){if(!e)return;let t=[];return Object.keys(e).forEach((n=>{t.push(n+"="+e[n])})),t.join("&")},fetchAllComponents(e,t){let n=this._getUrlWithProject(e,t,"/rest/clic/components/all/");return l(n).then((e=>e.data))},fetchSelectedComponents(e,t){let n=this._getUrlWithProject(e,t,"/rest/clic/components/selected/");return l(n).then((e=>e.data))},fetchConfigurationTools(e,t){let n=this._getUrlWithProject(e,t,"/rest/clic/configtools/all/");return l(n).then((e=>e.data))},fetchProjectInfo(e,t){let n=this._getUrlWithProject(e,t,"/rest/clic/projectinfo/");return l(n).then((e=>e.data))},fetchProjectReportInfo(e,t){const n=this._getUrlWithProject(e,t,"/rest/clic/projectreport/");return l(n).then((e=>e.data))},fetchQuickLinks(e,t){let n=this._getUrlWithProject(e,t,"/rest/clic/quicklink/");return l(n).then((e=>e.data)).catch((e=>e.data))},fetchBoards(e,t,n){let o=e+"/rest/studio/boards/all";return n&&"allowNone"in n&&(o=o+"?allowNone="+n.allowNone),l(o).then((e=>e.data))},checkBoardListConsistency(e,t,n){let o=e+"/rest/studio/boards/checkConsistency",s=this._createParamsForMap(n);return s&&s.length>0&&(o=o+"?"+s),l(o).then((e=>e.data))},fetchParts(e,t,n){let o=e+"/rest/studio/parts",s=this._createParamsForMap(n);return s&&s.length>0&&(o=o+"?"+s),l(o).then((e=>e.data))},fetchSdks(e,t,n){let o=e+"/rest/sdk/sdks",s=this._createParamsForMap(n);return s&&s.length>0&&(o=o+"?"+s),l(o).then((e=>e.data))},configureComponent(e,t,n){const o=this._getUrlWithProject(e,t,"/rest/clic/component/configure/");return r(o,n).catch((e=>window.console.error("configureComponent returned ERROR "+e.response.data)))},enableExtension(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/extension/");return o+="/configure",r(o,n).then((e=>e.data))},configureProject(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/projectinfo/");return o+="/configure",r(o,n).then((e=>e.data))},addComponent(e,t,n,o){const s=this._getUrlWithProject(e,t,"/rest/clic/component/add/");return r(s,n)},changeView(e,t,n){const o=this._getUrlWithProject(e,t,"/rest/studio/ui/services/showView");return r(o,n)},removeComponent(e,t,n,o){let s=this._getUrlWithProject(e,t,"/rest/clic/component/remove/");return r(s,n)},changeComponent(e,t,n,o){const s=this._getUrlWithProject(e,t,"/rest/clic/component/change/");return r(s,n)},fetchComponentDetails(e,t,n){const o=this._getUrlWithProject(e,t,"/rest/clic/component/details/");let s={};return"componentId"in n&&(s["componentId"]=n.componentId),"instanceName"in n&&(s["instanceName"]=n.instanceName),l(o,s).then((e=>e.data))},openExternalUrl(e,t,n){const o=e+"/rest/studio/ui/services/openurl",s={url:n};return r(o,s).then((e=>e.data))},openComponentDocumentation(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/component/details/");o+="/opendocs";let s={};return"componentId"in n&&(s["componentId"]=n.componentId),"instanceName"in n&&(s["instanceName"]=n.instanceName),r(o,s).then((e=>e.data)).catch()},openEditor(e,t){const n=e+"/rest/clic/component/openeditor",o={path:t};return r(n,o).then((e=>e.data))},getComponentDocumentation(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/component/details/");o+="/getdocs";let s={};return"componentId"in n&&(s["componentId"]=n.componentId),"instanceName"in n&&(s["instanceName"]=n.instanceName),l(o,s).then((e=>e.data))},fetchComponentDependencies(e,t,n){const o=this._getUrlWithProject(e,t,"/rest/clic/component/depends/"),s={componentId:n};return l(o,s).then((e=>e.data))},fetchComponentInstances(e,t,n){const o=this._getUrlWithProject(e,t,"/rest/clic/component/instances/"),s={componentId:n};return l(o,s).then((e=>e.data))},fetchBoardPhotos(e,t,n){let o=e+"/rest/studio/hardware/photoinfo";const s=this._createParamsForMap(n);return s&&s.length>0&&(o=o+"?"+s),l(o).then((e=>e.data))},showManageSdks(e,t,n){let o=e+"/rest/sdk/sdks/manage";return r(o)},saveSelectedGenerators(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/projectinfo/");o+="/setgenerators";let s={};return n&&(s["generators"]=n.join(",")),r(o,s).then((e=>e.data))},saveDeviceAndSdkSelection(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/projectinfo/");o+="/setprojectinfo";let s={};return n&&("boards"in n&&(s["boards"]=n.boards.map((e=>e.id)).join(",")),"part"in n&&(s["part"]=n.part.id),"sdk"in n&&(s["sdk"]=n.sdk.id),"extensionId"in n&&"extensionVersion"in n&&(s["extensionId"]=n.extensionId,s["extensionVersion"]=n.extensionVersion)),r(o,s).then((e=>e.data))},generateProjectReport(e,t,n){const o=this._getUrlWithProject(e,t,"/rest/clic/projectreport/");return r(o).then((e=>e)).catch((e=>e))},forceGeneration(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/projectinfo/");return o+="/forceGeneration",r(o).then((e=>e.data))},setImportMode(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/projectinfo/");o+="/setimportmode";let s={};return n&&"id"in n&&(s["importModeId"]=n.id),r(o,s).then((e=>e.data))},openLaunchable(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/projectinfo/");return o+="/openLaunchable",r(o,n).then((e=>e.data))},openExternalIDE(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/projectinfo/");return o+="/openExternalIDE",r(o,n).then((e=>e.data))},setProjectName(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/projectinfo/");o+="/rename";let s={};return n&&(s["newName"]=n),r(o,s).then((e=>e.data))},checkProjectUpgradeability(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/projectinfo/");return o+="/checkUpgradeability",r(o,n).then((e=>e.data))},upgradeProject(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/projectinfo/");o+="/upgradeProject";let s={};return n&&(s["fileName"]=n.fileName,s["configFolder"]=n.configFolder,s["newProjectName"]=n.newProjectName,s["sdkId"]=n.sdkId,s["sdkVersion"]=n.sdkVersion),r(o,s).then((e=>e.data))},fetchUpgradeableSdks(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/projectinfo/");return o+="/getUpgradeableSdks",r(o).then((e=>e.data))},revertProjectSdk(e,t,n){let o=this._getUrlWithProject(e,t,"/rest/clic/projectinfo/");return o+="/revertProject",r(o,n).then((e=>e.data))},trackQuickLink(e,t,n){const o=this._getUrlWithProject(e,t,"/rest/clic/quicklink/");return r(o,{link:n}).then((e=>e.data))}},d=(0,o.MT)({state:{origin:null,searchParams:null,allComponents:[],selectedComponents:[],configurationTools:[],projectInfo:{},boards:[],parts:{},sdks:[],latestComponentDelta:{},latestValidationErrors:{},latestUserSelectedSdk:null,contextChangedCnt:0,sdksChangedCnt:0,globalComponentSelection:null,globalSearchTerm:null,quicklinks:{},tab:null,projectReportInfo:{generated:!1,path:"",loading:!1,success:!0,outdated:!1,msg:""}},getters:{getOrigin(e){return e.origin},getSearchParams(e){return e.searchParams},projectReportInfo(e){return e.projectReportInfo}},mutations:{setOrigin(e,t){e.origin=t},setSearchParams(e,t){e.searchParams=t},setAllComponents(e,t){e.allComponents=t},setSelectedComponents(e,t){e.selectedComponents=t},setConfigurationTools(e,t){e.configurationTools=t},setProjectInfo(e,t){e.projectInfo=t},setQuickLinks(e,t){e.quicklinks=t},setBoards(e,t){e.boards=t},setParts(e,t){e.parts=t},setSdks(e,t){e.sdks=t},updateSelectedComponents(e,t){"tree"in t&&(e.selectedComponents=JSON.parse(t.tree))},setLatestComponentDelta(e,t){if("delta"in t){let n=t.delta;"string"===typeof t.delta&&(n=JSON.parse(t.delta)),e.latestComponentDelta=n}},setLatestValidationErrors(e,t){e.latestValidationErrors=t},saveSelectedGenerators(e,t){return c.saveSelectedGenerators(e.origin,e.searchParams,t).then((n=>(e.projectInfo=n,t.includes("visual-studio-code")&&this.dispatch("fetchQuickLinks"),n)))},saveDeviceAndSdkSelection(e,t){return"sdk"in t&&(e.latestUserSelectedSdk=t.sdk),c.saveDeviceAndSdkSelection(e.origin,e.searchParams,t).then((t=>(e.projectInfo=t,t)))},notifyContextChanged(e,t){e.contextChangedCnt++},notifySdksChanged(e,t){e.sdksChangedCnt++},setImportMode(e,t){return c.setImportMode(e.origin,e.searchParams,t).then((t=>(e.projectInfo=t,t)))},setGlobalComponentSelection(e,t){e.globalComponentSelection=t},setGlobalSearchTerm(e,t){e.globalSearchTerm=t},setProjectName(e,t){return c.setProjectName(e.origin,e.searchParams,t).then((t=>(e.projectInfo=t,t)))},setTab(e,t){e.tab=t},setProjectReportInfo(e,t){"generated"in t&&(e.projectReportInfo.generated=t.generated),"path"in t&&(e.projectReportInfo.path=t.path),"loading"in t&&(e.projectReportInfo.loading=t.loading),"success"in t&&(e.projectReportInfo.success=t.success),"outdated"in t&&(e.projectReportInfo.outdated=t.outdated),"msg"in t&&(e.projectReportInfo.msg=t.msg)}},actions:{fetchAllComponents(e){return c.fetchAllComponents(e.state.origin,e.state.searchParams).then((t=>(e.commit("setAllComponents",t),t)))},fetchConfigurationTools(e){return c.fetchConfigurationTools(e.state.origin,e.state.searchParams).then((t=>(e.commit("setConfigurationTools",t),t)))},fetchSelectedComponents(e){return c.fetchSelectedComponents(e.state.origin,e.state.searchParams).then((t=>(e.commit("setSelectedComponents",t),t)))},fetchLatestComponentDelta(e){return e.state.latestComponentDelta},fetchLatestValidationErrors(e){return e.state.latestValidationErrors},fetchProjectInfo(e){return c.fetchProjectInfo(e.state.origin,e.state.searchParams).then((t=>(e.commit("setProjectInfo",t),t)))},fetchProjectReportInfo(e){return c.fetchProjectReportInfo(e.state.origin,e.state.searchParams).then((t=>(e.commit("setProjectReportInfo",t),t)))},fetchQuickLinks(e){return c.fetchQuickLinks(e.state.origin,e.state.searchParams).then((t=>(e.commit("setQuickLinks",t),t)))},fetchBoards(e,t){return c.fetchBoards(e.state.origin,e.state.searchParams,t).then((t=>(e.commit("setBoards",t),t)))},checkBoardListConsistency(e,t){return c.checkBoardListConsistency(e.state.origin,e.state.searchParams,t)},fetchParts(e,t){return c.fetchParts(e.state.origin,e.state.searchParams,t).then((t=>(e.commit("setParts",t),t)))},fetchSdks(e,t){return c.fetchSdks(e.state.origin,e.state.searchParams,t).then((t=>(e.commit("setSdks",t),t)))},configureComponent(e,t){return c.configureComponent(e.state.origin,e.state.searchParams,t)},configureProject(e,t){return c.configureProject(e.state.origin,e.state.searchParams,t)},checkProjectUpgradeability(e,t){return c.checkProjectUpgradeability(e.state.origin,e.state.searchParams,t)},changeView(e,t){return c.changeView(e.state.origin,e.state.searchParams,t)},enableExtension(e,t){return c.enableExtension(e.state.origin,e.state.searchParams,t)},upgradeProject(e,t){return c.upgradeProject(e.state.origin,e.state.searchParams,t).then((t=>(e.commit("setProjectInfo",t),t)))},addComponent(e,t){return c.addComponent(e.state.origin,e.state.searchParams,t,this).then(e.commit("setLatestValidationErrors",null)).catch((t=>{e.commit("setLatestValidationErrors",t.response.data)}))},removeComponent(e,t){return c.removeComponent(e.state.origin,e.state.searchParams,t,this).then(e.commit("setLatestValidationErrors",null)).catch((t=>{e.commit("setLatestValidationErrors",t.response.data)}))},changeComponent(e,t){return c.changeComponent(e.state.origin,e.state.searchParams,t,this).then(e.commit("setLatestValidationErrors",null)).catch((t=>{e.commit("setLatestValidationErrors",t.response.data)}))},fetchComponentDetails(e,t){return c.fetchComponentDetails(e.state.origin,e.state.searchParams,t)},openExternalUrl(e,t){if(e.state.quicklinks.data){const n=e.state.quicklinks.data.find((e=>e.data===t));e.dispatch("trackQuickLink",n)}return c.openExternalUrl(e.state.origin,e.state.searchParams,t)},openComponentDocumentation(e,t){return c.openComponentDocumentation(e.state.origin,e.state.searchParams,t)},openEditor(e,t){return c.openEditor(e.state.origin,t)},openEditorQuickLink(e,t){if(e.state.quicklinks.data){const n=e.state.quicklinks.data.find((e=>e.data===t));e.dispatch("trackQuickLink",n)}return c.openEditor(e.state.origin,t)},getComponentDocumentation(e,t){return c.getComponentDocumentation(e.state.origin,e.state.searchParams,t)},fetchComponentDependencies(e,t){return c.fetchComponentDependencies(e.state.origin,e.state.searchParams,t)},fetchComponentInstances(e,t){return c.fetchComponentInstances(e.state.origin,e.state.searchParams,t)},fetchBoardPhotos(e,t){return c.fetchBoardPhotos(e.state.origin,e.state.searchParams,t)},showManageSdks(e,t){return c.showManageSdks(e.state.origin,e.state.searchParams,t)},generateProjectReport(e){return c.generateProjectReport(e.state.origin,e.state.searchParams).then((e=>e))},forceGeneration(e,t){return c.forceGeneration(e.state.origin,e.state.searchParams,t)},openLaunchable(e,t){return c.openLaunchable(e.state.origin,e.state.searchParams,t)},openExternalIDE(e,t){return c.openExternalIDE(e.state.origin,e.state.searchParams,t)},fetchUpgradeableSdks(e,t){return c.fetchUpgradeableSdks(e.state.origin,e.state.searchParams,t)},revertProjectSdk(e,t){return c.revertProjectSdk(e.state.origin,e.state.searchParams,t)},trackQuickLink(e,t){return c.trackQuickLink(e.state.origin,e.state.searchParams,t).then(e.dispatch("fetchQuickLinks"))},changeTab(e,t){if(e.commit("setTab",t),"compLib"===t&&e.state.quicklinks.data){const t=e.state.quicklinks.data.find((e=>"softwarecomponents"===e.data));e.dispatch("trackQuickLink",t)}}},modules:{}})},6608:()=>{}},t={};function n(o){var s=t[o];if(void 0!==s)return s.exports;var i=t[o]={exports:{}};return e[o](i,i.exports,n),i.exports}n.m=e,(()=>{var e=[];n.O=(t,o,s,i)=>{if(!o){var a=1/0;for(d=0;d<e.length;d++){for(var[o,s,i]=e[d],l=!0,r=0;r<o.length;r++)(!1&i||a>=i)&&Object.keys(n.O).every((e=>n.O[e](o[r])))?o.splice(r--,1):(l=!1,i<a&&(a=i));if(l){e.splice(d--,1);var c=s();void 0!==c&&(t=c)}}return t}i=i||0;for(var d=e.length;d>0&&e[d-1][2]>i;d--)e[d]=e[d-1];e[d]=[o,s,i]}})(),(()=>{n.n=e=>{var t=e&&e.__esModule?()=>e["default"]:()=>e;return n.d(t,{a:t}),t}})(),(()=>{n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})}})(),(()=>{n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()})(),(()=>{n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})(),(()=>{n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}})(),(()=>{var e={143:0};n.O.j=t=>0===e[t];var t=(t,o)=>{var s,i,[a,l,r]=o,c=0;if(a.some((t=>0!==e[t]))){for(s in l)n.o(l,s)&&(n.m[s]=l[s]);if(r)var d=r(n)}for(t&&t(o);c<a.length;c++)i=a[c],n.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return n.O(d)},o=globalThis["webpackChunkuc_component_selector"]=globalThis["webpackChunkuc_component_selector"]||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var o=n.O(void 0,[736],(()=>n(7434)));o=n.O(o)})();