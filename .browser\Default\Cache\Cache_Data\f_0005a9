{"availableCount": 44, "searchTerms": [], "userState": "239a0aec-4e0b-4e2a-b9c0-ee1c8e138eea", "resources": [{"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/F:/data/sdk/app/bluetooth/example/bt_soc_cbap/readme.md"], "description": "Demonstrates Certificate Based Authentication and Pairing over BLE. This example utilizes TrustZone for storing keys.", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.12.2._1595114177.bt_soc_cbap_tz_ns.example/bt_soc_cbap/bt_soc_cbap_tz_ns.slcp", "text": "Bluetooth - SoC Certificate Based Authentication and Pairing (TrustZone)\n", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates Certificate Based Authentication and Pairing over BLE. This example utilizes TrustZone for storing keys.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk20240602/app/bluetooth/example/bt_soc_cbap/readme.md"], "description": "Demonstrates Certificate Based Authentication and Pairing over BLE. This example utilizes TrustZone for storing keys.", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.6.2._325272481.bt_soc_cbap_tz_ns.example/bt_soc_cbap/bt_soc_cbap_tz_ns.slcp", "text": "Bluetooth - SoC Certificate Based Authentication and Pairing (TrustZone)\n", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates Certificate Based Authentication and Pairing over BLE. This example utilizes TrustZone for storing keys.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.31/app/bluetooth/example/bt_soc_cbap/readme.md"], "description": "Demonstrates Certificate Based Authentication and Pairing over BLE. This example utilizes TrustZone for storing keys.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.2._-85640047.bt_soc_cbap_tz_ns.example/bt_soc_cbap/bt_soc_cbap_tz_ns.slcp", "text": "Bluetooth - SoC Certificate Based Authentication and Pairing (TrustZone)\n", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates Certificate Based Authentication and Pairing over BLE. This example utilizes TrustZone for storing keys.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_cbap/readme.md"], "description": "Demonstrates Certificate Based Authentication and Pairing over BLE. This example utilizes TrustZone for storing keys.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_soc_cbap_tz_ns.example/bt_soc_cbap/bt_soc_cbap_tz_ns.slcp", "text": "Bluetooth - SoC Certificate Based Authentication and Pairing (TrustZone)\n", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Demonstrates Certificate Based Authentication and Pairing over BLE. This example utilizes TrustZone for storing keys.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/F:/data/sdk/app/bluetooth/example/bt_soc_csr_generator/readme.md"], "description": "Certificate generating firmware example. Software is generating the device EC key pair, the signing request for the device certificate, and other related data. The generated data can be read out by the Central Authority. This example utilizes TrustZone for storing keys.", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.12.2._1595114177.bt_soc_csr_generator_tz_ns.example/bt_soc_csr_generator/bt_soc_csr_generator_tz_ns.slcp", "text": "Bluetooth - SoC Certificate Signing Request Generator (TrustZone)\n", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Certificate generating firmware example. Software is generating the device EC key pair, the signing request for the device certificate, and other related data. The generated data can be read out by the Central Authority. This example utilizes TrustZone for storing keys.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk20240602/app/bluetooth/example/bt_soc_csr_generator/readme.md"], "description": "Certificate generating firmware example. Software is generating the device EC key pair, the signing request for the device certificate, and other related data. The generated data can be read out by the Central Authority. This example utilizes TrustZone for storing keys.", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.6.2._325272481.bt_soc_csr_generator_tz_ns.example/bt_soc_csr_generator/bt_soc_csr_generator_tz_ns.slcp", "text": "Bluetooth - SoC Certificate Signing Request Generator (TrustZone)\n", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Certificate generating firmware example. Software is generating the device EC key pair, the signing request for the device certificate, and other related data. The generated data can be read out by the Central Authority. This example utilizes TrustZone for storing keys.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.31/app/bluetooth/example/bt_soc_csr_generator/readme.md"], "description": "Certificate generating firmware example. Software is generating the device EC key pair, the signing request for the device certificate, and other related data. The generated data can be read out by the Central Authority. This example utilizes TrustZone for storing keys.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.2._-85640047.bt_soc_csr_generator_tz_ns.example/bt_soc_csr_generator/bt_soc_csr_generator_tz_ns.slcp", "text": "Bluetooth - SoC Certificate Signing Request Generator (TrustZone)\n", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Certificate generating firmware example. Software is generating the device EC key pair, the signing request for the device certificate, and other related data. The generated data can be read out by the Central Authority. This example utilizes TrustZone for storing keys.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/bluetooth/example/bt_soc_csr_generator/readme.md"], "description": "Certificate generating firmware example. Software is generating the device EC key pair, the signing request for the device certificate, and other related data. The generated data can be read out by the Central Authority. This example utilizes TrustZone for storing keys.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_soc_csr_generator_tz_ns.example/bt_soc_csr_generator/bt_soc_csr_generator_tz_ns.slcp", "text": "Bluetooth - SoC Certificate Signing Request Generator (TrustZone)\n", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "Certificate generating firmware example. Software is generating the device EC key pair, the signing request for the device certificate, and other related data. The generated data can be read out by the Central Authority. This example utilizes TrustZone for storing keys.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/D:/2025BLE/gecko-sdk4.2.1/platform/bootloader/sample-apps/workspaces/bootloader-uart-bgapi/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.2.1._-1555327524.bootloader-uart-bgapi-nonsecure.sample-apps/workspaces/bootloader-uart-bgapi/bootloader-uart-bgapi-nonsecure.slcp", "text": "Bootloader - NCP BGAPI UART DFU Non-Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/D:/2025BLE/gecko-sdk4.2.1/platform/bootloader/sample-apps/workspaces/bootloader-uart-bgapi/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.2.1._-1555327524.bootloader-uart-bgapi-secure.sample-apps/workspaces/bootloader-uart-bgapi/bootloader-uart-bgapi-secure.slcp", "text": "Bootloader - NCP BGAPI UART DFU Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/D:/2025BLE/gecko-sdk4.2.1/platform/bootloader/sample-apps/workspaces/bootloader-spi-ezsp/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.2.1._-1555327524.bootloader-spi-ezsp-nonsecure.sample-apps/workspaces/bootloader-spi-ezsp/bootloader-spi-ezsp-nonsecure.slcp", "text": "Bootloader - NCP EZSP SPI Non-Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/D:/2025BLE/gecko-sdk4.2.1/platform/bootloader/sample-apps/workspaces/bootloader-spi-ezsp/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.2.1._-1555327524.bootloader-spi-ezsp-secure.sample-apps/workspaces/bootloader-spi-ezsp/bootloader-spi-ezsp-secure.slcp", "text": "Bootloader - NCP EZSP SPI Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/D:/2025BLE/gecko-sdk4.2.1/platform/bootloader/sample-apps/workspaces/bootloader-uart-xmodem/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.2.1._-1555327524.bootloader-uart-xmodem-nonsecure.sample-apps/workspaces/bootloader-uart-xmodem/bootloader-uart-xmodem-nonsecure.slcp", "text": "Bootloader - NCP UART XMODEM Non-Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/D:/2025BLE/gecko-sdk4.2.1/platform/bootloader/sample-apps/workspaces/bootloader-uart-xmodem/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.2.1._-1555327524.bootloader-uart-xmodem-secure.sample-apps/workspaces/bootloader-uart-xmodem/bootloader-uart-xmodem-secure.slcp", "text": "Bootloader - NCP UART XMODEM Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/D:/2025BLE/gecko-sdk4.2.1/platform/bootloader/sample-apps/workspaces/bootloader-apploader/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.2.1._-1555327524.bootloader-apploader-nonsecure.sample-apps/workspaces/bootloader-apploader/bootloader-apploader-nonsecure.slcp", "text": "Bootloader - SoC Bluetooth AppLoader OTA DFU Non-Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the non-secure part of the bootloader. The secure part of the bootloader, which contains the core functionalities needs to be built separately and used together with the non-secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/D:/2025BLE/gecko-sdk4.2.1/platform/bootloader/sample-apps/workspaces/bootloader-apploader/readme.md"], "description": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.", "id": "template.uc.com.silabs.sdk.stack.super:4.2.1._-1555327524.bootloader-apploader-secure.sample-apps/workspaces/bootloader-apploader/bootloader-apploader-secure.slcp", "text": "Bootloader - SoC Bluetooth AppLoader OTA DFU Secure part of Bootloader using TrustZone", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "TrustZone is used to split into a Secure and Non-Secure bootloader. This is the secure part of the bootloader. The non-secure part of the bootloader, which contains the communication interfaces needs to be built separately and used together with the secure part of the bootloader.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/D:/2025BLE/gecko-sdk4.2.1/platform/bootloader/sample-apps/workspaces/Series-1/bootloader-storage-internal-single/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x84000 (or 0x8084000 for device with 0x8000000 flash base), and have a size of 448 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.2.1._-1555327524.bootloader-storage-internal-single.sample-apps/workspaces/Series-1/bootloader-storage-internal-single/bootloader-storage-internal-single.slcp", "text": "Bootloader - SoC Internal Storage (single image on 1MB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x84000 (or 0x8084000 for device with 0x8000000 flash base), and have a size of 448 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/D:/2025BLE/gecko-sdk4.2.1/platform/bootloader/sample-apps/workspaces/Series-2/bootloader-storage-internal-single/readme.md"], "description": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x84000 (or 0x8084000 for device with 0x8000000 flash base), and have a size of 448 kB. This can be configured on the \"Storage\" tab.", "id": "template.uc.com.silabs.sdk.stack.super:4.2.1._-1555327524.bootloader-storage-internal-single.sample-apps/workspaces/Series-2/bootloader-storage-internal-single/bootloader-storage-internal-single.slcp", "text": "Bootloader - SoC Internal Storage (single image on 1MB device)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This sample configuration of the Gecko bootloader configures the bootloader to use the internal main flash to store firmware update images. The storage configuration is set up to store a single firmware update image at a time, in a single storage slot. The storage slot is configured to start at address 0x84000 (or 0x8084000 for device with 0x8000000 flash base), and have a size of 448 kB. This can be configured on the \"Storage\" tab.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.31/protocol/openthread/sample-apps/ot-cli/README.md", "file:/E:/software/gecko-sdk4.31/protocol/openthread/sample-apps/ot-cli/trustzone/README.md"], "description": "This is a variant of the openthread FTD CLI application with trustzone awareness enabled.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.2._-85640047.ot-cli-ftd-tz-ns.sample-apps/ot-cli/trustzone/ot-cli-ftd-tz-ns.slcp", "text": "OpenThread - SoC CLI (FTD) (TrustZone)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a variant of the openthread FTD CLI application with trustzone awareness enabled.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-cli/README.md", "file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-cli/trustzone/README.md"], "description": "This is a variant of the openthread FTD CLI application with trustzone awareness enabled.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.ot-cli-ftd-tz-ns.sample-apps/ot-cli/trustzone/ot-cli-ftd-tz-ns.slcp", "text": "OpenThread - SoC CLI (FTD) (TrustZone)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a variant of the openthread FTD CLI application with trustzone awareness enabled.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.31/protocol/openthread/sample-apps/ot-cli/README.md", "file:/E:/software/gecko-sdk4.31/protocol/openthread/sample-apps/ot-cli/trustzone/README.md"], "description": "This is a variant of the openthread MTD CLI application with trustzone awareness enabled.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.2._-85640047.ot-cli-mtd-tz-ns.sample-apps/ot-cli/trustzone/ot-cli-mtd-tz-ns.slcp", "text": "OpenThread - SoC CLI (MTD) (TrustZone)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a variant of the openthread MTD CLI application with trustzone awareness enabled.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-cli/README.md", "file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-cli/trustzone/README.md"], "description": "This is a variant of the openthread MTD CLI application with trustzone awareness enabled.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.ot-cli-mtd-tz-ns.sample-apps/ot-cli/trustzone/ot-cli-mtd-tz-ns.slcp", "text": "OpenThread - SoC CLI (MTD) (TrustZone)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a variant of the openthread MTD CLI application with trustzone awareness enabled.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/F:/data/sdk/app/common/example/tz_psa_attestation/tz_psa_attestation_ns/readme.md"], "description": "The Non-secure part of the application demonstrates how to generate PSA attestation token and print them in a human-readable format. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_attestation_ws).", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.12.2._1595114177.tz_psa_attestation_ns.example/tz_psa_attestation/tz_psa_attestation_ns/tz_psa_attestation_ns.slcp", "text": "Platform Security - SoC TrustZone PSA Attestation (Non-secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Non-secure part of the application demonstrates how to generate PSA attestation token and print them in a human-readable format. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_attestation_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk20240602/app/common/example/tz_psa_attestation/tz_psa_attestation_ns/readme.md"], "description": "The Non-secure part of the application demonstrates how to generate PSA attestation token and print them in a human-readable format. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_attestation_ws).", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.6.2._325272481.tz_psa_attestation_ns.example/tz_psa_attestation/tz_psa_attestation_ns/tz_psa_attestation_ns.slcp", "text": "Platform Security - SoC TrustZone PSA Attestation (Non-secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Non-secure part of the application demonstrates how to generate PSA attestation token and print them in a human-readable format. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_attestation_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.31/app/common/example/tz_psa_attestation/tz_psa_attestation_ns/readme.md"], "description": "The Non-secure part of the application demonstrates how to generate PSA attestation token and print them in a human-readable format. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_attestation_ws).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.2._-85640047.tz_psa_attestation_ns.example/tz_psa_attestation/tz_psa_attestation_ns/tz_psa_attestation_ns.slcp", "text": "Platform Security - SoC TrustZone PSA Attestation (Non-secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Non-secure part of the application demonstrates how to generate PSA attestation token and print them in a human-readable format. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_attestation_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/tz_psa_attestation/tz_psa_attestation_ns/readme.md"], "description": "The Non-secure part of the application demonstrates how to generate PSA attestation token and print them in a human-readable format. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_attestation_ws).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.tz_psa_attestation_ns.example/tz_psa_attestation/tz_psa_attestation_ns/tz_psa_attestation_ns.slcp", "text": "Platform Security - SoC TrustZone PSA Attestation (Non-secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Non-secure part of the application demonstrates how to generate PSA attestation token and print them in a human-readable format. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_attestation_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/F:/data/sdk/app/common/example/tz_psa_attestation/readme.md"], "description": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_attestation_ws).", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.12.2._1595114177.tz_psa_attestation_s.example/tz_psa_attestation/tz_psa_attestation_s.slcp", "text": "Platform Security - SoC TrustZone PSA Attestation (Secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_attestation_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk20240602/app/common/example/tz_psa_attestation/readme.md"], "description": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_attestation_ws).", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.6.2._325272481.tz_psa_attestation_s.example/tz_psa_attestation/tz_psa_attestation_s.slcp", "text": "Platform Security - SoC TrustZone PSA Attestation (Secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_attestation_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.31/app/common/example/tz_psa_attestation/readme.md"], "description": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_attestation_ws).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.2._-85640047.tz_psa_attestation_s.example/tz_psa_attestation/tz_psa_attestation_s.slcp", "text": "Platform Security - SoC TrustZone PSA Attestation (Secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_attestation_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/tz_psa_attestation/readme.md"], "description": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_attestation_ws).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.tz_psa_attestation_s.example/tz_psa_attestation/tz_psa_attestation_s.slcp", "text": "Platform Security - SoC TrustZone PSA Attestation (Secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_attestation_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/F:/data/sdk/app/common/example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_ns/readme.md"], "description": "The Non-secure part of the application demonstrates how to use the ECDH key agreement API. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_crypto_ecdh_ws).", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.12.2._1595114177.tz_psa_crypto_ecdh_ns.example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_ns/tz_psa_crypto_ecdh_ns.slcp", "text": "Platform Security - SoC TrustZone PSA Crypto ECDH (Non-secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Non-secure part of the application demonstrates how to use the ECDH key agreement API. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_crypto_ecdh_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk20240602/app/common/example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_ns/readme.md"], "description": "The Non-secure part of the application demonstrates how to use the ECDH key agreement API. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_crypto_ecdh_ws).", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.6.2._325272481.tz_psa_crypto_ecdh_ns.example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_ns/tz_psa_crypto_ecdh_ns.slcp", "text": "Platform Security - SoC TrustZone PSA Crypto ECDH (Non-secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Non-secure part of the application demonstrates how to use the ECDH key agreement API. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_crypto_ecdh_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.31/app/common/example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_ns/readme.md"], "description": "The Non-secure part of the application demonstrates how to use the ECDH key agreement API. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_crypto_ecdh_ws).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.2._-85640047.tz_psa_crypto_ecdh_ns.example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_ns/tz_psa_crypto_ecdh_ns.slcp", "text": "Platform Security - SoC TrustZone PSA Crypto ECDH (Non-secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Non-secure part of the application demonstrates how to use the ECDH key agreement API. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_crypto_ecdh_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_ns/readme.md"], "description": "The Non-secure part of the application demonstrates how to use the ECDH key agreement API. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_crypto_ecdh_ws).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.tz_psa_crypto_ecdh_ns.example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_ns/tz_psa_crypto_ecdh_ns.slcp", "text": "Platform Security - SoC TrustZone PSA Crypto ECDH (Non-secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Non-secure part of the application demonstrates how to use the ECDH key agreement API. The Non-secure application needs to build with the Secure application on a workspace (tz_psa_crypto_ecdh_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/F:/data/sdk/app/common/example/tz_psa_crypto_ecdh/readme.md"], "description": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_crypto_ecdh_ws).", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.12.2._1595114177.tz_psa_crypto_ecdh_s.example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_s.slcp", "text": "Platform Security - SoC TrustZone PSA Crypto ECDH (Secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_crypto_ecdh_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk20240602/app/common/example/tz_psa_crypto_ecdh/readme.md"], "description": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_crypto_ecdh_ws).", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.6.2._325272481.tz_psa_crypto_ecdh_s.example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_s.slcp", "text": "Platform Security - SoC TrustZone PSA Crypto ECDH (Secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_crypto_ecdh_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.31/app/common/example/tz_psa_crypto_ecdh/readme.md"], "description": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_crypto_ecdh_ws).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.2._-85640047.tz_psa_crypto_ecdh_s.example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_s.slcp", "text": "Platform Security - SoC TrustZone PSA Crypto ECDH (Secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_crypto_ecdh_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/app/common/example/tz_psa_crypto_ecdh/readme.md"], "description": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_crypto_ecdh_ws).", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.tz_psa_crypto_ecdh_s.example/tz_psa_crypto_ecdh/tz_psa_crypto_ecdh_s.slcp", "text": "Platform Security - SoC TrustZone PSA Crypto ECDH (Secure application)", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "The Secure part of the application provides the PSA Crypto and Attestation functionalities in the Secure world. The Secure application needs to build with the Non-secure application on a workspace (tz_psa_crypto_ecdh_ws).\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": [], "description": "This project can be used as a reference implementation for creating secure applications with TrustZone for bluetooth products. This project makes a TrustZone secure library for running PSA libraries in the secure world.", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.12.2._1595114177.bt_soc_tz_secure_application.example/bt_soc_tz_secure_application/bt_soc_tz_secure_application.slcp", "text": "Sample project for TrustZone Secure Key Library", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project can be used as a reference implementation for creating secure applications with TrustZone for bluetooth products.\nThis project makes a TrustZone secure library for running PSA libraries in the secure world."}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": [], "description": "This project can be used as a reference implementation for creating secure applications with TrustZone for bluetooth products. This project makes a TrustZone secure library for running PSA libraries in the secure world.", "id": "template.uc.com.silabs.sdk.stack.sisdk:2024.6.2._325272481.bt_soc_tz_secure_application.example/bt_soc_tz_secure_application/bt_soc_tz_secure_application.slcp", "text": "Sample project for TrustZone Secure Key Library", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project can be used as a reference implementation for creating secure applications with TrustZone for bluetooth products.\nThis project makes a TrustZone secure library for running PSA libraries in the secure world."}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": [], "description": "This project can be used as a reference implementation for creating secure applications with TrustZone for bluetooth products. This project makes a TrustZone secure library for running PSA libraries in the secure world.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.2._-85640047.bt_soc_tz_secure_application.example/bt_soc_tz_secure_application/bt_soc_tz_secure_application.slcp", "text": "Sample project for TrustZone Secure Key Library", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project can be used as a reference implementation for creating secure applications with TrustZone for bluetooth products.\nThis project makes a TrustZone secure library for running PSA libraries in the secure world."}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.31/protocol/openthread/sample-apps/ot-tz-secure-app/README.md"], "description": "This is a trustzone enabled application used to manage security operations from trustzone aware OpenThread applications.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.2._-85640047.ot-tz-secure-application.sample-apps/ot-tz-secure-app/ot-tz-secure-application.slcp", "text": "Sample project for TrustZone Secure Key Library", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a trustzone enabled application used to manage security operations from trustzone aware OpenThread applications.\n"}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": [], "description": "This project can be used as a reference implementation for creating secure applications with TrustZone for bluetooth products. This project makes a TrustZone secure library for running PSA libraries in the secure world.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.bt_soc_tz_secure_application.example/bt_soc_tz_secure_application/bt_soc_tz_secure_application.slcp", "text": "Sample project for TrustZone Secure Key Library", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This project can be used as a reference implementation for creating secure applications with TrustZone for bluetooth products.\nThis project makes a TrustZone secure library for running PSA libraries in the secure world."}, {"imageURL": "bundleentry://460.fwk308980712/icons/projectNew.svg", "supportDocuments": ["file:/E:/software/gecko-sdk4.44/protocol/openthread/sample-apps/ot-tz-secure-app/README.md"], "description": "This is a trustzone enabled application used to manage security operations from trustzone aware OpenThread applications.", "id": "template.uc.com.silabs.sdk.stack.super:4.4.4._-85640013.ot-tz-secure-application.sample-apps/ot-tz-secure-app/ot-tz-secure-application.slcp", "text": "Sample project for TrustZone Secure Key Library", "priority": 9999999, "category": "SOFTWARE", "toolTipText": "This is a trustzone enabled application used to manage security operations from trustzone aware OpenThread applications.\n"}], "filters": [{"futureCount": 0, "anySelected": false, "id": 0, "filters": [{"futureCount": 12, "anySelected": false, "id": 0, "filters": [], "title": "Bluetooth", "parentId": 0, "selected": false}, {"futureCount": 6, "anySelected": false, "id": 1, "filters": [], "title": "<PERSON><PERSON><PERSON>", "parentId": 0, "selected": false}], "title": "Wireless Technology", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 1, "filters": [{"futureCount": 6, "anySelected": false, "id": 0, "filters": [], "title": "NCP", "parentId": 1, "selected": false}, {"futureCount": 38, "anySelected": false, "id": 1, "filters": [], "title": "SoC", "parentId": 1, "selected": false}], "title": "Device Type", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 2, "filters": [{"futureCount": 16, "anySelected": false, "id": 0, "filters": [], "title": "32-bit MCU", "parentId": 2, "selected": false}, {"futureCount": 10, "anySelected": false, "id": 1, "filters": [], "title": "Bootloader", "parentId": 2, "selected": false}], "title": "MCU", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 3, "filters": [{"futureCount": 44, "anySelected": false, "id": 0, "filters": [], "title": "Advanced", "parentId": 3, "selected": false}], "title": "Project Difficulty", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 4, "filters": [{"futureCount": 14, "anySelected": false, "id": 0, "filters": [], "title": "Evaluation", "parentId": 4, "selected": false}, {"futureCount": 12, "anySelected": false, "id": 1, "filters": [], "title": "Experimental", "parentId": 4, "selected": false}, {"futureCount": 18, "anySelected": false, "id": 2, "filters": [], "title": "Production", "parentId": 4, "selected": false}], "title": "Quality", "parentId": -1, "selected": false}, {"futureCount": 0, "anySelected": false, "id": 5, "filters": [{"futureCount": 10, "anySelected": false, "id": 0, "filters": [], "title": "Gecko SDK Suite v4.2.1", "parentId": 5, "selected": false}, {"futureCount": 10, "anySelected": false, "id": 1, "filters": [], "title": "Gecko SDK Suite v4.4.2", "parentId": 5, "selected": false}, {"futureCount": 10, "anySelected": false, "id": 2, "filters": [], "title": "Gecko SDK Suite v4.4.4", "parentId": 5, "selected": false}, {"futureCount": 7, "anySelected": false, "id": 3, "filters": [], "title": "Simplicity SDK Suite v2024.12.2", "parentId": 5, "selected": false}, {"futureCount": 7, "anySelected": false, "id": 4, "filters": [], "title": "Simplicity SDK Suite v2024.6.2", "parentId": 5, "selected": false}], "title": "Provider", "parentId": -1, "selected": false}], "totalCount": 44}