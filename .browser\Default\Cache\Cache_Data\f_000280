{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>USART - Synchronous/Asynchronous Serial<span id=\"usart-synchronous-asynchronous-serial\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-synchronous-asynchronous-serial\">#</a></span></h1><p style=\"color:inherit\">Universal Synchronous/Asynchronous Receiver/Transmitter Peripheral API. </p><p style=\"color:inherit\">The Universal Synchronous/Asynchronous Receiver/Transmitter (USART) is a very flexible serial I/O module. It supports full duplex asynchronous UART communication as well as RS-485, SPI, MicroWire, and 3-wire. It can also interface with ISO7816 Smart-Cards, and IrDA devices.</p><p style=\"color:inherit\">The USART has a wide selection of operating modes, frame formats, and baud rates. All features are supported through the API of this module.</p><p style=\"color:inherit\">Triple buffering and DMA support makes high data-rates possible with minimal CPU intervention. It is possible to transmit and receive large frames while the MCU remains in EM1 Sleep.</p><p style=\"color:inherit\">This module does not support DMA configuration. The UARTDRV and SPIDRV drivers provide full support for DMA and more.</p><p style=\"color:inherit\">The following steps are necessary for basic operation:</p><p style=\"color:inherit\">Clock enable: </p><pre class=\"language-clike\"><code class=\"language-clike\">#<span class=\"token keyword\">if</span> <span class=\"token operator\">!</span><span class=\"token function\">defined</span><span class=\"token punctuation\">(</span>_SILICON_LABS_32B_SERIES_2<span class=\"token punctuation\">)</span>\n<span class=\"token comment\">/* USART is a HFPERCLK peripheral. Enable HFPERCLK domain and USART0.\n * We also need to enable the clock for GPIO to configure pins. */</span>\n<span class=\"token function\">CMU_ClockEnable</span><span class=\"token punctuation\">(</span>cmuClock_HFPER<span class=\"token punctuation\">,</span> <span class=\"token boolean\">true</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token function\">CMU_ClockEnable</span><span class=\"token punctuation\">(</span>cmuClock_USART0<span class=\"token punctuation\">,</span> <span class=\"token boolean\">true</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token function\">CMU_ClockEnable</span><span class=\"token punctuation\">(</span>cmuClock_GPIO<span class=\"token punctuation\">,</span> <span class=\"token boolean\">true</span><span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n#endif\n</code></pre><p style=\"color:inherit\">To initialize the USART for asynchronous operation (e.g., UART): </p><pre class=\"language-clike\"><code class=\"language-clike\"><span class=\"token comment\">/* Initialize with default settings and then update fields according to application requirements. */</span>\nUSART_InitAsync_TypeDef initAsync <span class=\"token operator\">=</span> USART_INITASYNC_DEFAULT<span class=\"token punctuation\">;</span>\ninitAsync<span class=\"token punctuation\">.</span>baudrate <span class=\"token operator\">=</span> <span class=\"token number\">38400</span><span class=\"token punctuation\">;</span>\n<span class=\"token function\">USART_InitAsync</span><span class=\"token punctuation\">(</span>USART0<span class=\"token punctuation\">,</span> <span class=\"token operator\">&amp;</span>initAsync<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n</code></pre><p style=\"color:inherit\">To initialize the USART for synchronous operation (e.g., SPI): </p><pre class=\"language-clike\"><code class=\"language-clike\"><span class=\"token comment\">/* Initialize with default settings and then update fields according to application requirements. */</span>\nUSART_InitSync_TypeDef initSync <span class=\"token operator\">=</span> USART_INITSYNC_DEFAULT<span class=\"token punctuation\">;</span>\n<span class=\"token comment\">/* Operate as SPI master */</span>\ninitSync<span class=\"token punctuation\">.</span>master <span class=\"token operator\">=</span> <span class=\"token boolean\">true</span><span class=\"token punctuation\">;</span>\n<span class=\"token comment\">/* Clock idle low, sample on falling edge. */</span>\ninitSync<span class=\"token punctuation\">.</span>clockMode <span class=\"token operator\">=</span> usartClockMode1<span class=\"token punctuation\">;</span>\n<span class=\"token function\">USART_InitSync</span><span class=\"token punctuation\">(</span>USART0<span class=\"token punctuation\">,</span> <span class=\"token operator\">&amp;</span>initSync<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n</code></pre><p style=\"color:inherit\">After pins are assigned for the application/board, enable pins at the desired location. Available locations can be obtained from the Pin Definitions section in the data sheet.   <strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">UARTDRV supports all types of UART flow control. Software assisted hardware flow control is available for parts without true UART hardware flow control. </p></li></ul><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart-init-async-type-def\" target=\"_blank\" rel=\"\">USART_InitAsync_TypeDef</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart-prs-trigger-init-type-def\" target=\"_blank\" rel=\"\">USART_PrsTriggerInit_TypeDef</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart-init-sync-type-def\" target=\"_blank\" rel=\"\">USART_InitSync_TypeDef</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart-init-ir-da-type-def\" target=\"_blank\" rel=\"\">USART_InitIrDA_TypeDef</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart-init-i2s-type-def\" target=\"_blank\" rel=\"\">USART_InitI2s_TypeDef</a></p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-databits-type-def\">USART_Databits_TypeDef</a> {</div><div class=\"enum\">usartDatabits4 = USART_FRAME_DATABITS_FOUR</div><div class=\"enum\">usartDatabits5 = USART_FRAME_DATABITS_FIVE</div><div class=\"enum\">usartDatabits6 = USART_FRAME_DATABITS_SIX</div><div class=\"enum\">usartDatabits7 = USART_FRAME_DATABITS_SEVEN</div><div class=\"enum\">usartDatabits8 = USART_FRAME_DATABITS_EIGHT</div><div class=\"enum\">usartDatabits9 = USART_FRAME_DATABITS_NINE</div><div class=\"enum\">usartDatabits10 = USART_FRAME_DATABITS_TEN</div><div class=\"enum\">usartDatabits11 = USART_FRAME_DATABITS_ELEVEN</div><div class=\"enum\">usartDatabits12 = USART_FRAME_DATABITS_TWELVE</div><div class=\"enum\">usartDatabits13 = USART_FRAME_DATABITS_THIRTEEN</div><div class=\"enum\">usartDatabits14 = USART_FRAME_DATABITS_FOURTEEN</div><div class=\"enum\">usartDatabits15 = USART_FRAME_DATABITS_FIFTEEN</div><div class=\"enum\">usartDatabits16 = USART_FRAME_DATABITS_SIXTEEN</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Databit selection. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-enable-type-def\">USART_Enable_TypeDef</a> {</div><div class=\"enum\">usartDisable = 0x0</div><div class=\"enum\">usartEnableRx = USART_CMD_RXEN</div><div class=\"enum\">usartEnableTx = USART_CMD_TXEN</div><div class=\"enum\">usartEnable = (USART_CMD_RXEN | USART_CMD_TXEN)</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Enable selection. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-ovs-type-def\">USART_OVS_TypeDef</a> {</div><div class=\"enum\">usartOVS16 = USART_CTRL_OVS_X16</div><div class=\"enum\">usartOVS8 = USART_CTRL_OVS_X8</div><div class=\"enum\">usartOVS6 = USART_CTRL_OVS_X6</div><div class=\"enum\">usartOVS4 = USART_CTRL_OVS_X4</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Oversampling selection, used for asynchronous operation. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-parity-type-def\">USART_Parity_TypeDef</a> {</div><div class=\"enum\">usartNoParity = USART_FRAME_PARITY_NONE</div><div class=\"enum\">usartEvenParity = USART_FRAME_PARITY_EVEN</div><div class=\"enum\">usartOddParity = USART_FRAME_PARITY_ODD</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Parity selection, mainly used for asynchronous operation. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-stopbits-type-def\">USART_Stopbits_TypeDef</a> {</div><div class=\"enum\">usartStopbits0p5 = USART_FRAME_STOPBITS_HALF</div><div class=\"enum\">usartStopbits1 = USART_FRAME_STOPBITS_ONE</div><div class=\"enum\">usartStopbits1p5 = USART_FRAME_STOPBITS_ONEANDAHALF</div><div class=\"enum\">usartStopbits2 = USART_FRAME_STOPBITS_TWO</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Stop bits selection, used for asynchronous operation. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-hw-flow-control-type-def\">USART_HwFlowControl_TypeDef</a> {</div><div class=\"enum\">usartHwFlowControlNone = 0</div><div class=\"enum\">usartHwFlowControlCts </div><div class=\"enum\">usartHwFlowControlRts </div><div class=\"enum\">usartHwFlowControlCtsAndRts </div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Hardware Flow Control Selection. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-clock-mode-type-def\">USART_ClockMode_TypeDef</a> {</div><div class=\"enum\">usartClockMode0 = USART_CTRL_CLKPOL_IDLELOW | USART_CTRL_CLKPHA_SAMPLELEADING</div><div class=\"enum\">usartClockMode1 = USART_CTRL_CLKPOL_IDLELOW | USART_CTRL_CLKPHA_SAMPLETRAILING</div><div class=\"enum\">usartClockMode2 = USART_CTRL_CLKPOL_IDLEHIGH | USART_CTRL_CLKPHA_SAMPLELEADING</div><div class=\"enum\">usartClockMode3 = USART_CTRL_CLKPOL_IDLEHIGH | USART_CTRL_CLKPHA_SAMPLETRAILING</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Clock polarity/phase mode. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-ir-da-pw-typedef\">USART_IrDAPw_Typedef</a> {</div><div class=\"enum\">usartIrDAPwONE = USART_IRCTRL_IRPW_ONE</div><div class=\"enum\">usartIrDAPwTWO = USART_IRCTRL_IRPW_TWO</div><div class=\"enum\">usartIrDAPwTHREE = USART_IRCTRL_IRPW_THREE</div><div class=\"enum\">usartIrDAPwFOUR = USART_IRCTRL_IRPW_FOUR</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">Pulse width selection for IrDA mode. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-i2s-format-type-def\">USART_I2sFormat_TypeDef</a> {</div><div class=\"enum\">usartI2sFormatW32D32 = USART_I2SCTRL_FORMAT_W32D32</div><div class=\"enum\">usartI2sFormatW32D24M = USART_I2SCTRL_FORMAT_W32D24M</div><div class=\"enum\">usartI2sFormatW32D24 = USART_I2SCTRL_FORMAT_W32D24</div><div class=\"enum\">usartI2sFormatW32D16 = USART_I2SCTRL_FORMAT_W32D16</div><div class=\"enum\">usartI2sFormatW32D8 = USART_I2SCTRL_FORMAT_W32D8</div><div class=\"enum\">usartI2sFormatW16D16 = USART_I2SCTRL_FORMAT_W16D16</div><div class=\"enum\">usartI2sFormatW16D8 = USART_I2SCTRL_FORMAT_W16D8</div><div class=\"enum\">usartI2sFormatW8D8 = USART_I2SCTRL_FORMAT_W8D8</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">I2S format selection. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-i2s-justify-type-def\">USART_I2sJustify_TypeDef</a> {</div><div class=\"enum\">usartI2sJustifyLeft = USART_I2SCTRL_JUSTIFY_LEFT</div><div class=\"enum\">usartI2sJustifyRight = USART_I2SCTRL_JUSTIFY_RIGHT</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">I2S frame data justify. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Typedefs<span id=\"typedef-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#typedef-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">typedef uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-prs-channel-t\">USART_PRS_Channel_t</a></div><div class=\"classdescription\"><p style=\"color:inherit\">PRS Channel type. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-baudrate-async-set\">USART_BaudrateAsyncSet</a>(USART_TypeDef *usart, uint32_t refFreq, uint32_t baudrate, USART_OVS_TypeDef ovs)</div><div class=\"classdescription\"><p style=\"color:inherit\">Configure USART/UART operating in asynchronous mode to use a given baudrate (or as close as possible to a specified baudrate). </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint32_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-baudrate-calc\">USART_BaudrateCalc</a>(uint32_t refFreq, uint32_t clkdiv, bool syncmode, USART_OVS_TypeDef ovs)</div><div class=\"classdescription\"><p style=\"color:inherit\">Calculate baudrate for USART/UART given reference frequency, clock division, and oversampling rate (if async mode). </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint32_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-baudrate-get\">USART_BaudrateGet</a>(USART_TypeDef *usart)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get the current baudrate for USART/UART. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-baudrate-sync-set\">USART_BaudrateSyncSet</a>(USART_TypeDef *usart, uint32_t refFreq, uint32_t baudrate)</div><div class=\"classdescription\"><p style=\"color:inherit\">Configure the USART operating in synchronous mode to use a given baudrate (or as close as possible to a specified baudrate). </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-enable-1\">USART_Enable</a>(USART_TypeDef *usart, USART_Enable_TypeDef enable)</div><div class=\"classdescription\"><p style=\"color:inherit\">Enable/disable USART/UART receiver and/or transmitter. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-init-async\">USART_InitAsync</a>(USART_TypeDef *usart, const USART_InitAsync_TypeDef *init)</div><div class=\"classdescription\"><p style=\"color:inherit\">Initialize USART/UART for normal asynchronous mode. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-init-sync\">USART_InitSync</a>(USART_TypeDef *usart, const USART_InitSync_TypeDef *init)</div><div class=\"classdescription\"><p style=\"color:inherit\">Initialize USART for synchronous mode. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usar-tn-init-ir-da\">USARTn_InitIrDA</a>(USART_TypeDef *usart, const USART_InitIrDA_TypeDef *init)</div><div class=\"classdescription\"><p style=\"color:inherit\">Initialize USART for asynchronous IrDA mode. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-init-i2s\">USART_InitI2s</a>(USART_TypeDef *usart, USART_InitI2s_TypeDef *init)</div><div class=\"classdescription\"><p style=\"color:inherit\">Initialize USART for I2S mode. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-init-prs-trigger\">USART_InitPrsTrigger</a>(USART_TypeDef *usart, const USART_PrsTriggerInit_TypeDef *init)</div><div class=\"classdescription\"><p style=\"color:inherit\">Initialize the automatic transmissions using PRS channel as a trigger. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-reset\">USART_Reset</a>(USART_TypeDef *usart)</div><div class=\"classdescription\"><p style=\"color:inherit\">Reset USART/UART to the same state that it was in after a hardware reset. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-rx\">USART_Rx</a>(USART_TypeDef *usart)</div><div class=\"classdescription\"><p style=\"color:inherit\">Receive one 4-8 bit frame, (or part of 10-16 bit frame). </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint16_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-rx-double\">USART_RxDouble</a>(USART_TypeDef *usart)</div><div class=\"classdescription\"><p style=\"color:inherit\">Receive two 4-8 bit frames or one 10-16 bit frame. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint32_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-rx-double-ext\">USART_RxDoubleExt</a>(USART_TypeDef *usart)</div><div class=\"classdescription\"><p style=\"color:inherit\">Receive two 4-9 bit frames, or one 10-16 bit frame with extended information. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint16_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-rx-ext\">USART_RxExt</a>(USART_TypeDef *usart)</div><div class=\"classdescription\"><p style=\"color:inherit\">Receive one 4-9 bit frame (or part of 10-16 bit frame) with extended information. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-spi-transfer\">USART_SpiTransfer</a>(USART_TypeDef *usart, uint8_t data)</div><div class=\"classdescription\"><p style=\"color:inherit\">Perform one 8 bit frame SPI transfer. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-tx\">USART_Tx</a>(USART_TypeDef *usart, uint8_t data)</div><div class=\"classdescription\"><p style=\"color:inherit\">Transmit one 4-9 bit frame. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-tx-double\">USART_TxDouble</a>(USART_TypeDef *usart, uint16_t data)</div><div class=\"classdescription\"><p style=\"color:inherit\">Transmit two 4-9 bit frames or one 10-16 bit frame. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-tx-double-ext\">USART_TxDoubleExt</a>(USART_TypeDef *usart, uint32_t data)</div><div class=\"classdescription\"><p style=\"color:inherit\">Transmit two 4-9 bit frames or one 10-16 bit frame with extended control. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-tx-ext\">USART_TxExt</a>(USART_TypeDef *usart, uint16_t data)</div><div class=\"classdescription\"><p style=\"color:inherit\">Transmit one 4-9 bit frame with extended control. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-int-clear\">USART_IntClear</a>(USART_TypeDef *usart, uint32_t flags)</div><div class=\"classdescription\"><p style=\"color:inherit\">Clear one or more pending USART interrupts. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-int-disable\">USART_IntDisable</a>(USART_TypeDef *usart, uint32_t flags)</div><div class=\"classdescription\"><p style=\"color:inherit\">Disable one or more USART interrupts. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-int-enable\">USART_IntEnable</a>(USART_TypeDef *usart, uint32_t flags)</div><div class=\"classdescription\"><p style=\"color:inherit\">Enable one or more USART interrupts. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint32_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-int-get\">USART_IntGet</a>(USART_TypeDef *usart)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get pending USART interrupt flags. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint32_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-int-get-enabled\">USART_IntGetEnabled</a>(USART_TypeDef *usart)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get enabled and pending USART interrupt flags. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-int-set\">USART_IntSet</a>(USART_TypeDef *usart, uint32_t flags)</div><div class=\"classdescription\"><p style=\"color:inherit\">Set one or more pending USART interrupts from SW. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint32_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-status-get\">USART_StatusGet</a>(USART_TypeDef *usart)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get USART STATUS register. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint8_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-rx-data-get\">USART_RxDataGet</a>(USART_TypeDef *usart)</div><div class=\"classdescription\"><p style=\"color:inherit\">Receive one 4-8 bit frame, (or part of 10-16 bit frame). </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint16_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-rx-double-get\">USART_RxDoubleGet</a>(USART_TypeDef *usart)</div><div class=\"classdescription\"><p style=\"color:inherit\">Receive two 4-8 bit frames, or one 10-16 bit frame. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint32_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-rx-double-x-get\">USART_RxDoubleXGet</a>(USART_TypeDef *usart)</div><div class=\"classdescription\"><p style=\"color:inherit\">Receive two 4-9 bit frames, or one 10-16 bit frame with extended information. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">uint16_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-rx-data-x-get\">USART_RxDataXGet</a>(USART_TypeDef *usart)</div><div class=\"classdescription\"><p style=\"color:inherit\">Receive one 4-9 bit frame, (or part of 10-16 bit frame) with extended information. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-initasync-default\">USART_INITASYNC_DEFAULT</a> undefined</div><div class=\"classdescription\"><p style=\"color:inherit\">Default configuration for USART asynchronous initialization structure. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-initprstrigger-default\">USART_INITPRSTRIGGER_DEFAULT</a> undefined</div><div class=\"classdescription\"><p style=\"color:inherit\">Default configuration for USART PRS triggering structure. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-initsync-default\">USART_INITSYNC_DEFAULT</a> undefined</div><div class=\"classdescription\"><p style=\"color:inherit\">Default configuration for USART sync initialization structure. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-initirda-default\">USART_INITIRDA_DEFAULT</a> undefined</div><div class=\"classdescription\"><p style=\"color:inherit\">Default configuration for IrDA mode initialization structure. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#usart-initi2-s-default\">USART_INITI2S_DEFAULT</a> undefined</div><div class=\"classdescription\"><p style=\"color:inherit\">Default configuration for I2S mode initialization structure. </p></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>USART_Databits_TypeDef<span id=\"usart-databits-type-def\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-databits-type-def\">#</a></span></h3><blockquote>USART_Databits_TypeDef</blockquote><p style=\"color:inherit\">Databit selection. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">usartDatabits4</td><td class=\"fielddescription\"><p style=\"color:inherit\">4 data bits (not available for UART). </p></td></tr><tr><td class=\"fieldname\">usartDatabits5</td><td class=\"fielddescription\"><p style=\"color:inherit\">5 data bits (not available for UART). </p></td></tr><tr><td class=\"fieldname\">usartDatabits6</td><td class=\"fielddescription\"><p style=\"color:inherit\">6 data bits (not available for UART). </p></td></tr><tr><td class=\"fieldname\">usartDatabits7</td><td class=\"fielddescription\"><p style=\"color:inherit\">7 data bits (not available for UART). </p></td></tr><tr><td class=\"fieldname\">usartDatabits8</td><td class=\"fielddescription\"><p style=\"color:inherit\">8 data bits. </p></td></tr><tr><td class=\"fieldname\">usartDatabits9</td><td class=\"fielddescription\"><p style=\"color:inherit\">9 data bits. </p></td></tr><tr><td class=\"fieldname\">usartDatabits10</td><td class=\"fielddescription\"><p style=\"color:inherit\">10 data bits (not available for UART). </p></td></tr><tr><td class=\"fieldname\">usartDatabits11</td><td class=\"fielddescription\"><p style=\"color:inherit\">11 data bits (not available for UART). </p></td></tr><tr><td class=\"fieldname\">usartDatabits12</td><td class=\"fielddescription\"><p style=\"color:inherit\">12 data bits (not available for UART). </p></td></tr><tr><td class=\"fieldname\">usartDatabits13</td><td class=\"fielddescription\"><p style=\"color:inherit\">13 data bits (not available for UART). </p></td></tr><tr><td class=\"fieldname\">usartDatabits14</td><td class=\"fielddescription\"><p style=\"color:inherit\">14 data bits (not available for UART). </p></td></tr><tr><td class=\"fieldname\">usartDatabits15</td><td class=\"fielddescription\"><p style=\"color:inherit\">15 data bits (not available for UART). </p></td></tr><tr><td class=\"fieldname\">usartDatabits16</td><td class=\"fielddescription\"><p style=\"color:inherit\">16 data bits (not available for UART). </p></td></tr></tbody></table><br><div>Definition at line <code>97</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_Enable_TypeDef<span id=\"usart-enable-type-def\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-enable-type-def\">#</a></span></h3><blockquote>USART_Enable_TypeDef</blockquote><p style=\"color:inherit\">Enable selection. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">usartDisable</td><td class=\"fielddescription\"><p style=\"color:inherit\">Disable both receiver and transmitter. </p></td></tr><tr><td class=\"fieldname\">usartEnableRx</td><td class=\"fielddescription\"><p style=\"color:inherit\">Enable receiver only, transmitter disabled. </p></td></tr><tr><td class=\"fieldname\">usartEnableTx</td><td class=\"fielddescription\"><p style=\"color:inherit\">Enable transmitter only, receiver disabled. </p></td></tr><tr><td class=\"fieldname\">usartEnable</td><td class=\"fielddescription\"><p style=\"color:inherit\">Enable both receiver and transmitter. </p></td></tr></tbody></table><br><div>Definition at line <code>114</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_OVS_TypeDef<span id=\"usart-ovs-type-def\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-ovs-type-def\">#</a></span></h3><blockquote>USART_OVS_TypeDef</blockquote><p style=\"color:inherit\">Oversampling selection, used for asynchronous operation. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">usartOVS16</td><td class=\"fielddescription\"><p style=\"color:inherit\">16x oversampling (normal). </p></td></tr><tr><td class=\"fieldname\">usartOVS8</td><td class=\"fielddescription\"><p style=\"color:inherit\">8x oversampling. </p></td></tr><tr><td class=\"fieldname\">usartOVS6</td><td class=\"fielddescription\"><p style=\"color:inherit\">6x oversampling. </p></td></tr><tr><td class=\"fieldname\">usartOVS4</td><td class=\"fielddescription\"><p style=\"color:inherit\">4x oversampling. </p></td></tr></tbody></table><br><div>Definition at line <code>129</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_Parity_TypeDef<span id=\"usart-parity-type-def\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-parity-type-def\">#</a></span></h3><blockquote>USART_Parity_TypeDef</blockquote><p style=\"color:inherit\">Parity selection, mainly used for asynchronous operation. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">usartNoParity</td><td class=\"fielddescription\"><p style=\"color:inherit\">No parity. </p></td></tr><tr><td class=\"fieldname\">usartEvenParity</td><td class=\"fielddescription\"><p style=\"color:inherit\">Even parity. </p></td></tr><tr><td class=\"fieldname\">usartOddParity</td><td class=\"fielddescription\"><p style=\"color:inherit\">Odd parity. </p></td></tr></tbody></table><br><div>Definition at line <code>137</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_Stopbits_TypeDef<span id=\"usart-stopbits-type-def\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-stopbits-type-def\">#</a></span></h3><blockquote>USART_Stopbits_TypeDef</blockquote><p style=\"color:inherit\">Stop bits selection, used for asynchronous operation. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">usartStopbits0p5</td><td class=\"fielddescription\"><p style=\"color:inherit\">0.5 stop bits. </p></td></tr><tr><td class=\"fieldname\">usartStopbits1</td><td class=\"fielddescription\"><p style=\"color:inherit\">1 stop bits. </p></td></tr><tr><td class=\"fieldname\">usartStopbits1p5</td><td class=\"fielddescription\"><p style=\"color:inherit\">1.5 stop bits. </p></td></tr><tr><td class=\"fieldname\">usartStopbits2</td><td class=\"fielddescription\"><p style=\"color:inherit\">2 stop bits. </p></td></tr></tbody></table><br><div>Definition at line <code>144</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_HwFlowControl_TypeDef<span id=\"usart-hw-flow-control-type-def\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-hw-flow-control-type-def\">#</a></span></h3><blockquote>USART_HwFlowControl_TypeDef</blockquote><p style=\"color:inherit\">Hardware Flow Control Selection. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">usartHwFlowControlNone</td><td class=\"fielddescription\"><p style=\"color:inherit\">No hardware flow control. </p></td></tr><tr><td class=\"fieldname\">usartHwFlowControlCts</td><td class=\"fielddescription\"><p style=\"color:inherit\">CTS signal is enabled for TX flow control. </p></td></tr><tr><td class=\"fieldname\">usartHwFlowControlRts</td><td class=\"fielddescription\"><p style=\"color:inherit\">RTS signal is enabled for RX flow control. </p></td></tr><tr><td class=\"fieldname\">usartHwFlowControlCtsAndRts</td><td class=\"fielddescription\"><p style=\"color:inherit\">CTS and RTS signals are enabled for TX and RX flow control. </p></td></tr></tbody></table><br><div>Definition at line <code>166</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_ClockMode_TypeDef<span id=\"usart-clock-mode-type-def\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-clock-mode-type-def\">#</a></span></h3><blockquote>USART_ClockMode_TypeDef</blockquote><p style=\"color:inherit\">Clock polarity/phase mode. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">usartClockMode0</td><td class=\"fielddescription\"><p style=\"color:inherit\">Clock idle low, sample on rising edge. </p></td></tr><tr><td class=\"fieldname\">usartClockMode1</td><td class=\"fielddescription\"><p style=\"color:inherit\">Clock idle low, sample on falling edge. </p></td></tr><tr><td class=\"fieldname\">usartClockMode2</td><td class=\"fielddescription\"><p style=\"color:inherit\">Clock idle high, sample on falling edge. </p></td></tr><tr><td class=\"fieldname\">usartClockMode3</td><td class=\"fielddescription\"><p style=\"color:inherit\">Clock idle high, sample on rising edge. </p></td></tr></tbody></table><br><div>Definition at line <code>179</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_IrDAPw_Typedef<span id=\"usart-ir-da-pw-typedef\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-ir-da-pw-typedef\">#</a></span></h3><blockquote>USART_IrDAPw_Typedef</blockquote><p style=\"color:inherit\">Pulse width selection for IrDA mode. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">usartIrDAPwONE</td><td class=\"fielddescription\"><p style=\"color:inherit\">IrDA pulse width is 1/16 for OVS=0 and 1/8 for OVS=1. </p></td></tr><tr><td class=\"fieldname\">usartIrDAPwTWO</td><td class=\"fielddescription\"><p style=\"color:inherit\">IrDA pulse width is 2/16 for OVS=0 and 2/8 for OVS=1. </p></td></tr><tr><td class=\"fieldname\">usartIrDAPwTHREE</td><td class=\"fielddescription\"><p style=\"color:inherit\">IrDA pulse width is 3/16 for OVS=0 and 3/8 for OVS=1. </p></td></tr><tr><td class=\"fieldname\">usartIrDAPwFOUR</td><td class=\"fielddescription\"><p style=\"color:inherit\">IrDA pulse width is 4/16 for OVS=0 and 4/8 for OVS=1. </p></td></tr></tbody></table><br><div>Definition at line <code>194</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_I2sFormat_TypeDef<span id=\"usart-i2s-format-type-def\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-i2s-format-type-def\">#</a></span></h3><blockquote>USART_I2sFormat_TypeDef</blockquote><p style=\"color:inherit\">I2S format selection. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">usartI2sFormatW32D32</td><td class=\"fielddescription\"><p style=\"color:inherit\">32-bit word, 32-bit data. </p></td></tr><tr><td class=\"fieldname\">usartI2sFormatW32D24M</td><td class=\"fielddescription\"><p style=\"color:inherit\">32-bit word, 32-bit data with 8 lsb masked. </p></td></tr><tr><td class=\"fieldname\">usartI2sFormatW32D24</td><td class=\"fielddescription\"><p style=\"color:inherit\">32-bit word, 24-bit data. </p></td></tr><tr><td class=\"fieldname\">usartI2sFormatW32D16</td><td class=\"fielddescription\"><p style=\"color:inherit\">32-bit word, 16-bit data. </p></td></tr><tr><td class=\"fieldname\">usartI2sFormatW32D8</td><td class=\"fielddescription\"><p style=\"color:inherit\">32-bit word, 8-bit data. </p></td></tr><tr><td class=\"fieldname\">usartI2sFormatW16D16</td><td class=\"fielddescription\"><p style=\"color:inherit\">16-bit word, 16-bit data. </p></td></tr><tr><td class=\"fieldname\">usartI2sFormatW16D8</td><td class=\"fielddescription\"><p style=\"color:inherit\">16-bit word, 8-bit data. </p></td></tr><tr><td class=\"fieldname\">usartI2sFormatW8D8</td><td class=\"fielddescription\"><p style=\"color:inherit\">8-bit word, 8-bit data. </p></td></tr></tbody></table><br><div>Definition at line <code>265</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_I2sJustify_TypeDef<span id=\"usart-i2s-justify-type-def\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-i2s-justify-type-def\">#</a></span></h3><blockquote>USART_I2sJustify_TypeDef</blockquote><p style=\"color:inherit\">I2S frame data justify. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">usartI2sJustifyLeft</td><td class=\"fielddescription\"><p style=\"color:inherit\">Data is left-justified within the frame. </p></td></tr><tr><td class=\"fieldname\">usartI2sJustifyRight</td><td class=\"fielddescription\"><p style=\"color:inherit\">Data is right-justified within the frame. </p></td></tr></tbody></table><br><div>Definition at line <code>277</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Typedef Documentation<span id=\"typedef-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#typedef-definition\">#</a></span></h2><div><h3>USART_PRS_Channel_t<span id=\"usart-prs-channel-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-prs-channel-t\">#</a></span></h3><blockquote>typedef uint8_t USART_PRS_Channel_t </blockquote><p style=\"color:inherit\">PRS Channel type. </p><br><div>Definition at line <code>209</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>USART_BaudrateAsyncSet<span id=\"usart-baudrate-async-set\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-baudrate-async-set\">#</a></span></h3><blockquote>void USART_BaudrateAsyncSet (USART_TypeDef * usart, uint32_t refFreq, uint32_t baudrate, <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-ovs-type-def\" target=\"_blank\" rel=\"\">USART_OVS_TypeDef</a> ovs)</blockquote><p style=\"color:inherit\">Configure USART/UART operating in asynchronous mode to use a given baudrate (or as close as possible to a specified baudrate). </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART/UART peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">refFreq</td><td><p style=\"color:inherit\">USART/UART reference clock frequency in Hz. If set to 0, the currently configured reference clock is assumed.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">baudrate</td><td><p style=\"color:inherit\">Baudrate to try to achieve for USART/UART.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">ovs</td><td><p style=\"color:inherit\">Oversampling to be used. Normal is 16x oversampling but lower oversampling may be used to achieve higher rates or better baudrate accuracy in some cases. Notice that lower oversampling frequency makes the channel more vulnerable to bit faults during reception due to clock inaccuracies compared to the link partner. </p></td></tr></tbody></table></div><br><div>Definition at line <code>313</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_BaudrateCalc<span id=\"usart-baudrate-calc\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-baudrate-calc\">#</a></span></h3><blockquote>uint32_t USART_BaudrateCalc (uint32_t refFreq, uint32_t clkdiv, bool syncmode, <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-ovs-type-def\" target=\"_blank\" rel=\"\">USART_OVS_TypeDef</a> ovs)</blockquote><p style=\"color:inherit\">Calculate baudrate for USART/UART given reference frequency, clock division, and oversampling rate (if async mode). </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">refFreq</td><td><p style=\"color:inherit\">USART/UART HF peripheral frequency used.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">clkdiv</td><td><p style=\"color:inherit\">A clock division factor to be used.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">syncmode</td><td><ul style=\"list-style:\"><li><p style=\"color:inherit\">True - synchronous mode operation. </p></li><li><p style=\"color:inherit\">False - asynchronous mode operation.</p></li></ul></td></tr><tr><td>[in]</td><td class=\"paramname\">ovs</td><td><p style=\"color:inherit\">Oversampling used if in asynchronous mode. Not used if <code>syncmode</code> is true.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function returns the baudrate that a USART/UART module will use if configured with the given frequency, clock divisor, and mode. Notice that this function will not use the hardware configuration. It can be used to determine if a given configuration is sufficiently accurate for the application.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Baudrate with given settings. </p></li></ul><br><div>Definition at line <code>455</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_BaudrateGet<span id=\"usart-baudrate-get\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-baudrate-get\">#</a></span></h3><blockquote>uint32_t USART_BaudrateGet (USART_TypeDef * usart)</blockquote><p style=\"color:inherit\">Get the current baudrate for USART/UART. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART/UART peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function returns the actual baudrate (not considering oscillator inaccuracies) used by a USART/UART peripheral.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The current baudrate. </p></li></ul><br><div>Definition at line <code>586</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_BaudrateSyncSet<span id=\"usart-baudrate-sync-set\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-baudrate-sync-set\">#</a></span></h3><blockquote>void USART_BaudrateSyncSet (USART_TypeDef * usart, uint32_t refFreq, uint32_t baudrate)</blockquote><p style=\"color:inherit\">Configure the USART operating in synchronous mode to use a given baudrate (or as close as possible to a specified baudrate). </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART peripheral register block. (Cannot be used on UART modules.)</p></td></tr><tr><td>[in]</td><td class=\"paramname\">refFreq</td><td><p style=\"color:inherit\">A USART reference clock frequency in Hz that will be used. If set to 0, the currently-configured reference clock is assumed.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">baudrate</td><td><p style=\"color:inherit\">Baudrate to try to achieve for USART. </p></td></tr></tbody></table></div><p style=\"color:inherit\">The configuration will be set to use a baudrate &lt;= the specified baudrate to ensure that the baudrate does not exceed the specified value.</p><p style=\"color:inherit\">The fractional clock division is suppressed, although the hardware design allows it. It could cause half clock cycles to exceed a specified limit and thus potentially violate specifications for the slave device. In some special situations, a fractional clock division may be useful even in synchronous mode, but in those cases it must be directly adjusted, possibly assisted by <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-baudrate-calc\" target=\"_blank\" rel=\"\">USART_BaudrateCalc()</a>:</p><p style=\"color:inherit\"><strong>Warnings</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The consequence of the aforementioned suppression of the fractional part of the clock divider is that some frequencies won't be achievable. The divider will only be able to be an integer value so the reference clock will only be dividable by N (where N is a positive integer).</p></li></ul><br><div>Definition at line <code>649</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_Enable<span id=\"usart-enable-1\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-enable-1\">#</a></span></h3><blockquote>void USART_Enable (USART_TypeDef * usart, <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-enable-type-def\" target=\"_blank\" rel=\"\">USART_Enable_TypeDef</a> enable)</blockquote><p style=\"color:inherit\">Enable/disable USART/UART receiver and/or transmitter. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART/UART peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">enable</td><td><p style=\"color:inherit\">Select the status for the receiver/transmitter. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Notice that this function does not do any configuration. Enabling should normally be done after initialization (if not enabled as part of initialization).</p><br><div>Definition at line <code>711</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_InitAsync<span id=\"usart-init-async\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-init-async\">#</a></span></h3><blockquote>void USART_InitAsync (USART_TypeDef * usart, const <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart-init-async-type-def\" target=\"_blank\" rel=\"\">USART_InitAsync_TypeDef</a> * init)</blockquote><p style=\"color:inherit\">Initialize USART/UART for normal asynchronous mode. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART/UART peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">init</td><td><p style=\"color:inherit\">A pointer to the initialization structure used to configure the basic async setup. </p></td></tr></tbody></table></div><p style=\"color:inherit\">This function will configure basic settings to operate in normal asynchronous mode.</p><p style=\"color:inherit\">A special control setup not covered by this function must be done after using this function by direct modification of the CTRL register.</p><p style=\"color:inherit\">Notice that pins used by the USART/UART module must be properly configured by the user explicitly for the USART/UART to work as intended. (When configuring pins, remember to consider the sequence of configuration to avoid unintended pulses/glitches on output pins.)</p><br><div>Definition at line <code>762</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_InitSync<span id=\"usart-init-sync\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-init-sync\">#</a></span></h3><blockquote>void USART_InitSync (USART_TypeDef * usart, const <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart-init-sync-type-def\" target=\"_blank\" rel=\"\">USART_InitSync_TypeDef</a> * init)</blockquote><p style=\"color:inherit\">Initialize USART for synchronous mode. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART peripheral register block. (UART does not support this mode.)</p></td></tr><tr><td>[in]</td><td class=\"paramname\">init</td><td><p style=\"color:inherit\">A pointer to the initialization structure used to configure basic async setup. </p></td></tr></tbody></table></div><p style=\"color:inherit\">This function will configure basic settings to operate in synchronous mode.</p><p style=\"color:inherit\">A special control setup not covered by this function must be done after using this function by direct modification of the CTRL register.</p><p style=\"color:inherit\">Notice that pins used by the USART module must be properly configured by the user explicitly for the USART to work as intended. (When configuring pins remember to consider the sequence of configuration to avoid unintended pulses/glitches on output pins.)</p><br><div>Definition at line <code>870</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USARTn_InitIrDA<span id=\"usar-tn-init-ir-da\" class=\"self-anchor\"><a class=\"perm\" href=\"#usar-tn-init-ir-da\">#</a></span></h3><blockquote>void USARTn_InitIrDA (USART_TypeDef * usart, const <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart-init-ir-da-type-def\" target=\"_blank\" rel=\"\">USART_InitIrDA_TypeDef</a> * init)</blockquote><p style=\"color:inherit\">Initialize USART for asynchronous IrDA mode. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">init</td><td><p style=\"color:inherit\">A pointer to the initialization structure used to configure async IrDA setup.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function will configure basic settings to operate in asynchronous IrDA mode.</p><p style=\"color:inherit\">A special control setup not covered by this function must be done after using this function by direct modification of the CTRL and IRCTRL registers.</p><p style=\"color:inherit\">Notice that pins used by the USART/UART module must be properly configured by the user explicitly for the USART/UART to work as intended. (When configuring pins, remember to consider the sequence of configuration to avoid unintended pulses/glitches on output pins.)</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Not all USART instances support IrDA. See the data sheet for your device. </p></li></ul><br><div>Definition at line <code>954</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_InitI2s<span id=\"usart-init-i2s\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-init-i2s\">#</a></span></h3><blockquote>void USART_InitI2s (USART_TypeDef * usart, <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart-init-i2s-type-def\" target=\"_blank\" rel=\"\">USART_InitI2s_TypeDef</a> * init)</blockquote><p style=\"color:inherit\">Initialize USART for I2S mode. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART peripheral register block. (UART does not support this mode.)</p></td></tr><tr><td>[in]</td><td class=\"paramname\">init</td><td><p style=\"color:inherit\">A pointer to the initialization structure used to configure the basic I2S setup.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function will configure basic settings to operate in I2S mode.</p><p style=\"color:inherit\">A special control setup not covered by this function must be done after using this function by direct modification of the CTRL and I2SCTRL registers.</p><p style=\"color:inherit\">Notice that pins used by the USART module must be properly configured by the user explicitly for the USART to work as intended. (When configuring pins, remember to consider the sequence of configuration to avoid unintended pulses/glitches on output pins.)</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">This function does not apply to all USART's. See the chip Reference Manual. </p></li></ul><br><div>Definition at line <code>1013</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_InitPrsTrigger<span id=\"usart-init-prs-trigger\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-init-prs-trigger\">#</a></span></h3><blockquote>void USART_InitPrsTrigger (USART_TypeDef * usart, const <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart-prs-trigger-init-type-def\" target=\"_blank\" rel=\"\">USART_PrsTriggerInit_TypeDef</a> * init)</blockquote><p style=\"color:inherit\">Initialize the automatic transmissions using PRS channel as a trigger. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to USART to configure.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">init</td><td><p style=\"color:inherit\">A pointer to the initialization structure. </p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Initialize USART with USART_Init() before setting up the PRS configuration.</p></li></ul><br><div>Definition at line <code>1053</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_Reset<span id=\"usart-reset\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-reset\">#</a></span></h3><blockquote>void USART_Reset (USART_TypeDef * usart)</blockquote><p style=\"color:inherit\">Reset USART/UART to the same state that it was in after a hardware reset. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to USART/UART peripheral register block. </p></td></tr></tbody></table></div><br><div>Definition at line <code>1087</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_Rx<span id=\"usart-rx\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-rx\">#</a></span></h3><blockquote>uint8_t USART_Rx (USART_TypeDef * usart)</blockquote><p style=\"color:inherit\">Receive one 4-8 bit frame, (or part of 10-16 bit frame). </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART/UART peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function is normally used to receive one frame when operating with frame length 4-8 bits. See <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-rx-ext\" target=\"_blank\" rel=\"\">USART_RxExt()</a> for reception of 9 bit frames.</p><p style=\"color:inherit\">Notice that possible parity/stop bits in asynchronous mode are not considered part of a specified frame bit length.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">This function will stall if the buffer is empty until data is received. Alternatively, the user can explicitly check whether data is available. If data is available, call <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-rx-data-get\" target=\"_blank\" rel=\"\">USART_RxDataGet()</a> to read the RXDATA register directly.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Data received. </p></li></ul><br><div>Definition at line <code>1182</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_RxDouble<span id=\"usart-rx-double\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-rx-double\">#</a></span></h3><blockquote>uint16_t USART_RxDouble (USART_TypeDef * usart)</blockquote><p style=\"color:inherit\">Receive two 4-8 bit frames or one 10-16 bit frame. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART/UART peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function is normally used to receive one frame when operating with frame length 10-16 bits. See <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-rx-double-ext\" target=\"_blank\" rel=\"\">USART_RxDoubleExt()</a> for reception of two 9 bit frames.</p><p style=\"color:inherit\">Notice that possible parity/stop bits in asynchronous mode are not considered part of a specified frame bit length.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">This function will stall if the buffer is empty until data is received. Alternatively, the user can explicitly check whether data is available. If data is available, call <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-rx-double-get\" target=\"_blank\" rel=\"\">USART_RxDoubleGet()</a> to read the RXDOUBLE register directly.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Data received. </p></li></ul><br><div>Definition at line <code>1214</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_RxDoubleExt<span id=\"usart-rx-double-ext\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-rx-double-ext\">#</a></span></h3><blockquote>uint32_t USART_RxDoubleExt (USART_TypeDef * usart)</blockquote><p style=\"color:inherit\">Receive two 4-9 bit frames, or one 10-16 bit frame with extended information. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART/UART peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function is normally used to receive one frame when operating with frame length 10-16 bits and additional RX status information is required.</p><p style=\"color:inherit\">Notice that possible parity/stop bits in asynchronous mode are not considered part of a specified frame bit length.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">This function will stall if buffer is empty until data is received. Alternatively, the user can explicitly check whether data is available. If data is available, call <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-rx-double-x-get\" target=\"_blank\" rel=\"\">USART_RxDoubleXGet()</a> to read the RXDOUBLEX register directly.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Data received. </p></li></ul><br><div>Definition at line <code>1246</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_RxExt<span id=\"usart-rx-ext\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-rx-ext\">#</a></span></h3><blockquote>uint16_t USART_RxExt (USART_TypeDef * usart)</blockquote><p style=\"color:inherit\">Receive one 4-9 bit frame (or part of 10-16 bit frame) with extended information. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART/UART peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function is normally used to receive one frame when operating with frame length 4-9 bits and additional RX status information is required.</p><p style=\"color:inherit\">Notice that possible parity/stop bits in asynchronous mode are not considered part of a specified frame bit length.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">This function will stall if the buffer is empty until data is received. Alternatively, the user can explicitly check whether data is available. If data is available, call <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-rx-data-x-get\" target=\"_blank\" rel=\"\">USART_RxDataXGet()</a> to read the RXDATAX register directly.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Data received. </p></li></ul><br><div>Definition at line <code>1278</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_SpiTransfer<span id=\"usart-spi-transfer\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-spi-transfer\">#</a></span></h3><blockquote>uint8_t USART_SpiTransfer (USART_TypeDef * usart, uint8_t data)</blockquote><p style=\"color:inherit\">Perform one 8 bit frame SPI transfer. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">data</td><td><p style=\"color:inherit\">Data to transmit.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">This function will stall if the transmit buffer is full. When a transmit buffer becomes available, data is written and the function will wait until data is fully transmitted. The SPI return value is then read out and returned.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Data received. </p></li></ul><br><div>Definition at line <code>1305</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_Tx<span id=\"usart-tx\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-tx\">#</a></span></h3><blockquote>void USART_Tx (USART_TypeDef * usart, uint8_t data)</blockquote><p style=\"color:inherit\">Transmit one 4-9 bit frame. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART/UART peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">data</td><td><p style=\"color:inherit\">Data to transmit. See details above for more information. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Depending on the frame length configuration, 4-8 (least significant) bits from <code>data</code> are transmitted. If the frame length is 9, 8 bits are transmitted from <code>data</code> and one bit as specified by CTRL register, BIT8DV field. See <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-tx-ext\" target=\"_blank\" rel=\"\">USART_TxExt()</a> for transmitting 9 bit frame with full control of all 9 bits.</p><p style=\"color:inherit\">Notice that possible parity/stop bits in asynchronous mode are not considered part of a specified frame bit length.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">This function will stall if the buffer is full until the buffer becomes available.</p></li></ul><br><div>Definition at line <code>1338</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_TxDouble<span id=\"usart-tx-double\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-tx-double\">#</a></span></h3><blockquote>void USART_TxDouble (USART_TypeDef * usart, uint16_t data)</blockquote><p style=\"color:inherit\">Transmit two 4-9 bit frames or one 10-16 bit frame. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART/UART peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">data</td><td><p style=\"color:inherit\">Data to transmit, the least significant byte holds the frame transmitted first. See details above for more info. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Depending on the frame length configuration, 4-8 (least significant) bits from each byte in <code>data</code> are transmitted. If frame length is 9, 8 bits are transmitted from each byte in <code>data</code> adding one bit as specified by the CTRL register, BIT8DV field, to each byte. See <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-tx-double-ext\" target=\"_blank\" rel=\"\">USART_TxDoubleExt()</a> for transmitting two 9 bit frames with full control of all 9 bits.</p><p style=\"color:inherit\">If the frame length is 10-16, 10-16 (least significant) bits from <code>data</code> are transmitted.</p><p style=\"color:inherit\">Notice that possible parity/stop bits in asynchronous mode are not considered part of a specified frame bit length.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">This function will stall if the buffer is full until the buffer becomes available.</p></li></ul><br><div>Definition at line <code>1373</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_TxDoubleExt<span id=\"usart-tx-double-ext\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-tx-double-ext\">#</a></span></h3><blockquote>void USART_TxDoubleExt (USART_TypeDef * usart, uint32_t data)</blockquote><p style=\"color:inherit\">Transmit two 4-9 bit frames or one 10-16 bit frame with extended control. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART/UART peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">data</td><td><p style=\"color:inherit\">Data to transmit with extended control. Contains two 16 bit words concatenated. Least significant word holds the frame transmitted first. If the frame length is 4-9, two frames with 4-9 least significant bits from each 16 bit word are transmitted. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Notice that possible parity/stop bits in asynchronous mode are not considered part of a specified frame bit length.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">This function will stall if the buffer is full until the buffer becomes available.</p></li></ul><ul style=\"list-style:\"><li><p style=\"color:inherit\">If the frame length is 10-16 bits, 8 data bits are taken from the least significant 16 bit word and the remaining bits from the other 16 bit word. </p></li><li><p style=\"color:inherit\">Additional control bits are available as documented in the reference manual (set to 0 if not used). For 10-16 bit frame length, these control bits are taken from the most significant 16 bit word. </p></li></ul><br><div>Definition at line <code>1408</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_TxExt<span id=\"usart-tx-ext\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-tx-ext\">#</a></span></h3><blockquote>void USART_TxExt (USART_TypeDef * usart, uint16_t data)</blockquote><p style=\"color:inherit\">Transmit one 4-9 bit frame with extended control. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">A pointer to the USART/UART peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">data</td><td><p style=\"color:inherit\">Data to transmit with extended control. Least significant bit contains frame bits. Additional control bits are available as documented in the reference manual (set to 0 if not used). </p></td></tr></tbody></table></div><p style=\"color:inherit\">Notice that possible parity/stop bits in asynchronous mode are not considered part of a specified frame bit length.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">This function will stall if the buffer is full until the buffer becomes available.</p></li></ul><br><div>Definition at line <code>1435</code> of file <code>platform/emlib/src/em_usart.c</code></div><br></div><div><h3>USART_IntClear<span id=\"usart-int-clear\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-int-clear\">#</a></span></h3><blockquote>void USART_IntClear (USART_TypeDef * usart, uint32_t flags)</blockquote><p style=\"color:inherit\">Clear one or more pending USART interrupts. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">Pointer to the USART/UART peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">Pending USART/UART interrupt source(s) to clear. Use one or more valid interrupt flags for the USART module (USART_IF_nnn) OR'ed together. </p></td></tr></tbody></table></div><br><div>Definition at line <code>803</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_IntDisable<span id=\"usart-int-disable\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-int-disable\">#</a></span></h3><blockquote>void USART_IntDisable (USART_TypeDef * usart, uint32_t flags)</blockquote><p style=\"color:inherit\">Disable one or more USART interrupts. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">Pointer to the USART/UART peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">USART/UART interrupt source(s) to disable. Use one or more valid interrupt flags for the USART module (USART_IF_nnn) OR'ed together. </p></td></tr></tbody></table></div><br><div>Definition at line <code>823</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_IntEnable<span id=\"usart-int-enable\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-int-enable\">#</a></span></h3><blockquote>void USART_IntEnable (USART_TypeDef * usart, uint32_t flags)</blockquote><p style=\"color:inherit\">Enable one or more USART interrupts. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">Pointer to the USART/UART peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">USART/UART interrupt source(s) to enable. Use one or more valid interrupt flags for the USART module (USART_IF_nnn) OR'ed together. </p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Depending on the use, a pending interrupt may already be set prior to enabling the interrupt. To ignore a pending interrupt, consider using <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-int-clear\" target=\"_blank\" rel=\"\">USART_IntClear()</a> prior to enabling the interrupt.</p></li></ul><br><div>Definition at line <code>844</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_IntGet<span id=\"usart-int-get\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-int-get\">#</a></span></h3><blockquote>uint32_t USART_IntGet (USART_TypeDef * usart)</blockquote><p style=\"color:inherit\">Get pending USART interrupt flags. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">Pointer to the USART/UART peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The event bits are not cleared by the use of this function.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">USART/UART interrupt source(s) pending. Returns one or more valid interrupt flags for the USART module (USART_IF_nnn) OR'ed together. </p></li></ul><br><div>Definition at line <code>863</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_IntGetEnabled<span id=\"usart-int-get-enabled\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-int-get-enabled\">#</a></span></h3><blockquote>uint32_t USART_IntGetEnabled (USART_TypeDef * usart)</blockquote><p style=\"color:inherit\">Get enabled and pending USART interrupt flags. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">Pointer to the USART/UART peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Useful for handling more interrupt sources in the same interrupt handler.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Interrupt flags are not cleared by the use of this function.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Pending and enabled USART interrupt sources. The return value is the bitwise AND combination of</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">the OR combination of enabled interrupt sources in USARTx_IEN_nnn register (USARTx_IEN_nnn) and</p></li><li><p style=\"color:inherit\">the OR combination of valid interrupt flags of the USART module (USARTx_IF_nnn). </p></li></ul></li></ul><br><div>Definition at line <code>887</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_IntSet<span id=\"usart-int-set\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-int-set\">#</a></span></h3><blockquote>void USART_IntSet (USART_TypeDef * usart, uint32_t flags)</blockquote><p style=\"color:inherit\">Set one or more pending USART interrupts from SW. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">Pointer to the USART/UART peripheral register block.</p></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">USART/UART interrupt source(s) to set to pending. Use one or more valid interrupt flags for the USART module (USART_IF_nnn) OR'ed together. </p></td></tr></tbody></table></div><br><div>Definition at line <code>910</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_StatusGet<span id=\"usart-status-get\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-status-get\">#</a></span></h3><blockquote>uint32_t USART_StatusGet (USART_TypeDef * usart)</blockquote><p style=\"color:inherit\">Get USART STATUS register. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">Pointer to the USART/UART peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">STATUS register value. </p></li></ul><br><div>Definition at line <code>930</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_RxDataGet<span id=\"usart-rx-data-get\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-rx-data-get\">#</a></span></h3><blockquote>uint8_t USART_RxDataGet (USART_TypeDef * usart)</blockquote><p style=\"color:inherit\">Receive one 4-8 bit frame, (or part of 10-16 bit frame). </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">Pointer to USART/UART peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function is used to quickly receive one 4-8 bits frame by reading the RXDATA register directly, without checking the STATUS register for the RXDATAV flag. This can be useful from the RXDATAV interrupt handler, i.e., waiting is superfluous, in order to quickly read the received data. Please refer to <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-rx-data-x-get\" target=\"_blank\" rel=\"\">USART_RxDataXGet()</a> for reception of 9 bit frames.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Because this function does not check whether the RXDATA register actually holds valid data, it should only be used in situations when it is certain that there is valid data, ensured by some external program routine, e.g., when handling an RXDATAV interrupt. The <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-rx\" target=\"_blank\" rel=\"\">USART_Rx()</a> is normally a better choice if the validity of the RXDATA register is not certain.</p></li><li><p style=\"color:inherit\">Notice that possible parity/stop bits in asynchronous mode are not considered part of specified frame bit length.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Data received. </p></li></ul><br><div>Definition at line <code>969</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_RxDoubleGet<span id=\"usart-rx-double-get\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-rx-double-get\">#</a></span></h3><blockquote>uint16_t USART_RxDoubleGet (USART_TypeDef * usart)</blockquote><p style=\"color:inherit\">Receive two 4-8 bit frames, or one 10-16 bit frame. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">Pointer to USART/UART peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function is used to quickly receive one 10-16 bits frame or two 4-8 bit frames by reading the RXDOUBLE register directly, without checking the STATUS register for the RXDATAV flag. This can be useful from the RXDATAV interrupt handler, i.e., waiting is superfluous, in order to quickly read the received data. This function is normally used to receive one frame when operating with frame length 10-16 bits. Please refer to <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-rx-double-x-get\" target=\"_blank\" rel=\"\">USART_RxDoubleXGet()</a> for reception of two 9 bit frames.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Because this function does not check whether the RXDOUBLE register actually holds valid data, it should only be used in situations when it is certain that there is valid data, ensured by some external program routine, e.g., when handling an RXDATAV interrupt. The <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-rx-double\" target=\"_blank\" rel=\"\">USART_RxDouble()</a> is normally a better choice if the validity of the RXDOUBLE register is not certain.</p></li><li><p style=\"color:inherit\">Notice that possible parity/stop bits in asynchronous mode are not considered part of specified frame bit length.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Data received. </p></li></ul><br><div>Definition at line <code>1006</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_RxDoubleXGet<span id=\"usart-rx-double-x-get\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-rx-double-x-get\">#</a></span></h3><blockquote>uint32_t USART_RxDoubleXGet (USART_TypeDef * usart)</blockquote><p style=\"color:inherit\">Receive two 4-9 bit frames, or one 10-16 bit frame with extended information. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">Pointer to USART/UART peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function is used to quickly receive one 10-16 bits frame or two 4-9 bit frames by reading the RXDOUBLEX register directly, without checking the STATUS register for the RXDATAV flag. This can be useful from the RXDATAV interrupt handler, i.e., waiting is superfluous, in order to quickly read the received data.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Because this function does not check whether the RXDOUBLEX register actually holds valid data, it should only be used in situations when it is certain that there is valid data, ensured by some external program routine, e.g., when handling an RXDATAV interrupt. The <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-rx-double-ext\" target=\"_blank\" rel=\"\">USART_RxDoubleExt()</a> is normally a better choice if the validity of the RXDOUBLEX register is not certain.</p></li><li><p style=\"color:inherit\">Notice that possible parity/stop bits in asynchronous mode are not considered part of specified frame bit length.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Data received. </p></li></ul><br><div>Definition at line <code>1041</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_RxDataXGet<span id=\"usart-rx-data-x-get\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-rx-data-x-get\">#</a></span></h3><blockquote>uint16_t USART_RxDataXGet (USART_TypeDef * usart)</blockquote><p style=\"color:inherit\">Receive one 4-9 bit frame, (or part of 10-16 bit frame) with extended information. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">usart</td><td><p style=\"color:inherit\">Pointer to USART/UART peripheral register block.</p></td></tr></tbody></table></div><p style=\"color:inherit\">This function is used to quickly receive one 4-9 bit frame, (or part of 10-16 bit frame) with extended information by reading the RXDATAX register directly, without checking the STATUS register for the RXDATAV flag. This can be useful from the RXDATAV interrupt handler, i.e., waiting is superfluous, in order to quickly read the received data.</p><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Because this function does not check whether the RXDATAX register actually holds valid data, it should only be used in situations when it is certain that there is valid data, ensured by some external program routine, e.g., when handling an RXDATAV interrupt. The <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart#usart-rx-ext\" target=\"_blank\" rel=\"\">USART_RxExt()</a> is normally a better choice if the validity of the RXDATAX register is not certain.</p></li><li><p style=\"color:inherit\">Notice that possible parity/stop bits in asynchronous mode are not considered part of specified frame bit length.</p></li></ul><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Data received. </p></li></ul><br><div>Definition at line <code>1075</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>USART_INITASYNC_DEFAULT<span id=\"usart-initasync-default\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-initasync-default\">#</a></span></h3><blockquote>#define USART_INITASYNC_DEFAULT</blockquote><b>Value:</b><div class=\"fragment\"><div class=\"macro\">  {                                                                                                \\</div><div class=\"macro\">    usartEnable,           /* Enable RX/TX when initialization is complete. */                     \\</div><div class=\"macro\">    0,                     /* Use current configured reference clock for configuring baud rate. */ \\</div><div class=\"macro\">    115200,                /* 115200 bits/s. */                                                    \\</div><div class=\"macro\">    usartOVS16,            /* 16x oversampling. */                                                 \\</div><div class=\"macro\">    usartDatabits8,        /* 8 data bits. */                                                      \\</div><div class=\"macro\">    usartNoParity,         /* No parity. */                                                        \\</div><div class=\"macro\">    usartStopbits1,        /* 1 stop bit. */                                                       \\</div><div class=\"macro\">    false,                 /* Do not disable majority vote. */                                     \\</div><div class=\"macro\">    false,                 /* Not USART PRS input mode. */                                         \\</div><div class=\"macro\">    0,                     /* PRS channel 0. */                                                    \\</div><div class=\"macro\">    false,                 /* Auto CS functionality enable/disable switch */                       \\</div><div class=\"macro\">    false,                 /* No CS invert. */                                                     \\</div><div class=\"macro\">    0,                     /* Auto CS Hold cycles. */                                              \\</div><div class=\"macro\">    0,                     /* Auto CS Setup cycles. */                                             \\</div><div class=\"macro\">    usartHwFlowControlNone /* No HW flow control. */                                               \\</div><div class=\"macro\">  }</div></div><p style=\"color:inherit\">Default configuration for USART asynchronous initialization structure. </p><br><div>Definition at line <code>392</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_INITPRSTRIGGER_DEFAULT<span id=\"usart-initprstrigger-default\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-initprstrigger-default\">#</a></span></h3><blockquote>#define USART_INITPRSTRIGGER_DEFAULT</blockquote><b>Value:</b><div class=\"fragment\"><div class=\"macro\">  {                                                             \\</div><div class=\"macro\">    false,             /* Do not enable autoTX triggering. */   \\</div><div class=\"macro\">    false,             /* Do not enable receive triggering. */  \\</div><div class=\"macro\">    false,             /* Do not enable transmit triggering. */ \\</div><div class=\"macro\">    0                  /* Set default channel to zero. */       \\</div><div class=\"macro\">  }</div></div><p style=\"color:inherit\">Default configuration for USART PRS triggering structure. </p><br><div>Definition at line <code>414</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_INITSYNC_DEFAULT<span id=\"usart-initsync-default\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-initsync-default\">#</a></span></h3><blockquote>#define USART_INITSYNC_DEFAULT</blockquote><b>Value:</b><div class=\"fragment\"><div class=\"macro\">  {                                                                                          \\</div><div class=\"macro\">    usartEnable,     /* Enable RX/TX when initialization is complete. */                     \\</div><div class=\"macro\">    0,               /* Use current configured reference clock for configuring baud rate. */ \\</div><div class=\"macro\">    1000000,         /* 1 Mbits/s. */                                                        \\</div><div class=\"macro\">    usartDatabits8,  /* 8 databits. */                                                       \\</div><div class=\"macro\">    true,            /* Master mode. */                                                      \\</div><div class=\"macro\">    false,           /* Send least significant bit first. */                                 \\</div><div class=\"macro\">    usartClockMode0, /* Clock idle low, sample on rising edge. */                            \\</div><div class=\"macro\">    false,           /* Not USART PRS input mode. */                                         \\</div><div class=\"macro\">    0,               /* PRS channel 0. */                                                    \\</div><div class=\"macro\">    false,           /* No AUTOTX mode. */                                                   \\</div><div class=\"macro\">    false,           /* No AUTOCS mode. */                                                   \\</div><div class=\"macro\">    false,           /* No CS invert. */                                                     \\</div><div class=\"macro\">    0,               /* Auto CS Hold cycles. */                                              \\</div><div class=\"macro\">    0                /* Auto CS Setup cycles. */                                             \\</div><div class=\"macro\">  }</div></div><p style=\"color:inherit\">Default configuration for USART sync initialization structure. </p><br><div>Definition at line <code>520</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_INITIRDA_DEFAULT<span id=\"usart-initirda-default\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-initirda-default\">#</a></span></h3><blockquote>#define USART_INITIRDA_DEFAULT</blockquote><b>Value:</b><div class=\"fragment\"><div class=\"macro\">  {                                                                                                  \\</div><div class=\"macro\">    {                                                                                                \\</div><div class=\"macro\">      usartEnable,           /* Enable RX/TX when initialization is complete. */                     \\</div><div class=\"macro\">      0,                     /* Use current configured reference clock for configuring baud rate. */ \\</div><div class=\"macro\">      115200,                /* 115200 bits/s. */                                                    \\</div><div class=\"macro\">      usartOVS16,            /* 16x oversampling. */                                                 \\</div><div class=\"macro\">      usartDatabits8,        /* 8 data bits. */                                                      \\</div><div class=\"macro\">      usartEvenParity,       /* Even parity. */                                                      \\</div><div class=\"macro\">      usartStopbits1,        /* 1 stop bit. */                                                       \\</div><div class=\"macro\">      false,                 /* Do not disable majority vote. */                                     \\</div><div class=\"macro\">      false,                 /* Not USART PRS input mode. */                                         \\</div><div class=\"macro\">      0,                     /* PRS channel 0. */                                                    \\</div><div class=\"macro\">      false,                 /* Auto CS functionality enable/disable switch */                       \\</div><div class=\"macro\">      false,                 /* No CS invert. */                                                     \\</div><div class=\"macro\">      0,                     /* Auto CS Hold cycles */                                               \\</div><div class=\"macro\">      0,                     /* Auto CS Setup cycles */                                              \\</div><div class=\"macro\">      usartHwFlowControlNone /* No HW flow control */                                                \\</div><div class=\"macro\">    },                                                                                               \\</div><div class=\"macro\">    false,            /* Rx invert disabled. */                                                      \\</div><div class=\"macro\">    false,            /* Filtering disabled. */                                                      \\</div><div class=\"macro\">    usartIrDAPwTHREE  /* Pulse width is set to ONE. */                                               \\</div><div class=\"macro\">  }</div></div><p style=\"color:inherit\">Default configuration for IrDA mode initialization structure. </p><br><div>Definition at line <code>640</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div><div><h3>USART_INITI2S_DEFAULT<span id=\"usart-initi2-s-default\" class=\"self-anchor\"><a class=\"perm\" href=\"#usart-initi2-s-default\">#</a></span></h3><blockquote>#define USART_INITI2S_DEFAULT</blockquote><b>Value:</b><div class=\"fragment\"><div class=\"macro\">  {                                                                                            \\</div><div class=\"macro\">    {                                                                                          \\</div><div class=\"macro\">      usartEnableTx,    /* Enable TX when init completed. */                                   \\</div><div class=\"macro\">      0,                /* Use current configured reference clock for configuring baudrate. */ \\</div><div class=\"macro\">      1000000,          /* Baudrate 1M bits/s. */                                              \\</div><div class=\"macro\">      usartDatabits16,  /* 16 databits. */                                                     \\</div><div class=\"macro\">      true,             /* Operate as I2S master. */                                           \\</div><div class=\"macro\">      true,             /* Most significant bit first. */                                      \\</div><div class=\"macro\">      usartClockMode0,  /* Clock idle low, sample on rising edge. */                           \\</div><div class=\"macro\">      false,            /* Don't enable USARTRx via PRS. */                                    \\</div><div class=\"macro\">      usartPrsRxCh0,    /* PRS channel selection (dummy). */                                   \\</div><div class=\"macro\">      false,            /* Disable AUTOTX mode. */                                             \\</div><div class=\"macro\">      false,            /* No AUTOCS mode */                                                   \\</div><div class=\"macro\">      false,            /* No CS invert. */                                                    \\</div><div class=\"macro\">      0,                /* Auto CS Hold cycles */                                              \\</div><div class=\"macro\">      0                 /* Auto CS Setup cycles */                                             \\</div><div class=\"macro\">    },                                                                                         \\</div><div class=\"macro\">    usartI2sFormatW16D16, /* 16-bit word, 16-bit data */                                       \\</div><div class=\"macro\">    true,               /* Delay on I2S data. */                                               \\</div><div class=\"macro\">    false,              /* No DMA split. */                                                    \\</div><div class=\"macro\">    usartI2sJustifyLeft,/* Data is left-justified within the frame */                          \\</div><div class=\"macro\">    false               /* Stereo mode. */                                                     \\</div><div class=\"macro\">  }</div></div><p style=\"color:inherit\">Default configuration for I2S mode initialization structure. </p><br><div>Definition at line <code>738</code> of file <code>platform/emlib/inc/em_usart.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/gecko-platform/4.4.4/platform-emlib-efr32xg21/usart", "status": "success"}