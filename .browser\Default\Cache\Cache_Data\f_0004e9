{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>GATT Client<span id=\"gatt-client\" class=\"self-anchor\"><a class=\"perm\" href=\"#gatt-client\">#</a></span></h1><p style=\"color:inherit\">GATT Client. </p><p style=\"color:inherit\">The commands and events in this class are used to browse and manage attributes in a remote GATT server. </p><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-mtu-exchanged\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_mtu_exchanged</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-service\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_service</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-descriptor\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_descriptor</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-descriptor-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_descriptor_value</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a></p><div class=\"decl-class-section\"><h2>Enumerations<span id=\"enum-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-att-opcode-t\">sl_bt_gatt_att_opcode_t</a> {</div><div class=\"enum\">sl_bt_gatt_read_by_type_request = 0x8</div><div class=\"enum\">sl_bt_gatt_read_by_type_response = 0x9</div><div class=\"enum\">sl_bt_gatt_read_request = 0xa</div><div class=\"enum\">sl_bt_gatt_read_response = 0xb</div><div class=\"enum\">sl_bt_gatt_read_blob_request = 0xc</div><div class=\"enum\">sl_bt_gatt_read_blob_response = 0xd</div><div class=\"enum\">sl_bt_gatt_read_multiple_request = 0xe</div><div class=\"enum\">sl_bt_gatt_read_multiple_response = 0xf</div><div class=\"enum\">sl_bt_gatt_write_request = 0x12</div><div class=\"enum\">sl_bt_gatt_write_response = 0x13</div><div class=\"enum\">sl_bt_gatt_write_command = 0x52</div><div class=\"enum\">sl_bt_gatt_prepare_write_request = 0x16</div><div class=\"enum\">sl_bt_gatt_prepare_write_response = 0x17</div><div class=\"enum\">sl_bt_gatt_execute_write_request = 0x18</div><div class=\"enum\">sl_bt_gatt_execute_write_response = 0x19</div><div class=\"enum\">sl_bt_gatt_handle_value_notification = 0x1b</div><div class=\"enum\">sl_bt_gatt_handle_value_indication = 0x1d</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values indicate which attribute request or response has caused the event. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-client-config-flag-t\">sl_bt_gatt_client_config_flag_t</a> {</div><div class=\"enum\">sl_bt_gatt_disable = 0x0</div><div class=\"enum\">sl_bt_gatt_notification = 0x1</div><div class=\"enum\">sl_bt_gatt_indication = 0x2</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values define whether the client is to receive notifications or indications from a remote GATT server. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">enum</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-execute-write-flag-t\">sl_bt_gatt_execute_write_flag_t</a> {</div><div class=\"enum\">sl_bt_gatt_cancel = 0x0</div><div class=\"enum\">sl_bt_gatt_commit = 0x1</div><div class=\"attributename\">}</div><div class=\"classdescription\"><p style=\"color:inherit\">These values define whether the GATT server is to cancel all queued writes or commit all queued writes to a remote database. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-set-max-mtu\">sl_bt_gatt_set_max_mtu</a>(uint16_t max_mtu, uint16_t *max_mtu_out)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-discover-primary-services\">sl_bt_gatt_discover_primary_services</a>(uint8_t connection)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-discover-primary-services-by-uuid\">sl_bt_gatt_discover_primary_services_by_uuid</a>(uint8_t connection, size_t uuid_len, const uint8_t *uuid)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-find-included-services\">sl_bt_gatt_find_included_services</a>(uint8_t connection, uint32_t service)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-discover-characteristics\">sl_bt_gatt_discover_characteristics</a>(uint8_t connection, uint32_t service)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-discover-characteristics-by-uuid\">sl_bt_gatt_discover_characteristics_by_uuid</a>(uint8_t connection, uint32_t service, size_t uuid_len, const uint8_t *uuid)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-discover-descriptors\">sl_bt_gatt_discover_descriptors</a>(uint8_t connection, uint16_t characteristic)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-discover-characteristic-descriptors\">sl_bt_gatt_discover_characteristic_descriptors</a>(uint8_t connection, uint16_t start, uint16_t end)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-set-characteristic-notification\">sl_bt_gatt_set_characteristic_notification</a>(uint8_t connection, uint16_t characteristic, uint8_t flags)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-send-characteristic-confirmation\">sl_bt_gatt_send_characteristic_confirmation</a>(uint8_t connection)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-read-characteristic-value\">sl_bt_gatt_read_characteristic_value</a>(uint8_t connection, uint16_t characteristic)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-read-characteristic-value-from-offset\">sl_bt_gatt_read_characteristic_value_from_offset</a>(uint8_t connection, uint16_t characteristic, uint16_t offset, uint16_t maxlen)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-read-multiple-characteristic-values\">sl_bt_gatt_read_multiple_characteristic_values</a>(uint8_t connection, size_t characteristic_list_len, const uint8_t *characteristic_list)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-read-characteristic-value-by-uuid\">sl_bt_gatt_read_characteristic_value_by_uuid</a>(uint8_t connection, uint32_t service, size_t uuid_len, const uint8_t *uuid)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-write-characteristic-value\">sl_bt_gatt_write_characteristic_value</a>(uint8_t connection, uint16_t characteristic, size_t value_len, const uint8_t *value)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-write-characteristic-value-without-response\">sl_bt_gatt_write_characteristic_value_without_response</a>(uint8_t connection, uint16_t characteristic, size_t value_len, const uint8_t *value, uint16_t *sent_len)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-prepare-characteristic-value-write\">sl_bt_gatt_prepare_characteristic_value_write</a>(uint8_t connection, uint16_t characteristic, uint16_t offset, size_t value_len, const uint8_t *value, uint16_t *sent_len)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-prepare-characteristic-value-reliable-write\">sl_bt_gatt_prepare_characteristic_value_reliable_write</a>(uint8_t connection, uint16_t characteristic, uint16_t offset, size_t value_len, const uint8_t *value, uint16_t *sent_len)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-execute-characteristic-value-write\">sl_bt_gatt_execute_characteristic_value_write</a>(uint8_t connection, uint8_t flags)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-read-descriptor-value\">sl_bt_gatt_read_descriptor_value</a>(uint8_t connection, uint16_t descriptor)</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-gatt-write-descriptor-value\">sl_bt_gatt_write_descriptor_value</a>(uint8_t connection, uint16_t descriptor, size_t value_len, const uint8_t *value)</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-set-max-mtu-id\">sl_bt_cmd_gatt_set_max_mtu_id</a> 0x00090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-discover-primary-services-id\">sl_bt_cmd_gatt_discover_primary_services_id</a> 0x01090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-discover-primary-services-by-uuid-id\">sl_bt_cmd_gatt_discover_primary_services_by_uuid_id</a> 0x02090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-find-included-services-id\">sl_bt_cmd_gatt_find_included_services_id</a> 0x10090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-discover-characteristics-id\">sl_bt_cmd_gatt_discover_characteristics_id</a> 0x03090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-discover-characteristics-by-uuid-id\">sl_bt_cmd_gatt_discover_characteristics_by_uuid_id</a> 0x04090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-discover-descriptors-id\">sl_bt_cmd_gatt_discover_descriptors_id</a> 0x06090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-discover-characteristic-descriptors-id\">sl_bt_cmd_gatt_discover_characteristic_descriptors_id</a> 0x14090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-set-characteristic-notification-id\">sl_bt_cmd_gatt_set_characteristic_notification_id</a> 0x05090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-send-characteristic-confirmation-id\">sl_bt_cmd_gatt_send_characteristic_confirmation_id</a> 0x0d090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-read-characteristic-value-id\">sl_bt_cmd_gatt_read_characteristic_value_id</a> 0x07090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-read-characteristic-value-from-offset-id\">sl_bt_cmd_gatt_read_characteristic_value_from_offset_id</a> 0x12090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-read-multiple-characteristic-values-id\">sl_bt_cmd_gatt_read_multiple_characteristic_values_id</a> 0x11090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-read-characteristic-value-by-uuid-id\">sl_bt_cmd_gatt_read_characteristic_value_by_uuid_id</a> 0x08090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-write-characteristic-value-id\">sl_bt_cmd_gatt_write_characteristic_value_id</a> 0x09090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-write-characteristic-value-without-response-id\">sl_bt_cmd_gatt_write_characteristic_value_without_response_id</a> 0x0a090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-prepare-characteristic-value-write-id\">sl_bt_cmd_gatt_prepare_characteristic_value_write_id</a> 0x0b090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-prepare-characteristic-value-reliable-write-id\">sl_bt_cmd_gatt_prepare_characteristic_value_reliable_write_id</a> 0x13090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-execute-characteristic-value-write-id\">sl_bt_cmd_gatt_execute_characteristic_value_write_id</a> 0x0c090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-read-descriptor-value-id\">sl_bt_cmd_gatt_read_descriptor_value_id</a> 0x0e090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-cmd-gatt-write-descriptor-value-id\">sl_bt_cmd_gatt_write_descriptor_value_id</a> 0x0f090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-set-max-mtu-id\">sl_bt_rsp_gatt_set_max_mtu_id</a> 0x00090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-discover-primary-services-id\">sl_bt_rsp_gatt_discover_primary_services_id</a> 0x01090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-discover-primary-services-by-uuid-id\">sl_bt_rsp_gatt_discover_primary_services_by_uuid_id</a> 0x02090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-find-included-services-id\">sl_bt_rsp_gatt_find_included_services_id</a> 0x10090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-discover-characteristics-id\">sl_bt_rsp_gatt_discover_characteristics_id</a> 0x03090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-discover-characteristics-by-uuid-id\">sl_bt_rsp_gatt_discover_characteristics_by_uuid_id</a> 0x04090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-discover-descriptors-id\">sl_bt_rsp_gatt_discover_descriptors_id</a> 0x06090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-discover-characteristic-descriptors-id\">sl_bt_rsp_gatt_discover_characteristic_descriptors_id</a> 0x14090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-set-characteristic-notification-id\">sl_bt_rsp_gatt_set_characteristic_notification_id</a> 0x05090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-send-characteristic-confirmation-id\">sl_bt_rsp_gatt_send_characteristic_confirmation_id</a> 0x0d090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-read-characteristic-value-id\">sl_bt_rsp_gatt_read_characteristic_value_id</a> 0x07090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-read-characteristic-value-from-offset-id\">sl_bt_rsp_gatt_read_characteristic_value_from_offset_id</a> 0x12090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-read-multiple-characteristic-values-id\">sl_bt_rsp_gatt_read_multiple_characteristic_values_id</a> 0x11090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-read-characteristic-value-by-uuid-id\">sl_bt_rsp_gatt_read_characteristic_value_by_uuid_id</a> 0x08090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-write-characteristic-value-id\">sl_bt_rsp_gatt_write_characteristic_value_id</a> 0x09090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-write-characteristic-value-without-response-id\">sl_bt_rsp_gatt_write_characteristic_value_without_response_id</a> 0x0a090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-prepare-characteristic-value-write-id\">sl_bt_rsp_gatt_prepare_characteristic_value_write_id</a> 0x0b090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-prepare-characteristic-value-reliable-write-id\">sl_bt_rsp_gatt_prepare_characteristic_value_reliable_write_id</a> 0x13090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-execute-characteristic-value-write-id\">sl_bt_rsp_gatt_execute_characteristic_value_write_id</a> 0x0c090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-read-descriptor-value-id\">sl_bt_rsp_gatt_read_descriptor_value_id</a> 0x0e090020</div><div class=\"classdescription\"></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-bt-rsp-gatt-write-descriptor-value-id\">sl_bt_rsp_gatt_write_descriptor_value_id</a> 0x0f090020</div><div class=\"classdescription\"></div></div></div></div></div><div class=\"def-class-section\"><h2>Enumeration Documentation<span id=\"enum-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#enum-definition\">#</a></span></h2><div><h3>sl_bt_gatt_att_opcode_t<span id=\"sl-bt-gatt-att-opcode-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-att-opcode-t\">#</a></span></h3><blockquote>sl_bt_gatt_att_opcode_t</blockquote><p style=\"color:inherit\">These values indicate which attribute request or response has caused the event. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_gatt_read_by_type_request</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x8) Read by type request </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_read_by_type_response</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x9) Read by type response </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_read_request</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0xa) Read request </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_read_response</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0xb) Read response </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_read_blob_request</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0xc) Read blob request </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_read_blob_response</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0xd) Read blob response </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_read_multiple_request</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0xe) Read multiple request </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_read_multiple_response</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0xf) Read multiple response </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_write_request</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x12) Write request </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_write_response</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x13) Write response </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_write_command</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x52) Write command </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_prepare_write_request</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x16) Prepare write request </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_prepare_write_response</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x17) Prepare write response </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_execute_write_request</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x18) Execute write request </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_execute_write_response</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x19) Execute write response </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_handle_value_notification</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1b) Notification </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_handle_value_indication</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1d) Indication </p></td></tr></tbody></table><br><div>Definition at line <code>7946</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_client_config_flag_t<span id=\"sl-bt-gatt-client-config-flag-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-client-config-flag-t\">#</a></span></h3><blockquote>sl_bt_gatt_client_config_flag_t</blockquote><p style=\"color:inherit\">These values define whether the client is to receive notifications or indications from a remote GATT server. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_gatt_disable</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Disable notifications and indications </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_notification</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Notification </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_indication</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x2) Indication </p></td></tr></tbody></table><br><div>Definition at line <code>7973</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_execute_write_flag_t<span id=\"sl-bt-gatt-execute-write-flag-t\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-execute-write-flag-t\">#</a></span></h3><blockquote>sl_bt_gatt_execute_write_flag_t</blockquote><p style=\"color:inherit\">These values define whether the GATT server is to cancel all queued writes or commit all queued writes to a remote database. </p><table><tbody><tr><th colspan=\"2\">Enumerator</th></tr><tr><td class=\"fieldname\">sl_bt_gatt_cancel</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x0) Cancel all queued writes </p></td></tr><tr><td class=\"fieldname\">sl_bt_gatt_commit</td><td class=\"fielddescription\"><p style=\"color:inherit\">(0x1) Commit all queued writes </p></td></tr></tbody></table><br><div>Definition at line <code>7985</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_bt_gatt_set_max_mtu<span id=\"sl-bt-gatt-set-max-mtu\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-set-max-mtu\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_set_max_mtu (uint16_t max_mtu, uint16_t * max_mtu_out)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">max_mtu</td><td><p style=\"color:inherit\">Maximum size of Message Transfer Units (MTU) allowed</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Range: 23 to 250</p></li></ul><p style=\"color:inherit\">Default: 247 </p></td></tr><tr><td>[out]</td><td class=\"paramname\">max_mtu_out</td><td><p style=\"color:inherit\">The maximum ATT_MTU selected by the system if this command succeeds</p></td></tr></tbody></table></div><p style=\"color:inherit\">Set the maximum size of ATT Message Transfer Units (MTU). Functionality is the same as <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt-server#sl-bt-gatt-server-set-max-mtu\" target=\"_blank\" rel=\"\">sl_bt_gatt_server_set_max_mtu</a> and this setting applies to both GATT client and server. If the given value is too large according to the maximum BGAPI payload size, the system will select the maximum possible value as the maximum ATT_MTU. If the maximum ATT_MTU is larger than 23, the GATT client in the stack will automatically send an MTU exchange request after a Bluetooth connection has been established.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>8223</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_discover_primary_services<span id=\"sl-bt-gatt-discover-primary-services\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-discover-primary-services\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_discover_primary_services (uint8_t connection)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Discover all primary services of a remote GATT database. This command generates a unique gatt_service event for every discovered primary service. Received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that this GATT procedure has successfully completed or failed with an error.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-service\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_service</a> - Discovered service from remote GATT database</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8243</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_discover_primary_services_by_uuid<span id=\"sl-bt-gatt-discover-primary-services-by-uuid\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-discover-primary-services-by-uuid\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_discover_primary_services_by_uuid (uint8_t connection, size_t uuid_len, const uint8_t * uuid)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">uuid_len</td><td><p style=\"color:inherit\">Length of data in <code>uuid</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">uuid</td><td><p style=\"color:inherit\">Service UUID in little endian format</p></td></tr></tbody></table></div><p style=\"color:inherit\">Discover primary services with the specified UUID in a remote GATT database. This command generates unique gatt_service event for every discovered primary service. Received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that this GATT procedure was successfully completed or failed with an error.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-service\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_service</a> - Discovered service from remote GATT database.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8265</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_find_included_services<span id=\"sl-bt-gatt-find-included-services\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-find-included-services\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_find_included_services (uint8_t connection, uint32_t service)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">service</td><td><p style=\"color:inherit\">GATT service handle. This value is normally received from the gatt_service event.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Find the services that are included by a service in a remote GATT database. This command generates a unique gatt_service event for each included service. The received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that this GATT procedure was successfully completed or failed with an error.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-service\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_service</a> - Discovered service from remote GATT database.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8289</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_discover_characteristics<span id=\"sl-bt-gatt-discover-characteristics\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-discover-characteristics\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_discover_characteristics (uint8_t connection, uint32_t service)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">service</td><td><p style=\"color:inherit\">GATT service handle. This value is normally received from the gatt_service event.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Discover all characteristics of a GATT service from a remote GATT database. This command generates a unique gatt_characteristic event for every discovered characteristic. Received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that this GATT procedure was successfully completed or failed with an error.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic</a> - Discovered characteristic from remote GATT database.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8313</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_discover_characteristics_by_uuid<span id=\"sl-bt-gatt-discover-characteristics-by-uuid\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-discover-characteristics-by-uuid\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_discover_characteristics_by_uuid (uint8_t connection, uint32_t service, size_t uuid_len, const uint8_t * uuid)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">service</td><td><p style=\"color:inherit\">GATT service handle. This value is normally received from the gatt_service event. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">uuid_len</td><td><p style=\"color:inherit\">Length of data in <code>uuid</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">uuid</td><td><p style=\"color:inherit\">Characteristic UUID in little endian format</p></td></tr></tbody></table></div><p style=\"color:inherit\">Discover all characteristics of a GATT service in a remote GATT database having the specified UUID. This command generates a unique gatt_characteristic event for every discovered characteristic having the specified UUID. Received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that this GATT procedure was successfully completed or failed with an error.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic</a> - Discovered characteristic from remote GATT database.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8340</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_discover_descriptors<span id=\"sl-bt-gatt-discover-descriptors\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-discover-descriptors\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_discover_descriptors (uint8_t connection, uint16_t characteristic)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">GATT characteristic handle. This value is normally received from the gatt_characteristic event.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Discover all descriptors in a remote GATT database starting from handle. It generates a unique gatt_descriptor event for every discovered descriptor. Received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that this GATT procedure has successfully completed or failed with an error.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-descriptor\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_descriptor</a> - Discovered descriptor from remote GATT database.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8365</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_discover_characteristic_descriptors<span id=\"sl-bt-gatt-discover-characteristic-descriptors\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-discover-characteristic-descriptors\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_discover_characteristic_descriptors (uint8_t connection, uint16_t start, uint16_t end)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">start</td><td><p style=\"color:inherit\">GATT characteristic handle. This value is normally received from the gatt_characteristic event. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">end</td><td><p style=\"color:inherit\">GATT characteristic handle. This value is normally received from the gatt_characteristic event.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Discover all descriptors of a GATT characteristic in a remote GATT database. It generates a unique gatt_descriptor event for every discovered descriptor. Received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that this GATT procedure has successfully completed or failed with an error.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-descriptor\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_descriptor</a> - Discovered descriptor from remote GATT database.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8390</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_set_characteristic_notification<span id=\"sl-bt-gatt-set-characteristic-notification\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-set-characteristic-notification\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_set_characteristic_notification (uint8_t connection, uint16_t characteristic, uint8_t flags)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">GATT characteristic handle. This value is normally received from the gatt_characteristic event. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-client-config-flag-t\" target=\"_blank\" rel=\"\">sl_bt_gatt_client_config_flag_t</a>. Characteristic client configuration flags. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gatt_disable (0x0):</strong> Disable notifications and indications</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gatt_notification (0x1):</strong> Notification</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gatt_indication (0x2):</strong> Indication</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Enable or disable the notifications and indications sent from a remote GATT server. This procedure discovers a characteristic client configuration descriptor and writes the related configuration flags to a remote GATT database. A received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that this GATT procedure was successfully completed or that it failed with an error.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a> - If an indication or notification has been enabled for a characteristic, this event is triggered whenever an indication or notification is sent by the remote GATT server. The triggering conditions of the GATT server are defined by an upper level, for example by a profile. <strong>As a result, it is possible that no values are ever received, or that it may take time, depending on how the server is configured.</strong></p></li></ul><br><div>Definition at line <code>8426</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_send_characteristic_confirmation<span id=\"sl-bt-gatt-send-characteristic-confirmation\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-send-characteristic-confirmation\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_send_characteristic_confirmation (uint8_t connection)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Send a confirmation to a remote GATT server after receiving a characteristic indication. The <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a> event carries the <code>att_opcode</code> containing <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-handle-value-indication\" target=\"_blank\" rel=\"\">sl_bt_gatt_handle_value_indication</a> (0x1d), which reveals that an indication has been received and must be confirmed with this command. The confirmation needs to be sent within 30 seconds, otherwise further GATT transactions are not allowed by the remote side.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>8444</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_read_characteristic_value<span id=\"sl-bt-gatt-read-characteristic-value\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-read-characteristic-value\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_read_characteristic_value (uint8_t connection, uint16_t characteristic)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">GATT characteristic handle. This value is normally received from the gatt_characteristic event.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Read the value of a characteristic from a remote GATT database. A single <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a> is generated if the characteristic value fits in one ATT PDU. Otherwise, more than one <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a> event is generated because the firmware will automatically use the Read Long Characteristic Values procedure. A received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that all data was read successfully or that an error response was received.</p><p style=\"color:inherit\">Note that the GATT client does not verify if the requested attribute is a characteristic value. Therefore, before calling this command, ensure that the attribute handle is for a characteristic value, for example, by performing characteristic discovery.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a> - Contains the data of a characteristic sent by the GATT Server.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8474</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_read_characteristic_value_from_offset<span id=\"sl-bt-gatt-read-characteristic-value-from-offset\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-read-characteristic-value-from-offset\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_read_characteristic_value_from_offset (uint8_t connection, uint16_t characteristic, uint16_t offset, uint16_t maxlen)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">GATT characteristic handle. This value is normally received from the gatt_characteristic event. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">offset</td><td><p style=\"color:inherit\">Offset of the characteristic value </p></td></tr><tr><td>[in]</td><td class=\"paramname\">maxlen</td><td><p style=\"color:inherit\">Maximum bytes to read. If this parameter is 0, all characteristic values starting at a given offset will be read.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Read a partial characteristic value with a specified offset and maximum length from a remote GATT database. It is equivalent to <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-read-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_gatt_read_characteristic_value</a> if both the offset and maximum length parameters are 0. A single <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a> event is generated if the value to read fits in one ATT PDU. Otherwise, more than one <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a> events are generated because the firmware will automatically use the Read Long Characteristic Values procedure. A received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that all data was read successfully or that an error response was received.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a> - Contains data of a characteristic sent by the GATT Server.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8505</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_read_multiple_characteristic_values<span id=\"sl-bt-gatt-read-multiple-characteristic-values\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-read-multiple-characteristic-values\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_read_multiple_characteristic_values (uint8_t connection, size_t characteristic_list_len, const uint8_t * characteristic_list)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic_list_len</td><td><p style=\"color:inherit\">Length of data in <code>characteristic_list</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic_list</td><td><p style=\"color:inherit\">List of uint16 characteristic handles each in little endian format.</p></td></tr></tbody></table></div><p style=\"color:inherit\">Read values of multiple characteristics from a remote GATT database at once. The GATT server returns values in one ATT PDU as the response. If the total set of values is greater than (ATT_MTU - 1) bytes in length, only the first (ATT_MTU - 1) bytes are included. A single <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a> event is generated, in which the characteristic is set to 0 and data in the value parameter is a concatenation of characteristic values in the order they were requested. The received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates either that this GATT procedure was successfully completed or failed with an error.</p><p style=\"color:inherit\">Use this command only for characteristics values that have a known fixed size, except the last one that could have variable length.</p><p style=\"color:inherit\">When the remote GATT server is from Silicon Labs Bluetooth stack, the server returns ATT Invalid PDU (0x04) if this command only reads one characteristic value. The server returns ATT Application Error 0x80 if this command reads the value of a user-type characteristic.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a> - A concatenation of characteristic values in the order they were requested</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was either successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8544</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_read_characteristic_value_by_uuid<span id=\"sl-bt-gatt-read-characteristic-value-by-uuid\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-read-characteristic-value-by-uuid\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_read_characteristic_value_by_uuid (uint8_t connection, uint32_t service, size_t uuid_len, const uint8_t * uuid)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">service</td><td><p style=\"color:inherit\">GATT service handle. This value is normally received from the gatt_service event. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">uuid_len</td><td><p style=\"color:inherit\">Length of data in <code>uuid</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">uuid</td><td><p style=\"color:inherit\">Characteristic UUID in little endian format</p></td></tr></tbody></table></div><p style=\"color:inherit\">Read characteristic values of a service from a remote GATT database by giving the UUID of the characteristic and the handle of the service containing this characteristic. If multiple characteristic values are received in one ATT PDU, one <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a> event is generated for each value. If the first characteristic value does not fit in one ATT PDU, the firmware automatically uses the Read Long Characteristic Values procedure and generate more <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a> events until the value has been completely read. A received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that all data was read successfully or that an error response was received.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_characteristic_value</a> - Contains the data of a characteristic sent by the GATT Server.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8576</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_write_characteristic_value<span id=\"sl-bt-gatt-write-characteristic-value\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-write-characteristic-value\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_write_characteristic_value (uint8_t connection, uint16_t characteristic, size_t value_len, const uint8_t * value)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">GATT characteristic handle. This value is normally received from the gatt_characteristic event. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">value_len</td><td><p style=\"color:inherit\">Length of data in <code>value</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">value</td><td><p style=\"color:inherit\">Characteristic value</p></td></tr></tbody></table></div><p style=\"color:inherit\">Write the value of a characteristic in a remote GATT database. If the value length is greater than (ATT_MTU - 3) and does not fit in one ATT PDU, \"write\nlong\" GATT procedure is used automatically. Received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that all data was written successfully or that an error response was received.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8602</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_write_characteristic_value_without_response<span id=\"sl-bt-gatt-write-characteristic-value-without-response\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-write-characteristic-value-without-response\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_write_characteristic_value_without_response (uint8_t connection, uint16_t characteristic, size_t value_len, const uint8_t * value, uint16_t * sent_len)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">GATT characteristic handle. This value is normally received from the gatt_characteristic event. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">value_len</td><td><p style=\"color:inherit\">Length of data in <code>value</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">value</td><td><p style=\"color:inherit\">Characteristic value </p></td></tr><tr><td>[out]</td><td class=\"paramname\">sent_len</td><td><p style=\"color:inherit\">The length of data sent to the remote GATT server</p></td></tr></tbody></table></div><p style=\"color:inherit\">Write the value of a characteristic in a remote GATT server. It does not generate an event. All failures on the server are ignored silently. For example, if an error is generated in the remote GATT server and the given value is not written into the database, no error message will be reported to the local GATT client. Note that this command can't be used to write long values. At most ATT_MTU - 3 number of bytes can be sent once.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise. </p></li></ul><br><div>Definition at line <code>8626</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_prepare_characteristic_value_write<span id=\"sl-bt-gatt-prepare-characteristic-value-write\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-prepare-characteristic-value-write\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_prepare_characteristic_value_write (uint8_t connection, uint16_t characteristic, uint16_t offset, size_t value_len, const uint8_t * value, uint16_t * sent_len)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">GATT characteristic handle. This value is normally received from the gatt_characteristic event. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">offset</td><td><p style=\"color:inherit\">Offset of the characteristic value </p></td></tr><tr><td>[in]</td><td class=\"paramname\">value_len</td><td><p style=\"color:inherit\">Length of data in <code>value</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">value</td><td><p style=\"color:inherit\">Value to write into the specified characteristic of the remote GATT database </p></td></tr><tr><td>[out]</td><td class=\"paramname\">sent_len</td><td><p style=\"color:inherit\">The length of data sent to the remote GATT server</p></td></tr></tbody></table></div><p style=\"color:inherit\">Add a characteristic value to the write queue of a remote GATT server. It can be used when long attributes need to be written or a set of values needs to be written atomically. At most ATT_MTU - 5 number of bytes can be sent at one time. Writes are executed or canceled with the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-execute-characteristic-value-write\" target=\"_blank\" rel=\"\">sl_bt_gatt_execute_characteristic_value_write</a> command. Whether the writes succeed or not is indicated in the response of the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-execute-characteristic-value-write\" target=\"_blank\" rel=\"\">sl_bt_gatt_execute_characteristic_value_write</a> command.</p><p style=\"color:inherit\">In all use cases where the amount of data to transfer fits into the BGAPI payload, use the command <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-write-characteristic-value\" target=\"_blank\" rel=\"\">sl_bt_gatt_write_characteristic_value</a> to write long values because it transparently performs the prepare_write and execute_write commands.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8663</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_prepare_characteristic_value_reliable_write<span id=\"sl-bt-gatt-prepare-characteristic-value-reliable-write\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-prepare-characteristic-value-reliable-write\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_prepare_characteristic_value_reliable_write (uint8_t connection, uint16_t characteristic, uint16_t offset, size_t value_len, const uint8_t * value, uint16_t * sent_len)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">characteristic</td><td><p style=\"color:inherit\">GATT characteristic handle. This value is normally received from the gatt_characteristic event. </p></td></tr><tr><td>[in]</td><td class=\"paramname\">offset</td><td><p style=\"color:inherit\">Offset of the characteristic value </p></td></tr><tr><td>[in]</td><td class=\"paramname\">value_len</td><td><p style=\"color:inherit\">Length of data in <code>value</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">value</td><td><p style=\"color:inherit\">Value to write into the specified characteristic of the remote GATT database </p></td></tr><tr><td>[out]</td><td class=\"paramname\">sent_len</td><td><p style=\"color:inherit\">The length of data sent to the remote GATT server</p></td></tr></tbody></table></div><p style=\"color:inherit\">Add a characteristic value to the write queue of a remote GATT server and verify whether the value was correctly received by the server. Received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that this GATT procedure was successfully completed or failed with an error. Specifically, error code 0x0194 (data_corrupted) will be returned if the value received from the GATT server's response fails to pass the reliable write verification. At most, ATT_MTU - 5 number of bytes can be sent at one time. Writes are executed or canceled with the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-execute-characteristic-value-write\" target=\"_blank\" rel=\"\">sl_bt_gatt_execute_characteristic_value_write</a> command. Whether the writes succeed or not is indicated in the response of the <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-execute-characteristic-value-write\" target=\"_blank\" rel=\"\">sl_bt_gatt_execute_characteristic_value_write</a> command.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8699</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_execute_characteristic_value_write<span id=\"sl-bt-gatt-execute-characteristic-value-write\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-execute-characteristic-value-write\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_execute_characteristic_value_write (uint8_t connection, uint8_t flags)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">flags</td><td><p style=\"color:inherit\">Enum <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-execute-write-flag-t\" target=\"_blank\" rel=\"\">sl_bt_gatt_execute_write_flag_t</a>. Execute write flag. Values:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><strong>sl_bt_gatt_cancel (0x0):</strong> Cancel all queued writes</p></li><li><p style=\"color:inherit\"><strong>sl_bt_gatt_commit (0x1):</strong> Commit all queued writes</p></li></ul></td></tr></tbody></table></div><p style=\"color:inherit\">Commit or cancel previously queued writes to a long characteristic of a remote GATT server. Writes are sent to the queue with <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt#sl-bt-gatt-prepare-characteristic-value-write\" target=\"_blank\" rel=\"\">sl_bt_gatt_prepare_characteristic_value_write</a> command. Content, offset, and length of queued values are validated by this procedure. A received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that all data was written successfully or that an error response was received.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8728</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_read_descriptor_value<span id=\"sl-bt-gatt-read-descriptor-value\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-read-descriptor-value\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_read_descriptor_value (uint8_t connection, uint16_t descriptor)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">descriptor</td><td><p style=\"color:inherit\">GATT characteristic descriptor handle</p></td></tr></tbody></table></div><p style=\"color:inherit\">Read the descriptor value of a characteristic in a remote GATT database. A single <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-descriptor-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_descriptor_value</a> event is generated if the descriptor value fits in one ATT PDU. Otherwise, more than one <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-descriptor-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_descriptor_value</a> events are generated because the firmware automatically uses the Read Long Characteristic Values procedure. A received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates that all data was read successfully or that an error response was received.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-descriptor-value\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_descriptor_value</a> - Descriptor value received from the remote GATT server.</p></li><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8753</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_gatt_write_descriptor_value<span id=\"sl-bt-gatt-write-descriptor-value\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-gatt-write-descriptor-value\">#</a></span></h3><blockquote>sl_status_t sl_bt_gatt_write_descriptor_value (uint8_t connection, uint16_t descriptor, size_t value_len, const uint8_t * value)</blockquote><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">connection</td><td><p style=\"color:inherit\">Connection handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">descriptor</td><td><p style=\"color:inherit\">GATT characteristic descriptor handle </p></td></tr><tr><td>[in]</td><td class=\"paramname\">value_len</td><td><p style=\"color:inherit\">Length of data in <code>value</code></p></td></tr><tr><td>[in]</td><td class=\"paramname\">value</td><td><p style=\"color:inherit\">Descriptor value</p></td></tr></tbody></table></div><p style=\"color:inherit\">Write the value of a characteristic descriptor in a remote GATT database. If the value length is greater than ATT_MTU - 3 and does not fit in one ATT PDU, \"write long\" GATT procedure is used automatically. Received <a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> event indicates either that all data was written successfully or that an error response was received.</p><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK if successful. Error code otherwise.</p></li></ul><p style=\"color:inherit\"><strong>Events</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-evt-gatt-procedure-completed\" target=\"_blank\" rel=\"\">sl_bt_evt_gatt_procedure_completed</a> - Procedure was successfully completed or failed with an error. </p></li></ul><br><div>Definition at line <code>8776</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>sl_bt_cmd_gatt_set_max_mtu_id<span id=\"sl-bt-cmd-gatt-set-max-mtu-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-set-max-mtu-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_set_max_mtu_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00090020</pre><br><div>Definition at line <code>7899</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_discover_primary_services_id<span id=\"sl-bt-cmd-gatt-discover-primary-services-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-discover-primary-services-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_discover_primary_services_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01090020</pre><br><div>Definition at line <code>7900</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_discover_primary_services_by_uuid_id<span id=\"sl-bt-cmd-gatt-discover-primary-services-by-uuid-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-discover-primary-services-by-uuid-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_discover_primary_services_by_uuid_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02090020</pre><br><div>Definition at line <code>7901</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_find_included_services_id<span id=\"sl-bt-cmd-gatt-find-included-services-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-find-included-services-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_find_included_services_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x10090020</pre><br><div>Definition at line <code>7902</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_discover_characteristics_id<span id=\"sl-bt-cmd-gatt-discover-characteristics-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-discover-characteristics-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_discover_characteristics_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03090020</pre><br><div>Definition at line <code>7903</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_discover_characteristics_by_uuid_id<span id=\"sl-bt-cmd-gatt-discover-characteristics-by-uuid-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-discover-characteristics-by-uuid-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_discover_characteristics_by_uuid_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x04090020</pre><br><div>Definition at line <code>7904</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_discover_descriptors_id<span id=\"sl-bt-cmd-gatt-discover-descriptors-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-discover-descriptors-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_discover_descriptors_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x06090020</pre><br><div>Definition at line <code>7905</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_discover_characteristic_descriptors_id<span id=\"sl-bt-cmd-gatt-discover-characteristic-descriptors-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-discover-characteristic-descriptors-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_discover_characteristic_descriptors_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x14090020</pre><br><div>Definition at line <code>7906</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_set_characteristic_notification_id<span id=\"sl-bt-cmd-gatt-set-characteristic-notification-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-set-characteristic-notification-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_set_characteristic_notification_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x05090020</pre><br><div>Definition at line <code>7907</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_send_characteristic_confirmation_id<span id=\"sl-bt-cmd-gatt-send-characteristic-confirmation-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-send-characteristic-confirmation-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_send_characteristic_confirmation_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0d090020</pre><br><div>Definition at line <code>7908</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_read_characteristic_value_id<span id=\"sl-bt-cmd-gatt-read-characteristic-value-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-read-characteristic-value-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_read_characteristic_value_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x07090020</pre><br><div>Definition at line <code>7909</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_read_characteristic_value_from_offset_id<span id=\"sl-bt-cmd-gatt-read-characteristic-value-from-offset-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-read-characteristic-value-from-offset-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_read_characteristic_value_from_offset_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x12090020</pre><br><div>Definition at line <code>7910</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_read_multiple_characteristic_values_id<span id=\"sl-bt-cmd-gatt-read-multiple-characteristic-values-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-read-multiple-characteristic-values-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_read_multiple_characteristic_values_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x11090020</pre><br><div>Definition at line <code>7911</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_read_characteristic_value_by_uuid_id<span id=\"sl-bt-cmd-gatt-read-characteristic-value-by-uuid-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-read-characteristic-value-by-uuid-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_read_characteristic_value_by_uuid_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x08090020</pre><br><div>Definition at line <code>7912</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_write_characteristic_value_id<span id=\"sl-bt-cmd-gatt-write-characteristic-value-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-write-characteristic-value-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_write_characteristic_value_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x09090020</pre><br><div>Definition at line <code>7913</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_write_characteristic_value_without_response_id<span id=\"sl-bt-cmd-gatt-write-characteristic-value-without-response-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-write-characteristic-value-without-response-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_write_characteristic_value_without_response_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0a090020</pre><br><div>Definition at line <code>7914</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_prepare_characteristic_value_write_id<span id=\"sl-bt-cmd-gatt-prepare-characteristic-value-write-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-prepare-characteristic-value-write-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_prepare_characteristic_value_write_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0b090020</pre><br><div>Definition at line <code>7915</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_prepare_characteristic_value_reliable_write_id<span id=\"sl-bt-cmd-gatt-prepare-characteristic-value-reliable-write-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-prepare-characteristic-value-reliable-write-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_prepare_characteristic_value_reliable_write_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x13090020</pre><br><div>Definition at line <code>7916</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_execute_characteristic_value_write_id<span id=\"sl-bt-cmd-gatt-execute-characteristic-value-write-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-execute-characteristic-value-write-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_execute_characteristic_value_write_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0c090020</pre><br><div>Definition at line <code>7917</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_read_descriptor_value_id<span id=\"sl-bt-cmd-gatt-read-descriptor-value-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-read-descriptor-value-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_read_descriptor_value_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0e090020</pre><br><div>Definition at line <code>7918</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_cmd_gatt_write_descriptor_value_id<span id=\"sl-bt-cmd-gatt-write-descriptor-value-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-cmd-gatt-write-descriptor-value-id\">#</a></span></h3><blockquote>#define sl_bt_cmd_gatt_write_descriptor_value_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0f090020</pre><br><div>Definition at line <code>7919</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_set_max_mtu_id<span id=\"sl-bt-rsp-gatt-set-max-mtu-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-set-max-mtu-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_set_max_mtu_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x00090020</pre><br><div>Definition at line <code>7920</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_discover_primary_services_id<span id=\"sl-bt-rsp-gatt-discover-primary-services-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-discover-primary-services-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_discover_primary_services_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x01090020</pre><br><div>Definition at line <code>7921</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_discover_primary_services_by_uuid_id<span id=\"sl-bt-rsp-gatt-discover-primary-services-by-uuid-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-discover-primary-services-by-uuid-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_discover_primary_services_by_uuid_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x02090020</pre><br><div>Definition at line <code>7922</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_find_included_services_id<span id=\"sl-bt-rsp-gatt-find-included-services-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-find-included-services-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_find_included_services_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x10090020</pre><br><div>Definition at line <code>7923</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_discover_characteristics_id<span id=\"sl-bt-rsp-gatt-discover-characteristics-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-discover-characteristics-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_discover_characteristics_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x03090020</pre><br><div>Definition at line <code>7924</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_discover_characteristics_by_uuid_id<span id=\"sl-bt-rsp-gatt-discover-characteristics-by-uuid-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-discover-characteristics-by-uuid-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_discover_characteristics_by_uuid_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x04090020</pre><br><div>Definition at line <code>7925</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_discover_descriptors_id<span id=\"sl-bt-rsp-gatt-discover-descriptors-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-discover-descriptors-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_discover_descriptors_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x06090020</pre><br><div>Definition at line <code>7926</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_discover_characteristic_descriptors_id<span id=\"sl-bt-rsp-gatt-discover-characteristic-descriptors-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-discover-characteristic-descriptors-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_discover_characteristic_descriptors_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x14090020</pre><br><div>Definition at line <code>7927</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_set_characteristic_notification_id<span id=\"sl-bt-rsp-gatt-set-characteristic-notification-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-set-characteristic-notification-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_set_characteristic_notification_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x05090020</pre><br><div>Definition at line <code>7928</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_send_characteristic_confirmation_id<span id=\"sl-bt-rsp-gatt-send-characteristic-confirmation-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-send-characteristic-confirmation-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_send_characteristic_confirmation_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0d090020</pre><br><div>Definition at line <code>7929</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_read_characteristic_value_id<span id=\"sl-bt-rsp-gatt-read-characteristic-value-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-read-characteristic-value-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_read_characteristic_value_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x07090020</pre><br><div>Definition at line <code>7930</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_read_characteristic_value_from_offset_id<span id=\"sl-bt-rsp-gatt-read-characteristic-value-from-offset-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-read-characteristic-value-from-offset-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_read_characteristic_value_from_offset_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x12090020</pre><br><div>Definition at line <code>7931</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_read_multiple_characteristic_values_id<span id=\"sl-bt-rsp-gatt-read-multiple-characteristic-values-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-read-multiple-characteristic-values-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_read_multiple_characteristic_values_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x11090020</pre><br><div>Definition at line <code>7932</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_read_characteristic_value_by_uuid_id<span id=\"sl-bt-rsp-gatt-read-characteristic-value-by-uuid-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-read-characteristic-value-by-uuid-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_read_characteristic_value_by_uuid_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x08090020</pre><br><div>Definition at line <code>7933</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_write_characteristic_value_id<span id=\"sl-bt-rsp-gatt-write-characteristic-value-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-write-characteristic-value-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_write_characteristic_value_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x09090020</pre><br><div>Definition at line <code>7934</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_write_characteristic_value_without_response_id<span id=\"sl-bt-rsp-gatt-write-characteristic-value-without-response-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-write-characteristic-value-without-response-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_write_characteristic_value_without_response_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0a090020</pre><br><div>Definition at line <code>7935</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_prepare_characteristic_value_write_id<span id=\"sl-bt-rsp-gatt-prepare-characteristic-value-write-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-prepare-characteristic-value-write-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_prepare_characteristic_value_write_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0b090020</pre><br><div>Definition at line <code>7936</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_prepare_characteristic_value_reliable_write_id<span id=\"sl-bt-rsp-gatt-prepare-characteristic-value-reliable-write-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-prepare-characteristic-value-reliable-write-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_prepare_characteristic_value_reliable_write_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x13090020</pre><br><div>Definition at line <code>7937</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_execute_characteristic_value_write_id<span id=\"sl-bt-rsp-gatt-execute-characteristic-value-write-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-execute-characteristic-value-write-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_execute_characteristic_value_write_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0c090020</pre><br><div>Definition at line <code>7938</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_read_descriptor_value_id<span id=\"sl-bt-rsp-gatt-read-descriptor-value-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-read-descriptor-value-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_read_descriptor_value_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0e090020</pre><br><div>Definition at line <code>7939</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div><div><h3>sl_bt_rsp_gatt_write_descriptor_value_id<span id=\"sl-bt-rsp-gatt-write-descriptor-value-id\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-bt-rsp-gatt-write-descriptor-value-id\">#</a></span></h3><blockquote>#define sl_bt_rsp_gatt_write_descriptor_value_id</blockquote><b>Value:</b><pre class=\"macroshort\">0x0f090020</pre><br><div>Definition at line <code>7940</code> of file <code>/mnt/raid/workspaces/ws.pY3F1RWXq/overlay/gsdk/protocol/bluetooth/build_release/bt_api/sw/bgapi/inc/sl_bt_api.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/bluetooth/7.1.2/bluetooth-stack-api/sl-bt-gatt", "status": "success"}