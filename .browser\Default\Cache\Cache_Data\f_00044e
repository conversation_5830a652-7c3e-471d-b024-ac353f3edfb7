{"html": "<article><div class=\"content-top\"></div><div class=\"textblock\"><h1>Simple RGB PWM LED Driver<span id=\"simple-rgb-pwm-led-driver\" class=\"self-anchor\"><a class=\"perm\" href=\"#simple-rgb-pwm-led-driver\">#</a></span></h1><p style=\"color:inherit\">Simple Red/Green/Blue PWM LED Driver. </p><p style=\"color:inherit\"><br></p><h2>Introduction<span id=\"introduction\" class=\"self-anchor\"><a class=\"perm\" href=\"#introduction\">#</a></span></h2><p style=\"color:inherit\">The Simple RGB PWM LED Driver is a module for the LED driver that provides functionality for controlling Red/Green/Blue LEDs that are driven by PWM.</p><p style=\"color:inherit\"><br></p><h2>RGB PWM LED Configuration<span id=\"rgb-pwm-led-configuration\" class=\"self-anchor\"><a class=\"perm\" href=\"#rgb-pwm-led-configuration\">#</a></span></h2><p style=\"color:inherit\">RGB PWM LEDs use the <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-led-t\" target=\"_blank\" rel=\"\">sl_led_t</a> struct, and their own structs <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-simple-rgb-pwm-led-context-t\" target=\"_blank\" rel=\"\">sl_simple_rgb_pwm_led_context_t</a> and <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-led-rgb-pwm-t\" target=\"_blank\" rel=\"\">sl_led_rgb_pwm_t</a>. These are automatically generated into the following files, as well as instance specific headers with macro definitions in them. The samples below are for a single instance called \"inst0\".</p><pre class=\"language-clike\"><code class=\"language-clike\"><span class=\"token comment\">// sl_simple_rgb_pwm_led_instances.c</span>\n\n#include <span class=\"token string\">\"em_gpio.h\"</span>\n#include <span class=\"token string\">\"sl_simple_rgb_pwm_led.h\"</span>\n\n#include <span class=\"token string\">\"sl_simple_rgb_pwm_led_inst0_config.h\"</span>\n\n\n\nsl_led_pwm_t red_inst0 <span class=\"token operator\">=</span> <span class=\"token punctuation\">{</span>\n  <span class=\"token punctuation\">.</span>port <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_PORT<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>pin <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_PIN<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>polarity <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_POLARITY<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>channel <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_CHANNEL<span class=\"token punctuation\">,</span>\n#<span class=\"token keyword\">if</span> <span class=\"token function\">defined</span><span class=\"token punctuation\">(</span>SL_SIMPLE_RGB_PWM_LED_INST0_RED_LOC<span class=\"token punctuation\">)</span>\n  <span class=\"token punctuation\">.</span>location <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_LOC<span class=\"token punctuation\">,</span>\n#endif\n<span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\n\nsl_led_pwm_t green_inst0 <span class=\"token operator\">=</span> <span class=\"token punctuation\">{</span>\n  <span class=\"token punctuation\">.</span>port <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_PORT<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>pin <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_PIN<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>polarity <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_POLARITY<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>channel <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_CHANNEL<span class=\"token punctuation\">,</span>\n#<span class=\"token keyword\">if</span> <span class=\"token function\">defined</span><span class=\"token punctuation\">(</span>SL_SIMPLE_RGB_PWM_LED_INST0_RED_LOC<span class=\"token punctuation\">)</span>\n  <span class=\"token punctuation\">.</span>location <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_LOC<span class=\"token punctuation\">,</span>\n#endif\n<span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\n\nsl_led_pwm_t blue_inst0 <span class=\"token operator\">=</span> <span class=\"token punctuation\">{</span>\n  <span class=\"token punctuation\">.</span>port <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_PORT<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>pin <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_PIN<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>polarity <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_POLARITY<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>channel <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_CHANNEL<span class=\"token punctuation\">,</span>\n#<span class=\"token keyword\">if</span> <span class=\"token function\">defined</span><span class=\"token punctuation\">(</span>SL_SIMPLE_RGB_PWM_LED_INST0_RED_LOC<span class=\"token punctuation\">)</span>\n  <span class=\"token punctuation\">.</span>location <span class=\"token operator\">=</span> SIMPLE_RGB_PWM_LED_INST0_LOC<span class=\"token punctuation\">,</span>\n#endif\n<span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\n\nsl_simple_rgb_pwm_led_context_t simple_rgb_pwm_inst0_context <span class=\"token operator\">=</span> <span class=\"token punctuation\">{</span>\n  <span class=\"token punctuation\">.</span>red <span class=\"token operator\">=</span> <span class=\"token operator\">&amp;</span>red_inst0<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>green <span class=\"token operator\">=</span> <span class=\"token operator\">&amp;</span>green_inst0<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>blue <span class=\"token operator\">=</span> <span class=\"token operator\">&amp;</span>blue_inst0<span class=\"token punctuation\">,</span>\n\n  <span class=\"token punctuation\">.</span>timer <span class=\"token operator\">=</span> SL_SIMPLE_RGB_PWM_LED_INST0_PERIPHERAL<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>frequency <span class=\"token operator\">=</span> SL_SIMPLE_RGB_PWM_LED_INST0_FREQUENCY<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>resolution <span class=\"token operator\">=</span> SL_SIMPLE_RGB_PWM_LED_INST0_RESOLUTION<span class=\"token punctuation\">,</span>\n<span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\n\nconst sl_led_rgb_pwm_t sl_inst0 <span class=\"token operator\">=</span> <span class=\"token punctuation\">{</span>\n  <span class=\"token punctuation\">.</span>led_common<span class=\"token punctuation\">.</span>context <span class=\"token operator\">=</span> <span class=\"token operator\">&amp;</span>simple_rgb_pwm_inst0_context<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>led_common<span class=\"token punctuation\">.</span>init <span class=\"token operator\">=</span> sl_simple_rgb_pwm_led_init<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>led_common<span class=\"token punctuation\">.</span>turn_on <span class=\"token operator\">=</span> sl_simple_rgb_pwm_led_turn_on<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>led_common<span class=\"token punctuation\">.</span>turn_off <span class=\"token operator\">=</span> sl_simple_rgb_pwm_led_turn_off<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>led_common<span class=\"token punctuation\">.</span>toggle <span class=\"token operator\">=</span> sl_simple_rgb_pwm_led_toggle<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>led_common<span class=\"token punctuation\">.</span>get_state <span class=\"token operator\">=</span> sl_simple_rgb_pwm_led_get_state<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>set_rgb_color <span class=\"token operator\">=</span> sl_simple_rgb_pwm_led_set_color<span class=\"token punctuation\">,</span>\n  <span class=\"token punctuation\">.</span>get_rgb_color <span class=\"token operator\">=</span> sl_simple_rgb_pwm_led_get_color<span class=\"token punctuation\">,</span>\n<span class=\"token punctuation\">}</span><span class=\"token punctuation\">;</span>\n\n\n\nvoid <span class=\"token function\">sl_simple_rgb_pwm_led_init_instances</span><span class=\"token punctuation\">(</span>void<span class=\"token punctuation\">)</span>\n<span class=\"token punctuation\">{</span>\n\n  <span class=\"token function\">sl_led_init</span><span class=\"token punctuation\">(</span><span class=\"token punctuation\">(</span>sl_led_t <span class=\"token operator\">*</span><span class=\"token punctuation\">)</span><span class=\"token operator\">&amp;</span>sl_inst0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n\n<span class=\"token punctuation\">}</span>\n</code></pre><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The sl_simple_rgb_pwm_led_instances.c file is shown with only one instance, but if more were in use they would all appear in this .c file.</p></li></ul><pre class=\"language-clike\"><code class=\"language-clike\"><span class=\"token comment\">// sl_simple_rgb_pwm_led_instances.h</span>\n\n#ifndef SL_SIMPLE_RGB_PWM_LED_INSTANCES_H\n#define SL_SIMPLE_RGB_PWM_LED_INSTANCES_H\n\n#include <span class=\"token string\">\"sl_simple_rgb_pwm_led.h\"</span>\n\nextern const sl_led_rgb_pwm_t sl_inst0<span class=\"token punctuation\">;</span>\n\nvoid <span class=\"token function\">sl_simple_rgb_pwm_led_init_instances</span><span class=\"token punctuation\">(</span>void<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n\n#endif <span class=\"token comment\">// SL_SIMPLE_RGB_PWM_LED_INIT_H</span>\n</code></pre><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">The sl_simple_rgb_pwm_led_instances.h file is shown with only one instance, but if more were in use they would all appear in this .h file.</p></li></ul><p style=\"color:inherit\"><br></p><h2>RGB PWM LED Usage<span id=\"rgb-pwm-led-usage\" class=\"self-anchor\"><a class=\"perm\" href=\"#rgb-pwm-led-usage\">#</a></span></h2><p style=\"color:inherit\">The RGB PWM Led driver provides functionality for controlling Red/Green/Blue/White LEDs that are driven by PWM. The LEDs can be turned on and off and toggled, and remember their color and brightness state when being turned back on. The color and brightness can be set using values of 0-65535 for red, green, blue, and white. Retrieving the state gives the on/off value, while retrieving the color gives the rgb values. The following code shows how to control these LEDs. An LED should always be initialized before calling any other functions with it.</p><pre class=\"language-clike\"><code class=\"language-clike\"><span class=\"token comment\">// initialize rgb LED</span>\n<span class=\"token function\">sl_led_init</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>rgb_led_inst0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n\n<span class=\"token comment\">// turn on LED, set color to purple, turn off, toggle (would maintain purple color)</span>\n<span class=\"token function\">sl_led_turn_on</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>rgb_led_inst0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\nuint16_t red <span class=\"token operator\">=</span> <span class=\"token number\">65535</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// max red</span>\nuint16_t green <span class=\"token operator\">=</span> <span class=\"token number\">0</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// no green</span>\nuint16_t blue <span class=\"token operator\">=</span> <span class=\"token number\">65535</span><span class=\"token punctuation\">;</span> <span class=\"token comment\">// max blue</span>\n<span class=\"token function\">sl_led_set_rgb_color</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>rgb_led_inst0<span class=\"token punctuation\">,</span> red<span class=\"token punctuation\">,</span> green<span class=\"token punctuation\">,</span> blue<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token function\">sl_led_turn_off</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>rgb_led_inst0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n<span class=\"token function\">sl_led_toggle</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>rgb_led_inst0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n\n<span class=\"token comment\">// get the state of the led</span>\nsl_led_state_t state <span class=\"token operator\">=</span> <span class=\"token function\">sl_led_get_state</span><span class=\"token punctuation\">(</span><span class=\"token operator\">&amp;</span>rgb_led_inst0<span class=\"token punctuation\">)</span><span class=\"token punctuation\">;</span>\n</code></pre><h2>Modules<span id=\"modules\" class=\"self-anchor\"><a class=\"perm\" href=\"#modules\">#</a></span></h2><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-simple-rgb-pwm-led-context-t\" target=\"_blank\" rel=\"\">sl_simple_rgb_pwm_led_context_t</a></p><p style=\"color:inherit\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-led-rgb-pwm-t\" target=\"_blank\" rel=\"\">sl_led_rgb_pwm_t</a></p><div class=\"decl-class-section\"><h2>Functions<span id=\"func-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">sl_status_t</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-rgb-pwm-led-init\">sl_simple_rgb_pwm_led_init</a>(void *rgb)</div><div class=\"classdescription\"><p style=\"color:inherit\">Initialize an RGB PWM LED driver. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-rgb-pwm-led-turn-on\">sl_simple_rgb_pwm_led_turn_on</a>(void *rgb)</div><div class=\"classdescription\"><p style=\"color:inherit\">Turn on an RBG LED. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-rgb-pwm-led-turn-off\">sl_simple_rgb_pwm_led_turn_off</a>(void *rgb)</div><div class=\"classdescription\"><p style=\"color:inherit\">Turn off an RGB LED. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-rgb-pwm-led-toggle\">sl_simple_rgb_pwm_led_toggle</a>(void *rgb)</div><div class=\"classdescription\"><p style=\"color:inherit\">Toggle an RGB LED. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\"><a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/led#sl-led-state-t\" target=\"_blank\" rel=\"\">sl_led_state_t</a></div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-rgb-pwm-led-get-state\">sl_simple_rgb_pwm_led_get_state</a>(void *rgb)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get status of an RGB LED. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-rgb-pwm-led-set-color\">sl_simple_rgb_pwm_led_set_color</a>(void *rgb, uint16_t red, uint16_t green, uint16_t blue)</div><div class=\"classdescription\"><p style=\"color:inherit\">Set color mixing and dimming level of an RGB LED. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-rgb-pwm-led-get-color\">sl_simple_rgb_pwm_led_get_color</a>(void *rgb, uint16_t *red, uint16_t *green, uint16_t *blue)</div><div class=\"classdescription\"><p style=\"color:inherit\">Get current color mixing and dimming level of an RGB LED. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-led-set-rgb-color\">sl_led_set_rgb_color</a>(const sl_led_rgb_pwm_t *rgb, uint16_t red, uint16_t green, uint16_t blue)</div><div class=\"classdescription\"><p style=\"color:inherit\">LED set RGB color. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">void</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-led-get-rgb-color\">sl_led_get_rgb_color</a>(const sl_led_rgb_pwm_t *rgb, uint16_t *red, uint16_t *green, uint16_t *blue)</div><div class=\"classdescription\"><p style=\"color:inherit\">LED get RGB setting. </p></div></div></div></div></div><div class=\"decl-class-section\"><h2>Macros<span id=\"define-declaration\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-declaration\">#</a></span></h2><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-rgb-pwm-led-polarity-active-high\">SL_SIMPLE_RGB_PWM_LED_POLARITY_ACTIVE_HIGH</a> 0U</div><div class=\"classdescription\"><p style=\"color:inherit\">LED Active polarity High. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-rgb-pwm-led-polarity-active-low\">SL_SIMPLE_RGB_PWM_LED_POLARITY_ACTIVE_LOW</a> 1U</div><div class=\"classdescription\"><p style=\"color:inherit\">LED Active polarity Low. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-rgb-pwm-led-color-r\">SL_SIMPLE_RGB_PWM_LED_COLOR_R</a> 0U</div><div class=\"classdescription\"><p style=\"color:inherit\">LED Red. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-rgb-pwm-led-color-g\">SL_SIMPLE_RGB_PWM_LED_COLOR_G</a> 1U</div><div class=\"classdescription\"><p style=\"color:inherit\">LED Green. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-rgb-pwm-led-color-b\">SL_SIMPLE_RGB_PWM_LED_COLOR_B</a> 2U</div><div class=\"classdescription\"><p style=\"color:inherit\">LED Blue. </p></div></div></div></div><div><div class=\"columns\"><div class=\"attributename has-text-right\">#define</div><div class=\"column\"><div class=\"attributename\"><a href=\"#sl-simple-rgb-pwm-led-num-cc-required\">SL_SIMPLE_RGB_PWM_LED_NUM_CC_REQUIRED</a> 3U</div><div class=\"classdescription\"><p style=\"color:inherit\">Number of Timer Capture Channels required (1 for each RGB color) </p></div></div></div></div></div><div class=\"def-class-section\"><h2>Function Documentation<span id=\"func-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#func-definition\">#</a></span></h2><div><h3>sl_simple_rgb_pwm_led_init<span id=\"sl-simple-rgb-pwm-led-init\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-rgb-pwm-led-init\">#</a></span></h3><blockquote>sl_status_t sl_simple_rgb_pwm_led_init (void * rgb)</blockquote><p style=\"color:inherit\">Initialize an RGB PWM LED driver. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">rgb</td><td><p style=\"color:inherit\">Pointer to rgb-pwm-led specific data.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Status Code:</p><ul style=\"list-style:\"><li><p style=\"color:inherit\">SL_STATUS_OK Success</p></li><li><p style=\"color:inherit\">SL_STATUS_FAIL Init error </p></li></ul></li></ul><br><div>Definition at line <code>109</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div><div><h3>sl_simple_rgb_pwm_led_turn_on<span id=\"sl-simple-rgb-pwm-led-turn-on\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-rgb-pwm-led-turn-on\">#</a></span></h3><blockquote>void sl_simple_rgb_pwm_led_turn_on (void * rgb)</blockquote><p style=\"color:inherit\">Turn on an RBG LED. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">rgb</td><td><p style=\"color:inherit\">Pointer to rgb_pwm-led specific data. </p></td></tr></tbody></table></div><p style=\"color:inherit\">Turns on at previously set color levels If no previous levels set, turns on at max level for all RGB LEDs</p><br><div>Definition at line <code>119</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div><div><h3>sl_simple_rgb_pwm_led_turn_off<span id=\"sl-simple-rgb-pwm-led-turn-off\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-rgb-pwm-led-turn-off\">#</a></span></h3><blockquote>void sl_simple_rgb_pwm_led_turn_off (void * rgb)</blockquote><p style=\"color:inherit\">Turn off an RGB LED. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">rgb</td><td><p style=\"color:inherit\">Pointer to rgb-pwm-led specific data. </p></td></tr></tbody></table></div><br><div>Definition at line <code>127</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div><div><h3>sl_simple_rgb_pwm_led_toggle<span id=\"sl-simple-rgb-pwm-led-toggle\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-rgb-pwm-led-toggle\">#</a></span></h3><blockquote>void sl_simple_rgb_pwm_led_toggle (void * rgb)</blockquote><p style=\"color:inherit\">Toggle an RGB LED. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">rgb</td><td><p style=\"color:inherit\">Pointer to rgb-pwm-led specific data. </p></td></tr></tbody></table></div><p style=\"color:inherit\">The toggle \"ON\" behavior is as defined for <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/simple-rgb-pwm-led#sl-simple-rgb-pwm-led-turn-on\" target=\"_blank\" rel=\"\">sl_simple_rgb_pwm_led_turn_on()</a></p><br><div>Definition at line <code>136</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div><div><h3>sl_simple_rgb_pwm_led_get_state<span id=\"sl-simple-rgb-pwm-led-get-state\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-rgb-pwm-led-get-state\">#</a></span></h3><blockquote>sl_led_state_t sl_simple_rgb_pwm_led_get_state (void * rgb)</blockquote><p style=\"color:inherit\">Get status of an RGB LED. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">rgb</td><td><p style=\"color:inherit\">Pointer to rgb-pwm-led specific data.</p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Returns</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">sl_led_state_t Current state of RGB LED. 0 for Red, Green, Blue and White LEDs are all OFF 1 for Red, Green, Blue or White LED is ON </p></li></ul><br><div>Definition at line <code>147</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div><div><h3>sl_simple_rgb_pwm_led_set_color<span id=\"sl-simple-rgb-pwm-led-set-color\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-rgb-pwm-led-set-color\">#</a></span></h3><blockquote>void sl_simple_rgb_pwm_led_set_color (void * rgb, uint16_t red, uint16_t green, uint16_t blue)</blockquote><p style=\"color:inherit\">Set color mixing and dimming level of an RGB LED. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">rgb</td><td><p style=\"color:inherit\">Pointer to rgb-pwm-led specific data:</p></td></tr><tr><td>[in]</td><td class=\"paramname\">red</td><td><p style=\"color:inherit\">Red color level (PWM duty-cycle [0-65535]) </p></td></tr><tr><td>[in]</td><td class=\"paramname\">green</td><td><p style=\"color:inherit\">Green color level (PWM duty-cycle [0-65535]) </p></td></tr><tr><td>[in]</td><td class=\"paramname\">blue</td><td><p style=\"color:inherit\">Blue color level (PWM duty-cycle [0-65535]) </p></td></tr></tbody></table></div><br><div>Definition at line <code>159</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div><div><h3>sl_simple_rgb_pwm_led_get_color<span id=\"sl-simple-rgb-pwm-led-get-color\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-rgb-pwm-led-get-color\">#</a></span></h3><blockquote>void sl_simple_rgb_pwm_led_get_color (void * rgb, uint16_t * red, uint16_t * green, uint16_t * blue)</blockquote><p style=\"color:inherit\">Get current color mixing and dimming level of an RGB LED. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>[in]</td><td class=\"paramname\">rgb</td><td><p style=\"color:inherit\">Pointer to rgb-pwm-led specific data:</p></td></tr><tr><td>[out]</td><td class=\"paramname\">red</td><td><p style=\"color:inherit\">Red color level (PWM duty-cycle [0-65535]) </p></td></tr><tr><td>[out]</td><td class=\"paramname\">green</td><td><p style=\"color:inherit\">Green color level (PWM duty-cycle [0-65535]) </p></td></tr><tr><td>[out]</td><td class=\"paramname\">blue</td><td><p style=\"color:inherit\">Blue color level (PWM duty-cycle [0-65535]) </p></td></tr></tbody></table></div><p style=\"color:inherit\"><strong>Note</strong></p><ul style=\"list-style:\"><li><p style=\"color:inherit\">Will return the last stored levels regardless of the current ON/OFF state. Call <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/simple-rgb-pwm-led#sl-simple-rgb-pwm-led-get-state\" target=\"_blank\" rel=\"\">sl_simple_rgb_pwm_led_get_state()</a> to determine if the RGB LED is actually ON or OFF.</p></li></ul><br><div>Definition at line <code>178</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div><div><h3>sl_led_set_rgb_color<span id=\"sl-led-set-rgb-color\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-led-set-rgb-color\">#</a></span></h3><blockquote>void sl_led_set_rgb_color (const <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-led-rgb-pwm-t\" target=\"_blank\" rel=\"\">sl_led_rgb_pwm_t</a> * rgb, uint16_t red, uint16_t green, uint16_t blue)</blockquote><p style=\"color:inherit\">LED set RGB color. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\">rgb</td><td></td></tr><tr><td>N/A</td><td class=\"paramname\">red</td><td></td></tr><tr><td>N/A</td><td class=\"paramname\">green</td><td></td></tr><tr><td>N/A</td><td class=\"paramname\">blue</td><td></td></tr></tbody></table></div><br><div>Definition at line <code>187</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div><div><h3>sl_led_get_rgb_color<span id=\"sl-led-get-rgb-color\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-led-get-rgb-color\">#</a></span></h3><blockquote>void sl_led_get_rgb_color (const <a href=\"http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/sl-led-rgb-pwm-t\" target=\"_blank\" rel=\"\">sl_led_rgb_pwm_t</a> * rgb, uint16_t * red, uint16_t * green, uint16_t * blue)</blockquote><p style=\"color:inherit\">LED get RGB setting. </p><div class=\"parameters-section\"><strong class=\"parameters\">Parameters</strong><table><tbody><tr><td>N/A</td><td class=\"paramname\">rgb</td><td></td></tr><tr><td>N/A</td><td class=\"paramname\">red</td><td></td></tr><tr><td>N/A</td><td class=\"paramname\">green</td><td></td></tr><tr><td>N/A</td><td class=\"paramname\">blue</td><td></td></tr></tbody></table></div><br><div>Definition at line <code>193</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div></div><div class=\"def-class-section\"><h2>Macro Definition Documentation<span id=\"define-definition\" class=\"self-anchor\"><a class=\"perm\" href=\"#define-definition\">#</a></span></h2><div><h3>SL_SIMPLE_RGB_PWM_LED_POLARITY_ACTIVE_HIGH<span id=\"sl-simple-rgb-pwm-led-polarity-active-high\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-rgb-pwm-led-polarity-active-high\">#</a></span></h3><blockquote>#define SL_SIMPLE_RGB_PWM_LED_POLARITY_ACTIVE_HIGH</blockquote><b>Value:</b><pre class=\"macroshort\">0U</pre><p style=\"color:inherit\">LED Active polarity High. </p><br><div>Definition at line <code>58</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div><div><h3>SL_SIMPLE_RGB_PWM_LED_POLARITY_ACTIVE_LOW<span id=\"sl-simple-rgb-pwm-led-polarity-active-low\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-rgb-pwm-led-polarity-active-low\">#</a></span></h3><blockquote>#define SL_SIMPLE_RGB_PWM_LED_POLARITY_ACTIVE_LOW</blockquote><b>Value:</b><pre class=\"macroshort\">1U</pre><p style=\"color:inherit\">LED Active polarity Low. </p><br><div>Definition at line <code>59</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div><div><h3>SL_SIMPLE_RGB_PWM_LED_COLOR_R<span id=\"sl-simple-rgb-pwm-led-color-r\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-rgb-pwm-led-color-r\">#</a></span></h3><blockquote>#define SL_SIMPLE_RGB_PWM_LED_COLOR_R</blockquote><b>Value:</b><pre class=\"macroshort\">0U</pre><p style=\"color:inherit\">LED Red. </p><br><div>Definition at line <code>61</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div><div><h3>SL_SIMPLE_RGB_PWM_LED_COLOR_G<span id=\"sl-simple-rgb-pwm-led-color-g\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-rgb-pwm-led-color-g\">#</a></span></h3><blockquote>#define SL_SIMPLE_RGB_PWM_LED_COLOR_G</blockquote><b>Value:</b><pre class=\"macroshort\">1U</pre><p style=\"color:inherit\">LED Green. </p><br><div>Definition at line <code>62</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div><div><h3>SL_SIMPLE_RGB_PWM_LED_COLOR_B<span id=\"sl-simple-rgb-pwm-led-color-b\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-rgb-pwm-led-color-b\">#</a></span></h3><blockquote>#define SL_SIMPLE_RGB_PWM_LED_COLOR_B</blockquote><b>Value:</b><pre class=\"macroshort\">2U</pre><p style=\"color:inherit\">LED Blue. </p><br><div>Definition at line <code>63</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div><div><h3>SL_SIMPLE_RGB_PWM_LED_NUM_CC_REQUIRED<span id=\"sl-simple-rgb-pwm-led-num-cc-required\" class=\"self-anchor\"><a class=\"perm\" href=\"#sl-simple-rgb-pwm-led-num-cc-required\">#</a></span></h3><blockquote>#define SL_SIMPLE_RGB_PWM_LED_NUM_CC_REQUIRED</blockquote><b>Value:</b><pre class=\"macroshort\">3U</pre><p style=\"color:inherit\">Number of Timer Capture Channels required (1 for each RGB color) </p><br><div>Definition at line <code>65</code> of file <code>platform/driver/leddrv/inc/sl_simple_rgb_pwm_led.h</code></div><br></div></div></div></article><footer class=\"footer-commons-msg\"></footer><div class=\"light_theme hidden-lg\" ub-in-page=\"634f96f53042a864e44b82f8\"></div><div class=\"dark_theme hidden-lg\" ub-in-page=\"6735652b86eaf07b7012c67e\"></div>", "url": "http://docs.silabs.com/gecko-platform/4.4.4/platform-driver/simple-rgb-pwm-led", "status": "success"}